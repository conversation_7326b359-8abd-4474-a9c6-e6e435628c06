package com.yorha.common.utils.jol;

import com.yorha.common.utils.TextTable;
import org.openjdk.jol.info.GraphLayout;

import java.util.Comparator;
import java.util.Set;

/**
 * Java-Obj-Layout 解析，分析java对象的内存占用
 */
public class JolParser {

    private final Set<Class<?>> excludeClazzSet;

    public JolParser(Set<Class<?>> excludeClazzSet) {
        this.excludeClazzSet = excludeClazzSet;
    }

    public GraphLayout parse(Object root) {
        return GraphLayout.parseInstance(root);
    }

    public static TextTable toDisplay(TreeLayout treeLayout, int layer, int maxNodeInLayer) {
        TextTable textTable = new TextTable("treeLayout");
        textTable.addColumn("size", "layer", "name");
        TreeLayout.Node node = treeLayout.getRoot();
        displayTreeNode(textTable, node, layer, maxNodeInLayer);
        return textTable;
    }

    private static void displayTreeNode(TextTable textTable, TreeLayout.Node node, int layer, int maxNodeInLayer) {
        TextTable.Row row = textTable.addRow();
        StringBuilder sizeStr = new StringBuilder();
        StringBuilder nameStr = new StringBuilder();
        for (int i = 0; i < node.getLayer() - 1; i++) {
            sizeStr.append(" ");
            nameStr.append(" ");
        }
        sizeStr.append(node.getSize());
        nameStr.append(node.getName());
        row.add(sizeStr, "ly=" + node.getLayer(), nameStr);
        if (node.getLayer() < layer) {
            // 内存占用从高到底排个序
            node.getChildren().sort(Comparator.comparingLong(TreeLayout.Node::getSize).reversed());
            // 按序输出
            for (int i = 0; i < Math.min(maxNodeInLayer, node.getChildren().size()); i++) {
                displayTreeNode(textTable, node.getChildren().get(i), layer, maxNodeInLayer);
            }
        }
    }

}

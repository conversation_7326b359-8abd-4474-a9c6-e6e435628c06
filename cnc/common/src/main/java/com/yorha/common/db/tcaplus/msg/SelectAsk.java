package com.yorha.common.db.tcaplus.msg;

import com.google.protobuf.Message;
import com.yorha.common.db.tcaplus.option.GetByPartKeyOption;
import com.yorha.common.db.tcaplus.result.GetByPartKeyResult;

public class SelectAsk<T extends Message.Builder> implements GameDbReq<GetByPartKeyResult<T>> {
    private final T req;
    private final GetByPartKeyOption option;

    public SelectAsk(T req) {
        this(req, GetByPartKeyOption.newBuilder().build());
    }

    public SelectAsk(T req, GetByPartKeyOption option) {
        this.req = req;
        this.option = option;
    }

    public T getReq() {
        return req;
    }

    public GetByPartKeyOption getOption() {
        return option;
    }
}

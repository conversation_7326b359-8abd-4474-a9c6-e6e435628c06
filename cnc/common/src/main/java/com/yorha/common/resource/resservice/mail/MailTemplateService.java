package com.yorha.common.resource.resservice.mail;

import com.google.common.collect.Sets;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.exception.ResourceException;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import res.template.ItemTemplate;
import res.template.MailExpireTemplate;
import res.template.MailTemplate;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class MailTemplateService extends AbstractResService {


    public MailTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    private int maxMailNum;

    @Override
    public void load() throws ResourceException {
        maxMailNum = 0;
        for (final MailExpireTemplate expireTemplate : getResHolder().getListFromMap(MailExpireTemplate.class)) {
            maxMailNum = Math.max(maxMailNum, expireTemplate.getMax());

        }
    }


    @Override
    public void checkValid() throws ResourceException {
        Collection<MailTemplate> listFromMap = getResHolder().getListFromMap(MailTemplate.class);
        Map<Integer, ItemTemplate> itemMap = getResHolder().getMap(ItemTemplate.class);
        for (MailTemplate mailTemplate : listFromMap) {
            for (IntPairType kv : mailTemplate.getAwardPairList()) {
                if (!itemMap.containsKey(kv.getKey())) {
                    throw new ResourceException(StringUtils.format("邮件配置:{} 奖励道具:{} 尚未在道具表中配置", mailTemplate.getId(), kv.getKey()));
                }
                if (kv.getValue() <= 0) {
                    throw new ResourceException("邮件配置:{} 奖励道具数量错误:{}", mailTemplate.getId(), kv.getValue());
                }
            }
        }
        Collection<MailExpireTemplate> expireListFromMap = getResHolder().getListFromMap(MailExpireTemplate.class);
        Set<CommonEnum.MailTabsType> configedMailTabsTypes = Sets.newHashSet();
        for (MailExpireTemplate template : expireListFromMap) {
            // 允许不可收藏的页签
            if (template.getCollectMax() < 0) {
                throw new ResourceException("邮件过期配置:{} 收藏上限为负数:{}", template.getId(), template.getCollectMax());
            }

            if (template.getMax() <= 0) {
                throw new ResourceException("邮件过期配置:{} 页签上限为非正数:{}", template.getId(), template.getMax());
            }

            if (template.getCollectMax() >= template.getMax()) {
                throw new ResourceException("邮件过期配置:{} 收藏上限大于页签上限", template.getId());
            }

            if (template.getExpire() <= 0) {
                throw new ResourceException("邮件过期配置:{} 过期天数非正数:{}", template.getId(), template.getExpire());
            }

            if (configedMailTabsTypes.contains(template.getMailTabsType())) {
                throw new ResourceException("邮件过期配置:{} MailTabsType重复:{}", template.getId(), template.getMailTabsType());
            }
            configedMailTabsTypes.add(template.getMailTabsType());
        }

        for (CommonEnum.MailTabsType mailTabsType : CommonEnum.MailTabsType.values()) {
            // 收藏不走配表
            if (mailTabsType == CommonEnum.MailTabsType.MAIL_TABS_TYPE_STORE) {
                continue;
            }
            if (mailTabsType == CommonEnum.MailTabsType.MAIL_TABS_TYPE_UNKNOWN) {
                continue;
            }
            if (!configedMailTabsTypes.contains(mailTabsType)) {
                throw new ResourceException("邮件过期配置 MailTabsType未配置:{}", mailTabsType);
            }
        }

    }

    public MailTemplate getMailTemplate(int id) {
        return getResHolder().getValueFromMap(MailTemplate.class, id);
    }

    public MailExpireTemplate getMailExpireTemplate(final CommonEnum.MailTabsType tabsType) {
        for (final MailExpireTemplate expireTemplate : getResHolder().getListFromMap(MailExpireTemplate.class)) {
            if (expireTemplate.getMailTabsType() == tabsType) {
                return expireTemplate;
            }
        }
        return null;
    }

    public int getMaxMailNum() {
        return this.maxMailNum;
    }

}

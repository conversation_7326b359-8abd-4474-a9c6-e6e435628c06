package com.yorha.common.enums.qlog.clan;

public enum ClanGiftAction {
    /**
     * 购买礼包
     */
    BUY_BUNDLE("buy_bundle"),

    /**
     * 击杀野怪
     */
    KILL_MONSTER("kill_monster"),

    /**
     * 领取奖励
     */
    COLLECT_REWARD("collect_reward"),

    /**
     * 礼物过期
     */
    REWARD_EXPIRE("reward_expire");

    private String type;

    ClanGiftAction(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}



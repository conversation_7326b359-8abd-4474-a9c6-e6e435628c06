package com.yorha.common.resource.resservice.task;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yorha.common.constant.Constants;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.activity.ActivityResService;
import com.yorha.common.resource.resservice.activity.ActivityTaskConf;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import res.template.*;

import java.util.*;

import static com.yorha.proto.CommonEnum.TaskType.*;

/**
 * 任务表
 *
 * <AUTHOR>
 */
public class TaskTemplateService extends AbstractResService {

    private final Map<Integer, List<Integer>> taskParamMap = new HashMap<>();

    /**
     * 记录了不同解锁条件下链首配置
     */
    private final Map<CommonEnum.BranchTaskCondition, Set<TaskBranchTemplate>> branchConditionTask = new HashMap<>();

    private final Map<Integer, Set<Integer>> chapterTask = new HashMap<>();
    private final Map<Integer, Integer> chapterMainTask = new HashMap<>();

    private final Map<Integer, Set<Integer>> pveTask = new HashMap<>();
    private final Map<Integer, Integer> pveMainTask = new HashMap<>();

    private final Set<CommonEnum.TaskType> uncertainParamNumTask = new HashSet<>();

    public TaskTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        // 普通任务
        loadOrdinaryTask();

        initAllParam();

        uncertainParamNumTask.add(TT_USE_ITEM_TASK);
        uncertainParamNumTask.add(TT_ADD_XX_ITEM_XX_NUM);
        uncertainParamNumTask.add(TT_CONSTRUCT_XX_NUM_XX_LEVEL_BUILD);
        uncertainParamNumTask.add(TT_FINISH_RESEARCH_XX_TECH);
    }

    private void initAllParam() throws ResourceException {
        for (TaskPoolTemplate taskPoolTemplate : getResHolder().getListFromMap(TaskPoolTemplate.class)) {
            if (taskParamMap.get(taskPoolTemplate.getId()) != null) {
                throw new ResourceException(StringUtils.format("任务池重复id：{}", taskPoolTemplate.getId()));
            }
            taskParamMap.put(taskPoolTemplate.getId(), taskPoolTemplate.getTypeValueList());
        }
    }

    private void loadOrdinaryTask() {
        for (TaskBranchTemplate branchTemplate : getResHolder().getListFromMap(TaskBranchTemplate.class)) {
            for (IntPairType intPairType : branchTemplate.getUnlockConditionPairList()) {
                CommonEnum.BranchTaskCondition branchTaskCondition = CommonEnum.BranchTaskCondition.forNumber(intPairType.getKey());
                Set<TaskBranchTemplate> taskBranchTemplates = branchConditionTask.computeIfAbsent(branchTaskCondition, (key) -> new HashSet<>());
                taskBranchTemplates.add(branchTemplate);
            }
        }

        for (TaskChapterTemplate taskTemplate : getResHolder().getListFromMap(TaskChapterTemplate.class)) {
            chapterTask.computeIfAbsent(taskTemplate.getChapterId(), (key) -> new HashSet<>()).add(taskTemplate.getId());

            TaskPoolTemplate poolTemplate = getResHolder().getValueFromMap(TaskPoolTemplate.class, taskTemplate.getTaskId());
            if (poolTemplate != null && poolTemplate.getTaskType() == TT_CHAPTER_COMPLETE_TASK) {
                chapterMainTask.put(taskTemplate.getChapterId(), taskTemplate.getId());
            }
        }

        for (TaskPveTemplate taskTemplate : getResHolder().getListFromMap(TaskPveTemplate.class)) {
            pveTask.computeIfAbsent(taskTemplate.getPvechapterId(), (key) -> new HashSet<>()).add(taskTemplate.getId());

            //设置限时的任务就是主任务
            if (taskTemplate.getPVEchapterTime() > 0) {
                if (!pveMainTask.containsValue(taskTemplate.getPvechapterId())) {
                    pveMainTask.put(taskTemplate.getPvechapterId(), taskTemplate.getId());
                }
            }
        }
    }

    /**
     * 获取主线任务后置任务
     *
     * @return 可为空
     */
    public TaskMainTemplate getMainPostTask(TaskMainTemplate task) {
        int frontTask = task.getFrontTask();
        if (frontTask <= 0) {
            return null;
        }
        return getResHolder().getValueFromMap(TaskMainTemplate.class, frontTask);
    }


    /**
     * 根据主城等级获取每日任务列表
     */
    public List<TaskDailyTemplate> getDailyTaskByCityLevel(int mainCityLevel) {
        List<TaskDailyTemplate> result = Lists.newArrayList();
        for (TaskDailyTemplate taskDailyTemplate : getResHolder().getListFromMap(TaskDailyTemplate.class)) {
            if (StringUtils.isEmpty(taskDailyTemplate.getCityLevelRange())) {
                result.add(taskDailyTemplate);
                continue;
            }
            String[] split = taskDailyTemplate.getCityLevelRange().split(Constants.XIA_HUA_XIAN);
            if (split.length < 2) {
                continue;
            }
            if (Integer.parseInt(split[0]) <= mainCityLevel && mainCityLevel <= Integer.parseInt(split[1])) {
                result.add(taskDailyTemplate);
            }
        }
        return result;
    }

    /**
     * 返回的是链首复本哈
     */
    public Set<TaskBranchTemplate> getUnacceptedTask(CommonEnum.BranchTaskCondition reason) {
        var tasks = branchConditionTask.get(reason);
        if (tasks == null) {
            return Sets.newHashSet();
        } else {
            return Sets.newHashSet(tasks);
        }
    }

    public List<IntPairType> getReward(CommonEnum.TaskClass handlerType, int configId) {
        switch (handlerType) {
            case TC_MAIN: {
                return getResHolder().getValueFromMap(TaskMainTemplate.class, configId).getRewardPairList();
            }
            case TC_DAILY: {
                return getResHolder().getValueFromMap(TaskDailyTemplate.class, configId).getRewardPairList();
            }
            case TC_BRANCH: {
                return getResHolder().getValueFromMap(TaskBranchTemplate.class, configId).getRewardPairList();
            }
            default: {
                throw new GeminiException("unknown task type:{}", handlerType);
            }
        }
    }

    public Set<Integer> getChapterTasks(int chapter) {
        return chapterTask.get(chapter);
    }

    public int getChapterMainTask(int chapter) {
        return chapterMainTask.getOrDefault(chapter, 0);
    }

    public Set<Integer> getPveChapterTasks(int chapter) {
        return pveTask.get(chapter);
    }

    public int getPveChapterMainTask(int chapter) {
        return pveMainTask.getOrDefault(chapter, 0);
    }

    @Override
    public void checkValid() throws ResourceException {
        checkTask();
        idRepeatCheck();
        checkTaskNewbie();
        checkLinkMainTask();
    }

    /**
     * 主线链表检测
     */
    private void checkLinkMainTask() throws ResourceException {
        int firstMainTaskId = getResHolder().getConstTemplate(ConstTemplate.class).getFirstMainTaskId();
        TaskMainTemplate valueFromMap = getResHolder().getValueFromMap(TaskMainTemplate.class, firstMainTaskId);

        TaskMainTemplate slot = valueFromMap;
        TaskMainTemplate fast = valueFromMap;
        while (fast != null) {
            slot = getResHolder().findValueFromMap(TaskMainTemplate.class, slot.getFrontTask());
            fast = getResHolder().findValueFromMap(TaskMainTemplate.class, fast.getFrontTask());
            if (fast == null) {
                break;
            }
            fast = getResHolder().findValueFromMap(TaskMainTemplate.class, fast.getFrontTask());
            if (fast == null) {
                break;
            }
            if (slot == fast) {
                throw new ResourceException("主线配置错误，检查主线链表继承顺序");
            }
        }

    }

    /**
     * 新手相关检测
     */
    private void checkTaskNewbie() throws ResourceException {
        Set<Integer> receiveGroup = new HashSet<>();
        for (TaskMainTemplate template : getResHolder().getListFromMap(TaskMainTemplate.class)) {
            if (template.getTaskGetGuide() != 0) {
                if (receiveGroup.contains(template.getTaskGetGuide())) {
                    throw new ResourceException("主线任务行为事件流配置错误.配置的组id已存在 groupId={}", template.getTaskGetGuide());
                }
                receiveGroup.add(template.getTaskGetGuide());
            }
            if (template.getTaskCompleteGuide() != 0) {
                if (receiveGroup.contains(template.getTaskCompleteGuide())) {
                    throw new ResourceException("主线任务行为事件流配置错误.配置的组id已存在 groupId={}", template.getTaskCompleteGuide());
                }
                receiveGroup.add(template.getTaskCompleteGuide());
            }
            if (template.getTaskDoneGuide() != 0) {
                if (receiveGroup.contains(template.getTaskDoneGuide())) {
                    throw new ResourceException("主线任务行为事件流配置错误.配置的组id已存在 groupId={}", template.getTaskDoneGuide());
                }
                receiveGroup.add(template.getTaskDoneGuide());
            }
        }
        for (TaskMainTemplate template : getResHolder().getListFromMap(TaskMainTemplate.class)) {
            int taskGetDeleteGuide = template.getTaskGetDeleteGuide();
            int taskCompleteDeleteGuide = template.getTaskCompleteDeleteGuide();
            int taskDoneDeleteGuide = template.getTaskDoneDeleteGuide();
            if (taskGetDeleteGuide > 0) {
                if (taskGetDeleteGuide == taskDoneDeleteGuide || taskGetDeleteGuide == taskCompleteDeleteGuide) {
                    throw new ResourceException("主线任务行为事件流配置错误.group重复 接取={} 完成={} 领奖={}",
                            template.getTaskGetGuide(), template.getTaskCompleteGuide(), template.getTaskDoneGuide());

                }
                receiveGroup.remove(taskGetDeleteGuide);
            }

            if (taskCompleteDeleteGuide > 0) {
                if (taskCompleteDeleteGuide == taskDoneDeleteGuide) {
                    throw new ResourceException("主线任务行为事件流配置错误.group重复 接取={} 完成={} 领奖={}",
                            template.getTaskGetGuide(), template.getTaskCompleteGuide(), template.getTaskDoneGuide());

                }
                receiveGroup.remove(taskCompleteDeleteGuide);
            }
            if (taskDoneDeleteGuide > 0) {
                receiveGroup.remove(taskDoneDeleteGuide);
            }
        }
        if (!receiveGroup.isEmpty()) {
            throw new ResourceException("主线任务行为事件流配置错误.group未移除 groups:{}", receiveGroup);
        }
    }

    /**
     * id重复检测
     */
    private void idRepeatCheck() throws ResourceException {
        HashMap<Integer, Integer> idMap = Maps.newHashMap();
        for (TaskPoolTemplate taskPoolTemplate : getResHolder().getListFromMap(TaskPoolTemplate.class)) {
            int idCount = idMap.computeIfAbsent(taskPoolTemplate.getId(), (key) -> 0);
            idCount++;
            idMap.put(taskPoolTemplate.getId(), idCount);
        }
        for (Map.Entry<Integer, Integer> entry : idMap.entrySet()) {
            if (entry.getValue() > 1) {
                throw new ResourceException("任务表任务池id重复。重复id:{}", entry.getKey());
            }
        }
    }


    private void checkTask() throws ResourceException {
        for (TaskDailyTemplate value : getResHolder().getListFromMap(TaskDailyTemplate.class)) {
            String reason = "每日任务";
            checkTaskId(value.getTaskId(), reason);
            checkDailyTemplate(value);
            checkTaskParam(value.getTaskId(), reason);
        }

        for (TaskBranchTemplate value : getResHolder().getListFromMap(TaskBranchTemplate.class)) {
            String reason = "支线任务";
            checkTaskId(value.getTaskId(), reason);
            checkBranchTemplate(value);
            checkTaskParam(value.getTaskId(), reason);
        }
        for (TaskMainTemplate value : getResHolder().getListFromMap(TaskMainTemplate.class)) {
            String reason = "主线任务";
            checkTaskId(value.getTaskId(), reason);
            checkMainTemplate(value);
            checkTaskParam(value.getTaskId(), reason);
        }

        ActivityResService activityResService = getResHolder().innerGetResService(ActivityResService.class);
        for (Map.Entry<String, Map<Integer, ActivityTaskConf>> entry0 : activityResService.getTaskPoolMap().entrySet()) {
            String poolName = entry0.getKey();
            for (ActivityTaskConf atc : entry0.getValue().values()) {
                checkTaskId(atc.getTaskId(), "活动-" + poolName);
                checkTaskParam(atc.getTaskId(), "活动任务-" + poolName);
            }
        }

        for (TaskChapterTemplate value : getResHolder().getListFromMap(TaskChapterTemplate.class)) {
            String reason = "chapter task";
            checkTaskId(value.getTaskId(), reason);
            checkChapterTemplate(value);
            checkTaskParam(value.getTaskId(), reason);
        }

        for (TaskPveTemplate value : getResHolder().getListFromMap(TaskPveTemplate.class)) {
            String reason = "pve task";
            checkTaskId(value.getPVEtaskId(), reason);
            checkTaskParam(value.getPVEtaskId(), reason);
        }
    }

    private void checkMainTemplate(TaskMainTemplate value) throws ResourceException {
        if (value.getRewardPairList() == null || value.getRewardPairList().size() <= 0) {
            throw new ResourceException(StringUtils.format("主线任务奖励不允许为空. taskId :{}", value.getTaskId()));
        }
    }

    private void checkBranchTemplate(TaskBranchTemplate template) throws ResourceException {
        ArrayList<String> ret = new ArrayList<>();
        if (!template.getUnlockConditionPairList().isEmpty()) {
            for (TaskBranchTemplate taskBranchTemplate : getResHolder().getListFromMap(TaskBranchTemplate.class)) {
                if (taskBranchTemplate.getFrontTask() == template.getId()) {
                    ret.add(StringUtils.format("支线任务的链首任务不允许被后置任务接取. errorBranchId:{}", template.getId()));
                }
            }
        }

        if (!ret.isEmpty()) {
            throw new ResourceException(ret.toString());
        }
        if (template.getRewardPairList() == null || template.getRewardPairList().size() <= 0) {
            throw new ResourceException(StringUtils.format("支线任务奖励不允许为空. taskId :{}", template.getTaskId()));
        }
    }

    public void ensureParam(CommonEnum.TaskType key, int paramSize, List<Integer> integers, String reason) throws ResourceException {
        // 参数可变的任务无需校验参数个数
        if (uncertainParamNumTask.contains(key)) {
            return;
        }
        if (integers.size() != paramSize) {
            throw new ResourceException(StringUtils.format("条件参数不匹配，任务表:{} 任务:{} 需要参数{}个，实际参数:{} 有{}个", reason, key, paramSize, integers, integers.size()));
        }
    }

    private void checkTaskParam(int taskId, String reason) throws ResourceException {
        TaskPoolTemplate taskPoolTemplate = getResHolder().getValueFromMap(TaskPoolTemplate.class, taskId);
        CommonEnum.TaskType taskType = CommonEnum.TaskType.forNumber(taskPoolTemplate.getTaskType().getNumber());
        if (taskType == null || taskType == CommonEnum.TaskType.TT_TASK_NONE) {
            // 临时屏蔽，防止启动失败
            throw new ResourceException(StringUtils.format("任务表配置了未实现的任务类型:{}. taskTable:{} taskId:{}", taskPoolTemplate.getTaskType(), reason, taskId));
        }
        int taskTypeParamCount = getTaskTypeParamCount(taskType);
        checkTaskType(taskType, reason);
        ensureParam(taskType, taskTypeParamCount, taskPoolTemplate.getTypeValueList(), reason);
    }

    private void checkDailyTemplate(TaskDailyTemplate value) throws ResourceException {
        if (StringUtils.isEmpty(value.getCityLevelRange())) {
            return;
        }
        String[] split = value.getCityLevelRange().split(Constants.XIA_HUA_XIAN);
        if (split.length < 2) {
            throw new ResourceException(StringUtils.format("错误的等级区间. TaskDailyTemplate cityLevelRange:{}", value.getCityLevelRange()));
        }
        if (Integer.parseInt(split[1]) < Integer.parseInt(split[0])) {
            throw new ResourceException(StringUtils.format("错误的等级区间. TaskDailyTemplate cityLevelRange:{}", value.getCityLevelRange()));
        }
        if (value.getRewardPairList() == null || value.getRewardPairList().size() <= 0) {
            throw new ResourceException(StringUtils.format("每日任务奖励不允许为空. taskId :{}", value.getTaskId()));
        }
    }

    private void checkChapterTemplate(TaskChapterTemplate value) throws ResourceException {
        if (value.getRewardPairList() == null || value.getRewardPairList().size() <= 0) {
            throw new ResourceException(StringUtils.format("章节任务奖励不允许为空. taskId :{}", value.getTaskId()));
        }
    }

    private void checkTaskId(int taskId, String reason) throws ResourceException {
        TaskPoolTemplate valueFromMap = getResHolder().findValueFromMap(TaskPoolTemplate.class, taskId);
        if (valueFromMap == null) {
            throw new ResourceException(StringUtils.format("{}任务，配置了任务池中不存在任务，taskId：{}", reason, taskId));
        }
    }

    private void checkTaskType(CommonEnum.TaskType taskType, String reason) throws ResourceException {
        if (taskType == null || taskType == CommonEnum.TaskType.TT_TASK_NONE) {
            throw new ResourceException(StringUtils.format("未实现的任务类型.taskTable:{} taskType:{}", reason, taskType));
        }
    }

    private int getTaskTypeParamCount(CommonEnum.TaskType taskType) {
        switch (taskType) {
            case TT_BUILDING_LEVEL:
            case TT_CONSTRUCT_BUILD_XX_COUNT:
            case TT_CONSTRUCT_BUILDID_XX_COUNT:
            case TT_TECHNOLOGY_REACHES_LEVEL_X:
            case TT_START_TRAINING_XX_ARMS_X_TIMES:
            case TT_FINISH_TRAINING_XX_ARMS_X_TIMES:
            case TT_FINISH_TRAINING_XX_ARMS_X_LEVEL_X_TIMES:
            case TT_MINING_XX_RESOURCES_REACH_XX:
            case TT_FINISH_TRAINING_XX_LEVEL_ARMS_XX_NUM:
            case TT_HAVE_X_UNITS:
            case TT_X_HERO_REACHES_X_STARS:
            case TT_OUTPUT_RATE_OF_X_RESOURCES:
            case TT_COLLECT_XX_RESOURCES_IN_THE_CITY_REACH_XX:
            case TT_AIR_FORCE_UPGRADE:
            case TT_OPEN_XX_TREASURE_CHESTS_XX_TIMES:
            case TT_USE_XX_ITEMTYPE_XX_TIMES:
            case TT_USE_ITEM_TASK:
            case TT_X_HERO_REACHES_X_LEVEL:
            case TT_NUMBER_OF_SOLDIERS_KILLED_IN_PVP:
            case TT_EXPLORE_X_NUMBER_OF_MAP_POINTS_TYPE_X_START:
            case TT_EXPLORE_X_NUMBER_OF_MAP_POINTS_TYPE_X_FIN:
            case TT_CONSUME_CURRENCY:
            case TT_TECH_START_RESEARCH_XX:
            case TT_ATTACK_X_OR_HIGHER_LEVEL_STRONGHOLD_X_TIMES:
            case TT_ATTACK_X_OR_HIGHER_LEVEL_CITY_X_TIMES:
            case TT_CHAPTER_BUILD_RH_BUILDING:
            case TT_X_UNIT_REACHES_X_LEVEL:
            case TT_X_TROOP_X_HERO: {
                return 2;
            }
            case ADD_X_POINT_TO_HERO_TALENT:
            case TT_COLLECT_ANY_RESOURCES_IN_THE_CITY_REACH:
            case TT_RESBUILDING_COLLECT_TIME:
            case TT_COMPLETE_XX_TECHNOLOGICAL_XX_TIMES:
            case TT_TREATMENT_SOLDIERS_X:
            case TT_HEAT_VALUE_TASK:
            case TT_HERO_POWER_INCREASE:
            case TT_TROOP_STRENGTH_UPGRADE:
            case TT_TECHNOLOGICAL_IMPROVEMENT:
            case TT_BUILDING_COMBAT_POWER:
            case TT_OVERALL_COMBAT_POWER_INCREASE:
            case TT_POWER_REACHES_X:
            case TT_BLACK_MARKET_REFRESH_X_COUNT:
            case TT_BLACKMARKET_COST_GOLD_X_TIMES:
            case TT_ALLIANCE_DONATED_X_TIMES:
            case TT_ALLIANCE_HELPED_X_TIMES:
            case TT_NUMBER_OF_AIR_FORCES_USED:
            case TT_NUMBER_OF_PLAYERS_ATTACKED_IN_PVP_X_TIMES:
            case TT_PVP_WINS_X_TIMES:
            case TT_PHYSICAL_EXERTION:
            case TT_OCCUPY_THE_STRONGHOLD_FOR_X_MINUTES:
            case TT_USE_ACCELERATE_ITEM_X_MIN:
            case TT_USE_BUILD_ACCELERATE_ITEM_X_MIN:
            case TT_USE_TRAIN_ACCELERATE_ITEM_X_MIN:
            case TT_USE_TECH_ACCELERATE_ITEM_X_MIN:
            case TT_EXPLORE_WORLD_CITY_NUM:
            case TT_ATTACK_STRONGHOLD_X_TIMES:
            case TT_OCCUPY_STRONGHOLD_X_TIMES:
            case TT_KILL_SPECIFIC_MONSTERS:
            case TT_DRAW_HEROES_X_TIMES:
            case TT_CHAPTER_TASK:
            case TT_DISPATCH_TROOP_XX_TIMES:
            case TT_LEVEL_BUILD_XX_COUNT:
            case TT_COMPLETE_X_HERO_UPGRADES:
            case TT_UPDATE_HERO_STAR_X_TIMES:
            case TT_NUMBER_OF_BLACK_MARKET_PURCHASES:
            case TT_SPY_PLANE_SURVEY_NUM:
            case TT_LOGIN_DAYS:
            case TT_UPDATE_HERO_SKILL_X_TIMES:
            case TT_PROSPERITY_X_MAX:
            case TT_EXPLORE_CITY_ANY_LEVEL_ONCE_FIN:
            case TT_EXPLORE_CITY_ANY_LEVEL_ONCE_START:
            case TT_KILL_BUILDING_GUARD:
            case TT_PICK_UP_BUFF:
            case TT_INC_BUILD_OR_TROOP_POWER:
            case TT_INC_TECH_OR_TROOP_POWER:
            case TT_ALLIANCE_HAS_X_COMMAND_CENTER:
            case TT_CHARGE_TIMES:
            case TT_CHARGE_GAIN_DIAMOND:
            case TT_INC_BUILD_TECH_TROOP_POWER:
            case TT_CLAN_TERRITORY_LV_REACH_X:
            case TT_DESTROY_X_TIMES_RALLY_MONSTER:
            case TT_HERO_INTENSIVE:
            case TT_RECRUIT_EPIC_HERO:
            case TT_RECRUIT_LEAGUE_HERO:
            case TT_BUILD_CLAN_FLAG:
            case TT_CLAN_OBTAINED_PRIVATE_SCORE:
            case TT_FINISH_DAILY_TASK:
            case TT_RESBUILDING_COLLECT_TIME_OUT:
            case TT_USE_BUILD_TRAIN_ACCELERATE_ITEM_X_MIN:
            case TT_USE_TECH_TRAIN_ACCELERATE_ITEM_X_MIN:
            case TT_INC_BUILD_OR_TRAIN_TROOP_POWER:
            case TT_INC_TECH_OR_TRAIN_TROOP_POWER:
            case TT_INC_TRAIN_TROOP_POWER:
            case TT_SKYNET_X_FINISH:
            case TT_SKYNET_X_REWARD:
            case TT_ALLIANCE_GET_GIFT_X_TIMES:
            case TT_CHAPTER_UNLOCK_UNIT:
            case TT_CHAPTER_COMPLETE_TASK:
            case TT_CHAPTER_COMPLETE_MISSION:
            case TT_WIN_X_DEFEND_BATTLE:
            case TT_BASE_EXPAND_TO_X: {
                return 1;
            }
            case TT_KILL_RALLY_MONSTERS_OF_A_CERTAIN_LEVEL_X_TIMES:
            case TT_KILL_MONSTERS_OF_A_CERTAIN_LEVEL_X_TIMES:
            case TT_FINISH_TRAINING_X_TYPE_X_LEVEL_ARMS_X_NUM:
            case TT_ATTACK_A_CERTAIN_LEVEL_OF_MONSTERS_X_TIMES: {
                return 3;
            }
            case TT_X_HEROES_WITH_X_RANK_X_GRADE_X_STAR: {
                return 4;
            }
            default: {
                return 0;
            }
        }
    }
}
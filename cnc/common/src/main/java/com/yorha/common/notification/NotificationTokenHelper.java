package com.yorha.common.notification;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Player;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 通知的Token工具类。
 * 会维持一个(playerId, Player.ScenePlayerPushNtfModel)的进程级别映射。
 *
 * <AUTHOR>
 */
public final class NotificationTokenHelper {
    public static final int NOTIFICATION_MASK_BIT_IS_INIT_OK = 63;
    /**
     * 无TOKEN的情况下客户端会设置，已经落到数据库里了，需要做兼容
     */
    public static final String NOTIFICATION_CLIENT_DEFAULT_TOKEN = "0";

    private static final int NOTIFICATION_MASK_SIZE = Long.SIZE;
    private static final ConcurrentHashMap<Long, Player.ScenePlayerPushNtfModel> MODEL_CACHE = new ConcurrentHashMap<>();

    private NotificationTokenHelper() {
    }

    /**
     * 客户端上传的token是否为无效token。
     *
     * @param token 上传token。
     * @return true or false。
     */
    public static boolean isUselessClientToken(final String token) {
        return StringUtils.isEmpty(token) || StringUtils.equals(token, NOTIFICATION_CLIENT_DEFAULT_TOKEN);
    }

    /**
     * 获取玩家推送相关的数据描述。(用于前置判断是否有token&推送时发给firebase)
     *
     * @param playerId 玩家id。
     * @return 推送相关的Token。
     */
    public static String getToken(final long playerId) {
        final Player.ScenePlayerPushNtfModel model = MODEL_CACHE.get(playerId);
        if (model == null) {
            return null;
        }
        final long nowTsMs = SystemClock.nowNative();
        final long deadLineTsMs = model.getIntlTokenRefreshTsMs() + TimeUnit.DAYS.toMillis(7);
        if (nowTsMs >= deadLineTsMs) {
            return null;
        }
        if (isUselessClientToken(model.getIntlNtfToken())) {
            return null;
        }
        return model.getIntlNtfToken();
    }

    /**
     * 获取玩家的推送语言
     * 默认返回英文
     *
     * @param playerId 玩家id。
     * @return 推送语言。（如未设置，使用英文）
     */
    public static CommonEnum.Language getLanguage(final long playerId) {
        final Player.ScenePlayerPushNtfModel model = MODEL_CACHE.get(playerId);
        if (model == null) {
            return CommonEnum.Language.en;
        }
        if (model.getLanguage() == CommonEnum.Language.L_NONE) {
            return CommonEnum.Language.en;
        }
        return model.getLanguage();
    }

    /**
     * 获取玩家的推送设置
     * 如果不存在返回-1
     *
     * @param playerId 玩家id
     * @return 推送设置
     */
    public static long getNotificationMask(final long playerId) {
        final Player.ScenePlayerPushNtfModel model = MODEL_CACHE.get(playerId);
        if (model == null) {
            return 0L;
        }
        return model.getNotificationMask();
    }

    public static long initNotificationMask() {
        return 1L << NOTIFICATION_MASK_BIT_IS_INIT_OK;
    }

    /**
     * 获取playerId对应的推送掩码，1表示可以推送，0表示不能推送。
     *
     * @param playerId 玩家id。
     * @param modelId  模块id。
     * @return 是否需要推送。
     */
    public static boolean isNeedNotification(final long playerId, final int modelId) {
        if (!isRightModelId(modelId)) {
            return false;
        }
        final Player.ScenePlayerPushNtfModel model = MODEL_CACHE.get(playerId);
        if (model == null) {
            return false;
        }
        return (model.getNotificationMask() & (1L << modelId)) != 0;
    }

    /**
     * 推送掩码是否符合要求。
     *
     * @param mask 掩码。
     * @return true 表示正确，false表示失败。
     */
    public static boolean isPushNtfMaskRight(final long mask) {
        final long initMask = 1L << NotificationTokenHelper.NOTIFICATION_MASK_BIT_IS_INIT_OK;
        return (mask & initMask) != 0;
    }

    /**
     * 根据之前的推送模块列表，重新获取一个推送掩码。
     *
     * @param openIdList  推送开启模块id列表。
     * @param closeIdList 推送关闭模块id列表
     * @return mask。
     */
    public static long getPushNtfMaskFromPrevModel(long mark, final List<Integer> openIdList, final List<Integer> closeIdList) {
        if (!isPushNtfMaskRight(mark)) {
            throw new GeminiException("prev mark not a right push mask");
        }
        for (final int modelId : openIdList) {
            if (!NotificationTokenHelper.isRightModelId(modelId)) {
                throw new GeminiException("index out of bound, index={}", modelId);
            }
            mark = mark | (1L << modelId);
        }
        for (final int modelId : closeIdList) {
            if (!NotificationTokenHelper.isRightModelId(modelId)) {
                throw new GeminiException("index out of bound, index={}", modelId);
            }
            mark = mark & (~(1L << modelId));
        }
        if (!isPushNtfMaskRight(mark)) {
            throw new GeminiException("update mark not a right push mask");
        }
        return mark;
    }

    /**
     * 推送的模块id是否正确。
     *
     * @param modelId 模块id。
     * @return true 正确，false 不正确。
     */
    public static boolean isRightModelId(final int modelId) {
        return (modelId >= 0 && modelId < NOTIFICATION_MASK_SIZE) && (modelId != NOTIFICATION_MASK_BIT_IS_INIT_OK);
    }

    /**
     * 更新推送相关的描述。
     *
     * @param playerId 玩家id。
     * @param model    推送相关的数据描述。
     * @return 未更新前的推送相关的数据描述。
     */
    public static Player.ScenePlayerPushNtfModel updateScenePlayerPushNtfModel(final long playerId, final Player.ScenePlayerPushNtfModel model) {
        if (model == null) {
            throw new GeminiException("player={} update model is null");
        }
        if (!isPushNtfMaskRight(model.getNotificationMask())) {
            throw new GeminiException("player={} update mask fail, mask not init", playerId);
        }
        return MODEL_CACHE.put(playerId, model);
    }

    /**
     * 移除玩家推送token（移民 or 进入kvk时调用）
     *
     * @param playerId
     */
    public static void removePushNtfModel(final long playerId) {
        MODEL_CACHE.remove(playerId);
    }
}

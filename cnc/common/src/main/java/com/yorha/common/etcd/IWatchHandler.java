package com.yorha.common.etcd;

import org.jetbrains.annotations.NotNull;

/**
 * etcd事件的回调。
 *
 * <AUTHOR>
 */
public interface IWatchHandler {
    /**
     * key被删除。
     *
     * @param key 目标key。
     */
    void onDelete(@NotNull String key);

    /**
     * key对应的内容更新。
     *
     * @param key   key。
     * @param value value。
     */
    void onUpdate(@NotNull String key, @NotNull String value);
}

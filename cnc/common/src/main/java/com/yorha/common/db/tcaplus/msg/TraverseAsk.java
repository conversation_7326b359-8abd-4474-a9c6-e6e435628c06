package com.yorha.common.db.tcaplus.msg;

import com.google.protobuf.Message;
import com.yorha.common.db.tcaplus.option.TraverseOption;
import com.yorha.common.db.tcaplus.result.TraverseResult;

/**
 * 遍历表所需要的请求。
 *
 * <AUTHOR>
 */
public class TraverseAsk<T extends Message.Builder> implements GameDbReq<TraverseResult> {
    private final T req;
    private final TraverseOption<T> option;

    public TraverseAsk(T req, TraverseOption<T> option) {
        this.req = req;
        this.option = option;
    }

    /**
     * @return 对应的请求id。
     */
    public T getReq() {
        return this.req;
    }

    /**
     * @return 遍历操作的选项。
     */
    public TraverseOption<T> getOption() {
        return option;
    }

    @Override
    public String toString() {
        return "TraverseAsk{" +
                "req=" + req +
                ", option=" + option +
                '}';
    }
}

package com.yorha.common.resource.resservice.player;

import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.*;

import java.util.*;

/**
 * vip相关配表数据的检查及配表数据的快捷获取接口
 *
 * <AUTHOR>
 */
public class VipResService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(VipResService.class);

    /**
     * vip等级 -> 配置
     */
    private final Map<Integer, VipCommonTemplate> lvToVipConfig = new HashMap<>();

    /**
     * 商品id -> vip等级
     */
    private final Map<Integer, Integer> exclusiveIdToLv = new HashMap<>();

    /**
     * vip box可自选qualityId集合
     */
    private final Set<Integer> vipBoxQualityIdSet = new HashSet<>();

    /**
     * vip box qualityId -> 最低可选的vip等级
     */
    private final Map<Integer, Integer> qualityIdToMinLv = new HashMap<>();

    /**
     * vip最大等级
     */
    private int maxVipLevel = 0;

    /**
     * vip等级对应的最大经验
     */
    private int maxExp = 0;

    public VipResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        parseDataMaps();
        parseMaxVipLevelAndExp();
    }

    @Override
    public void checkValid() throws ResourceException {
        checkConstConfig();
        checkVipCommonConfig();
        checkVipShopConfig();
    }

    //----------------------------- parse functions -------------------------------------- //

    public void parseDataMaps() {
        for (VipCommonTemplate template : getResHolder().getListFromMap(VipCommonTemplate.class)) {
            lvToVipConfig.put(template.getLevel(), template);
            exclusiveIdToLv.put(template.getExclusiveBoxId(), template.getLevel());
            vipBoxQualityIdSet.add(template.getQuality());
            if (qualityIdToMinLv.containsKey(template.getQuality())) {
                if (template.getLevel() < qualityIdToMinLv.get(template.getQuality())) {
                    qualityIdToMinLv.put(template.getQuality(), template.getLevel());
                }
            } else {
                qualityIdToMinLv.put(template.getQuality(), template.getLevel());
            }
        }
    }

    public void parseMaxVipLevelAndExp() {
        for (VipCommonTemplate template : getResHolder().getListFromMap(VipCommonTemplate.class)) {
            if (template.getLevel() > maxVipLevel) {
                maxVipLevel = template.getLevel();
            }
            if (template.getExp() > maxExp) {
                maxExp = template.getExp();
            }
        }
    }

    // ----------------------------- check functions -------------------------------------- //

    private void checkConstConfig() throws ResourceException {
        ConstVipTemplate template = getResHolder().getConstTemplate(ConstVipTemplate.class);
        if (template.getDailyRefreshTime() < 0) {
            throw new ResourceException("vip系统每日刷新时间非法, 当前值为 {}", template.getDailyRefreshTime());
        }
    }

    private void checkVipCommonConfig() throws ResourceException {
        Map<Integer, Integer> additionToValueMap = new HashMap<>();
        Set<Integer> additionSet = new HashSet<>();
        Set<Integer> levelSet = new HashSet<>();
        int lastLevel = -1;
        for (VipCommonTemplate template : getResHolder().getListFromMap(VipCommonTemplate.class)) {
            // 等级检查
            if (template.getLevel() < 0) {
                throw new ResourceException("vip等级非法, 当前id为 {}, 当前vip等级为 {}", template.getId(), template.getLevel());
            }
            if (template.getLevel() != ++lastLevel) {
                throw new ResourceException("vip等级必须为顺序逐步增加1, 当前id为 {}, 当前vip等级为 {}", template.getId(), template.getLevel());
            }
            if (levelSet.contains(template.getLevel())) {
                throw new ResourceException("vip等级重复, 当前id为 {}, 当前vip等级为 {}", template.getId(), template.getLevel());
            }
            levelSet.add(template.getLevel());
            // 经验检查
            if (template.getExp() < 0) {
                throw new ResourceException("vip经验非法, 当前id为 {}, 当前值为 {}", template.getId(), template.getExp());
            }
            // 尊享宝箱检查
            final int exclusiveBoxId = template.getExclusiveBoxId();
            if (exclusiveBoxId < 0) {
                throw new ResourceException("vip尊享宝箱id非法, 当前id为 {}, 宝箱商品id为 {}", template.getId(), template.getExclusiveBoxId());
            }
            if (getResHolder().findValueFromMap(ChargeGoodsTemplate.class, exclusiveBoxId) == null) {
                throw new ResourceException("vip尊享宝箱id没配置, 当前id为 {}, 宝箱商品id为 {}", template.getId(), template.getExclusiveBoxId());
            }
            // 加成检查
            for (IntPairType additionPair : template.getAdditionsPairList()) {
                int additionId = additionPair.getKey();
                if (additionSet.contains(additionId)) {
                    throw new ResourceException("同一等级同一加成列表中, 同一类加成应该只有一个配置, 当前等级 {}, 加成id {}", template.getId(), additionId);
                }
                additionSet.add(additionId);
                if (additionToValueMap.get(additionId) != null) {
                    int additionValue = additionToValueMap.get(additionPair.getKey());
                    if (additionValue > additionPair.getValue()) {
                        throw new ResourceException("vip增益值应该非递减, 当前等级值为 {}, 之前的对应等级值为 {}", additionPair.getValue(), additionValue);
                    }
                }
                additionToValueMap.put(additionPair.getKey(), additionPair.getValue());
            }
            // 加成set需要删除，否则会影响下一次的检查
            additionSet.clear();
            // 自选英雄品质检查
            int qualityId = template.getQuality();
            if (CommonEnum.HeroQuality.forNumber(qualityId) == null) {
                throw new ResourceException("vip每日礼包 等级={}, 英雄品质有误={}", template.getLevel(), qualityId);
            }

            // 自选英雄碎片个数检查
            int itemNum = template.getOptional();
            if (qualityId > 0 && itemNum <= 0) {
                throw new ResourceException("vip每日礼包 等级={}, 碎片数量有误={}", template.getLevel(), itemNum);
            }
        }
    }

    private void checkVipShopConfig() throws ResourceException {
        Set<Integer> vipGooodsIdSet = new HashSet<>();
        for (VipShopTemplate template : getResHolder().getListFromMap(VipShopTemplate.class)) {
            // 商品ID唯一
            int vipGoodsId = template.getId();
            if (vipGooodsIdSet.contains(template.getId())) {
                throw new ResourceException("vip商店 商品id={}, 重复商品ID", vipGoodsId);
            }
            vipGooodsIdSet.add(template.getId());

            // 限购数量非负
            if (template.getBuyLimit() <= 0) {
                throw new ResourceException("vip商店 商品id={}, 限购数量有误={}", vipGoodsId, template.getBuyLimit());
            }

            // 货币已知
            int currency = template.getPricePair().getKey();
            if (CommonEnum.CurrencyType.forNumber(currency) == null) {
                throw new ResourceException("vip商店 商品id={}, 货币有误={}", vipGoodsId, currency);
            }

            // 价格为正
            int price = template.getPricePair().getValue();
            if (price <= 0) {
                throw new ResourceException("vip商店 商品id={}, 价格有误={}", vipGoodsId, price);
            }

            if (template.getItemsPair() != null) {
                // 道具已知
                int itemId = template.getItemsPair().getKey();
                if (getResHolder().findValueFromMap(ItemTemplate.class, itemId) == null) {
                    throw new ResourceException("vip商店 商品id={}, 未知道具Id={}", vipGoodsId, itemId);
                }

                // 道具数量为正
                int itemNum = template.getItemsPair().getValue();
                if (itemNum <= 0) {
                    throw new ResourceException("vip商店 商品id={}, 道具数量有误={}", vipGoodsId, itemNum);
                }
            } else {
                int qualityId = template.getQuality();
                if (CommonEnum.HeroQuality.forNumber(qualityId) == null) {
                    throw new ResourceException("vip商店 商品id={}, 英雄品质有误={}", vipGoodsId, qualityId);
                }

                int itemNum = template.getOptional();
                if (itemNum <= 0) {
                    throw new ResourceException("vip商店 商店id={}, 碎片数量有误={}", vipGoodsId, itemNum);
                }
            }
        }
    }

    // ----------------------------- interface -------------------------------------- //

    /**
     * @param cumLoginDays 累计登录
     * @return 返回累计登录天数对应的vip经验
     */
    public int getExpByCumLoginDays(int cumLoginDays) {
        if (cumLoginDays < 0) {
            LOGGER.error("cumLoginDays {}, wrong, back zero", cumLoginDays);
            return 0;
        }
        ConstVipTemplate constTemplate = getResHolder().getConstTemplate(ConstVipTemplate.class);
        int baseExp = constTemplate.getVipExpBase();
        int exp = baseExp + (cumLoginDays - 1) * constTemplate.getDailyAddVipExp();
        return Math.min(exp, baseExp + constTemplate.getVipExpMax());
    }

    /**
     * @param exp vip经验
     * @return 返回vip经验对应的vip等级
     */
    public int getLevelByExp(int exp) {
        if (exp < 0) {
            LOGGER.error("getLevelByExp: vip exp {} wrong, back zero", exp);
            return 0;
        }
        for (VipCommonTemplate template : getResHolder().getListFromMap(VipCommonTemplate.class)) {
            if (exp < template.getExp()) {
                return template.getLevel() - 1;
            }
        }
        return maxVipLevel;
    }

    /**
     * copilot 生成
     *
     * @param before vip等级
     * @param after vip等级
     * @return 返回vip等级对应的增益列表
     * @throws GeminiException 可能返回的错误码为 {@link ErrorCode#VIP_CONFIG_NOT_EXIST}
     */
    public Set<Integer> getAdditionListByVipLevel(int before, int after) throws GeminiException {
        VipCommonTemplate beforeConfig = lvToVipConfig.get(before);
        if (beforeConfig == null) {
            throw new GeminiException(ErrorCode.VIP_CONFIG_NOT_EXIST);
        }
        VipCommonTemplate afterConfig = lvToVipConfig.get(after);
        if (afterConfig == null) {
            throw new GeminiException(ErrorCode.VIP_CONFIG_NOT_EXIST);
        }
        Set<Integer> additionList = new HashSet<>();
        for (IntPairType pairType : beforeConfig.getAdditionsPairList()) {
            additionList.add(pairType.getKey());
        }
        for (IntPairType pairType : afterConfig.getAdditionsPairList()) {
            additionList.add(pairType.getKey());
        }
        return additionList;
    }

    /**
     * copilot 生成
     *
     * @param vipLevel   vip等级
     * @param additionId 增益id
     * @return 返回vip等级对应的增益值，如果增益不存在或者vip等级不存在则返回0
     * @throws GeminiException 可能返回的错误码为 {@link ErrorCode#VIP_CONFIG_NOT_EXIST}
     */
    public long getAdditionValueByVipLevelAndId(int vipLevel, int additionId) throws GeminiException {
        VipCommonTemplate template = lvToVipConfig.get(vipLevel);
        if (template == null) {
            throw new GeminiException(ErrorCode.VIP_CONFIG_NOT_EXIST);
        }
        for (IntPairType pairType : template.getAdditionsPairList()) {
            if (pairType.getKey() == additionId) {
                return pairType.getValue();
            }
        }
        return 0;
    }

    public VipCommonTemplate getVipCommonTemplate(int vipLevel) {
        if (lvToVipConfig.containsKey(vipLevel)) {
            return lvToVipConfig.get(vipLevel);
        } else {
            throw new GeminiException(ErrorCode.VIP_CONFIG_NOT_EXIST);
        }
    }

    /**
     * 检查vip box的qualityId是否在配置中
     *
     * @param qualityId 品质id
     * @throws GeminiException 可能返回的错误码为 {@link ErrorCode#PARAM_PARAMETER_EXCEPTION}
     */
    public void checkVipBoxQualityId(int qualityId) throws GeminiException {
        if (!vipBoxQualityIdSet.contains(qualityId)) {
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
    }

    public int getVipBoxMinLv(int qualityId) {
        if (qualityIdToMinLv.containsKey(qualityId)) {
            return qualityIdToMinLv.get(qualityId);
        }
        return -1;
    }

    /**
     * @return 返回vip最大等级
     */
    public int getMaxVipLevel() {
        return maxVipLevel;
    }

    /**
     * @return 返回vip等级对应的最大经验
     */
    public int getMaxExp() {
        return maxExp;
    }

    /**
     * @param goodsId 商品id
     * @return 返回商品id对应的vip等级
     */
    public int getLvByGoodsId(int goodsId) {
        if (!exclusiveIdToLv.containsKey(goodsId)) {
            return -1;
        }
        return exclusiveIdToLv.get(goodsId);
    }
}

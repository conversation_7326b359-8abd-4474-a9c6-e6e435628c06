package com.yorha.common.asset;

import com.yorha.game.gen.prop.CurrencyProp;
import com.yorha.proto.CommonEnum;

public class CurrencyDesc extends AssetDesc {
    private final CommonEnum.CurrencyType currencyType;
    private final long num;

    protected CurrencyDesc(CommonEnum.CurrencyType type, long num) {
        super(AssetType.CURRENCY);
        this.currencyType = type;
        this.num = num;
    }

    @Override
    public int getId() {
        return currencyType.getNumber();
    }

    @Override
    public long getAmount() {
        return this.num;
    }

    @Override
    public AssetDesc plusImpl(AssetDesc another) {
        return new CurrencyDesc(this.currencyType, Math.addExact(this.num, another.getAmount()));
    }

    @Override
    public AssetDesc copyWithAmount(long amount) {
        return new CurrencyDesc(this.currencyType, amount);
    }

    public CommonEnum.CurrencyType getCurrencyType() {
        return currencyType;
    }

    public CurrencyProp toCurrencyProp() {
        return new CurrencyProp()
                .setType(currencyType.getNumber())
                .setCount(num);
    }

    @Override
    public String toString() {
        return "CurrencyDesc{" +
                "currencyType=" + currencyType +
                ", num=" + num +
                '}';
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof CurrencyDesc)) {
            return false;
        }

        CurrencyDesc that = (CurrencyDesc) o;
        if (num != that.num) {
            return false;
        }
        return currencyType == that.currencyType;
    }

    @Override
    public int hashCode() {
        int result = currencyType != null ? currencyType.hashCode() : 0;
        result = 31 * result + (int) (num ^ (num >>> 32));
        return result;
    }
}

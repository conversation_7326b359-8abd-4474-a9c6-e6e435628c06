package com.yorha.common.db.tcaplus.op;

import com.google.protobuf.Message;
import com.tencent.tcaplus.client.Client;
import com.tencent.tcaplus.client.Record;
import com.tencent.tcaplus.client.Request;
import com.tencent.tcaplus.client.Response;
import com.tencent.tdr.tcaplus_protocol_cs.TcaplusProtocolCsConstants;
import com.yorha.common.db.tcaplus.option.DeleteOption;
import com.yorha.common.db.tcaplus.result.DeleteResult;

/**
 * <AUTHOR>
 */
public class TcaplusDelete<T extends Message.Builder> extends TcaplusOperation<T, DeleteOption, DeleteResult> {
    public TcaplusDelete(Client client, T t, DeleteOption option) {
        super(client, PbFieldMetaCaches.getMetaData(t), t, option);
    }

    @Override
    protected int getType() {
        return TcaplusProtocolCsConstants.TCAPLUS_CMD_DELETE_REQ;
    }

    @Override
    protected void configRequestProperty(Request request) {
        Record record = request.addRecord();
        configRecordProperty(record);
        setRequestKeys(getReq(), record);
    }

    private void configRecordProperty(Record record) {
        // 设置版本
        if (getOption().getVersion() >= 0) {
            record.setVersion(getOption().getVersion());
        }
    }

    @Override
    protected DeleteResult buildResult(Response response) {
        return new DeleteResult(response.getResult());
    }
}

package com.yorha.common.server;

import com.yorha.common.server.config.ZoneSeasonInfo;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * Zone进程共享数据
 * <p>
 * 跨actor需要共享的数据，更新频率低，数据量小
 *
 * <AUTHOR>
 * @date 2024/6/18
 */
public class ZoneContext {
    private static final Logger LOGGER = LogManager.getLogger(ZoneContext.class);
    /**
     * 当前zone的开服时间
     */
    private static volatile long serverOpenTsMs = 0;
    /**
     * 赛季信息
     */
    private static final ZoneSeasonInfo ZONE_SEASON_INFO = new ZoneSeasonInfo();

    private ZoneContext() {
    }

    public static ZoneSeasonInfo getZoneSeasonInfo() {
        return ZoneContext.ZONE_SEASON_INFO;
    }

    /**
     * 调用之前必须先调用isServerOpen判断！
     */
    public static long getServerOpenTsMs() {
        if (ZoneContext.serverOpenTsMs == 0) {
            WechatLog.error("getServerOpenTsMs is not initialized");
        }
        return ZoneContext.serverOpenTsMs;
    }

    public static long getServerOpenDays() {
        if (!ZoneContext.isServerOpen()) {
            LOGGER.info("getServerOpenDays end server is not open={}", ZoneContext.getServerOpenTsMs());
            return 0;
        }
        return TimeUtils.getAbsNatureDaysBetween(ZoneContext.getServerOpenTsMs(), SystemClock.now());
    }

    public static boolean isServerOpen() {
        return 0 < ZoneContext.serverOpenTsMs && ZoneContext.serverOpenTsMs <= SystemClock.now();
    }

    /**
     * 危险！！！
     * 只允许 bigscene initBigWorldScene时设置初始时间
     * ZoneOpenComponent.openZone中允许更改开服时间
     * 设置进程级开服时间
     *
     * @param serverOpenTsMs 开服时间
     */
    public static void setServerOpenTsMs(long serverOpenTsMs) {
        ZoneContext.serverOpenTsMs = serverOpenTsMs;
        LOGGER.info("ZoneContext setServerOpenTsMs {}", ZoneContext.getServerOpenTsMs());
    }

}

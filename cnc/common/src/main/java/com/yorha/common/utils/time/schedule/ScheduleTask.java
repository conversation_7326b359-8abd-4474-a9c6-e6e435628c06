package com.yorha.common.utils.time.schedule;

import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.msg.ActorTimerMsg;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 定时任务
 *
 * <AUTHOR>
 */
public final class ScheduleTask {
    private static final Logger LOGGER = LogManager.getLogger(ScheduleTask.class);
    private ScheduleTask pre = null;
    private ScheduleTask next = null;
    /**
     * 定时器定义的id 只用于定时器触发根据name拿task时对比id判断是否是同一个task
     */
    private final int id;
    private final String name;
    /**
     * 归属的mgr
     */
    final ScheduleMgr scheduleMgr;
    /**
     * 需要投递到的actor ref
     */
    private final IActorRef actorRef;
    /**
     * 下次任务到期时间
     */
    private long nextExecuteTime;
    /**
     * 懒删除标记
     */
    private volatile boolean isCanceled;
    /**
     * 防止taskList的删除重入
     */
    private volatile boolean hasCanceled;
    /**
     * 间隔时间
     */
    private final long period;
    /**
     * 在时间轮中的索引，用于删除任务时使用
     */
    private int index;
    /**
     * 标志是否为固定时间定时器
     */
    private final boolean isFix;

    /**
     * package-private，通过ScheduleMgr接口构造
     *
     * @param scheduleMgr    归属的mgr
     * @param initialDelayMs 首次延迟时间，单位ms
     * @param periodMs       间隔时间，单位ms
     * @param isFix          固定时间定时器
     */
    ScheduleTask(ScheduleMgr scheduleMgr, IActorRef actorRef, int id, String name, long initialDelayMs, long periodMs, boolean isFix) {
        this.scheduleMgr = scheduleMgr;
        this.id = id;
        this.actorRef = actorRef;
        this.name = name;
        // 当前时间 + 延迟时间
        this.nextExecuteTime = SystemClock.now() + initialDelayMs;
        this.period = periodMs;
        this.isFix = isFix;
        // 外部使用时已经保护了，到这里还有period异常，保底会抛异常
        if (this.isFix && period <= 0) {
            throw new GeminiException("ScheduleTask isFix but param error name={} id={} period={}", name, id, period);
        }
        LOGGER.info("Schedule create repeat {} ScheduleTask {} {} initialDelay:{} ms,period:{} ms", isRepeat(), id, name, initialDelayMs, periodMs);
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public long getNextExecuteTime() {
        return nextExecuteTime;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public int getIndex() {
        return index;
    }

    /**
     * 懒删除
     */
    public void cancel() {
        if (isCanceled) {
            WechatLog.error("repeat_cancel ScheduleTask {}", this);
            return;
        }
        LOGGER.info("Schedule cancel isRepeat:{} task {} ", isRepeat(), this);
        this.isCanceled = true;
        this.scheduleMgr.markCancel(this);
    }

    public void setHasCanceled() {
        hasCanceled = true;
    }

    public boolean getHasCanceled() {
        return hasCanceled;
    }

    public void resetHasCanceled() {
        hasCanceled = false;
    }

    @Override
    public String toString() {
        return "ScheduleTask{" +
                "name=" + name +
                ", id=" + id +
                ", deadline=" + nextExecuteTime +
                ", index=" + index +
                ", isCanceled=" + isCanceled +
                '}';
    }

    /**
     * 时间到，执行
     *
     * @return true代表执行后重新添加到队列中，是repeat定时器
     */
    public boolean execute() {
        if (isCanceled()) {
            return false;
        }
        try {
            tryRun();
        } catch (Exception e) {
            LOGGER.error("ScheduleTask execute fail, catch ", e);
        }
        // 再判断一次，可能runnable执行的时候把自己取消了
        if (isCanceled()) {
            return false;
        }
        if (isRepeat()) {
            long now = SystemClock.now();
            this.nextExecuteTime = isFix ? calcFixNextExecuteTime(this.nextExecuteTime, now, this.period) : calcNextExecuteTime(now, this.period);
            return true;
        }
        return false;
    }

    private long calcNextExecuteTime(final long now, final long periodParam) {
        return now + periodParam;
    }

    private static long calcFixNextExecuteTime(final long curExecuteTime, final long now, final long periodParam) {
        long result = curExecuteTime + periodParam;
        if (result <= now) {
            long delay = now - curExecuteTime;
            result = curExecuteTime + (delay / periodParam + 1) * periodParam;
        }
        return result;
    }

    public void tryRun() {
        // 包装成消息给到actor，再真正run
        ActorTimerMsg actorTimerMsg = ActorTimerMsg.valueOf(getId(), getName());
        ActorSendMsgUtils.send(this.actorRef, actorTimerMsg);
    }

    public boolean isRepeat() {
        return this.period != 0;
    }

    public boolean isNotReady(long nowTime) {
        return this.nextExecuteTime > nowTime;
    }

    public boolean isCanceled() {
        return this.isCanceled;
    }

    public long getPeriod() {
        return this.period;
    }

    public void setPre(ScheduleTask pre) {
        this.pre = pre;
    }

    public void setNext(ScheduleTask next) {
        this.next = next;
    }

    public ScheduleTask getPre() {
        return this.pre;
    }

    public ScheduleTask getNext() {
        return this.next;
    }
}

package com.yorha.common.resource.resservice.rank;

import com.google.common.collect.Sets;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum.CurrencyType;
import com.yorha.proto.CommonEnum.RankScoreType;
import com.yorha.proto.CommonEnum.RankType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstRankTemplate;
import res.template.RankTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * some description
 *
 * <AUTHOR>
 */
public class RankDataTemplateService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(RankDataTemplateService.class);

    public RankDataTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    private Map<RankType, Integer> rankTypeToConfigRankId = new HashMap<>();

    private final Map<CurrencyType, Integer> currencyTypeToCoefficient = new HashMap<>();

    private final Map<CurrencyType, Integer> assistCurrencyTypeToCoefficient = new HashMap<>();

    /**
     * 数据的合法性检测
     *
     * @throws GeminiException 框架异常
     */
    @Override
    public void checkValid() throws ResourceException {
        this.checkTypeToCoefficient();
        checkNotNewType();
    }

    private static void checkNotNewType() throws ResourceException {
        // 不允许新增rankType了！！！
        // 为了大一统rankId和rankType，但是又废弃不掉RankType（因为老功能在用），所以后面新功能统一使用rankId来做
        Set<RankType> rankType = Sets.newHashSet(
                RankType.RAT_NONE,
                RankType.RT_CLAN_POWER,
                RankType.RT_CLAN_KILL,
                RankType.RT_CLAN_FLAG,
                RankType.RT_PLAYER_POWER,
                RankType.RT_PLAYER_KILL,
                RankType.RT_PLAYER_CITY_LEVEL,
                RankType.RT_PLAYER_COLLECT_LEVEL,
                RankType.RT_CLAN_PLAYER_POWER,
                RankType.RT_CLAN_PLAYER_KILL,
                RankType.RT_CLAN_PLAYER_TECHNOLOGY_DONATION,
                RankType.RT_CLAN_PLAYER_BUILDING,
                RankType.RT_CLAN_PLAYER_HELP,
                RankType.RT_CLAN_PLAYER_RESOURCE_ASSISTANCE,
                RankType.RT_CLAN_PLAYER_TECHNOLOGY_DONATION_DAILY,
                RankType.RT_ACTIVITY_POWER_MAX,
                RankType.RT_ZLCB
        );
        for (RankType value : RankType.values()) {
            if (!rankType.contains(value)) {
                throw new ResourceException(StringUtils.format("不允许新增RankType={}！！！！", value));
            }
        }
    }

    private void checkTypeToCoefficient() throws ResourceException {
        ConstRankTemplate constRankTemplate = getResHolder().getConstTemplate(ConstRankTemplate.class);
        for (IntPairType pair : constRankTemplate.getCollectPoint()) {
            if (CurrencyType.forNumber(pair.getKey()) == null) {
                throw new ResourceException("排行榜常量表中采集积分计算系数的类型 {} 不存在, 请参考CurrencyType: " + pair.getKey());
            }
            if (pair.getValue() < 0) {
                throw new ResourceException("排行榜常量表中采集积分计算系统不可以为负数");
            }
        }
        for (IntPairType pair : constRankTemplate.getResourcePoint()) {
            if (CurrencyType.forNumber(pair.getKey()) == null) {
                throw new ResourceException("排行榜常量表中资源援助积分计算系数的类型 {} 不存在, 请参考CurrencyType: " + pair.getKey());
            }
            if (pair.getValue() < 0) {
                throw new ResourceException("排行榜常量表中资源援助积分计算系统不可以为负数");
            }
        }
        for (RankTemplate template : getResHolder().getListFromMap(RankTemplate.class)) {
            if (template.getMaxRank() == 0) {
                throw new ResourceException("排行榜的最大容量不能为0 配置id: {} " + template.getId());
            }

            this.checkRankScoreType(template, RankScoreType.RST_NONE);
        }
    }

    private void checkRankScoreType(final RankTemplate rankTemplate, final RankScoreType wantScoreType) throws ResourceException {
        if (rankTemplate.getRankScoreType() == wantScoreType) {
            return;
        }
        throw new ResourceException("排行榜={}, 排行榜类型={}, 需要配置排行榜分数类型为={}, 目前不匹配, 请确认!", rankTemplate.getId(), rankTemplate.getType().name(), wantScoreType.name());
    }

    /**
     * 数据加载
     *
     * @throws GeminiException 框架异常
     */
    @Override
    public void load() throws ResourceException {
        loadConfigRankIdToRankType();
        loadCollectTypeToCoefficient();
        loadAssistTypeToCoefficient();
    }

    public int getCoefficientByCurrencyType(CurrencyType currencyType) {
        return currencyTypeToCoefficient.getOrDefault(currencyType, 0);
    }

    public int getAssistCoefficientByCurrencyType(CurrencyType currencyType) {
        return assistCurrencyTypeToCoefficient.getOrDefault(currencyType, 0);
    }

    private void loadConfigRankIdToRankType() {
        Map<RankType, Integer> tmpRankTypeToConfigRankId = new HashMap<>();
        for (RankTemplate template : getResHolder().getListFromMap(RankTemplate.class)) {
            RankType rankType = template.getType();
            if (rankType != null && rankType != RankType.RAT_NONE) {
                tmpRankTypeToConfigRankId.put(rankType, template.getId());
            }
        }
        rankTypeToConfigRankId = tmpRankTypeToConfigRankId;
    }

    private void loadCollectTypeToCoefficient() {
        ConstRankTemplate constRankTemplate = getResHolder().getConstTemplate(ConstRankTemplate.class);
        for (IntPairType pair : constRankTemplate.getCollectPoint()) {
            currencyTypeToCoefficient.put(CurrencyType.forNumber(pair.getKey()), pair.getValue());
        }
    }

    private void loadAssistTypeToCoefficient() {
        ConstRankTemplate constRankTemplate = getResHolder().getConstTemplate(ConstRankTemplate.class);
        for (IntPairType pair : constRankTemplate.getResourcePoint()) {
            assistCurrencyTypeToCoefficient.put(CurrencyType.forNumber(pair.getKey()), pair.getValue());
        }
    }

    public int getRankIdByRankTypeImpl(RankType rankType) {
        if (rankTypeToConfigRankId.containsKey(rankType)) {
            return rankTypeToConfigRankId.get(rankType);
        } else {
            LOGGER.warn("rankType not exist. rankType:{}", rankType);
            return 0;
        }
    }

    public static RankTemplate findRankTemplate(int rankId) {
        return ResHolder.getInstance().findValueFromMap(RankTemplate.class, rankId);
    }

    public static int getRankIdByRankType(RankType rankType) {
        return ResHolder.getResService(RankDataTemplateService.class).getRankIdByRankTypeImpl(rankType);
    }
}

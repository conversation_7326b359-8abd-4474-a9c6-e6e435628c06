package com.yorha.common.helper;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.io.MsgType;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstKVResService;
import com.yorha.common.utils.UnitConvertUtils;
import com.yorha.common.utils.shape.Point;
import com.yorha.game.gen.prop.ClanFlagInfoProp;
import com.yorha.game.gen.prop.DisplayDataProp;
import com.yorha.proto.*;
import com.yorha.proto.CommonEnum.DisplayParamType;
import com.yorha.proto.Core.Code;
import com.yorha.proto.PlayerScene.Player_MarqueeMessage_NTF;
import com.yorha.proto.PlayerScene.Player_PlayDialog_NTF;
import com.yorha.proto.StructPB.DisplayDataPB;

import java.util.HashMap;
import java.util.Map;

/**
 * msg快捷构造器
 *
 * <AUTHOR>
 */
public class MsgHelper {


    private static final Map<Class<? extends GeneratedMessageV3>, Integer> CS_MSG_CLAZZ_2_TYPE = new HashMap<>();

    static {
        MsgType.getMsgId2ProtoMsgMap().forEach((type, msg) -> CS_MSG_CLAZZ_2_TYPE.put(msg.getClass(), type));
    }

    public static int getMsgTypeByClazz(Class<? extends GeneratedMessageV3> clazz) {
        return CS_MSG_CLAZZ_2_TYPE.getOrDefault(clazz, 0);
    }

    /**
     * 通用错误码消息
     */
    public static Player_PlayDialog_NTF buildErrorMsg(Code code) {
        Player_PlayDialog_NTF.Builder builder = Player_PlayDialog_NTF.newBuilder();
        // 通用error tip show id
        int playShowId = ResHolder.getResService(ConstKVResService.class).getTemplate().getErrorTipShowId();
        builder.setId(playShowId);
        builder.getParamsBuilder().setErrorCode(code.getId());
        return builder.build();
    }

    /**
     * 通用错误码消息
     */
    public static Player_PlayDialog_NTF buildErrorMsg(int codeId) {
        Player_PlayDialog_NTF.Builder builder = Player_PlayDialog_NTF.newBuilder();
        // 通用error tip show id
        int playShowId = ResHolder.getResService(ConstKVResService.class).getTemplate().getErrorTipShowId();
        builder.setId(playShowId);
        builder.getParamsBuilder().setErrorCode(codeId);
        return builder.build();
    }

    /**
     * 客户端效果消息
     */
    public static Player_PlayDialog_NTF buildBuildingClientShowMsg(int clientShowId) {
        return Player_PlayDialog_NTF.newBuilder().setId(clientShowId).build();
    }

    public static Point transPoint(Struct.Point p) {
        return Point.valueOf(p.getX(), p.getY());
    }

    public static Struct.Point transPoint(Point p) {
        return Struct.Point.newBuilder().setX(p.getX()).setY(p.getY()).build();
    }

    public static Player_PlayDialog_NTF buildClientShowMsg(int playShowId, long entityId, int templateId) {
        Player_PlayDialog_NTF.Builder builder = Player_PlayDialog_NTF.newBuilder();
        builder.setId(playShowId);
        builder.getParamsBuilder().setEntityId(entityId).setTemplateId(templateId);
        return builder.build();
    }

    public static Player_MarqueeMessage_NTF buildMarqueeMsg(int marqueeId, Struct.DisplayData displayData) {
        DisplayDataProp displayDataProp = new DisplayDataProp();
        displayDataProp.mergeChangeFromSs(displayData);
        StructPB.DisplayDataPB disPlayDataPb = displayDataProp.getCopyCsBuilder().build();
        return buildMarqueeMsg(marqueeId, disPlayDataPb);
    }

    public static Player_MarqueeMessage_NTF buildMarqueeMsg(int marqueeId, DisplayDataPB displayData) {
        return Player_MarqueeMessage_NTF.newBuilder().setMessageId(marqueeId).setDisplayData(displayData).build();
    }

    public static Player_MarqueeMessage_NTF buildMarqueeMsg(int marqueeId, DisplayDataPB displayData, StructClanPB.ClanFlagInfoPB flagInfo) {
        Player_MarqueeMessage_NTF.Builder builder = Player_MarqueeMessage_NTF.newBuilder();
        if (flagInfo != null) {
            builder.setClanFlag(flagInfo);
        }
        return builder.setMessageId(marqueeId).setDisplayData(displayData).build();
    }

    public static Struct.DisplayData buildRallyCancelMarqueeMsg(String name) {
        Struct.DisplayData.Builder builder = Struct.DisplayData.newBuilder();
        builder.getParamsBuilder().addDatas(buildDisPlayText(name));
        return builder.build();
    }

    public static StructPB.DisplayDataPB buildRallyCancelMarqueePbMsg(String name) {
        StructPB.DisplayDataPB.Builder builder = StructPB.DisplayDataPB.newBuilder();
        builder.getParamsBuilder().addDatas(buildDisPlayTextPb(name));
        return builder.build();
    }

    /**
     * nameActor相关快捷构造
     */
    public static SsName.NamePair buildNamePair(CommonEnum.NameType nameType, String name) {
        return SsName.NamePair.newBuilder().setNameType(nameType).setName(name).build();
    }
    //--------------------------------------  特殊参数类型构造  --------------------------------------

    /**
     * 文本类型
     */
    public static Struct.DisplayParam buildDisPlayText(String text) {
        Struct.DisplayParam.Builder builder = Struct.DisplayParam.newBuilder();
        builder.setType(DisplayParamType.DPT_TEXT).setText(text);
        return builder.build();
    }

    /**
     * 文本类型
     */
    public static StructPB.DisplayParamPB buildDisPlayTextPb(String text) {
        StructPB.DisplayParamPB.Builder builder = StructPB.DisplayParamPB.newBuilder();
        builder.setType(DisplayParamType.DPT_TEXT).setText(text);
        return builder.build();
    }

    /**
     * 文本类型
     */
    public static Struct.DisplayParam buildDisPlayMultiLangKey(String text) {
        Struct.DisplayParam.Builder builder = Struct.DisplayParam.newBuilder();
        builder.setType(DisplayParamType.DPT_MULTI_LANG_KEY).setText(text);
        return builder.build();
    }

    /**
     * 文本类型
     */
    public static StructPB.DisplayParamPB buildDisPlayMultiLangKeyPb(String text) {
        StructPB.DisplayParamPB.Builder builder = StructPB.DisplayParamPB.newBuilder();
        builder.setType(DisplayParamType.DPT_MULTI_LANG_KEY).setText(text);
        return builder.build();
    }

    /**
     * 坐标类型
     */
    public static Struct.DisplayParam buildDisPlayPoint(int x, int y, final CommonEnum.MapType mapType, final long mapId) {
        Struct.DisplayParam.Builder builder = Struct.DisplayParam.newBuilder();
        int xx = UnitConvertUtils.cmToDecameter(x);
        int yy = UnitConvertUtils.cmToDecameter(y);
        builder.setType(DisplayParamType.DPT_POINT)
                .getPositionBuilder()
                .setX(xx)
                .setY(yy)
                .setMapType(mapType.getNumber())
                .setMapId(mapId);
        return builder.build();
    }

    /**
     * 坐标类型
     */
    public static StructPB.DisplayParamPB buildDisPlayPointPb(int x, int y, final CommonEnum.MapType mapType, final long mapId) {
        StructPB.DisplayParamPB.Builder builder = StructPB.DisplayParamPB.newBuilder();
        int xx = UnitConvertUtils.cmToDecameter(x);
        int yy = UnitConvertUtils.cmToDecameter(y);
        builder.setType(DisplayParamType.DPT_POINT)
                .getPositionBuilder()
                .setX(xx)
                .setY(yy)
                .setMapType(mapType.getNumber())
                .setMapId(mapId);
        return builder.build();
    }

    /**
     * 坐标类型
     */
    public static StructPB.DisplayParamPB buildDisPlayPointPb(Struct.Point point) {
        StructPB.DisplayParamPB.Builder builder = StructPB.DisplayParamPB.newBuilder();
        int xx = UnitConvertUtils.cmToDecameter(point.getX());
        int yy = UnitConvertUtils.cmToDecameter(point.getY());
        builder.setType(DisplayParamType.DPT_POINT)
                .getPositionBuilder()
                .setX(xx)
                .setY(yy)
                .setMapType(point.getMapType())
                .setMapId(point.getMapId());
        return builder.build();
    }

    /**
     * 坐标类型
     */
    public static Struct.DisplayParam buildDisPlayPoint(Point p, final CommonEnum.MapType mapType, final long mapId) {
        Struct.DisplayParam.Builder builder = Struct.DisplayParam.newBuilder();
        int xx = UnitConvertUtils.cmToDecameter(p.getX());
        int yy = UnitConvertUtils.cmToDecameter(p.getY());
        builder.setType(DisplayParamType.DPT_POINT)
                .getPositionBuilder()
                .setX(xx)
                .setY(yy)
                .setMapType(mapType.getNumber())
                .setMapId(mapId);
        return builder.build();
    }

    /**
     * 坐标类型
     */
    public static StructPB.DisplayParamPB buildDisPlayPointPb(Point p, final CommonEnum.MapType mapType, final long mapId) {
        StructPB.DisplayParamPB.Builder builder = StructPB.DisplayParamPB.newBuilder();
        int xx = UnitConvertUtils.cmToDecameter(p.getX());
        int yy = UnitConvertUtils.cmToDecameter(p.getY());
        builder.setType(DisplayParamType.DPT_POINT)
                .getPositionBuilder()
                .setX(xx)
                .setY(yy)
                .setMapType(mapType.getNumber())
                .setMapId(mapId);
        return builder.build();
    }

    /**
     * GOTO坐标类型
     *
     * @param x 横坐标
     * @param y 竖坐标
     */
    public static Struct.DisplayParam buildGotoDisplayPoint(int x, int y, final CommonEnum.MapType mapType, final long mapId) {
        Struct.DisplayParam.Builder builder = Struct.DisplayParam.newBuilder();
        int xx = UnitConvertUtils.cmToDecameter(x);
        int yy = UnitConvertUtils.cmToDecameter(y);
        builder.setType(DisplayParamType.DPT_GOTO_POINT)
                .getPositionBuilder()
                .setX(xx)
                .setY(yy)
                .setMapType(mapType.getNumber())
                .setMapId(mapId);
        return builder.build();
    }

    /**
     * GOTO坐标类型
     *
     * @param point 坐标
     */
    public static Struct.DisplayParam buildGotoDisplayPoint(Struct.Point point) {
        Struct.DisplayParam.Builder builder = Struct.DisplayParam.newBuilder();
        int xx = UnitConvertUtils.cmToDecameter(point.getX());
        int yy = UnitConvertUtils.cmToDecameter(point.getY());
        builder.setType(DisplayParamType.DPT_GOTO_POINT)
                .getPositionBuilder()
                .setX(xx)
                .setY(yy)
                .setMapType(point.getMapType())
                .setMapId(point.getMapId());
        return builder.build();
    }

    /**
     * GOTO坐标类型
     *
     * @param p 点信息
     */
    public static Struct.DisplayParam buildGotoDisplayPoint(Point p, final CommonEnum.MapType mapType, final long mapId) {
        Struct.DisplayParam.Builder builder = Struct.DisplayParam.newBuilder();
        int xx = UnitConvertUtils.cmToDecameter(p.getX());
        int yy = UnitConvertUtils.cmToDecameter(p.getY());
        builder.setType(DisplayParamType.DPT_GOTO_POINT)
                .getPositionBuilder()
                .setX(xx)
                .setY(yy)
                .setMapType(mapType.getNumber())
                .setMapId(mapId);
        return builder.build();
    }

    /**
     * 各种数字id类型
     */
    public static Struct.DisplayParam buildDisPlayId(DisplayParamType idType, long id) {
        Struct.DisplayParam.Builder builder = Struct.DisplayParam.newBuilder();
        builder.setType(idType).setNumber(id);
        return builder.build();
    }

    /**
     * 各种数字类型
     */
    public static StructPB.DisplayParamPB buildDisPlayIdPb(DisplayParamType idType, long id) {
        StructPB.DisplayParamPB.Builder builder = StructPB.DisplayParamPB.newBuilder();
        builder.setType(idType).setNumber(id);
        return builder.build();
    }

    public static CommonMsg.ActorRefData buildRefMsg(IActorRef ref) {
        return CommonMsg.ActorRefData.newBuilder()
                .setActorId(ref.getActorId())
                .setActorRole(ref.getActorRole())
                .setBusId(ref.getBusId())
                .build();
    }

    public static Struct.DisplayParam buildDisPlayHero(int heroId) {
        Struct.DisplayParam.Builder builder = Struct.DisplayParam.newBuilder();
        builder.setType(DisplayParamType.DPT_HERO_ID).setNumber(heroId);
        return builder.build();
    }

    // ----------------------------- ss -> pb --------------------------------

    public static StructPB.DisplayDataPB getDisplayDataPB(Struct.DisplayData data) {
        DisplayDataProp displayDataProp = new DisplayDataProp();
        displayDataProp.mergeFromSs(data);
        return displayDataProp.getCopyCsBuilder().build();
    }

    public static StructClanPB.ClanFlagInfoPB getClanFlagInfoPB(StructClan.ClanFlagInfo data) {
        ClanFlagInfoProp prop = new ClanFlagInfoProp();
        prop.mergeChangeFromSs(data);
        return prop.getCopyCsBuilder().build();
    }

}

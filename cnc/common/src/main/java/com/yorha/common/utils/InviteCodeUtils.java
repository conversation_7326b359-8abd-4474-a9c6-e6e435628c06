package com.yorha.common.utils;

import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.db.tcaplus.msg.InsertAsk;
import com.yorha.common.db.tcaplus.msg.SelectUniqueAsk;
import com.yorha.common.db.tcaplus.result.GetResult;
import com.yorha.common.db.tcaplus.result.InsertResult;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;

public class InviteCodeUtils {
    private static final Logger LOGGER = LogManager.getLogger(InviteCodeUtils.class);

    private static final String INVITE_CODE_STR = "AUVWXYZ123456789";

    private static final Integer INVITE_CODE_PRIME_NUMBER = 8388593;

    private static final Integer INVITE_CODE_SALT = 13;

    private static final long INVITE_NUM_MAX = 1L << 40;

    private static final int INVITE_CODE_LENGTH = 10;

    private static final int INVITE_BORN_ZONE_ID_MAX = 16384;

    private static final int INVITE_BORN_ZONE_ID_MIN = 0;

    private static final int INVITE_BORN_INDEX_MAX = 65536;

    private static final int INVITE_BORN_INDEX_MIN = 0;

    private static final int INVITE_ACTIVITY_INDEX_MAX = 1024;

    private static final int INVITE_ACTIVITY_INDEX_MIN = 0;

    public static String genAndGetInviteCode(GameActorWithCall playerActor, long playerId, int bornZoneId, int bornIndex, int activityIndex) throws GeminiException {
        // 生成邀请码
        String inviteCode = genInviteCode(bornZoneId, bornIndex, activityIndex);
        // 错误处理
        if (null == inviteCode) {
            LOGGER.error("genAndGetInviteCode failed, playerId={}, bornZoneId={}, bornIndex={}, activityIndex={}",
                    playerId, bornZoneId, bornIndex, activityIndex);
            throw new GeminiException(ErrorCode.INVITE_CODE_GEN_FAILED);
        }
        // 插入邀请码
        insertInviteCode(playerActor, inviteCode, playerId, activityIndex);
        return inviteCode;
    }

    /**
     * 生成邀请码
     *
     * @param bornZoneId    玩家出生区id [0, 16384) 2^14
     * @param bornIndex     玩家出生区顺序 [0, 65536) 2^16
     * @param activityIndex 活动id [0, 1024) 2^10
     * @return 返回本次活动玩家对应的唯一id，如果生成失败会返回null
     */
    @Nullable
    private static String genInviteCode(int bornZoneId, int bornIndex, int activityIndex) {
        if (bornZoneId < INVITE_BORN_ZONE_ID_MIN || bornZoneId >= INVITE_BORN_ZONE_ID_MAX) {
            LOGGER.error("genInviteCode failed, bornZoneId={} not in range", bornZoneId);
            return null;
        }
        if (bornIndex < INVITE_BORN_INDEX_MIN || bornIndex >= INVITE_BORN_INDEX_MAX) {
            LOGGER.error("genInviteCode failed, bornIndex={} not in range", bornIndex);
            return null;
        }
        if (activityIndex < INVITE_ACTIVITY_INDEX_MIN || activityIndex >= INVITE_ACTIVITY_INDEX_MAX) {
            LOGGER.error("genInviteCode failed, activityIndex={} not in range", activityIndex);
            return null;
        }
        long inviteNum = genInviteNum(bornZoneId, bornIndex, activityIndex);
        long hashedInviteNum = hashInviteNum(inviteNum);
        return transInviteNumToInviteCode(hashedInviteNum);
    }

    /**
     * @param bornZoneId    玩家出生区id [0, 16384) 2^14
     * @param bornIndex     玩家出生区顺序 [0, 65536) 2^16
     * @param activityIndex 活动id [0, 1024) 2^10
     * @return 拼接出生区id，出生顺序和活动id，生成一个最多40位1的long型数，从高位到低位依次是[活动id, 出生区id, 出生顺序]
     */
    private static long genInviteNum(int bornZoneId, int bornIndex, int activityIndex) {
        long genId = 0L;
        genId |= (long) (bornIndex & 0xFFFF);
        genId |= (long) (bornZoneId & 0x3FFF) << 16;
        genId |= (long) (activityIndex & 0x3FF) << 30;
        return genId;
    }

    /**
     * @param inviteNum 生成的邀请数字
     * @return 返回散列后的邀请码
     */
    private static long hashInviteNum(long inviteNum) {
        return (inviteNum * INVITE_CODE_PRIME_NUMBER + INVITE_CODE_SALT) % INVITE_NUM_MAX;
    }

    /**
     * @param hashedInviteNum 散列后的邀请数字
     * @return 将散列后的邀请数字转换为邀请字符串
     */
    private static String transInviteNumToInviteCode(long hashedInviteNum) {
        StringBuilder inviteCode = new StringBuilder();
        final long INVITE_CODE_STR_LENGTH = INVITE_CODE_STR.length();
        for (int i = 0; i < INVITE_CODE_LENGTH; i++) {
            inviteCode.append(INVITE_CODE_STR.charAt((int) (hashedInviteNum % INVITE_CODE_STR_LENGTH)));
            hashedInviteNum = hashedInviteNum / INVITE_CODE_STR_LENGTH;
        }
        return inviteCode.toString();
    }

    /**
     * 将邀请码 -> 玩家id的映射插入数据库中
     *
     * @param playerActor playerActor
     * @param inviteCode  邀请码
     * @param playerId    玩家id
     * @param actId       活动id
     * @throws GeminiException
     */
    private static void insertInviteCode(GameActorWithCall playerActor, String inviteCode, long playerId, int actId) throws GeminiException {
        final CommonMsg.ActInvitationMsg.Builder actInvitationMsgBuilder = CommonMsg.ActInvitationMsg.newBuilder().setPlayerId(playerId);
        final CommonMsg.ExtraActCodeInfo.Builder extraCodeGenInfoBuilder = CommonMsg.ExtraActCodeInfo.newBuilder().setActInvitationMsg(actInvitationMsgBuilder);
        final TcaplusDb.ActInviteCodeInfoTable.Builder insertReq = TcaplusDb.ActInviteCodeInfoTable.newBuilder()
                .setInviteCode(inviteCode)
                .setActId(actId)
                .setValue(extraCodeGenInfoBuilder);
        final InsertResult<TcaplusDb.ActInviteCodeInfoTable.Builder> insertResult = insertInviteCode(playerActor, insertReq);
        // 插入成功，直接return
        if (insertResult.isOk()) {
            LOGGER.info("insertInviteCode success, inviteCode={}, playerId={}, actId={}", inviteCode, playerId, actId);
            return;
        }
        // 插入失败
        WechatLog.error("insertInviteCode failed, inviteCode={}, playerId={}, result={}", inviteCode, playerId, insertResult);
        throw new GeminiException(ErrorCode.INVITE_CODE_GEN_FAILED);
    }

    private static InsertResult<TcaplusDb.ActInviteCodeInfoTable.Builder> insertInviteCode(
            GameActorWithCall playerActor,
            final TcaplusDb.ActInviteCodeInfoTable.Builder insertReq
    ) {
        try {
            return playerActor.callGameDb(new InsertAsk<>(insertReq));
        } catch (Exception e) {
            throw new GeminiException("insertInviteCode fail, e=", e);
        }
    }

    private static GetResult<TcaplusDb.ActInviteCodeInfoTable.Builder> getInviteCode(
            GameActorWithCall playerActor,
            final TcaplusDb.ActInviteCodeInfoTable.Builder queryReq
    ) {
        try {
            return playerActor.callGameDb(new SelectUniqueAsk<>(queryReq));
        } catch (Exception e) {
            throw new GeminiException("getInviteCode fail, e=", e);
        }
    }

    /**
     * @param playerActor playeActo
     * @param inviteCode  邀请码
     * @param actId       活动id
     * @return 根据邀请码查询玩家id
     * @throws GeminiException
     */
    public static long reqPlayerIdWithInviteCode(GameActorWithCall playerActor, String inviteCode, int actId) throws GeminiException {
        final TcaplusDb.ActInviteCodeInfoTable.Builder queryReq = TcaplusDb.ActInviteCodeInfoTable.newBuilder()
                .setActId(actId)
                .setInviteCode(inviteCode);
        final GetResult<TcaplusDb.ActInviteCodeInfoTable.Builder> queryResult = getInviteCode(playerActor, queryReq);
        // 查询成功，直接return
        if (queryResult.isOk()) {
            return queryResult.value.getValue().getActInvitationMsg().getPlayerId();
        } else if (queryResult.isNotExist()) {
            // 邀请码不存在
            throw new GeminiException(ErrorCode.LASHIN_ILLEGAL_INVITE_CODE);
        } else {
            LOGGER.error("reqPlayerIdWithInviteCode failed, inviteCode={}, result={}", inviteCode, queryResult);
            throw new GeminiException(ErrorCode.FAILED, "reqPlayerIdWithInviteCode failed");
        }
    }


    public static StringBuilder gmTestInviteCode(int bornZoneId, int bornIndexBegin, int bornIndexEnd, int activityIndex) throws GeminiException {
        if (bornIndexEnd < bornIndexBegin) {
            LOGGER.error("gmTestInviteCode failed, bornIndexEnd={} < bornIndexBegin={}", bornIndexEnd, bornIndexBegin);
            throw new GeminiException(ErrorCode.PARAM_PARAMETER_EXCEPTION);
        }
        StringBuilder sb = new StringBuilder();
        int genCount = 0;
        for (int bornIndex = bornIndexBegin; bornIndex <= bornIndexEnd; bornIndex++) {
            String inviteCode = genInviteCode(bornZoneId, bornIndex, activityIndex);
            if (null == inviteCode) {
                LOGGER.error("gmTestInviteCode failed, bornZoneId={}, bornIndex={}, activityIndex={}", bornZoneId, bornIndex, activityIndex);
            }
            ++genCount;
            sb.append(inviteCode).append("\n");
        }
        sb.append("genCount=").append(genCount);
        LOGGER.info("gmTestInviteCode success, bornZoneId={}, bornIndexBegin={}, bornIndexEnd={}, activityIndex={}",
                bornZoneId, bornIndexBegin, bornIndexEnd, activityIndex);
        LOGGER.warn("gmTestInviteCode result=\n{}", sb.toString());
        return sb;
    }

}

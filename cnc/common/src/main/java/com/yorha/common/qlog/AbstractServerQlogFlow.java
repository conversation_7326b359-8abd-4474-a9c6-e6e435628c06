package com.yorha.common.qlog;

/**
 * 以服务器为单位派发流水
 *
 * <AUTHOR>
 */
public abstract class AbstractServerQlogFlow extends AbstractQlogFlow {
    /**
     * 服务器类型
     */
    private String ServerType;
    /**
     * 服务器id
     */
    private int IZoneAreaID;
    /**
     * 服务器开放时间
     */
    private String ServerOpenTime;
    /**
     * 开服小时数
     */
    private long ServerOpenTimeFar;
    /**
     * 服务器在线人数
     */
    private int ServerOnline;
    /**
     * 服务器状态
     */
    private String ServerCondition;
    /**
     * 服务器里程碑任务阶段
     */
    private int ServerMilestoneStage;
    /**
     * accountId
     */
    private String accountId;

    protected AbstractServerQlogFlow() {
        ServerType = "";
        IZoneAreaID = 0;
        ServerOpenTime = "";
        ServerCondition = "";
        ServerMilestoneStage = 0;
        accountId = "";
    }

    private void setServerType(String serverType) {
        this.ServerType = serverType;
    }

    private void setIZoneAreaID(int iZoneAreaID) {
        this.IZoneAreaID = iZoneAreaID;
    }

    private void setServerOpenTime(String serverOpenTime) {
        this.ServerOpenTime = serverOpenTime;
    }

    private void setServerOpenTimeFar(long serverOpenTimeFar) {
        this.ServerOpenTimeFar = serverOpenTimeFar;
    }

    private void setServerOnline(int serverOnline) {
        this.ServerOnline = serverOnline;
    }

    private void setServerCondition(String serverCondition) {
        this.ServerCondition = serverCondition;
    }

    private void setServerMilestoneStage(int serverMilestoneStage) {
        this.ServerMilestoneStage = serverMilestoneStage;
    }

    private void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    /**
     * 初始化公共头部
     */
    private void fillFullHead(QlogServerFlowInterface serverFlow) {
        setServerType(serverFlow.getServerType());
        setIZoneAreaID(serverFlow.getIZoneAreaID());
        setServerOpenTime(serverFlow.getServerOpenTime());
        setServerOpenTimeFar(serverFlow.getServerOpenTimeFar());
        setServerOnline(serverFlow.getServerOnline());
        setServerCondition(serverFlow.getServerCondition());
        setServerMilestoneStage(serverFlow.getServerMilestoneStage());
        setAccountId(serverFlow.getAccountId());
    }

    private void fillSimpleHead(QlogServerFlowInterface serverFlow) {
        setServerType(serverFlow.getServerType());
        setIZoneAreaID(serverFlow.getIZoneAreaID());
        setServerOpenTime(serverFlow.getServerOpenTime());
        setAccountId(serverFlow.getAccountId());
        setServerOpenTimeFar(0);
        setServerOnline(0);
        setServerCondition(serverFlow.getServerCondition());
        setServerMilestoneStage(0);
    }

    public void fillHead(QlogServerFlowInterface serverFlow) {
        if (needFullHead()) {
            fillFullHead(serverFlow);
            return;
        }
        fillSimpleHead(serverFlow);
    }

    protected boolean needFullHead() {
        return true;
    }

    /**
     * 填充头部字段
     *
     * @param builder
     */
    @Override
    protected void addFlowHeadContent(StringBuilder builder) {
        builder.append("|").append(ServerType)
                .append("|").append(IZoneAreaID)
                .append("|").append(ServerOpenTime)
                .append("|").append(ServerOpenTimeFar)
                .append("|").append(ServerOnline)
                .append("|").append(ServerCondition)
                .append("|").append(ServerMilestoneStage)
                .append("|").append(accountId);
    }
}

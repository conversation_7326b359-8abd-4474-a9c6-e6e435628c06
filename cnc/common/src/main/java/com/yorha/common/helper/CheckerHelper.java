package com.yorha.common.helper;

import com.yorha.common.enums.reason.SoldierNumChangeReason;
import com.yorha.proto.CommonEnum;

public class CheckerHelper {

    public static boolean isTrain(CommonEnum.QueueTaskType taskType) {
        if (taskType == CommonEnum.QueueTaskType.SOLDIER_ARMORED_CAR_TRAINING) {
            return true;
        }
        if (taskType == CommonEnum.QueueTaskType.SOLDIER_FOOT_TRAINING) {
            return true;
        }
        if (taskType == CommonEnum.QueueTaskType.SOLDIER_TANK_TRAINING) {
            return true;
        }
        if (taskType == CommonEnum.QueueTaskType.SOLDIER_ARTILLERY_TRAINING) {
            return true;
        }
        return false;
    }

    public static boolean isUpdatePowerByTrainOrLevelUp(Integer soldierNumChangeReason) {
        if (soldierNumChangeReason == null) {
            return false;
        }
        return soldierNumChangeReason == SoldierNumChangeReason.train_army.getId()
                || soldierNumChangeReason == SoldierNumChangeReason.update_army.getId();
    }
}

package com.yorha.common.helper;

import com.google.common.collect.Maps;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.battle.SkillDataTemplateService;
import com.yorha.common.resource.resservice.hero.HeroTemplateService;
import com.yorha.common.utils.FormulaUtils;
import com.yorha.common.utils.MathUtils;
import com.yorha.game.gen.prop.PlayerHeroProp;
import com.yorha.game.gen.prop.PlayerHeroSkillProp;
import com.yorha.game.gen.prop.PlayerHeroTalentPageProp;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.Struct;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.*;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class HeroHelper {
    private static final Logger LOGGER = LogManager.getLogger(HeroHelper.class);

    public static int getHeroPower(PlayerHeroProp heroProp) {
        HeroTemplateService resService = ResHolder.getResService(HeroTemplateService.class);
        // 等级战力
        LOGGER.warn("hero id:{} level:{} getHeroPower", heroProp.getHeroId(), heroProp.getLevel());
        int levelPower = resService.getHeroLevelTemplate(heroProp.getHeroId(), heroProp.getLevel()).getPower();
        // 星级战力
        int starPower = resService.getHeroStarTemplate(heroProp.getStar()).getPower();
        //技能战力
        int skillPower = 0;
        Collection<PlayerHeroSkillProp> skillProps = heroProp.getSkillStore().values();
        for (PlayerHeroSkillProp e : skillProps) {
            HeroSkillLevelRhTemplate template = resService.getHeroSkillLevelTemplate(e.getSkillGroupId(), e.getSkillLevel());
            if (template != null) {
                skillPower += template.getPower();
            } else {
                LOGGER.warn("技能升级模板不存在:{},{}", e.getSkillGroupId(), e.getSkillLevel());
            }

        }
        return starPower + levelPower + skillPower;
    }

    public static int getHeroPower(Struct.Hero hero) {
        HeroTemplateService resService = ResHolder.getResService(HeroTemplateService.class);
        // 等级战力
        int levelPower = resService.getHeroLevelTemplate(hero.getHeroId(), hero.getLevel()).getPower();
        // 星级战力
        int starPower = resService.getHeroStarTemplate(hero.getStar()).getPower();
        int skillPower = 0;
        List<Struct.SimpleSkill> list = hero.getSkills().getDatasList();
        for (Struct.SimpleSkill e : list) {
            HeroSkillLevelRhTemplate template = resService.getHeroSkillLevelTemplate(e.getSkillGroupId(), e.getLevel());
            if (template != null) {
                skillPower += template.getPower();
            } else {
                LOGGER.warn("技能升级模板不存在:{},{}", e.getSkillGroupId(), e.getLevel());
            }

        }
        //    int talentPower = hero.getTalentIds().getDatasList().stream().mapToInt(HeroHelper::getTalentPower).sum();
        return starPower + levelPower + skillPower;
    }


    public static boolean isUsedStatus(PlayerHeroTalentPageProp prop) {
        return prop.getTalentPageState() == CommonEnum.talentPateState.TPS_USED;
    }


    public static long getHeroAddition(Struct.Hero hero, CommonEnum.BuffEffectType type, boolean ignoreTalent) {
//        List<Integer> skillIdList = hero.getSkillIds().getDatasList();
        long ret = 0;
        // 技能
//        for (int skillId : skillIdList) {
//            SkillTemplate skillTemplate = ResHolder.getResService(SkillDataTemplateService.class).getSkillTemplate(skillId);
//            CommonEnum.SkillType skillType = CommonEnum.SkillType.forNumber(skillTemplate.getType());
//            if (skillType == CommonEnum.SkillType.ST_PASSIVE) {
//                ret += getEffectAddition(skillTemplate.getGroupSkillList()).getOrDefault(type, 0L);
//            }
//        }
        if (!ignoreTalent) {
            List<Integer> talentIdList = hero.getTalentIds().getDatasList();
            // 天赋
            for (int talentId : talentIdList) {
                TalentTemplate talentTemplate = ResHolder.getInstance().findValueFromMap(TalentTemplate.class, talentId);
                ret += getEffectAddition(talentTemplate.getEffectList()).getOrDefault(type, 0L);
            }
        }

        return ret;
    }

    /**
     * 获取英雄被动增益
     */
    public static Map<CommonEnum.BuffEffectType, Long> getEffectAddition(List<Integer> effectIds) {
        Map<CommonEnum.BuffEffectType, Long> additionMap = Maps.newHashMap();

        for (Integer effectId : effectIds) {
            SkillEffectTemplate effectTemplate = ResHolder.getResService(SkillDataTemplateService.class).getSkillEffectTemplate(effectId);
            if (effectTemplate.getType() == CommonEnum.SkillEffectType.SET_ADD_ADDITION
                    && effectTemplate.getTrigger() == CommonEnum.TriggerType.TT_NONE) {
                for (IntPairType intPairType : effectTemplate.getAdditionGroupPairList()) {
                    CommonEnum.BuffEffectType buffEffectType = CommonEnum.BuffEffectType.forNumber(intPairType.getKey());
                    additionMap.put(buffEffectType, additionMap.getOrDefault(buffEffectType, 0L) + intPairType.getValue());
                }
            }
        }

        return additionMap;
    }

    /**
     * 获取英雄带兵量
     */
    public static long getHeroTroops(int level, int star, boolean isMainHero) {
        long capacity = 0;
        if (isMainHero) {
            // 等级带兵量
            HeroLevelRhTemplate heroLevelTemplate = ResHolder.getInstance().getValueFromMap(HeroLevelRhTemplate.class, level);
            capacity += heroLevelTemplate.getRtsTroopCapacity();

            // 星级带兵量
   //         HeroStarRhTemplate heroStarTemplate = ResHolder.getResService(HeroTemplateService.class).getHeroStarTemplate(star);
//            capacity += heroStar.getCapacity();
        }
        return capacity;
    }

    /**
     * 获取带兵量，目前仅英雄 日落峡谷只需要英雄，以后加了模块的话需要把英雄拆出来
     *
     * @param cardinality 配置提供的基数
     */
    public static long getSoldierMaxNum(int cardinality, Struct.Hero mainHero, Struct.Hero deputyHero, Map<CommonEnum.BuffEffectType, Long> additionMap) {
        long soldierMaxNum;

        // 主将提供出兵量
        long mainHeroMaxNum = 0L;
        // 副将提供出兵量
        long deputyHeroMaxNum = 0L;
        // 增幅百分比
        long buffGainPercentage = 0L;
        long base;
        if (mainHero != null && mainHero.getHeroId() > 0) {
            mainHeroMaxNum = HeroHelper.getHeroTroops(mainHero.getLevel(), mainHero.getStar(), true);
            buffGainPercentage += HeroHelper.getHeroAddition(mainHero, CommonEnum.BuffEffectType.ET_TROOP_CAPACITY_PERCENT, false);
        }
        if (deputyHero != null && deputyHero.getHeroId() > 0) {
            deputyHeroMaxNum = HeroHelper.getHeroTroops(deputyHero.getLevel(), deputyHero.getStar(), false);
            buffGainPercentage += HeroHelper.getHeroAddition(deputyHero, CommonEnum.BuffEffectType.ET_TROOP_CAPACITY_PERCENT, true);
        }
        // 英雄基础最大量
        base = MathUtils.addExact(mainHeroMaxNum, deputyHeroMaxNum);
        // 外围提供的基数
        base = MathUtils.addExact(base, cardinality);


        if (additionMap.containsKey(CommonEnum.BuffEffectType.ET_TROOP_CAP_FIXED)) {
            base = MathUtils.addExact(base, additionMap.get(CommonEnum.BuffEffectType.ET_TROOP_CAP_FIXED));
        }
        // 属性增幅
        if (additionMap.containsKey(CommonEnum.BuffEffectType.ET_TROOP_CAPACITY_PERCENT)) {
            buffGainPercentage += additionMap.get(CommonEnum.BuffEffectType.ET_TROOP_CAPACITY_PERCENT);
        }

        soldierMaxNum = FormulaUtils.f1(base, 0, buffGainPercentage, 1);
        LOGGER.info("troop max capacity:{}. main hero capacity:{}. deputy hero capacity:{}. building capacity:{}. buff gain percentage:{}",
                soldierMaxNum, mainHeroMaxNum, deputyHeroMaxNum, base, buffGainPercentage);
        return soldierMaxNum;
    }

}

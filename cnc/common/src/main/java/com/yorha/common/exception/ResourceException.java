package com.yorha.common.exception;


import com.yorha.gemini.utils.StringUtils;

/**
 * 策划资源错误
 *
 */
public class ResourceException extends Exception {

    public ResourceException(String msg) {
        super(msg);
    }

    public ResourceException(String msgPattern, Object... arguments) {
        this(StringUtils.format(msgPattern, arguments));
    }

    public ResourceException(String msg, Throwable t) {
        super(msg, t);
    }
}

package com.yorha.common.resource.resservice.campaign;

import com.google.common.collect.Maps;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.gemini.utils.StringUtils;
import res.template.CampaignMissionLevelTemplate;

import java.util.HashMap;

public class CampaignMissionLevelResService extends AbstractResService {

    /**
     * 战役关卡配置 战役难度等级->关卡等级->template
     */
    private final HashMap<Integer, HashMap<Integer, CampaignMissionLevelTemplate>> missionLevelMap = new HashMap<>();

    private final HashMap<Integer, Integer> missionMaxLevelMap = new HashMap<>();

    public CampaignMissionLevelResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        // 刷新地图
        refreshCampaignMissionLevelTemplate();
    }

    @Override
    public void checkValid() throws ResourceException {

    }

    private void refreshCampaignMissionLevelTemplate() {
        for (CampaignMissionLevelTemplate template : getResHolder().getListFromMap(CampaignMissionLevelTemplate.class)) {
            HashMap<Integer, CampaignMissionLevelTemplate> levelMap = missionLevelMap.computeIfAbsent(template.getCampaignDifficultyId(), (key) -> Maps.newHashMap());
            levelMap.put(template.getTaskDifficultyId(), template);

            if (missionMaxLevelMap.getOrDefault(template.getCampaignDifficultyId(), 0) < template.getTaskDifficultyId()) {
                missionMaxLevelMap.put(template.getCampaignDifficultyId(), template.getTaskDifficultyId());
            }
        }
    }

    public CampaignMissionLevelTemplate getMissionLevel(int campaignLevel, int missionLevel) {
        HashMap<Integer, CampaignMissionLevelTemplate> levelMap = missionLevelMap.get(campaignLevel);
        if (levelMap == null) {
            throw new GeminiException(StringUtils.format("战役任务等级配置错误（campaign_mission_level）. campaignDifficultyId:{}, taskDifficultyId:{} 不存在", campaignLevel, missionLevel));
        }
        CampaignMissionLevelTemplate template = levelMap.get(missionLevel);
        if (template == null) {
            throw new GeminiException(StringUtils.format("战役任务等级配置错误（campaign_mission_level）. campaignDifficultyId:{}, taskDifficultyId:{} 不存在", campaignLevel, missionLevel));
        }
        return template;
    }

}
package com.yorha.common.resource.resservice.city;

import com.google.common.collect.Sets;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.datatype.IntTripleType;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.RandomUtils;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum.MapAreaType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.jetbrains.annotations.Unmodifiable;
import res.template.*;

import java.util.*;

/**
 * 迁城、导量相关
 *
 * <AUTHOR>
 */
public class CityBornService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(CityBornService.class);
    private final Map<MapAreaType, ErrorCode> limitArea = new HashMap<>();

    /**
     * zoneId-> template
     */
    private final Map<Integer, BornAreaLimitTemplate> bornAreaLimit = new HashMap<>();

    /**
     * 出生优先区域  根据优先级排好序的配置 同一个优先级在一个list里
     */
    private final List<List<BornAreaSelectTemplate>> priorityAreaList = new ArrayList<>();
    /**
     * 出生州表 阶段  州梯度   stage-> (num,regionList)
     */
    private final Map<Integer, List<Pair<Integer, List<Integer>>>> regionGradient = new HashMap<>();
    /**
     * 最大迁城阻挡半径   搜索时取自己的+最大的
     */
    private int maxBuildMoveRange;
    /**
     * 村庄迁城半径
     */
    private int townsCityMoveRadius = 0;
    /**
     * 导量可选项  区域类型，区域等级 -> 配置id
     */
    private final Map<Pair<MapAreaType, Integer>, Integer> areaSelect = new HashMap<>();

    /**
     * key=stage val={key=driveHardwareLevel, val=List<Pair<regionId, limitNum>>}
     */
    private final Map<Integer, Map<Integer, List<Pair<Integer, Integer>>>> driveHardwareLevelToRegion = new HashMap<>();


    public CityBornService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        // 导量限制表
        Collection<LimitMoveCityTemplate> configs = getResHolder().getListFromMap(LimitMoveCityTemplate.class);
        for (LimitMoveCityTemplate config : configs) {
            limitArea.put(config.getAreaType(), ErrorCode.valueOf(config.getModule().toUpperCase() + "_" + config.getName().toUpperCase()));
        }

        // 构建州导量梯度
        getResHolder().getListFromMap(BornRegionSelectTemplate.class).forEach(it -> {
            regionGradient.put(it.getId(), new ArrayList<>());
            List<IntPairType> playerNumPairList = new ArrayList<>();
            List<Integer> regionList = new ArrayList<>();
            for (IntPairType item : it.getPlayerNumPairList()) {
                regionList.add(item.getKey());
                playerNumPairList.add(item);
            }
            playerNumPairList.sort(Comparator.comparingInt(IntPairType::getValue));
            List<Pair<Integer, List<Integer>>> gradient = regionGradient.get(it.getId());
            int num = 0;
            int last = 0;
            for (IntPairType item : playerNumPairList) {
                if (last == item.getValue()) {
                    regionList.remove((Integer) item.getKey());
                    continue;
                }
                num += regionList.size() * (item.getValue() - last);
                gradient.add(Pair.of(num, new ArrayList<>(regionList)));
                regionList.remove((Integer) item.getKey());
                last = item.getValue();
            }
            // 构造当前stage下导量机型对应的各州人数限制
            driveHardwareLevelToRegion.put(it.getId(), new HashMap<>());
            Map<Integer, List<Pair<Integer, Integer>>> curStage = driveHardwareLevelToRegion.get(it.getId());
            for (IntTripleType tripleType : it.getTypeNumTripleList()) {
                int regionId = tripleType.getKey();
                int driveHardwareLevel = tripleType.getValue1();
                int limit = tripleType.getValue2();
                if (!curStage.containsKey(driveHardwareLevel)) {
                    curStage.put(driveHardwareLevel, new LinkedList<>());
                }
                curStage.get(driveHardwareLevel).add(Pair.of(regionId, limit));
            }
        });

        // 区域导量优先级
        TreeMap<Integer, List<BornAreaSelectTemplate>> priorityArea = new TreeMap<>();
        getResHolder().getListFromMap(BornAreaSelectTemplate.class).forEach(it -> {
            if (!priorityArea.containsKey(it.getPriority())) {
                priorityArea.put(it.getPriority(), new ArrayList<>());
            }
            priorityArea.get(it.getPriority()).add(it);
            areaSelect.put(Pair.of(it.getAreaType(), it.getAreaLevel()), it.getId());
        });

        while (!priorityArea.isEmpty()) {
            priorityAreaList.add(priorityArea.firstEntry().getValue());
            priorityArea.remove(priorityArea.firstKey());
        }

        // 计算最大的迁城搜索范围
        for (MapBuildingTemplate template : getResHolder().getListFromMap(MapBuildingTemplate.class)) {
            if (template.getCollisionRadius() > maxBuildMoveRange) {
                maxBuildMoveRange = template.getCollisionRadius();
            }
        }

        loadBornAreaTemplate();
    }

    private void loadBornAreaTemplate() {
        for (BornAreaLimitTemplate template : getResHolder().getListFromMap(BornAreaLimitTemplate.class)) {
            bornAreaLimit.put(template.getStateId(), template);
        }
    }

    @Unmodifiable
    @NonNull
    public Map<Integer, List<Pair<Integer, Integer>>> getDriveTypeNum(int stage) {
        return Collections.unmodifiableMap(this.driveHardwareLevelToRegion.getOrDefault(stage, Collections.emptyMap()));
    }

    public Map<Pair<MapAreaType, Integer>, Integer> getAreaSelect() {
        return areaSelect;
    }

    public BornAreaSelectTemplate getBornAreaSelectTemplate(MapAreaType type, int level) {
        Pair<MapAreaType, Integer> key = Pair.of(type, level);
        if (areaSelect.containsKey(key)) {
            return getResHolder().getValueFromMap(BornAreaSelectTemplate.class, areaSelect.get(key));
        }
        return null;
    }

    /**
     * 州导量人数上限表
     */
    public BornAreaLimitTemplate getBornAreaLimitTemplate(int regionId) {
        return bornAreaLimit.get(regionId);
    }

    /**
     * 检查区域类型能否落城
     */
    public ErrorCode getAreaBornCode(MapAreaType areaType) {
        return limitArea.getOrDefault(areaType, ErrorCode.OK);
    }

    /**
     * 获取服务器导量阶段
     */
    public int getStage(int playerNum) {
        Collection<BornRegionSelectTemplate> list = getResHolder().getListFromMap(BornRegionSelectTemplate.class);
        for (BornRegionSelectTemplate config : list) {
            if (playerNum >= config.getRangeStart() && playerNum <= config.getRangeEnd()) {
                LOGGER.info("atZoneDrive CityBornService getStage curStage={}, playerNum={}", config.getId(), playerNum);
                return config.getId();
            }
        }
        return 0;
    }


    /**
     * 获取根据playerNum导量的州
     *
     * @param bornStage 导量阶段
     * @param playerNo  第n个玩家
     */
    public int getBornRegion(int bornStage, int playerNo) {
        LOGGER.info("atZoneDrive CityBornService getBornRegion cur bornStage={}, playerNo={}", bornStage, playerNo);
        List<Pair<Integer, List<Integer>>> numToRegion = regionGradient.get(bornStage);
        if (numToRegion == null) {
            LOGGER.info("atZoneDrive CityBornService getBornRegion no numToRegion");
            return -1;
        }
        playerNo = playerNo - getResHolder().getValueFromMap(BornRegionSelectTemplate.class, bornStage).getRangeStart() + 1;
        if (playerNo < 0) {
            LOGGER.error("atZoneDrive CityBornService getBornRegion error playerNo={} < 0", playerNo);
            return -1;
        }
        int last = 0;
        int result = -1;
        for (Pair<Integer, List<Integer>> item : numToRegion) {
            if (playerNo > item.getFirst()) {
                last = item.getFirst();
            } else {
                List<Integer> regionList = new ArrayList<>(item.getSecond());
                if (regionList.isEmpty()) {
                    LOGGER.error("atZoneDrive CityBornService getBornRegion regionList empty, item first={}", item.getFirst());
                    result = -1;
                } else {
                    // Found the appropriate region, calculate and return the result
                    int regionIndex = (playerNo - last) % regionList.size();
                    result = regionList.get(regionIndex);
                }
                break;
            }
        }

        if (result == -1) {
            LOGGER.error("atZoneDrive CityBornService getBornRegion not found bornRegion");
        }
        return result;
    }

    public List<BornAreaSelectTemplate> getPriorityAreaList() {
        List<BornAreaSelectTemplate> ret = new ArrayList<>();
        for (int i = 0; i < priorityAreaList.size(); i++) {
            List<BornAreaSelectTemplate> list = priorityAreaList.get(i % priorityAreaList.size());
            int index2 = RandomUtils.nextInt(list.size());
            for (int j = index2; j < index2 + list.size(); j++) {
                ret.add(list.get(j % list.size()));
            }
        }
        return ret;
    }

    public int getMaxBuildMoveRange() {
        return maxBuildMoveRange;
    }

    public int getTownsCityMoveRadius() {
        return townsCityMoveRadius;
    }

    @Override
    public void checkValid() throws ResourceException {
        // 统计每个州的片 check 州上限够不够
        // 每个州的可导量人数
        Map<Integer, Integer> regionCanBornNum = new HashMap<>();
        getResHolder().getMap(RegionalSettingMap1004Template.class).values().forEach(it -> {
            Pair<MapAreaType, Integer> key = Pair.of(it.getAreaType(), it.getLevel());
            if (areaSelect.containsKey(key)) {
                BornAreaSelectTemplate template = getResHolder().getValueFromMap(BornAreaSelectTemplate.class, areaSelect.get(key));
                regionCanBornNum.put(it.getRegionId(), regionCanBornNum.getOrDefault(it.getRegionId(), 0) + template.getLimit());
            }
        });
        for (BornAreaLimitTemplate template : bornAreaLimit.values()) {
            Integer canBorn = regionCanBornNum.getOrDefault(template.getStateId(), 0);
            if (template.getNumLimit() > canBorn) {
                throw new ResourceException("导量州人数限制错误  片上限只能支持: {}  当前限制: {}  州id: {}", canBorn, template.getNumLimit(), template.getStateId());
            }
        }
        // 导量州配置上限
        Set<Integer> bornAreaZone = Sets.newHashSet();
        for (BornAreaLimitTemplate template : getResHolder().getListFromMap(BornAreaLimitTemplate.class)) {
            if (bornAreaZone.contains(template.getStateId())) {
                throw new ResourceException("城池导量表 born_area_limit 出现重复州id: {}", template.getStateId());
            }
            bornAreaZone.add(template.getStateId());
        }
        for (int i = 0; i <= 5; i++) {
            if (!bornAreaZone.contains(i)) {
                throw new ResourceException("城池导量表 born_area_limit 遗漏州id配置: {}", i);
            }
        }
        int lastTypeNumTotal = 0;
        for (BornRegionSelectTemplate data : getResHolder().getListFromMap(BornRegionSelectTemplate.class)) {
            if (CollectionUtils.isEmpty(data.getPlayerNumPairList())) {
                throw new ResourceException(StringUtils.format("城池导量表 born_region_select id: {} 未配置playerNum", data.getId()));
            }
            if (CollectionUtils.isEmpty(data.getTypeNumTripleList())) {
                throw new ResourceException(StringUtils.format("城池导量表 born_region_select id: {} 未配置typeNum", data.getId()));
            }
            int typeNumTotal = data.getTypeNumTripleList().stream().mapToInt(IntTripleType::getValue2).sum();
            if (typeNumTotal > data.getRangeEnd()) {
                throw new ResourceException(StringUtils.format("城池导量表 born_region_select id: {} typeNum配置人数超出人数范围", data.getId()));
            }
            if (lastTypeNumTotal > typeNumTotal) {
                throw new ResourceException(StringUtils.format("城池导量表 born_region_select id: {} typeNum配置人数总和小于上次", data.getId()));
            }
            lastTypeNumTotal = typeNumTotal;
            if (data.getTypeNumTripleList().stream().anyMatch(intTripleType -> (!bornAreaZone.contains(intTripleType.getValue1())))) {
                throw new ResourceException(StringUtils.format("城池导量表 born_region_select id: {} typeNum含非法州id", data.getId()));
            }

            int playerNumTotal = data.getPlayerNumPairList().stream().mapToInt(IntPairType::getValue).sum();
            if (playerNumTotal != data.getRangeEnd() - data.getRangeStart() + 1) {
                throw new ResourceException(StringUtils.format("城池导量表 born_region_select id: {} playerNum配置人数不等于本阶段人数范围", data.getId()));
            }
            if (data.getPlayerNumPairList().stream().anyMatch(intPairType -> (!bornAreaZone.contains(intPairType.getKey())))) {
                throw new ResourceException(StringUtils.format("城池导量表 born_region_select id: {} playerNum含非法州id", data.getId()));
            }
        }
    }
}

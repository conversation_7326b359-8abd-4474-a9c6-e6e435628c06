package com.yorha.common.actorservice;

import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.cluster.toplogy.Cluster;
import com.yorha.common.actor.cluster.toplogy.Node;
import com.yorha.common.actor.mailbox.IActorMailbox;
import com.yorha.common.actor.msg.IActorMsg;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.time.SystemClock;
import it.unimi.dsi.fastutil.objects.Object2ObjectOpenHashMap;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.concurrent.ThreadSafe;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 服务发现各种Shard、Actor的Region。
 *
 * <AUTHOR>
 */
@ThreadSafe
public class ActorRegistryValue {
    private static final Logger LOGGER = LogManager.getLogger(ActorRegistryValue.class);
    /**
     * 用于保证更新操作的原子性。
     */
    private final ReentrantLock refreshLock;
    /**
     * 集群拓扑信息。
     */
    private volatile Cluster cluster;
    /**
     * 系统。
     */
    private final ActorSystem actorSystem;
    /**
     * 本地邮箱容器。 <ActorRole, <ActorId, ActorMailbox> >
     */
    private final Map<String, Map<String, IActorMailbox>> localActorMailboxMap;
    /**
     * 本地邮箱占位符号数量，用于做Mailbox的熔断。
     */
    private final Map<String, AtomicInteger> localActorMailboxPlaceHolderCount;
    /**
     * 当前集成节点。
     */
    private final Node node;

    public ActorRegistryValue(final ActorSystem actorSystem, final String busId, final List<ActorMetaData> metaDataList) {
        this.actorSystem = actorSystem;
        // 使用公平锁，保证FIFO的更新顺序
        this.refreshLock = new ReentrantLock(true);
        // 本地Mailbox
        final Map<String, Map<String, IActorMailbox>> localActorMailboxMap = new ConcurrentHashMap<>(metaDataList.size() * 2);
        final Map<String, AtomicInteger> localActorMailboxPlaceHolderCount = new ConcurrentHashMap<>(metaDataList.size() * 2);
        for (final ActorMetaData metaData : metaDataList) {
            localActorMailboxMap.put(metaData.getActorRole(), new ConcurrentHashMap<>());
            localActorMailboxPlaceHolderCount.put(metaData.getActorRole(), new AtomicInteger(0));
        }
        this.localActorMailboxMap = Collections.unmodifiableMap(localActorMailboxMap);
        this.localActorMailboxPlaceHolderCount = Collections.unmodifiableMap(localActorMailboxPlaceHolderCount);
        this.node = Node.newBuilder().busId(busId).startTsMs(TimeUnit.NANOSECONDS.toMillis(SystemClock.nanoTimeNative())).build();
        this.cluster = Cluster.newBuilder().build();
    }

    /**
     * 获取一个本地的邮箱。
     *
     * @param ref actor ref。
     * @return 邮箱。
     */
    public IActorMailbox getMailbox(IActorRef ref) {
        final Map<String, IActorMailbox> map = this.localActorMailboxMap.get(ref.getActorRole());
        if (map == null) {
            return null;
        }
        return map.get(ref.getActorId());
    }

    /**
     * 符合条件的情况下新建一个邮箱并返回，以下情况下返回null：
     * 1. ActorSystem已经关闭。
     * 2. 当前产生的邮箱过多。
     *
     * @param ref actor ref。
     * @return 邮箱。
     */
    public IActorMailbox computeMailboxIfNeeded(final IActorRef ref) {
        final Map<String, IActorMailbox> map = this.localActorMailboxMap.get(ref.getActorRole());
        if (map == null) {
            throw new GeminiException("ActorRole {} Not Register!", ref.getActorRole());
        }
        return map.computeIfAbsent(ref.getActorId(), (key) -> {
            // 禁止写日志！！！此处处于一个ConcurrentHashMap的computeIfAbsent中，有synchronized锁
            // 如果actor system关闭了，不能产生新的邮箱
            if (this.actorSystem.isShutdown()) {
                return null;
            }
            // 邮箱产生太多了，不产生新的邮箱
            final ActorMetaData metaData = this.actorSystem.getActorMetaDataMgr().getActorMetaData(ref.getActorRole());
            final AtomicInteger placeHolderCounter = this.localActorMailboxPlaceHolderCount.get(metaData.getActorRole());
            if (placeHolderCounter.incrementAndGet() > metaData.getMailboxMaxCount()) {
                placeHolderCounter.decrementAndGet();
                return null;
            }
            // 新建邮箱
            return metaData.getLocalMailboxFactory().newMailbox(ref);
        });
    }

    /**
     * 删除邮箱。
     *
     * @param ref actor ref。
     * @return true 删除成功; false 删除失败(无邮箱存在)。
     */
    public boolean removeMailbox(final IActorRef ref) {
        final Map<String, IActorMailbox> map = this.localActorMailboxMap.get(ref.getActorRole());
        if (map == null) {
            return false;
        }
        if (map.remove(ref.getActorId()) == null) {
            return false;
        }
        // 删除占位符
        this.localActorMailboxPlaceHolderCount.get(ref.getActorRole()).decrementAndGet();
        return true;
    }

    /**
     * 邮箱数量。
     *
     * @param actorRole 角色。
     * @return 邮箱数量。
     */
    public int getMailboxSizeByActorRole(final String actorRole) {
        return this.localActorMailboxMap.getOrDefault(actorRole, Collections.emptyMap()).size();
    }

    /**
     * 批量获取目标ActorRole的邮箱列表。
     *
     * @param actorRole 目标ActorRole。
     * @return 邮箱列表。
     */
    public List<IActorMailbox> batchGetMailboxByActorRole(String actorRole) {
        final Map<String, IActorMailbox> mailboxMap = this.localActorMailboxMap.getOrDefault(actorRole, Collections.emptyMap());
        final List<IActorMailbox> mailboxList = new ArrayList<>(mailboxMap.size());
        mailboxList.addAll(mailboxMap.values());
        return mailboxList;
    }

    /**
     * @return 本地的LocalActor列表。
     */
    public Collection<String> getLocalActorRoleList() {
        return this.localActorMailboxMap.keySet();
    }

    /**
     * 广播消息给当前本地所有Actor。
     *
     * @param actorRoleSet 目标的角色集合。
     * @param msg          待广播消息。
     */
    public void broadcastLocal(final Set<String> actorRoleSet, final IActorMsg msg) {
        for (final Map.Entry<String, Map<String, IActorMailbox>> kv : this.localActorMailboxMap.entrySet()) {
            if (!actorRoleSet.contains(kv.getKey())) {
                continue;
            }
            for (final IActorMailbox mb : kv.getValue().values()) {
                ActorSendMsgUtils.send(mb.ref(), msg);
            }
        }
    }

    /**
     * 效果等于broadcastLocal
     * 额外告知使用者，最终发送的对象数量
     */
    public Map<String, Integer> broadcastLocalWithReturn(final Set<String> actorRoleSet, final IActorMsg msg) {
        Map<String, Integer> sendNum = new Object2ObjectOpenHashMap<>();
        for (final Map.Entry<String, Map<String, IActorMailbox>> kv : this.localActorMailboxMap.entrySet()) {
            if (!actorRoleSet.contains(kv.getKey())) {
                continue;
            }
            for (final IActorMailbox mb : kv.getValue().values()) {
                ActorSendMsgUtils.send(mb.ref(), msg);
                sendNum.put(kv.getKey(), sendNum.getOrDefault(kv.getKey(), 0) + 1);
            }
        }
        return sendNum;
    }

    /**
     * 判断是否某一种actor全部销毁了
     */
    public boolean isAllActorDestroy(final String actorRole) {
        Map<String, IActorMailbox> ret = this.localActorMailboxMap.get(actorRole);
        if (ret == null) {
            LOGGER.warn("isAllActorDestroy but {} is null", actorRole);
            return true;
        }
        if (!ret.isEmpty()) {
            LOGGER.warn("isAllActorDestroy {} {} notEmpty", actorRole, ret.size());
            return false;
        }
        LOGGER.warn("isAllActorDestroy {} {}", actorRole, ret.size());
        return true;
    }


    /**
     * 解析ActorRole对应的ActorRef列表。
     *
     * @param serverType 目标ActorRole。
     * @return ActorRef列表
     */
    public List<Node> getServerTypeNodeList(final int serverType) {
        return this.cluster.getNodeList(serverType);
    }

    /**
     * 根据busId获取对应节点描述。
     *
     * @param busId bus id。
     * @return busId无效，返回null; busId有效，返回Node。
     */
    public Node getNode(final String busId) {
        final Node node = this.cluster.getNode(busId);
        if (node != null) {
            return node;
        }
        if (this.node.getBusId().equals(busId)) {
            return this.node;
        }
        return null;
    }

    /**
     * 本地的Node描述。
     *
     * @return Node。
     */
    public Node getLocalNode() {
        return this.node;
    }

    /**
     * 集群描述。
     *
     * @return 集群描述。
     */
    public Cluster getCluster() {
        return this.cluster;
    }

    public void refreshCluster(Cluster cluster) {
        // 使用了CopyOnWrite的技术（lock保证原子性，volatile保证线程可见性）
        try {
            this.refreshLock.lock();
            LOGGER.info("gemini_system refreshCluster elder={}, new={}", this.cluster, cluster);
            this.cluster = cluster;
        } finally {
            this.refreshLock.unlock();
        }
    }
}

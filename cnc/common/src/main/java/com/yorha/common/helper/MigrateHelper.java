package com.yorha.common.helper;

import com.yorha.common.actor.IActorRef;
import com.yorha.common.actorservice.ActorMsgSystem;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;

/**
 * 迁服
 *
 * <AUTHOR>
 */
public class MigrateHelper {
    /**
     * 发现迁服后转发消息用，对于该消息的envelop来说，转发消息只能变更接收人，其余信息应保持一致
     * 故不能生成新的envelop，msgId也不能变
     *
     * @param envelope
     * @param targetRef
     */
    public static void transferMsg(ActorMsgEnvelope envelope, IActorRef targetRef) {
        final ActorMsgEnvelope msgEnvelope = ActorMsgEnvelope.createFromTransfer(envelope, targetRef);
        ActorMsgSystem.dispatchMsg(msgEnvelope);
    }

    public static final String ZONE_MIGRATE = "PlayerZoneMigrate";
    public static final int ZONE_MIGRATE_START = 11;
    public static final int ZONE_MIGRATE_STAGE_2 = 12;
    public static final int ZONE_MIGRATE_STAGE_3 = 13;
    public static final int ZONE_MIGRATE_STAGE_4 = 14;
    public static final int ZONE_MIGRATE_END = 15;

    /**
     * 1:复制db并插入kvk
     * 2:删原服内存和db
     * 3:拉起kvk服务器的sp、city
     */
    public static boolean isZoneMigrate(int stage) {
        return stage >= ZONE_MIGRATE_START && stage <= ZONE_MIGRATE_END;
    }

    public static boolean isZoneMigrateEnd(int stage) {
        return ZONE_MIGRATE_END == stage;
    }
}

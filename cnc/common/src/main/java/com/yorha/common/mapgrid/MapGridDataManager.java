package com.yorha.common.mapgrid;

import com.yorha.common.constant.BigSceneConstants;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.mapdata.MapTemplateDataItem;
import com.yorha.common.resource.mapdata.RegionalAreaSettingTemplate;
import com.yorha.common.resource.resservice.map.MapSubdivisionDataService;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.UnitConvertUtils;
import com.yorha.common.utils.shape.AABB;
import com.yorha.common.utils.shape.Point;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonEnum.MapAreaType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.MapConfigTemplate;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>   并发!!
 * <p>
 * 大世界地缘数据管理器
 */
public class MapGridDataManager {
    private static final Logger LOGGER = LogManager.getLogger(MapGridDataManager.class);
    private static final MapGridDataManager INSTANCE = new MapGridDataManager();
    /**
     * 不允许运行中修改MapGridData的数据
     */
    private final Map<Integer, MapGridData> gridData = new ConcurrentHashMap<>();

    public static MapGridDataManager getInstance() {
        return INSTANCE;
    }

    private static MapTemplateDataItem getMapTemplateDataItem(int mapId) {
        return ResHolder.getResService(MapSubdivisionDataService.class).getMapTemplateDataItem(mapId);
    }

    /**
     * 根据州Id 获取随机坐标
     */
    public static Point randomBornPointByRegion(int mapId, int regionId) {
        Map<MapAreaType, List<Integer>> parts = getMapTemplateDataItem(mapId).getRegionBornPartList(regionId);
        if (parts == null || parts.isEmpty()) {
            return null;
        }
        List<MapAreaType> areaTypeList = new ArrayList<>(parts.keySet());
        Object areaType = RandomUtils.randomList(areaTypeList);
        List<Integer> partList = parts.get(areaType);
        return randomPointByPart(mapId, RandomUtils.randomList(partList));
    }

    /**
     * 根据片Id 获取随机坐标
     */
    public static Point randomPointByPart(int mapId, int partId) {
        List<Integer> gridList = getGridData(mapId).getGridList(partId);
        if (CollectionUtils.isEmpty(gridList)) {
            return null;
        }
        return randomPointByGrid(RandomUtils.randomList(gridList));
    }

    /**
     * 根据格子 获取随机坐标
     */
    private static Point randomPointByGrid(int gridId) {
        int ratio = BigSceneConstants.GRID_INDEX_TO_MAP_COORDINATE_RATIO;
        // 格子左上角坐标
        int baseY = gridId & BigSceneConstants.MAP_GRID_Y;
        int baseX = (gridId & BigSceneConstants.MAP_GRID_X) >> BigSceneConstants.MAP_GRID_Y_DIGIT_NUM;
        // 格子内偏移
        int x = RandomUtils.nextInt(ratio);
        int y = RandomUtils.nextInt(ratio);
        return Point.valueOf(x + baseX * ratio, y + baseY * ratio);
    }


    /**
     * 根据片id + 区域类型  获取随机点出生点(已经排除阻挡片、过河片)
     */
    public static Point getRandomBornPoint(int mapId, int regionId, MapAreaType areaType) {
        List<Integer> parts = getMapTemplateDataItem(mapId).getEntityCanBornPartList(regionId, areaType);
        if (parts == null || parts.isEmpty()) {
            return null;
        }
        Integer partId = RandomUtils.randomList(parts);
        List<Integer> gridList = getGridData(mapId).getGridList(partId);
        return randomPointByGrid(RandomUtils.randomList(gridList));
    }

    /**
     * 根据片id获取AABB
     */
    public static AABB getPartAABB(int mapId, int partId) {
        Collection<Integer> grids = getGridData(mapId).getGridList(partId);
        int left = -1;
        int top = -1;
        int right = -1;
        int bottom = -1;
        if (CollectionUtils.isEmpty(grids)) {
            LOGGER.error("{} not has grid", partId);
            RegionalAreaSettingTemplate template = getMapTemplateDataItem(mapId).getValueFromMap(RegionalAreaSettingTemplate.class, partId);
            return Point.valueOf(template.getPosX(), template.getPosY()).getAABB();
        }
        int ratio = BigSceneConstants.GRID_INDEX_TO_MAP_COORDINATE_RATIO;
        for (int gridId : grids) {
            // 格子左上角坐标
            int baseY = gridId & BigSceneConstants.MAP_GRID_Y;
            int baseX = (gridId & BigSceneConstants.MAP_GRID_X) >> BigSceneConstants.MAP_GRID_Y_DIGIT_NUM;
            if (left == -1 || left > baseX) {
                left = baseX;
            }
            if (right == -1 || right < baseX + 1) {
                right = baseX + 1;
            }
            if (top == -1 || top > baseY) {
                top = baseY;
            }
            if (bottom == -1 || bottom < baseY + 1) {
                bottom = baseY + 1;
            }
        }
        return new AABB(left * ratio, top * ratio, right * ratio, bottom * ratio);
    }

    /**
     * 获取地貌类型   格子id/坐标（cm）
     */
    public static int getLandformsType(int mapId, int x, int y) {
        return getLandformsTypeTag(getGridData(mapId).getGridTag(x, y));
    }

    /**
     * 获取tag中具体的地形tag
     */
    private static int getLandformsTypeTag(int tag) {
        return tag & BigSceneConstants.LANDFORMS_TYPE_TAG;
    }

    /**
     * 获取所属的片 id
     */
    public static int getPartId(int mapId, int gridId) {
        return getPartTag(getGridData(mapId).getGridTag(gridId));
    }

    public static int getPartId(int mapId, int x, int y) {
        return getPartTag(getGridData(mapId).getGridTag(x, y));
    }

    public static int getPartId(int mapId, Point point) {
        return getPartTag(getGridData(mapId).getGridTag(point.getX(), point.getY()));
    }

    /**
     * 获取州 id
     */
    public static int getRegionId(int mapId, int x, int y) {
        return getRegionTag(getGridData(mapId).getGridTag(x, y));
    }

    /**
     * 获取州 id
     */
    public static int getRegionId(int mapId, Point point) {
        return getRegionTag(getGridData(mapId).getGridTag(point.getX(), point.getY()));
    }

    /**
     * 获取tag中具体的片id tag
     */
    private static int getPartTag(int tag) {
        return (tag & BigSceneConstants.PART_TAG) >> BigSceneConstants.LANDFORMS_TYPE_DIGIT_NUM;
    }

    private static int getRegionTag(int tag) {
        return (tag & BigSceneConstants.REGION_ID_TAG) >> BigSceneConstants.BEFORE_REGION_DIGIT_NUM;
    }

    public static MapAreaType getAreaType(int mapId, int x, int y) {
        int partId = getPartId(mapId, x, y);
        return getMapTemplateDataItem(mapId).getValueFromMap(RegionalAreaSettingTemplate.class, partId).getAreaType();
    }

    /**
     * 获取某州某类型的可出生的格子数目
     */
    public int getRegionBornAreaNum(int mapId, int regionId, MapAreaType areaType) {
        return getGridData(mapId).getRegionBornAreaNum(regionId, areaType);
    }

    /**
     * 相同州判断
     */
    public static boolean isSameRegion(int mapId, int srcX, int srcY, int dstX, int dstY, MapConfigTemplate config) {
        int width = UnitConvertUtils.meterToCm(config.getWidth());
        int length = UnitConvertUtils.meterToCm(config.getLength());
        if (srcX <= 0 || srcX > width) {
            return true;
        }
        if (srcY <= 0 || srcY > length) {
            return true;
        }
        //目标点合法校验
        if (dstX <= 0 || dstX > width) {
            return false;
        }
        if (dstY <= 0 || dstY > length) {
            return false;
        }
        if (getRegionId(mapId, srcX, srcY) != getRegionId(mapId, dstX, dstY)) {
            return false;
        }
        return true;
    }

    /**
     * 外圈州判断
     */
    public static boolean isOutCircleRegion(int mapId, int storyId, int x, int y) {
        int regionId = getRegionId(mapId, x, y);
        return ResHolder.getResService(MapSubdivisionDataService.class).isOutCircleRegion(storyId, regionId);
    }

    /**
     * 坐标转格子id
     */
    public static int getGridId(int mapId, int x, int y) {
        return getGridData(mapId).getGridId(x, y);
    }

    /**
     * 坐标转格子id
     */
    public static int getGridId(int mapId, Point p) {
        return getGridData(mapId).getGridId(p.getX(), p.getY());
    }

    public static Point getRandomBornPoint(int gridId) {
        int i = (gridId & BigSceneConstants.MAP_GRID_X) >> BigSceneConstants.MAP_GRID_Y_DIGIT_NUM;
        int j = gridId & BigSceneConstants.MAP_GRID_Y;
        int x = RandomUtils.nextInt(BigSceneConstants.GRID_INDEX_TO_MAP_COORDINATE_RATIO);
        int y = RandomUtils.nextInt(BigSceneConstants.GRID_INDEX_TO_MAP_COORDINATE_RATIO);
        return Point.valueOf(i * BigSceneConstants.GRID_INDEX_TO_MAP_COORDINATE_RATIO + x, j * BigSceneConstants.GRID_INDEX_TO_MAP_COORDINATE_RATIO + y);
    }

    /**
     * 获取partId对应的所有grid
     */
    public static List<Integer> getGridList(int mapId, int partId) {
        List<Integer> gridList = getGridData(mapId).getGridList(partId);
        if (gridList == null) {
            return Collections.emptyList();
        }
        return Collections.unmodifiableList(gridList);
    }

    public static Map<Integer, Map<Integer, List<Integer>>> getCrossBesiegeAngle(int mapId) {
        return getGridData(mapId).getCrossBesiegeAngle();
    }

    private static MapGridData getGridData(int mapId) {
        return getInstance().gridData.get(mapId);
    }

    public void load(int mapId) {
        // 已经初始化过 不用了
        if (gridData.containsKey(mapId)) {
            return;
        }
        MapGridData data = new MapGridData();
        gridData.put(mapId, data);
        try {
            data.load(mapId);
        } catch (ResourceException e) {
            WechatLog.error("地缘数据有问题:{}", e.getMessage(), e);
            throw new GeminiException("MapGridDataManager load fail", e);
        }
    }
}

package com.yorha.common.resource.resservice.ai;

import com.google.common.collect.Maps;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.proto.CommonEnum;
import res.template.AiBaseModelTemplate;
import res.template.AiInstanceTemplate;
import res.template.AiStateTriggerTemplate;

import java.util.*;

/**
 * <AUTHOR>
 */
public class AiResService extends AbstractResService {
    private final Map<Integer, Map<CommonEnum.AiParams, Integer>> params = new HashMap<>();
    private final Map<CommonEnum.AiBaseModelType, List<Integer>> modelToStatesMap = Maps.newHashMap();
    private final Map<CommonEnum.AiBaseModelType, List<CommonEnum.AiParams>> modelToParamsMap = Maps.newHashMap();

    public AiResService(ResHolder resHolder) {
        super(resHolder);
    }

    public Map<CommonEnum.AiParams, Integer> getParams(int aiInstanceId) {
        return params.getOrDefault(aiInstanceId, Maps.newHashMap());
    }

    @Override
    public void load() throws ResourceException {
        // 加载基础模型
        for (AiBaseModelTemplate modelTemplate : getResHolder().getListFromMap(AiBaseModelTemplate.class)) {
            // 加载基础模型支持配置的参数，状态
            if (modelToParamsMap.containsKey(modelTemplate.getType())) {
                throw new ResourceException("ai_base_model has repeat type:{}", modelTemplate.getType());
            }
            modelToParamsMap.put(modelTemplate.getType(), getParamsList(modelTemplate));
            String jsonStr = modelTemplate.getStates().replaceAll("@", "\"");
            JsonObject obj = JsonUtils.parseObject(jsonStr);
            List<Integer> stateList = new ArrayList<>();
            for (JsonElement element : obj.get("states").getAsJsonArray()) {
                int stateId = element.getAsJsonObject().get("id").getAsInt();
                if (stateList.contains(stateId)) {
                    throw new ResourceException("ai_base_model states illegal id:{}", modelTemplate.getId());
                }
                stateList.add(stateId);
            }
            modelToStatesMap.put(modelTemplate.getType(), stateList);
        }
        // 加在模型实例
        for (AiInstanceTemplate instanceTemplate : getResHolder().getListFromMap(AiInstanceTemplate.class)) {
            Map<CommonEnum.AiParams, Integer> config = Maps.newHashMap();
            for (IntPairType pair : instanceTemplate.getParamsPairList()) {
                config.put(CommonEnum.AiParams.forNumber(pair.getKey()), pair.getValue());
            }
            params.put(instanceTemplate.getId(), config);
        }
    }

    private void paramCheck(AiStateTriggerTemplate triggerTemplate) {
        // 校验模型实例配置正确性
    }

    @Override
    public void checkValid() throws ResourceException {
        // 加载trigger触发器
        Set<Integer> triggerSet = getResHolder().getMap(AiStateTriggerTemplate.class).keySet();
        for (AiStateTriggerTemplate triggerTemplate : getResHolder().getListFromMap(AiStateTriggerTemplate.class)) {
            paramCheck(triggerTemplate);
        }

        // 校验模型实例配置正确性
        for (AiInstanceTemplate instanceTemplate : getResHolder().getListFromMap(AiInstanceTemplate.class)) {
            // 校验param正确性
            List<CommonEnum.AiParams> hasParams = new ArrayList<>();
            List<CommonEnum.AiParams> needParams = modelToParamsMap.get(instanceTemplate.getModel());
            for (IntPairType intPairType : instanceTemplate.getParamsPairList()) {
                CommonEnum.AiParams param = CommonEnum.AiParams.forNumber(intPairType.getKey());
                if (!needParams.contains(param)) {
                    throw new ResourceException("ai_instance param illegal id:{}", instanceTemplate.getId());
                }
                hasParams.add(param);
            }
            if (hasParams.size() != needParams.size()) {
                throw new ResourceException("ai_instance param num illegal id:{}", instanceTemplate.getId());
            }
            // 校验trigger正确性
            List<Integer> hasStates = modelToStatesMap.get(instanceTemplate.getModel());
            for (IntPairType intPairType : instanceTemplate.getTriggersPairList()) {
                if (!hasStates.contains(intPairType.getKey())) {
                    throw new ResourceException("ai_instance param states illegal id:{}", instanceTemplate.getId());
                }
                if (!triggerSet.contains(intPairType.getValue())) {
                    throw new ResourceException("ai_instance triggers illegal id:{}", instanceTemplate.getId());
                }
            }

        }
    }

    /**
     * excel配置->AiParam映射
     *
     * @param modelTemplate
     * @return
     */
    private List<CommonEnum.AiParams> getParamsList(AiBaseModelTemplate modelTemplate) {
        List<CommonEnum.AiParams> result = new ArrayList<>();
        if (modelTemplate.getApAlertRange()) {
            result.add(CommonEnum.AiParams.AP_ALERT_RANGE);
        }

        if (modelTemplate.getApPatrolRange()) {
            result.add(CommonEnum.AiParams.AP_PATROL_RANGE);
        }

        if (modelTemplate.getApMoveMinDistance()) {
            result.add(CommonEnum.AiParams.AP_MOVE_MIN_DISTANCE);
        }

        if (modelTemplate.getApMoveMaxDistance()) {
            result.add(CommonEnum.AiParams.AP_MOVE_MAX_DISTANCE);
        }

        if (modelTemplate.getApChaseRange()) {
            result.add(CommonEnum.AiParams.AP_CHASE_RANGE);
        }

        if (modelTemplate.getApRecoverySoldier()) {
            result.add(CommonEnum.AiParams.AP_RECOVERY_SOLDIER);
        }

        if (modelTemplate.getApPatrolInterval()) {
            result.add(CommonEnum.AiParams.AP_PATROL_INTERVAL);
        }

        if (modelTemplate.getApSkill()) {
            result.add(CommonEnum.AiParams.AP_SKILL);
        }

        if (modelTemplate.getApSkillInterval()) {
            result.add(CommonEnum.AiParams.AP_SKILL_INTERVAL);
        }
        return result;
    }


    public AiInstanceTemplate getConfig(int aiIndex) {
        return ResHolder.getInstance().getValueFromMap(AiInstanceTemplate.class, aiIndex);
    }

    public List<Integer> getStates(CommonEnum.AiBaseModelType modelType) {
        return modelToStatesMap.getOrDefault(modelType, new ArrayList<>());
    }
}

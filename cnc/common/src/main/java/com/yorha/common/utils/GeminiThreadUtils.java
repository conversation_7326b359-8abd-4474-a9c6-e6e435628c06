package com.yorha.common.utils;

import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public final class GeminiThreadUtils {
    /**
     * 单个线程的CPU统计信息。
     */
    public static class ThreadCpuStatInfo {
        private final long tid;
        private final String name;
        private final long lastCpuTime;
        private final long lastStatTime;
        private final double cpuUsage;

        ThreadCpuStatInfo(long tid, String name, long lastCpuTime, long lastStatTime, double cpuUsage) {
            this.tid = tid;
            this.name = name;
            this.lastCpuTime = lastCpuTime;
            this.lastStatTime = lastStatTime;
            this.cpuUsage = cpuUsage;
        }

        public double getThreadCpuUsage() {
            return cpuUsage;
        }

        public String getThreadName() {
            return name;
        }

        public long getThreadId() {
            return tid;
        }
    }

    /**
     * 统计用户线程CPU。
     *
     * @param tmxb              用户Thread Bean。
     * @param lastCollectResult 上次CPU统计表，线程id: 线程统计信息。
     * @return lastCollectResult到当前时间戳的CPU统计。
     */
    public static Map<Long, ThreadCpuStatInfo> collectUserThreadCpuUsage(final ThreadMXBean tmxb, Map<Long, ThreadCpuStatInfo> lastCollectResult) {
        // 清理掉上次已经清理的线程数据
        final Map<Long, ThreadCpuStatInfo> result = new HashMap<>(lastCollectResult.size());
        // 获取线程
        final long[] tids = tmxb.getAllThreadIds();
        // 获取当前线程消息
        final ThreadInfo[] tInfos = tmxb.getThreadInfo(tids);
        // 当前时间戳
        final long now = System.nanoTime();
        for (int i = 0; i < tids.length; i++) {
            final long tid = tids[i];
            if (tid < 0) {
                continue;
            }
            final ThreadInfo threadInfo = tInfos[i];
            final long lastCpuTime = tmxb.getThreadCpuTime(tid);
            // 线程已经消失
            if (lastCpuTime == -1) {
                continue;
            }
            // 线程数据不存在，则新建统计
            final ThreadCpuStatInfo threadCpuStatInfo = lastCollectResult.get(tid);
            if (threadCpuStatInfo == null) {
                final ThreadCpuStatInfo newThreadCpuStatInfo = new ThreadCpuStatInfo(tid, threadInfo.getThreadName(), lastCpuTime, now, 0.0);
                result.put(tid, newThreadCpuStatInfo);
                continue;
            }
            // 统计消息
            final long elapsedCpu = lastCpuTime - threadCpuStatInfo.lastCpuTime;
            final long elapsedTime = now - threadCpuStatInfo.lastStatTime;
            final double cpuUsage = Math.min(0.9999, ((double) elapsedCpu) / elapsedTime);
            result.put(tid, new ThreadCpuStatInfo(tid, threadInfo.getThreadName(), lastCpuTime, now, cpuUsage));
        }
        return result;
    }


    /**
     * 统计系统线程CPU。
     *
     * @param tmx               ThreadMXBean 实例
     * @param lastCollectResult 上次统计结果，用于计算差值
     * @return 当前时间窗口内各“系统线程”的 CPU 使用率
     */
    public static Map<String, ThreadCpuStatInfo> collectSystemThreadCpuUsage(
            ThreadMXBean tmx,
            Map<String, ThreadCpuStatInfo> lastCollectResult) {

        Map<String, ThreadCpuStatInfo> result = new HashMap<>();
        long now = System.nanoTime();

        for (long tid : tmx.getAllThreadIds()) {
            ThreadInfo info = tmx.getThreadInfo(tid);
            if (info == null) {
                continue;
            }

            String name = info.getThreadName();
            // 匹配常见系统线程名（可扩展）
            if (name.contains("GC") || name.contains("Compiler") || name.contains("ReferenceHandler")) {
                long cpuTime = tmx.getThreadCpuTime(tid);
                if (cpuTime == -1) {
                    continue; // 不支持 CPU 时间采集的线程跳过
                }

                ThreadCpuStatInfo prev = lastCollectResult.get(name);
                if (prev == null) {
                    result.put(name, new ThreadCpuStatInfo(-1, name, cpuTime, now, 0.0));
                } else {
                    long elapsedCpu = cpuTime - prev.lastCpuTime;
                    long elapsedTime = now - prev.lastStatTime;
                    double usage = Math.min(99.99, ((double) elapsedCpu) / elapsedTime);
                    result.put(name, new ThreadCpuStatInfo(-1, name, cpuTime, now, usage));
                }
            }
        }

        return result;
    }
}

package com.yorha.common.qlog;


/**
 * 以角色为单位的派发流水
 *
 * <AUTHOR>
 */
public abstract class AbstractPlayerQlogFlow extends AbstractQlogFlow {
    /**
     * 游戏的appid，用来区分不同的项目
     */
    private String gameappid;
    /**
     * 用户唯一编码，腾讯sdk生成
     */
    private String vOpenID;
    /**
     * sdk账号首次创建时间
     */
    private String accountCreate_time;
    /**
     * sdk账号生成后在游戏侧首次完成注册的时间
     */
    private String accountReg_time;
    /**
     * 服务器类型
     */
    private String server_type;
    /**
     * 创建角色时的服务器id
     */
    private String iZoneAreaID;
    /**
     * 大区id
     */
    private String worldId;
    /**
     * 当前所处的服务器id
     */
    private String gameSvrId;
    /**
     * 当前主堡所处的地图坐标
     */
    private String now_coordinate;
    /**
     * 角色id，每个角色独有
     */
    private String vRoleID;
    /**
     * sdk账号首次创建时间
     */
    private String vRoleName;
    /**
     * 该账号下的第几个角色
     */
    private int role_num;
    /**
     * 角色类型
     */
    private String role_type;
    /**
     * 角色创建时间
     */
    private String roleCreate_time;
    /**
     * 主堡等级
     */
    private int iLevel;
    /**
     * vip等级
     */
    private int iVipLevel;
    /**
     * 角色战力
     */
    private long iRoleCE;
    /**
     * 历史最高战力
     */
    private long highestRole_power;
    /**
     * 玩家好友数量
     */
    private int playerFriendsNum;
    /**
     * 角色id，历史付费金额
     */
    private float recharge_sum;
    /**
     * 所处联盟id
     */
    private long iGuildID;
    /**
     * 联盟名称
     */
    private String vGuildName;
    /**
     * 联盟职位
     */
    private String guildGrade;
    /**
     * 联盟职级
     */
    private String guildClass;
    /**
     * 操作系统
     */
    private int platID;
    /**
     * 游戏语言
     */
    private String game_language;
    /**
     * 客户端IP
     */
    private String vClientIP;
    /**
     * 客户端IPV6地址
     */
    private String vClientIPV6;
    /**
     * 历史总杀敌数
     */
    private long kill_num;
    /**
     * 客户端版本
     */
    private String clientVersion;
    /**
     * 当前充值货币余量
     */
    private int money_config;
    /**
     * 是否为转服玩家
     */
    private int transferOrNot;

    protected AbstractPlayerQlogFlow() {
        gameappid = "";
        vOpenID = "";
        accountCreate_time = "";
        accountReg_time = "";
        server_type = "";
        iZoneAreaID = "";
        gameSvrId = "";
        worldId = "";
        now_coordinate = "";
        vRoleID = "";
        vRoleName = "";
        role_type = "";
        roleCreate_time = "";
        recharge_sum = 0;
        vGuildName = "";
        guildGrade = "";
        guildClass = "";
        game_language = "";
        vClientIP = "";
        vClientIPV6 = "";
        kill_num = 0;
        clientVersion = "";
    }

    public void setGameappid(String gameappid) {
        this.gameappid = gameappid;
    }

    public void setVOpenID(String vOpenID) {
        this.vOpenID = vOpenID;
    }

    public void setAccountCreate_time(String accountCreate_time) {
        this.accountCreate_time = accountCreate_time;
    }

    public void setAccountReg_time(String accountReg_time) {
        this.accountReg_time = accountReg_time;
    }

    public void setServer_type(String server_type) {
        this.server_type = server_type;
    }

    public void setIZoneAreaID(String iZoneAreaID) {
        this.iZoneAreaID = iZoneAreaID;
    }

    public void setWorldId(String worldId) {
        this.worldId = worldId;
    }

    public void setGameSvrId(String gameSvrId) {
        this.gameSvrId = gameSvrId;
    }

    public void setNow_coordinate(String now_coordinate) {
        this.now_coordinate = now_coordinate;
    }

    public void setVRoleID(String vRoleID) {
        this.vRoleID = vRoleID;
    }

    public void setVRoleName(String vRoleName) {
        this.vRoleName = vRoleName;
    }

    public void setRole_num(int role_num) {
        this.role_num = role_num;
    }

    public void setRole_type(String role_type) {
        this.role_type = role_type;
    }

    public void setRoleCreate_time(String roleCreate_time) {
        this.roleCreate_time = roleCreate_time;
    }

    public void setILevel(int iLevel) {
        this.iLevel = iLevel;
    }

    public void setIVipLevel(int iVipLevel) {
        this.iVipLevel = iVipLevel;
    }

    public void setIRoleCE(long iRoleCE) {
        this.iRoleCE = iRoleCE;
    }

    public void setHighestRole_power(long highestRole_power) {
        this.highestRole_power = highestRole_power;
    }

    public void setPlayerFriendsNum(int playerFriendsNum) {
        this.playerFriendsNum = playerFriendsNum;
    }

    public void setRecharge_sum(float recharge_sum) {
        this.recharge_sum = recharge_sum;
    }

    public void setIGuildID(long iGuildID) {
        this.iGuildID = iGuildID;
    }

    public void setVGuildName(String vGuildName) {
        this.vGuildName = vGuildName;
    }

    public void setGuildGrade(String guildGrade) {
        this.guildGrade = guildGrade;
    }

    public void setGuildClass(String guildClass) {
        this.guildClass = guildClass;
    }

    public void setPlatID(int platID) {
        this.platID = platID;
    }

    public void setGame_language(String game_language) {
        this.game_language = game_language;
    }

    public void setVClientIP(String vClientIP) {
        this.vClientIP = vClientIP;
    }

    public void setVClientIPV6(String vClientIPV6) {
        this.vClientIPV6 = vClientIPV6;
    }

    public void setKill_num(long deviceId) {
        kill_num = deviceId;
    }

    public void setClientVersion(String clientVersion) {
        this.clientVersion = clientVersion;
    }

    public void setMoney_config(int money_config) {
        this.money_config = money_config;
    }

    public void setTransferOrNot(int transferOrNot) {
        this.transferOrNot = transferOrNot;
    }

    public void fillHead(QlogPlayerFlowInterface playerFlow) {
        if (needFullHead()) {
            fillFullHead(playerFlow);
            return;
        }
        fillSimpleHead(playerFlow);
    }

    protected boolean needFullHead() {
        return true;
    }

    private void fillSimpleHead(QlogPlayerFlowInterface playerFlow) {
        setGameappid(playerFlow.getGameappid());
        setVOpenID(playerFlow.getVOpenID());
        setAccountCreate_time(playerFlow.getAccountCreate_time());
        setAccountReg_time(playerFlow.getAccountReg_time());
        setServer_type(playerFlow.getServer_type());
        setIZoneAreaID(playerFlow.getIZoneAreaID());
        setGameSvrId(playerFlow.getGameSvrId());
        setWorldId(playerFlow.getWorldId());
        setVRoleID(playerFlow.getVRoleID());
        setRole_num(playerFlow.getRole_num());
        setRole_type(playerFlow.getRole_type());
        setRoleCreate_time(playerFlow.getRoleCreate_time());
        setILevel(playerFlow.getILevel());
        setIVipLevel(playerFlow.getIVipLevel());
        setIGuildID(playerFlow.getIGuildID());
        setPlatID(playerFlow.getPlatID());
        setVRoleName("");
        setNow_coordinate("");
        setIRoleCE(0);
        setHighestRole_power(0);
        setPlayerFriendsNum(0);
        setRecharge_sum(0);
        setVGuildName("");
        setGuildGrade("");
        setGuildClass("");
        setGame_language("");
        setVClientIP("");
        setVClientIPV6("");
        setKill_num(0);
        setClientVersion("");
        setMoney_config(0);
        setTransferOrNot(0);
    }

    private void fillFullHead(QlogPlayerFlowInterface playerFlow) {
        setGameappid(playerFlow.getGameappid());
        setVOpenID(playerFlow.getVOpenID());
        setAccountCreate_time(playerFlow.getAccountCreate_time());
        setAccountReg_time(playerFlow.getAccountReg_time());
        setServer_type(playerFlow.getServer_type());
        setIZoneAreaID(playerFlow.getIZoneAreaID());
        setGameSvrId(playerFlow.getGameSvrId());
        setWorldId(playerFlow.getWorldId());
        setNow_coordinate(playerFlow.getNow_coordinate());
        setVRoleID(playerFlow.getVRoleID());
        setVRoleName(playerFlow.getVRoleName());
        setRole_num(playerFlow.getRole_num());
        setRole_type(playerFlow.getRole_type());
        setRoleCreate_time(playerFlow.getRoleCreate_time());
        setILevel(playerFlow.getILevel());
        setIVipLevel(playerFlow.getIVipLevel());
        setIRoleCE(playerFlow.getIRoleCE());
        setHighestRole_power(playerFlow.getHighestRole_power());
        setPlayerFriendsNum(playerFlow.getPlayerFriendsNum());
        setRecharge_sum(playerFlow.getRecharge_sum());
        setIGuildID(playerFlow.getIGuildID());
        setVGuildName(playerFlow.getVGuildName());
        setGuildGrade(playerFlow.getGuildGrade());
        setGuildClass(playerFlow.getGuildClass());
        setPlatID(playerFlow.getPlatID());
        setGame_language(playerFlow.getGame_language());
        setVClientIP(playerFlow.getVClientIP());
        setVClientIPV6(playerFlow.getVClientIPV6());
        setKill_num(playerFlow.getKill_num());
        setClientVersion(playerFlow.getClientVersion());
        setMoney_config(playerFlow.getMoney_config());
        setTransferOrNot(playerFlow.getTransferOrNot());
    }

    @Override
    protected void addFlowHeadContent(StringBuilder builder) {
        builder.append("|").append(gameappid)
                .append("|").append(vOpenID)
                .append("|").append(accountCreate_time)
                .append("|").append(accountReg_time)
                .append("|").append(server_type)
                .append("|").append(iZoneAreaID)
                .append("|").append(gameSvrId)
                .append("|").append(worldId)
                .append("|").append(now_coordinate)
                .append("|").append(vRoleID)
                .append("|").append(vRoleName)
                .append("|").append(role_num)
                .append("|").append(role_type)
                .append("|").append(roleCreate_time)
                .append("|").append(iLevel)
                .append("|").append(iVipLevel)
                .append("|").append(iRoleCE)
                .append("|").append(highestRole_power)
                .append("|").append(playerFriendsNum)
                .append("|").append(recharge_sum)
                .append("|").append(iGuildID)
                .append("|").append(vGuildName)
                .append("|").append(guildGrade)
                .append("|").append(guildClass)
                .append("|").append(platID)
                .append("|").append(game_language)
                .append("|").append(vClientIP)
                .append("|").append(vClientIPV6)
                .append("|").append(kill_num)
                .append("|").append(clientVersion)
                .append("|").append(money_config)
                .append("|").append(transferOrNot);
    }
}

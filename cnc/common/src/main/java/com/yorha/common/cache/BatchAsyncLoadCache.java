package com.yorha.common.cache;

import com.yorha.common.cache.action.BatchAsyncAddAction;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.id.IntegerAdder;
import com.yorha.common.utils.time.SystemClock;
import it.unimi.dsi.fastutil.ints.Int2IntOpenHashMap;
import it.unimi.dsi.fastutil.objects.Object2ObjectOpenHashMap;
import it.unimi.dsi.fastutil.objects.ObjectOpenHashSet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.checkerframework.checker.nullness.qual.NonNull;

import java.util.*;
import java.util.function.Consumer;

/**
 * 批量异步加载cache
 *
 * <AUTHOR>
 */
public class BatchAsyncLoadCache<K, V> extends BaseSyncCache<K, V> {
    private static final Logger LOGGER = LogManager.getLogger(BatchAsyncLoadCache.class);
    /**
     * 加载器
     */
    private final BatchAsyncAddAction<K, V> loader;
    /**
     * 查询等待回复的消息id们
     */
    private final Map<Integer, Integer> waitCompleteQuery = new Int2IntOpenHashMap();
    /**
     * 查询回调
     */
    private final Map<K, List<Consumer<V>>> cbList = new Object2ObjectOpenHashMap<>();
    /**
     * 查询超时的时间戳
     */
    private final Map<K, Long> cbTimeoutTsMs = new Object2ObjectOpenHashMap<>();

    /**
     * 同步异步：异步
     * 缓存类型：任意数据
     * 插入缓存：手动
     *
     * @param entryLimit  缓存条目上限
     * @param timeLimitMs 缓存写入后存储时间,ms（==0 永不过期）
     */
    BatchAsyncLoadCache(int entryLimit, long timeLimitMs, BatchAsyncAddAction<K, V> loader, String name) {
        super(entryLimit, timeLimitMs, name);
        this.loader = loader;
    }

    public void getOrLoadData(K key, Consumer<V> onFound) {
        V data = super.getDataOrNull(key);
        if (data != null) {
            onFound.accept(data);
            return;
        }
        if (addCallback(key, onFound)) {
            loader.loadOne(key);
        }
    }

    public void batchGetOrLoadData(@NonNull Collection<K> keys, Consumer<V> onFound, Runnable allComplete) {
        final int queryId = IntegerAdder.nextId();
        // 等待其他人查询回来的
        Set<K> waitOther = new ObjectOpenHashSet<>();
        // 等待自己查询回来的
        Set<K> waitSelf = new ObjectOpenHashSet<>();
        for (K key : keys) {
            V data = super.getDataOrNull(key);
            if (data != null) {
                onFound.accept(data);
                continue;
            }
            boolean needQuery = addCallback(key,
                    (v) -> {
                        onFound.accept(v);
                        int restNum = waitCompleteQuery.getOrDefault(queryId, 0) - 1;
                        if (restNum <= 0) {
                            waitCompleteQuery.remove(queryId);
                            allComplete.run();
                            return;
                        }
                        waitCompleteQuery.put(queryId, restNum);
                    });
            if (needQuery) {
                waitSelf.add(key);
            } else {
                waitOther.add(key);
            }
        }
        if (waitOther.isEmpty() && waitSelf.isEmpty()) {
            allComplete.run();
            return;
        }
        waitCompleteQuery.put(queryId, waitOther.size() + waitSelf.size());
        if (waitSelf.isEmpty()) {
            return;
        }
        loader.loadBatch(waitSelf);
    }

    /**
     * 返回是否需要发起查询
     */
    private boolean addCallback(K key, Consumer<V> onFound) {
        // 没有发起过查询
        if (!cbList.containsKey(key)) {
            cbList.computeIfAbsent(key, k -> new ArrayList<>()).add(onFound);
            // 少一点 提前触发超时回调
            cbTimeoutTsMs.put(key, SystemClock.now() + ServerContext.getRpcTimeout());
            return true;
        }
        // 发起的查询超时了
        if (!cbTimeoutTsMs.containsKey(key) || cbTimeoutTsMs.get(key) <= SystemClock.now()) {
            LOGGER.warn("addCallback but before is timeout key={}", key);
            // 把过期的触发下
            onCacheLoaded(key);
            cbList.computeIfAbsent(key, k -> new ArrayList<>()).add(onFound);
            // 少一点 提前触发超时回调
            cbTimeoutTsMs.put(key, SystemClock.now() + ServerContext.getRpcTimeout());
            return true;
        }
        cbList.computeIfAbsent(key, k -> new ArrayList<>()).add(onFound);
        return false;
    }

    public void onCacheLoaded(K key) {
        cbTimeoutTsMs.remove(key);
        List<Consumer<V>> remove = cbList.remove(key);
        if (remove == null) {
            LOGGER.warn("onCacheLoaded but callback is not exist key={}", key);
            return;
        }
        V data = super.getDataOrNull(key);
        for (Consumer<V> consumer : remove) {
            try {
                consumer.accept(data);
            } catch (Exception e) {
                LOGGER.error("onCacheLoaded callback run failed key={} ", key, e);
            }
        }
    }
}

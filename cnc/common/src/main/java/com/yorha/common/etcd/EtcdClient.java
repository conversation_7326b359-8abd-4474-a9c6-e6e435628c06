package com.yorha.common.etcd;

import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.concurrent.executor.GeminiThreadPoolExecutor;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.utils.StringUtils;
import io.etcd.jetcd.Client;
import io.etcd.jetcd.ClientBuilder;
import io.etcd.jetcd.KeyValue;
import io.etcd.jetcd.kv.DeleteResponse;
import io.etcd.jetcd.kv.GetResponse;
import io.etcd.jetcd.kv.PutResponse;
import io.etcd.jetcd.kv.TxnResponse;
import io.etcd.jetcd.op.Cmp;
import io.etcd.jetcd.op.CmpTarget;
import io.etcd.jetcd.op.Op;
import io.etcd.jetcd.options.DeleteOption;
import io.etcd.jetcd.options.GetOption;
import io.etcd.jetcd.options.PutOption;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.concurrent.ThreadSafe;
import java.time.Duration;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;


/**
 * etcd 客户端
 *
 * <AUTHOR>
 */
@ThreadSafe
public class EtcdClient {
    private static final Logger LOGGER = LogManager.getLogger(EtcdClient.class);
    /**
     * Etcd客户端。
     */
    private final Client etcdClient;
    /**
     * Etcd KV对的维护者。
     */
    private final EtcdKeyValueKeeper keeper;
    /**
     * Etcd KV对的观察者。
     */
    private final EtcdKeyValueWatcher watcher;
    /**
     * Gemini依赖于KONA，三方库需要跑在物理线程上。
     */
    private final ExecutorService executor;

    public EtcdClient(final String[] etcdAddress, final String user, final String password) {
        final ClientBuilder builder = Client.builder().target(etcdAddress[0]).connectTimeout(Duration.ofMillis(EtcdUtils.ETCD_TIME_OUT_TIME_MS));
        if (!StringUtils.isEmpty(user)) {
            builder.user(EtcdUtils.bytesOf(user)).password(EtcdUtils.bytesOf(password));
        }
        // 物理线程
        this.executor = ConcurrentHelper.newSingleThreadExecutor("EtcdClient", 0, true);
        this.etcdClient = builder.build();
        this.keeper = new EtcdKeyValueKeeper(this.etcdClient, this.executor);
        this.watcher = new EtcdKeyValueWatcher(this.etcdClient, this.executor);
        // 设置钝化时间
        ((GeminiThreadPoolExecutor) executor).setKeepAliveTime(10, TimeUnit.SECONDS);
        ((GeminiThreadPoolExecutor) executor).allowCoreThreadTimeOut(true);
        LOGGER.info("EtcdClient init, client={} address={} user={} password{}", etcdClient, etcdAddress, user, password);
    }

    /**
     * 获取指定的key
     *
     * @param key etcd key。
     * @return etcd内容。
     */
    public String getSingle(String key) {
        try {
            return this.executor.submit(() -> {
                final GetOption option = GetOption.newBuilder()
                        .isPrefix(false)
                        // 读取的时候坚决不使用序列化模式，使用线性化模式。
                        // 线性化模式返回达成共识后的值。序列化模式在未达成共识的时候也可以返回，吞吐更好。
                        .withSerializable(false)
                        .build();
                final GetResponse getResponse = etcdClient.getKVClient()
                        .get(EtcdUtils.bytesOf(key), option)
                        .get(EtcdUtils.ETCD_TIME_OUT_TIME_MS, TimeUnit.MILLISECONDS);
                // 没找到就是没有这个key  正常的
                if (getResponse.getKvs().isEmpty()) {
                    LOGGER.info("EtcdClient getSingle, key={}, value empty", key);
                    return null;
                }
                // 由于接口是以list返回的 所以getSingle的时候直接取第一个就行了
                final String value = EtcdUtils.toString(getResponse.getKvs().getFirst().getValue());
                LOGGER.info("EtcdClient getSingle, key={}, value={}", key, value);
                return value;
            }).get();
        } catch (Exception e) {
            WechatLog.error("EtcdClient getSingle fail, key={}, e=", key, e);
        }
        return null;
    }

    /**
     * 获取指定前缀的kv对。
     *
     * @param key etcd key prefix。
     * @return etcd内容。（按照返回顺序）
     */
    public Map<String, String> getPrefix(String key) {
        try {
            return this.executor.submit(() -> {
                final GetOption option = GetOption.newBuilder()
                        .isPrefix(true)
                        // 读取的时候坚决不使用序列化模式，使用线性化模式。
                        // 线性化模式返回达成共识后的值。序列化模式在未达成共识的时候也可以返回，吞吐更好。
                        .withSerializable(false)
                        .build();
                final GetResponse getResponse = etcdClient.getKVClient()
                        .get(EtcdUtils.bytesOf(key), option)
                        .get(EtcdUtils.ETCD_TIME_OUT_TIME_MS, TimeUnit.MILLISECONDS);
                // 按照顺序返回
                final Map<String, String> ret = new LinkedHashMap<>(getResponse.getKvs().size());
                for (final KeyValue kv : getResponse.getKvs()) {
                    ret.put(EtcdUtils.toString(kv.getKey()), EtcdUtils.toString(kv.getValue()));
                }
                LOGGER.info("EtcdClient getPrefix, key={}, value={}", key, ret);
                return ret;
            }).get();
        } catch (Exception e) {
            WechatLog.error("EtcdClient getPrefix key={}, e=", key, e);
        }
        return null;
    }

    /**
     * 删除一个key，会同时删除本机对于此Key的KeepAlive。
     *
     * @return true 成功；false失败。
     */
    public boolean deleteSingle(String key) {
        try {
            return this.executor.submit(() -> {
                final DeleteOption option = DeleteOption.newBuilder()
                        .isPrefix(false)
                        .withPrevKV(true)
                        .build();
                final DeleteResponse deleteResponse = etcdClient.getKVClient()
                        .delete(EtcdUtils.bytesOf(key), option)
                        .get(EtcdUtils.ETCD_TIME_OUT_TIME_MS, TimeUnit.MILLISECONDS);
                if (deleteResponse.getDeleted() != 0L) {
                    LOGGER.info("EtcdClient deleteSingle, key={}, value={}", key, EtcdUtils.toString(deleteResponse.getPrevKvs().getFirst().getValue()));
                    return true;
                }
                LOGGER.info("EtcdClient deleteSingle, key={} count={}", key, deleteResponse.getDeleted());
                // 清理Key，去除Key和Lease的关系
                this.keeper.clearLeaseMap(key, false);
                return false;
            }).get();
        } catch (Exception e) {
            WechatLog.error("EtcdClient deleteSingle, key={}, e=", key, e);
        }
        return false;
    }

    /**
     * 删除指定前缀的kv，会同时删除本机对于此前缀的KeepAlive。
     *
     * @return -1 表示删除失败; >=0 删除数量;
     */
    public long deletePrefix(String key) {
        try {
            return this.executor.submit(() -> {
                final DeleteOption option = DeleteOption.newBuilder()
                        .isPrefix(true)
                        .withPrevKV(true)
                        .build();
                final DeleteResponse deleteResponse = etcdClient.getKVClient()
                        .delete(EtcdUtils.bytesOf(key), option)
                        .get(EtcdUtils.ETCD_TIME_OUT_TIME_MS, TimeUnit.MILLISECONDS);
                if (deleteResponse.getDeleted() == 0L) {
                    LOGGER.info("EtcdClient deletePrefix, key={}, count=0", key);
                    return 0L;
                }
                // 清理Key，去除Key和Lease的关系
                this.keeper.clearLeaseMap(key, true);
                // 构造返回数据
                final List<String> keys = new ArrayList<>(deleteResponse.getPrevKvs().size());
                for (final KeyValue kv : deleteResponse.getPrevKvs()) {
                    keys.add(EtcdUtils.toString(kv.getKey()));
                }
                LOGGER.info("EtcdClient deletePrefix, key={}, count={}, keys={}", key, deleteResponse.getDeleted(), keys);
                return deleteResponse.getDeleted();
            }).get();
        } catch (Exception e) {
            WechatLog.error("EtcdClient deletePrefix, key={}, e=", key, e);
        }
        return -1;
    }

    /**
     * 设置某个key，会同时删除本机对于此Key的KeepAlive。
     *
     * @param key   键。
     * @param value 值。
     */
    public boolean put(final String key, final String value) {
        try {
            return this.executor.submit(() -> {
                final PutOption putOption = PutOption.newBuilder()
                        .withPrevKV()
                        .build();
                final PutResponse putResponse = etcdClient.getKVClient()
                        .put(EtcdUtils.bytesOf(key), EtcdUtils.bytesOf(value), putOption)
                        .get(EtcdUtils.ETCD_TIME_OUT_TIME_MS, TimeUnit.MILLISECONDS);
                this.keeper.clearLeaseMap(key, false);
                final KeyValue prevKv = putResponse.getPrevKv();
                if (prevKv != null) {
                    LOGGER.info("EtcdClient put, key={}, old={}, new={}", key, EtcdUtils.toString(prevKv.getValue()), value);
                    return true;
                }
                LOGGER.info("EtcdClient put, key={}, new={}, old not exist", key, value);
                return true;
            }).get();
        } catch (Exception e) {
            WechatLog.error("EtcdClient put, key={}, value={}, e=", key, value, e);
        }
        return false;
    }


    /**
     * 设置包含租约过期事件的键值对，会更新此Key的KeepAlive。
     *
     * @param key          键。
     * @param value        值。
     * @param leaseTimeSec 租约过期时间。
     * @return 成功则返回租约id;失败的返回值为 <=0的值。
     */
    public long putWithAliveLease(final String key, final String value, final long leaseTimeSec) {
        if (leaseTimeSec <= 0) {
            throw new GeminiException("EtcdClient putWithAliveLease, key={}, value={}, leaseTimeSec not illegal!", key, value);
        }
        try {
            return this.executor.submit(() -> {
                final long leaseId = this.keeper.keepKeyValueWithTTL(key, value, leaseTimeSec);
                LOGGER.debug("EtcdClient putWithAliveLease, key={}, value={}, leaseTimeSec={}, with leaseId={}!", key, value, leaseTimeSec, leaseId);
                return leaseId;
            }).get();
        } catch (Exception e) {
            WechatLog.error("EtcdClient putWithLeaseTime, key={}, value={}, leaseTimeSec={}, e=", key, value, leaseTimeSec, e);
        }
        return -1;
    }


    /**
     * compare and set  如果v与期望值相等，则设置为更新值，会同时删除本机对于此Key的KeepAlive。
     *
     * @param key     key。
     * @param exceptV old值。
     * @param updateV 新的值。
     * @return true 成功; false失败。
     */
    public boolean cas(String key, String exceptV, String updateV) {
        try {
            return this.executor.submit(() -> {
                // 是否相等的比较
                final PutOption putOption = PutOption.DEFAULT;
                final Cmp cmp = new Cmp(EtcdUtils.bytesOf(key), Cmp.Op.EQUAL, CmpTarget.value(EtcdUtils.bytesOf(exceptV)));
                // 事务
                final TxnResponse txnResponse = etcdClient.getKVClient().txn()
                        .If(cmp)
                        .Then(Op.put(EtcdUtils.bytesOf(key), EtcdUtils.bytesOf(updateV), putOption))
                        .commit()
                        .get(EtcdUtils.ETCD_TIME_OUT_TIME_MS, TimeUnit.MILLISECONDS);
                if (!txnResponse.isSucceeded()) {
                    LOGGER.error("EtcdClient cas fail, key={}, exceptV={}, updateV={}", key, exceptV, updateV);
                    return false;
                }
                this.keeper.clearLeaseMap(key, false);
                LOGGER.info("EtcdClient cas, key={}, exceptV={}, updateV={}", key, exceptV, updateV);
                return CollectionUtils.isNotEmpty(txnResponse.getPutResponses());
            }).get();
        } catch (Exception e) {
            WechatLog.error("EtcdClient cas, key={}, exceptV={}, updateV={}, e=", key, exceptV, updateV, e);
        }
        return false;
    }

    /**
     * 如果key不存在，则设置value，会同时删除本机对于Key的KeepAlive。
     *
     * @param key   值。
     * @param value 数值。
     * @return true 设置成功; false 失败。
     */
    public boolean setNX(String key, String value) {
        try {
            return this.executor.submit(() -> {
                // 是否相等的比较
                final Cmp cmp = new Cmp(EtcdUtils.bytesOf(key), Cmp.Op.EQUAL, CmpTarget.createRevision(0));
                final PutOption option = PutOption.DEFAULT;
                final TxnResponse txnResponse = etcdClient.getKVClient().txn()
                        .If(cmp)
                        .Then(Op.put(EtcdUtils.bytesOf(key), EtcdUtils.bytesOf(value), option))
                        .commit()
                        .get(EtcdUtils.ETCD_TIME_OUT_TIME_MS, TimeUnit.MILLISECONDS);
                if (!txnResponse.isSucceeded()) {
                    LOGGER.info("EtcdClient setNX, key={}, value={}", key, value);
                    return false;
                }
                this.keeper.clearLeaseMap(key, false);
                LOGGER.info("EtcdClient setNX, key={}, value={}", key, value);
                return CollectionUtils.isNotEmpty(txnResponse.getPutResponses());
            }).get();
        } catch (Exception e) {
            WechatLog.error("EtcdClient setNX, key={}, update={}, e=", key, value, e);
        }
        return false;
    }

    /**
     * 监听Key对应的值的变化。
     *
     * @param key     key
     * @param handler 消息监听器。（保证并发安全）
     * @return true 成功; false 失败。
     */
    public boolean watch(final String key, IWatchHandler handler) {
        try {
            return this.executor.submit(() -> {
                this.watcher.watch(key, handler, false);
                return true;
            }).get();
        } catch (Exception e) {
            WechatLog.error("EtcdClient watch fail! key={}, e=", key, e);
        }
        return false;
    }

    /**
     * 监听前缀对应的Key集合的值的变化。
     *
     * @param key     消息key
     * @param handler 消息监听器。（保证并发安全）
     * @return true 成功; false 失败;
     */
    public boolean watchPrefix(String key, IWatchHandler handler) {
        try {
            return this.executor.submit(() -> {
                this.watcher.watch(key, handler, true);
                return true;
            }).get();
        } catch (Exception e) {
            WechatLog.error("EtcdClient watchPrefix fail! key={}, e=", key, e);
        }
        return false;
    }

    /**
     * 取消监听。
     *
     * @param key      key。
     * @param isPrefix 是否前缀。
     * @return true 成功; false 失败。
     */
    public boolean unwatch(String key, boolean isPrefix) {
        try {
            return this.executor.submit(() -> {
                this.watcher.unwatch(key, isPrefix);
                return true;
            }).get();
        } catch (Exception e) {
            WechatLog.error("EtcdClient watchPrefix fail! key={}, e=", key, e);
        }
        return false;
    }

    /**
     * 关闭EtcdClient。
     */
    public void shutDown() {
        try {
            LOGGER.info("EtcdClient shutdown begin");
            this.executor.execute(() -> {
                this.watcher.close();
                this.keeper.close();
            });
            this.executor.shutdown();
            final boolean isOk = this.executor.awaitTermination(5, TimeUnit.SECONDS);
            LOGGER.info("EtcdClient shutdown executor isOk={}", isOk);
        } catch (Exception e) {
            LOGGER.error("EtcdClient shutDown executor, e=", e);
        }
        this.etcdClient.close();
        LOGGER.info("EtcdClient shutdown end");
    }
}

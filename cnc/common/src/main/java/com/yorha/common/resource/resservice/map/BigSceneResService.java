package com.yorha.common.resource.resservice.map;

import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.MonsterCategory;
import com.yorha.proto.CommonEnum.SceneObjQuality;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.*;

import javax.annotation.Nullable;
import java.util.*;

/**
 * <AUTHOR>
 */
public class BigSceneResService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(BigSceneResService.class);
    private Map<Integer, Map<MonsterCategory, Map<SceneObjQuality, Map<Integer, List<MonsterTemplate>>>>> monsterSpawnMap = new HashMap<>();
    private Map<Integer, TreeMap<Integer, Map<MonsterCategory, Map<SceneObjQuality, List<BigSceneSpawnRegionTemplate>>>>> monsterSpawnRegionMap = new HashMap<>();
    private Map<Integer, Map<CommonEnum.MapBuildingType, List<Integer>>> monsterSpawnBuildingMap = new HashMap<>();
    private Map<Integer, MonsterParametersTemplate> storyId2MonsterParameterMap = new HashMap<>();
    /**
     * 刷怪里程碑
     */
    private Map<Integer, TreeMap<Integer, BigSceneMonsterMilestoneTemplate>> monsterMilestoneMap = new HashMap<>();
    /**
     * 大地图搜索数据
     */
    private TreeMap<Integer, BigSceneSearchTemplate> searchMap = new TreeMap<>();

    public BigSceneResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        // 建筑类型->守护者刷怪配置
        for (BigSceneSpawnBuildingTemplate template : getResHolder().getMap(BigSceneSpawnBuildingTemplate.class).values()) {
            monsterSpawnBuildingMap.computeIfAbsent(template.getStoryId(), k -> new HashMap<>())
                    .computeIfAbsent(template.getType(), k -> new ArrayList<>())
                    .add(template.getId());
        }
        // 根据类型、品质、等级区分怪物
        Map<Integer, Map<MonsterCategory, Map<SceneObjQuality, Map<Integer, List<MonsterTemplate>>>>> spawnMap = new HashMap<>();
        getResHolder().getListFromMap(MonsterTemplate.class).forEach(template -> {
            spawnMap.computeIfAbsent(template.getStoryId(), k -> new HashMap<>())
                    .computeIfAbsent(template.getCategory(), k -> new HashMap<>())
                    .computeIfAbsent(template.getQuality(), k -> new HashMap<>())
                    .computeIfAbsent(template.getLevel(), k -> new ArrayList<>())
                    .add(template);
        });
        monsterSpawnMap = spawnMap;

        // 根据类型、品质、设置各个区域怪物
        Map<Integer, TreeMap<Integer, Map<MonsterCategory, Map<SceneObjQuality, List<BigSceneSpawnRegionTemplate>>>>> regionMap = new HashMap<>();
        getResHolder().getListFromMap(BigSceneSpawnRegionTemplate.class).forEach(template -> {
            regionMap.computeIfAbsent(template.getStoryId(), k -> new TreeMap<>())
                    .computeIfAbsent(template.getMilestone(), k -> new HashMap<>())
                    .computeIfAbsent(template.getCategory(), k -> new HashMap<>())
                    .computeIfAbsent(template.getQuality(), k -> new ArrayList<>())
                    .add(template);
        });
        monsterSpawnRegionMap = regionMap;

        // 根据开服时间设置里程碑
        Map<Integer, TreeMap<Integer, BigSceneMonsterMilestoneTemplate>> monsterMilestone = new HashMap<>();
        getResHolder().getListFromMap(BigSceneMonsterMilestoneTemplate.class).forEach(template -> {
            monsterMilestone.computeIfAbsent(template.getStoryId(), k -> new TreeMap<>())
                    .put(template.getTimePeriod(), template);
        });
        monsterMilestoneMap = monsterMilestone;

        // 根据对象等级保存数据
        TreeMap<Integer, BigSceneSearchTemplate> search = new TreeMap<>();
        getResHolder().getListFromMap(BigSceneSearchTemplate.class).forEach(template -> {
            search.put(template.getLevel(), template);
        });
        searchMap = search;

        getResHolder().getListFromMap(MonsterParametersTemplate.class).forEach(template -> {
            storyId2MonsterParameterMap.put(template.getStoryId(), template);
        });
    }

    /**
     * 获取剧本id对应的野怪参数配置，目前有搜索野怪最大最小级
     */
    @Nullable
    public MonsterParametersTemplate getMonsterParametersTemplate(int storyId) {
        return storyId2MonsterParameterMap.get(storyId);
    }

    @Override
    public void checkValid() throws ResourceException {
        // 无极缩放视野限制配置参数强校验
        for (WorldLayerTemplate data : getResHolder().getListFromMap(WorldLayerTemplate.class)) {
            if (data.getAoiHeightPair() == null || data.getAoiWidthPair() == null) {
                throw new ResourceException(StringUtils.format("WorldLayerTemplate layer: {} no AoiHeight or AoiWidth", data.getId()));
            }
        }
        // 守护者配置校验
        for (BigSceneSpawnBuildingTemplate template : getResHolder().getMap(BigSceneSpawnBuildingTemplate.class).values()) {
            for (IntPairType pair : template.getRefreshTimePairList()) {
                // 小时校验
                if (pair.getKey() >= 24) {
                    throw new ResourceException("BigSceneSpawnBuilding:{} time rule error", template.getId());
                }
                if (pair.getKey() < 0) {
                    throw new ResourceException("BigSceneSpawnBuilding:{} time rule error", template.getId());
                }
                // 分钟校验
                if (pair.getValue() >= 60) {
                    throw new ResourceException("BigSceneSpawnBuilding:{} time rule error", template.getId());
                }
                if (pair.getValue() < 0) {
                    throw new ResourceException("BigSceneSpawnBuilding:{} time rule error", template.getId());
                }
            }
        }

        for (BigSceneSearchTemplate template : getResHolder().getListFromMap(BigSceneSearchTemplate.class)) {
            int limit = getResHolder().getConstTemplate(ConstTemplate.class).getMonsterGenerateRangeNum();
            if (template.getPara() > limit) {
                throw new ResourceException("BigSceneSearchTemplate:{} para should less than {}", template.getId(), limit);
            }
        }

        // 野怪里程碑
        if (!getResHolder().getMap(BigSceneMonsterMilestoneTemplate.class).containsKey(1)) {
            throw new ResourceException("BigSceneMonsterMilestoneTemplate must have id 1");
        }
    }

    @Override
    public String getResName() {
        return getClass().getSimpleName();
    }

    public Map<CommonEnum.MapBuildingType, List<Integer>> getMonsterSpawnBuildingMap(int kvkId) {
        return monsterSpawnBuildingMap.get(kvkId);
    }

    /**
     * 根据类型和品质获取各个区域的刷怪数量数据
     */
    public List<BigSceneSpawnRegionTemplate> getMonsterSpawnRegionTemplates(int storyId, int hour, MonsterCategory category, SceneObjQuality quality) {
        int key = 0;
        // 只有大世界野怪有里程碑的概念
        if (category == MonsterCategory.BIG_SCENE_ACTIVE) {
            BigSceneMonsterMilestoneTemplate monsterMilestone = getMonsterMilestone(storyId, hour);
            Integer key1 = monsterSpawnRegionMap.get(storyId).ceilingKey(monsterMilestone.getId());
            if (key1 == null) {
                key = monsterSpawnRegionMap.get(storyId).lastKey();
            } else {
                key = key1;
            }
        }
        LOGGER.info("BigSceneResService getMonsterSpawnRegionTemplates storyId={}, hour={}, milestone={}", storyId, hour, key);
        return monsterSpawnRegionMap.get(storyId).get(key).getOrDefault(category, new HashMap<>()).getOrDefault(quality, new ArrayList<>());
    }

    public List<BigSceneSpawnRegionTemplate> getRallyMonsterTemplates(int storyId, int level, MonsterCategory category, SceneObjQuality quality) {
        // K服里程碑初始值为-1，有默认刷怪
        if (level == 0) {
            return new ArrayList<>();
        }
        TreeMap<Integer, Map<MonsterCategory, Map<SceneObjQuality, List<BigSceneSpawnRegionTemplate>>>> levelMap = monsterSpawnRegionMap.get(storyId);
        if (levelMap == null) {
            return new ArrayList<>();
        }
        Map<MonsterCategory, Map<SceneObjQuality, List<BigSceneSpawnRegionTemplate>>> categoryMap = levelMap.get(level);
        if (categoryMap == null) {
            return new ArrayList<>();
        }
        Map<SceneObjQuality, List<BigSceneSpawnRegionTemplate>> sceneObjQualityListMap = categoryMap.get(category);
        if (sceneObjQualityListMap == null) {
            return new ArrayList<>();
        }
        return sceneObjQualityListMap.get(quality);
    }

    /**
     * 根据类型、品质、等级获取刷怪数据
     */
    public List<MonsterTemplate> getMonsterTemplates(MonsterCategory category, SceneObjQuality quality, int level, int kvkId) {
        return monsterSpawnMap.getOrDefault(kvkId, new HashMap<>())
                .getOrDefault(category, new HashMap<>())
                .getOrDefault(quality, new HashMap<>())
                .getOrDefault(level, new ArrayList<>());
    }

    /**
     * 根据开服时间获取里程碑
     */
    public BigSceneMonsterMilestoneTemplate getMonsterMilestone(int storyId, int hour) {

        Integer key = monsterMilestoneMap.get(storyId).ceilingKey(hour);
        if (key == null) {
            key = monsterMilestoneMap.get(storyId).lastKey();
        }

        return monsterMilestoneMap.get(storyId).get(key);
    }

    /**
     * 获取搜索数据
     */
    public BigSceneSearchTemplate getSearchTemplate(int level) {
        Integer key = searchMap.ceilingKey(level);
        if (key == null) {
            key = searchMap.lastKey();
        }

        return searchMap.get(key);
    }
}

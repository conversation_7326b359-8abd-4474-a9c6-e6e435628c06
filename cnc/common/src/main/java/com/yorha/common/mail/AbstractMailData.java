package com.yorha.common.mail;

import com.google.common.collect.Maps;
import com.yorha.proto.StructMail;

import java.util.Collections;
import java.util.Map;

public abstract class AbstractMailData {
    /**
     * 邮件集合
     */
    private final Map<Long, StructMail.NewMailCache> mailCacheMap = Maps.newHashMap();

    /**
     * 邮件最大下标
     */
    private int maxMailIndex = 0;

    public void addMailCache(StructMail.NewMailCache mailCache) {
        mailCacheMap.put(mailCache.getMailId(), mailCache);
        maxMailIndex = Math.max(maxMailIndex, mailCache.getMailIndex());
    }

    public int getNextMailIndex() {
        return ++maxMailIndex;
    }

    public int getMaxMailIndex() {
        return maxMailIndex;
    }

    public boolean removeMailCache(long mailId) {
        StructMail.NewMailCache remove = mailCacheMap.remove(mailId);
        if (remove == null) {
            return false;
        }
        if (maxMailIndex == remove.getMailIndex()) {
            maxMailIndex--;
        }
        return true;
    }

    public Map<Long, StructMail.NewMailCache> getMailCacheMap() {
        return Collections.unmodifiableMap(mailCacheMap);
    }
}

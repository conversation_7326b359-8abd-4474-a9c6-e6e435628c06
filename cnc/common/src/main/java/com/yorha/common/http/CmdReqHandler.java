package com.yorha.common.http;

import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.utils.reflect.MethodWrapper;
import io.netty.channel.Channel;
import io.netty.handler.codec.http.FullHttpRequest;
import io.netty.handler.codec.http.FullHttpResponse;
import io.netty.handler.codec.http.HttpMethod;
import io.netty.handler.codec.http.QueryStringDecoder;
import io.netty.handler.codec.http.multipart.*;
import org.apache.commons.collections4.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 后端指令请求处理中心
 *
 * <AUTHOR>
 */
public class CmdReqHandler implements ReqHandler {
    private static final Logger LOGGER = LogManager.getLogger(CmdReqHandler.class);
    private static final HttpDataFactory FACTORY = new DefaultHttpDataFactory(false);
    /**
     * 方法映射
     */
    private final Map<String, MethodWrapper> cachedMethod = new HashMap<>();

    public CmdReqHandler(CommonCmdProcessor processor) {
        cachedMethod.putAll(registerMethods(processor));
    }

    @Override
    public String getUrl() {
        return "/cmd";
    }

    @Override
    public void processRequest(Channel channel, FullHttpRequest req, FullHttpResponse resp) {
        QueryStringDecoder queryStringDecoder = new QueryStringDecoder(req.uri());
        Map<String, String> store = new HashMap<>();
        Map<String, List<String>> map = queryStringDecoder.parameters();
        for (Map.Entry<String, List<String>> entry : map.entrySet()) {
            store.put(entry.getKey(), entry.getValue().getFirst().trim());
        }
        // store
        if (req.method() == HttpMethod.POST) {
            Map<String, String> postStore = this.getParamMap(req);
            if (MapUtils.isNotEmpty(postStore)) {
                store.putAll(postStore);
            }
        }
        String functionName = store.get("funName");
        if (StringUtils.isNotEmpty(functionName)) {
            MethodWrapper wrapper = cachedMethod.get(functionName);
            if (wrapper != null) {
                Class<?> clazz = wrapper.getParamClazz();
                Object paramObject;
                String param = store.get("param");
                LOGGER.info("processRequest param={}", param);
                try {
                    paramObject = JsonUtils.parseObject(param, clazz);
                } catch (Exception e) {
                    LOGGER.error("Error processRequest: , fun={}", functionName, e);
                    return;
                }
                Object result = wrapper.invoke(paramObject);
                LOGGER.info("fun={},param={}", functionName, param);
                resp.content().writeBytes(result.toString().getBytes());
            } else {
                throw new GeminiException(StringUtils.format("current function {} is not defined", functionName));
            }
        } else {
            LOGGER.warn("request illegal url={}", req.uri());
        }
    }

    public Map<String, String> getParamMap(FullHttpRequest req) {
        Map<String, String> ret = new HashMap<>();
        HttpPostRequestDecoder decoder = new HttpPostRequestDecoder(FACTORY, req);
        List<InterfaceHttpData> list = decoder.getBodyHttpDatas();
        for (InterfaceHttpData e : list) {
            Attribute attribute = (Attribute) e;
            try {
                ret.put(attribute.getName(), attribute.getValue());
                FACTORY.removeHttpDataFromClean(req, e);
            } catch (IOException ex) {
                LOGGER.error(ex.getMessage(), ex);
            } finally {
                e.release();
            }
        }
        decoder.destroy();
        return ret;
    }
}

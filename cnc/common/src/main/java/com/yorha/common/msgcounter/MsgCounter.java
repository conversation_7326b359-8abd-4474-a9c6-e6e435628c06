package com.yorha.common.msgcounter;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.io.MsgType;
import com.yorha.common.perf.CsPerfLogger;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.concurrent.NotThreadSafe;
import java.util.HashMap;
import java.util.Map;

/**
 * 单线程！！
 * 消息统计器
 *
 * <AUTHOR>
 */
@NotThreadSafe
public class MsgCounter {
    private static final Logger LOGGER = LogManager.getLogger(MsgCounter.class);
    public static final CsPerfLogger PERF_LOGGER = new CsPerfLogger();
    // <seqId, <msgType, TsMs>>
    private Map<Integer, Pair<Integer, Long>> msgSeqIdMap;

    /**
     * 收到客户端一条协议
     */
    public void onReceiveClientMsg(int msgType, String msgName, int seqId, int msgSize) {
        if (seqId < 0) {
            throw new GeminiException("GeminiMsgCounter onReceiveClientMsg msgType:{} {} seqId:{} < 0", msgName, msgType, seqId);
        } else if (seqId == 0) {
            // 是心跳包，唯一一个cs的seqId为0
        } else {
            if (msgSeqIdMap == null) {
                msgSeqIdMap = new HashMap<>();
            }
            // 正常的c2s
            if (msgSeqIdMap.containsKey(seqId)) {
                // 说明seqId重复！
                throw new GeminiException("GeminiMsgCounter onReceiveClientMsg msgType:{} {} seqId:{} already exist", msgName, msgType, seqId);
            }
            msgSeqIdMap.put(seqId, new Pair<>(msgType, SystemClock.nowNative()));
        }
        PERF_LOGGER.logC2C(msgName, msgSize);
    }

    /**
     * 发送客户端一条协议
     */
    public void onSendMsg(int msgType, String msgName, int seqId, long msgSize) {
        if (seqId < 0) {
            throw new GeminiException("GeminiMsgCounter onSendMsg msgType:{} {} seqId:{} < 0", msgName, msgType, seqId);
        } else if (seqId == 0) {
            // 说明不是c2s的s2c回包，应该是个ntf
        } else {
            // 是个s2c的回包
            Pair<Integer, Long> remove = msgSeqIdMap.remove(seqId);
            if (remove == null) {
                // seqId没有了？
                throw new GeminiException("GeminiMsgCounter onSendMsg msgType:{} {} seqId:{} not exist", msgName, msgType, seqId);
            }
            // 当时发送的c2s的msgType
            Integer first = remove.getFirst();
            // 当时发送的时间戳
            Long second = remove.getSecond();

            int retMsgId = MsgType.getRetMsgId(first);
            if (retMsgId != msgType) {
                throw new GeminiException("GeminiMsgCounter onSendMsg msgType:{} {} seqId:{} first:{} second:{} retMsgId:{} not equals",
                        msgName, msgType, seqId, first, second, retMsgId);
            }

            long phased = SystemClock.nowNative() - second;
            if (phased < 0) {
                throw new GeminiException("GeminiMsgCounter onSendMsg msgType:{} {} seqId:{} first:{} second:{} retMsgId:{} phased:{} < 0",
                        msgName, msgType, seqId, first, second, retMsgId, phased);
            } else if (phased == 0) {
                // 也许可能呢？速度真快，0ms
            } else if (phased < 100) {
                // 100ms内，没问题吧
            } else if (phased < 200) {
                LOGGER.warn("GeminiMsgCounter msgType:{} {} cost:{} ms", msgName, msgType, phased);
            } else if (phased < 800) {
                // 800ms，有问题！
                LOGGER.warn("GeminiMsgCounter too much msgType:{} {} cost:{} ms", msgName, msgType, phased);
            } else {
                // 大问题
                LOGGER.warn("GeminiMsgCounter so too much msgType:{} {} cost:{} ms", msgName, msgType, phased);
            }
            PERF_LOGGER.logS2C(msgName, msgSize, phased);
        }

        PERF_LOGGER.logNtf(msgName, msgSize);
    }
}

package com.yorha.common.resource.resservice.marquee;


import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.utils.Pair;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum.MarqueeType;
import res.template.MarqueeMessageTemplate;
import res.template.MarqueeTypeTemplate;
import res.template.RadomGoodsMarqueeTemplate;

import java.util.*;

/**
 * 跑马灯配置
 *
 * <AUTHOR>
 */
public class MarqueeResService extends AbstractResService {

    private final Map<MarqueeType, MarqueeMessageTemplate> templateMap = new EnumMap<>(MarqueeType.class);

    private final Map<Integer, Set<Pair<Integer, Integer>>> goods2SpItem = new HashMap<>();

    public MarqueeResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        for (MarqueeTypeTemplate template : getResHolder().getListFromMap(MarqueeTypeTemplate.class)) {
            MarqueeMessageTemplate valueFromMap = getResHolder().checkValueFromMap(MarqueeMessageTemplate.class, template.getMarqueeId(),
                    () -> StringUtils.format("跑马灯配置不存在 MarqueeMessageTemplate. marqueeId:{}", template.getMarqueeId()));
            this.templateMap.put(template.getMessage(), valueFromMap);
        }

        for (RadomGoodsMarqueeTemplate template : getResHolder().getListFromMap(RadomGoodsMarqueeTemplate.class)) {
            final Set<Pair<Integer, Integer>> spItemSet = this.goods2SpItem.computeIfAbsent(template.getId(), key -> new HashSet<>());
            for (IntPairType pair : template.getSpItemPairList()) {
                final Pair<Integer, Integer> itemPair = new Pair<>(pair.getKey(), pair.getValue());
                spItemSet.add(itemPair);
            }
        }
    }

    public Set<Pair<Integer, Integer>> getSpItem(int goodsId) {
        return goods2SpItem.get(goodsId);
    }

    public Integer getMarqueeId(MarqueeType type) {
        return templateMap.get(type).getId();
    }

    @Override
    public void checkValid() throws ResourceException {
        for (MarqueeType type : MarqueeType.values()) {
            if (templateMap.get(type) == null && type != MarqueeType.COMMON_MARQUEE) {
                throw new ResourceException(StringUtils.format("跑马灯配置枚举:{} 未配置", type));
            }
        }
    }

}

package com.yorha.common.clan;

import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.server.ServerContext;
import com.yorha.proto.CommonEnum.ClanOperationType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ClanAuthorityTemplate;
import res.template.ClanOfficeTemplate;

import java.util.Collection;

/**
 * 军团权限检查的工具类
 *
 * <AUTHOR>
 */
public class ClanPermissionUtils {
    private static final Logger LOGGER = LogManager.getLogger(ClanPermissionUtils.class);

    /**
     * 检查传入的职位是否有对应权限，若权限不存在，职位不存在或职位没有对应权限都将抛出GeminiException，需要上层处理
     * 权限存在、职位存在，职位有对应权限情况下无任何返回
     *
     * @param operation     操作类型
     * @param playerStaffId 玩家staffId
     * @throws GeminiException 权限不存在、职位不存在、职位没有对应权限
     */
    public static void checkPermission(ClanOperationType operation, int playerStaffId) throws GeminiException {
        // gm跳过校验
        if (ServerContext.getServerDebugOption().isSkipClanPermission()) {
            return;
        }
        // 检查operation是否正确
        if (operation == ClanOperationType.COT_NONE) {
            throw new GeminiException(ErrorCode.CLAN_OPERATION_NOT_EXIST);
        }

        // 遍历找到玩家职位对应的单条配置，不直接取template的原因是希望能抛出特定的业务异常
        Collection<ClanOfficeTemplate> templates = ResHolder.getInstance().getListFromMap(ClanOfficeTemplate.class);
        for (ClanOfficeTemplate template : templates) {
            if (template.getId() != playerStaffId) {
                continue;
            }
            // 找到对应的职位配置
            int staffLevel = template.getStaffLevel();
            boolean isSpecialOfficial = template.getStaffLimit() == 1;
            // 检查职位是否有对应权限
            if (hasPermission(operation, staffLevel, isSpecialOfficial)) {
                return;
            }
            // 职位没有对应权限
            throw new GeminiException(ErrorCode.CLAN_NO_PERMIT);
        }
        // 未找到职位配置，职位不存在
        LOGGER.warn("checkPermission: not found config for {}", playerStaffId);
        throw new GeminiException(ErrorCode.CLAN_STAFF_NOT_EXIST);
    }

    /**
     * 检查传入的职位返回是否有对应权限，不会抛出GeminiException
     *
     * @param operation     操作类型
     * @param playerStaffId 玩家staffId
     * @return 若权限不存在，职位不存在或职位没有对应权限都将返回false，否则返回true
     */
    public static boolean hasPermissionNoThrow(ClanOperationType operation, int playerStaffId) {
        // 检查operation是否正确
        if (operation == ClanOperationType.COT_NONE) {
            return false;
        }

        // 直接通过ID查找玩家职位对应的配置
        ClanOfficeTemplate template = ResHolder.getInstance().findValueFromMap(ClanOfficeTemplate.class, playerStaffId);
        if (template == null) {
            // 未找到职位配置，职位不存在
            LOGGER.warn("checkPermission: not found config for {}", playerStaffId);
            return false;
        }

        // 找到对应的职位配置
        int staffLevel = template.getStaffLevel();
        boolean isSpecialOfficial = template.getStaffLimit() == 1;
        // 检查职位是否有对应权限
        return hasPermission(operation, staffLevel, isSpecialOfficial);
    }

    /**
     * 检查对应职位等级是否有某操作的权限
     *
     * @param operation         操作
     * @param playerStaffLevel  玩家职位等级
     * @param isSpecialOfficial 是否是特殊官员
     * @return 有权限则返回true，没有或没找到配置则返回false
     */
    private static boolean hasPermission(ClanOperationType operation, int playerStaffLevel, boolean isSpecialOfficial) {
        return ResHolder.getInstance().getListFromMap(ClanAuthorityTemplate.class)
                .stream()
                .filter(template -> operation == template.getOpType())
                .findFirst()
                .map(template -> checkPermissionForTemplate(template, playerStaffLevel, isSpecialOfficial))
                .orElse(false);
    }

    /**
     * 检查特定权限模板是否允许玩家执行操作
     *
     * @param template          权限模板
     * @param playerStaffLevel  玩家职位等级
     * @param isSpecialOfficial 是否是特殊官员
     * @return 有权限则返回true，否则返回false
     */
    private static boolean checkPermissionForTemplate(ClanAuthorityTemplate template, int playerStaffLevel, boolean isSpecialOfficial) {
        // 默认成员等级中不包含传入的玩家职位等级
        if (!template.getDefaultAllowLevelList().contains(playerStaffLevel)) {
            return false;
        }
        // 玩家职位是特殊官员时或者配置不要求是特殊官员时均表示玩家拥有对应权限
        return isSpecialOfficial || !template.getOfficiallyExclusive();
    }
}

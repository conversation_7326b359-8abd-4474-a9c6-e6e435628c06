package com.yorha.common.helper;

import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class SeasonActivityHelper {
    protected static final Logger LOGGER = LogManager.getLogger(SeasonActivityHelper.class);

    public static CommonEnum.CommanderActivtyStage getActStage(CommonEnum.ZoneSeason season, CommonEnum.ZoneSeasonStage stage) {
        switch (season) {
            case ZS_BUILDING: {
                return CommonEnum.CommanderActivtyStage.CAS_BUILDING;
            }
            case ZS_PREPARE: {
                return CommonEnum.CommanderActivtyStage.CAS_BEFORE_K1;
            }
            default:
                LOGGER.warn("ZoneBestCommanderUnit getActStage failed season={}, stage={}", season, stage);
                return CommonEnum.CommanderActivtyStage.CAS_NONE;
        }
    }
}

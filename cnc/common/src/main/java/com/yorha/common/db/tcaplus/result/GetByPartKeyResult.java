package com.yorha.common.db.tcaplus.result;

import com.google.protobuf.Message;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.msg.GameDbResp;

import java.util.Collections;
import java.util.List;

public class GetByPartKeyResult<T extends Message.Builder> implements GameDbResp {
    public TcaplusErrorCode code = TcaplusErrorCode.GEN_ERR_SUC;
    public List<ValueWithVersion<T>> values;
    public long requestId;

    @Override
    public int getCode() {
        return code.getValue();
    }

    public List<ValueWithVersion<T>> getValues() {
        return values == null ? Collections.emptyList() : values;
    }

    @Override
    public boolean isOk() {
        return code == TcaplusErrorCode.GEN_ERR_SUC;
    }
}

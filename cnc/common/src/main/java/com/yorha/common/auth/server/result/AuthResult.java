package com.yorha.common.auth.server.result;

import com.yorha.proto.CommonEnum;

/**
 * 鉴权接口处理的结果，封装正确回包和ErrorCode
 *
 * <AUTHOR>
 */
public class AuthResult<T> {
    public static final int CODE_SUCCESS = 0;

    private final int retCode;
    private final String msg;

    protected AuthResult(int retCode, String msg) {
        this.retCode = retCode;
        this.msg = msg;
    }

    public static <T> AuthResult<T> fail(CommonEnum.AuthRetCode retCode, String msg) {
        return new AuthResult<>(retCode.getNumber(), msg);
    }

    public static <T> AuthResult<T> ofSuccess() {
        return new AuthResult<>(CODE_SUCCESS, "OK");
    }

    public int getRetCode() {
        return retCode;
    }

    public boolean isSuccess() {
        return retCode == CODE_SUCCESS;
    }

    public String getMsg() {
        return msg;
    }

    @Override
    public String toString() {
        return "AuthResult{" +
                "retCode=" + retCode +
                ", msg='" + msg + '\'' +
                '}';
    }
}

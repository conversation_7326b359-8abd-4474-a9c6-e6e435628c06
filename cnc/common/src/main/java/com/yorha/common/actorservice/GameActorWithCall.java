package com.yorha.common.actorservice;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.db.tcaplus.msg.GameDbReq;
import com.yorha.common.db.tcaplus.msg.GameDbResp;


/**
 * 游戏actor基类,带call能力
 *
 * <AUTHOR>
 */
public abstract class GameActorWithCall extends BaseGameActor {

    public GameActorWithCall(ActorSystem system, IActorRef self) {
        super(system, self);
    }

    public <RESP> RESP callZoneRank(GeneratedMessageV3 msg) {
        return call(RefFactory.ofRank(getZoneId()), msg);
    }

    public <RESP> RESP callZoneRank(int zone, GeneratedMessageV3 msg) {
        return call(RefFactory.ofRank(zone), msg);
    }

    public <RESP extends GameDbResp> RESP callGameDb(GameDbReq<RESP> msg) {
        return call(RefFactory.dbActorRef(), msg);
    }

    public <RESP extends GameDbResp> RESP callGameDb(GameDbReq<RESP> msg, long timeoutMs) {
        return call(RefFactory.dbActorRef(), msg, timeoutMs);
    }

    public <RESP> RESP callClan(int zoneId, long clanId, GeneratedMessageV3 msg) {
        return call(RefFactory.ofClan(zoneId, clanId), msg);
    }
    
    public <RESP> RESP callScene(long sceneId, GeneratedMessageV3 msg) {
        return call(RefFactory.ofBigScene((int) sceneId), msg);
    }

    public <RESP> RESP callName(GeneratedMessageV3 msg) {
        return call(RefFactory.ofName(getZoneId()), msg);
    }

    public <RESP> RESP callName(int zoneId, GeneratedMessageV3 msg) {
        return call(RefFactory.ofName(zoneId), msg);
    }

    public <RESP> RESP callZoneChat(int zoneId, GeneratedMessageV3 msg) {
        return call(RefFactory.ofZoneChat(zoneId), msg);
    }

    public <RESP> RESP callZoneCard(GeneratedMessageV3 msg) {
        return call(RefFactory.ofZoneCard(), msg);
    }

    public <RESP> RESP callPlayer(int zoneId, long targetPlayerId, GeneratedMessageV3 msg) {
        return call(RefFactory.ofPlayer(zoneId, targetPlayerId), msg);
    }

    public <RESP> RESP callPlayer(int zoneId, long targetPlayerId, GeneratedMessageV3 msg, long timeoutMs) {
        return call(RefFactory.ofPlayer(zoneId, targetPlayerId), msg, timeoutMs);
    }

    public <RESP> RESP callGroupChat(int chatChannel, String channelId, GeneratedMessageV3 msg) {
        return call(RefFactory.ofGroupChat(chatChannel, channelId), msg);
    }

    public <RESP> RESP callBattle(GeneratedMessageV3 msg) {
        IActorRef actorRef = RefFactory.ofBattle();
        if (actorRef == null) {
            return null;
        }
        return call(actorRef, msg);
    }
}


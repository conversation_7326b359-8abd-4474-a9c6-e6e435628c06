package com.yorha.common.resource.resservice.model;

import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.exception.ResourceException;
import com.yorha.proto.CommonEnum.TroopInteractionType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.InteractionDistanceTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 部队交互距离配置
 *
 * <AUTHOR>
 * 2021年10月18日 21:41:00
 */
public class InteractionDistanceTemplateService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(InteractionDistanceTemplateService.class);

    private final Map<TroopInteractionType, InteractionDistanceTemplate> config = new HashMap<>();

    public InteractionDistanceTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        getResHolder().getListFromMap(InteractionDistanceTemplate.class).forEach(it -> config.put(it.getAimType(), it));
    }

    @Override
    public void checkValid() throws ResourceException {

    }

    public InteractionDistanceTemplate getInteractionDistance(TroopInteractionType type) {
        InteractionDistanceTemplate interactionDistanceTemplate = config.get(type);
        if (interactionDistanceTemplate == null) {
            LOGGER.error("TroopInteractionType config error. type:{} config:{}", type, config);
        }
        return interactionDistanceTemplate;
    }
}

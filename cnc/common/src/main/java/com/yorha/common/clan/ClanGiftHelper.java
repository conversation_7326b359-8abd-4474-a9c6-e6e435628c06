package com.yorha.common.clan;

import com.google.common.collect.Lists;
import com.yorha.common.resource.ResHolder;
import com.yorha.game.gen.prop.ClanGiftItemListProp;
import com.yorha.game.gen.prop.ClanGiftItemProp;
import res.template.ConstClanTemplate;

import java.util.List;

/**
 * 军团礼物发送与删除助手
 * 确保礼物数量不超出策划配置上限
 *
 * <AUTHOR>
 */
public class ClanGiftHelper {
    protected int normalGiftCnt;
    protected int rareGiftCnt;
    private final ClanGiftItemListProp clanGiftItemProps;


    public ClanGiftHelper(ClanGiftItemListProp clanGiftItemProps) {
        this.clanGiftItemProps = clanGiftItemProps;
        normalGiftCnt = (int) clanGiftItemProps.stream().filter(gift -> ClanGiftUtils.isNormalGift(gift.getGiftId())).count();
        rareGiftCnt = clanGiftItemProps.size() - normalGiftCnt;
    }

    /**
     * 删除超生命周期军团礼物
     */
    public final void removeNeedDeleteClanGifts() {
        List<ClanGiftItemProp> giftsToDelete = Lists.newLinkedList();
        for (ClanGiftItemProp giftItemProp : this.getClanGifts()) {
            this.onRemoveTraverse(giftItemProp);
            // 时序添加，剪枝优化
            if (!ClanGiftUtils.isGiftExpired(giftItemProp)) {
                break;
            }
            if (ClanGiftUtils.isGiftNeedDelete(giftItemProp)) {
                giftsToDelete.add(giftItemProp);
                if (ClanGiftUtils.isNormalGift(giftItemProp.getGiftId())) {
                    normalGiftCnt--;
                } else {
                    rareGiftCnt--;
                }
            }
        }
        this.getClanGifts().removeAll(giftsToDelete);

    }

    /**
     * 删除遍历时的扩展接口
     *
     * @param giftItemProp 当前军团礼物
     */
    protected void onRemoveTraverse(ClanGiftItemProp giftItemProp) {
    }

    /**
     * 添加军团礼物（含上限保护）
     *
     * @param newClanGiftItem 待添加军团礼物
     */
    public final void addClanGift(ClanGiftItemProp newClanGiftItem) {
        this.removeNeedDeleteClanGifts();

        //防止无限增长
        if (ClanGiftUtils.isNormalGift(newClanGiftItem.getGiftId())) {
            if (normalGiftCnt >= ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class).getGiftRareStorageLimit()) {
                this.shrinkGiftItem(true);
                normalGiftCnt--;
            }
            normalGiftCnt++;
        } else {
            if (rareGiftCnt >= ResHolder.getInstance().getConstTemplate(ConstClanTemplate.class).getGiftRareStorageLimit()) {
                this.shrinkGiftItem(false);
                rareGiftCnt--;
            }
            rareGiftCnt++;
        }

        this.getClanGifts().add(newClanGiftItem);
    }

    /**
     * 获取ClanGiftItemListProp
     *
     * @return ClanGiftItemListProp
     */
    protected final ClanGiftItemListProp getClanGifts() {
        return this.clanGiftItemProps;
    }

    /**
     * 缩减超上限礼物
     *
     * @param isNormalGift 目标是否是普通军团礼物
     */
    private void shrinkGiftItem(boolean isNormalGift) {
        for (int i = 0; i < this.getClanGifts().size(); i++) {
            ClanGiftItemProp clanGiftItemProp = this.getClanGifts().get(i);
            if (ClanGiftUtils.isNormalGift(clanGiftItemProp.getGiftId()) == isNormalGift) {
                this.getClanGifts().remove(i);
                return;
            }
        }
    }
}

package com.yorha.common.resource.resservice.activity;

import com.google.common.collect.Maps;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.HeroRhTemplate;
import res.template.LotteryHeroRewardTemplate;
import res.template.LotterySeasonTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 幸运大转盘
 *
 * <AUTHOR>
 */
public class ActivityLotterySeasonService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(ActivityLotterySeasonService.class);

    Map<CommonEnum.CommanderActivtyStage, List<LotterySeasonTemplate>> templateMap = Maps.newEnumMap(CommonEnum.CommanderActivtyStage.class);

    public ActivityLotterySeasonService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        for (LotterySeasonTemplate template : getResHolder().getListFromMap(LotterySeasonTemplate.class)) {
            List<LotterySeasonTemplate> volume2Template = templateMap.computeIfAbsent(template.getSeasonStatelottery(), (key) -> new ArrayList<>());
            volume2Template.add(template);
        }
    }

    @Override
    public void checkValid() throws ResourceException {
        for (LotterySeasonTemplate lotterySeasonTemplate : getResHolder().getListFromMap(LotterySeasonTemplate.class)) {
            if (lotterySeasonTemplate.getHeroRewardIdsList() == null || lotterySeasonTemplate.getHeroRewardIdsList().size() <= 0) {
                throw new ResourceException("幸运大转盘，无英雄配置 id={}", lotterySeasonTemplate.getId());
            }
        }
        for (LotteryHeroRewardTemplate template : getResHolder().getListFromMap(LotteryHeroRewardTemplate.class)) {
            if (getResHolder().findValueFromMap(HeroRhTemplate.class, template.getHeroId()) == null) {
                throw new ResourceException("抽奖英雄奖励，英雄id不存在 hero={}", template.getHeroId());
            }
        }
    }

    /**
     * 获取本期自选配置
     *
     * @param stage  赛季阶段
     * @param volume 当前赛季的第几期
     */
    public LotterySeasonTemplate getSeasonConfig(CommonEnum.CommanderActivtyStage stage, int volume) {
        List<LotterySeasonTemplate> zoneSeasonListMap = templateMap.get(stage);
        if (zoneSeasonListMap == null) {
            LOGGER.info("ActivityLotterySeasonService getSeasonConfig null stage={} volumn={}", stage, volume);
            return null;
        }
        // 返回对应周期的配置
        LotterySeasonTemplate foundTemplate = null;
        for (LotterySeasonTemplate seasonTemplate : zoneSeasonListMap) {
            final IntPairType volumePair = seasonTemplate.getVolumePair();
            if (volumePair.getKey() <= volume && volumePair.getValue() > volume) {
                foundTemplate = seasonTemplate;
                break;
            }
        }
        if (foundTemplate != null) {
            return foundTemplate;
        }
        LOGGER.error("ActivityLotterySeasonService getSeasonConfig null stage={} volumn={}", stage, volume);
        return null;
    }

}

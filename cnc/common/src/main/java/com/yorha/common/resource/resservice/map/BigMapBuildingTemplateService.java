package com.yorha.common.resource.resservice.map;

import com.google.common.collect.Maps;
import com.yorha.common.constant.GameLogicConstants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonEnum.MapBuildingType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.*;

import java.util.*;

/**
 * <AUTHOR>
 * <p>
 * 地图建筑数据管理器
 */
public class BigMapBuildingTemplateService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(BigMapBuildingTemplateService.class);

    /**
     * 建筑类型 对应 类型表
     */
    private final Map<MapBuildingType, MapBuildingTypeTemplate> mapBuildingTypeTemplate = new HashMap<>();

    /**
     * 建筑类型 对应 建筑id list
     */
    private final Map<MapBuildingType, List<Integer>> mapBuildingTypeListMap = Maps.newHashMap();

    public BigMapBuildingTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        getResHolder().getListFromMap(MapBuildingTypeTemplate.class).forEach(v -> mapBuildingTypeTemplate.put(v.getType(), v));
        getResHolder().getListFromMap(MapBuildingTemplate.class).forEach(
                data -> {
                    mapBuildingTypeListMap.computeIfAbsent(data.getType(), k -> new ArrayList<>()).add(data.getId());
                }
        );
    }

    @Override
    public void checkValid() throws ResourceException {
        for (MapBuildingTemplate data : getResHolder().getMap(MapBuildingTemplate.class).values()) {
            if (!mapBuildingTypeTemplate.containsKey(data.getType())) {
                throw new ResourceException(StringUtils.format("map building type 没有配置在地图建筑类型表中, building id: {}", data.getId()));
            }
            if (data.getType() == MapBuildingType.MBT_PASS) {
                if (CollectionUtils.isEmpty(data.getBesiegeAngleList()) || data.getBesiegeAngleList().size() % 2 != 0) {
                    throw new ResourceException(StringUtils.format("地图建筑表 关隘围攻角度配置错误, building id: {}", data.getId()));
                }
            }
            if (data.getTroopId() != 0) {
                if (getResHolder().findValueFromMap(TroopTemplate.class, data.getTroopId()) == null) {
                    throw new ResourceException("地图建筑表 部队配置错误, building id: {}", data.getId());
                }
            }
        }
        // 检测玩家城池配置是否存在
        getResHolder().checkValueFromMap(MapBuildingTemplate.class, GameLogicConstants.CITY_CONFIG_TEMPLATE_ID,
                () -> StringUtils.format("玩家城池配置不存在 MapBuildingTemplate. id:{}", GameLogicConstants.CITY_CONFIG_TEMPLATE_ID));
    }

    public List<Integer> getMapBuildingIdListByType(MapBuildingType type) {
        return mapBuildingTypeListMap.getOrDefault(type, Collections.emptyList());
    }

    public TerritoryBuildingTemplate getClanBuildingTemplate(CommonEnum.MapBuildingType type, int storyId) {
        ClanBuildingResourceTemplate template = getClanBuildingTemplate(type, 1, storyId);
        return getResHolder().getValueFromMap(TerritoryBuildingTemplate.class, template.getBuildingId());
    }

    public ClanBuildingResourceTemplate getClanBuildingTemplate(CommonEnum.MapBuildingType wantBuildType, int rebuildNum, int storyId) {
        for (ClanBuildingResourceTemplate template : getResHolder().getListFromMap(ClanBuildingResourceTemplate.class)) {
            if (template.getType() != wantBuildType) {
                continue;
            }
            if (template.getStoryId() != storyId) {
                continue;
            }
            if (template.getNumLower() <= rebuildNum && rebuildNum <= template.getNumUpper()) {
                return template;
            }
        }
        LOGGER.error("getClanBuildingTemplate failed type={} num={} storyId={}", wantBuildType, rebuildNum, storyId);
        throw new GeminiException(ErrorCode.CLAN_BUILD_COST_CONFIG_NOT_FOUND);
    }
}

package com.yorha.common.aoiView.manager;

import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.enums.TimerReasonType;

import javax.annotation.Nullable;
import java.util.concurrent.TimeUnit;

public interface IActorForAoiTimer {
    
    /**
     * 添加重复定时器 只允许AoiNodeManageer可以调用
     */
    @Nullable
    ActorTimer addAoiTimer(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit, boolean isFix);
}

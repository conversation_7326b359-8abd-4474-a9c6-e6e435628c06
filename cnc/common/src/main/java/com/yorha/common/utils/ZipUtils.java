package com.yorha.common.utils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.DataFormatException;
import java.util.zip.Deflater;
import java.util.zip.Inflater;

/**
 * 字节压缩工具类
 *
 * <AUTHOR>
 */
public class ZipUtils {

    public static byte[] compress(byte[] input) throws IOException {
        if (input.length == 0) {
            return input;
        }
        byte[] ret;
        Deflater deflater = new Deflater();
        try (ByteArrayOutputStream stream = new ByteArrayOutputStream(input.length)) {
            deflater.setInput(input);
            deflater.finish();
            byte[] buf = new byte[1024];
            while (!deflater.finished()) {
                int got = deflater.deflate(buf);
                stream.write(buf, 0, got);
            }
            ret = stream.toByteArray();
        } finally {
            deflater.end();
        }
        return ret;
    }

    public static byte[] uncompress(byte[] input) throws IOException {
        if (input.length == 0) {
            return input;
        }
        byte[] ret;
        Inflater inflater = new Inflater();
        try (ByteArrayOutputStream stream = new ByteArrayOutputStream(input.length)) {
            inflater.reset();
            inflater.setInput(input, 0, input.length);
            byte[] buf = new byte[1024];
            int got;
            while (!inflater.finished()) {
                got = inflater.inflate(buf);
                stream.write(buf, 0, got);
            }
            ret = stream.toByteArray();
        } catch (DataFormatException e) {
            throw new IOException(e);
        } finally {
            inflater.end();
        }
        return ret;
    }
}

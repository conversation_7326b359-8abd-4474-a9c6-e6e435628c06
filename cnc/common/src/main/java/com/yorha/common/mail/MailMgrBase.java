package com.yorha.common.mail;

import com.google.protobuf.Message;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.BaseGameActor;
import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.msg.DeleteAsk;
import com.yorha.common.db.tcaplus.msg.InsertAsk;
import com.yorha.common.utils.MailUtil;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.StructMail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.checkerframework.checker.nullness.qual.NonNull;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.function.Consumer;

public abstract class MailMgrBase {
    public static final Logger LOGGER = LogManager.getLogger(MailMgrBase.class);

    private final BaseGameActor actor;

    protected MailMgrBase(BaseGameActor actor) {
        this.actor = actor;
    }

    public BaseGameActor getActor() {
        return actor;
    }

    /**
     * 获取邮件数据的游标
     */
    public abstract int getMailIndex();

    /**
     * 获取插入邮件的请求Builder
     */
    protected abstract Message.Builder getInsertReqBuilder(StructMail.NewMailCache mailCache);

    /**
     * 获取删除邮件的请求Builder
     */
    protected abstract Message.Builder getRemoveReqBuilder(long mailId);


    /**
     * 删除邮件的后处理
     *
     * @param mailId 邮件id
     */
    protected abstract void afterRemoveMail(long mailId);

    /**
     * 将新的邮件缓存加到db中
     */
    protected void addNewMailCacheToDb(StructMail.MailSendParams mailSendParams,
                                       CommonEnum.MailType mailType,
                                       AbstractMailData mailData,
                                       long mailTypeId, long mailId, int nextMailIndex,
                                       @NonNull Consumer<StructMail.NewMailCache> onDbAddSuccess) {
        StructMail.NewMailCache mailCache = MailUtil.buildNewMailCachePb(mailSendParams, mailType, mailTypeId, mailId, nextMailIndex).build();
        Message.Builder insertReqBuilder = getInsertReqBuilder(mailCache);

        getActor().askGameDb(new InsertAsk<>(insertReqBuilder))
                .onComplete((ans, e) -> {
                    if (e != null) {
                        LOGGER.error("addNewMailCacheToDb add new mail db failed. mailId={}", mailCache.getMailId(), e);
                        return;
                    }
                    if (!DbUtil.isOk(ans.getCode())) {
                        LOGGER.error("addNewMailCacheToDb add new mail db failed. mailId={}, code={}", mailCache.getMailId(), ans.getCode());
                        return;
                    }
                    LOGGER.info("addNewMailCacheToDb add new mail db mailId={}", mailCache.getMailId());
                    mailData.addMailCache(mailCache);
                    onDbAddSuccess.accept(mailCache);
                });
    }

    /**
     * 根据过期时间与最大大小清理邮件
     *
     * @param mailManager 邮件管理
     * @param expireDays  过期天数
     * @param maxSize     最大大小
     */
    protected int clearMails(final AbstractMailData mailManager, final int expireDays, final int maxSize) {
        int delNum = 0;
        if (mailManager == null) {
            return delNum;
        }
        // 过期清理
        List<StructMail.NewMailCache> expiredMails = mailManager.getMailCacheMap().values().stream().filter(
                (mail) -> TimeUtils.getAbsDaysBetween(mail.getCreateTimestamp(), SystemClock.now()) > expireDays
        ).toList();
        for (StructMail.NewMailCache expiredMail : expiredMails) {
            LOGGER.info("clearMails mailId={} index={} is expired, try remove", expiredMail.getMailId(), expiredMail.getMailIndex());
            removeMail(expiredMail.getMailId(), mailManager);
            ++delNum;
        }

        final int curMailNum = mailManager.getMailCacheMap().size();
        if (curMailNum <= maxSize) {
            return delNum;
        }
        // 超数量清理
        Comparator<StructMail.NewMailCache> cacheComparator = Comparator.comparing(StructMail.NewMailCache::getCreateTimestamp);
        List<StructMail.NewMailCache> overLoadMails = mailManager.getMailCacheMap().values().stream().sorted(cacheComparator).toList().subList(0, curMailNum - maxSize);
        for (StructMail.NewMailCache overLoadMail : overLoadMails) {
            LOGGER.info("clearMails mailId={} index={} is overLoad, try remove", overLoadMail.getMailId(), overLoadMail.getMailIndex());
            removeMail(overLoadMail.getMailId(), mailManager);
            ++delNum;
        }
        return delNum;
    }

    /**
     * 删除邮件（内存&db）
     *
     * @param mailId      邮件id
     * @param mailManager 邮件管理器
     */
    protected void removeMail(final long mailId, final AbstractMailData mailManager) {
        // 注意，内存后于db删除! 内存中无数据确保DB无数据
        if (!mailManager.getMailCacheMap().containsKey(mailId)) {
            LOGGER.error("removeMail remove cache! mailManager {} mail {} not exist", mailManager, mailId);
            return;
        }
        // 获取删除请求
        Message.Builder removeReqBuilder = getRemoveReqBuilder(mailId);
        // 异步删除mail
        getActor().askGameDb(new DeleteAsk<>(removeReqBuilder))
                .onComplete((ans, e) -> {
                    // 操作失败，直接返回
                    if (e != null) {
                        LOGGER.error("removeMail failed! mailManager {} mail {}, cause:{}", mailManager, mailId, e);
                        return;
                    }
                    final boolean isRecordNotExist = DbUtil.isRecordNotExist(ans.getCode());
                    // 操作db失败，直接退出
                    if (!isRecordNotExist && !DbUtil.isOk(ans.getCode())) {
                        LOGGER.error("removeMail failed! mailManager {} mail {} errorCode {}", mailManager, mailId, ans.getCode());
                        return;
                    }
                    afterRemoveMail(mailId);
                });
    }

    /**
     * @param playerIds 玩家id列表。
     * @return ref列表
     */
    protected List<IActorRef> toActorRef(Collection<Long> playerIds) {
        List<IActorRef> refList = new ArrayList<>(playerIds.size());
        for (Long playerId : playerIds) {
            refList.add(RefFactory.ofPlayer(getActor().getZoneId(), playerId));
        }
        return refList;
    }
}

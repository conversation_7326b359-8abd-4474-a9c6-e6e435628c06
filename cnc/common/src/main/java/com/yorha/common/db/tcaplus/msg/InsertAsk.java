package com.yorha.common.db.tcaplus.msg;

import com.google.protobuf.Message;
import com.yorha.common.db.tcaplus.option.InsertOption;
import com.yorha.common.db.tcaplus.result.InsertResult;

public class InsertAsk<T extends Message.Builder> implements GameDbReq<InsertResult<T>> {
    private final T req;
    private final InsertOption option;

    public InsertAsk(T req) {
        this(req, InsertOption.DEFAULT_VALUE);
    }

    public InsertAsk(T req, InsertOption option) {
        this.req = req;
        this.option = option;
    }

    public T getReq() {
        return req;
    }

    public InsertOption getOption() {
        return option;
    }
}

package com.yorha.common.resource.resservice.clan;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * 角色信息
 *
 * <AUTHOR>
 */
public class RoleHolder {
    /**
     * 角色默认操作权限
     */
    public final Set<Integer> defaultOperations;
    /**
     * 角色最大拥有的操作权限
     */
    public final Set<Integer> maxOperations;
    /**
     * 角色最小拥有的操作权限
     */
    public final Set<Integer> minOperations;

    static class Builder {
        private final Set<Integer> defaultOperations;
        private final Set<Integer> maxOperations;
        private final Set<Integer> minOperations;

        Builder() {
            this.defaultOperations = new HashSet<>();
            this.maxOperations = new HashSet<>();
            this.minOperations = new HashSet<>();
        }

        public void addDefaultOperations(Integer op) {
            this.defaultOperations.add(op);
        }

        public void addMaxOperations(Integer op) {
            this.maxOperations.add(op);
        }

        public void addMinOperations(Integer op) {
            this.minOperations.add(op);
        }

        public RoleHolder build() {
            return new RoleHolder(this.defaultOperations, this.maxOperations, this.minOperations);
        }
    }

    public RoleHolder(final Set<Integer> defaultOperations, final Set<Integer> maxOperations, final Set<Integer> minOperations) {
        this.defaultOperations = Collections.unmodifiableSet(defaultOperations);
        this.maxOperations = Collections.unmodifiableSet(maxOperations);
        this.minOperations = Collections.unmodifiableSet(minOperations);
    }


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}

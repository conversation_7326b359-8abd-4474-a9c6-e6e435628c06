package com.yorha.common.resource.resservice.city;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.proto.CommonEnum;
import res.template.BuffEffectTemplate;
import res.template.DressResTemplate;
import res.template.DressTemplate;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class CitySkinService extends AbstractResService {

    public CitySkinService(ResHolder resHolder) {
        super(resHolder);
    }

    private Map<CommonEnum.DressType, Map<Integer, DressResTemplate>> dressRes = Maps.newHashMap();

    @Override
    public void checkValid() throws ResourceException {
        checkDress();
        checkDressRes();
    }

    private void checkDress() throws ResourceException {
        for (DressTemplate dressTemplate : getResHolder().getListFromMap(DressTemplate.class)) {
            //check dressId
            if (getResHolder().findValueFromMap(DressResTemplate.class, dressTemplate.getDressId()) == null) {
                throw new ResourceException("基地外观配置dress表 id:{} 无效外观ID:{}", dressTemplate.getId(), dressTemplate.getDressId());
            }
            //check continueTime
            if (dressTemplate.getContinueTime() < 0) {
                throw new ResourceException("基地外观配置dress表 id:{} 持续时间小于0:{}", dressTemplate.getId(), dressTemplate.getContinueTime());
            }
        }
    }

    private void checkDressRes() throws ResourceException {
        for (DressResTemplate dressResTemplate : getResHolder().getListFromMap(DressResTemplate.class)) {
            //check dressType
            if (CommonEnum.DressType.forNumber(dressResTemplate.getDressType()) == null || dressResTemplate.getDressType() <= 0) {
                throw new ResourceException("基地外观配置dress_res表 dressid:{} 无效外观类型:{}", dressResTemplate.getId(), dressResTemplate.getDressType());
            }
            Set<Integer> effectIds = Sets.newHashSet();
            //check useAttribute
            for (IntPairType useAttribute : dressResTemplate.getUseAttributePairList()) {
                if (effectIds.contains(useAttribute.getKey())) {
                    throw new ResourceException("基地外观配置dress_res表 dressid:{} 使用属性配置错误，含重复增益效果id:{}", dressResTemplate.getId(), dressResTemplate.getUseAttributePairList());
                }
                effectIds.add(useAttribute.getKey());

                if (useAttribute.getValue() < 0) {
                    throw new ResourceException("基地外观配置dress_res表 dressid:{} 使用属性配置错误，含非正数:{}", dressResTemplate.getId(), dressResTemplate.getUseAttributePairList());
                }

                if (getResHolder().findValueFromMap(BuffEffectTemplate.class, useAttribute.getKey()) == null) {
                    throw new ResourceException("基地外观配置dress_res表 dressid:{} 含未知增益效果类型:{}", dressResTemplate.getId(), dressResTemplate.getUseAttributePairList());
                }

            }
        }
    }

    @Override
    public void load() throws ResourceException {
        for (DressResTemplate dressResTemplate : getResHolder().getListFromMap(DressResTemplate.class)) {
            dressRes.computeIfAbsent(CommonEnum.DressType.forNumber(dressResTemplate.getDressType()), k -> Maps.newHashMap())
                    .put(dressResTemplate.getId(), dressResTemplate);
        }
    }

    /**
     * 根据外观子ID获取外观ID
     *
     * @param dressSubTemplateId 外观子ID
     * @return 外观ID
     */
    public int getDressId(int dressSubTemplateId) {
        DressTemplate dressTemplate = getResHolder().getValueFromMap(DressTemplate.class, dressSubTemplateId);
        return dressTemplate.getDressId();
    }

    /**
     * 根据外观子ID获取持续时间
     *
     * @param dressSubTemplateId 外观子ID
     * @return 持续时间
     */
    public int getContinueTime(int dressSubTemplateId) {
        DressTemplate dressTemplate = getResHolder().getValueFromMap(DressTemplate.class, dressSubTemplateId);
        return dressTemplate.getContinueTime();
    }

    /**
     * 获得皮肤使用加成
     *
     * @param dressTemplateId 外观ID
     * @return Map<加成ID, 加成数值>
     */
    public Map<Integer, Long> getUseAttribute(int dressTemplateId) {
        DressResTemplate dressResTemplate = getResHolder().getValueFromMap(DressResTemplate.class, dressTemplateId);
        return dressResTemplate.getUseAttributePairList().stream().collect(Collectors.toMap(IntPairType::getKey, useAttribute -> Long.valueOf(useAttribute.getValue())));
    }

    /**
     * 获得皮肤类型
     *
     * @param dressSubTemplateId 外观子ID
     * @return 皮肤类型
     */
    public int getDressType(int dressSubTemplateId) {
        DressTemplate dressTemplate = getResHolder().getValueFromMap(DressTemplate.class, dressSubTemplateId);
        DressResTemplate dressResTemplate = getResHolder().getValueFromMap(DressResTemplate.class, dressTemplate.getDressId());
        return dressResTemplate.getDressType();
    }

    /**
     * 获得皮肤外观表
     *
     * @param dressType          皮肤类型
     * @param dressSubTemplateId 外观子ID
     * @return 皮肤外观表
     */
    public DressResTemplate getDressResTemplate(CommonEnum.DressType dressType, int dressSubTemplateId) {
        Map<Integer, DressResTemplate> dressTypeMap = this.dressRes.get(dressType);
        if (dressTypeMap == null) {
            throw new GeminiException(ErrorCode.DRESS_TYPE_INVALID);
        }
        DressResTemplate dressResTemplate = dressTypeMap.get(this.getDressId(dressSubTemplateId));
        if (dressResTemplate == null) {
            throw new GeminiException(ErrorCode.DRESS_UNKNOWN);
        }
        return dressResTemplate;
    }
}

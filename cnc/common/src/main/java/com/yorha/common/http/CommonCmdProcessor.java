package com.yorha.common.http;


import com.yorha.common.http.cmdItem.*;
import com.yorha.common.resource.ResLoader;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.script.ScriptMgr;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Duration;
import java.time.Instant;

/**
 * 这里实现通用的HTTP请求处理
 *
 * <AUTHOR> Jiang
 */
public class CommonCmdProcessor {
    private static final Logger LOGGER = LogManager.getLogger(CommonCmdProcessor.class);

    /**
     * 存活检测
     */
    public String ping(PingCmd msg) {
        String result = "fail";
        if (ServerContext.getServer().isRunning()) {
            result = "ok";
        }
        LOGGER.info("k8s alive check result: {}", result);
        return result;
    }

    /**
     * 执行指定目录的脚本
     */
    public String execScript(ExecScriptCmd msg) {
        Instant startTime = Instant.now();
        String localPath = ServerContext.getScriptLocalPath();
        if (!localPath.endsWith("/")) {
            localPath = localPath + "/";
        }
        String result = ScriptMgr.getInstance().executeByFile(localPath + msg.getScript());
        Instant endTime = Instant.now();
        long interval = Duration.between(startTime, endTime).toMillis();
        return result + "\ngroovy exec cost: " + interval;
    }

    /**
     * 执行脚本内容
     */
    public String execScriptContent(ExecSciptContentCmd msg) {
        Instant startTime = Instant.now();
        String result = ScriptMgr.getInstance().executeByGroovy(msg.getScript());
        Instant endTime = Instant.now();
        long interval = Duration.between(startTime, endTime).toMillis();
        return result + "\ngroovy exec cost: " + interval;
    }

    /**
     * 重载配置文件
     */
    public String reloadCfg(ReloadCfgCmd msg) {
        try {
            ServerContext.loadServerCfg();
            return "reload cfg success";
        } catch (Exception e) {
            LOGGER.error("CommonCmdProcessor reloadCfg ", e);
            return "FAILED";
        }
    }

    /**
     * 策划资源热更新
     */
    public String reloadRes(ReloadResCmd msg) {
        final String localPath = ServerContext.getGameDataLocalPath();
        try {
            ResLoader.load(localPath);
            return "reload xml success!!!";
        } catch (Exception e) {
            LOGGER.error("CommonCmdProcessor reloadRes ", e);
            return "FAILED";
        }
    }

}

package com.yorha.common.resource.resservice.skynet;

import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.utils.RandomUtils;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.*;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class SkynetTemplateService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(SkynetTemplateService.class);

    private final Map<String, List<SkynetTaskTemplate>> skynetTaskPool = new HashMap<>();

    /**
     * 普通任务刷新间隔毫秒数
     */
    private long refreshIntervalMsWithNormal;
    /**
     * 守护者任务刷新间隔毫秒数
     */
    private long refreshIntervalMsWithGuard;

    public SkynetTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        for (SkynetTaskTemplate skynetTaskTemplate : getResHolder().getListFromMap(SkynetTaskTemplate.class)) {
            String taskKey = formatTaskKey(skynetTaskTemplate.getTaskType(), skynetTaskTemplate.getLevel(), skynetTaskTemplate.getQuality());
            List<SkynetTaskTemplate> taskList = skynetTaskPool.computeIfAbsent(taskKey, (key) -> new ArrayList<>());
            taskList.add(skynetTaskTemplate);
        }

        int taskRefreshCycleWithNormal = getResHolder().getConstTemplate(ConstSkynetTemplate.class).getTaskRefreshCycle();
        int defenderRefreshCycleWithGuard = getResHolder().getConstTemplate(ConstSkynetTemplate.class).getDefenderRefreshCycle();

        long oneDay = TimeUnit.DAYS.toMillis(1);
        refreshIntervalMsWithNormal = oneDay / taskRefreshCycleWithNormal;
        refreshIntervalMsWithGuard = oneDay / defenderRefreshCycleWithGuard;

    }

    /**
     * 获取时间戳距离下次刷新的剩余时间
     *
     * @param isNormal        普通任务
     * @param lastRefreshTsMs 上次刷新时间戳
     */
    public long getNextRefreshLaveMs(boolean isNormal, long lastRefreshTsMs) {
        // 获取下一个零点的间隔时间
        long nextDayDurationMs = TimeUtils.getNextDayDurMs(lastRefreshTsMs);
        if (nextDayDurationMs < 0) {
            WechatLog.error("SkynetTemplateService getNextRefreshLaveMs, nextDayDurMs < 0, lastRefreshTsMs={}", lastRefreshTsMs);
            return -1;
        }
        long remainingMs;
        if (isNormal) {
            remainingMs = calculateRemainingMs(nextDayDurationMs, refreshIntervalMsWithNormal);
        } else {
            remainingMs = calculateRemainingMs(nextDayDurationMs, refreshIntervalMsWithGuard);
        }
        return remainingMs;
    }

    /**
     * 计算剩余时间
     *
     * @param durationMs      间隔时间
     * @param refreshInterval 刷新间隔
     * @return 剩余时间
     */
    private long calculateRemainingMs(long durationMs, long refreshInterval) {
        long remainingMs = durationMs % refreshInterval;
        return remainingMs == 0 ? refreshInterval : remainingMs;
    }

    /**
     * 计算需要刷新多少次
     *
     * @param isNormal        普通任务
     * @param lastRefreshTsMs 上次刷新时间戳
     */
    public long calcNeedRefreshCount(boolean isNormal, long lastRefreshTsMs, long now) {
        if (lastRefreshTsMs <= 0) {
            LOGGER.error("SkynetTemplateService calcNeedRefreshCount, lastRefreshTsMs should be init, isNormal={}", isNormal);
            return 0;
        }
        long diffMs = now - lastRefreshTsMs;
        if (diffMs <= 0) {
            return 0;
        }
        long count;
        if (isNormal) {
            long value1 = lastRefreshTsMs / refreshIntervalMsWithNormal;
            long value2 = now / refreshIntervalMsWithNormal;
            count = value2 - value1;
        } else {
            long value1 = lastRefreshTsMs / refreshIntervalMsWithGuard;
            long value2 = now / refreshIntervalMsWithGuard;
            count = value2 - value1;
        }
        if (count < 0) {
            LOGGER.error("calcNeedRefreshCount failed, isNormal={} lastRefreshTsMs={} now={} refreshIntervalMsWithNormal={} refreshIntervalMsWithGuard={}",
                    isNormal, lastRefreshTsMs, now, refreshIntervalMsWithNormal, refreshIntervalMsWithGuard);
            return 0;
        }
        return count;
    }

    @Nullable
    public SkynetTaskTemplate rollSkynetTaskTemplate(SkynetLevelTemplate levelTemplate, boolean isGuardTaskType) {
        int level = levelTemplate.getId();
        CommonEnum.SkynetTaskType taskType = isGuardTaskType ? CommonEnum.SkynetTaskType.SNTT_GUARD : randomTaskTypeWithLevelWeight(levelTemplate);
        int quality = randomQualityTypeWithLevelWeight(taskType, levelTemplate);
        List<SkynetTaskTemplate> skynetTaskTemplates = skynetTaskPool.get(formatTaskKey(taskType, level, quality));
        if (skynetTaskTemplates == null) {
            WechatLog.error("PlayerSkynetComponent checkOrUnlockGuardTask, no guard config, taskType={} level={} quality={}", taskType, level, quality);
            return null;
        }
        int index = RandomUtils.nextInt(skynetTaskTemplates.size());
        SkynetTaskTemplate skynetTaskTemplate = skynetTaskTemplates.get(index);
        LOGGER.info("SkynetTemplateService rollSkynetTaskTemplate roll success, taskType={} level={} quality={} taskId={}", taskType, level, quality, skynetTaskTemplate.getId());
        return skynetTaskTemplate;
    }

    private static CommonEnum.SkynetTaskType randomTaskTypeWithLevelWeight(SkynetLevelTemplate levelTemplate) {
        IntPairType randomResult = RandomUtils.randomByWeight(levelTemplate.getTaskTypeWeightPairList(), IntPairType::getValue);
        return CommonEnum.SkynetTaskType.forNumber(randomResult.getKey());
    }

    private int randomQualityTypeWithLevelWeight(CommonEnum.SkynetTaskType taskType, SkynetLevelTemplate levelTemplate) {
        if (taskType == CommonEnum.SkynetTaskType.SNTT_GUARD) {
            return getResHolder().getConstTemplate(ConstSkynetTemplate.class).getDefenderQuality();
        }
        IntPairType randomResult = RandomUtils.randomByWeight(levelTemplate.getQualityWeightPairList(), IntPairType::getValue);
        return randomResult.getKey();
    }


    /**
     * 任务key值的格式化
     */
    private String formatTaskKey(CommonEnum.SkynetTaskType taskType, int level, int quality) {
        return taskType.getNumber() + "_" + level + "_" + quality;
    }

    public SkynetBossTemplate getInitBossTemplate() {
        // 策划要写死
        return getResHolder().getValueFromMap(SkynetBossTemplate.class, 1);
    }

    @Nullable
    public SkynetBossTemplate getNextBossTemplate(int skynetTaskId) {
        SkynetBossTemplate bossTemplate = getResHolder().getValueFromMap(SkynetBossTemplate.class, skynetTaskId);
        return getResHolder().findValueFromMap(SkynetBossTemplate.class, bossTemplate.getNextID());
    }


    @Override
    public void checkValid() throws ResourceException {
        for (Map.Entry<String, List<SkynetTaskTemplate>> entry : skynetTaskPool.entrySet()) {
            if (entry.getValue().size() <= 0) {
                throw new ResourceException("天网任务表无对应类型_等级_品质任务, key={}", entry.getKey());
            }
        }

        for (SkynetTaskTemplate taskTemplate : getResHolder().getListFromMap(SkynetTaskTemplate.class)) {
            if (taskTemplate.getTaskType() == CommonEnum.SkynetTaskType.SNTT_MONSTER) {
                if (taskTemplate.getNeedPower() > 0) {
                    throw new ResourceException("天网任务表野怪类型不允许配置体力消耗, key={}", taskTemplate.getId());
                }
                if (taskTemplate.getParam() <= 0) {
                    throw new ResourceException("天网任务表野怪参数无配置, key={}", taskTemplate.getId());
                }
                if (getResHolder().findValueFromMap(MonsterTemplate.class, taskTemplate.getParam()) == null) {
                    throw new ResourceException("天网任务表野怪对应野怪表无配置, key={} param={}", taskTemplate.getId(), taskTemplate.getParam());
                }
            }
            if (taskTemplate.getTaskType() == CommonEnum.SkynetTaskType.SNTT_GUARD) {
                if (taskTemplate.getNeedPower() > 0) {
                    throw new ResourceException("天网任务表守护者类型不允许配置体力消耗, key={}", taskTemplate.getId());
                }
                if (taskTemplate.getTime() > 0) {
                    throw new ResourceException("天网任务表未配置任务时效(不包括守护者任务), key={}", taskTemplate.getId());
                }
//                if (getResHolder().findValueFromMap(MonsterTemplate.class, taskTemplate.getParam()) == null) {
//                    throw new ResourceException("天网任务表守护者对应野怪表无配置, key={} param={}", taskTemplate.getId(), taskTemplate.getParam());
//                }
            }
            if (taskTemplate.getRewardID() > 0) {
                if (getResHolder().findValueFromMap(SelecteRewardTemplate.class, taskTemplate.getRewardID()) == null) {
                    throw new ResourceException("天网任务表奖励对应奖励表无配置, key={} rewardId={}", taskTemplate.getId(), taskTemplate.getRewardID());
                }
            }
            if (!taskTemplate.getExtraRewardPairList().isEmpty()) {
                for (IntPairType intPairType : taskTemplate.getExtraRewardPairList()) {
                    if (getResHolder().findValueFromMap(ItemTemplate.class, intPairType.getKey()) == null) {
                        throw new ResourceException("天网任务表额外奖励对应道具表无配置, key={} itemId={}", taskTemplate.getId(), intPairType.getKey());
                    }
                }
            }
            if (taskTemplate.getTaskType() == CommonEnum.SkynetTaskType.SNTT_TALK) {
                if (taskTemplate.getMailID() <= 0) {
                    throw new ResourceException("天网任务表对话任务无邮件配置, key={} mailId={}", taskTemplate.getId(), taskTemplate.getMailID());
                }
                if (getResHolder().findValueFromMap(MailTemplate.class, taskTemplate.getMailID()) == null) {
                    throw new ResourceException("天网任务表对话任务邮件对应邮件表配置不存在, key={} mailId={}", taskTemplate.getId(), taskTemplate.getMailID());
                }
            }
        }
    }
}

package com.yorha.common.ratelimiter;

import com.yorha.common.utils.time.SystemClock;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 粗略的 不准不准
 *
 * <AUTHOR>
 */
public class GeminiThreadRateLimiter {
    private final AtomicInteger counter = new AtomicInteger(0);
    private final AtomicLong countTs = new AtomicLong(0);
    private final int max;

    public GeminiThreadRateLimiter(int maxQps) {
        this.max = maxQps;
    }

    public boolean tryAcquire() {
        long now = SystemClock.nowOfSeconds();
        long old = countTs.get();
        if (old < now && countTs.compareAndSet(old, now)) {
            counter.set(0);
        }
        return counter.getAndIncrement() < max;
    }
}

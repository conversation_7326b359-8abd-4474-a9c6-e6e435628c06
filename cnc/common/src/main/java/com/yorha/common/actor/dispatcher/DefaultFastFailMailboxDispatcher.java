package com.yorha.common.actor.dispatcher;

import com.yorha.common.actor.mailbox.IRunnableActorMailbox;
import com.yorha.common.exception.GeminiException;

/**
 * 默认的邮箱调度器，任意接口调用了都会异常。
 *
 * <AUTHOR>
 */
public class DefaultFastFailMailboxDispatcher implements IMailboxDispatcher {
    public DefaultFastFailMailboxDispatcher() {
    }

    @Override
    public String getName() {
        throw new GeminiException("getName {} FastFail!", this);
    }

    @Override
    public int getThroughput() {
        throw new GeminiException("getThroughput {} FastFail!", this);
    }

    @Override
    public void schedule(IRunnableActorMailbox mb) {
        throw new GeminiException("schedule {} at {} FastFail!", mb, this);
    }

    @Override
    public void setParallelism(int parallelism) {
        throw new GeminiException("setParallelism {} FastFail!", this);
    }

    @Override
    public int getParallelism() {
        throw new GeminiException("getParallelism {} FastFail!", this);
    }

    @Override
    public int getFiberCnt(String actorRole) {
        throw new GeminiException("getFiberCnt {} FastFail!", this);
    }

    @Override
    public int getKeepAliveSec() {
        throw new GeminiException("getKeepAliveSec {} FastFail!", this);
    }

    @Override
    public void shutdown() {
        throw new GeminiException("shutdown {} FastFail!", this);
    }

    @Override
    public String toString() {
        return "FastFailMailboxDispatcher{}";
    }
}

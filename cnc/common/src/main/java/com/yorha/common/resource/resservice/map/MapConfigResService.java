package com.yorha.common.resource.resservice.map;

import com.google.common.collect.Lists;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import res.template.MapConfigRhTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

public class MapConfigResService extends AbstractResService {

    /**
     * 地图配置 地图类型->地图等级->template
     */
    private final HashMap<Integer, List<MapConfigRhTemplate>> mapTypeMap = new HashMap<>();

    public MapConfigResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        // 刷新地图
        refreshMapConfigTemplate();
    }

    @Override
    public void checkValid() throws ResourceException {

    }

    private void refreshMapConfigTemplate() throws ResourceException {
        for (MapConfigRhTemplate template : getResHolder().getListFromMap(MapConfigRhTemplate.class)) {
            List<MapConfigRhTemplate> mapList = mapTypeMap.computeIfAbsent(template.getMapType(), (key) -> Lists.newArrayList());
            mapList.add(template);
        }
    }

    public List<MapConfigRhTemplate> getMapList(int mapType, List<Integer> mapLevelList) {
        List<MapConfigRhTemplate> mapList = mapTypeMap.get(mapType);
        if (mapList == null) {
            return Lists.newArrayList();
        }
        return mapList.stream().filter(t -> mapLevelList.contains(t.getMapLevel())).collect(Collectors.toList());
    }
}
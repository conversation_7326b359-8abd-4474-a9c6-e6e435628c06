package com.yorha.common.resource.resservice.mail;

import com.yorha.common.enums.reason.RallyDismissReason;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import res.template.RallyMailTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class RallyMailService extends AbstractResService {
    private final Map<RallyDismissReason, Integer> dismissMail = new HashMap<>();

    public RallyMailService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        for (RallyMailTemplate template : getResHolder().getListFromMap(RallyMailTemplate.class)) {
            RallyDismissReason reason = RallyDismissReason.valueOf(template.getReason());
            dismissMail.put(reason, template.getMailId());
        }
    }

    public int getDismissMail(RallyDismissReason reason) {
        return dismissMail.getOrDefault(reason, 0);
    }

    @Override
    public void checkValid() throws ResourceException {

    }
}

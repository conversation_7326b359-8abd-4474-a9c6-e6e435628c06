package com.yorha.common.resource.resservice.constant;

import com.yorha.common.constant.Constants;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.exception.ResourceException;
import com.yorha.gemini.utils.StringUtils;
import res.template.ConstClanTemplate;
import res.template.ItemTemplate;

/**
 * <AUTHOR>
 */
public class ConstClanKVResService extends AbstractResService {

    public ConstClanKVResService(ResHolder resHolder) {
        super(resHolder);
    }

    public ConstClanTemplate getTemplate() {
        return getResHolder().getConstTemplate(ConstClanTemplate.class);
    }

    @Override
    public void load() throws ResourceException {
    }

    @Override
    public void checkValid() throws ResourceException {
        int decTimePercent = getTemplate().getHelpBaseDecTimePercent();
        if (decTimePercent > Constants.N_10_000 || decTimePercent < 0) {
            throw new ResourceException(StringUtils.format("ConstClanTemplate {} 军团帮助概率万分比减少时间不能超过10000或小于0", decTimePercent));
        }
        for (IntPairType itemPairType : getTemplate().getFirstJoinClanReward()) {
            int itemId = itemPairType.getKey();
            int itemNum = itemPairType.getValue();
            if (getResHolder().findValueFromMap(ItemTemplate.class, itemId) == null) {
                throw new ResourceException(StringUtils.format("ConstClanTemplate {} 军团首次加入奖励物品不存在", itemId));
            }
            if (itemNum <= 0) {
                throw new ResourceException(StringUtils.format("ConstClanTemplate {} {} 军团首次加入奖励物品数量不能小于0", itemId, itemNum));
            }
        }
    }
}

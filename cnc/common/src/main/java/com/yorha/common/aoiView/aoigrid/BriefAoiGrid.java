package com.yorha.common.aoiView.aoigrid;

import com.yorha.common.constant.BigSceneConstants;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;
import it.unimi.dsi.fastutil.longs.LongOpenHashSet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class BriefAoiGrid extends AoiGrid {
    private static final Logger LOGGER = LogManager.getLogger(BriefAoiGrid.class);

    /**
     * 简要数据层Obj  层级-> 物体id列表
     */
    private final Map<Integer, LongOpenHashSet> briefSceneObj = new Int2ObjectOpenHashMap<>();

    public BriefAoiGrid(int x, int y) {
        super(x, y);
    }

    @Override
    public void addSceneObj(long objId, int layer) {
        briefSceneObj.computeIfAbsent(layer, k -> new LongOpenHashSet()).add(objId);
    }

    @Override
    public void removeSceneObj(long objId, int layer) {
        if (briefSceneObj.containsKey(layer)) {
            briefSceneObj.get(layer).remove(objId);
        } else {
            LOGGER.error("removeSceneObj layer not add");
        }
    }

    @Override
    public void changeSceneObjLayer(long objId, int oldLayer, int newLayer) {
        removeSceneObj(objId, oldLayer);
        if (newLayer == 0) {
            return;
        }
        addSceneObj(objId, newLayer);
    }

    @Override
    public Set<Long> getSceneObjIds() {
        Set<Long> ret = new LongOpenHashSet();
        for (LongOpenHashSet value : briefSceneObj.values()) {
            ret.addAll(value);
        }
        return ret;
    }

    @Override
    public Set<Long> getSceneObjIds(int layer) {
        Set<Long> ret = new LongOpenHashSet();
        int maxLayer = BigSceneConstants.WORLD_MAX_LAYER;
        for (int i = maxLayer; i >= layer; i--) {
            if (!briefSceneObj.containsKey(i)) {
                continue;
            }
            ret.addAll(briefSceneObj.get(i));
        }
        return ret;
    }
}

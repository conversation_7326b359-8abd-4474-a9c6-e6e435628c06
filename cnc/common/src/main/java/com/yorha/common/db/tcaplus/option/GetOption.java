package com.yorha.common.db.tcaplus.option;

import java.util.Arrays;
import java.util.List;

public class GetOption {
    /**
     * 需要的字段。
     */
    private List<String> fieldNames;
    /**
     * 获得所有的字段
     * 默认：true
     */
    private boolean getAllFields = true;
    /**
     * 如果设置为负数，表示当前数据不启动版本控制
     * 只有fetchOnlyIfModified=true时有效
     */
    private int version = -1;
    /**
     * 数据变更才取回，需要指定version
     * "数据变更才取回"标志位。在发起读操作之前，用户代码通过 TcaplusServiceRecord::SetVersion()
     * 带上本地缓存数据的版本号，并将此标志置位，那么存储端检测到当前数据与API本地缓存的数据版本
     * 一致时，表明该记录未发生过修改，API缓存的数据是最新的，因此在响应中将不会携带实际的数据，
     * 只是返回 TcapErrCode::COMMON_INFO_DATA_NOT_MODIFIED 的错误码
     * 在请求中设置了此标志位之后，收到响应后应首先通过 TcaplusServiceResponse::GetFlags() 来获知
     * 发送请求时是否设置了TCAPLUS_FLAG_FETCH_ONLY_IF_MODIFIED标志.
     * 只有如下请求支持设置此标志：
     * TCAPLUS_API_GET_REQ,
     * TCAPLUS_API_LIST_GET_REQ,
     * TCAPLUS_API_LIST_GETALL_REQ
     * 默认： false
     */
    private boolean fetchOnlyIfModified = false;

    private GetOption() {
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public List<String> getFieldNames() {
        return fieldNames;
    }

    public boolean isGetAllFields() {
        return getAllFields;
    }

    public int getVersion() {
        return version;
    }

    public boolean isFetchOnlyIfModified() {
        return fetchOnlyIfModified;
    }

    public static final class Builder {
        private List<String> fieldNames;
        private boolean getAllFields = true;
        private int version = -1;
        private boolean fetchOnlyIfModified = false;

        private Builder() {
        }

        public Builder withFieldNames(List<String> fieldNames) {
            this.fieldNames = fieldNames;
            return this;
        }

        public Builder withFieldNames(String... fieldNames) {
            this.fieldNames = Arrays.asList(fieldNames);
            return this;
        }

        public Builder withGetAllFields(boolean getAllFields) {
            this.getAllFields = getAllFields;
            return this;
        }

        public Builder withVersion(int version) {
            this.version = version;
            return this;
        }

        public Builder withFetchOnlyIfModified(boolean fetchOnlyIfModified) {
            this.fetchOnlyIfModified = fetchOnlyIfModified;
            return this;
        }

        public GetOption build() {
            GetOption getOption = new GetOption();
            getOption.fetchOnlyIfModified = this.fetchOnlyIfModified;
            getOption.version = this.version;
            getOption.getAllFields = this.getAllFields;
            getOption.fieldNames = this.fieldNames;
            return getOption;
        }
    }
}

package com.yorha.common.resource.resservice.whiteList;

import com.google.common.collect.Sets;
import com.yorha.common.actor.node.TempWhiteListConfig;
import com.yorha.common.enums.WhitePermissions;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.server.config.WhiteListConfig;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import res.template.WhiteListTemplate;
import res.template.WhiteModelTemplate;
import res.template.WhiteVersionTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * <p>
 * 白名单数据管理器
 */
public class WhiteListResService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(WhiteListResService.class);
    private final Map<Integer, Map<String, WhiteListTemplate>> whiteAccount = new HashMap<>();
    private final Map<Integer, Map<String, WhiteListTemplate>> whiteDevice = new HashMap<>();
    private final Map<Integer, Map<String, WhiteListTemplate>> whiteIp = new HashMap<>();


    private final Set<String> whiteModel = Sets.newTreeSet();
    private final Set<String> whiteVersion = Sets.newTreeSet();

    /**
     * 表格配置zoneId为-1，代表适用于该world下的所有zone
     */
    private static final int ANY_ZONE_ID = -1;

    public WhiteListResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        getResHolder().getListFromMap(WhiteModelTemplate.class).forEach(t -> whiteModel.add(t.getModel()));
        getResHolder().getListFromMap(WhiteVersionTemplate.class).forEach(t -> whiteVersion.add(t.getVersion()));

        StringBuilder sb = new StringBuilder();
        sb.append("WhiteListTemplateInfo ").append(" : ").append("\n");
        for (WhiteListTemplate template : getResHolder().getListFromMap(WhiteListTemplate.class)) {
            if (template.getWorldId() != ServerContext.getWorldId()) {
                continue;
            }
            if (!StringUtils.isEmpty(template.getDeviceId())) {
                int templateZoneId = template.getZoneId();
                Map<String, WhiteListTemplate> map = whiteDevice.computeIfAbsent(templateZoneId, k -> new HashMap<>());
                map.put(template.getDeviceId(), template);
            }
            if (!StringUtils.isEmpty(template.getIp())) {
                int templateZoneId = template.getZoneId();
                Map<String, WhiteListTemplate> map = whiteIp.computeIfAbsent(templateZoneId, k -> new HashMap<>());
                map.put(template.getIp(), template);
            }
            if (!StringUtils.isEmpty(template.getOpenId())) {
                int templateZoneId = template.getZoneId();
                Map<String, WhiteListTemplate> map = whiteAccount.computeIfAbsent(templateZoneId, k -> new HashMap<>());
                map.put(template.getOpenId(), template);
            }
            sb.append(JsonUtils.toJsonString(template)).append("\n");
        }
        LOGGER.info(sb);
    }

    private WhiteListConfig getTempWhiteListConfig() {
        return ServerContext.getWhiteListConfig();
    }

    public boolean isWhiteModel(String model) {
        return whiteModel.contains(model);
    }

    public boolean isWhiteVersion(String version) {
        return whiteVersion.contains(version);
    }

    @Override
    public void checkValid() throws ResourceException {
        for (WhiteListTemplate template : getResHolder().getListFromMap(WhiteListTemplate.class)) {
            if (StringUtils.isEmpty(template.getDeviceId()) && StringUtils.isEmpty(template.getOpenId()) && StringUtils.isEmpty(template.getIp())) {
                throw new ResourceException("白名单表 id:{}, 设备id，账号id，ip均为空", template.getId());
            }
        }
    }

    /**
     * 是否有指定白名单权限
     */
    public boolean hasWhitePermission(int zoneId, String openId, String deviceId, String clientIp, WhitePermissions whitePermissions) {
        WhiteListTemplate template = getWhiteListTemplate(zoneId, openId, deviceId, clientIp);
        if (template != null) {
            return switch (whitePermissions) {
                case WP_DEBUG_LOG -> template.getDebugLog();
                case WP_REGISTER_LIMIT -> template.getRegisterLimit();
                case WP_IP_LIMIT -> template.getIpLimit();
                case WP_POWER -> template.getPowerUser();
                case WP_AUTH -> template.getNoAuth();
            };
        }

        TempWhiteListConfig.WhiteConfig config = getTempWhiteListTemplate(zoneId, openId, deviceId, clientIp);
        if (config != null) {
            return switch (whitePermissions) {
                case WP_DEBUG_LOG -> config.getDebugLog();
                case WP_REGISTER_LIMIT -> config.getRegisterLimit();
                case WP_IP_LIMIT -> config.getIpLimit();
                case WP_POWER -> config.getPowerUser();
                default -> throw new GeminiException("isWhite unSupport WhitePermissions :{}", whitePermissions);
            };
        }
        return false;
    }

    /**
     * 获取etcd临时白名单配置
     */
    private TempWhiteListConfig.WhiteConfig getTempWhiteListTemplate(int zoneId, String openId, String deviceId, String clientIp) {
        WhiteListConfig whiteListConfig = getTempWhiteListConfig();
        if (whiteListConfig == null) {
            return null;
        }
        if (zoneId == 0) {
            TempWhiteListConfig.WhiteConfig result = findInTempWhiteListMap(whiteListConfig.getEtcdWhiteAccount(), openId);
            if (result != null) {
                return result;
            }

            result = findInTempWhiteListMap(whiteListConfig.getEtcdWhiteDevice(), deviceId);
            if (result != null) {
                return result;
            }

            result = findInTempWhiteListMap(whiteListConfig.getEtcdWhiteIp(), clientIp);
            return result;
        }

        Map<String, TempWhiteListConfig.WhiteConfig> map;
        map = whiteListConfig.getEtcdWhiteAccount().get(ANY_ZONE_ID);
        if (map != null && map.containsKey(openId)) {
            return map.get(openId);
        }
        map = whiteListConfig.getEtcdWhiteAccount().get(zoneId);
        if (map != null && map.containsKey(openId)) {
            return map.get(openId);
        }
        map = whiteListConfig.getEtcdWhiteDevice().get(ANY_ZONE_ID);
        if (map != null && map.containsKey(deviceId)) {
            return map.get(deviceId);
        }
        map = whiteListConfig.getEtcdWhiteDevice().get(zoneId);
        if (map != null && map.containsKey(deviceId)) {
            return map.get(deviceId);
        }
        map = whiteListConfig.getEtcdWhiteIp().get(ANY_ZONE_ID);
        if (map != null && map.containsKey(clientIp)) {
            return map.get(clientIp);
        }
        map = whiteListConfig.getEtcdWhiteIp().get(zoneId);
        if (map != null && map.containsKey(clientIp)) {
            return map.get(clientIp);
        }
        return null;
    }

    /**
     * 获取xml配置
     *
     * @param zoneId   0：所有world； -1：当前world下所有zone；其他：当前world下指定zone
     */
    private WhiteListTemplate getWhiteListTemplate(int zoneId, String openId, String deviceId, String clientIp) {
        if (zoneId == 0) {
            WhiteListTemplate result = findInWhiteListMap(whiteAccount, openId);
            if (result != null) {
                return result;
            }

            result = findInWhiteListMap(whiteDevice, deviceId);
            if (result != null) {
                return result;
            }

            result = findInWhiteListMap(whiteIp, clientIp);
            return result;
        }

        Map<String, WhiteListTemplate> map;
        map = whiteAccount.get(ANY_ZONE_ID);
        if (map != null && map.containsKey(openId)) {
            return map.get(openId);
        }
        map = whiteAccount.get(zoneId);
        if (map != null && map.containsKey(openId)) {
            return map.get(openId);
        }
        map = whiteDevice.get(ANY_ZONE_ID);
        if (map != null && map.containsKey(deviceId)) {
            return map.get(deviceId);
        }
        map = whiteDevice.get(zoneId);
        if (map != null && map.containsKey(deviceId)) {
            return map.get(deviceId);
        }
        map = whiteIp.get(ANY_ZONE_ID);
        if (map != null && map.containsKey(clientIp)) {
            return map.get(clientIp);
        }
        map = whiteIp.get(zoneId);
        if (map != null && map.containsKey(clientIp)) {
            return map.get(clientIp);
        }
        return null;
    }

    public boolean clientCanLogin(String version) {
        try {
            if (version.isEmpty()) {
                return true;
            }
            ClientVersion clientVersion = ClientVersion.of(version);
            List<String> list = ClusterConfigUtils.getWorldConfig().getListStrItemMayEmpty("client_version_limit");
            if (list == null) {
                return false;
            }
            for (String config : list) {
                ClientVersion configVersion = ClientVersion.of(config);
                if (configVersion.compareTo(clientVersion) >= 0) {
                    return true;
                }
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    /**
     * Helper method to find a WhiteListTemplate in a map by key
     *
     * @param map the map to search in
     * @param key the key to search for
     * @return the WhiteListTemplate if found, null otherwise
     */
    private WhiteListTemplate findInWhiteListMap(Map<Integer, Map<String, WhiteListTemplate>> map, String key) {
        for (Map.Entry<Integer, Map<String, WhiteListTemplate>> entry : map.entrySet()) {
            if (entry.getValue().containsKey(key)) {
                return entry.getValue().get(key);
            }
        }
        return null;
    }

    /**
     * Helper method to find a TempWhiteListConfig.WhiteConfig in a map by key
     *
     * @param map the map to search in
     * @param key the key to search for
     * @return the TempWhiteListConfig.WhiteConfig if found, null otherwise
     */
    private TempWhiteListConfig.WhiteConfig findInTempWhiteListMap(Map<Integer, Map<String, TempWhiteListConfig.WhiteConfig>> map, String key) {
        for (Map.Entry<Integer, Map<String, TempWhiteListConfig.WhiteConfig>> entry : map.entrySet()) {
            if (entry.getValue().containsKey(key)) {
                return entry.getValue().get(key);
            }
        }
        return null;
    }

    public static class ClientVersion implements Comparable<ClientVersion> {
        public final int a;
        public final int b;
        public final int c;

        private ClientVersion(int a, int b, int c) {
            this.a = a;
            this.b = b;
            this.c = c;
        }

        public static ClientVersion of(String rawVersion) {
            if (StringUtils.isEmpty(rawVersion)) {
                throw new GeminiException("version is null", rawVersion);
            }
            String[] version = StringUtils.split(rawVersion, ".");
            if (version.length < 3) {
                throw new GeminiException("version={} not right", rawVersion);
            }
            return new ClientVersion(Integer.parseInt(version[0]), Integer.parseInt(version[1]), Integer.parseInt(version[2]));
        }

        @Override
        public int compareTo(@NotNull ClientVersion o) {
            if (a != o.a) {
                return -1;
            }

            if (b != o.b) {
                return -1;
            }

            if (c != o.c) {
                return -1;
            }
            return 0;
        }
    }
}

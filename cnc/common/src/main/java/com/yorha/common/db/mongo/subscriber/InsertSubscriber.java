package com.yorha.common.db.mongo.subscriber;

import com.mongodb.MongoWriteException;

public class InsertSubscriber<Response> extends NormalSubscriber<Response> {
    public InsertSubscriber() {
        super();
    }

    @Override
    public void onComplete() {
        // insert已存在时会在java层直接返回异常
        if (insertAlreadyExists(error)) {
            error = null;
        }
        safeConsume(afterConsumer, received, error);

    }

    private static boolean insertAlreadyExists(Throwable t) {
        if (t instanceof MongoWriteException) {
            //11000 插入失败：已存在
            return ((MongoWriteException) t).getError().getCode() == 11000;
        }
        return false;
    }
}

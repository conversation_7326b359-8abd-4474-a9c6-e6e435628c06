package com.yorha.common.utils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.db.tcaplus.msg.SelectAsk;
import com.yorha.common.db.tcaplus.msg.SelectUniqueAsk;
import com.yorha.common.db.tcaplus.option.GetByPartKeyOption;
import com.yorha.common.db.tcaplus.option.GetOption;
import com.yorha.common.db.tcaplus.result.GetByPartKeyResult;
import com.yorha.common.db.tcaplus.result.GetResult;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.TcaplusDb;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class PlayerUtils {
    private static final Logger LOGGER = LogManager.getLogger(PlayerUtils.class);

    private PlayerUtils() {
    }

    /**
     * 获取账号上次登录playerId以及tcaplus版本号
     *
     * @param openId 账号openId
     * @param actor  actor
     * @return Pair<playerId, tcaplusVesion>
     */
    public static Pair<Long, Integer> getLastLoginPlayerIdWithVersion(final String openId, final GameActorWithCall actor) {

        TcaplusDb.KVStoreTable.Builder builder = TcaplusDb.KVStoreTable.newBuilder()
                .setKey(openId)
                .setZoneId(openId.hashCode());
        GetOption getOption = GetOption.newBuilder().withGetAllFields(true).build();
        GetResult<TcaplusDb.KVStoreTable.Builder> getResult = null;
        try {
            getResult = actor.callGameDb(new SelectUniqueAsk<>(builder, getOption));
        } catch (Exception e) {
            throw new GeminiException("getLastLoginPlayerIdWithVersion fail", e);
        }

        // DB没有历史登录记录
        if (getResult.isNotExist()) {
            return Pair.of(0L, 0);
        }
        // DB操作失败
        if (!getResult.isOk()) {
            throw new GeminiException("getLastLoginPlayerIdWithVersion tcaplus errorCode={}", getResult.getCode());
        }
        return Pair.of(getResult.value.getValue().getLongValue(), getResult.getVersion());
    }


    /**
     * 获取一个账户各zone下的account
     *
     * @param openId 账号openId
     * @param actor  actor
     * @return Map<zoneId, List < AccountRole>>
     */
    public static Map<Integer, List<CommonMsg.AccountRole>> getAccountRoleUnderZone(final String openId, final GameActorWithCall actor) {
        TcaplusDb.AccountRoleTable.Builder req = TcaplusDb.AccountRoleTable.newBuilder();
        req.setOpenId(openId);
        GetByPartKeyResult<TcaplusDb.AccountRoleTable.Builder> result;
        try {
            result = actor.callGameDb(new SelectAsk<>(req, GetByPartKeyOption.newBuilder().build()));
        } catch (Exception e) {
            throw new GeminiException("getAccountRoleUnderZone fail", e);
        }
        // 无角色
        if (result.isRecordNotExist()) {
            return Collections.emptyMap();
        }
        // DB操作失败
        if (!result.isOk()) {
            LOGGER.error("PlayerUtils getAccountRoleUnderZone, db fail, requestId={}, code={}", result.requestId, result.getCode());
            throw new GeminiException("getAccountRoleUnderZone tcaplus errorCode={}", result.getCode());
        }
        final Map<Integer, List<CommonMsg.AccountRole>> ret = Maps.newHashMap();
        for (ValueWithVersion<TcaplusDb.AccountRoleTable.Builder> value : result.values) {
            int zoneId = value.value.getRoleInfo().getZoneId();
            if (!ret.containsKey(zoneId)) {
                ret.put(zoneId, Lists.newLinkedList());
            }
            ret.get(zoneId).add(value.value.getRoleInfo());
        }
        return ret;
    }

    /**
     * 获取一个账户的所有account
     *
     * @param openId 账号openId
     * @param actor  actor
     * @return List<AccountRole>
     */
    public static List<CommonMsg.AccountRole> getAccountRole(final String openId, final GameActorWithCall actor) {
        Map<Integer, List<CommonMsg.AccountRole>> accountUnderZone = getAccountRoleUnderZone(openId, actor);
        List<CommonMsg.AccountRole> ret = Lists.newLinkedList();
        for (List<CommonMsg.AccountRole> accounts : accountUnderZone.values()) {
            ret.addAll(accounts);
        }
        return ret;
    }

    /**
     * 检查指定zone下创角数量是否到达上限
     *
     * @param openId 账号openId
     * @param zoneId zoneId
     * @param actor  actor
     */
    public static void checkAccountRegisterLimit(final String openId, final int zoneId, final GameActorWithCall actor) throws GeminiException {
        final Map<Integer, List<CommonMsg.AccountRole>> zoneAccountInfo;
        try {
            zoneAccountInfo = PlayerUtils.getAccountRoleUnderZone(openId, actor);
        } catch (GeminiException geminiException) {
            LOGGER.error("checkAccountRegisterLimit getAccountRoleUnderZone fail, ", geminiException);
            throw new GeminiException(ErrorCode.MULTI_SERVER_REGISTER_REACH_ZONE_LIMIT);
        }

        final int registerLimit = ClusterConfigUtils.getWorldConfig().getIntItem("account_zone_register_limit");
        final int registerNum = zoneAccountInfo.getOrDefault(zoneId, Collections.emptyList()).size();
        LOGGER.info("checkAccountRegisterLimit openId={} has registered {} account in zone={}, cur registerLimit={}", openId, registerNum, zoneId, registerLimit);
        if (registerNum >= registerLimit) {
            LOGGER.info("checkAccountRegisterLimit openId={} has reached register limit in zone={}", openId, zoneId);
            throw new GeminiException(ErrorCode.MULTI_SERVER_REGISTER_REACH_ZONE_LIMIT);
        }
    }
}

package com.yorha.common.wechatlog;

import java.util.concurrent.TimeUnit;

public class ThirdPartyErrLogConfig {

    /**
     * @param limiter
     * @return 两次执行操作的最短间隔时间
     */
    public static long getGapMs(ThirdPartyErrLogLimiter limiter) {
        switch (limiter) {
            case HTTP_FAIL:
                return TimeUnit.MINUTES.toMillis(1);
            case INTL_AUTH_OUT_OF_TIME:
                return TimeUnit.MINUTES.toMillis(1);
            case MIDAS_OUT_OF_TIME:
                return TimeUnit.MINUTES.toMillis(1);
            case TEXT_FILTER_OUT_OF_TIME:
                return TimeUnit.MINUTES.toMillis(1);
            default:
                return 0;
        }
    }

    /**
     * @return 触发执行的阈值
     */
    public static int getThreshold(ThirdPartyErrLogLimiter limiter) {
        return switch (limiter) {
            case HTTP_FAIL -> 10;
            case INTL_AUTH_OUT_OF_TIME, TEXT_FILTER_OUT_OF_TIME, MIDAS_OUT_OF_TIME -> 5;
        };
    }

}

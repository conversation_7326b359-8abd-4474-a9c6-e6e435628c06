package com.yorha.common.resource.resservice.activity;

import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.proto.CommonEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.mockito.internal.util.collections.Sets;
import res.template.ActivityTemplate;
import res.template.BestCommanderSeasonTemplate;
import res.template.ItemTemplate;

import java.util.*;

/**
 * 最强指挥官活动配置
 *
 * <AUTHOR>
 */
public class BestCommanderActResService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(BestCommanderActResService.class);

    public BestCommanderActResService(ResHolder resHolder) {
        super(resHolder);
    }

    private final Map<Integer, Map<CommonEnum.CommanderActivtyStage, List<BestCommanderSeasonTemplate>>> season2ConfigMap = new HashMap<>();

    /**
     * 获取本期最强指挥官配置
     *
     * @param actId  活动id
     * @param volume 当前赛季的第几期
     * @return 如果没有对应配置，可能为null，业务侧需要对null做好处理
     */
    public BestCommanderSeasonTemplate getSeasonConfig(int actId, CommonEnum.CommanderActivtyStage actStage, int volume) {
        final Map<CommonEnum.CommanderActivtyStage, List<BestCommanderSeasonTemplate>> zoneSeasonListMap = season2ConfigMap.get(actId);
        if (zoneSeasonListMap == null) {
            LOGGER.error("BestCommanderActResService getSeasonConfig null actId: {}", actId);
            return null;
        }
        final List<BestCommanderSeasonTemplate> zoneSeasonList = zoneSeasonListMap.get(actStage);
        if (zoneSeasonList == null) {
            LOGGER.error("BestCommanderActResService getSeasonConfig null actId: {} stage: {}", actId, actStage);
            return null;
        }
        // 返回对应周期的配置
        for (BestCommanderSeasonTemplate seasonTemplate : zoneSeasonList) {
            final IntPairType volumePair = seasonTemplate.getVolumePair();
            if (volumePair.getKey() <= volume && volumePair.getValue() > volume) {
                return seasonTemplate;
            }
        }
        return null;
    }

    @Override
    public void load() throws ResourceException {
        for (BestCommanderSeasonTemplate seasonTemplate : getResHolder().getListFromMap(BestCommanderSeasonTemplate.class)) {
            final var zoneSeasonListMap = season2ConfigMap.computeIfAbsent(seasonTemplate.getActId(), key -> new HashMap<>());
            zoneSeasonListMap.computeIfAbsent(seasonTemplate.getActStage(), key -> new ArrayList<>()).add(seasonTemplate);
        }
    }

    /**
     * 需要注意的是，这里校验完后，是有可能存在某个赛季，某一期的最强指挥官活动没有配置的，这时候需要业务侧进行处理
     */
    @Override
    public void checkValid() throws ResourceException {
        // 配置了赛季版本最强指挥官配置的活动id
        final Set<Integer> hasSeasonConfigSet = Sets.newSet();
        for (BestCommanderSeasonTemplate seasonTemplate : getResHolder().getListFromMap(BestCommanderSeasonTemplate.class)) {
            hasSeasonConfigSet.add(seasonTemplate.getActId());
            final Map<Integer, ItemTemplate> itemTemplateMap = getResHolder().getMap(ItemTemplate.class);
            final IntPairType volumePair = seasonTemplate.getVolumePair();
            // 循环期数的校验
            if (volumePair.getValue() <= volumePair.getKey()) {
                throw new ResourceException("{}, id: {} 配置循环期数最大期数小于等于最小期数了: {}", getExcelName(), seasonTemplate.getId(), volumePair);
            }
            // 自选奖励没有配置
            if (seasonTemplate.getIsSelect() && (seasonTemplate.getRewardList().size() <= 0)) {
                throw new ResourceException("{}, id: {} 自选奖励没有配置", getExcelName(), seasonTemplate.getId());
            }
            // 奖励英雄凭证的校验
            for (int reward : seasonTemplate.getRewardList()) {
                if (!itemTemplateMap.containsKey(reward)) {
                    throw new ResourceException("{}, id: {} 配置了不存在的道具id: {}", getExcelName(), seasonTemplate.getId(), reward);
                }
            }
        }
        // 每一个赛季版本的最强指挥官活动都需要在 Z_最强指挥官活动赛季版中对应配置
        for (ActivityTemplate activityTemplate : getResHolder().getListFromMap(ActivityTemplate.class)) {
            if (activityTemplate.getSpecUnitType() != CommonEnum.ActivityUnitType.AUT_BEST_COMMANDER_SEASON) {
                continue;
            }
            if (!hasSeasonConfigSet.contains(activityTemplate.getId())) {
                throw new ResourceException("{}，缺少配置 活动id {}", getExcelName(), activityTemplate.getId());
            }
        }
    }

    private String getExcelName() {
        return "Z_最强指挥官活动赛季版, best_commander_season";
    }
}

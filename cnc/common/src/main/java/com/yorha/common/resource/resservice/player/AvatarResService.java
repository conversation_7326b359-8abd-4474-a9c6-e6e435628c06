package com.yorha.common.resource.resservice.player;

import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.exception.ResourceException;
import com.yorha.proto.CommonEnum;
import res.template.CommanderAvatarFrameTemplate;
import res.template.CommanderAvatarTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 头像配置
 *
 * <AUTHOR>
 */
public class AvatarResService extends AbstractResService {
    private final List<Integer> initPic = new ArrayList<>();
    private final List<Integer> initPicFrame = new ArrayList<>();

    public AvatarResService(ResHolder resHolder) {
        super(resHolder);
    }

    public List<Integer> getInitPic() {
        return initPic;
    }

    public List<Integer> getInitPicFrame() {
        return initPicFrame;
    }

    @Override
    public void load() throws ResourceException {
        for (CommanderAvatarTemplate template : getResHolder().getListFromMap(CommanderAvatarTemplate.class)) {
            if (template.getUnlock() != CommonEnum.AvatarUnlockType.AULT_INIT) {
                continue;
            }
            initPic.add(template.getId());
            if (template.getUnlockMovingPicId() > 0) {
                initPic.add(template.getUnlockMovingPicId());
            }
        }

        for (CommanderAvatarFrameTemplate template : getResHolder().getListFromMap(CommanderAvatarFrameTemplate.class)) {
            if (template.getUnlock() != CommonEnum.AvatarUnlockType.AULT_INIT) {
                continue;
            }
            initPicFrame.add(template.getId());
        }
    }

    public int getMovingPicId(int picId) {
        CommanderAvatarTemplate template = getResHolder().getValueFromMap(CommanderAvatarTemplate.class, picId);
        return template.getUnlockMovingPicId();
    }

    @Override
    public void checkValid() throws ResourceException {
        Map<Integer, CommanderAvatarTemplate> avatarTemplateMap = getResHolder().getMap(CommanderAvatarTemplate.class);
        for (CommanderAvatarTemplate template : avatarTemplateMap.values()) {
            int movingPicId = template.getUnlockMovingPicId();
            if (movingPicId <= 0) {
                continue;
            }
            if (!avatarTemplateMap.containsKey(movingPicId)) {
                throw new ResourceException("AvatarResService not have UnlockMovingAvatarId:{} for id:{}", movingPicId, template.getId());
            }
        }
    }
}

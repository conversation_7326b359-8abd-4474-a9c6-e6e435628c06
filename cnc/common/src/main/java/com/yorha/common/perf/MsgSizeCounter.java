package com.yorha.common.perf;

import com.yorha.common.server.ServerContext;
import io.prometheus.client.Gauge;

import java.util.HashMap;
import java.util.Map;

public class MsgSizeCounter {
    private final Map<String, Long> maxSizeMap = new HashMap<>();
    // 只有比这个值大才有关注的意义
    private final long minSize;
    private final Gauge gauge;

    public MsgSizeCounter(long minSize, Gauge gauge) {
        this.minSize = minSize;
        this.gauge = gauge;
    }

    public void log(String msgTypeName, long curSize) {
        if (curSize <= minSize) {
            // 不如阈值，886
            return;
        }

        Long oldSize = maxSizeMap.getOrDefault(msgTypeName, 0L);
        if (curSize > oldSize) {
            maxSizeMap.put(msgTypeName, curSize);
            gauge.labels(ServerContext.getBusId(), msgTypeName).set(curSize);
        }
    }

    public void clear() {
        // 清理monitor
        gauge.clear();
    }
}

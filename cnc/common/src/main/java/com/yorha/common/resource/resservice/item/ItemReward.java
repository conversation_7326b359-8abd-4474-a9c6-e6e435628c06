package com.yorha.common.resource.resservice.item;

import com.yorha.game.gen.prop.DisplayRewardProp;
import com.yorha.common.exception.GeminiException;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * <AUTHOR>
 */
public class ItemReward {

    private final int itemTemplateId;
    private final int count;
    private final int weight;
    private final int configId;
    private final ItemRewardCondition condition;

    public static class Builder {

        private int itemTemplateId;
        private int count;
        private int weight;
        private int configId;
        private ItemRewardCondition condition;

        public Builder setCondition(ItemRewardCondition condition) {
            this.condition = condition;
            return this;
        }

        public Builder setItemTemplate(int itemTemplateId) {
            this.itemTemplateId = itemTemplateId;
            return this;
        }

        public Builder setCount(int count) {
            this.count = count;
            return this;
        }

        public Builder setWeight(int weight) {
            this.weight = weight;
            return this;
        }

        public Builder setConfigId(int configId) {
            this.configId = configId;
            return this;
        }

        public ItemReward build() {
            if (itemTemplateId <= 0) {
                throw new GeminiException("invaild itemReward");
            }
            return new ItemReward(this);
        }
    }

    private ItemReward(Builder builder) {
        this.itemTemplateId = builder.itemTemplateId;
        this.count = builder.count;
        this.weight = builder.weight;
        this.configId = builder.configId;
        this.condition = builder.condition;
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public int getItemTemplateId() {
        return itemTemplateId;
    }

    public int getCount() {
        return count;
    }

    public int getConfigId() {
        return configId;
    }

    public boolean canPlus(ItemReward other) {
        return itemTemplateId == other.getItemTemplateId();
    }

    public ItemReward plus(ItemReward other) {
        if (canPlus(other)) {
            return ItemReward.newBuilder()
                    .setItemTemplate(other.getItemTemplateId())
                    .setCount(Math.addExact(this.getCount(), other.getCount()))
                    .build();
        }
        return this;
    }

    public int getWeight() {
        return weight;
    }

    public ItemRewardCondition getCondition() {
        return condition;
    }

    public DisplayRewardProp toDisplay() {
        return new DisplayRewardProp().setId(itemTemplateId).setNum(count);
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}

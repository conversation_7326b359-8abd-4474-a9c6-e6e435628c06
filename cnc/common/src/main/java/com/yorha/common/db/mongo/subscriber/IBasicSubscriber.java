package com.yorha.common.db.mongo.subscriber;

import org.reactivestreams.Subscriber;
import org.reactivestreams.Subscription;

import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 * subscriber基类
 *
 * <AUTHOR>
 */
public abstract class IBasicSubscriber<Response, ConsumeResponse> implements Subscriber<Response> {
    protected Subscription subscription;
    protected BiConsumer<ConsumeResponse, Throwable> afterConsumer;
    protected ConsumeResponse received = null;
    protected Throwable error = null;

    @Override
    public void onComplete() {
        this.safeConsume(afterConsumer, received, error);
    }

    @Override
    public final void onSubscribe(final Subscription s) {
        subscription = s;
    }

    public final void start() {
        this.subscription.request(Long.MAX_VALUE);
    }

    public void afterConsume(final BiConsumer<ConsumeResponse, Throwable> afterConsumer) {
        this.afterConsumer = afterConsumer;
    }

    protected <T> void safeConsume(Consumer<T> consumer, T data) {
        if (consumer != null) {
            consumer.accept(data);
        }
    }

    protected <T> void safeConsume(BiConsumer<T, Throwable> consumer, T data, Throwable t) {
        if (consumer != null) {
            consumer.accept(data, t);
        }
    }
}

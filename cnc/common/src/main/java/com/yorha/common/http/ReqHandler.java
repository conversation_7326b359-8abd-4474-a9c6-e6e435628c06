package com.yorha.common.http;

import com.yorha.common.utils.reflect.MethodWrapper;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.handler.codec.http.*;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * http URL 处理器
 *
 * <AUTHOR>
 */
public interface ReqHandler {

    /**
     * 处理请求
     */
    void processRequest(Channel channel, FullHttpRequest req, FullHttpResponse resp);

    /**
     * 请求前缀
     */
    String getUrl();

    default void processReqAndSendRes(Channel channel, FullHttpRequest req, FullHttpResponse resp) {
        processRequest(channel, req, resp);
        sendRes(channel, req, resp);
    }

    default void sendRes(Channel channel, FullHttpRequest req, FullHttpResponse resp) {
        resp.headers().set(HttpHeaderNames.CONTENT_TYPE, "text/html; charset=UTF-8");
        int length = resp.content().readableBytes();
        if (length == 0) {
            resp.setStatus(HttpResponseStatus.NO_CONTENT);
        }
        resp.headers().set(HttpHeaderNames.CONTENT_LENGTH, length);
        boolean keepAlive = HttpUtil.isKeepAlive(req);
        if (keepAlive) {
            resp.headers().set(HttpHeaderNames.CONNECTION, HttpHeaderValues.KEEP_ALIVE);
        }
        ChannelFuture future = channel.writeAndFlush(resp);
        // Close the non-keep-alive connection after the write operation is
        // done.
        if (!keepAlive || resp.status() != HttpResponseStatus.OK) {
            future.addListener(ChannelFutureListener.CLOSE);
        }
    }

    default Map<String, MethodWrapper> registerMethods(Object instance) {
        Map<String, MethodWrapper> cachedMethod = new HashMap<>();
        Method[] methods = instance.getClass().getSuperclass().getDeclaredMethods();

        for (Method method : methods) {
            if (!StringUtils.contains(method.getName(), "lambda")) {
                String methodName = method.getName();
                method.setAccessible(true);
                cachedMethod.put(methodName, new MethodWrapper(method, instance));
            }
        }
        return cachedMethod;
    }
}

package com.yorha.common.actorservice;

import com.yorha.common.actor.cluster.net.IActorClusterNetServer;
import com.yorha.common.actor.cluster.net.NatsActorClusterNetServer;
import com.yorha.common.actor.dispatcher.ElasticFiberPoolActorDispatcherImpl;
import com.yorha.common.actor.dispatcher.IMailboxDispatcher;
import com.yorha.common.actor.dispatcher.OneDriveFiberActorDispatcherImpl;
import com.yorha.common.actor.dispatcher.ThreadPoolActorDispatcherImpl;
import com.yorha.common.actor.msg.ActorSerializer;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.server.ServerContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 统管同一个system下的actor和ref
 *
 * <AUTHOR>
 */
public class ActorSystem {
    private static final Logger LOGGER = LogManager.getLogger(ActorSystem.class);

    /**
     * 全服相关的zone，注意是字符串
     */
    public static final int GLOBAL_ZONE = 0;
    /**
     * 系统名称
     */
    private final String name;
    /**
     * Actor元信息管理
     */
    private final ActorMetaDataMgr actorMetaDataMgr;
    /**
     * nats连接
     */
    private final IActorClusterNetServer netServer;
    /**
     * 服务发现。
     */
    private final ActorRegistryValue registryValue;
    /**
     * 分配器列表
     */
    private final Map<String, LazyLoadDispatcher> dispatcherMap;
    /**
     * 是否已经关闭。
     */
    private volatile boolean isShutdown;

    private ActorSystem(Builder builder) {
        this.name = builder.name;
        this.dispatcherMap = new HashMap<>(builder.dispatcherMap.size());
        for (final Map.Entry<String, Supplier<IMailboxDispatcher>> kv : builder.dispatcherMap.entrySet()) {
            this.dispatcherMap.put(kv.getKey(), new LazyLoadDispatcher(kv.getValue()));
        }
        this.netServer = builder.server;
        final List<ActorMetaData> metaDataList = builder.metaBuilderMap.values().stream().map(b -> b.build(this)).collect(Collectors.toList());
        this.registryValue = new ActorRegistryValue(this, builder.busId, metaDataList);
        this.actorMetaDataMgr = new ActorMetaDataMgr(metaDataList);
        this.isShutdown = true;
    }

    public IMailboxDispatcher getDispatcher(final String name) {
        if (this.dispatcherMap.containsKey(name)) {
            return this.dispatcherMap.get(name).load();
        }
        throw new GeminiException("{} dispatcher {} not exist!", this, name);
    }

    public synchronized void start() {
        if (!this.isShutdown) {
            throw new GeminiException("{} already start!", this);
        }
        this.netServer.start();
        this.isShutdown = false;
    }

    /**
     * 关闭整个ActorSystem。
     */
    public synchronized void shutdown() {
        if (this.isShutdown) {
            throw new GeminiException("{} already shutdown!", this);
        }
        this.isShutdown = true;
        this.netServer.shutdown();
        for (final LazyLoadDispatcher dispatcher : dispatcherMap.values()) {
            dispatcher.shutdown();
            LOGGER.info("gemini_system shutdown system! close ActorDispatcher {}!", dispatcher);
        }
        LOGGER.info("gemini_system shutdown system! done!");
    }

    /**
     * 元数据管理器。
     *
     * @return 管理器对象。
     */
    public ActorMetaDataMgr getActorMetaDataMgr() {
        return actorMetaDataMgr;
    }

    /**
     * 集群网络通信服务器。
     *
     * @return 通信服务器。
     */
    public IActorClusterNetServer getNetServer() {
        return this.netServer;
    }

    /**
     * 服务发现列表。
     *
     * @return 服务发现器。
     */
    public ActorRegistryValue getRegistryValue() {
        return registryValue;
    }

    /**
     * 是否已关闭。
     *
     * @return 是否关闭。
     */
    public boolean isShutdown() {
        return this.isShutdown;
    }

    /**
     * 打印数据统计。
     */
    public void printPerfLog() {
        // 打印任务统计
        ServerContext.getActorPerfLogger().flush();
        for (final String actorRole : this.registryValue.getLocalActorRoleList()) {
            this.getActorMetaDataMgr().getActorMetaData(actorRole).getMailboxMiddleware().perfReport();
        }
    }

    @Override
    public String toString() {
        return "ActorSystem{" + this.name + "}";
    }

    /**
     * 懒加载的MailboxDispatcher
     */
    private static class LazyLoadDispatcher {
        private final Supplier<IMailboxDispatcher> factory;
        private IMailboxDispatcher dispatcher;

        private LazyLoadDispatcher(final Supplier<IMailboxDispatcher> factory) {
            this.factory = factory;
        }

        private synchronized IMailboxDispatcher load() {
            if (this.dispatcher == null) {
                this.dispatcher = this.factory.get();
            }
            return this.dispatcher;
        }

        private synchronized void shutdown() {
            if (this.dispatcher != null) {
                this.dispatcher.shutdown();
            }
            this.dispatcher = null;
        }

        @Override
        public String toString() {
            return "LazyLoadDispatcher{" +
                    ", dispatcher=" + dispatcher +
                    '}';
        }
    }

    public static class Builder {
        private final Map<String, Supplier<IMailboxDispatcher>> dispatcherMap = new HashMap<>();
        private final Map<String, ActorMetaData.Builder> metaBuilderMap = new HashMap<>();
        private IActorClusterNetServer server;
        private String name;
        private String busId;

        private Builder() {
        }

        /**
         * 配置ActorSystem的名称。
         *
         * @param name 名称。
         * @return builder。
         */
        public Builder name(final String name) {
            this.name = name;
            return this;
        }

        /**
         * 配置ActorSystem对应的集群busId，必须配置。
         *
         * @param busId bus id。
         * @return builder。
         */
        public Builder busId(final String busId) {
            this.busId = busId;
            return this;
        }

        /**
         * 配置一个一次性协程调度器。
         *
         * @param name         调度器名称。
         * @param throughput    一次调度消费消息上限。
         * @param parallelism  并发度，代表占用CPU数量。
         * @param keepAliveSec 无消息情况下，协程的存活时间。
         * @return builder。
         */
        public Builder addOneDriveFiberDispatcher(final String name, final int throughput, final int parallelism, final int keepAliveSec) {
            if (this.dispatcherMap.containsKey(name)) {
                throw new GeminiException("already add dispatcher {} into Builder!", name);
            }

            this.dispatcherMap.put(name, () -> new OneDriveFiberActorDispatcherImpl(name, throughput, parallelism, keepAliveSec));
            return this;
        }

        /**
         * 配置一个线程池调度器。
         *
         * @param name        调度器名称。
         * @param throughput  一次调度消费消息上限。
         * @param parallelism 并发度，代表占用CPU数量。
         * @return builder。
         */
        public Builder addThreadPoolDispatcher(final String name, final int throughput, final int parallelism) {
            if (this.dispatcherMap.containsKey(name)) {
                throw new GeminiException("already add dispatcher {} into Builder!", name);
            }
            this.dispatcherMap.put(name, () -> new ThreadPoolActorDispatcherImpl(name, throughput, parallelism));
            return this;
        }

        /**
         * @param name            调度器名称。
         * @param throughput      一次调度消费消息上限。
         * @param parallelism     并发度，代表占用CPU数量。
         * @param keepAliveSec    无消息情况下，协程被占用（存活）的时间。
         * @param poolFiberCnt    池化协程的最大数量。
         * @param elasticFiberCnt 即产即销毁协程的最大数量。
         * @return Builder。
         */
        public Builder addElasticFiberPoolDispatcher(
                final String name,
                final int throughput,
                final int parallelism,
                final int keepAliveSec,
                final int poolFiberCnt,
                final int elasticFiberCnt
        ) {
            if (this.dispatcherMap.containsKey(name)) {
                throw new GeminiException("already add dispatcher {} into Builder!", name);
            }
            this.dispatcherMap.put(name,
                    () -> new ElasticFiberPoolActorDispatcherImpl(name, throughput, parallelism, keepAliveSec, poolFiberCnt, elasticFiberCnt));
            return this;
        }

        /**
         * 添加一个构造好的supplier
         *
         * @param name     调度器名称
         * @param supplier Dispatcher构造方法
         * @return Builder
         */
        public Builder addDispatcher(final String name, final Supplier<IMailboxDispatcher> supplier) {
            if (this.dispatcherMap.containsKey(name)) {
                throw new GeminiException("already add dispatcher {} into Builder!", name);
            }
            this.dispatcherMap.put(name, supplier);
            return this;
        }

        /**
         * 配置一个Actor元数据的描述。
         *
         * @param name     名称。
         * @param shardNum > 0 标识shard模式actor; <= 0 代表普通Actor。
         * @return builder。
         */
        public Builder addActorMetaBuilder(final String name, final int shardNum) {
            if (this.metaBuilderMap.containsKey(name)) {
                throw new GeminiException("dispatcher {} not add into Builder!", name);
            }
            this.metaBuilderMap.put(name, ActorMetaData.newBuilder(name, shardNum));
            return this;
        }

        /**
         * 使用构造好的ActorMetaBuilder更新Builder
         *
         * @param name    名称
         * @param builder ActorMetaBuilder
         * @return builder
         */
        public Builder updateActorMetaBuilder(final String name, final ActorMetaData.Builder builder) {
            if (!this.metaBuilderMap.containsKey(name)) {
                throw new GeminiException("ActorMetaData {} not add into Builder!", name);
            }
            this.metaBuilderMap.put(name, builder);
            return this;
        }

        /**
         * 配置基于nats的网络通信方案。
         *
         * @param natsAddress nats地址。
         * @param serializer  消息序列化器。
         * @return builder。
         */
        public Builder natsServer(final String natsAddress, final ActorSerializer serializer) {
            if (this.busId == null) {
                throw new GeminiException("bus id not set!");
            }
            this.server = new NatsActorClusterNetServer(natsAddress, busId, serializer);
            return this;
        }

        /**
         * 配置网络方案。
         *
         * @param server 网络服务器。
         * @return builder。
         */
        public Builder server(final IActorClusterNetServer server) {
            if (this.server != null) {
                throw new GeminiException("server already set!");
            }
            this.server = server;
            return this;
        }

        /**
         * 获取一个配置好的actor元数据描述的构造器。
         *
         * @param name 元数据描述名称。
         * @return 元数据构造器。
         */
        public ActorMetaData.Builder getActorMetaBuilder(final String name) {
            if (!this.metaBuilderMap.containsKey(name)) {
                throw new GeminiException("{} not add into Builder!", name);
            }
            return this.metaBuilderMap.get(name);
        }

        /**
         * 构造一个ActorSystem。
         *
         * @return ActorSystem实例。
         */
        public ActorSystem build() {
            if (this.busId == null) {
                throw new GeminiException("bus id not set!");
            }
            return new ActorSystem(this);
        }
    }

    public static Builder newBuilder() {
        return new Builder();
    }
}

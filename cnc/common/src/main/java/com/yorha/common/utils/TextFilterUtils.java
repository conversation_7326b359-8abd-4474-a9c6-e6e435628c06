package com.yorha.common.utils;

import com.yorha.common.constant.TextFilterConstant;
import com.yorha.common.server.config.ClusterConfigUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 * @date 2023/12/14
 */
public class TextFilterUtils {
    private static final Logger LOGGER = LogManager.getLogger(TextFilterUtils.class);

    public static TextFilterConstant.TextFilterType getTextFilterType() {
        int textFilterType = 0;
        try {
            textFilterType = ClusterConfigUtils.getWorldConfig().getIntItem("text_filter_type");
        } catch (Exception e) {
            LOGGER.warn("text_filter_type no config use 0");
        }
        return TextFilterConstant.TextFilterType.forNumber(textFilterType);
    }

    public static boolean isTssSdk() {
        return getTextFilterType() == TextFilterConstant.TextFilterType.TSSSDK;
    }
}

package com.yorha.common.actor.mailbox;

import com.yorha.common.actor.mailbox.msg.ActorMailboxMsg;
import com.yorha.common.actorservice.IMsgContext;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import org.jetbrains.annotations.NotNull;

import javax.annotation.concurrent.NotThreadSafe;

/**
 * <AUTHOR>
 */
@NotThreadSafe
public final class MsgContext implements IMsgContext {
    public static class SubMsg {
        public final long startTsNs;
        public final long endTsNs;
        public final ActorMsgEnvelope envelope;
        private SubMsg next;

        public SubMsg(long startTsNs, long endTsNs, ActorMsgEnvelope envelope, SubMsg next) {
            this.startTsNs = startTsNs;
            this.endTsNs = endTsNs;
            this.envelope = envelope;
            this.next = next;
        }

        public SubMsg getNext() {
            return next;
        }
    }

    private ActorMailboxMsg msg;
    private final IActorMailbox mailbox;
    private SubMsg head;
    private SubMsg tail;

    MsgContext(IActorMailbox mailbox) {
        this.mailbox = mailbox;
    }

    void setMsg(ActorMailboxMsg msg) {
        this.msg = msg;
        this.tail = this.head = null;
    }

    @Override
    public @NotNull ActorMsgEnvelope getEnvelope() {
        return this.msg.getMsg();
    }

    @Override
    public @NotNull IActorMailbox getMailbox() {
        return this.mailbox;
    }

    @Override
    public void addCostSendEnvelop(final long startTsNs, final long endTsNs, final ActorMsgEnvelope envelope) {
        if (this.tail == null) {
            this.tail = this.head = new SubMsg(startTsNs, endTsNs, envelope, null);
            return;
        }
        this.tail.next = new SubMsg(startTsNs, endTsNs, envelope, null);
        this.tail = this.tail.next;
    }

    public SubMsg getSubEnvelopHead() {
        return this.head;
    }

    public ActorMailboxMsg getMsg() {
        return this.msg;
    }
}

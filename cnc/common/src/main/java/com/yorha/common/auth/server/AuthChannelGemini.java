package com.yorha.common.auth.server;

import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.auth.server.result.AuthResult;
import com.yorha.common.constant.AuthConstant;
import com.yorha.common.io.ISession;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.CsAccount;
import com.yorha.proto.User;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * Gemini鉴权渠道
 *
 * <AUTHOR>
 */
public class AuthChannelGemini implements AuthChannel {
    private static final Logger LOGGER = LogManager.getLogger(AuthChannelGemini.class);

    @Override
    public AuthConstant.AuthChannelType authChannelType() {
        return AuthConstant.AuthChannelType.GEMINI;
    }

    @Override
    public void init() {
    }

    @Override
    public AuthResult<CommonMsg.YoAccountToken> auth(ISession session, GameActorWithCall actor, String openId, User.GetRoleList_C2S_Msg msg) {
        if (!StringUtils.equals(openId, msg.getAccountToken().getOpenId())) {
            return AuthResult.fail(CommonEnum.AuthRetCode.ARC_FAIL, "Token Not Right");
        }

        String accessToken = msg.getAccountToken().getAccessToken();

        // 能取到真实的yorha员工名
        // String userInfo = YorhaAuthHandler.getInstance().getUserInfo(accessToken, openId);
        LOGGER.info("AuthChannelGemini auth openId:{} token:{}", openId, accessToken);

        return AuthResult.ofSuccess();
    }

    @Override
    public String getOpenId(User.GetRoleList_C2S_Msg msg) {
        return msg.getAccountToken().getOpenId();
    }

    @Override
    public String getOpenId(CsAccount.DirGetServerList_C2S_Msg msg) {
        return msg.getAccountToken().getOpenId();
    }

    @Override
    public String getOpenId(CsAccount.DirGetZone_C2S_Msg msg) {
        return msg.getAccountToken().getOpenId();
    }
}

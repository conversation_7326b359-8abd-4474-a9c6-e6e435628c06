package com.yorha.common.concurrent;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 线程工厂,可以设置别名
 *
 * <AUTHOR>
 */
public class NameableThreadFactory implements ThreadFactory {
    private final String name;
    private final ThreadGroup group;
    private final AtomicInteger threadCounter = new AtomicInteger(0);
    private final boolean isDaemon;

    @Override
    public Thread newThread(Runnable runnable) {
        Thread thread = new Thread(group, runnable, name + "-" + threadCounter.getAndIncrement());
        thread.setDaemon(this.isDaemon);
        return thread;
    }

    /**
     * 构造一个线程工厂，创建的线程属于当前线程组，具有指定名称和守护状态。
     *
     * @param name     线程名称前缀
     * @param isDaemon 是否为守护线程
     */
    public NameableThreadFactory(String name, boolean isDaemon) {
        this.name = name;
        this.isDaemon = isDaemon;
        this.group = Thread.currentThread().getThreadGroup();
    }

    public NameableThreadFactory(String name) {
        this(name, true);
    }
}

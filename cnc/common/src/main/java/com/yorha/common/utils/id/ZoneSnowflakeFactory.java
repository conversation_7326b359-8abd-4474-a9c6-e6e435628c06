package com.yorha.common.utils.id;

import com.yorha.common.actor.cluster.ActorClusterUrlUtils;
import com.yorha.common.server.ServerContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.atomic.AtomicReference;

/**
 * Zone专用雪花算法ID生成器工厂
 * 负责为每个Zone分配唯一的workerId并创建SnowflakeIdGenerator实例
 */
public class ZoneSnowflakeFactory {
    private static final Logger LOGGER = LogManager.getLogger(ZoneSnowflakeFactory.class);

    private static final AtomicReference<SnowflakeIdGenerator> INSTANCE = new AtomicReference<>();
    private static int workerId;

    private ZoneSnowflakeFactory() {
        // 私有构造函数防止实例化
    }

    /**
     * 初始化并获取Zone专用的SnowflakeIdGenerator实例
     *
     * @return SnowflakeIdGenerator实例
     */
    public static synchronized SnowflakeIdGenerator getInstance() {
        ensureZoneServer();

        if (INSTANCE.get() != null) {
            return INSTANCE.get();
        }

        int zoneId = ServerContext.getZoneId();
        workerId = allocateWorkerId(zoneId);

        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(workerId);
        INSTANCE.set(generator);

        LOGGER.info("ZoneSnowflakeFactory initialized for zoneId: {}, with workerId: {}", zoneId, workerId);
        return generator;
    }

    /**
     * 使用workerId创建一个新的SnowflakeIdGenerator实例
     *
     * @return SnowflakeIdGenerator实例
     */
    public static SnowflakeIdGenerator newInstance() {
        ensureZoneServer();
        return new SnowflakeIdGenerator(workerId);
    }

    /**
     * 不建议使用默认的nextId方法！！！
     * 建议每个actor内部维护一个SnowflakeIdGenerator对象
     * 使用雪花算法生成下一个ID
     */
    public static synchronized Long nextId() {
        ensureZoneServer();
        return getInstance().nextId();
    }

    private static void ensureZoneServer() {
        if (!ServerContext.isZoneServer()) {
            throw new IllegalStateException("ZoneSnowflakeFactory can only be used in ZoneServer");
        }
    }

    /**
     * 为指定的zoneId分配一个唯一的workerId
     *
     * @param zoneId 区服ID
     * @return 分配的workerId
     */
    private static int allocateWorkerId(int zoneId) {
        String etcdWorkerIdKey = ActorClusterUrlUtils.etcdGlobalWorkerIdUrl(zoneId);
        String workerIdStr = ServerContext.getEtcdClient().getSingle(etcdWorkerIdKey);
        LOGGER.info("allocateWorkerId, etcdKey={}, workerIdStr={}", etcdWorkerIdKey, workerIdStr);

        if (workerIdStr != null && !workerIdStr.isEmpty()) {
            // 已经分配过workerId，直接使用
            var existingId = Integer.parseInt(workerIdStr);
            //确保workerId在有效范围内
            if (existingId < 0 || existingId >= SnowflakeIdGenerator.MAX_WORKER_ID) {
                throw new RuntimeException("WorkerId exceeded maximum value: " + SnowflakeIdGenerator.MAX_WORKER_ID);
            }
            LOGGER.info("Found existing workerId: {} for zoneId: {}", existingId, zoneId);
            return existingId;
        }

        // 需要分配新的workerId
        String globalCounterKey = ActorClusterUrlUtils.etcdGlobalWorkerIdCounterUrl();

        // 尝试15次，避免并发问题
        for (int i = 0; i < 15; i++) {
            String rawValue = ServerContext.getEtcdClient().getSingle(globalCounterKey);
            int counterValue = 0;

            if (rawValue != null && !rawValue.isEmpty()) {
                counterValue = Integer.parseInt(rawValue);
            }

            // 检查是否超出最大workerId
            if (counterValue >= SnowflakeIdGenerator.MAX_WORKER_ID) {
                throw new RuntimeException("WorkerId counter exceeded maximum value: " + SnowflakeIdGenerator.MAX_WORKER_ID);
            }

            int newWorkerId = counterValue + 1;
            boolean updateCounter;

            if (rawValue == null) {
                updateCounter = ServerContext.getEtcdClient().setNX(globalCounterKey, String.valueOf(newWorkerId));
            } else {
                updateCounter = ServerContext.getEtcdClient().cas(globalCounterKey, rawValue, String.valueOf(newWorkerId));
            }

            if (updateCounter) {
                // 将分配的workerId与zoneId绑定
                boolean success = ServerContext.getEtcdClient().setNX(etcdWorkerIdKey, String.valueOf(newWorkerId));
                if (success) {
                    LOGGER.info("Allocated new workerId: {} for zoneId: {}", newWorkerId, zoneId);
                    return newWorkerId;
                }
            }

            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Interrupted while allocating workerId", e);
            }
        }

        throw new RuntimeException("Failed to allocate workerId after multiple attempts");
    }
}
package com.yorha.common.actorservice;

import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.msg.IActorMsg;
import com.yorha.common.concurrent.FiberAsync;

/**
 * <AUTHOR>
 */
public class ActorCallContext extends FiberAsync<Object, Exception> {

    private final AbstractActor actor;
    private final IActorRef target;
    private final ActorMsgEnvelope actorMsgEnvelope;

    public ActorCallContext(AbstractActor actor, IActorRef targetActorRef, ActorMsgEnvelope envelope) {
        this.actor = actor;
        this.target = targetActorRef;
        this.actorMsgEnvelope = envelope;
    }

    public long getMsgSeqId() {
        return actorMsgEnvelope.getMsgSeqId();
    }

    @Override
    protected void requestAsync() {
        actor.realSend(target, actorMsgEnvelope);
    }

    public void tryCompleteWithException(Exception exception) {
        asyncFailed(exception);
    }

    public void tryCompleteWithMsg(IActorMsg msg) {
        asyncCompleted(new ActorMsgWithTimeStamp(msg));
    }

    @Override
    public String toString() {
        return "ActorCallContext{" +
                "sender=" + this.getSender() +
                ", receiver=" + this.target +
                ", envelop=" + this.actorMsgEnvelope +
                '}';
    }

    public IActorRef getSender() {
        return this.actor.self();
    }
}

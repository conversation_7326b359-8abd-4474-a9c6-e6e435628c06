package com.yorha.common.utils.ip;

import com.github.jarod.qqwry.IPZone;
import com.github.jarod.qqwry.QQWry;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.exception.GeminiException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashSet;
import java.util.Set;

/**
 * IP归属地查询
 *
 * <AUTHOR>
 * 2022年02月07日 15:58:00
 */
public class IpSeekerUtils {
    private static final Logger LOGGER = LogManager.getLogger(IpSeekerUtils.class);
    private static final String[] AREAS = {
            "北京", "中国", "天津", "河北", "山西", "内蒙古", "辽宁", "吉林",
            "黑龙江", "上海", "江苏", "浙江", "安徽", "福建", "江西", "山东",
            "河南", "湖北", "湖南", "广东", "广西", "海南", "重庆", "四川",
            "贵州", "云南", "西藏", "陕西", "甘肃", "青海", "宁夏", "新疆"};

    private static QQWry qqwry;

    public static void init() {
        if (qqwry == null) {
            try {
                qqwry = new QQWry();
            } catch (IOException e) {
                LOGGER.error("qqwry init fail.", e);
            }
        } else {
            throw new GeminiException("repeat init IpSeekerUtils");
        }
    }

    public static IPZone getIpZone(String ip) {
        String dbVer = qqwry.getDatabaseVersion();
        IPZone ipzone = qqwry.findIP(ip);
        LOGGER.debug("qqwry version:{}--------- IP：{} ; area is：{}，({})", dbVer, ip, ipzone.getMainInfo(), ipzone.getSubInfo());
        return ipzone;
    }

    public static boolean isChina(IPZone ipZone) {
        if (ipZone == null) {
            return false;
        }
        for (String area : AREAS) {
            if (ipZone.getMainInfo().contains(area)) {
                return true;
            }
        }
        return false;
    }

    public static void main(String[] args) {
        IpSeekerUtils.init();
        // 非洲
        test("*************");
        // 中国-香港
        test("************");
        // 中国-北京
        test("************");
        // 美国
        test("************");
        // 中国-河南
        test("************");
        // 中国-上海
        test("*************");
        // 本机ip
        test("127.0.0.1");
        // 内网ip
        test("***********");
        // vs6MatchDaLogIpToCountry("F:\\works\\cnc_server\\tools\\utils\\ip.data", "F:\\works\\cnc_server\\tools\\utils\\country.csv");
    }

    public static void test(String ip) {
        IPZone ipZone = IpSeekerUtils.getIpZone(ip);
        System.out.println(ipZone);
        System.out.println(IpSeekerUtils.isChina(ipZone));
    }

    public static void vs6MatchDaLogIpToCountry(final String csvPath, final String targetCsvPath) {
        final String DELIMITER = ",";
        final Set<String> ipSet = new HashSet<>();
        try (BufferedReader br = Files.newBufferedReader(Paths.get(csvPath)); BufferedWriter out = new BufferedWriter(new FileWriter(targetCsvPath))) {
            // 按行读取
            String line;
            out.write("ClientIp,Country");
            out.newLine();
            while ((line = br.readLine()) != null) {
                line = line.trim();
                if (StringUtils.isEmpty(line)) {
                    continue;
                }
                final String[] columns = line.split(DELIMITER);
                final String ip = columns[0].trim();
                if (ipSet.contains(ip)) {
                    continue;
                }
                final IPZone zone = IpSeekerUtils.getIpZone(ip);
                final String mainInfo = zone.getMainInfo();
                ipSet.add(ip);
                out.write(ip + "," + mainInfo);
                out.newLine();
            }
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }
}
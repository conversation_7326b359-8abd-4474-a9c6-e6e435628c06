package com.yorha.common.actor.mailbox;

import com.yorha.common.actor.dispatcher.IMailboxDispatcher;
import com.yorha.common.actor.mailbox.middleware.IMailboxMiddleware;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actor.IActorRef;

import java.util.function.Function;

/**
 * 邮箱依赖的邮箱元数据。
 *
 * <AUTHOR>
 */
public interface IMailboxMetaData {
    /**
     * 返回Actor构造工厂。
     *
     * @return actor的工厂。
     */
    Function<IActorRef, AbstractActor> getActorFactory();

    /**
     * 返回邮箱中间件。
     *
     * @return 邮箱中间件。
     */
    IMailboxMiddleware getMailboxMiddleware();

    /**
     * 返回邮箱调度器。
     *
     * @return 邮箱调度器。
     */
    IMailboxDispatcher getDispatcher();

    /**
     * 邮箱的队列的长度，投递超过邮箱队列长度的消息将被丢失。
     *
     * @return == 0代表不限长度的邮箱; > 0 则表示邮箱的消息队列长度。
     */
    int getMailboxQueueSize();

    /**
     * 所属ActorSystem。
     *
     * @return actor system。
     */
    ActorSystem getActorSystem();
}

package com.yorha.common.utils;

import com.yorha.common.constant.Constants;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.Core;

/**
 * errorcode工具类
 *
 * <AUTHOR>
 */
public final class ErrorCodeUtils {

    /**
     * 通过异常获取错误码对象
     */
    public static Core.Code getCodeFromException(Exception e) {
        final Core.Code retCode;
        if (e instanceof GeminiException) {
            // 内部异常
            retCode = buildCode(((GeminiException) e).getCodeId(), e.getMessage());
        } else {
            // 拿缓存的Code对象，避免重复new
            retCode = ErrorCode.FAILED.getCode();
        }
        return retCode;
    }

    /**
     * 工厂方法，通过codeId和消息构造返回的错误码。
     *
     * @param codeId 错误码id。
     * @param msg    错误消息。
     * @return 错误码。
     */
    public static Core.Code buildCode(final int codeId, final String msg) {
        final Core.Code retCode;
        if (StringUtils.isEmpty(msg)) {
            // 拿缓存的Code对象，避免重复new
            retCode = ErrorCode.getCode(codeId);
        } else {
            Core.Code.Builder builder = Core.Code.newBuilder();
            builder.setId(codeId).addParam(msg);
            retCode = builder.build();
        }
        return retCode;
    }

    public static String getErrorCodeName(GeminiException e) {
        return ErrorCode.getCodeName(e.getCodeId());
    }

    /**
     * 错误码入参工厂方法，构建封禁相关参数。
     * （例如：角色封禁参数，账号封禁）
     *
     * @param reasonId   封禁原因。
     * @param banEndTsMs 封禁结束时间。
     * @return 封禁参数。
     */
    public static String buildBanParam(final int reasonId, final long banEndTsMs) {
        return reasonId + Constants.VERTICAL_BAR_SPLIT_FLAG + banEndTsMs;
    }

    /**
     * 错误码入参工厂方法，构建封禁相关参数。
     * （例如：角色封禁参数，账号封禁）
     *
     * @param reason     封禁原因。
     * @param banEndTsMs 封禁结束时间。
     * @return 封禁参数。
     */
    public static String buildBanParam(final String reason, final long banEndTsMs) {
        return reason + Constants.VERTICAL_BAR_SPLIT_FLAG + banEndTsMs;
    }
}

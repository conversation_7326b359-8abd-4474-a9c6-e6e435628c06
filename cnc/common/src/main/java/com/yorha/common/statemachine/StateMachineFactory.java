package com.yorha.common.statemachine;

import org.apache.commons.collections4.MapUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public class StateMachineFactory<STATE, EVENT, CONTEXT> {

    private final Map<STATE, Map<EVENT, Transition<STATE, EVENT, CONTEXT>>> stateMachineTable;
    private final Map<STATE, Action<CONTEXT>> actionTable;

    private final STATE initState;

    public StateMachineFactory(STATE initState) {
        this.initState = initState;
        this.stateMachineTable = new HashMap<>();
        this.stateMachineTable.put(initState, new HashMap<>());
        this.actionTable = new HashMap<>();
    }

    public void addTransition(Transition<STATE, EVENT, CONTEXT> transition) {
        Transition<STATE, EVENT, CONTEXT> old = stateMachineTable.computeIfAbsent(transition.sourceState(), k -> new HashMap<>())
                .put(transition.getEvent(), transition);
        if (old != null) {
            throw new IllegalArgumentException("StateMachine cant put same transition " + transition.getEvent() + ":" + transition.sourceState() + "->" + transition.targetState());
        }

        if (!actionTable.containsKey(transition.targetState())) {
            actionTable.put(transition.targetState(), transition.getAction());
        }
    }

    public StateMachine<STATE, EVENT, CONTEXT> newInstance() {
        return new DefaultStateMachine(this, initState);
    }

    private class DefaultStateMachine implements StateMachine<STATE, EVENT, CONTEXT> {

        private STATE currentState;
        private Action<CONTEXT> currentAction;
        DefaultStateMachine(StateMachineFactory<STATE, EVENT, CONTEXT> factory, STATE initState) {
            this.currentState = initState;
        }

        @Override
        public STATE currentState() {
            return currentState;
        }

        @Override
        public boolean transitIsSatisfied(STATE preState, EVENT event, CONTEXT context) {
            Map<EVENT, Transition<STATE, EVENT, CONTEXT>> transitionMap = stateMachineTable.get(preState);
            if (MapUtils.isEmpty(transitionMap)) {
                return false;
            }
            Transition<STATE, EVENT, CONTEXT> transition = transitionMap.get(event);
            if (transition == null) {
                return false;
            }
            return transition.getAction().isSatisfied(context);
        }

        @Override
        public STATE transit(STATE preState, EVENT event, CONTEXT context) {
            Map<EVENT, Transition<STATE, EVENT, CONTEXT>> transitionMap = stateMachineTable.get(preState);
            if (MapUtils.isNotEmpty(transitionMap)) {
                Transition<STATE, EVENT, CONTEXT> transition = transitionMap.get(event);
                if (transition != null) {
                    this.currentState = transition.targetState();
                    this.currentAction = transition.getAction();
                    transition.getAction().onEnter(context);
                }
            }

            return currentState;
        }

        @Override
        public Optional<Action<CONTEXT>> getAction(STATE state) {
            if (state == currentState) {
                if (currentAction != null) {
                    return Optional.of(currentAction);
                }
            }
            return Optional.ofNullable(actionTable.get(state));
        }
    }
}

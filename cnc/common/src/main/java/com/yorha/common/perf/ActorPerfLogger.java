package com.yorha.common.perf;

import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.TimeUnitUtils;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.utils.TextTable;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class ActorPerfLogger extends AbstractPerfLogger {
    private final Map<String, Map<String, ActorMsgRecord>> actorMsgRecordMap = new HashMap<>();

    public ActorPerfLogger() {
        super("actor-perf");
    }

    private static class ActorMsgRecord {
        public long offerCount = 0;
        public long lastQpsTsMs = 0;
        public int curSecQps = 0;
        public int maxSecQps = 0;
        public long startNum = 0;
        public long totalWait = 0;
        public long maxWait = 0;
        public long doneNum = 0;
        public long totalCost = 0;
        public long maxCost = 0;

        public void onOffer() {
            offerCount++;
            if (lastQpsTsMs == 0 && curSecQps == 0) {
                // 第一次触发
                this.curSecQps++;
                this.maxSecQps = this.curSecQps;
                this.lastQpsTsMs = SystemClock.nowNative();
                return;
            }
            if (SystemClock.nowNative() - this.lastQpsTsMs > TimeUnit.SECONDS.toMillis(1)) {
                this.lastQpsTsMs = SystemClock.nowNative();
                this.maxSecQps = Math.max(this.curSecQps, this.maxSecQps);
                this.curSecQps = 0;
            }
            this.curSecQps++;
        }

        public void onStart(long wait) {
            startNum++;
            totalWait += wait;
            maxWait = Math.max(maxWait, wait);
        }

        public void onDone(long cost) {
            doneNum++;
            totalCost += cost;
            maxCost = Math.max(maxCost, cost);
        }
    }

    public void logOfferActorMsg(String actorRole, String msgName) {
        run(() -> {
            Map<String, ActorMsgRecord> map = actorMsgRecordMap.computeIfAbsent(actorRole, k -> new HashMap<>());
            ActorMsgRecord record = map.computeIfAbsent(msgName, k -> new ActorMsgRecord());
            record.onOffer();
        });
    }

    public void logStartMsg(String actorRole, String msgName, long waitTimeNs) {
        run(() -> {
            Map<String, ActorMsgRecord> map = actorMsgRecordMap.computeIfAbsent(actorRole, k -> new HashMap<>());
            ActorMsgRecord record = map.computeIfAbsent(msgName, k -> new ActorMsgRecord());
            record.onStart(waitTimeNs);
        });
    }

    public void logMsgDone(String actorRole, String msgName, long costNs) {
        run(() -> {
            Map<String, ActorMsgRecord> map = actorMsgRecordMap.computeIfAbsent(actorRole, k -> new HashMap<>());
            ActorMsgRecord record = map.computeIfAbsent(msgName, k -> new ActorMsgRecord());
            record.onDone(costNs);
        });
    }

    private void showActorMsg() {
        MonitorUnit.ACTOR_MSG_MAX_QPS.clear();
        MonitorUnit.ACTOR_MSG_AVG_COST.clear();

        for (Map.Entry<String, Map<String, ActorMsgRecord>> entry : actorMsgRecordMap.entrySet()) {
            TextTable textTable = new TextTable("Actor-Task-" + entry.getKey());
            textTable.addColumn("msgName", "offerCount", "maxQps", "startCount", "avgWait", "maxWait", "doneCount", "avgCost", "maxCost");
            Map<String, ActorMsgRecord> map = entry.getValue();
            int size = map.size();
            if (size > 300) {
                WechatLog.error("ActorPerfLogger maybe memory leak {} {}", entry.getKey(), size);
            }
            for (Map.Entry<String, ActorMsgRecord> e : map.entrySet()) {
                ActorMsgRecord value = e.getValue();
                long avgWait = -1;
                if (value.startNum != 0) {
                    avgWait = value.totalWait / value.startNum;
                }
                long maxWait = value.maxWait;
                long avgCost = -1;
                if (value.doneNum != 0) {
                    avgCost = value.totalCost / value.doneNum;
                }
                long maxCost = value.maxCost;
                textTable.addRow().add(
                        e.getKey(),
                        value.offerCount,
                        value.maxSecQps,
                        value.startNum,
                        TimeUnitUtils.getStringFromNs(avgWait),
                        TimeUnitUtils.getStringFromNs(maxWait),
                        value.doneNum,
                        TimeUnitUtils.getStringFromNs(avgCost),
                        TimeUnitUtils.getStringFromNs(maxCost)
                );
                MonitorUnit.ACTOR_MSG_MAX_QPS.labels(ServerContext.getBusId(), entry.getKey(), e.getKey()).set(value.maxSecQps);
                MonitorUnit.ACTOR_MSG_AVG_COST.labels(ServerContext.getBusId(), entry.getKey(), e.getKey()).set(avgCost);
            }
            textTable.info(getLoggerPerf(), false);
        }

        actorMsgRecordMap.clear();
    }

    public void flush() {
        run(this::showActorMsg);
    }

}

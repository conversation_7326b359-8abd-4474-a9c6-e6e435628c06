package com.yorha.common.utils.time;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.concurrent.NotThreadSafe;
import java.util.HashMap;
import java.util.concurrent.ConcurrentLinkedDeque;

/**
 * 计时器 线程不安全
 *
 */
@NotThreadSafe
public class GeminiStopWatch {
    private static final Logger LOGGER = LogManager.getLogger(GeminiStopWatch.class);

    long startTime = System.currentTimeMillis();
    private final Object name;
    private final ConcurrentLinkedDeque<MarkInfo> markInfoDeque = new ConcurrentLinkedDeque<>();

    static class MarkInfo {
        final long time = System.currentTimeMillis();
        private final String name;

        MarkInfo(String name) {
            this.name = name;
        }
    }

    public GeminiStopWatch(Object name) {
        this.name = name;
    }

    public long getTotalCost() {
        if (markInfoDeque.isEmpty()) {
            return 0;
        }
        return markInfoDeque.getLast().time - startTime;
    }

    /**
     * 重置计时器
     */
    public void reset() {
        startTime = System.currentTimeMillis();
        markInfoDeque.clear();
    }

    public void mark(String name) {
        markInfoDeque.offer(new MarkInfo(name));
    }

    /**
     * 只打印消耗大于1ms的统计项
     */
    public String stat() {
        try {
            if (markInfoDeque.isEmpty()) {
                return "";
            }
            StringBuilder result = new StringBuilder();
            result.append("\nGeminiStopWatch ").append(name).append(" ");
            double totalCost = markInfoDeque.getLast().time - startTime;
            result.append("total: ").append(totalCost).append(" ms, ");
            long lastTime = startTime;
            HashMap<String, Double> keyMap = new HashMap<>();

            MarkInfo markInfo = markInfoDeque.poll();
            double currentCost = 0;
            while (markInfo != null) {
                currentCost = markInfo.time - lastTime;
                Double cost = keyMap.get(markInfo.name);
                if (currentCost >= 1) {
                    if (cost == null || cost < currentCost) {
                        keyMap.put(markInfo.name, currentCost);
                        result.append(markInfo.name).append(": ").append(currentCost).append(" ms").append(", ");
                    }
                }
                lastTime = markInfo.time;
                markInfo = markInfoDeque.poll();
            }
            return result.toString();
        } finally {
            reset();
        }
    }

    public static void main(String[] args) throws InterruptedException {

        GeminiStopWatch a = new GeminiStopWatch("aaa");
        Thread.sleep(1000);
        a.mark("stage-1");
        Thread.sleep(200);
        a.mark("stage-2");
        Thread.sleep(400);
        a.mark("stage-3");
        LOGGER.error("qqqqq, {}", a.stat());
    }
}

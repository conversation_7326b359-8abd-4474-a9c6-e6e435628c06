package com.yorha.common.resource.resservice.clan;

import com.google.common.collect.Lists;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.constant.ConstClanKVResService;
import com.yorha.common.exception.ResourceException;
import com.yorha.gemini.utils.StringUtils;
import res.template.*;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
public class ClanDataTemplateService extends AbstractResService {
    private static final int NOT_SET_STAFF = 0;

    /**
     * {Staff Id: StaffHolder} 职位id：职位相关数据
     */
    private final Map<Integer, StaffHolder> staffRoles = new HashMap<>();
    /**
     * {Role Id: RoleHolder} 角色id：角色相关数据
     */
    private final Map<Integer, RoleHolder> roles = new HashMap<>();
    /**
     * staff id 中等阶级staff id集合
     */
    private final Set<Integer> middleStaffIdSet = new HashSet<>();
    /**
     * staff id 特权阶级staff id集合
     */
    private final Set<Integer> highStaffIdSet = new HashSet<>();
    /**
     * 去除职位后新获取的职位表
     */
    private int staffIdAfterRemovingStaff = NOT_SET_STAFF;
    /**
     * 联盟拥有者的staff id
     */
    private int clanOwnerStaff = NOT_SET_STAFF;
    /**
     * 新入联盟的官职
     */
    private int clanNewbieStaff = NOT_SET_STAFF;
    /**
     * 未持有buff的联盟高官staff id
     */
    private int clanWithoutBuffHighStaff = NOT_SET_STAFF;
    /**
     * 未持有buff的联盟高官staff 最大数量
     */
    private int clanWithoutBuffHighStaffMaxNum = NOT_SET_STAFF;

    private final Map<Integer, Integer> clanPlayerResourceLimit = new HashMap<>();

    public ClanDataTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        this.staffRoles.putAll(this.parseClanOfficeTable(getResHolder()));
        this.roles.putAll(this.parseClanRole2Operation(getResHolder()));
        this.highStaffIdSet.addAll(this.parseHighStaffIdSet(getResHolder()));
        this.middleStaffIdSet.addAll(this.parseMiddleStaffIdSet(getResHolder()));
        this.clanNewbieStaff = this.parseClanNewbieStaff(getResHolder());
        this.clanOwnerStaff = this.parseClanOwnerStaff(getResHolder());
        this.staffIdAfterRemovingStaff = this.parseStaffIdAfterRemovingStaff(getResHolder());
        // NOTE(furson): 这个parse跟上面的有依赖关系
        this.clanWithoutBuffHighStaff = this.parseWithoutSpecialBuffMiddleStaff();
        this.clanWithoutBuffHighStaffMaxNum = this.parseWithoutSpecialBuffMiddleStaffMaxNum(getResHolder());
        getResHolder().getConstTemplate(ConstClanTerritoryTemplate.class).getUpLimitOfMemberResource().forEach(d -> {
            clanPlayerResourceLimit.put(d.getKey(), d.getValue());
        });
    }

    @Override
    public void checkValid() throws ResourceException {
        this.checkClanStaff();
        this.checkOthers();
        checkClanPowerReward();
    }

    /**
     * 检查联盟简称合法性
     *
     * @param sName 联盟简称
     * @return 是 or 否
     */
    public boolean checkClanSimpleNameLegal(String sName) {
        ConstClanTemplate bean = getResHolder().getConstTemplate(ConstClanTemplate.class);
        // 只支持英文字母数字
        return StringUtils.isAlphanumericAndSpecialSymbols(sName) && StringUtils.checkLength(sName, bean.getClanSimpleNameMaxLen(), bean.getClanSimpleNameMinLen());
    }

    public boolean checkSearchClanSimpleNameLegal(String sName) {
        ConstClanTemplate bean = getResHolder().getConstTemplate(ConstClanTemplate.class);
        // 只支持英文字母数字
        return StringUtils.isAlphanumericAndSpecialSymbols(sName) && StringUtils.checkLength(sName, bean.getClanSimpleNameMaxLen(), 0);
    }

    /**
     * 检查联盟全称合法性
     *
     * @param name 全称
     * @return 是 or 否
     */
    public boolean checkClanNameLegal(String name) {
        ConstClanTemplate bean = getResHolder().getConstTemplate(ConstClanTemplate.class);
        return StringUtils.checkLength(name, bean.getClanNameMaxLen(), bean.getClanNameMinLen());
    }

    /**
     * 检查联盟描述合法性
     *
     * @param describe 联盟描述
     * @return 是 or 否
     */
    public boolean checkClanDescribe(String describe) {
        ConstClanTemplate bean = getResHolder().getConstTemplate(ConstClanTemplate.class);
        return StringUtils.checkLengthWithoutHtml(describe, bean.getClanDescribeMaxLen(), 0);
    }

    /**
     * 角色是否有权限更改其他角色，并设置其新角色。1. 盟主随便设置 2. 联盟R4及以上官员对联盟R3，R2，R1使用，最多调整到R3
     *
     * @param callerStaff     主动发起方
     * @param calleeFromStaff 被更改方
     * @param calleeToStaff   被更改方的目标角色
     * @return 是 or 否
     */
    public boolean canStaffAdjustOtherStaff(int callerStaff, int calleeFromStaff, int calleeToStaff) {
        if (callerStaff == this.clanOwnerStaff) {
            return this.staffRoles.containsKey(calleeFromStaff) && this.staffRoles.containsKey(calleeToStaff);
        }
        StaffHolder callerHolder = this.staffRoles.get(callerStaff);
        StaffHolder calleeFromHolder = this.staffRoles.get(calleeFromStaff);
        StaffHolder calleeToHolder = this.staffRoles.get(calleeToStaff);
        if (callerHolder == null || calleeFromHolder == null || calleeToHolder == null) {
            return false;
        }
        if (!ClanPrivilegeRole.LEVEL_ROLE_HIGH_PRIVILEGE.contains(callerHolder.getRole())) {
            return false;
        }
        return ClanPrivilegeRole.LEVEL_ROLE_LOW_PRIVILEGE.contains(calleeFromHolder.getRole()) &&
                ClanPrivilegeRole.LEVEL_ROLE_LOW_PRIVILEGE.contains(calleeToHolder.getRole());
    }

    /**
     * 职位是否能踢掉其他角色：R4以上对比其等级低的对象
     *
     * @param callerStaff 主导方
     * @param calleeStaff 被踢出联盟的角色
     * @return 是 or 否
     */
    public boolean canStaffKickOffOtherStaff(int callerStaff, int calleeStaff) {
        StaffHolder callerStaffHolder = this.staffRoles.get(callerStaff);
        StaffHolder calleeStaffHolder = this.staffRoles.get(calleeStaff);
        if (callerStaffHolder == null || calleeStaffHolder == null) {
            return false;
        }
        if (!ClanPrivilegeRole.LEVEL_ROLE_HIGH_PRIVILEGE.contains(callerStaffHolder.getRole())) {
            return false;
        }
        return callerStaffHolder.getRole() > calleeStaffHolder.getRole();
    }

    /**
     * 获取新玩家的职位
     *
     * @return 新玩家的职位
     */
    public int getClanNewbieStaff() {
        return this.clanNewbieStaff;
    }

    public int getNotSetStaff() {
        return NOT_SET_STAFF;
    }

    /**
     * 获得去除职位后的职位id
     *
     * @return 职位id
     */
    public int getStaffIdAfterRemovingStaff() {
        return this.staffIdAfterRemovingStaff;
    }

    /**
     * 获得没有特殊buff的高官职位id
     *
     * @return 职位id
     */
    public int getClanWithoutBuffHighStaff() {
        return this.clanWithoutBuffHighStaff;
    }

    /**
     * 获得没有特殊buff的高官最大数目
     *
     * @return 职位id
     */
    public int getClanWithoutBuffHighStaffMaxNum() {
        return this.clanWithoutBuffHighStaffMaxNum;
    }

    /**
     * 获取联盟中所有角色的id。
     *
     * @return 返回联盟中所有角色id
     */
    public Set<Integer> getRoleIds() {
        return this.roles.keySet();
    }

    /**
     * 获取联盟所有职位的id列表
     *
     * @return 职位id集合
     */
    public Set<Integer> getStaffIds() {
        return this.staffRoles.keySet();
    }

    /**
     * 获取联盟中中等职位的id列表
     *
     * @return 中等职位id集合
     */
    public Set<Integer> getMiddleStaffIdSet() {
        return this.middleStaffIdSet;
    }

    /**
     * 判断是否staff特殊
     *
     * @param staffId 职位id
     * @return yes or not
     */
    public boolean isHighStaff(final Integer staffId) {
        return this.highStaffIdSet.contains(staffId);
    }

    /**
     * 判断官职1是否权限上大于官职2
     *
     * @param staff1Id 官职1
     * @param staff2Id 官职2
     * @return 0 相等， 1 大于， -1小于
     */
    public int compareStaff(int staff1Id, int staff2Id) {
        StaffHolder staffHolder1 = this.staffRoles.get(staff1Id);
        StaffHolder staffHolder2 = this.staffRoles.get(staff2Id);
        if (staffHolder1 == null || staffHolder2 == null) {
            return -1;
        }
        int staffHolderLevel1 = staffHolder1.getRole();
        int staffHolderLevel2 = staffHolder2.getRole();
        return Integer.compare(staffHolderLevel1, staffHolderLevel2);
    }

    /**
     * 获取盟主的staff
     *
     * @return 盟主的staff id
     */
    public int getOwnerStaffId() {
        return this.clanOwnerStaff;
    }

    /**
     * 获取角色数据
     * tips：获取角色相关数据，存在数据拷贝
     *
     * @param roleId 角色id
     * @return 角色相关数据不存在时，返回null；职位存在，返回职位的snapshot
     */
    public RoleHolder getRoleHolder(int roleId) {
        if (this.roles.containsKey(roleId)) {
            return this.roles.get(roleId);
        }
        return null;
    }

    /**
     * 获取职位的相关数据
     * tips：获取的是当前的镜像数据，存在数据拷贝
     *
     * @param staffId 职位id
     * @return 职位不存在，返回null；职位存在，返回职位的snapshot。
     */
    public StaffHolder getStaffHolder(int staffId) {
        if (this.staffRoles.containsKey(staffId)) {
            return this.staffRoles.get(staffId);
        }
        return null;
    }

    /**
     * 解析资源中的Office相关数据
     *
     * @param resHolder 资源句柄
     * @return 资源map
     */
    public Map<Integer, StaffHolder> parseClanOfficeTable(ResHolder resHolder) {
        List<ClanOfficeTemplate> officeTemplateList = Lists.newArrayList(resHolder.getListFromMap(ClanOfficeTemplate.class));
        Map<Integer, StaffHolder> staffHolderHashMap = new HashMap<>(officeTemplateList.size());
        for (ClanOfficeTemplate officeTemplate : officeTemplateList) {
            int role = officeTemplate.getStaffLevel();
            int cnt = officeTemplate.getStaffLimit();
            if (cnt == 0) {
                cnt = Integer.MAX_VALUE;
            }
            List<Integer> buffList = new ArrayList<>(officeTemplate.getStaffBuffListList());
            StaffHolder staffHolder = new StaffHolder(role, cnt, buffList);
            staffHolderHashMap.put(officeTemplate.getId(), staffHolder);
        }
        return staffHolderHashMap;
    }

    /**
     * 解析中权限职业
     *
     * @param resHolder 资源句柄
     * @return 官职列表
     */
    public Set<Integer> parseMiddleStaffIdSet(ResHolder resHolder) {
        List<ClanOfficeTemplate> officeTemplateList = Lists.newArrayList(resHolder.getListFromMap(ClanOfficeTemplate.class));
        Set<Integer> middleStaffIdSet = new HashSet<>();
        for (ClanOfficeTemplate officeTemplate : officeTemplateList) {
            if (ClanPrivilegeRole.isMiddleRole(officeTemplate.getStaffLevel())) {
                middleStaffIdSet.add(officeTemplate.getId());
            }
        }
        return middleStaffIdSet;
    }

    /**
     * 解析高权限职业
     *
     * @param resHolder 资源句柄
     * @return 官职列表
     */
    public Set<Integer> parseHighStaffIdSet(ResHolder resHolder) {
        List<ClanOfficeTemplate> officeTemplateList = Lists.newArrayList(resHolder.getListFromMap(ClanOfficeTemplate.class));
        Set<Integer> highStaffIdSet = new HashSet<>();
        for (ClanOfficeTemplate officeTemplate : officeTemplateList) {
            // 设定特殊角色
            if (ClanPrivilegeRole.isHighRole(officeTemplate.getStaffLevel())) {
                highStaffIdSet.add(officeTemplate.getId());
            }
        }
        return highStaffIdSet;
    }

    /**
     * 解析玩家解除职位后的岗位id
     *
     * @param resHolder 资源句柄
     * @return 岗位id
     */
    public int parseStaffIdAfterRemovingStaff(ResHolder resHolder) throws ResourceException {
        List<ClanOfficeTemplate> officeTemplateList = Lists.newArrayList(resHolder.getListFromMap(ClanOfficeTemplate.class));
        for (ClanOfficeTemplate officeTemplate : officeTemplateList) {
            if (officeTemplate.getStaffLevel() == ClanPrivilegeRole.CLAN_LOW_PRIVILEGE_ONE_ROLE.getLevel()) {
                return officeTemplate.getId();
            }
        }
        throw new ResourceException("未配置解除职位后的玩家岗位");
    }

    /**
     * 解析没有特殊BUFF的高官岗位id
     *
     * @return 岗位idp
     */
    public int parseWithoutSpecialBuffMiddleStaff() throws ResourceException {
        ConstClanTemplate template = getResHolder().getConstTemplate(ConstClanTemplate.class);
        for (int highStaffId : middleStaffIdSet) {
            if (!template.getSpecialStaffIDs().contains(highStaffId)) {
                return highStaffId;
            }
        }
        throw new ResourceException("未配置没有buff的高职位岗位");
    }

    /**
     * 解析没有特殊BUFF的高官最大的数目
     *
     * @param resHolder 资源句柄
     * @return 岗位idp
     */
    public int parseWithoutSpecialBuffMiddleStaffMaxNum(ResHolder resHolder) {
        return resHolder.getValueFromMap(ClanOfficialLevelTemplate.class, ClanPrivilegeRole.CLAN_HIGH_PRIVILEGE_ROLE.getLevel()).getStaffLimit();
    }

    /**
     * 解析联盟拥有者岗位
     *
     * @param resHolder 资源句柄
     * @return 联盟所有者官职id
     */
    public int parseClanOwnerStaff(ResHolder resHolder) throws ResourceException {
        List<ClanOfficeTemplate> officeTemplateList = Lists.newArrayList(resHolder.getListFromMap(ClanOfficeTemplate.class));
        for (ClanOfficeTemplate officeTemplate : officeTemplateList) {
            if (ClanPrivilegeRole.isClanOwner(officeTemplate.getStaffLevel())) {
                return officeTemplate.getId();
            }
        }
        throw new ResourceException("未配置联盟所有者岗位");
    }

    /**
     * 解析萌新岗位
     *
     * @param resHolder 资源句柄
     * @return 萌新岗位staff id
     */
    public int parseClanNewbieStaff(ResHolder resHolder) throws ResourceException {
        List<ClanOfficeTemplate> officeTemplateList = Lists.newArrayList(resHolder.getListFromMap(ClanOfficeTemplate.class));
        for (ClanOfficeTemplate officeTemplate : officeTemplateList) {
            if (ClanPrivilegeRole.CLAN_LOW_PRIVILEGE_ONE_ROLE.getLevel() == officeTemplate.getStaffLevel()) {
                return officeTemplate.getId();
            }
        }
        throw new ResourceException("未配置萌新岗位");
    }

    /**
     * 解析资源中的authority相关数据
     *
     * @param resHolder 资源句柄
     * @return 权限holder
     */
    public Map<Integer, OperationHolder> parseClanAuthorityTable(ResHolder resHolder) {
        List<ClanAuthorityTemplate> authorityTemplateList = Lists.newArrayList(resHolder.getListFromMap(ClanAuthorityTemplate.class));
        Map<Integer, OperationHolder> operationHolderMap = new HashMap<>(authorityTemplateList.size());
        for (ClanAuthorityTemplate authorityTemplate : authorityTemplateList) {
            final Set<Integer> maxAllowOperationRoles = new HashSet<>(authorityTemplate.getMaxAllowLevelListList());
            final Set<Integer> minAllowOperationRoles = new HashSet<>(authorityTemplate.getMinAllowLevelListList());
            final Set<Integer> defaultAllowOperationRoles = new HashSet<>(authorityTemplate.getDefaultAllowLevelList());
            OperationHolder holder = new OperationHolder(maxAllowOperationRoles, minAllowOperationRoles, defaultAllowOperationRoles);
            operationHolderMap.put(authorityTemplate.getId(), holder);
        }
        return operationHolderMap;
    }

    /**
     * 解析资源中的角色对应的操作
     *
     * @param resHolder 资源句柄
     * @return 角色权限holder
     */
    public Map<Integer, RoleHolder> parseClanRole2Operation(ResHolder resHolder) {
        List<ClanAuthorityTemplate> authorityTemplateList = Lists.newArrayList(resHolder.getListFromMap(ClanAuthorityTemplate.class));
        final Map<Integer, RoleHolder.Builder> role2OperationMap = new HashMap<>(authorityTemplateList.size());
        for (ClanAuthorityTemplate authorityTemplate : authorityTemplateList) {
            final Integer operationId = authorityTemplate.getId();
            final Set<Integer> maxAllowOperationRoles = new HashSet<>(authorityTemplate.getMaxAllowLevelListList());
            final Set<Integer> minAllowOperationRoles = new HashSet<>(authorityTemplate.getMinAllowLevelListList());
            final Set<Integer> defaultAllowOperationRoles = new HashSet<>(authorityTemplate.getDefaultAllowLevelList());
            Stream.of(maxAllowOperationRoles, minAllowOperationRoles, defaultAllowOperationRoles)
                    .flatMap(Collection::stream)
                    .distinct()
                    .forEach(role -> {
                        if (!role2OperationMap.containsKey(role)) {
                            role2OperationMap.put(role, new RoleHolder.Builder());
                        }
                    });
            defaultAllowOperationRoles.forEach(roleId -> role2OperationMap.get(roleId).addDefaultOperations(operationId));
            minAllowOperationRoles.forEach(roleId -> {
                role2OperationMap.get(roleId).addMinOperations(operationId);
            });
            maxAllowOperationRoles.forEach(roleId -> role2OperationMap.get(roleId).addMaxOperations(operationId));
        }
        return role2OperationMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().build()));
    }

    /**
     * 检查联盟官员表
     */
    private void checkClanStaff() throws ResourceException {
        for (Map.Entry<Integer, StaffHolder> entry : this.staffRoles.entrySet()) {
            int staffId = entry.getKey();
            StaffHolder staffRole = entry.getValue();
            int role = staffRole.getRole();
            if (!this.roles.containsKey(role)) {
                throw new ResourceException(StringUtils.format("官员配置了不合理的角色，官员id: {}, 官员: {}", staffId, staffRole));
            }
            if (ClanPrivilegeRole.isClanRole(role) && !ClanPrivilegeRole.isCntSuitable(role, staffRole.getMaxCnt())) {
                throw new ResourceException(StringUtils.format("官员的数量和角色数量不匹配，官员id: {}, 官员: {}", staffId, staffRole));
            }
            if (staffId == NOT_SET_STAFF) {
                throw new ResourceException("官员id：0为保留职位，不能使用");
            }
        }
    }

    private void checkOthers() throws ResourceException {
        if (!this.staffRoles.keySet().containsAll(this.highStaffIdSet)) {
            throw new ResourceException(StringUtils.format("高级别官员列表存在错误id。staffId: {}, highStaffId: {}", this.staffRoles.keySet(), this.highStaffIdSet));
        }
        if (this.clanOwnerStaff == NOT_SET_STAFF) {
            throw new ResourceException("未配置联盟拥有者的官职id");
        }
        if (this.clanNewbieStaff == NOT_SET_STAFF) {
            throw new ResourceException("未配置新人的官职id");
        }
        if (this.staffIdAfterRemovingStaff == NOT_SET_STAFF) {
            throw new ResourceException("未配置失去官职后的官职id");
        }
    }

    public int getClanPowerRewardId(long power) {
        Map<Integer, ClanPowerRewardTemplate> map = getResHolder().getMap(ClanPowerRewardTemplate.class);
        if (power < 0) {
            return 0;
        }
        for (ClanPowerRewardTemplate template : map.values()) {
            if (power <= template.getPowerMax() && power >= template.getPowerMin()) {
                return template.getRewardId();
            }
        }
        return 0;
    }

    public int getClanRewardLevel(long power) {
        Map<Integer, ClanPowerRewardTemplate> map = getResHolder().getMap(ClanPowerRewardTemplate.class);
        if (power <= 1) {
            return 1;
        }
        for (ClanPowerRewardTemplate template : map.values()) {
            if (power <= template.getPowerMax() && power >= template.getPowerMin()) {
                return template.getId();
            }
        }
        return 2;
    }

    public boolean isSpecialStaff(int staffId) {
        ConstClanTemplate constClanTemplate = ResHolder.getResService(ConstClanKVResService.class).getTemplate();
        return constClanTemplate.getSpecialStaffIDs().contains(staffId);
    }

    public List<Integer> getBuffIdsByClanStaffId(int staffId) {
        ClanOfficeTemplate template = getResHolder().findValueFromMap(ClanOfficeTemplate.class, staffId);
        if (template == null) {
            // 返回一个空的List，上层不再需要判断
            return Lists.newArrayList();
        }
        return template.getStaffBuffListList();
    }

    public int getClanPlayerResourceLimit(int type) {
        return clanPlayerResourceLimit.getOrDefault(type, 0);
    }

    private void checkClanPowerReward() {

    }

}


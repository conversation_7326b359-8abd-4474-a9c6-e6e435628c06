package com.yorha.common.concurrent.singleflight;

import com.yorha.common.concurrent.executor.ConcurrentHelper;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.BiConsumer;
import java.util.function.Supplier;

/**
 * SingleFlight 模型实现，来自于go语言，参考链接：模型来源：https://segmentfault.com/a/1190000018464029
 *
 * <AUTHOR>
 */
public class SingleFlightGroup<K, V> {
    private static final Logger LOGGER = LogManager.getLogger(SingleFlightGroup.class);

    private final Lock lock = new ReentrantLock();
    private final Map<K, Call<V>> callMap = new HashMap<>();
    private final Executor executor;

    public SingleFlightGroup() {
        this(null);
    }

    public SingleFlightGroup(Executor executor) {
        this.executor = executor;
    }


    public Call<V> get(K k) {
        this.lock.lock();
        try {
            return this.callMap.get(k);
        } finally {
            this.lock.unlock();
        }
    }

    public int size() {
        this.lock.lock();
        try {
            return this.callMap.size();
        } finally {
            this.lock.unlock();
        }
    }

    /**
     * 阻塞执行函数。
     *
     * @param key 分组键。
     * @param fn  执行函数体。注意，同样键只会执行第一次传入的函数体。
     * @return 执行结果。
     * @throws Throwable 执行错误。
     */
    public V invoke(K key, Supplier<V> fn) throws Throwable {
        this.lock.lock();
        final Call<V> call = this.callMap.get(key);
        if (call != null) {
            final Call.SyncCallTask<V> task = call.addSyncTask();
            this.lock.unlock();
            return task.get();
        }
        final Call<V> newCall = new Call<>();
        this.callMap.put(key, newCall);
        this.lock.unlock();
        return this.doCall(key, newCall, fn, null);
    }

    /**
     * 异步执行函数体。
     *
     * @param key 键。
     * @param fn  执行函数体。注意，同样键只会执行第一次传入的函数体。(会到其他线程执行，注意并发。)
     * @param cb  执行函数体的回调，(会到其他线程执行，注意并发。)
     */
    public void invokeAsync(K key, Supplier<V> fn, BiConsumer<V, Throwable> cb) {
        this.lock.lock();
        final Call<V> call = this.callMap.get(key);
        if (call != null) {
            call.addAsyncTask().whenComplete(cb);
            this.lock.unlock();
            return;
        }
        final Call<V> syncCall = new Call<>();
        this.callMap.put(key, syncCall);
        this.lock.unlock();
        final Runnable runnable = () -> this.doCall(key, syncCall, fn, cb);
        if (this.executor == null) {
            ConcurrentHelper.newFiber("SingleFlight#" + key, runnable).start();
        } else {
            this.executor.execute(runnable);
        }
    }

    private V doCall(final K key, final Call<V> call, final Supplier<V> fn, BiConsumer<V, Throwable> cb) {
        V result = null;
        Throwable throwable = null;
        try {
            return result = fn.get();
        } catch (Throwable e) {
            throwable = e;
            if (cb != null) {
                return null;
            }
            throw e;
        } finally {
            this.lock.lock();
            final Call<V> remove = this.callMap.remove(key);
            this.lock.unlock();
            if (cb != null) {
                cb.accept(result, throwable);
            }
            call.notifyTask(result, throwable);
            if (call != remove) {
                LOGGER.error("please check key:{}", key);
            }
        }
    }
}


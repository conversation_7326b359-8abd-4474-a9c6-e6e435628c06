package com.yorha.common.helper;

import com.yorha.common.actor.ref.RefFactory;
import com.yorha.common.actorservice.AbstractActor;
import com.yorha.common.actorservice.GameActorWithCall;
import com.yorha.common.server.NodeRole;
import com.yorha.common.server.ServerContext;
import com.yorha.common.actor.IActorRef;
import com.yorha.proto.CommonMsg.ClanCardInfo;
import com.yorha.proto.CommonMsg.ClanSimpleInfo;
import com.yorha.proto.SsClanCard.*;
import com.yorha.proto.SsPlayerCard.*;
import com.yorha.proto.StructPB;
import it.unimi.dsi.fastutil.longs.LongOpenHashSet;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public class CardHelper {
    private static final Logger LOGGER = LogManager.getLogger(CardHelper.class);

    /**
     * 构造查PlayerCard的目标ref
     */
    public static IActorRef genPlayerCardRef(long playerId) {
        return RefFactory.ofPlayerCardActor(playerId % getPlayerCardActorNum());
    }

    /**
     * 构造查PlayerCard的目标ref
     */
    public static IActorRef genClanCardRef(long clanId) {
        return RefFactory.ofClanCardActor(clanId % getClanCardActorNum());
    }

    /**
     * call询单个玩家名片
     */
    public static QueryPlayerCardAns queryPlayerCardSync(GameActorWithCall actor, long playerId) {
        IActorRef ref = genPlayerCardRef(playerId);
        QueryPlayerCardAsk msg = QueryPlayerCardAsk.newBuilder().setPlayerId(playerId).build();
        return actor.call(ref, msg);
    }

    /**
     * call询单个玩家名片 附帶联盟
     */
    public static StructPB.PlayerCardInfoPB queryPlayerCardWithClanSync(GameActorWithCall actor, long playerId) {
        IActorRef ref = genPlayerCardRef(playerId);
        QueryPlayerCardAns ans = actor.call(ref, QueryPlayerCardAsk.newBuilder().setPlayerId(playerId).build());
        if (ans.getCardInfo().getClanId() == 0) {
            return ans.getCardInfo();
        }
        IActorRef refClan = genClanCardRef(ans.getCardInfo().getClanId());
        QueryClanNameAns ans2 = actor.call(refClan, QueryClanNameAsk.newBuilder().setClanId(ans.getCardInfo().getClanId()).build());
        return ans.getCardInfo().toBuilder().setClanSimpleName(ans2.getClanSimpleName()).setClanName(ans2.getClanName()).build();
    }

    /**
     * call询单个玩家头
     */
    public static QueryPlayerCardHeadAns queryPlayerCardHeadSync(GameActorWithCall actor, long playerId) {
        IActorRef ref = genPlayerCardRef(playerId);
        QueryPlayerCardHeadAsk msg = QueryPlayerCardHeadAsk.newBuilder().setPlayerId(playerId).build();
        return actor.call(ref, msg);
    }

    /**
     * call查询 单个联盟simple
     */
    public static QueryClanSimpleAns queryClanSimpleSync(GameActorWithCall actor, long clanId) {
        IActorRef ref = genClanCardRef(clanId);
        QueryClanSimpleAsk msg = QueryClanSimpleAsk.newBuilder().setClanId(clanId).build();
        return actor.call(ref, msg);
    }

    /**
     * call查询 单个联盟card
     */
    public static QueryClanCardAns queryClanCardSync(GameActorWithCall actor, long clanId) {
        IActorRef ref = genClanCardRef(clanId);
        QueryClanCardAsk msg = QueryClanCardAsk.newBuilder().setClanId(clanId).build();
        return actor.call(ref, msg);
    }

    /**
     * ask询单个玩家头
     */
    public static void queryPlayerCardHeadASync(AbstractActor actor, long playerId, Consumer<StructPB.PlayerCardHeadPB> complete) {
        IActorRef ref = genPlayerCardRef(playerId);
        QueryPlayerCardHeadAsk msg = QueryPlayerCardHeadAsk.newBuilder().setPlayerId(playerId).build();
        actor.<QueryPlayerCardHeadAns>ask(ref, msg).onComplete(
                (res, t) -> {
                    if (res != null) {
                        complete.accept(res.getCardHead());
                    } else {
                        LOGGER.error("queryPlayerCardHeadASync failed playerId {} ", playerId, t);
                        complete.accept(StructPB.PlayerCardHeadPB.getDefaultInstance());
                    }
                }
        );
    }

    /**
     * ask询单个玩家名片
     */
    public static void queryPlayerCardAsync(AbstractActor actor, long playerId, Consumer<StructPB.PlayerCardInfoPB> complete) {
        IActorRef ref = genPlayerCardRef(playerId);
        QueryPlayerCardAsk msg = QueryPlayerCardAsk.newBuilder().setPlayerId(playerId).build();
        actor.<QueryPlayerCardAns>ask(ref, msg).onComplete(
                (res, t) -> {
                    if (res != null) {
                        complete.accept(res.getCardInfo());
                    } else {
                        LOGGER.error("queryPlayerCardAsync failed playerId {} ", playerId, t);
                        complete.accept(StructPB.PlayerCardInfoPB.getDefaultInstance());
                    }
                }
        );
    }

    /**
     * ask查询单个玩家名片  附带联盟名字
     */
    public static void queryPlayerCardWithClanAsync(AbstractActor actor, long playerId, Consumer<StructPB.PlayerCardInfoPB> complete) {
        queryPlayerCardAsync(actor, playerId,
                (pb) -> {
                    if (pb.getClanId() == 0) {
                        complete.accept(pb.toBuilder().setClanId(0).build());
                        return;
                    }
                    StructPB.PlayerCardInfoPB.Builder builder = pb.toBuilder();
                    queryClanNameAsync(actor, pb.getClanId(),
                            (ans) -> {
                                if (ans == null) {
                                    complete.accept(builder.build());
                                    return;
                                }
                                builder.setClanName(ans.getClanName()).setClanSimpleName(ans.getClanSimpleName());
                                complete.accept(builder.build());
                            });
                });

    }

    /**
     * ask查询 单个联盟名字
     */
    public static void queryClanNameAsync(AbstractActor actor, long clanId, Consumer<QueryClanNameAns> onComplete) {
        IActorRef ref = genClanCardRef(clanId);
        QueryClanNameAsk msg = QueryClanNameAsk.newBuilder().setClanId(clanId).build();
        actor.<QueryClanNameAns>ask(ref, msg).onComplete(
                (res, t) -> {
                    if (res != null) {
                        onComplete.accept(res);
                    } else {
                        LOGGER.error("queryClanCardAsync failed clanId {} ", clanId, t);
                        onComplete.accept(null);
                    }
                }
        );
    }

    /**
     * ask查询 单个联盟Simple
     */
    public static void queryClanSimpleAsync(AbstractActor actor, long clanId, Consumer<ClanSimpleInfo> onComplete) {
        IActorRef ref = genClanCardRef(clanId);
        QueryClanSimpleAsk msg = QueryClanSimpleAsk.newBuilder().setClanId(clanId).build();
        actor.<QueryClanSimpleAns>ask(ref, msg).onComplete(
                (res, t) -> {
                    if (res != null) {
                        onComplete.accept(res.getInfo());
                    } else {
                        LOGGER.error("queryClanCardAsync failed clanId {} ", clanId, t);
                        onComplete.accept(null);
                    }
                }
        );
    }

    /**
     * ask查询 单个联盟card
     */
    public static void queryClanCardAsync(AbstractActor actor, long clanId, Consumer<ClanCardInfo> onComplete) {
        IActorRef ref = genClanCardRef(clanId);
        QueryClanCardAsk msg = QueryClanCardAsk.newBuilder().setClanId(clanId).build();
        actor.<QueryClanCardAns>ask(ref, msg).onComplete(
                (res, t) -> {
                    if (res != null) {
                        onComplete.accept(res.getInfo());
                    } else {
                        LOGGER.error("queryClanCardAsync failed clanId {} ", clanId, t);
                        onComplete.accept(null);
                    }
                }
        );
    }

    /**
     * ask批量查询玩家名片
     */
    public static void batchQueryPlayerCard(AbstractActor actor, Collection<Long> playerList, Consumer<Map<Long, StructPB.PlayerCardInfoPB>> allComplete) {
        final int num = getPlayerCardActorNum();
        Map<Long, BatchQueryPlayerCardAsk.Builder> msg = new HashMap<>(num);
        for (Long playerId : playerList) {
            long actorId = playerId % num;
            msg.computeIfAbsent(actorId, (k) -> BatchQueryPlayerCardAsk.newBuilder()).addPlayers(playerId);
        }
        Map<Long, StructPB.PlayerCardInfoPB> map = new HashMap<>();
        actor.<BatchQueryPlayerCardAns>batchAsk(
                msg,
                RefFactory::ofPlayerCardActor,
                (res) -> map.putAll(res.getCardInfosMap()),
                () -> allComplete.accept(map));
    }

    /**
     * ask批量查询单个玩家名片  附带联盟名字
     */
    public static void batchQueryPlayerCardWithClan(AbstractActor actor, List<Long> playerList, Consumer<Map<Long, StructPB.PlayerCardInfoPB>> onComplete) {
        final int num = getPlayerCardActorNum();
        Map<Long, BatchQueryPlayerCardAsk.Builder> msg = new HashMap<>(num);
        for (Long playerId : playerList) {
            long actorId = playerId % num;
            msg.computeIfAbsent(actorId, (k) -> BatchQueryPlayerCardAsk.newBuilder()).addPlayers(playerId);
        }
        Map<Long, StructPB.PlayerCardInfoPB> ret = new HashMap<>();
        Map<Long, StructPB.PlayerCardInfoPB.Builder> playerMap = new HashMap<>();
        Set<Long> clanSet = new LongOpenHashSet();
        actor.<BatchQueryPlayerCardAns>batchAsk(
                msg,
                RefFactory::ofPlayerCardActor,
                (res) -> {
                    for (Map.Entry<Long, StructPB.PlayerCardInfoPB> entry : res.getCardInfosMap().entrySet()) {
                        ret.put(entry.getKey(), entry.getValue());
                        if (entry.getValue().getClanId() != 0) {
                            playerMap.put(entry.getKey(), entry.getValue().toBuilder());
                            clanSet.add(entry.getValue().getClanId());
                        }
                    }
                },
                () -> {
                    if (clanSet.isEmpty()) {
                        onComplete.accept(ret);
                        return;
                    }
                    batchQueryClanName(
                            actor,
                            clanSet,
                            (name) -> {
                                for (Map.Entry<Long, StructPB.PlayerCardInfoPB.Builder> entry : playerMap.entrySet()) {
                                    long clanId = entry.getValue().getClanId();
                                    ClanAllName allName = name.get(clanId);
                                    if (allName != null) {
                                        ret.put(entry.getKey(), entry.getValue().setClanName(allName.getClanName()).setClanSimpleName(allName.getClanSimpleName()).build());
                                    }
                                }
                                onComplete.accept(ret);
                            }
                    );
                });

    }

    /**
     * ask批量查询单个玩家排行榜数据
     */
    public static void batchQueryPlayerRankWithClan(
            AbstractActor actor,
            Collection<Long> playerList,
            BiConsumer<Map<Long, StructPB.PlayerCardInfoPB>, Map<Long, ClanSimpleInfo>> onComplete
    ) {
        final int num = getPlayerCardActorNum();
        Map<Long, BatchQueryPlayerCardAsk.Builder> msg = new HashMap<>(num);
        for (Long playerId : playerList) {
            long actorId = playerId % num;
            msg.computeIfAbsent(actorId, (k) -> BatchQueryPlayerCardAsk.newBuilder()).addPlayers(playerId);
        }
        Map<Long, StructPB.PlayerCardInfoPB> players = new HashMap<>();
        Set<Long> clanSet = new LongOpenHashSet();
        actor.<BatchQueryPlayerCardAns>batchAsk(
                msg,
                RefFactory::ofPlayerCardActor,
                (res) -> {
                    for (Map.Entry<Long, StructPB.PlayerCardInfoPB> entry : res.getCardInfosMap().entrySet()) {
                        players.put(entry.getKey(), entry.getValue());
                        if (entry.getValue().getClanId() != 0) {
                            clanSet.add(entry.getValue().getClanId());
                        }
                    }
                },
                () -> {
                    if (clanSet.isEmpty()) {
                        onComplete.accept(players, Collections.emptyMap());
                        return;
                    }
                    batchQueryClanSimple(
                            actor,
                            clanSet,
                            (clanMap) -> onComplete.accept(players, clanMap)
                    );
                });

    }

    /**
     * ask批量查询单个玩家头
     */
    public static void batchQueryPlayerHead(AbstractActor actor, Collection<Long> playerList, Consumer<Map<Long, StructPB.PlayerCardHeadPB>> allComplete) {
        final int num = getPlayerCardActorNum();
        Map<Long, BatchQueryPlayerCardHeadAsk.Builder> msg = new HashMap<>(num);
        for (Long playerId : playerList) {
            long actorId = playerId % num;
            msg.computeIfAbsent(actorId, (k) -> BatchQueryPlayerCardHeadAsk.newBuilder()).addPlayerIds(playerId);
        }
        Map<Long, StructPB.PlayerCardHeadPB> map = new HashMap<>();
        actor.<BatchQueryPlayerCardHeadAns>batchAsk(
                msg,
                RefFactory::ofPlayerCardActor,
                (res) -> map.putAll(res.getCardHeadListMap()),
                () -> allComplete.accept(map));

    }

    /**
     * ask批量查询单个玩家zoneId
     */
    public static void batchQueryPlayerZoneId(AbstractActor actor, Collection<Long> playerList, Consumer<Map<Long, Integer>> allComplete) {
        final int num = getPlayerCardActorNum();
        Map<Long, BatchQueryPlayerZoneIdAsk.Builder> msg = new HashMap<>(num);
        for (Long playerId : playerList) {
            long actorId = playerId % num;
            msg.computeIfAbsent(actorId, (k) -> BatchQueryPlayerZoneIdAsk.newBuilder()).addPlayerIds(playerId);
        }
        Map<Long, Integer> map = new HashMap<>();
        actor.<BatchQueryPlayerZoneIdAns>batchAsk(
                msg,
                RefFactory::ofPlayerCardActor,
                (res) -> map.putAll(res.getPlayerZoneIdsMap()),
                () -> allComplete.accept(map));
    }

    public static void batchQueryPlayerZoneId(AbstractActor actor, Collection<Long> playerList, Consumer<BatchQueryPlayerZoneIdAns> onOneDone, Runnable allDone) {
        final int num = getPlayerCardActorNum();
        Map<Long, BatchQueryPlayerZoneIdAsk.Builder> msg = new HashMap<>(num);
        for (Long playerId : playerList) {
            long actorId = playerId % num;
            msg.computeIfAbsent(actorId, (k) -> BatchQueryPlayerZoneIdAsk.newBuilder()).addPlayerIds(playerId);
        }
        actor.batchAsk(
                msg,
                RefFactory::ofPlayerCardActor,
                onOneDone,
                allDone);
    }

    /**
     * ask 批量查询联盟name
     */
    public static void batchQueryClanName(AbstractActor actor, Set<Long> clans, Consumer<Map<Long, ClanAllName>> allComplete) {
        final int num = getClanCardActorNum();
        Map<Long, BatchQueryClanNameAsk.Builder> msg = new HashMap<>(num);
        for (long clanId : clans) {
            long actorId = clanId % num;
            msg.computeIfAbsent(actorId, (k) -> BatchQueryClanNameAsk.newBuilder()).addClans(clanId);
        }
        Map<Long, ClanAllName> name = new HashMap<>();
        actor.<BatchQueryClanNameAns>batchAsk(
                msg,
                RefFactory::ofClanCardActor,
                (res) -> {
                    name.putAll(res.getInfoMap());
                },
                () -> allComplete.accept(name));
    }

    /**
     * ask 批量查询联盟simple
     */
    public static void batchQueryClanSimple(AbstractActor actor, Set<Long> clans, Consumer<Map<Long, ClanSimpleInfo>> allComplete) {
        final int num = getClanCardActorNum();
        Map<Long, BatchQueryClanSimpleAsk.Builder> msg = new HashMap<>(num);
        for (long clanId : clans) {
            long actorId = clanId % num;
            msg.computeIfAbsent(actorId, (k) -> BatchQueryClanSimpleAsk.newBuilder()).addClans(clanId);
        }
        Map<Long, ClanSimpleInfo> ret = new HashMap<>();
        actor.<BatchQueryClanSimpleAns>batchAsk(
                msg,
                RefFactory::ofClanCardActor,
                (res) -> ret.putAll(res.getInfoMap()),
                () -> allComplete.accept(ret));
    }

    /**
     * ask 批量查询联盟card
     */
    public static void batchQueryClanCard(AbstractActor actor, Collection<Long> clans, Consumer<Map<Long, ClanCardInfo>> allComplete) {
        final int num = getClanCardActorNum();
        Map<Long, BatchQueryClanCardAsk.Builder> msg = new HashMap<>(num);
        for (long clanId : clans) {
            long actorId = clanId % num;
            msg.computeIfAbsent(actorId, (k) -> BatchQueryClanCardAsk.newBuilder()).addClans(clanId);
        }
        Map<Long, ClanCardInfo> ret = new HashMap<>();
        actor.<BatchQueryClanCardAns>batchAsk(
                msg,
                RefFactory::ofClanCardActor,
                (res) -> ret.putAll(res.getInfoMap()),
                () -> allComplete.accept(ret));
    }

    private static int getPlayerCardActorNum() {
        return Math.max(ServerContext.getActorSystem().getRegistryValue().getServerTypeNodeList(NodeRole.Global.getTypeId()).size() * 2, 1);
    }

    private static int getClanCardActorNum() {
        return Math.max(ServerContext.getActorSystem().getRegistryValue().getServerTypeNodeList(NodeRole.Global.getTypeId()).size() * 2, 1);
    }

}

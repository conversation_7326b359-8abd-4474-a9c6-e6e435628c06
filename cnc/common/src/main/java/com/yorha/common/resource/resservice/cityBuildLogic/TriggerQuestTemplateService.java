package com.yorha.common.resource.resservice.cityBuildLogic;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.utils.Pair;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonEnum;
import res.template.TriggerQuestionTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * eva
 *
 * <AUTHOR>
 */
public class TriggerQuestTemplateService extends AbstractResService {

    private Map<CommonEnum.QuestTriggerType, List<TriggerQuestionTemplate>> configMap = new HashMap<>();

    public TriggerQuestTemplateService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        configMap = getResHolder().getListFromMap(TriggerQuestionTemplate.class).stream().collect(Collectors.groupingBy(TriggerQuestionTemplate::getTriggerType));
    }


    @Override
    public void checkValid() throws ResourceException {

    }

    public List<TriggerQuestionTemplate> getConfigByType(CommonEnum.QuestTriggerType type) {
        List<TriggerQuestionTemplate> triggerQuestionTemplates = configMap.get(type);
        if (triggerQuestionTemplates == null) {
            return new ArrayList<>();
        }
        return triggerQuestionTemplates;
    }

    public int getParamByType(CommonEnum.QuestTriggerType type, TriggerQuestionTemplate triggerQuestionTemplate) {
        switch (type) {
            case QTT_EVENT:
            case QTT_CREAT_ROLE_TIME: {
                return triggerQuestionTemplate.getTriggerParameterList().getFirst();
            }
            default: {

                throw new GeminiException(StringUtils.format("QuestTriggerType={} param={} error. ", type, triggerQuestionTemplate.getTriggerParameterList()));
            }
        }
    }

    public Pair<Integer, Integer> getParamPairByType(CommonEnum.QuestTriggerType type, TriggerQuestionTemplate template) {
        switch (type) {
            case QTT_BUILDING_LV: {
                return Pair.of(template.getTriggerParameterList().get(0), template.getTriggerParameterList().get(1));
            }
            default: {
                throw new GeminiException(StringUtils.format("QuestTriggerType={} param={} error. ", type, template.getTriggerParameterList()));
            }
        }
    }
}
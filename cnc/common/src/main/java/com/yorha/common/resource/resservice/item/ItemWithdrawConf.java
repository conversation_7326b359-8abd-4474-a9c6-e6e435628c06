package com.yorha.common.resource.resservice.item;

import com.google.common.collect.Lists;
import com.yorha.common.constant.Constants;
import com.yorha.common.utils.time.TimeUtils;

import java.time.ZonedDateTime;

/**
 * 道具回收的配置
 * <p>
 * 多个条件之间是 “或” 关系
 */
public interface ItemWithdrawConf {

    class List {
        private final java.util.List<ItemWithdrawConf> list;

        public List(java.util.List<ItemWithdrawConf> list) {
            this.list = list;
        }

        public java.util.List<ItemWithdrawConf> getConfList() {
            return list;
        }

        public static List parse(String params) {
            String[] paramArray = params.split(Constants.FEN_HAO);
            java.util.List<ItemWithdrawConf> typeList = Lists.newArrayList();
            for (String param : paramArray) {
                String[] subArray = param.split(Constants.XIA_HUA_XIAN);
                int type = Integer.parseInt(subArray[0]);
                switch (type) {
                    case 1:
                        typeList.add(new PinnedDate(subArray));
                        break;
                    case 2:
                        typeList.add(new FromRoleCreateHours(subArray));
                        break;
                    case 3:
                        typeList.add(new CityLevel(subArray));
                        break;
                    default:
                        break;
                }
            }
            return new List(typeList);
        }

    }


    // 固定时间
    class PinnedDate implements ItemWithdrawConf {

        private final ZonedDateTime date;

        public PinnedDate(String[] args) {
            this.date = TimeUtils.string2ZoneDate(args[1]);
        }

        public ZonedDateTime getDate() {
            return date;
        }
    }

    // 创角n小时后回收
    class FromRoleCreateHours implements ItemWithdrawConf {

        private final int hours;

        public FromRoleCreateHours(String[] args) {
            this.hours = Integer.parseInt(args[1]);
        }

        public int getHours() {
            return hours;
        }
    }

    // 主堡达到n级
    class CityLevel implements ItemWithdrawConf {

        private final int cityLevel;

        public CityLevel(String[] args) {
            this.cityLevel = Integer.parseInt(args[1]);
        }

        public int getCityLevel() {
            return cityLevel;
        }
    }

}

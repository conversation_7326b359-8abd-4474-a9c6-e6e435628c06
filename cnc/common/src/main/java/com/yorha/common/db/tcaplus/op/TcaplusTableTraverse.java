package com.yorha.common.db.tcaplus.op;

import com.google.protobuf.Descriptors;
import com.google.protobuf.Message;
import com.tencent.tcaplus.client.*;
import com.tencent.tcaplus.client.Record;
import com.tencent.tcaplus.util.TCaplusException;
import com.tencent.tdr.tcaplus_protocol_cs.TcaplusProtocolCsConstants;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.option.TraverseOption;
import com.yorha.common.db.tcaplus.result.TraverseResult;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import com.yorha.common.utils.time.SystemClock;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.function.Consumer;

/**
 * 遍历操作。
 * 注意：不支持阻塞操作。
 *
 * @param <T> 请求类型。
 * <AUTHOR>
 */
public class TcaplusTableTraverse<T extends Message.Builder> extends TcaplusOperation<T, TraverseOption<T>, TraverseResult> {

    private static final Logger LOGGER = LogManager.getLogger(TcaplusTableTraverse.class);

    private static final int RESPONSE_LIMIT_COUNT = 100;

    private int responseIndex = 0;

    public TcaplusTableTraverse(Client client, T t, TraverseOption<T> traverseOption) {
        super(client, PbFieldMetaCaches.getMetaData(t), t, traverseOption);
    }

    @Override
    protected int getType() {
        return TcaplusProtocolCsConstants.TCAPLUS_CMD_TABLE_TRAVERSE_REQ;
    }

    @Override
    protected void configRequestProperty(Request request) {
        throw new NotImplementedException();
    }

    @Override
    public TraverseResult run() {
        // 设置启动时间戳
        final long tsMs = SystemClock.nowNative();
        final long timeoutTsMs = getOption().getTimeoutMs() + tsMs;

        // 构造遍历器
        final GenericTableTraverser traverser = this.configGenericTableTraverser();

        // 记录请求数据
        this.onDbRequest();

        // 初始化错误码
        TcaplusErrorCode errorCode = TcaplusErrorCode.GEN_ERR_SUC;
        int totalNum = 0;

        try {
            // 准备容器
            List<ValueWithVersion<T>> values = new ArrayList<>(RESPONSE_LIMIT_COUNT);

            // 开始迭代
            final Iterator<Record> iterator = traverser.start();
            while (iterator.hasNext()) {
                // 超时
                if (SystemClock.nowNative() > timeoutTsMs) {
                    errorCode = TcaplusErrorCode.API_ERR_WAIT_RSP_TIMEOUT;
                    break;
                }

                // 构造数据
                final Record record = iterator.next();
                final ValueWithVersion<T> vv = this.buildValueWithVersion(record);
                values.add(vv);

                // 分包回调
                if (values.size() == RESPONSE_LIMIT_COUNT) {
                    totalNum += values.size();
                    LOGGER.info("TcaplusTableTraverser run requestId={}, tableName={}, responseIndex={}, count={}, cursor={}",
                            this.getRequestId(), this.getTableName(), responseIndex, values.size(), traverser.getCursor());
                    this.getOption().getHandler().accept(values);
                    values = new ArrayList<>(RESPONSE_LIMIT_COUNT);
                    this.responseIndex += 1;
                }
            }

            // 最终包处理一下
            if (errorCode == TcaplusErrorCode.GEN_ERR_SUC && !values.isEmpty()) {
                totalNum += values.size();
                LOGGER.info("TcaplusTableTraverser run requestId={}, tableName={}, responseIndex={}, count={}, cursor={}",
                        this.getRequestId(), this.getTableName(), responseIndex, values.size(), traverser.getCursor());
                this.getOption().getHandler().accept(values);
            }
        } catch (Exception e) {
            LOGGER.error("TcaplusTableTraverser run requestId={}, tableName={}, responseIndex={}, totalNum={}, cursor={}, e=",
                    this.getRequestId(), this.getTableName(), responseIndex, totalNum, traverser.getCursor(), e);
            errorCode = TcaplusErrorCode.GEN_ERR_ERR;

            if (e instanceof TCaplusException) {
                final int errno = ((TCaplusException) e).getErrno();
                errorCode = errno == 0 ? TcaplusErrorCode.GEN_ERR_ERR : TcaplusErrorCode.forNumber(errno);
            }
        }


        // 计算耗时
        final long costMs = SystemClock.nowNative() - tsMs;

        // 统计回包
        this.onDbResponse(errorCode.getValue());

        LOGGER.info("TcaplusTableTraverser run finish, requestId={}, tableName={}, option={}, total={}, errorCode={}, costMs={}, cursor={}",
                this.getRequestId(), this.getTableName(), this.getOption(), totalNum, errorCode, costMs, traverser.getCursor());

        return new TraverseResult(errorCode, this.getRequestId());
    }

    @Override
    public void runAsync(Consumer<TraverseResult> cb) {
        // 设置启动时间戳
        final long tsMs = SystemClock.nowNative();
        final long timeoutTsMs = this.getOption().getTimeoutMs() + tsMs;

        // 构造遍历器
        final GenericTableTraverser traverser = this.configGenericTableTraverser();

        // 记录请求数据
        this.onDbRequest();

        // 异步遍历构造器
        traverser.startAsync(
                response -> {
                    // 构造数据容器
                    final List<ValueWithVersion<T>> values = new ArrayList<>(RESPONSE_LIMIT_COUNT);

                    TcaplusErrorCode errorCode = TcaplusErrorCode.forNumber(response.getResult());

                    // 超时
                    if (SystemClock.nowNative() > timeoutTsMs) {
                        errorCode = TcaplusErrorCode.API_ERR_WAIT_RSP_TIMEOUT;
                    }

                    // 成功，汇聚容器
                    if (errorCode == TcaplusErrorCode.GEN_ERR_SUC) {
                        for (final Record record : response.getRecordList()) {
                            final ValueWithVersion<T> vv = TcaplusTableTraverse.this.buildValueWithVersion(record);
                            values.add(vv);
                        }
                    }

                    // 错误码
                    if (errorCode == null) {
                        errorCode = TcaplusErrorCode.GEN_ERR_ERR;
                    }

                    // 回包正常且未完成，则继续遍历
                    if (errorCode == TcaplusErrorCode.GEN_ERR_SUC && !response.isTraverseCompleted()) {
                        LOGGER.info("TcaplusTableTraverser runAsync requestId={}, tableName={}, responseIndex={}, count={}, cursor={}",
                                this.getRequestId(), this.getTableName(), responseIndex, values.size(), traverser.getCursor());
                        this.responseIndex += 1;
                        this.getOption().getHandler().accept(values);
                        return;
                    }

                    if (errorCode == TcaplusErrorCode.GEN_ERR_SUC) {
                        LOGGER.info("TcaplusTableTraverser runAsync requestId={}, tableName={}, responseIndex={}, count={}, cursor={}",
                                this.getRequestId(), this.getTableName(), responseIndex, values.size(), traverser.getCursor());
                        this.getOption().getHandler().accept(values);
                    }

                    // 计算耗时
                    final long costMs = SystemClock.nowNative() - tsMs;

                    // 统计回包
                    this.onDbResponse(errorCode.getValue());
                    var total = values.size() + responseIndex * RESPONSE_LIMIT_COUNT;
                    LOGGER.info("TcaplusTableTraverser runAsync finish, requestId={}, tableName={}, option={}, total={}, errorCode={}, resultCode={}, costMs={}, cursor={}",
                            getRequestId(), getTableName(), getOption(), total, errorCode, response.getResult(), costMs, traverser.getCursor());

                    cb.accept(new TraverseResult(errorCode, this.getRequestId()));
                });
    }

    @Override
    protected TraverseResult buildResult(Response response) {
        throw new NotImplementedException();
    }

    /**
     * 根据Record信息，编译一个结果。
     *
     * @param record 数据记录。
     * @return 数据结果。
     */
    protected final ValueWithVersion<T> buildValueWithVersion(final Record record) {
        // 配置数据
        final ValueWithVersion<T> vv = new ValueWithVersion<>();
        vv.version = record.getVersion();
        vv.value = buildDefaultValue(this.getReq());
        this.readFromResponseValues(record, vv.value);
        return vv;
    }

    protected final GenericTableTraverser configGenericTableTraverser() {
        // 申请遍历器
        final GenericTableTraverser traverser = this.client.getGenericTableTraverser();

        // 设置表名
        traverser.setTableName(this.getTableName());

        // 设置需要的field
        if (this.getOption().isNeedAllFields()) {

            for (final Descriptors.FieldDescriptor descriptor : this.getFieldMetaData().valueFieldsList) {
                traverser.addFieldsName(descriptor.getName());
                this.addRequestBytes(descriptor.getName().length());
            }

        } else {

            for (final String fieldName : this.getOption().getFieldNames()) {
                if (this.getFieldMetaData().keyFieldsMap.containsKey(fieldName)) {
                    throw new IllegalArgumentException("field name can't be table key, " + fieldName + " not right");
                }

                if (!this.getFieldMetaData().valueFieldsMap.containsKey(fieldName)) {
                    throw new IllegalArgumentException("field name " + fieldName + " not right");
                }
                traverser.addFieldsName(fieldName);
                this.addRequestBytes(fieldName.length());
            }

        }

        // 设置每次回包的数量
        traverser.setLoadOptions(RESPONSE_LIMIT_COUNT);
        this.addRequestBytes(Integer.SIZE);
        // 设置请求总数量，代表遍历所有数据
        traverser.setTotalLimit(this.getOption().getTotalLimit());
        this.addRequestBytes(Integer.SIZE);

        return traverser;

    }
}

package com.yorha.common.db.mongo;

import com.yorha.gemini.utils.StringUtils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 */
public class MongoClientConfig {
    private String user;
    private String password;
    private String database;
    private List<String> addressList;
    private int maxPoolSize = 48;
    private int minPoolSize = 8;
    private int connectTimeoutMs = 5000;
    private int socketTimeoutMs = 10000;
    private int maxWaitTimeMs = 10000;
    private int maxIdleTimeSec = 900;
    private int maxConnecting = 4;
    private int readTimeoutMs = 10000;

    public int getReadTimeoutMs() {
        return readTimeoutMs;
    }

    public void setReadTimeoutMs(int readTimeoutMs) {
        this.readTimeoutMs = readTimeoutMs;
    }

    public int getMaxConnecting() {
        return maxConnecting;
    }

    public void setMaxConnecting(int maxConnecting) {
        this.maxConnecting = maxConnecting;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getDatabase() {
        return database;
    }

    public void setDatabase(String database) {
        this.database = database;
    }

    public List<String> getAddressList() {
        return addressList;
    }

    public void setAddressList(List<String> addressList) {
        this.addressList = addressList;
    }

    public int getMaxPoolSize() {
        return maxPoolSize;
    }

    public void setMaxPoolSize(int maxPoolSize) {
        this.maxPoolSize = maxPoolSize;
    }

    public int getMinPoolSize() {
        return minPoolSize;
    }

    public void setMinPoolSize(int minPoolSize) {
        this.minPoolSize = minPoolSize;
    }

    public int getConnectTimeoutMs() {
        return connectTimeoutMs;
    }

    public void setConnectTimeoutMs(int connectTimeoutMs) {
        this.connectTimeoutMs = connectTimeoutMs;
    }

    public int getSocketTimeoutMs() {
        return socketTimeoutMs;
    }

    public void setSocketTimeoutMs(int socketTimeoutMs) {
        this.socketTimeoutMs = socketTimeoutMs;
    }

    public int getMaxWaitTimeMs() {
        return maxWaitTimeMs;
    }

    public void setMaxWaitTimeMs(int maxWaitTimeMs) {
        this.maxWaitTimeMs = maxWaitTimeMs;
    }

    public int getMaxIdleTimeSec() {
        return maxIdleTimeSec;
    }

    public void setMaxIdleTimeSec(int maxIdleTimeSec) {
        this.maxIdleTimeSec = maxIdleTimeSec;
    }
    public String buildAddress() {
        StringBuilder ret = new StringBuilder();
        for (int i = 0; i < addressList.size(); i++) {
            ret.append(addressList.get(i));
            if (i != addressList.size() - 1) {
                ret.append(",");
            }
        }
    return ret.toString();
    }
    public String buildConnectionString() {
        StringBuilder ret = new StringBuilder();
        ret.append("mongodb://");
        if (StringUtils.isNotEmpty(user)) {
            ret.append(URLEncoder.encode(user,  StandardCharsets.UTF_8));
            if (StringUtils.isNotEmpty(password)) {
                ret.append(":").append(password);
            }
            ret.append("@");
        }
        ret.append(buildAddress());
        ret.append("/").append(database);
        ret.append("?authSource=admin");
        if (addressList.size()  > 1) {
            ret.append("&replicaSet=rs0");
        }
        return ret.toString();
    }
}

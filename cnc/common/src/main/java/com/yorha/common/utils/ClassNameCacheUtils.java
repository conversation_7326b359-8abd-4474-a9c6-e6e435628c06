package com.yorha.common.utils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * class的simpleName缓存
 * 避免每次getSimpleName的损耗
 *
 * <AUTHOR>
 */
public class ClassNameCacheUtils {

    private ClassNameCacheUtils() {
    }

    private static final Map<Class<?>, String> SIMPLE_NAME_CACHE = new ConcurrentHashMap<>();

    public static String getSimpleName(Class<?> clazz) {
        if (!SIMPLE_NAME_CACHE.containsKey(clazz)) {
            SIMPLE_NAME_CACHE.put(clazz, clazz.getSimpleName());
        }
        return SIMPLE_NAME_CACHE.get(clazz);
    }
}

package com.yorha.common.helper;

import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.utils.time.SystemClock;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 */
public class LoginHelper {
    private final AtomicInteger counter = new AtomicInteger(0);
    private final AtomicLong countTs = new AtomicLong(0);

    public boolean checkCanLogin() {
        long now = SystemClock.nowOfSeconds();
        long old = countTs.get();
        if (old < now && countTs.compareAndSet(old, now)) {
            counter.set(0);
        }
        return counter.getAndIncrement() < ClusterConfigUtils.getWorldConfig().getIntItem("gate_login_max_per_second");
    }
}

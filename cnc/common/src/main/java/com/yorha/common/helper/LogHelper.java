package com.yorha.common.helper;

import com.yorha.common.server.ServerContext;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 */
public class LogHelper {
    private static final Logger LOGGER = LogManager.getLogger(LogHelper.class);

    public static void timerErrorLog(String format, Object... arguments) {
        if (ServerContext.isTestEnv()) {
            WechatLog.error(format, arguments);
            return;
        }
        LOGGER.error(format, arguments);
    }
}

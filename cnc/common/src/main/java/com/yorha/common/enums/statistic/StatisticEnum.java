package com.yorha.common.enums.statistic;

import com.yorha.proto.CommonEnum;

/**
 * 统计项枚举
 * 用于服务器内部玩家侧数据记录统计
 * 用于任务、英雄、面板历史数据使用
 *
 * <AUTHOR>
 */
public enum StatisticEnum {
    // 内城建筑升级事件
    INNER_BUILD_TOTAL(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 科技研究次数
    TECH_RESEARCH_TOTAL(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 英雄升级
    HERO_LEVELUP_TOTAL(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 黑市购买次数
    DISCOUNT_STORE_BUY_TOTAL(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 黑市使用金币购买次数
    DISCOUNT_STORE_DIAMONDBUY_TOTAL(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 黑市刷新次数
    DISCOUNT_STORE_REFRESH_TOTAL(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 使用加速道具累计多少分钟
    USE_ACCELERATE_ITEM_TOTAL(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 使用城建加速xx分钟
    USE_BUILD_ACCELERATE_ITEM_TOTAL(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 使用训练加速xx分钟
    USE_TRAIN_ACCELERATE_ITEM_TOTAL(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 使用科技加速xx分钟
    USE_TECH_ACCELERATE_ITEM_TOTAL(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 使用空军次数
    AIR_FORCES_USED_TIMES(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 历史建造xx建筑xx个
    CONSTRUCT_INNER_BUILD_NUM_TOTAL(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 空军升级x次
    PLANE_LEVEL_UP_TOTAL(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 使用xx道具xx次
    USE_ITEM_TOTAL(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 使用xx道具类型xx次（不包含自动使用的道具）
    USE_ITEMTYPE_TOTAL(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 开始训练某个兵种x个
    START_TRAIN_SOLDIER_NUM(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 完成训练某个等级兵种x个
    FINISH_TRAIN_SOLDIERID_NUM(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 完成治疗士兵x个
    FINISH_TREAT_SOLDIER_NUM(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 城内采集某种资源达到多少
    INNER_COLLECT_RESOURCE_NUM(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 城内采集任何资源达到多少
    INNER_COLLECT_ANY_RESOURCE_NUM(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 联盟帮助x次
    CLAN_HELP_NUM_TOTAL(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 加入联盟
    JOIN_CLAN_NUM(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 派遣你的军队x次
    DISPATCH_TROOP_TOTAL(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 攻击某个等级野怪x次
    ATTACK_MONSTER_NUM_GROUPBY_LEVEL(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 击杀某个等级野怪x次
    KILL_MONSTER_NUM_GROUPBY_LEVEL(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 击杀特定野怪
    KILL_SPECIAL_MONSTER(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_COVER),
    // 攻打据点x次
    ATTACK_POINT_NUM(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 世界采某种资源达到多少
    RESBUILDING_COLLECT_RESOURCE_NUM(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 世界采集任意资源达到多少
    RESBUILDING_COLLECT_ANY_RESOURCE_NUM(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 获取迷雾奖励x次
    GET_FOG_REWARD_TOTAL(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 获取xx道具xx次
    ADD_ITEM_TOTAL(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 探索迷雾x块
    UNLOCK_FOG_NUM_TOTAL(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 使用侦察机调查完毕x次
    SPY_PLANE_SURVEY_TOTAL(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 侦察机探索迷雾x次
    SPY_PLANE_EXPLORE_NUM(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),

    // 士兵击杀数量统计
    SOLDIER_KILL_TOTAL(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 世界采集资源x次
    RESBUILDING_COLLECT_RESOURCE_TIME(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 英雄升星次数
    HERO_STARUP_TIMES(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 英雄升级技能次数
    HERO_UPDATE_SKILL_TIMES(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 占领据点x次
    OCCUPY_POINT_NUM(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 占领据点x秒
    OCCUPY_POINT_TIME(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 体力消耗
    CONSUME_ENERGY_TOTAL(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 打开xx类型宝箱xx次
    OPEN_TREASURE_CHEST_TIME(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 摧毁变异人营地等级
    KILL_RALLY_MONSTER_NUM_GROUPBY_LEVEL(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 累计登录天数
    LOGIN_DAYS(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 装配配件x次
    EQUIP_ARTIFACT_TIME(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 激活套装次数x次
    ACTIVATE_SUIT_TIME(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 历史总杀敌数(pvp)
    KILL_PVP_TOTAL(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 远征次数
    EXPEDITION_NUM(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 英雄天赋点数
    HERO_TALENT_NUM(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 累计士兵升级（以被升级士兵id为key）
    SOLDIER_LEVEL_UP(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 繁荣度历史最大值
    PROPERTIES_MAX_NUM(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_MAX),
    // 探索到村庄的数量
    EXPLORE_VALLERY_NUM(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 探索到山洞的数量
    EXPLORE_CAVE_NUM(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 携带机甲出征x次，不再记录为记录项，但后端可能有数据，新增项时请考虑清档后复用59
    // DISPATCH_TROOP_WITH_MECHA_TOTAL(59, 1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 累计直充次数
    CHARGE_BASE_TIMES(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 累计购买直购礼包的次数
    BUY_GOODS_TIMES(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 通过购买直购礼包而获得的计入累充的黄金数目，需要注意的是直充获得的黄金不在这里
    BUY_GOODS_GAIN_DIAMOND(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),

    CHALLENGE_ARENA_COUNT(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    UNLOCK_EVA_SKILL(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 调查xx地图建筑xx次（按MapBuildingType分类)
    EXPLORE_WORLD_BUILDING_NUM_START(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 调查完成xx地图建筑xx次（按MapBuildingType分类)
    EXPLORE_WORLD_BUILDING_NUM_END(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 二级密码每日校验失败次数
    FAILED_TIMES_TIME(1, StatisticRefreshType.DAILY, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 开始晋升某个兵种x个
    START_LEVEL_UP_SOLDIER_NUM(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 开始研究某个科技X次
    START_LEVEL_UP_TECH_NUM(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 联盟捐献
    CLAN_TECH_DONATE(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 主动资源援助
    RESOURCE_ASSIST_NUM(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 首次绑定账号
    FIRST_ACCOUNT_BOUND(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_COVER),
    // 调查邮件奖励次数
    SURVEY_REWARD_TIMES(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 完成天网任务
    SKYNET_FINISH(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 领取天网奖励
    SKYNET_REWARD(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 参与建造时长
    JOIN_CLAN_BUILD(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 击杀特定类型的野怪的数量
    KILL_CATEGORY_MONSTER(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 占领黑暗祭坛时长
    OCCUPY_DARK(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 占领建筑类型次数
    OCCUPY_MAP_BUILDING_NUM(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 击杀怪物组次数
    KILL_MONSTER_GROUP_NUM(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 荣誉值（个人、军团、单服都共用这个）
    HONOR(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_COVER),
    // 完成防守战
    DEFEND_FINISH(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_ADD),
    // 一维统计项(无法计算与记录，只用于成就模块标识本触发类型为一维)
    CANT_RECORD_SINGLE(1, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_NONE),
    // 二维统计项(无法计算与记录，只用于成就模块标识本触发类型为二维)
    CANT_RECORD_SECOND(2, StatisticRefreshType.NO_REFRESH, CommonEnum.DataRecordCalcType.DRCT_NONE);
    /**
     * 统计维度
     */
    private final int level;

    /**
     * 刷新类型
     */
    private final StatisticRefreshType refreshType;

    private final CommonEnum.DataRecordCalcType calcType;

    /**
     * @param level       维度等级（1:无条件统计项 2:包含一个int条件的统计项）
     * @param refreshType 刷新类型（NO_REFRESH:不刷新 DAILY:日刷 WEEKLY:周刷）
     * @param calcType    计算类型(ADD:递增 SUB:递减 COVER:覆盖 MAX:取最大值)
     */
    StatisticEnum(int level, StatisticRefreshType refreshType, CommonEnum.DataRecordCalcType calcType) {
        this.level = level;
        this.refreshType = refreshType;
        this.calcType = calcType;
    }

    public int getLevel() {
        return level;
    }

    public boolean isSecondStatistic() {
        return getLevel() == 2;
    }

    public boolean isSingleStatistic() {
        return getLevel() == 1;
    }

    public StatisticRefreshType getRefreshType() {
        return refreshType;
    }

    public CommonEnum.DataRecordCalcType getCalcType() {
        return calcType;
    }

    public static com.yorha.common.enums.statistic.StatisticEnum forNumber(int ordinal) {
        for (com.yorha.common.enums.statistic.StatisticEnum value : values()) {
            if (value.ordinal() == ordinal) {
                return value;
            }
        }
        return null;
    }

}

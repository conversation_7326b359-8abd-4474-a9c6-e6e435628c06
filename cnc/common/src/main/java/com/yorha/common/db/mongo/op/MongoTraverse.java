package com.yorha.common.db.mongo.op;

import com.google.protobuf.Message;
import com.mongodb.client.model.Projections;
import com.mongodb.reactivestreams.client.FindPublisher;
import com.mongodb.reactivestreams.client.MongoDatabase;
import com.yorha.common.db.mongo.subscriber.MultiGetIBasicSubscriber;
import com.yorha.common.db.mongo.utils.PbHelper;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.op.PbFieldMetaCaches;
import com.yorha.common.db.tcaplus.option.TraverseOption;
import com.yorha.common.db.tcaplus.result.TraverseResult;
import com.yorha.common.db.tcaplus.result.ValueWithVersion;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.reactivestreams.Publisher;

import java.util.ArrayList;
import java.util.List;

/**
 * //TODO(JOSEFREN): 还没完整测试
 *
 * <AUTHOR>
 */
public class MongoTraverse<T extends Message.Builder> extends MongoOperation<T, TraverseOption<T>, Document, List<Document>, TraverseResult> {
    private static final Logger LOGGER = LogManager.getLogger(MongoOperation.class);

    public MongoTraverse(MongoDatabase database, T t, TraverseOption<T> traverseOption) {
        super(database, PbFieldMetaCaches.getMetaData(t), t, traverseOption);
    }

    @Override
    protected MultiGetIBasicSubscriber getSubscriber() {
        return new MultiGetIBasicSubscriber();
    }

    @Override
    protected Publisher<Document> getPublisher() {
        FindPublisher<Document> result = this.database.getCollection(this.getTableName()).find();
        if (this.getOption().isNeedAllFields()) {
            return result.projection(Projections.excludeId());
        }
        this.checkFieldNames(this.getOption().getFieldNames(), this.getReq().getDescriptorForType());
        return result.projection(this.buildProjection(this.getOption().getFieldNames()));
    }


    @Override
    protected TraverseResult buildResult(List<Document> documents) {
        final TcaplusErrorCode code;
        if (documents.isEmpty()) {
            code = TcaplusErrorCode.TXHDB_ERR_RECORD_NOT_EXIST;
        } else {
            code = TcaplusErrorCode.GEN_ERR_SUC;
            List<ValueWithVersion<T>> values = new ArrayList<>(documents.size());
            for (Document document : documents) {
                T proto = MongoOperation.buildDefaultValue(this.getReq());
                this.addResponseBytes(PbHelper.document2Pb(document, this.getFieldMetaData(), proto));
                ValueWithVersion<T> value = new ValueWithVersion<>();
                value.value = proto;
                values.add(value);
            }
            LOGGER.info("MongoTraverse buildResult documentSize={}", documents.size());
            this.getOption().getHandler().accept(values);
        }

        return new TraverseResult(code, this.getRequestId());
    }

    @Override
    protected TraverseResult onMongoError() {
        return new TraverseResult(DEFAULT_ERROR_CODE, this.getRequestId());
    }
}
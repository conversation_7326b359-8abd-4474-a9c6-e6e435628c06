package com.yorha.common.actor.cluster.net;


import com.yorha.common.actorservice.msg.ActorMsgEnvelope;

/**
 * 集群服务器。
 *
 * <AUTHOR>
 */
public interface IActorClusterNetServer {
    /**
     * 启动网络服务。
     */
    void start();

    /**
     * 关闭网络服务。
     */
    void shutdown();

    /**
     * 发送消息。
     *
     * @param busId    目标busId。
     * @param envelope 消息信封。
     */
    void sendMsg(String busId, ActorMsgEnvelope envelope);
}

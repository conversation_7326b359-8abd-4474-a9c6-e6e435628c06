package com.yorha.common.db.mongo.op;

import com.google.protobuf.Message;
import com.mongodb.client.model.FindOneAndUpdateOptions;
import com.mongodb.client.model.ReturnDocument;
import com.mongodb.reactivestreams.client.MongoDatabase;
import com.yorha.common.db.mongo.subscriber.NormalSubscriber;
import com.yorha.common.db.mongo.utils.DocumentHelper;
import com.yorha.common.db.mongo.utils.PbHelper;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.op.PbFieldMetaCaches;
import com.yorha.common.db.tcaplus.option.IncreaseOption;
import com.yorha.common.db.tcaplus.result.IncreaseResult;
import com.yorha.proto.CommonEnum;
import org.bson.Document;
import org.reactivestreams.Publisher;

/**
 * <AUTHOR>
 */
public class MongoIncrease<T extends Message.Builder> extends MongoOperation<T, IncreaseOption, Document, Document, IncreaseResult<T>> {
    /**
     * 获取更新后数据
     */
    static final FindOneAndUpdateOptions GET_LATEST_DATA = new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER);
    /**
     * 获取更新前数据
     */
    static final FindOneAndUpdateOptions GET_OLD_DATA = new FindOneAndUpdateOptions().returnDocument(ReturnDocument.BEFORE);

    public MongoIncrease(MongoDatabase database, T t, IncreaseOption increaseOption) {
        super(database, PbFieldMetaCaches.getMetaData(t), t, increaseOption);
    }

    @Override
    protected NormalSubscriber<Document> getSubscriber() {
        return new NormalSubscriber<>();
    }

    @Override
    protected Publisher<Document> getPublisher() {
        var filter = DocumentHelper.formKey(this.getReq());
        var update = DocumentHelper.formIncrease(this.getReq());
        return this.database.getCollection(this.getTableName()).findOneAndUpdate(filter, update, formOption(this.getOption()));

    }

    private static FindOneAndUpdateOptions formOption(final IncreaseOption increaseOption) {
        final int resultFlag = increaseOption.getResultFlag();
        if (resultFlag == CommonEnum.TcaplusResultFlag.RESULT_FLAG_FIELDS_ALL_OLD_VALUE) {
            return GET_OLD_DATA;
        }
        return GET_LATEST_DATA;
    }

    @Override
    protected IncreaseResult<T> buildResult(Document document) {
        T proto = MongoOperation.buildDefaultValue(this.getReq());
        final IncreaseResult<T> result = new IncreaseResult<T>();
        result.code = TcaplusErrorCode.TXHDB_ERR_RECORD_NOT_EXIST;
        if (document != null) {
            this.addResponseBytes(PbHelper.document2Pb(document, this.getFieldMetaData(), proto));
            result.code = TcaplusErrorCode.GEN_ERR_SUC;
        }
        result.value = proto;
        return result;
    }

    @Override
    protected IncreaseResult<T> onMongoError() {
        final IncreaseResult<T> result = new IncreaseResult<T>();
        result.code = DEFAULT_ERROR_CODE;
        result.value = MongoOperation.buildDefaultValue(this.getReq());
        return result;
    }
}

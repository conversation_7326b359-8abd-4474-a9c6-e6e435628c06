package com.yorha.common.db.mongo.op;

import com.google.protobuf.Message;
import com.mongodb.client.model.FindOneAndUpdateOptions;
import com.mongodb.client.model.ReturnDocument;
import com.mongodb.reactivestreams.client.MongoDatabase;
import com.yorha.common.db.mongo.subscriber.NormalSubscriber;
import com.yorha.common.db.mongo.utils.DocumentHelper;
import com.yorha.common.db.mongo.utils.PbHelper;
import com.yorha.common.db.tcaplus.op.PbFieldMetaCaches;
import com.yorha.common.db.tcaplus.option.UpsertOption;
import com.yorha.common.db.tcaplus.result.UpsertResult;
import com.yorha.proto.CommonEnum;
import org.bson.Document;
import org.reactivestreams.Publisher;

/**
 * <AUTHOR>
 */
public class MongoUpsert<T extends Message.Builder> extends MongoOperation<T, UpsertOption, Document, Document, UpsertResult<T>> {
    /**
     * 获取更新后数据
     */
    static final FindOneAndUpdateOptions GET_LATEST_DATA = new FindOneAndUpdateOptions().upsert(true).returnDocument(ReturnDocument.AFTER);
    /**
     * 获取更新前数据
     */
    static final FindOneAndUpdateOptions GET_OLD_DATA = new FindOneAndUpdateOptions().upsert(true).returnDocument(ReturnDocument.BEFORE);

    public MongoUpsert(MongoDatabase database, T t, UpsertOption upsertOption) {
        super(database, PbFieldMetaCaches.getMetaData(t), t, upsertOption);
    }

    @Override
    protected NormalSubscriber<Document> getSubscriber() {
        return new NormalSubscriber<>();
    }

    @Override
    protected Publisher<Document> getPublisher() {
        var filter = DocumentHelper.formKey(this.getReq());
        var update = DocumentHelper.formUpdate(this.getReq());
        return this.database.getCollection(this.getTableName()).findOneAndUpdate(filter, update, formOption(this.getOption()));

    }

    private static FindOneAndUpdateOptions formOption(final UpsertOption upsertOption) {
        final int resultFlag = upsertOption.getResultFlag();
        if (resultFlag == CommonEnum.TcaplusResultFlag.RESULT_FLAG_FIELDS_ALL_OLD_VALUE) {
            return GET_OLD_DATA;
        }
        return GET_LATEST_DATA;
    }

    @Override
    protected UpsertResult<T> buildResult(Document document) {
        T proto = MongoOperation.buildDefaultValue(this.getReq());
        final UpsertResult<T> result = new UpsertResult<>();
        if (document != null) {
            this.addResponseBytes(PbHelper.document2Pb(document, this.getFieldMetaData(), proto));
        }

        result.value = proto;
        return result;
    }

    @Override
    protected UpsertResult<T> onMongoError() {
        final UpsertResult<T> result = new UpsertResult<>();
        result.code = DEFAULT_ERROR_CODE;
        result.value = MongoOperation.buildDefaultValue(this.getReq());
        return result;
    }
}

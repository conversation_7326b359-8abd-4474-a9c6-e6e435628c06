package com.yorha.common.db.tcaplus.result;

import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.msg.GameDbResp;

/**
 * 遍历请求的最终结果。
 *
 * <AUTHOR>
 */
public class TraverseResult implements GameDbResp {
    private final TcaplusErrorCode code;
    private final long requestId;

    public TraverseResult(TcaplusErrorCode code, final long requestId) {
        this.code = code;
        this.requestId = requestId;
    }

    /**
     * @return 请求requestId。
     */
    public long getRequestId() {
        return requestId;
    }


    /**
     * @return 错误码。
     */
    @Override
    public int getCode() {
        return code.getValue();
    }


    @Override
    public String toString() {
        return "TraverseResult{" +
                "code=" + code +
                ", requestId=" + requestId +
                '}';
    }
}

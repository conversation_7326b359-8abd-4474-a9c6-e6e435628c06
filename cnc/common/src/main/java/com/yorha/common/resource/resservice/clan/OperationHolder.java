package com.yorha.common.resource.resservice.clan;

import java.util.Collections;
import java.util.Set;

/**
 * 操作信息
 */
public class OperationHolder {
    /**
     * 最大允许角色
     */
    public final Set<Integer> maxAllowRoles;
    /**
     * 最小允许角色
     */
    public final Set<Integer> minAllowRoles;
    /**
     * 默认允许角色
     */
    public final Set<Integer> defaultAllowRoles;

    OperationHolder(Set<Integer> maxAllowRoles, Set<Integer> minAllowRoles, Set<Integer> defaultAllowRoles) {
        this.maxAllowRoles = Collections.unmodifiableSet(maxAllowRoles);
        this.minAllowRoles = Collections.unmodifiableSet(minAllowRoles);
        this.defaultAllowRoles = Collections.unmodifiableSet(defaultAllowRoles);
    }
}

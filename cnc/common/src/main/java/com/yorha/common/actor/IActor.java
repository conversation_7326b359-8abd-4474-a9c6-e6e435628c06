package com.yorha.common.actor;

import java.util.Map;

public interface IActor {
    /**
     * 热更示例
     * RefFactory.ofNewDungeon(1).tell(new ActorRunnable<IActor>("a", actor -> {
     * HashMap<Object, Object> map = new HashMap<>();
     * map.put("str1", "123123123");
     * map.put("int1", 99999);
     * actor.patchMethod("热更新方法", map);
     * }));
     */
    Map<Object, Object> patchMethod(String str, Map<Object, Object> map);
}

package com.yorha.common.resource.resservice.scene;

import com.google.common.collect.Maps;
import com.yorha.common.achievement.AchievementHelper;
import com.yorha.common.enums.statistic.StatisticEnum;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import com.yorha.common.resource.resservice.item.ItemResService;
import com.yorha.proto.CommonEnum;
import res.template.AchievementConfigTemplate;
import res.template.DemandConfigTemplate;
import res.template.MonsterGroupTemplate;

import javax.annotation.Nullable;
import java.util.*;

/**
 * 成就系统
 *
 * <AUTHOR>
 */
public class AchievementResService extends AbstractResService {

    /**
     * 触发类型->关心该触发类型的需求list
     */
    private final Map<CommonEnum.AchievementStatisticCheckerType, List<DemandConfigTemplate>> checkerType2Demand = Maps.newHashMap();
    /**
     * 需求id->成就
     */
    private final Map<Integer, AchievementConfigTemplate> demand2Achievement = Maps.newHashMap();
    /**
     * 野怪配置id->成就野怪组id
     */
    private final Map<Integer, Integer> monster2GroupId = Maps.newHashMap();

    public AchievementResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {
        final Collection<AchievementConfigTemplate> achievementConfigTemplates = getResHolder().getListFromMap(AchievementConfigTemplate.class);
        final Collection<DemandConfigTemplate> demandConfigTemplates = getResHolder().getListFromMap(DemandConfigTemplate.class);
        final Collection<MonsterGroupTemplate> monsterGroupTemplates = getResHolder().getListFromMap(MonsterGroupTemplate.class);
        for (AchievementConfigTemplate achievementConfigTemplate : achievementConfigTemplates) {
            for (int demand : achievementConfigTemplate.getDemandsList()) {
                demand2Achievement.put(demand, achievementConfigTemplate);
            }
        }
        for (DemandConfigTemplate demandConfigTemplate : demandConfigTemplates) {
            checkerType2Demand.computeIfAbsent(demandConfigTemplate.getCheckType(), key -> new ArrayList<>()).add(demandConfigTemplate);
        }
        for (MonsterGroupTemplate monsterGroupTemplate : monsterGroupTemplates) {
            for (int monster : monsterGroupTemplate.getMonsterListList()) {
                monster2GroupId.put(monster, monsterGroupTemplate.getId());
            }
        }
    }

    @Override
    public void checkValid() throws ResourceException {
        final Map<Integer, DemandConfigTemplate> demandConfigTemplates = getResHolder().getMap(DemandConfigTemplate.class);
        final ItemResService itemResService = getResHolder().innerGetResService(ItemResService.class);
        final Collection<AchievementConfigTemplate> achievementConfigTemplates = getResHolder().getListFromMap(AchievementConfigTemplate.class);
        // 成就表校验
        for (Map.Entry<Integer, AchievementConfigTemplate> entry : demand2Achievement.entrySet()) {
            final AchievementConfigTemplate achievementConfigTemplate = entry.getValue();
            if (!demandConfigTemplates.containsKey(entry.getKey())) {
                throw new ResourceException("C_成就, 成就id={} 中的需求id={} 在需求表中不存在", achievementConfigTemplate.getId(), entry.getKey());
            }
        }
        for (AchievementConfigTemplate achievementConfigTemplate : achievementConfigTemplates) {
            if (achievementConfigTemplate.getRewardPairList().size() <= 0) {
                throw new ResourceException("C_成就, 成就id={} 奖励没配", achievementConfigTemplate.getId());
            }
            for (IntPairType intPairType : achievementConfigTemplate.getRewardPairList()) {
                if (!itemResService.isItemExists(intPairType.getKey())) {
                    throw new ResourceException("C_成就, 成就id={} 中奖励id={}不存在", achievementConfigTemplate.getId(), intPairType.getKey());
                }
                if (intPairType.getValue() <= 0) {
                    throw new ResourceException("C_成就, 成就id={} 中奖励id={}数量<=0", achievementConfigTemplate.getId(), intPairType.getKey());
                }
            }
        }
        // 需求配置表校验
        for (DemandConfigTemplate demandConfigTemplate : demandConfigTemplates.values()) {
            final StatisticEnum statisticEnum = AchievementHelper.checkType2StatisticEnum(demandConfigTemplate.getCheckType());
            if (statisticEnum == null) {
                throw new ResourceException("C_成就, 校验类型={}, 没有绑定统计项 id={}", demandConfigTemplate.getCheckType(), demandConfigTemplate.getId());
            }
            // 统计项维度
            final int level = statisticEnum.getLevel();
            // 需求的配置参数数量必须与对应统计项的维度相同
            if (demandConfigTemplate.getParamList().size() != level) {
                throw new ResourceException("C_成就, 参数不符合规则 id={}", demandConfigTemplate.getId());
            }
            // 校验类型没配置
            if ((demandConfigTemplate.getCheckType() == null) || (demandConfigTemplate.getCheckType() == CommonEnum.AchievementStatisticCheckerType.ASCT_NONE)) {
                throw new ResourceException("C_成就, 校验类型没配置 id={}", demandConfigTemplate.getId());
            }
            // 需求类型与成就类型匹配校验
            final AchievementConfigTemplate achievementConfigTemplate = demand2Achievement.get(demandConfigTemplate.getId());
            if (achievementConfigTemplate == null) {
                throw new ResourceException("C_成就, 需求id={} 没有绑定成就", demandConfigTemplate.getId());
            }
            switch (achievementConfigTemplate.getType()) {
                case ACHT_PLAYER: {
                    if (demandConfigTemplate.getType() != CommonEnum.AchievementDemandType.ACHST_PLAYER) {
                        throw new ResourceException("C_成就, 成就类型为玩家个人的成就只可以绑定玩家个人的成就需求 成就id={}, 需求id={}", achievementConfigTemplate.getId(), demandConfigTemplate.getId());
                    }
                    break;
                }
                default:
                    throw new ResourceException("C_成就, 成就类型不符合规则 成就id={}", achievementConfigTemplate.getId());
            }
        }
    }

    /**
     * 根据需求id，获得属于的成就配置
     *
     * @param demandId 需求id
     * @return 成就配置
     */
    @Nullable
    public AchievementConfigTemplate getAchievementConfigTemplate(int demandId) {
        return demand2Achievement.get(demandId);
    }

    /**
     * 根据checkType, 获得关心本次触发的需求表
     *
     * @param checkerType 触发类型
     * @return 关心本次触发的需求表
     */
    public List<DemandConfigTemplate> getDemands(CommonEnum.AchievementStatisticCheckerType checkerType) {
        return checkerType2Demand.getOrDefault(checkerType, Collections.emptyList());
    }

    /**
     * 获取成就野怪组
     * 对于不在任意组的野怪返回0
     */
    public int getGroupId(int monsterId) {
        return monster2GroupId.getOrDefault(monsterId, 0);
    }
}

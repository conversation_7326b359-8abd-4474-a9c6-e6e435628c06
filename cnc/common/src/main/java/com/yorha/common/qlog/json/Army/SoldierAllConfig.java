package com.yorha.common.qlog.json.Army;

import com.yorha.game.gen.prop.SoldierProp;

/**
 * <AUTHOR>
 */
public class SoldierAllConfig {
    private final int soldierId;
    private final int num;
    private final int slightNum;
    private final int severeNum;
    private final int deadNum;

    public SoldierAllConfig(SoldierProp soldierProp) {
        this.soldierId = soldierProp.getSoldierId();
        this.num = soldierProp.getNum();
        this.slightNum = soldierProp.getSlightWoundNum();
        this.severeNum = soldierProp.getSevereWoundNum();
        this.deadNum = soldierProp.getDeadNum();
    }

    public int getSoldierId() {
        return soldierId;
    }

    public int getNum() {
        return num;
    }

    public int getSlightNum() {
        return slightNum;
    }

    public int getSevereNum() {
        return severeNum;
    }

    public int getDeadNum() {
        return deadNum;
    }

    @Override
    public String toString() {
        return "Soldier{" +
                "id=" + soldierId +
                ", num=" + num +
                ", slight=" + slightNum +
                ", severe=" + severeNum +
                ", dead=" + deadNum +
                '}';
    }
}

package com.yorha.common.aoiView.manager;

import com.yorha.common.actor.IActorRef;
import com.yorha.common.aoiView.aoigrid.AoiGrid;
import com.yorha.common.utils.shape.AABB;

import java.util.Collections;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface AoiObserver {
    /**
     * id
     * 目前只有sceneplayer用  observe未实现
     */
    long getId();

    /**
     * 该观察者的zone
     */
    int getSceneZoneId();

    IActorRef getSessionRef();

    //****************缩略层使用*****************//

    /**
     * 设置新的视野  缩略层使用
     */
    void setNewView(Set<AoiGrid> curAoiGrids, int layer);

    Set<AoiGrid> getOldAoiGrid();

    int getOldLayer();

    //****************普通层使用*****************//

    void setUpdateView(AABB aabb, int layer, int entityNumMax);

    int getEntityNumMax();

    AABB getNewAABB();

    int getNewLayer();

    /**
     * 获取看到的视野内物体
     */
    Set<Long> getCurViewObj();

    /**
     * 设置看到的视野内物体
     */
    void setCurViewObj(Set<Long> obj);

    void addViewObj(long objId);

    void removeViewObj(long objId);

    default Set<Long> getObTargetSet() {
        return Collections.emptySet();
    }

}

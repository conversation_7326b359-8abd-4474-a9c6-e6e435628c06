package com.yorha.common.db.tcaplus;

import com.google.protobuf.Message;
import com.tencent.tcaplus.client.Client;
import com.tencent.tcaplus.client.ClientFactory;
import com.tencent.tcaplus.common.BaseCfg;
import com.yorha.common.concurrent.executor.ConcurrentHelper;
import com.yorha.common.db.IDbOperation;
import com.yorha.common.db.IDbOperationFactory;
import com.yorha.common.db.tcaplus.op.*;
import com.yorha.common.db.tcaplus.option.*;
import com.yorha.common.db.tcaplus.result.*;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.server.config.ClusterConfigUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * tcaplus操作工厂
 *
 * <AUTHOR>
 */
public class TcaplusOperationFactory implements IDbOperationFactory {
    private static final Logger LOGGER = LogManager.getLogger(TcaplusOperationFactory.class);

    private final Client client;

    public TcaplusOperationFactory() {
        final int appId = ClusterConfigUtils.getWorldConfig().getIntItem("tcaplus_app_id");
        final int zoneId = ClusterConfigUtils.getWorldConfig().getIntItem("tcaplus_zone_id");
        final String password = ClusterConfigUtils.getWorldConfig().getStringItem("tcaplus_password");
        final List<String> addressList = ClusterConfigUtils.getWorldConfig().getListStrItem("tcaplus_address_list");
        LOGGER.info("TcaplusOperationFactory appId={} zoneId={} password={} addressList={}", appId, zoneId, password, addressList);
        // 设置Tcaplus默认的消息超时时间，单位：ms
        BaseCfg.msgTimeout = TcaplusUtils.TCAPLUS_TIMEOUT_MS;
        this.client = ClientFactory.createClient(appId, zoneId, password, addressList, 4, 5000);
    }

    @Override
    public <T extends Message.Builder> IDbOperation<T, GetByPartKeyOption, GetByPartKeyResult<T>> getByPartKeyOperation(T dbData, GetByPartKeyOption option) {
        return new TcaplusGetByPartKey<>(client, dbData, option);
    }

    @Override
    public <T extends Message.Builder> IDbOperation<T, UpdateOption, UpdateResult<T>> updateOperation(T dbData, UpdateOption option) {
        return new TcaplusUpdate<>(client, dbData, option);
    }

    @Override
    public void checkTable() {
        // 检测tcaplus表数据结构
        FutureTask<Boolean> futureTask = new FutureTask<>(TcaplusCheckHelper::checkTcaplusTable);
        ConcurrentHelper.newFiber("check#TcaplusTableDir", futureTask).start();
        try {
            boolean result = futureTask.get(10, TimeUnit.SECONDS);
            if (result) {
                throw new GeminiException("checkDb fail, error exists");
            }
        } catch (InterruptedException e) {
            throw new GeminiException("checkDb interrupted!");
        } catch (ExecutionException e) {
            throw new GeminiException("", e);
        } catch (TimeoutException e) {
            throw new GeminiException("checkDb timeout!");
        }
    }

    @Override
    public <T extends Message.Builder> IDbOperation<T, InsertOption, InsertResult<T>> insertOperation(T dbData, InsertOption option) {
        return new TcaplusInsert<>(client, dbData, option);
    }

    @Override
    public <T extends Message.Builder> IDbOperation<T, GetOption, GetResult<T>> getOperation(T dbData, GetOption option) {
        return new TcaplusGet<>(client, dbData, option);
    }

    @Override
    public <T extends Message.Builder> IDbOperation<T, UpsertOption, UpsertResult<T>> upsertOperation(T dbData, UpsertOption option) {
        return new TcaplusUpsert<>(client, dbData, option);
    }

    @Override
    public <T extends Message.Builder> IDbOperation<T, IncreaseOption, IncreaseResult<T>> increaseOperation(T dbData, IncreaseOption option) {
        return new TcaplusIncrease<>(client, dbData, option);
    }

    @Override
    public <T extends Message.Builder> IDbOperation<T, DeleteOption, DeleteResult> deleteOperation(T dbData, DeleteOption option) {
        return new TcaplusDelete<>(client, dbData, option);
    }

    @Override
    public <T extends Message.Builder> IDbOperation<T, DeleteByPartKeyOption, DeleteByPartKeyResult> deleteByPartKeyOperation(T dbData, DeleteByPartKeyOption option) {
        return new TcaplusDeleteByPartKey<>(client, dbData, option);
    }

    @Override
    public <T extends Message.Builder> IDbOperation<T, GetMetaOption, GetMetaResult> getMetaOperation(T dbData, GetMetaOption option) {
        return new TcaplusGetMeta<>(client, dbData, option);
    }

    @Override
    public <T extends Message.Builder> IDbOperation<T, TraverseOption<T>, TraverseResult> traverseOperation(T dbData, TraverseOption<T> option) {
        return new TcaplusTableTraverse<>(client, dbData, option);
    }

    @Override
    public <T extends Message.Builder> IDbOperation<Collection<T>, BatchGetOption, BatchGetResult<T>> batchGetOperation(Collection<T> dbData, BatchGetOption option) {
        return new TcaplusBatchGet<>(client, dbData, option);
    }

    @Override
    public void close() {
        ClientFactory.destroyClient(client);
    }
}

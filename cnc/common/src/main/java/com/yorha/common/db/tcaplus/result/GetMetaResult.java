package com.yorha.common.db.tcaplus.result;

import com.tencent.tcaplus.client.impl.TableMetadata;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.msg.GameDbResp;


public class GetMetaResult implements GameDbResp {
    public TcaplusErrorCode code = TcaplusErrorCode.GEN_ERR_SUC;
    public TableMetadata value;

    @Override
    public int getCode() {
        return code.getValue();
    }

    @Override
    public boolean isOk() {
        return code == TcaplusErrorCode.GEN_ERR_SUC;
    }

    public boolean isTableNotExist() {
        return code == TcaplusErrorCode.PROXY_ERR_ROUTE_MSG;
    }


    @Override
    public String toString() {
        return String.format("GetMetaResult{code=%d}", getCode());
    }
}

package com.yorha.common.db.mongo.subscriber;

/**
 * 除findMany外所有操作的subscriber都应继承本class
 *
 * <AUTHOR>
 */
public class NormalSubscriber<Response> extends IBasicSubscriber<Response, Response> {


    public NormalSubscriber() {
    }


    @Override
    public void onNext(Response response) {
        // 接收结果
        received = response;
    }


    @Override
    public void onError(final Throwable t) {
        error = t;
        onComplete();
    }
    

}



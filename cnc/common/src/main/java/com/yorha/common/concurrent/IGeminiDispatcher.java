package com.yorha.common.concurrent;

/**
 * 任务派发器，选择合适的任务消费者执行任务
 *
 * <AUTHOR>
 */
@Deprecated
public interface IGeminiDispatcher {
    /**
     * 投递任务
     *
     * @param runnable 带名字和标识id的runnable
     */
    void execute(NamedRunnableWithId runnable);

    /**
     * 获取任务统计
     *
     * @return 统计任务
     */
    TaskStats getTaskStats();

    /**
     * 关闭GeminiDispatcher
     */
    void shutdown();
}

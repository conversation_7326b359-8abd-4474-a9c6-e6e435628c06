package com.yorha.common.enums.reason;

import com.yorha.common.exception.ResourceException;

import javax.annotation.Nullable;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

/**
 * 士兵数量变更
 * <p>
 * java内部枚举，id保持自增即可
 */
public enum SoldierNumChangeReason {
    /**
     * gm
     */
    gm(1, true),
    /**
     * 部队训练
     */
    train_army(2, true),
    /**
     * 晋升
     */
    update_army(3, true),
    /**
     * 被晋升
     */
    be_upgraded(4, false),
    /**
     * 从重伤中恢复（医院治疗）
     */
    serious_recovered(5, true),
    /**
     * 手动遣散
     */
    manual_severance(6, true),
    /**
     * 取消升级
     */
    cancel_level_up(7, false),
    /**
     * 轻伤
     */
    slight_injury(8, false),
    /**
     * 重伤
     */
    serious_injury(9, true),
    /**
     * 重伤不治身亡
     */
    serious_injury_dead(10, true),
    /**
     * 战斗中直接死亡
     */
    battle_dead(11, true),
    /**
     * 轻伤自动复活
     */
    slight_injury_recovered(12, false),
    /**
     * 创建行军
     */
    army_out(13, false),
    /**
     * 行军回城
     */
    army_return(14, false),
    /**
     * 中途加入
     */
    army_cut_in(15, false),
    /**
     * 中途退出
     */
    army_cut_out(16, false),

    /**
     * 使用加士兵道具
     */
    use_soldier_item(17, true),
    /**
     * 新号给兵
     */
    init(18, true),
    /**
     * idip
     */
    idip(19, true),
    /**
     * 英灵殿固定返兵
     */
    valhalla_fix_return(20, true),
    /**
     * 英灵殿额外返兵
     */
    valhalla_extra_return(21, true),
    /**
     * 未知
     */
    unknown(999, true),
    ;

    private final boolean needUpdatePower;

    private final int id;

    SoldierNumChangeReason(int id, boolean needUpdatePower) {
        this.id = id;
        this.needUpdatePower = needUpdatePower;
    }

    public boolean isNeedUpdatePower() {
        return needUpdatePower;
    }

    public int getId() {
        return id;
    }

    public static Map<Integer, SoldierNumChangeReason> id2Enum = new HashMap<>();


    static {
        for (SoldierNumChangeReason reason : values()) {
            id2Enum.put(reason.getId(), reason);
        }
    }

    /**
     * 检查枚举是否合法
     *
     * @throws ResourceException 枚举重复
     */
    public static void checkValid() throws ResourceException {
        HashSet<Integer> ids = new HashSet<>();
        for (SoldierNumChangeReason reason : values()) {
            if (ids.contains(reason.getId())) {
                throw new ResourceException("后台枚举 SoldierNumChangeReason id 重复，SoldierNumChangeReason id {} duplicate", reason.getId());
            }
            ids.add(reason.getId());
        }
    }

    /**
     * 根据id获取枚举
     *
     * @param id 枚举id
     * @return 如果枚举id对应枚举不存在，返回null
     */
    @Nullable
    public static SoldierNumChangeReason valueOf(@Nullable Integer id) {
        if (id == null) {
            return null;
        }
        if (id2Enum.containsKey(id)) {
            return id2Enum.get(id);
        }
        return null;
    }
}

package com.yorha.common.exception;

import com.yorha.gemini.utils.StringUtils;

import java.io.Serial;

/**
 * Gemini异常，包含错误码
 * 0成功
 * 1失败（后台内部异常）
 * 其他数字（逻辑异常，需要发送客户端错误码并多语言翻译提示）
 *
 * <AUTHOR>
 */
public class GeminiException extends RuntimeException {
    @Serial
    private static final long serialVersionUID = 1L;

    private final int code;
    private static final int ERROR_ID_FAILED = 1;

    /**
     * -------------------------- framework -------------------------
     */
    public GeminiException(String msg) {
        super(msg);
        this.code = ERROR_ID_FAILED;
    }

    public GeminiException(String msgPattern, Object... arguments) {
        super(StringUtils.format(msgPattern, arguments));
        this.code = ERROR_ID_FAILED;
    }

    public GeminiException(String msg, Throwable cause) {
        super(msg, cause, true, false);
        this.code = ERROR_ID_FAILED;
    }

    public GeminiException(String msg, Throwable cause, int codeId) {
        super(msg, cause, true, false);
        this.code = codeId;
    }

    /**
     * -------------------------- logic -------------------------
     */
    public GeminiException(int code) {
        super();
        this.code = code;
    }

    public GeminiException(int code, String msg) {
        super(msg);
        this.code = code;
    }

    public GeminiException(IErrorCode errorCode) {
        super();
        this.code = errorCode.getCodeId();
    }

    public GeminiException(IErrorCode errorCode, String msg) {
        super(msg);
        this.code = errorCode.getCodeId();
    }

    public GeminiException(IErrorCode errorCode, String msgPattern, Object... arguments) {
        super(StringUtils.format(msgPattern, arguments));
        this.code = errorCode.getCodeId();
    }

    @Override
    public String getMessage() {
        String detailMessage = super.getMessage();
        if (detailMessage == null) {
            detailMessage = "";
        }
        IErrorCode geminiErrorCode = GeminiErrorCodeManager.getGeminiErrorCode(this.code);
        return StringUtils.format("[codeId={}] {}", geminiErrorCode, detailMessage);
    }

    public int getCodeId() {
        return code;
    }

    /**
     * 是否是后台错误，即非业务错误码
     */
    public boolean isFailed() {
        return this.code == ERROR_ID_FAILED;
    }

    public static boolean isLogicException(Throwable t) {
        // 是GeminiException且不是Failed，说明是内部错误码
        return t instanceof GeminiException && !((GeminiException) t).isFailed();
    }
}

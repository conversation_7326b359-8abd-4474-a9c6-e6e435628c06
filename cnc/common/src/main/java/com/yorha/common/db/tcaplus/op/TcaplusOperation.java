package com.yorha.common.db.tcaplus.op;

import com.google.protobuf.Descriptors;
import com.google.protobuf.Message;
import com.tencent.tcaplus.client.Client;
import com.tencent.tcaplus.client.Record;
import com.tencent.tcaplus.client.Request;
import com.tencent.tcaplus.client.Response;
import com.yorha.common.db.IDbOperation;
import com.yorha.common.db.tcaplus.PbDynamicHandler;
import com.yorha.common.db.tcaplus.TcaplusDBException;
import com.yorha.common.db.tcaplus.TcaplusUtils;
import com.yorha.common.utils.ClassNameCacheUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nonnull;
import java.util.List;
import java.util.function.Consumer;

/**
 * tcaplus操作的基类，所有tcaplus操作的封装都源自于此
 *
 * <AUTHOR>
 */
public abstract class TcaplusOperation<Req, Option, Result> extends IDbOperation<Req, Option, Result> {
    private static final Logger LOGGER = LogManager.getLogger(TcaplusOperation.class);

    /**
     * 底层的Tcaplus 封装的Fiber Api
     */
    protected final Client client;

    public TcaplusOperation(Client client, FieldMetaData fieldMetaData, Req req, Option option) {
        super(req, option, fieldMetaData);
        this.client = client;
    }

    /**
     * 操作类型，用于构建请求包头
     *
     * @return 操作类型
     */
    protected abstract int getType();

    /**
     * 设置Request请求的包头
     *
     * @param request 待设置的请求
     */
    protected abstract void configRequestProperty(Request request);

    /**
     * 工厂方法，从Db的Response构建一个Result结果
     *
     * @param response Db返回包
     * @return Result类型对象
     */
    protected abstract Result buildResult(Response response);

    protected void setRequestKeys(Message.Builder req, Record record) {
        List<Descriptors.FieldDescriptor> keyFields = this.getFieldMetaData().keyFieldsList;
        for (Descriptors.FieldDescriptor field : keyFields) {
            if (req.hasField(field)) {
                this.addRequestBytes(PbDynamicHandler.setKeyField(record, field, req.getField(field)));
            }
        }
        LOGGER.debug("operate={},type={},tableName={},param=\n{{}}", ClassNameCacheUtils.getSimpleName(getClass()), getType(), this.getTableName(), req);
    }

    protected void setRequestValues(Message.Builder req, Record record) {
        List<Descriptors.FieldDescriptor> valueFields = this.getFieldMetaData().valueFieldsList;
        for (Descriptors.FieldDescriptor field : valueFields) {
            if (!req.hasField(field)) {
                if (field.hasOptionalKeyword()) {
                    continue;
                }
                throw new TcaplusDBException(StringUtils.format("{} request has no field {}",
                        this.getTableName(), field.getName()));
            }
            this.addRequestBytes(PbDynamicHandler.setValueField(record, field, req.getField(field)));
        }
    }

    /**
     * 从Response的Record里读取数据
     *
     * @param record 数据库中读取到的记录
     * @param value  待返回的结果
     */
    protected void readFromResponseValues(Record record, Message.Builder value) {
        for (Descriptors.FieldDescriptor desc : this.getFieldMetaData().keyFieldsList) {
            if (record.getKeyMap().containsKey(desc.getName())) {
                this.addResponseBytes(PbDynamicHandler.getKeyField(record, desc, value));
            }
        }
        for (Descriptors.FieldDescriptor desc : this.getFieldMetaData().valueFieldsList) {
            if (record.getValueMap().containsKey(desc.getName())) {
                this.addResponseBytes(PbDynamicHandler.getValueField(record, desc, value));
            }
        }
    }

    /**
     * 为tcaplus request添加指定拉取的字段
     *
     * @param fieldNames        pb字段名
     * @param requestDescriptor pb结构descriptor
     * @param request           tcaplus请求
     */
    protected void addRequestedFields(@Nonnull final List<String> fieldNames, @Nonnull final Descriptors.Descriptor requestDescriptor, @Nonnull final Request request) {
        for (final String fieldName : fieldNames) {
            if (requestDescriptor.findFieldByName(fieldName) == null) {
                throw new TcaplusDBException(StringUtils.format("addRequestedFields tableName={} unknown fieldName={}", this.getTableName(), fieldName));
            }
            request.addFieldName(fieldName);
            this.addRequestBytes(fieldName.length());
        }
    }

    /**
     * 驱动Operation进行执行的的主体执行流函数
     *
     * @return 执行结果
     */
    @Override
    public Result run() {
        this.onDbRequest();
        final Request request = this.client.acquireRequest();
        request.setCmd(getType());
        request.setTableName(getFieldMetaData().tableName);
        configRequestProperty(request);
        // 处理响应
        Response response = TcaplusUtils.requestResponse(this.client, request, TcaplusUtils.TCAPLUS_TIMEOUT_MS + 1_000);
        Result result = buildResult(response);
        this.onDbResponse(response.getResult());
        return result;
    }

    /**
     * 驱动Operation异步执行的的主体执行流函数.
     *
     * @param cb 回调.
     */
    @Override
    public void runAsync(Consumer<Result> cb) {
        this.onDbRequest();
        final Request request = this.client.acquireRequest();
        request.setCmd(getType());
        request.setTableName(getTableName());
        configRequestProperty(request);
        if (request.getMultiResponseFlag() == 1) {
            throw new RuntimeException("multiple response");
        }
        final long tcaplusRealStartTsMs = SystemClock.nowNative();
        TcaplusUtils.requestResponseAsync(client, request, (response) -> {
            // r = getResultFromMock(r);
            long tcaplusFutureStartTsMs = SystemClock.nowNative();
            // tcaplus发送到回调触发的耗时
            long tcaplusRealCostMs = tcaplusFutureStartTsMs - tcaplusRealStartTsMs;
            if (tcaplusRealCostMs > 100) {
                LOGGER.warn("gemini_perf TcaplusUtils requestResponseAsync, cmd={}, tableName={}, realCost={}ms",
                        request.getCmd(), request.getTableName(), tcaplusRealCostMs);
            }
            Result result = buildResult(response);
            this.onDbResponse(response.getResult());
            cb.accept(result);
        });

    }

    /**
     * 设置默认值的工具方法
     *
     * @param req 请求
     * @param <T> 待返回类型
     * @return T类型返回对象
     */
    @SuppressWarnings("unchecked")
    protected static <T extends Message.Builder> T buildDefaultValue(T req) {
        return (T) req.getDefaultInstanceForType().newBuilderForType();
    }

}

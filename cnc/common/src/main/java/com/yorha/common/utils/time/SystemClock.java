package com.yorha.common.utils.time;

import com.yorha.common.concurrent.NameableThreadFactory;
import com.yorha.common.concurrent.NamedRunnable;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 系统时钟
 *
 * <AUTHOR>
 */
public class SystemClock {
    private static final Logger LOGGER = LogManager.getLogger(SystemClock.class);

    private static final ScheduledThreadPoolExecutor SYSTEM_CLOCK_POOL = new ScheduledThreadPoolExecutor(1, new NameableThreadFactory("SystemClock", true));

    /**
     * 时钟更新间隔，单位毫秒
     */
    private final long period;
    /**
     * 时间偏移值
     */
    private final AtomicLong offset;

    //------------------------------------------------------------------------ static
    /**
     * 现在时刻的毫秒数
     */
    private volatile long now;

    /**
     * 当前时间
     */
    public static long now() {
        return InstanceHolder.INSTANCE.currentTimeMillis();
    }

    public static Instant nowInstant() {
        return Instant.ofEpochMilli(now());
    }

    public static ZonedDateTime nowZonedDateTime() {
        return nowInstant().atZone(TimeUtils.getZoneId());
    }

    /**
     * 当前秒数
     */
    public static long nowOfSeconds() {
        return TimeUtils.ms2Second(now());
    }

    /**
     * 系统真实时间
     *
     * @return 无视偏移量
     */
    public static long nowNative() {
        return System.currentTimeMillis();
    }

    public static long nowOfSecondsNative() {
        return TimeUtils.ms2Second(nowNative());
    }

    /**
     * 系统真实纳秒
     *
     * @return 无视偏移量
     */
    public static long nanoTimeNative() {
        return System.nanoTime();
    }

    /**
     * 构造
     *
     * @param period 时钟更新间隔，单位毫秒
     */
    private SystemClock(long period) {
        this.period = period;
        this.offset = new AtomicLong(0L);
        this.now = System.currentTimeMillis() + offset.get();
        scheduleClock();
    }

    public static void updateOffset(long millis, boolean init) {
        InstanceHolder.INSTANCE.innerUpdateOffset(millis, init);
    }

    private void innerUpdateOffset(long millis, boolean init) {
        long curOffsetMs = offset.get();
        if (curOffsetMs == millis) {
            return;
        }
        // 提醒大家
        if (init) {
            // 初始化服务器的时候，这里wechatLog还没有初始化好
            LOGGER.warn("gemini_system init SystemClock offset={}", millis);
        } else {
            ZonedDateTime nowBefore = ZonedDateTime.ofInstant(Instant.ofEpochMilli(SystemClock.now()), TimeUtils.getZoneId());
            ZonedDateTime nowAfter = ZonedDateTime.ofInstant(Instant.ofEpochMilli(SystemClock.nowNative()).plusMillis(millis), TimeUtils.getZoneId());
            LOGGER.error("gemini_system try updateOffset ms: {}={}, timestamp change {}->{}", millis, TimeUtils.formatDurationMs(millis), nowBefore, nowAfter);
        }
        offset.set(millis);
    }


    /**
     * 开启计时器线程
     */
    private void scheduleClock() {
        SYSTEM_CLOCK_POOL.scheduleWithFixedDelay(
                new NamedRunnable("SystemClockRunnable", () -> now = System.currentTimeMillis() + offset.get()),
                period, period, TimeUnit.MILLISECONDS);
    }

    /**
     * 当前时间毫秒数
     */
    private long currentTimeMillis() {
        return now;
    }

    /**
     * 单例
     */
    private static class InstanceHolder {
        public static final SystemClock INSTANCE = new SystemClock(1);
    }

    public static AtomicLong getOffset() {
        return InstanceHolder.INSTANCE.offset;
    }
}

package com.yorha.common.concurrent.executor;

import com.yorha.common.concurrent.IGeminiExecutor;
import com.yorha.common.concurrent.dispatcher.GeminiDispatcher;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jctools.queues.MpscBlockingConsumerArrayQueue;
import org.jetbrains.annotations.NotNull;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程版的任务消费者
 *
 * <AUTHOR>
 */
public class GeminiThreadExecutor implements IGeminiExecutor {
    private static final Logger LOGGER = LogManager.getLogger(GeminiThreadExecutor.class);

    /**
     * 实际的运行器
     */
    private final ThreadPoolExecutor executor;
    private final String name;

    public GeminiThreadExecutor(Object key, GeminiDispatcher dispatcher, int taskSize) {
        this.name = dispatcher.getDispatcherName() + "-" + key;
        this.executor = new ThreadPoolExecutor(1, 1, 0L, TimeUnit.MILLISECONDS,
                new MpscBlockingConsumerArrayQueue<>(taskSize),
                Thread.ofPlatform().daemon(false).name(name).factory(),
                (r, executor) -> LOGGER.error("executor {} {} reject {}", executor, name, r)
        );
    }

    @Override
    public void execute(@NotNull Runnable runnable) {
        executor.execute(runnable);
    }

    @Override
    public String getName() {
        return name;
    }

    /**
     * 停止新的任务，但是对于旧的任务，等待执行完成。
     */
    @Override
    public void shutdown() {
        this.executor.shutdown();
        try {
            if (!this.executor.awaitTermination(5, TimeUnit.SECONDS)) {
                LOGGER.error("shutdown {} await fail!", this);
            }
        } catch (InterruptedException e) {
            LOGGER.error("shutdown {} await fail!", this, e);
        }
    }

    @Override
    public String toString() {
        return "GeminiThreadExecutor{" +
                "name=" + name +
                '}';
    }
}

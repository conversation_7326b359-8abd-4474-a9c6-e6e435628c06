package com.yorha.common.actor.node;

import com.yorha.common.actor.cluster.ActorClusterUrlUtils;
import com.yorha.common.etcd.IWatchHandler;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.server.ServerContext;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.server.config.ConfigObj;
import com.yorha.common.server.discovery.ZoneItem;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.YamlUtils;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.wechatlog.WechatLog;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Nullable;
import java.util.*;

/**
 * ZoneItem、GateItem的数据Handler。
 *
 * <AUTHOR>
 */
public class DefaultZoneGateItemHandler implements IWatchHandler {
    private static final Logger LOGGER = LogManager.getLogger(DefaultZoneGateItemHandler.class);

    @Override
    public void onDelete(@NotNull String key) {
        // zoneList发现
        if (key.startsWith(ActorClusterUrlUtils.etcdZoneConfigPrefix())) {
            final int zoneId = ActorClusterUrlUtils.getZoneFromEtcdZoneConfigUrl(key);
            final Map<Integer, Map<String, Pair<String, Integer>>> newZoneListMap = new HashMap<>(this.zoneListMap);
            newZoneListMap.remove(zoneId);
            LOGGER.info("gemini_system DefaultZoneGateItemHandler delete key={}, zoneId={}", key, zoneId);
            this.zoneListMap = Collections.unmodifiableMap(newZoneListMap);
            this.refreshSuggestZones(ClusterConfigUtils.getZoneConfigs());
            return;
        }
        // zone发现
        if (key.startsWith(ActorClusterUrlUtils.etcdWorldZoneFoundPrefix())) {
            final Map<Integer, ZoneItem> newZoneItemMap = new HashMap<>(this.zoneItemMap);
            final Integer zoneId = this.loadZoneIdFromWorldZoneFoundKey(key);
            newZoneItemMap.remove(zoneId);
            this.zoneItemMap = Collections.unmodifiableMap(newZoneItemMap);
            LOGGER.info("gemini_system DefaultZoneGateItemHandler delete key={}, newItem={}", key, newZoneItemMap);
            return;
        }
        WechatLog.error("gemini_system DefaultZoneGateItemHandler delete key={}", key);
    }

    /**
     * 加载每个服加速通道信息
     */
    private void loadZoneList(final int zoneId, final String value) {
        final Map<String, Object> zoneInfoYaml = YamlUtils.newInstance(value);
        LOGGER.info("gemini_system loadZoneList zoneId={} map={}! yaml={}!", zoneId, zoneInfoYaml, value);
        @SuppressWarnings("unchecked") final Map<String, Object> accChannel = (Map<String, Object>) zoneInfoYaml.get("acceleration_channel");
        final Map<String, Pair<String, Integer>> channelMap = new HashMap<>(zoneInfoYaml.size());
        for (final Map.Entry<String, Object> zoneInfoEntry : accChannel.entrySet()) {
            @SuppressWarnings("unchecked") final Map<String, Object> ipPortInfo = (Map<String, Object>) zoneInfoEntry.getValue();
            channelMap.put(zoneInfoEntry.getKey(), Pair.of(ipPortInfo.get("ip").toString(), Integer.parseInt(ipPortInfo.get("port").toString())));
        }
        final Map<Integer, Map<String, Pair<String, Integer>>> newZoneListMap = new HashMap<>(this.zoneListMap);
        newZoneListMap.put(zoneId, channelMap);
        this.zoneListMap = Collections.unmodifiableMap(newZoneListMap);
    }

    @Override
    public void onUpdate(@NotNull String key, @NotNull String value) {
        // zoneList发现
        if (key.startsWith(ActorClusterUrlUtils.etcdZoneConfigPrefix())) {
            final int zoneId = ActorClusterUrlUtils.getZoneFromEtcdZoneConfigUrl(key);
            this.loadZoneList(zoneId, value);
            ClusterConfigUtils.refreshZone("refresh_zone_config_by_dir " + zoneId, zoneId, value);
            this.refreshSuggestZones(ClusterConfigUtils.getZoneConfigs());
            return;
        }
        // zone发现
        if (key.startsWith(ActorClusterUrlUtils.etcdWorldZoneFoundPrefix())) {
            final Map<Integer, ZoneItem> newZoneItemMap = new HashMap<>(this.zoneItemMap);
            final ZoneItem zoneItem = JsonUtils.parseObject(value, ZoneItem.class);
            if (zoneItem == null) {
                WechatLog.error("gemini_system update zone not json!!! key={} value={}!", key, value);
                return;
            }
            final Integer zoneId = this.loadZoneIdFromWorldZoneFoundKey(key);
            newZoneItemMap.put(zoneId, zoneItem);
            this.zoneItemMap = Collections.unmodifiableMap(newZoneItemMap);
            LOGGER.info("gemini_system update zone key={}! newItem={} newZoneItemMap={}", key, zoneItem, newZoneItemMap);
            if (ServerContext.isDirServer()) {
                updateZoneStatusMonitor(zoneId, suggestZoneList.contains(zoneId) ? 1 : 0);
            }
            return;
        }
        WechatLog.error("gemini_system DefaultZoneGateItemHandler update key={}, value={}!", key, value);
    }

    /**
     * 更新推荐服
     */
    private void refreshSuggestZones(Map<Integer, ConfigObj> allZoneConfig) {
        List<Integer> suggestZoneIds = new ArrayList<>();
        for (Map.Entry<Integer, ConfigObj> entry : allZoneConfig.entrySet()) {
            Integer zoneId = entry.getKey();
            ConfigObj zoneConfig = entry.getValue();
            boolean isSuggestZone = false;
            try {
                isSuggestZone = zoneConfig.getBooleanItem("is_suggest_zone");
            } catch (Exception e) {
                LOGGER.error("refreshSuggestZones fail zoneId={}, e=", zoneId, e);
            }

            if (isSuggestZone) {
                suggestZoneIds.add(zoneId);
            }
        }
        Collections.sort(suggestZoneIds);
        this.suggestZoneList = suggestZoneIds;
    }

    private Integer loadZoneIdFromWorldZoneFoundKey(final String key) {
        return ActorClusterUrlUtils.getZoneFromEtcdWorldZoneFoundUrl(key);
    }

    private static class LazyHolder {
        private static final DefaultZoneGateItemHandler INSTANCE = new DefaultZoneGateItemHandler();
    }

    public static DefaultZoneGateItemHandler getInstance() {
        return DefaultZoneGateItemHandler.LazyHolder.INSTANCE;
    }

    @SuppressWarnings("unchecked")
    private volatile Map<Integer, ZoneItem> zoneItemMap = Collections.EMPTY_MAP;
    @SuppressWarnings("unchecked")
    private volatile List<Integer> suggestZoneList = Collections.EMPTY_LIST;
    @SuppressWarnings("unchecked")
    private volatile Map<Integer, Map<String, Pair<String, Integer>>> zoneListMap = Collections.EMPTY_MAP;


    private DefaultZoneGateItemHandler() {
    }

    /**
     * 拉取存活游戏服列表。
     *
     * @return 存活游戏服列表。
     */
    public Collection<ZoneItem> getZoneList() {
        return zoneItemMap.values();
    }

    /**
     * 拉取存活游戏服id列表
     *
     * @return 存活游戏服id列表
     */
    public Set<Integer> getWorldFoundZoneIdSet() {
        return zoneItemMap.keySet();
    }

    /**
     * 获取指定存活游戏服
     *
     * @param zoneId 游戏服id
     * @return 存活游戏服信息 or null
     */
    @Nullable
    public ZoneItem getZone(int zoneId) {
        return this.zoneItemMap.getOrDefault(zoneId, null);
    }

    /**
     * 获取推荐服列表
     *
     * @return 推荐服列表
     */
    public List<Integer> getSuggestZoneList() {
        return suggestZoneList;
    }

    /**
     * 根据channelName获取一个Ip/Port对，若无则返回null
     *
     * @param zoneId      zoneId
     * @param channelName 加速通道
     * @return Pair<Ip, port>。
     */
    @Nullable
    public Pair<String, Integer> getGateAddressByChannel(int zoneId, String channelName) {
        if (channelName.isEmpty() || zoneListMap.isEmpty()) {
            LOGGER.info("getGateAddressByChannel fail channelName:{} zoneListMap size:{}", channelName, zoneListMap.size());
            return null;
        }
        if (!zoneListMap.containsKey(zoneId)) {
            LOGGER.info("getGateAddressByChannel fail zoneListMap not contain {}", zoneId);
            return null;
        }
        Map<String, Pair<String, Integer>> zoneInfoMap = zoneListMap.get(zoneId);
        // 上报的channelName,不在配置中
        if (!zoneInfoMap.containsKey(channelName)) {
            LOGGER.info("getGateAddressByChannel zoneId:{} not have channel config for:{}", zoneId, channelName);
            return null;
        }
        return zoneInfoMap.get(channelName);
    }

    private static void updateZoneStatusMonitor(int zoneId, long value) {
        MonitorUnit.GAME_ZONE_STATUS_IS_RECOMMEND.labels(
                ServerContext.getBusId(),
                ServerContext.getWorldIdStr(),
                String.valueOf(zoneId))
                .set(value);
    }
}

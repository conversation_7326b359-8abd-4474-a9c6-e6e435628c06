package com.yorha.common.db;

import com.yorha.common.db.tcaplus.DbUtil;
import com.yorha.common.db.tcaplus.op.FieldMetaData;
import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.time.SystemClock;

import java.util.function.Consumer;

/**
 * db操作基类
 *
 * <AUTHOR>
 */
public abstract class IDbOperation<Req, Option, Result> {
    /**
     * 异步执行
     *
     * @param cb 回调函数
     */
    public abstract void runAsync(Consumer<Result> cb);

    /**
     * 同步执行
     *
     * @return db返回结果
     */
    public abstract Result run();

    /**
     * 请求
     */
    private final Req req;
    /**
     * 请求配置
     */
    private final Option option;
    /**
     * 操作对应的数据表的元数据
     */
    private final FieldMetaData fieldMetaData;

    public Option getOption() {
        return option;
    }

    public FieldMetaData getFieldMetaData() {
        return fieldMetaData;
    }

    /**
     * Db请求统计的业务层字节数
     */
    private int requestBytesSize = 0;

    public long getRequestId() {
        return requestId;
    }

    /**
     * Db回包统计的业务层字节数
     */
    private int responseBytesSize = 0;
    /**
     * 流水号。
     */
    private final long requestId;

    private long requestSendTsMs;

    public IDbOperation(Req req, Option option, FieldMetaData fieldMetaData) {
        this.req = req;
        this.option = option;
        this.fieldMetaData = fieldMetaData;
        this.requestId = DbUtil.nextRequestId();
    }

    public Req getReq() {
        return req;
    }

    /**
     * @return 操作相关表名称
     */
    public String getTableName() {
        return this.fieldMetaData.tableName;
    }

    /**
     * @return 业务层请求的字节流数量
     */
    public int getRequestBytesSize() {
        return this.requestBytesSize;
    }

    /**
     * @return 业务层回包的字节流数量
     */
    public int getResponseBytesSize() {
        return this.responseBytesSize;
    }


    protected void addRequestBytes(final int bytes) {
        this.requestBytesSize += bytes;
    }

    protected void addResponseBytes(final int bytes) {
        this.responseBytesSize += bytes;
    }

    protected void addRequestBytes(final long bytes) {
        final long result = this.requestBytesSize + bytes;

        this.requestBytesSize = MathUtils.safeLong2Int(result);
    }

    protected void addResponseBytes(final long bytes) {
        final long result = this.responseBytesSize + bytes;
        this.responseBytesSize = MathUtils.safeLong2Int(result);
    }

    protected void onDbRequest() {
        this.requestSendTsMs = SystemClock.nowNative();
        DbManager.getInstance().onRequest(this);
    }

    /**
     * @param dbErrorCode db错误码
     * @return db耗时
     */
    protected long onDbResponse(final int dbErrorCode) {
        final long costMs = SystemClock.nowNative() - this.requestSendTsMs;
        DbManager.getInstance().onResponse(this, costMs, dbErrorCode);
        return costMs;
    }
}

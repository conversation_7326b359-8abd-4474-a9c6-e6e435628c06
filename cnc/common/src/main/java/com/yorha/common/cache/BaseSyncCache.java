package com.yorha.common.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.yorha.common.server.ServerContext;
import com.yorha.common.monitor.MonitorUnit;
import org.checkerframework.checker.nullness.qual.NonNull;

import javax.annotation.Nullable;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
public class BaseSyncCache<K, V> {
    private final Cache<K, V> cache;

    private final String name;

    public String getName() {
        return name;
    }

    /**
     * 同步异步：同步
     * 缓存类型：任意数据
     * 插入缓存：手动
     *
     * @param entryLimit  缓存条目上限
     * @param timeLimitMs 缓存写入后存储时间,ms（==0 永不过期）
     */
    BaseSyncCache(int entryLimit, long timeLimitMs, String name) {
        // 无需设置软引用or弱引用，根据写入时间淘汰即可
        Caffeine<Object, Object> caffeine = Caffeine.newBuilder().maximumSize(entryLimit);
        if (timeLimitMs != 0) {
            caffeine.expireAfterAccess(timeLimitMs, TimeUnit.MILLISECONDS);
        }
        this.cache = caffeine.build();
        this.name = name;
    }

    /**
     * 读取缓存内数据，若无则返回null
     *
     * @param key key
     * @return 缓存数据 或 null
     */
    @Nullable
    public V getDataOrNull(@NonNull K key) {
        // 禁止使用Cache.get，其传入的mappingFunction在synchronized中执行
        if (cache.getIfPresent(key) == null) {
            MonitorUnit.CACHE_NOT_HIT_TOTAL.labels(ServerContext.getBusId(), getName()).inc();
        } else {
            MonitorUnit.CACHE_HIT_TOTAL.labels(ServerContext.getBusId(), getName()).inc();
        }
        return cache.getIfPresent(key);
    }

    /**
     * 数据写缓存
     *
     * @param key key（非空）
     * @param val val（非空）
     */
    public final void putData(@NonNull K key, @NonNull V val) {
        cache.put(key, val);
    }

    /**
     * 手动失效指定缓存
     *
     * @param key key
     */
    public final void removeData(K key) {
        cache.invalidate(key);
    }

    /**
     * 获取缓存值
     */

    public final long cacheSize() {
        cache.cleanUp();
        return cache.estimatedSize();
    }
}

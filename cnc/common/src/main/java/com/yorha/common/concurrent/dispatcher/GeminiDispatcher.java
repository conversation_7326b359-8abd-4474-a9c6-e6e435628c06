package com.yorha.common.concurrent.dispatcher;

import com.yorha.common.concurrent.IGeminiDispatcher;
import com.yorha.common.concurrent.IGeminiExecutor;
import com.yorha.common.concurrent.NamedRunnableWithId;
import com.yorha.common.concurrent.TaskStats;
import com.yorha.common.concurrent.executor.GeminiThreadExecutor;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * gemini的任务派发器
 * 按task的id做hash分发
 *
 *
 * <AUTHOR>
 */
@Deprecated
public class GeminiDispatcher implements IGeminiDispatcher {
    private static final Logger LOGGER = LogManager.getLogger(GeminiDispatcher.class);

    /**
     * 池名
     */
    private final String dispatcherName;
    /**
     * 线程消费者数量
     */
    private final int threadNum;
    /**
     * 每个线程消费者
     */
    private final GeminiThreadExecutor[] threadExecutors;
    /**
     * 统计
     */
    private final TaskStats taskStats;

    GeminiDispatcher(String name, int threadNum, int taskSize) {
        this.dispatcherName = name;
        this.taskStats = new TaskStats(name);
        this.threadNum = threadNum;
        this.threadExecutors = new GeminiThreadExecutor[threadNum];
        // 创建线程消费者
        for (int i = 0; i < threadNum; i++) {
            this.threadExecutors[i] = new GeminiThreadExecutor(i, this, taskSize);
        }
    }

    /**
     * 投递任务
     */
    @Override
    public void execute(NamedRunnableWithId runnable) {
        Object id = runnable.getId();
        if (id == null) {
            throw new IllegalArgumentException(StringUtils.format("runnable:{}", runnable));
        }
        // 统计投递的任务次数
        String runnableName = runnable.getName();
        taskStats.onOffer(runnableName);

        IGeminiExecutor executor = getExecutor(id);
        long offerNanos = SystemClock.nanoTimeNative();

        // 获取当前scope的span
        try {
            executor.execute(IGeminiExecutor.wrapWithStats(runnable, offerNanos, taskStats, LOGGER, dispatcherName));
        } catch (Exception e) {
            // 这里往往是投递失败，reject队列满
            WechatLog.error("GeminiDispatcher::execute {} {} fail! ", dispatcherName, runnable, e);
        }
    }

    public IGeminiExecutor getExecutor(Object id) {
        int h = id.hashCode();
        int index = h & (threadNum - 1);
        return threadExecutors[index];
    }

    public String getDispatcherName() {
        return dispatcherName;
    }

    @Override
    public TaskStats getTaskStats() {
        return taskStats;
    }

    @Override
    public void shutdown() {
        for (GeminiThreadExecutor executor : this.threadExecutors) {
            executor.shutdown();
        }
    }

    @Override
    public String toString() {
        return "GeminiDispatcher{" + dispatcherName + '}';
    }
}

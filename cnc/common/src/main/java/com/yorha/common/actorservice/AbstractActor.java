package com.yorha.common.actorservice;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.actor.IActor;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.msg.*;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actorservice.msg.ActorMsgEnvelope;
import com.yorha.common.actorservice.msg.ActorMsgUtil;
import com.yorha.common.actorservice.msg.GeminiCompletionStage;
import com.yorha.common.dbactor.DbMgrService;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.helper.LogHelper;
import com.yorha.common.monitor.MonitorUnit;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.id.IntegerAdder;
import com.yorha.common.utils.json.JsonUtils;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.common.utils.time.TimeUtils;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.actor.msg.TypedMsg;
import com.yorha.gemini.utils.StringUtils;
import com.yorha.proto.CommonMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.ThreadContext;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Nullable;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 基类actor
 *
 * <AUTHOR>
 */
public abstract class AbstractActor implements IActor {
    private static final Logger LOGGER = LogManager.getLogger(AbstractActor.class);
    /**
     * 所处的actorSystem。
     */
    private final ActorSystem actorSystem;
    /**
     * Actor得ActorRef。
     */
    private final IActorRef self;
    /**
     * 缓存得Actor名称。
     */
    private final String toStringName;
    /**
     * msgSeqId -> ask stage
     */
    private final Map<Long, GeminiCompletionStage<Object>> askingStage = new HashMap<>();
    /**
     * 执行上下文。
     */
    private IMsgContext context;
    /**
     * 是否已经销毁。
     */
    private boolean isDestroy = false;
    /**
     * 懒加载，序列化ActorRef。
     */
    private CommonMsg.ActorRefData actorRefData;
    /**
     * 用于热更临时使用，必须使用getPatchMap来获取
     */
    private Map<Object, Object> patchMap;

    private boolean banCall = false;

    public AbstractActor(ActorSystem system, IActorRef self) {
        this.actorSystem = system;
        this.self = self;
        this.toStringName = self.getActorRole() + "-" + self.getActorId();
    }

    public void setBanCall() {
        banCall = true;
    }

    // ------------------------------------------------------------------------------------- actor淘汰相关

    public void onStopping() {
        // 取消并删除所有定时器
        dangerousCancelAllTimer();
        LOGGER.info("{} onStopping", this);
    }

    /**
     * 热更map
     */
    public Map<Object, Object> getPatchMap() {
        if (patchMap == null) {
            patchMap = new HashMap<>();
        }
        return patchMap;
    }

    /**
     * 热更临时使用函数
     */
    @Override
    public Map<Object, Object> patchMethod(String str, Map<Object, Object> map) {
        return ServerContext.getPatchInterface().patchMethod(str, map);
    }

    /**
     * 留一手
     */
    public void patchMsgToActor(IActorRef actorRef, String str, Map<Object, Object> map) {
        ActorSendMsgUtils.send(actorRef, new ActorRunnable<>("patchMsgToActor", targetActor -> {
            targetActor.patchMethod(str, map);
        }));
    }

    /**
     * 异步淘汰
     */
    public void destroyAsync(String reason) {
        ActorSendMsgUtils.send(self(), new ActorDestroyMsg(reason));
    }

    /**
     * 立刻淘汰，当前消息是最后一个。
     * 队列里剩余的消息会重新投递到mailbox，如果没有设置isCreate，则会被丢弃
     */
    public void forceDestroy(String reason) {
        this.handleActorDestroyMsg(reason);
        // 取消并删除所有定时器
        dangerousCancelAllTimer();
        MonitorUnit.ACTOR_DESTROY_COUNTER.labels(ServerContext.getBusId(), getRole()).inc();
        LOGGER.info("actor real destroy {} {}", reason, this);
    }

    /**
     * 设置的超时淘汰时间，单位秒
     */
    private int timeoutSeconds = -1;
    /**
     * 最近设置的超时淘汰的时间戳，单位秒
     */
    private long lastSetReceiveTimeoutTsSec = -1;
    /**
     * 最后一次接受到消息的时间戳，单位秒，用的系统时间，而非逻辑时间
     * 本身receiveTimeout的定时器不会更新该值
     */
    private long lastReceiveMsgTsSec = TimeUtils.ms2Second(SystemClock.nowNative());
    private ActorTimer receiveTimeoutTimer;

    /**
     * 设置超时淘汰
     * 单位秒，n秒后未收到消息则自动destroy
     * 0和负数是无效的
     */
    public void setReceiveTimeout(int seconds) {
        if (seconds <= 0) {
            throw new GeminiException("setReceiveTimeout {}", seconds);
        }
        if (this.timeoutSeconds != -1) {
            throw new GeminiException("unSupport repeat setReceiveTimeout {}", seconds);
        }
        this.timeoutSeconds = seconds;
        this.lastSetReceiveTimeoutTsSec = TimeUtils.ms2Second(SystemClock.nowNative());
        this.receiveTimeoutTimer = dangerousAddTimer("system", TimerReasonType.ACTOR_RECEIVE_TIMEOUT,
                this::onReceiveTimeoutTimer, timeoutSeconds, TimeUnit.SECONDS);
    }

    public void cancelReceiveTimeout() {
        this.timeoutSeconds = -1;
        this.lastSetReceiveTimeoutTsSec = -1;
        if (receiveTimeoutTimer != null) {
            receiveTimeoutTimer.cancel();
            receiveTimeoutTimer = null;
        }
    }

    /**
     * 业务层控制是否可以真的destroy
     */
    protected boolean canDestroy() {
        return this.askingStage.isEmpty();
    }

    /**
     * 定时淘汰到点了
     */
    private void onReceiveTimeoutTimer() {
        // 当前系统时间，单位秒
        final long curMsgTsSec = TimeUtils.ms2Second(SystemClock.nowNative());
        final long gap = curMsgTsSec - lastReceiveMsgTsSec;
        final long newGap;
        if (gap >= timeoutSeconds) {
            // 真的超时了, 清理无效askstage。
            this.clearExpireAskStage();
            if (canDestroy()) {
                if (this.lastSetReceiveTimeoutTsSec != -1) {
                    final long receiveTimeoutCostTimeSec = curMsgTsSec - this.lastSetReceiveTimeoutTsSec;
                    LOGGER.info("actor timeout destroy {} {}", this.self(), receiveTimeoutCostTimeSec);
                }
                // 业务层也允许destroy
                forceDestroy("onReceiveTimeoutTimer");
                return;
            } else {
                LOGGER.warn("can not destroy {} and move on timer", this);
                // 2倍超时时间
                newGap = 2L * TimeUnit.MILLISECONDS.toSeconds(ServerContext.getRpcTimeout());
            }
        } else {
            // 没超时，那就续上，计算距离上次超时还需要多久的newGap
            newGap = timeoutSeconds - gap;
        }
        LOGGER.info("onReceiveTimeoutTimer and move on {}, next gap {}", this, newGap);
        this.receiveTimeoutTimer = dangerousAddTimer("system", TimerReasonType.ACTOR_RECEIVE_TIMEOUT, this::onReceiveTimeoutTimer, newGap, TimeUnit.SECONDS);
    }

    private void trySetLastReceiveMsgTsSec(ActorMsgEnvelope envelope) {
        if (envelope.isTimer()) {
            // 所有定时器都不计入上次处理的时间
            return;
        }
        this.lastReceiveMsgTsSec = TimeUtils.ms2Second(SystemClock.nowNative());
    }

    /**
     * 清理无效ask stage的时间长度（毫秒）
     */
    private static final long EXPIRE_ASK_STAGE_TIME_MS = 60_000;
    /**
     * 下一次清理Ask Stage的时间戳。
     */
    private long clearExpireAskStageTsMs = 0;

    /**
     * 尝试清理过期的Ask Stage。
     * <p>
     * 每次收到消息，触发超时的AskStage清理事件，间隔60s。
     */
    private void tryClearExpireAskStage() {
        final long nowTsMs = SystemClock.nowNative();
        // 未超时，跳过
        if (nowTsMs <= this.clearExpireAskStageTsMs) {
            return;
        }
        final long elderExpireTsMs = this.clearExpireAskStageTsMs;
        // 更新下次超时时间戳
        this.clearExpireAskStageTsMs = nowTsMs + EXPIRE_ASK_STAGE_TIME_MS;
        // 首次触发，跳过
        if (elderExpireTsMs == 0) {
            return;
        }
        // 当前消息是销毁消息，则跳过
        if (this.getCurrentEnvelope().getPayload() instanceof ActorDestroyMsg) {
            return;
        }
        // 当前无未完成的stage，则跳过
        if (this.askingStage.isEmpty()) {
            return;
        }
        // 触发事件。（为了让这个调用不影响cost分析，因此专门采用了tell）
        ActorSendMsgUtils.send(this.self(), new ActorRunnable<>("clearExpireAskStage", (AbstractActor::clearExpireAskStage)));
    }

    /**
     * 清理过期的Ask Stage。
     */
    private void clearExpireAskStage() {
        // 尝试给超时stage做超时判定，askingStage可能在onCompeted中继续ask，所以不能用迭代器
        final List<Long> keys = new ArrayList<>(this.askingStage.keySet());
        for (final Long key : keys) {
            final GeminiCompletionStage<Object> completionStage = this.askingStage.get(key);
            if (!completionStage.checkExpiration()) {
                continue;
            }
            LOGGER.error("clearExpireAskStage {} stageId {} ask answer maybe lose, stage {}", this, key, completionStage);
            this.askingStage.remove(key);
        }
    }
    // ------------------------------------------------------------------------------------- 消息处理相关

    public final void handleMsg(@NotNull IMsgContext context) {
        final ActorMsgEnvelope envelope = context.getEnvelope();
        // 设置接受消息的时间戳
        this.trySetLastReceiveMsgTsSec(envelope);
        // 打印
        ActorMsgUtil.debugLogRecvMsg(this.self, LOGGER, envelope);
        try {
            // 先设置context上下文信息
            this.setMsgContext(context);
            this.tryClearExpireAskStage();
            // 设置debugLog开关
            if (envelope.isDebugLog()) {
                ThreadContext.put("debugLog", "true");
            }
            this.handleActorMsgEnvelope(envelope);
        } finally {
            // 去除上下文
            this.setMsgContext(null);
            // 去除debugLog开关
            if (envelope.isDebugLog()) {
                ThreadContext.remove("debugLog");
            }
        }
    }

    /**
     * 处理系统级的 ActorMsgEnvelope
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected void handleActorMsgEnvelope(final ActorMsgEnvelope envelope) {
        if (envelope.isAskAnswer()) {
            // ask回包
            final Long askStageId = envelope.getAskStageId();
            final long msgSeqId = envelope.getMsgSeqId();
            final GeminiCompletionStage<Object> theStage = this.askingStage.get(askStageId);
            if (theStage != null) {
                LOGGER.debug("{} ask answer to stage {} msgId {}", this, askStageId, msgSeqId);
                final IActorMsg payload = envelope.getPayload();
                if (payload instanceof ActorExceptionMsg) {
                    ActorExceptionMsg exceptionMsg = (ActorExceptionMsg) payload;
                    if (exceptionMsg.getErr() instanceof GeminiCompletionStage.GeminiStageTimeoutException) {
                        LOGGER.error("actor ask timeout! actor={} envelop={}!", this, envelope);
                    }
                    theStage.completeExceptionally(msgSeqId, exceptionMsg.getErr());
                } else {
                    theStage.complete(msgSeqId, payload instanceof TypedMsg ? ((TypedMsg) payload).getMsg() : payload);
                }
                if (theStage.isDone()) {
                    this.askingStage.remove(askStageId);
                    LOGGER.debug("{} remove ask stage {}", this, askStageId);
                }
            } else {
                final IActorMsg payload = envelope.getPayload();
                final Exception exceptionMsg = payload instanceof ActorExceptionMsg ? ((ActorExceptionMsg) payload).getErr() : null;
                if (exceptionMsg instanceof GeminiCompletionStage.GeminiStageTimeoutException) {
                    LOGGER.warn("actor answer context already done, but GeminiStageTimeoutException come, actor={} stageId={} envelop={}!",
                            this, askStageId, envelope);
                } else {
                    LOGGER.error("actor answer context not found! askContext actor={} stageId={} envelop={}!", this, askStageId, envelope);
                }
            }
        } else {
            try {
                final IActorMsg payload = envelope.getPayload();
                if (payload instanceof TypedMsg) {
                    this.handleTypedMsg((TypedMsg) payload);
                } else if (payload instanceof ActorTimerMsg) {
                    ActorTimerMsg actorTimerMsg = (ActorTimerMsg) payload;
                    this.handleActorTimerMsg(actorTimerMsg);
                } else if (payload instanceof ActorStoppingMsg) {
                    this.onStopping();
                } else if (payload instanceof ActorDestroyMsg) {
                    String reason = ((ActorDestroyMsg) payload).getReason();
                    this.forceDestroy(reason);
                } else if (payload instanceof IActorRunnable) {
                    ((IActorRunnable) payload).run(this);
                } else if (payload instanceof ActorReLoadResMsg) {
                    Set<Class<? extends IResTemplate>> templateClass = ((ActorReLoadResMsg) payload).getTemplateClass();
                    this.onReloadRes(templateClass);
                } else {
                    this.handleOtherMsg(envelope, payload);
                }
            } catch (Exception e) {
                if (GeminiException.isLogicException(e)) {
                    LOGGER.warn("exception_perf logic AbstractActor handle {} and catch exception:", envelope, e);
                } else {
                    WechatLog.error("exception_perf AbstractActor handle {} and catch exception:", envelope, e);
                }
                answerWithException(envelope, e);
            }
        }
    }

    protected void onReloadRes(Set<Class<? extends IResTemplate>> updatedTemplates) {

    }

    protected void handleOtherMsg(ActorMsgEnvelope envelope, IActorMsg payload) {
        logUnexpectedEnvelope(envelope);
    }

    protected void logUnexpectedEnvelope(ActorMsgEnvelope envelope) {
        throw new GeminiException("unknown msg {} to {}", envelope, this);
    }

    protected abstract void handleTypedMsg(TypedMsg typedMsg);

    /**
     * 处理定时消息
     */
    private void handleActorTimerMsg(ActorTimerMsg actorTimerMsg) {
        ActorTimer actorTimer = removeTimerBeforeExecute(actorTimerMsg.getId(), actorTimerMsg.getName());
        if (actorTimer != null) {
            actorTimer.run();
        }
    }

    /**
     * 处理销毁消息
     */
    protected void handleActorDestroyMsg(String reason) {
        LOGGER.info("handleActorDestroyMsg {} reason:{}", this, reason);
        this.isDestroy = true;
    }

    public boolean isDestroy() {
        return isDestroy;
    }

    // ------------------------------------------------------------------------------------- 自身成员变量相关

    /**
     * 区服id
     */
    public int getZoneId() {
        return ServerContext.getZoneId();
    }

    private String zoneIdStr = null;

    public String getZoneIdStr() {
        if (zoneIdStr == null) {
            zoneIdStr = String.valueOf(getZoneId());
        }
        return zoneIdStr;
    }

    public final String getRole() {
        return this.self.getActorRole();
    }

    public final String getId() {
        return this.self.getActorId();
    }

    protected final void setMsgContext(IMsgContext context) {
        this.context = context;
    }

    public ActorMsgEnvelope getCurrentEnvelope() {
        if (context == null) {
            return null;
        }
        return this.context.getEnvelope();
    }

    protected IMsgContext getContext() {
        return this.context;
    }

    public IActorRef sender() {
        return this.getCurrentEnvelope() == null ? IActorRef.NOBODY : this.getCurrentEnvelope().getSender();
    }

    public IActorRef self() {
        return self;
    }

    public CommonMsg.ActorRefData getActorRefData() {
        if (actorRefData == null) {
            CommonMsg.ActorRefData.Builder builder = CommonMsg.ActorRefData.newBuilder();
            builder.setActorId(getId());
            builder.setActorRole(this.getRole());
            builder.setBusId(ServerContext.getBusId());
            actorRefData = builder.build();
        }
        return actorRefData;
    }

    public ActorSystem system() {
        return actorSystem;
    }

    @Override
    public final String toString() {
        return toStringName;
    }

    // ------------------------------------------------------------------------------------- 发送相关
    public void realSend(IActorRef target, ActorMsgEnvelope envelope) {
        envelope.setReceiver(target);
        // 自身debug，则链路全打印
        envelope.setTagDebugLog(isDebugLog());
        if (target instanceof DbMgrService) {
            ((DbMgrService) target).dispatchProtoMsg(envelope);
        } else {
            ActorMsgSystem.dispatchMsg(envelope);
        }
    }

    public void tell(IActorRef targetActorRef, GeneratedMessageV3 msg) {
        realSend(targetActorRef, ActorMsgEnvelope.createFromTell(this.self(), msg));
    }

    public void tell(IActorRef targetActorRef, IActorMsg msg) {
        realSend(targetActorRef, ActorMsgEnvelope.createFromTell(this.self(), msg));
    }

    public void tellAndCreate(IActorRef targetActorRef, GeneratedMessageV3 msg) {
        ActorMsgEnvelope envelope = ActorMsgEnvelope.createFromTell(this.self(), msg);
        envelope.setTagCreate(true);
        realSend(targetActorRef, envelope);
    }

    public void tellAndCreate(IActorRef targetActorRef, IActorMsg msg) {
        ActorMsgEnvelope envelope = ActorMsgEnvelope.createFromTell(this.self(), msg);
        envelope.setTagCreate(true);
        realSend(targetActorRef, envelope);
    }

    public <Resp> GeminiCompletionStage<Resp> ask(IActorRef target, IActorMsg msg, long timeoutMillis) {
        ActorMsgEnvelope msgContext = ActorMsgEnvelope.createFromAsk(this.self(), msg);
        return ask0(target, msgContext, timeoutMillis);
    }

    public <Resp> GeminiCompletionStage<Resp> ask(IActorRef target, GeneratedMessageV3 msg, long timeoutMillis) {
        ActorMsgEnvelope msgContext = ActorMsgEnvelope.createFromAsk(this.self(), msg);
        return ask0(target, msgContext, timeoutMillis);
    }

    public <Resp> GeminiCompletionStage<Resp> ask(IActorRef target, GeneratedMessageV3 msg) {
        ActorMsgEnvelope msgContext = ActorMsgEnvelope.createFromAsk(this.self(), msg);
        return ask0(target, msgContext, ServerContext.getRpcTimeout());
    }

    public <Resp> GeminiCompletionStage<Resp> ask(IActorRef target, IActorMsg msg) {
        ActorMsgEnvelope msgContext = ActorMsgEnvelope.createFromAsk(this.self(), msg);
        return ask0(target, msgContext, ServerContext.getRpcTimeout());
    }

    @SuppressWarnings({"unchecked"})
    private <Resp> GeminiCompletionStage<Resp> ask0(IActorRef target, ActorMsgEnvelope msgContext,
                                                    long timeoutMillis) {
        final long msgSeqId = msgContext.getMsgSeqId();
        final Long stageId = msgSeqId;
        msgContext.setAskStageId(stageId);
        // 发送消息，确保消息发出
        realSend(target, msgContext);
        // 创建stage，并记录（不要调换和realSend的顺序）
        final GeminiCompletionStage<Resp> stage = new GeminiCompletionStage<>(msgContext, timeoutMillis);
        this.askingStage.put(stageId, (GeminiCompletionStage<Object>) stage);
        LOGGER.debug("{} add ask {} with stage key={} value={}", this, msgSeqId, stageId, stage);
        return stage;
    }

    public <R> R call(IActorRef targetActorRef, GeneratedMessageV3 msg) throws GeminiException {
        return call(targetActorRef, msg, ServerContext.getRpcTimeout());
    }

    public <R> R callAndCreate(IActorRef targetActorRef, GeneratedMessageV3 msg) throws GeminiException {
        final ActorMsgEnvelope msgEnvelope = ActorMsgEnvelope.createFromCall(this.self(), msg);
        msgEnvelope.setTagCreate(true);
        return call0(targetActorRef, msgEnvelope, ServerContext.getRpcTimeout());
    }

    public <R> R call(IActorRef targetActorRef, IActorMsg msg) throws GeminiException {
        return call(targetActorRef, msg, ServerContext.getRpcTimeout());
    }

    public <R> R call(IActorRef targetActorRef, GeneratedMessageV3 msg, long timeoutMillis) throws GeminiException {
        ActorMsgEnvelope msgEnvelope = ActorMsgEnvelope.createFromCall(this.self(), msg);
        return call0(targetActorRef, msgEnvelope, timeoutMillis);
    }

    public <R> R call(IActorRef targetActorRef, IActorMsg msg, long timeoutMillis) throws GeminiException {
        ActorMsgEnvelope msgEnvelope = ActorMsgEnvelope.createFromCall(this.self(), msg);
        return call0(targetActorRef, msgEnvelope, timeoutMillis);
    }

    @SuppressWarnings("unchecked")
    private <R> R call0(IActorRef targetActorRef, ActorMsgEnvelope envelope, long timeoutMillis) throws GeminiException {
        if (banCall) {
            if (ServerContext.isProdSvr()) {
                LOGGER.error("want call but ban this={} target={}", this, targetActorRef);
            } else {
                WechatLog.error("want call but ban this={} target={}", this, targetActorRef);
            }
        }
        final long startTimeTsNs = SystemClock.nanoTimeNative();
        final String profName = ActorMsgUtil.profNameOf(envelope);
        // 包装
        final ActorCallContext actorCallContext = new ActorCallContext(this, targetActorRef, envelope);
        Object ret;
        try {
            ActorMsgSystem.putActorCallContext(actorCallContext);
            ret = actorCallContext.run(timeoutMillis, TimeUnit.MILLISECONDS);
            if (ret instanceof ActorMsgWithTimeStamp) {
                ActorMsgWithTimeStamp msgWithTimeStamp = (ActorMsgWithTimeStamp) ret;
                long cur = SystemClock.nanoTimeNative();
                final long fiberWaitCarrierCostTime = TimeUnit.NANOSECONDS.toMillis(cur - msgWithTimeStamp.recvTs);
                if (fiberWaitCarrierCostTime > 100) {
                    LOGGER.warn("{} call msgType:{} fiberWaitCarrierCostTime:{} ms,payload{}, {}",
                            this, profName, fiberWaitCarrierCostTime, JsonUtils.toJsonString(envelope.getPayload()), msgWithTimeStamp.msg);
                }
            }
        } catch (TimeoutException e) {
            LOGGER.error("actor call timeout! actor={} envelop={}!", this, envelope);
            throw new GeminiException("", e);
        } catch (Exception e) {
            if (e instanceof GeminiException) {
                throw (GeminiException) e;
            } else {
                throw new GeminiException("", e);
            }
        } finally {
            ActorMsgSystem.removeActorCallContext(actorCallContext.getMsgSeqId());
            final long endTimeTsNs = SystemClock.nanoTimeNative();
            if (this.context != null) {
                this.context.addCostSendEnvelop(startTimeTsNs, endTimeTsNs, envelope);
            } else {
                LOGGER.warn("call without context! {}ms! envelop={}!", TimeUnit.NANOSECONDS.toMillis(endTimeTsNs - startTimeTsNs), envelope);
            }
        }
        if (ret instanceof Throwable) {
            if (ret instanceof GeminiException) {
                throw (GeminiException) ret;
            } else {
                throw new GeminiException("", (Throwable) ret);
            }
        }
        if (ret instanceof ActorMsgWithTimeStamp) {
            IActorMsg m = ((ActorMsgWithTimeStamp) ret).msg;
            return (R) (m instanceof TypedMsg ? ((TypedMsg) m).getMsg() : m);
        }
        return null;
    }

    /**
     * 默认使用getCurrentEnvelope
     */
    public void answer(GeneratedMessageV3 ans) {
        ActorMsgEnvelope currentEnvelope = getCurrentEnvelope();
        if (currentEnvelope == null) {
            throw new GeminiException(StringUtils.format("try answer with null currentEnvelope {}", this));
        }
        answerWithContext(currentEnvelope, ans);
    }

    /**
     * 指定context进行回复
     */
    public void answerWithContext(ActorMsgEnvelope actorMsgEnvelope, GeneratedMessageV3 ans) {
        if (actorMsgEnvelope.isByTell()) {
            WechatLog.error("try answer msg by tell! {}", actorMsgEnvelope);
            return;
        }
        IActorRef sender = actorMsgEnvelope.getSender();
        ActorMsgEnvelope answerEnvelope = ActorMsgEnvelope.createFromAnswer(actorMsgEnvelope, ans);
        realSend(sender, answerEnvelope);
    }

    /**
     * 出错回复
     */
    public void answerWithException(ActorMsgEnvelope context, Exception error) {
        if (context.isByTell()) {
            return;
        }
        IActorRef sender = context.getSender();
        ActorMsgEnvelope answerEnvelope = ActorMsgEnvelope.createFromAnswerException(context, error);
        realSend(sender, answerEnvelope);
    }

    private final Map<Integer, Integer> waitAsk = new HashMap<>();

    public <RESP> void batchAsk(Map<IActorRef, ? extends GeneratedMessageV3.Builder> msg, BiConsumer<RESP, Throwable> oneComplete, Runnable allComplete) {
        if (msg.isEmpty()) {
            allComplete.run();
            return;
        }
        int id = IntegerAdder.nextId();
        waitAsk.put(id, msg.size());
        for (Map.Entry<IActorRef, ? extends GeneratedMessageV3.Builder> entry : msg.entrySet()) {
            this.<RESP>ask(entry.getKey(), (GeneratedMessageV3) entry.getValue().build()).onComplete((res, err) -> {
                oneComplete.accept(res, err);
                int num = waitAsk.getOrDefault(id, 0) - 1;
                if (num == 0) {
                    waitAsk.remove(id);
                    allComplete.run();
                    return;
                }
                waitAsk.put(id, num);
            });
        }
    }

    public <RESP> void batchAsk(Map<Integer, ? extends
            GeneratedMessageV3.Builder> msg, Function<Integer, IActorRef> refFac, BiConsumer<RESP, Throwable> oneComplete, Runnable
                                        allComplete) {
        if (msg.isEmpty()) {
            allComplete.run();
            return;
        }
        int id = IntegerAdder.nextId();
        waitAsk.put(id, msg.size());
        for (Map.Entry<Integer, ? extends GeneratedMessageV3.Builder> entry : msg.entrySet()) {
            IActorRef ref = refFac.apply(entry.getKey());
            ActorMsgEnvelope envelope = ActorMsgEnvelope.createFromAsk(self(), (GeneratedMessageV3) entry.getValue().build());
            this.<RESP>ask0(ref, envelope, ServerContext.getRpcTimeout()).onComplete((res, err) -> {
                oneComplete.accept(res, err);
                int num = waitAsk.getOrDefault(id, 0) - 1;
                if (num == 0) {
                    waitAsk.remove(id);
                    allComplete.run();
                    return;
                }
                waitAsk.put(id, num);
            });
        }
    }

    public <RESP> void batchAsk(
            Map<Long, ? extends GeneratedMessageV3.Builder> msg,
            Function<Long, IActorRef> refFac,
            Consumer<RESP> oneComplete,
            Runnable allComplete
    ) {
        if (msg.isEmpty()) {
            allComplete.run();
            return;
        }
        int id = IntegerAdder.nextId();
        waitAsk.put(id, msg.size());
        for (Map.Entry<Long, ? extends GeneratedMessageV3.Builder> entry : msg.entrySet()) {
            IActorRef ref = refFac.apply(entry.getKey());
            ActorMsgEnvelope envelope = ActorMsgEnvelope.createFromAsk(self(), (GeneratedMessageV3) entry.getValue().build());
            this.<RESP>ask0(ref, envelope, ServerContext.getRpcTimeout()).onComplete(
                    (res, err) -> {
                        if (res != null) {
                            oneComplete.accept(res);
                        } else {
                            LOGGER.error("batchAsk failed {}", this, err);
                        }
                        int num = waitAsk.getOrDefault(id, 0) - 1;
                        if (num == 0) {
                            waitAsk.remove(id);
                            allComplete.run();
                            return;
                        }
                        waitAsk.put(id, num);
                    }
            );
        }
    }

    public <RESP> void batchAsk(List<IActorRef> refList, GeneratedMessageV3
            msg, Consumer<RESP> oneComplete, Runnable allComplete) {
        if (refList.isEmpty()) {
            allComplete.run();
            return;
        }
        int id = IntegerAdder.nextId();
        waitAsk.put(id, refList.size());
        for (IActorRef ref : refList) {
            ActorMsgEnvelope envelope = ActorMsgEnvelope.createFromAsk(self(), msg);
            this.<RESP>ask0(ref, envelope, ServerContext.getRpcTimeout()).onComplete(
                    (res, err) -> {
                        if (res != null) {
                            oneComplete.accept(res);
                        } else {
                            LOGGER.error("batchAsk failed {}", this, err);
                        }
                        int num = waitAsk.getOrDefault(id, 0) - 1;
                        if (num == 0) {
                            waitAsk.remove(id);
                            allComplete.run();
                            return;
                        }
                        waitAsk.put(id, num);
                    }
            );
        }
    }

    // ------------------------------------------------------------------------------------- timer

    /**
     * actor持有的定时器
     */
    private Map<String, ActorTimer> timerMap = null;
    private final AtomicInteger id = new AtomicInteger(0);

    public Integer genTimerId() {
        return id.incrementAndGet();
    }

    public ActorTimer getTimer(String prefix, TimerReasonType timerReasonType) {
        if (timerReasonType == null) {
            return null;
        }
        final String name = prefix + "@" + timerReasonType.name();
        return timerMap.get(name);
    }

    public ActorTimer getTimerByName(String name) {
        if (timerMap == null) {
            return null;
        }
        return timerMap.get(name);
    }

    /**
     * 添加一次性定时器基础方法
     * 只允许AbstractActor内部, addLimitTimer可以调用
     */
    @Nullable
    public ActorTimer dangerousAddTimer(String prefix, TimerReasonType timerReasonType, Runnable runnable, long initialDelay, TimeUnit unit) {
        if (StringUtils.isEmpty(prefix) || (timerReasonType == null)) {
            LogHelper.timerErrorLog("innerAddTimer but {} {}", prefix, timerReasonType);
            return null;
        }
        String name = timerReasonType.name();
        // 如果在停服中 拦截
        if (ServerContext.getServerStopStep() > 1) {
            return null;
        }
        name = prefix + "@" + name;
        if (timerMap != null && timerMap.containsKey(name)) {
            LogHelper.timerErrorLog("innerAddTimer repeat! name:{}", name);
            return timerMap.get(name);
        }
        ActorTimer actorTimer = ActorTimer.of(this, name, runnable, initialDelay, unit);
        if (actorTimer == null) {
            return null;
        }
        internalAddTimer(actorTimer);
        return actorTimer;
    }

    /**
     * 添加重复定时器基础方法
     * 只允许AbstractActor内部可以调用
     */
    @Nullable
    public ActorTimer dangerousAddRepeatTimer(
            String prefix,
            TimerReasonType timerReasonType,
            Runnable runnable,
            long initialDelay,
            long period,
            TimeUnit unit,
            boolean isFix
    ) {
        if (StringUtils.isEmpty(prefix) || (timerReasonType == null)) {
            LogHelper.timerErrorLog("innerAddRepeatTimer but {} {}", prefix, timerReasonType);
            return null;
        }
        String name = timerReasonType.name();
        // 如果在停服中 拦截
        if (ServerContext.getServerStopStep() > 1) {
            return null;
        }
        name = prefix + "@" + name;
        if (timerMap != null && timerMap.containsKey(name)) {
            LogHelper.timerErrorLog("innerAddRepeatTimer repeat! name:{}", name);
            return timerMap.get(name);
        }
        ActorTimer actorTimer = isFix ? ActorTimer.ofFixRepeat(this, name, runnable, initialDelay, period, unit)
                : ActorTimer.ofRepeat(this, name, runnable, initialDelay, period, unit);
        if (actorTimer == null) {
            return null;
        }
        internalAddTimer(actorTimer);
        return actorTimer;
    }

    /**
     * 添加到actor内部维护
     */
    private void internalAddTimer(ActorTimer actorTimer) {
        if (timerMap == null) {
            timerMap = new HashMap<>(8);
        }
        timerMap.put(actorTimer.getName(), actorTimer);
    }

    /**
     * 只允许ActorTimer回调！！！
     * 移出actor内部维护
     */
    public void dangerousRemoveTimer(String name) {
        if (timerMap == null) {
            if (ServerContext.getServerStopStep() != 0) {
                return;
            }
            throw new GeminiException("timerSet is null, actor:{} timer:{}", this, name);
        }
        timerMap.remove(name);
    }

    /**
     * 一次性的  执行前先remove了   防止在timer::run的时候加timer失败
     * 多次的 不remove 如果在timer::run的时候没cancel前加，就报错
     */
    private ActorTimer removeTimerBeforeExecute(int id, String name) {
        if (timerMap == null) {
            return null;
        }
        ActorTimer actorTimer = timerMap.get(name);
        if (actorTimer == null) {
            LOGGER.warn("timer run but is already canceled {} {}", id, name);
            return null;
        }
        if (actorTimer.getId() != id) {
            LOGGER.warn("timer run but is another one {} {}", id, name);
            return null;
        }
        if (actorTimer.isRepeat()) {
            return actorTimer;
        }
        return timerMap.remove(name);
    }

    /**
     * 取消自身所有的定时器，一般用于actor销毁时
     */
    public void dangerousCancelAllTimer() {
        if (timerMap == null) {
            return;
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("{} cancelAllTimer : {}", this, timerMap.keySet());
        }
        for (ActorTimer timer : timerMap.values()) {
            timer.onlyCancel();
        }
        timerMap = null;
    }

    /**
     * 是否所有的循环定时器已经取消
     */
    public boolean isAllRepeatTimerCancel() {
        if (timerMap == null) {
            return true;
        }
        boolean flag = true;
        for (ActorTimer actorTimer : timerMap.values()) {
            if (actorTimer.isRepeat() && !actorTimer.isCanceled()) {
                flag = false;
                LOGGER.error("isAllRepeatTimerCancel timer is not canceled, {} {}", actorTimer, this);
            }
        }
        return flag;
    }

    // ------------------------------------------------------------------------------------- debugLog

    /**
     * actor当前是否处于debugLog状态
     */
    private boolean isDebugLog = false;

    public void setDebugLog(boolean isDebugLog) {
        this.isDebugLog = isDebugLog;
        if (isDebugLog) {
            // key值用于log4j的DynamicThresholdFilter白名单日志过滤，value值不重要
            ThreadContext.put("debugLog", "true");
        }
    }

    public boolean isDebugLog() {
        if (isDebugLog) {
            return true;
        }
        final ActorMsgEnvelope currentEnvelope = this.getCurrentEnvelope();
        if (currentEnvelope == null) {
            return false;
        }
        return currentEnvelope.isDebugLog();
    }

    // ------------------------------------------------------------------------------------- end
}

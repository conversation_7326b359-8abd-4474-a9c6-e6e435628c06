package com.yorha.common.resource.resservice.guidance;

import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.exception.ResourceException;
import com.yorha.gemini.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.TutorialsTemplate;

import java.util.*;

/**
 * 触发式引导相关配置的后处理
 */
public class GuidanceTemplateService extends AbstractResService {
    private static final Logger LOGGER = LogManager.getLogger(GuidanceTemplateService.class);

    /**
     * jobId -> endStep
     */
    private final Map<Integer, Integer> jobId2EndStep = new HashMap<>();

    /**
     * jobId -> recordWhenStart
     */
    private final Map<Integer, Boolean> jobId2RecordWhenStart = new HashMap<>();

    public GuidanceTemplateService(ResHolder resHolder) {
        super(resHolder);
    }


    @Override
    public void load() throws ResourceException {
        Collection<TutorialsTemplate> tutorialsTemplates = getResHolder().getListFromMap(TutorialsTemplate.class);
        int curJobId = -1;
        int lastStepId = 0;
        for (TutorialsTemplate template : tutorialsTemplates) {
            if (curJobId != template.getJobId()) {
                if (curJobId != -1 && lastStepId != 0) {
                    jobId2EndStep.put(curJobId, lastStepId);
                }
                curJobId = template.getJobId();
                jobId2RecordWhenStart.put(curJobId, template.getUploadWhenStart() != 0);
            }
            lastStepId = template.getStep();
        }
        jobId2EndStep.put(curJobId, lastStepId);
    }

    @Override
    public void checkValid() throws ResourceException {
        Collection<TutorialsTemplate> tutorialsTemplates = getResHolder().getListFromMap(TutorialsTemplate.class);
        // 获取jobId的集合，判断step是否从1开始，是否不单调递减，即类似[1, 2, 2, 3, 4]
        Set<Integer> jobIdSet = new HashSet<>();
        final int FIRST_STEP = 1;
        int curJobId = -1;
        int lastStepId = 0;
        for (TutorialsTemplate template : tutorialsTemplates) {
            if (curJobId != template.getJobId()) {
                curJobId = template.getJobId();
                if (template.getStep() != FIRST_STEP) {
                    throw new ResourceException(StringUtils.format("新job配置step必须从1开始，当前job为{}，step为{}", template.getJobId(), template.getStep()));
                }
                lastStepId = FIRST_STEP;
            }
            if (lastStepId > template.getStep() || lastStepId < template.getStep() - 1) {
                LOGGER.warn("curStepId is {}, template.getStep() is {}", lastStepId, template.getStep());
                throw new ResourceException(StringUtils.format("同一job内step必须为连续非递减，job {} 配置有误", template.getJobId()));
            }
            lastStepId = template.getStep();
            jobIdSet.add(template.getJobId());
        }
        // 判断上报更新jobId中是否有未配置的jobId
        for (TutorialsTemplate template : tutorialsTemplates) {
            for (int uploadId : template.getUploadIdList()) {
                if (!jobIdSet.contains(uploadId)) {
                    throw new ResourceException(StringUtils.format("上报更新jobId中有未配置的引导id: {}", uploadId));
                }
            }
        }
    }

    public boolean isGuidanceConfigExist(int jobId) {
        return jobId2EndStep.containsKey(jobId);
    }

    public boolean isGuidanceNeedRecordWhenStart(int jobId) {
        return jobId2RecordWhenStart.containsKey(jobId) && jobId2RecordWhenStart.get(jobId);
    }

    public boolean isGuidanceEndStep(int jobId, int stepId) {
        return jobId2EndStep.containsKey(jobId) && jobId2EndStep.get(jobId) == stepId;
    }
}

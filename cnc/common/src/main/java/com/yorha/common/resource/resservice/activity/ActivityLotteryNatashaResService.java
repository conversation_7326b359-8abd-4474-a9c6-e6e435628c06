package com.yorha.common.resource.resservice.activity;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yorha.common.exception.ResourceException;
import com.yorha.common.resource.AbstractResService;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.datatype.IntPairType;
import res.template.*;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/6
 */
public class ActivityLotteryNatashaResService extends AbstractResService {
    private final Map<Integer, DrawPool> drawPoolMap = Maps.newHashMap();

    record DrawPool(LotteryDrawTemplate largePool, LotteryDrawTemplate smallPool, LotteryDrawTemplate normalPool) {
    }


    public DrawPool getDrawPool(ActivityLotteryTemplate template) {
        return drawPoolMap.get(template.getId());
    }

    public ActivityLotteryNatashaResService(ResHolder resHolder) {
        super(resHolder);
    }

    @Override
    public void load() throws ResourceException {

    }

    @Override
    public void checkValid() throws ResourceException {
        validateLotteryDrawTemplates();
        validateLotteryNatashaTemplates();
        validateNatashaShopTemplates();
    }

    private void validateLotteryDrawTemplates() throws ResourceException {
        for (LotteryDrawTemplate lotteryDrawTemplate : getResHolder().getListFromMap(LotteryDrawTemplate.class)) {
            var lotteryDrawId = lotteryDrawTemplate.getId();
            for (IntPairType intPairType : lotteryDrawTemplate.getCostItemsPairList()) {
                if (getResHolder().findValueFromMap(ItemTemplate.class, intPairType.getKey()) == null) {
                    throw new ResourceException("lottery_draw.xml id={} costItems={} 配置了不存在的道具消耗", lotteryDrawId, lotteryDrawTemplate.getCostItemsPairList());
                }
                if (intPairType.getValue() <= 0) {
                    throw new ResourceException("lottery_draw.xml id={} costItems={} 配置了错误的道具消耗数量", lotteryDrawId, lotteryDrawTemplate.getCostItemsPairList());
                }
            }
            SelecteRewardTemplate selecteRewardTemplate = getResHolder().findValueFromMap(SelecteRewardTemplate.class, lotteryDrawTemplate.getRewardId());
            if (selecteRewardTemplate == null) {
                throw new ResourceException("lottery_draw.xml id={} rewardId={} 配置了不存在的奖励id", lotteryDrawId, lotteryDrawTemplate.getRewardId());
            }
        }
    }

    private void validateLotteryNatashaTemplates() throws ResourceException {
        for (LotteryNatashaTemplate lotteryNatashaTemplate : getResHolder().getListFromMap(LotteryNatashaTemplate.class)) {
            int lotteryId = lotteryNatashaTemplate.getActivityLotteryid();
            ActivityLotteryTemplate lotteryTemplate = getResHolder().findValueFromMap(ActivityLotteryTemplate.class, lotteryId);
            if (lotteryTemplate == null) {
                throw new ResourceException("lottery_natasha.xml 活动={} 配置了不存在的转盘池activitylotteryid={}", lotteryNatashaTemplate.getId(), lotteryId);
            }

            ArrayList<LotteryDrawTemplate> drawTemplates = buildDrawTemplates(lotteryTemplate);
            DrawPool drawPool = new DrawPool(drawTemplates.get(0), drawTemplates.get(1), drawTemplates.get(2));

            validateDrawPoolGuarantees(drawPool);
            validateDrawPoolCostConsistency(drawPool);

            drawPoolMap.put(lotteryTemplate.getId(), drawPool);
        }
    }

    private ArrayList<LotteryDrawTemplate> buildDrawTemplates(ActivityLotteryTemplate lotteryTemplate) throws ResourceException {
        ArrayList<LotteryDrawTemplate> drawTemplates = Lists.newArrayList();
        for (Integer drawId : lotteryTemplate.getDrawIdsList()) {
            LotteryDrawTemplate drawTemplate = getResHolder().findValueFromMap(LotteryDrawTemplate.class, drawId);
            if (drawTemplate == null) {
                throw new ResourceException("activity_lottery.xml id={} 配置了不存在的抽奖id={}", lotteryTemplate.getId(), drawId);
            }
            drawTemplates.add(drawTemplate);
        }
        drawTemplates.sort(Comparator.comparingInt(LotteryDrawTemplate::getMinimumGuarantee).reversed());
        if (drawTemplates.size() != 3) {
            throw new ResourceException("activity_lottery.xml id={} 配置了大于或小于三个的抽奖id", lotteryTemplate.getId(), lotteryTemplate.getId());
        }
        return drawTemplates;
    }

    private void validateDrawPoolGuarantees(DrawPool drawPool) throws ResourceException {
        validateGuaranteeItems(drawPool.largePool.getBigGuaranteePairList(), drawPool.largePool.getId(), "大保底重置道具", "bigGuarantee");
        validateGuaranteeItems(drawPool.smallPool.getSmallGuaranteePairList(), drawPool.smallPool.getId(), "小保底重置道具", "smallGuarantee");
    }

    private void validateGuaranteeItems(List<IntPairType> guaranteeItems, int poolId, String itemType, String guaranteeType) throws ResourceException {
        if (guaranteeItems == null || guaranteeItems.isEmpty()) {
            throw new ResourceException("lottery_draw.xml id={} 没配{}", poolId, itemType);
        }
        for (IntPairType item : guaranteeItems) {
            if (getResHolder().findValueFromMap(ItemTemplate.class, item.getKey()) == null) {
                throw new ResourceException("lottery_draw.xml id={} {}={} 配置了不存在的{}触发道具", poolId, guaranteeType, guaranteeItems, itemType);
            }
            if (item.getValue() <= 0) {
                throw new ResourceException("lottery_draw.xml id={} {}={} 配置了错误的{}触发道具数量", poolId, guaranteeType, guaranteeItems, itemType);
            }
        }
    }

    private void validateDrawPoolCostConsistency(DrawPool drawPool) throws ResourceException {
        if (!sameCostCheck(drawPool.smallPool.getCostItemsPairList(), drawPool.normalPool.getCostItemsPairList())) {
            throw new ResourceException("lottery_draw.xml id={} 小保底奖池和默认奖池消耗不一样", drawPool.smallPool.getId());
        }
        if (!sameCostCheck(drawPool.largePool.getCostItemsPairList(), drawPool.normalPool.getCostItemsPairList())) {
            throw new ResourceException("lottery_draw.xml id={} 大保底奖池和默认奖池消耗不一样", drawPool.largePool.getId());
        }
    }

    private void validateNatashaShopTemplates() throws ResourceException {
        for (NatashaShopTemplate natashaShopTemplate : getResHolder().getListFromMap(NatashaShopTemplate.class)) {
            validateShopItems(natashaShopTemplate.getPricePairList(), natashaShopTemplate.getId(), "消耗", "price");
            validateShopItems(natashaShopTemplate.getItemIdPairList(), natashaShopTemplate.getId(), "奖励", "itemId");
        }
    }

    private void validateShopItems(List<IntPairType> items, int shopId, String itemType, String fieldName) throws ResourceException {
        for (IntPairType intPairType : items) {
            if (getResHolder().findValueFromMap(ItemTemplate.class, intPairType.getKey()) == null) {
                throw new ResourceException("natasha_shop.xml id={} {}={} 配置了不存在的{}", shopId, fieldName, items, itemType);
            }
            if (intPairType.getValue() <= 0) {
                throw new ResourceException("natasha_shop.xml id={} {}={} 配置了错误的{}数量", shopId, fieldName, items, itemType);
            }
        }
    }

    private static boolean sameCostCheck(List<IntPairType> cost, List<IntPairType> base) {
        if (cost.size() != base.size()) {
            return false;
        }
        for (int i = 0; i < cost.size(); i++) {
            IntPairType intPairType = base.get(i);
            IntPairType intPairType1 = cost.get(i);
            if (intPairType.getKey() != intPairType1.getKey() || intPairType.getValue() != intPairType1.getValue()) {
                return false;
            }
        }
        return true;
    }

    public LotteryDrawTemplate getDrawTemplate(ActivityLotteryTemplate lotteryTemplate, int drawNumSmall, int drawNumLarge) {
        DrawPool drawPool = getDrawPool(lotteryTemplate);
        if (drawNumLarge >= drawPool.largePool.getMinimumGuarantee()) {
            return drawPool.largePool;
        }
        if (drawNumSmall >= drawPool.smallPool.getMinimumGuarantee()) {
            return drawPool.smallPool;
        }
        return drawPool.normalPool;
    }

    public LotteryDrawTemplate getNormalDrawTemplate(ActivityLotteryTemplate lotteryTemplate) {
        DrawPool drawPool = getDrawPool(lotteryTemplate);
        return drawPool.normalPool;
    }
}

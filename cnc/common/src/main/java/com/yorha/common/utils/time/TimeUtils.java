package com.yorha.common.utils.time;


import com.google.common.collect.ImmutableList;
import com.yorha.common.exception.GeminiException;
import com.yorha.common.server.ServerContext;
import com.yorha.common.utils.Pair;
import com.yorha.common.wechatlog.WechatLog;
import com.yorha.gemini.utils.StringUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.time.temporal.IsoFields;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * 时间处理工具类
 * 1、API全部受时区影响
 * 2、周开始时间统一为周一零点，天开始时间统一为次天零点
 * 3、javaApi仅提供了秒级的逻辑判断，所以使用的时候尽量转为秒进行逻辑判断
 *
 * <AUTHOR>
 */
public class TimeUtils {

    /**
     * 使用0时区
     * 国内版本要切换为shanghai时区
     */
    private static final ZoneId ZONE_ID = ServerContext.isChinaVersion() ? ZoneId.of("UTC+8") : ZoneId.of("GMT");
    private static final String PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern(PATTERN);
    public static final DateTimeFormatter ZONE_FORMATTER = DateTimeFormatter.ofPattern(PATTERN).withZone(ZONE_ID);
    public static final LocalTime DAY_START_TIME = LocalTime.of(0, 0, 0, 0);

    /**
     * ----------------------------------- 提供便携的查询功能 ----------------------------------
     **/

    public static ZoneId getZoneId() {
        return ZONE_ID;
    }

    public static String getFormat() {
        return PATTERN;
    }

    public static String now2String() {
        return timeStampMs2String(SystemClock.now());
    }

    public static String nowNative2String() {
        return timeStampMs2String(SystemClock.nowNative());
    }


    /**------------------------------------ 提供基础的format ---------------------------------**/

    /**
     * string格式为ZoneDateTime
     */
    public static ZonedDateTime string2ZoneDate(String dateTime) {
        ZonedDateTime parse;
        try {
            parse = ZonedDateTime.parse(dateTime, ZONE_FORMATTER);
        } catch (DateTimeParseException e) {
            throw new GeminiException(StringUtils.format("parse time error. dateTime:{}", dateTime), e);
        }
        return parse;
    }

    /**
     * 时间戳转string
     */
    public static String timeStampMs2String(long timeMs) {
        return ZONE_FORMATTER.format(Instant.ofEpochMilli(timeMs));
    }

    /**
     * 根据时间获取时间戳
     */
    public static long string2TimeStampMs(String dateTime) {
        Instant parse;
        try {
            parse = ZonedDateTime.parse(dateTime, ZONE_FORMATTER).toInstant();
        } catch (DateTimeParseException e) {
            throw new GeminiException(StringUtils.format("time conversion failed:{}", dateTime), e);
        }
        return parse.toEpochMilli();
    }

    /**
     * 根据时间获取时间戳
     *
     * @param pattern 格式
     */
    public static long string2ZoneDate(String pattern, String dateTime) {
        Instant parse;
        try {
            final DateTimeFormatter zoneFormatter = DateTimeFormatter.ofPattern(pattern).withZone(ZONE_ID);
            parse = ZonedDateTime.parse(dateTime, zoneFormatter).toInstant();
        } catch (DateTimeParseException e) {
            throw new GeminiException(StringUtils.format("time conversion failed:{}", dateTime), e);
        }
        return parse.toEpochMilli();
    }

    /**
     * 秒 -> 毫秒
     */
    public static long second2Ms(long timeSec) {
        return TimeUnit.SECONDS.toMillis(timeSec);
    }

    /**
     * 毫秒 -> 秒
     */
    public static long ms2Second(long timeMs) {
        return TimeUnit.MILLISECONDS.toSeconds(timeMs);
    }


    /**------------------------------------ 提供基础的api ------------------------------------**/

    /**
     * 自然日！！！
     * <p>
     * 按照计时码类型获取目标和现在的时间差值
     * 注：用于计算两个时间戳之间跨越的时间间隔（差值按跨越的零点来算）
     *
     * @param smallMs 小的时间戳
     * @param largeMs 大的时间戳
     * @return 相隔天数（可能<0）
     */
    public static long getAbsNatureDaysBetween(long smallMs, long largeMs) {
        // 转换为 Instant 对象
        Instant instant1 = Instant.ofEpochMilli(smallMs);
        Instant instant2 = Instant.ofEpochMilli(largeMs);
        // 转换为 LocalDate 对象，使用默认时区
        LocalDate date1 = instant1.atZone(getZoneId()).toLocalDate();
        LocalDate date2 = instant2.atZone(getZoneId()).toLocalDate();
        // 计算日期之间的天数差值
        long offset = date2.toEpochDay() - date1.toEpochDay();
        if (offset < 0) {
            WechatLog.error("getAbsDaysBetweenOfGame fail, smallMs={} largeMs={}", smallMs, largeMs);
            return -offset;
        }
        return offset;
    }

    /**
     * 绝对时间！！非自然日
     * <p>
     * 指定两个时间戳，获取两者间隔天数，必为正数
     * 受时区影响，跨天判断为时区的零点
     *
     * @param smallMs 小的时间戳
     * @param largeMs 大的时间戳
     * @return 相隔天数（>0）
     */
    public static long getAbsDaysBetween(long smallMs, long largeMs) {
        long between = getBetween(smallMs, largeMs, ChronoUnit.DAYS);
        if (between < 0) {
            WechatLog.error("getAbsDaysBetween fail, smallMs={} largeMs={}", smallMs, largeMs);
            return -between;
        }
        return between;
    }

    /**
     * 指定两个时间戳，获取两者间隔周数，必为正数
     * 受时区影响，跨天判断为时区的零点
     *
     * @param smallMs 小的时间戳
     * @param largeMs 大的时间戳
     * @return 相隔周数（>0）
     */
    public static long getAbsWeeksBetween(long smallMs, long largeMs) {
        long smallWeekStartMs = getWeekStartMs(smallMs);
        long largeWeekStartMs = getWeekStartMs(largeMs);
        long between = getBetween(smallWeekStartMs, largeWeekStartMs, ChronoUnit.WEEKS);
        if (between < 0) {
            WechatLog.error("getAbsWeeksBetween fail, smallMs={} largeMs={}", smallMs, largeMs);
            return -between;
        }
        return between;
    }

    /**
     * 指定时间与当前是否是同一天
     */
    public static boolean isSameDayWithNow(long oneMs) {
        return isSameDay(oneMs, SystemClock.now());
    }

    /**
     * 指定两个时间是否是同一天，使用默认时区
     */
    public static boolean isSameDay(long oneMs, long otherMs) {
        return getNextDayStartMs(oneMs) == getNextDayStartMs(otherMs);
    }

    /**
     * 指定时间与当前时间是否是同一周
     */
    public static boolean isSameWeekWithNow(long oneMs) {
        return isSameWeek(oneMs, SystemClock.now());
    }

    /**
     * 指定两个时间是否是同一周
     */
    public static boolean isSameWeek(long oneMs, long otherMs) {
        LocalDate date1 = LocalDate.ofEpochDay(oneMs / (24 * 3600 * 1000));
        LocalDate date2 = LocalDate.ofEpochDay(otherMs / (24 * 3600 * 1000));
        int weekNumber1 = date1.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
        int weekNumber2 = date2.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
        return weekNumber1 == weekNumber2;
    }

    /**
     * 判断指定时间在当前时间之前
     */
    public static boolean isBeforeNow(long timeMs) {
        return timeMs < SystemClock.now();
    }

    /**
     * 判断指定时间在当前时间之后
     */
    public static boolean isAfterNow(long timeMs) {
        return timeMs > SystemClock.now();
    }

    /**
     * 判断指定时间在当前时间之后, 精度只到分钟
     */
    public static boolean isAfterNow(int hour, int min) {
        long now = SystemClock.now();
        int nowHour = getHourOfDay(now);
        if (nowHour > hour) {
            return false;
        }
        if (nowHour < hour) {
            return true;
        }
        int nowMin = getMinuteOfDay(now);
        if (min > nowMin) {
            return true;
        }
        return false;
    }

    /**
     * 获取指定时间秒钟数
     */
    public static int getSecondOfDay(long timeTsMs) {
        Instant instant = Instant.ofEpochMilli(timeTsMs);
        return instant.atZone(ZONE_ID).getSecond();
    }

    /**
     * 获取指定时间分钟数
     */
    public static int getMinuteOfDay(long timeTsMs) {
        Instant instant = Instant.ofEpochMilli(timeTsMs);
        return instant.atZone(ZONE_ID).getMinute();
    }

    /**
     * 获取指定时间是几点
     */
    public static int getHourOfDay(long timeTsMs) {
        Instant instant = Instant.ofEpochMilli(timeTsMs);
        return instant.atZone(ZONE_ID).getHour();
    }

    /**
     * 获取现在是周几
     */
    public static DayOfWeek getDayOfWeek() {
        ZonedDateTime now = SystemClock.nowZonedDateTime();
        return now.getDayOfWeek();
    }

    /**
     * 获取指定时间的当天0点的时间戳
     */
    public static Instant getDayStartInstant(Instant instant) {
        return instant.atZone(ZONE_ID).with(DAY_START_TIME).toInstant();
    }

    /**
     * 获取指定时间的下一天零点的时间戳
     */
    public static long getNextDayStartMs(long timeMs) {
        ZonedDateTime zonedDateTime = getInstant(timeMs).atZone(ZONE_ID);
        long second = zonedDateTime.getLong(ChronoField.SECOND_OF_DAY);
        long nextTimeMs = getInstant(timeMs).atZone(ZONE_ID).plus(Duration.ofHours(24)).minusSeconds(second).with(ChronoField.HOUR_OF_DAY, 0).toInstant().toEpochMilli();
        return formatTsMs(nextTimeMs);
    }

    /**
     * 获取指定时间到下一天零点的时间间隔
     */
    public static long getNextDayDurMs(long timeMs) {
        return getNextDayStartMs(timeMs) - timeMs;
    }

    /**
     * 获取指定时间下一个周开始时间的时间（周一为每周第一天）
     */
    public static long getNextWeekStartMs(long timeMs) {
        ZonedDateTime zonedDateTime = getInstant(timeMs).atZone(ZONE_ID);
        long second = zonedDateTime.getLong(ChronoField.SECOND_OF_DAY);
        return zonedDateTime.with(TemporalAdjusters.next(DayOfWeek.MONDAY)).minusSeconds(second).toInstant().toEpochMilli();
    }

    public static long getNextWeekDayStartMs(long timeMs, DayOfWeek dayOfWeek) {
        ZonedDateTime zonedDateTime = getInstant(timeMs).atZone(ZONE_ID);
        long second = zonedDateTime.getLong(ChronoField.SECOND_OF_DAY);
        return zonedDateTime.with(TemporalAdjusters.next(dayOfWeek)).minusSeconds(second).toInstant().toEpochMilli();
    }

    /**
     * 获取指定时间本周开始时间的时间（周一为每周第一天）
     */
    public static long getWeekStartMs(long timeMs) {
        ZonedDateTime zonedDateTime = getInstant(timeMs).atZone(ZONE_ID);
        long second = zonedDateTime.getLong(ChronoField.SECOND_OF_DAY);
        return zonedDateTime.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).minusSeconds(second).toInstant().toEpochMilli();
    }

    /**
     * 获取时间戳对应的ZonedDateTime
     */
    public static ZonedDateTime getZonedDateTime(long timeMs) {
        return getInstant(timeMs).atZone(ZONE_ID);
    }

    /**
     * 获取指定时间到下一个周开始的时间间隔
     */
    public static long getNextWeekDurMs(long timeMs) {
        return getNextWeekStartMs(timeMs) - timeMs;
    }

    /**
     * 获取现在到这个时间过了多久
     */
    public static long getDeltaMs(long timeTsMs) {
        long now = SystemClock.now();
        long t = now - timeTsMs;
        if (t < 0) {
            throw new GeminiException("getDeltaMs error t:{}  now:{}", timeTsMs, now);
        }
        return t;
    }


    /**------------------------------------- 提供时间处理 -------------------------------**/

    /**
     * 获取当前时刻
     */
    private static Instant getInstant(long timeMs) {
        return Instant.ofEpochMilli(timeMs);
    }

    /**
     * 按照计时码类型获取目标和现在的时间差值
     *
     * @param unit 判断标准（抹除时区概念，统一使用时间戳计算）
     */
    private static long getBetween(long oneMs, long otherMs, ChronoUnit unit) {
        ZonedDateTime oneZone = Instant.ofEpochMilli(oneMs).atZone(ZONE_ID);
        ZonedDateTime otherZone = Instant.ofEpochMilli(otherMs).atZone(ZONE_ID);
        return unit.between(oneZone, otherZone);
    }

    public static int subDateToHour(long minDateMs, long maxDateMs) {
        long time = maxDateMs - minDateMs;
        time = time > 0 ? time : 0;
        return (int) TimeUnit.MILLISECONDS.toHours(time);
    }

    /**
     * 由于java仅提供了秒级的api，这里做逻辑判断后需要抹除多余毫秒数
     */
    private static long formatTsMs(long nextTimeMs) {
        long l = TimeUtils.ms2Second(nextTimeMs);
        return TimeUtils.second2Ms(l);
    }

    private static final List<Pair<Long, String>> DURATION_FORMAT_PARAM = ImmutableList.of(
            Pair.of(Duration.ofDays(1).toMillis(), "d"),
            Pair.of(Duration.ofHours(1).toMillis(), "h"),
            Pair.of(Duration.ofMinutes(1).toMillis(), "m"),
            Pair.of(Duration.ofSeconds(1).toMillis(), "s"),
            Pair.of(Duration.ofMillis(1).toMillis(), "ms")
    );

    /**
     * 输出类似这样的描述 5d14h39m4s370ms
     */
    public static String formatDuration(Duration duration) {
        return formatDurationMs(duration.toMillis());
    }

    /**
     * 输出类似这样的描述 5d14h39m4s370ms
     */
    public static String formatDurationMs(long durationMs) {
        StringBuilder str = new StringBuilder();
        for (Pair<Long, String> pair : DURATION_FORMAT_PARAM) {
            if (durationMs <= 0) {
                break;
            }
            long x = durationMs / pair.getFirst();
            if (x > 0) {
                str.append(x).append(pair.getSecond());
            }
            durationMs = durationMs % pair.getFirst();
        }
        return str.toString();
    }

    public static void main(String[] args) {
        System.out.println(formatDuration(Duration.ofMillis(484744370)));
    }

}

package com.yorha.common.utils.shape;

import com.yorha.common.utils.RandomUtils;

/**
 * <AUTHOR>
 */
public class Ring implements Shape {
    /**
     * 外圆
     */
    private final Circle outerCircle;
    /**
     * 内圆
     */
    private final Circle innerCircle;

    public Ring(int x, int y, int outerR, int innerR) {
        this.outerCircle = Circle.valueOf(x, y, outerR);
        this.innerCircle = Circle.valueOf(x, y, innerR);
    }

    public static Ring valueOf(Point point, int outerR, int innerR) {
        return new Ring(point.getX(), point.getY(), outerR, innerR);
    }

    public static Ring valueOf(int x, int y, int outerR, int innerR) {
        return new Ring(x, y, outerR, innerR);
    }

    @Override
    public boolean containsPoint(long x, long y) {
        long distance = (x - getX()) * (x - getX()) + (y - getY()) * (y - getY());
        return distance <= (long) outerCircle.getR() * outerCircle.getR() && distance >= (long) innerCircle.getR() * innerCircle.getR();
    }

    @Override
    public AABB getAABB() {
        return outerCircle.getAABB();
    }

    @Override
    public Point getRandomPoint() {
        int angle = RandomUtils.nextInt(360);
        double sin = Math.sin(angle);
        double cos = Math.cos(angle);
        int newR = RandomUtils.nextInt(innerCircle.getR(), outerCircle.getR() + 1);
        return Point.valueOf((int) (getX() + newR * sin), (int) (getY() + newR * cos));
    }

    public int getX() {
        return innerCircle.getX();
    }

    public int getY() {
        return innerCircle.getY();
    }

    public Circle getOuterCircle() {
        return outerCircle;
    }

    public Circle getInnerCircle() {
        return innerCircle;
    }

    /**
     * 检测内外圆数据
     */
    public static boolean checkInnerOuterData(int innerR, int outerR) {
        if (innerR <= 0 || outerR <= 0) {
            return false;
        }
        if (innerR >= outerR) {
            return false;
        }
        return true;
    }
}

package com.yorha.common.utils;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;

import javax.annotation.concurrent.NotThreadSafe;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 运行时统计工具，在log中输出
 * 类似这样的横纵统计数据
 *
 * <AUTHOR>
 */
@NotThreadSafe
public class TextTable {

    private final String topic;

    /**
     * 二维数据存储
     */
    private final List<Row> table;


    public TextTable(String topic) {
        this.topic = topic;
        this.table = Lists.newArrayList(new Row());
    }

    public void addColumn(String... columnKey) {
        table.getFirst().add(columnKey);
    }

    public Row addRow() {
        Row row = new Row();
        table.add(row);
        return row;
    }

    public void info(Logger logger, boolean isAlwaysPrint) {
        if (!isAlwaysPrint && this.table.size() <= 1) {
            return;
        }
        logger.info(this.newStringBuilder());
    }


    public StringBuilder newStringBuilder() {
        StringBuilder builder = new StringBuilder();
        int colSpacing = 2;
        List<Integer> widthList = collectColWidth();

        builder.append("\n################################ ").append(topic).append(" ################################\n");
        for (Row row : table) {
            for (int i = 0; i < row.values.size(); i++) {
                String content = row.values.get(i);
                int length = widthList.get(i) + colSpacing;
                builder.append(StringUtils.rightPad(content, length));
            }
            builder.append('\n');
        }
        return builder;
    }

    public String newString() {
        StringBuilder builder = new StringBuilder();

        builder.append("`").append(topic).append("` \n");
        for (Row row : table) {
            for (int i = 0; i < row.values.size(); i++) {
                builder.append(" | ");
                String content = row.values.get(i);
                builder.append(content);
            }
            builder.append(" | \n");
        }
        return builder.toString();
    }

    public List<Integer> collectColWidth() {
        ArrayList<Integer> widthList = Lists.newArrayList();
        for (Row value : table) {
            List<String> row = value.values;
            for (int idx = 0; idx < row.size(); idx++) {
                int length = row.get(idx).length();
                if (widthList.size() <= idx) {
                    widthList.add(length);
                } else {
                    Integer max = widthList.get(idx);
                    if (length > max) {
                        widthList.set(idx, length);
                    }
                }
            }
        }
        return widthList;
    }

    public static class Row {
        private final List<String> values;

        public Row() {
            this.values = Lists.newArrayList();
        }

        public Row add(String... value) {
            values.addAll(Arrays.asList(value));
            return this;
        }

        public Row add(Object... value) {
            for (Object o : value) {
                values.add(String.valueOf(o));
            }
            return this;
        }

        public List<String> getValues() {
            return values;
        }
    }

    public static void main(String[] args) {
        TextTable table = new TextTable("temp topic");
        table.addColumn("name", "min", "max", "avg", "count");
        table.addRow().add("task1adasdadas", "10", "100asdasdas", "23", "222");
        table.addRow().add("task2", "1", "2sadasdasdasdasdasdasd0", "7", "333");
        StringBuilder out = table.newStringBuilder();
        System.out.println(out);
    }

}
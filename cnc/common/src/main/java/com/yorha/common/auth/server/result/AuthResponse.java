package com.yorha.common.auth.server.result;


/**
 * <AUTHOR>
 * @date 2024/5/9
 */
public class AuthResponse {
    private final int ret;
    private final String msg;
    private final String seq;

    public AuthResponse(int ret, String msg, String seq) {
        this.ret = ret;
        this.msg = msg;
        this.seq = seq;
    }

    public static AuthResponse success() {
        return new AuthResponse(0, "OK", "");
    }

    public static AuthResponse fail(String msg) {
        return new AuthResponse(-1, msg, "");
    }

    public boolean isSuccess() {
        return ret == 0;
    }

    @Override
    public String toString() {
        return "{" +
                "ret:" + ret +
                ", msg:'" + msg + '\'' +
                ", seq:'" + seq + '\'' +
                '}';
    }
}

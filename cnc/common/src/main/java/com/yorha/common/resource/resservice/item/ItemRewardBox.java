package com.yorha.common.resource.resservice.item;

import com.yorha.common.exception.GeminiException;
import com.yorha.common.utils.RandomUtils;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;

/**
 * 参考{@link SelectorType#COMPOUND}描述的配置方式
 *
 * <AUTHOR>
 */
public class ItemRewardBox {

    public static final int WEIGHT_BASE = 100000;
    private final List<ItemReward> rewards;
    private int weight = 0;
    private int boxWeight = 0;

    public ItemRewardBox() {
        this.rewards = new ArrayList<>();
    }

    public ItemRewardBox(int boxWeight) {
        this.rewards = new ArrayList<>();
        this.boxWeight = boxWeight;
    }

    public void addReward(ItemReward reward) {
        this.rewards.add(reward);
        this.weight += reward.getWeight();
    }

    @Nullable
    public ItemReward reward() {
        return reward(null);
    }

    @Nullable
    public ItemReward reward(ItemRewardParam param) {
        if (CollectionUtils.isEmpty(rewards) || weight == 0) {
            return null;
        }
        // 个数为1的奖池，使用十万分比。多个数的奖池，使用概率累加和
        if (rewards.size() == 1) {
            ItemReward itemReward = rewards.get(0);
            if (RandomUtils.nextInt(WEIGHT_BASE) < itemReward.getWeight()) {
                return itemReward;
            }
        } else {
            List<ItemReward> legalRewards = new ArrayList<>();
            for (ItemReward reward : rewards) {
                if (reward.getCondition() == null) {
                    legalRewards.add(reward);
                    continue;
                }
                if (param == null) {
                    throw new GeminiException("reward has condition but param is null rewardId:{}", reward.getConfigId());
                }
                if (reward.getCondition().isOpen(param.getServerOpenDays())) {
                    legalRewards.add(reward);
                }
            }
            ItemReward theOne = RandomUtils.randomByWeight(legalRewards, ItemReward::getWeight);
            return theOne.getCount() <= 0 ? null : theOne;
        }

        return null;
    }

    public ItemReward pickReward(int index) {
        if (CollectionUtils.isEmpty(rewards) || index >= rewards.size() || weight != 0) {
            return null;
        }

        return rewards.get(index);
    }

    public int getWeight() {
        return boxWeight;
    }

    public List<ItemReward> getRewards() {
        return rewards;
    }
}

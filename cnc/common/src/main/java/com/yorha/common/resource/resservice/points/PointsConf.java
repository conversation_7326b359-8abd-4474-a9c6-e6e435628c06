package com.yorha.common.resource.resservice.points;

import res.template.ActivityPointsTemplate;

public abstract class PointsConf {

    private final ActivityPointsTemplate template;

    public PointsConf(ActivityPointsTemplate template) {
        this.template = template;
    }

    public int getPoints() {
        return template.getPoints();
    }

    public ActivityPointsTemplate getTemplate() {
        return template;
    }

    public int getTemplateId() {
        return template.getId();
    }
}

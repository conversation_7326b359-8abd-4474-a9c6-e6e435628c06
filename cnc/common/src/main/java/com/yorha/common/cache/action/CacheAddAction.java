package com.yorha.common.cache.action;

import com.yorha.common.actorservice.GameActorWithCall;

import javax.annotation.Nullable;

/**
 * <AUTHOR>
 */
public abstract class CacheAddAction<K, V> {

    /**
     * 避免lambda转化为类
     * lambda内可含this指针，存在gc风险
     */
    public CacheAddAction() {
    }

    /**
     * 执行缓存添加操作
     *
     * @param key   key
     * @param actor actor
     * @return val
     */
    @Nullable
    public abstract V execute(K key, GameActorWithCall actor);
}

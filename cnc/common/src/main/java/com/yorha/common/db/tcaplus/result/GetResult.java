package com.yorha.common.db.tcaplus.result;

import com.google.protobuf.Message;
import com.yorha.common.db.tcaplus.TcaplusErrorCode;
import com.yorha.common.db.tcaplus.msg.GameDbResp;

/**
 * <AUTHOR>
 */
public class GetResult<T extends Message.Builder> implements GameDbResp {
    public TcaplusErrorCode code = TcaplusErrorCode.GEN_ERR_SUC;
    public int version = -1;
    public T value;

    @Override
    public int getCode() {
        return code.getValue();
    }

    @Override
    public boolean isOk() {
        return code == TcaplusErrorCode.GEN_ERR_SUC;
    }

    public boolean isNotExist() {
        return code == TcaplusErrorCode.TXHDB_ERR_RECORD_NOT_EXIST;
    }

    /**
     * option设置withFetchOnlyIfModified后，若远端version与本地一致，则返回错误码COMMON_INFO_DATA_NOT_MODIFIED
     *
     * @return 数据是否未变
     */
    public boolean isNotChanged() {
        return code == TcaplusErrorCode.COMMON_INFO_DATA_NOT_MODIFIED;
    }

    public int getVersion() {
        return version;
    }

    @Override
    public String toString() {
        return "GetResult{" +
                "code=" + code +
                ", version=" + version +
                '}';
    }
}

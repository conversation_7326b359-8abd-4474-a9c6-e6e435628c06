package com.yorha.gemini.props;

/**
 * <AUTHOR>
 */
public interface IMarkContainer {
    /**
     * 标记索引对应数据为脏数据
     *
     * @param index 索引。
     */
    void mark(int index);

    /**
     * 取消索引对应的标记。
     *
     * @param index 索引。
     */
    void unMark(int index);

    /**
     * 索引对应标记是否标记上。
     *
     * @param index 标记。
     * @return true or false。
     */
    boolean hasMark(int index);

    /**
     * 任意索引是否标记。
     *
     * @return true or false。
     */
    boolean hasAnyMark();

    /**
     * 返回容器的属性数量。
     *
     * @return 容器的属性数量。
     */
    int getFieldCount();

    /**
     * 检查数据是否相等
     *
     * @param node 目标节点
     * @return 是否相等
     */
    boolean compareDataTo(IMarkContainer node);
}

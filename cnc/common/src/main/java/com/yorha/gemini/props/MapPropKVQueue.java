package com.yorha.gemini.props;

import com.yorha.common.exception.GeminiException;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 属性系统FIFO的队列代理。特别注意，设计上是FIFO的。
 * 会将AbstractMapNode<K, V>代理成一个有序的队列进行使用。
 * tips: 使用此Proxy后，请勿绕过它对原始prop进行访问，否则会有不可预知的后果。
 *
 * <AUTHOR>
 */
public class MapPropKVQueue<K, V extends AbstractContainerElementNode<K>> {
    private final int capacity;
    private final AbstractMapNode<K, V> map;
    private LinkedList<K> sortedKeys;

    public MapPropKVQueue(int capacity, AbstractMapNode<K, V> map, Comparator<? super V> sortor, final Predicate<? super V> filter) {
        this.capacity = capacity;
        this.map = map;
        this.sortedKeys = new LinkedList<>();
        final ArrayList<V> vs = new ArrayList<>(this.map.values());
        vs.sort(sortor);
        for (final V v : vs) {
            if (!filter.test(v)) {
                continue;
            }
            this.sortedKeys.add(v.getPrivateKey());
        }
    }

    public MapPropKVQueue(int capacity, AbstractMapNode<K, V> map, Comparator<? super V> comparator) {
        this(capacity, map, comparator, (v) -> true);
    }

    /**
     * 列表是否为空。
     *
     * @return true or false。
     */
    public boolean isEmpty() {
        return this.sortedKeys.isEmpty();
    }

    /**
     * 列表是否已满。
     *
     * @return true or false。
     */
    public boolean isFull() {
        return !this.isEmpty() && this.sortedKeys.size() >= this.capacity;
    }

    /**
     * @return 队列容量。
     */
    public int getCapacity() {
        return this.capacity;
    }

    /**
     * @return 队列大小。
     */
    public int getSize() {
        return this.sortedKeys.size();
    }

    /**
     * 缩容。
     *
     * @return 缩容淘汰内容。
     */
    @SuppressWarnings("unchecked")
    public List<V> shrinkSize(final int size) {
        if (this.sortedKeys.size() <= size) {
            return Collections.EMPTY_LIST;
        }
        LinkedList<V> removeVs = new LinkedList<>();
        while (this.sortedKeys.size() > size) {
            final V v = this.pollFirst();
            removeVs.add(v);
        }
        return removeVs;
    }

    /**
     * 根据predicate缩容。
     *
     * @return 缩容淘汰内容。
     */
    @SuppressWarnings("unchecked")
    public List<V> shrinkSize(final int size, Predicate<V> predicate) {
        if (this.sortedKeys.size() <= size) {
            return Collections.EMPTY_LIST;
        }
        LinkedList<V> removeVs = new LinkedList<>();
        ListIterator<K> it = this.sortedKeys.listIterator();
        // 迭代器从头往后删除符合条件的
        while (this.getSize() > size && it.hasNext()) {
            K key = it.next();
            if (predicate.test(this.map.get(key))) {
                //迭代器删除更高效
                it.remove();
                removeVs.add(this.map.remove(key));
            }
        }
        if (this.getSize() > size) {
            throw new GeminiException("shrinkSize failed, check Predicate:{}", predicate);
        }
        return removeVs;
    }

    /**
     * 在尾部加入一个新的数据对象。
     *
     * @param k 新对象的Key。
     * @return 新对象
     */
    public V offerLast(K k) {
        if (this.map.containsKey(k)) {
            throw new IllegalArgumentException("key " + k + " repeat");
        }
        if (this.isFull()) {
            throw new RuntimeException("queue is full!");
        }
        final V v = this.map.addEmptyValue(k);
        this.sortedKeys.addLast(k);
        return v;
    }

    /**
     * 删除所有不符合匹配的数据项。
     *
     * @param filter 有效数据项过滤器。
     * @return true如果删除了数据；false没有。
     */
    public List<V> removeTargetValues(Predicate<V> filter) {
        final List<V> removeValues = this.sortedKeys.stream().map(this.map::get).filter((v) -> !filter.test(v))
                .collect(Collectors.toList());
        if (removeValues.isEmpty()) {
            return removeValues;
        }
        this.sortedKeys = this.sortedKeys.stream()
                .filter((k) -> filter.test(this.map.get(k)))
                .collect(LinkedList::new, LinkedList::add, LinkedList::addAll);
        for (final V v : removeValues) {
            this.map.remove(v.getPrivateKey());
        }
        return removeValues;
    }

    /**
     * 获取并去除头部元素。
     *
     * @return 元素，不存在则返回null。
     */
    public V pollFirst() {
        final V v = this.peekFirst();
        if (v == null) {
            return null;
        }
        this.sortedKeys.removeFirst();
        this.map.remove(v.getPrivateKey());
        return v;
    }

    /**
     * 获取头部元素。
     *
     * @return 元素，不存在则返回null。
     */
    public V peekFirst() {
        final K k = this.sortedKeys.peekFirst();
        if (k == null) {
            return null;
        }
        final V v = this.map.get(k);
        if (v == null) {
            throw new AssertionError("map not match to queue! please check queue");
        }
        return v;
    }

    /**
     * 获取尾部元素。
     *
     * @return 元素，不存在则返回null。
     */
    public V peekLast() {
        final K k = this.sortedKeys.peekLast();
        if (k == null) {
            return null;
        }
        final V v = this.map.get(k);
        if (v == null) {
            throw new AssertionError("map not match to queue! please check queue");
        }
        return v;
    }

    /**
     * 返回所有values。
     *
     * @return 值列表。
     */
    public List<V> values() {
        return this.sortedKeys.stream().map(this.map::get).collect(Collectors.toList());
    }

    /**
     * @return 键列表。
     */
    public List<K> keys() {
        return Collections.unmodifiableList(this.sortedKeys);
    }

    /**
     * 删除一个对象。复杂度O(n)。
     *
     * @param k 键。
     * @return 被删除对象。
     */
    public V remove(K k) {
        final boolean isRm = this.sortedKeys.remove(k);
        if (!isRm) {
            return null;
        }
        return this.map.remove(k);
    }

    /**
     * 删除N个对象。复杂度O(n)。
     *
     * @param keys 键集合。
     * @return 值集合。
     */
    public List<V> removeN(Collection<K> keys) {
        if (!(keys instanceof HashSet)) {
            keys = new HashSet<>(keys);
        }
        final List<V> values = new ArrayList<>(keys.size());
        for (K k : keys) {
            final V v = this.map.remove(k);
            if (v == null) {
                continue;
            }
            values.add(v);
        }
        this.sortedKeys.removeIf(keys::contains);
        return values;
    }
}

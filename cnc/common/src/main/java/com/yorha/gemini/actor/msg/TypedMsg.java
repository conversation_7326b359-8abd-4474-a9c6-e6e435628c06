package com.yorha.gemini.actor.msg;

import com.google.protobuf.ByteString;
import com.google.protobuf.GeneratedMessageV3;
import com.yorha.common.actor.msg.IActorMsg;
import com.yorha.common.utils.ClassNameCacheUtils;

/**
 * 携带type的msg
 *
 * <AUTHOR>
 */
public class TypedMsg implements IActorMsg {

    private final int msgType;
    private final GeneratedMessageV3 msg;

    public TypedMsg(int msgType, GeneratedMessageV3 msg) {
        this.msgType = msgType;
        this.msg = msg;
    }

    public int getMsgType() {
        return msgType;
    }

    public GeneratedMessageV3 getMsg() {
        return msg;
    }

    /**
     * 生成二进制数据
     */
    public ByteString getBytes() {
        return msg.toByteString();
    }

    @Override
    public String toString() {
        return "TypedMsg{" + ClassNameCacheUtils.getSimpleName(msg.getClass()) + "}";
    }

    @Override
    public boolean canRemote() {
        return true;
    }

    @Override
    public String profName() {
        return "msgType:" + msgType;
    }
}

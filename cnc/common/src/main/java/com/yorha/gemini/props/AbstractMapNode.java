package com.yorha.gemini.props;

import com.yorha.gemini.utils.StringUtils;
import com.yorha.common.exception.GeminiException;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.function.Consumer;

/**
 * 属性系统map类型的节点
 *
 * <AUTHOR>
 */

public abstract class AbstractMapNode<K, V extends AbstractContainerElementNode<K>> implements Map<K, V>, ITreeMarkContainer {
    /**
     * 不允许修改的，默认容器Map对应的属性index
     */
    public static final int DEFAULT_FIELD_INDEX = 0;
    /**
     * 容器的属性数量
     */
    public static final int FIELD_COUNT = 1;
    /**
     * 属性描述的掩码。
     * <p>
     * 00-09位：并发修改计数器
     * 10位：脏数据位
     * 11位：是否清除数据位
     * 12-26位：索引位
     */
    public static final int FIELD_DESCRIBE_MOD_COUNT_BIT_SIZE = 10;
    public static final int FIELD_DESCRIBE_DIRTY_BIT_SIZE = 1;
    public static final int FILED_DESCRIBE_CLEAR_BIT_SIZE = 1;
    public static final int FILED_DESCRIBE_MOD_MASK = (0x01 << FIELD_DESCRIBE_MOD_COUNT_BIT_SIZE) - 1;
    public static final int FIELD_DESCRIBE_DIRTY_MASK = (0x01 << FIELD_DESCRIBE_MOD_COUNT_BIT_SIZE);
    public static final int FIELD_DESCRIBE_CLEAR_MASK = (0x01 << (FIELD_DESCRIBE_MOD_COUNT_BIT_SIZE + 1));
    public static final int FILED_DESCRIBE_INDEX_BIT_MOVE_SIZE = FIELD_DESCRIBE_MOD_COUNT_BIT_SIZE + FIELD_DESCRIBE_DIRTY_BIT_SIZE + FILED_DESCRIBE_CLEAR_BIT_SIZE;
    public static final int FIELD_DESCRIBE_INDEX_MASK = ((0x01 << Constant.FIELD_DESCRIBE_INDEX_BITS_SIZE) - 1) << FILED_DESCRIBE_INDEX_BIT_MOVE_SIZE;

    // 检查平台是否符合代码的前置要求
    static {
        if (Integer.SIZE != 32) {
            throw new AssertionError("不支持整形非32位的平台!!!");
        }
    }

    /**
     * 数据容器
     */
    private final Map<K, V> dataAll;
    /**
     * 需要变更数据的键
     */
    private Set<K> existDirtyKeys;
    /**
     * 需要删除的键
     */
    private Set<K> refreshKeys;
    /**
     * 父节点
     */
    private IMarkContainer parentNode;
    /**
     * 描述关键字
     */
    private int fieldDescribeMask;

    /**
     * 构造函数。
     *
     * @param parent     父节点
     * @param fieldIndex 子节点但位于父节点的属性索引
     */
    public AbstractMapNode(final IMarkContainer parent, final int fieldIndex) {
        final int fieldIndexMask = fieldIndex << FILED_DESCRIBE_INDEX_BIT_MOVE_SIZE;
        if ((fieldIndexMask & FIELD_DESCRIBE_INDEX_MASK) != fieldIndexMask) {
            throw new GeminiException("fieldIndex {} not right!", fieldIndex);
        }
        this.parentNode = parent;
        this.fieldDescribeMask = fieldIndexMask;
        this.dataAll = new HashMap<>();
    }

    /**
     * @param k key值。
     * @return 值对象。
     */
    public abstract V addEmptyValue(K k);

    /**
     * @return 返回对象对应父容器的标脏索引。
     */
    @Override
    public int getFieldIndex() {
        return (this.fieldDescribeMask & FIELD_DESCRIBE_INDEX_MASK) >>> FILED_DESCRIBE_INDEX_BIT_MOVE_SIZE;
    }

    /**
     * @return 对象包含的属性数量。
     */
    @Override
    public int getFieldCount() {
        return FIELD_COUNT;
    }

    /**
     * @return 父亲节点。
     */
    @Override
    public IMarkContainer getParentNode() {
        return this.parentNode;
    }

    /**
     * 设置容器对应的父亲节点。
     *
     * @param parentNode 父亲节点。
     */
    @Override
    public void setParentNode(IMarkContainer parentNode) {
        if (parentNode == null) {
            // 允许置空
            this.parentNode = null;
        } else if (this.parentNode == null) {
            // 第一次赋值
            this.parentNode = parentNode;
        } else if (this.parentNode == parentNode) {
            // 反复放进map容器
            return;
        }
        throw new RuntimeException("setParentNode fail, maybe set twice, check the logic");
    }

    /**
     * 设置一个键-值对。
     *
     * @param k 键。
     * @param v 值。
     * @return k对应的老的值。
     */
    @Override
    public final V put(final K k, final V v) {
        if (v.getFieldIndex() != DEFAULT_FIELD_INDEX) {
            throw new RuntimeException("field index " + v.getFieldIndex() + " not right!");
        }
        this.isSamePrivateKey(k, v);
        final V put = this.dataAll.put(k, v);
        if (put == v) {
            return put;
        }
        v.markAll();
        v.setParentNode(this);
        if (put != null) {
            put.setParentNode(null);
            put.unMarkAll();
            this.getInnerRefreshKeys().add(k);
        }
        this.getInnerExistDirtyKeys().add(k);
        this.markDirty();
        this.markParentNode();
        this.incModCount();
        return put;
    }

    @Override
    public final V remove(Object rawKey) {
        final V remove = this.dataAll.remove(rawKey);
        if (remove == null) {
            return null;
        }
        remove.setParentNode(null);
        remove.unMarkAll();
        this.getInnerRefreshKeys().add(remove.getPrivateKey());
        this.getInnerExistDirtyKeys().remove(remove.getPrivateKey());
        this.markDirty();
        this.markParentNode();
        this.incModCount();
        return remove;
    }

    @Override
    public final void putAll(final Map<? extends K, ? extends V> m) {
        for (final Map.Entry<? extends K, ? extends V> kv : m.entrySet()) {
            this.put(kv.getKey(), kv.getValue());
        }
    }

    /**
     * 清理map容器。
     */
    @Override
    public final void clear() {
        // 非新增Key都为本此处理的删除Key
        this.existDirtyKeys = null;
        // 设置删除Key
        this.refreshKeys = null;
        // 无元素情况下，如果是脏数据，则设置clearFlag
        // 如果当前为空，且数据不脏，表示不需要下发数据
        if (this.isEmpty()) {
            this.markClearWhenDirty();
            return;
        }
        // 清理value
        for (final V v : this.dataAll.values()) {
            v.setParentNode(null);
            v.unMarkAll();
        }
        this.dataAll.clear();
        // 标记
        this.markClearAndDirty();
        // 增量变动
        this.markParentNode();
        this.incModCount();
    }

    @Override
    public final int size() {
        return dataAll.size();
    }

    @Override
    public final boolean isEmpty() {
        return dataAll.isEmpty();
    }

    @Override
    public final boolean containsKey(Object key) {
        return this.dataAll.containsKey(key);
    }

    @Override
    public final boolean containsValue(Object value) {
        return this.dataAll.containsValue(value);
    }

    @Override
    public final V get(Object key) {
        return this.dataAll.get(key);
    }

    @SuppressWarnings({"unchecked"})
    public final Set<K> getRefreshKeys() {
        if (this.refreshKeys == null) {
            return Collections.EMPTY_SET;
        }
        return Collections.unmodifiableSet(this.refreshKeys);
    }

    @SuppressWarnings({"unchecked"})
    public final Set<K> getExistDirtyKeys() {
        if (this.existDirtyKeys == null) {
            return Collections.EMPTY_SET;
        }
        return Collections.unmodifiableSet(this.existDirtyKeys);
    }

    @Override
    public final Set<Map.Entry<K, V>> entrySet() {
        return Collections.unmodifiableMap(this.dataAll).entrySet();
    }

    @Override
    public final Set<K> keySet() {
        return new KeySetView<>(this);
    }

    public final Iterator<K> keySetIterator() {
        return new KeyIterator<>(this);
    }

    @Override
    public final Collection<V> values() {
        return new ValuesView<>(this);
    }

    public Iterator<V> valuesIterator() {
        return new ValueIterator<>(this);
    }

    @Override
    public final void mark(int index) {
        throw new UnsupportedOperationException();
    }

    @Override
    public final void unMark(int index) {
        throw new UnsupportedOperationException();
    }

    @Override
    public final boolean hasMark(final int index) {
        if (index != DEFAULT_FIELD_INDEX) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        return this.isDirty();
    }

    @Override
    public final boolean hasAnyMark() {
        return this.isDirty();
    }

    @Override
    public final void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        for (final V v : this.dataAll.values()) {
            v.unMarkAll();
        }
        if (this.refreshKeys != null && this.refreshKeys.size() < 10) {
            this.refreshKeys.clear();
        } else {
            this.refreshKeys = null;
        }
        if (this.existDirtyKeys != null && this.existDirtyKeys.size() < 10) {
            this.existDirtyKeys.clear();
        } else {
            this.existDirtyKeys = null;
        }
        this.unMarkClearAndDirty();
        this.unMarkParentNode();
    }


    @Override
    public final void markAll() {
        for (final Map.Entry<K, V> kv : this.dataAll.entrySet()) {
            kv.getValue().markAll();
            this.getInnerExistDirtyKeys().add(kv.getKey());
        }
        this.markDirty();
        this.markParentNode();
    }

    /**
     * 是否标记clear
     *
     * @return 是否处于clear状态
     */
    public final boolean isClearFlag() {
        return (this.fieldDescribeMask & FIELD_DESCRIBE_CLEAR_MASK) != 0;
    }

    /**
     * 比较数据是否相同。
     *
     * @param other 其他数据
     * @return true or false。
     */
    @Override
    public final boolean compareDataTo(final IMarkContainer other) {
        if (other == null) {
            return false;
        }
        if (this == other) {
            return true;
        }
        if (this.getClass() != other.getClass()) {
            return false;
        }
        @SuppressWarnings({"rawtypes"}) final AbstractMapNode otherMap = (AbstractMapNode) other;
        if (this.dataAll.size() != otherMap.dataAll.size()) {
            return false;
        }
        for (final Map.Entry<K, V> kv : this.dataAll.entrySet()) {
            if (otherMap.get(kv.getKey()) == null) {
                return false;
            }
            if (!kv.getValue().compareDataTo(otherMap.get(kv.getKey()))) {
                return false;
            }
        }
        return true;
    }


    /**
     * 批量删除Key值，用于增量合并的过程中的key值获取。
     *
     * @param keys key的集合
     * @return 删除个数
     */
    public final int removeAll(final Collection<K> keys) {
        int cnt = 0;
        for (final K k : keys) {
            if (this.remove(k) != null) {
                // 同步delete key
                cnt++;
            }
        }
        return cnt;
    }

    final void mark(V v) {
        if (v == null) {
            return;
        }
        if (v.getParentNode() != this) {
            throw new RuntimeException("markElement fail!" + v.toString());
        }
        final K k = v.getPrivateKey();
        if (!this.containsKey(k)) {
            throw new RuntimeException("markElement fail! key not exit's!" + v.toString());
        }
        this.getInnerExistDirtyKeys().add(k);
        this.markDirty();
        this.markParentNode();
    }

    final void unMark(V v) {
        if (v == null) {
            return;
        }
        if (v.getParentNode() != this) {
            throw new RuntimeException("markElement fail!" + v.toString());
        }
        final K k = v.getPrivateKey();
        if (!this.containsKey(k)) {
            throw new RuntimeException("markElement fail! key not exit's!" + v.toString());
        }
        this.getInnerRefreshKeys().remove(k);
        this.getInnerExistDirtyKeys().remove(k);
        if (this.getInnerRefreshKeys().size() == 0 && this.getInnerExistDirtyKeys().size() == 0) {
            this.unMarkDirty();
            this.unMarkParentNode();
        }
    }

    private void isSamePrivateKey(K k, V v) {
        boolean res = k.equals(v.getPrivateKey());
        // value中所设外引key与key不符（开发者未正确设置value中key）
        if (!res) {
            throw new RuntimeException(StringUtils.format("privateKey error, key={}, key in val={}", k, v.getPrivateKey()));
        }
    }

    /**
     * 获取需要删除的keys
     *
     * @return key集合
     */
    private Set<K> getInnerRefreshKeys() {
        if (this.refreshKeys == null) {
            this.refreshKeys = new HashSet<>();
        }
        return this.refreshKeys;
    }

    /**
     * 获取修改【新增k.v、修改v】的keys
     *
     * @return key集合
     */
    private Set<K> getInnerExistDirtyKeys() {
        if (this.existDirtyKeys == null) {
            this.existDirtyKeys = new HashSet<>();
        }
        return this.existDirtyKeys;
    }

    private void markParentNode() {
        if (this.parentNode == null) {
            return;
        }
        if (this.parentNode.hasMark(this.getFieldIndex())) {
            return;
        }
        this.parentNode.mark(this.getFieldIndex());
    }

    private void unMarkParentNode() {
        if (this.parentNode == null) {
            return;
        }
        if (!this.parentNode.hasMark(this.getFieldIndex())) {
            return;
        }
        this.parentNode.unMark(this.getFieldIndex());
    }

    /**
     * @return 容器是否有数据修改。
     */
    private boolean isDirty() {
        return (this.fieldDescribeMask & FIELD_DESCRIBE_DIRTY_MASK) != 0;
    }

    /**
     * @return 容器的变更次数。
     */
    private int getModCount() {
        return (this.fieldDescribeMask & FILED_DESCRIBE_MOD_MASK);
    }

    /**
     * 标记容器有数据变更。
     */
    private void markDirty() {
        this.fieldDescribeMask = this.fieldDescribeMask | FIELD_DESCRIBE_DIRTY_MASK;
    }

    /**
     * 取消容器有数据变更的标记。
     */
    private void unMarkDirty() {
        this.fieldDescribeMask = this.fieldDescribeMask & (~FIELD_DESCRIBE_DIRTY_MASK);
    }

    /**
     * 当前处于容器有数据变更前提下，设置当前容器处于clear状态。
     */
    private void markClearWhenDirty() {
        this.fieldDescribeMask = this.fieldDescribeMask | ((FIELD_DESCRIBE_DIRTY_MASK & this.fieldDescribeMask) << 1);
    }

    /**
     * 取消容器的clear状态和变更状态标记。
     */
    private void unMarkClearAndDirty() {
        this.fieldDescribeMask = this.fieldDescribeMask & (~(FIELD_DESCRIBE_CLEAR_MASK | FIELD_DESCRIBE_DIRTY_MASK));
    }

    /**
     * 标记容器处于clear状态且状态变更。
     */
    private void markClearAndDirty() {
        this.fieldDescribeMask = this.fieldDescribeMask | (FIELD_DESCRIBE_CLEAR_MASK | FIELD_DESCRIBE_DIRTY_MASK);
    }

    /**
     * 自增修改次数。
     */
    private void incModCount() {
        final int modCount = (this.getModCount() + 1) & FILED_DESCRIBE_MOD_MASK;
        this.fieldDescribeMask = (this.fieldDescribeMask & (~FILED_DESCRIBE_MOD_MASK)) | modCount;
    }

    abstract static class CollectionView<K, V extends AbstractContainerElementNode<K>, E> implements Collection<E> {
        final AbstractMapNode<K, V> map;

        private static final int MAX_ARRAY_SIZE = Integer.MAX_VALUE - 8;
        private static final String OOME_MSG = "Required array size too large";

        CollectionView(AbstractMapNode<K, V> map) {
            this.map = map;
        }

        /**
         * Returns the map backing this view.
         *
         * @return the map backing this view
         */
        public AbstractMapNode<K, V> getMap() {
            return map;
        }

        /**
         * Removes all of the elements from this view, by removing all
         * the mappings from the map backing this view.
         */
        @Override
        public abstract void clear();

        @Override
        public abstract int size();

        @Override
        public abstract boolean isEmpty();

        // implementations below rely on concrete classes supplying these
        // abstract methods

        /**
         * Returns an iterator over the elements in this collection.
         *
         * <p>The returned iterator is
         * <a href="package-summary.html#Weakly"><i>weakly consistent</i></a>.
         *
         * @return an iterator over the elements in this collection
         */
        @Override
        public abstract @NotNull Iterator<E> iterator();

        @Override
        public abstract boolean contains(Object o);

        @Override
        public abstract boolean remove(Object o);


        @Override
        public final Object[] toArray() {
            int n = size();
            Object[] r = new Object[n];
            Iterator<E> it = iterator();
            int i = 0;
            while (it.hasNext()) {
                E e = it.next();
                if (i == n) {
                    if (n >= MAX_ARRAY_SIZE) {
                        throw new OutOfMemoryError(OOME_MSG);
                    }
                    if (n >= MAX_ARRAY_SIZE - (MAX_ARRAY_SIZE >>> 1) - 1) {
                        n = MAX_ARRAY_SIZE;
                    } else {
                        n += (n >>> 1) + 1;
                    }
                    r = Arrays.copyOf(r, n);
                }
                r[i++] = e;
            }

            return (i == n) ? r : Arrays.copyOf(r, i);
        }

        @Override
        @SuppressWarnings("unchecked")
        public final <T> T[] toArray(T[] a) {
            int m = size();
            T[] r = (a.length >= m) ? a :
                    (T[]) java.lang.reflect.Array.newInstance(a.getClass().getComponentType(), m);
            int n = r.length;
            int i = 0;
            Iterator<E> it = iterator();
            while (it.hasNext()) {
                E e = it.next();
                if (i == n) {
                    if (n >= MAX_ARRAY_SIZE) {
                        throw new OutOfMemoryError(OOME_MSG);
                    }
                    if (n >= MAX_ARRAY_SIZE - (MAX_ARRAY_SIZE >>> 1) - 1) {
                        n = MAX_ARRAY_SIZE;
                    } else {
                        n += (n >>> 1) + 1;
                    }
                    r = Arrays.copyOf(r, n);
                }
                r[i++] = (T) e;
            }
            if (a == r && i < n) {
                r[i] = null; // null-terminate
                return r;
            }
            return (i == n) ? r : Arrays.copyOf(r, i);
        }

        @Override
        public final String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append('[');
            Iterator<E> it = iterator();
            if (it.hasNext()) {
                for (; ; ) {
                    Object e = it.next();
                    sb.append(e == this ? "(this Collection)" : e);
                    if (!it.hasNext()) {
                        break;
                    }
                    sb.append(',').append(' ');
                }
            }
            return sb.append(']').toString();
        }

        @Override
        public final boolean containsAll(final Collection<?> c) {
            if (c != this) {
                for (Object e : c) {
                    if (e == null || !contains(e)) {
                        return false;
                    }
                }
            }
            return true;
        }

        @Override
        public final boolean removeAll(final Collection<?> c) {
            if (c == null) {
                throw new NullPointerException();
            }
            boolean modified = false;
            for (final Iterator<E> it = iterator(); it.hasNext(); ) {
                if (c.contains(it.next())) {
                    it.remove();
                    modified = true;
                }
            }
            return modified;
        }

        @Override
        public final boolean retainAll(Collection<?> c) {
            if (c == null) {
                throw new NullPointerException();
            }
            boolean modified = false;
            for (final Iterator<E> it = iterator(); it.hasNext(); ) {
                if (!c.contains(it.next())) {
                    it.remove();
                    modified = true;
                }
            }
            return modified;
        }
    }

    static final class ValuesView<K, V extends AbstractContainerElementNode<K>> extends AbstractMapNode.CollectionView<K, V, V>
            implements Collection<V> {

        ValuesView(AbstractMapNode<K, V> map) {
            super(map);
        }

        @Override
        public void clear() {
            map.clear();
        }

        @Override
        public int size() {
            return map.size();
        }

        @Override
        public boolean isEmpty() {
            return map.isEmpty();
        }

        @Override
        @SuppressWarnings("unchecked")
        public boolean contains(Object o) {
            if (o == null) {
                return false;
            }
            if (!(o instanceof AbstractContainerElementNode)) {
                return false;
            }
            return map.containsKey(((AbstractContainerElementNode<K>) o).getPrivateKey());
        }

        @Override
        public boolean remove(Object o) {
            if (!(o instanceof AbstractContainerElementNode)) {
                return false;
            }
            @SuppressWarnings("rawtypes")
            Object key = ((AbstractContainerElementNode) o).getPrivateKey();
            return map.remove(key) != null;

        }

        @Override
        public @NotNull Iterator<V> iterator() {
            return new ValueIterator<>(map);
        }

        @Override
        public boolean add(V e) {
            map.put(e.getPrivateKey(), e);
            return true;
        }

        @Override
        public boolean addAll(Collection<? extends V> c) {
            boolean rlt = false;
            for (V v : c) {
                if (add(v)) {
                    rlt = true;
                }
            }
            return rlt;
        }

        @Override
        public Spliterator<V> spliterator() {
            return Collections.unmodifiableCollection(this.map.dataAll.values()).spliterator();
        }

        @Override
        public void forEach(Consumer<? super V> action) {
            if (action == null) {
                throw new NullPointerException();
            }
            for (V v : this) {
                action.accept(v);
            }
        }
    }

    static final class KeySetView<K, V extends AbstractContainerElementNode<K>> extends AbstractMapNode.CollectionView<K, V, K>
            implements Set<K> {

        KeySetView(AbstractMapNode<K, V> map) {
            super(map);
        }

        @Override
        public void clear() {
            this.map.clear();
        }

        @Override
        public int size() {
            return this.map.size();
        }

        @Override
        public boolean isEmpty() {
            return this.map.isEmpty();
        }

        @Override
        public boolean contains(Object o) {
            if (o != null) {
                return this.map.containsKey(o);
            }
            return false;
        }

        @Override
        public boolean remove(Object o) {
            return this.map.remove(o) != null;

        }

        @Override
        public boolean addAll(@NotNull Collection<? extends K> c) {
            throw new UnsupportedOperationException();
        }

        @Override
        public @NotNull Iterator<K> iterator() {
            return new KeyIterator<>(this.map);
        }

        @Override
        public boolean add(K e) {
            throw new UnsupportedOperationException();
        }


        @Override
        public Spliterator<K> spliterator() {
            return Collections.unmodifiableCollection(this.map.dataAll.keySet()).spliterator();
        }

        @Override
        public void forEach(final Consumer<? super K> action) {
            if (action == null) {
                throw new NullPointerException();
            }
            for (final K k : this) {
                action.accept(k);
            }
        }
    }


    static final class ValueIterator<K, V extends AbstractContainerElementNode<K>> implements Iterator<V> {
        private final KeyIterator<K, V> keyIterator;

        ValueIterator(AbstractMapNode<K, V> map) {
            this.keyIterator = new KeyIterator<>(map);
        }

        @Override
        public boolean hasNext() {
            return this.keyIterator.hasNext();
        }

        @Override
        public V next() {
            final V v = this.keyIterator.map.get(this.keyIterator.next());
            if (v == null) {
                throw new ConcurrentModificationException();
            }
            return v;
        }

        @Override
        public void remove() {
            this.keyIterator.remove();
        }
    }

    static final class KeyIterator<K, V extends AbstractContainerElementNode<K>> implements Iterator<K> {
        private final AbstractMapNode<K, V> map;
        private final List<K> keyList;
        private int expectedModCount;
        private int lastRet;
        private int cursor;

        KeyIterator(final AbstractMapNode<K, V> map) {
            this.map = map;
            this.expectedModCount = map.getModCount();
            this.keyList = new ArrayList<>(map.dataAll.keySet());
            this.lastRet = -1;
            this.cursor = 0;
        }

        @Override
        public boolean hasNext() {
            if (this.expectedModCount != map.getModCount()) {
                throw new ConcurrentModificationException();
            }
            return this.cursor < this.keyList.size();
        }

        @Override
        public K next() {
            if (!this.hasNext()) {
                throw new NoSuchElementException();
            }
            this.lastRet = this.cursor++;
            return this.getKey();
        }

        @Override
        public void remove() {
            final K delKey = this.getKey();
            if (this.expectedModCount != this.map.getModCount()) {
                throw new ConcurrentModificationException();
            }
            final V v = this.map.remove(delKey);
            if (v == null) {
                throw new ConcurrentModificationException(delKey.toString());
            }
            this.expectedModCount = this.map.getModCount();
            this.lastRet = -1;
        }

        private K getKey() {
            if (this.lastRet == -1) {
                throw new IllegalStateException();
            }
            return this.keyList.get(this.lastRet);
        }
    }
}

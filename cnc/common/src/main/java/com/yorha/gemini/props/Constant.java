package com.yorha.gemini.props;

/**
 * 常量表。
 *
 * <AUTHOR>
 */
public final class Constant {
    /**
     * 属性系统默认值列表。
     */
    public static final String DEFAULT_STR_VALUE = "";
    public static final int DEFAULT_INT_VALUE = 0;
    public static final long DEFAULT_LONG_VALUE = 0L;
    public static final boolean DEFAULT_BOOLEAN_VALUE = false;
    public static final com.google.protobuf.ByteString DEFAULT_BYTE_STRING_VALUE = com.google.protobuf.ByteString.EMPTY;
    /**
     * 子类型的标记掩码。
     */
    public static final int MARK_BIT_SIZE = 64;
    public static final int MARK_MOVE_RIGHT_BIT_SIZE = 6;
    public static final int MARK_MOD_MASK = Long.SIZE - 1;
    /**
     * 属性系统标记位
     */
    public static final int FIELD_DESCRIBE_INDEX_BITS_SIZE = 15;

    static {
        if (Long.SIZE != 64) {
            throw new AssertionError("不支持长整形非64位的平台!!!");
        }
    }

    private Constant() {
    }
}

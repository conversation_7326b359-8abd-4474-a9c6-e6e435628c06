package com.yorha.gemini.props;

/**
 * 提供标记能力的容器。
 *
 * <AUTHOR>
 */
public interface ITreeMarkContainer extends IMarkContainer {
    /**
     * 设置父亲节点。
     *
     * @param parent 父亲节点。
     */
    void setParentNode(IMarkContainer parent);

    /**
     * 返回父亲节点。
     *
     * @return 父亲节点。
     */
    IMarkContainer getParentNode();

    /**
     * 标记所有索引。
     */
    void markAll();

    /**
     * 取消所有索引标记。
     */
    void unMarkAll();

    /**
     * 返回容器对应父容器的索引值
     *
     * @return 索引值。
     */
    int getFieldIndex();
}

/*
 * Copyright 2015-2020 yorha Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.yorha.gemini.utils;

import com.yorha.common.utils.MathUtils;
import com.yorha.common.utils.RandomUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.xml.bind.DatatypeConverter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串工具类.
 *
 * <AUTHOR> Jiang
 */
public class StringUtils {
    /**
     * 一个空的字节数组.
     */
    public static final byte[] EMPTY_BYTE_ARRAY = {};
    /**
     * 空字符串 {@code ""}.
     */
    public static final String EMPTY = "";
    /**
     * 一个空格字符串 {@code " "}
     */
    public static final String SPACE = " ";
    /**
     * 一个换行字符串 {@code "\n"}
     */
    public static final String LF = "\n";
    /**
     * 一个回车字符串 {@code "\r"}
     */
    public static final String CR = "\r";
    /**
     * 一个英文逗号字符串 {@code ","}
     */
    public static final String COMMA = ",";
    /**
     * 一个英文连字符字符串 {@code "-"}
     */
    public static final String HYPHEN = "-";

    /**
     * 一个英文左括号字符串 {@code "("}
     */
    public static final String LPAREN = "(";
    /**
     * 一个英文右括号字符串 {@code ")"}
     */
    public static final String RPAREN = ")";
    /**
     * 一个英文左大括号字符串 "{"
     */
    public static final String LBRACE = "{";
    /**
     * 一个英文右大括号字符串 "}"
     */
    public static final String RBRACE = "}";
    /**
     * 一个英文左中括号字符串 {@code "["}
     */
    public static final String LBRACKET = "[";
    /**
     * 一个英文右中括号字符串 {@code "]"}
     */
    public static final String RBRACKET = "]";
    /**
     * 一个英文冒号字符串 {@code ":"}
     */
    public static final String COLON = ":";
    /**
     * 一个英文星号字符串 {@code "*"}
     */
    public static final String ASTERISK = "*";
    /**
     * 一个空字符串数组.
     */
    public static final String[] EMPTY_STRING_ARRAY = {};


    private static final char DELIM_START = '{';
    private static final char DELIM_STOP = '}';

    public static final List<Character> STRING_SEED_POOL = Arrays.asList(
            '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', 'q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', 'a',
            's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'z', 'x', 'c', 'v', 'b', 'n', 'm', 'Q', 'W', 'E', 'R', 'T', 'Y',
            'U', 'I', 'O', 'P', 'A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', 'Z', 'X', 'C', 'V', 'B', 'N', 'M'
    );

    /**
     * 国服专用
     */
    public static final List<Character> CHINA_SEED_POOL = Arrays.asList(
            '1', '9', '3', '5', '7', '2', '8', '4', '6', '0'
    );

    /**
     * 检测字符串是否不为 null且不为"".
     *
     * <pre>
     * StringUtils.isNotEmpty(null)      = false
     * StringUtils.isNotEmpty("")        = false
     * StringUtils.isNotEmpty(" ")       = true
     * StringUtils.isNotEmpty("test")    = true
     * StringUtils.isNotEmpty("  test ") = true
     * </pre>
     *
     * @param text 被检测字符串
     * @return 如果字符不为 null且不为""则返回true,否则返回false.
     */
    public static boolean isNotEmpty(final String text) {
        return !isEmpty(text);
    }

    /**
     * 检测字符串是否为空，null或Java空白字符。
     * <p>
     * Java空白字符的定义请参考{@link Character#isWhitespace(char)}.
     *
     * <pre>
     * StringUtils.isBlank(null)      = true
     * StringUtils.isBlank("")        = true
     * StringUtils.isBlank(" ")       = true
     * StringUtils.isBlank("bob")     = false
     * StringUtils.isBlank("  bob  ") = false
     * </pre>
     *
     * @param text 被检测字符串
     * @return 如果字符串为空，null或Java空白字符则返回true,否则返回false.
     */
    public static boolean isBlank(final String text) {
        if (text == null) {
            return true;
        }
        // 只要有一个字符不为空白，那就肯定不是空白
        for (int i = 0, len = text.length(); i < len; i++) {
            if (!Character.isWhitespace(text.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检测字符串是否不为空，null或Java空白字符。
     * <p>
     * Java空白字符的定义请参考{@link Character#isWhitespace(char)}.
     *
     * <pre>
     * StringUtils.isNotBlank(null)      = false
     * StringUtils.isNotBlank("")        = false
     * StringUtils.isNotBlank(" ")       = false
     * StringUtils.isNotBlank("bob")     = true
     * StringUtils.isNotBlank("  bob  ") = true
     * </pre>
     *
     * @param text 被检测字符串
     * @return 如果字符串不为空，null或Java空白字符则返回true,否则返回false.
     */
    public static boolean isNotBlank(final String text) {
        return !isBlank(text);
    }

    /**
     * 检测一个字符串长度.
     * <p>
     * 字符串有可能包含中文等其他文字，中文应该算2个长度.
     *
     * @param text 被检测字符串
     * @return 字符串长度
     */
    public static int length(final String text) {
        if (text == null) {
            return 0;
        }

        int sum = 0;
        for (int i = 0, len = text.length(); i < len; i++) {
            sum += text.charAt(i) > 127 ? 2 : 1;
        }
        return sum;
    }

    /**
     * <p>
     * Splits the provided text into an array, separators specified. This is an alternative to using StringTokenizer.
     * </p>
     *
     * <p>
     * The separator is not included in the returned String array. Adjacent separators are treated as one separator. For more control over the split use the StrTokenizer class.
     * </p>
     *
     * <p>
     * A {@code null} input String returns {@code null}. A {@code null} separatorChars splits on whitespace.
     * </p>
     *
     * <pre>
     * StringUtils.split(null, *)         = null
     * StringUtils.split("", *)           = []
     * StringUtils.split("abc def", null) = ["abc", "def"]
     * StringUtils.split("abc def", " ")  = ["abc", "def"]
     * StringUtils.split("abc  def", " ") = ["abc", "def"]
     * StringUtils.split("ab:cd:ef", ":") = ["ab", "cd", "ef"]
     * </pre>
     *
     * @param str            the String to parse, may be null
     * @param separatorChars the characters used as the delimiters, {@code null} splits on whitespace
     * @return an array of parsed Strings, {@code null} if null String input
     */
    public static String[] split(final String str, final String separatorChars) {
        return splitWorker(str, separatorChars, -1, false);
    }

    /**
     * Performs the logic for the {@code split} and {@code splitPreserveAllTokens} methods that return a maximum array length.
     *
     * @param str               the String to parse, may be {@code null}
     * @param separatorChars    the separate character
     * @param max               the maximum number of elements to include in the array. A zero or negative value implies no limit.
     * @param preserveAllTokens if {@code true}, adjacent separators are treated as empty token separators; if {@code false}, adjacent separators are treated as one separator.
     * @return an array of parsed Strings, {@code null} if null String input
     */
    private static String[] splitWorker(final String str, final String separatorChars, final int max, final boolean preserveAllTokens) {
        if (str == null) {
            return EMPTY_STRING_ARRAY;
        }
        final int len = str.length();
        if (len == 0) {
            return EMPTY_STRING_ARRAY;
        }
        final List<String> list = new ArrayList<>();
        int sizePlus1 = 1;
        int i = 0;
        int start = 0;
        boolean match = false;
        boolean lastMatch = false;
        if (separatorChars == null) {
            while (i < len) {
                if (Character.isWhitespace(str.charAt(i))) {
                    if (match || preserveAllTokens) {
                        lastMatch = true;
                        if (sizePlus1++ == max) {
                            i = len;
                            lastMatch = false;
                        }
                        list.add(str.substring(start, i));
                        match = false;
                    }
                    start = ++i;
                    continue;
                }
                lastMatch = false;
                match = true;
                i++;
            }
        } else if (separatorChars.length() == 1) {
            final char sep = separatorChars.charAt(0);
            while (i < len) {
                if (str.charAt(i) == sep) {
                    if (match || preserveAllTokens) {
                        lastMatch = true;
                        if (sizePlus1++ == max) {
                            i = len;
                            lastMatch = false;
                        }
                        list.add(str.substring(start, i));
                        match = false;
                    }
                    start = ++i;
                    continue;
                }
                lastMatch = false;
                match = true;
                i++;
            }
        } else {
            while (i < len) {
                if (separatorChars.indexOf(str.charAt(i)) >= 0) {
                    if (match || preserveAllTokens) {
                        lastMatch = true;
                        if (sizePlus1++ == max) {
                            i = len;
                            lastMatch = false;
                        }
                        list.add(str.substring(start, i));
                        match = false;
                    }
                    start = ++i;
                    continue;
                }
                lastMatch = false;
                match = true;
                i++;
            }
        }
        final boolean flag = preserveAllTokens && lastMatch;
        if (match || flag) {
            list.add(str.substring(start, i));
        }
        return list.toArray(new String[0]);
    }

    /**
     * Replace placeholders in the given messagePattern with arguments.
     *
     * @param messagePattern the message pattern containing placeholders.
     * @param arguments      the arguments to be used to replace placeholders.
     * @return the formatted message.
     */
    public static String format(String messagePattern, Object... arguments) {
        if (messagePattern == null || arguments == null || arguments.length == 0) {
            return messagePattern;
        }
        final StringBuilder result = new StringBuilder();
        int currentArgument = 0;
        for (int i = 0; i < messagePattern.length(); i++) {
            final char curChar = messagePattern.charAt(i);
            if (curChar == DELIM_START && i < messagePattern.length() - 1 && messagePattern.charAt(i + 1) == DELIM_STOP) {
                if (currentArgument < arguments.length) {
                    result.append(arguments[currentArgument]);
                } else {
                    result.append(DELIM_START).append(DELIM_STOP);
                }
                currentArgument++;
                i++;
            } else {
                result.append(curChar);
            }
        }
        return result.toString();
    }

    /**
     * 从输入流中读出所有文本.
     *
     * @param inputStream 输入流
     * @return 返回流中的文本
     * @throws IOException If an I/O error occurs
     */
    public static String readString(InputStream inputStream) throws IOException {
        return readString(inputStream, StandardCharsets.UTF_8);
    }

    /**
     * 从输入流中读出所有文本.
     *
     * @param inputStream 输入流
     * @param charset     文本的编码方式
     * @return 返回流中的文本
     * @throws IOException If an I/O error occurs
     */
    public static String readString(InputStream inputStream, Charset charset) throws IOException {
        try (InputStreamReader isr = new InputStreamReader(inputStream, charset)) {
            return readString(isr);
        }
    }

    /**
     * 读出所有文本。
     * <p>
     * 这里没有选择BufferedReader就是不想一行一行的读，浪费字符串拼接性能 <br>
     * 正常用于读HTTP的响应，配置文件内容，小文件等情况
     *
     * @param reader 抽象的文本流
     * @return 返回流中所有文本
     * @throws IOException If an I/O error occurs
     */
    public static String readString(Reader reader) throws IOException {
        final StringBuilder sb = new StringBuilder(1024);
        // 申明一次读取缓冲区
        final char[] array = new char[256];
        // 这里并没有使用while(true),如果一个文本超过100W，还是放弃后面的算了
        while (sb.length() < MathUtils.MILLION) {
            int n = reader.read(array);
            // 读结束了，就GG了
            if (n < 0) {
                break;
            }
            sb.append(array, 0, n);
        }
        return sb.toString();
    }

    public static boolean isEmpty(String... array) {
        for (String str : array) {
            if (org.apache.commons.lang3.StringUtils.isEmpty(str)) {
                return true;
            }
        }
        return false;
    }

    public static boolean hasLength(String str) {
        return hasLength((CharSequence) str);
    }

    public static boolean hasLength(CharSequence str) {
        return (str != null && str.length() > 0);
    }

    public static boolean hasText(CharSequence str) {
        if (!hasLength(str)) {
            return false;
        }
        int strLen = str.length();
        for (int i = 0; i < strLen; i++) {
            if (!Character.isWhitespace(str.charAt(i))) {
                return true;
            }
        }
        return false;
    }

    public static boolean equals(final CharSequence cs1, final CharSequence cs2) {
        if (cs1 == cs2) {
            return true;
        }
        if (cs1 == null || cs2 == null) {
            return false;
        }
        if (cs1.length() != cs2.length()) {
            return false;
        }
        if (cs1 instanceof String && cs2 instanceof String) {
            return cs1.equals(cs2);
        }
        // Step-wise comparison
        final int length = cs1.length();
        for (int i = 0; i < length; i++) {
            if (cs1.charAt(i) != cs2.charAt(i)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 生成随机长度字符串
     */
    public static String randomString(int length) {
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < length; i++) {
            char ch = 0;
            if (RandomUtils.nextInt(1) == 0) {
                ch = (char) RandomUtils.nextInt('a', 'z');
            } else {
                ch = (char) RandomUtils.nextInt('A', 'Z');
            }
            builder.append(ch);
        }
        return builder.toString();
    }

    public static int getUTF8Length(String s) {
        return s.getBytes(StandardCharsets.UTF_8).length;
    }

    public static int getLengthWithChinese(String s, int n) {
        if (s == null) {
            return 0;
        }
        int sum = 0;
        for (int i = 0, len = s.length(); i < len; i++) {
            sum += s.charAt(i) > 127 ? n : 1;
        }
        return sum;
    }

    public static boolean checkLength(String s, int max, int min) {
        int len = getUTF8Length(s);
        if (min > 0 && len < min) {
            return false;
        }
        if (len > max) {
            return false;
        }
        return true;
    }

    public static boolean containsSpecialChar(String s) {
        String regEx = "[_`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？\\\\]|\n|\r|\t";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(s);
        return m.find();
    }

    public static boolean isOnlyAlphanumeric(String s) {
        String regex = "^[a-z0-9A-Z]+$";
        return s.matches(regex);
    }

    public static boolean isAlphanumericAndSpecialSymbols(String s) {
        String regex = "^[A-Za-z0-9,!':~@;/-=‘#&<>]+$";
        return s.matches(regex);
    }

    public static boolean checkLengthWithoutHtml(String s, int max, int min) {
        s = s.replaceAll("\\&[a-zA-Z]{1,10};", "").replaceAll("<[^>]*>", "").replaceAll("[(/>)<]", "");
        return checkLength(s, max, min);
    }

    /**
     * 检测字符串是否为 null或"".
     *
     * <pre>
     * StringUtils.isEmpty(null)      = true
     * StringUtils.isEmpty("")        = true
     * StringUtils.isEmpty(" ")       = false
     * StringUtils.isEmpty("test")     = false
     * StringUtils.isEmpty("  test  ") = false
     * </pre>
     *
     * @param text 被检测字符串
     * @return 如果字符为null或""则返回true,否则返回false.
     */
    public static boolean isEmpty(final String text) {
        return text == null || text.length() == 0;
    }

    /**
     * 计算MD5值
     *
     * @param input 待计算MD5的字符串
     * @return MD5字符串
     */
    public static String md5(String input) {
        if (isEmpty(input)) {
            return null;
        }
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(input.getBytes(StandardCharsets.UTF_8));
            byte[] byteArray = md5.digest();
            return DatatypeConverter.printHexBinary(byteArray);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 计算MD5值
     *
     * @param input 待计算MD5的字符串
     * @return MD5字符串
     */
    public static String md5(byte[] input) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(input);
            byte[] byteArray = md5.digest();
            return DatatypeConverter.printHexBinary(byteArray);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 只是为取一个值
     */
    public static long getStringHashId(String text, int num) {
        int hash = text.length();
        int maxLoop = Math.min(20, text.length());
        for (int i = 0; i < maxLoop; i++) {
            hash += text.charAt(i);
        }
        return (hash % num);
    }

    public static String reflectionToString(Object obj) {
        return ToStringBuilder.reflectionToString(obj, ToStringStyle.SHORT_PREFIX_STYLE);
    }

    public static String randomSuffix(int length) {
        List<Character> cList = STRING_SEED_POOL;
        if (length > cList.size()) {
            Collections.shuffle(cList);
        } else {
            cList = RandomUtils.randomList(cList, length);
        }
        StringBuilder sb = new StringBuilder();
        cList.forEach(sb::append);
        return sb.toString();
    }

    public static String randomSuffixByRandom(int length, Random random) {
        List<Character> cList = STRING_SEED_POOL;
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(cList.get(random.nextInt(cList.size())));
        }
        return sb.toString();
    }
}
package com.yorha.gemini.props;

/**
 * 提供监听能力的容器。
 *
 * <AUTHOR>
 */
public interface IPropListenerContainer {
//    interface IPropListener {
//        /**
//         * 触发属性变更时间。
//         *
//         * @param reason 触发原因。
//         */
//        void trigger(String reason);
//    }

    /**
     * 设置属性监听器。
     *
     * @param listener 监听器。
     */
    void setListener(PropertyChangeListener listener);

    /**
     * 获取属性监听器。
     *
     * @return 监听器。
     */
    PropertyChangeListener getListener();
}

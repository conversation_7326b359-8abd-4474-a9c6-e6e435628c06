package com.yorha.gemini.props;

import com.yorha.common.exception.GeminiException;
import org.apache.commons.lang3.NotImplementedException;

/**
 * 属性节点
 *
 * <AUTHOR>
 */
public abstract class AbstractPropNode implements ITreeMarkContainer, IPropListenerContainer {
    /**
     * 属性描述的掩码。
     * 00-14位：索引位
     * 15-30位：属性长度位
     */
    public static final int FIELD_DESCRIBE_MAX_FIELD_COUNT = 0x1 << Constant.FIELD_DESCRIBE_INDEX_BITS_SIZE;
    public static final int FIELD_DESCRIBE_FIELD_INDEX_MASK = FIELD_DESCRIBE_MAX_FIELD_COUNT - 1;
    public static final int FIELD_DESCRIBE_FIELD_COUNT_MASK = FIELD_DESCRIBE_FIELD_INDEX_MASK << Constant.FIELD_DESCRIBE_INDEX_BITS_SIZE;

    static {
        if (Integer.SIZE != 32) {
            throw new AssertionError("不支持整形非32位的平台!!!");
        }
    }

    /**
     * 父节点
     */
    private IMarkContainer parentNode;
    /**
     * 字段描述掩码.
     * 0-14位: fieldIndex
     * 15-29位: fieldCount
     */
    private final int fieldDescribeMask;

    /**
     * 构造函数
     *
     * @param parent     父亲节点
     * @param fieldIndex 在父亲节点的位置。
     * @param fieldCount 当前节点的属性数量。
     */
    protected AbstractPropNode(final ITreeMarkContainer parent, final int fieldIndex, final int fieldCount) {
        if (fieldCount <= 0 || fieldCount > FIELD_DESCRIBE_MAX_FIELD_COUNT) {
            throw new GeminiException("fieldCount {} not right!", fieldCount);
        }
        if (fieldIndex < 0 || fieldIndex >= FIELD_DESCRIBE_MAX_FIELD_COUNT) {
            throw new GeminiException("fieldIndex {} not right!", fieldIndex);
        }
        this.parentNode = parent;
        this.fieldDescribeMask = fieldIndex | (fieldCount << Constant.FIELD_DESCRIBE_INDEX_BITS_SIZE);
    }

    protected void markParentNode() {
        if (this.parentNode == null) {
            return;
        }
        if (this.parentNode.hasMark(this.getFieldIndex())) {
            return;
        }
        this.parentNode.mark(this.getFieldIndex());
    }

    /**
     * 去除父节点对应属性的标记
     */
    protected void unMarkParentNode() {
        if (this.parentNode == null) {
            return;
        }
        if (!this.parentNode.hasMark(this.getFieldIndex())) {
            return;
        }
        this.parentNode.unMark(this.getFieldIndex());
    }

    /**
     * @return 获取父节点
     */
    @Override
    public final IMarkContainer getParentNode() {
        return this.parentNode;
    }

    /**
     * 设置数据父节点
     *
     * @param parentNode 父亲节点
     */
    @Override
    public final void setParentNode(final IMarkContainer parentNode) {
        if (parentNode == null) {
            // 允许置空
        } else if (this.parentNode == null) {
            // 第一次赋值
        } else if (this.parentNode == parentNode) {
            // 反复放进map容器
        } else {
            throw new RuntimeException("setParentNode fail, maybe set twice, check the logic");
        }
        this.parentNode = parentNode;
    }

    /**
     * 获取在父节点中的位置
     *
     * @return 返回在父亲节点的位置
     */
    @Override
    public final int getFieldIndex() {
        return this.fieldDescribeMask & FIELD_DESCRIBE_FIELD_INDEX_MASK;
    }

    /**
     * 获取本节点的字段数量。
     *
     * @return 字段数量。
     */
    @Override
    public final int getFieldCount() {
        return (this.fieldDescribeMask & FIELD_DESCRIBE_FIELD_COUNT_MASK) >>> Constant.FIELD_DESCRIBE_INDEX_BITS_SIZE;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        throw new NotImplementedException("");
    }

    @Override
    public PropertyChangeListener getListener() {
        throw new NotImplementedException("");
    }

    @Override
    public String toString() {
        return this.getClass().getName() + "{" +
                "fieldIndex=" + this.getFieldIndex() +
                ", fieldCount=" + this.getFieldCount() +
                '}';
    }
}

package com.yorha.gemini.props;

import com.yorha.common.actor.IActorRef;
import org.jetbrains.annotations.NotNull;

/**
 * 可显式指定停止工作的PropertyChangeListener
 */
public class CanStopPropertyChangeListener extends PropertyChangeListener {
    private boolean isStop = false;

    public CanStopPropertyChangeListener(@NotNull Runnable runnable, IActorRef actorRef) {
        super(runnable, actorRef);
    }

    @Override
    public void trigger(String propName) {
        if (PropertyChangeListener.isStop) {
            return;
        }
        if (this.isStop) {
            return;
        }
        super.trigger(propName);

    }

    public void stop() {
        this.isStop = true;
    }
}

package com.yorha.gemini.props;

import com.yorha.common.actor.msg.FastFailActorRunnable;
import com.yorha.common.actor.ref.ActorSendMsgUtils;
import com.yorha.common.actor.IActorRef;
import com.yorha.common.exception.GeminiException;
import org.jetbrains.annotations.NotNull;

/**
 * 属性变更回调
 *
 * <AUTHOR>
 */
public class PropertyChangeListener {
    private final Runnable runnable;
    /**
     * 是否在任务队列中
     */
    private boolean isInTaskQueue = false;
    /**
     * Actor Ref
     */
    protected final IActorRef actorRef;

    public PropertyChangeListener(@NotNull Runnable runnable, IActorRef actorRef) {
        this.runnable = runnable;
        this.actorRef = actorRef;
    }

    public static volatile boolean isStop = false;

    /**
     * 外层禁止调用
     */
    public void dangerousRun() {
        try {
            this.runnable.run();
        } finally {
            isInTaskQueue = false;
        }
    }

    protected void realTell(String propName) {
        try {
            ActorSendMsgUtils.send(this.actorRef, new FastFailActorRunnable<>(
                    "prop.trigger." + propName,
                    (actor) -> {
                        dangerousRun();
                    }
            ));
        } catch (GeminiException e) {
            isInTaskQueue = false;
        }

    }

    public void trigger(String propName) {
        if (isStop) {
            return;
        }
        if (this.isInTaskQueue) {
            return;
        }
        try {
            this.isInTaskQueue = true;
            realTell(propName);
        } catch (Exception e) {
            this.isInTaskQueue = false;
            throw e;
        }
    }

    public boolean isInTaskQueue() {
        return this.isInTaskQueue;
    }

}

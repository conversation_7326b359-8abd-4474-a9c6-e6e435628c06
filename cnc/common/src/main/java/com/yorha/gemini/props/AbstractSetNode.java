package com.yorha.gemini.props;

import com.google.common.collect.Lists;
import com.yorha.common.exception.GeminiException;
import org.jetbrains.annotations.NotNull;

import java.util.*;

/**
 * 属性系统集合节点
 *
 * @param <E> 集合存储的数据结构类型，仅能是原生类型
 * <AUTHOR>
 */
public abstract class AbstractSetNode<E> implements ITreeMarkContainer {
    /**
     * 容器的默认子index
     */
    public static final int DEFAULT_FIELD_INDEX = 0;
    /**
     * 容器的属性数量
     */
    protected static final int FIELD_COUNT = 1;

    /**
     * 属性描述的掩码。
     * <p>
     * 00-09位：并发修改计数器
     * 10位：脏数据位
     * 11位：是否清除数据位
     * 12-26位：索引位
     */
    public static final int FIELD_DESCRIBE_MOD_COUNT_BIT_SIZE = 10;
    public static final int FIELD_DESCRIBE_DIRTY_BIT_SIZE = 1;
    public static final int FILED_DESCRIBE_CLEAR_BIT_SIZE = 1;
    public static final int FILED_DESCRIBE_MOD_MASK = (0x01 << FIELD_DESCRIBE_MOD_COUNT_BIT_SIZE) - 1;
    public static final int FIELD_DESCRIBE_DIRTY_MASK = (0x01 << FIELD_DESCRIBE_MOD_COUNT_BIT_SIZE);
    public static final int FIELD_DESCRIBE_CLEAR_MASK = (0x01 << (FIELD_DESCRIBE_MOD_COUNT_BIT_SIZE + 1));
    public static final int FILED_DESCRIBE_INDEX_BIT_MOVE_SIZE = FIELD_DESCRIBE_MOD_COUNT_BIT_SIZE + FIELD_DESCRIBE_DIRTY_BIT_SIZE + FILED_DESCRIBE_CLEAR_BIT_SIZE;
    public static final int FIELD_DESCRIBE_INDEX_MASK = ((0x01 << Constant.FIELD_DESCRIBE_INDEX_BITS_SIZE) - 1) << FILED_DESCRIBE_INDEX_BIT_MOVE_SIZE;

    private final Set<E> dataAll;
    /**
     * 脏的键，当且仅当键存在与dataAll中时，标记键为脏
     */
    private Set<E> addKeys;
    /**
     * 键需要被清理
     */
    private Set<E> deleteKeys;
    /**
     * 父节点
     */
    private IMarkContainer parentNode;
    /**
     * 描述关键字
     */
    private int fieldDescribeMask;

    /**
     * 构造函数
     *
     * @param parent     父节点
     * @param fieldIndex 当前节点位于父节点的位置索引
     */
    public AbstractSetNode(final IMarkContainer parent, int fieldIndex) {
        final int fieldIndexMask = fieldIndex << FILED_DESCRIBE_INDEX_BIT_MOVE_SIZE;
        if ((fieldIndexMask & FIELD_DESCRIBE_INDEX_MASK) != fieldIndexMask) {
            throw new GeminiException("fieldIndex {} not right!", fieldIndex);
        }
        this.parentNode = parent;
        this.fieldDescribeMask = fieldIndexMask;
        this.dataAll = new HashSet<>();
    }

    /**
     * @return 父亲节点。
     */
    @Override
    public IMarkContainer getParentNode() {
        return this.parentNode;
    }

    /**
     * 设置容器对应的父亲节点。
     *
     * @param parentNode 父亲节点。
     */
    @Override
    public void setParentNode(final IMarkContainer parentNode) {
        if (parentNode == null) {
            // 允许置空
            this.parentNode = null;
        } else if (this.parentNode == null) {
            // 第一次赋值
            this.parentNode = parentNode;
        } else if (this.parentNode == parentNode) {
            // 反复放进map容器
            return;
        }
        throw new RuntimeException("setParentNode fail, maybe set twice, check the logic");
    }

    /**
     * @return 返回对象对应父容器的标脏索引。
     */
    @Override
    public int getFieldIndex() {
        return (this.fieldDescribeMask & FIELD_DESCRIBE_INDEX_MASK) >>> FILED_DESCRIBE_INDEX_BIT_MOVE_SIZE;
    }

    /**
     * @return 对象包含的属性数量。
     */
    @Override
    public int getFieldCount() {
        return FIELD_COUNT;
    }

    /**
     * 是否标记clear
     *
     * @return 是否处于clear状态
     */
    public final boolean isClearFlag() {
        return (this.fieldDescribeMask & FIELD_DESCRIBE_CLEAR_MASK) != 0;
    }

    /**
     * @return 容器内包含的元素数量。
     */
    public final int size() {
        return this.dataAll.size();
    }

    /**
     * @return 容器中元素是否为空。
     */
    public final boolean isEmpty() {
        return this.dataAll.isEmpty();
    }

    /**
     * @param o 目标元素
     * @return 是否包含某元素。
     */
    public final boolean contains(final E o) {
        return this.dataAll.contains(o);
    }

    /**
     * 新增元素。
     *
     * @param e 数据。
     * @return 是否新增成功。
     */
    public final boolean add(final E e) {
        if (!this.dataAll.add(e)) {
            return false;
        }
        // 必须要保持操作记录，否则fullAttr持有部分changeAttr的时候合并会有问题
        this.getInnerDeleteKeys().remove(e);
        this.getInnerAddKeys().add(e);
        this.markDirty();
        this.markParentNode();
        return true;
    }

    /**
     * 删除元素。
     *
     * @param o 元素。
     * @return 是否删除成功。
     */
    public final boolean remove(final E o) {
        if (!this.dataAll.remove(o)) {
            return false;
        }
        // 必须要保持操作记录，否则fullAttr持有部分changeAttr的时候合并会有问题
        this.getInnerAddKeys().remove(o);
        this.getInnerDeleteKeys().add(o);
        this.markDirty();
        this.markParentNode();
        this.incModCount();
        return true;
    }


    /**
     * 删除结合中所有元素。
     *
     * @param c 集合。
     * @return 内容是否变更。
     */
    public final boolean removeAll(final Collection<E> c) {
        boolean isChanged = false;
        for (final E o : c) {
            isChanged = this.remove(o) || isChanged;
        }
        return isChanged;
    }

    /**
     * 清理集合。
     */
    public final void clear() {
        // 清空新增的数据的记录
        this.addKeys = null;
        // 清空需要被清理的数据的记录
        this.deleteKeys = null;
        // 本来就没有元素的情况下clear，不进行标脏
        if (this.isEmpty()) {
            this.markClearWhenDirty();
            return;
        }
        // 清理数据
        this.dataAll.clear();
        // 设置清理flag
        this.markClearAndDirty();
        this.markParentNode();
        this.incModCount();
    }

    /**
     * @param c 保存集合中的数据。
     * @return 集合是否被修改。
     */
    public boolean retainAll(@NotNull Collection<E> c) {
        boolean modified = false;
        final Iterator<E> it = iterator();
        while (it.hasNext()) {
            if (c.contains(it.next())) {
                continue;
            }
            it.remove();
            modified = true;
        }
        return modified;
    }

    @Override
    public void mark(int index) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void unMark(int index) {
        throw new UnsupportedOperationException();
    }

    @Override
    public boolean hasMark(final int index) {
        if (index != DEFAULT_FIELD_INDEX) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        return this.isDirty();
    }

    @Override
    public final void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.deleteKeys != null && this.deleteKeys.size() < 10) {
            this.deleteKeys.clear();
        } else {
            this.deleteKeys = null;
        }
        if (this.addKeys != null && this.addKeys.size() < 10) {
            this.addKeys.clear();
        } else {
            this.addKeys = null;
        }
        this.unMarkClearAndDirty();
        this.unMarkParentNode();
    }

    @Override
    public final void markAll() {
        this.getInnerAddKeys().addAll(this.dataAll);
        this.markDirty();
        this.markParentNode();
    }

    @Override
    public boolean hasAnyMark() {
        return this.isDirty();
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (this.getClass() != node.getClass()) {
            return false;
        }
        @SuppressWarnings({"rawtypes"}) final AbstractSetNode otherSet = (AbstractSetNode) node;
        if (this.dataAll.size() != otherSet.dataAll.size()) {
            return false;
        }
        if (this.isClearFlag() != otherSet.isClearFlag()) {
            return false;
        }
        for (final E entry : this.dataAll) {
            if (!otherSet.dataAll.contains(entry)) {
                return false;
            }
        }
        return true;
    }

    public boolean containsAll(final @NotNull Collection<E> c) {
        return this.dataAll.containsAll(c);
    }

    public final Collection<E> getValues() {
        return new KeySetView<>(this);
    }

    public final Iterator<E> iterator() {
        return new PropSetIterator<>(this);
    }

    /**
     * 添加集合中的所有数据。。
     *
     * @param c 集合。
     * @return 数据是否变更。
     */
    public boolean addAll(final Collection<? extends E> c) {
        boolean isChange = false;
        for (final E e : c) {
            isChange = this.add(e) || isChange;
        }
        return isChange;
    }

    /**
     * @return delete keys集合。
     */
    public final Set<E> getDeleteKeys() {
        if (this.deleteKeys == null) {
            return Collections.emptySet();
        }
        return Collections.unmodifiableSet(this.deleteKeys);
    }

    /**
     * @return add keys集合。
     */
    public final Set<E> getAddKeys() {
        if (this.addKeys == null) {
            return Collections.emptySet();
        }
        return Collections.unmodifiableSet(this.addKeys);
    }

    /**
     * 获取需要删除的keys
     *
     * @return key集合
     */
    protected final Set<E> getInnerDeleteKeys() {
        if (this.deleteKeys == null) {
            this.deleteKeys = new HashSet<>();
        }
        return this.deleteKeys;
    }

    /**
     * 获取修改【新增k.v、修改v】的keys
     *
     * @return key集合
     */
    protected final Set<E> getInnerAddKeys() {
        if (this.addKeys == null) {
            this.addKeys = new HashSet<>();
        }
        return this.addKeys;
    }

    /**
     * 标记父节点。
     */
    private void markParentNode() {
        if (this.parentNode == null) {
            return;
        }
        if (this.parentNode.hasMark(this.getFieldIndex())) {
            return;
        }
        this.parentNode.mark(this.getFieldIndex());
    }

    /**
     * 取消父节点标记。
     */
    private void unMarkParentNode() {
        if (this.parentNode == null) {
            return;
        }
        if (!this.parentNode.hasMark(this.getFieldIndex())) {
            return;
        }
        this.parentNode.unMark(this.getFieldIndex());
    }

    /**
     * @return 容器是否有数据修改。
     */
    private boolean isDirty() {
        return (this.fieldDescribeMask & FIELD_DESCRIBE_DIRTY_MASK) != 0;
    }

    /**
     * @return 容器的变更次数。
     */
    private int getModCount() {
        return (this.fieldDescribeMask & FILED_DESCRIBE_MOD_MASK);
    }

    /**
     * 标记容器有数据变更。
     */
    private void markDirty() {
        this.fieldDescribeMask = this.fieldDescribeMask | FIELD_DESCRIBE_DIRTY_MASK;
    }

    /**
     * 当前处于容器有数据变更前提下，设置当前容器处于clear状态。
     */
    private void markClearWhenDirty() {
        this.fieldDescribeMask = this.fieldDescribeMask | ((FIELD_DESCRIBE_DIRTY_MASK & this.fieldDescribeMask) << 1);
    }

    /**
     * 取消容器的clear状态和变更状态标记。
     */
    private void unMarkClearAndDirty() {
        this.fieldDescribeMask = this.fieldDescribeMask & (~(FIELD_DESCRIBE_CLEAR_MASK | FIELD_DESCRIBE_DIRTY_MASK));
    }

    /**
     * 标记容器处于clear状态且状态变更。
     */
    private void markClearAndDirty() {
        this.fieldDescribeMask = this.fieldDescribeMask | (FIELD_DESCRIBE_CLEAR_MASK | FIELD_DESCRIBE_DIRTY_MASK);
    }

    /**
     * 自增修改次数。
     */
    private void incModCount() {
        final int modCount = (this.getModCount() + 1) & FILED_DESCRIBE_MOD_MASK;
        this.fieldDescribeMask = (this.fieldDescribeMask & (~FILED_DESCRIBE_MOD_MASK)) | modCount;
    }

    // ----------------------------- Views ---------------------------------
    abstract static class CollectionView<E> implements Collection<E> {
        protected final AbstractSetNode<E> setNode;

        protected CollectionView(final AbstractSetNode<E> setNode) {
            this.setNode = setNode;
        }

        @Override
        public abstract void clear();

        @Override
        public abstract int size();

        @Override
        public abstract boolean isEmpty();

        @Override
        public abstract @NotNull Iterator<E> iterator();

        @Override
        public abstract boolean contains(Object o);

        @Override
        public abstract boolean remove(Object o);

        @Override
        public final String toString() {
            Iterator<E> it = iterator();
            // 可能没有元素
            if (!it.hasNext()) {
                return "[]";
            }
            StringBuilder sb = new StringBuilder();
            sb.append('[');
            for (; ; ) {
                E e = it.next();
                sb.append(e == this ? "(this Collection)" : e);
                if (!it.hasNext()) {
                    return sb.append(']').toString();
                }
                sb.append(',').append(' ');
            }
        }

        @Override
        public final boolean containsAll(@NotNull final Collection<?> c) {
            if (c != this) {
                for (final Object e : c) {
                    if (e == null || !contains(e)) {
                        return false;
                    }
                }
            }
            return true;
        }

        @Override
        public final boolean removeAll(final Collection<?> c) {
            if (c == null) {
                throw new NullPointerException();
            }
            boolean modified = false;
            for (final Iterator<?> it = this.iterator(); it.hasNext(); ) {
                if (c.contains(it.next())) {
                    it.remove();
                    modified = true;
                }
            }
            return modified;
        }

        @Override
        public final boolean retainAll(final Collection<?> c) {
            if (c == null) {
                throw new NullPointerException();
            }
            boolean modified = false;
            for (final Iterator<?> it = this.iterator(); it.hasNext(); ) {
                if (!c.contains(it.next())) {
                    it.remove();
                    modified = true;
                }
            }
            return modified;
        }
    }

    static final class KeySetView<E> extends CollectionView<E> implements Collection<E> {

        KeySetView(AbstractSetNode<E> setNode) {
            super(setNode);
        }

        @Override
        public void clear() {
            this.setNode.clear();
        }

        @Override
        public int size() {
            return this.setNode.size();
        }

        @Override
        public boolean isEmpty() {
            return this.setNode.isEmpty();
        }

        @Override
        public @NotNull Iterator<E> iterator() {
            return new PropSetIterator<>(this.setNode);
        }

        @NotNull
        @Override
        public Object[] toArray() {
            return this.setNode.dataAll.toArray();
        }

        @NotNull
        @Override
        public <T> T[] toArray(@NotNull final T[] a) {
            return this.setNode.dataAll.toArray(a);
        }

        @Override
        public boolean add(final E e) {
            return this.setNode.add(e);
        }

        @Override
        @SuppressWarnings("unchecked")
        public boolean contains(final Object o) {
            return this.setNode.contains((E) o);
        }

        @Override
        @SuppressWarnings("unchecked")
        public boolean remove(final Object o) {
            return this.setNode.remove((E) o);
        }

        @Override
        public boolean addAll(@NotNull final Collection<? extends E> c) {
            return this.setNode.addAll(c);
        }

        @Override
        public Spliterator<E> spliterator() {
            throw new UnsupportedOperationException();
        }
    }

    static final class PropSetIterator<E> implements Iterator<E> {
        private final AbstractSetNode<E> set;
        private final List<E> dataList;
        private int lastRet;
        private int cursor;
        private int expectedModCount;

        PropSetIterator(final AbstractSetNode<E> set) {
            this.set = set;
            this.expectedModCount = set.getModCount();
            this.dataList = Lists.newArrayList(set.dataAll);
            this.lastRet = -1;
            this.cursor = 0;
        }

        @Override
        public boolean hasNext() {
            if (this.expectedModCount != this.set.getModCount()) {
                throw new ConcurrentModificationException();
            }
            return this.cursor < this.dataList.size();
        }

        @Override
        public E next() {
            if (!this.hasNext()) {
                throw new NoSuchElementException();
            }
            this.lastRet = this.cursor++;
            return this.getValue();
        }

        @Override
        public void remove() {
            final E value = this.getValue();
            if (set.getModCount() != expectedModCount) {
                throw new ConcurrentModificationException();
            }
            if (!this.set.remove(value)) {
                throw new ConcurrentModificationException(value.toString());
            }
            this.expectedModCount = this.set.getModCount();
            this.lastRet = -1;
        }

        private E getValue() {
            if (this.lastRet == -1) {
                throw new IllegalStateException();
            }
            return this.dataList.get(this.lastRet);
        }
    }
}

package com.yorha.gemini.navmesh;
// jni生成的C++头文件含java package，若修改package则需再生成dll、so

import com.yorha.common.Easy3dNav.EasyNavFunc;
import com.yorha.common.Easy3dNav.Vector3f;
import com.yorha.common.utils.FileUtils;
import com.yorha.common.utils.NativeUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;

/**
 * recast native 接口封装
 *
 * <AUTHOR> Jiang
 */
public class GeminiNav implements EasyNavFunc {
    private static final Logger LOGGER = LogManager.getLogger(GeminiNav.class);
    private static final ReentrantReadWriteLock LOCK = new ReentrantReadWriteLock();

    private int id;

    static {
        try {
            NativeUtils.loadLibrary("recast");
        } catch (Throwable e) {
            LOGGER.error(e.getMessage(), e);
            System.exit(1);
        }

    }

    public void init(int id, String filePath) throws IOException {
        File file = new File(filePath);
        byte[] content = FileUtils.readFileToByteArray(file);
        LOGGER.info("navmesh file id:{},size:{}", id, content.length);
        try {
            LOCK.writeLock().lock();
            this.id = this.load(id, content, content.length);
        } catch (Throwable e) {
            LOGGER.error(e);
        } finally {
            LOCK.writeLock().unlock();
        }
        if (this.id < 0) {
            throw new IOException("load map failed,mapId" + id);
        }
    }

    /**
     * 加载地图
     *
     * @param navmeshId 寻路数据地图ID
     * @param content   地图文件的路径例
     * @param length    数据长度
     * @return navmeshId, 为负数表示加载失败，为正数表示加载成功，后续寻路时传入此id为参数 由于这个id可能传0 所以0也是成功的
     */
    private native int load(int navmeshId, byte[] content, int length);

    /**
     * 寻路
     *
     * @param navmeshId 寻路数据地图ID
     * @param startX    起始点X
     * @param startY    起始点Y
     * @param endX      结束点X
     * @param endY      结束点Y
     * @return 返回路径点列表，注意，查找不到时，会返回空
     */
    private native float[] find(int navmeshId, float startX, float startY, float endX, float endY);

    /**
     * 找到目标点最近的静态可达点
     *
     * @param navmeshId 寻路数据地图ID
     * @param pointX    参考点X
     * @param pointY    参考点Y
     * @return 如果目标点可达，返回目标点即可， 如果搜索范围内没有，返回空
     */
    private native float[] findNearest(int navmeshId, float pointX, float pointY);


    /**
     * 光线照射发，寻找可以支线通过的hit点，如果可通过则返回hit
     *
     * @param navmeshId 寻路数据地图ID
     * @param startX    起始点X
     * @param startY    起始点Y
     * @param endX      结束点X
     * @param endY      结束点Y
     * @return 返回射线射过去遇到的第一个阻挡点，如果到终点都没有阻挡，返回终点
     */
    private native float[] raycast(int navmeshId, float startX, float startY, float endX, float endY);


    /**
     * 释放加载的地图数据
     *
     * @param navmeshId 寻路数据地图ID
     */
    private native void release(int navmeshId);


    /**
     * 释放加载的所有地图数据
     */
    private native void releaseAll();

    @Override
    public List<float[]> find(float[] start, float[] end) {
        List<float[]> ret = new ArrayList<>();
        float[] array;
        try {
            LOCK.readLock().lock();
            array = this.find(this.id, start[0], start[2], end[0], end[2]);
        } finally {
            LOCK.readLock().unlock();
        }
        if (array == null) {
            return ret;
        }
        int length = array.length / 3;
        for (int i = 0; i < length; i++) {
            float[] point = new float[3];
            int index = i * 3;
            point[0] = array[index];
            point[1] = array[index + 1];
            point[2] = array[index + 2];
            ret.add(point);
        }
        return ret;
    }

    @Override
    public List<Vector3f> find(Vector3f start, Vector3f end) {
        List<float[]> paths = find(v3fToFArr(start), v3fToFArr(end));
        return paths.stream().map(p -> new Vector3f(p[0], p[1], p[2])).collect(Collectors.toList());
    }

    @Override
    public float[] raycast(float[] start, float[] end) {
        try {
            LOCK.readLock().lock();
            return this.raycast(id, start[0], start[2], end[0], end[2]);
        } finally {
            LOCK.readLock().unlock();
        }
    }

    @Override
    public Vector3f raycast(Vector3f start, Vector3f end) {
        Vector3f ret = null;
        float[] point = raycast(v3fToFArr(start), v3fToFArr(end));
        if (point != null) {
            ret = FArrTov3f(point);
        }
        return ret;
    }

    @Override
    public float[] findNearest(float[] point) {
        try {
            LOCK.readLock().lock();
            return findNearest(id, point[0], point[2]);
        } finally {
            LOCK.readLock().unlock();
        }
    }

    @Override
    public Vector3f findNearest(Vector3f point) {
        Vector3f ret = null;
        float[] p = this.findNearest(v3fToFArr(point));
        if (p != null) {
            ret = FArrTov3f(p);
        }
        return ret;
    }

    public void release() {
        if (id < 0) {
            LOGGER.error("map not exist");
            return;
        }
        try {
            LOCK.writeLock().lock();
            release(id);
        } finally {
            LOCK.writeLock().unlock();
        }
    }

    public void releaseAllNav() {
        try {
            LOCK.writeLock().lock();
            releaseAll();
        } finally {
            LOCK.writeLock().unlock();
        }
    }
}


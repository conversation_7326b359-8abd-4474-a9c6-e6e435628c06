package com.yorha.gemini.props;

import com.yorha.common.exception.GeminiException;
import org.apache.commons.lang3.NotImplementedException;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.function.UnaryOperator;


/**
 * 属性系统列表节点。
 * 注意：链表是一种不支持增量的数据结构。
 *
 * <AUTHOR>
 */
public abstract class AbstractListNode<V> implements List<V>, ITreeMarkContainer {
    /**
     * 容器的默认子index
     */
    public static final int DEFAULT_FIELD_INDEX = 0;
    /**
     * 容器的属性数量
     */
    protected static final int FIELD_COUNT = 1;
    /**
     * 属性描述的掩码。
     * 00-14位：是索引位。
     * 15位   ：脏数据位。
     */
    public static final int FIELD_DESCRIBE_INDEX_MASK = (0x1 << Constant.FIELD_DESCRIBE_INDEX_BITS_SIZE) - 1;
    public static final int FIELD_DESCRIBE_DIRTY_MASK = (0x1 << Constant.FIELD_DESCRIBE_INDEX_BITS_SIZE);

    // 平台检查
    static {
        if (Integer.SIZE != 32) {
            throw new AssertionError("不支持整形非32位的平台!!!");
        }
    }

    /**
     * 父节点
     */
    private IMarkContainer parentNode;
    /**
     * 字段描述掩码.
     * 0-15位: fieldIndex
     * 16位：数据是否为脏
     */
    private int fieldDescribeMask;
    /**
     * 真实数据容器
     */
    private final List<V> dataAll;

    /**
     * 构造函数
     *
     * @param parent          父节点
     * @param fieldIndex      当前节点位于父节点的位置索引
     * @param initialCapacity list的初始容量
     */
    public AbstractListNode(final IMarkContainer parent, final int fieldIndex, final int initialCapacity) {
        if ((fieldIndex & FIELD_DESCRIBE_INDEX_MASK) != fieldIndex) {
            throw new GeminiException("fieldIndex {} not right!", fieldIndex);
        }
        this.dataAll = new ArrayList<>(initialCapacity);
        this.parentNode = parent;
        this.fieldDescribeMask = fieldIndex & FIELD_DESCRIBE_INDEX_MASK;
    }

    protected AbstractListNode(final IMarkContainer parent, final int fieldIndex) {
        this(parent, fieldIndex, 0);
    }

    /**
     * @return 尾插的空值。
     */
    public abstract V addEmptyValue();

    /**
     * @return 对应父容器的属性索引。
     */
    @Override
    public final int getFieldIndex() {
        return this.fieldDescribeMask & FIELD_DESCRIBE_INDEX_MASK;
    }

    /**
     * @return 当前容器包含的字段数。
     */
    @Override
    public final int getFieldCount() {
        return FIELD_COUNT;
    }

    /**
     * @param parentNode 需要设置的父容器节点。
     */
    @Override
    public final void setParentNode(IMarkContainer parentNode) {
        if (parentNode == null) {
            // 允许置空
        } else if (this.parentNode == null) {
            // 第一次赋值
        } else if (this.parentNode == parentNode) {
            // 反复放进map容器
        } else {
            throw new RuntimeException("setParentNode fail, maybe set twice, check the logic");
        }
        this.parentNode = parentNode;
    }

    /**
     * @return 父容器节点。
     */
    @Override
    public final IMarkContainer getParentNode() {
        return this.parentNode;
    }

    @Override
    public final int size() {
        return this.dataAll.size();
    }

    @Override
    public final boolean isEmpty() {
        return this.dataAll.isEmpty();
    }

    @Override
    public final boolean contains(Object v) {
        return this.dataAll.contains(v);
    }

    @Override
    public final Iterator<V> iterator() {
        return new PropListIterator<>(this);
    }

    @Override
    public final Object[] toArray() {
        return dataAll.toArray();
    }

    @Override
    public final <T> T[] toArray(@NotNull T[] a) {
        return dataAll.toArray(a);
    }

    @Override
    public final boolean add(final V v) {
        if (v instanceof ITreeMarkContainer && (((ITreeMarkContainer) v).getParentNode() != null)) {
            throw new RuntimeException(v.toString() + " is already belong to other container!");
        }
        this.dataAll.add(v);
        if (v instanceof ITreeMarkContainer) {
            ((ITreeMarkContainer) v).markAll();
            ((ITreeMarkContainer) v).setParentNode(this);
        }
        // 标脏
        this.markDirty();
        this.markParentNode();
        return true;
    }

    @Override
    public final boolean remove(Object o) {
        final int index = this.dataAll.indexOf(o);
        if (index < 0) {
            return false;
        }
        final V remove = this.remove(index);
        return remove != null;
    }

    @Override
    public final boolean containsAll(Collection<?> c) {
        return this.dataAll.containsAll(c);
    }

    @Override
    public final boolean addAll(final Collection<? extends V> c) {
        return this.addAll(this.size(), c);
    }

    @Override
    public final boolean addAll(final int index, final Collection<? extends V> c) {
        if (c == null) {
            return false;
        }
        for (final V v : c) {
            if (!(v instanceof ITreeMarkContainer)) {
                continue;
            }
            if (((ITreeMarkContainer) v).getParentNode() == null) {
                continue;
            }
            throw new RuntimeException(v.toString() + " is already belong to other container!");
        }
        if (!this.dataAll.addAll(index, c)) {
            return false;
        }
        for (final V v : c) {
            if (!(v instanceof ITreeMarkContainer)) {
                continue;
            }
            ((ITreeMarkContainer) v).markAll();
            ((ITreeMarkContainer) v).setParentNode(this);
        }
        // 标脏
        this.markDirty();
        this.markParentNode();
        return true;
    }

    @Override
    public final boolean removeAll(final Collection<?> c) {
        if (c == null) {
            return false;
        }
        if (!this.dataAll.removeAll(c)) {
            return false;
        }
        for (final Object v : c) {
            if (!(v instanceof ITreeMarkContainer)) {
                continue;
            }
            if (((ITreeMarkContainer) v).getParentNode() != this) {
                continue;
            }
            ((ITreeMarkContainer) v).setParentNode(null);
            ((ITreeMarkContainer) v).unMarkAll();
        }
        // 标脏
        this.markDirty();
        this.markParentNode();
        return true;
    }

    @Override
    public final boolean retainAll(final Collection<?> c) {
        final List<V> delayRemoveList = new ArrayList<>(c.size());
        for (final V v : this.dataAll) {
            if (c.contains(v)) {
                continue;
            }
            delayRemoveList.add(v);
        }
        if (!delayRemoveList.isEmpty()) {
            return this.removeAll(delayRemoveList);
        }
        return false;
    }

    @Override
    @Deprecated
    public void replaceAll(UnaryOperator<V> operator) {
        throw new NotImplementedException();
    }

    @Override
    @Deprecated
    public void sort(Comparator<? super V> c) {
        throw new NotImplementedException();
    }

    @Override
    public final void clear() {
        if (this.dataAll.isEmpty()) {
            return;
        }
        for (final V v : this.dataAll) {
            if (!(v instanceof ITreeMarkContainer)) {
                continue;
            }
            ((ITreeMarkContainer) v).setParentNode(null);
            ((ITreeMarkContainer) v).unMarkAll();
        }
        // 清理保存的数据
        this.dataAll.clear();
        // 标脏
        this.markDirty();
        this.markParentNode();
    }

    @Override
    public final V get(int index) {
        return this.dataAll.get(index);
    }

    @Override
    public final V set(final int index, final V v) {
        if (v instanceof ITreeMarkContainer && (((ITreeMarkContainer) v).getParentNode() != null)) {
            throw new RuntimeException(v.toString() + " is already belong to other container!");
        }
        final V elder = this.dataAll.set(index, v);
        if (elder instanceof ITreeMarkContainer) {
            ((ITreeMarkContainer) elder).setParentNode(null);
            ((ITreeMarkContainer) elder).unMarkAll();
            assert v instanceof ITreeMarkContainer;
            ((ITreeMarkContainer) v).markAll();
            ((ITreeMarkContainer) v).setParentNode(this);
        }
        // 标脏
        this.markDirty();
        this.markParentNode();
        return elder;
    }

    @Override
    public final void add(final int index, final V v) {
        if (v instanceof ITreeMarkContainer && (((ITreeMarkContainer) v).getParentNode() != null)) {
            throw new RuntimeException(v.toString() + " is already belong to other container!");
        }
        this.dataAll.add(index, v);
        if (v instanceof ITreeMarkContainer) {
            ((ITreeMarkContainer) v).markAll();
            ((ITreeMarkContainer) v).setParentNode(this);
        }
        // 标脏
        this.markDirty();
        this.markParentNode();
    }

    @Override
    public final V remove(int index) {
        final V remove = this.dataAll.remove(index);
        if (remove == null) {
            return null;
        }
        if (remove instanceof ITreeMarkContainer) {
            ((ITreeMarkContainer) remove).setParentNode(null);
            ((ITreeMarkContainer) remove).unMarkAll();
        }
        // 标脏
        this.markDirty();
        this.markParentNode();
        return remove;
    }

    /**
     * 获取容器中元素的索引
     *
     * @param o element
     * @return -1 if not exists in this container, or return index
     */
    @Override
    public final int indexOf(Object o) {
        return this.dataAll.indexOf(o);
    }

    /**
     * 获取容器中元素的索引
     *
     * @param o element
     * @return 元素索引。
     */
    @Override
    public final int lastIndexOf(Object o) {
        return this.dataAll.lastIndexOf(o);
    }

    /**
     * 列表迭代
     *
     * @return 列表迭代对象
     */
    @NotNull
    @Override
    public final ListIterator<V> listIterator() {
        return new PropListIterator<>(this);
    }

    /**
     * 列表迭代
     *
     * @param index 起始索引
     * @return 列表迭代对象
     */
    @Override
    public final ListIterator<V> listIterator(int index) {
        return new PropListIterator<>(this, index);
    }

    /**
     * 获取子列表
     *
     * @param fromIndex 起始索引（包含）
     * @param toIndex   最终索引（不包含）
     * @return 子列表
     */
    @Override
    public final List<V> subList(int fromIndex, int toIndex) {
        return this.dataAll.subList(fromIndex, toIndex);
    }

    @Override
    @Deprecated
    public final Spliterator<V> spliterator() {
        return Collections.unmodifiableList(this.dataAll).spliterator();
    }

    @Override
    public final void mark(int index) {
        if (index != DEFAULT_FIELD_INDEX) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        this.markDirty();
        this.markParentNode();
    }

    @Override
    public final void unMark(int index) {
        if (index != DEFAULT_FIELD_INDEX) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        this.markUnDirty();
        this.unMarkParentNode();
    }

    @Override
    public final boolean hasMark(final int index) {
        if (index != DEFAULT_FIELD_INDEX) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        return this.isDirty();
    }

    @Override
    public final boolean hasAnyMark() {
        return this.isDirty();
    }

    @Override
    public final void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        for (final V v : this.dataAll) {
            if (!(v instanceof ITreeMarkContainer)) {
                continue;
            }
            ((ITreeMarkContainer) v).unMarkAll();
        }
        this.markUnDirty();
        this.unMarkParentNode();
    }

    @Override
    public final void markAll() {
        for (final V v : this.dataAll) {
            if (v instanceof ITreeMarkContainer) {
                ((ITreeMarkContainer) v).markAll();
            }
        }
        this.markDirty();
        this.markParentNode();
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (node.getClass() != this.getClass()) {
            return false;
        }
        final AbstractListNode otherList = (AbstractListNode) node;
        if (otherList.dataAll.size() != this.dataAll.size()) {
            return false;
        }
        for (int i = 0; i < this.dataAll.size(); i++) {
            V a = this.dataAll.get(i);
            V b = (V) otherList.dataAll.get(i);
            if (a instanceof AbstractPropNode) {
                if (!((AbstractPropNode) a).compareDataTo((AbstractPropNode) b)) {
                    return false;
                }
            } else {
                if (!Objects.equals(a, b)) {
                    return false;
                }
            }
        }
        return true;
    }

    private void markParentNode() {
        if (this.parentNode == null) {
            return;
        }
        if (this.parentNode.hasMark(this.getFieldIndex())) {
            return;
        }
        this.parentNode.mark(this.getFieldIndex());
    }

    private void unMarkParentNode() {
        if (this.parentNode == null) {
            return;
        }
        if (!this.parentNode.hasMark(this.getFieldIndex())) {
            return;
        }
        this.parentNode.unMark(this.getFieldIndex());
    }

    private boolean isDirty() {
        return (this.fieldDescribeMask & FIELD_DESCRIBE_DIRTY_MASK) != 0;
    }

    private void markDirty() {
        this.fieldDescribeMask = FIELD_DESCRIBE_DIRTY_MASK | this.fieldDescribeMask;
    }

    private void markUnDirty() {
        this.fieldDescribeMask = (~FIELD_DESCRIBE_DIRTY_MASK) & this.fieldDescribeMask;
    }

    // NOTE(furson): 抄的ArrayList的ListIterator的代码
    static final class PropListIterator<E> implements ListIterator<E> {
        private final AbstractListNode<E> list;
        private int cursor;
        private int lastRet;

        PropListIterator(final AbstractListNode<E> list) {
            this(list, 0);
        }

        PropListIterator(final AbstractListNode<E> list, final int cursor) {
            if (cursor < 0 || cursor > list.size()) {
                throw new IndexOutOfBoundsException();
            }
            this.list = list;
            this.cursor = cursor;
            this.lastRet = -1;
        }

        @Override
        public boolean hasNext() {
            return this.cursor < this.list.size();
        }

        @Override
        public E next() {
            if (!this.hasNext()) {
                throw new NoSuchElementException();
            }
            this.lastRet = this.cursor++;
            return this.list.get(this.lastRet);
        }

        @Override
        public boolean hasPrevious() {
            return this.cursor > 0;
        }

        @Override
        public E previous() {
            if (!this.hasPrevious()) {
                throw new NoSuchElementException();
            }
            this.lastRet = --this.cursor;
            return this.list.get(this.lastRet);
        }

        @Override
        public int nextIndex() {
            return this.cursor;
        }

        @Override
        public int previousIndex() {
            return this.cursor - 1;
        }

        @Override
        public void remove() {
            if (this.lastRet < 0) {
                throw new IllegalStateException();
            }
            try {
                this.list.remove(this.lastRet);
                this.cursor = this.lastRet;
                this.lastRet = -1;
            } catch (IndexOutOfBoundsException ex) {
                throw new ConcurrentModificationException();
            }
        }

        @Override
        public void set(E e) {
            if (this.lastRet < 0) {
                throw new IllegalStateException();
            }
            try {
                this.list.set(this.lastRet, e);
            } catch (IndexOutOfBoundsException ex) {
                throw new ConcurrentModificationException();
            }
        }

        @Override
        public void add(E e) {
            try {
                this.list.add(this.cursor++, e);
                this.lastRet = -1;
            } catch (IndexOutOfBoundsException ex) {
                throw new ConcurrentModificationException();
            }
        }
    }
}

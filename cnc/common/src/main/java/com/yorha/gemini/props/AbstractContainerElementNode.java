package com.yorha.gemini.props;

/**
 * element of container
 *
 * <AUTHOR>
 */
public abstract class AbstractContainerElementNode<K> extends AbstractPropNode {
    /**
     * 构造函数
     *
     * @param parent     父节点
     * @param fieldIndex 当前容器位于父容器的索引
     * @param fieldCount 当前容器包含属性数量
     */
    public AbstractContainerElementNode(final ITreeMarkContainer parent, final int fieldIndex, final int fieldCount) {
        super(parent, fieldIndex, fieldCount);
    }

    public AbstractContainerElementNode(int fieldIndex, int fieldCount) {
        super(null, fieldIndex, fieldCount);
    }


    /**
     * 获取 node Key
     *
     * @return node key
     */
    public abstract K getPrivateKey();


    /**
     * 在父节点标记 自身修改
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    @Override
    protected final void markParentNode() {
        final IMarkContainer node = this.getParentNode();
        if (node == null) {
            return;
        }
        if (node instanceof AbstractMapNode) {
            ((AbstractMapNode) node).mark(this);
            return;
        }
        super.markParentNode();
    }

    /**
     * 在父节点去除对自身修改的标记
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    @Override
    protected void unMarkParentNode() {
        final IMarkContainer node = this.getParentNode();
        if (node == null) {
            return;
        }
        if (node instanceof AbstractMapNode) {
            ((AbstractMapNode) node).unMark(this);
            return;
        }
        super.unMarkParentNode();
    }
}

package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_里程碑.xlsx", node="milestone_task.xml")
public class MilestoneTaskTemplate implements IResTemplate  {

    /**
    * 里程碑任务ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 假数据参数
    * string fakeDataConst
    * 
    */
    @ResAttribute("fakeDataConst")
    private  String fakeDataConst;

    /**
    * 里程碑任务类型id
    * CommonEnum.MileStoneTaskType milestoneTaskTypeId
    * 
    */
    @ResAttribute("milestoneTaskTypeId")
    private CommonEnum.MileStoneTaskType milestoneTaskTypeId;

    /**
    * 任务参数
（用_来分割类型；用，来分割同一个类型下的多个枚举）
    * string taskConst
    * 
    */
    @ResAttribute("taskConst")
    private  String taskConst;



    /**
    * 里程碑任务ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 假数据参数
    * string fakeDataConst
    * 
    */
    public String getFakeDataConst(){
        return fakeDataConst;
    }

    /**
    * 里程碑任务类型id
    * CommonEnum.MileStoneTaskType milestoneTaskTypeId
    * 
    */
    public CommonEnum.MileStoneTaskType getMilestoneTaskTypeId(){
        return milestoneTaskTypeId;
    }
    /**
    * 任务参数
（用_来分割类型；用，来分割同一个类型下的多个枚举）
    * string taskConst
    * 
    */
    public String getTaskConst(){
        return taskConst;
    }

}
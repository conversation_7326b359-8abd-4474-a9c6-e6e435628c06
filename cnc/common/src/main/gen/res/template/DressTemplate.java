package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_基地外观.xlsx", node="dress.xml")
public class DressTemplate implements IResTemplate  {

    /**
    * 外观子ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 外观ID
    * int dressId
    * 
    */
    @ResAttribute("dressId")
    private  int dressId;

    /**
    * 持续时间
    * int continueTime
    * 
    */
    @ResAttribute("continueTime")
    private  int continueTime;



    /**
    * 外观子ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 外观ID
    * int dressId
    * 
    */
    public int getDressId(){
        return dressId;
    }

    /**
    * 持续时间
    * int continueTime
    * 
    */
    public int getContinueTime(){
        return continueTime;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "S_商城表.xlsx", node = "const_discount_shop.xml", isConst = true)
public class ConstDiscountShopTemplate implements IResConstTemplate  {

    /**
     * 商店刷新的整点时间（点）
     */
    private int shop_refresh_interval = 0;
    /**
     * 免费刷新次数：固定拥有的免费次数
     */
    private int free_refresh_times = 0;
    /**
     * 每次非免费刷新的价格
     */
    private List<Integer> refresh_price;
    /**
     * 每类商品刷新的种类数量
     */
    private List<Integer> refresh_num;
    /**
     * 可刷新的次数(收费)
     */
    private int refresh_times = 0;


    public int getShopRefreshInterval(){
        return this.shop_refresh_interval;
    }

    public int getFreeRefreshTimes(){
        return this.free_refresh_times;
    }

    public List<Integer> getRefreshPrice(){
        return this.refresh_price;
    }

    public List<Integer> getRefreshNum(){
        return this.refresh_num;
    }

    public int getRefreshTimes(){
        return this.refresh_times;
    }

    @Override
    public int getId() {
        return 0;
    }
}

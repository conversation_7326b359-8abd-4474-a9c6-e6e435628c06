package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_奖励.xlsx", node="random_reward_pool.xml")
public class RandomRewardPoolTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 保底奖励（固定）
    * pairarray guaranteeReward
    * 
    */
    @ResAttribute("guaranteeReward")
    private List<IntPairType> guaranteeRewardPairList;

    /**
    * 保底奖励（随机）
    * triplearray randomGuaranteeReward
    * 
    */
    @ResAttribute("randomGuaranteeReward")
    private List<IntTripleType> randomGuaranteeRewardTripleList;

    /**
    * 触发保底次数
    * int triggerTime
    * 
    */
    @ResAttribute("triggerTime")
    private  int triggerTime;

    /**
    * 清空保底条件（获得对应道具）
    * intarray guaranteeTrigger
    * 
    */
    @ResAttribute("guaranteeTrigger")
    private List<Integer> guaranteeTriggerList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 保底奖励（固定）
    * pairarray guaranteeReward
    * 
    */
    public List<IntPairType> getGuaranteeRewardPairList(){
        return guaranteeRewardPairList;
    }

    /**
    * 保底奖励（随机）
    * triplearray randomGuaranteeReward
    * 
    */
    public List<IntTripleType> getRandomGuaranteeRewardTripleList(){
        return randomGuaranteeRewardTripleList;
    }       
    /**
    * 触发保底次数
    * int triggerTime
    * 
    */
    public int getTriggerTime(){
        return triggerTime;
    }


    /**
    * 清空保底条件（获得对应道具）
    * intarray guaranteeTrigger
    * 
    */
    public List<Integer> getGuaranteeTriggerList(){
        return guaranteeTriggerList;
    }


}
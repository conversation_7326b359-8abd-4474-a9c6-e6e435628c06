package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="", node="skynet_boss.xml")
public class SkynetBossTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * BOSS怪ID
    * int bossID
    * 
    */
    @ResAttribute("bossID")
    private  int bossID;

    /**
    * 主堡等级
    * int baseLevel
    * 
    */
    @ResAttribute("baseLevel")
    private  int baseLevel;

    /**
    * 下一级ID
    * int nextID
    * 
    */
    @ResAttribute("nextID")
    private  int nextID;

    /**
    * 情报任务奖励
    * int taskReward
    * 
    */
    @ResAttribute("taskReward")
    private  int taskReward;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * BOSS怪ID
    * int bossID
    * 
    */
    public int getBossID(){
        return bossID;
    }

    /**
    * 主堡等级
    * int baseLevel
    * 
    */
    public int getBaseLevel(){
        return baseLevel;
    }

    /**
    * 下一级ID
    * int nextID
    * 
    */
    public int getNextID(){
        return nextID;
    }

    /**
    * 情报任务奖励
    * int taskReward
    * 
    */
    public int getTaskReward(){
        return taskReward;
    }


}
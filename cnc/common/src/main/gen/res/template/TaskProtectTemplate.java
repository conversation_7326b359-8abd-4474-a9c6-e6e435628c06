package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动任务.xlsx", node="task_protect.xml")
public class TaskProtectTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 任务ID
    * int taskId
    * 
    */
    @ResAttribute("taskId")
    private  int taskId;

    /**
    * 任务奖励
    * pairarray reward
    * 
    */
    @ResAttribute("reward")
    private List<IntPairType> rewardPairList;

    /**
    * 后置任务
    * int frontTask
    * 
    */
    @ResAttribute("frontTask")
    private  int frontTask;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 任务ID
    * int taskId
    * 
    */
    public int getTaskId(){
        return taskId;
    }


    /**
    * 任务奖励
    * pairarray reward
    * 
    */
    public List<IntPairType> getRewardPairList(){
        return rewardPairList;
    }
    /**
    * 后置任务
    * int frontTask
    * 
    */
    public int getFrontTask(){
        return frontTask;
    }


}
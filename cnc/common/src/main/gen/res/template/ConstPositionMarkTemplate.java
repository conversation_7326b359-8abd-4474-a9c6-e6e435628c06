package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "Z_坐标收藏.xlsx", node = "const_position_mark.xml", isConst = true)
public class ConstPositionMarkTemplate implements IResConstTemplate  {

    /**
     * 军团收藏上限不能超过20
     */
    private int AlliMarkRange = 0;
    /**
     * 特殊，朋友，敌方加在一起总上限不能超过100
     */
    private int PerAllMarkRange = 0;
    /**
     * 个人收藏文本字数上限不能超过100字段
     */
    private int PerMarkTextRange = 0;
    /**
     * 军团收藏文本字数上限不能超过100字段
     */
    private int AlliMarkTextRange = 0;


    public int getAlliMarkRange(){
        return this.AlliMarkRange;
    }

    public int getPerAllMarkRange(){
        return this.PerAllMarkRange;
    }

    public int getPerMarkTextRange(){
        return this.PerMarkTextRange;
    }

    public int getAlliMarkTextRange(){
        return this.AlliMarkTextRange;
    }

    @Override
    public int getId() {
        return 0;
    }
}

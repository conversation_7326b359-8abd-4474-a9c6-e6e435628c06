package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_连锁礼包.xlsx", node="continues_gift_act.xml")
public class ContinuesGiftActTemplate implements IResTemplate  {

    /**
    * 活动id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 礼包链
    * intarray giftList
    * 
    */
    @ResAttribute("giftList")
    private List<Integer> giftListList;

    /**
    * 折扣券道具id
    * int discountItemId
    * 
    */
    @ResAttribute("discountItemId")
    private  int discountItemId;

    /**
    * 邮件id
    * int mailId
    * 
    */
    @ResAttribute("mailId")
    private  int mailId;

    /**
    * 过期邮件id
    * int expireMailId
    * 
    */
    @ResAttribute("expireMailId")
    private  int expireMailId;



    /**
    * 活动id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 礼包链
    * intarray giftList
    * 
    */
    public List<Integer> getGiftListList(){
        return giftListList;
    }

    /**
    * 折扣券道具id
    * int discountItemId
    * 
    */
    public int getDiscountItemId(){
        return discountItemId;
    }

    /**
    * 邮件id
    * int mailId
    * 
    */
    public int getMailId(){
        return mailId;
    }

    /**
    * 过期邮件id
    * int expireMailId
    * 
    */
    public int getExpireMailId(){
        return expireMailId;
    }


}
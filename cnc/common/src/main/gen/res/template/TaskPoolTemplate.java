package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="R_任务.xlsx", node="task_pool.xml")
public class TaskPoolTemplate implements IResTemplate  {

    /**
    * 任务ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 任务类型
    * CommonEnum.TaskType taskType
    * 
    */
    @ResAttribute("taskType")
    private CommonEnum.TaskType taskType;

    /**
    * 类型参数
    * intarray typeValue
    * 
    */
    @ResAttribute("typeValue")
    private List<Integer> typeValueList;

    /**
    * 计算方式
    * CommonEnum.TaskCalcType taskCalculationMethod
    * 
    */
    @ResAttribute("taskCalculationMethod")
    private CommonEnum.TaskCalcType taskCalculationMethod;



    /**
    * 任务ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 任务类型
    * CommonEnum.TaskType taskType
    * 
    */
    public CommonEnum.TaskType getTaskType(){
        return taskType;
    }

    /**
    * 类型参数
    * intarray typeValue
    * 
    */
    public List<Integer> getTypeValueList(){
        return typeValueList;
    }


    /**
    * 计算方式
    * CommonEnum.TaskCalcType taskCalculationMethod
    * 
    */
    public CommonEnum.TaskCalcType getTaskCalculationMethod(){
        return taskCalculationMethod;
    }

}
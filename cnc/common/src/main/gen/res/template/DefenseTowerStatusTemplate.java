package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="S_士兵表_兵种.xlsx", node="defense_tower_status.xml")
public class DefenseTowerStatusTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 状态描述
    * string statusDes
    * 
    */
    @ResAttribute("statusDes")
    private  String statusDes;

    /**
    * 耐久度区间
    * pair durabilityRange
    * 
    */
    @ResAttribute("durabilityRange")
    private IntPairType durabilityRangePair;

    /**
    * 攻击力比例
    * float atkRate
    * 
    */

    @ResAttribute("atkRate")
    private  float atkRate;
    /**
    * 保护部队比例
    * float protectRate
    * 
    */

    @ResAttribute("protectRate")
    private  float protectRate;


    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 状态描述
    * string statusDes
    * 
    */
    public String getStatusDes(){
        return statusDes;
    }

    /**
    * 耐久度区间
    * pair durabilityRange
    * 
    */
    public IntPairType getDurabilityRangePair(){
        return durabilityRangePair;
    }
    /**
    * 攻击力比例
    * float atkRate
    * 
    */
    public float getAtkRate(){
        return atkRate;
    }

    /**
    * 保护部队比例
    * float protectRate
    * 
    */
    public float getProtectRate(){
        return protectRate;
    }


}
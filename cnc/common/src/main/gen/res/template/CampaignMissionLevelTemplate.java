package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_战役配置表【RH】.xlsx", node="campaign_mission_level.xml")
public class CampaignMissionLevelTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 战役难度ID
    * int campaignDifficultyId
    * 
    */
    @ResAttribute("campaignDifficultyId")
    private  int campaignDifficultyId;

    /**
    * 任务难度ID
    * int taskDifficultyId
    * 
    */
    @ResAttribute("taskDifficultyId")
    private  int taskDifficultyId;

    /**
    * 兵种生命加成百分比
    * int unitHpBonus
    * 
    */
    @ResAttribute("unitHpBonus")
    private  int unitHpBonus;

    /**
    * 兵种攻击加成百分比
    * int unitAttackBonus
    * 
    */
    @ResAttribute("unitAttackBonus")
    private  int unitAttackBonus;

    /**
    * 敌人可用兵种
    * intarray enemyUnit
    * 
    */
    @ResAttribute("enemyUnit")
    private List<Integer> enemyUnitList;

    /**
    * 敌人可用技能
    * intarray enemySkill
    * 
    */
    @ResAttribute("enemySkill")
    private List<Integer> enemySkillList;

    /**
    * 建筑生命加成百分比
    * int architectureHpBonus
    * 
    */
    @ResAttribute("architectureHpBonus")
    private  int architectureHpBonus;

    /**
    * 建筑攻击加成百分比
    * int architectureAttackBonus
    * 
    */
    @ResAttribute("architectureAttackBonus")
    private  int architectureAttackBonus;

    /**
    * 敌人可用建筑
    * pairarray enemyArchitecture
    * 
    */
    @ResAttribute("enemyArchitecture")
    private List<IntPairType> enemyArchitecturePairList;

    /**
    * 己方单位升星几率%
    * int rankUpChance
    * 
    */
    @ResAttribute("rankUpChance")
    private  int rankUpChance;

    /**
    * 己方单位疲劳几率
    * int fatigueChance
    * 
    */
    @ResAttribute("fatigueChance")
    private  int fatigueChance;

    /**
    * 获得疲劳最大数量
    * int maxFatigueGains
    * 
    */
    @ResAttribute("maxFatigueGains")
    private  int maxFatigueGains;

    /**
    * 损失数量范围
    * pair retreatLoss
    * 
    */
    @ResAttribute("retreatLoss")
    private IntPairType retreatLossPair;

    /**
    * 损失保底
    * int retreatLossGuarantee
    * 
    */
    @ResAttribute("retreatLossGuarantee")
    private  int retreatLossGuarantee;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 战役难度ID
    * int campaignDifficultyId
    * 
    */
    public int getCampaignDifficultyId(){
        return campaignDifficultyId;
    }

    /**
    * 任务难度ID
    * int taskDifficultyId
    * 
    */
    public int getTaskDifficultyId(){
        return taskDifficultyId;
    }

    /**
    * 兵种生命加成百分比
    * int unitHpBonus
    * 
    */
    public int getUnitHpBonus(){
        return unitHpBonus;
    }

    /**
    * 兵种攻击加成百分比
    * int unitAttackBonus
    * 
    */
    public int getUnitAttackBonus(){
        return unitAttackBonus;
    }


    /**
    * 敌人可用兵种
    * intarray enemyUnit
    * 
    */
    public List<Integer> getEnemyUnitList(){
        return enemyUnitList;
    }


    /**
    * 敌人可用技能
    * intarray enemySkill
    * 
    */
    public List<Integer> getEnemySkillList(){
        return enemySkillList;
    }

    /**
    * 建筑生命加成百分比
    * int architectureHpBonus
    * 
    */
    public int getArchitectureHpBonus(){
        return architectureHpBonus;
    }

    /**
    * 建筑攻击加成百分比
    * int architectureAttackBonus
    * 
    */
    public int getArchitectureAttackBonus(){
        return architectureAttackBonus;
    }


    /**
    * 敌人可用建筑
    * pairarray enemyArchitecture
    * 
    */
    public List<IntPairType> getEnemyArchitecturePairList(){
        return enemyArchitecturePairList;
    }
    /**
    * 己方单位升星几率%
    * int rankUpChance
    * 
    */
    public int getRankUpChance(){
        return rankUpChance;
    }

    /**
    * 己方单位疲劳几率
    * int fatigueChance
    * 
    */
    public int getFatigueChance(){
        return fatigueChance;
    }

    /**
    * 获得疲劳最大数量
    * int maxFatigueGains
    * 
    */
    public int getMaxFatigueGains(){
        return maxFatigueGains;
    }


    /**
    * 损失数量范围
    * pair retreatLoss
    * 
    */
    public IntPairType getRetreatLossPair(){
        return retreatLossPair;
    }
    /**
    * 损失保底
    * int retreatLossGuarantee
    * 
    */
    public int getRetreatLossGuarantee(){
        return retreatLossGuarantee;
    }


}
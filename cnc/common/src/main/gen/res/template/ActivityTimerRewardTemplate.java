package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动表.xlsx", node="activity_timer_reward.xml")
public class ActivityTimerRewardTemplate implements IResTemplate  {

    /**
    * 活动ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 奖励
    * pairarray reward
    * 
    */
    @ResAttribute("reward")
    private List<IntPairType> rewardPairList;

    /**
    * 多长时间后可领取（秒）
    * int rewardArriveSec
    * 
    */
    @ResAttribute("rewardArriveSec")
    private  int rewardArriveSec;

    /**
    * 过期补发邮件id
    * int expireMailId
    * 
    */
    @ResAttribute("expireMailId")
    private  int expireMailId;



    /**
    * 活动ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 奖励
    * pairarray reward
    * 
    */
    public List<IntPairType> getRewardPairList(){
        return rewardPairList;
    }
    /**
    * 多长时间后可领取（秒）
    * int rewardArriveSec
    * 
    */
    public int getRewardArriveSec(){
        return rewardArriveSec;
    }

    /**
    * 过期补发邮件id
    * int expireMailId
    * 
    */
    public int getExpireMailId(){
        return expireMailId;
    }


}
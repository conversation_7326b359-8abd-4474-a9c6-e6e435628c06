package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_地缘区域限制.xlsx", node="limit_move_city.xml")
public class LimitMoveCityTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 区域类型
    * CommonEnum.MapAreaType AreaType
    * 
    */
    @ResAttribute("AreaType")
    private CommonEnum.MapAreaType AreaType;

    /**
    * ErrorCode_Module
    * string module
    * 
    */
    @ResAttribute("module")
    private  String module;

    /**
    * Errorcode_Name
    * string name
    * 
    */
    @ResAttribute("name")
    private  String name;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 区域类型
    * CommonEnum.MapAreaType AreaType
    * 
    */
    public CommonEnum.MapAreaType getAreaType(){
        return AreaType;
    }
    /**
    * ErrorCode_Module
    * string module
    * 
    */
    public String getModule(){
        return module;
    }
    /**
    * Errorcode_Name
    * string name
    * 
    */
    public String getName(){
        return name;
    }

}
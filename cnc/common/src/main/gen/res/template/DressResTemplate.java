package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_基地外观.xlsx", node="dress_res.xml")
public class DressResTemplate implements IResTemplate  {

    /**
    * 外观id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 外观类型
    * int dressType
    * 
    */
    @ResAttribute("dressType")
    private  int dressType;

    /**
    * 使用属性
    * pairarray useAttribute
    * 
    */
    @ResAttribute("useAttribute")
    private List<IntPairType> useAttributePairList;



    /**
    * 外观id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 外观类型
    * int dressType
    * 
    */
    public int getDressType(){
        return dressType;
    }


    /**
    * 使用属性
    * pairarray useAttribute
    * 
    */
    public List<IntPairType> getUseAttributePairList(){
        return useAttributePairList;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_城建表.xlsx", node="inner_camera.xml")
public class InnerCameraTemplate implements IResTemplate  {

    /**
    * 评级
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;



    /**
    * 评级
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


}
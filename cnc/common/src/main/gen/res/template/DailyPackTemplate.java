package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="M_每日特惠.xlsx", node="daily_pack.xml")
public class DailyPackTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 礼包ID
    * intarray goodsIds
    * 
    */
    @ResAttribute("goodsIds")
    private List<Integer> goodsIdsList;

    /**
    * 超值礼包ID
    * intarray supergoodsIds
    * 
    */
    @ResAttribute("supergoodsIds")
    private List<Integer> supergoodsIdsList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 礼包ID
    * intarray goodsIds
    * 
    */
    public List<Integer> getGoodsIdsList(){
        return goodsIdsList;
    }


    /**
    * 超值礼包ID
    * intarray supergoodsIds
    * 
    */
    public List<Integer> getSupergoodsIdsList(){
        return supergoodsIdsList;
    }


}
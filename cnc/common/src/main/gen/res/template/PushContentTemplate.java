package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="X_消息推送.xlsx", node="push_content.xml")
public class PushContentTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 类型ID
    * int typeID
    * 
    */
    @ResAttribute("typeID")
    private  int typeID;

    /**
    * 推送文案
    * string text
    * 
    */
    @ResAttribute("text")
    private  String text;

    /**
    * 推送标题
    * string pushName
    * 
    */
    @ResAttribute("pushName")
    private  String pushName;

    /**
    * 服务器推送内容
    * int serverText
    * 
    */
    @ResAttribute("serverText")
    private  int serverText;

    /**
    * 服务器推送标题
    * int serverPushName
    * 
    */
    @ResAttribute("serverPushName")
    private  int serverPushName;

    /**
    * 有效时间
    * int validTime
    * 
    */
    @ResAttribute("validTime")
    private  int validTime;

    /**
    * 推送间隔id
    * int coldID
    * 
    */
    @ResAttribute("coldID")
    private  int coldID;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 类型ID
    * int typeID
    * 
    */
    public int getTypeID(){
        return typeID;
    }

    /**
    * 推送文案
    * string text
    * 
    */
    public String getText(){
        return text;
    }
    /**
    * 推送标题
    * string pushName
    * 
    */
    public String getPushName(){
        return pushName;
    }
    /**
    * 服务器推送内容
    * int serverText
    * 
    */
    public int getServerText(){
        return serverText;
    }

    /**
    * 服务器推送标题
    * int serverPushName
    * 
    */
    public int getServerPushName(){
        return serverPushName;
    }

    /**
    * 有效时间
    * int validTime
    * 
    */
    public int getValidTime(){
        return validTime;
    }

    /**
    * 推送间隔id
    * int coldID
    * 
    */
    public int getColdID(){
        return coldID;
    }


}
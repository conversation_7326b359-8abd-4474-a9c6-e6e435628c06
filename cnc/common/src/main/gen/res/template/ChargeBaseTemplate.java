package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_支付.xlsx", node="charge_base.xml")
public class ChargeBaseTemplate implements IResTemplate  {

    /**
    * 直充配置id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 支付的产品id
    * int chargeSdkId
    * 
    */
    @ResAttribute("chargeSdkId")
    private  int chargeSdkId;

    /**
    * 计入累充的货币奖励
    * int directDiamond
    * 
    */
    @ResAttribute("directDiamond")
    private  int directDiamond;

    /**
    * 触发邮件id
    * int triggerMailId
    * 
    */
    @ResAttribute("triggerMailId")
    private  int triggerMailId;



    /**
    * 直充配置id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 支付的产品id
    * int chargeSdkId
    * 
    */
    public int getChargeSdkId(){
        return chargeSdkId;
    }

    /**
    * 计入累充的货币奖励
    * int directDiamond
    * 
    */
    public int getDirectDiamond(){
        return directDiamond;
    }

    /**
    * 触发邮件id
    * int triggerMailId
    * 
    */
    public int getTriggerMailId(){
        return triggerMailId;
    }


}
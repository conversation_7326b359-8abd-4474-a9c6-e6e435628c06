package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_战役配置表【RH】.xlsx", node="campaign_difficulty_level.xml")
public class CampaignDifficultyLevelTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 难度需要的建设值
    * int constructionValuePerLevel
    * 
    */
    @ResAttribute("constructionValuePerLevel")
    private  int constructionValuePerLevel;

    /**
    * 生成几个地图
    * int generatedMapCount
    * 
    */
    @ResAttribute("generatedMapCount")
    private  int generatedMapCount;

    /**
    * 地图奖励方案ID
    * intarray mapID
    * 
    */
    @ResAttribute("mapID")
    private List<Integer> mapIDList;

    /**
    * 地图内任务数量
    * int generatedMissionCount
    * 
    */
    @ResAttribute("generatedMissionCount")
    private  int generatedMissionCount;

    /**
    * 任务地图类型权重
    * pairarray mapTypeWeighting
    * 
    */
    @ResAttribute("mapTypeWeighting")
    private List<IntPairType> mapTypeWeightingPairList;

    /**
    * 地图类型保底
    * intarray mapTypeGuarantee
    * 
    */
    @ResAttribute("mapTypeGuarantee")
    private List<Integer> mapTypeGuaranteeList;

    /**
    * 地图难度等级
    * intarray mapLevel
    * 
    */
    @ResAttribute("mapLevel")
    private List<Integer> mapLevelList;

    /**
    * 事件生成几率（%）
    * int eventGenerationChance
    * 
    */
    @ResAttribute("eventGenerationChance")
    private  int eventGenerationChance;

    /**
    * 事件生成保底数量
    * int minEventGeneration
    * 
    */
    @ResAttribute("minEventGeneration")
    private  int minEventGeneration;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 难度需要的建设值
    * int constructionValuePerLevel
    * 
    */
    public int getConstructionValuePerLevel(){
        return constructionValuePerLevel;
    }

    /**
    * 生成几个地图
    * int generatedMapCount
    * 
    */
    public int getGeneratedMapCount(){
        return generatedMapCount;
    }


    /**
    * 地图奖励方案ID
    * intarray mapID
    * 
    */
    public List<Integer> getMapIDList(){
        return mapIDList;
    }

    /**
    * 地图内任务数量
    * int generatedMissionCount
    * 
    */
    public int getGeneratedMissionCount(){
        return generatedMissionCount;
    }


    /**
    * 任务地图类型权重
    * pairarray mapTypeWeighting
    * 
    */
    public List<IntPairType> getMapTypeWeightingPairList(){
        return mapTypeWeightingPairList;
    }

    /**
    * 地图类型保底
    * intarray mapTypeGuarantee
    * 
    */
    public List<Integer> getMapTypeGuaranteeList(){
        return mapTypeGuaranteeList;
    }


    /**
    * 地图难度等级
    * intarray mapLevel
    * 
    */
    public List<Integer> getMapLevelList(){
        return mapLevelList;
    }

    /**
    * 事件生成几率（%）
    * int eventGenerationChance
    * 
    */
    public int getEventGenerationChance(){
        return eventGenerationChance;
    }

    /**
    * 事件生成保底数量
    * int minEventGeneration
    * 
    */
    public int getMinEventGeneration(){
        return minEventGeneration;
    }


}
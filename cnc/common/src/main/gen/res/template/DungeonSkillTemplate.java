package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="F_副本技能表.xlsx", node="dungeon_skill.xml")
public class DungeonSkillTemplate implements IResTemplate  {

    /**
    * 技能id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 技能类型
    * CommonEnum.DungeonSkillType type
    * 
    */
    @ResAttribute("type")
    private CommonEnum.DungeonSkillType type;

    /**
    * 技能cd（秒）
    * int cd
    * 
    */
    @ResAttribute("cd")
    private  int cd;

    /**
    * eva技能id
    * int evaSkillId
    * 
    */
    @ResAttribute("evaSkillId")
    private  int evaSkillId;

    /**
    * 区域技能id
    * int AreaSkillId
    * 
    */
    @ResAttribute("AreaSkillId")
    private  int AreaSkillId;

    /**
    * 增益Buffid
    * intarray BuffId
    * 
    */
    @ResAttribute("BuffId")
    private List<Integer> BuffIdList;

    /**
    * 战斗Buffid
    * intarray BattleBuffId
    * 
    */
    @ResAttribute("BattleBuffId")
    private List<Integer> BattleBuffIdList;



    /**
    * 技能id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 技能类型
    * CommonEnum.DungeonSkillType type
    * 
    */
    public CommonEnum.DungeonSkillType getType(){
        return type;
    }
    /**
    * 技能cd（秒）
    * int cd
    * 
    */
    public int getCd(){
        return cd;
    }

    /**
    * eva技能id
    * int evaSkillId
    * 
    */
    public int getEvaSkillId(){
        return evaSkillId;
    }

    /**
    * 区域技能id
    * int AreaSkillId
    * 
    */
    public int getAreaSkillId(){
        return AreaSkillId;
    }


    /**
    * 增益Buffid
    * intarray BuffId
    * 
    */
    public List<Integer> getBuffIdList(){
        return BuffIdList;
    }


    /**
    * 战斗Buffid
    * intarray BattleBuffId
    * 
    */
    public List<Integer> getBattleBuffIdList(){
        return BattleBuffIdList;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_里程碑.xlsx", node="milestone_taskTemplate.xml")
public class MilestoneTasktemplateTemplate implements IResTemplate  {

    /**
    * 序号
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * taskId
    * int taskId
    * 
    */
    @ResAttribute("taskId")
    private  int taskId;

    /**
    * milestoneTemplateId
    * int milestoneTemplateId
    * 
    */
    @ResAttribute("milestoneTemplateId")
    private  int milestoneTemplateId;

    /**
    * 任务参数
（用_来分割类型；用，来分割同一个类型下的多个枚举）
    * string taskConst
    * 
    */
    @ResAttribute("taskConst")
    private  String taskConst;



    /**
    * 序号
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * taskId
    * int taskId
    * 
    */
    public int getTaskId(){
        return taskId;
    }

    /**
    * milestoneTemplateId
    * int milestoneTemplateId
    * 
    */
    public int getMilestoneTemplateId(){
        return milestoneTemplateId;
    }

    /**
    * 任务参数
（用_来分割类型；用，来分割同一个类型下的多个枚举）
    * string taskConst
    * 
    */
    public String getTaskConst(){
        return taskConst;
    }

}
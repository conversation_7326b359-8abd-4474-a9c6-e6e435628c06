package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="W_问卷奖励.xlsx", node="survey_reward.xml")
public class SurveyRewardTemplate implements IResTemplate  {

    /**
    * 冗余id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 问卷id
    * string surveyId
    * 
    */
    @ResAttribute("surveyId")
    private  String surveyId;

    /**
    * 奖励邮件id
    * int rewardMaillId
    * 
    */
    @ResAttribute("rewardMaillId")
    private  int rewardMaillId;



    /**
    * 冗余id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 问卷id
    * string surveyId
    * 
    */
    public String getSurveyId(){
        return surveyId;
    }
    /**
    * 奖励邮件id
    * int rewardMaillId
    * 
    */
    public int getRewardMaillId(){
        return rewardMaillId;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_城建_RH.xlsx", node="base_expand_RH.xml")
public class BaseExpandRhTemplate implements IResTemplate  {

    /**
    * 区域ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 区域坐标（左下角x_y）
    * pair zoneCoordinate
    * 
    */
    @ResAttribute("zoneCoordinate")
    private IntPairType zoneCoordinatePair;

    /**
    * 区域宽高
    * pair zoneSize
    * 
    */
    @ResAttribute("zoneSize")
    private IntPairType zoneSizePair;

    /**
    * 解锁条件
    * int unlockMission
    * 
    */
    @ResAttribute("unlockMission")
    private  int unlockMission;

    /**
    * 解锁顺序
    * int unlockOrder
    * 
    */
    @ResAttribute("unlockOrder")
    private  int unlockOrder;



    /**
    * 区域ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 区域坐标（左下角x_y）
    * pair zoneCoordinate
    * 
    */
    public IntPairType getZoneCoordinatePair(){
        return zoneCoordinatePair;
    }

    /**
    * 区域宽高
    * pair zoneSize
    * 
    */
    public IntPairType getZoneSizePair(){
        return zoneSizePair;
    }
    /**
    * 解锁条件
    * int unlockMission
    * 
    */
    public int getUnlockMission(){
        return unlockMission;
    }

    /**
    * 解锁顺序
    * int unlockOrder
    * 
    */
    public int getUnlockOrder(){
        return unlockOrder;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_联盟配置表.xlsx", node="clan_office.xml")
public class ClanOfficeTemplate implements IResTemplate  {

    /**
    * 职位ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 联盟等级
    * int staffLevel
    * 
    */
    @ResAttribute("staffLevel")
    private  int staffLevel;

    /**
    * 该职位数量上限
    * int staffLimit
    * 
    */
    @ResAttribute("staffLimit")
    private  int staffLimit;

    /**
    * Buff列表
    * intarray staffBuffList
    * 
    */
    @ResAttribute("staffBuffList")
    private List<Integer> staffBuffListList;



    /**
    * 职位ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 联盟等级
    * int staffLevel
    * 
    */
    public int getStaffLevel(){
        return staffLevel;
    }

    /**
    * 该职位数量上限
    * int staffLimit
    * 
    */
    public int getStaffLimit(){
        return staffLimit;
    }


    /**
    * Buff列表
    * intarray staffBuffList
    * 
    */
    public List<Integer> getStaffBuffListList(){
        return staffBuffListList;
    }


}
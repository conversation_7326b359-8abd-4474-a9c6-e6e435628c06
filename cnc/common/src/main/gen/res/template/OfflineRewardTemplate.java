package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="F_发展度.xlsx", node="offline_reward.xml")
public class OfflineRewardTemplate implements IResTemplate  {

    /**
    * 主堡等级
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 奖励
    * pairarray reward
    * 
    */
    @ResAttribute("reward")
    private List<IntPairType> rewardPairList;

    /**
    * 触发瘫痪所需离线时间
    * int offLineTime
    * 
    */
    @ResAttribute("offLineTime")
    private  int offLineTime;



    /**
    * 主堡等级
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 奖励
    * pairarray reward
    * 
    */
    public List<IntPairType> getRewardPairList(){
        return rewardPairList;
    }
    /**
    * 触发瘫痪所需离线时间
    * int offLineTime
    * 
    */
    public int getOffLineTime(){
        return offLineTime;
    }


}
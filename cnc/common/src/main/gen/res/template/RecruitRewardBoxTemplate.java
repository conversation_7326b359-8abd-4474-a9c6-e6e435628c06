package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_招募.xlsx", node="recruit_reward_box.xml")
public class RecruitRewardBoxTemplate implements IResTemplate  {

    /**
    * 奖励项
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 奖励所属类
    * int boxId
    * 
    */
    @ResAttribute("boxId")
    private  int boxId;

    /**
    * 道具id
    * int itemId
    * 
    */
    @ResAttribute("itemId")
    private  int itemId;

    /**
    * 数量
    * int num
    * 
    */
    @ResAttribute("num")
    private  int num;

    /**
    * 权重
    * int weight
    * 
    */
    @ResAttribute("weight")
    private  int weight;



    /**
    * 奖励项
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 奖励所属类
    * int boxId
    * 
    */
    public int getBoxId(){
        return boxId;
    }

    /**
    * 道具id
    * int itemId
    * 
    */
    public int getItemId(){
        return itemId;
    }

    /**
    * 数量
    * int num
    * 
    */
    public int getNum(){
        return num;
    }

    /**
    * 权重
    * int weight
    * 
    */
    public int getWeight(){
        return weight;
    }


}
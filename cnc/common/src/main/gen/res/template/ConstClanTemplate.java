package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "L_联盟配置表.xlsx", node = "const_clan.xml", isConst = true)
public class ConstClanTemplate implements IResConstTemplate  {

    /**
     * 联盟简称字符下限
     */
    private int ClanSimpleNameMinLen = 0;
    /**
     * 联盟简称字符上限
     */
    private int ClanSimpleNameMaxLen = 0;
    /**
     * 联盟名称字符下限
     */
    private int ClanNameMinLen = 0;
    /**
     * 联盟名称字符上限
     */
    private int ClanNameMaxLen = 0;
    /**
     * 联盟公告字符上限
     */
    private int ClanDescribeMaxLen = 0;
    /**
     * 联盟创建消耗货币类型
     */
    private int ClanCreateMoneyType = 0;
    /**
     * 联盟创建消耗货币数量
     */
    private int ClanCreateMoney = 0;
    /**
     * 联盟列表默认显示联盟数量
     */
    private int ClanListNum = 0;
    /**
     * 非首次加入非自定义文本联盟邮件ID
     */
    private int ClanJoinMailIdWithoutRewordOrCustomize = 0;
    /**
     * 非首次加入自定义文本联盟邮件ID
     */
    private int ClanJoinMailId = 0;
    /**
     * 首次加入自定义文本联盟奖励邮件ID
     */
    private int ClanJoinRewardMailId = 0;
    /**
     * 退出联盟冷却时间，单位为秒
     */
    private int ClanQuitTime = 0;
    /**
     * 联盟人数基础上限
     */
    private int ClanMaxNum = 0;
    /**
     * 系统盟无管理状态留存时间
     */
    private int SystemClanStageNoStaffExpireTime = 0;
    /**
     * 系统盟有管理状态留存时间
     */
    private int SystemClanStageStaffExpireTime = 0;
    /**
     * 系统盟欢迎邮件
     */
    private int SystemClanJoinMailId = 0;
    /**
     * 系统盟解散邮件
     */
    private int SystemClanDissmissMailId = 0;
    /**
     * 系统盟官员任命邮件
     */
    private int SystemClanOfficialArrangementMailId = 0;
    /**
     * 系统盟司令奖励邮件
     */
    private int SystemClanOwnerRewardMailId = 0;
    /**
     * 系统盟官员奖励邮件
     */
    private int SystemClanStaffRewardMailId = 0;
    /**
     * 申请加入联盟被拒绝
     */
    private int ClanUnionApplicationDeniedMailId = 0;
    /**
     * 系统盟征召令
     */
    private int SystemClan_SummoningOrderId = 0;
    /**
     * 高级系统盟征召令
     */
    private int SystemClan_AdvancedSummoningOrderId = 0;
    /**
     * 系统盟官员产生(无指挥官)
     */
    private int SystemClanOfficialArrangementMailId_1 = 0;
    /**
     * 战力提升威望系数
     */
    private int PowerPrestigeRatio = 0;
    /**
     * 打野次数威望系数
     */
    private int MonsterKillRatio = 0;
    /**
     * 每秒占领积分系数
     */
    private int OccupyScoreRatio = 0;
    /**
     * 军团日志最多记录7天内的事情
     */
    private int DiaryLimitDays = 0;
    /**
     * 军团礼物24小时不领取会自动过期
     */
    private int GiftExpireHours = 0;
    /**
     * 
     */
    private int WelcomeAllianceWord = 0;
    /**
     * 首次加入非自定义文本联盟奖励邮件ID
     */
    private int ClanJoinMailIdWithRewordWithoutCustomize = 0;
    /**
     * 解散联盟后再次成为盟主的CD，单位：天
     */
    private int BeClanLeaderCD = 0;
    /**
     * 王国建立后处于初期的时间，单位：天
     */
    private int EarlyKingdomTime = 0;
    /**
     * 解散倒计时，单位分钟
     */
    private int DissolutionCountdown = 0;
    /**
     * 特殊职位授予CD，单位分钟
     */
    private int SpecialStaffGrantedCD = 0;
    /**
     * 特殊职位ID列表
     */
    private List<Integer> SpecialStaffIDs;
    /**
     * R4成员上限
     */
    private int R4num = 0;
    /**
     * 联盟日志上限
     */
    private int MaxNumOfAllianceLogs = 0;
    /**
     * 修改联盟简称花费
     */
    private int ChangeClanSimpleName = 0;
    /**
     * 修改联盟名称花费
     */
    private int ChangeClanName = 0;
    /**
     * 修改联盟旗帜花费
     */
    private int ChangeClanFlag = 0;
    /**
     * 加入联盟跑马灯通知
     */
    private int JoinClanMessage = 0;
    /**
     * 成员移除通知邮件
     */
    private int ClanMemberRemoveMail = 0;
    /**
     * 入盟申请审核通知邮件
     */
    private int ClanReviewApplyMail = 0;
    /**
     * 盟主申请通知邮件
     */
    private int ClanOwnerApplyMail = 0;
    /**
     * 联盟解散取消成功通知邮件
     */
    private int ClanCancelDissolveMail = 0;
    /**
     * 联盟成员等级升级通知邮件
     */
    private int ClanMemberLevelUpMail = 0;
    /**
     * 联盟成员等级降级通知邮件
     */
    private int ClanMemberLevelDownMail = 0;
    /**
     * 盟主转让通知邮件
     */
    private int ClanLeaderChangeMail = 0;
    /**
     * 联盟任命报告邮件
     */
    private int ClanOfficialArrangemetMail = 0;
    /**
     * 每天治疗队列最多可发起1000次帮助
     */
    private int CureHelpDailyLimit = 0;
    /**
     * 每次帮助至少减少60s的队列时长
     */
    private int HelpBaseDecTime = 0;
    /**
     * 每次帮助最多可以减少万分之100（1%）的总队列时长
     */
    private int HelpBaseDecTimePercent = 0;
    /**
     * 请求的被帮助次数最少为5
     */
    private int AtLeastHelpTimes = 0;
    /**
     * 每次帮助可获得个人积分100
     */
    private int EachHelpPoint = 0;
    /**
     * 每天因为帮助获得个人积分最多100次
     */
    private int DailyGetPointsLimit = 0;
    /**
     * 以utc时间为基准
     */
    private int AllianceSupportRefreshTime = 0;
    /**
     * 联盟积分初始值
     */
    private int AllianceInitialPoints = 0;
    /**
     * 联盟资源初始上限值
     */
    private List<IntPairType> AllianceInitialTopLimitResource;
    /**
     * 联盟资源日志数量上限
     */
    private int AllianceWarehouseLog = 0;
    /**
     * 成员被移除跑马灯
     */
    private int leaveClanMessage = 0;
    /**
     * 首次加入联盟奖励
     */
    private List<IntPairType> FirstJoinClanReward;
    /**
     * 首次加入军团奖励邮件
     */
    private int FirstJoinAllianceMail = 0;
    /**
     * 军团信息设置失败邮件
     */
    private int AllianceSettingsFailMail = 0;
    /**
     * 军团推荐文本
     */
    private String recommendClanTips = "";     
    /**
     * 在线时长超过X秒未加入军团开启推荐，每次登录重新计时，单位秒
     */
    private int startRecommendClanTime = 0;
    /**
     * 军团推荐间隔，单位秒
     */
    private int recommendClanInterval = 0;
    /**
     * 推荐军团的成员人数
     */
    private int recommendClanMember = 0;
    /**
     * 推荐军团id缓存，可推送军团小于5时，推送完就停止了；直到有新的军团id出现
     */
    private int recommendClanBufferLength = 0;
    /**
     * 推荐军团主堡等级限制
     */
    private int recommendClanLevel = 0;
    /**
     * 取消申请军团长
     */
    private int ClanMemberCancelApplyOwner = 0;
    /**
     * 军团列表固定位置推荐任何人可加入的军团（从1开始计数）
     */
    private List<Integer> ClanFixRecommendLocs;
    /**
     * 固定位置推荐在超过一定军团数量时触发
     */
    private int ClanFixRecommendLeast = 0;
    /**
     * 系统资源补偿，联盟帮助：等待帮助最长时间（秒）
     */
    private int compensateHelpTimeout = 0;
    /**
     * 系统资源补偿，联盟帮助：等待帮助倒计时结束没有玩家帮助，随机选择至多几个人
     */
    private int compensateHelpTimeoutMaxNum = 0;
    /**
     * 军团资源中心消失时间（单位：天）
     */
    private int ClanResourceBuildingDisappearTime = 0;
    /**
     * 军团资源中心采集邮件
     */
    private int ClanCollectMailId = 0;
    /**
     * 军团礼物120小时清除
     */
    private int GiftDeleteHours = 0;
    /**
     * 军团礼物单次拉取数量
     */
    private int GiftShowList = 0;
    /**
     * 普通军团礼物储存上限
     */
    private int GiftStorageLimit = 0;
    /**
     * 稀有军团礼物储存上限
     */
    private int GiftRareStorageLimit = 0;
    /**
     * 军团礼物等级上限
     */
    private int GiftLevelLimit = 0;
    /**
     * 军团礼物好的奖励道具带特效
     */
    private List<Integer> ShowGoodGift;
    /**
     * 礼物点数对应道具ID
     */
    private int GiftPointsID = 0;
    /**
     * 钥匙点数对应道具ID
     */
    private int KeyPointsID = 0;
    /**
     * 邀请他人加入军团等级
     */
    private int InviteJoinClanLevel = 0;
    /**
     * 邀请他人加入军团邮件
     */
    private int InviteJoinClanMail = 0;
    /**
     * 邀请人数上限
     */
    private int InviteLimit = 0;
    /**
     * 邀请过期时间（单位：小时）
     */
    private int InviteExpireHours = 0;
    /**
     * 军团长长时间不上线转让邮件ID
     */
    private int ClanOwnerTransferMail = 0;
    /**
     * 军团长不上线转让时间老区（单位：小时）
     */
    private int ClanOwnerTransferTimeOld = 0;
    /**
     * 军团长不上线转让时间新区（单位：小时）
     */
    private int ClanOwnerTransferTimeNew = 0;
    /**
     * 军团长不上线转让功能新老区区分开区时间（单位（天）
     */
    private int ClanOwnerTransferDivideOpenTime = 0;


    public int getClanSimpleNameMinLen(){
        return this.ClanSimpleNameMinLen;
    }

    public int getClanSimpleNameMaxLen(){
        return this.ClanSimpleNameMaxLen;
    }

    public int getClanNameMinLen(){
        return this.ClanNameMinLen;
    }

    public int getClanNameMaxLen(){
        return this.ClanNameMaxLen;
    }

    public int getClanDescribeMaxLen(){
        return this.ClanDescribeMaxLen;
    }

    public int getClanCreateMoneyType(){
        return this.ClanCreateMoneyType;
    }

    public int getClanCreateMoney(){
        return this.ClanCreateMoney;
    }

    public int getClanListNum(){
        return this.ClanListNum;
    }

    public int getClanJoinMailIdWithoutRewordOrCustomize(){
        return this.ClanJoinMailIdWithoutRewordOrCustomize;
    }

    public int getClanJoinMailId(){
        return this.ClanJoinMailId;
    }

    public int getClanJoinRewardMailId(){
        return this.ClanJoinRewardMailId;
    }

    public int getClanQuitTime(){
        return this.ClanQuitTime;
    }

    public int getClanMaxNum(){
        return this.ClanMaxNum;
    }

    public int getSystemClanStageNoStaffExpireTime(){
        return this.SystemClanStageNoStaffExpireTime;
    }

    public int getSystemClanStageStaffExpireTime(){
        return this.SystemClanStageStaffExpireTime;
    }

    public int getSystemClanJoinMailId(){
        return this.SystemClanJoinMailId;
    }

    public int getSystemClanDissmissMailId(){
        return this.SystemClanDissmissMailId;
    }

    public int getSystemClanOfficialArrangementMailId(){
        return this.SystemClanOfficialArrangementMailId;
    }

    public int getSystemClanOwnerRewardMailId(){
        return this.SystemClanOwnerRewardMailId;
    }

    public int getSystemClanStaffRewardMailId(){
        return this.SystemClanStaffRewardMailId;
    }

    public int getClanUnionApplicationDeniedMailId(){
        return this.ClanUnionApplicationDeniedMailId;
    }

    public int getSystemclanSummoningorderid(){
        return this.SystemClan_SummoningOrderId;
    }

    public int getSystemclanAdvancedsummoningorderid(){
        return this.SystemClan_AdvancedSummoningOrderId;
    }

    public int getSystemclanofficialarrangementmailid1(){
        return this.SystemClanOfficialArrangementMailId_1;
    }

    public int getPowerPrestigeRatio(){
        return this.PowerPrestigeRatio;
    }

    public int getMonsterKillRatio(){
        return this.MonsterKillRatio;
    }

    public int getOccupyScoreRatio(){
        return this.OccupyScoreRatio;
    }

    public int getDiaryLimitDays(){
        return this.DiaryLimitDays;
    }

    public int getGiftExpireHours(){
        return this.GiftExpireHours;
    }

    public int getWelcomeAllianceWord(){
        return this.WelcomeAllianceWord;
    }

    public int getClanJoinMailIdWithRewordWithoutCustomize(){
        return this.ClanJoinMailIdWithRewordWithoutCustomize;
    }

    public int getBeClanLeaderCD(){
        return this.BeClanLeaderCD;
    }

    public int getEarlyKingdomTime(){
        return this.EarlyKingdomTime;
    }

    public int getDissolutionCountdown(){
        return this.DissolutionCountdown;
    }

    public int getSpecialStaffGrantedCD(){
        return this.SpecialStaffGrantedCD;
    }

    public List<Integer> getSpecialStaffIDs(){
        return this.SpecialStaffIDs;
    }

    public int getR4num(){
        return this.R4num;
    }

    public int getMaxNumOfAllianceLogs(){
        return this.MaxNumOfAllianceLogs;
    }

    public int getChangeClanSimpleName(){
        return this.ChangeClanSimpleName;
    }

    public int getChangeClanName(){
        return this.ChangeClanName;
    }

    public int getChangeClanFlag(){
        return this.ChangeClanFlag;
    }

    public int getJoinClanMessage(){
        return this.JoinClanMessage;
    }

    public int getClanMemberRemoveMail(){
        return this.ClanMemberRemoveMail;
    }

    public int getClanReviewApplyMail(){
        return this.ClanReviewApplyMail;
    }

    public int getClanOwnerApplyMail(){
        return this.ClanOwnerApplyMail;
    }

    public int getClanCancelDissolveMail(){
        return this.ClanCancelDissolveMail;
    }

    public int getClanMemberLevelUpMail(){
        return this.ClanMemberLevelUpMail;
    }

    public int getClanMemberLevelDownMail(){
        return this.ClanMemberLevelDownMail;
    }

    public int getClanLeaderChangeMail(){
        return this.ClanLeaderChangeMail;
    }

    public int getClanOfficialArrangemetMail(){
        return this.ClanOfficialArrangemetMail;
    }

    public int getCureHelpDailyLimit(){
        return this.CureHelpDailyLimit;
    }

    public int getHelpBaseDecTime(){
        return this.HelpBaseDecTime;
    }

    public int getHelpBaseDecTimePercent(){
        return this.HelpBaseDecTimePercent;
    }

    public int getAtLeastHelpTimes(){
        return this.AtLeastHelpTimes;
    }

    public int getEachHelpPoint(){
        return this.EachHelpPoint;
    }

    public int getDailyGetPointsLimit(){
        return this.DailyGetPointsLimit;
    }

    public int getAllianceSupportRefreshTime(){
        return this.AllianceSupportRefreshTime;
    }

    public int getAllianceInitialPoints(){
        return this.AllianceInitialPoints;
    }

    public List<IntPairType> getAllianceInitialTopLimitResource(){
        return this.AllianceInitialTopLimitResource;
    }

    public int getAllianceWarehouseLog(){
        return this.AllianceWarehouseLog;
    }

    public int getLeaveClanMessage(){
        return this.leaveClanMessage;
    }

    public List<IntPairType> getFirstJoinClanReward(){
        return this.FirstJoinClanReward;
    }

    public int getFirstJoinAllianceMail(){
        return this.FirstJoinAllianceMail;
    }

    public int getAllianceSettingsFailMail(){
        return this.AllianceSettingsFailMail;
    }

    public String getRecommendClanTips(){
        return this.recommendClanTips;
    }   

    public int getStartRecommendClanTime(){
        return this.startRecommendClanTime;
    }

    public int getRecommendClanInterval(){
        return this.recommendClanInterval;
    }

    public int getRecommendClanMember(){
        return this.recommendClanMember;
    }

    public int getRecommendClanBufferLength(){
        return this.recommendClanBufferLength;
    }

    public int getRecommendClanLevel(){
        return this.recommendClanLevel;
    }

    public int getClanMemberCancelApplyOwner(){
        return this.ClanMemberCancelApplyOwner;
    }

    public List<Integer> getClanFixRecommendLocs(){
        return this.ClanFixRecommendLocs;
    }

    public int getClanFixRecommendLeast(){
        return this.ClanFixRecommendLeast;
    }

    public int getCompensateHelpTimeout(){
        return this.compensateHelpTimeout;
    }

    public int getCompensateHelpTimeoutMaxNum(){
        return this.compensateHelpTimeoutMaxNum;
    }

    public int getClanResourceBuildingDisappearTime(){
        return this.ClanResourceBuildingDisappearTime;
    }

    public int getClanCollectMailId(){
        return this.ClanCollectMailId;
    }

    public int getGiftDeleteHours(){
        return this.GiftDeleteHours;
    }

    public int getGiftShowList(){
        return this.GiftShowList;
    }

    public int getGiftStorageLimit(){
        return this.GiftStorageLimit;
    }

    public int getGiftRareStorageLimit(){
        return this.GiftRareStorageLimit;
    }

    public int getGiftLevelLimit(){
        return this.GiftLevelLimit;
    }

    public List<Integer> getShowGoodGift(){
        return this.ShowGoodGift;
    }

    public int getGiftPointsID(){
        return this.GiftPointsID;
    }

    public int getKeyPointsID(){
        return this.KeyPointsID;
    }

    public int getInviteJoinClanLevel(){
        return this.InviteJoinClanLevel;
    }

    public int getInviteJoinClanMail(){
        return this.InviteJoinClanMail;
    }

    public int getInviteLimit(){
        return this.InviteLimit;
    }

    public int getInviteExpireHours(){
        return this.InviteExpireHours;
    }

    public int getClanOwnerTransferMail(){
        return this.ClanOwnerTransferMail;
    }

    public int getClanOwnerTransferTimeOld(){
        return this.ClanOwnerTransferTimeOld;
    }

    public int getClanOwnerTransferTimeNew(){
        return this.ClanOwnerTransferTimeNew;
    }

    public int getClanOwnerTransferDivideOpenTime(){
        return this.ClanOwnerTransferDivideOpenTime;
    }

    @Override
    public int getId() {
        return 0;
    }
}

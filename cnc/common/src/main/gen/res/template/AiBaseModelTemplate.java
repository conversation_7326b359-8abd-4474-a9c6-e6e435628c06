package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="A_AI指令.xlsx", node="ai_base_model.xml")
public class AiBaseModelTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 模型名
    * CommonEnum.AiBaseModelType type
    * 
    */
    @ResAttribute("type")
    private CommonEnum.AiBaseModelType type;

    /**
    * 包含的状态
    * string states
    * 
    */
    @ResAttribute("states")
    private  String states;

    /**
    * param:1  警戒范围/cm（是否支持配置）
    * bool AP_ALERT_RANGE
    * 
    */
    @ResAttribute("AP_ALERT_RANGE")
    private  boolean AP_ALERT_RANGE;

    /**
    * param2: 巡逻范围/cm
    * bool AP_PATROL_RANGE
    * 
    */
    @ResAttribute("AP_PATROL_RANGE")
    private  boolean AP_PATROL_RANGE;

    /**
    * param3: 移动最小距离/cm
    * bool AP_MOVE_MIN_DISTANCE
    * 
    */
    @ResAttribute("AP_MOVE_MIN_DISTANCE")
    private  boolean AP_MOVE_MIN_DISTANCE;

    /**
    * param4: 移动最大距离/cm
    * bool AP_MOVE_MAX_DISTANCE
    * 
    */
    @ResAttribute("AP_MOVE_MAX_DISTANCE")
    private  boolean AP_MOVE_MAX_DISTANCE;

    /**
    * param5: 追击范围/cm
    * bool AP_CHASE_RANGE
    * 
    */
    @ResAttribute("AP_CHASE_RANGE")
    private  boolean AP_CHASE_RANGE;

    /**
    * param6: 脱战回血(等于0不回血)
    * bool AP_RECOVERY_SOLDIER
    * 
    */
    @ResAttribute("AP_RECOVERY_SOLDIER")
    private  boolean AP_RECOVERY_SOLDIER;

    /**
    * param7:巡逻时间间隔/ms
    * bool AP_PATROL_INTERVAL
    * 
    */
    @ResAttribute("AP_PATROL_INTERVAL")
    private  boolean AP_PATROL_INTERVAL;

    /**
    * param8:技能id
    * bool AP_SKILL
    * 
    */
    @ResAttribute("AP_SKILL")
    private  boolean AP_SKILL;

    /**
    * param9:技能释放间隔
    * bool AP_SKILL_INTERVAL
    * 
    */
    @ResAttribute("AP_SKILL_INTERVAL")
    private  boolean AP_SKILL_INTERVAL;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 模型名
    * CommonEnum.AiBaseModelType type
    * 
    */
    public CommonEnum.AiBaseModelType getType(){
        return type;
    }
    /**
    * 包含的状态
    * string states
    * 
    */
    public String getStates(){
        return states;
    }

    /**
    * param:1  警戒范围/cm（是否支持配置）
    * bool AP_ALERT_RANGE
    * 
    */
    public boolean getApAlertRange(){
        return AP_ALERT_RANGE;
    }

    /**
    * param2: 巡逻范围/cm
    * bool AP_PATROL_RANGE
    * 
    */
    public boolean getApPatrolRange(){
        return AP_PATROL_RANGE;
    }

    /**
    * param3: 移动最小距离/cm
    * bool AP_MOVE_MIN_DISTANCE
    * 
    */
    public boolean getApMoveMinDistance(){
        return AP_MOVE_MIN_DISTANCE;
    }

    /**
    * param4: 移动最大距离/cm
    * bool AP_MOVE_MAX_DISTANCE
    * 
    */
    public boolean getApMoveMaxDistance(){
        return AP_MOVE_MAX_DISTANCE;
    }

    /**
    * param5: 追击范围/cm
    * bool AP_CHASE_RANGE
    * 
    */
    public boolean getApChaseRange(){
        return AP_CHASE_RANGE;
    }

    /**
    * param6: 脱战回血(等于0不回血)
    * bool AP_RECOVERY_SOLDIER
    * 
    */
    public boolean getApRecoverySoldier(){
        return AP_RECOVERY_SOLDIER;
    }

    /**
    * param7:巡逻时间间隔/ms
    * bool AP_PATROL_INTERVAL
    * 
    */
    public boolean getApPatrolInterval(){
        return AP_PATROL_INTERVAL;
    }

    /**
    * param8:技能id
    * bool AP_SKILL
    * 
    */
    public boolean getApSkill(){
        return AP_SKILL;
    }

    /**
    * param9:技能释放间隔
    * bool AP_SKILL_INTERVAL
    * 
    */
    public boolean getApSkillInterval(){
        return AP_SKILL_INTERVAL;
    }

}
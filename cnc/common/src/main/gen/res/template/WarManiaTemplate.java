package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_基地增益.xlsx", node="war_mania.xml")
public class WarManiaTemplate implements IResTemplate  {

    /**
    * 主堡等级
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 状态持续效果
    * int buffId
    * 
    */
    @ResAttribute("buffId")
    private  int buffId;



    /**
    * 主堡等级
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 状态持续效果
    * int buffId
    * 
    */
    public int getBuffId(){
        return buffId;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_奖励.xlsx", node="selecte_reward.xml")
public class SelecteRewardTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 选择类型
    * CommonEnum.SelectorType type
    * 
    */
    @ResAttribute("type")
    private CommonEnum.SelectorType type;

    /**
    * Compound类型随机次数
    * int randTimes
    * 
    */
    @ResAttribute("randTimes")
    private  int randTimes;

    /**
    * 奖励
    * string rewards
    * 
    */
    @ResAttribute("rewards")
    private  String rewards;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 选择类型
    * CommonEnum.SelectorType type
    * 
    */
    public CommonEnum.SelectorType getType(){
        return type;
    }
    /**
    * Compound类型随机次数
    * int randTimes
    * 
    */
    public int getRandTimes(){
        return randTimes;
    }

    /**
    * 奖励
    * string rewards
    * 
    */
    public String getRewards(){
        return rewards;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_野怪.xlsx", node="monster.xml")
public class MonsterTemplate implements IResTemplate  {

    /**
    * 野怪id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * kvk剧本id
    * int storyId
    * 
    */
    @ResAttribute("storyId")
    private  int storyId;

    /**
    * 野怪类型
    * CommonEnum.MonsterCategory category
    * 
    */
    @ResAttribute("category")
    private CommonEnum.MonsterCategory category;

    /**
    * 品质
    * CommonEnum.SceneObjQuality quality
    * 
    */
    @ResAttribute("quality")
    private CommonEnum.SceneObjQuality quality;

    /**
    * 等级
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;

    /**
    * 部队表索引
    * int troopIndex
    * 
    */
    @ResAttribute("troopIndex")
    private  int troopIndex;

    /**
    * 野怪攻击力修正
    * float atkParam
    * 
    */

    @ResAttribute("atkParam")
    private  float atkParam;
    /**
    * 野怪兵力系数修正
    * float soldierCountParam
    * 
    */

    @ResAttribute("soldierCountParam")
    private  float soldierCountParam;
    /**
    * 移速系数
    * float moveSpeedParam
    * 
    */

    @ResAttribute("moveSpeedParam")
    private  float moveSpeedParam;
    /**
    * ai行为模式
    * int aiIndex
    * 
    */
    @ResAttribute("aiIndex")
    private  int aiIndex;

    /**
    * 仇恨列表类型
    * CommonEnum.HateListType hateListType
    * 
    */
    @ResAttribute("hateListType")
    private CommonEnum.HateListType hateListType;

    /**
    * 野怪模型圈半径（厘米）
    * int modelSize
    * 
    */
    @ResAttribute("modelSize")
    private  int modelSize;

    /**
    * 野怪阻挡圈半径(厘米)
    * int blockRadius
    * 
    */
    @ResAttribute("blockRadius")
    private  int blockRadius;

    /**
    * 仇恨过期时长(秒)
    * int hateExpire
    * 
    */
    @ResAttribute("hateExpire")
    private  int hateExpire;

    /**
    * 长时间未攻击脱战时长
    * int OutBattleTime
    * 
    */
    @ResAttribute("OutBattleTime")
    private  int OutBattleTime;

    /**
    * 最远攻击距离
    * int FarthestAttackDistance
    * 
    */
    @ResAttribute("FarthestAttackDistance")
    private  int FarthestAttackDistance;

    /**
    * 触发器
    * intarray trigger
    * 
    */
    @ResAttribute("trigger")
    private List<Integer> triggerList;

    /**
    * 阵营
    * int camp
    * 
    */
    @ResAttribute("camp")
    private  int camp;

    /**
    * 行军动态阻挡半径
单位：厘米
默认为空，无动态阻挡
    * int dynamicObstruct
    * 
    */
    @ResAttribute("dynamicObstruct")
    private  int dynamicObstruct;

    /**
    * 野怪静止朝向（角度）
    * int yaw
    * 
    */
    @ResAttribute("yaw")
    private  int yaw;

    /**
    * 普通奖励
    * intarray rewardId
    * 
    */
    @ResAttribute("rewardId")
    private List<Integer> rewardIdList;

    /**
    * 首杀奖励
    * intarray firstRewardId
    * 
    */
    @ResAttribute("firstRewardId")
    private List<Integer> firstRewardIdList;

    /**
    * 权重奖励
    * pairarray damageReward
    * 
    */
    @ResAttribute("damageReward")
    private List<IntPairType> damageRewardPairList;

    /**
    * 击杀邮件通知
    * int mailId
    * 
    */
    @ResAttribute("mailId")
    private  int mailId;

    /**
    * 击杀获取经验值
    * int exp
    * 
    */
    @ResAttribute("exp")
    private  int exp;

    /**
    * 击杀掉落刷新id
    * pairarray dropGroupId
    * 
    */
    @ResAttribute("dropGroupId")
    private List<IntPairType> dropGroupIdPairList;

    /**
    * 联盟宝箱奖励ID
    * int clanGiftId
    * 
    */
    @ResAttribute("clanGiftId")
    private  int clanGiftId;

    /**
    * 击杀奖励冷却buff
    * int killRewardCdBuffId
    * 
    */
    @ResAttribute("killRewardCdBuffId")
    private  int killRewardCdBuffId;

    /**
    * 体力消耗
    * int energy
    * 
    */
    @ResAttribute("energy")
    private  int energy;

    /**
    * 集结体力消耗（仅针对单人进攻或集结）
    * int RallyEnergy
    * 
    */
    @ResAttribute("RallyEnergy")
    private  int RallyEnergy;

    /**
    * 蓄力
    * int cast
    * 
    */
    @ResAttribute("cast")
    private  int cast;

    /**
    * 被攻击模式
    * CommonEnum.BeAttackModel beAttackModel
    * 
    */
    @ResAttribute("beAttackModel")
    private CommonEnum.BeAttackModel beAttackModel;

    /**
    * 是否参与围攻
    * bool besiege
    * 
    */
    @ResAttribute("besiege")
    private  boolean besiege;

    /**
    * 出生后不可战斗时长(毫秒,
负数为持续整个生命周期)
    * int spawnBattleForbidden
    * 
    */
    @ResAttribute("spawnBattleForbidden")
    private  int spawnBattleForbidden;

    /**
    * 是否禁用反伤
    * bool cantCounterAttack
    * 
    */
    @ResAttribute("cantCounterAttack")
    private  boolean cantCounterAttack;

    /**
    * 是否禁用普攻
    * bool banAttackAction
    * 
    */
    @ResAttribute("banAttackAction")
    private  boolean banAttackAction;

    /**
    * 是否禁用并行追击
    * bool banParallelChase
    * 
    */
    @ResAttribute("banParallelChase")
    private  boolean banParallelChase;

    /**
    * 是否关闭伤害仇恨
    * bool closeDamageHate
    * 
    */
    @ResAttribute("closeDamageHate")
    private  boolean closeDamageHate;

    /**
    * 怪物警戒值
    * int warnValue
    * 
    */
    @ResAttribute("warnValue")
    private  int warnValue;



    /**
    * 野怪id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * kvk剧本id
    * int storyId
    * 
    */
    public int getStoryId(){
        return storyId;
    }


    /**
    * 野怪类型
    * CommonEnum.MonsterCategory category
    * 
    */
    public CommonEnum.MonsterCategory getCategory(){
        return category;
    }

    /**
    * 品质
    * CommonEnum.SceneObjQuality quality
    * 
    */
    public CommonEnum.SceneObjQuality getQuality(){
        return quality;
    }
    /**
    * 等级
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }

    /**
    * 部队表索引
    * int troopIndex
    * 
    */
    public int getTroopIndex(){
        return troopIndex;
    }

    /**
    * 野怪攻击力修正
    * float atkParam
    * 
    */
    public float getAtkParam(){
        return atkParam;
    }

    /**
    * 野怪兵力系数修正
    * float soldierCountParam
    * 
    */
    public float getSoldierCountParam(){
        return soldierCountParam;
    }

    /**
    * 移速系数
    * float moveSpeedParam
    * 
    */
    public float getMoveSpeedParam(){
        return moveSpeedParam;
    }

    /**
    * ai行为模式
    * int aiIndex
    * 
    */
    public int getAiIndex(){
        return aiIndex;
    }


    /**
    * 仇恨列表类型
    * CommonEnum.HateListType hateListType
    * 
    */
    public CommonEnum.HateListType getHateListType(){
        return hateListType;
    }
    /**
    * 野怪模型圈半径（厘米）
    * int modelSize
    * 
    */
    public int getModelSize(){
        return modelSize;
    }

    /**
    * 野怪阻挡圈半径(厘米)
    * int blockRadius
    * 
    */
    public int getBlockRadius(){
        return blockRadius;
    }

    /**
    * 仇恨过期时长(秒)
    * int hateExpire
    * 
    */
    public int getHateExpire(){
        return hateExpire;
    }

    /**
    * 长时间未攻击脱战时长
    * int OutBattleTime
    * 
    */
    public int getOutBattleTime(){
        return OutBattleTime;
    }

    /**
    * 最远攻击距离
    * int FarthestAttackDistance
    * 
    */
    public int getFarthestAttackDistance(){
        return FarthestAttackDistance;
    }


    /**
    * 触发器
    * intarray trigger
    * 
    */
    public List<Integer> getTriggerList(){
        return triggerList;
    }

    /**
    * 阵营
    * int camp
    * 
    */
    public int getCamp(){
        return camp;
    }

    /**
    * 行军动态阻挡半径
单位：厘米
默认为空，无动态阻挡
    * int dynamicObstruct
    * 
    */
    public int getDynamicObstruct(){
        return dynamicObstruct;
    }

    /**
    * 野怪静止朝向（角度）
    * int yaw
    * 
    */
    public int getYaw(){
        return yaw;
    }


    /**
    * 普通奖励
    * intarray rewardId
    * 
    */
    public List<Integer> getRewardIdList(){
        return rewardIdList;
    }


    /**
    * 首杀奖励
    * intarray firstRewardId
    * 
    */
    public List<Integer> getFirstRewardIdList(){
        return firstRewardIdList;
    }


    /**
    * 权重奖励
    * pairarray damageReward
    * 
    */
    public List<IntPairType> getDamageRewardPairList(){
        return damageRewardPairList;
    }
    /**
    * 击杀邮件通知
    * int mailId
    * 
    */
    public int getMailId(){
        return mailId;
    }

    /**
    * 击杀获取经验值
    * int exp
    * 
    */
    public int getExp(){
        return exp;
    }


    /**
    * 击杀掉落刷新id
    * pairarray dropGroupId
    * 
    */
    public List<IntPairType> getDropGroupIdPairList(){
        return dropGroupIdPairList;
    }
    /**
    * 联盟宝箱奖励ID
    * int clanGiftId
    * 
    */
    public int getClanGiftId(){
        return clanGiftId;
    }

    /**
    * 击杀奖励冷却buff
    * int killRewardCdBuffId
    * 
    */
    public int getKillRewardCdBuffId(){
        return killRewardCdBuffId;
    }

    /**
    * 体力消耗
    * int energy
    * 
    */
    public int getEnergy(){
        return energy;
    }

    /**
    * 集结体力消耗（仅针对单人进攻或集结）
    * int RallyEnergy
    * 
    */
    public int getRallyEnergy(){
        return RallyEnergy;
    }

    /**
    * 蓄力
    * int cast
    * 
    */
    public int getCast(){
        return cast;
    }


    /**
    * 被攻击模式
    * CommonEnum.BeAttackModel beAttackModel
    * 
    */
    public CommonEnum.BeAttackModel getBeAttackModel(){
        return beAttackModel;
    }

    /**
    * 是否参与围攻
    * bool besiege
    * 
    */
    public boolean getBesiege(){
        return besiege;
    }
    /**
    * 出生后不可战斗时长(毫秒,
负数为持续整个生命周期)
    * int spawnBattleForbidden
    * 
    */
    public int getSpawnBattleForbidden(){
        return spawnBattleForbidden;
    }


    /**
    * 是否禁用反伤
    * bool cantCounterAttack
    * 
    */
    public boolean getCantCounterAttack(){
        return cantCounterAttack;
    }

    /**
    * 是否禁用普攻
    * bool banAttackAction
    * 
    */
    public boolean getBanAttackAction(){
        return banAttackAction;
    }

    /**
    * 是否禁用并行追击
    * bool banParallelChase
    * 
    */
    public boolean getBanParallelChase(){
        return banParallelChase;
    }

    /**
    * 是否关闭伤害仇恨
    * bool closeDamageHate
    * 
    */
    public boolean getCloseDamageHate(){
        return closeDamageHate;
    }
    /**
    * 怪物警戒值
    * int warnValue
    * 
    */
    public int getWarnValue(){
        return warnValue;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "H_活动表.xlsx", node = "const_activity.xml", isConst = true)
public class ConstActivityTemplate implements IResConstTemplate  {

    /**
     * 直接花费黄金永久解锁第二队列的价格
     */
    private int secondQueueDiamond = 0;
    /**
     * 永久解锁第二队列的付费礼包id
     */
    private int secondQueueGoodsId = 0;
    /**
     * 永久第二队列的道具id
     */
    private int secondQueueItemId = 0;
    /**
     * 战略储备活动野外采集
     */
    private int eventReserveMapResource = 0;
    /**
     * 战略储备活动城内收集
     */
    private int eventReserveCityResource = 0;
    /**
     * 战略储备奖励邮件id
     */
    private int eventReserveMailId = 0;
    /**
     * 最强指挥官界面展示奖励
     */
    private List<Integer> BestCommanderEventRewardShow;
    /**
     * 成长基金礼包购买需要的vip等级
     */
    private int growthFundGoodsReqVipLevel = 0;
    /**
     * 周月卡过期提醒邮件id
     */
    private int monthlyCardExpiredMailId = 0;
    /**
     * 战略储备奖励邮件id
     */
    private int eventReserveMailId_2 = 0;
    /**
     * 机甲打野配件礼包id
     */
    private int mechaStalkerBundleId = 0;
    /**
     * 在线开启活动限时时长
     */
    private String onlineEventDays = "";     
    /**
     * 机甲坦克配件礼包id
     */
    private int mechaTankBundleId = 0;
    /**
     * 最强指挥官界面展示奖励_保罗·科尔特斯
     */
    private List<Integer> BestCommanderEventRewardShow_100022;
    /**
     * 最强指挥官界面展示奖励_保罗·科尔特斯
     */
    private List<Integer> BestCommanderEventRewardShow_100009;
    /**
     * 最强指挥官界面展示奖励_奈杰尔·格兰特
     */
    private List<Integer> BestCommanderEventRewardShow_100023;
    /**
     * 最强指挥官预告角色半身像_保罗·科尔特斯
     */
    private int BestCommanderShowHeroSpId_100022 = 0;
    /**
     * 最强指挥官预告角色半身像_保罗·科尔特斯
     */
    private int BestCommanderShowHeroSpId_100009 = 0;
    /**
     * 最强指挥官预告角色半身像_奈杰尔·格兰特
     */
    private int BestCommanderShowHeroSpId_100023 = 0;
    /**
     * 周卡半价道具
     */
    private int weekMonthCardCouponID = 0;
    /**
     * 最强指挥官界面展示奖励_保罗·科尔特斯
     */
    private List<Integer> BestCommanderEventRewardShow_10003;
    /**
     * 最强指挥官界面展示奖励_保罗·科尔特斯
     */
    private List<Integer> BestCommanderEventRewardShow_10101;
    /**
     * 最强指挥官界面展示奖励_奈杰尔·格兰特
     */
    private List<Integer> BestCommanderEventRewardShow_10102;
    /**
     * 最强指挥官预告角色半身像_保罗·科尔特斯
     */
    private int BestCommanderShowHeroSpId_10003 = 0;
    /**
     * 最强指挥官预告角色半身像_保罗·科尔特斯
     */
    private int BestCommanderShowHeroSpId_10101 = 0;
    /**
     * 最强指挥官预告角色半身像_奈杰尔·格兰特
     */
    private int BestCommanderShowHeroSpId_10102 = 0;
    /**
     * 累充黄金价值数量
     */
    private List<Integer> businessTotalPayGoldWorth;
    /**
     * 商店评分弹窗触发次数上限
     */
    private int storeRatingTriggerTime = 0;
    /**
     * 基础触发时间间隔，单位小时
     */
    private int initialTriggerInterval = 0;
    /**
     * 额外触发时间间隔，单位小时
     */
    private int extraTriggerInterval = 0;
    /**
     * 落堡后禁止评分时间间隔，单位小时
     */
    private int cityFallBanRatingInterval = 0;
    /**
     * 基地遭受攻击后禁止评分时间间隔，单位小时
     */
    private int baseBeAttackBanRatingInterval = 0;
    /**
     * PVP失败后禁止评分时间间隔，单位小时
     */
    private int pvpFailBanRatingInterval = 0;
    /**
     * 最强指挥官界面展示奖励_乌玛贡
     */
    private List<Integer> BestCommanderEventRewardShow_10103;
    /**
     * 最强指挥官预告角色半身像_乌玛贡
     */
    private int BestCommanderShowHeroSpId_10103 = 0;
    /**
     * 最强指挥官预告英雄ID_保罗·科尔特斯
     */
    private int BestCommanderShowHeroId_10101 = 0;
    /**
     * 最强指挥官预告英雄ID_奈杰尔·格兰特
     */
    private int BestCommanderShowHeroId_10102 = 0;
    /**
     * 最强指挥官预告英雄ID_乌玛贡
     */
    private int BestCommanderShowHeroId_10103 = 0;
    /**
     * 最强指挥官预告英雄ID_保罗·科尔特斯
     */
    private int BestCommanderShowHeroId_10003 = 0;
    /**
     * 在线送兵活动主界面红点
     */
    private int ActivityOnlineRedPointNum1 = 0;
    /**
     * 在线送兵活动弹窗红点
     */
    private int ActivityOnlineRedPointNum2 = 0;
    /**
     * 在线送兵活动主界面弹窗跳转
     */
    private int ActivityOnlineGotoId = 0;
    /**
     * 活动日历最多可显示活动数量
     */
    private int activityCalendarNumLimit = 0;
    /**
     * 最强指挥官界面展示道具奖励_非自选活动
     */
    private List<Integer> BestCommanderEventRewardShow_50000;
    /**
     * 最强指挥官界面展示道具奖励_非自选活动
     */
    private List<Integer> BestCommanderEventRewardShow_50001;
    /**
     * 部落之王阶段奖励邮件
     */
    private int ClanKingScoreMail = 0;
    /**
     * 娜塔莎武器库活动获得子弹途径
     */
    private int NatashaArsenalGotoId = 0;
    /**
     * 合成条件道具对应位置
     */
    private List<IntPairType> preActivitySynthesisiItemNumber;
    /**
     * 合成道具
     */
    private int preActivitySynthesisiTreasure = 0;
    /**
     * 合成道具给的积分
     */
    private int preActivitySynthesisiTreasurePoint = 0;
    /**
     * 击杀寨子给的服务器积分
     */
    private int preActivityZoneKillPoints = 0;
    /**
     * 总的抽奖次数限制_幸运大转盘
     */
    private int totalTimesLimit = 0;
    /**
     * 金条限制购买总次数_幸运大转盘
     */
    private int goldBuyNum = 0;
    /**
     * 金条购买幸运券道具单价_幸运大转盘
     */
    private int luckyBuyNum = 0;
    /**
     * 道具消耗id_幸运大转盘
     */
    private int itemNumid = 0;
    /**
     * 筹码价格（筹码个数_金条数量）_幸运大转盘
     */
    private List<IntPairType> buyPrices;
    /**
     * 娜塔莎兑换商店通用代币
     */
    private int natashaToken = 0;
    /**
     * 累计充值活动看板英雄佐菲亚
     */
    private List<Integer> AccumulatedRechargeheroid;


    public int getSecondQueueDiamond(){
        return this.secondQueueDiamond;
    }

    public int getSecondQueueGoodsId(){
        return this.secondQueueGoodsId;
    }

    public int getSecondQueueItemId(){
        return this.secondQueueItemId;
    }

    public int getEventReserveMapResource(){
        return this.eventReserveMapResource;
    }

    public int getEventReserveCityResource(){
        return this.eventReserveCityResource;
    }

    public int getEventReserveMailId(){
        return this.eventReserveMailId;
    }

    public List<Integer> getBestCommanderEventRewardShow(){
        return this.BestCommanderEventRewardShow;
    }

    public int getGrowthFundGoodsReqVipLevel(){
        return this.growthFundGoodsReqVipLevel;
    }

    public int getMonthlyCardExpiredMailId(){
        return this.monthlyCardExpiredMailId;
    }

    public int getEventreservemailid2(){
        return this.eventReserveMailId_2;
    }

    public int getMechaStalkerBundleId(){
        return this.mechaStalkerBundleId;
    }

    public String getOnlineEventDays(){
        return this.onlineEventDays;
    }   

    public int getMechaTankBundleId(){
        return this.mechaTankBundleId;
    }

    public List<Integer> getBestcommandereventrewardshow100022(){
        return this.BestCommanderEventRewardShow_100022;
    }

    public List<Integer> getBestcommandereventrewardshow100009(){
        return this.BestCommanderEventRewardShow_100009;
    }

    public List<Integer> getBestcommandereventrewardshow100023(){
        return this.BestCommanderEventRewardShow_100023;
    }

    public int getBestcommandershowherospid100022(){
        return this.BestCommanderShowHeroSpId_100022;
    }

    public int getBestcommandershowherospid100009(){
        return this.BestCommanderShowHeroSpId_100009;
    }

    public int getBestcommandershowherospid100023(){
        return this.BestCommanderShowHeroSpId_100023;
    }

    public int getWeekMonthCardCouponID(){
        return this.weekMonthCardCouponID;
    }

    public List<Integer> getBestcommandereventrewardshow10003(){
        return this.BestCommanderEventRewardShow_10003;
    }

    public List<Integer> getBestcommandereventrewardshow10101(){
        return this.BestCommanderEventRewardShow_10101;
    }

    public List<Integer> getBestcommandereventrewardshow10102(){
        return this.BestCommanderEventRewardShow_10102;
    }

    public int getBestcommandershowherospid10003(){
        return this.BestCommanderShowHeroSpId_10003;
    }

    public int getBestcommandershowherospid10101(){
        return this.BestCommanderShowHeroSpId_10101;
    }

    public int getBestcommandershowherospid10102(){
        return this.BestCommanderShowHeroSpId_10102;
    }

    public List<Integer> getBusinessTotalPayGoldWorth(){
        return this.businessTotalPayGoldWorth;
    }

    public int getStoreRatingTriggerTime(){
        return this.storeRatingTriggerTime;
    }

    public int getInitialTriggerInterval(){
        return this.initialTriggerInterval;
    }

    public int getExtraTriggerInterval(){
        return this.extraTriggerInterval;
    }

    public int getCityFallBanRatingInterval(){
        return this.cityFallBanRatingInterval;
    }

    public int getBaseBeAttackBanRatingInterval(){
        return this.baseBeAttackBanRatingInterval;
    }

    public int getPvpFailBanRatingInterval(){
        return this.pvpFailBanRatingInterval;
    }

    public List<Integer> getBestcommandereventrewardshow10103(){
        return this.BestCommanderEventRewardShow_10103;
    }

    public int getBestcommandershowherospid10103(){
        return this.BestCommanderShowHeroSpId_10103;
    }

    public int getBestcommandershowheroid10101(){
        return this.BestCommanderShowHeroId_10101;
    }

    public int getBestcommandershowheroid10102(){
        return this.BestCommanderShowHeroId_10102;
    }

    public int getBestcommandershowheroid10103(){
        return this.BestCommanderShowHeroId_10103;
    }

    public int getBestcommandershowheroid10003(){
        return this.BestCommanderShowHeroId_10003;
    }

    public int getActivityOnlineRedPointNum1(){
        return this.ActivityOnlineRedPointNum1;
    }

    public int getActivityOnlineRedPointNum2(){
        return this.ActivityOnlineRedPointNum2;
    }

    public int getActivityOnlineGotoId(){
        return this.ActivityOnlineGotoId;
    }

    public int getActivityCalendarNumLimit(){
        return this.activityCalendarNumLimit;
    }

    public List<Integer> getBestcommandereventrewardshow50000(){
        return this.BestCommanderEventRewardShow_50000;
    }

    public List<Integer> getBestcommandereventrewardshow50001(){
        return this.BestCommanderEventRewardShow_50001;
    }

    public int getClanKingScoreMail(){
        return this.ClanKingScoreMail;
    }

    public int getNatashaArsenalGotoId(){
        return this.NatashaArsenalGotoId;
    }

    public List<IntPairType> getPreActivitySynthesisiItemNumber(){
        return this.preActivitySynthesisiItemNumber;
    }

    public int getPreActivitySynthesisiTreasure(){
        return this.preActivitySynthesisiTreasure;
    }

    public int getPreActivitySynthesisiTreasurePoint(){
        return this.preActivitySynthesisiTreasurePoint;
    }

    public int getPreActivityZoneKillPoints(){
        return this.preActivityZoneKillPoints;
    }

    public int getTotalTimesLimit(){
        return this.totalTimesLimit;
    }

    public int getGoldBuyNum(){
        return this.goldBuyNum;
    }

    public int getLuckyBuyNum(){
        return this.luckyBuyNum;
    }

    public int getItemNumid(){
        return this.itemNumid;
    }

    public List<IntPairType> getBuyPrices(){
        return this.buyPrices;
    }

    public int getNatashaToken(){
        return this.natashaToken;
    }

    public List<Integer> getAccumulatedRechargeheroid(){
        return this.AccumulatedRechargeheroid;
    }

    @Override
    public int getId() {
        return 0;
    }
}

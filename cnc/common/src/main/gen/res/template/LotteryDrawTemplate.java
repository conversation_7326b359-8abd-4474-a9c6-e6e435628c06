package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_抽奖活动表.xlsx", node="lottery_draw.xml")
public class LotteryDrawTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 抽取几次
    * int costTimes
    * 
    */
    @ResAttribute("costTimes")
    private  int costTimes;

    /**
    * 消耗黄金
    * int costDiamond
    * 
    */
    @ResAttribute("costDiamond")
    private  int costDiamond;

    /**
    * 奖励id
    * int rewardId
    * 
    */
    @ResAttribute("rewardId")
    private  int rewardId;

    /**
    * 道具消耗id
    * pairarray costItems
    * 
    */
    @ResAttribute("costItems")
    private List<IntPairType> costItemsPairList;

    /**
    * 触发保底次数
    * int minimumGuarantee
    * 
    */
    @ResAttribute("minimumGuarantee")
    private  int minimumGuarantee;

    /**
    * 小保底重置触发道具ID
    * pairarray smallGuarantee
    * 
    */
    @ResAttribute("smallGuarantee")
    private List<IntPairType> smallGuaranteePairList;

    /**
    * 大保底重置触发道具ID
    * pairarray bigGuarantee
    * 
    */
    @ResAttribute("bigGuarantee")
    private List<IntPairType> bigGuaranteePairList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 抽取几次
    * int costTimes
    * 
    */
    public int getCostTimes(){
        return costTimes;
    }

    /**
    * 消耗黄金
    * int costDiamond
    * 
    */
    public int getCostDiamond(){
        return costDiamond;
    }

    /**
    * 奖励id
    * int rewardId
    * 
    */
    public int getRewardId(){
        return rewardId;
    }


    /**
    * 道具消耗id
    * pairarray costItems
    * 
    */
    public List<IntPairType> getCostItemsPairList(){
        return costItemsPairList;
    }
    /**
    * 触发保底次数
    * int minimumGuarantee
    * 
    */
    public int getMinimumGuarantee(){
        return minimumGuarantee;
    }


    /**
    * 小保底重置触发道具ID
    * pairarray smallGuarantee
    * 
    */
    public List<IntPairType> getSmallGuaranteePairList(){
        return smallGuaranteePairList;
    }

    /**
    * 大保底重置触发道具ID
    * pairarray bigGuarantee
    * 
    */
    public List<IntPairType> getBigGuaranteePairList(){
        return bigGuaranteePairList;
    }

}
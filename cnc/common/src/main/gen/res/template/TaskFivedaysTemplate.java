package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动任务.xlsx", node="task_fivedays.xml")
public class TaskFivedaysTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 任务ID
    * int taskId
    * 
    */
    @ResAttribute("taskId")
    private  int taskId;

    /**
    * 任务奖励
    * pairarray reward
    * 
    */
    @ResAttribute("reward")
    private List<IntPairType> rewardPairList;

    /**
    * 奖励活动积分id
    * string scoreUnitId
    * 
    */
    @ResAttribute("scoreUnitId")
    private  String scoreUnitId;

    /**
    * 奖励活动积分数
    * int scoreNum
    * 
    */
    @ResAttribute("scoreNum")
    private  int scoreNum;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 任务ID
    * int taskId
    * 
    */
    public int getTaskId(){
        return taskId;
    }


    /**
    * 任务奖励
    * pairarray reward
    * 
    */
    public List<IntPairType> getRewardPairList(){
        return rewardPairList;
    }
    /**
    * 奖励活动积分id
    * string scoreUnitId
    * 
    */
    public String getScoreUnitId(){
        return scoreUnitId;
    }
    /**
    * 奖励活动积分数
    * int scoreNum
    * 
    */
    public int getScoreNum(){
        return scoreNum;
    }


}
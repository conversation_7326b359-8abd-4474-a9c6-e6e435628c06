package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_大世界野怪投放区域.xlsx", node="big_scene_spawn_region.xml")
public class BigSceneSpawnRegionTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * kvk剧本id
    * int storyId
    * 
    */
    @ResAttribute("storyId")
    private  int storyId;

    /**
    * milestone
    * int milestone
    * 
    */
    @ResAttribute("milestone")
    private  int milestone;

    /**
    * 州ID序号
    * int regionId
    * 
    */
    @ResAttribute("regionId")
    private  int regionId;

    /**
    * 区域类型
    * CommonEnum.MapAreaType areaType
    * 
    */
    @ResAttribute("areaType")
    private CommonEnum.MapAreaType areaType;

    /**
    * 怪物类型
    * CommonEnum.MonsterCategory category
    * 
    */
    @ResAttribute("category")
    private CommonEnum.MonsterCategory category;

    /**
    * 品质
    * CommonEnum.SceneObjQuality quality
    * 
    */
    @ResAttribute("quality")
    private CommonEnum.SceneObjQuality quality;

    /**
    * 刷新比例（按照米，也就是说配置0.0001相当于1%的野怪）
    * float ratio
    * 
    */

    @ResAttribute("ratio")
    private  float ratio;
    /**
    * 数量
    * int num
    * 
    */
    @ResAttribute("num")
    private  int num;

    /**
    * 等级权重比
    * pairarray weight
    * 
    */
    @ResAttribute("weight")
    private List<IntPairType> weightPairList;

    /**
    * kvk活动类型
    * bool isKvkAct
    * 
    */
    @ResAttribute("isKvkAct")
    private  boolean isKvkAct;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * kvk剧本id
    * int storyId
    * 
    */
    public int getStoryId(){
        return storyId;
    }

    /**
    * milestone
    * int milestone
    * 
    */
    public int getMilestone(){
        return milestone;
    }

    /**
    * 州ID序号
    * int regionId
    * 
    */
    public int getRegionId(){
        return regionId;
    }


    /**
    * 区域类型
    * CommonEnum.MapAreaType areaType
    * 
    */
    public CommonEnum.MapAreaType getAreaType(){
        return areaType;
    }

    /**
    * 怪物类型
    * CommonEnum.MonsterCategory category
    * 
    */
    public CommonEnum.MonsterCategory getCategory(){
        return category;
    }

    /**
    * 品质
    * CommonEnum.SceneObjQuality quality
    * 
    */
    public CommonEnum.SceneObjQuality getQuality(){
        return quality;
    }
    /**
    * 刷新比例（按照米，也就是说配置0.0001相当于1%的野怪）
    * float ratio
    * 
    */
    public float getRatio(){
        return ratio;
    }

    /**
    * 数量
    * int num
    * 
    */
    public int getNum(){
        return num;
    }


    /**
    * 等级权重比
    * pairarray weight
    * 
    */
    public List<IntPairType> getWeightPairList(){
        return weightPairList;
    }

    /**
    * kvk活动类型
    * bool isKvkAct
    * 
    */
    public boolean getIsKvkAct(){
        return isKvkAct;
    }

}
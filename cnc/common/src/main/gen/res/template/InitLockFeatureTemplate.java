package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="G_功能解锁.xlsx", node="init_lock_feature.xml")
public class InitLockFeatureTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 上锁的功能模块
    * CommonEnum.ModuleEnum lockedFeature
    * 
    */
    @ResAttribute("lockedFeature")
    private CommonEnum.ModuleEnum lockedFeature;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 上锁的功能模块
    * CommonEnum.ModuleEnum lockedFeature
    * 
    */
    public CommonEnum.ModuleEnum getLockedFeature(){
        return lockedFeature;
    }

}
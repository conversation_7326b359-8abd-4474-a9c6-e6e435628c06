package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_抽奖活动表.xlsx", node="lottery_stage_reward.xml")
public class LotteryStageRewardTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 抽取几次(这里是细分次数)
    * int needTimes
    * 
    */
    @ResAttribute("needTimes")
    private  int needTimes;

    /**
    * 奖励
    * pairarray reward
    * 
    */
    @ResAttribute("reward")
    private List<IntPairType> rewardPairList;

    /**
    * 阶段奖励分组
    * int stageGroup
    * 
    */
    @ResAttribute("stageGroup")
    private  int stageGroup;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 抽取几次(这里是细分次数)
    * int needTimes
    * 
    */
    public int getNeedTimes(){
        return needTimes;
    }


    /**
    * 奖励
    * pairarray reward
    * 
    */
    public List<IntPairType> getRewardPairList(){
        return rewardPairList;
    }
    /**
    * 阶段奖励分组
    * int stageGroup
    * 
    */
    public int getStageGroup(){
        return stageGroup;
    }


}
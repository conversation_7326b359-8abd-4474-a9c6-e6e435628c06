package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_界面开关配置表.xlsx", node="ui_switch_config.xml")
public class UiSwitchConfigTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 未解锁是否显示，
在未解锁的时候是否会展示ui入口，0=不展示，1=展示，2=置灰
    * int show
    * 
    */
    @ResAttribute("show")
    private  int show;

    /**
    * 主堡等级解锁，
配置主堡的等级，当主堡等级达到配置的数值便会解锁
默认为0
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;

    /**
    * 开服时间，
配置具体的时分，在服务器开启一定时间后会解锁，
默认不填 
    * string servertime
    * 
    */
    @ResAttribute("servertime")
    private  String servertime;

    /**
    * 现实日期（UTC+0），
配置现实中的具体时间日期，用于配合运营的活动
月/日/年，9/20/2022
默认不填
    * string date
    * 
    */
    @ResAttribute("date")
    private  String date;

    /**
    * 开始时间，
配置一天的具体时间，在当天的指定时间会开启
如，14:30
默认不填
    * string section
    * 
    */
    @ResAttribute("section")
    private  String section;

    /**
    * 持续时间，
在功能开启后可以持续开启的时间，配置单位为分
(1小时=60分)
（1天=1440分）
默认为-1（无结束时间）
    * int lasttime
    * 
    */
    @ResAttribute("lasttime")
    private  int lasttime;

    /**
    * 触发式引导解锁，
配置触发引导表中具体步骤的id，当对应步骤结束后，会解锁
默认为0
    * int guide
    * 
    */
    @ResAttribute("guide")
    private  int guide;

    /**
    * 是否在新手引导阶段解锁功能
（0关闭，1解锁）
默认为1
    * int newguide
    * 
    */
    @ResAttribute("newguide")
    private  int newguide;

    /**
    * 里程碑解锁，
会根据里程碑的id来判断功能的解锁
默认不填
    * string chapterId
    * 
    */
    @ResAttribute("chapterId")
    private  String chapterId;

    /**
    * 特殊解锁，
当有脱离于前面几个解锁条件时，通过配置对应功能的key来进行判断（目前仅有排行榜功能需要）
默认不填
    * string excondition
    * 
    */
    @ResAttribute("excondition")
    private  String excondition;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 未解锁是否显示，
在未解锁的时候是否会展示ui入口，0=不展示，1=展示，2=置灰
    * int show
    * 
    */
    public int getShow(){
        return show;
    }

    /**
    * 主堡等级解锁，
配置主堡的等级，当主堡等级达到配置的数值便会解锁
默认为0
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }

    /**
    * 开服时间，
配置具体的时分，在服务器开启一定时间后会解锁，
默认不填 
    * string servertime
    * 
    */
    public String getServertime(){
        return servertime;
    }
    /**
    * 现实日期（UTC+0），
配置现实中的具体时间日期，用于配合运营的活动
月/日/年，9/20/2022
默认不填
    * string date
    * 
    */
    public String getDate(){
        return date;
    }
    /**
    * 开始时间，
配置一天的具体时间，在当天的指定时间会开启
如，14:30
默认不填
    * string section
    * 
    */
    public String getSection(){
        return section;
    }
    /**
    * 持续时间，
在功能开启后可以持续开启的时间，配置单位为分
(1小时=60分)
（1天=1440分）
默认为-1（无结束时间）
    * int lasttime
    * 
    */
    public int getLasttime(){
        return lasttime;
    }

    /**
    * 触发式引导解锁，
配置触发引导表中具体步骤的id，当对应步骤结束后，会解锁
默认为0
    * int guide
    * 
    */
    public int getGuide(){
        return guide;
    }

    /**
    * 是否在新手引导阶段解锁功能
（0关闭，1解锁）
默认为1
    * int newguide
    * 
    */
    public int getNewguide(){
        return newguide;
    }

    /**
    * 里程碑解锁，
会根据里程碑的id来判断功能的解锁
默认不填
    * string chapterId
    * 
    */
    public String getChapterId(){
        return chapterId;
    }
    /**
    * 特殊解锁，
当有脱离于前面几个解锁条件时，通过配置对应功能的key来进行判断（目前仅有排行榜功能需要）
默认不填
    * string excondition
    * 
    */
    public String getExcondition(){
        return excondition;
    }

}
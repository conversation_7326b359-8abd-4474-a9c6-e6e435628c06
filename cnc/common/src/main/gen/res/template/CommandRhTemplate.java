package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_城建_RH.xlsx", node="command_RH.xml")
public class CommandRhTemplate implements IResTemplate  {

    /**
    * 指令id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 指令组
    * int commandGroup
    * 
    */
    @ResAttribute("commandGroup")
    private  int commandGroup;

    /**
    * 指令属性加成效果
    * pairarray orderBuffEffect
    * 
    */
    @ResAttribute("orderBuffEffect")
    private List<IntPairType> orderBuffEffectPairList;

    /**
    * 指令特殊效果
    * pairarray param
    * 
    */
    @ResAttribute("param")
    private List<IntPairType> paramPairList;



    /**
    * 指令id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 指令组
    * int commandGroup
    * 
    */
    public int getCommandGroup(){
        return commandGroup;
    }


    /**
    * 指令属性加成效果
    * pairarray orderBuffEffect
    * 
    */
    public List<IntPairType> getOrderBuffEffectPairList(){
        return orderBuffEffectPairList;
    }

    /**
    * 指令特殊效果
    * pairarray param
    * 
    */
    public List<IntPairType> getParamPairList(){
        return paramPairList;
    }

}
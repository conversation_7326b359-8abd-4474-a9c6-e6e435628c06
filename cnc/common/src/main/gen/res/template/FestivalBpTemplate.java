package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_节日活动.xlsx", node="festival_bp.xml")
public class FestivalBpTemplate implements IResTemplate  {

    /**
    * 活动id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 通行证
    * intarray bpList
    * 
    */
    @ResAttribute("bpList")
    private List<Integer> bpListList;

    /**
    * 等级积分列表
    * intarray levelScore
    * 
    */
    @ResAttribute("levelScore")
    private List<Integer> levelScoreList;

    /**
    * 积分道具id
    * int scoreItem
    * 
    */
    @ResAttribute("scoreItem")
    private  int scoreItem;

    /**
    * 积分暴击(概率(分母1w)_积分数)
    * pairarray scoreSurprise
    * 
    */
    @ResAttribute("scoreSurprise")
    private List<IntPairType> scoreSurprisePairList;

    /**
    * 补发邮件id
    * int mailId
    * 
    */
    @ResAttribute("mailId")
    private  int mailId;

    /**
    * 活动结束后兑换
    * pairarray overExchange
    * 
    */
    @ResAttribute("overExchange")
    private List<IntPairType> overExchangePairList;



    /**
    * 活动id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 通行证
    * intarray bpList
    * 
    */
    public List<Integer> getBpListList(){
        return bpListList;
    }


    /**
    * 等级积分列表
    * intarray levelScore
    * 
    */
    public List<Integer> getLevelScoreList(){
        return levelScoreList;
    }

    /**
    * 积分道具id
    * int scoreItem
    * 
    */
    public int getScoreItem(){
        return scoreItem;
    }


    /**
    * 积分暴击(概率(分母1w)_积分数)
    * pairarray scoreSurprise
    * 
    */
    public List<IntPairType> getScoreSurprisePairList(){
        return scoreSurprisePairList;
    }
    /**
    * 补发邮件id
    * int mailId
    * 
    */
    public int getMailId(){
        return mailId;
    }


    /**
    * 活动结束后兑换
    * pairarray overExchange
    * 
    */
    public List<IntPairType> getOverExchangePairList(){
        return overExchangePairList;
    }

}
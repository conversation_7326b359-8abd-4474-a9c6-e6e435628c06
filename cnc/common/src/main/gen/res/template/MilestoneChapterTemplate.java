package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_里程碑.xlsx", node="milestone_chapter.xml")
public class MilestoneChapterTemplate implements IResTemplate  {

    /**
    * 章节ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 后置章节ID
    * int nextChapterID
    * 
    */
    @ResAttribute("nextChapterID")
    private  int nextChapterID;

    /**
    * 是否需要奖励列表ID
    * int needRewardList
    * 
    */
    @ResAttribute("needRewardList")
    private  int needRewardList;

    /**
    * 章节目标任务ID
    * int taskId
    * 
    */
    @ResAttribute("taskId")
    private  int taskId;

    /**
    * 章节持续时间（秒）
    * int duration
    * 
    */
    @ResAttribute("duration")
    private  int duration;

    /**
    * 章节奖励ID
rewardId
    * pairarray rewardId
    * 
    */
    @ResAttribute("rewardId")
    private List<IntPairType> rewardIdPairList;

    /**
    * 解锁关联玩法
    * intarray unlockGameplayType
    * 
    */
    @ResAttribute("unlockGameplayType")
    private List<Integer> unlockGameplayTypeList;

    /**
    * 章节开始时发送的邮件ID
    * intarray mailId
    * 
    */
    @ResAttribute("mailId")
    private List<Integer> mailIdList;



    /**
    * 章节ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 后置章节ID
    * int nextChapterID
    * 
    */
    public int getNextChapterID(){
        return nextChapterID;
    }

    /**
    * 是否需要奖励列表ID
    * int needRewardList
    * 
    */
    public int getNeedRewardList(){
        return needRewardList;
    }

    /**
    * 章节目标任务ID
    * int taskId
    * 
    */
    public int getTaskId(){
        return taskId;
    }

    /**
    * 章节持续时间（秒）
    * int duration
    * 
    */
    public int getDuration(){
        return duration;
    }


    /**
    * 章节奖励ID
rewardId
    * pairarray rewardId
    * 
    */
    public List<IntPairType> getRewardIdPairList(){
        return rewardIdPairList;
    }

    /**
    * 解锁关联玩法
    * intarray unlockGameplayType
    * 
    */
    public List<Integer> getUnlockGameplayTypeList(){
        return unlockGameplayTypeList;
    }


    /**
    * 章节开始时发送的邮件ID
    * intarray mailId
    * 
    */
    public List<Integer> getMailIdList(){
        return mailIdList;
    }


}
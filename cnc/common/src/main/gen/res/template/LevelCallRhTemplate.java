package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="G_关卡表_RH.xlsx", node="Level_call_RH.xml")
public class LevelCallRhTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 被召唤单位坐标偏移
    * pair SummonedUnitCoordinateOffset
    * 
    */
    @ResAttribute("SummonedUnitCoordinateOffset")
    private IntPairType SummonedUnitCoordinateOffsetPair;

    /**
    * 被召唤单位ID0
    * int SummonedUnitID0
    * 
    */
    @ResAttribute("SummonedUnitID0")
    private  int SummonedUnitID0;

    /**
    * 被召唤单位间隔0
    * int SummonedUnitInterval0
    * 
    */
    @ResAttribute("SummonedUnitInterval0")
    private  int SummonedUnitInterval0;

    /**
    * 被召唤单位数量0
    * int SummonedUnitQuantity0
    * 
    */
    @ResAttribute("SummonedUnitQuantity0")
    private  int SummonedUnitQuantity0;

    /**
    * 被召唤单位ID1
    * int SummonedUnitID1
    * 
    */
    @ResAttribute("SummonedUnitID1")
    private  int SummonedUnitID1;

    /**
    * 被召唤单位间隔1
    * int SummonedUnitInterval1
    * 
    */
    @ResAttribute("SummonedUnitInterval1")
    private  int SummonedUnitInterval1;

    /**
    * 被召唤单位数量1
    * int SummonedUnitQuantity1
    * 
    */
    @ResAttribute("SummonedUnitQuantity1")
    private  int SummonedUnitQuantity1;

    /**
    * 被召唤建筑ID0
    * int SummonedBuildingID0
    * 
    */
    @ResAttribute("SummonedBuildingID0")
    private  int SummonedBuildingID0;

    /**
    * 被召唤建筑范围半径0
    * int SummonedBuildingRangeRadius0
    * 
    */
    @ResAttribute("SummonedBuildingRangeRadius0")
    private  int SummonedBuildingRangeRadius0;

    /**
    * 被召唤建筑间隔0
    * int SummonedBuildingInterval0
    * 
    */
    @ResAttribute("SummonedBuildingInterval0")
    private  int SummonedBuildingInterval0;

    /**
    * 被召唤建筑数量0
    * int SummonedBuildingQuantity0
    * 
    */
    @ResAttribute("SummonedBuildingQuantity0")
    private  int SummonedBuildingQuantity0;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 被召唤单位坐标偏移
    * pair SummonedUnitCoordinateOffset
    * 
    */
    public IntPairType getSummonedUnitCoordinateOffsetPair(){
        return SummonedUnitCoordinateOffsetPair;
    }
    /**
    * 被召唤单位ID0
    * int SummonedUnitID0
    * 
    */
    public int getSummonedUnitID0(){
        return SummonedUnitID0;
    }

    /**
    * 被召唤单位间隔0
    * int SummonedUnitInterval0
    * 
    */
    public int getSummonedUnitInterval0(){
        return SummonedUnitInterval0;
    }

    /**
    * 被召唤单位数量0
    * int SummonedUnitQuantity0
    * 
    */
    public int getSummonedUnitQuantity0(){
        return SummonedUnitQuantity0;
    }

    /**
    * 被召唤单位ID1
    * int SummonedUnitID1
    * 
    */
    public int getSummonedUnitID1(){
        return SummonedUnitID1;
    }

    /**
    * 被召唤单位间隔1
    * int SummonedUnitInterval1
    * 
    */
    public int getSummonedUnitInterval1(){
        return SummonedUnitInterval1;
    }

    /**
    * 被召唤单位数量1
    * int SummonedUnitQuantity1
    * 
    */
    public int getSummonedUnitQuantity1(){
        return SummonedUnitQuantity1;
    }

    /**
    * 被召唤建筑ID0
    * int SummonedBuildingID0
    * 
    */
    public int getSummonedBuildingID0(){
        return SummonedBuildingID0;
    }

    /**
    * 被召唤建筑范围半径0
    * int SummonedBuildingRangeRadius0
    * 
    */
    public int getSummonedBuildingRangeRadius0(){
        return SummonedBuildingRangeRadius0;
    }

    /**
    * 被召唤建筑间隔0
    * int SummonedBuildingInterval0
    * 
    */
    public int getSummonedBuildingInterval0(){
        return SummonedBuildingInterval0;
    }

    /**
    * 被召唤建筑数量0
    * int SummonedBuildingQuantity0
    * 
    */
    public int getSummonedBuildingQuantity0(){
        return SummonedBuildingQuantity0;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_单位配置表【RH】.xlsx", node="units_config.xml")
public class UnitsConfigTemplate implements IResTemplate  {

    /**
    * 单位ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 单位名称
    * string UnitName
    * 
    */
    @ResAttribute("UnitName")
    private  String UnitName;

    /**
    * 枚举ID
    * int UnitType
    * 
    */
    @ResAttribute("UnitType")
    private  int UnitType;

    /**
    * 是否要养成解锁
    * int UnitGrowthLock
    * 
    */
    @ResAttribute("UnitGrowthLock")
    private  int UnitGrowthLock;

    /**
    * 单位模板ID
    * int UnitsTemplateID
    * 
    */
    @ResAttribute("UnitsTemplateID")
    private  int UnitsTemplateID;

    /**
    * 单位星级
    * int StartLevel
    * 
    */
    @ResAttribute("StartLevel")
    private  int StartLevel;

    /**
    * 移动速度
    * int Speed
    * 
    */
    @ResAttribute("Speed")
    private  int Speed;

    /**
    * 主武器/AA
    * int ArmamentPRIMARY
    * 
    */
    @ResAttribute("ArmamentPRIMARY")
    private  int ArmamentPRIMARY;

    /**
    * 副武器/AG
    * int ArmamentSECONDARY
    * 
    */
    @ResAttribute("ArmamentSECONDARY")
    private  int ArmamentSECONDARY;

    /**
    * 出生建筑
    * int Building
    * 
    */
    @ResAttribute("Building")
    private  int Building;

    /**
    * 出击的数量
    * int Quantity
    * 
    */
    @ResAttribute("Quantity")
    private  int Quantity;

    /**
    * 出击消耗
    * int Cost
    * 
    */
    @ResAttribute("Cost")
    private  int Cost;

    /**
    * 出击冷却时间（毫秒）
    * int Cooldown
    * 
    */
    @ResAttribute("Cooldown")
    private  int Cooldown;

    /**
    * 敌人出兵冷却时间
    * int EnemyCoolDown
    * 
    */
    @ResAttribute("EnemyCoolDown")
    private  int EnemyCoolDown;



    /**
    * 单位ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 单位名称
    * string UnitName
    * 
    */
    public String getUnitName(){
        return UnitName;
    }
    /**
    * 枚举ID
    * int UnitType
    * 
    */
    public int getUnitType(){
        return UnitType;
    }

    /**
    * 是否要养成解锁
    * int UnitGrowthLock
    * 
    */
    public int getUnitGrowthLock(){
        return UnitGrowthLock;
    }

    /**
    * 单位模板ID
    * int UnitsTemplateID
    * 
    */
    public int getUnitsTemplateID(){
        return UnitsTemplateID;
    }

    /**
    * 单位星级
    * int StartLevel
    * 
    */
    public int getStartLevel(){
        return StartLevel;
    }

    /**
    * 移动速度
    * int Speed
    * 
    */
    public int getSpeed(){
        return Speed;
    }

    /**
    * 主武器/AA
    * int ArmamentPRIMARY
    * 
    */
    public int getArmamentPRIMARY(){
        return ArmamentPRIMARY;
    }

    /**
    * 副武器/AG
    * int ArmamentSECONDARY
    * 
    */
    public int getArmamentSECONDARY(){
        return ArmamentSECONDARY;
    }

    /**
    * 出生建筑
    * int Building
    * 
    */
    public int getBuilding(){
        return Building;
    }

    /**
    * 出击的数量
    * int Quantity
    * 
    */
    public int getQuantity(){
        return Quantity;
    }

    /**
    * 出击消耗
    * int Cost
    * 
    */
    public int getCost(){
        return Cost;
    }

    /**
    * 出击冷却时间（毫秒）
    * int Cooldown
    * 
    */
    public int getCooldown(){
        return Cooldown;
    }

    /**
    * 敌人出兵冷却时间
    * int EnemyCoolDown
    * 
    */
    public int getEnemyCoolDown(){
        return EnemyCoolDown;
    }


}
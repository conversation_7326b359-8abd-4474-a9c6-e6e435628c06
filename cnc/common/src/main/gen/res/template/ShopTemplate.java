package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="S_商城表.xlsx", node="shop.xml")
public class ShopTemplate implements IResTemplate  {

    /**
    * 商品id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 道具id
    * int itemID
    * 
    */
    @ResAttribute("itemID")
    private  int itemID;

    /**
    * 购买重置时间（秒）
    * int refreshTime
    * 
    */
    @ResAttribute("refreshTime")
    private  int refreshTime;

    /**
    * 限购数量
    * int buyLimit
    * 
    */
    @ResAttribute("buyLimit")
    private  int buyLimit;

    /**
    * 上架时间
    * string startTime
    * 
    */
    @ResAttribute("startTime")
    private  String startTime;

    /**
    * 下架时间
    * string offTime
    * 
    */
    @ResAttribute("offTime")
    private  String offTime;

    /**
    * 实际价格
    * pair price
    * 
    */
    @ResAttribute("price")
    private IntPairType pricePair;

    /**
    * 是否显示
    * int show
    * 
    */
    @ResAttribute("show")
    private  int show;

    /**
    * 额外赠送道具（规避政策风险用）
    * pair extraItem
    * 
    */
    @ResAttribute("extraItem")
    private IntPairType extraItemPair;

    /**
    * 限制购买的组id
    * int numLimitGroupId
    * 
    */
    @ResAttribute("numLimitGroupId")
    private  int numLimitGroupId;



    /**
    * 商品id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 道具id
    * int itemID
    * 
    */
    public int getItemID(){
        return itemID;
    }

    /**
    * 购买重置时间（秒）
    * int refreshTime
    * 
    */
    public int getRefreshTime(){
        return refreshTime;
    }

    /**
    * 限购数量
    * int buyLimit
    * 
    */
    public int getBuyLimit(){
        return buyLimit;
    }

    /**
    * 上架时间
    * string startTime
    * 
    */
    public String getStartTime(){
        return startTime;
    }
    /**
    * 下架时间
    * string offTime
    * 
    */
    public String getOffTime(){
        return offTime;
    }

    /**
    * 实际价格
    * pair price
    * 
    */
    public IntPairType getPricePair(){
        return pricePair;
    }
    /**
    * 是否显示
    * int show
    * 
    */
    public int getShow(){
        return show;
    }


    /**
    * 额外赠送道具（规避政策风险用）
    * pair extraItem
    * 
    */
    public IntPairType getExtraItemPair(){
        return extraItemPair;
    }
    /**
    * 限制购买的组id
    * int numLimitGroupId
    * 
    */
    public int getNumLimitGroupId(){
        return numLimitGroupId;
    }


}
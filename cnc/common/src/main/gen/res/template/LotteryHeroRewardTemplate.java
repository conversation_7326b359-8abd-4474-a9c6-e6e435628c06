package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_抽奖活动表.xlsx", node="lottery_hero_reward.xml")
public class LotteryHeroRewardTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 英雄id
    * int heroId
    * 
    */
    @ResAttribute("heroId")
    private  int heroId;

    /**
    * 抽奖id
    * intarray drawIds
    * 
    */
    @ResAttribute("drawIds")
    private List<Integer> drawIdsList;

    /**
    * 阶段奖励id
    * intarray stageRewardIds
    * 
    */
    @ResAttribute("stageRewardIds")
    private List<Integer> stageRewardIdsList;

    /**
    * 阶段奖励补发邮件id
    * int stageRewardMailId
    * 
    */
    @ResAttribute("stageRewardMailId")
    private  int stageRewardMailId;

    /**
    * 界面展示英雄立绘
    * int showHeroSpId
    * 
    */
    @ResAttribute("showHeroSpId")
    private  int showHeroSpId;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 英雄id
    * int heroId
    * 
    */
    public int getHeroId(){
        return heroId;
    }


    /**
    * 抽奖id
    * intarray drawIds
    * 
    */
    public List<Integer> getDrawIdsList(){
        return drawIdsList;
    }


    /**
    * 阶段奖励id
    * intarray stageRewardIds
    * 
    */
    public List<Integer> getStageRewardIdsList(){
        return stageRewardIdsList;
    }

    /**
    * 阶段奖励补发邮件id
    * int stageRewardMailId
    * 
    */
    public int getStageRewardMailId(){
        return stageRewardMailId;
    }

    /**
    * 界面展示英雄立绘
    * int showHeroSpId
    * 
    */
    public int getShowHeroSpId(){
        return showHeroSpId;
    }


}
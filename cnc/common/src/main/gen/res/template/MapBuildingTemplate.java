package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_地图建筑配置表.xlsx", node="map_building.xml")
public class MapBuildingTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 建筑类型
    * CommonEnum.MapBuildingType type
    * 
    */
    @ResAttribute("type")
    private CommonEnum.MapBuildingType type;

    /**
    * 迁城阻挡半径（cm）
    * int collisionRadius
    * 
    */
    @ResAttribute("collisionRadius")
    private  int collisionRadius;

    /**
    * 行军阻挡半径（cm）
    * int pathRadius
    * 
    */
    @ResAttribute("pathRadius")
    private  int pathRadius;

    /**
    * 战斗模型圈半径（cm）
    * int modelRadius
    * 
    */
    @ResAttribute("modelRadius")
    private  int modelRadius;

    /**
    * 无极缩放物体枚举
    * CommonEnum.SceneObjectEnum objType
    * 
    */
    @ResAttribute("objType")
    private CommonEnum.SceneObjectEnum objType;

    /**
    * 重伤率计算枚举
    * CommonEnum.DamageRatioTypeEnum ratioType
    * 
    */
    @ResAttribute("ratioType")
    private CommonEnum.DamageRatioTypeEnum ratioType;

    /**
    * 部队Id
（对应部队配置表）
    * int troopId
    * 
    */
    @ResAttribute("troopId")
    private  int troopId;

    /**
    * 集结、驻防兵力上限
    * int maxSoldierNum
    * 
    */
    @ResAttribute("maxSoldierNum")
    private  int maxSoldierNum;

    /**
    * 探索报告
    * int mail
    * 
    */
    @ResAttribute("mail")
    private  int mail;

    /**
    * 视野范围
    * int view
    * 
    */
    @ResAttribute("view")
    private  int view;

    /**
    * 探索类型
    * int exploreType
    * 
    */
    @ResAttribute("exploreType")
    private  int exploreType;

    /**
    * 探索奖励
    * int reward
    * 
    */
    @ResAttribute("reward")
    private  int reward;

    /**
    * 探索奖励邮件
    * int rewardMail
    * 
    */
    @ResAttribute("rewardMail")
    private  int rewardMail;

    /**
    * 合围上限
    * int SiegeLimit
    * 
    */
    @ResAttribute("SiegeLimit")
    private  int SiegeLimit;

    /**
    * 合围角度
    * intarray besiegeAngle
    * 
    */
    @ResAttribute("besiegeAngle")
    private List<Integer> besiegeAngleList;

    /**
    * 刷新的野怪
    * pairarray refreshMonster
    * 
    */
    @ResAttribute("refreshMonster")
    private List<IntPairType> refreshMonsterPairList;

    /**
    * 初始化刷新组
    * int initializationGroup
    * 
    */
    @ResAttribute("initializationGroup")
    private  int initializationGroup;

    /**
    * ai行为模式
    * int aiIndex
    * 
    */
    @ResAttribute("aiIndex")
    private  int aiIndex;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 建筑类型
    * CommonEnum.MapBuildingType type
    * 
    */
    public CommonEnum.MapBuildingType getType(){
        return type;
    }
    /**
    * 迁城阻挡半径（cm）
    * int collisionRadius
    * 
    */
    public int getCollisionRadius(){
        return collisionRadius;
    }

    /**
    * 行军阻挡半径（cm）
    * int pathRadius
    * 
    */
    public int getPathRadius(){
        return pathRadius;
    }

    /**
    * 战斗模型圈半径（cm）
    * int modelRadius
    * 
    */
    public int getModelRadius(){
        return modelRadius;
    }


    /**
    * 无极缩放物体枚举
    * CommonEnum.SceneObjectEnum objType
    * 
    */
    public CommonEnum.SceneObjectEnum getObjType(){
        return objType;
    }

    /**
    * 重伤率计算枚举
    * CommonEnum.DamageRatioTypeEnum ratioType
    * 
    */
    public CommonEnum.DamageRatioTypeEnum getRatioType(){
        return ratioType;
    }
    /**
    * 部队Id
（对应部队配置表）
    * int troopId
    * 
    */
    public int getTroopId(){
        return troopId;
    }

    /**
    * 集结、驻防兵力上限
    * int maxSoldierNum
    * 
    */
    public int getMaxSoldierNum(){
        return maxSoldierNum;
    }

    /**
    * 探索报告
    * int mail
    * 
    */
    public int getMail(){
        return mail;
    }

    /**
    * 视野范围
    * int view
    * 
    */
    public int getView(){
        return view;
    }

    /**
    * 探索类型
    * int exploreType
    * 
    */
    public int getExploreType(){
        return exploreType;
    }

    /**
    * 探索奖励
    * int reward
    * 
    */
    public int getReward(){
        return reward;
    }

    /**
    * 探索奖励邮件
    * int rewardMail
    * 
    */
    public int getRewardMail(){
        return rewardMail;
    }

    /**
    * 合围上限
    * int SiegeLimit
    * 
    */
    public int getSiegeLimit(){
        return SiegeLimit;
    }


    /**
    * 合围角度
    * intarray besiegeAngle
    * 
    */
    public List<Integer> getBesiegeAngleList(){
        return besiegeAngleList;
    }


    /**
    * 刷新的野怪
    * pairarray refreshMonster
    * 
    */
    public List<IntPairType> getRefreshMonsterPairList(){
        return refreshMonsterPairList;
    }
    /**
    * 初始化刷新组
    * int initializationGroup
    * 
    */
    public int getInitializationGroup(){
        return initializationGroup;
    }

    /**
    * ai行为模式
    * int aiIndex
    * 
    */
    public int getAiIndex(){
        return aiIndex;
    }


}
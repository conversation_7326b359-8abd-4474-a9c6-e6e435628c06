package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_军团科技.xlsx", node="clan_tech_donate.xml")
public class ClanTechDonateTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 捐献类型
    * CommonEnum.ClanTechDonateType type
    * 
    */
    @ResAttribute("type")
    private CommonEnum.ClanTechDonateType type;

    /**
    * 倍率
    * int magnification
    * 
    */
    @ResAttribute("magnification")
    private  int magnification;

    /**
    * 概率
    * int probability
    * 
    */
    @ResAttribute("probability")
    private  int probability;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 捐献类型
    * CommonEnum.ClanTechDonateType type
    * 
    */
    public CommonEnum.ClanTechDonateType getType(){
        return type;
    }
    /**
    * 倍率
    * int magnification
    * 
    */
    public int getMagnification(){
        return magnification;
    }

    /**
    * 概率
    * int probability
    * 
    */
    public int getProbability(){
        return probability;
    }


}
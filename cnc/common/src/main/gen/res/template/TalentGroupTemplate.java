package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_英雄表.xlsx", node="talent_group.xml")
public class TalentGroupTemplate implements IResTemplate  {

    /**
    * 天赋组id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 解锁条件(前置组_天赋等级)
    * pairarray unlock
    * 
    */
    @ResAttribute("unlock")
    private List<IntPairType> unlockPairList;

    /**
    * 天赋定位
    * int type
    * 
    */
    @ResAttribute("type")
    private  int type;



    /**
    * 天赋组id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 解锁条件(前置组_天赋等级)
    * pairarray unlock
    * 
    */
    public List<IntPairType> getUnlockPairList(){
        return unlockPairList;
    }
    /**
    * 天赋定位
    * int type
    * 
    */
    public int getType(){
        return type;
    }


}
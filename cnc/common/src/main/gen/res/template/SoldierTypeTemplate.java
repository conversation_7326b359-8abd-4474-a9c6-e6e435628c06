package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="S_士兵表_兵种.xlsx", node="soldier_type.xml")
public class SoldierTypeTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 名称
    * language name
    * 
    */
    @ResAttribute("name")
    private  String name;

    /**
    * 兵种
    * int soldierType
    * 
    */
    @ResAttribute("soldierType")
    private  int soldierType;

    /**
    * 兵组等级
    * int soldierLevel
    * 
    */
    @ResAttribute("soldierLevel")
    private  int soldierLevel;

    /**
    * 攻
    * int atk
    * 
    */
    @ResAttribute("atk")
    private  int atk;

    /**
    * 防
    * int def
    * 
    */
    @ResAttribute("def")
    private  int def;

    /**
    * 血
    * int hp
    * 
    */
    @ResAttribute("hp")
    private  int hp;

    /**
    * 移速
    * int moveSpeed
    * 
    */
    @ResAttribute("moveSpeed")
    private  int moveSpeed;

    /**
    * 治疗消耗资源
    * pairarray treatRssCost
    * 
    */
    @ResAttribute("treatRssCost")
    private List<IntPairType> treatRssCostPairList;

    /**
    * 治疗时间
    * int treatTimeMillis
    * 
    */
    @ResAttribute("treatTimeMillis")
    private  int treatTimeMillis;

    /**
    * 负载
    * int burden
    * 
    */
    @ResAttribute("burden")
    private  int burden;

    /**
    * 战力
    * int power
    * 
    */
    @ResAttribute("power")
    private  int power;

    /**
    * 实际战力
    * int battlePower
    * 
    */
    @ResAttribute("battlePower")
    private  int battlePower;

    /**
    * 训练时间
    * int trainTimeMs
    * 
    */
    @ResAttribute("trainTimeMs")
    private  int trainTimeMs;

    /**
    * 消耗资源
    * pairarray trainRssCost
    * 
    */
    @ResAttribute("trainRssCost")
    private List<IntPairType> trainRssCostPairList;

    /**
    * 兵种归属
    * CommonEnum.SoldierBelong soldierBelong
    * 
    */
    @ResAttribute("soldierBelong")
    private CommonEnum.SoldierBelong soldierBelong;

    /**
    * 所属国家
    * int countryId
    * 
    */
    @ResAttribute("countryId")
    private  int countryId;

    /**
    * 战斗计算优先级
    * int battleCalcPriority
    * 
    */
    @ResAttribute("battleCalcPriority")
    private  int battleCalcPriority;

    /**
    * 兵种是否默认解锁
    * int defaultUnlock
    * 
    */
    @ResAttribute("defaultUnlock")
    private  int defaultUnlock;

    /**
    * 兵种超出容量的一回合死亡优先级
    * int diePriority
    * 
    */
    @ResAttribute("diePriority")
    private  int diePriority;

    /**
    * 击杀积分
    * float killScore
    * 
    */

    @ResAttribute("killScore")
    private  float killScore;


    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 名称
    * language name
    * 
    */
    public String getName(){
        return name;
    }
    /**
    * 兵种
    * int soldierType
    * 
    */
    public int getSoldierType(){
        return soldierType;
    }

    /**
    * 兵组等级
    * int soldierLevel
    * 
    */
    public int getSoldierLevel(){
        return soldierLevel;
    }

    /**
    * 攻
    * int atk
    * 
    */
    public int getAtk(){
        return atk;
    }

    /**
    * 防
    * int def
    * 
    */
    public int getDef(){
        return def;
    }

    /**
    * 血
    * int hp
    * 
    */
    public int getHp(){
        return hp;
    }

    /**
    * 移速
    * int moveSpeed
    * 
    */
    public int getMoveSpeed(){
        return moveSpeed;
    }


    /**
    * 治疗消耗资源
    * pairarray treatRssCost
    * 
    */
    public List<IntPairType> getTreatRssCostPairList(){
        return treatRssCostPairList;
    }
    /**
    * 治疗时间
    * int treatTimeMillis
    * 
    */
    public int getTreatTimeMillis(){
        return treatTimeMillis;
    }

    /**
    * 负载
    * int burden
    * 
    */
    public int getBurden(){
        return burden;
    }

    /**
    * 战力
    * int power
    * 
    */
    public int getPower(){
        return power;
    }

    /**
    * 实际战力
    * int battlePower
    * 
    */
    public int getBattlePower(){
        return battlePower;
    }

    /**
    * 训练时间
    * int trainTimeMs
    * 
    */
    public int getTrainTimeMs(){
        return trainTimeMs;
    }


    /**
    * 消耗资源
    * pairarray trainRssCost
    * 
    */
    public List<IntPairType> getTrainRssCostPairList(){
        return trainRssCostPairList;
    }

    /**
    * 兵种归属
    * CommonEnum.SoldierBelong soldierBelong
    * 
    */
    public CommonEnum.SoldierBelong getSoldierBelong(){
        return soldierBelong;
    }
    /**
    * 所属国家
    * int countryId
    * 
    */
    public int getCountryId(){
        return countryId;
    }

    /**
    * 战斗计算优先级
    * int battleCalcPriority
    * 
    */
    public int getBattleCalcPriority(){
        return battleCalcPriority;
    }

    /**
    * 兵种是否默认解锁
    * int defaultUnlock
    * 
    */
    public int getDefaultUnlock(){
        return defaultUnlock;
    }

    /**
    * 兵种超出容量的一回合死亡优先级
    * int diePriority
    * 
    */
    public int getDiePriority(){
        return diePriority;
    }

    /**
    * 击杀积分
    * float killScore
    * 
    */
    public float getKillScore(){
        return killScore;
    }


}
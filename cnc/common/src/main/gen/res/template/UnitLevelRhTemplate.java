package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_英雄表_New.xlsx", node="unit_level_rh.xml")
public class UnitLevelRhTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 兵种ID
    * int unit_id
    * 
    */
    @ResAttribute("unit_id")
    private  int unit_id;

    /**
    * 等级
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;

    /**
    * 攻击
    * int atk
    * 
    */
    @ResAttribute("atk")
    private  int atk;

    /**
    * 防御
    * int def
    * 
    */
    @ResAttribute("def")
    private  int def;

    /**
    * 生命
    * int hp
    * 
    */
    @ResAttribute("hp")
    private  int hp;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 兵种ID
    * int unit_id
    * 
    */
    public int getUnitId(){
        return unit_id;
    }

    /**
    * 等级
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }

    /**
    * 攻击
    * int atk
    * 
    */
    public int getAtk(){
        return atk;
    }

    /**
    * 防御
    * int def
    * 
    */
    public int getDef(){
        return def;
    }

    /**
    * 生命
    * int hp
    * 
    */
    public int getHp(){
        return hp;
    }


}
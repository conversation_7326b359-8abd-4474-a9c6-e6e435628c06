package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "J_军团科技.xlsx", node = "const_clan_tech.xml", isConst = true)
public class ConstClanTechTemplate implements IResConstTemplate  {

    /**
     * 联盟成员可捐献次数上限（单位：次）
     */
    private int MaxResDonateTimes = 0;
    /**
     * 联盟成员捐献次数回复时间（单位：s）
     */
    private int ResDonateRecoverySec = 0;
    /**
     * 加入联盟后金币捐献CD（单位：s）
     */
    private int DiamondDonateCdSec = 0;
    /**
     * 金币捐献初始消耗值
     */
    private int DiamondDonateInitialValue = 0;
    /**
     * 金币捐献每次增长消耗值
     */
    private int DiamondDonateGrowthValue = 0;
    /**
     * 金币捐献每次最大消耗值
     */
    private int DiamondDonateMaxValue = 0;
    /**
     * 每日贡献积分排行限制
     */
    private int DailyContributionPointsRankingLimit = 0;
    /**
     * 每周贡献积分排行限制
     */
    private int WeeklyContributionPointsRankingLimit = 0;
    /**
     * 联盟建筑类型和建筑id对应关系：类型id_建筑id
     */
    private List<IntPairType> ClanBuildingTypeMatch;
    /**
     * 科技研发完成邮件id
     */
    private int researchFinishMailId = 0;
    /**
     * 军团每日贡献排名邮件
     */
    private int dailyContributionRankMailId = 0;
    /**
     * 军团每周贡献排名邮件
     */
    private int weeklyContributionRankMailId = 0;
    /**
     * 军团科技红点计时间隔（单位：s），用于计算多久刷新一次红点数据，如果要修改需要通知客户端
     */
    private int redDotCalculationTime = 0;
    /**
     * 官员推荐箭头引导消失时间（单位：s）
     */
    private int arrowDisappearTime = 0;


    public int getMaxResDonateTimes(){
        return this.MaxResDonateTimes;
    }

    public int getResDonateRecoverySec(){
        return this.ResDonateRecoverySec;
    }

    public int getDiamondDonateCdSec(){
        return this.DiamondDonateCdSec;
    }

    public int getDiamondDonateInitialValue(){
        return this.DiamondDonateInitialValue;
    }

    public int getDiamondDonateGrowthValue(){
        return this.DiamondDonateGrowthValue;
    }

    public int getDiamondDonateMaxValue(){
        return this.DiamondDonateMaxValue;
    }

    public int getDailyContributionPointsRankingLimit(){
        return this.DailyContributionPointsRankingLimit;
    }

    public int getWeeklyContributionPointsRankingLimit(){
        return this.WeeklyContributionPointsRankingLimit;
    }

    public List<IntPairType> getClanBuildingTypeMatch(){
        return this.ClanBuildingTypeMatch;
    }

    public int getResearchFinishMailId(){
        return this.researchFinishMailId;
    }

    public int getDailyContributionRankMailId(){
        return this.dailyContributionRankMailId;
    }

    public int getWeeklyContributionRankMailId(){
        return this.weeklyContributionRankMailId;
    }

    public int getRedDotCalculationTime(){
        return this.redDotCalculationTime;
    }

    public int getArrowDisappearTime(){
        return this.arrowDisappearTime;
    }

    @Override
    public int getId() {
        return 0;
    }
}

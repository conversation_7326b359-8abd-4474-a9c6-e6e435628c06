package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_野怪.xlsx", node="monster_invoke.xml")
public class MonsterInvokeTemplate implements IResTemplate  {

    /**
    * 召唤id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 野怪id
    * int monsterId
    * 
    */
    @ResAttribute("monsterId")
    private  int monsterId;

    /**
    * 召唤数量
    * int num
    * 
    */
    @ResAttribute("num")
    private  int num;

    /**
    * 存活时间(秒)
    * int lifeTime
    * 
    */
    @ResAttribute("lifeTime")
    private  int lifeTime;

    /**
    * 出生区域
    * circlearray bornArea
    * 
    */
    @ResAttribute("bornArea")
    private List<CircleType> bornAreaList;
    /**
    * 出生移动目标区域
参数1：向量方向 参数2：长度厘米 参数3：半径厘米

    * circlearray moveArea
    * 
    */
    @ResAttribute("moveArea")
    private List<CircleType> moveAreaList;
    /**
    * 出生移动是否禁止战斗
    * bool moveBattleForbidden
    * 
    */
    @ResAttribute("moveBattleForbidden")
    private  boolean moveBattleForbidden;



    /**
    * 召唤id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 野怪id
    * int monsterId
    * 
    */
    public int getMonsterId(){
        return monsterId;
    }

    /**
    * 召唤数量
    * int num
    * 
    */
    public int getNum(){
        return num;
    }

    /**
    * 存活时间(秒)
    * int lifeTime
    * 
    */
    public int getLifeTime(){
        return lifeTime;
    }


    /**
    * 出生区域
    * circlearray bornArea
    * 
    */
    public List<CircleType> getBornAreaList(){
        return bornAreaList;
    }

    /**
    * 出生移动目标区域
参数1：向量方向 参数2：长度厘米 参数3：半径厘米

    * circlearray moveArea
    * 
    */
    public List<CircleType> getMoveAreaList(){
        return moveAreaList;
    }

    /**
    * 出生移动是否禁止战斗
    * bool moveBattleForbidden
    * 
    */
    public boolean getMoveBattleForbidden(){
        return moveBattleForbidden;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="B_BOSS战场配置表.xlsx", node="monster_refresh_group.xml")
public class MonsterRefreshGroupTemplate implements IResTemplate  {

    /**
    * 序号
（仅作为唯一id）
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 刷新组Id
    * int groupId
    * 
    */
    @ResAttribute("groupId")
    private  int groupId;

    /**
    * 延迟时间
    * int delayTime
    * 
    */
    @ResAttribute("delayTime")
    private  int delayTime;

    /**
    * 刷新个数
    * int num
    * 
    */
    @ResAttribute("num")
    private  int num;

    /**
    * 触发的表现ID
    * int playShowId
    * 
    */
    @ResAttribute("playShowId")
    private  int playShowId;



    /**
    * 序号
（仅作为唯一id）
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 刷新组Id
    * int groupId
    * 
    */
    public int getGroupId(){
        return groupId;
    }

    /**
    * 延迟时间
    * int delayTime
    * 
    */
    public int getDelayTime(){
        return delayTime;
    }

    /**
    * 刷新个数
    * int num
    * 
    */
    public int getNum(){
        return num;
    }

    /**
    * 触发的表现ID
    * int playShowId
    * 
    */
    public int getPlayShowId(){
        return playShowId;
    }


}
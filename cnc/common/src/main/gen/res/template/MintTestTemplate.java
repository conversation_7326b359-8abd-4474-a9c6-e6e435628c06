package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_演示测试导表工具专用.xlsx", node="mint_test.xml")
public class MintTestTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 字段1
    * string value1
    * 
    */
    @ResAttribute("value1")
    private  String value1;

    /**
    * 字段2
    * int value2
    * 
    */
    @ResAttribute("value2")
    private  int value2;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 字段1
    * string value1
    * 
    */
    public String getValue1(){
        return value1;
    }
    /**
    * 字段2
    * int value2
    * 
    */
    public int getValue2(){
        return value2;
    }


}
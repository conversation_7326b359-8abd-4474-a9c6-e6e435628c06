package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_道具表.xlsx", node="item.xml")
public class ItemTemplate implements IResTemplate  {

    /**
    * 道具ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 道具名称
    * string name
    * 
    */
    @ResAttribute("name")
    private  String name;

    /**
    * 是否可在背包中使用
    * int useAble
    * 
    */
    @ResAttribute("useAble")
    private  int useAble;

    /**
    * 道具效果类型
    * int effectType
    * 
    */
    @ResAttribute("effectType")
    private  int effectType;

    /**
    * 道具效果ID
    * int effectId
    * 
    */
    @ResAttribute("effectId")
    private  int effectId;

    /**
    * 道具效果value
    * int effectValue
    * 
    */
    @ResAttribute("effectValue")
    private  int effectValue;

    /**
    * 道具效果value2
    * int effectValue2
    * 
    */
    @ResAttribute("effectValue2")
    private  int effectValue2;

    /**
    * 道具批量使用上限
    * int batchUseLimit
    * 
    */
    @ResAttribute("batchUseLimit")
    private  int batchUseLimit;

    /**
    * 道具拥有上限
    * int ownLimit
    * 
    */
    @ResAttribute("ownLimit")
    private  int ownLimit;

    /**
    * 道具冷却时间
    * int cdSeconds
    * 
    */
    @ResAttribute("cdSeconds")
    private  int cdSeconds;

    /**
    * 品质
    * int quality
    * 
    */
    @ResAttribute("quality")
    private  int quality;

    /**
    * 最小使用等级限制
    * int minLevel
    * 
    */
    @ResAttribute("minLevel")
    private  int minLevel;

    /**
    * 最大使用等级限制
    * int maxLevel
    * 
    */
    @ResAttribute("maxLevel")
    private  int maxLevel;

    /**
    * 道具回收配置
    * string withdrawParam
    * 
    */
    @ResAttribute("withdrawParam")
    private  String withdrawParam;

    /**
    * 道具回收时兑换变成的道具
    * pairarray withdrawItemId
    * 
    */
    @ResAttribute("withdrawItemId")
    private List<IntPairType> withdrawItemIdPairList;

    /**
    * 道具模型
    * string model
    * 
    */
    @ResAttribute("model")
    private  String model;

    /**
    * 是否自动使用
    * bool useNow
    * 
    */
    @ResAttribute("useNow")
    private  boolean useNow;

    /**
    * 道具重复使用后可兑换道具
    * pairarray exchangeItemId
    * 
    */
    @ResAttribute("exchangeItemId")
    private List<IntPairType> exchangeItemIdPairList;

    /**
    * 道具占战役背包多大
    * int itemCost
    * 
    */
    @ResAttribute("itemCost")
    private  int itemCost;



    /**
    * 道具ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 道具名称
    * string name
    * 
    */
    public String getName(){
        return name;
    }
    /**
    * 是否可在背包中使用
    * int useAble
    * 
    */
    public int getUseAble(){
        return useAble;
    }

    /**
    * 道具效果类型
    * int effectType
    * 
    */
    public int getEffectType(){
        return effectType;
    }

    /**
    * 道具效果ID
    * int effectId
    * 
    */
    public int getEffectId(){
        return effectId;
    }

    /**
    * 道具效果value
    * int effectValue
    * 
    */
    public int getEffectValue(){
        return effectValue;
    }

    /**
    * 道具效果value2
    * int effectValue2
    * 
    */
    public int getEffectValue2(){
        return effectValue2;
    }

    /**
    * 道具批量使用上限
    * int batchUseLimit
    * 
    */
    public int getBatchUseLimit(){
        return batchUseLimit;
    }

    /**
    * 道具拥有上限
    * int ownLimit
    * 
    */
    public int getOwnLimit(){
        return ownLimit;
    }

    /**
    * 道具冷却时间
    * int cdSeconds
    * 
    */
    public int getCdSeconds(){
        return cdSeconds;
    }

    /**
    * 品质
    * int quality
    * 
    */
    public int getQuality(){
        return quality;
    }

    /**
    * 最小使用等级限制
    * int minLevel
    * 
    */
    public int getMinLevel(){
        return minLevel;
    }

    /**
    * 最大使用等级限制
    * int maxLevel
    * 
    */
    public int getMaxLevel(){
        return maxLevel;
    }

    /**
    * 道具回收配置
    * string withdrawParam
    * 
    */
    public String getWithdrawParam(){
        return withdrawParam;
    }

    /**
    * 道具回收时兑换变成的道具
    * pairarray withdrawItemId
    * 
    */
    public List<IntPairType> getWithdrawItemIdPairList(){
        return withdrawItemIdPairList;
    }
    /**
    * 道具模型
    * string model
    * 
    */
    public String getModel(){
        return model;
    }

    /**
    * 是否自动使用
    * bool useNow
    * 
    */
    public boolean getUseNow(){
        return useNow;
    }

    /**
    * 道具重复使用后可兑换道具
    * pairarray exchangeItemId
    * 
    */
    public List<IntPairType> getExchangeItemIdPairList(){
        return exchangeItemIdPairList;
    }
    /**
    * 道具占战役背包多大
    * int itemCost
    * 
    */
    public int getItemCost(){
        return itemCost;
    }


}
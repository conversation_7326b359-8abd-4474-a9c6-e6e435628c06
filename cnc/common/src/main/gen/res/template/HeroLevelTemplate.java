package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_英雄表.xlsx", node="hero_level.xml")
public class HeroLevelTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 等级
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;

    /**
    * 经验需求
    * pairarray exp
    * 
    */
    @ResAttribute("exp")
    private List<IntPairType> expPairList;

    /**
    * 部队容量
    * int capacity
    * 
    */
    @ResAttribute("capacity")
    private  int capacity;

    /**
    * 战斗力增加
    * int power
    * 
    */
    @ResAttribute("power")
    private  int power;

    /**
    * 获得天赋点
    * int talentPoint
    * 
    */
    @ResAttribute("talentPoint")
    private  int talentPoint;

    /**
    * 攻击力加成
    * int atkBuff
    * 
    */
    @ResAttribute("atkBuff")
    private  int atkBuff;

    /**
    * 护甲值加成
    * int defBuff
    * 
    */
    @ResAttribute("defBuff")
    private  int defBuff;

    /**
    * 生命值加成
    * int healthBuff
    * 
    */
    @ResAttribute("healthBuff")
    private  int healthBuff;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 等级
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }


    /**
    * 经验需求
    * pairarray exp
    * 
    */
    public List<IntPairType> getExpPairList(){
        return expPairList;
    }
    /**
    * 部队容量
    * int capacity
    * 
    */
    public int getCapacity(){
        return capacity;
    }

    /**
    * 战斗力增加
    * int power
    * 
    */
    public int getPower(){
        return power;
    }

    /**
    * 获得天赋点
    * int talentPoint
    * 
    */
    public int getTalentPoint(){
        return talentPoint;
    }

    /**
    * 攻击力加成
    * int atkBuff
    * 
    */
    public int getAtkBuff(){
        return atkBuff;
    }

    /**
    * 护甲值加成
    * int defBuff
    * 
    */
    public int getDefBuff(){
        return defBuff;
    }

    /**
    * 生命值加成
    * int healthBuff
    * 
    */
    public int getHealthBuff(){
        return healthBuff;
    }


}
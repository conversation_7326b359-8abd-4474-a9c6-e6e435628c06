package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_技能表_效果.xlsx", node="summoning_monster.xml")
public class SummoningMonsterTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 召唤物组ID
    * int GroupId
    * 
    */
    @ResAttribute("GroupId")
    private  int GroupId;

    /**
    * 野怪ID
    * int monsterID
    * 
    */
    @ResAttribute("monsterID")
    private  int monsterID;

    /**
    * 出生偏移（X,Y）
    * pair deviation
    * 
    */
    @ResAttribute("deviation")
    private IntPairType deviationPair;

    /**
    * 出生范围（环：内圆、外圆）
    * pair Range
    * 
    */
    @ResAttribute("Range")
    private IntPairType RangePair;

    /**
    * 出生面向
    * CommonEnum.InvokeYawType BirthFace
    * 
    */
    @ResAttribute("BirthFace")
    private CommonEnum.InvokeYawType BirthFace;

    /**
    * 存活时间（秒）
    * int savetime
    * 
    */
    @ResAttribute("savetime")
    private  int savetime;

    /**
    * 野怪回收（0：回收 1：不回收）
    * int recycling
    * 
    */
    @ResAttribute("recycling")
    private  int recycling;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 召唤物组ID
    * int GroupId
    * 
    */
    public int getGroupId(){
        return GroupId;
    }

    /**
    * 野怪ID
    * int monsterID
    * 
    */
    public int getMonsterID(){
        return monsterID;
    }


    /**
    * 出生偏移（X,Y）
    * pair deviation
    * 
    */
    public IntPairType getDeviationPair(){
        return deviationPair;
    }

    /**
    * 出生范围（环：内圆、外圆）
    * pair Range
    * 
    */
    public IntPairType getRangePair(){
        return RangePair;
    }

    /**
    * 出生面向
    * CommonEnum.InvokeYawType BirthFace
    * 
    */
    public CommonEnum.InvokeYawType getBirthFace(){
        return BirthFace;
    }
    /**
    * 存活时间（秒）
    * int savetime
    * 
    */
    public int getSavetime(){
        return savetime;
    }

    /**
    * 野怪回收（0：回收 1：不回收）
    * int recycling
    * 
    */
    public int getRecycling(){
        return recycling;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="S_设置表.xlsx", node="expression.xml")
public class ExpressionTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 类型
    * int type
    * 
    */
    @ResAttribute("type")
    private  int type;

    /**
    * 是否默认解锁
    * bool unlock
    * 
    */
    @ResAttribute("unlock")
    private  boolean unlock;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 类型
    * int type
    * 
    */
    public int getType(){
        return type;
    }


    /**
    * 是否默认解锁
    * bool unlock
    * 
    */
    public boolean getUnlock(){
        return unlock;
    }

}
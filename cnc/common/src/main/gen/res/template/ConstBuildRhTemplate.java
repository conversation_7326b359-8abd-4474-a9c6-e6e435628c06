package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "C_城建_RH.xlsx", node = "const_build_RH.xml", isConst = true)
public class ConstBuildRhTemplate implements IResConstTemplate  {

    /**
     * 建造中心可摆放区域大小
     */
    private IntPairType constructionYardBaseSize;
    /**
     * 指令解锁特殊效果小图标
     */
    private List<IntPairType> commandParamIcon;
    /**
     * 建设值图标
     */
    private int constructionIcon = 0;
    /**
     * 基地原点坐标
     */
    private IntPairType baseLocationOrigin;


    public IntPairType getConstructionYardBaseSize(){
        return this.constructionYardBaseSize;
    }

    public List<IntPairType> getCommandParamIcon(){
        return this.commandParamIcon;
    }

    public int getConstructionIcon(){
        return this.constructionIcon;
    }

    public IntPairType getBaseLocationOrigin(){
        return this.baseLocationOrigin;
    }

    @Override
    public int getId() {
        return 0;
    }
}

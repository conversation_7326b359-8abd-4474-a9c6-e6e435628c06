package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="S_商城表.xlsx", node="shop_limit.xml")
public class ShopLimitTemplate implements IResTemplate  {

    /**
    * 序号id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 限制购买的组id
    * int numLimitGroupId
    * 
    */
    @ResAttribute("numLimitGroupId")
    private  int numLimitGroupId;

    /**
    * 主堡等级
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;

    /**
    * 每天最多购买的数量
    * int purchaseMaxNum
    * 
    */
    @ResAttribute("purchaseMaxNum")
    private  int purchaseMaxNum;

    /**
    * 购买数量不足时提示的错误码
    * int errorcodeId
    * 
    */
    @ResAttribute("errorcodeId")
    private  int errorcodeId;



    /**
    * 序号id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 限制购买的组id
    * int numLimitGroupId
    * 
    */
    public int getNumLimitGroupId(){
        return numLimitGroupId;
    }

    /**
    * 主堡等级
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }

    /**
    * 每天最多购买的数量
    * int purchaseMaxNum
    * 
    */
    public int getPurchaseMaxNum(){
        return purchaseMaxNum;
    }

    /**
    * 购买数量不足时提示的错误码
    * int errorcodeId
    * 
    */
    public int getErrorcodeId(){
        return errorcodeId;
    }


}
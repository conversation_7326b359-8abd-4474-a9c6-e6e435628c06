package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_呼脸礼包.xlsx", node="activity_resell_trigger_goods.xml")
public class ActivityResellTriggerGoodsTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 活动id
    * int activityId
    * 
    */
    @ResAttribute("activityId")
    private  int activityId;

    /**
    * 礼包类型
    * int bundleType
    * 
    */
    @ResAttribute("bundleType")
    private  int bundleType;

    /**
    * 类型数量
    * int count
    * 
    */
    @ResAttribute("count")
    private  int count;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 活动id
    * int activityId
    * 
    */
    public int getActivityId(){
        return activityId;
    }

    /**
    * 礼包类型
    * int bundleType
    * 
    */
    public int getBundleType(){
        return bundleType;
    }

    /**
    * 类型数量
    * int count
    * 
    */
    public int getCount(){
        return count;
    }


}
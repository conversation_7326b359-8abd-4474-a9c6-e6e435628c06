package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_联盟领地表.xlsx", node="clan_power_reward.xml")
public class ClanPowerRewardTemplate implements IResTemplate  {

    /**
    * 势力等级
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 势力档位下限
    * int powerMin
    * 
    */
    @ResAttribute("powerMin")
    private  int powerMin;

    /**
    * 势力档位上限
    * int powerMax
    * 
    */
    @ResAttribute("powerMax")
    private  int powerMax;

    /**
    * 普通奖励id
    * int rewardId
    * 
    */
    @ResAttribute("rewardId")
    private  int rewardId;

    /**
    * 首次达到该等级奖励邮件
    * int mailId
    * 
    */
    @ResAttribute("mailId")
    private  int mailId;



    /**
    * 势力等级
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 势力档位下限
    * int powerMin
    * 
    */
    public int getPowerMin(){
        return powerMin;
    }

    /**
    * 势力档位上限
    * int powerMax
    * 
    */
    public int getPowerMax(){
        return powerMax;
    }

    /**
    * 普通奖励id
    * int rewardId
    * 
    */
    public int getRewardId(){
        return rewardId;
    }

    /**
    * 首次达到该等级奖励邮件
    * int mailId
    * 
    */
    public int getMailId(){
        return mailId;
    }


}
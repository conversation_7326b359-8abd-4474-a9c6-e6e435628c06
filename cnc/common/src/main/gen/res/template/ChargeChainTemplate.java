package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_支付.xlsx", node="charge_chain.xml")
public class ChargeChainTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 礼包链中的子礼包id
    * intarray chargeChainGoodsId
    * 
    */
    @ResAttribute("chargeChainGoodsId")
    private List<Integer> chargeChainGoodsIdList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 礼包链中的子礼包id
    * intarray chargeChainGoodsId
    * 
    */
    public List<Integer> getChargeChainGoodsIdList(){
        return chargeChainGoodsIdList;
    }


}
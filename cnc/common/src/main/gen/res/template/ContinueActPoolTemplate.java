package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_连续活动.xlsx", node="continue_act_pool.xml")
public class ContinueActPoolTemplate implements IResTemplate  {

    /**
    * 奖励id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 所需积分
    * int score
    * 
    */
    @ResAttribute("score")
    private  int score;

    /**
    * 奖励
    * pair reward
    * 
    */
    @ResAttribute("reward")
    private IntPairType rewardPair;



    /**
    * 奖励id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 所需积分
    * int score
    * 
    */
    public int getScore(){
        return score;
    }


    /**
    * 奖励
    * pair reward
    * 
    */
    public IntPairType getRewardPair(){
        return rewardPair;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_障碍物.xlsx", node="obstacle.xml")
public class ObstacleTemplate implements IResTemplate  {

    /**
    * 障碍物ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 阻挡区域
    * triple blockingArea
    * 
    */
    @ResAttribute("blockingArea")
    private IntTripleType blockingAreaTriple;

    /**
    * 障碍物半径
    * int radius
    * 
    */
    @ResAttribute("radius")
    private  int radius;



    /**
    * 障碍物ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 阻挡区域
    * triple blockingArea
    * 
    */
    public IntTripleType getBlockingAreaTriple(){
        return blockingAreaTriple;
    }
    /**
    * 障碍物半径
    * int radius
    * 
    */
    public int getRadius(){
        return radius;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="M_模板.xlsx", node="template.xml")
public class TemplateTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 类型
    * int type
    * 
    */
    @ResAttribute("type")
    private  int type;

    /**
    * 等级
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;

    /**
    * 位置
    * string posPrior
    * 
    */
    @ResAttribute("posPrior")
    private  String posPrior;

    /**
    * 回合
    * bool attackRound
    * 
    */
    @ResAttribute("attackRound")
    private  boolean attackRound;

    /**
    * 攻击范围
    * pairarray attackDis
    * 
    */
    @ResAttribute("attackDis")
    private List<IntPairType> attackDisPairList;

    /**
    * 权重
    * triple weight
    * 
    */
    @ResAttribute("weight")
    private IntTripleType weightTriple;

    /**
    * 飞行高度
    * triplearray tall
    * 
    */
    @ResAttribute("tall")
    private List<IntTripleType> tallTripleList;

    /**
    * 日期
    * date date
    * 
    */
    @ResAttribute("date")
    private Date dateDt;
    /**
    * 日期
    * float f
    * 
    */

    @ResAttribute("f")
    private  float f;
    /**
    * 日期
    * floatarray fa
    * 
    */
    @ResAttribute("fa")
    private List<Float> faList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 类型
    * int type
    * 
    */
    public int getType(){
        return type;
    }

    /**
    * 等级
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }

    /**
    * 位置
    * string posPrior
    * 
    */
    public String getPosPrior(){
        return posPrior;
    }

    /**
    * 回合
    * bool attackRound
    * 
    */
    public boolean getAttackRound(){
        return attackRound;
    }

    /**
    * 攻击范围
    * pairarray attackDis
    * 
    */
    public List<IntPairType> getAttackDisPairList(){
        return attackDisPairList;
    }

    /**
    * 权重
    * triple weight
    * 
    */
    public IntTripleType getWeightTriple(){
        return weightTriple;
    }

    /**
    * 飞行高度
    * triplearray tall
    * 
    */
    public List<IntTripleType> getTallTripleList(){
        return tallTripleList;
    }       

    /**
    * 日期
    * date date
    * 
    */
    public Date getDateDt(){
        return dateDt;
    }
    /**
    * 日期
    * float f
    * 
    */
    public float getF(){
        return f;
    }


    /**
    * 日期
    * floatarray fa
    * 
    */
    public List<Float> getFaList(){
        return faList;
    }

}
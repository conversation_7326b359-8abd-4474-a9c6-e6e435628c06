package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="X_消息推送.xlsx", node="push_setting.xml")
public class PushSettingTemplate implements IResTemplate  {

    /**
    * 类型ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 客户端设置
    * bool isClient
    * 
    */
    @ResAttribute("isClient")
    private  boolean isClient;

    /**
    * 默认开启状态
    * bool initialOpen
    * 
    */
    @ResAttribute("initialOpen")
    private  boolean initialOpen;



    /**
    * 类型ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 客户端设置
    * bool isClient
    * 
    */
    public boolean getIsClient(){
        return isClient;
    }

    /**
    * 默认开启状态
    * bool initialOpen
    * 
    */
    public boolean getInitialOpen(){
        return initialOpen;
    }

}
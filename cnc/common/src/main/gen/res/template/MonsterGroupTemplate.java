package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_成就.xlsx", node="monster_group.xml")
public class MonsterGroupTemplate implements IResTemplate  {

    /**
    * 野怪组id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 野怪id列表
    * intarray monsterList
    * 
    */
    @ResAttribute("monsterList")
    private List<Integer> monsterListList;



    /**
    * 野怪组id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 野怪id列表
    * intarray monsterList
    * 
    */
    public List<Integer> getMonsterListList(){
        return monsterListList;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_联盟配置表.xlsx", node="clan_official_level.xml")
public class ClanOfficialLevelTemplate implements IResTemplate  {

    /**
    * 职位等级id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 等级数量上限
    * int staffLimit
    * 
    */
    @ResAttribute("staffLimit")
    private  int staffLimit;



    /**
    * 职位等级id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 等级数量上限
    * int staffLimit
    * 
    */
    public int getStaffLimit(){
        return staffLimit;
    }


}
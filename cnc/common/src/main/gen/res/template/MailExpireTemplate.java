package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_邮件.xlsx", node="mail_expire.xml")
public class MailExpireTemplate implements IResTemplate  {

    /**
    * 冗余ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 邮件类型页签TYPE
    * CommonEnum.MailTabsType mailTabsType
    * 
    */
    @ResAttribute("mailTabsType")
    private CommonEnum.MailTabsType mailTabsType;

    /**
    * 邮件最大条数
    * int max
    * 
    */
    @ResAttribute("max")
    private  int max;

    /**
    * 收藏邮件最大条数
    * int collectMax
    * 
    */
    @ResAttribute("collectMax")
    private  int collectMax;

    /**
    * 邮件过期时间（天）
    * int expire
    * 
    */
    @ResAttribute("expire")
    private  int expire;



    /**
    * 冗余ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 邮件类型页签TYPE
    * CommonEnum.MailTabsType mailTabsType
    * 
    */
    public CommonEnum.MailTabsType getMailTabsType(){
        return mailTabsType;
    }
    /**
    * 邮件最大条数
    * int max
    * 
    */
    public int getMax(){
        return max;
    }

    /**
    * 收藏邮件最大条数
    * int collectMax
    * 
    */
    public int getCollectMax(){
        return collectMax;
    }

    /**
    * 邮件过期时间（天）
    * int expire
    * 
    */
    public int getExpire(){
        return expire;
    }


}
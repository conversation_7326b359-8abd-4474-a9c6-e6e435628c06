package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="R_任务.xlsx", node="task_PVE.xml")
public class TaskPveTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * PVE任务ID
    * int PVEtaskId
    * 
    */
    @ResAttribute("PVEtaskId")
    private  int PVEtaskId;

    /**
    * 关卡进度解锁
    * int unlockLevelId
    * 
    */
    @ResAttribute("unlockLevelId")
    private  int unlockLevelId;

    /**
    * 任务限时
    * int PVEchapterTime
    * 
    */
    @ResAttribute("PVEchapterTime")
    private  int PVEchapterTime;

    /**
    * PVE章节ID
    * int PvechapterId
    * 
    */
    @ResAttribute("PvechapterId")
    private  int PvechapterId;

    /**
    * 任务奖励
    * pairarray reward
    * 
    */
    @ResAttribute("reward")
    private List<IntPairType> rewardPairList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * PVE任务ID
    * int PVEtaskId
    * 
    */
    public int getPVEtaskId(){
        return PVEtaskId;
    }

    /**
    * 关卡进度解锁
    * int unlockLevelId
    * 
    */
    public int getUnlockLevelId(){
        return unlockLevelId;
    }

    /**
    * 任务限时
    * int PVEchapterTime
    * 
    */
    public int getPVEchapterTime(){
        return PVEchapterTime;
    }

    /**
    * PVE章节ID
    * int PvechapterId
    * 
    */
    public int getPvechapterId(){
        return PvechapterId;
    }


    /**
    * 任务奖励
    * pairarray reward
    * 
    */
    public List<IntPairType> getRewardPairList(){
        return rewardPairList;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_联盟领地表.xlsx", node="clan_building_resource.xml")
public class ClanBuildingResourceTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 建筑类型
    * CommonEnum.MapBuildingType type
    * 
    */
    @ResAttribute("type")
    private CommonEnum.MapBuildingType type;

    /**
    * 赛季剧本id
    * int storyId
    * 
    */
    @ResAttribute("storyId")
    private  int storyId;

    /**
    * 建筑id
    * int buildingId
    * 
    */
    @ResAttribute("buildingId")
    private  int buildingId;

    /**
    * 档位对应建筑个数下限
    * int numLower
    * 
    */
    @ResAttribute("numLower")
    private  int numLower;

    /**
    * 档位对应建筑个数上限
    * int numUpper
    * 
    */
    @ResAttribute("numUpper")
    private  int numUpper;

    /**
    * 建筑消耗资源
    * pairarray resources
    * 
    */
    @ResAttribute("resources")
    private List<IntPairType> resourcesPairList;

    /**
    * 建设需要的军团人数
    * int needMemberNums
    * 
    */
    @ResAttribute("needMemberNums")
    private  int needMemberNums;

    /**
    * 建设需要的军团战力
    * int needCombat
    * 
    */
    @ResAttribute("needCombat")
    private  int needCombat;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 建筑类型
    * CommonEnum.MapBuildingType type
    * 
    */
    public CommonEnum.MapBuildingType getType(){
        return type;
    }
    /**
    * 赛季剧本id
    * int storyId
    * 
    */
    public int getStoryId(){
        return storyId;
    }

    /**
    * 建筑id
    * int buildingId
    * 
    */
    public int getBuildingId(){
        return buildingId;
    }

    /**
    * 档位对应建筑个数下限
    * int numLower
    * 
    */
    public int getNumLower(){
        return numLower;
    }

    /**
    * 档位对应建筑个数上限
    * int numUpper
    * 
    */
    public int getNumUpper(){
        return numUpper;
    }


    /**
    * 建筑消耗资源
    * pairarray resources
    * 
    */
    public List<IntPairType> getResourcesPairList(){
        return resourcesPairList;
    }
    /**
    * 建设需要的军团人数
    * int needMemberNums
    * 
    */
    public int getNeedMemberNums(){
        return needMemberNums;
    }

    /**
    * 建设需要的军团战力
    * int needCombat
    * 
    */
    public int getNeedCombat(){
        return needCombat;
    }


}
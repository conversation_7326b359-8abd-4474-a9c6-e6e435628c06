package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="B_BOSS战场配置表.xlsx", node="boss_field_type.xml")
public class BossFieldTypeTemplate implements IResTemplate  {

    /**
    * boss战场类型
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 初始阶段
    * int initialStage
    * 
    */
    @ResAttribute("initialStage")
    private  int initialStage;

    /**
    * 结算成功事件
(对应boss_field_event表事件ID) 
    * int endSuccEvent
    * 
    */
    @ResAttribute("endSuccEvent")
    private  int endSuccEvent;

    /**
    * 结算失败事件
(对应boss_field_event表事件ID)
    * int endFailEvent
    * 
    */
    @ResAttribute("endFailEvent")
    private  int endFailEvent;

    /**
    * 订阅事件
(对应boss_field_event表事件ID)
    * intarray subscribeEvent
    * 
    */
    @ResAttribute("subscribeEvent")
    private List<Integer> subscribeEventList;

    /**
    * 成功奖励邮件
（rank_mail）
    * pairarray succMail
    * 
    */
    @ResAttribute("succMail")
    private List<IntPairType> succMailPairList;

    /**
    * 失败奖励邮件
    * int failMail
    * 
    */
    @ResAttribute("failMail")
    private  int failMail;

    /**
    * 空投上限
    * int armyLimit
    * 
    */
    @ResAttribute("armyLimit")
    private  int armyLimit;

    /**
    *  冷却时间/秒
    * int cdTime
    * 
    */
    @ResAttribute("cdTime")
    private  int cdTime;



    /**
    * boss战场类型
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 初始阶段
    * int initialStage
    * 
    */
    public int getInitialStage(){
        return initialStage;
    }

    /**
    * 结算成功事件
(对应boss_field_event表事件ID) 
    * int endSuccEvent
    * 
    */
    public int getEndSuccEvent(){
        return endSuccEvent;
    }

    /**
    * 结算失败事件
(对应boss_field_event表事件ID)
    * int endFailEvent
    * 
    */
    public int getEndFailEvent(){
        return endFailEvent;
    }


    /**
    * 订阅事件
(对应boss_field_event表事件ID)
    * intarray subscribeEvent
    * 
    */
    public List<Integer> getSubscribeEventList(){
        return subscribeEventList;
    }


    /**
    * 成功奖励邮件
（rank_mail）
    * pairarray succMail
    * 
    */
    public List<IntPairType> getSuccMailPairList(){
        return succMailPairList;
    }
    /**
    * 失败奖励邮件
    * int failMail
    * 
    */
    public int getFailMail(){
        return failMail;
    }

    /**
    * 空投上限
    * int armyLimit
    * 
    */
    public int getArmyLimit(){
        return armyLimit;
    }

    /**
    *  冷却时间/秒
    * int cdTime
    * 
    */
    public int getCdTime(){
        return cdTime;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_地图建筑配置表.xlsx", node="map_building_type.xml")
public class MapBuildingTypeTemplate implements IResTemplate  {

    /**
    * 序号
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 建筑类型
    * CommonEnum.MapBuildingType type
    * 
    */
    @ResAttribute("type")
    private CommonEnum.MapBuildingType type;



    /**
    * 序号
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 建筑类型
    * CommonEnum.MapBuildingType type
    * 
    */
    public CommonEnum.MapBuildingType getType(){
        return type;
    }

}
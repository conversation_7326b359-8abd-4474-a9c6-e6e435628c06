package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "W_王国管理.xlsx", node = "const_kingdom.xml", isConst = true)
public class ConstKingdomTemplate implements IResConstTemplate  {

    /**
     * 王国增益开启后的冷却时间，公用CD（单位：s）
     */
    private int kingdomBuffCD = 0;
    /**
     * 王国增益开启时，操作者需要花费的货币数量
     */
    private IntPairType kingdomBuffConsume;
    /**
     * 王国恩赐可发放数量的固定每周重置时间，格式：星期,时,分,秒
     */
    private List<Integer> kingdomGiftRefreshTime;
    /**
     * 可以任命更低级别的王国头衔级别（头衔级别通过kingdom_title表的rank字段查询）
     */
    private List<Integer> kingdomTitleAppoint;
    /**
     * 历任国王界面每页拉取的记录数量
     */
    private int kingHistoryPerPageNum = 0;
    /**
     * 国王邮件模板的ID
     */
    private int kingdomMailID = 0;
    /**
     * 任命正面头衔时的跑马灯ID，内容为"{0}【联盟缩写】{1}【玩家名字】已成为了{2}【头衔名字】"
     */
    private int kingdomMarqueePositiveID = 0;
    /**
     * 任命负面头衔时的跑马灯ID，内容为"{0}【联盟缩写】{1}【玩家名字】已被判为了{2}【头衔名字】"
     */
    private int kingdomMarqueeNegativeID = 0;
    /**
     * 正面头衔的级别列表
     */
    private List<Integer> positiveTitleRanks;
    /**
     * 王国技能【赋税】的资源采集速度对应的buffID
     */
    private int kingdomSkillTaxPara1 = 0;
    /**
     * 王国技能【赋税】的资源上缴比例数值，万分位（例：配置100，则实际为1%）
     */
    private int kingdomSkillTaxPara2 = 0;
    /**
     * 王国技能【赋税】的国王每种资源最大获取数值
     */
    private int kingdomSkillTaxPara3 = 0;
    /**
     * 国王开启增益后全服玩家收到的提醒邮件ID
     */
    private int kingdomBuffMailID = 0;
    /**
     * 国王开启技能后全服玩家收到的提醒邮件ID
     */
    private int kingdomSkillMailID = 0;
    /**
     * 国王赋税技能释放完成后的资源统计邮件ID
     */
    private int kingdomSkillTaxMailID = 0;
    /**
     * 国王赋税包括的资源类型ID
     */
    private List<Integer> kingdomSkillTaxResourceID;


    public int getKingdomBuffCD(){
        return this.kingdomBuffCD;
    }

    public IntPairType getKingdomBuffConsume(){
        return this.kingdomBuffConsume;
    }

    public List<Integer> getKingdomGiftRefreshTime(){
        return this.kingdomGiftRefreshTime;
    }

    public List<Integer> getKingdomTitleAppoint(){
        return this.kingdomTitleAppoint;
    }

    public int getKingHistoryPerPageNum(){
        return this.kingHistoryPerPageNum;
    }

    public int getKingdomMailID(){
        return this.kingdomMailID;
    }

    public int getKingdomMarqueePositiveID(){
        return this.kingdomMarqueePositiveID;
    }

    public int getKingdomMarqueeNegativeID(){
        return this.kingdomMarqueeNegativeID;
    }

    public List<Integer> getPositiveTitleRanks(){
        return this.positiveTitleRanks;
    }

    public int getKingdomSkillTaxPara1(){
        return this.kingdomSkillTaxPara1;
    }

    public int getKingdomSkillTaxPara2(){
        return this.kingdomSkillTaxPara2;
    }

    public int getKingdomSkillTaxPara3(){
        return this.kingdomSkillTaxPara3;
    }

    public int getKingdomBuffMailID(){
        return this.kingdomBuffMailID;
    }

    public int getKingdomSkillMailID(){
        return this.kingdomSkillMailID;
    }

    public int getKingdomSkillTaxMailID(){
        return this.kingdomSkillTaxMailID;
    }

    public List<Integer> getKingdomSkillTaxResourceID(){
        return this.kingdomSkillTaxResourceID;
    }

    @Override
    public int getId() {
        return 0;
    }
}

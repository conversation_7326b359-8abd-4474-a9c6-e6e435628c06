package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "W_王国管理.xlsx", node = "const_kingdom.xml", isConst = true)
public class ConstKingdomTemplate implements IResConstTemplate  {

    /**
     */
    private int kingdomBuffCD = 0;
    /**
     */
    private IntPairType kingdomBuffConsume;
    /**
     */
    private List<Integer> kingdomGiftRefreshTime;
    /**
     */
    private List<Integer> kingdomTitleAppoint;
    /**
     */
    private int kingHistoryPerPageNum = 0;
    /**
     */
    private int kingdomMailID = 0;
    /**
     */
    private int kingdomMarqueePositiveID = 0;
    /**
     */
    private int kingdomMarqueeNegativeID = 0;
    /**
     */
    private List<Integer> positiveTitleRanks;
    /**
     */
    private int kingdomSkillTaxPara1 = 0;
    /**
     */
    private int kingdomSkillTaxPara2 = 0;
    /**
     */
    private int kingdomSkillTaxPara3 = 0;
    /**
     */
    private int kingdomBuffMailID = 0;
    /**
     */
    private int kingdomSkillMailID = 0;
    /**
     */
    private int kingdomSkillTaxMailID = 0;
    /**
     */
    private List<Integer> kingdomSkillTaxResourceID;


    public int getKingdomBuffCD(){
        return this.kingdomBuffCD;
    }

    public IntPairType getKingdomBuffConsume(){
        return this.kingdomBuffConsume;
    }

    public List<Integer> getKingdomGiftRefreshTime(){
        return this.kingdomGiftRefreshTime;
    }

    public List<Integer> getKingdomTitleAppoint(){
        return this.kingdomTitleAppoint;
    }

    public int getKingHistoryPerPageNum(){
        return this.kingHistoryPerPageNum;
    }

    public int getKingdomMailID(){
        return this.kingdomMailID;
    }

    public int getKingdomMarqueePositiveID(){
        return this.kingdomMarqueePositiveID;
    }

    public int getKingdomMarqueeNegativeID(){
        return this.kingdomMarqueeNegativeID;
    }

    public List<Integer> getPositiveTitleRanks(){
        return this.positiveTitleRanks;
    }

    public int getKingdomSkillTaxPara1(){
        return this.kingdomSkillTaxPara1;
    }

    public int getKingdomSkillTaxPara2(){
        return this.kingdomSkillTaxPara2;
    }

    public int getKingdomSkillTaxPara3(){
        return this.kingdomSkillTaxPara3;
    }

    public int getKingdomBuffMailID(){
        return this.kingdomBuffMailID;
    }

    public int getKingdomSkillMailID(){
        return this.kingdomSkillMailID;
    }

    public int getKingdomSkillTaxMailID(){
        return this.kingdomSkillTaxMailID;
    }

    public List<Integer> getKingdomSkillTaxResourceID(){
        return this.kingdomSkillTaxResourceID;
    }

    @Override
    public int getId() {
        return 0;
    }
}

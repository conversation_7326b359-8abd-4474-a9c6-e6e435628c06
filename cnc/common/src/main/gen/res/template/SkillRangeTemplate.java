package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_技能表_范围.xlsx", node="skill_range.xml")
public class SkillRangeTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 形状类型
    * int type
    * 
    */
    @ResAttribute("type")
    private  int type;

    /**
    * 角度（宽度）
    * int angle
    * 
    */
    @ResAttribute("angle")
    private  int angle;

    /**
    * 半径（长度）
    * int radius
    * 
    */
    @ResAttribute("radius")
    private  int radius;

    /**
    * 内径（长度）
    * int innerDiameter
    * 
    */
    @ResAttribute("innerDiameter")
    private  int innerDiameter;

    /**
    * 数量限制
    * int limit
    * 
    */
    @ResAttribute("limit")
    private  int limit;

    /**
    * 目标排序
    * CommonEnum.SkillRangeSortType sortType
    * 
    */
    @ResAttribute("sortType")
    private CommonEnum.SkillRangeSortType sortType;

    /**
    * 技能偏移
    * intarray offset
    * 
    */
    @ResAttribute("offset")
    private List<Integer> offsetList;

    /**
    * 技能旋转
    * int revolve
    * 
    */
    @ResAttribute("revolve")
    private  int revolve;

    /**
    * 效果衰减
    * int reduce
    * 
    */
    @ResAttribute("reduce")
    private  int reduce;

    /**
    * 过滤掉带buff的目标
    * intarray filterbuff
    * 
    */
    @ResAttribute("filterbuff")
    private List<Integer> filterbuffList;

    /**
    * 手动施法类型
    * int manualType
    * 
    */
    @ResAttribute("manualType")
    private  int manualType;

    /**
    * 是否继承技能目标
    * int inheritTarget
    * 
    */
    @ResAttribute("inheritTarget")
    private  int inheritTarget;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 形状类型
    * int type
    * 
    */
    public int getType(){
        return type;
    }

    /**
    * 角度（宽度）
    * int angle
    * 
    */
    public int getAngle(){
        return angle;
    }

    /**
    * 半径（长度）
    * int radius
    * 
    */
    public int getRadius(){
        return radius;
    }

    /**
    * 内径（长度）
    * int innerDiameter
    * 
    */
    public int getInnerDiameter(){
        return innerDiameter;
    }

    /**
    * 数量限制
    * int limit
    * 
    */
    public int getLimit(){
        return limit;
    }


    /**
    * 目标排序
    * CommonEnum.SkillRangeSortType sortType
    * 
    */
    public CommonEnum.SkillRangeSortType getSortType(){
        return sortType;
    }

    /**
    * 技能偏移
    * intarray offset
    * 
    */
    public List<Integer> getOffsetList(){
        return offsetList;
    }

    /**
    * 技能旋转
    * int revolve
    * 
    */
    public int getRevolve(){
        return revolve;
    }

    /**
    * 效果衰减
    * int reduce
    * 
    */
    public int getReduce(){
        return reduce;
    }


    /**
    * 过滤掉带buff的目标
    * intarray filterbuff
    * 
    */
    public List<Integer> getFilterbuffList(){
        return filterbuffList;
    }

    /**
    * 手动施法类型
    * int manualType
    * 
    */
    public int getManualType(){
        return manualType;
    }

    /**
    * 是否继承技能目标
    * int inheritTarget
    * 
    */
    public int getInheritTarget(){
        return inheritTarget;
    }


}
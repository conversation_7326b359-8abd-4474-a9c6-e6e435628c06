package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="S_商城表.xlsx", node="discount_shop.xml")
public class DiscountShopTemplate implements IResTemplate  {

    /**
    * 商品id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 所属分组
    * int group
    * 
    */
    @ResAttribute("group")
    private  int group;

    /**
    * 道具id
    * int itemId
    * 
    */
    @ResAttribute("itemId")
    private  int itemId;

    /**
    * 道具数量
    * int itemNum
    * 
    */
    @ResAttribute("itemNum")
    private  int itemNum;

    /**
    * 优惠比例（百分比）
    * int discount
    * 
    */
    @ResAttribute("discount")
    private  int discount;

    /**
    * 刷新权重
    * int refreshWeight
    * 
    */
    @ResAttribute("refreshWeight")
    private  int refreshWeight;

    /**
    * 实际价格(货币_数值）
    * pair price
    * 
    */
    @ResAttribute("price")
    private IntPairType pricePair;

    /**
    * 排序权重
    * int sortingWeight
    * 
    */
    @ResAttribute("sortingWeight")
    private  int sortingWeight;

    /**
    * 上架时间
    * date startTime
    * 
    */
    @ResAttribute("startTime")
    private Date startTimeDt;
    /**
    * 下架时间
    * date offTime
    * 
    */
    @ResAttribute("offTime")
    private Date offTimeDt;
    /**
    * 主堡等级
    * pair cityLevel
    * 
    */
    @ResAttribute("cityLevel")
    private IntPairType cityLevelPair;



    /**
    * 商品id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 所属分组
    * int group
    * 
    */
    public int getGroup(){
        return group;
    }

    /**
    * 道具id
    * int itemId
    * 
    */
    public int getItemId(){
        return itemId;
    }

    /**
    * 道具数量
    * int itemNum
    * 
    */
    public int getItemNum(){
        return itemNum;
    }

    /**
    * 优惠比例（百分比）
    * int discount
    * 
    */
    public int getDiscount(){
        return discount;
    }

    /**
    * 刷新权重
    * int refreshWeight
    * 
    */
    public int getRefreshWeight(){
        return refreshWeight;
    }


    /**
    * 实际价格(货币_数值）
    * pair price
    * 
    */
    public IntPairType getPricePair(){
        return pricePair;
    }
    /**
    * 排序权重
    * int sortingWeight
    * 
    */
    public int getSortingWeight(){
        return sortingWeight;
    }


    /**
    * 上架时间
    * date startTime
    * 
    */
    public Date getStartTimeDt(){
        return startTimeDt;
    }

    /**
    * 下架时间
    * date offTime
    * 
    */
    public Date getOffTimeDt(){
        return offTimeDt;
    }

    /**
    * 主堡等级
    * pair cityLevel
    * 
    */
    public IntPairType getCityLevelPair(){
        return cityLevelPair;
    }

}
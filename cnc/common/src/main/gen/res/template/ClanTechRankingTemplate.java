package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_军团科技.xlsx", node="clan_tech_ranking.xml")
public class ClanTechRankingTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 排行类型
    * int type
    * 
    */
    @ResAttribute("type")
    private  int type;

    /**
    * 名次
    * pair rank
    * 
    */
    @ResAttribute("rank")
    private IntPairType rankPair;

    /**
    * 奖励ID
    * int rewardId
    * 
    */
    @ResAttribute("rewardId")
    private  int rewardId;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 排行类型
    * int type
    * 
    */
    public int getType(){
        return type;
    }


    /**
    * 名次
    * pair rank
    * 
    */
    public IntPairType getRankPair(){
        return rankPair;
    }
    /**
    * 奖励ID
    * int rewardId
    * 
    */
    public int getRewardId(){
        return rewardId;
    }


}
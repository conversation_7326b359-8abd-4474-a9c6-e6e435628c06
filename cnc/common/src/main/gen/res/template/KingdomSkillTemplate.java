package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="W_王国管理.xlsx", node="kingdom_skill.xml")
public class KingdomSkillTemplate implements IResTemplate  {

    /**
    * 王国技能ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 王国技能类型（1=主动，2=被动）
    * int type
    * 
    */
    @ResAttribute("type")
    private  int type;

    /**
    * 施放消耗资源数量
    * pair consume
    * 
    */
    @ResAttribute("consume")
    private IntPairType consumePair;

    /**
    * 技能生效时间（单位：秒）
    * int effectTime
    * 
    */
    @ResAttribute("effectTime")
    private  int effectTime;

    /**
    * 技能冷却时间（单位：秒）
    * int cd
    * 
    */
    @ResAttribute("cd")
    private  int cd;

    /**
    * 内城增益buff
    * intarray buff
    * 
    */
    @ResAttribute("buff")
    private List<Integer> buffList;

    /**
    * 战斗增益buff
    * intarray battlebuff
    * 
    */
    @ResAttribute("battlebuff")
    private List<Integer> battlebuffList;



    /**
    * 王国技能ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 王国技能类型（1=主动，2=被动）
    * int type
    * 
    */
    public int getType(){
        return type;
    }


    /**
    * 施放消耗资源数量
    * pair consume
    * 
    */
    public IntPairType getConsumePair(){
        return consumePair;
    }
    /**
    * 技能生效时间（单位：秒）
    * int effectTime
    * 
    */
    public int getEffectTime(){
        return effectTime;
    }

    /**
    * 技能冷却时间（单位：秒）
    * int cd
    * 
    */
    public int getCd(){
        return cd;
    }


    /**
    * 内城增益buff
    * intarray buff
    * 
    */
    public List<Integer> getBuffList(){
        return buffList;
    }


    /**
    * 战斗增益buff
    * intarray battlebuff
    * 
    */
    public List<Integer> getBattlebuffList(){
        return battlebuffList;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_支付.xlsx", node="trigger_goods.xml")
public class TriggerGoodsTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 礼包id
    * int goodsId
    * 
    */
    @ResAttribute("goodsId")
    private  int goodsId;

    /**
    * 触发id
    * int triggerId
    * 
    */
    @ResAttribute("triggerId")
    private  int triggerId;

    /**
    * 可触发次数
    * int limitTriggerTimes
    * 
    */
    @ResAttribute("limitTriggerTimes")
    private  int limitTriggerTimes;

    /**
    * 触发间隔
    * int triggerIntervalHours
    * 
    */
    @ResAttribute("triggerIntervalHours")
    private  int triggerIntervalHours;

    /**
    * 触发主堡等级下限
    * int minTriggerCentreLevel
    * 
    */
    @ResAttribute("minTriggerCentreLevel")
    private  int minTriggerCentreLevel;

    /**
    * 触发主堡等级上线
    * int maxTriggerCentreLevel
    * 
    */
    @ResAttribute("maxTriggerCentreLevel")
    private  int maxTriggerCentreLevel;

    /**
    * 触发礼包的持续销售时间
    * int lastingHours
    * 
    */
    @ResAttribute("lastingHours")
    private  int lastingHours;

    /**
    * 礼包的类型
    * int type
    * 
    */
    @ResAttribute("type")
    private  int type;

    /**
    * 礼包的级别（数字越大，礼包越屌）
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 礼包id
    * int goodsId
    * 
    */
    public int getGoodsId(){
        return goodsId;
    }

    /**
    * 触发id
    * int triggerId
    * 
    */
    public int getTriggerId(){
        return triggerId;
    }

    /**
    * 可触发次数
    * int limitTriggerTimes
    * 
    */
    public int getLimitTriggerTimes(){
        return limitTriggerTimes;
    }

    /**
    * 触发间隔
    * int triggerIntervalHours
    * 
    */
    public int getTriggerIntervalHours(){
        return triggerIntervalHours;
    }

    /**
    * 触发主堡等级下限
    * int minTriggerCentreLevel
    * 
    */
    public int getMinTriggerCentreLevel(){
        return minTriggerCentreLevel;
    }

    /**
    * 触发主堡等级上线
    * int maxTriggerCentreLevel
    * 
    */
    public int getMaxTriggerCentreLevel(){
        return maxTriggerCentreLevel;
    }

    /**
    * 触发礼包的持续销售时间
    * int lastingHours
    * 
    */
    public int getLastingHours(){
        return lastingHours;
    }

    /**
    * 礼包的类型
    * int type
    * 
    */
    public int getType(){
        return type;
    }

    /**
    * 礼包的级别（数字越大，礼包越屌）
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="P_跑马灯.xlsx", node="marquee_type.xml")
public class MarqueeTypeTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 消息枚举
    * CommonEnum.MarqueeType message
    * 
    */
    @ResAttribute("message")
    private CommonEnum.MarqueeType message;

    /**
    * 跑马灯id
    * int marqueeId
    * 
    */
    @ResAttribute("marqueeId")
    private  int marqueeId;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 消息枚举
    * CommonEnum.MarqueeType message
    * 
    */
    public CommonEnum.MarqueeType getMessage(){
        return message;
    }
    /**
    * 跑马灯id
    * int marqueeId
    * 
    */
    public int getMarqueeId(){
        return marqueeId;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_地图建筑配置表.xlsx", node="interaction_distance.xml")
public class InteractionDistanceTemplate implements IResTemplate  {

    /**
    * 场景id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 目标点类型
    * CommonEnum.TroopInteractionType aimType
    * 
    */
    @ResAttribute("aimType")
    private CommonEnum.TroopInteractionType aimType;

    /**
    * 交互距离（厘米）
    * int distanceOfAction
    * 
    */
    @ResAttribute("distanceOfAction")
    private  int distanceOfAction;



    /**
    * 场景id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 目标点类型
    * CommonEnum.TroopInteractionType aimType
    * 
    */
    public CommonEnum.TroopInteractionType getAimType(){
        return aimType;
    }
    /**
    * 交互距离（厘米）
    * int distanceOfAction
    * 
    */
    public int getDistanceOfAction(){
        return distanceOfAction;
    }


}
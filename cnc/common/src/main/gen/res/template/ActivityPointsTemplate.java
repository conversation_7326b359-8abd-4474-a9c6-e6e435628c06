package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动积分.xlsx", node="activity_points.xml")
public class ActivityPointsTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 获取积分途径
    * CommonEnum.ActivityPointsWay way
    * 
    */
    @ResAttribute("way")
    private CommonEnum.ActivityPointsWay way;

    /**
    * 参数1
    * string param1
    * 
    */
    @ResAttribute("param1")
    private  String param1;

    /**
    * 参数2
    * string param2
    * 
    */
    @ResAttribute("param2")
    private  String param2;

    /**
    * 获得积分
    * int points
    * 
    */
    @ResAttribute("points")
    private  int points;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 获取积分途径
    * CommonEnum.ActivityPointsWay way
    * 
    */
    public CommonEnum.ActivityPointsWay getWay(){
        return way;
    }
    /**
    * 参数1
    * string param1
    * 
    */
    public String getParam1(){
        return param1;
    }
    /**
    * 参数2
    * string param2
    * 
    */
    public String getParam2(){
        return param2;
    }
    /**
    * 获得积分
    * int points
    * 
    */
    public int getPoints(){
        return points;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_战斗增益.xlsx", node="battle_buff.xml")
public class BattleBuffTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * buff标签
    * intarray tag
    * 
    */
    @ResAttribute("tag")
    private List<Integer> tagList;

    /**
    * 效果类型
    * int type
    * 
    */
    @ResAttribute("type")
    private  int type;

    /**
    * 效果数值
    * int value
    * 
    */
    @ResAttribute("value")
    private  int value;

    /**
    * 添加effect
    * intarray addSkillEffect
    * 
    */
    @ResAttribute("addSkillEffect")
    private List<Integer> addSkillEffectList;

    /**
    * 是否叠加
    * bool merge
    * 
    */
    @ResAttribute("merge")
    private  boolean merge;

    /**
    * 叠加层数上限
    * int ruleValue
    * 
    */
    @ResAttribute("ruleValue")
    private  int ruleValue;

    /**
    * 本回合立刻生效
    * bool effectiveImmediately
    * 
    */
    @ResAttribute("effectiveImmediately")
    private  boolean effectiveImmediately;

    /**
    * BUFF分类
    * int effectClass
    * 
    */
    @ResAttribute("effectClass")
    private  int effectClass;

    /**
    * 优先级
    * int effectPriority
    * 
    */
    @ResAttribute("effectPriority")
    private  int effectPriority;

    /**
    * 所属BUFF效果组
    * int effectGroupId
    * 
    */
    @ResAttribute("effectGroupId")
    private  int effectGroupId;

    /**
    * 生效类型
    * int lifeCycleType
    * 
    */
    @ResAttribute("lifeCycleType")
    private  int lifeCycleType;

    /**
    * 持续时间
    * int lifeCycleValue
    * 
    */
    @ResAttribute("lifeCycleValue")
    private  int lifeCycleValue;

    /**
    * 脱战后不移除
    * int outFightRemove
    * 
    */
    @ResAttribute("outFightRemove")
    private  int outFightRemove;

    /**
    * 是否在战报中体现
    * bool warReport
    * 
    */
    @ResAttribute("warReport")
    private  boolean warReport;

    /**
    * BUFF来源
    * int source
    * 
    */
    @ResAttribute("source")
    private  int source;

    /**
    * 子buff
    * intarray subBuff
    * 
    */
    @ResAttribute("subBuff")
    private List<Integer> subBuffList;

    /**
    * BUFF是否展示
    * bool buffShow
    * 
    */
    @ResAttribute("buffShow")
    private  boolean buffShow;

    /**
    * 战斗协议
    * bool CombatAgreement
    * 
    */
    @ResAttribute("CombatAgreement")
    private  boolean CombatAgreement;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * buff标签
    * intarray tag
    * 
    */
    public List<Integer> getTagList(){
        return tagList;
    }

    /**
    * 效果类型
    * int type
    * 
    */
    public int getType(){
        return type;
    }

    /**
    * 效果数值
    * int value
    * 
    */
    public int getValue(){
        return value;
    }


    /**
    * 添加effect
    * intarray addSkillEffect
    * 
    */
    public List<Integer> getAddSkillEffectList(){
        return addSkillEffectList;
    }


    /**
    * 是否叠加
    * bool merge
    * 
    */
    public boolean getMerge(){
        return merge;
    }
    /**
    * 叠加层数上限
    * int ruleValue
    * 
    */
    public int getRuleValue(){
        return ruleValue;
    }


    /**
    * 本回合立刻生效
    * bool effectiveImmediately
    * 
    */
    public boolean getEffectiveImmediately(){
        return effectiveImmediately;
    }
    /**
    * BUFF分类
    * int effectClass
    * 
    */
    public int getEffectClass(){
        return effectClass;
    }

    /**
    * 优先级
    * int effectPriority
    * 
    */
    public int getEffectPriority(){
        return effectPriority;
    }

    /**
    * 所属BUFF效果组
    * int effectGroupId
    * 
    */
    public int getEffectGroupId(){
        return effectGroupId;
    }

    /**
    * 生效类型
    * int lifeCycleType
    * 
    */
    public int getLifeCycleType(){
        return lifeCycleType;
    }

    /**
    * 持续时间
    * int lifeCycleValue
    * 
    */
    public int getLifeCycleValue(){
        return lifeCycleValue;
    }

    /**
    * 脱战后不移除
    * int outFightRemove
    * 
    */
    public int getOutFightRemove(){
        return outFightRemove;
    }


    /**
    * 是否在战报中体现
    * bool warReport
    * 
    */
    public boolean getWarReport(){
        return warReport;
    }
    /**
    * BUFF来源
    * int source
    * 
    */
    public int getSource(){
        return source;
    }


    /**
    * 子buff
    * intarray subBuff
    * 
    */
    public List<Integer> getSubBuffList(){
        return subBuffList;
    }


    /**
    * BUFF是否展示
    * bool buffShow
    * 
    */
    public boolean getBuffShow(){
        return buffShow;
    }

    /**
    * 战斗协议
    * bool CombatAgreement
    * 
    */
    public boolean getCombatAgreement(){
        return CombatAgreement;
    }

}
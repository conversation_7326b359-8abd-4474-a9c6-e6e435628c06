package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_侦察.xlsx", node="spy_tech.xml")
public class SpyTechTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 侦查报告权限列表
    * intarray SpyContentType
    * 
    */
    @ResAttribute("SpyContentType")
    private List<Integer> SpyContentTypeList;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 侦查报告权限列表
    * intarray SpyContentType
    * 
    */
    public List<Integer> getSpyContentTypeList(){
        return SpyContentTypeList;
    }


}
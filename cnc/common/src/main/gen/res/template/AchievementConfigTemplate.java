package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_成就.xlsx", node="achievement_config.xml")
public class AchievementConfigTemplate implements IResTemplate  {

    /**
    * 成就id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 服务器成就类型
    * CommonEnum.AchievementType type
    * 
    */
    @ResAttribute("type")
    private CommonEnum.AchievementType type;

    /**
    * 剧本id
    * int storyId
    * 
    */
    @ResAttribute("storyId")
    private  int storyId;

    /**
    * 包含需求id
    * intarray demands
    * 
    */
    @ResAttribute("demands")
    private List<Integer> demandsList;

    /**
    * 奖励
    * pairarray reward
    * 
    */
    @ResAttribute("reward")
    private List<IntPairType> rewardPairList;

    /**
    * 成就档次
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;



    /**
    * 成就id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 服务器成就类型
    * CommonEnum.AchievementType type
    * 
    */
    public CommonEnum.AchievementType getType(){
        return type;
    }
    /**
    * 剧本id
    * int storyId
    * 
    */
    public int getStoryId(){
        return storyId;
    }


    /**
    * 包含需求id
    * intarray demands
    * 
    */
    public List<Integer> getDemandsList(){
        return demandsList;
    }


    /**
    * 奖励
    * pairarray reward
    * 
    */
    public List<IntPairType> getRewardPairList(){
        return rewardPairList;
    }
    /**
    * 成就档次
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }


}
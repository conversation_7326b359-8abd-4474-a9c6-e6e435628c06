package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="B_BOSS战场配置表.xlsx", node="boss_field_area.xml")
public class BossFieldAreaTemplate implements IResTemplate  {

    /**
    * 区域id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 区域类型
（1:圆形 2:三角形 3:矩形）
    * int type
    * 
    */
    @ResAttribute("type")
    private  int type;

    /**
    * 圆心
    * pair center
    * 
    */
    @ResAttribute("center")
    private IntPairType centerPair;

    /**
    * 半径
    * int radius
    * 
    */
    @ResAttribute("radius")
    private  int radius;

    /**
    * 点
(多边形的点list)
    * pairarray pointList
    * 
    */
    @ResAttribute("pointList")
    private List<IntPairType> pointListPairList;



    /**
    * 区域id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 区域类型
（1:圆形 2:三角形 3:矩形）
    * int type
    * 
    */
    public int getType(){
        return type;
    }


    /**
    * 圆心
    * pair center
    * 
    */
    public IntPairType getCenterPair(){
        return centerPair;
    }
    /**
    * 半径
    * int radius
    * 
    */
    public int getRadius(){
        return radius;
    }


    /**
    * 点
(多边形的点list)
    * pairarray pointList
    * 
    */
    public List<IntPairType> getPointListPairList(){
        return pointListPairList;
    }

}
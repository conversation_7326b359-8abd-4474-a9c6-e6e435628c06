package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动触发器.xlsx", node="act_trigger_list.xml")
public class ActTriggerListTemplate implements IResTemplate  {

    /**
    * 活动id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 触发器列表
    * intarray triggerList
    * 
    */
    @ResAttribute("triggerList")
    private List<Integer> triggerListList;



    /**
    * 活动id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 触发器列表
    * intarray triggerList
    * 
    */
    public List<Integer> getTriggerListList(){
        return triggerListList;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_节日活动.xlsx", node="festival_bp_pool.xml")
public class FestivalBpPoolTemplate implements IResTemplate  {

    /**
    * 通行证id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 类型
    * CommonEnum.FestivalBpType type
    * 
    */
    @ResAttribute("type")
    private CommonEnum.FestivalBpType type;

    /**
    * 金条数量(仅供金条购买类型选择)
    * int price
    * 
    */
    @ResAttribute("price")
    private  int price;

    /**
    * 礼包ID
    * int goodsId
    * 
    */
    @ResAttribute("goodsId")
    private  int goodsId;

    /**
    * 等级奖励（道具id_数量）
    * intarray levelReward
    * 
    */
    @ResAttribute("levelReward")
    private List<Integer> levelRewardList;



    /**
    * 通行证id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 类型
    * CommonEnum.FestivalBpType type
    * 
    */
    public CommonEnum.FestivalBpType getType(){
        return type;
    }
    /**
    * 金条数量(仅供金条购买类型选择)
    * int price
    * 
    */
    public int getPrice(){
        return price;
    }

    /**
    * 礼包ID
    * int goodsId
    * 
    */
    public int getGoodsId(){
        return goodsId;
    }


    /**
    * 等级奖励（道具id_数量）
    * intarray levelReward
    * 
    */
    public List<Integer> getLevelRewardList(){
        return levelRewardList;
    }


}
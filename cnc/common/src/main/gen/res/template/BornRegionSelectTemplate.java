package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_城池导量表.xlsx", node="born_region_select.xml")
public class BornRegionSelectTemplate implements IResTemplate  {

    /**
    * 阶段
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 人数范围-start
    * int rangeStart
    * 
    */
    @ResAttribute("rangeStart")
    private  int rangeStart;

    /**
    * 人数范围-end
    * int rangeEnd
    * 
    */
    @ResAttribute("rangeEnd")
    private  int rangeEnd;

    /**
    * 该阶段机型人数
    * triplearray typeNum
    * 
    */
    @ResAttribute("typeNum")
    private List<IntTripleType> typeNumTripleList;

    /**
    * 各州导量人数
    * pairarray playerNum
    * 
    */
    @ResAttribute("playerNum")
    private List<IntPairType> playerNumPairList;



    /**
    * 阶段
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 人数范围-start
    * int rangeStart
    * 
    */
    public int getRangeStart(){
        return rangeStart;
    }

    /**
    * 人数范围-end
    * int rangeEnd
    * 
    */
    public int getRangeEnd(){
        return rangeEnd;
    }


    /**
    * 该阶段机型人数
    * triplearray typeNum
    * 
    */
    public List<IntTripleType> getTypeNumTripleList(){
        return typeNumTripleList;
    }       

    /**
    * 各州导量人数
    * pairarray playerNum
    * 
    */
    public List<IntPairType> getPlayerNumPairList(){
        return playerNumPairList;
    }

}
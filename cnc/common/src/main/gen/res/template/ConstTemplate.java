package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "C_常量表.xlsx", node = "const.xml", isConst = true)
public class ConstTemplate implements IResConstTemplate  {

    /**
     * 
     */
    private int soldierTimeCost = 0;
    /**
     * 
     */
    private int soldierNum = 0;
    /**
     * 初始英雄（新手）
     */
    private List<Integer> InitialHero;
    /**
     * 初始士兵+数量
     */
    private List<IntPairType> InitialSolider;
    /**
     * 主城占地面积
     */
    private int baseSize = 0;
    /**
     * 内城格子数
     */
    private int baseLatticeNumber = 0;
    /**
     * 城外摄像机默认高度\n实际值=配置值-cameraHeightParam\ncameraHeightParam摄像机参数差
     */
    private int cameraHeightOutside = 0;
    /**
     * 内城摄像机默认高度\n实际值=配置值-cameraHeightParam\ncameraHeightParam摄像机参数差
     */
    private int cameraHeightInside = 0;
    /**
     * 
     */
    private int BattleTickMs = 0;
    /**
     * 指挥官技能关联部队
     */
    private int commanderSkillArmy = 0;
    /**
     * 里约危机杀敌积分
     */
    private List<IntPairType> enemyKillPoints;
    /**
     * 空运技能飞行速度系数/100=玩家行军速度的倍数
     */
    private int AirTransportSpeed = 0;
    /**
     * 里约危机准备阶段保护罩
     */
    private List<IntPairType> rioRedProtect;
    /**
     * 里约危机准备阶段保护罩
     */
    private List<IntPairType> rioBlueProtect;
    /**
     * 里约危机准备阶段保护罩
     */
    private List<Float> floatArrayTest;
    /**
     * 里约危机传送阵
     */
    private List<IntPairType> RioCrisisCampTransport;
    /**
     * 拖拽部队移动镜头时，相机滑动速度
     */
    private float cameraSpeed = 0.0f;
    /**
     * 自动补怪次数上限
     */
    private int autoAddMonsterLimit = 0;
    /**
     * 扣减自动补怪次数时间周期(秒)
     */
    private int reduceAddMonsterPeriod = 0;
    /**
     * 玩家初始化体力值
     */
    private int playerInitEnergy = 0;
    /**
     * 玩家自然回复的体力存储上限
     */
    private int playerEnergyRecoverMax = 0;
    /**
     * 玩家体力值的服务器存储上限
     */
    private int playerEnergyValueMax = 0;
    /**
     * 玩家体力回复间隔时间(秒)
     */
    private int playerEneryRecoverTimePeriod = 0;
    /**
     * 打开部队面板的点击音效
     */
    private String UIArmyHubOpen = "";     
    /**
     * 关闭非部队面板的音效
     */
    private String UIHubClose = "";     
    /**
     * 打开非部队面板的音效
     */
    private String UINotArmyHubOpen = "";     
    /**
     * 背景音乐BGM
     */
    private String BigWorldBGMTest = "";     
    /**
     * 初始化角色物品配置
     */
    private List<IntPairType> InitialItem;
    /**
     * 初始化角色资源配置
     */
    private List<IntPairType> InitialCurrency;
    /**
     * BOSS战场行军阵营
     */
    private int bossFieldArmyCamp = 0;
    /**
     * 运输机降落时间（秒）
     */
    private int landingTime = 0;
    /**
     * 运输机成功返航时间（秒）
     */
    private int returnTime = 0;
    /**
     * 集结时间（秒）
     */
    private String accumulationTime = "";     
    /**
     * 模型单位长度(cm)
     */
    private int standardModelDiameter = 0;
    /**
     * 阵型射程配置(cm)
     */
    private int standardRange = 0;
    /**
     * 选中圈调节参数(mm)
     */
    private int PickCircleAdjustment = 0;
    /**
     * 选中圈最小半径(mm)
     */
    private int MinPickCircle = 0;
    /**
     * 取消集结跑马灯id
     */
    private int cancelRallyMarqueeId = 0;
    /**
     * 我方对敌方基地发起集结跑马灯id
     */
    private int rallyToCityAttackerMarqueeId = 0;
    /**
     * 敌方对我方盟友发起集结跑马灯id
     */
    private int rallyToCityDefenderMarqueeId = 0;
    /**
     * 我方对敌方集结部队发起集结跑马灯id
     */
    private int rallyToArmyAttackerMarqueeId = 0;
    /**
     * 敌方对我方集结部队发起集结跑马灯id
     */
    private int rallyToArmyDefenderMarqueeId = 0;
    /**
     * 士兵移动的基本速度，对应动画播放速度为1
     */
    private int armyMoveNormalSpeed = 0;
    /**
     * 内城边长（米）
     */
    private int baseLength = 0;
    /**
     * errorCode通用tip的表现id (对应UI表现表)
     */
    private int errorTipShowId = 0;
    /**
     * 城建加速立即完成消耗（金条）
     */
    private int cityBuildFinishCost = 0;
    /**
     * 取消城建资源返还万分比
     */
    private int cityBuildCancleReturnRatio = 0;
    /**
     * 繁荣度区间（必须4段，3个区间：破损、稳定、繁荣）
     */
    private List<Integer> cityBuildProsperitySection;
    /**
     * 破城后扣除繁荣度万分比
     */
    private int battleFailDeductingProsperity = 0;
    /**
     * 立即完成建造所需道具
     */
    private int innerBuildCompleteNeedItem = 0;
    /**
     * 一键加速阀值（秒）
     */
    private int innerBuildSpeedUpThreshold = 0;
    /**
     * 一键加速溢出XX秒以上给予提示（秒）
     */
    private int innerBuildSpeedUpOverflow = 0;
    /**
     * 英雄天赋页数量
     */
    private int talentPageNumber = 0;
    /**
     * 医院伤兵第一次达到容量的80%时会发邮件给玩家告警
     */
    private int hospitalFirstWarningPer100 = 0;
    /**
     * 医院伤兵第一次达到容量的80%时的通知邮件id
     */
    private int hospitalFirstReach80PercentMailId = 0;
    /**
     * 医院容量不足导致重伤兵直接死亡的通知邮件id
     */
    private int hospitalDeadMailId = 0;
    /**
     * 援助历史记录上限条数
     */
    private int assistanceRecord = 0;
    /**
     * 天赋页命名长度上限（字符）
     */
    private int talentPageNameLimit = 0;
    /**
     * 初始英雄等级(废弃废弃！！！！)
     */
    private int initialHeroLevel = 0;
    /**
     * 初始英雄星阶
     */
    private int initialHeroStarLevel = 0;
    /**
     * 英雄技能解锁后的初始等级
     */
    private int initialHeroSkillLevel = 0;
    /**
     * 英雄默认解锁天赋页
     */
    private int initialHeroTalentPage = 0;
    /**
     * 州的最大数量
     */
    private int maxRegionNum = 0;
    /**
     * 我方对XXX联盟(xx，xx)据点名字发起集结  跑马灯id
     */
    private int rallyToBuildingAttackerMarqueeId = 0;
    /**
     * XXX联盟对我方(xx，xx)据点名字发起了集结进攻  跑马灯id
     */
    private int rallyToBuildingDefenderMarqueeId = 0;
    /**
     * 我方对XXX联盟(xx，xx)正在占领的据点名字发起集结  跑马灯id
     */
    private int rallyToBuildingOccupyAttackerMarqueeId = 0;
    /**
     * XXX联盟对我方(xx，xx)正在占领的据点名字发起了集结进攻  跑马灯id
     */
    private int rallyToBuildingOccupyDefenderMarqueeId = 0;
    /**
     * XXX联盟对(xx，xx)据点名字发起了集结进攻
     */
    private int rallyToBuildingMarqueeId = 0;
    /**
     * 坐标跳转通用tip的表现id (对应UI表现表)
     */
    private int MovePosTipShowId = 0;
    /**
     * 单人部队的阻挡半径(厘米)
     */
    private int NormalArmyBlockRadius = 0;
    /**
     * 集结部队的阻挡半径(厘米)
     */
    private int AssembleArmyBlockRadius = 0;
    /**
     * 指挥官名称字符下限
     */
    private int commanderNameMinLen = 0;
    /**
     * 指挥官名称字符上限
     */
    private int commanderNameMaxLen = 0;
    /**
     * 指挥官随机名字符串长度（字符）
     */
    private int commanderRandomNameLen = 0;
    /**
     * 定点迁城道具id
     */
    private int itemIdmoveCityNormal = 0;
    /**
     * 新手迁城道具id
     */
    private int itemIdmoveCitynewPlayer = 0;
    /**
     * 领土迁城道具id
     */
    private int itemIdmoveCityManor = 0;
    /**
     * boss战积分计算击杀系数
     */
    private int bossFieldDamageRatio = 0;
    /**
     * boss战积分计算损伤系数
     */
    private int bossFieldLossRatio = 0;
    /**
     * 里约危机时间控制，格式：活动预热开始时间1，活动开始时间1，活动结束时间1；使用时间戳
     */
    private List<Integer> rioCrisisTime;
    /**
     * 里约危机副本ID
     */
    private int rioCrisisID = 0;
    /**
     * 里约危机胜利奖励预览
     */
    private List<IntPairType> rioCrisisSuccessReward;
    /**
     * 里约危机参与奖励预览
     */
    private List<IntPairType> rioCrisisJoinReward;
    /**
     * 英雄升级经验超过所需值多少弹出二次弹窗提示
     */
    private int heroLevelUpTipsExp = 0;
    /**
     * 聊天一次请求多少条消息
     */
    private int ChatReqMessageNumber = 0;
    /**
     * 开服多久开放城市（分钟）
     */
    private int MapBuildingCityOpenTime = 0;
    /**
     * 王国聊天CD（秒）
     */
    private int ChatCD = 0;
    /**
     * 聊天最大字符
     */
    private int CharacterLength = 0;
    /**
     * idip邮件
     */
    private int IdipMail = 0;
    /**
     * 内城基地建筑类型id
     */
    private int buildingBaseId = 0;
    /**
     * 战力对比分段
     */
    private String battlePowerPart = "";     
    /**
     * 系统盟固定标识
     */
    private String SystemClanFixFlag = "";     
    /**
     * 邮件标题:战斗胜利
     */
    private String BattleRecord1 = "";     
    /**
     * 邮件标题:战斗失败
     */
    private String BattleRecord2 = "";     
    /**
     * 邮件标题:战争胜利
     */
    private String BattleRecord4 = "";     
    /**
     * 邮件标题:战争失败
     */
    private String BattleRecord5 = "";     
    /**
     * 邮件子标题:您的基地遭到了%s的攻击
     */
    private String BattleRecordSub1 = "";     
    /**
     * 邮件子标题:您的基地遭到了%s等人的攻击
     */
    private String BattleRecordSub2 = "";     
    /**
     * 邮件子标题:攻击了%s的地标
     */
    private String BattleRecordSub3 = "";     
    /**
     * 邮件子标题:与%s发生了战斗
     */
    private String BattleRecordSub4 = "";     
    /**
     * 邮件子标题:与%s等人发生了战斗
     */
    private String BattleRecordSub5 = "";     
    /**
     * 邮件子标题:%s遭到了[%s]%s攻击
     */
    private String BattleRecordSub6 = "";     
    /**
     * 邮件子标题:%s遭到了[%s]%s等人攻击
     */
    private String BattleRecordSub7 = "";     
    /**
     * 邮件子标题:攻击了[%s]的%s
     */
    private String BattleRecordSub8 = "";     
    /**
     * 邮件子标题:攻击了%s
     */
    private String BattleRecordSub9 = "";     
    /**
     * 队列任务免费加速次数
     */
    private int QueueTaskFreeSpeedCount = 0;
    /**
     * 队列免费加速时长（秒）
     */
    private int QueueTaskFreeSpeedTimeSec = 0;
    /**
     * 可以使用免费加速的队列类型（建筑，科研）
     */
    private List<Integer> FreeSpeedQueueType;
    /**
     * 邮件标题:战争报告
     */
    private String BattleRecord6 = "";     
    /**
     * 邮件标题:战斗报告
     */
    private String BattleRecord3 = "";     
    /**
     * 邮件子标题:与等级%s%s发生了战斗
     */
    private String BattleRecordSub10 = "";     
    /**
     * 邮件子标题:与等级%s%s等人发生了战斗
     */
    private String BattleRecordSub11 = "";     
    /**
     * 行军部队的迁城阻挡半径=模型圈半径+常量A。此处是常量A的大小
     */
    private int movecity_troop_constA = 0;
    /**
     * 行军部队之单人玩家部队的迁城阻挡半径值(厘米）
     */
    private int movecity_troop_single_const = 0;
    /**
     * 行军部队之集结玩家部队的迁城阻挡半径值(厘米）
     */
    private int movecity_troop_multi_const = 0;
    /**
     * 收取资源间隔时间（秒）
     */
    private int CollectResourcesTime = 0;
    /**
     * 用于服务器维护时给全员施加的护盾
     */
    private int ServerMaintenanceBuff = 0;
    /**
     * 新手创建后的护盾
     */
    private int NewPlayerProtectBuff = 0;
    /**
     * 新手破盾主堡等级
     */
    private int RemoveProtectTowerLevel = 0;
    /**
     * 进攻取消邮件
     */
    private int Attack_Cancel_Mail = 0;
    /**
     * 侦察报告（开盾版），发给侦察方
     */
    private int Reconnaissance_report = 0;
    /**
     * 侦察报告（开盾版），发给被侦察方
     */
    private int Defend_Against_Aeconnaissance = 0;
    /**
     * 超链接字体颜色
     */
    private String Hyperlinks_color = "";     
    /**
     * 内城矩形范围的左上角顶点坐标，数据需要-1，保证从0开始
     */
    private IntPairType InnerBuildStarPoint;
    /**
     * 内城矩形范围的右下角顶点坐标，数据需要-1，保证从0开始
     */
    private IntPairType InnerBuildEndPoint;
    /**
     * 自由城建无效点
     */
    private List<IntPairType> InvalidPoints;
    /**
     * 内城普通层摄像机最低高度
     */
    private float InnerBuildCameraLowest = 0.0f;
    /**
     * 内城普通层摄像机默认高度
     */
    private float InnerBuildCameraDefault = 0.0f;
    /**
     * 内城普通层摄像机最高高度
     */
    private float InnerBuildCameraHighest = 0.0f;
    /**
     * 内城至大世界普通层默认高度
     */
    private float WorldCameraDefault = 0.0f;
    /**
     * 内城摄像机移动速度系数
     */
    private float InnerBuildCameraMoveSpeed = 0.0f;
    /**
     * 内城摄像机移动范围系数（长，宽）
     */
    private IntPairType InnerBuildCameraRange;
    /**
     * 大世界-内城缩放时，围墙出现的比例
     */
    private float InnerBuildWallDisplayScale = 0.0f;
    /**
     * 城墙燃烧每分钟耐久扣除值
     */
    private int CityWallHpReduceSpeed = 0;
    /**
     * 燃烧状态持续时间（秒）
     */
    private int CityWallBurningDuration = 0;
    /**
     * 冒烟状态持续时间（秒）
     */
    private int CityWallSmokeDuration = 0;
    /**
     * 灭火消耗资源（金条）数量
     */
    private IntPairType CityWallOutfireCost;
    /**
     * 单次维修回复耐久
     */
    private int CityWallRepairHp = 0;
    /**
     * 维修CD时间（秒）
     */
    private int CityWallRepairCd = 0;
    /**
     * 英雄总览界面休闲动画播放间隔随机时间区间（秒）
     */
    private IntPairType HeroRelaxAnimInterval;
    /**
     * 国际包discord社区跳转链接
     */
    private String GlobalDiscordUrl = "";     
    /**
     * 英雄总览界面进入休息动画序列得准备时间
     */
    private int HeroWaitAnimInterval = 0;
    /**
     * 断线重连：网络连接不好时，重连时间间隔（单位S），时间需要大于1s
     */
    private int ReconnectInterval = 0;
    /**
     * 断线重连：最大连续重连次数，超过次数则会弹出错误提示，并提醒玩家重登（单位次）
     */
    private int MaxReconnectTimes = 0;
    /**
     * 请求消息之后，x秒之后如果没有回包，则开始播放转菊花
     */
    private int LoadingAnimPlayDelay = 0;
    /**
     * 初始接取的普通任务数
     */
    private int OrdinaryTaskInitNum = 0;
    /**
     * 玩家初始章节id
     */
    private int InitChapterId = 0;
    /**
     * 活跃度道具id
     */
    private int HeatValueItemId = 0;
    /**
     * 英雄通用技能特效表现
     */
    private String HeroSkillEffectPath = "";     
    /**
     * 攻城胜利OpenUI
     */
    private int victoryOpenUi = 0;
    /**
     * 联盟欢迎语字符串字数限制
     */
    private int welcomeallianceword = 0;
    /**
     * 个人发个人邮件id
     */
    private int personalMailId = 0;
    /**
     * 个人发个人邮件字数限制
     */
    private int personalMailWordLimit = 0;
    /**
     * 道具品质颜色这边的第一个代表品质1 第二个代表品质2 以此类推
     */
    private String ItemNameColor = "";     
    /**
     * 被联盟其他成员帮助后的跑马灯
     */
    private int cureHelpedByClanMembersMarquee = 0;
    /**
     * 初始解锁采集资源
     */
    private List<Integer> techUnlockRareEarthTiberiumDiamond;
    /**
     * 初始解锁T1四个兵种
     */
    private List<Integer> initiallyunlockclasses;
    /**
     * 取消科技研究返还资源比率
     */
    private int cancelresearch = 0;
    /**
     * 医院钻石秒治疗参数
     */
    private int HOSPITAL_FAST_TREAT_CURRENCY_FACTOR = 0;
    /**
     * 医院钻石秒治疗参数
     */
    private int HOSPITAL_FAST_TREAT_MILLIS_FACTOR = 0;
    /**
     * 
     */
    private int DropObjectEndShow = 0;
    /**
     * 运输机清除冷却道具ID
     */
    private int transportPlaneItem = 0;
    /**
     * 建筑界面上不需要显示的枚举ID
     */
    private List<Integer> InnerBuildEnumNotShow;
    /**
     * 主将能携带副将的最低星级
     */
    private int UnlockDeputyHeroStarLimit = 0;
    /**
     * 在医院中拥有专属容量的兵种id
     */
    private int hospitalExclusiveSoldierId = 0;
    /**
     * 医院中的专属容量
     */
    private int hospitalExclusiveSoldierCapacity = 0;
    /**
     * 医院中治疗的最小时间（秒）
     */
    private int hospitalTreatMinSeconds = 0;
    /**
     * 最大编队数量
     */
    private int formationNumLimit = 0;
    /**
     * 编队名字最大字符数
     */
    private int formationNameLengthLimit = 0;
    /**
     * 取消训练士兵返还的资源百分比
     */
    private int cancelArmyReturnResource = 0;
    /**
     * 创建部队界面需要显示的属性枚举
     */
    private List<Integer> CreatTroopShowAddition;
    /**
     * 建筑界面上不需要显示的增益枚举ID
     */
    private List<Integer> InnerBuildBuffEffectNotShow;
    /**
     * 野怪搜索范围半径（单位m）
     */
    private int MonsterSearchRange = 0;
    /**
     * 出征界面，镜头高度
     */
    private int BattleCameraHight = 0;
    /**
     * 连续击败消耗体力的叛军，每层减免的体力
     */
    private int continuityMonsterEnergy = 0;
    /**
     * 连续击败消耗体力的叛军，体力减免的最大层数
     */
    private int continuityMonsterEnergyMaxTimes = 0;
    /**
     * 邮件子标题:与%s发生了战争
     */
    private String BattleRecordSub12 = "";     
    /**
     * 邮件子标题:与%s等人发生了战争
     */
    private String BattleRecordSub13 = "";     
    /**
     * 邮件子标题:与等级%s%s发生了战争
     */
    private String BattleRecordSub14 = "";     
    /**
     * 邮件子标题:与等级%s%s等人发生了战争
     */
    private String BattleRecordSub15 = "";     
    /**
     * 战报用UI字色-名字绿色
     */
    private String BATTLE_LOG_COLOR_GREEN = "";     
    /**
     * 战报用UI字色-名字红色
     */
    private String BATTLE_LOG_COLOR_RED = "";     
    /**
     * 战报用UI字色-英雄等级
     */
    private String BATTLE_LOG_COLOR_HERO_LEVEL = "";     
    /**
     * 战报用UI字色-兵力数字
     */
    private String BATTLE_LOG_COLOR_SOLDIER_NUMBER = "";     
    /**
     * 战报用UI字色-技能数字
     */
    private String BATTLE_LOG_COLOR_YELLOW_NUMBER = "";     
    /**
     * 战报用UI字色-技能名字
     */
    private String BATTLE_LOG_COLOR_SKILL_NAME = "";     
    /**
     * 战报用UI字色-我方标签
     */
    private String BATTLE_LOG_COLOR_BLUE_TEAM_TAG = "";     
    /**
     * 战报用UI字色-敌方标签
     */
    private String BATTLE_LOG_COLOR_RED_TEAM_TAG = "";     
    /**
     * 战报用UI字色-伤害颜色
     */
    private String BATTLE_LOG_COLOR_DAMAGE_NUMBER = "";     
    /**
     * 获取改名卡道具id
     */
    private int ModifyNameItemId = 0;
    /**
     * 获取改名一次，消耗改名卡数量
     */
    private int ModifyNameItemNum = 0;
    /**
     * 指挥官昵称前缀多语言id
     */
    private int PlayerNamePrefix = 0;
    /**
     * 负载与石油资源比
     */
    private int loadVsOil = 0;
    /**
     * 负载与钢铁资源比
     */
    private int loadVsIron = 0;
    /**
     * 负载与稀土资源比
     */
    private int loadVsRare = 0;
    /**
     * 负载与泰矿资源比
     */
    private int loadVsTiberium = 0;
    /**
     * 负载与金条资源比
     */
    private int loadVsGold = 0;
    /**
     * 解锁石油资源等级
     */
    private int oilLockLevel = 0;
    /**
     * 解锁钢铁资源等级
     */
    private int steelLockLevel = 0;
    /**
     * 解锁稀土资源等级
     */
    private int rareLockLevel = 0;
    /**
     * 解锁泰矿资源等级
     */
    private int tiberiumLockLevel = 0;
    /**
     * 该ID的timeline仅自己能看到，其他玩家看不到
     */
    private List<Integer> timelineOnlySelfSee;
    /**
     * 玩家每日可以免费领取的体力值
     */
    private int dailyFreeEnergy = 0;
    /**
     * 英雄技能重置道具的ID
     */
    private int heroSkillResetItemId = 0;
    /**
     * 当没有体力恢复道具时，指挥官体力界面快速购买的体力道具ID
     */
    private int energyBuyItemId = 0;
    /**
     * 战报增益详情界面显示增益
     */
    private List<Integer> Battle_Report_Bufflist;
    /**
     * 英雄背景界面经验消耗转换成经验书
     */
    private int heroBioExpConvertItemId = 0;
    /**
     * 指挥官每日免费领取行动力的客户端外显道具ID
     */
    private int commanderEnergyEverydayFreeItemId = 0;
    /**
     * 一次性最大接取支线任务数
     */
    private int onceReceiveTaskNum = 0;
    /**
     * 体力返还邮件（体力没有达到上限）
     */
    private int energyRollBackMailId1 = 0;
    /**
     * 体力返还邮件（体力达到上限）
     */
    private int energyRollBackMailId2 = 0;
    /**
     * 部队镜头跟随时镜头高度
     */
    private int selectArmyDefaultHeight = 0;
    /**
     * 侦察机镜头跟随时镜头高度
     */
    private int selectPlaneDefaultHeight = 0;
    /**
     * 登录小贴士
     */
    private String loggingIn_tips = "";     
    /**
     * 无极缩放非内城区域相机最低高度
     */
    private int WorldCameraMinimumHeight = 0;
    /**
     * 迷雾跳转镜头高度参数
     */
    private int FogJumpHeight = 0;
    /**
     * 招募英雄时，只有橙色的英雄才会播放天空视频。（橙色是5，紫色是4）
     */
    private int RecruitHeroQuanlity = 0;
    /**
     * 指挥官界面头像框的缺省值图片表id
     */
    private int commanderAvatarFrameDefaultPictureId = 0;
    /**
     * 医院治疗消耗的资源，相当于训练资源的百分比
     */
    private int hospitalCostPercent = 0;
    /**
     * 公告图片缓存本地时间（秒）
     */
    private int AnnouncementCacheTime = 0;
    /**
     * 导量落堡地图边缘上下限(cm)
     */
    private IntPairType CityBornPositionLimit;
    /**
     * 导量落堡基地之间间隔最小距离(cm)
     */
    private int CityBornDistanceLimit = 0;
    /**
     * 个人发联盟邮件id
     */
    private int clanMailId = 0;
    /**
     * 写邮件标题字数限制（总长度）
     */
    private int MailTitleLimit = 0;
    /**
     * 写邮件内容字数限制
     */
    private int MailContentLimit = 0;
    /**
     * 屏蔽邮件上限
     */
    private int shieldMailLimit = 0;
    /**
     * 已发送邮件模板id
     */
    private int sendMailId = 0;
    /**
     * 战力变化时，图标飞起第一步的时长：单位毫秒
     */
    private int innerEffectPowerFly1 = 0;
    /**
     * 战力变化时，图标飞起第二步的时长：单位毫秒
     */
    private int innerEffectPowerFly2 = 0;
    /**
     * 城市守卫者玩法，每日机甲奖励材料的领取次数（每日重置）
     */
    private int monsterCityGuardEverydayRewardTimes = 0;
    /**
     * 天赋升级时，战斗力的增加点数
     */
    private int heroTalentLevelUpPower = 0;
    /**
     * 多个收件人标题字数限制
     */
    private int SendMailTitleLimit = 0;
    /**
     * 可选多个联系人数量上限
     */
    private int SendMailNumLimit = 0;
    /**
     * 军团邮件主堡等级限制
     */
    private int ClanMailLevelLimit = 0;
    /**
     * 城市守卫者，符文掉落数量，每个城池片ID存在的符文数量最多为10个
     */
    private int cityGuardRuneLimited = 0;
    /**
     * 机甲模型单位长度(cm)
     */
    private int standardMechaDiameter = 0;
    /**
     * 邮件标题可输入长度
     */
    private int MailTitleEnterLimit = 0;
    /**
     * 里程碑初始章节Id
     */
    private int mileStoneStarId = 0;
    /**
     * key:时间判断标准 value:开启时间(当天小时数超过key会使value变为第二天对应小时数)
     */
    private IntPairType mileStoneFuncUnlockTime;
    /**
     * 基地周围野怪刷新数量限制
     */
    private int LuohaMonsterLimit = 0;
    /**
     * 里程碑解锁领土建筑的大类型包含哪几种子类型（中圈超武包含哪几个地图建筑类型）
     */
    private String mileStonePlayUnlockConst1 = "";     
    /**
     * 里程碑解锁领土建筑的大类型包含哪几种子类型（内圈超武包含哪几个地图建筑类型）
     */
    private String mileStonePlayUnlockConst2 = "";     
    /**
     * 新手迁城可使用的州范围
     */
    private List<Integer> itemMoveCityNewPlayerRegion;
    /**
     * 大世界领土建筑（包括城市，关隘，据点）在无人占领时，铭牌文本前显示图标的图片ID
     */
    private int WorldBuildingNeutralIcon = 0;
    /**
     * 洛哈的试炼界面展示奖励
     */
    private List<Integer> LuohaEventRewardShow;
    /**
     * 主题活动界面展示奖励
     */
    private List<Integer> MainThemeEventRewardShow;
    /**
     * 里程碑界面领取奖励的基地等级要求
     */
    private int milestoneGetRewardLevel = 0;
    /**
     * 集结时间（秒）
     */
    private String ExpeditionAccumulationTime = "";     
    /**
     * 断线重连尝试次数达到N次时，将显示退出游戏按钮
     */
    private int reConnectTimesLimitted = 0;
    /**
     * 初始可获得英雄ID（首包英雄资源检测将跳过这些英雄）
     */
    private List<Integer> initialHeroID;
    /**
     * 次日送英雄英雄id
     */
    private int HeroComeId = 0;
    /**
     * 上阵排序等级系数
     */
    private int gradeCoefficient = 0;
    /**
     * 上阵排序技能等级总和系数
     */
    private int skillCoefficient = 0;
    /**
     * 上阵排序稀有度系数
     */
    private int rarityCoefficient = 0;
    /**
     * 觉醒排序值加成
     */
    private int awakeCoefficient = 0;
    /**
     * 最小编队名称长度
     */
    private int formationNumMinLimit = 0;
    /**
     * 邮件屏蔽上限
     */
    private int BlockMailLimit = 0;
    /**
     * 二级密码取消时间（天）
     */
    private int SecondaryPasswordCloseTime = 0;
    /**
     * 二级密码取消邮件
     */
    private int SecondaryPasswordCloseMail = 0;
    /**
     * 使用金条触发二级密码
     */
    private int SecondaryPasswordGoldLimit = 0;
    /**
     * 国旗功能：个人旗帜的旗杆模型ID
     */
    private int nationFlagPrivateResourceId = 0;
    /**
     * 国旗功能：军团旗帜的旗杆模型ID
     */
    private int nationFlagLeagueResourceId = 0;
    /**
     * 建筑默认普攻
     */
    private int BuildingDefaultAttack = 0;
    /**
     * 建筑默认普攻间隔
     */
    private List<Integer> BuildingDefaultAttackCD;
    /**
     * 领地线默认颜色
     */
    private String territoryLineColor = "";     
    /**
     * 可分享邮件类型（报告邮件分类）
     */
    private List<Integer> ShareMailType;
    /**
     * 部队无法回城时，强行溃败的间隔CD（秒）
     */
    private int TroopDefeatedCD = 0;
    /**
     * 活动页签优先级排序
     */
    private String eventTypeList = "";     
    /**
     * 默认基地皮肤
     */
    private int defaultBaseDress = 0;
    /**
     * 手动技能镜头高度
     */
    private int manualSkillLensHeight = 0;
    /**
     * 超武司令部-电网销毁后司令部释放技能
     */
    private int SuperWeaponLeaderSkill = 0;
    /**
     * 邮件子标题:受到了军团[%s]的%s攻击
     */
    private String BattleRecordSub16 = "";     
    /**
     * 并行追击攻击距离补偿（cm）
     */
    private int ParallelDistance = 0;
    /**
     * 采集-联盟贡献数额(万分比)
     */
    private int CollectResourcesContribute = 0;
    /**
     * 野怪首杀奖励提示UI持续时长
     */
    private int firstDefeatUIDuration = 0;
    /**
     * 新手loading视频时长
     */
    private int newGuideVideoTime = 0;
    /**
     * 每日网络测试ping值的测试（每日重置）
     */
    private int networkTestEverydayTimes = 0;
    /**
     * 核弹发射井所在的片ID
     */
    private List<Integer> nuclearWeaponPos;
    /**
     * 每天按时发送聊天ID的功能（配置说明：晚上23:30,发送ID12)
     */
    private List<IntPairType> everydayTimeChatMsgId;
    /**
     * 进入迁城界面时镜头默认高度
     */
    private int MoveCityCameraHight = 0;
    /**
     * 代表英雄总览，升星，技能，升星详情，技能详情的阴影max distance参数
     */
    private List<Float> HeroSystemSheetCameraConst;
    /**
     * 行军线短线上限（性能优化）
     */
    private int marchingLineLimit = 0;
    /**
     * 侦察机在简化模式公式中的系数
     */
    private float ReconnaissanceAircraftCoefficient = 0.0f;
    /**
     * 背包页签图标（资源，加速，增益，其他）
     */
    private List<IntPairType> BagUIIcon;
    /**
     * 合围状态部队移动速度
     */
    private int siegeMoveSpeed = 0;
    /**
     * 资源道具第二套图标（配置方法3D图片id-2D图片id）
     */
    private List<IntPairType> ResourceSecondIcon;
    /**
     * 英雄经验界面获取途径提示
     */
    private String HeroExpGetPath = "";     
    /**
     * EVA经验界面获取途径提示
     */
    private String EvaExpGetPath = "";     
    /**
     * 首个主线任务的ID
     */
    private int FirstMainTaskId = 0;
    /**
     * 野怪生成范围数量
     */
    private int MonsterGenerateRangeNum = 0;
    /**
     * 次日送英雄飞行器上显示英雄名key
     */
    private String AirplaneShowHeroComeId = "";     
    /**
     * 扩员道具记录上限值
     */
    private int ExpansionItemLimit = 0;
    /**
     * 代金券道具ID
     */
    private int voucherItemId = 0;
    /**
     * 搜索指挥官主堡显示（等级分段）
     */
    private List<IntPairType> searchCommanderLevelShow;
    /**
     * 搜索指挥官主堡显示（对应图片）
     */
    private List<IntPairType> searchCommanderCityShow;
    /**
     * 搜索指挥官字符下限
     */
    private int searchCommanderNameMin = 0;
    /**
     * 搜索指挥官字符上限
     */
    private int searchCommanderNameMax = 0;
    /**
     * 单次搜索条数
     */
    private int searchCommanderNum = 0;
    /**
     * 搜索指挥官打开等级
     */
    private int searchCommanderLevelLimit = 0;
    /**
     * 加速道具类型_判断优先级（用于计算一键加速）
     */
    private List<IntPairType> speedItemTypePriority;
    /**
     * 超过30分钟以上，加速所需时间自动补足5分钟（用于一键加速计算）
     */
    private int overflowTimeMax = 0;
    /**
     * 出征界面兵种排序，步兵
     */
    private List<Integer> infantryListInFormation;
    /**
     * 出征界面兵种排序，坦克
     */
    private List<Integer> tankListInFormation;
    /**
     * 出征界面兵种排序，火炮
     */
    private List<Integer> artilleryListInFormation;
    /**
     * 出征界面兵种排序，采集
     */
    private List<Integer> gatherListInFormation;
    /**
     * 首次绑定账户奖励
     */
    private int firstBindAccountAward = 0;
    /**
     * 出征界面机甲信息高亮色号
     */
    private String formationMechaColor = "";     
    /**
     * CG视频播放X秒后显示跳过按钮
     */
    private int skipCGTime = 0;
    /**
     * 远征场景迷雾中心点偏移
     */
    private int expeditionFogCenterOffset = 0;
    /**
     * 远征场景迷雾块边长
     */
    private int expeditionFogLength = 0;
    /**
     * 玩家离线推送（单位：小时）
     */
    private int LeavePushTime = 0;
    /**
     * UA收入上报系数
     */
    private float UACoefficient = 0.0f;
    /**
     * 双端评分多少星以下打开H5
     */
    private int storeRatingStarRating = 0;
    /**
     * 双端评分H5Url
     */
    private String storeRatingH5 = "";     
    /**
     * 首次登陆CG视频字幕ID
     */
    private int firstLoginSubtitle = 0;
    /**
     * 军团礼物黄金宝箱ID
     */
    private int clanGoldGiftId = 0;
    /**
     * 游戏内米大师曝光最低主堡等级
     */
    private int midasNotifyMainCityMinLevel = 0;
    /**
     * 游戏内米大师曝光最低充值金币数量
     */
    private int midasNotifyMinSaveAmt = 0;
    /**
     * 游戏内米大师曝光持续小时数
     */
    private int midasNotifyLastHour = 0;
    /**
     * 游戏内米大师再次糊脸间隔天数
     */
    private int midasNotifyRenotifyDay = 0;
    /**
     * 官网地址
     */
    private String midasaddress = "";     
    /**
     * 特殊的军团排行榜ID（此类排行榜有最低上榜分数需求，初始0分数据不能上榜）
     */
    private List<Integer> specialClanRankListId;
    /**
     * 远征场景迷雾右偏移
     */
    private List<IntPairType> expeditionFogRightOffset;
    /**
     * 远征冲刺界面展示奖励
     */
    private List<Integer> RushwarsEventRewardShow;
    /**
     * 内城建筑HUD按钮名称
     */
    /**
     * 登录糊脸礼包需要的主堡等级
     */
    private int loginfacetappinglevel = 0;
    /**
     * 登录糊脸礼包（1奇数首充，偶数双队列，0就换过来）
     */
    private int loginfacetapping = 0;
    /**
     * 凡是卡巴尔营地宝箱的奖励邮件，都需要在该字段添加邮件id
     */
    private List<Integer> GiftMailFromCabalBoss;
    /**
     * 机甲经验补偿道具ID
     */
    private int mechaExpItem = 0;
    /**
     * 机甲经验补偿道具的邮件id
     */
    private int mechaFixMail = 0;
    /**
     * 连锁礼包打折券
     */
    private int ChaingiftsActivityCoupon = 0;
    /**
     * 幸运大转盘二代幸运券购买途径
     */
    private List<Integer> luckyTurnTable;
    /**
     * 连锁礼包邮件
     */
    private int ContinuousGiftMail = 0;
    /**
     * 默认基地铭牌
     */
    private int defaultBaseNameplate = 0;
    /**
     * 连锁礼包打折券
     */
    private int ChaingiftsActivityCoupon2 = 0;
    /**
     * 远征-奖励最低领取时间间隔（秒）
     */
    private int ExpeditionMinCollectionTime = 0;
    /**
     * 远征-最大积累时间（秒）
     */
    private int ExpeditionMaxCollectionTime = 0;
    /**
     * 远征-提醒时间（秒）
     */
    private int ExpeditionRemindCollectionTime = 0;
    /**
     * KVK寨子默认等级
     */
    private int KVKDefaultLevelOfStockade = 0;
    /**
     * 默认解锁的技能id
     */
    private List<Integer> DefaultBattleSkill;
    /**
     * 玩家初始部队
     */
    private List<Integer> initialUnit;
    /**
     * 距离基地多远触发指南针
     */
    private int CompassCenterDistance = 0;
    /**
     * 英雄升星用的通用碎片（分别对应英雄品质HeroQuality的2，3，4，5）
     */
    private List<IntPairType> omniHeroShardItem;
    /**
     * 塔防玩法我方单位刷出时间间隔
     */
    private int UnitRefreshTime = 0;
    /**
     * 战斗公式伤害系数k1
     */
    private float DamageCoefficientK1 = 0.0f;
    /**
     * 战斗公式防御系数k2
     */
    private float DefenseCoefficientK2 = 0.0f;
    /**
     * 战斗公式兵力系数k3
     */
    private float TroopCoefficientK3 = 0.0f;


    public int getSoldierTimeCost(){
        return this.soldierTimeCost;
    }

    public int getSoldierNum(){
        return this.soldierNum;
    }

    public List<Integer> getInitialHero(){
        return this.InitialHero;
    }

    public List<IntPairType> getInitialSolider(){
        return this.InitialSolider;
    }

    public int getBaseSize(){
        return this.baseSize;
    }

    public int getBaseLatticeNumber(){
        return this.baseLatticeNumber;
    }

    public int getCameraHeightOutside(){
        return this.cameraHeightOutside;
    }

    public int getCameraHeightInside(){
        return this.cameraHeightInside;
    }

    public int getBattleTickMs(){
        return this.BattleTickMs;
    }

    public int getCommanderSkillArmy(){
        return this.commanderSkillArmy;
    }

    public List<IntPairType> getEnemyKillPoints(){
        return this.enemyKillPoints;
    }

    public int getAirTransportSpeed(){
        return this.AirTransportSpeed;
    }

    public List<IntPairType> getRioRedProtect(){
        return this.rioRedProtect;
    }

    public List<IntPairType> getRioBlueProtect(){
        return this.rioBlueProtect;
    }

    public List<Float> getFloatArrayTest(){
        return this.floatArrayTest;
    }

    public List<IntPairType> getRioCrisisCampTransport(){
        return this.RioCrisisCampTransport;
    }

    public float getCameraSpeed(){
        return this.cameraSpeed;
    }        

    public int getAutoAddMonsterLimit(){
        return this.autoAddMonsterLimit;
    }

    public int getReduceAddMonsterPeriod(){
        return this.reduceAddMonsterPeriod;
    }

    public int getPlayerInitEnergy(){
        return this.playerInitEnergy;
    }

    public int getPlayerEnergyRecoverMax(){
        return this.playerEnergyRecoverMax;
    }

    public int getPlayerEnergyValueMax(){
        return this.playerEnergyValueMax;
    }

    public int getPlayerEneryRecoverTimePeriod(){
        return this.playerEneryRecoverTimePeriod;
    }

    public String getUIArmyHubOpen(){
        return this.UIArmyHubOpen;
    }   

    public String getUIHubClose(){
        return this.UIHubClose;
    }   

    public String getUINotArmyHubOpen(){
        return this.UINotArmyHubOpen;
    }   

    public String getBigWorldBGMTest(){
        return this.BigWorldBGMTest;
    }   

    public List<IntPairType> getInitialItem(){
        return this.InitialItem;
    }

    public List<IntPairType> getInitialCurrency(){
        return this.InitialCurrency;
    }

    public int getBossFieldArmyCamp(){
        return this.bossFieldArmyCamp;
    }

    public int getLandingTime(){
        return this.landingTime;
    }

    public int getReturnTime(){
        return this.returnTime;
    }

    public String getAccumulationTime(){
        return this.accumulationTime;
    }   

    public int getStandardModelDiameter(){
        return this.standardModelDiameter;
    }

    public int getStandardRange(){
        return this.standardRange;
    }

    public int getPickCircleAdjustment(){
        return this.PickCircleAdjustment;
    }

    public int getMinPickCircle(){
        return this.MinPickCircle;
    }

    public int getCancelRallyMarqueeId(){
        return this.cancelRallyMarqueeId;
    }

    public int getRallyToCityAttackerMarqueeId(){
        return this.rallyToCityAttackerMarqueeId;
    }

    public int getRallyToCityDefenderMarqueeId(){
        return this.rallyToCityDefenderMarqueeId;
    }

    public int getRallyToArmyAttackerMarqueeId(){
        return this.rallyToArmyAttackerMarqueeId;
    }

    public int getRallyToArmyDefenderMarqueeId(){
        return this.rallyToArmyDefenderMarqueeId;
    }

    public int getArmyMoveNormalSpeed(){
        return this.armyMoveNormalSpeed;
    }

    public int getBaseLength(){
        return this.baseLength;
    }

    public int getErrorTipShowId(){
        return this.errorTipShowId;
    }

    public int getCityBuildFinishCost(){
        return this.cityBuildFinishCost;
    }

    public int getCityBuildCancleReturnRatio(){
        return this.cityBuildCancleReturnRatio;
    }

    public List<Integer> getCityBuildProsperitySection(){
        return this.cityBuildProsperitySection;
    }

    public int getBattleFailDeductingProsperity(){
        return this.battleFailDeductingProsperity;
    }

    public int getInnerBuildCompleteNeedItem(){
        return this.innerBuildCompleteNeedItem;
    }

    public int getInnerBuildSpeedUpThreshold(){
        return this.innerBuildSpeedUpThreshold;
    }

    public int getInnerBuildSpeedUpOverflow(){
        return this.innerBuildSpeedUpOverflow;
    }

    public int getTalentPageNumber(){
        return this.talentPageNumber;
    }

    public int getHospitalFirstWarningPer100(){
        return this.hospitalFirstWarningPer100;
    }

    public int getHospitalFirstReach80PercentMailId(){
        return this.hospitalFirstReach80PercentMailId;
    }

    public int getHospitalDeadMailId(){
        return this.hospitalDeadMailId;
    }

    public int getAssistanceRecord(){
        return this.assistanceRecord;
    }

    public int getTalentPageNameLimit(){
        return this.talentPageNameLimit;
    }

    public int getInitialHeroLevel(){
        return this.initialHeroLevel;
    }

    public int getInitialHeroStarLevel(){
        return this.initialHeroStarLevel;
    }

    public int getInitialHeroSkillLevel(){
        return this.initialHeroSkillLevel;
    }

    public int getInitialHeroTalentPage(){
        return this.initialHeroTalentPage;
    }

    public int getMaxRegionNum(){
        return this.maxRegionNum;
    }

    public int getRallyToBuildingAttackerMarqueeId(){
        return this.rallyToBuildingAttackerMarqueeId;
    }

    public int getRallyToBuildingDefenderMarqueeId(){
        return this.rallyToBuildingDefenderMarqueeId;
    }

    public int getRallyToBuildingOccupyAttackerMarqueeId(){
        return this.rallyToBuildingOccupyAttackerMarqueeId;
    }

    public int getRallyToBuildingOccupyDefenderMarqueeId(){
        return this.rallyToBuildingOccupyDefenderMarqueeId;
    }

    public int getRallyToBuildingMarqueeId(){
        return this.rallyToBuildingMarqueeId;
    }

    public int getMovePosTipShowId(){
        return this.MovePosTipShowId;
    }

    public int getNormalArmyBlockRadius(){
        return this.NormalArmyBlockRadius;
    }

    public int getAssembleArmyBlockRadius(){
        return this.AssembleArmyBlockRadius;
    }

    public int getCommanderNameMinLen(){
        return this.commanderNameMinLen;
    }

    public int getCommanderNameMaxLen(){
        return this.commanderNameMaxLen;
    }

    public int getCommanderRandomNameLen(){
        return this.commanderRandomNameLen;
    }

    public int getItemIdmoveCityNormal(){
        return this.itemIdmoveCityNormal;
    }

    public int getItemIdmoveCitynewPlayer(){
        return this.itemIdmoveCitynewPlayer;
    }

    public int getItemIdmoveCityManor(){
        return this.itemIdmoveCityManor;
    }

    public int getBossFieldDamageRatio(){
        return this.bossFieldDamageRatio;
    }

    public int getBossFieldLossRatio(){
        return this.bossFieldLossRatio;
    }

    public List<Integer> getRioCrisisTime(){
        return this.rioCrisisTime;
    }

    public int getRioCrisisID(){
        return this.rioCrisisID;
    }

    public List<IntPairType> getRioCrisisSuccessReward(){
        return this.rioCrisisSuccessReward;
    }

    public List<IntPairType> getRioCrisisJoinReward(){
        return this.rioCrisisJoinReward;
    }

    public int getHeroLevelUpTipsExp(){
        return this.heroLevelUpTipsExp;
    }

    public int getChatReqMessageNumber(){
        return this.ChatReqMessageNumber;
    }

    public int getMapBuildingCityOpenTime(){
        return this.MapBuildingCityOpenTime;
    }

    public int getChatCD(){
        return this.ChatCD;
    }

    public int getCharacterLength(){
        return this.CharacterLength;
    }

    public int getIdipMail(){
        return this.IdipMail;
    }

    public int getBuildingBaseId(){
        return this.buildingBaseId;
    }

    public String getBattlePowerPart(){
        return this.battlePowerPart;
    }   

    public String getSystemClanFixFlag(){
        return this.SystemClanFixFlag;
    }   

    public String getBattleRecord1(){
        return this.BattleRecord1;
    }   

    public String getBattleRecord2(){
        return this.BattleRecord2;
    }   

    public String getBattleRecord4(){
        return this.BattleRecord4;
    }   

    public String getBattleRecord5(){
        return this.BattleRecord5;
    }   

    public String getBattleRecordSub1(){
        return this.BattleRecordSub1;
    }   

    public String getBattleRecordSub2(){
        return this.BattleRecordSub2;
    }   

    public String getBattleRecordSub3(){
        return this.BattleRecordSub3;
    }   

    public String getBattleRecordSub4(){
        return this.BattleRecordSub4;
    }   

    public String getBattleRecordSub5(){
        return this.BattleRecordSub5;
    }   

    public String getBattleRecordSub6(){
        return this.BattleRecordSub6;
    }   

    public String getBattleRecordSub7(){
        return this.BattleRecordSub7;
    }   

    public String getBattleRecordSub8(){
        return this.BattleRecordSub8;
    }   

    public String getBattleRecordSub9(){
        return this.BattleRecordSub9;
    }   

    public int getQueueTaskFreeSpeedCount(){
        return this.QueueTaskFreeSpeedCount;
    }

    public int getQueueTaskFreeSpeedTimeSec(){
        return this.QueueTaskFreeSpeedTimeSec;
    }

    public List<Integer> getFreeSpeedQueueType(){
        return this.FreeSpeedQueueType;
    }

    public String getBattleRecord6(){
        return this.BattleRecord6;
    }   

    public String getBattleRecord3(){
        return this.BattleRecord3;
    }   

    public String getBattleRecordSub10(){
        return this.BattleRecordSub10;
    }   

    public String getBattleRecordSub11(){
        return this.BattleRecordSub11;
    }   

    public int getMovecityTroopConsta(){
        return this.movecity_troop_constA;
    }

    public int getMovecityTroopSingleConst(){
        return this.movecity_troop_single_const;
    }

    public int getMovecityTroopMultiConst(){
        return this.movecity_troop_multi_const;
    }

    public int getCollectResourcesTime(){
        return this.CollectResourcesTime;
    }

    public int getServerMaintenanceBuff(){
        return this.ServerMaintenanceBuff;
    }

    public int getNewPlayerProtectBuff(){
        return this.NewPlayerProtectBuff;
    }

    public int getRemoveProtectTowerLevel(){
        return this.RemoveProtectTowerLevel;
    }

    public int getAttackCancelMail(){
        return this.Attack_Cancel_Mail;
    }

    public int getReconnaissanceReport(){
        return this.Reconnaissance_report;
    }

    public int getDefendAgainstAeconnaissance(){
        return this.Defend_Against_Aeconnaissance;
    }

    public String getHyperlinksColor(){
        return this.Hyperlinks_color;
    }   

    public IntPairType getInnerBuildStarPoint(){
        return this.InnerBuildStarPoint;
    }

    public IntPairType getInnerBuildEndPoint(){
        return this.InnerBuildEndPoint;
    }

    public List<IntPairType> getInvalidPoints(){
        return this.InvalidPoints;
    }

    public float getInnerBuildCameraLowest(){
        return this.InnerBuildCameraLowest;
    }        

    public float getInnerBuildCameraDefault(){
        return this.InnerBuildCameraDefault;
    }        

    public float getInnerBuildCameraHighest(){
        return this.InnerBuildCameraHighest;
    }        

    public float getWorldCameraDefault(){
        return this.WorldCameraDefault;
    }        

    public float getInnerBuildCameraMoveSpeed(){
        return this.InnerBuildCameraMoveSpeed;
    }        

    public IntPairType getInnerBuildCameraRange(){
        return this.InnerBuildCameraRange;
    }

    public float getInnerBuildWallDisplayScale(){
        return this.InnerBuildWallDisplayScale;
    }        

    public int getCityWallHpReduceSpeed(){
        return this.CityWallHpReduceSpeed;
    }

    public int getCityWallBurningDuration(){
        return this.CityWallBurningDuration;
    }

    public int getCityWallSmokeDuration(){
        return this.CityWallSmokeDuration;
    }

    public IntPairType getCityWallOutfireCost(){
        return this.CityWallOutfireCost;
    }

    public int getCityWallRepairHp(){
        return this.CityWallRepairHp;
    }

    public int getCityWallRepairCd(){
        return this.CityWallRepairCd;
    }

    public IntPairType getHeroRelaxAnimInterval(){
        return this.HeroRelaxAnimInterval;
    }

    public String getGlobalDiscordUrl(){
        return this.GlobalDiscordUrl;
    }   

    public int getHeroWaitAnimInterval(){
        return this.HeroWaitAnimInterval;
    }

    public int getReconnectInterval(){
        return this.ReconnectInterval;
    }

    public int getMaxReconnectTimes(){
        return this.MaxReconnectTimes;
    }

    public int getLoadingAnimPlayDelay(){
        return this.LoadingAnimPlayDelay;
    }

    public int getOrdinaryTaskInitNum(){
        return this.OrdinaryTaskInitNum;
    }

    public int getInitChapterId(){
        return this.InitChapterId;
    }

    public int getHeatValueItemId(){
        return this.HeatValueItemId;
    }

    public String getHeroSkillEffectPath(){
        return this.HeroSkillEffectPath;
    }   

    public int getVictoryOpenUi(){
        return this.victoryOpenUi;
    }

    public int getWelcomeallianceword(){
        return this.welcomeallianceword;
    }

    public int getPersonalMailId(){
        return this.personalMailId;
    }

    public int getPersonalMailWordLimit(){
        return this.personalMailWordLimit;
    }

    public String getItemNameColor(){
        return this.ItemNameColor;
    }   

    public int getCureHelpedByClanMembersMarquee(){
        return this.cureHelpedByClanMembersMarquee;
    }

    public List<Integer> getTechUnlockRareEarthTiberiumDiamond(){
        return this.techUnlockRareEarthTiberiumDiamond;
    }

    public List<Integer> getInitiallyunlockclasses(){
        return this.initiallyunlockclasses;
    }

    public int getCancelresearch(){
        return this.cancelresearch;
    }

    public int getHospitalFastTreatCurrencyFactor(){
        return this.HOSPITAL_FAST_TREAT_CURRENCY_FACTOR;
    }

    public int getHospitalFastTreatMillisFactor(){
        return this.HOSPITAL_FAST_TREAT_MILLIS_FACTOR;
    }

    public int getDropObjectEndShow(){
        return this.DropObjectEndShow;
    }

    public int getTransportPlaneItem(){
        return this.transportPlaneItem;
    }

    public List<Integer> getInnerBuildEnumNotShow(){
        return this.InnerBuildEnumNotShow;
    }

    public int getUnlockDeputyHeroStarLimit(){
        return this.UnlockDeputyHeroStarLimit;
    }

    public int getHospitalExclusiveSoldierId(){
        return this.hospitalExclusiveSoldierId;
    }

    public int getHospitalExclusiveSoldierCapacity(){
        return this.hospitalExclusiveSoldierCapacity;
    }

    public int getHospitalTreatMinSeconds(){
        return this.hospitalTreatMinSeconds;
    }

    public int getFormationNumLimit(){
        return this.formationNumLimit;
    }

    public int getFormationNameLengthLimit(){
        return this.formationNameLengthLimit;
    }

    public int getCancelArmyReturnResource(){
        return this.cancelArmyReturnResource;
    }

    public List<Integer> getCreatTroopShowAddition(){
        return this.CreatTroopShowAddition;
    }

    public List<Integer> getInnerBuildBuffEffectNotShow(){
        return this.InnerBuildBuffEffectNotShow;
    }

    public int getMonsterSearchRange(){
        return this.MonsterSearchRange;
    }

    public int getBattleCameraHight(){
        return this.BattleCameraHight;
    }

    public int getContinuityMonsterEnergy(){
        return this.continuityMonsterEnergy;
    }

    public int getContinuityMonsterEnergyMaxTimes(){
        return this.continuityMonsterEnergyMaxTimes;
    }

    public String getBattleRecordSub12(){
        return this.BattleRecordSub12;
    }   

    public String getBattleRecordSub13(){
        return this.BattleRecordSub13;
    }   

    public String getBattleRecordSub14(){
        return this.BattleRecordSub14;
    }   

    public String getBattleRecordSub15(){
        return this.BattleRecordSub15;
    }   

    public String getBattleLogColorGreen(){
        return this.BATTLE_LOG_COLOR_GREEN;
    }   

    public String getBattleLogColorRed(){
        return this.BATTLE_LOG_COLOR_RED;
    }   

    public String getBattleLogColorHeroLevel(){
        return this.BATTLE_LOG_COLOR_HERO_LEVEL;
    }   

    public String getBattleLogColorSoldierNumber(){
        return this.BATTLE_LOG_COLOR_SOLDIER_NUMBER;
    }   

    public String getBattleLogColorYellowNumber(){
        return this.BATTLE_LOG_COLOR_YELLOW_NUMBER;
    }   

    public String getBattleLogColorSkillName(){
        return this.BATTLE_LOG_COLOR_SKILL_NAME;
    }   

    public String getBattleLogColorBlueTeamTag(){
        return this.BATTLE_LOG_COLOR_BLUE_TEAM_TAG;
    }   

    public String getBattleLogColorRedTeamTag(){
        return this.BATTLE_LOG_COLOR_RED_TEAM_TAG;
    }   

    public String getBattleLogColorDamageNumber(){
        return this.BATTLE_LOG_COLOR_DAMAGE_NUMBER;
    }   

    public int getModifyNameItemId(){
        return this.ModifyNameItemId;
    }

    public int getModifyNameItemNum(){
        return this.ModifyNameItemNum;
    }

    public int getPlayerNamePrefix(){
        return this.PlayerNamePrefix;
    }

    public int getLoadVsOil(){
        return this.loadVsOil;
    }

    public int getLoadVsIron(){
        return this.loadVsIron;
    }

    public int getLoadVsRare(){
        return this.loadVsRare;
    }

    public int getLoadVsTiberium(){
        return this.loadVsTiberium;
    }

    public int getLoadVsGold(){
        return this.loadVsGold;
    }

    public int getOilLockLevel(){
        return this.oilLockLevel;
    }

    public int getSteelLockLevel(){
        return this.steelLockLevel;
    }

    public int getRareLockLevel(){
        return this.rareLockLevel;
    }

    public int getTiberiumLockLevel(){
        return this.tiberiumLockLevel;
    }

    public List<Integer> getTimelineOnlySelfSee(){
        return this.timelineOnlySelfSee;
    }

    public int getDailyFreeEnergy(){
        return this.dailyFreeEnergy;
    }

    public int getHeroSkillResetItemId(){
        return this.heroSkillResetItemId;
    }

    public int getEnergyBuyItemId(){
        return this.energyBuyItemId;
    }

    public List<Integer> getBattleReportBufflist(){
        return this.Battle_Report_Bufflist;
    }

    public int getHeroBioExpConvertItemId(){
        return this.heroBioExpConvertItemId;
    }

    public int getCommanderEnergyEverydayFreeItemId(){
        return this.commanderEnergyEverydayFreeItemId;
    }

    public int getOnceReceiveTaskNum(){
        return this.onceReceiveTaskNum;
    }

    public int getEnergyRollBackMailId1(){
        return this.energyRollBackMailId1;
    }

    public int getEnergyRollBackMailId2(){
        return this.energyRollBackMailId2;
    }

    public int getSelectArmyDefaultHeight(){
        return this.selectArmyDefaultHeight;
    }

    public int getSelectPlaneDefaultHeight(){
        return this.selectPlaneDefaultHeight;
    }

    public String getLogginginTips(){
        return this.loggingIn_tips;
    }   

    public int getWorldCameraMinimumHeight(){
        return this.WorldCameraMinimumHeight;
    }

    public int getFogJumpHeight(){
        return this.FogJumpHeight;
    }

    public int getRecruitHeroQuanlity(){
        return this.RecruitHeroQuanlity;
    }

    public int getCommanderAvatarFrameDefaultPictureId(){
        return this.commanderAvatarFrameDefaultPictureId;
    }

    public int getHospitalCostPercent(){
        return this.hospitalCostPercent;
    }

    public int getAnnouncementCacheTime(){
        return this.AnnouncementCacheTime;
    }

    public IntPairType getCityBornPositionLimit(){
        return this.CityBornPositionLimit;
    }

    public int getCityBornDistanceLimit(){
        return this.CityBornDistanceLimit;
    }

    public int getClanMailId(){
        return this.clanMailId;
    }

    public int getMailTitleLimit(){
        return this.MailTitleLimit;
    }

    public int getMailContentLimit(){
        return this.MailContentLimit;
    }

    public int getShieldMailLimit(){
        return this.shieldMailLimit;
    }

    public int getSendMailId(){
        return this.sendMailId;
    }

    public int getInnerEffectPowerFly1(){
        return this.innerEffectPowerFly1;
    }

    public int getInnerEffectPowerFly2(){
        return this.innerEffectPowerFly2;
    }

    public int getMonsterCityGuardEverydayRewardTimes(){
        return this.monsterCityGuardEverydayRewardTimes;
    }

    public int getHeroTalentLevelUpPower(){
        return this.heroTalentLevelUpPower;
    }

    public int getSendMailTitleLimit(){
        return this.SendMailTitleLimit;
    }

    public int getSendMailNumLimit(){
        return this.SendMailNumLimit;
    }

    public int getClanMailLevelLimit(){
        return this.ClanMailLevelLimit;
    }

    public int getCityGuardRuneLimited(){
        return this.cityGuardRuneLimited;
    }

    public int getStandardMechaDiameter(){
        return this.standardMechaDiameter;
    }

    public int getMailTitleEnterLimit(){
        return this.MailTitleEnterLimit;
    }

    public int getMileStoneStarId(){
        return this.mileStoneStarId;
    }

    public IntPairType getMileStoneFuncUnlockTime(){
        return this.mileStoneFuncUnlockTime;
    }

    public int getLuohaMonsterLimit(){
        return this.LuohaMonsterLimit;
    }

    public String getMileStonePlayUnlockConst1(){
        return this.mileStonePlayUnlockConst1;
    }   

    public String getMileStonePlayUnlockConst2(){
        return this.mileStonePlayUnlockConst2;
    }   

    public List<Integer> getItemMoveCityNewPlayerRegion(){
        return this.itemMoveCityNewPlayerRegion;
    }

    public int getWorldBuildingNeutralIcon(){
        return this.WorldBuildingNeutralIcon;
    }

    public List<Integer> getLuohaEventRewardShow(){
        return this.LuohaEventRewardShow;
    }

    public List<Integer> getMainThemeEventRewardShow(){
        return this.MainThemeEventRewardShow;
    }

    public int getMilestoneGetRewardLevel(){
        return this.milestoneGetRewardLevel;
    }

    public String getExpeditionAccumulationTime(){
        return this.ExpeditionAccumulationTime;
    }   

    public int getReConnectTimesLimitted(){
        return this.reConnectTimesLimitted;
    }

    public List<Integer> getInitialHeroID(){
        return this.initialHeroID;
    }

    public int getHeroComeId(){
        return this.HeroComeId;
    }

    public int getGradeCoefficient(){
        return this.gradeCoefficient;
    }

    public int getSkillCoefficient(){
        return this.skillCoefficient;
    }

    public int getRarityCoefficient(){
        return this.rarityCoefficient;
    }

    public int getAwakeCoefficient(){
        return this.awakeCoefficient;
    }

    public int getFormationNumMinLimit(){
        return this.formationNumMinLimit;
    }

    public int getBlockMailLimit(){
        return this.BlockMailLimit;
    }

    public int getSecondaryPasswordCloseTime(){
        return this.SecondaryPasswordCloseTime;
    }

    public int getSecondaryPasswordCloseMail(){
        return this.SecondaryPasswordCloseMail;
    }

    public int getSecondaryPasswordGoldLimit(){
        return this.SecondaryPasswordGoldLimit;
    }

    public int getNationFlagPrivateResourceId(){
        return this.nationFlagPrivateResourceId;
    }

    public int getNationFlagLeagueResourceId(){
        return this.nationFlagLeagueResourceId;
    }

    public int getBuildingDefaultAttack(){
        return this.BuildingDefaultAttack;
    }

    public List<Integer> getBuildingDefaultAttackCD(){
        return this.BuildingDefaultAttackCD;
    }

    public String getTerritoryLineColor(){
        return this.territoryLineColor;
    }   

    public List<Integer> getShareMailType(){
        return this.ShareMailType;
    }

    public int getTroopDefeatedCD(){
        return this.TroopDefeatedCD;
    }

    public String getEventTypeList(){
        return this.eventTypeList;
    }   

    public int getDefaultBaseDress(){
        return this.defaultBaseDress;
    }

    public int getManualSkillLensHeight(){
        return this.manualSkillLensHeight;
    }

    public int getSuperWeaponLeaderSkill(){
        return this.SuperWeaponLeaderSkill;
    }

    public String getBattleRecordSub16(){
        return this.BattleRecordSub16;
    }   

    public int getParallelDistance(){
        return this.ParallelDistance;
    }

    public int getCollectResourcesContribute(){
        return this.CollectResourcesContribute;
    }

    public int getFirstDefeatUIDuration(){
        return this.firstDefeatUIDuration;
    }

    public int getNewGuideVideoTime(){
        return this.newGuideVideoTime;
    }

    public int getNetworkTestEverydayTimes(){
        return this.networkTestEverydayTimes;
    }

    public List<Integer> getNuclearWeaponPos(){
        return this.nuclearWeaponPos;
    }

    public List<IntPairType> getEverydayTimeChatMsgId(){
        return this.everydayTimeChatMsgId;
    }

    public int getMoveCityCameraHight(){
        return this.MoveCityCameraHight;
    }

    public List<Float> getHeroSystemSheetCameraConst(){
        return this.HeroSystemSheetCameraConst;
    }

    public int getMarchingLineLimit(){
        return this.marchingLineLimit;
    }

    public float getReconnaissanceAircraftCoefficient(){
        return this.ReconnaissanceAircraftCoefficient;
    }        

    public List<IntPairType> getBagUIIcon(){
        return this.BagUIIcon;
    }

    public int getSiegeMoveSpeed(){
        return this.siegeMoveSpeed;
    }

    public List<IntPairType> getResourceSecondIcon(){
        return this.ResourceSecondIcon;
    }

    public String getHeroExpGetPath(){
        return this.HeroExpGetPath;
    }   

    public String getEvaExpGetPath(){
        return this.EvaExpGetPath;
    }   

    public int getFirstMainTaskId(){
        return this.FirstMainTaskId;
    }

    public int getMonsterGenerateRangeNum(){
        return this.MonsterGenerateRangeNum;
    }

    public String getAirplaneShowHeroComeId(){
        return this.AirplaneShowHeroComeId;
    }   

    public int getExpansionItemLimit(){
        return this.ExpansionItemLimit;
    }

    public int getVoucherItemId(){
        return this.voucherItemId;
    }

    public List<IntPairType> getSearchCommanderLevelShow(){
        return this.searchCommanderLevelShow;
    }

    public List<IntPairType> getSearchCommanderCityShow(){
        return this.searchCommanderCityShow;
    }

    public int getSearchCommanderNameMin(){
        return this.searchCommanderNameMin;
    }

    public int getSearchCommanderNameMax(){
        return this.searchCommanderNameMax;
    }

    public int getSearchCommanderNum(){
        return this.searchCommanderNum;
    }

    public int getSearchCommanderLevelLimit(){
        return this.searchCommanderLevelLimit;
    }

    public List<IntPairType> getSpeedItemTypePriority(){
        return this.speedItemTypePriority;
    }

    public int getOverflowTimeMax(){
        return this.overflowTimeMax;
    }

    public List<Integer> getInfantryListInFormation(){
        return this.infantryListInFormation;
    }

    public List<Integer> getTankListInFormation(){
        return this.tankListInFormation;
    }

    public List<Integer> getArtilleryListInFormation(){
        return this.artilleryListInFormation;
    }

    public List<Integer> getGatherListInFormation(){
        return this.gatherListInFormation;
    }

    public int getFirstBindAccountAward(){
        return this.firstBindAccountAward;
    }

    public String getFormationMechaColor(){
        return this.formationMechaColor;
    }   

    public int getSkipCGTime(){
        return this.skipCGTime;
    }

    public int getExpeditionFogCenterOffset(){
        return this.expeditionFogCenterOffset;
    }

    public int getExpeditionFogLength(){
        return this.expeditionFogLength;
    }

    public int getLeavePushTime(){
        return this.LeavePushTime;
    }

    public float getUACoefficient(){
        return this.UACoefficient;
    }        

    public int getStoreRatingStarRating(){
        return this.storeRatingStarRating;
    }

    public String getStoreRatingH5(){
        return this.storeRatingH5;
    }   

    public int getFirstLoginSubtitle(){
        return this.firstLoginSubtitle;
    }

    public int getClanGoldGiftId(){
        return this.clanGoldGiftId;
    }

    public int getMidasNotifyMainCityMinLevel(){
        return this.midasNotifyMainCityMinLevel;
    }

    public int getMidasNotifyMinSaveAmt(){
        return this.midasNotifyMinSaveAmt;
    }

    public int getMidasNotifyLastHour(){
        return this.midasNotifyLastHour;
    }

    public int getMidasNotifyRenotifyDay(){
        return this.midasNotifyRenotifyDay;
    }

    public String getMidasaddress(){
        return this.midasaddress;
    }   

    public List<Integer> getSpecialClanRankListId(){
        return this.specialClanRankListId;
    }

    public List<IntPairType> getExpeditionFogRightOffset(){
        return this.expeditionFogRightOffset;
    }

    public List<Integer> getRushwarsEventRewardShow(){
        return this.RushwarsEventRewardShow;
    }


    public int getLoginfacetappinglevel(){
        return this.loginfacetappinglevel;
    }

    public int getLoginfacetapping(){
        return this.loginfacetapping;
    }

    public List<Integer> getGiftMailFromCabalBoss(){
        return this.GiftMailFromCabalBoss;
    }

    public int getMechaExpItem(){
        return this.mechaExpItem;
    }

    public int getMechaFixMail(){
        return this.mechaFixMail;
    }

    public int getChaingiftsActivityCoupon(){
        return this.ChaingiftsActivityCoupon;
    }

    public List<Integer> getLuckyTurnTable(){
        return this.luckyTurnTable;
    }

    public int getContinuousGiftMail(){
        return this.ContinuousGiftMail;
    }

    public int getDefaultBaseNameplate(){
        return this.defaultBaseNameplate;
    }

    public int getChaingiftsActivityCoupon2(){
        return this.ChaingiftsActivityCoupon2;
    }

    public int getExpeditionMinCollectionTime(){
        return this.ExpeditionMinCollectionTime;
    }

    public int getExpeditionMaxCollectionTime(){
        return this.ExpeditionMaxCollectionTime;
    }

    public int getExpeditionRemindCollectionTime(){
        return this.ExpeditionRemindCollectionTime;
    }

    public int getKVKDefaultLevelOfStockade(){
        return this.KVKDefaultLevelOfStockade;
    }

    public List<Integer> getDefaultBattleSkill(){
        return this.DefaultBattleSkill;
    }

    public List<Integer> getInitialUnit(){
        return this.initialUnit;
    }

    public int getCompassCenterDistance(){
        return this.CompassCenterDistance;
    }

    public List<IntPairType> getOmniHeroShardItem(){
        return this.omniHeroShardItem;
    }

    public int getUnitRefreshTime(){
        return this.UnitRefreshTime;
    }

    public float getDamageCoefficientK1(){
        return this.DamageCoefficientK1;
    }        

    public float getDefenseCoefficientK2(){
        return this.DefenseCoefficientK2;
    }        

    public float getTroopCoefficientK3(){
        return this.TroopCoefficientK3;
    }        

    @Override
    public int getId() {
        return 0;
    }
}

package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "L_联盟领地表.xlsx", node = "const_clan_territory.xml", isConst = true)
public class ConstClanTerritoryTemplate implements IResConstTemplate  {

    /**
     * 单兵占领速度加成\n(每n个兵提供1速度)
     */
    private int SoldierOccupyRatio = 0;
    /**
     * 基本占领速度 （每秒）
     */
    private int OccupyBaseSpeed = 0;
    /**
     * 占领积分获取率（每n秒提供1积分，不足不计算）
     */
    private int OccupyScoreRatio = 0;
    /**
     * 每日重建积分上限
     */
    private int dailyRebuildPointsLimit = 0;
    /**
     * 基础燃烧速度(每秒)，计算时会除以10000
     */
    private int basicFireSpeed = 0;
    /**
     * 夜间模式下燃烧速度百分比
     */
    private int nightBurnPercent = 0;
    /**
     * 燃烧持续时间(秒)
     */
    private int maxFireTime = 0;
    /**
     * 强制燃烧时间(秒)
     */
    private int cannotExtinguishTime = 0;
    /**
     * 灭火需要钻石
     */
    private int extinguishNeedDiamond = 0;
    /**
     * 灭火需要联盟资源类型及数值
     */
    private List<IntPairType> extinguishNeedMoney;
    /**
     * 堡垒间最小距离(cm)
     */
    private int fortMinDis = 0;
    /**
     * 宣战准备时间选项
     */
    private List<Float> declareWarPrepare;
    /**
     * 联盟领地宝箱储存资源的上限
     */
    private List<IntPairType> UpLimitOfMemberResource;
    /**
     * 基础恢复速度(每秒)
     */
    private int recoverFireSpeed = 0;
    /**
     * 获取一层BUFF所需的指挥中心数量
     */
    private int CommandCenterNumToGetBuff = 0;
    /**
     * 单层指挥中心BUFF id
     */
    private int CommandCenterSingleBuff = 0;
    /**
     * 堡垒BUFF
     */
    private int FortressBuff = 0;
    /**
     * 主基地BUFF
     */
    private int MainBaseBuff = 0;
    /**
     * 开始建设联盟建筑邮件
     */
    private int StartBuildMail = 0;
    /**
     * 完成建设联盟建筑邮件
     */
    private int FinishBuildMail = 0;
    /**
     * 取消建设联盟建筑邮件
     */
    private int CancelBuildMail = 0;
    /**
     * 攻击导致联盟建筑建设失败邮件
     */
    private int AttackWhenBuildMail = 0;
    /**
     * 主动拆除联盟建筑邮件
     */
    private int RemoveBuildingMail = 0;
    /**
     * 建筑烧毁邮件
     */
    private int DetroyByFireMail = 0;
    /**
     * 建筑被攻击邮件
     */
    private int AttackAfterBuildMail = 0;
    /**
     * 可攻占数量初始值
     */
    private int InitialBuildingNum = 0;
    /**
     * 攻占增加时间（分钟）  增加1
     */
    private int BuildingNumIncreaseInterval = 0;
    /**
     * 可攻占数量上限值
     */
    private int BuildingNumMax = 0;
    /**
     * 建筑占领成功邮件
     */
    private int BuildingOccupationSuccess = 0;
    /**
     * 建筑被其他军团占领完成的邮件
     */
    private int BuildingOccupiedByEnemy = 0;
    /**
     * 关隘建筑通行时被阻断邮件
     */
    private int PassBuildingImpassable = 0;
    /**
     * 建筑被其他军团开始占领时的邮件
     */
    private int BuildingOccupiedBeginByEnemy = 0;
    /**
     * 如果kvk没有特殊规定，默认使用该数值，据点可攻占数量
     */
    private int KVK_InitialSpikeNum = 0;
    /**
     * 如果kvk没有特殊规定，默认使用该数值，指挥中心数量
     */
    private int KVK_InitialCommandCenterNum = 0;
    /**
     * 如果kvk没有特殊规定，默认使用该数值，基地数量
     */
    private int KVK_InitialBaseNum = 0;
    /**
     * 如果kvk没有特殊规定，默认使用该数值，要塞数量
     */
    private int KVK_IntialfortressNum = 0;


    public int getSoldierOccupyRatio(){
        return this.SoldierOccupyRatio;
    }

    public int getOccupyBaseSpeed(){
        return this.OccupyBaseSpeed;
    }

    public int getOccupyScoreRatio(){
        return this.OccupyScoreRatio;
    }

    public int getDailyRebuildPointsLimit(){
        return this.dailyRebuildPointsLimit;
    }

    public int getBasicFireSpeed(){
        return this.basicFireSpeed;
    }

    public int getNightBurnPercent(){
        return this.nightBurnPercent;
    }

    public int getMaxFireTime(){
        return this.maxFireTime;
    }

    public int getCannotExtinguishTime(){
        return this.cannotExtinguishTime;
    }

    public int getExtinguishNeedDiamond(){
        return this.extinguishNeedDiamond;
    }

    public List<IntPairType> getExtinguishNeedMoney(){
        return this.extinguishNeedMoney;
    }

    public int getFortMinDis(){
        return this.fortMinDis;
    }

    public List<Float> getDeclareWarPrepare(){
        return this.declareWarPrepare;
    }

    public List<IntPairType> getUpLimitOfMemberResource(){
        return this.UpLimitOfMemberResource;
    }

    public int getRecoverFireSpeed(){
        return this.recoverFireSpeed;
    }

    public int getCommandCenterNumToGetBuff(){
        return this.CommandCenterNumToGetBuff;
    }

    public int getCommandCenterSingleBuff(){
        return this.CommandCenterSingleBuff;
    }

    public int getFortressBuff(){
        return this.FortressBuff;
    }

    public int getMainBaseBuff(){
        return this.MainBaseBuff;
    }

    public int getStartBuildMail(){
        return this.StartBuildMail;
    }

    public int getFinishBuildMail(){
        return this.FinishBuildMail;
    }

    public int getCancelBuildMail(){
        return this.CancelBuildMail;
    }

    public int getAttackWhenBuildMail(){
        return this.AttackWhenBuildMail;
    }

    public int getRemoveBuildingMail(){
        return this.RemoveBuildingMail;
    }

    public int getDetroyByFireMail(){
        return this.DetroyByFireMail;
    }

    public int getAttackAfterBuildMail(){
        return this.AttackAfterBuildMail;
    }

    public int getInitialBuildingNum(){
        return this.InitialBuildingNum;
    }

    public int getBuildingNumIncreaseInterval(){
        return this.BuildingNumIncreaseInterval;
    }

    public int getBuildingNumMax(){
        return this.BuildingNumMax;
    }

    public int getBuildingOccupationSuccess(){
        return this.BuildingOccupationSuccess;
    }

    public int getBuildingOccupiedByEnemy(){
        return this.BuildingOccupiedByEnemy;
    }

    public int getPassBuildingImpassable(){
        return this.PassBuildingImpassable;
    }

    public int getBuildingOccupiedBeginByEnemy(){
        return this.BuildingOccupiedBeginByEnemy;
    }

    public int getKvkInitialspikenum(){
        return this.KVK_InitialSpikeNum;
    }

    public int getKvkInitialcommandcenternum(){
        return this.KVK_InitialCommandCenterNum;
    }

    public int getKvkInitialbasenum(){
        return this.KVK_InitialBaseNum;
    }

    public int getKvkIntialfortressnum(){
        return this.KVK_IntialfortressNum;
    }

    @Override
    public int getId() {
        return 0;
    }
}

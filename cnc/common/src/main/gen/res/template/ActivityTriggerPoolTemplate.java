package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动触发器.xlsx", node="activity_trigger_pool.xml")
public class ActivityTriggerPoolTemplate implements IResTemplate  {

    /**
    * 触发器id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 触发器类型
    * CommonEnum.ActivityUnitTriggerType triggerType
    * 
    */
    @ResAttribute("triggerType")
    private CommonEnum.ActivityUnitTriggerType triggerType;

    /**
    * 参数
    * pairarray param
    * 
    */
    @ResAttribute("param")
    private List<IntPairType> paramPairList;

    /**
    * 触发次数限制
    * int triggerTimes
    * 
    */
    @ResAttribute("triggerTimes")
    private  int triggerTimes;



    /**
    * 触发器id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 触发器类型
    * CommonEnum.ActivityUnitTriggerType triggerType
    * 
    */
    public CommonEnum.ActivityUnitTriggerType getTriggerType(){
        return triggerType;
    }

    /**
    * 参数
    * pairarray param
    * 
    */
    public List<IntPairType> getParamPairList(){
        return paramPairList;
    }
    /**
    * 触发次数限制
    * int triggerTimes
    * 
    */
    public int getTriggerTimes(){
        return triggerTimes;
    }


}
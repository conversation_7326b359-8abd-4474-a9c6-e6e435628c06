package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="T_通行证.xlsx", node="bp_reward.xml")
public class BpRewardTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * bpId
    * int bpId
    * 
    */
    @ResAttribute("bpId")
    private  int bpId;

    /**
    * 等级
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;

    /**
    * 白银奖励ID
    * pairarray silverReward
    * 
    */
    @ResAttribute("silverReward")
    private List<IntPairType> silverRewardPairList;

    /**
    * 黄金奖励ID
    * pairarray goldReward
    * 
    */
    @ResAttribute("goldReward")
    private List<IntPairType> goldRewardPairList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * bpId
    * int bpId
    * 
    */
    public int getBpId(){
        return bpId;
    }

    /**
    * 等级
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }


    /**
    * 白银奖励ID
    * pairarray silverReward
    * 
    */
    public List<IntPairType> getSilverRewardPairList(){
        return silverRewardPairList;
    }

    /**
    * 黄金奖励ID
    * pairarray goldReward
    * 
    */
    public List<IntPairType> getGoldRewardPairList(){
        return goldRewardPairList;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="M_每日特惠.xlsx", node="daily_pack_hero.xml")
public class DailyPackHeroTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 英雄ID
    * int heroId
    * 
    */
    @ResAttribute("heroId")
    private  int heroId;

    /**
    * 上架时间类型
    * CommonEnum.ActivityOpenType openType
    * 
    */
    @ResAttribute("openType")
    private CommonEnum.ActivityOpenType openType;

    /**
    * 上架天数
    * int openTime
    * 
    */
    @ResAttribute("openTime")
    private  int openTime;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 英雄ID
    * int heroId
    * 
    */
    public int getHeroId(){
        return heroId;
    }


    /**
    * 上架时间类型
    * CommonEnum.ActivityOpenType openType
    * 
    */
    public CommonEnum.ActivityOpenType getOpenType(){
        return openType;
    }
    /**
    * 上架天数
    * int openTime
    * 
    */
    public int getOpenTime(){
        return openTime;
    }


}
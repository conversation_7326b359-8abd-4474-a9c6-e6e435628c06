package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_军团科技.xlsx", node="clan_tech_sub.xml")
public class ClanTechSubTemplate implements IResTemplate  {

    /**
    * 科技子ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 科技ID
    * int techId
    * 
    */
    @ResAttribute("techId")
    private  int techId;

    /**
    * 等级
    * int techLevel
    * 
    */
    @ResAttribute("techLevel")
    private  int techLevel;

    /**
    * 前置条件
    * intarray frontTech
    * 
    */
    @ResAttribute("frontTech")
    private List<Integer> frontTechList;

    /**
    * 属性枚举
    * pairarray param
    * 
    */
    @ResAttribute("param")
    private List<IntPairType> paramPairList;

    /**
    * buff枚举
    * pairarray buff
    * 
    */
    @ResAttribute("buff")
    private List<IntPairType> buffPairList;

    /**
    * 科技点要求
    * int costPoint
    * 
    */
    @ResAttribute("costPoint")
    private  int costPoint;

    /**
    * 时间消耗（秒）
    * int costSec
    * 
    */
    @ResAttribute("costSec")
    private  int costSec;

    /**
    * 科技研究资源消耗
    * pairarray costRes
    * 
    */
    @ResAttribute("costRes")
    private List<IntPairType> costResPairList;

    /**
    * 捐献资源消耗
    * pairarray donateCostRes
    * 
    */
    @ResAttribute("donateCostRes")
    private List<IntPairType> donateCostResPairList;

    /**
    * 捐献获得科技点数
    * int techPoint
    * 
    */
    @ResAttribute("techPoint")
    private  int techPoint;

    /**
    * 个人积分
    * int personalPoint
    * 
    */
    @ResAttribute("personalPoint")
    private  int personalPoint;

    /**
    * 联盟积分
    * int clanPoint
    * 
    */
    @ResAttribute("clanPoint")
    private  int clanPoint;

    /**
    * 个人贡献
    * int contributionPoint
    * 
    */
    @ResAttribute("contributionPoint")
    private  int contributionPoint;



    /**
    * 科技子ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 科技ID
    * int techId
    * 
    */
    public int getTechId(){
        return techId;
    }

    /**
    * 等级
    * int techLevel
    * 
    */
    public int getTechLevel(){
        return techLevel;
    }


    /**
    * 前置条件
    * intarray frontTech
    * 
    */
    public List<Integer> getFrontTechList(){
        return frontTechList;
    }


    /**
    * 属性枚举
    * pairarray param
    * 
    */
    public List<IntPairType> getParamPairList(){
        return paramPairList;
    }

    /**
    * buff枚举
    * pairarray buff
    * 
    */
    public List<IntPairType> getBuffPairList(){
        return buffPairList;
    }
    /**
    * 科技点要求
    * int costPoint
    * 
    */
    public int getCostPoint(){
        return costPoint;
    }

    /**
    * 时间消耗（秒）
    * int costSec
    * 
    */
    public int getCostSec(){
        return costSec;
    }


    /**
    * 科技研究资源消耗
    * pairarray costRes
    * 
    */
    public List<IntPairType> getCostResPairList(){
        return costResPairList;
    }

    /**
    * 捐献资源消耗
    * pairarray donateCostRes
    * 
    */
    public List<IntPairType> getDonateCostResPairList(){
        return donateCostResPairList;
    }
    /**
    * 捐献获得科技点数
    * int techPoint
    * 
    */
    public int getTechPoint(){
        return techPoint;
    }

    /**
    * 个人积分
    * int personalPoint
    * 
    */
    public int getPersonalPoint(){
        return personalPoint;
    }

    /**
    * 联盟积分
    * int clanPoint
    * 
    */
    public int getClanPoint(){
        return clanPoint;
    }

    /**
    * 个人贡献
    * int contributionPoint
    * 
    */
    public int getContributionPoint(){
        return contributionPoint;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_传送门.xlsx", node="transport.xml")
public class TransportTemplate implements IResTemplate  {

    /**
    * 传送门ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 传送触发半径
    * int radius
    * 
    */
    @ResAttribute("radius")
    private  int radius;

    /**
    * 传送目标地图
    * int targetMapId
    * 
    */
    @ResAttribute("targetMapId")
    private  int targetMapId;

    /**
    * 传送目标点
    * pairarray position
    * 
    */
    @ResAttribute("position")
    private List<IntPairType> positionPairList;

    /**
    * 目标点距离随机
    * int randDis
    * 
    */
    @ResAttribute("randDis")
    private  int randDis;



    /**
    * 传送门ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 传送触发半径
    * int radius
    * 
    */
    public int getRadius(){
        return radius;
    }

    /**
    * 传送目标地图
    * int targetMapId
    * 
    */
    public int getTargetMapId(){
        return targetMapId;
    }


    /**
    * 传送目标点
    * pairarray position
    * 
    */
    public List<IntPairType> getPositionPairList(){
        return positionPairList;
    }
    /**
    * 目标点距离随机
    * int randDis
    * 
    */
    public int getRandDis(){
        return randDis;
    }


}
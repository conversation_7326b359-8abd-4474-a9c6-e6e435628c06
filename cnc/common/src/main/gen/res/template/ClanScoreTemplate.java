package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_联盟配置表.xlsx", node="clan_score.xml")
public class ClanScoreTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 积分类型
    * CommonEnum.ClanScoreCategory scoreType
    * 
    */
    @ResAttribute("scoreType")
    private CommonEnum.ClanScoreCategory scoreType;

    /**
    * 每日获取上限
    * int day_limit
    * 
    */
    @ResAttribute("day_limit")
    private  int day_limit;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 积分类型
    * CommonEnum.ClanScoreCategory scoreType
    * 
    */
    public CommonEnum.ClanScoreCategory getScoreType(){
        return scoreType;
    }
    /**
    * 每日获取上限
    * int day_limit
    * 
    */
    public int getDayLimit(){
        return day_limit;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_争夺建筑表.xlsx", node="scramble_building.xml")
public class ScrambleBuildingTemplate implements IResTemplate  {

    /**
    * 建筑ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 建筑本身半径
    * int radius
    * 
    */
    @ResAttribute("radius")
    private  int radius;

    /**
    * 建筑争夺区半径
    * int scrambleRadius
    * 
    */
    @ResAttribute("scrambleRadius")
    private  int scrambleRadius;

    /**
    * 战斗部队
    * int troopId
    * 
    */
    @ResAttribute("troopId")
    private  int troopId;

    /**
    * 开放争夺时间（毫秒）
    * int openTime
    * 
    */
    @ResAttribute("openTime")
    private  int openTime;

    /**
    * 占领结算时间
    * int occupyTime
    * 
    */
    @ResAttribute("occupyTime")
    private  int occupyTime;

    /**
    * 占领获得积分
    * int integral
    * 
    */
    @ResAttribute("integral")
    private  int integral;

    /**
    * 占领后召唤的生物
    * pairarray occupySpawn
    * 
    */
    @ResAttribute("occupySpawn")
    private List<IntPairType> occupySpawnPairList;



    /**
    * 建筑ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 建筑本身半径
    * int radius
    * 
    */
    public int getRadius(){
        return radius;
    }

    /**
    * 建筑争夺区半径
    * int scrambleRadius
    * 
    */
    public int getScrambleRadius(){
        return scrambleRadius;
    }

    /**
    * 战斗部队
    * int troopId
    * 
    */
    public int getTroopId(){
        return troopId;
    }

    /**
    * 开放争夺时间（毫秒）
    * int openTime
    * 
    */
    public int getOpenTime(){
        return openTime;
    }

    /**
    * 占领结算时间
    * int occupyTime
    * 
    */
    public int getOccupyTime(){
        return occupyTime;
    }

    /**
    * 占领获得积分
    * int integral
    * 
    */
    public int getIntegral(){
        return integral;
    }


    /**
    * 占领后召唤的生物
    * pairarray occupySpawn
    * 
    */
    public List<IntPairType> getOccupySpawnPairList(){
        return occupySpawnPairList;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="K_科技.xlsx", node="tech_subid.xml")
public class TechSubidTemplate implements IResTemplate  {

    /**
    * 科技子ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 科技ID
    * int parentId
    * 
    */
    @ResAttribute("parentId")
    private  int parentId;

    /**
    * 等级
    * int techLevel
    * 
    */
    @ResAttribute("techLevel")
    private  int techLevel;

    /**
    * 前置条件
    * pairarray frontTech
    * 
    */
    @ResAttribute("frontTech")
    private List<IntPairType> frontTechPairList;

    /**
    * 后置科技子ID
    * int nextLevel
    * 
    */
    @ResAttribute("nextLevel")
    private  int nextLevel;

    /**
    * 属性枚举
    * pairarray param
    * 
    */
    @ResAttribute("param")
    private List<IntPairType> paramPairList;

    /**
    * buff枚举
    * pairarray buff
    * 
    */
    @ResAttribute("buff")
    private List<IntPairType> buffPairList;

    /**
    * 资源消耗
    * pairarray costResources
    * 
    */
    @ResAttribute("costResources")
    private List<IntPairType> costResourcesPairList;

    /**
    * 时间消耗（秒）
    * int timing
    * 
    */
    @ResAttribute("timing")
    private  int timing;

    /**
    * 战斗力
    * int power
    * 
    */
    @ResAttribute("power")
    private  int power;

    /**
    * 消耗道具（功能预留）
    * pairarray costItem
    * 
    */
    @ResAttribute("costItem")
    private List<IntPairType> costItemPairList;



    /**
    * 科技子ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 科技ID
    * int parentId
    * 
    */
    public int getParentId(){
        return parentId;
    }

    /**
    * 等级
    * int techLevel
    * 
    */
    public int getTechLevel(){
        return techLevel;
    }


    /**
    * 前置条件
    * pairarray frontTech
    * 
    */
    public List<IntPairType> getFrontTechPairList(){
        return frontTechPairList;
    }
    /**
    * 后置科技子ID
    * int nextLevel
    * 
    */
    public int getNextLevel(){
        return nextLevel;
    }


    /**
    * 属性枚举
    * pairarray param
    * 
    */
    public List<IntPairType> getParamPairList(){
        return paramPairList;
    }

    /**
    * buff枚举
    * pairarray buff
    * 
    */
    public List<IntPairType> getBuffPairList(){
        return buffPairList;
    }

    /**
    * 资源消耗
    * pairarray costResources
    * 
    */
    public List<IntPairType> getCostResourcesPairList(){
        return costResourcesPairList;
    }
    /**
    * 时间消耗（秒）
    * int timing
    * 
    */
    public int getTiming(){
        return timing;
    }

    /**
    * 战斗力
    * int power
    * 
    */
    public int getPower(){
        return power;
    }


    /**
    * 消耗道具（功能预留）
    * pairarray costItem
    * 
    */
    public List<IntPairType> getCostItemPairList(){
        return costItemPairList;
    }

}
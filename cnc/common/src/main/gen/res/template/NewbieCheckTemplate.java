package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="X_事件行为流.xlsx", node="newbie_check.xml")
public class NewbieCheckTemplate implements IResTemplate  {

    /**
    * 检测id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 校验类型
    * CommonEnum.NewbieCheckType type
    * 
    */
    @ResAttribute("type")
    private CommonEnum.NewbieCheckType type;

    /**
    * 校验子类
    * int typeId
    * 
    */
    @ResAttribute("typeId")
    private  int typeId;

    /**
    * 数额
    * int value
    * 
    */
    @ResAttribute("value")
    private  int value;

    /**
    * 需要校验
    * int needCheck
    * 
    */
    @ResAttribute("needCheck")
    private  int needCheck;



    /**
    * 检测id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 校验类型
    * CommonEnum.NewbieCheckType type
    * 
    */
    public CommonEnum.NewbieCheckType getType(){
        return type;
    }
    /**
    * 校验子类
    * int typeId
    * 
    */
    public int getTypeId(){
        return typeId;
    }

    /**
    * 数额
    * int value
    * 
    */
    public int getValue(){
        return value;
    }

    /**
    * 需要校验
    * int needCheck
    * 
    */
    public int getNeedCheck(){
        return needCheck;
    }


}
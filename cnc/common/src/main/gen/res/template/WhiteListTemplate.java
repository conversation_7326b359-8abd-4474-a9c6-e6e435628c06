package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="B_白名单.xlsx", node="white_list.xml")
public class WhiteListTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 大区号
    * int worldId
    * 
    */
    @ResAttribute("worldId")
    private  int worldId;

    /**
    * 小服号
    * int zoneId
    * 
    */
    @ResAttribute("zoneId")
    private  int zoneId;

    /**
    * 设备id
    * string deviceId
    * 
    */
    @ResAttribute("deviceId")
    private  String deviceId;

    /**
    * 账号id
    * string openId
    * 
    */
    @ResAttribute("openId")
    private  String openId;

    /**
    * ip
    * string ip
    * 
    */
    @ResAttribute("ip")
    private  String ip;

    /**
    * 开启debug日志
    * bool debugLog
    * 
    */
    @ResAttribute("debugLog")
    private  boolean debugLog;

    /**
    * 无视注册上限
    * bool registerLimit
    * 
    */
    @ResAttribute("registerLimit")
    private  boolean registerLimit;

    /**
    * 无视IP限制
    * bool ipLimit
    * 
    */
    @ResAttribute("ipLimit")
    private  boolean ipLimit;

    /**
    * 超级用户
    * bool powerUser
    * 
    */
    @ResAttribute("powerUser")
    private  boolean powerUser;

    /**
    * 无视鉴权
    * bool noAuth
    * 
    */
    @ResAttribute("noAuth")
    private  boolean noAuth;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 大区号
    * int worldId
    * 
    */
    public int getWorldId(){
        return worldId;
    }

    /**
    * 小服号
    * int zoneId
    * 
    */
    public int getZoneId(){
        return zoneId;
    }

    /**
    * 设备id
    * string deviceId
    * 
    */
    public String getDeviceId(){
        return deviceId;
    }
    /**
    * 账号id
    * string openId
    * 
    */
    public String getOpenId(){
        return openId;
    }
    /**
    * ip
    * string ip
    * 
    */
    public String getIp(){
        return ip;
    }

    /**
    * 开启debug日志
    * bool debugLog
    * 
    */
    public boolean getDebugLog(){
        return debugLog;
    }

    /**
    * 无视注册上限
    * bool registerLimit
    * 
    */
    public boolean getRegisterLimit(){
        return registerLimit;
    }

    /**
    * 无视IP限制
    * bool ipLimit
    * 
    */
    public boolean getIpLimit(){
        return ipLimit;
    }

    /**
    * 超级用户
    * bool powerUser
    * 
    */
    public boolean getPowerUser(){
        return powerUser;
    }

    /**
    * 无视鉴权
    * bool noAuth
    * 
    */
    public boolean getNoAuth(){
        return noAuth;
    }

}
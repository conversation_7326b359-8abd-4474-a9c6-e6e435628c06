package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_城池导量表.xlsx", node="city_ascend.xml")
public class CityAscendTemplate implements IResTemplate  {

    /**
    * 主堡等级
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 离线小时数
    * int hours
    * 
    */
    @ResAttribute("hours")
    private  int hours;

    /**
    * kvk地图离线小时数
    * int kvkHours
    * 
    */
    @ResAttribute("kvkHours")
    private  int kvkHours;

    /**
    * 耐久值
    * int durability
    * 
    */
    @ResAttribute("durability")
    private  int durability;



    /**
    * 主堡等级
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 离线小时数
    * int hours
    * 
    */
    public int getHours(){
        return hours;
    }

    /**
    * kvk地图离线小时数
    * int kvkHours
    * 
    */
    public int getKvkHours(){
        return kvkHours;
    }

    /**
    * 耐久值
    * int durability
    * 
    */
    public int getDurability(){
        return durability;
    }


}
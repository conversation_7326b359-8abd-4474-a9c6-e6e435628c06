package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="A_AI指令.xlsx", node="ai_instance.xml")
public class AiInstanceTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 使用的ai基础模型（见ai_base_model提供）
    * CommonEnum.AiBaseModelType model
    * 
    */
    @ResAttribute("model")
    private CommonEnum.AiBaseModelType model;

    /**
    * 参数
    * pairarray params
    * 
    */
    @ResAttribute("params")
    private List<IntPairType> paramsPairList;

    /**
    * 状态效果触发器（状态id_触发器id，状态id见ai_base_model提供）
    * pairarray triggers
    * 
    */
    @ResAttribute("triggers")
    private List<IntPairType> triggersPairList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 使用的ai基础模型（见ai_base_model提供）
    * CommonEnum.AiBaseModelType model
    * 
    */
    public CommonEnum.AiBaseModelType getModel(){
        return model;
    }

    /**
    * 参数
    * pairarray params
    * 
    */
    public List<IntPairType> getParamsPairList(){
        return paramsPairList;
    }

    /**
    * 状态效果触发器（状态id_触发器id，状态id见ai_base_model提供）
    * pairarray triggers
    * 
    */
    public List<IntPairType> getTriggersPairList(){
        return triggersPairList;
    }

}
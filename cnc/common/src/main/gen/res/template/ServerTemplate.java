package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="A_服务器环境.xlsx", node="server.xml")
public class ServerTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 名称
    * string name
    * 
    */
    @ResAttribute("name")
    private  String name;

    /**
    * IP
    * string ip
    * 
    */
    @ResAttribute("ip")
    private  String ip;

    /**
    * 端口
    * int port
    * 
    */
    @ResAttribute("port")
    private  int port;

    /**
    * 正式环境
    * int envType
    * 
    */
    @ResAttribute("envType")
    private  int envType;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 名称
    * string name
    * 
    */
    public String getName(){
        return name;
    }
    /**
    * IP
    * string ip
    * 
    */
    public String getIp(){
        return ip;
    }
    /**
    * 端口
    * int port
    * 
    */
    public int getPort(){
        return port;
    }

    /**
    * 正式环境
    * int envType
    * 
    */
    public int getEnvType(){
        return envType;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="F_副本信息表(里约危机).xlsx", node="spawn_riocrisis_dungeon.xml")
public class SpawnRiocrisisDungeonTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * X坐标
    * int post_X
    * 
    */
    @ResAttribute("post_X")
    private  int post_X;

    /**
    * Y坐标
    * int post_Y
    * 
    */
    @ResAttribute("post_Y")
    private  int post_Y;

    /**
    * 关联表枚举
    * CommonEnum.DungeonBuildType enum_type
    * 
    */
    @ResAttribute("enum_type")
    private CommonEnum.DungeonBuildType enum_type;

    /**
    * 关联ID
    * int correlationId
    * 
    */
    @ResAttribute("correlationId")
    private  int correlationId;

    /**
    * 地图初始化时是否创建
    * bool initSpawn
    * 
    */
    @ResAttribute("initSpawn")
    private  boolean initSpawn;

    /**
    * 阵营
    * int camp
    * 
    */
    @ResAttribute("camp")
    private  int camp;

    /**
    * 初始buff
    * pairarray buffs
    * 
    */
    @ResAttribute("buffs")
    private List<IntPairType> buffsPairList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * X坐标
    * int post_X
    * 
    */
    public int getPostX(){
        return post_X;
    }

    /**
    * Y坐标
    * int post_Y
    * 
    */
    public int getPostY(){
        return post_Y;
    }


    /**
    * 关联表枚举
    * CommonEnum.DungeonBuildType enum_type
    * 
    */
    public CommonEnum.DungeonBuildType getEnumType(){
        return enum_type;
    }
    /**
    * 关联ID
    * int correlationId
    * 
    */
    public int getCorrelationId(){
        return correlationId;
    }


    /**
    * 地图初始化时是否创建
    * bool initSpawn
    * 
    */
    public boolean getInitSpawn(){
        return initSpawn;
    }
    /**
    * 阵营
    * int camp
    * 
    */
    public int getCamp(){
        return camp;
    }


    /**
    * 初始buff
    * pairarray buffs
    * 
    */
    public List<IntPairType> getBuffsPairList(){
        return buffsPairList;
    }

}
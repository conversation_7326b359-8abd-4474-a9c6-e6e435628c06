package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_建筑群.xlsx", node="build_group.xml")
public class BuildGroupTemplate implements IResTemplate  {

    /**
    * 建筑群ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 建筑群半径
    * int radius
    * 
    */
    @ResAttribute("radius")
    private  int radius;

    /**
    * 部队表ID
    * int troopId
    * 
    */
    @ResAttribute("troopId")
    private  int troopId;



    /**
    * 建筑群ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 建筑群半径
    * int radius
    * 
    */
    public int getRadius(){
        return radius;
    }

    /**
    * 部队表ID
    * int troopId
    * 
    */
    public int getTroopId(){
        return troopId;
    }


}
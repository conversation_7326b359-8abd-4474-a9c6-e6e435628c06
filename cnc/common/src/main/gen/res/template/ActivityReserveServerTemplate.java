package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动表_策划分表.xlsx", node="activity_reserve_server.xml")
public class ActivityReserveServerTemplate implements IResTemplate  {

    /**
    * 活动id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 活动逻辑单元
    * CommonEnum.ActivityUnitType unitType
    * 
    */
    @ResAttribute("unitType")
    private CommonEnum.ActivityUnitType unitType;

    /**
    * 怪物等级
    * pair levelPair
    * 
    */
    @ResAttribute("levelPair")
    private IntPairType levelPairPair;

    /**
    * 掉落概率(万分比)
    * int prob
    * 
    */
    @ResAttribute("prob")
    private  int prob;

    /**
    * 邮件id
    * int mailId
    * 
    */
    @ResAttribute("mailId")
    private  int mailId;



    /**
    * 活动id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 活动逻辑单元
    * CommonEnum.ActivityUnitType unitType
    * 
    */
    public CommonEnum.ActivityUnitType getUnitType(){
        return unitType;
    }

    /**
    * 怪物等级
    * pair levelPair
    * 
    */
    public IntPairType getLevelPairPair(){
        return levelPairPair;
    }
    /**
    * 掉落概率(万分比)
    * int prob
    * 
    */
    public int getProb(){
        return prob;
    }

    /**
    * 邮件id
    * int mailId
    * 
    */
    public int getMailId(){
        return mailId;
    }


}
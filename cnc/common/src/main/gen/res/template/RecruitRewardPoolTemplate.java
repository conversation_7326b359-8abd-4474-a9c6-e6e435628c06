package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_招募.xlsx", node="recruit_reward_pool.xml")
public class RecruitRewardPoolTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 所属奖池id
    * int poolId
    * 
    */
    @ResAttribute("poolId")
    private  int poolId;

    /**
    * 奖励所属类
    * int boxId
    * 
    */
    @ResAttribute("boxId")
    private  int boxId;

    /**
    * 权重
    * int weight
    * 
    */
    @ResAttribute("weight")
    private  int weight;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 所属奖池id
    * int poolId
    * 
    */
    public int getPoolId(){
        return poolId;
    }

    /**
    * 奖励所属类
    * int boxId
    * 
    */
    public int getBoxId(){
        return boxId;
    }

    /**
    * 权重
    * int weight
    * 
    */
    public int getWeight(){
        return weight;
    }


}
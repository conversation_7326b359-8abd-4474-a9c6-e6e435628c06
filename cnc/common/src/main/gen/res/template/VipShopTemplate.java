package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="V_VIP.xlsx", node="vip_shop.xml")
public class VipShopTemplate implements IResTemplate  {

    /**
    * VIP商品id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * vip等级
    * int vipLevel
    * 
    */
    @ResAttribute("vipLevel")
    private  int vipLevel;

    /**
    * 道具
    * pair items
    * 
    */
    @ResAttribute("items")
    private IntPairType itemsPair;

    /**
    * 限购数量
    * int buyLimit
    * 
    */
    @ResAttribute("buyLimit")
    private  int buyLimit;

    /**
    * 实际价格
    * pair price
    * 
    */
    @ResAttribute("price")
    private IntPairType pricePair;

    /**
    * 自选英雄凭证数量
    * int optional
    * 
    */
    @ResAttribute("optional")
    private  int optional;

    /**
    * 自选英雄凭证品质
    * int quality
    * 
    */
    @ResAttribute("quality")
    private  int quality;



    /**
    * VIP商品id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * vip等级
    * int vipLevel
    * 
    */
    public int getVipLevel(){
        return vipLevel;
    }


    /**
    * 道具
    * pair items
    * 
    */
    public IntPairType getItemsPair(){
        return itemsPair;
    }
    /**
    * 限购数量
    * int buyLimit
    * 
    */
    public int getBuyLimit(){
        return buyLimit;
    }


    /**
    * 实际价格
    * pair price
    * 
    */
    public IntPairType getPricePair(){
        return pricePair;
    }
    /**
    * 自选英雄凭证数量
    * int optional
    * 
    */
    public int getOptional(){
        return optional;
    }

    /**
    * 自选英雄凭证品质
    * int quality
    * 
    */
    public int getQuality(){
        return quality;
    }


}
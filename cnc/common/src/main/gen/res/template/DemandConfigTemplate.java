package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_成就.xlsx", node="demand_config.xml")
public class DemandConfigTemplate implements IResTemplate  {

    /**
    * 需求id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 需求类型
    * CommonEnum.AchievementDemandType type
    * 
    */
    @ResAttribute("type")
    private CommonEnum.AchievementDemandType type;

    /**
    * 校验类型
    * CommonEnum.AchievementStatisticCheckerType checkType
    * 
    */
    @ResAttribute("checkType")
    private CommonEnum.AchievementStatisticCheckerType checkType;

    /**
    * 校验参数
    * intarray param
    * 
    */
    @ResAttribute("param")
    private List<Integer> paramList;



    /**
    * 需求id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 需求类型
    * CommonEnum.AchievementDemandType type
    * 
    */
    public CommonEnum.AchievementDemandType getType(){
        return type;
    }

    /**
    * 校验类型
    * CommonEnum.AchievementStatisticCheckerType checkType
    * 
    */
    public CommonEnum.AchievementStatisticCheckerType getCheckType(){
        return checkType;
    }

    /**
    * 校验参数
    * intarray param
    * 
    */
    public List<Integer> getParamList(){
        return paramList;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="X_事件行为流.xlsx", node="newbie.xml")
public class NewbieTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 组id
    * int groupId
    * 
    */
    @ResAttribute("groupId")
    private  int groupId;

    /**
    * 步骤
    * int step
    * 
    */
    @ResAttribute("step")
    private  int step;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 组id
    * int groupId
    * 
    */
    public int getGroupId(){
        return groupId;
    }

    /**
    * 步骤
    * int step
    * 
    */
    public int getStep(){
        return step;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_节日活动.xlsx", node="fp_reward_pool.xml")
public class FpRewardPoolTemplate implements IResTemplate  {

    /**
    * 奖励id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 道具
    * pairarray items
    * 
    */
    @ResAttribute("items")
    private List<IntPairType> itemsPairList;



    /**
    * 奖励id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 道具
    * pairarray items
    * 
    */
    public List<IntPairType> getItemsPairList(){
        return itemsPairList;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="K_空军.xlsx", node="battle_plane.xml")
public class BattlePlaneTemplate implements IResTemplate  {

    /**
    * 战机id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 初始模型
    * int initModel
    * 
    */
    @ResAttribute("initModel")
    private  int initModel;

    /**
    * 解锁消耗道具
    * string cost
    * 
    */
    @ResAttribute("cost")
    private  String cost;



    /**
    * 战机id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 初始模型
    * int initModel
    * 
    */
    public int getInitModel(){
        return initModel;
    }

    /**
    * 解锁消耗道具
    * string cost
    * 
    */
    public String getCost(){
        return cost;
    }

}
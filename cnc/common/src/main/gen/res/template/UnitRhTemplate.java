package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_英雄表_New.xlsx", node="unit_rh.xml")
public class UnitRhTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 对应yaml模板
    * string template
    * 
    */
    @ResAttribute("template")
    private  String template;

    /**
    * 单位类型
    * int type
    * 
    */
    @ResAttribute("type")
    private  int type;

    /**
    * 单位功能类型
    * int function_type
    * 
    */
    @ResAttribute("function_type")
    private  int function_type;

    /**
    * 技能1(默认技能）
    * pair skill1
    * 
    */
    @ResAttribute("skill1")
    private IntPairType skill1Pair;

    /**
    * 技能2
    * pair skill2
    * 
    */
    @ResAttribute("skill2")
    private IntPairType skill2Pair;

    /**
    * 技能3
    * pair skill3
    * 
    */
    @ResAttribute("skill3")
    private IntPairType skill3Pair;

    /**
    * 技能4
    * pair skill4
    * 
    */
    @ResAttribute("skill4")
    private IntPairType skill4Pair;

    /**
    * 移动速度
    * float speed
    * 
    */

    @ResAttribute("speed")
    private  float speed;
    /**
    * 属性继承对象
    * int attr_link_target
    * 
    */
    @ResAttribute("attr_link_target")
    private  int attr_link_target;

    /**
    * 属性继承攻击百分比
    * int attr_link_atk_percentage
    * 
    */
    @ResAttribute("attr_link_atk_percentage")
    private  int attr_link_atk_percentage;

    /**
    * 属性继承防御百分比
    * int attr_link_def_percentage
    * 
    */
    @ResAttribute("attr_link_def_percentage")
    private  int attr_link_def_percentage;

    /**
    * 属性继承生命百分比
    * int attr_link_hp_percentage
    * 
    */
    @ResAttribute("attr_link_hp_percentage")
    private  int attr_link_hp_percentage;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 对应yaml模板
    * string template
    * 
    */
    public String getTemplate(){
        return template;
    }
    /**
    * 单位类型
    * int type
    * 
    */
    public int getType(){
        return type;
    }

    /**
    * 单位功能类型
    * int function_type
    * 
    */
    public int getFunctionType(){
        return function_type;
    }


    /**
    * 技能1(默认技能）
    * pair skill1
    * 
    */
    public IntPairType getSkill1Pair(){
        return skill1Pair;
    }

    /**
    * 技能2
    * pair skill2
    * 
    */
    public IntPairType getSkill2Pair(){
        return skill2Pair;
    }

    /**
    * 技能3
    * pair skill3
    * 
    */
    public IntPairType getSkill3Pair(){
        return skill3Pair;
    }

    /**
    * 技能4
    * pair skill4
    * 
    */
    public IntPairType getSkill4Pair(){
        return skill4Pair;
    }
    /**
    * 移动速度
    * float speed
    * 
    */
    public float getSpeed(){
        return speed;
    }

    /**
    * 属性继承对象
    * int attr_link_target
    * 
    */
    public int getAttrLinkTarget(){
        return attr_link_target;
    }

    /**
    * 属性继承攻击百分比
    * int attr_link_atk_percentage
    * 
    */
    public int getAttrLinkAtkPercentage(){
        return attr_link_atk_percentage;
    }

    /**
    * 属性继承防御百分比
    * int attr_link_def_percentage
    * 
    */
    public int getAttrLinkDefPercentage(){
        return attr_link_def_percentage;
    }

    /**
    * 属性继承生命百分比
    * int attr_link_hp_percentage
    * 
    */
    public int getAttrLinkHpPercentage(){
        return attr_link_hp_percentage;
    }


}
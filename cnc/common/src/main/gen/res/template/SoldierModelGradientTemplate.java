package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="S_士兵表_阵型.xlsx", node="soldier_model_gradient.xml")
public class SoldierModelGradientTemplate implements IResTemplate  {

    /**
    * 序号
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 兵力数量
    * pair armyGradient
    * 
    */
    @ResAttribute("armyGradient")
    private IntPairType armyGradientPair;

    /**
    * 兵种数量1
    * pairarray type
    * 
    */
    @ResAttribute("type")
    private List<IntPairType> typePairList;



    /**
    * 序号
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 兵力数量
    * pair armyGradient
    * 
    */
    public IntPairType getArmyGradientPair(){
        return armyGradientPair;
    }

    /**
    * 兵种数量1
    * pairarray type
    * 
    */
    public List<IntPairType> getTypePairList(){
        return typePairList;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "L_聊天表.xlsx", node = "const_chat.xml", isConst = true)
public class ConstChatTemplate implements IResConstTemplate  {

    /**
     * 聊天一次请求多少条消息
     */
    private int ChatReqMessageNumber = 0;
    /**
     * 王国聊天CD（秒）
     */
    private int ChatCD = 0;
    /**
     * 聊天最大字符
     */
    private int CharacterLength = 0;
    /**
     * 建造中心<10级为新手
     */
    private int NewbieStandard = 0;
    /**
     * 新手的聊天CD为60秒
     */
    private int NewbieChatCD = 0;
    /**
     * 私聊等级限制
     */
    private int PrivateChatLevel = 0;
    /**
     * 创群最少人数
     */
    private int GroupChatNumber = 0;
    /**
     * 群聊上限
     */
    private int GroupChatMax = 0;
    /**
     * 私聊上限
     */
    private int PrivateChatMax = 0;
    /**
     * (世界，军团，群聊，私聊)是否展示国旗
     */
    private List<Integer> ShowFlag;
    /**
     * 分享战报侦察报告过期时间（天）
     */
    private int MailShareTime = 0;


    public int getChatReqMessageNumber(){
        return this.ChatReqMessageNumber;
    }

    public int getChatCD(){
        return this.ChatCD;
    }

    public int getCharacterLength(){
        return this.CharacterLength;
    }

    public int getNewbieStandard(){
        return this.NewbieStandard;
    }

    public int getNewbieChatCD(){
        return this.NewbieChatCD;
    }

    public int getPrivateChatLevel(){
        return this.PrivateChatLevel;
    }

    public int getGroupChatNumber(){
        return this.GroupChatNumber;
    }

    public int getGroupChatMax(){
        return this.GroupChatMax;
    }

    public int getPrivateChatMax(){
        return this.PrivateChatMax;
    }

    public List<Integer> getShowFlag(){
        return this.ShowFlag;
    }

    public int getMailShareTime(){
        return this.MailShareTime;
    }

    @Override
    public int getId() {
        return 0;
    }
}

package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动商店表.xlsx", node="activity_store_goods.xml")
public class ActivityStoreGoodsTemplate implements IResTemplate  {

    /**
    * 商品id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 消耗道具id数量
    * pairarray costItem
    * 
    */
    @ResAttribute("costItem")
    private List<IntPairType> costItemPairList;

    /**
    * 获得道具id数量
    * pairarray getItem
    * 
    */
    @ResAttribute("getItem")
    private List<IntPairType> getItemPairList;

    /**
    * 兑换次数上限
    * int limit
    * 
    */
    @ResAttribute("limit")
    private  int limit;

    /**
    * 是否立即打开兑换后道具
    * int autoOpen
    * 
    */
    @ResAttribute("autoOpen")
    private  int autoOpen;



    /**
    * 商品id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 消耗道具id数量
    * pairarray costItem
    * 
    */
    public List<IntPairType> getCostItemPairList(){
        return costItemPairList;
    }

    /**
    * 获得道具id数量
    * pairarray getItem
    * 
    */
    public List<IntPairType> getGetItemPairList(){
        return getItemPairList;
    }
    /**
    * 兑换次数上限
    * int limit
    * 
    */
    public int getLimit(){
        return limit;
    }

    /**
    * 是否立即打开兑换后道具
    * int autoOpen
    * 
    */
    public int getAutoOpen(){
        return autoOpen;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="B_部队配置表.xlsx", node="troop.xml")
public class TroopTemplate implements IResTemplate  {

    /**
    * 部队Id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 主将Id
    * int mainHeroId
    * 
    */
    @ResAttribute("mainHeroId")
    private  int mainHeroId;

    /**
    * 主将英雄等级
    * int mainHeroLevel
    * 
    */
    @ResAttribute("mainHeroLevel")
    private  int mainHeroLevel;

    /**
    * 主将英雄星级
    * int mainHeroStar
    * 
    */
    @ResAttribute("mainHeroStar")
    private  int mainHeroStar;

    /**
    * 副将Id
    * int deputyHeroId
    * 
    */
    @ResAttribute("deputyHeroId")
    private  int deputyHeroId;

    /**
    * 副将英雄等级
    * int deputyHeroLevel
    * 
    */
    @ResAttribute("deputyHeroLevel")
    private  int deputyHeroLevel;

    /**
    * 副将英雄星级
    * int deputyHeroStar
    * 
    */
    @ResAttribute("deputyHeroStar")
    private  int deputyHeroStar;

    /**
    * 是否会受到对NPC部队增益影响
    * bool belongsToNpc
    * 
    */
    @ResAttribute("belongsToNpc")
    private  boolean belongsToNpc;

    /**
    * 兵种组合（兵种ID_数量）
    * pairarray soldier
    * 
    */
    @ResAttribute("soldier")
    private List<IntPairType> soldierPairList;

    /**
    * 增益BUFF组(万分比）
    * pairarray additionGroup
    * 
    */
    @ResAttribute("additionGroup")
    private List<IntPairType> additionGroupPairList;



    /**
    * 部队Id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 主将Id
    * int mainHeroId
    * 
    */
    public int getMainHeroId(){
        return mainHeroId;
    }

    /**
    * 主将英雄等级
    * int mainHeroLevel
    * 
    */
    public int getMainHeroLevel(){
        return mainHeroLevel;
    }

    /**
    * 主将英雄星级
    * int mainHeroStar
    * 
    */
    public int getMainHeroStar(){
        return mainHeroStar;
    }

    /**
    * 副将Id
    * int deputyHeroId
    * 
    */
    public int getDeputyHeroId(){
        return deputyHeroId;
    }

    /**
    * 副将英雄等级
    * int deputyHeroLevel
    * 
    */
    public int getDeputyHeroLevel(){
        return deputyHeroLevel;
    }

    /**
    * 副将英雄星级
    * int deputyHeroStar
    * 
    */
    public int getDeputyHeroStar(){
        return deputyHeroStar;
    }


    /**
    * 是否会受到对NPC部队增益影响
    * bool belongsToNpc
    * 
    */
    public boolean getBelongsToNpc(){
        return belongsToNpc;
    }

    /**
    * 兵种组合（兵种ID_数量）
    * pairarray soldier
    * 
    */
    public List<IntPairType> getSoldierPairList(){
        return soldierPairList;
    }

    /**
    * 增益BUFF组(万分比）
    * pairarray additionGroup
    * 
    */
    public List<IntPairType> getAdditionGroupPairList(){
        return additionGroupPairList;
    }

}
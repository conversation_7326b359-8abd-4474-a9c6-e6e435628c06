package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="T_天网.xlsx", node="skynet_level.xml")
public class SkynetLevelTemplate implements IResTemplate  {

    /**
    * 天网等级
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 界面显示上限
    * int displayMaxNum
    * 
    */
    @ResAttribute("displayMaxNum")
    private  int displayMaxNum;

    /**
    * 每轮刷新任务数量
    * int taskNum
    * 
    */
    @ResAttribute("taskNum")
    private  int taskNum;

    /**
    * 升级后补充日常任务数量
    * int levelUpGiveTaskNum
    * 
    */
    @ResAttribute("levelUpGiveTaskNum")
    private  int levelUpGiveTaskNum;

    /**
    * 升级后补充守卫者任务数量
    * int levelUpGiveDefenderTaskNum
    * 
    */
    @ResAttribute("levelUpGiveDefenderTaskNum")
    private  int levelUpGiveDefenderTaskNum;

    /**
    * 品质权重
    * pairarray qualityWeight
    * 
    */
    @ResAttribute("qualityWeight")
    private List<IntPairType> qualityWeightPairList;

    /**
    * 任务类型权重
    * pairarray taskTypeWeight
    * 
    */
    @ResAttribute("taskTypeWeight")
    private List<IntPairType> taskTypeWeightPairList;

    /**
    * 升至下级所需经验
    * int needExp
    * 
    */
    @ResAttribute("needExp")
    private  int needExp;



    /**
    * 天网等级
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 界面显示上限
    * int displayMaxNum
    * 
    */
    public int getDisplayMaxNum(){
        return displayMaxNum;
    }

    /**
    * 每轮刷新任务数量
    * int taskNum
    * 
    */
    public int getTaskNum(){
        return taskNum;
    }

    /**
    * 升级后补充日常任务数量
    * int levelUpGiveTaskNum
    * 
    */
    public int getLevelUpGiveTaskNum(){
        return levelUpGiveTaskNum;
    }

    /**
    * 升级后补充守卫者任务数量
    * int levelUpGiveDefenderTaskNum
    * 
    */
    public int getLevelUpGiveDefenderTaskNum(){
        return levelUpGiveDefenderTaskNum;
    }


    /**
    * 品质权重
    * pairarray qualityWeight
    * 
    */
    public List<IntPairType> getQualityWeightPairList(){
        return qualityWeightPairList;
    }

    /**
    * 任务类型权重
    * pairarray taskTypeWeight
    * 
    */
    public List<IntPairType> getTaskTypeWeightPairList(){
        return taskTypeWeightPairList;
    }
    /**
    * 升至下级所需经验
    * int needExp
    * 
    */
    public int getNeedExp(){
        return needExp;
    }


}
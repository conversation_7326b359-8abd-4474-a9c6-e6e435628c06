package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="G_公告.xlsx", node="notice_board.xml")
public class NoticeBoardTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 页签标题
    * int tab
    * 
    */
    @ResAttribute("tab")
    private  int tab;

    /**
    * 公告标题
    * int title
    * 
    */
    @ResAttribute("title")
    private  int title;

    /**
    * 公告内容
    * int des
    * 
    */
    @ResAttribute("des")
    private  int des;

    /**
    * 宣传图片
    * string image
    * 
    */
    @ResAttribute("image")
    private  String image;

    /**
    * 上架时间
    * string startTime
    * 
    */
    @ResAttribute("startTime")
    private  String startTime;

    /**
    * 下架时间
    * string offTime
    * 
    */
    @ResAttribute("offTime")
    private  String offTime;

    /**
    * 排序权重
    * int weight
    * 
    */
    @ResAttribute("weight")
    private  int weight;

    /**
    * 是否拍脸
    * int activePush
    * 
    */
    @ResAttribute("activePush")
    private  int activePush;

    /**
    * 是否显示上架时间
    * int displayTime
    * 
    */
    @ResAttribute("displayTime")
    private  int displayTime;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 页签标题
    * int tab
    * 
    */
    public int getTab(){
        return tab;
    }

    /**
    * 公告标题
    * int title
    * 
    */
    public int getTitle(){
        return title;
    }

    /**
    * 公告内容
    * int des
    * 
    */
    public int getDes(){
        return des;
    }

    /**
    * 宣传图片
    * string image
    * 
    */
    public String getImage(){
        return image;
    }
    /**
    * 上架时间
    * string startTime
    * 
    */
    public String getStartTime(){
        return startTime;
    }
    /**
    * 下架时间
    * string offTime
    * 
    */
    public String getOffTime(){
        return offTime;
    }
    /**
    * 排序权重
    * int weight
    * 
    */
    public int getWeight(){
        return weight;
    }

    /**
    * 是否拍脸
    * int activePush
    * 
    */
    public int getActivePush(){
        return activePush;
    }

    /**
    * 是否显示上架时间
    * int displayTime
    * 
    */
    public int getDisplayTime(){
        return displayTime;
    }


}
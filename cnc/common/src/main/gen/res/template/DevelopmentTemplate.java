package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="F_发展度.xlsx", node="development.xml")
public class DevelopmentTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 效果
    * pairarray effect
    * 
    */
    @ResAttribute("effect")
    private List<IntPairType> effectPairList;

    /**
    * 奖励ID
    * pairarray item
    * 
    */
    @ResAttribute("item")
    private List<IntPairType> itemPairList;

    /**
    * 所需发展度
    * int neednum
    * 
    */
    @ResAttribute("neednum")
    private  int neednum;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 效果
    * pairarray effect
    * 
    */
    public List<IntPairType> getEffectPairList(){
        return effectPairList;
    }

    /**
    * 奖励ID
    * pairarray item
    * 
    */
    public List<IntPairType> getItemPairList(){
        return itemPairList;
    }
    /**
    * 所需发展度
    * int neednum
    * 
    */
    public int getNeednum(){
        return neednum;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_增益效果类型.xlsx", node="buff_display.xml")
public class BuffDisplayTemplate implements IResTemplate  {

    /**
    * 效果展示id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;



    /**
    * 效果展示id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


}
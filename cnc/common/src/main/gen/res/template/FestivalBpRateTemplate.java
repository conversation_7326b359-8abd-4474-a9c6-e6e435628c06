package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_节日活动.xlsx", node="festival_bp_rate.xml")
public class FestivalBpRateTemplate implements IResTemplate  {

    /**
    * 活动id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 爆率
    * int rate
    * 
    */
    @ResAttribute("rate")
    private  int rate;

    /**
    * 奖励邮件
    * int rewardMail
    * 
    */
    @ResAttribute("rewardMail")
    private  int rewardMail;

    /**
    * 积分道具id
    * int scoreItem
    * 
    */
    @ResAttribute("scoreItem")
    private  int scoreItem;



    /**
    * 活动id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 爆率
    * int rate
    * 
    */
    public int getRate(){
        return rate;
    }

    /**
    * 奖励邮件
    * int rewardMail
    * 
    */
    public int getRewardMail(){
        return rewardMail;
    }

    /**
    * 积分道具id
    * int scoreItem
    * 
    */
    public int getScoreItem(){
        return scoreItem;
    }


}
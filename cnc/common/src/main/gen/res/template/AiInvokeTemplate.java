package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="A_AI指令.xlsx", node="ai_invoke.xml")
public class AiInvokeTemplate implements IResTemplate  {

    /**
    * 召唤id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 野怪id
    * int monsterId
    * 
    */
    @ResAttribute("monsterId")
    private  int monsterId;

    /**
    * 召唤数量
    * int num
    * 
    */
    @ResAttribute("num")
    private  int num;

    /**
    * 存活时间(秒)
    * int lifeTime
    * 
    */
    @ResAttribute("lifeTime")
    private  int lifeTime;

    /**
    * 出生区域
    * circlearray bornArea
    * 
    */
    @ResAttribute("bornArea")
    private List<CircleType> bornAreaList;
    /**
    * 朝向
    * CommonEnum.InvokeYawType yaw
    * 
    */
    @ResAttribute("yaw")
    private CommonEnum.InvokeYawType yaw;



    /**
    * 召唤id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 野怪id
    * int monsterId
    * 
    */
    public int getMonsterId(){
        return monsterId;
    }

    /**
    * 召唤数量
    * int num
    * 
    */
    public int getNum(){
        return num;
    }

    /**
    * 存活时间(秒)
    * int lifeTime
    * 
    */
    public int getLifeTime(){
        return lifeTime;
    }


    /**
    * 出生区域
    * circlearray bornArea
    * 
    */
    public List<CircleType> getBornAreaList(){
        return bornAreaList;
    }

    /**
    * 朝向
    * CommonEnum.InvokeYawType yaw
    * 
    */
    public CommonEnum.InvokeYawType getYaw(){
        return yaw;
    }

}
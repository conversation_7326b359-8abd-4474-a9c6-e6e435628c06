package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_侦察.xlsx", node="spy_cost.xml")
public class SpyCostTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 侦察类型枚举
    * CommonEnum.SpyType Type
    * 
    */
    @ResAttribute("Type")
    private CommonEnum.SpyType Type;

    /**
    * 对应等级
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;

    /**
    * 消耗资源数值
    * int currencyNum
    * 
    */
    @ResAttribute("currencyNum")
    private  int currencyNum;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 侦察类型枚举
    * CommonEnum.SpyType Type
    * 
    */
    public CommonEnum.SpyType getType(){
        return Type;
    }
    /**
    * 对应等级
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }

    /**
    * 消耗资源数值
    * int currencyNum
    * 
    */
    public int getCurrencyNum(){
        return currencyNum;
    }


}
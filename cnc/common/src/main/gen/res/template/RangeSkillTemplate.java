package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_技能表_技能.xlsx", node="range_skill.xml")
public class RangeSkillTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 回收时间
    * int lifetime
    * 
    */
    @ResAttribute("lifetime")
    private  int lifetime;

    /**
    * 部队表索引
    * int troopIndex
    * 
    */
    @ResAttribute("troopIndex")
    private  int troopIndex;

    /**
    * 出生点范围选择
    * CommonEnum.AreaSkillBornPointType createRange
    * 
    */
    @ResAttribute("createRange")
    private CommonEnum.AreaSkillBornPointType createRange;

    /**
    * 范围
    * intarray range
    * 
    */
    @ResAttribute("range")
    private List<Integer> rangeList;

    /**
    * 主人脱战时回收
    * int masterRemove
    * 
    */
    @ResAttribute("masterRemove")
    private  int masterRemove;

    /**
    * 主人死亡时回收
    * int masterDieRemove
    * 
    */
    @ResAttribute("masterDieRemove")
    private  int masterDieRemove;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 回收时间
    * int lifetime
    * 
    */
    public int getLifetime(){
        return lifetime;
    }

    /**
    * 部队表索引
    * int troopIndex
    * 
    */
    public int getTroopIndex(){
        return troopIndex;
    }


    /**
    * 出生点范围选择
    * CommonEnum.AreaSkillBornPointType createRange
    * 
    */
    public CommonEnum.AreaSkillBornPointType getCreateRange(){
        return createRange;
    }

    /**
    * 范围
    * intarray range
    * 
    */
    public List<Integer> getRangeList(){
        return rangeList;
    }

    /**
    * 主人脱战时回收
    * int masterRemove
    * 
    */
    public int getMasterRemove(){
        return masterRemove;
    }

    /**
    * 主人死亡时回收
    * int masterDieRemove
    * 
    */
    public int getMasterDieRemove(){
        return masterDieRemove;
    }


}
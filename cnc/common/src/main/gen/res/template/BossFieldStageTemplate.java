package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="B_BOSS战场配置表.xlsx", node="boss_field_stage.xml")
public class BossFieldStageTemplate implements IResTemplate  {

    /**
    * 序号
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 阶段
    * int stage
    * 
    */
    @ResAttribute("stage")
    private  int stage;

    /**
    * 阶段启动进入条件事件ID
(对应boss_field_event表事件ID)
    * int enterEventId
    * 
    */
    @ResAttribute("enterEventId")
    private  int enterEventId;



    /**
    * 序号
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 阶段
    * int stage
    * 
    */
    public int getStage(){
        return stage;
    }

    /**
    * 阶段启动进入条件事件ID
(对应boss_field_event表事件ID)
    * int enterEventId
    * 
    */
    public int getEnterEventId(){
        return enterEventId;
    }


}
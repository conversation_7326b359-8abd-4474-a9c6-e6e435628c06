package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_英雄表_New.xlsx", node="hero_level_rh.xml")
public class HeroLevelRhTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 英雄ID
    * int hero_id
    * 
    */
    @ResAttribute("hero_id")
    private  int hero_id;

    /**
    * 等级
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;

    /**
    * 消耗（货币）
    * pair cost
    * 
    */
    @ResAttribute("cost")
    private IntPairType costPair;

    /**
    * 需求建筑等级
    * pair require_building_level
    * 
    */
    @ResAttribute("require_building_level")
    private IntPairType require_building_levelPair;

    /**
    * rts带兵量
    * int rts_troop_capacity
    * 
    */
    @ResAttribute("rts_troop_capacity")
    private  int rts_troop_capacity;

    /**
    * slg带兵量
    * int slg_troop_capacity
    * 
    */
    @ResAttribute("slg_troop_capacity")
    private  int slg_troop_capacity;

    /**
    * 战力增加值
    * int power
    * 
    */
    @ResAttribute("power")
    private  int power;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 英雄ID
    * int hero_id
    * 
    */
    public int getHeroId(){
        return hero_id;
    }

    /**
    * 等级
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }


    /**
    * 消耗（货币）
    * pair cost
    * 
    */
    public IntPairType getCostPair(){
        return costPair;
    }

    /**
    * 需求建筑等级
    * pair require_building_level
    * 
    */
    public IntPairType getRequireBuildingLevelPair(){
        return require_building_levelPair;
    }
    /**
    * rts带兵量
    * int rts_troop_capacity
    * 
    */
    public int getRtsTroopCapacity(){
        return rts_troop_capacity;
    }

    /**
    * slg带兵量
    * int slg_troop_capacity
    * 
    */
    public int getSlgTroopCapacity(){
        return slg_troop_capacity;
    }

    /**
    * 战力增加值
    * int power
    * 
    */
    public int getPower(){
        return power;
    }


}
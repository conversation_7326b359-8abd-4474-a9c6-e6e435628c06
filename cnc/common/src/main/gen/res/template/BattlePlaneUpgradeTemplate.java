package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="K_空军.xlsx", node="battle_plane_upgrade.xml")
public class BattlePlaneUpgradeTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 战机类型
    * int planeType
    * 
    */
    @ResAttribute("planeType")
    private  int planeType;

    /**
    * 后置等级
    * int postLevel
    * 
    */
    @ResAttribute("postLevel")
    private  int postLevel;

    /**
    * 消耗道具
    * pairarray needItem
    * 
    */
    @ResAttribute("needItem")
    private List<IntPairType> needItemPairList;

    /**
    * 技能ID
    * int skillId
    * 
    */
    @ResAttribute("skillId")
    private  int skillId;

    /**
    * 战斗力
    * int power
    * 
    */
    @ResAttribute("power")
    private  int power;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 战机类型
    * int planeType
    * 
    */
    public int getPlaneType(){
        return planeType;
    }

    /**
    * 后置等级
    * int postLevel
    * 
    */
    public int getPostLevel(){
        return postLevel;
    }


    /**
    * 消耗道具
    * pairarray needItem
    * 
    */
    public List<IntPairType> getNeedItemPairList(){
        return needItemPairList;
    }
    /**
    * 技能ID
    * int skillId
    * 
    */
    public int getSkillId(){
        return skillId;
    }

    /**
    * 战斗力
    * int power
    * 
    */
    public int getPower(){
        return power;
    }


}
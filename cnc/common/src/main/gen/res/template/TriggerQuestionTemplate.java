package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_游戏内问卷.xlsx", node="trigger_question.xml")
public class TriggerQuestionTemplate implements IResTemplate  {

    /**
    * 问卷id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 触发行为
    * CommonEnum.QuestTriggerType triggerType
    * 
    */
    @ResAttribute("triggerType")
    private CommonEnum.QuestTriggerType triggerType;

    /**
    * 行为参数
    * intarray triggerParameter
    * 
    */
    @ResAttribute("triggerParameter")
    private List<Integer> triggerParameterList;

    /**
    * 弹出延迟
    * int triggerDelay
    * 
    */
    @ResAttribute("triggerDelay")
    private  int triggerDelay;

    /**
    * 奖励道具
    * pairarray reward
    * 
    */
    @ResAttribute("reward")
    private List<IntPairType> rewardPairList;

    /**
    * 持续时长
    * int triggerTime
    * 
    */
    @ResAttribute("triggerTime")
    private  int triggerTime;

    /**
    * 问卷失效时间
    * int outOfOrderTime
    * 
    */
    @ResAttribute("outOfOrderTime")
    private  int outOfOrderTime;



    /**
    * 问卷id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 触发行为
    * CommonEnum.QuestTriggerType triggerType
    * 
    */
    public CommonEnum.QuestTriggerType getTriggerType(){
        return triggerType;
    }

    /**
    * 行为参数
    * intarray triggerParameter
    * 
    */
    public List<Integer> getTriggerParameterList(){
        return triggerParameterList;
    }

    /**
    * 弹出延迟
    * int triggerDelay
    * 
    */
    public int getTriggerDelay(){
        return triggerDelay;
    }


    /**
    * 奖励道具
    * pairarray reward
    * 
    */
    public List<IntPairType> getRewardPairList(){
        return rewardPairList;
    }
    /**
    * 持续时长
    * int triggerTime
    * 
    */
    public int getTriggerTime(){
        return triggerTime;
    }

    /**
    * 问卷失效时间
    * int outOfOrderTime
    * 
    */
    public int getOutOfOrderTime(){
        return outOfOrderTime;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_城建_RH.xlsx", node="build_upgrade_RH.xml")
public class BuildUpgradeRhTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 建筑id
    * int buildId
    * 
    */
    @ResAttribute("buildId")
    private  int buildId;

    /**
    * 建筑等级
    * int buildLevel
    * 
    */
    @ResAttribute("buildLevel")
    private  int buildLevel;

    /**
    * 前置条件
    * string unlockCondition
    * 
    */
    @ResAttribute("unlockCondition")
    private  String unlockCondition;

    /**
    * 当前等级属性加成效果
    * pairarray buffEffect
    * 
    */
    @ResAttribute("buffEffect")
    private List<IntPairType> buffEffectPairList;

    /**
    * 每级资源消耗
    * pairarray costResources
    * 
    */
    @ResAttribute("costResources")
    private List<IntPairType> costResourcesPairList;

    /**
    * 每级时间消耗
    * int costTime
    * 
    */
    @ResAttribute("costTime")
    private  int costTime;

    /**
    * 解锁编队槽位
    * pairarray squadUnlock
    * 
    */
    @ResAttribute("squadUnlock")
    private List<IntPairType> squadUnlockPairList;

    /**
    * 建筑位置（固定位）
    * pairarray fixCoordinate
    * 
    */
    @ResAttribute("fixCoordinate")
    private List<IntPairType> fixCoordinatePairList;

    /**
    * 英雄等级上限开放
    * int heroLevelMax
    * 
    */
    @ResAttribute("heroLevelMax")
    private  int heroLevelMax;

    /**
    * 兵种等级上限开放
    * int soldierLevelMax
    * 
    */
    @ResAttribute("soldierLevelMax")
    private  int soldierLevelMax;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 建筑id
    * int buildId
    * 
    */
    public int getBuildId(){
        return buildId;
    }

    /**
    * 建筑等级
    * int buildLevel
    * 
    */
    public int getBuildLevel(){
        return buildLevel;
    }

    /**
    * 前置条件
    * string unlockCondition
    * 
    */
    public String getUnlockCondition(){
        return unlockCondition;
    }

    /**
    * 当前等级属性加成效果
    * pairarray buffEffect
    * 
    */
    public List<IntPairType> getBuffEffectPairList(){
        return buffEffectPairList;
    }

    /**
    * 每级资源消耗
    * pairarray costResources
    * 
    */
    public List<IntPairType> getCostResourcesPairList(){
        return costResourcesPairList;
    }
    /**
    * 每级时间消耗
    * int costTime
    * 
    */
    public int getCostTime(){
        return costTime;
    }


    /**
    * 解锁编队槽位
    * pairarray squadUnlock
    * 
    */
    public List<IntPairType> getSquadUnlockPairList(){
        return squadUnlockPairList;
    }

    /**
    * 建筑位置（固定位）
    * pairarray fixCoordinate
    * 
    */
    public List<IntPairType> getFixCoordinatePairList(){
        return fixCoordinatePairList;
    }
    /**
    * 英雄等级上限开放
    * int heroLevelMax
    * 
    */
    public int getHeroLevelMax(){
        return heroLevelMax;
    }

    /**
    * 兵种等级上限开放
    * int soldierLevelMax
    * 
    */
    public int getSoldierLevelMax(){
        return soldierLevelMax;
    }


}
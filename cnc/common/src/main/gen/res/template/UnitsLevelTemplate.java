package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_单位配置表【RH】.xlsx", node="units_level.xml")
public class UnitsLevelTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 兵种ID
    * int UnitID
    * 
    */
    @ResAttribute("UnitID")
    private  int UnitID;

    /**
    * 等级
    * int Level
    * 
    */
    @ResAttribute("Level")
    private  int Level;

    /**
    * 经验值消耗
    * int ExpCost
    * 
    */
    @ResAttribute("ExpCost")
    private  int ExpCost;

    /**
    * 生命
    * int Health
    * 
    */
    @ResAttribute("Health")
    private  int Health;

    /**
    * 护甲
    * int Armor
    * 
    */
    @ResAttribute("Armor")
    private  int Armor;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 兵种ID
    * int UnitID
    * 
    */
    public int getUnitID(){
        return UnitID;
    }

    /**
    * 等级
    * int Level
    * 
    */
    public int getLevel(){
        return Level;
    }

    /**
    * 经验值消耗
    * int ExpCost
    * 
    */
    public int getExpCost(){
        return ExpCost;
    }

    /**
    * 生命
    * int Health
    * 
    */
    public int getHealth(){
        return Health;
    }

    /**
    * 护甲
    * int Armor
    * 
    */
    public int getArmor(){
        return Armor;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "Q_千人同屏.xlsx", node = "const_thousands_test.xml", isConst = true)
public class ConstThousandsTestTemplate implements IResConstTemplate  {

    /**
     * 被攻击的地图建筑的part id
     */
    private int TargetPartId = 0;
    /**
     * 机器人部队配置 [组id] 机器人会顺序轮流取列表的配置组建军队
     */
    private List<Integer> ArmyConfig;
    /**
     * 战斗区域 x,y,r
     */
    private List<Integer> BattleArea;


    public int getTargetPartId(){
        return this.TargetPartId;
    }

    public List<Integer> getArmyConfig(){
        return this.ArmyConfig;
    }

    public List<Integer> getBattleArea(){
        return this.BattleArea;
    }

    @Override
    public int getId() {
        return 0;
    }
}

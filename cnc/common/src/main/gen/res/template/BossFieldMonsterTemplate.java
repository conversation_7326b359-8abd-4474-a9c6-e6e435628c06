package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="B_BOSS战场配置表.xlsx", node="boss_field_monster.xml")
public class BossFieldMonsterTemplate implements IResTemplate  {

    /**
    * 刷新序号
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 怪物ID
    * int monsterId
    * 
    */
    @ResAttribute("monsterId")
    private  int monsterId;

    /**
    * 拾取物GroupId
    * int dropGroupId
    * 
    */
    @ResAttribute("dropGroupId")
    private  int dropGroupId;

    /**
    * 刷新区域
(对应boss_field表的怪物刷新区域的第几个，从序号1开始)
    * int area
    * 
    */
    @ResAttribute("area")
    private  int area;

    /**
    * 刷新坐标
（不填表示取区域）
    * pairarray position
    * 
    */
    @ResAttribute("position")
    private List<IntPairType> positionPairList;

    /**
    * 刷新组Id
（对应monster_refresh_group 刷新组ID）
    * int monsterGroupId
    * 
    */
    @ResAttribute("monsterGroupId")
    private  int monsterGroupId;

    /**
    * 销毁事件
    * intarray destroyEventId
    * 
    */
    @ResAttribute("destroyEventId")
    private List<Integer> destroyEventIdList;



    /**
    * 刷新序号
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 怪物ID
    * int monsterId
    * 
    */
    public int getMonsterId(){
        return monsterId;
    }

    /**
    * 拾取物GroupId
    * int dropGroupId
    * 
    */
    public int getDropGroupId(){
        return dropGroupId;
    }

    /**
    * 刷新区域
(对应boss_field表的怪物刷新区域的第几个，从序号1开始)
    * int area
    * 
    */
    public int getArea(){
        return area;
    }


    /**
    * 刷新坐标
（不填表示取区域）
    * pairarray position
    * 
    */
    public List<IntPairType> getPositionPairList(){
        return positionPairList;
    }
    /**
    * 刷新组Id
（对应monster_refresh_group 刷新组ID）
    * int monsterGroupId
    * 
    */
    public int getMonsterGroupId(){
        return monsterGroupId;
    }


    /**
    * 销毁事件
    * intarray destroyEventId
    * 
    */
    public List<Integer> getDestroyEventIdList(){
        return destroyEventIdList;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="S_士兵表_兵种.xlsx", node="soldier_buff.xml")
public class SoldierBuffTemplate implements IResTemplate  {

    /**
    * 兵种类型
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 攻击增加
    * int atkUp
    * 
    */
    @ResAttribute("atkUp")
    private  int atkUp;

    /**
    * 攻击减少
    * int atkDown
    * 
    */
    @ResAttribute("atkDown")
    private  int atkDown;

    /**
    * 防御增加
    * int defUp
    * 
    */
    @ResAttribute("defUp")
    private  int defUp;

    /**
    * 防御减少
    * int defDown
    * 
    */
    @ResAttribute("defDown")
    private  int defDown;

    /**
    * 血量增加
    * int hpUp
    * 
    */
    @ResAttribute("hpUp")
    private  int hpUp;

    /**
    * 血量减少
    * int hpDown
    * 
    */
    @ResAttribute("hpDown")
    private  int hpDown;



    /**
    * 兵种类型
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 攻击增加
    * int atkUp
    * 
    */
    public int getAtkUp(){
        return atkUp;
    }

    /**
    * 攻击减少
    * int atkDown
    * 
    */
    public int getAtkDown(){
        return atkDown;
    }

    /**
    * 防御增加
    * int defUp
    * 
    */
    public int getDefUp(){
        return defUp;
    }

    /**
    * 防御减少
    * int defDown
    * 
    */
    public int getDefDown(){
        return defDown;
    }

    /**
    * 血量增加
    * int hpUp
    * 
    */
    public int getHpUp(){
        return hpUp;
    }

    /**
    * 血量减少
    * int hpDown
    * 
    */
    public int getHpDown(){
        return hpDown;
    }


}
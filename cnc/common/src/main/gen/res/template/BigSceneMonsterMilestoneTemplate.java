package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_大世界野怪投放区域.xlsx", node="big_scene_monster_milestone.xml")
public class BigSceneMonsterMilestoneTemplate implements IResTemplate  {

    /**
    * 野怪里程碑阶段ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * kvk剧本id
    * int storyId
    * 
    */
    @ResAttribute("storyId")
    private  int storyId;

    /**
    * 野怪里程碑阶段ID
    * int milestoneId
    * 
    */
    @ResAttribute("milestoneId")
    private  int milestoneId;

    /**
    * 服务器时间范围右值(小时)
    * int timePeriod
    * 
    */
    @ResAttribute("timePeriod")
    private  int timePeriod;

    /**
    * 补刷野怪的等级上限
    * int addMonsterLevelMax
    * 
    */
    @ResAttribute("addMonsterLevelMax")
    private  int addMonsterLevelMax;

    /**
    * 野怪搜索的等级上限
    * int searchMonsterLevelMax
    * 
    */
    @ResAttribute("searchMonsterLevelMax")
    private  int searchMonsterLevelMax;

    /**
    * 野怪最小可搜索等级
    * int searchMonsterLevelMin
    * 
    */
    @ResAttribute("searchMonsterLevelMin")
    private  int searchMonsterLevelMin;



    /**
    * 野怪里程碑阶段ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * kvk剧本id
    * int storyId
    * 
    */
    public int getStoryId(){
        return storyId;
    }

    /**
    * 野怪里程碑阶段ID
    * int milestoneId
    * 
    */
    public int getMilestoneId(){
        return milestoneId;
    }

    /**
    * 服务器时间范围右值(小时)
    * int timePeriod
    * 
    */
    public int getTimePeriod(){
        return timePeriod;
    }

    /**
    * 补刷野怪的等级上限
    * int addMonsterLevelMax
    * 
    */
    public int getAddMonsterLevelMax(){
        return addMonsterLevelMax;
    }

    /**
    * 野怪搜索的等级上限
    * int searchMonsterLevelMax
    * 
    */
    public int getSearchMonsterLevelMax(){
        return searchMonsterLevelMax;
    }

    /**
    * 野怪最小可搜索等级
    * int searchMonsterLevelMin
    * 
    */
    public int getSearchMonsterLevelMin(){
        return searchMonsterLevelMin;
    }


}
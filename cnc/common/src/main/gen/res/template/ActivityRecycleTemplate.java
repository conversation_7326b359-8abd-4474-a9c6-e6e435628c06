package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动表_策划分表.xlsx", node="activity_recycle.xml")
public class ActivityRecycleTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 活动id
    * int activityId
    * 
    */
    @ResAttribute("activityId")
    private  int activityId;

    /**
    * 活动结束后回收道具id
    * pairarray recycleItem
    * 
    */
    @ResAttribute("recycleItem")
    private List<IntPairType> recycleItemPairList;

    /**
    * 补偿邮件id
    * int recycleMail
    * 
    */
    @ResAttribute("recycleMail")
    private  int recycleMail;

    /**
    * 补偿道具
    * pairarray compensateItem
    * 
    */
    @ResAttribute("compensateItem")
    private List<IntPairType> compensateItemPairList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 活动id
    * int activityId
    * 
    */
    public int getActivityId(){
        return activityId;
    }


    /**
    * 活动结束后回收道具id
    * pairarray recycleItem
    * 
    */
    public List<IntPairType> getRecycleItemPairList(){
        return recycleItemPairList;
    }
    /**
    * 补偿邮件id
    * int recycleMail
    * 
    */
    public int getRecycleMail(){
        return recycleMail;
    }


    /**
    * 补偿道具
    * pairarray compensateItem
    * 
    */
    public List<IntPairType> getCompensateItemPairList(){
        return compensateItemPairList;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_英雄表.xlsx", node="hero_star.xml")
public class HeroStarTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 英雄星阶
    * int star
    * 
    */
    @ResAttribute("star")
    private  int star;

    /**
    * 解锁等级
    * int unlockLevel
    * 
    */
    @ResAttribute("unlockLevel")
    private  int unlockLevel;

    /**
    * 等级上限
    * int maxLevel
    * 
    */
    @ResAttribute("maxLevel")
    private  int maxLevel;

    /**
    * 战力增加
    * int power
    * 
    */
    @ResAttribute("power")
    private  int power;

    /**
    * 部队容量
    * int capacity
    * 
    */
    @ResAttribute("capacity")
    private  int capacity;

    /**
    * 获得天赋点
    * int talentPoint
    * 
    */
    @ResAttribute("talentPoint")
    private  int talentPoint;

    /**
    * 解锁技能位
    * intarray skillUnlock
    * 
    */
    @ResAttribute("skillUnlock")
    private List<Integer> skillUnlockList;

    /**
    * 经验需求
    * pairarray exp
    * 
    */
    @ResAttribute("exp")
    private List<IntPairType> expPairList;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 英雄星阶
    * int star
    * 
    */
    public int getStar(){
        return star;
    }

    /**
    * 解锁等级
    * int unlockLevel
    * 
    */
    public int getUnlockLevel(){
        return unlockLevel;
    }

    /**
    * 等级上限
    * int maxLevel
    * 
    */
    public int getMaxLevel(){
        return maxLevel;
    }

    /**
    * 战力增加
    * int power
    * 
    */
    public int getPower(){
        return power;
    }

    /**
    * 部队容量
    * int capacity
    * 
    */
    public int getCapacity(){
        return capacity;
    }

    /**
    * 获得天赋点
    * int talentPoint
    * 
    */
    public int getTalentPoint(){
        return talentPoint;
    }


    /**
    * 解锁技能位
    * intarray skillUnlock
    * 
    */
    public List<Integer> getSkillUnlockList(){
        return skillUnlockList;
    }


    /**
    * 经验需求
    * pairarray exp
    * 
    */
    public List<IntPairType> getExpPairList(){
        return expPairList;
    }

}
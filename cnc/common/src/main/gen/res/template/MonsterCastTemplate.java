package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_野怪.xlsx", node="monster_cast.xml")
public class MonsterCastTemplate implements IResTemplate  {

    /**
    * 蓄力ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 蓄力值上限
    * int castValue
    * 
    */
    @ResAttribute("castValue")
    private  int castValue;

    /**
    * 速率
    * int castRate
    * 
    */
    @ResAttribute("castRate")
    private  int castRate;

    /**
    * 增益类型
    * CommonEnum.MonsterCastAddition additionType
    * 
    */
    @ResAttribute("additionType")
    private CommonEnum.MonsterCastAddition additionType;

    /**
    * 增益参数
    * string params
    * 
    */
    @ResAttribute("params")
    private  String params;



    /**
    * 蓄力ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 蓄力值上限
    * int castValue
    * 
    */
    public int getCastValue(){
        return castValue;
    }

    /**
    * 速率
    * int castRate
    * 
    */
    public int getCastRate(){
        return castRate;
    }


    /**
    * 增益类型
    * CommonEnum.MonsterCastAddition additionType
    * 
    */
    public CommonEnum.MonsterCastAddition getAdditionType(){
        return additionType;
    }
    /**
    * 增益参数
    * string params
    * 
    */
    public String getParams(){
        return params;
    }

}
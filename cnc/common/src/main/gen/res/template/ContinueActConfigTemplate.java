package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_连续活动.xlsx", node="continue_act_config.xml")
public class ContinueActConfigTemplate implements IResTemplate  {

    /**
    * 活动id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 免费奖池
    * intarray freePool
    * 
    */
    @ResAttribute("freePool")
    private List<Integer> freePoolList;

    /**
    * 付费奖池
    * intarray payPool
    * 
    */
    @ResAttribute("payPool")
    private List<Integer> payPoolList;

    /**
    * 付费奖池礼包id
    * int payGoodsId
    * 
    */
    @ResAttribute("payGoodsId")
    private  int payGoodsId;

    /**
    * 升级类型
    * CommonEnum.ContinueActLevelUpType levelUpType
    * 
    */
    @ResAttribute("levelUpType")
    private CommonEnum.ContinueActLevelUpType levelUpType;

    /**
    * 积分参数
    * int scoreParam
    * 
    */
    @ResAttribute("scoreParam")
    private  int scoreParam;

    /**
    * 邮件id
    * int mailId
    * 
    */
    @ResAttribute("mailId")
    private  int mailId;

    /**
    * 过期只发付费奖池
    * bool expireOnlyPay
    * 
    */
    @ResAttribute("expireOnlyPay")
    private  boolean expireOnlyPay;



    /**
    * 活动id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 免费奖池
    * intarray freePool
    * 
    */
    public List<Integer> getFreePoolList(){
        return freePoolList;
    }


    /**
    * 付费奖池
    * intarray payPool
    * 
    */
    public List<Integer> getPayPoolList(){
        return payPoolList;
    }

    /**
    * 付费奖池礼包id
    * int payGoodsId
    * 
    */
    public int getPayGoodsId(){
        return payGoodsId;
    }


    /**
    * 升级类型
    * CommonEnum.ContinueActLevelUpType levelUpType
    * 
    */
    public CommonEnum.ContinueActLevelUpType getLevelUpType(){
        return levelUpType;
    }
    /**
    * 积分参数
    * int scoreParam
    * 
    */
    public int getScoreParam(){
        return scoreParam;
    }

    /**
    * 邮件id
    * int mailId
    * 
    */
    public int getMailId(){
        return mailId;
    }


    /**
    * 过期只发付费奖池
    * bool expireOnlyPay
    * 
    */
    public boolean getExpireOnlyPay(){
        return expireOnlyPay;
    }

}
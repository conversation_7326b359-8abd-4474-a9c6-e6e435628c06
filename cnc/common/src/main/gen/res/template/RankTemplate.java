package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="P_排行榜.xlsx", node="rank.xml")
public class RankTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 排行榜类型
    * CommonEnum.RankType type
    * 
    */
    @ResAttribute("type")
    private CommonEnum.RankType type;

    /**
    * 排行榜范围：
1全服排行榜
2军团内排行榜
    * int scope
    * 
    */
    @ResAttribute("scope")
    private  int scope;

    /**
    * 单位
    * int member
    * 
    */
    @ResAttribute("member")
    private  int member;

    /**
    * 数据是否每周重置
    * bool reset
    * 
    */
    @ResAttribute("reset")
    private  boolean reset;

    /**
    * 显示个数
    * int show
    * 
    */
    @ResAttribute("show")
    private  int show;

    /**
    * 最多名次
    * int maxRank
    * 
    */
    @ResAttribute("maxRank")
    private  int maxRank;

    /**
    * 是否常驻原服
    * bool isResident
    * 
    */
    @ResAttribute("isResident")
    private  boolean isResident;

    /**
    * 上榜条件(主堡等级)
    * int rankOpen
    * 
    */
    @ResAttribute("rankOpen")
    private  int rankOpen;

    /**
    * 排行榜分数类型
    * CommonEnum.RankScoreType rankScoreType
    * 
    */
    @ResAttribute("rankScoreType")
    private CommonEnum.RankScoreType rankScoreType;

    /**
    * 限制移民玩家上榜
    * bool limitMigrate
    * 
    */
    @ResAttribute("limitMigrate")
    private  boolean limitMigrate;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 排行榜类型
    * CommonEnum.RankType type
    * 
    */
    public CommonEnum.RankType getType(){
        return type;
    }
    /**
    * 排行榜范围：
1全服排行榜
2军团内排行榜
    * int scope
    * 
    */
    public int getScope(){
        return scope;
    }

    /**
    * 单位
    * int member
    * 
    */
    public int getMember(){
        return member;
    }


    /**
    * 数据是否每周重置
    * bool reset
    * 
    */
    public boolean getReset(){
        return reset;
    }
    /**
    * 显示个数
    * int show
    * 
    */
    public int getShow(){
        return show;
    }

    /**
    * 最多名次
    * int maxRank
    * 
    */
    public int getMaxRank(){
        return maxRank;
    }


    /**
    * 是否常驻原服
    * bool isResident
    * 
    */
    public boolean getIsResident(){
        return isResident;
    }
    /**
    * 上榜条件(主堡等级)
    * int rankOpen
    * 
    */
    public int getRankOpen(){
        return rankOpen;
    }


    /**
    * 排行榜分数类型
    * CommonEnum.RankScoreType rankScoreType
    * 
    */
    public CommonEnum.RankScoreType getRankScoreType(){
        return rankScoreType;
    }

    /**
    * 限制移民玩家上榜
    * bool limitMigrate
    * 
    */
    public boolean getLimitMigrate(){
        return limitMigrate;
    }

}
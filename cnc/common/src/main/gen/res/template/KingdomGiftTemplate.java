package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="W_王国管理.xlsx", node="kingdom_gift.xml")
public class KingdomGiftTemplate implements IResTemplate  {

    /**
    * 王国恩赐ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 王国恩赐道具奖励
    * pairarray item
    * 
    */
    @ResAttribute("item")
    private List<IntPairType> itemPairList;

    /**
    * 王国恩赐发放次数上限
    * int buffTime
    * 
    */
    @ResAttribute("buffTime")
    private  int buffTime;

    /**
    * 王国恩赐奖励邮件ID
    * int mail
    * 
    */
    @ResAttribute("mail")
    private  int mail;



    /**
    * 王国恩赐ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 王国恩赐道具奖励
    * pairarray item
    * 
    */
    public List<IntPairType> getItemPairList(){
        return itemPairList;
    }
    /**
    * 王国恩赐发放次数上限
    * int buffTime
    * 
    */
    public int getBuffTime(){
        return buffTime;
    }

    /**
    * 王国恩赐奖励邮件ID
    * int mail
    * 
    */
    public int getMail(){
        return mail;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_资源田.xlsx", node="resource_refresh.xml")
public class ResourceRefreshTemplate implements IResTemplate  {

    /**
    * 标识id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 剧本id
    * int storyId
    * 
    */
    @ResAttribute("storyId")
    private  int storyId;

    /**
    * 州id
    * int region
    * 
    */
    @ResAttribute("region")
    private  int region;

    /**
    * 片等级下限
    * int levelMin
    * 
    */
    @ResAttribute("levelMin")
    private  int levelMin;

    /**
    * 片等级上限
    * int levelMax
    * 
    */
    @ResAttribute("levelMax")
    private  int levelMax;

    /**
    * 资源种类权重比
    * pairarray typeWeight
    * 
    */
    @ResAttribute("typeWeight")
    private List<IntPairType> typeWeightPairList;

    /**
    * 资源带等级权重比
    * pairarray levelWeight
    * 
    */
    @ResAttribute("levelWeight")
    private List<IntPairType> levelWeightPairList;



    /**
    * 标识id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 剧本id
    * int storyId
    * 
    */
    public int getStoryId(){
        return storyId;
    }

    /**
    * 州id
    * int region
    * 
    */
    public int getRegion(){
        return region;
    }

    /**
    * 片等级下限
    * int levelMin
    * 
    */
    public int getLevelMin(){
        return levelMin;
    }

    /**
    * 片等级上限
    * int levelMax
    * 
    */
    public int getLevelMax(){
        return levelMax;
    }


    /**
    * 资源种类权重比
    * pairarray typeWeight
    * 
    */
    public List<IntPairType> getTypeWeightPairList(){
        return typeWeightPairList;
    }

    /**
    * 资源带等级权重比
    * pairarray levelWeight
    * 
    */
    public List<IntPairType> getLevelWeightPairList(){
        return levelWeightPairList;
    }

}
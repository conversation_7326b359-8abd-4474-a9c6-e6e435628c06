package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="B_BOSS战场配置表.xlsx", node="boss_field_event.xml")
public class BossFieldEventTemplate implements IResTemplate  {

    /**
    * 事件ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 事件类型
    * CommonEnum.BossFieldEventType type
    * 
    */
    @ResAttribute("type")
    private CommonEnum.BossFieldEventType type;

    /**
    * 要求计数
    * int num
    * 
    */
    @ResAttribute("num")
    private  int num;

    /**
    * 怪物id
    * int monsterId
    * 
    */
    @ResAttribute("monsterId")
    private  int monsterId;

    /**
    * 触发的延迟事件
（事件Id_延迟时间/S）
（支持多个）
    * pairarray delayEventId
    * 
    */
    @ResAttribute("delayEventId")
    private List<IntPairType> delayEventIdPairList;

    /**
    * 触发后刷新序号
（对应boss_field_monster表的刷新序号ID）
    * intarray monsterRefreshId
    * 
    */
    @ResAttribute("monsterRefreshId")
    private List<Integer> monsterRefreshIdList;

    /**
    * 播放表现ID
    * int playShowId
    * 
    */
    @ResAttribute("playShowId")
    private  int playShowId;



    /**
    * 事件ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 事件类型
    * CommonEnum.BossFieldEventType type
    * 
    */
    public CommonEnum.BossFieldEventType getType(){
        return type;
    }
    /**
    * 要求计数
    * int num
    * 
    */
    public int getNum(){
        return num;
    }

    /**
    * 怪物id
    * int monsterId
    * 
    */
    public int getMonsterId(){
        return monsterId;
    }


    /**
    * 触发的延迟事件
（事件Id_延迟时间/S）
（支持多个）
    * pairarray delayEventId
    * 
    */
    public List<IntPairType> getDelayEventIdPairList(){
        return delayEventIdPairList;
    }

    /**
    * 触发后刷新序号
（对应boss_field_monster表的刷新序号ID）
    * intarray monsterRefreshId
    * 
    */
    public List<Integer> getMonsterRefreshIdList(){
        return monsterRefreshIdList;
    }

    /**
    * 播放表现ID
    * int playShowId
    * 
    */
    public int getPlayShowId(){
        return playShowId;
    }


}
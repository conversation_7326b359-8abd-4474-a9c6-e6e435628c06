package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="S_设置表.xlsx", node="spassword_check.xml")
public class SpasswordCheckTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 类型
    * CommonEnum.SPassWordCheckType type
    * 
    */
    @ResAttribute("type")
    private CommonEnum.SPassWordCheckType type;

    /**
    * 是否开启校验
    * bool needCheck
    * 
    */
    @ResAttribute("needCheck")
    private  boolean needCheck;

    /**
    * 参数
    * intarray param
    * 
    */
    @ResAttribute("param")
    private List<Integer> paramList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 类型
    * CommonEnum.SPassWordCheckType type
    * 
    */
    public CommonEnum.SPassWordCheckType getType(){
        return type;
    }

    /**
    * 是否开启校验
    * bool needCheck
    * 
    */
    public boolean getNeedCheck(){
        return needCheck;
    }

    /**
    * 参数
    * intarray param
    * 
    */
    public List<Integer> getParamList(){
        return paramList;
    }


}
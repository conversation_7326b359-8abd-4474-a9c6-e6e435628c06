package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_支付.xlsx", node="radom_goods_marquee.xml")
public class RadomGoodsMarqueeTemplate implements IResTemplate  {

    /**
    * 礼包ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 特殊道具
    * pairarray spItem
    * 
    */
    @ResAttribute("spItem")
    private List<IntPairType> spItemPairList;

    /**
    * 跑马灯ID
    * int marquee
    * 
    */
    @ResAttribute("marquee")
    private  int marquee;



    /**
    * 礼包ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 特殊道具
    * pairarray spItem
    * 
    */
    public List<IntPairType> getSpItemPairList(){
        return spItemPairList;
    }
    /**
    * 跑马灯ID
    * int marquee
    * 
    */
    public int getMarquee(){
        return marquee;
    }


}
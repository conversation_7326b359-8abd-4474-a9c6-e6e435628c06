package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "M_每日特惠.xlsx", node = "const_daily_pack.xml", isConst = true)
public class ConstDailyPackTemplate implements IResConstTemplate  {

    /**
     * 礼包档位（除合集外）
     */
    private List<Integer> packLevels;
    /**
     * 礼包合集
     */
    private int collectionPackLevel = 0;
    /**
     * 英雄何时开始展示（天）
     */
    private int heroShowDay = 0;
    /**
     * 每日特惠免费奖励
     */
    private List<IntPairType> dailyFreeReward;
    /**
     * 创角后就可购买的英雄ID
     */
    private int primaryHeroId = 0;
    /**
     * 开出英雄碎片的箱子的概率展示（小数点后保留几位）
     */
    private int probabilityShowRule = 0;
    /**
     * 超值每日礼包档位（除合集外）
     */
    private List<Integer> superpackLevels;
    /**
     * 超值每日礼包合集
     */
    private int collectionsuperPackLevel = 0;


    public List<Integer> getPackLevels(){
        return this.packLevels;
    }

    public int getCollectionPackLevel(){
        return this.collectionPackLevel;
    }

    public int getHeroShowDay(){
        return this.heroShowDay;
    }

    public List<IntPairType> getDailyFreeReward(){
        return this.dailyFreeReward;
    }

    public int getPrimaryHeroId(){
        return this.primaryHeroId;
    }

    public int getProbabilityShowRule(){
        return this.probabilityShowRule;
    }

    public List<Integer> getSuperpackLevels(){
        return this.superpackLevels;
    }

    public int getCollectionsuperPackLevel(){
        return this.collectionsuperPackLevel;
    }

    @Override
    public int getId() {
        return 0;
    }
}

package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_英雄表.xlsx", node="hero.xml")
public class HeroTemplate implements IResTemplate  {

    /**
    * 英雄ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 天赋
    * intarray gift
    * 
    */
    @ResAttribute("gift")
    private List<Integer> giftList;

    /**
    * 英雄碎片
    * int heroChip
    * 
    */
    @ResAttribute("heroChip")
    private  int heroChip;

    /**
    * 稀有度
    * CommonEnum.HeroQuality rarity
    * 
    */
    @ResAttribute("rarity")
    private CommonEnum.HeroQuality rarity;

    /**
    * 招募所需数量
    * int unLockNum
    * 
    */
    @ResAttribute("unLockNum")
    private  int unLockNum;

    /**
    * 英雄碎片兑换道具
    * int generalHeroChip
    * 
    */
    @ResAttribute("generalHeroChip")
    private  int generalHeroChip;

    /**
    * 英雄碎片兑换比例
    * pair exchangeRate
    * 
    */
    @ResAttribute("exchangeRate")
    private IntPairType exchangeRatePair;

    /**
    * 开放时间
    * date openTime
    * 
    */
    @ResAttribute("openTime")
    private Date openTimeDt;
    /**
    * 版本内投放的英雄
0为投放，1为特殊英雄不投放
    * int special
    * 
    */
    @ResAttribute("special")
    private  int special;

    /**
    * 主动技
    * int activeSkill
    * 
    */
    @ResAttribute("activeSkill")
    private  int activeSkill;

    /**
    * 被动技能
    * intarray passiveSkill
    * 
    */
    @ResAttribute("passiveSkill")
    private List<Integer> passiveSkillList;

    /**
    * 觉醒技能
    * int intensiveSkill
    * 
    */
    @ResAttribute("intensiveSkill")
    private  int intensiveSkill;

    /**
    * 英雄名称
    * language name
    * 
    */
    @ResAttribute("name")
    private  String name;

    /**
    * 首次获得完整英雄时获得对应的指挥官头像框ID
    * int getAvartarId
    * 
    */
    @ResAttribute("getAvartarId")
    private  int getAvartarId;

    /**
    * 英雄关联兵种
    * int RelatedUnit
    * 
    */
    @ResAttribute("RelatedUnit")
    private  int RelatedUnit;

    /**
    * 英雄关联战斗本体
    * int RelatedHero
    * 
    */
    @ResAttribute("RelatedHero")
    private  int RelatedHero;

    /**
    * 攻击加成系数
    * int atkBuffRate
    * 
    */
    @ResAttribute("atkBuffRate")
    private  int atkBuffRate;

    /**
    * 护甲加成系数
    * int defBuffRate
    * 
    */
    @ResAttribute("defBuffRate")
    private  int defBuffRate;

    /**
    * 生命值加成系数
    * int healthBuffRate
    * 
    */
    @ResAttribute("healthBuffRate")
    private  int healthBuffRate;



    /**
    * 英雄ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 天赋
    * intarray gift
    * 
    */
    public List<Integer> getGiftList(){
        return giftList;
    }

    /**
    * 英雄碎片
    * int heroChip
    * 
    */
    public int getHeroChip(){
        return heroChip;
    }


    /**
    * 稀有度
    * CommonEnum.HeroQuality rarity
    * 
    */
    public CommonEnum.HeroQuality getRarity(){
        return rarity;
    }
    /**
    * 招募所需数量
    * int unLockNum
    * 
    */
    public int getUnLockNum(){
        return unLockNum;
    }

    /**
    * 英雄碎片兑换道具
    * int generalHeroChip
    * 
    */
    public int getGeneralHeroChip(){
        return generalHeroChip;
    }


    /**
    * 英雄碎片兑换比例
    * pair exchangeRate
    * 
    */
    public IntPairType getExchangeRatePair(){
        return exchangeRatePair;
    }

    /**
    * 开放时间
    * date openTime
    * 
    */
    public Date getOpenTimeDt(){
        return openTimeDt;
    }
    /**
    * 版本内投放的英雄
0为投放，1为特殊英雄不投放
    * int special
    * 
    */
    public int getSpecial(){
        return special;
    }

    /**
    * 主动技
    * int activeSkill
    * 
    */
    public int getActiveSkill(){
        return activeSkill;
    }


    /**
    * 被动技能
    * intarray passiveSkill
    * 
    */
    public List<Integer> getPassiveSkillList(){
        return passiveSkillList;
    }

    /**
    * 觉醒技能
    * int intensiveSkill
    * 
    */
    public int getIntensiveSkill(){
        return intensiveSkill;
    }

    /**
    * 英雄名称
    * language name
    * 
    */
    public String getName(){
        return name;
    }
    /**
    * 首次获得完整英雄时获得对应的指挥官头像框ID
    * int getAvartarId
    * 
    */
    public int getGetAvartarId(){
        return getAvartarId;
    }

    /**
    * 英雄关联兵种
    * int RelatedUnit
    * 
    */
    public int getRelatedUnit(){
        return RelatedUnit;
    }

    /**
    * 英雄关联战斗本体
    * int RelatedHero
    * 
    */
    public int getRelatedHero(){
        return RelatedHero;
    }

    /**
    * 攻击加成系数
    * int atkBuffRate
    * 
    */
    public int getAtkBuffRate(){
        return atkBuffRate;
    }

    /**
    * 护甲加成系数
    * int defBuffRate
    * 
    */
    public int getDefBuffRate(){
        return defBuffRate;
    }

    /**
    * 生命值加成系数
    * int healthBuffRate
    * 
    */
    public int getHealthBuffRate(){
        return healthBuffRate;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_战斗技能.xlsx", node="buff_config.xml")
public class BuffConfigTemplate implements IResTemplate  {

    /**
    * BuffID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 单位，技能名
    * string name
    * 
    */
    @ResAttribute("name")
    private  String name;

    /**
    * 特效
    * string fx
    * 
    */
    @ResAttribute("fx")
    private  String fx;

    /**
    * buff挂载目标
    * string target
    * 
    */
    @ResAttribute("target")
    private  String target;

    /**
    * buff挂载概率
    * int apply_chance
    * 
    */
    @ResAttribute("apply_chance")
    private  int apply_chance;

    /**
    * 效果名
    * string effect_name
    * 
    */
    @ResAttribute("effect_name")
    private  String effect_name;

    /**
    * 效果参数0
    * int effect_value0
    * 
    */
    @ResAttribute("effect_value0")
    private  int effect_value0;

    /**
    * 效果参数1
    * int v
    * 
    */
    @ResAttribute("v")
    private  int v;

    /**
    * 效果生效概率
    * int chance
    * 
    */
    @ResAttribute("chance")
    private  int chance;



    /**
    * BuffID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 单位，技能名
    * string name
    * 
    */
    public String getName(){
        return name;
    }
    /**
    * 特效
    * string fx
    * 
    */
    public String getFx(){
        return fx;
    }
    /**
    * buff挂载目标
    * string target
    * 
    */
    public String getTarget(){
        return target;
    }
    /**
    * buff挂载概率
    * int apply_chance
    * 
    */
    public int getApplyChance(){
        return apply_chance;
    }

    /**
    * 效果名
    * string effect_name
    * 
    */
    public String getEffectName(){
        return effect_name;
    }
    /**
    * 效果参数0
    * int effect_value0
    * 
    */
    public int getEffectValue0(){
        return effect_value0;
    }

    /**
    * 效果参数1
    * int v
    * 
    */
    public int getV(){
        return v;
    }

    /**
    * 效果生效概率
    * int chance
    * 
    */
    public int getChance(){
        return chance;
    }


}
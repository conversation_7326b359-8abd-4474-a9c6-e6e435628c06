package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="N_NPC.xlsx", node="npc_city.xml")
public class NpcCityTemplate implements IResTemplate  {

    /**
    * 主堡id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 等级
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;

    /**
    * 阵营
    * CommonEnum.Camp camp
    * 
    */
    @ResAttribute("camp")
    private CommonEnum.Camp camp;

    /**
    * 守城兵力上限
    * int garrisonMax
    * 
    */
    @ResAttribute("garrisonMax")
    private  int garrisonMax;

    /**
    * 兵力
    * pairarray soldier
    * 
    */
    @ResAttribute("soldier")
    private List<IntPairType> soldierPairList;

    /**
    * 英雄
    * triplearray hero
    * 
    */
    @ResAttribute("hero")
    private List<IntTripleType> heroTripleList;

    /**
    * 警戒塔
    * int guardTowerId
    * 
    */
    @ResAttribute("guardTowerId")
    private  int guardTowerId;

    /**
    * 援助上限
    * int assistNumMax
    * 
    */
    @ResAttribute("assistNumMax")
    private  int assistNumMax;



    /**
    * 主堡id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 等级
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }


    /**
    * 阵营
    * CommonEnum.Camp camp
    * 
    */
    public CommonEnum.Camp getCamp(){
        return camp;
    }
    /**
    * 守城兵力上限
    * int garrisonMax
    * 
    */
    public int getGarrisonMax(){
        return garrisonMax;
    }


    /**
    * 兵力
    * pairarray soldier
    * 
    */
    public List<IntPairType> getSoldierPairList(){
        return soldierPairList;
    }

    /**
    * 英雄
    * triplearray hero
    * 
    */
    public List<IntTripleType> getHeroTripleList(){
        return heroTripleList;
    }       
    /**
    * 警戒塔
    * int guardTowerId
    * 
    */
    public int getGuardTowerId(){
        return guardTowerId;
    }

    /**
    * 援助上限
    * int assistNumMax
    * 
    */
    public int getAssistNumMax(){
        return assistNumMax;
    }


}
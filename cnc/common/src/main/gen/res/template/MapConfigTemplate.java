package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_地图配置.xlsx", node="map_config.xml")
public class MapConfigTemplate implements IResTemplate  {

    /**
    * 地图ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 场景类型(1大世界，2副本 3PVP，4内城，0登录，5日落峡谷,10新可战斗内城)
    * int type
    * 
    */
    @ResAttribute("type")
    private  int type;

    /**
    * 重伤比类型
    * int damageRatioType
    * 
    */
    @ResAttribute("damageRatioType")
    private  int damageRatioType;

    /**
    * 地图宽度(可行走)
    * int width
    * 
    */
    @ResAttribute("width")
    private  int width;

    /**
    * 地图长度(可行走)
    * int length
    * 
    */
    @ResAttribute("length")
    private  int length;

    /**
    * 视野切分单轴个数
    * int aoiGridNum
    * 
    */
    @ResAttribute("aoiGridNum")
    private  int aoiGridNum;

    /**
    * 是否可空运
    * bool transport
    * 
    */
    @ResAttribute("transport")
    private  boolean transport;

    /**
    * 内城金矿采集区枚举2
    * triplearray innerCityGold
    * 
    */
    @ResAttribute("innerCityGold")
    private List<IntTripleType> innerCityGoldTripleList;

    /**
    * 内城石油井枚举1
    * triplearray innerCityOil
    * 
    */
    @ResAttribute("innerCityOil")
    private List<IntTripleType> innerCityOilTripleList;



    /**
    * 地图ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 场景类型(1大世界，2副本 3PVP，4内城，0登录，5日落峡谷,10新可战斗内城)
    * int type
    * 
    */
    public int getType(){
        return type;
    }

    /**
    * 重伤比类型
    * int damageRatioType
    * 
    */
    public int getDamageRatioType(){
        return damageRatioType;
    }

    /**
    * 地图宽度(可行走)
    * int width
    * 
    */
    public int getWidth(){
        return width;
    }

    /**
    * 地图长度(可行走)
    * int length
    * 
    */
    public int getLength(){
        return length;
    }

    /**
    * 视野切分单轴个数
    * int aoiGridNum
    * 
    */
    public int getAoiGridNum(){
        return aoiGridNum;
    }


    /**
    * 是否可空运
    * bool transport
    * 
    */
    public boolean getTransport(){
        return transport;
    }

    /**
    * 内城金矿采集区枚举2
    * triplearray innerCityGold
    * 
    */
    public List<IntTripleType> getInnerCityGoldTripleList(){
        return innerCityGoldTripleList;
    }       

    /**
    * 内城石油井枚举1
    * triplearray innerCityOil
    * 
    */
    public List<IntTripleType> getInnerCityOilTripleList(){
        return innerCityOilTripleList;
    }       

}
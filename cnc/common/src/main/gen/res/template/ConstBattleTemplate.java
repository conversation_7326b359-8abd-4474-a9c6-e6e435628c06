package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "Z_战斗常量表.xlsx", node = "const_battle.xml", isConst = true)
public class ConstBattleTemplate implements IResConstTemplate  {

    /**
     * 攻城出战兵力上限
     */
    private int CityBattleMaxSoldier = 0;
    /**
     * 攻城战后特效显示时间
     */
    private int CityBattleEndEffectTime = 0;
    /**
     * 基础反击伤害倍率 （基于普通攻击）
     */
    private float AtkBackRatio = 0.0f;
    /**
     * 防御率等级系数
     */
    private float DefenseLevelFactor = 0.0f;
    /**
     * 防御率基础系数
     */
    private int DefenseBaseFactor = 0;
    /**
     * 普通攻击伤害倍率
     */
    private float OrdinaryAtkBaseRatio = 0.0f;
    /**
     * 部队数量计算次方（分母）
     */
    private float SoldierCountFactor = 0.0f;
    /**
     * 技能伤害倍率
     */
    private float SkillRatio = 0.0f;
    /**
     * 相碰触发距离
     */
    private int Distance = 0;
    /**
     * 离开战场冷却的tick数量
     */
    private int MaxTickTimes = 0;
    /**
     * 副将将在主将发动轮次后第几轮发动技能
     */
    private int TriggerRound = 0;
    /**
     * 每次提升怒气值
     */
    private int AngerInterval = 0;
    /**
     * 治疗修正系数
     */
    private float TreatmentBaseRatio = 0.0f;
    /**
     * 战败部队移动速度
     */
    private int ArmyRetreatSpeed = 0;
    /**
     * 护盾修正系数
     */
    private float ShieldBaseRatio = 0.0f;
    /**
     * 寻路阻挡圈额外半径
     */
    private int RoutingCircle = 0;
    /**
     * 掠夺资源损失比例
     */
    private float PlunderRatio = 0.0f;
    /**
     * 玩家每日掠夺上限
     */
    private int PlunderLimit = 0;
    /**
     * 防御塔耐久度每秒回复
     */
    private float DefenseTowerHpRecover = 0.0f;
    /**
     * 防御塔承伤修正系数
     */
    private int DefenseTowerHitParam = 0;
    /**
     * 采集车承伤修正系数
     */
    private int ResourceVehicleHitParam = 0;
    /**
     * 标准部队模型圈半径（cm)
     */
    private int BaseModelRadius = 0;
    /**
     * 战损战力修正系数
     */
    private float WoundPowerParam = 0.0f;
    /**
     * 普通攻击配表基础回怒
     */
    private int DirectAtkRageRecover = 0;
    /**
     * 反击攻击配表基础回怒
     */
    private int AntiAtkRageRecover = 0;
    /**
     * 单回合回复的怒气上限
     */
    private int RageRecoverLimit = 0;
    /**
     * 默认战损率配置（重伤率，死兵率）
     */
    private List<Float> DefaultWoundParam;
    /**
     * 行军速度加成最小值
     */
    private float marchSpeedUpMin = 0.0f;
    /**
     * 行军速度加成最大值
     */
    private float marchSpeedUpMax = 0.0f;
    /**
     * 兵力修正系数（幂）
     */
    private float SoldierCountParam = 0.0f;
    /**
     * 战斗规模系数调整参数
     */
    private int BattleScaleFactor = 0;
    /**
     * 战斗规模系数调整参数2
     */
    private float BattleScaleFactor2 = 0.0f;
    /**
     * 普通攻击伤害倍率
     */
    private float BaseAtkFactor = 0.0f;
    /**
     * 反击伤害倍率
     */
    private float AntiAtkFactor = 0.0f;
    /**
     * 重伤修正系数
     */
    private float WoundFactor = 0.0f;
    /**
     * 特殊重伤率的战力边界（倍）
     */
    private float WoundPowerLimit = 0.0f;
    /**
     * 劣势方普攻/反击额外回怒
     */
    private int DisadvantageRageRecover = 0;
    /**
     * 战报邮件id（打架）
     */
    private int BattleReportMailId = 0;
    /**
     * 战报兵力折线图每X回合记点
     */
    private int BattleReportPointGapNum = 0;
    /**
     * 战报兵力折线图分段回合数
     */
    private int BattleReportPointSegmentNum = 0;
    /**
     * 战报邮件id（打野）
     */
    private int MonsterBattleReportMailId = 0;
    /**
     * 防御塔最大耐久调整系数（幂）
     */
    private float GuardTowerMaxHpFactor = 0.0f;
    /**
     * 战斗规模系数调整参数3（上限值）
     */
    private float BattleScaleFactor3 = 0.0f;
    /**
     * 选中目标部队时的优先级
     */
    private int SelectEnemyPriority = 0;
    /**
     * 选中目标部队时的优先级
     */
    private int SelectNeutralPriority = 0;
    /**
     * 选中目标部队时的优先级
     */
    private int SelectFriendlyPriority = 0;
    /**
     * 精准选中距离
     */
    private float AccurateSelectionDis = 0.0f;
    /**
     * 选中目标部队时的优先级
     */
    private int SelectSelfPriority = 0;
    /**
     * 选中目标部队时的优先级
     */
    private int SelectSameCountryPriority = 0;
    /**
     * 每日无消耗AOE打野怪获得奖励次数
     */
    private int FreeHitMonster = 0;


    public int getCityBattleMaxSoldier(){
        return this.CityBattleMaxSoldier;
    }

    public int getCityBattleEndEffectTime(){
        return this.CityBattleEndEffectTime;
    }

    public float getAtkBackRatio(){
        return this.AtkBackRatio;
    }        

    public float getDefenseLevelFactor(){
        return this.DefenseLevelFactor;
    }        

    public int getDefenseBaseFactor(){
        return this.DefenseBaseFactor;
    }

    public float getOrdinaryAtkBaseRatio(){
        return this.OrdinaryAtkBaseRatio;
    }        

    public float getSoldierCountFactor(){
        return this.SoldierCountFactor;
    }        

    public float getSkillRatio(){
        return this.SkillRatio;
    }        

    public int getDistance(){
        return this.Distance;
    }

    public int getMaxTickTimes(){
        return this.MaxTickTimes;
    }

    public int getTriggerRound(){
        return this.TriggerRound;
    }

    public int getAngerInterval(){
        return this.AngerInterval;
    }

    public float getTreatmentBaseRatio(){
        return this.TreatmentBaseRatio;
    }        

    public int getArmyRetreatSpeed(){
        return this.ArmyRetreatSpeed;
    }

    public float getShieldBaseRatio(){
        return this.ShieldBaseRatio;
    }        

    public int getRoutingCircle(){
        return this.RoutingCircle;
    }

    public float getPlunderRatio(){
        return this.PlunderRatio;
    }        

    public int getPlunderLimit(){
        return this.PlunderLimit;
    }

    public float getDefenseTowerHpRecover(){
        return this.DefenseTowerHpRecover;
    }        

    public int getDefenseTowerHitParam(){
        return this.DefenseTowerHitParam;
    }

    public int getResourceVehicleHitParam(){
        return this.ResourceVehicleHitParam;
    }

    public int getBaseModelRadius(){
        return this.BaseModelRadius;
    }

    public float getWoundPowerParam(){
        return this.WoundPowerParam;
    }        

    public int getDirectAtkRageRecover(){
        return this.DirectAtkRageRecover;
    }

    public int getAntiAtkRageRecover(){
        return this.AntiAtkRageRecover;
    }

    public int getRageRecoverLimit(){
        return this.RageRecoverLimit;
    }

    public List<Float> getDefaultWoundParam(){
        return this.DefaultWoundParam;
    }

    public float getMarchSpeedUpMin(){
        return this.marchSpeedUpMin;
    }        

    public float getMarchSpeedUpMax(){
        return this.marchSpeedUpMax;
    }        

    public float getSoldierCountParam(){
        return this.SoldierCountParam;
    }        

    public int getBattleScaleFactor(){
        return this.BattleScaleFactor;
    }

    public float getBattleScaleFactor2(){
        return this.BattleScaleFactor2;
    }        

    public float getBaseAtkFactor(){
        return this.BaseAtkFactor;
    }        

    public float getAntiAtkFactor(){
        return this.AntiAtkFactor;
    }        

    public float getWoundFactor(){
        return this.WoundFactor;
    }        

    public float getWoundPowerLimit(){
        return this.WoundPowerLimit;
    }        

    public int getDisadvantageRageRecover(){
        return this.DisadvantageRageRecover;
    }

    public int getBattleReportMailId(){
        return this.BattleReportMailId;
    }

    public int getBattleReportPointGapNum(){
        return this.BattleReportPointGapNum;
    }

    public int getBattleReportPointSegmentNum(){
        return this.BattleReportPointSegmentNum;
    }

    public int getMonsterBattleReportMailId(){
        return this.MonsterBattleReportMailId;
    }

    public float getGuardTowerMaxHpFactor(){
        return this.GuardTowerMaxHpFactor;
    }        

    public float getBattleScaleFactor3(){
        return this.BattleScaleFactor3;
    }        

    public int getSelectEnemyPriority(){
        return this.SelectEnemyPriority;
    }

    public int getSelectNeutralPriority(){
        return this.SelectNeutralPriority;
    }

    public int getSelectFriendlyPriority(){
        return this.SelectFriendlyPriority;
    }

    public float getAccurateSelectionDis(){
        return this.AccurateSelectionDis;
    }        

    public int getSelectSelfPriority(){
        return this.SelectSelfPriority;
    }

    public int getSelectSameCountryPriority(){
        return this.SelectSameCountryPriority;
    }

    public int getFreeHitMonster(){
        return this.FreeHitMonster;
    }

    @Override
    public int getId() {
        return 0;
    }
}

package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="R_任务目标参数.xlsx", node="goal_param.xml")
public class GoalParamTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 类型
    * string type
    * 
    */
    @ResAttribute("type")
    private  String type;

    /**
    * 目标参数
    * string params
    * 
    */
    @ResAttribute("params")
    private  String params;

    /**
    * 目标数值
    * int value
    * 
    */
    @ResAttribute("value")
    private  int value;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 类型
    * string type
    * 
    */
    public String getType(){
        return type;
    }
    /**
    * 目标参数
    * string params
    * 
    */
    public String getParams(){
        return params;
    }
    /**
    * 目标数值
    * int value
    * 
    */
    public int getValue(){
        return value;
    }


}
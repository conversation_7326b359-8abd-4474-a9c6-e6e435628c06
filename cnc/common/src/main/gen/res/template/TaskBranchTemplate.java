package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="R_任务.xlsx", node="task_branch.xml")
public class TaskBranchTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 任务组ID
    * int groupId
    * 
    */
    @ResAttribute("groupId")
    private  int groupId;

    /**
    * 任务组解锁条件
    * pairarray UnlockCondition
    * 
    */
    @ResAttribute("UnlockCondition")
    private List<IntPairType> UnlockConditionPairList;

    /**
    * 后置任务
    * int frontTask
    * 
    */
    @ResAttribute("frontTask")
    private  int frontTask;

    /**
    * 任务ID
    * int taskId
    * 
    */
    @ResAttribute("taskId")
    private  int taskId;

    /**
    * 任务奖励
    * pairarray reward
    * 
    */
    @ResAttribute("reward")
    private List<IntPairType> rewardPairList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 任务组ID
    * int groupId
    * 
    */
    public int getGroupId(){
        return groupId;
    }


    /**
    * 任务组解锁条件
    * pairarray UnlockCondition
    * 
    */
    public List<IntPairType> getUnlockConditionPairList(){
        return UnlockConditionPairList;
    }
    /**
    * 后置任务
    * int frontTask
    * 
    */
    public int getFrontTask(){
        return frontTask;
    }

    /**
    * 任务ID
    * int taskId
    * 
    */
    public int getTaskId(){
        return taskId;
    }


    /**
    * 任务奖励
    * pairarray reward
    * 
    */
    public List<IntPairType> getRewardPairList(){
        return rewardPairList;
    }

}
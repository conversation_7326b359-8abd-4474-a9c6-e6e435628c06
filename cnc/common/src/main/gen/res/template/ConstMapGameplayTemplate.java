package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "D_地图玩法初始化.xlsx", node = "const_map_gameplay.xml", isConst = true)
public class ConstMapGameplayTemplate implements IResConstTemplate  {

    /**
     * 小型卡巴尔营地刷新间隔时间、秒(BOSS死亡后的刷新间隔时间，单位s）
     */
    private int CableCampSmallRefreshSecond = 0;
    /**
     * 小型卡巴尔营地boss存活时间，单位s
     */
    private int CableCampSmallBossLifeSecond = 0;
    /**
     * 大型营地小怪补充刷新间隔时间，单位s
     */
    private int CableCampBigRefreshSecond = 0;
    /**
     * 大型营地小怪存活时间，单位s
     */
    private IntPairType CableCampBigMonsterLifeSecond;
    /**
     * 大型营地保护时间，单位s
     */
    private int CableCampBigCd = 0;
    /**
     * 大型营地boss存活时间，单位s
     */
    private int CableCampBigBossLifeSecond = 0;
    /**
     * 大型营地警戒值上限
     */
    private List<IntPairType> CableCampBigWarnValue;
    /**
     * 小型营地小怪存活时间，单位s
     */
    private IntPairType CableCampSmallMonsterLifeSecond;
    /**
     * 大营地建筑名称
     */
    private String CableCampBigBuildingName = "";     
    /**
     * 大营地建筑tips
     */
    private String CableCampBigBuildingTips = "";     
    /**
     * 大营地建筑UI图标
     */
    private int CableCampBigBuildingUiIicon = 0;
    /**
     * 大营地中心建筑偏移位置
     */
    private IntPairType CableCampGigBuildingCoordinate;
    /**
     * 大营地BOSS被连续击杀{0}次营地等级升级
     */
    private int CableCampBigBuildingNumberOfKillUpgrades = 0;


    public int getCableCampSmallRefreshSecond(){
        return this.CableCampSmallRefreshSecond;
    }

    public int getCableCampSmallBossLifeSecond(){
        return this.CableCampSmallBossLifeSecond;
    }

    public int getCableCampBigRefreshSecond(){
        return this.CableCampBigRefreshSecond;
    }

    public IntPairType getCableCampBigMonsterLifeSecond(){
        return this.CableCampBigMonsterLifeSecond;
    }

    public int getCableCampBigCd(){
        return this.CableCampBigCd;
    }

    public int getCableCampBigBossLifeSecond(){
        return this.CableCampBigBossLifeSecond;
    }

    public List<IntPairType> getCableCampBigWarnValue(){
        return this.CableCampBigWarnValue;
    }

    public IntPairType getCableCampSmallMonsterLifeSecond(){
        return this.CableCampSmallMonsterLifeSecond;
    }

    public String getCableCampBigBuildingName(){
        return this.CableCampBigBuildingName;
    }   

    public String getCableCampBigBuildingTips(){
        return this.CableCampBigBuildingTips;
    }   

    public int getCableCampBigBuildingUiIicon(){
        return this.CableCampBigBuildingUiIicon;
    }

    public IntPairType getCableCampGigBuildingCoordinate(){
        return this.CableCampGigBuildingCoordinate;
    }

    public int getCableCampBigBuildingNumberOfKillUpgrades(){
        return this.CableCampBigBuildingNumberOfKillUpgrades;
    }

    @Override
    public int getId() {
        return 0;
    }
}

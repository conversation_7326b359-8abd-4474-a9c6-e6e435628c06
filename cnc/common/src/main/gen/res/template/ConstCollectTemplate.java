package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "Z_资源田.xlsx", node = "const_collect.xml", isConst = true)
public class ConstCollectTemplate implements IResConstTemplate  {

    /**
     * 全局刷新时间点（0-23时）
     */
    private int globalRefreshHour = 0;
    /**
     * 补充刷新触发百分比  少于则触发
     */
    private int replenishRefreshRatio = 0;
    /**
     * 资源田最大搜索范围，米为单位
     */
    private int searchDistance = 0;
    /**
     * 每种资源最大可搜索等级
     */
    private int searchLvLimit = 0;
    /**
     * 采集邮件id
     */
    private int collectMailId = 0;
    /**
     * 片区等级与资源田富饶程度对照表
     */
    private List<IntPairType> resourceLevel;
    /**
     * 最贫瘠色号
     */
    private String resourceColor1 = "";     
    /**
     * 倒数第2贫瘠色号
     */
    private String resourceColor2 = "";     
    /**
     * 倒数第3贫瘠色号
     */
    private String resourceColor3 = "";     
    /**
     * 倒数第4贫瘠色号
     */
    private String resourceColor4 = "";     
    /**
     * 倒数第5贫瘠色号
     */
    private String resourceColor5 = "";     
    /**
     * 倒数第6贫瘠色号
     */
    private String resourceColor6 = "";     
    /**
     * 倒数第6贫瘠色号
     */
    private List<IntPairType> resourceColorTransparency;
    /**
     * 白底色号
     */
    private String whiteColor = "";     
    /**
     * 白色透明度
     */
    private int whiteColorTransparency = 0;
    /**
     * 仓库捐献值（万分比）
     */
    private int warehouseDonation = 0;
    /**
     * 黄金矿全局刷新时间点（0-23时）
     */
    private int goldRefreshHour = 0;


    public int getGlobalRefreshHour(){
        return this.globalRefreshHour;
    }

    public int getReplenishRefreshRatio(){
        return this.replenishRefreshRatio;
    }

    public int getSearchDistance(){
        return this.searchDistance;
    }

    public int getSearchLvLimit(){
        return this.searchLvLimit;
    }

    public int getCollectMailId(){
        return this.collectMailId;
    }

    public List<IntPairType> getResourceLevel(){
        return this.resourceLevel;
    }

    public String getResourceColor1(){
        return this.resourceColor1;
    }   

    public String getResourceColor2(){
        return this.resourceColor2;
    }   

    public String getResourceColor3(){
        return this.resourceColor3;
    }   

    public String getResourceColor4(){
        return this.resourceColor4;
    }   

    public String getResourceColor5(){
        return this.resourceColor5;
    }   

    public String getResourceColor6(){
        return this.resourceColor6;
    }   

    public List<IntPairType> getResourceColorTransparency(){
        return this.resourceColorTransparency;
    }

    public String getWhiteColor(){
        return this.whiteColor;
    }   

    public int getWhiteColorTransparency(){
        return this.whiteColorTransparency;
    }

    public int getWarehouseDonation(){
        return this.warehouseDonation;
    }

    public int getGoldRefreshHour(){
        return this.goldRefreshHour;
    }

    @Override
    public int getId() {
        return 0;
    }
}

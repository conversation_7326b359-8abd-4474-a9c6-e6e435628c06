package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_大世界野怪投放区域.xlsx", node="big_scene_spawn_building.xml")
public class BigSceneSpawnBuildingTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * kvk剧本id
    * int storyId
    * 
    */
    @ResAttribute("storyId")
    private  int storyId;

    /**
    * 建筑类型
    * CommonEnum.MapBuildingType type
    * 
    */
    @ResAttribute("type")
    private CommonEnum.MapBuildingType type;

    /**
    * 边长（cm）
    * int range
    * 
    */
    @ResAttribute("range")
    private  int range;

    /**
    * 刷新时间
    * pairarray refreshTime
    * 
    */
    @ResAttribute("refreshTime")
    private List<IntPairType> refreshTimePairList;

    /**
    * 指定id与数量刷新
    * pairarray refreshConfig
    * 
    */
    @ResAttribute("refreshConfig")
    private List<IntPairType> refreshConfigPairList;

    /**
    * 存活时间(秒)
    * int lifeTime
    * 
    */
    @ResAttribute("lifeTime")
    private  int lifeTime;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * kvk剧本id
    * int storyId
    * 
    */
    public int getStoryId(){
        return storyId;
    }


    /**
    * 建筑类型
    * CommonEnum.MapBuildingType type
    * 
    */
    public CommonEnum.MapBuildingType getType(){
        return type;
    }
    /**
    * 边长（cm）
    * int range
    * 
    */
    public int getRange(){
        return range;
    }


    /**
    * 刷新时间
    * pairarray refreshTime
    * 
    */
    public List<IntPairType> getRefreshTimePairList(){
        return refreshTimePairList;
    }

    /**
    * 指定id与数量刷新
    * pairarray refreshConfig
    * 
    */
    public List<IntPairType> getRefreshConfigPairList(){
        return refreshConfigPairList;
    }
    /**
    * 存活时间(秒)
    * int lifeTime
    * 
    */
    public int getLifeTime(){
        return lifeTime;
    }


}
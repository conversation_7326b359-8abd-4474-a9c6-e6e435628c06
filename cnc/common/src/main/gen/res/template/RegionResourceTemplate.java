package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_资源田.xlsx", node="region_resource.xml")
public class RegionResourceTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 剧本id
    * int storyId
    * 
    */
    @ResAttribute("storyId")
    private  int storyId;

    /**
    * 州id
    * int regionId
    * 
    */
    @ResAttribute("regionId")
    private  int regionId;

    /**
    * 黄金矿总数上限
    * int goldNum
    * 
    */
    @ResAttribute("goldNum")
    private  int goldNum;

    /**
    * 黄金矿等级权重比
    * pairarray goldLevelWeight
    * 
    */
    @ResAttribute("goldLevelWeight")
    private List<IntPairType> goldLevelWeightPairList;

    /**
    * 普通资源田总数上限
    * int num
    * 
    */
    @ResAttribute("num")
    private  int num;

    /**
    * 一个片最少的资源田数量
    * int areaMinResource
    * 
    */
    @ResAttribute("areaMinResource")
    private  int areaMinResource;

    /**
    * 一个片最多的资源田数量
    * int areaMaxResource
    * 
    */
    @ResAttribute("areaMaxResource")
    private  int areaMaxResource;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 剧本id
    * int storyId
    * 
    */
    public int getStoryId(){
        return storyId;
    }

    /**
    * 州id
    * int regionId
    * 
    */
    public int getRegionId(){
        return regionId;
    }

    /**
    * 黄金矿总数上限
    * int goldNum
    * 
    */
    public int getGoldNum(){
        return goldNum;
    }


    /**
    * 黄金矿等级权重比
    * pairarray goldLevelWeight
    * 
    */
    public List<IntPairType> getGoldLevelWeightPairList(){
        return goldLevelWeightPairList;
    }
    /**
    * 普通资源田总数上限
    * int num
    * 
    */
    public int getNum(){
        return num;
    }

    /**
    * 一个片最少的资源田数量
    * int areaMinResource
    * 
    */
    public int getAreaMinResource(){
        return areaMinResource;
    }

    /**
    * 一个片最多的资源田数量
    * int areaMaxResource
    * 
    */
    public int getAreaMaxResource(){
        return areaMaxResource;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="W_王国管理.xlsx", node="kingdom_buff.xml")
public class KingdomBuffTemplate implements IResTemplate  {

    /**
    * 王国增益ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 王国增益属性ID
    * int buffID
    * 
    */
    @ResAttribute("buffID")
    private  int buffID;

    /**
    * 王国增益生效时间(单位秒，显示为小时)
    * int buffTime
    * 
    */
    @ResAttribute("buffTime")
    private  int buffTime;

    /**
    * 施放消耗资源数量
    * pair consume
    * 
    */
    @ResAttribute("consume")
    private IntPairType consumePair;



    /**
    * 王国增益ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 王国增益属性ID
    * int buffID
    * 
    */
    public int getBuffID(){
        return buffID;
    }

    /**
    * 王国增益生效时间(单位秒，显示为小时)
    * int buffTime
    * 
    */
    public int getBuffTime(){
        return buffTime;
    }


    /**
    * 施放消耗资源数量
    * pair consume
    * 
    */
    public IntPairType getConsumePair(){
        return consumePair;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_城池导量表.xlsx", node="born_limit.xml")
public class BornLimitTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 大区id
    * int worldId
    * 
    */
    @ResAttribute("worldId")
    private  int worldId;

    /**
    * 小服id
    * int zoneId
    * 
    */
    @ResAttribute("zoneId")
    private  int zoneId;

    /**
    * 各语言限制
(语言类型_限制百分比)
（没有配置对应语言表示不限制，配100也表示不限制，配0表示一个也不给进）
    * pairarray limit
    * 
    */
    @ResAttribute("limit")
    private List<IntPairType> limitPairList;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 大区id
    * int worldId
    * 
    */
    public int getWorldId(){
        return worldId;
    }

    /**
    * 小服id
    * int zoneId
    * 
    */
    public int getZoneId(){
        return zoneId;
    }


    /**
    * 各语言限制
(语言类型_限制百分比)
（没有配置对应语言表示不限制，配100也表示不限制，配0表示一个也不给进）
    * pairarray limit
    * 
    */
    public List<IntPairType> getLimitPairList(){
        return limitPairList;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="F_服务器多服和移民表.xlsx", node="server_jumpPosition.xml")
public class ServerJumppositionTemplate implements IResTemplate  {

    /**
    * 序号
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 州id
    * int regionId
    * 
    */
    @ResAttribute("regionId")
    private  int regionId;

    /**
    * 坐标
    * intarray position
    * 
    */
    @ResAttribute("position")
    private List<Integer> positionList;



    /**
    * 序号
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 州id
    * int regionId
    * 
    */
    public int getRegionId(){
        return regionId;
    }


    /**
    * 坐标
    * intarray position
    * 
    */
    public List<Integer> getPositionList(){
        return positionList;
    }


}
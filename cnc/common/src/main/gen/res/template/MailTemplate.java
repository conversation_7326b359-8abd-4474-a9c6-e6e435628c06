package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_邮件.xlsx", node="mail.xml")
public class MailTemplate implements IResTemplate  {

    /**
    * 邮件模板ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 邮件类型页签TYPE
    * CommonEnum.MailTabsType mailTabsType
    * 
    */
    @ResAttribute("mailTabsType")
    private CommonEnum.MailTabsType mailTabsType;

    /**
    * 报告邮件分类
    * int mailClassType
    * 
    */
    @ResAttribute("mailClassType")
    private  int mailClassType;

    /**
    * 是否需要折叠
    * bool needFold
    * 
    */
    @ResAttribute("needFold")
    private  boolean needFold;

    /**
    * 奖励
    * pairarray award
    * 
    */
    @ResAttribute("award")
    private List<IntPairType> awardPairList;

    /**
    * 推送消息
    * language pushText
    * 
    */
    @ResAttribute("pushText")
    private  String pushText;

    /**
    * 是否读完自动删除
    * bool autoDel
    * 
    */
    @ResAttribute("autoDel")
    private  boolean autoDel;



    /**
    * 邮件模板ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 邮件类型页签TYPE
    * CommonEnum.MailTabsType mailTabsType
    * 
    */
    public CommonEnum.MailTabsType getMailTabsType(){
        return mailTabsType;
    }
    /**
    * 报告邮件分类
    * int mailClassType
    * 
    */
    public int getMailClassType(){
        return mailClassType;
    }


    /**
    * 是否需要折叠
    * bool needFold
    * 
    */
    public boolean getNeedFold(){
        return needFold;
    }

    /**
    * 奖励
    * pairarray award
    * 
    */
    public List<IntPairType> getAwardPairList(){
        return awardPairList;
    }
    /**
    * 推送消息
    * language pushText
    * 
    */
    public String getPushText(){
        return pushText;
    }

    /**
    * 是否读完自动删除
    * bool autoDel
    * 
    */
    public boolean getAutoDel(){
        return autoDel;
    }

}
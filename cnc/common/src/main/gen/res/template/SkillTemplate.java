package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_技能表_技能.xlsx", node="skill.xml")
public class SkillTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 技能组
    * int category
    * 
    */
    @ResAttribute("category")
    private  int category;

    /**
    * 名称
    * string name
    * 
    */
    @ResAttribute("name")
    private  String name;

    /**
    * 类型
    * int type
    * 
    */
    @ResAttribute("type")
    private  int type;

    /**
    * 等级
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;

    /**
    * 提升战力
    * int fightingPower
    * 
    */
    @ResAttribute("fightingPower")
    private  int fightingPower;

    /**
    * 施放消耗条件
    * int condition
    * 
    */
    @ResAttribute("condition")
    private  int condition;

    /**
    * 施放消耗
    * int cost
    * 
    */
    @ResAttribute("cost")
    private  int cost;

    /**
    * 下一级技能ID
    * int nextLevel
    * 
    */
    @ResAttribute("nextLevel")
    private  int nextLevel;

    /**
    * 替换技能ID
    * int replaceSkill
    * 
    */
    @ResAttribute("replaceSkill")
    private  int replaceSkill;

    /**
    * 技能效果组
    * intarray groupSkill
    * 
    */
    @ResAttribute("groupSkill")
    private List<Integer> groupSkillList;

    /**
    * CD
    * int cd
    * 
    */
    @ResAttribute("cd")
    private  int cd;

    /**
    * 效果延迟时间
    * int singtime
    * 
    */
    @ResAttribute("singtime")
    private  int singtime;

    /**
    * 吟唱特效、音效表现
    * int singshow
    * 
    */
    @ResAttribute("singshow")
    private  int singshow;

    /**
    * 特效、音效表现
    * int skillShowId
    * 
    */
    @ResAttribute("skillShowId")
    private  int skillShowId;

    /**
    * 战斗协议
    * bool CombatAgreement
    * 
    */
    @ResAttribute("CombatAgreement")
    private  boolean CombatAgreement;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 技能组
    * int category
    * 
    */
    public int getCategory(){
        return category;
    }

    /**
    * 名称
    * string name
    * 
    */
    public String getName(){
        return name;
    }
    /**
    * 类型
    * int type
    * 
    */
    public int getType(){
        return type;
    }

    /**
    * 等级
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }

    /**
    * 提升战力
    * int fightingPower
    * 
    */
    public int getFightingPower(){
        return fightingPower;
    }

    /**
    * 施放消耗条件
    * int condition
    * 
    */
    public int getCondition(){
        return condition;
    }

    /**
    * 施放消耗
    * int cost
    * 
    */
    public int getCost(){
        return cost;
    }

    /**
    * 下一级技能ID
    * int nextLevel
    * 
    */
    public int getNextLevel(){
        return nextLevel;
    }

    /**
    * 替换技能ID
    * int replaceSkill
    * 
    */
    public int getReplaceSkill(){
        return replaceSkill;
    }


    /**
    * 技能效果组
    * intarray groupSkill
    * 
    */
    public List<Integer> getGroupSkillList(){
        return groupSkillList;
    }

    /**
    * CD
    * int cd
    * 
    */
    public int getCd(){
        return cd;
    }

    /**
    * 效果延迟时间
    * int singtime
    * 
    */
    public int getSingtime(){
        return singtime;
    }

    /**
    * 吟唱特效、音效表现
    * int singshow
    * 
    */
    public int getSingshow(){
        return singshow;
    }

    /**
    * 特效、音效表现
    * int skillShowId
    * 
    */
    public int getSkillShowId(){
        return skillShowId;
    }


    /**
    * 战斗协议
    * bool CombatAgreement
    * 
    */
    public boolean getCombatAgreement(){
        return CombatAgreement;
    }

}
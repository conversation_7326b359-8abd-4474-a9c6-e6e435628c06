package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="W_王国管理.xlsx", node="kingdom_title.xml")
public class KingdomTitleTemplate implements IResTemplate  {

    /**
    * 头衔ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 头衔级别
    * int rank
    * 
    */
    @ResAttribute("rank")
    private  int rank;

    /**
    * 头衔属性ID
    * intarray buffID
    * 
    */
    @ResAttribute("buffID")
    private List<Integer> buffIDList;



    /**
    * 头衔ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 头衔级别
    * int rank
    * 
    */
    public int getRank(){
        return rank;
    }


    /**
    * 头衔属性ID
    * intarray buffID
    * 
    */
    public List<Integer> getBuffIDList(){
        return buffIDList;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_资源田.xlsx", node="resource_const.xml")
public class ResourceConstTemplate implements IResTemplate  {

    /**
    * 标识id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 剧本id
    * int storyId
    * 
    */
    @ResAttribute("storyId")
    private  int storyId;

    /**
    * 每种资源最大可搜索等级
    * int searchLvLimit
    * 
    */
    @ResAttribute("searchLvLimit")
    private  int searchLvLimit;

    /**
    * 每种资源最小可搜索等级
    * int searchminLvLimit
    * 
    */
    @ResAttribute("searchminLvLimit")
    private  int searchminLvLimit;

    /**
    * 全局刷新时间点（0-23时）
    * int globalRefreshHour
    * 
    */
    @ResAttribute("globalRefreshHour")
    private  int globalRefreshHour;

    /**
    * 资源田最大搜索范围，米为单位
    * int replenishRefreshRatio
    * 
    */
    @ResAttribute("replenishRefreshRatio")
    private  int replenishRefreshRatio;

    /**
    * 采集邮件id
    * int collectMailId
    * 
    */
    @ResAttribute("collectMailId")
    private  int collectMailId;

    /**
    * 片区等级与资源田富饶程度对照表
    * pairarray resourceLevel
    * 
    */
    @ResAttribute("resourceLevel")
    private List<IntPairType> resourceLevelPairList;

    /**
    * 最贫瘠色号
    * string resourceColor1
    * 
    */
    @ResAttribute("resourceColor1")
    private  String resourceColor1;

    /**
    * 倒数第2贫瘠色号
    * string resourceColor2
    * 
    */
    @ResAttribute("resourceColor2")
    private  String resourceColor2;

    /**
    * 倒数第3贫瘠色号
    * string resourceColor3
    * 
    */
    @ResAttribute("resourceColor3")
    private  String resourceColor3;

    /**
    * 倒数第4贫瘠色号
    * string resourceColor4
    * 
    */
    @ResAttribute("resourceColor4")
    private  String resourceColor4;

    /**
    * 倒数第5贫瘠色号
    * string resourceColor5
    * 
    */
    @ResAttribute("resourceColor5")
    private  String resourceColor5;

    /**
    * 倒数第6贫瘠色号
    * string resourceColor6
    * 
    */
    @ResAttribute("resourceColor6")
    private  String resourceColor6;

    /**
    * 资源丰富度对应透明度
    * pairarray resourceColorTransparency
    * 
    */
    @ResAttribute("resourceColorTransparency")
    private List<IntPairType> resourceColorTransparencyPairList;

    /**
    * 白底色号
    * string whiteColor
    * 
    */
    @ResAttribute("whiteColor")
    private  String whiteColor;

    /**
    * 白色透明度
    * int whiteColorTransparency
    * 
    */
    @ResAttribute("whiteColorTransparency")
    private  int whiteColorTransparency;

    /**
    * 仓库捐献值（万分比）
    * int warehouseDonation
    * 
    */
    @ResAttribute("warehouseDonation")
    private  int warehouseDonation;

    /**
    * 黄金矿全局刷新时间点（0-23时）
    * int goldRefreshHour
    * 
    */
    @ResAttribute("goldRefreshHour")
    private  int goldRefreshHour;



    /**
    * 标识id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 剧本id
    * int storyId
    * 
    */
    public int getStoryId(){
        return storyId;
    }

    /**
    * 每种资源最大可搜索等级
    * int searchLvLimit
    * 
    */
    public int getSearchLvLimit(){
        return searchLvLimit;
    }

    /**
    * 每种资源最小可搜索等级
    * int searchminLvLimit
    * 
    */
    public int getSearchminLvLimit(){
        return searchminLvLimit;
    }

    /**
    * 全局刷新时间点（0-23时）
    * int globalRefreshHour
    * 
    */
    public int getGlobalRefreshHour(){
        return globalRefreshHour;
    }

    /**
    * 资源田最大搜索范围，米为单位
    * int replenishRefreshRatio
    * 
    */
    public int getReplenishRefreshRatio(){
        return replenishRefreshRatio;
    }

    /**
    * 采集邮件id
    * int collectMailId
    * 
    */
    public int getCollectMailId(){
        return collectMailId;
    }


    /**
    * 片区等级与资源田富饶程度对照表
    * pairarray resourceLevel
    * 
    */
    public List<IntPairType> getResourceLevelPairList(){
        return resourceLevelPairList;
    }
    /**
    * 最贫瘠色号
    * string resourceColor1
    * 
    */
    public String getResourceColor1(){
        return resourceColor1;
    }
    /**
    * 倒数第2贫瘠色号
    * string resourceColor2
    * 
    */
    public String getResourceColor2(){
        return resourceColor2;
    }
    /**
    * 倒数第3贫瘠色号
    * string resourceColor3
    * 
    */
    public String getResourceColor3(){
        return resourceColor3;
    }
    /**
    * 倒数第4贫瘠色号
    * string resourceColor4
    * 
    */
    public String getResourceColor4(){
        return resourceColor4;
    }
    /**
    * 倒数第5贫瘠色号
    * string resourceColor5
    * 
    */
    public String getResourceColor5(){
        return resourceColor5;
    }
    /**
    * 倒数第6贫瘠色号
    * string resourceColor6
    * 
    */
    public String getResourceColor6(){
        return resourceColor6;
    }

    /**
    * 资源丰富度对应透明度
    * pairarray resourceColorTransparency
    * 
    */
    public List<IntPairType> getResourceColorTransparencyPairList(){
        return resourceColorTransparencyPairList;
    }
    /**
    * 白底色号
    * string whiteColor
    * 
    */
    public String getWhiteColor(){
        return whiteColor;
    }
    /**
    * 白色透明度
    * int whiteColorTransparency
    * 
    */
    public int getWhiteColorTransparency(){
        return whiteColorTransparency;
    }

    /**
    * 仓库捐献值（万分比）
    * int warehouseDonation
    * 
    */
    public int getWarehouseDonation(){
        return warehouseDonation;
    }

    /**
    * 黄金矿全局刷新时间点（0-23时）
    * int goldRefreshHour
    * 
    */
    public int getGoldRefreshHour(){
        return goldRefreshHour;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="S_时间控制器.xlsx", node="schedule.xml")
public class ScheduleTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 指定日期
    * string date
    * 
    */
    @ResAttribute("date")
    private  String date;

    /**
    * 特殊日期类型
    * string dateCustomize
    * 
    */
    @ResAttribute("dateCustomize")
    private  String dateCustomize;

    /**
    * 特殊日期间隔
    * int datePeriod
    * 
    */
    @ResAttribute("datePeriod")
    private  int datePeriod;

    /**
    * 日期
    * string dayOfMonth
    * 
    */
    @ResAttribute("dayOfMonth")
    private  String dayOfMonth;

    /**
    * 月份
    * string month
    * 
    */
    @ResAttribute("month")
    private  String month;

    /**
    * 周几
    * string dayOfWeek
    * 
    */
    @ResAttribute("dayOfWeek")
    private  String dayOfWeek;

    /**
    * 分秒时
    * string timeStr
    * 
    */
    @ResAttribute("timeStr")
    private  String timeStr;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 指定日期
    * string date
    * 
    */
    public String getDate(){
        return date;
    }
    /**
    * 特殊日期类型
    * string dateCustomize
    * 
    */
    public String getDateCustomize(){
        return dateCustomize;
    }
    /**
    * 特殊日期间隔
    * int datePeriod
    * 
    */
    public int getDatePeriod(){
        return datePeriod;
    }

    /**
    * 日期
    * string dayOfMonth
    * 
    */
    public String getDayOfMonth(){
        return dayOfMonth;
    }
    /**
    * 月份
    * string month
    * 
    */
    public String getMonth(){
        return month;
    }
    /**
    * 周几
    * string dayOfWeek
    * 
    */
    public String getDayOfWeek(){
        return dayOfWeek;
    }
    /**
    * 分秒时
    * string timeStr
    * 
    */
    public String getTimeStr(){
        return timeStr;
    }

}
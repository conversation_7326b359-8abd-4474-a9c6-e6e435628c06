package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_抽奖活动表.xlsx", node="lottery_natasha.xml")
public class LotteryNatashaTemplate implements IResTemplate  {

    /**
    * 活动ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 免费任务ID
    * int taskPoolId
    * 
    */
    @ResAttribute("taskPoolId")
    private  int taskPoolId;

    /**
    * 充值五档ID
    * intarray taskChargeid
    * 
    */
    @ResAttribute("taskChargeid")
    private List<Integer> taskChargeidList;

    /**
    * 转盘池ID
    * int activityLotteryid
    * 
    */
    @ResAttribute("activityLotteryid")
    private  int activityLotteryid;

    /**
    * 宝箱开启时间ID
    * triplearray timeReward
    * 
    */
    @ResAttribute("timeReward")
    private List<IntTripleType> timeRewardTripleList;



    /**
    * 活动ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 免费任务ID
    * int taskPoolId
    * 
    */
    public int getTaskPoolId(){
        return taskPoolId;
    }


    /**
    * 充值五档ID
    * intarray taskChargeid
    * 
    */
    public List<Integer> getTaskChargeidList(){
        return taskChargeidList;
    }

    /**
    * 转盘池ID
    * int activityLotteryid
    * 
    */
    public int getActivityLotteryid(){
        return activityLotteryid;
    }


    /**
    * 宝箱开启时间ID
    * triplearray timeReward
    * 
    */
    public List<IntTripleType> getTimeRewardTripleList(){
        return timeRewardTripleList;
    }       

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_支付.xlsx", node="charge_goods.xml")
public class ChargeGoodsTemplate implements IResTemplate  {

    /**
    * 礼包id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 礼包类型
    * CommonEnum.GoodsType goodsType
    * 
    */
    @ResAttribute("goodsType")
    private CommonEnum.GoodsType goodsType;

    /**
    * 支付的产品id
    * int chargeSdkId
    * 
    */
    @ResAttribute("chargeSdkId")
    private  int chargeSdkId;

    /**
    * 计入累充的货币奖励
    * int directDiamond
    * 
    */
    @ResAttribute("directDiamond")
    private  int directDiamond;

    /**
    * 移民是否限购次数重置
    * bool migrateReset
    * 
    */
    @ResAttribute("migrateReset")
    private  boolean migrateReset;

    /**
    * 赠送奖励(可以有钻石，但不会计入累充)
    * pairarray goodsReward
    * 
    */
    @ResAttribute("goodsReward")
    private List<IntPairType> goodsRewardPairList;

    /**
    * 奖励表id（自选宝箱，权重宝箱）
    * intarray selectRewards
    * 
    */
    @ResAttribute("selectRewards")
    private List<Integer> selectRewardsList;

    /**
    * 联盟礼物id
    * int clanGiftId
    * 
    */
    @ResAttribute("clanGiftId")
    private  int clanGiftId;

    /**
    * 包含的子礼包内容
    * pairarray subGoodsId
    * 
    */
    @ResAttribute("subGoodsId")
    private List<IntPairType> subGoodsIdPairList;

    /**
    * 周卡月卡持续天数
    * int lastingTime
    * 
    */
    @ResAttribute("lastingTime")
    private  int lastingTime;

    /**
    * 每日奖励
    * pairarray dailyReward
    * 
    */
    @ResAttribute("dailyReward")
    private List<IntPairType> dailyRewardPairList;

    /**
    * 限购次数
    * int maxPurchaseTimes
    * 
    */
    @ResAttribute("maxPurchaseTimes")
    private  int maxPurchaseTimes;

    /**
    * 触发邮件id
    * int triggerMailId
    * 
    */
    @ResAttribute("triggerMailId")
    private  int triggerMailId;



    /**
    * 礼包id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 礼包类型
    * CommonEnum.GoodsType goodsType
    * 
    */
    public CommonEnum.GoodsType getGoodsType(){
        return goodsType;
    }
    /**
    * 支付的产品id
    * int chargeSdkId
    * 
    */
    public int getChargeSdkId(){
        return chargeSdkId;
    }

    /**
    * 计入累充的货币奖励
    * int directDiamond
    * 
    */
    public int getDirectDiamond(){
        return directDiamond;
    }


    /**
    * 移民是否限购次数重置
    * bool migrateReset
    * 
    */
    public boolean getMigrateReset(){
        return migrateReset;
    }

    /**
    * 赠送奖励(可以有钻石，但不会计入累充)
    * pairarray goodsReward
    * 
    */
    public List<IntPairType> getGoodsRewardPairList(){
        return goodsRewardPairList;
    }

    /**
    * 奖励表id（自选宝箱，权重宝箱）
    * intarray selectRewards
    * 
    */
    public List<Integer> getSelectRewardsList(){
        return selectRewardsList;
    }

    /**
    * 联盟礼物id
    * int clanGiftId
    * 
    */
    public int getClanGiftId(){
        return clanGiftId;
    }


    /**
    * 包含的子礼包内容
    * pairarray subGoodsId
    * 
    */
    public List<IntPairType> getSubGoodsIdPairList(){
        return subGoodsIdPairList;
    }
    /**
    * 周卡月卡持续天数
    * int lastingTime
    * 
    */
    public int getLastingTime(){
        return lastingTime;
    }


    /**
    * 每日奖励
    * pairarray dailyReward
    * 
    */
    public List<IntPairType> getDailyRewardPairList(){
        return dailyRewardPairList;
    }
    /**
    * 限购次数
    * int maxPurchaseTimes
    * 
    */
    public int getMaxPurchaseTimes(){
        return maxPurchaseTimes;
    }

    /**
    * 触发邮件id
    * int triggerMailId
    * 
    */
    public int getTriggerMailId(){
        return triggerMailId;
    }


}
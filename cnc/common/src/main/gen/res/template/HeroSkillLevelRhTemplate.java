package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_英雄表_New.xlsx", node="hero_skill_level_rh.xml")
public class HeroSkillLevelRhTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 技能ID
    * int skill_id
    * 
    */
    @ResAttribute("skill_id")
    private  int skill_id;

    /**
    * 技能等级
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;

    /**
    * 技能星级
    * int star
    * 
    */
    @ResAttribute("star")
    private  int star;

    /**
    * 消耗（货币）
    * pair cost
    * 
    */
    @ResAttribute("cost")
    private IntPairType costPair;

    /**
    * 需求星级
    * int require_star
    * 
    */
    @ResAttribute("require_star")
    private  int require_star;

    /**
    * 需求英雄等级
    * int require_hero_level
    * 
    */
    @ResAttribute("require_hero_level")
    private  int require_hero_level;

    /**
    * 战力增加值
    * int power
    * 
    */
    @ResAttribute("power")
    private  int power;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 技能ID
    * int skill_id
    * 
    */
    public int getSkillId(){
        return skill_id;
    }

    /**
    * 技能等级
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }

    /**
    * 技能星级
    * int star
    * 
    */
    public int getStar(){
        return star;
    }


    /**
    * 消耗（货币）
    * pair cost
    * 
    */
    public IntPairType getCostPair(){
        return costPair;
    }
    /**
    * 需求星级
    * int require_star
    * 
    */
    public int getRequireStar(){
        return require_star;
    }

    /**
    * 需求英雄等级
    * int require_hero_level
    * 
    */
    public int getRequireHeroLevel(){
        return require_hero_level;
    }

    /**
    * 战力增加值
    * int power
    * 
    */
    public int getPower(){
        return power;
    }


}
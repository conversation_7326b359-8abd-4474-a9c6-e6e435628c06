package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动表_策划分表.xlsx", node="activity_rushwars.xml")
public class ActivityRushwarsTemplate implements IResTemplate  {

    /**
    * 活动id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 远征关卡ID
    * int expeditionId
    * 
    */
    @ResAttribute("expeditionId")
    private  int expeditionId;



    /**
    * 活动id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 远征关卡ID
    * int expeditionId
    * 
    */
    public int getExpeditionId(){
        return expeditionId;
    }


}
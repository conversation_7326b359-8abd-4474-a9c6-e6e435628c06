package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_英雄表_New.xlsx", node="hero_star_rh.xml")
public class HeroStarRhTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 星级
    * int star
    * 
    */
    @ResAttribute("star")
    private  int star;

    /**
    * 攻击增益
    * int atk_buff
    * 
    */
    @ResAttribute("atk_buff")
    private  int atk_buff;

    /**
    * 防御增益
    * int def_buff
    * 
    */
    @ResAttribute("def_buff")
    private  int def_buff;

    /**
    * 生命增益
    * int hp_buff
    * 
    */
    @ResAttribute("hp_buff")
    private  int hp_buff;

    /**
    * 碎片消耗量
    * int cost_shard
    * 
    */
    @ResAttribute("cost_shard")
    private  int cost_shard;

    /**
    * 战力增加值
    * int power
    * 
    */
    @ResAttribute("power")
    private  int power;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 星级
    * int star
    * 
    */
    public int getStar(){
        return star;
    }

    /**
    * 攻击增益
    * int atk_buff
    * 
    */
    public int getAtkBuff(){
        return atk_buff;
    }

    /**
    * 防御增益
    * int def_buff
    * 
    */
    public int getDefBuff(){
        return def_buff;
    }

    /**
    * 生命增益
    * int hp_buff
    * 
    */
    public int getHpBuff(){
        return hp_buff;
    }

    /**
    * 碎片消耗量
    * int cost_shard
    * 
    */
    public int getCostShard(){
        return cost_shard;
    }

    /**
    * 战力增加值
    * int power
    * 
    */
    public int getPower(){
        return power;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_联盟配置表.xlsx", node="clan_gift.xml")
public class ClanGiftTemplate implements IResTemplate  {

    /**
    * 编号id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 礼物ID
    * int giftID
    * 
    */
    @ResAttribute("giftID")
    private  int giftID;

    /**
    * 礼物类型
    * CommonEnum.ClanGiftRarityType type
    * 
    */
    @ResAttribute("type")
    private CommonEnum.ClanGiftRarityType type;

    /**
    * 礼物等级下限
    * int giftlevelmin
    * 
    */
    @ResAttribute("giftlevelmin")
    private  int giftlevelmin;

    /**
    * 礼物等级上限
    * int giftlevelmax
    * 
    */
    @ResAttribute("giftlevelmax")
    private  int giftlevelmax;

    /**
    * 奖励id
    * int rewardId
    * 
    */
    @ResAttribute("rewardId")
    private  int rewardId;

    /**
    * 礼物点数数量
    * int point
    * 
    */
    @ResAttribute("point")
    private  int point;

    /**
    * 钥匙数
    * int key
    * 
    */
    @ResAttribute("key")
    private  int key;

    /**
    * 过期时间
    * int time
    * 
    */
    @ResAttribute("time")
    private  int time;



    /**
    * 编号id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 礼物ID
    * int giftID
    * 
    */
    public int getGiftID(){
        return giftID;
    }


    /**
    * 礼物类型
    * CommonEnum.ClanGiftRarityType type
    * 
    */
    public CommonEnum.ClanGiftRarityType getType(){
        return type;
    }
    /**
    * 礼物等级下限
    * int giftlevelmin
    * 
    */
    public int getGiftlevelmin(){
        return giftlevelmin;
    }

    /**
    * 礼物等级上限
    * int giftlevelmax
    * 
    */
    public int getGiftlevelmax(){
        return giftlevelmax;
    }

    /**
    * 奖励id
    * int rewardId
    * 
    */
    public int getRewardId(){
        return rewardId;
    }

    /**
    * 礼物点数数量
    * int point
    * 
    */
    public int getPoint(){
        return point;
    }

    /**
    * 钥匙数
    * int key
    * 
    */
    public int getKey(){
        return key;
    }

    /**
    * 过期时间
    * int time
    * 
    */
    public int getTime(){
        return time;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_城池导量表.xlsx", node="born_area_limit.xml")
public class BornAreaLimitTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 州id
    * int stateId
    * 
    */
    @ResAttribute("stateId")
    private  int stateId;

    /**
    * 人数限制
    * int numLimit
    * 
    */
    @ResAttribute("numLimit")
    private  int numLimit;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 州id
    * int stateId
    * 
    */
    public int getStateId(){
        return stateId;
    }

    /**
    * 人数限制
    * int numLimit
    * 
    */
    public int getNumLimit(){
        return numLimit;
    }


}
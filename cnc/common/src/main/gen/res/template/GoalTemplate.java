package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="R_任务目标.xlsx", node="goal.xml")
public class GoalTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 类型
    * int group
    * 
    */
    @ResAttribute("group")
    private  int group;

    /**
    * 目标参数
    * intarray params
    * 
    */
    @ResAttribute("params")
    private List<Integer> paramsList;

    /**
    * 完成次数
    * int count
    * 
    */
    @ResAttribute("count")
    private  int count;

    /**
    * 是否自动完成
    * bool autoReward
    * 
    */
    @ResAttribute("autoReward")
    private  boolean autoReward;

    /**
    * 完成默认奖励
    * intarray completeReward
    * 
    */
    @ResAttribute("completeReward")
    private List<Integer> completeRewardList;

    /**
    * 评分目标
    * intarray scoreGoal
    * 
    */
    @ResAttribute("scoreGoal")
    private List<Integer> scoreGoalList;

    /**
    * 评分奖励
    * string scoreReward
    * 
    */
    @ResAttribute("scoreReward")
    private  String scoreReward;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 类型
    * int group
    * 
    */
    public int getGroup(){
        return group;
    }


    /**
    * 目标参数
    * intarray params
    * 
    */
    public List<Integer> getParamsList(){
        return paramsList;
    }

    /**
    * 完成次数
    * int count
    * 
    */
    public int getCount(){
        return count;
    }


    /**
    * 是否自动完成
    * bool autoReward
    * 
    */
    public boolean getAutoReward(){
        return autoReward;
    }

    /**
    * 完成默认奖励
    * intarray completeReward
    * 
    */
    public List<Integer> getCompleteRewardList(){
        return completeRewardList;
    }


    /**
    * 评分目标
    * intarray scoreGoal
    * 
    */
    public List<Integer> getScoreGoalList(){
        return scoreGoalList;
    }

    /**
    * 评分奖励
    * string scoreReward
    * 
    */
    public String getScoreReward(){
        return scoreReward;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="K_空军.xlsx", node="spy_plane.xml")
public class SpyPlaneTemplate implements IResTemplate  {

    /**
    * 空指部id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 侦察机速度
    * int speed
    * 
    */
    @ResAttribute("speed")
    private  int speed;

    /**
    * 探索数量
    * int exploreNum
    * 
    */
    @ResAttribute("exploreNum")
    private  int exploreNum;



    /**
    * 空指部id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 侦察机速度
    * int speed
    * 
    */
    public int getSpeed(){
        return speed;
    }

    /**
    * 探索数量
    * int exploreNum
    * 
    */
    public int getExploreNum(){
        return exploreNum;
    }


}
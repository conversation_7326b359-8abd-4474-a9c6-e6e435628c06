package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="G_关卡表_RH.xlsx", node="PVE_level_RH.xml")
public class PveLevelRhTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 前置关卡
    * intarray prevMission
    * 
    */
    @ResAttribute("prevMission")
    private List<Integer> prevMissionList;

    /**
    * 关卡类型
    * CommonEnum.MissionType missionType
    * 
    */
    @ResAttribute("missionType")
    private CommonEnum.MissionType missionType;

    /**
    * 主线关卡
    * int mainMission
    * 
    */
    @ResAttribute("mainMission")
    private  int mainMission;

    /**
    * 场景资源
    * string ScenarioResources
    * 
    */
    @ResAttribute("ScenarioResources")
    private  String ScenarioResources;

    /**
    * 胜利条件
    * string VictoryConditions
    * 
    */
    @ResAttribute("VictoryConditions")
    private  String VictoryConditions;

    /**
    * 战场中心
    * pair BattlefieldCenter
    * 
    */
    @ResAttribute("BattlefieldCenter")
    private IntPairType BattlefieldCenterPair;

    /**
    * 战场长
    * int BattlefieldLength
    * 
    */
    @ResAttribute("BattlefieldLength")
    private  int BattlefieldLength;

    /**
    * 战场宽
    * int BattlefieldWidth
    * 
    */
    @ResAttribute("BattlefieldWidth")
    private  int BattlefieldWidth;

    /**
    * 我方英雄ID0
    * int HeroID0
    * 
    */
    @ResAttribute("HeroID0")
    private  int HeroID0;

    /**
    * 我方英雄ID1
    * int HeroID1
    * 
    */
    @ResAttribute("HeroID1")
    private  int HeroID1;

    /**
    * 我方英雄ID2
    * int HeroID2
    * 
    */
    @ResAttribute("HeroID2")
    private  int HeroID2;

    /**
    * 我方英雄ID3
    * int HeroID3
    * 
    */
    @ResAttribute("HeroID3")
    private  int HeroID3;

    /**
    * 我方英雄ID4
    * int HeroID4
    * 
    */
    @ResAttribute("HeroID4")
    private  int HeroID4;

    /**
    * 我方机甲ID
    * int MechID
    * 
    */
    @ResAttribute("MechID")
    private  int MechID;

    /**
    * 机甲坐标
    * pair MechCoordinate
    * 
    */
    @ResAttribute("MechCoordinate")
    private IntPairType MechCoordinatePair;

    /**
    * 我方编队0坐标
    * pair FormationCoordinate0
    * 
    */
    @ResAttribute("FormationCoordinate0")
    private IntPairType FormationCoordinate0Pair;

    /**
    * 我方编队1坐标
    * pair FormationCoordinate1
    * 
    */
    @ResAttribute("FormationCoordinate1")
    private IntPairType FormationCoordinate1Pair;

    /**
    * 我方编队2坐标
    * pair FormationCoordinate2
    * 
    */
    @ResAttribute("FormationCoordinate2")
    private IntPairType FormationCoordinate2Pair;

    /**
    * 我方编队3坐标
    * pair FormationCoordinate3
    * 
    */
    @ResAttribute("FormationCoordinate3")
    private IntPairType FormationCoordinate3Pair;

    /**
    * 我方编队4坐标
    * pair FormationCoordinate4
    * 
    */
    @ResAttribute("FormationCoordinate4")
    private IntPairType FormationCoordinate4Pair;

    /**
    * 敌方ID0
    * int EnemyUnit0
    * 
    */
    @ResAttribute("EnemyUnit0")
    private  int EnemyUnit0;

    /**
    * 敌方0坐标
    * pair EnemyUnitCoordinate0
    * 
    */
    @ResAttribute("EnemyUnitCoordinate0")
    private IntPairType EnemyUnitCoordinate0Pair;

    /**
    * 敌方ID1
    * int EnemyUnit1
    * 
    */
    @ResAttribute("EnemyUnit1")
    private  int EnemyUnit1;

    /**
    * 敌方1坐标
    * pair EnemyUnitCoordinate1
    * 
    */
    @ResAttribute("EnemyUnitCoordinate1")
    private IntPairType EnemyUnitCoordinate1Pair;

    /**
    * 敌方ID2
    * int EnemyUnit2
    * 
    */
    @ResAttribute("EnemyUnit2")
    private  int EnemyUnit2;

    /**
    * 敌方2坐标
    * pair EnemyUnitCoordinate2
    * 
    */
    @ResAttribute("EnemyUnitCoordinate2")
    private IntPairType EnemyUnitCoordinate2Pair;

    /**
    * 敌方ID3
    * int EnemyUnit3
    * 
    */
    @ResAttribute("EnemyUnit3")
    private  int EnemyUnit3;

    /**
    * 敌方3坐标
    * pair EnemyUnitCoordinate3
    * 
    */
    @ResAttribute("EnemyUnitCoordinate3")
    private IntPairType EnemyUnitCoordinate3Pair;

    /**
    * 敌方ID4
    * int EnemyUnit4
    * 
    */
    @ResAttribute("EnemyUnit4")
    private  int EnemyUnit4;

    /**
    * 敌方4坐标
    * pair EnemyUnitCoordinate4
    * 
    */
    @ResAttribute("EnemyUnitCoordinate4")
    private IntPairType EnemyUnitCoordinate4Pair;

    /**
    * 敌方建筑ID0
    * int EnemyBuilding0
    * 
    */
    @ResAttribute("EnemyBuilding0")
    private  int EnemyBuilding0;

    /**
    * 敌方建筑0坐标
    * pair EnemyBuildingCoordinate0
    * 
    */
    @ResAttribute("EnemyBuildingCoordinate0")
    private IntPairType EnemyBuildingCoordinate0Pair;

    /**
    * 敌方建筑0旋转
    * int EnemyBuildingRotation0
    * 
    */
    @ResAttribute("EnemyBuildingRotation0")
    private  int EnemyBuildingRotation0;

    /**
    * 敌方建筑ID1
    * int EnemyBuilding1
    * 
    */
    @ResAttribute("EnemyBuilding1")
    private  int EnemyBuilding1;

    /**
    * 敌方建筑1坐标
    * pair EnemyBuildingCoordinate1
    * 
    */
    @ResAttribute("EnemyBuildingCoordinate1")
    private IntPairType EnemyBuildingCoordinate1Pair;

    /**
    * 敌方建筑1旋转
    * int EnemyBuildingRotation1
    * 
    */
    @ResAttribute("EnemyBuildingRotation1")
    private  int EnemyBuildingRotation1;

    /**
    * 敌方建筑ID2
    * int EnemyBuilding2
    * 
    */
    @ResAttribute("EnemyBuilding2")
    private  int EnemyBuilding2;

    /**
    * 敌方建筑2坐标
    * pair EnemyBuildingCoordinate2
    * 
    */
    @ResAttribute("EnemyBuildingCoordinate2")
    private IntPairType EnemyBuildingCoordinate2Pair;

    /**
    * 敌方建筑2旋转
    * int EnemyBuildingRotation2
    * 
    */
    @ResAttribute("EnemyBuildingRotation2")
    private  int EnemyBuildingRotation2;

    /**
    * 敌方建筑ID3
    * int EnemyBuilding3
    * 
    */
    @ResAttribute("EnemyBuilding3")
    private  int EnemyBuilding3;

    /**
    * 敌方建筑3坐标
    * pair EnemyBuildingCoordinate3
    * 
    */
    @ResAttribute("EnemyBuildingCoordinate3")
    private IntPairType EnemyBuildingCoordinate3Pair;

    /**
    * 敌方建筑3旋转
    * int EnemyBuildingRotation3
    * 
    */
    @ResAttribute("EnemyBuildingRotation3")
    private  int EnemyBuildingRotation3;

    /**
    * 敌军配置
    * triplearray battleEnemy
    * 
    */
    @ResAttribute("battleEnemy")
    private List<IntTripleType> battleEnemyTripleList;

    /**
    * 地图坐标
    * pair missionLocation
    * 
    */
    @ResAttribute("missionLocation")
    private IntPairType missionLocationPair;

    /**
    * 战斗奖励
    * intarray battleReward
    * 
    */
    @ResAttribute("battleReward")
    private List<Integer> battleRewardList;

    /**
    * 宝箱配置
    * intarray cratesReward
    * 
    */
    @ResAttribute("cratesReward")
    private List<Integer> cratesRewardList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 前置关卡
    * intarray prevMission
    * 
    */
    public List<Integer> getPrevMissionList(){
        return prevMissionList;
    }


    /**
    * 关卡类型
    * CommonEnum.MissionType missionType
    * 
    */
    public CommonEnum.MissionType getMissionType(){
        return missionType;
    }
    /**
    * 主线关卡
    * int mainMission
    * 
    */
    public int getMainMission(){
        return mainMission;
    }

    /**
    * 场景资源
    * string ScenarioResources
    * 
    */
    public String getScenarioResources(){
        return ScenarioResources;
    }
    /**
    * 胜利条件
    * string VictoryConditions
    * 
    */
    public String getVictoryConditions(){
        return VictoryConditions;
    }

    /**
    * 战场中心
    * pair BattlefieldCenter
    * 
    */
    public IntPairType getBattlefieldCenterPair(){
        return BattlefieldCenterPair;
    }
    /**
    * 战场长
    * int BattlefieldLength
    * 
    */
    public int getBattlefieldLength(){
        return BattlefieldLength;
    }

    /**
    * 战场宽
    * int BattlefieldWidth
    * 
    */
    public int getBattlefieldWidth(){
        return BattlefieldWidth;
    }

    /**
    * 我方英雄ID0
    * int HeroID0
    * 
    */
    public int getHeroID0(){
        return HeroID0;
    }

    /**
    * 我方英雄ID1
    * int HeroID1
    * 
    */
    public int getHeroID1(){
        return HeroID1;
    }

    /**
    * 我方英雄ID2
    * int HeroID2
    * 
    */
    public int getHeroID2(){
        return HeroID2;
    }

    /**
    * 我方英雄ID3
    * int HeroID3
    * 
    */
    public int getHeroID3(){
        return HeroID3;
    }

    /**
    * 我方英雄ID4
    * int HeroID4
    * 
    */
    public int getHeroID4(){
        return HeroID4;
    }

    /**
    * 我方机甲ID
    * int MechID
    * 
    */
    public int getMechID(){
        return MechID;
    }


    /**
    * 机甲坐标
    * pair MechCoordinate
    * 
    */
    public IntPairType getMechCoordinatePair(){
        return MechCoordinatePair;
    }

    /**
    * 我方编队0坐标
    * pair FormationCoordinate0
    * 
    */
    public IntPairType getFormationCoordinate0Pair(){
        return FormationCoordinate0Pair;
    }

    /**
    * 我方编队1坐标
    * pair FormationCoordinate1
    * 
    */
    public IntPairType getFormationCoordinate1Pair(){
        return FormationCoordinate1Pair;
    }

    /**
    * 我方编队2坐标
    * pair FormationCoordinate2
    * 
    */
    public IntPairType getFormationCoordinate2Pair(){
        return FormationCoordinate2Pair;
    }

    /**
    * 我方编队3坐标
    * pair FormationCoordinate3
    * 
    */
    public IntPairType getFormationCoordinate3Pair(){
        return FormationCoordinate3Pair;
    }

    /**
    * 我方编队4坐标
    * pair FormationCoordinate4
    * 
    */
    public IntPairType getFormationCoordinate4Pair(){
        return FormationCoordinate4Pair;
    }
    /**
    * 敌方ID0
    * int EnemyUnit0
    * 
    */
    public int getEnemyUnit0(){
        return EnemyUnit0;
    }


    /**
    * 敌方0坐标
    * pair EnemyUnitCoordinate0
    * 
    */
    public IntPairType getEnemyUnitCoordinate0Pair(){
        return EnemyUnitCoordinate0Pair;
    }
    /**
    * 敌方ID1
    * int EnemyUnit1
    * 
    */
    public int getEnemyUnit1(){
        return EnemyUnit1;
    }


    /**
    * 敌方1坐标
    * pair EnemyUnitCoordinate1
    * 
    */
    public IntPairType getEnemyUnitCoordinate1Pair(){
        return EnemyUnitCoordinate1Pair;
    }
    /**
    * 敌方ID2
    * int EnemyUnit2
    * 
    */
    public int getEnemyUnit2(){
        return EnemyUnit2;
    }


    /**
    * 敌方2坐标
    * pair EnemyUnitCoordinate2
    * 
    */
    public IntPairType getEnemyUnitCoordinate2Pair(){
        return EnemyUnitCoordinate2Pair;
    }
    /**
    * 敌方ID3
    * int EnemyUnit3
    * 
    */
    public int getEnemyUnit3(){
        return EnemyUnit3;
    }


    /**
    * 敌方3坐标
    * pair EnemyUnitCoordinate3
    * 
    */
    public IntPairType getEnemyUnitCoordinate3Pair(){
        return EnemyUnitCoordinate3Pair;
    }
    /**
    * 敌方ID4
    * int EnemyUnit4
    * 
    */
    public int getEnemyUnit4(){
        return EnemyUnit4;
    }


    /**
    * 敌方4坐标
    * pair EnemyUnitCoordinate4
    * 
    */
    public IntPairType getEnemyUnitCoordinate4Pair(){
        return EnemyUnitCoordinate4Pair;
    }
    /**
    * 敌方建筑ID0
    * int EnemyBuilding0
    * 
    */
    public int getEnemyBuilding0(){
        return EnemyBuilding0;
    }


    /**
    * 敌方建筑0坐标
    * pair EnemyBuildingCoordinate0
    * 
    */
    public IntPairType getEnemyBuildingCoordinate0Pair(){
        return EnemyBuildingCoordinate0Pair;
    }
    /**
    * 敌方建筑0旋转
    * int EnemyBuildingRotation0
    * 
    */
    public int getEnemyBuildingRotation0(){
        return EnemyBuildingRotation0;
    }

    /**
    * 敌方建筑ID1
    * int EnemyBuilding1
    * 
    */
    public int getEnemyBuilding1(){
        return EnemyBuilding1;
    }


    /**
    * 敌方建筑1坐标
    * pair EnemyBuildingCoordinate1
    * 
    */
    public IntPairType getEnemyBuildingCoordinate1Pair(){
        return EnemyBuildingCoordinate1Pair;
    }
    /**
    * 敌方建筑1旋转
    * int EnemyBuildingRotation1
    * 
    */
    public int getEnemyBuildingRotation1(){
        return EnemyBuildingRotation1;
    }

    /**
    * 敌方建筑ID2
    * int EnemyBuilding2
    * 
    */
    public int getEnemyBuilding2(){
        return EnemyBuilding2;
    }


    /**
    * 敌方建筑2坐标
    * pair EnemyBuildingCoordinate2
    * 
    */
    public IntPairType getEnemyBuildingCoordinate2Pair(){
        return EnemyBuildingCoordinate2Pair;
    }
    /**
    * 敌方建筑2旋转
    * int EnemyBuildingRotation2
    * 
    */
    public int getEnemyBuildingRotation2(){
        return EnemyBuildingRotation2;
    }

    /**
    * 敌方建筑ID3
    * int EnemyBuilding3
    * 
    */
    public int getEnemyBuilding3(){
        return EnemyBuilding3;
    }


    /**
    * 敌方建筑3坐标
    * pair EnemyBuildingCoordinate3
    * 
    */
    public IntPairType getEnemyBuildingCoordinate3Pair(){
        return EnemyBuildingCoordinate3Pair;
    }
    /**
    * 敌方建筑3旋转
    * int EnemyBuildingRotation3
    * 
    */
    public int getEnemyBuildingRotation3(){
        return EnemyBuildingRotation3;
    }


    /**
    * 敌军配置
    * triplearray battleEnemy
    * 
    */
    public List<IntTripleType> getBattleEnemyTripleList(){
        return battleEnemyTripleList;
    }       

    /**
    * 地图坐标
    * pair missionLocation
    * 
    */
    public IntPairType getMissionLocationPair(){
        return missionLocationPair;
    }

    /**
    * 战斗奖励
    * intarray battleReward
    * 
    */
    public List<Integer> getBattleRewardList(){
        return battleRewardList;
    }


    /**
    * 宝箱配置
    * intarray cratesReward
    * 
    */
    public List<Integer> getCratesRewardList(){
        return cratesRewardList;
    }


}
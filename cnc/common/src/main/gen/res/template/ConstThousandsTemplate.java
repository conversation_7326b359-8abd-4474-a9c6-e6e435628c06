package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "Q_千人同屏.xlsx", node = "const_thousands.xml", isConst = true)
public class ConstThousandsTemplate implements IResConstTemplate  {

    /**
     * 代表0到99编号为阵营1，100~199编号为阵营2，200~299编号为阵营3.超出该编号的无阵营。
     */
    private List<Integer> leagueGroup;
    /**
     * 三个联盟的联盟简称和全称。
     */
    private List<IntPairType> leagueNameList;
    /**
     * 阵营1的迁城区域:(40,50)为中心点，半径为400米的区域。
     */
    private List<Integer> bornregion1;
    /**
     * 阵营2的迁城区域:(140,50)为中心点，半径为400米的区域。
     */
    private List<Integer> bornregion2;
    /**
     * 阵营3的迁城区域:(90,130)为中心点，半径为400米的区域。
     */
    private List<Integer> bornregion3;
    /**
     * 阵营1的集合区域:(80,73)为中心点，半径为30米的区域。
     */
    private List<Integer> battleregion1;
    /**
     * 阵营2的集合区域:(100,73)为中心点，半径为30米的区域。
     */
    private List<Integer> battleregion2;
    /**
     * 阵营3的集合区域:(90,84)为中心点，半径为30米的区域。55,63,30
     */
    private List<Integer> battleregion3;
    /**
     * 攻击检测的半径=300米
     */
    private int attackLength = 0;
    /**
     * 追击半径距离，单位米
     */
    private int chaseLength = 0;
    /**
     * 低于30%血量，有30%概率逃跑
     */
    private List<Integer> escapeCondition;
    /**
     * 逃跑半径距离，单位米
     */
    private int escapeLength = 0;
    /**
     * 如配置所示，相当于在(100,100),(200,100),(200,200),(100,200)的大正方形区域内战斗。
     */
    private List<IntPairType> battleRange;
    /**
     * 行军到集合区域时，休眠的时间。单位秒。
     */
    private int sleepAfterMoveTime = 0;
    /**
     * 部分配置字段，不受手动关闭简化模式影响
     */
    /**
     * 野怪算几个部队模型
     */
    private int monsterRatio = 0;
    /**
     * 同屏最大普攻飘字同时存在数
     */
    private List<Integer> atkDamageFlyNumMax;
    /**
     * 同屏最大技能飘字同时存在数
     */
    private List<Integer> skillDamageFlyNumMax;
    /**
     * 同屏最大buff飘字同时存在数
     */
    private List<Integer> buffFlyNumMax;
    /**
     * 同屏所有飘字汇总后的总数量上线
     */
    private List<Integer> allNumMax;
    /**
     * 特殊类必须显示的飘字数量上限
     */
    private List<Integer> specialFlyNumMax;
    /**
     * 同屏最大战斗头像数量
     */
    private List<Integer> simpleWorldBattleNumMax;


    public List<Integer> getLeagueGroup(){
        return this.leagueGroup;
    }

    public List<IntPairType> getLeagueNameList(){
        return this.leagueNameList;
    }

    public List<Integer> getBornregion1(){
        return this.bornregion1;
    }

    public List<Integer> getBornregion2(){
        return this.bornregion2;
    }

    public List<Integer> getBornregion3(){
        return this.bornregion3;
    }

    public List<Integer> getBattleregion1(){
        return this.battleregion1;
    }

    public List<Integer> getBattleregion2(){
        return this.battleregion2;
    }

    public List<Integer> getBattleregion3(){
        return this.battleregion3;
    }

    public int getAttackLength(){
        return this.attackLength;
    }

    public int getChaseLength(){
        return this.chaseLength;
    }

    public List<Integer> getEscapeCondition(){
        return this.escapeCondition;
    }

    public int getEscapeLength(){
        return this.escapeLength;
    }

    public List<IntPairType> getBattleRange(){
        return this.battleRange;
    }

    public int getSleepAfterMoveTime(){
        return this.sleepAfterMoveTime;
    }


    public int getMonsterRatio(){
        return this.monsterRatio;
    }

    public List<Integer> getAtkDamageFlyNumMax(){
        return this.atkDamageFlyNumMax;
    }

    public List<Integer> getSkillDamageFlyNumMax(){
        return this.skillDamageFlyNumMax;
    }

    public List<Integer> getBuffFlyNumMax(){
        return this.buffFlyNumMax;
    }

    public List<Integer> getAllNumMax(){
        return this.allNumMax;
    }

    public List<Integer> getSpecialFlyNumMax(){
        return this.specialFlyNumMax;
    }

    public List<Integer> getSimpleWorldBattleNumMax(){
        return this.simpleWorldBattleNumMax;
    }

    @Override
    public int getId() {
        return 0;
    }
}

package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_节日活动.xlsx", node="festival_bp_level.xml")
public class FestivalBpLevelTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 当前等级
    * int curLevel
    * 
    */
    @ResAttribute("curLevel")
    private  int curLevel;

    /**
    * 升级到下一级所需的积分
    * int score
    * 
    */
    @ResAttribute("score")
    private  int score;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 当前等级
    * int curLevel
    * 
    */
    public int getCurLevel(){
        return curLevel;
    }

    /**
    * 升级到下一级所需的积分
    * int score
    * 
    */
    public int getScore(){
        return score;
    }


}
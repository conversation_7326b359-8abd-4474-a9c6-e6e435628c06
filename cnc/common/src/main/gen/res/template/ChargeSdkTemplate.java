package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_支付.xlsx", node="charge_sdk.xml")
public class ChargeSdkTemplate implements IResTemplate  {

    /**
    * sdk产品id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 米大师productId
    * string midasProductId
    * 
    */
    @ResAttribute("midasProductId")
    private  String midasProductId;

    /**
    * 礼包价值（后台qlog数分用）
    * float price
    * 
    */

    @ResAttribute("price")
    private  float price;
    /**
    * 礼包消耗代金券数量
    * int goodsNeedVoucher
    * 
    */
    @ResAttribute("goodsNeedVoucher")
    private  int goodsNeedVoucher;

    /**
    * 荣耀金券道具ID
    * int honourVoucherItemId
    * 
    */
    @ResAttribute("honourVoucherItemId")
    private  int honourVoucherItemId;



    /**
    * sdk产品id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 米大师productId
    * string midasProductId
    * 
    */
    public String getMidasProductId(){
        return midasProductId;
    }
    /**
    * 礼包价值（后台qlog数分用）
    * float price
    * 
    */
    public float getPrice(){
        return price;
    }

    /**
    * 礼包消耗代金券数量
    * int goodsNeedVoucher
    * 
    */
    public int getGoodsNeedVoucher(){
        return goodsNeedVoucher;
    }

    /**
    * 荣耀金券道具ID
    * int honourVoucherItemId
    * 
    */
    public int getHonourVoucherItemId(){
        return honourVoucherItemId;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "P_排行榜.xlsx", node = "const_rank.xml", isConst = true)
public class ConstRankTemplate implements IResConstTemplate  {

    /**
     * 援助资源换算系数
     */
    private List<IntPairType> resourcePoint;
    /**
     * 采集积分计算系数
     */
    private List<IntPairType> collectPoint;
    /**
     * 单页数量
     */
    private int pageSize = 0;
    /**
     * 军团排行榜重置周期（天）
     */
    private int weeklyrefresh = 0;
    /**
     * 向上或者向下箭头的判定时间间隔（秒）
     */
    private int changeIconJudgmentTime = 0;


    public List<IntPairType> getResourcePoint(){
        return this.resourcePoint;
    }

    public List<IntPairType> getCollectPoint(){
        return this.collectPoint;
    }

    public int getPageSize(){
        return this.pageSize;
    }

    public int getWeeklyrefresh(){
        return this.weeklyrefresh;
    }

    public int getChangeIconJudgmentTime(){
        return this.changeIconJudgmentTime;
    }

    @Override
    public int getId() {
        return 0;
    }
}

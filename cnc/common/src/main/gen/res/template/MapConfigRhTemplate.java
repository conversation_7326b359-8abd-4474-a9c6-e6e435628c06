package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_地图关卡表【RH】.xlsx", node="map_config_rh.xml")
public class MapConfigRhTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 地图文件夹名字
    * string map_name
    * 
    */
    @ResAttribute("map_name")
    private  String map_name;

    /**
    * 地图类型
    * int map_type
    * 
    */
    @ResAttribute("map_type")
    private  int map_type;

    /**
    * 游戏计时
    * int GameTimer
    * 
    */
    @ResAttribute("GameTimer")
    private  int GameTimer;

    /**
    * 难度
    * int map_level
    * 
    */
    @ResAttribute("map_level")
    private  int map_level;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 地图文件夹名字
    * string map_name
    * 
    */
    public String getMapName(){
        return map_name;
    }
    /**
    * 地图类型
    * int map_type
    * 
    */
    public int getMapType(){
        return map_type;
    }

    /**
    * 游戏计时
    * int GameTimer
    * 
    */
    public int getGameTimer(){
        return GameTimer;
    }

    /**
    * 难度
    * int map_level
    * 
    */
    public int getMapLevel(){
        return map_level;
    }


}
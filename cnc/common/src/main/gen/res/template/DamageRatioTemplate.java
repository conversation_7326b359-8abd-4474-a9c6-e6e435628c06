package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_战斗常量表.xlsx", node="damage_ratio.xml")
public class DamageRatioTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 重伤比类型
    * int damageRatioType
    * 
    */
    @ResAttribute("damageRatioType")
    private  int damageRatioType;

    /**
    * 战斗类型枚举
    * CommonEnum.BattleType battleType
    * 
    */
    @ResAttribute("battleType")
    private CommonEnum.BattleType battleType;

    /**
    * 联盟建筑类型
    * CommonEnum.DamageRatioTypeEnum ratioType
    * 
    */
    @ResAttribute("ratioType")
    private CommonEnum.DamageRatioTypeEnum ratioType;

    /**
    * 部队类型枚举
    * CommonEnum.SceneObjType leftObjType
    * 
    */
    @ResAttribute("leftObjType")
    private CommonEnum.SceneObjType leftObjType;

    /**
    * 重伤比例
    * float leftSevereWoundRatio
    * 
    */

    @ResAttribute("leftSevereWoundRatio")
    private  float leftSevereWoundRatio;
    /**
    * 死兵比例
    * float leftDeadRatio
    * 
    */

    @ResAttribute("leftDeadRatio")
    private  float leftDeadRatio;
    /**
    * 部队类型枚举
    * CommonEnum.SceneObjType rightObjType
    * 
    */
    @ResAttribute("rightObjType")
    private CommonEnum.SceneObjType rightObjType;

    /**
    * 重伤比例
    * float rightSevereWoundRatio
    * 
    */

    @ResAttribute("rightSevereWoundRatio")
    private  float rightSevereWoundRatio;
    /**
    * 死兵比例
    * float rightDeadRatio
    * 
    */

    @ResAttribute("rightDeadRatio")
    private  float rightDeadRatio;


    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 重伤比类型
    * int damageRatioType
    * 
    */
    public int getDamageRatioType(){
        return damageRatioType;
    }


    /**
    * 战斗类型枚举
    * CommonEnum.BattleType battleType
    * 
    */
    public CommonEnum.BattleType getBattleType(){
        return battleType;
    }

    /**
    * 联盟建筑类型
    * CommonEnum.DamageRatioTypeEnum ratioType
    * 
    */
    public CommonEnum.DamageRatioTypeEnum getRatioType(){
        return ratioType;
    }

    /**
    * 部队类型枚举
    * CommonEnum.SceneObjType leftObjType
    * 
    */
    public CommonEnum.SceneObjType getLeftObjType(){
        return leftObjType;
    }
    /**
    * 重伤比例
    * float leftSevereWoundRatio
    * 
    */
    public float getLeftSevereWoundRatio(){
        return leftSevereWoundRatio;
    }

    /**
    * 死兵比例
    * float leftDeadRatio
    * 
    */
    public float getLeftDeadRatio(){
        return leftDeadRatio;
    }


    /**
    * 部队类型枚举
    * CommonEnum.SceneObjType rightObjType
    * 
    */
    public CommonEnum.SceneObjType getRightObjType(){
        return rightObjType;
    }
    /**
    * 重伤比例
    * float rightSevereWoundRatio
    * 
    */
    public float getRightSevereWoundRatio(){
        return rightSevereWoundRatio;
    }

    /**
    * 死兵比例
    * float rightDeadRatio
    * 
    */
    public float getRightDeadRatio(){
        return rightDeadRatio;
    }


}
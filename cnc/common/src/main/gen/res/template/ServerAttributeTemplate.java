package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="F_服务器多服和移民表.xlsx", node="server_attribute.xml")
public class ServerAttributeTemplate implements IResTemplate  {

    /**
    * 序号
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 所属的分组编号
    * int groupid
    * 
    */
    @ResAttribute("groupid")
    private  int groupid;

    /**
    * milestoneTemplateId
    * int milestoneTemplateId
    * 
    */
    @ResAttribute("milestoneTemplateId")
    private  int milestoneTemplateId;



    /**
    * 序号
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 所属的分组编号
    * int groupid
    * 
    */
    public int getGroupid(){
        return groupid;
    }

    /**
    * milestoneTemplateId
    * int milestoneTemplateId
    * 
    */
    public int getMilestoneTemplateId(){
        return milestoneTemplateId;
    }


}
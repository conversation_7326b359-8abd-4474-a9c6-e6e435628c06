package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="S_士兵表_阵型.xlsx", node="formation_const.xml")
public class FormationConstTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 名称
    * string name
    * 
    */
    @ResAttribute("name")
    private  String name;

    /**
    * 类型
    * string type
    * 
    */
    @ResAttribute("type")
    private  String type;

    /**
    * 数值
    * string value
    * 
    */
    @ResAttribute("value")
    private  String value;

    /**
    * 描述
    * string desc
    * 
    */
    @ResAttribute("desc")
    private  String desc;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 名称
    * string name
    * 
    */
    public String getName(){
        return name;
    }
    /**
    * 类型
    * string type
    * 
    */
    public String getType(){
        return type;
    }
    /**
    * 数值
    * string value
    * 
    */
    public String getValue(){
        return value;
    }
    /**
    * 描述
    * string desc
    * 
    */
    public String getDesc(){
        return desc;
    }

}
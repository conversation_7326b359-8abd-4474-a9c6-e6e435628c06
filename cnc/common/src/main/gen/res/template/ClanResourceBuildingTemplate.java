package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_联盟配置表.xlsx", node="clan_resource_building.xml")
public class ClanResourceBuildingTemplate implements IResTemplate  {

    /**
    * 建筑ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 解锁需要的军团人数
    * int needMemberNums
    * 
    */
    @ResAttribute("needMemberNums")
    private  int needMemberNums;

    /**
    * 解锁需要的军团势力
    * int needpower
    * 
    */
    @ResAttribute("needpower")
    private  int needpower;

    /**
    * 数量上限
    * int limit
    * 
    */
    @ResAttribute("limit")
    private  int limit;

    /**
    * 建筑消耗资源
    * pairarray resources
    * 
    */
    @ResAttribute("resources")
    private List<IntPairType> resourcesPairList;

    /**
    * 集结、驻防兵力上限
    * int maxSoldierNum
    * 
    */
    @ResAttribute("maxSoldierNum")
    private  int maxSoldierNum;

    /**
    * 单兵重建速度加成
(每n个兵提供1速度)
    * int SoldierRebuildRatio
    * 
    */
    @ResAttribute("SoldierRebuildRatio")
    private  int SoldierRebuildRatio;

    /**
    * 基础建造速度
    * int basicSpeed
    * 
    */
    @ResAttribute("basicSpeed")
    private  int basicSpeed;

    /**
    * 建造工程总量
    * int totalWorks
    * 
    */
    @ResAttribute("totalWorks")
    private  int totalWorks;

    /**
    * 积分获取系数
    * int coefficient
    * 
    */
    @ResAttribute("coefficient")
    private  int coefficient;

    /**
    * 初始采集速度
    * float resSpeed
    * 
    */

    @ResAttribute("resSpeed")
    private  float resSpeed;
    /**
    * 采集速度加成
    * int resSpeedUpRate
    * 
    */
    @ResAttribute("resSpeedUpRate")
    private  int resSpeedUpRate;

    /**
    * 默认带有资源量
    * int resNum
    * 
    */
    @ResAttribute("resNum")
    private  int resNum;

    /**
    * 资源种类
    * CommonEnum.CurrencyType resType
    * 
    */
    @ResAttribute("resType")
    private CommonEnum.CurrencyType resType;



    /**
    * 建筑ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 解锁需要的军团人数
    * int needMemberNums
    * 
    */
    public int getNeedMemberNums(){
        return needMemberNums;
    }

    /**
    * 解锁需要的军团势力
    * int needpower
    * 
    */
    public int getNeedpower(){
        return needpower;
    }

    /**
    * 数量上限
    * int limit
    * 
    */
    public int getLimit(){
        return limit;
    }


    /**
    * 建筑消耗资源
    * pairarray resources
    * 
    */
    public List<IntPairType> getResourcesPairList(){
        return resourcesPairList;
    }
    /**
    * 集结、驻防兵力上限
    * int maxSoldierNum
    * 
    */
    public int getMaxSoldierNum(){
        return maxSoldierNum;
    }

    /**
    * 单兵重建速度加成
(每n个兵提供1速度)
    * int SoldierRebuildRatio
    * 
    */
    public int getSoldierRebuildRatio(){
        return SoldierRebuildRatio;
    }

    /**
    * 基础建造速度
    * int basicSpeed
    * 
    */
    public int getBasicSpeed(){
        return basicSpeed;
    }

    /**
    * 建造工程总量
    * int totalWorks
    * 
    */
    public int getTotalWorks(){
        return totalWorks;
    }

    /**
    * 积分获取系数
    * int coefficient
    * 
    */
    public int getCoefficient(){
        return coefficient;
    }

    /**
    * 初始采集速度
    * float resSpeed
    * 
    */
    public float getResSpeed(){
        return resSpeed;
    }

    /**
    * 采集速度加成
    * int resSpeedUpRate
    * 
    */
    public int getResSpeedUpRate(){
        return resSpeedUpRate;
    }

    /**
    * 默认带有资源量
    * int resNum
    * 
    */
    public int getResNum(){
        return resNum;
    }


    /**
    * 资源种类
    * CommonEnum.CurrencyType resType
    * 
    */
    public CommonEnum.CurrencyType getResType(){
        return resType;
    }

}
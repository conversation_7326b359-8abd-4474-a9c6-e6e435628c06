package res.template;

import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.IntPairType;


/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_城建_RH.xlsx", node="inner_build_RH.xml")
public class InnerBuildRhTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 占地大小
    * pair modelSize
    * 
    */
    @ResAttribute("modelSize")
    private IntPairType modelSizePair;

    /**
    * 初始解锁数量
    * int newbieUnlockNum
    * 
    */
    @ResAttribute("newbieUnlockNum")
    private  int newbieUnlockNum;

    /**
    * 数量上限
    * int numMax
    * 
    */
    @ResAttribute("numMax")
    private  int numMax;

    /**
    * 战斗建筑id
    * int unitsConfigId
    * 
    */
    @ResAttribute("unitsConfigId")
    private  int unitsConfigId;

    /**
    * 建筑类型
    * int buildTag
    * 
    */
    @ResAttribute("buildTag")
    private  int buildTag;

    /**
    * 建筑位置是否固定
    * bool buildLock
    * 
    */
    @ResAttribute("buildLock")
    private  boolean buildLock;

    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 占地大小
    * pair modelSize
    * 
    */
    public IntPairType getModelSizePair(){
        return modelSizePair;
    }

    /**
    * 初始解锁数量
    * int newbieUnlockNum
    * 
    */
    public int getNewbieUnlockNum(){
        return newbieUnlockNum;
    }

    /**
    * 数量上限
    * int numMax
    * 
    */
    public int getNumMax(){
        return numMax;
    }

    /**
    * 战斗建筑id
    * int unitsConfigId
    * 
    */
    public int getUnitsConfigId(){
        return unitsConfigId;
    }

    /**
    * 建筑类型
    * int buildTag
    * 
    */
    public int getBuildTag(){
        return buildTag;
    }

    /**
    * 建筑位置是否固定
    * bool buildLock
    * 
    */
    public boolean getBuildLock(){
        return buildLock;
    }

}
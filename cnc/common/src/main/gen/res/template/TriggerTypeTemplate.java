package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_支付.xlsx", node="trigger_type.xml")
public class TriggerTypeTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 触发类型
    * int triggerType
    * 
    */
    @ResAttribute("triggerType")
    private  int triggerType;

    /**
    * 触发参数
    * string triggerParam
    * 
    */
    @ResAttribute("triggerParam")
    private  String triggerParam;

    /**
    * 特殊逻辑
    * string triggerSpecialParam
    * 
    */
    @ResAttribute("triggerSpecialParam")
    private  String triggerSpecialParam;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 触发类型
    * int triggerType
    * 
    */
    public int getTriggerType(){
        return triggerType;
    }

    /**
    * 触发参数
    * string triggerParam
    * 
    */
    public String getTriggerParam(){
        return triggerParam;
    }
    /**
    * 特殊逻辑
    * string triggerSpecialParam
    * 
    */
    public String getTriggerSpecialParam(){
        return triggerSpecialParam;
    }

}
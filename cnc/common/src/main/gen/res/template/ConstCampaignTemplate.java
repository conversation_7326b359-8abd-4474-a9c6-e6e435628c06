package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "Z_战役配置表【RH】.xlsx", node = "const_campaign.xml", isConst = true)
public class ConstCampaignTemplate implements IResConstTemplate  {

    /**
     * 战役初始背包数量
     */
    private int startStorage = 0;
    /**
     * 战役可出战数量
     */
    private int battleCost = 0;
    /**
     * 疲劳效果
     */
    private List<Integer> fatiguEeffectLeve;
    /**
     * 视频
     */
    private String video = "";     
    /**
     * npc对话文本
     */
    private String SessionText = "";     
    /**
     * 事件类型概率百分比
     */
    private List<Integer> event_type_weight;
    /**
     * 玩家带进战役的道具类型，可带出
     */
    private int item_type = 0;
    /**
     * 初始建筑
     */
    private List<Integer> defaultBuildingPermanent;
    /**
     * 玩家单次战役消耗值
     */
    private int playerInitEnergyCost = 0;
    /**
     * 战役内默认解锁的事件ID
     */
    private List<Integer> defaultUnlockEvent;
    /**
     * 战斗中，每个电厂增加 电量最大值
     */
    private int PowerPlantLevelMaxPower = 0;
    /**
     * 战斗中，每个电厂增加 每跳回电数量
     */
    private int PowerPlantLevelReplyPower = 0;
    /**
     * 单位最大星级（前端用）
     */
    private int UnitMaxStar = 0;
    /**
     * 集训概率，星级差值_升星概率
     */
    private List<Integer> EventStarUp;
    /**
     * 新手引导战役难度id
     */
    private int GuideCampaignDifficultyId = 0;
    /**
     * 新手引导战役npc对话文本
     */
    private String NoviceGuidedSession = "";     
    /**
     * 持续在线时长触发防守战
     */
    private List<IntPairType> OnlineTime;


    public int getStartStorage(){
        return this.startStorage;
    }

    public int getBattleCost(){
        return this.battleCost;
    }

    public List<Integer> getFatiguEeffectLeve(){
        return this.fatiguEeffectLeve;
    }

    public String getVideo(){
        return this.video;
    }   

    public String getSessionText(){
        return this.SessionText;
    }   

    public List<Integer> getEventTypeWeight(){
        return this.event_type_weight;
    }

    public int getItemType(){
        return this.item_type;
    }

    public List<Integer> getDefaultBuildingPermanent(){
        return this.defaultBuildingPermanent;
    }

    public int getPlayerInitEnergyCost(){
        return this.playerInitEnergyCost;
    }

    public List<Integer> getDefaultUnlockEvent(){
        return this.defaultUnlockEvent;
    }

    public int getPowerPlantLevelMaxPower(){
        return this.PowerPlantLevelMaxPower;
    }

    public int getPowerPlantLevelReplyPower(){
        return this.PowerPlantLevelReplyPower;
    }

    public int getUnitMaxStar(){
        return this.UnitMaxStar;
    }

    public List<Integer> getEventStarUp(){
        return this.EventStarUp;
    }

    public int getGuideCampaignDifficultyId(){
        return this.GuideCampaignDifficultyId;
    }

    public String getNoviceGuidedSession(){
        return this.NoviceGuidedSession;
    }   

    public List<IntPairType> getOnlineTime(){
        return this.OnlineTime;
    }

    @Override
    public int getId() {
        return 0;
    }
}

package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "X_事件行为流.xlsx", node = "const_newbie_svr.xml", isConst = true)
public class ConstNewbieSvrTemplate implements IResConstTemplate  {

    /**
     * 新手只有油田可以领取一次资源（1对应的是石油枚举）
     */
    private int firstProduceCurrencyType = 0;
    /**
     * 新手期间油田领取的石油数
     */
    private int firstProduceCurrencyNum = 0;
    /**
     * 新手第一次训练士兵的时长（秒)
     */
    private int firstSoldierTrainSeconds = 0;
    /**
     * 新手期间击杀野怪的最大等级（影响新手结束后搜索野怪的等级）
     */
    private int newbieKillMonsterMaxLevel = 0;
    /**
     * 新手流程中可以免费获取的英雄id，第二个被追击的英雄
     */
    private int freeHeroId = 0;
    /**
     * 新手流程中收集资源数量（纯显示）
     */
    private int resourceNum = 0;
    /**
     * 初始资源数量
     */
    private List<IntPairType> resource;
    /**
     * 新手战斗起止id
     */
    private IntPairType newbieInnerBattleId;
    /**
     * 新手第一次搜索内城新地图起止id
     */
    private IntPairType newbieMapSearchId;


    public int getFirstProduceCurrencyType(){
        return this.firstProduceCurrencyType;
    }

    public int getFirstProduceCurrencyNum(){
        return this.firstProduceCurrencyNum;
    }

    public int getFirstSoldierTrainSeconds(){
        return this.firstSoldierTrainSeconds;
    }

    public int getNewbieKillMonsterMaxLevel(){
        return this.newbieKillMonsterMaxLevel;
    }

    public int getFreeHeroId(){
        return this.freeHeroId;
    }

    public int getResourceNum(){
        return this.resourceNum;
    }

    public List<IntPairType> getResource(){
        return this.resource;
    }

    public IntPairType getNewbieInnerBattleId(){
        return this.newbieInnerBattleId;
    }

    public IntPairType getNewbieMapSearchId(){
        return this.newbieMapSearchId;
    }

    @Override
    public int getId() {
        return 0;
    }
}

package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="S_士兵表_兵种.xlsx", node="resource_vehicle_status.xml")
public class ResourceVehicleStatusTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 状态描述
    * string statusDes
    * 
    */
    @ResAttribute("statusDes")
    private  String statusDes;

    /**
    * 耐久度区间
    * intarray durabilityRange
    * 
    */
    @ResAttribute("durabilityRange")
    private List<Integer> durabilityRangeList;

    /**
    * 攻击力比例
    * float atkRate
    * 
    */

    @ResAttribute("atkRate")
    private  float atkRate;


    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 状态描述
    * string statusDes
    * 
    */
    public String getStatusDes(){
        return statusDes;
    }

    /**
    * 耐久度区间
    * intarray durabilityRange
    * 
    */
    public List<Integer> getDurabilityRangeList(){
        return durabilityRangeList;
    }

    /**
    * 攻击力比例
    * float atkRate
    * 
    */
    public float getAtkRate(){
        return atkRate;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_大世界野怪投放区域.xlsx", node="big_scene_search.xml")
public class BigSceneSearchTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * kvk剧本id
    * int storyId
    * 
    */
    @ResAttribute("storyId")
    private  int storyId;

    /**
    * 等级
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;

    /**
    * 搜索范围
    * int searchRange
    * 
    */
    @ResAttribute("searchRange")
    private  int searchRange;

    /**
    * 补怪逻辑参数
    * int para
    * 
    */
    @ResAttribute("para")
    private  int para;

    /**
    * 补怪最小距离
    * int monsterMinDis
    * 
    */
    @ResAttribute("monsterMinDis")
    private  int monsterMinDis;

    /**
    * 补怪最大距离
    * int monsterMaxDis
    * 
    */
    @ResAttribute("monsterMaxDis")
    private  int monsterMaxDis;

    /**
    * 补怪存活时间
    * int monsterLifeTime
    * 
    */
    @ResAttribute("monsterLifeTime")
    private  int monsterLifeTime;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * kvk剧本id
    * int storyId
    * 
    */
    public int getStoryId(){
        return storyId;
    }

    /**
    * 等级
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }

    /**
    * 搜索范围
    * int searchRange
    * 
    */
    public int getSearchRange(){
        return searchRange;
    }

    /**
    * 补怪逻辑参数
    * int para
    * 
    */
    public int getPara(){
        return para;
    }

    /**
    * 补怪最小距离
    * int monsterMinDis
    * 
    */
    public int getMonsterMinDis(){
        return monsterMinDis;
    }

    /**
    * 补怪最大距离
    * int monsterMaxDis
    * 
    */
    public int getMonsterMaxDis(){
        return monsterMaxDis;
    }

    /**
    * 补怪存活时间
    * int monsterLifeTime
    * 
    */
    public int getMonsterLifeTime(){
        return monsterLifeTime;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="T_通行证.xlsx", node="bp_schedule.xml")
public class BpScheduleTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 开启时间
    * int openTime
    * 
    */
    @ResAttribute("openTime")
    private  int openTime;

    /**
    * 分服开启时间延时
    * pairarray openDelay
    * 
    */
    @ResAttribute("openDelay")
    private List<IntPairType> openDelayPairList;

    /**
    * 持续时间
    * int duration
    * 
    */
    @ResAttribute("duration")
    private  int duration;

    /**
    * 第一周周任务ID
    * intarray weeklyMission1
    * 
    */
    @ResAttribute("weeklyMission1")
    private List<Integer> weeklyMission1List;

    /**
    * 第二周周任务ID
    * intarray weeklyMission2
    * 
    */
    @ResAttribute("weeklyMission2")
    private List<Integer> weeklyMission2List;

    /**
    * 第三周周任务ID
    * intarray weeklyMission3
    * 
    */
    @ResAttribute("weeklyMission3")
    private List<Integer> weeklyMission3List;

    /**
    * 第四周周任务ID
    * intarray weeklyMission4
    * 
    */
    @ResAttribute("weeklyMission4")
    private List<Integer> weeklyMission4List;

    /**
    * 第五周周任务ID
    * intarray weeklyMission5
    * 
    */
    @ResAttribute("weeklyMission5")
    private List<Integer> weeklyMission5List;

    /**
    * 赛季任务ID
    * intarray sessionMission
    * 
    */
    @ResAttribute("sessionMission")
    private List<Integer> sessionMissionList;

    /**
    * 50级之后奖励ID
    * pairarray extraRewardId
    * 
    */
    @ResAttribute("extraRewardId")
    private List<IntPairType> extraRewardIdPairList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 开启时间
    * int openTime
    * 
    */
    public int getOpenTime(){
        return openTime;
    }


    /**
    * 分服开启时间延时
    * pairarray openDelay
    * 
    */
    public List<IntPairType> getOpenDelayPairList(){
        return openDelayPairList;
    }
    /**
    * 持续时间
    * int duration
    * 
    */
    public int getDuration(){
        return duration;
    }


    /**
    * 第一周周任务ID
    * intarray weeklyMission1
    * 
    */
    public List<Integer> getWeeklyMission1List(){
        return weeklyMission1List;
    }


    /**
    * 第二周周任务ID
    * intarray weeklyMission2
    * 
    */
    public List<Integer> getWeeklyMission2List(){
        return weeklyMission2List;
    }


    /**
    * 第三周周任务ID
    * intarray weeklyMission3
    * 
    */
    public List<Integer> getWeeklyMission3List(){
        return weeklyMission3List;
    }


    /**
    * 第四周周任务ID
    * intarray weeklyMission4
    * 
    */
    public List<Integer> getWeeklyMission4List(){
        return weeklyMission4List;
    }


    /**
    * 第五周周任务ID
    * intarray weeklyMission5
    * 
    */
    public List<Integer> getWeeklyMission5List(){
        return weeklyMission5List;
    }


    /**
    * 赛季任务ID
    * intarray sessionMission
    * 
    */
    public List<Integer> getSessionMissionList(){
        return sessionMissionList;
    }


    /**
    * 50级之后奖励ID
    * pairarray extraRewardId
    * 
    */
    public List<IntPairType> getExtraRewardIdPairList(){
        return extraRewardIdPairList;
    }

}
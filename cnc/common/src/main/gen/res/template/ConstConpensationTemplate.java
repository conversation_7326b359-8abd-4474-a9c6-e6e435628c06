package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "X_系统补偿配置.xlsx", node = "const_conpensation.xml", isConst = true)
public class ConstConpensationTemplate implements IResConstTemplate  {

    /**
     * 等待帮助最长时间（秒）
     */
    private int compensateHelpTimeout = 0;
    /**
     * 暂时无意义，设置为1秒
     */
    private int compensateCD = 0;
    /**
     * 系统补偿每周触发的最大次数
     */
    private int compensateCount = 0;
    /**
     * 无盟补偿邮件ID
     */
    private int noAllianceCompensateMailId = 0;
    /**
     * 有盟补偿邮件ID
     */
    private int hasAllianceCompensateMailId = 0;
    /**
     * 1000石油道具Id
     */
    private int oilItemId = 0;
    /**
     * 1000钢铁道具Id
     */
    private int steelItemId = 0;
    /**
     * 750稀土道具Id
     */
    private int rareEarthItemId = 0;
    /**
     * 500泰矿道具Id
     */
    private int tiberiumItemId = 0;
    /**
     * 1分钟训练加速道具ID（60秒）
     */
    private int oneMinuteForTrainItemId = 0;
    /**
     * 1分钟治疗加速道具ID（60秒）
     */
    private int oneMinuteForTreatItemId = 0;
    /**
     * 客户端系统补偿表演专用：随机半径最小值:单位米
     */
    private int minRadius = 0;
    /**
     * 客户端系统补偿表演专用：随机半径最大值:单位米
     */
    private int maxRadius = 0;
    /**
     * 客户端系统补偿表演专用：伪行军速度:单位厘米/秒
     */
    private int fakeMarchSpeed = 0;
    /**
     * 客户端系统补偿表演专用：引导对话ID
     */
    private int guideDialogId = 0;
    /**
     * 运输部队（资源援助）的基础移动速度：单位厘米/秒（和大世界行军单位一致）
     */
    private int transferTroopSpeed = 0;
    /**
     * 资源援助报告邮件ID：援助他人
     */
    private int rssAssistReport = 0;
    /**
     * 资源援助报告邮件ID：被他人援助
     */
    private int rssAssistedReport = 0;
    /**
     * 资源援助失败回收资源的邮件ID
     */
    private int rssAssistFailedReport = 0;
    /**
     * 系统援助邮件，服务器存储时间的最大时间：2天，单位秒，超过时间后自动发送。
     */
    private int rssAssistSystemSaveTime = 0;
    /**
     * 系统援助邮件，服务器存储数量的最大数量，5个，超过数量后自动发送。
     */
    private int rssAssistSystemSaveNum = 0;
    /**
     * 资源援助，运输机头像
     */
    private int rssAssistIcon = 0;


    public int getCompensateHelpTimeout(){
        return this.compensateHelpTimeout;
    }

    public int getCompensateCD(){
        return this.compensateCD;
    }

    public int getCompensateCount(){
        return this.compensateCount;
    }

    public int getNoAllianceCompensateMailId(){
        return this.noAllianceCompensateMailId;
    }

    public int getHasAllianceCompensateMailId(){
        return this.hasAllianceCompensateMailId;
    }

    public int getOilItemId(){
        return this.oilItemId;
    }

    public int getSteelItemId(){
        return this.steelItemId;
    }

    public int getRareEarthItemId(){
        return this.rareEarthItemId;
    }

    public int getTiberiumItemId(){
        return this.tiberiumItemId;
    }

    public int getOneMinuteForTrainItemId(){
        return this.oneMinuteForTrainItemId;
    }

    public int getOneMinuteForTreatItemId(){
        return this.oneMinuteForTreatItemId;
    }

    public int getMinRadius(){
        return this.minRadius;
    }

    public int getMaxRadius(){
        return this.maxRadius;
    }

    public int getFakeMarchSpeed(){
        return this.fakeMarchSpeed;
    }

    public int getGuideDialogId(){
        return this.guideDialogId;
    }

    public int getTransferTroopSpeed(){
        return this.transferTroopSpeed;
    }

    public int getRssAssistReport(){
        return this.rssAssistReport;
    }

    public int getRssAssistedReport(){
        return this.rssAssistedReport;
    }

    public int getRssAssistFailedReport(){
        return this.rssAssistFailedReport;
    }

    public int getRssAssistSystemSaveTime(){
        return this.rssAssistSystemSaveTime;
    }

    public int getRssAssistSystemSaveNum(){
        return this.rssAssistSystemSaveNum;
    }

    public int getRssAssistIcon(){
        return this.rssAssistIcon;
    }

    @Override
    public int getId() {
        return 0;
    }
}

package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="T_天网.xlsx", node="skynet_shop.xml")
public class SkynetShopTemplate implements IResTemplate  {

    /**
    * 商品ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 道具ID
    * pair itemID
    * 
    */
    @ResAttribute("itemID")
    private IntPairType itemIDPair;

    /**
    * 限购数量
    * int limitNum
    * 
    */
    @ResAttribute("limitNum")
    private  int limitNum;

    /**
    * 兑换价格
    * int price
    * 
    */
    @ResAttribute("price")
    private  int price;

    /**
    * 是否可刷新
    * int refresh
    * 
    */
    @ResAttribute("refresh")
    private  int refresh;



    /**
    * 商品ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 道具ID
    * pair itemID
    * 
    */
    public IntPairType getItemIDPair(){
        return itemIDPair;
    }
    /**
    * 限购数量
    * int limitNum
    * 
    */
    public int getLimitNum(){
        return limitNum;
    }

    /**
    * 兑换价格
    * int price
    * 
    */
    public int getPrice(){
        return price;
    }

    /**
    * 是否可刷新
    * int refresh
    * 
    */
    public int getRefresh(){
        return refresh;
    }


}
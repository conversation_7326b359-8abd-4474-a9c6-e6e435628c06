package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动表.xlsx", node="activity.xml")
public class ActivityTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 子活动是否不能中途加入（默认为允许=False）
    * bool ChildInsertOpen
    * 
    */
    @ResAttribute("ChildInsertOpen")
    private  boolean ChildInsertOpen;

    /**
    * 活动的额外绑定的通用功能类型（Task=绑定任务组;Score=绑定积分）
    * string commonUnitTypes
    * 
    */
    @ResAttribute("commonUnitTypes")
    private  String commonUnitTypes;

    /**
    * 活动的特定逻辑单元
    * CommonEnum.ActivityUnitType specUnitType
    * 
    */
    @ResAttribute("specUnitType")
    private CommonEnum.ActivityUnitType specUnitType;

    /**
    * 子活动在父活动开启x小时后开启
    * int openOffsetHours
    * 
    */
    @ResAttribute("openOffsetHours")
    private  int openOffsetHours;

    /**
    * 活动持续时间（小时）
    * int lastingHours
    * 
    */
    @ResAttribute("lastingHours")
    private  int lastingHours;

    /**
    * 子活动id（在父活动开启x小时后开启）
    * intarray childActivityIdList
    * 
    */
    @ResAttribute("childActivityIdList")
    private List<Integer> childActivityIdListList;

    /**
    * 是否自动下线活动（所有子活动以及unit的内容已经消耗完毕时自动下线）
    * bool autoExpire
    * 
    */
    @ResAttribute("autoExpire")
    private  boolean autoExpire;

    /**
    * 活动结束时清空哪些数据，task=任务相关数据；point=积分相关数据；market=商店相关数据；bundle=礼包相关数据
    * string clearDataOnActivityEnd
    * 
    */
    @ResAttribute("clearDataOnActivityEnd")
    private  String clearDataOnActivityEnd;

    /**
    * 活动结束后回收道具id
    * intarray recycleItem
    * 
    */
    @ResAttribute("recycleItem")
    private List<Integer> recycleItemList;

    /**
    * 活动任务id列表
    * intarray activityTaskIds
    * 
    */
    @ResAttribute("activityTaskIds")
    private List<Integer> activityTaskIdsList;

    /**
    * 活动任务池
    * string taskPoolName
    * 
    */
    @ResAttribute("taskPoolName")
    private  String taskPoolName;

    /**
    * 活动礼包链
    * intarray goodsChainArray
    * 
    */
    @ResAttribute("goodsChainArray")
    private List<Integer> goodsChainArrayList;

    /**
    * 积分活动id
    * string scoreRewardUnitId
    * 
    */
    @ResAttribute("scoreRewardUnitId")
    private  String scoreRewardUnitId;

    /**
    * 活动商店id
    * int activityMarketId
    * 
    */
    @ResAttribute("activityMarketId")
    private  int activityMarketId;

    /**
    * 积分排行配置id
    * int scoreRankId
    * 
    */
    @ResAttribute("scoreRankId")
    private  int scoreRankId;

    /**
    * 活动排行榜id
    * CommonEnum.RankType activityRankType
    * 
    */
    @ResAttribute("activityRankType")
    private CommonEnum.RankType activityRankType;

    /**
    * 活动邮件id
    * int actMailId
    * 
    */
    @ResAttribute("actMailId")
    private  int actMailId;

    /**
    * 邮件展示排行前几名
    * int rankLimit
    * 
    */
    @ResAttribute("rankLimit")
    private  int rankLimit;

    /**
    * 抽奖活动id
    * int lotteryId
    * 
    */
    @ResAttribute("lotteryId")
    private  int lotteryId;

    /**
    * 成长基金付费任务id列表
    * intarray growthFundTaskIds
    * 
    */
    @ResAttribute("growthFundTaskIds")
    private List<Integer> growthFundTaskIdsList;

    /**
    * 拉新活动的配置id
    * int lashinActId
    * 
    */
    @ResAttribute("lashinActId")
    private  int lashinActId;

    /**
    * 循环礼包每日刷新限购
    * bool cycleChargeDailyRefresh
    * 
    */
    @ResAttribute("cycleChargeDailyRefresh")
    private  boolean cycleChargeDailyRefresh;

    /**
    * 是否显示活动日历
    * bool showCalendar
    * 
    */
    @ResAttribute("showCalendar")
    private  boolean showCalendar;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 子活动是否不能中途加入（默认为允许=False）
    * bool ChildInsertOpen
    * 
    */
    public boolean getChildInsertOpen(){
        return ChildInsertOpen;
    }
    /**
    * 活动的额外绑定的通用功能类型（Task=绑定任务组;Score=绑定积分）
    * string commonUnitTypes
    * 
    */
    public String getCommonUnitTypes(){
        return commonUnitTypes;
    }

    /**
    * 活动的特定逻辑单元
    * CommonEnum.ActivityUnitType specUnitType
    * 
    */
    public CommonEnum.ActivityUnitType getSpecUnitType(){
        return specUnitType;
    }
    /**
    * 子活动在父活动开启x小时后开启
    * int openOffsetHours
    * 
    */
    public int getOpenOffsetHours(){
        return openOffsetHours;
    }

    /**
    * 活动持续时间（小时）
    * int lastingHours
    * 
    */
    public int getLastingHours(){
        return lastingHours;
    }


    /**
    * 子活动id（在父活动开启x小时后开启）
    * intarray childActivityIdList
    * 
    */
    public List<Integer> getChildActivityIdListList(){
        return childActivityIdListList;
    }


    /**
    * 是否自动下线活动（所有子活动以及unit的内容已经消耗完毕时自动下线）
    * bool autoExpire
    * 
    */
    public boolean getAutoExpire(){
        return autoExpire;
    }
    /**
    * 活动结束时清空哪些数据，task=任务相关数据；point=积分相关数据；market=商店相关数据；bundle=礼包相关数据
    * string clearDataOnActivityEnd
    * 
    */
    public String getClearDataOnActivityEnd(){
        return clearDataOnActivityEnd;
    }

    /**
    * 活动结束后回收道具id
    * intarray recycleItem
    * 
    */
    public List<Integer> getRecycleItemList(){
        return recycleItemList;
    }


    /**
    * 活动任务id列表
    * intarray activityTaskIds
    * 
    */
    public List<Integer> getActivityTaskIdsList(){
        return activityTaskIdsList;
    }

    /**
    * 活动任务池
    * string taskPoolName
    * 
    */
    public String getTaskPoolName(){
        return taskPoolName;
    }

    /**
    * 活动礼包链
    * intarray goodsChainArray
    * 
    */
    public List<Integer> getGoodsChainArrayList(){
        return goodsChainArrayList;
    }

    /**
    * 积分活动id
    * string scoreRewardUnitId
    * 
    */
    public String getScoreRewardUnitId(){
        return scoreRewardUnitId;
    }
    /**
    * 活动商店id
    * int activityMarketId
    * 
    */
    public int getActivityMarketId(){
        return activityMarketId;
    }

    /**
    * 积分排行配置id
    * int scoreRankId
    * 
    */
    public int getScoreRankId(){
        return scoreRankId;
    }


    /**
    * 活动排行榜id
    * CommonEnum.RankType activityRankType
    * 
    */
    public CommonEnum.RankType getActivityRankType(){
        return activityRankType;
    }
    /**
    * 活动邮件id
    * int actMailId
    * 
    */
    public int getActMailId(){
        return actMailId;
    }

    /**
    * 邮件展示排行前几名
    * int rankLimit
    * 
    */
    public int getRankLimit(){
        return rankLimit;
    }

    /**
    * 抽奖活动id
    * int lotteryId
    * 
    */
    public int getLotteryId(){
        return lotteryId;
    }


    /**
    * 成长基金付费任务id列表
    * intarray growthFundTaskIds
    * 
    */
    public List<Integer> getGrowthFundTaskIdsList(){
        return growthFundTaskIdsList;
    }

    /**
    * 拉新活动的配置id
    * int lashinActId
    * 
    */
    public int getLashinActId(){
        return lashinActId;
    }


    /**
    * 循环礼包每日刷新限购
    * bool cycleChargeDailyRefresh
    * 
    */
    public boolean getCycleChargeDailyRefresh(){
        return cycleChargeDailyRefresh;
    }

    /**
    * 是否显示活动日历
    * bool showCalendar
    * 
    */
    public boolean getShowCalendar(){
        return showCalendar;
    }

}
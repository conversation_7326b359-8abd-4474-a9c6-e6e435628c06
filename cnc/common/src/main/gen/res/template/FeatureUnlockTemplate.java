package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="G_功能解锁.xlsx", node="feature_unlock.xml")
public class FeatureUnlockTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 解锁条件关系
    * CommonEnum.ConditionEnum conditionrelation
    * 
    */
    @ResAttribute("conditionrelation")
    private CommonEnum.ConditionEnum conditionrelation;

    /**
    * 建筑等级
    * pairarray buildlevel
    * 
    */
    @ResAttribute("buildlevel")
    private List<IntPairType> buildlevelPairList;

    /**
    * 主线任务
    * int task
    * 
    */
    @ResAttribute("task")
    private  int task;

    /**
    * 功能解锁
    * CommonEnum.ModuleEnum unLock
    * 
    */
    @ResAttribute("unLock")
    private CommonEnum.ModuleEnum unLock;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 解锁条件关系
    * CommonEnum.ConditionEnum conditionrelation
    * 
    */
    public CommonEnum.ConditionEnum getConditionrelation(){
        return conditionrelation;
    }

    /**
    * 建筑等级
    * pairarray buildlevel
    * 
    */
    public List<IntPairType> getBuildlevelPairList(){
        return buildlevelPairList;
    }
    /**
    * 主线任务
    * int task
    * 
    */
    public int getTask(){
        return task;
    }


    /**
    * 功能解锁
    * CommonEnum.ModuleEnum unLock
    * 
    */
    public CommonEnum.ModuleEnum getUnLock(){
        return unLock;
    }

}
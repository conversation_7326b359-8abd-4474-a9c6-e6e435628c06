package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_掉落物.xlsx", node="drop_group.xml")
public class DropGroupTemplate implements IResTemplate  {

    /**
    * 掉落组ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 掉落半径（厘米）
    * int radius
    * 
    */
    @ResAttribute("radius")
    private  int radius;

    /**
    * 拾取物ID和数量
    * triplearray dropList
    * 
    */
    @ResAttribute("dropList")
    private List<IntTripleType> dropListTripleList;

    /**
    * 同批次拾取物单一最大拾取数量
    * int limit
    * 
    */
    @ResAttribute("limit")
    private  int limit;

    /**
    * 次数限制周期时间(秒)
    * int overTime
    * 
    */
    @ResAttribute("overTime")
    private  int overTime;



    /**
    * 掉落组ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 掉落半径（厘米）
    * int radius
    * 
    */
    public int getRadius(){
        return radius;
    }


    /**
    * 拾取物ID和数量
    * triplearray dropList
    * 
    */
    public List<IntTripleType> getDropListTripleList(){
        return dropListTripleList;
    }       
    /**
    * 同批次拾取物单一最大拾取数量
    * int limit
    * 
    */
    public int getLimit(){
        return limit;
    }

    /**
    * 次数限制周期时间(秒)
    * int overTime
    * 
    */
    public int getOverTime(){
        return overTime;
    }


}
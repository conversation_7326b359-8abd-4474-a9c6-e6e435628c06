package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_触发式引导.xlsx", node="tutorials.xml")
public class TutorialsTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 引导ID
    * int jobId
    * 
    */
    @ResAttribute("jobId")
    private  int jobId;

    /**
    * 步骤
    * int step
    * 
    */
    @ResAttribute("step")
    private  int step;

    /**
    * 开始即上报？
    * int uploadWhenStart
    * 
    */
    @ResAttribute("uploadWhenStart")
    private  int uploadWhenStart;

    /**
    * 上报更新jobId
    * intarray uploadId
    * 
    */
    @ResAttribute("uploadId")
    private List<Integer> uploadIdList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 引导ID
    * int jobId
    * 
    */
    public int getJobId(){
        return jobId;
    }

    /**
    * 步骤
    * int step
    * 
    */
    public int getStep(){
        return step;
    }

    /**
    * 开始即上报？
    * int uploadWhenStart
    * 
    */
    public int getUploadWhenStart(){
        return uploadWhenStart;
    }


    /**
    * 上报更新jobId
    * intarray uploadId
    * 
    */
    public List<Integer> getUploadIdList(){
        return uploadIdList;
    }


}
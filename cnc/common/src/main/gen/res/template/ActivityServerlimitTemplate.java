package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动区服限制表.xlsx", node="activity_serverlimit.xml")
public class ActivityServerlimitTemplate implements IResTemplate  {

    /**
    * 活动日程ID(schedule id)
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 开放类型
    * CommonEnum.ActivityZoneOperateType openType
    * 
    */
    @ResAttribute("openType")
    private CommonEnum.ActivityZoneOperateType openType;

    /**
    * 影响区服
    * intarray serverList
    * 
    */
    @ResAttribute("serverList")
    private List<Integer> serverListList;

    /**
    * 限制时间-开始
    * date affectTime_begin
    * 
    */
    @ResAttribute("affectTime_begin")
    private Date affectTime_beginDt;
    /**
    * 限制时间-结束
    * date affectTime_end
    * 
    */
    @ResAttribute("affectTime_end")
    private Date affectTime_endDt;


    /**
    * 活动日程ID(schedule id)
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 开放类型
    * CommonEnum.ActivityZoneOperateType openType
    * 
    */
    public CommonEnum.ActivityZoneOperateType getOpenType(){
        return openType;
    }

    /**
    * 影响区服
    * intarray serverList
    * 
    */
    public List<Integer> getServerListList(){
        return serverListList;
    }


    /**
    * 限制时间-开始
    * date affectTime_begin
    * 
    */
    public Date getAffecttimeBeginDt(){
        return affectTime_beginDt;
    }

    /**
    * 限制时间-结束
    * date affectTime_end
    * 
    */
    public Date getAffecttimeEndDt(){
        return affectTime_endDt;
    }

}
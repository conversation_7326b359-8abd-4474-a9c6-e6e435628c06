package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动排行表.xlsx", node="activity_score_rank.xml")
public class ActivityScoreRankTemplate implements IResTemplate  {

    /**
    * 积分排行配置id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 积分id
    * intarray scoreIds
    * 
    */
    @ResAttribute("scoreIds")
    private List<Integer> scoreIdsList;

    /**
    * 活动排行榜id
    * int rankId
    * 
    */
    @ResAttribute("rankId")
    private  int rankId;

    /**
    * 活动邮件id
    * int rewardMailId
    * 
    */
    @ResAttribute("rewardMailId")
    private  int rewardMailId;

    /**
    * 邮件展示排行前几名
    * int rankLimit
    * 
    */
    @ResAttribute("rankLimit")
    private  int rankLimit;



    /**
    * 积分排行配置id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 积分id
    * intarray scoreIds
    * 
    */
    public List<Integer> getScoreIdsList(){
        return scoreIdsList;
    }

    /**
    * 活动排行榜id
    * int rankId
    * 
    */
    public int getRankId(){
        return rankId;
    }

    /**
    * 活动邮件id
    * int rewardMailId
    * 
    */
    public int getRewardMailId(){
        return rewardMailId;
    }

    /**
    * 邮件展示排行前几名
    * int rankLimit
    * 
    */
    public int getRankLimit(){
        return rankLimit;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_军团技能.xlsx", node="clan_skill_level.xml")
public class ClanSkillLevelTemplate implements IResTemplate  {

    /**
    * 技能子ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 技能组ID
    * int skillID
    * 
    */
    @ResAttribute("skillID")
    private  int skillID;

    /**
    * 等级
    * int skillLevel
    * 
    */
    @ResAttribute("skillLevel")
    private  int skillLevel;

    /**
    * 增益ID
    * intarray buff
    * 
    */
    @ResAttribute("buff")
    private List<Integer> buffList;

    /**
    * 技能释放所需能量
    * int skillenergy
    * 
    */
    @ResAttribute("skillenergy")
    private  int skillenergy;



    /**
    * 技能子ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 技能组ID
    * int skillID
    * 
    */
    public int getSkillID(){
        return skillID;
    }

    /**
    * 等级
    * int skillLevel
    * 
    */
    public int getSkillLevel(){
        return skillLevel;
    }


    /**
    * 增益ID
    * intarray buff
    * 
    */
    public List<Integer> getBuffList(){
        return buffList;
    }

    /**
    * 技能释放所需能量
    * int skillenergy
    * 
    */
    public int getSkillenergy(){
        return skillenergy;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动表_策划分表.xlsx", node="activity_online.xml")
public class ActivityOnlineTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 在线时长(秒)
    * int onlineSec
    * 
    */
    @ResAttribute("onlineSec")
    private  int onlineSec;

    /**
    * 邮件id
    * int mailId
    * 
    */
    @ResAttribute("mailId")
    private  int mailId;

    /**
    * 奖励
    * pairarray award
    * 
    */
    @ResAttribute("award")
    private List<IntPairType> awardPairList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 在线时长(秒)
    * int onlineSec
    * 
    */
    public int getOnlineSec(){
        return onlineSec;
    }

    /**
    * 邮件id
    * int mailId
    * 
    */
    public int getMailId(){
        return mailId;
    }


    /**
    * 奖励
    * pairarray award
    * 
    */
    public List<IntPairType> getAwardPairList(){
        return awardPairList;
    }

}
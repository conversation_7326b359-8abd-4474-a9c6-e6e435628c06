package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="W_玩家设置表【RH】.xlsx", node="player_rh.xml")
public class PlayerRhTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 最大电量
    * int maxElectric
    * 
    */
    @ResAttribute("maxElectric")
    private  int maxElectric;

    /**
    * 初始电量
    * int startElectric
    * 
    */
    @ResAttribute("startElectric")
    private  int startElectric;

    /**
    * 几秒回一次电（秒）
    * int recoveryTime
    * 
    */
    @ResAttribute("recoveryTime")
    private  int recoveryTime;

    /**
    * 每次回多少电
    * int recoveryValue
    * 
    */
    @ResAttribute("recoveryValue")
    private  int recoveryValue;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 最大电量
    * int maxElectric
    * 
    */
    public int getMaxElectric(){
        return maxElectric;
    }

    /**
    * 初始电量
    * int startElectric
    * 
    */
    public int getStartElectric(){
        return startElectric;
    }

    /**
    * 几秒回一次电（秒）
    * int recoveryTime
    * 
    */
    public int getRecoveryTime(){
        return recoveryTime;
    }

    /**
    * 每次回多少电
    * int recoveryValue
    * 
    */
    public int getRecoveryValue(){
        return recoveryValue;
    }


}
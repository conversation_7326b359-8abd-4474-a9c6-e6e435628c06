package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="S_士兵表_兵种.xlsx", node="resource_vehicle.xml")
public class ResourceVehicleTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 攻击力
    * int atk
    * 
    */
    @ResAttribute("atk")
    private  int atk;

    /**
    * 防御力
    * int def
    * 
    */
    @ResAttribute("def")
    private  int def;

    /**
    * 生命值
    * int hp
    * 
    */
    @ResAttribute("hp")
    private  int hp;

    /**
    * 耐久度
    * int durability
    * 
    */
    @ResAttribute("durability")
    private  int durability;

    /**
    * 战斗力
    * int power
    * 
    */
    @ResAttribute("power")
    private  int power;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 攻击力
    * int atk
    * 
    */
    public int getAtk(){
        return atk;
    }

    /**
    * 防御力
    * int def
    * 
    */
    public int getDef(){
        return def;
    }

    /**
    * 生命值
    * int hp
    * 
    */
    public int getHp(){
        return hp;
    }

    /**
    * 耐久度
    * int durability
    * 
    */
    public int getDurability(){
        return durability;
    }

    /**
    * 战斗力
    * int power
    * 
    */
    public int getPower(){
        return power;
    }


}
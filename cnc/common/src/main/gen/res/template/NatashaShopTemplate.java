package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_抽奖活动表.xlsx", node="natasha_shop.xml")
public class NatashaShopTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 所属类型
    * int group
    * 
    */
    @ResAttribute("group")
    private  int group;

    /**
    * 代币兑换所需数量
    * pairarray price
    * 
    */
    @ResAttribute("price")
    private List<IntPairType> pricePairList;

    /**
    * 道具奖励id
    * pairarray itemId
    * 
    */
    @ResAttribute("itemId")
    private List<IntPairType> itemIdPairList;

    /**
    * 排序权重
    * int sortingWeight
    * 
    */
    @ResAttribute("sortingWeight")
    private  int sortingWeight;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 所属类型
    * int group
    * 
    */
    public int getGroup(){
        return group;
    }


    /**
    * 代币兑换所需数量
    * pairarray price
    * 
    */
    public List<IntPairType> getPricePairList(){
        return pricePairList;
    }

    /**
    * 道具奖励id
    * pairarray itemId
    * 
    */
    public List<IntPairType> getItemIdPairList(){
        return itemIdPairList;
    }
    /**
    * 排序权重
    * int sortingWeight
    * 
    */
    public int getSortingWeight(){
        return sortingWeight;
    }


}
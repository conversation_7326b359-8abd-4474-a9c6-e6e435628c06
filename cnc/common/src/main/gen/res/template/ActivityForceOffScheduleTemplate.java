package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动强制下架_勿擅改.xlsx", node="activity_force_off_schedule.xml")
public class ActivityForceOffScheduleTemplate implements IResTemplate  {

    /**
    * 要下架的活动排期id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 描述一下下架的原因
    * string reason
    * 
    */
    @ResAttribute("reason")
    private  String reason;



    /**
    * 要下架的活动排期id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 描述一下下架的原因
    * string reason
    * 
    */
    public String getReason(){
        return reason;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "F_发展度.xlsx", node = "const_development.xml", isConst = true)
public class ConstDevelopmentTemplate implements IResConstTemplate  {

    /**
     * 发展度恢复进度条时间
     */
    private int repairTime = 0;
    /**
     * 瘫痪后基地发展度百分比
     */
    private int paralysisPer = 0;
    /**
     * 结束瘫痪后发展度百分比
     */
    private int recoverPer = 0;


    public int getRepairTime(){
        return this.repairTime;
    }

    public int getParalysisPer(){
        return this.paralysisPer;
    }

    public int getRecoverPer(){
        return this.recoverPer;
    }

    @Override
    public int getId() {
        return 0;
    }
}

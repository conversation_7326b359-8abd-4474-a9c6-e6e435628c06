package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_战斗技能.xlsx", node="skill_config.xml")
public class SkillConfigTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 技能名
    * language skill_name
    * 
    */
    @ResAttribute("skill_name")
    private  String skill_name;

    /**
    * 技能ID
    * int skill_id
    * 
    */
    @ResAttribute("skill_id")
    private  int skill_id;

    /**
    * 技能等级
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;

    /**
    * 技能星级
    * int star
    * 
    */
    @ResAttribute("star")
    private  int star;

    /**
    * 消耗类型
    * string cost_type
    * 
    */
    @ResAttribute("cost_type")
    private  String cost_type;

    /**
    * 消耗数值
    * int cost
    * 
    */
    @ResAttribute("cost")
    private  int cost;

    /**
    * 目标选择策略
    * string selection_stradegy
    * 
    */
    @ResAttribute("selection_stradegy")
    private  String selection_stradegy;

    /**
    * 技能最大距离
    * string max_range
    * 
    */
    @ResAttribute("max_range")
    private  String max_range;

    /**
    * BuffID0
    * int buff_id0
    * 
    */
    @ResAttribute("buff_id0")
    private  int buff_id0;

    /**
    * BuffID1
    * int buff_id1
    * 
    */
    @ResAttribute("buff_id1")
    private  int buff_id1;

    /**
    * 前摇
    * int cast_time
    * 
    */
    @ResAttribute("cast_time")
    private  int cast_time;

    /**
    * 后摇
    * int recovery_time
    * 
    */
    @ResAttribute("recovery_time")
    private  int recovery_time;

    /**
    * 技能类型
    * CommonEnum.SkillType skill_type
    * 
    */
    @ResAttribute("skill_type")
    private CommonEnum.SkillType skill_type;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 技能名
    * language skill_name
    * 
    */
    public String getSkillName(){
        return skill_name;
    }
    /**
    * 技能ID
    * int skill_id
    * 
    */
    public int getSkillId(){
        return skill_id;
    }

    /**
    * 技能等级
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }

    /**
    * 技能星级
    * int star
    * 
    */
    public int getStar(){
        return star;
    }

    /**
    * 消耗类型
    * string cost_type
    * 
    */
    public String getCostType(){
        return cost_type;
    }
    /**
    * 消耗数值
    * int cost
    * 
    */
    public int getCost(){
        return cost;
    }

    /**
    * 目标选择策略
    * string selection_stradegy
    * 
    */
    public String getSelectionStradegy(){
        return selection_stradegy;
    }
    /**
    * 技能最大距离
    * string max_range
    * 
    */
    public String getMaxRange(){
        return max_range;
    }
    /**
    * BuffID0
    * int buff_id0
    * 
    */
    public int getBuffId0(){
        return buff_id0;
    }

    /**
    * BuffID1
    * int buff_id1
    * 
    */
    public int getBuffId1(){
        return buff_id1;
    }

    /**
    * 前摇
    * int cast_time
    * 
    */
    public int getCastTime(){
        return cast_time;
    }

    /**
    * 后摇
    * int recovery_time
    * 
    */
    public int getRecoveryTime(){
        return recovery_time;
    }


    /**
    * 技能类型
    * CommonEnum.SkillType skill_type
    * 
    */
    public CommonEnum.SkillType getSkillType(){
        return skill_type;
    }

}
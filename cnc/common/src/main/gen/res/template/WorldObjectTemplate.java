package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="W_无极缩放.xlsx", node="world_object.xml")
public class WorldObjectTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 物体枚举
    * CommonEnum.SceneObjectEnum objType
    * 
    */
    @ResAttribute("objType")
    private CommonEnum.SceneObjectEnum objType;

    /**
    * 显示的最高层级
    * int layer
    * 
    */
    @ResAttribute("layer")
    private  int layer;

    /**
    * 分州版显示的最高层级
    * pairarray regionLayer
    * 
    */
    @ResAttribute("regionLayer")
    private List<IntPairType> regionLayerPairList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 物体枚举
    * CommonEnum.SceneObjectEnum objType
    * 
    */
    public CommonEnum.SceneObjectEnum getObjType(){
        return objType;
    }
    /**
    * 显示的最高层级
    * int layer
    * 
    */
    public int getLayer(){
        return layer;
    }


    /**
    * 分州版显示的最高层级
    * pairarray regionLayer
    * 
    */
    public List<IntPairType> getRegionLayerPairList(){
        return regionLayerPairList;
    }

}
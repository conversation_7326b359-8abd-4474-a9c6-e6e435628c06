package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动表.xlsx", node="activity_weekmonth_card.xml")
public class ActivityWeekmonthCardTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 活动id
    * int activityId
    * 
    */
    @ResAttribute("activityId")
    private  int activityId;

    /**
    * 礼包id
    * int goodsId
    * 
    */
    @ResAttribute("goodsId")
    private  int goodsId;

    /**
    * 折扣礼包ID
    * int weekMonthCardCouponID
    * 
    */
    @ResAttribute("weekMonthCardCouponID")
    private  int weekMonthCardCouponID;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 活动id
    * int activityId
    * 
    */
    public int getActivityId(){
        return activityId;
    }

    /**
    * 礼包id
    * int goodsId
    * 
    */
    public int getGoodsId(){
        return goodsId;
    }

    /**
    * 折扣礼包ID
    * int weekMonthCardCouponID
    * 
    */
    public int getWeekMonthCardCouponID(){
        return weekMonthCardCouponID;
    }


}
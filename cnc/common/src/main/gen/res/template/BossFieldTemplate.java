package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="", node="boss_field.xml")
public class BossFieldTemplate implements IResTemplate  {

    /**
    * boss战场ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 地图ID
    * int mapId
    * 
    */
    @ResAttribute("mapId")
    private  int mapId;

    /**
    * 战场类型
    * int type
    * 
    */
    @ResAttribute("type")
    private  int type;

    /**
    * 坐标
    * pair position
    * 
    */
    @ResAttribute("position")
    private IntPairType positionPair;

    /**
    * 战场半径
    * int radius
    * 
    */
    @ResAttribute("radius")
    private  int radius;

    /**
    * 玩家空投区域
(对应boss_field_area表中ID)
    * intarray bornAreaIdList
    * 
    */
    @ResAttribute("bornAreaIdList")
    private List<Integer> bornAreaIdListList;

    /**
    * 怪物刷新区域
(对应boss_field_area表中ID)
(到boss_field_monster表中的刷新区域使用)
    * string monsterAreaList
    * 
    */
    @ResAttribute("monsterAreaList")
    private  String monsterAreaList;

    /**
    * 战场重置时间（战场内无行军时开始计时）秒
    * int reInitTime
    * 
    */
    @ResAttribute("reInitTime")
    private  int reInitTime;



    /**
    * boss战场ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 地图ID
    * int mapId
    * 
    */
    public int getMapId(){
        return mapId;
    }

    /**
    * 战场类型
    * int type
    * 
    */
    public int getType(){
        return type;
    }


    /**
    * 坐标
    * pair position
    * 
    */
    public IntPairType getPositionPair(){
        return positionPair;
    }
    /**
    * 战场半径
    * int radius
    * 
    */
    public int getRadius(){
        return radius;
    }


    /**
    * 玩家空投区域
(对应boss_field_area表中ID)
    * intarray bornAreaIdList
    * 
    */
    public List<Integer> getBornAreaIdListList(){
        return bornAreaIdListList;
    }

    /**
    * 怪物刷新区域
(对应boss_field_area表中ID)
(到boss_field_monster表中的刷新区域使用)
    * string monsterAreaList
    * 
    */
    public String getMonsterAreaList(){
        return monsterAreaList;
    }
    /**
    * 战场重置时间（战场内无行军时开始计时）秒
    * int reInitTime
    * 
    */
    public int getReInitTime(){
        return reInitTime;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_指挥官.xlsx", node="commander_collection_point.xml")
public class CommanderCollectionPointTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 资源id
    * int resource
    * 
    */
    @ResAttribute("resource")
    private  int resource;

    /**
    * 采集积分
    * int integral
    * 
    */
    @ResAttribute("integral")
    private  int integral;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 资源id
    * int resource
    * 
    */
    public int getResource(){
        return resource;
    }

    /**
    * 采集积分
    * int integral
    * 
    */
    public int getIntegral(){
        return integral;
    }


}
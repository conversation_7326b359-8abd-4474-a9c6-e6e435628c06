package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_战役配置表【RH】.xlsx", node="campaign_map.xml")
public class CampaignMapTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 任务奖励类型权重
    * pairarray missionRewardTypeWeighting
    * 
    */
    @ResAttribute("missionRewardTypeWeighting")
    private List<IntPairType> missionRewardTypeWeightingPairList;

    /**
    * 任务奖励保底
    * intarray minimumGuarantee
    * 
    */
    @ResAttribute("minimumGuarantee")
    private List<Integer> minimumGuaranteeList;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 任务奖励类型权重
    * pairarray missionRewardTypeWeighting
    * 
    */
    public List<IntPairType> getMissionRewardTypeWeightingPairList(){
        return missionRewardTypeWeightingPairList;
    }

    /**
    * 任务奖励保底
    * intarray minimumGuarantee
    * 
    */
    public List<Integer> getMinimumGuaranteeList(){
        return minimumGuaranteeList;
    }


}
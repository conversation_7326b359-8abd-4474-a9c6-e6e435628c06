package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "T_天网.xlsx", node = "const_skynet.xml", isConst = true)
public class ConstSkynetTemplate implements IResConstTemplate  {

    /**
     * 野怪刷新范围区间
     */
    private List<IntPairType> monsterRefreshRange;
    /**
     * 目标点建筑刷新范围
     */
    private List<IntPairType> buildRefeshRange;
    /**
     * 探索任务所需时间(秒)
     */
    private int exploreTime = 0;
    /**
     * 飞机飞行速度km/s
     */
    private int flySpeed = 0;
    /**
     * 飞机停留最小时间（秒）
     */
    private int stayMinTime = 0;
    /**
     * 飞机停留最大时间（秒）
     */
    private int stayMaxTime = 0;
    /**
     * 天网任务存储上限
     */
    private int saveTaskNumLimit = 0;
    /**
     * 每日天网刷新次数（从0点开始）
     */
    private int taskRefreshCycle = 0;
    /**
     * 天网商店解锁等级
     */
    private int shopUnlockLevel = 0;
    /**
     * 兑换功能解锁等级
     */
    private int exchangeUnlocklevel = 0;
    /**
     * 兑换钥匙所需情报点数
     */
    private int needPoint = 0;
    /**
     * 钥匙获取上限
     */
    private int keyLimit = 0;
    /**
     * 情报点获取上限
     */
    private int pointLimit = 0;
    /**
     * 每日守卫者任务刷新数量上限
     */
    private int defenderTaskRefreshLimit = 0;
    /**
     * 守卫者任务存储上限
     */
    private int defenderSaveLimit = 0;
    /**
     * 情报任务怪负载上限
     */
    private int monsterLimit = 0;
    /**
     * 守卫者任务每日刷新次数（从0点开始）
     */
    private int defenderRefreshCycle = 0;
    /**
     * 打野任务进行中的失效时间（秒）
     */
    private int expirationTime = 0;
    /**
     * 普通野怪存活时间
     */
    private int monsterLifeTime = 0;
    /**
     * BOSS野怪存活时间
     */
    private int bossLifeTime = 0;
    /**
     * 情报点兑换的钥匙道具ID
     */
    private int keyItemId = 0;
    /**
     * 守卫者任务品质权重
     */
    private int defenderQuality = 0;
    /**
     * 守卫者任务解锁需要天网等级
     */
    private int defenderUnlockLevel = 0;


    public List<IntPairType> getMonsterRefreshRange(){
        return this.monsterRefreshRange;
    }

    public List<IntPairType> getBuildRefeshRange(){
        return this.buildRefeshRange;
    }

    public int getExploreTime(){
        return this.exploreTime;
    }

    public int getFlySpeed(){
        return this.flySpeed;
    }

    public int getStayMinTime(){
        return this.stayMinTime;
    }

    public int getStayMaxTime(){
        return this.stayMaxTime;
    }

    public int getSaveTaskNumLimit(){
        return this.saveTaskNumLimit;
    }

    public int getTaskRefreshCycle(){
        return this.taskRefreshCycle;
    }

    public int getShopUnlockLevel(){
        return this.shopUnlockLevel;
    }

    public int getExchangeUnlocklevel(){
        return this.exchangeUnlocklevel;
    }

    public int getNeedPoint(){
        return this.needPoint;
    }

    public int getKeyLimit(){
        return this.keyLimit;
    }

    public int getPointLimit(){
        return this.pointLimit;
    }

    public int getDefenderTaskRefreshLimit(){
        return this.defenderTaskRefreshLimit;
    }

    public int getDefenderSaveLimit(){
        return this.defenderSaveLimit;
    }

    public int getMonsterLimit(){
        return this.monsterLimit;
    }

    public int getDefenderRefreshCycle(){
        return this.defenderRefreshCycle;
    }

    public int getExpirationTime(){
        return this.expirationTime;
    }

    public int getMonsterLifeTime(){
        return this.monsterLifeTime;
    }

    public int getBossLifeTime(){
        return this.bossLifeTime;
    }

    public int getKeyItemId(){
        return this.keyItemId;
    }

    public int getDefenderQuality(){
        return this.defenderQuality;
    }

    public int getDefenderUnlockLevel(){
        return this.defenderUnlockLevel;
    }

    @Override
    public int getId() {
        return 0;
    }
}

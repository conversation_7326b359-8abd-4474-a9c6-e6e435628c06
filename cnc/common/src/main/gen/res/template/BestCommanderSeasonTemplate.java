package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_最强指挥官赛季版本.xlsx", node="best_commander_season.xml")
public class BestCommanderSeasonTemplate implements IResTemplate  {

    /**
    * 配置id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 活动id
    * int actId
    * 
    */
    @ResAttribute("actId")
    private  int actId;

    /**
    * 活动阶段
    * CommonEnum.CommanderActivtyStage actStage
    * 
    */
    @ResAttribute("actStage")
    private CommonEnum.CommanderActivtyStage actStage;

    /**
    * 循环期数(左闭右开)
    * pair volume
    * 
    */
    @ResAttribute("volume")
    private IntPairType volumePair;

    /**
    * 是否为自选活动
    * bool isSelect
    * 
    */
    @ResAttribute("isSelect")
    private  boolean isSelect;

    /**
    * 奖励英雄凭证
    * intarray reward
    * 
    */
    @ResAttribute("reward")
    private List<Integer> rewardList;

    /**
    * 子榜的活动id
    * intarray subRankActId
    * 
    */
    @ResAttribute("subRankActId")
    private List<Integer> subRankActIdList;

    /**
    * 总榜的活动id
    * int totalRankActId
    * 
    */
    @ResAttribute("totalRankActId")
    private  int totalRankActId;



    /**
    * 配置id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 活动id
    * int actId
    * 
    */
    public int getActId(){
        return actId;
    }


    /**
    * 活动阶段
    * CommonEnum.CommanderActivtyStage actStage
    * 
    */
    public CommonEnum.CommanderActivtyStage getActStage(){
        return actStage;
    }

    /**
    * 循环期数(左闭右开)
    * pair volume
    * 
    */
    public IntPairType getVolumePair(){
        return volumePair;
    }

    /**
    * 是否为自选活动
    * bool isSelect
    * 
    */
    public boolean getIsSelect(){
        return isSelect;
    }

    /**
    * 奖励英雄凭证
    * intarray reward
    * 
    */
    public List<Integer> getRewardList(){
        return rewardList;
    }


    /**
    * 子榜的活动id
    * intarray subRankActId
    * 
    */
    public List<Integer> getSubRankActIdList(){
        return subRankActIdList;
    }

    /**
    * 总榜的活动id
    * int totalRankActId
    * 
    */
    public int getTotalRankActId(){
        return totalRankActId;
    }


}
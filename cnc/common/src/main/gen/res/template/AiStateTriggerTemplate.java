package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="A_AI指令.xlsx", node="ai_state_trigger.xml")
public class AiStateTriggerTemplate implements IResTemplate  {

    /**
    * 触发器ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 触发类型
    * CommonEnum.MonsterTriggerType triggerType
    * 
    */
    @ResAttribute("triggerType")
    private CommonEnum.MonsterTriggerType triggerType;

    /**
    * 触发参数
    * pairarray triggerParams
    * 
    */
    @ResAttribute("triggerParams")
    private List<IntPairType> triggerParamsPairList;

    /**
    * 触发效果
    * CommonEnum.MonsterTriggerEffect triggerEffect
    * 
    */
    @ResAttribute("triggerEffect")
    private CommonEnum.MonsterTriggerEffect triggerEffect;

    /**
    * 效果参数
    * pairarray effectParams
    * 
    */
    @ResAttribute("effectParams")
    private List<IntPairType> effectParamsPairList;

    /**
    * 延时触发时长/s
    * int delay
    * 
    */
    @ResAttribute("delay")
    private  int delay;

    /**
    * 触发上限次数(0为无限触发)
    * int count
    * 
    */
    @ResAttribute("count")
    private  int count;

    /**
    * 触发时间周期（秒）
    * int period
    * 
    */
    @ResAttribute("period")
    private  int period;



    /**
    * 触发器ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 触发类型
    * CommonEnum.MonsterTriggerType triggerType
    * 
    */
    public CommonEnum.MonsterTriggerType getTriggerType(){
        return triggerType;
    }

    /**
    * 触发参数
    * pairarray triggerParams
    * 
    */
    public List<IntPairType> getTriggerParamsPairList(){
        return triggerParamsPairList;
    }

    /**
    * 触发效果
    * CommonEnum.MonsterTriggerEffect triggerEffect
    * 
    */
    public CommonEnum.MonsterTriggerEffect getTriggerEffect(){
        return triggerEffect;
    }

    /**
    * 效果参数
    * pairarray effectParams
    * 
    */
    public List<IntPairType> getEffectParamsPairList(){
        return effectParamsPairList;
    }
    /**
    * 延时触发时长/s
    * int delay
    * 
    */
    public int getDelay(){
        return delay;
    }

    /**
    * 触发上限次数(0为无限触发)
    * int count
    * 
    */
    public int getCount(){
        return count;
    }

    /**
    * 触发时间周期（秒）
    * int period
    * 
    */
    public int getPeriod(){
        return period;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="P_排行榜.xlsx", node="arrow_determine.xml")
public class ArrowDetermineTemplate implements IResTemplate  {

    /**
    * 序号
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 排名范围
    * pair range
    * 
    */
    @ResAttribute("range")
    private IntPairType rangePair;

    /**
    * 向上触发排名变化的数量
    * int upNum
    * 
    */
    @ResAttribute("upNum")
    private  int upNum;

    /**
    * 向下触发排名箭头的数量
    * int downNum
    * 
    */
    @ResAttribute("downNum")
    private  int downNum;



    /**
    * 序号
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 排名范围
    * pair range
    * 
    */
    public IntPairType getRangePair(){
        return rangePair;
    }
    /**
    * 向上触发排名变化的数量
    * int upNum
    * 
    */
    public int getUpNum(){
        return upNum;
    }

    /**
    * 向下触发排名箭头的数量
    * int downNum
    * 
    */
    public int getDownNum(){
        return downNum;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_战役配置表【RH】.xlsx", node="campaign_td.xml")
public class CampaignTdTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 主堡等级
    * int baseLevelId
    * 
    */
    @ResAttribute("baseLevelId")
    private  int baseLevelId;

    /**
    * 准备时长（分钟）
    * int preparationTime
    * 
    */
    @ResAttribute("preparationTime")
    private  int preparationTime;

    /**
    * 参战要求（建筑ID_等级_数量）
    * triplearray participationRequirements
    * 
    */
    @ResAttribute("participationRequirements")
    private List<IntTripleType> participationRequirementsTripleList;

    /**
    * 资源奖励
    * pairarray rewardType1
    * 
    */
    @ResAttribute("rewardType1")
    private List<IntPairType> rewardType1PairList;

    /**
    * 道具奖励
    * pairarray rewardType2
    * 
    */
    @ResAttribute("rewardType2")
    private List<IntPairType> rewardType2PairList;

    /**
    * 刷怪组ID
    * intarray farmGroupId
    * 
    */
    @ResAttribute("farmGroupId")
    private List<Integer> farmGroupIdList;

    /**
    * 升级工具箱
    * triplearray toolboxRefresh
    * 
    */
    @ResAttribute("toolboxRefresh")
    private List<IntTripleType> toolboxRefreshTripleList;

    /**
    * 升级工具箱刷新时间
    * pairarray toolboxRefreshTime
    * 
    */
    @ResAttribute("toolboxRefreshTime")
    private List<IntPairType> toolboxRefreshTimePairList;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 主堡等级
    * int baseLevelId
    * 
    */
    public int getBaseLevelId(){
        return baseLevelId;
    }

    /**
    * 准备时长（分钟）
    * int preparationTime
    * 
    */
    public int getPreparationTime(){
        return preparationTime;
    }


    /**
    * 参战要求（建筑ID_等级_数量）
    * triplearray participationRequirements
    * 
    */
    public List<IntTripleType> getParticipationRequirementsTripleList(){
        return participationRequirementsTripleList;
    }       

    /**
    * 资源奖励
    * pairarray rewardType1
    * 
    */
    public List<IntPairType> getRewardType1PairList(){
        return rewardType1PairList;
    }

    /**
    * 道具奖励
    * pairarray rewardType2
    * 
    */
    public List<IntPairType> getRewardType2PairList(){
        return rewardType2PairList;
    }

    /**
    * 刷怪组ID
    * intarray farmGroupId
    * 
    */
    public List<Integer> getFarmGroupIdList(){
        return farmGroupIdList;
    }


    /**
    * 升级工具箱
    * triplearray toolboxRefresh
    * 
    */
    public List<IntTripleType> getToolboxRefreshTripleList(){
        return toolboxRefreshTripleList;
    }       

    /**
    * 升级工具箱刷新时间
    * pairarray toolboxRefreshTime
    * 
    */
    public List<IntPairType> getToolboxRefreshTimePairList(){
        return toolboxRefreshTimePairList;
    }

}
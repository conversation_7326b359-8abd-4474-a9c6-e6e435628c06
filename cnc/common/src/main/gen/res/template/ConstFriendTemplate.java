package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "H_好友表.xlsx", node = "const_friend.xml", isConst = true)
public class ConstFriendTemplate implements IResConstTemplate  {

    /**
     * 申请好友需要主堡等级
     */
    private int SendFriendLimit = 0;
    /**
     * 好友人数上限
     */
    private int FriendMax = 0;
    /**
     * 申请过期时间（秒）
     */
    private int ApplyOverdue = 0;
    /**
     * 申请删除时间（秒）
     */
    private int ApplyDelete = 0;
    /**
     * 申请上限
     */
    private int ApplyDMax = 0;
    /**
     * 屏蔽上限
     */
    private int ShieldMax = 0;


    public int getSendFriendLimit(){
        return this.SendFriendLimit;
    }

    public int getFriendMax(){
        return this.FriendMax;
    }

    public int getApplyOverdue(){
        return this.ApplyOverdue;
    }

    public int getApplyDelete(){
        return this.ApplyDelete;
    }

    public int getApplyDMax(){
        return this.ApplyDMax;
    }

    public int getShieldMax(){
        return this.ShieldMax;
    }

    @Override
    public int getId() {
        return 0;
    }
}

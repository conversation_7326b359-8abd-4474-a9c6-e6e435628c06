package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "V_VIP.xlsx", node = "const_vip.xml", isConst = true)
public class ConstVipTemplate implements IResConstTemplate  {

    /**
     *    每日刷新时间（UTC-0时区）
     */
    private int dailyRefreshTime = 0;
    /**
     * 每日特权点数基础值
     */
    private int vipExpBase = 0;
    /**
     * 每日增加特权点数的值
     */
    private int dailyAddVipExp = 0;
    /**
     * 每日特权点数加成值上限
     */
    private int vipExpMax = 0;
    /**
     * 未登录N天重置累登天数
     */
    private int cumLoginResetDay = 0;


    public int getDailyRefreshTime(){
        return this.dailyRefreshTime;
    }

    public int getVipExpBase(){
        return this.vipExpBase;
    }

    public int getDailyAddVipExp(){
        return this.dailyAddVipExp;
    }

    public int getVipExpMax(){
        return this.vipExpMax;
    }

    public int getCumLoginResetDay(){
        return this.cumLoginResetDay;
    }

    @Override
    public int getId() {
        return 0;
    }
}

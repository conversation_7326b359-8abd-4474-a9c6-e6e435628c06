package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_军团技能.xlsx", node="clan_skill_group.xml")
public class ClanSkillGroupTemplate implements IResTemplate  {

    /**
    * 技能组ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 超武技能ID
    * int skillID
    * 
    */
    @ResAttribute("skillID")
    private  int skillID;

    /**
    * 类型
    * CommonEnum.SuperWeaponSkillType skillType
    * 
    */
    @ResAttribute("skillType")
    private CommonEnum.SuperWeaponSkillType skillType;

    /**
    * 解锁来源
    * int unlock
    * 
    */
    @ResAttribute("unlock")
    private  int unlock;

    /**
    * 技能持续时间
    * int continueTime
    * 
    */
    @ResAttribute("continueTime")
    private  int continueTime;

    /**
    * 技能冷却时间
    * int skillCD
    * 
    */
    @ResAttribute("skillCD")
    private  int skillCD;



    /**
    * 技能组ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 超武技能ID
    * int skillID
    * 
    */
    public int getSkillID(){
        return skillID;
    }


    /**
    * 类型
    * CommonEnum.SuperWeaponSkillType skillType
    * 
    */
    public CommonEnum.SuperWeaponSkillType getSkillType(){
        return skillType;
    }
    /**
    * 解锁来源
    * int unlock
    * 
    */
    public int getUnlock(){
        return unlock;
    }

    /**
    * 技能持续时间
    * int continueTime
    * 
    */
    public int getContinueTime(){
        return continueTime;
    }

    /**
    * 技能冷却时间
    * int skillCD
    * 
    */
    public int getSkillCD(){
        return skillCD;
    }


}
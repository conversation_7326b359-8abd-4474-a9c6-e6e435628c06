package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_大世界野怪投放区域.xlsx", node="big_scene_spawn_strategy.xml")
public class BigSceneSpawnStrategyTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * kvk剧本id
    * int storyId
    * 
    */
    @ResAttribute("storyId")
    private  int storyId;

    /**
    * 类型
    * CommonEnum.MonsterCategory category
    * 
    */
    @ResAttribute("category")
    private CommonEnum.MonsterCategory category;

    /**
    * 品质
    * CommonEnum.SceneObjQuality quality
    * 
    */
    @ResAttribute("quality")
    private CommonEnum.SceneObjQuality quality;

    /**
    * 投放方式
    * CommonEnum.BigSceneMonsterSpawnType strategy
    * 
    */
    @ResAttribute("strategy")
    private CommonEnum.BigSceneMonsterSpawnType strategy;

    /**
    * 定时器
    * int schedule
    * 
    */
    @ResAttribute("schedule")
    private  int schedule;

    /**
    * 存活时间(秒)
    * int lifeTime
    * 
    */
    @ResAttribute("lifeTime")
    private  int lifeTime;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * kvk剧本id
    * int storyId
    * 
    */
    public int getStoryId(){
        return storyId;
    }


    /**
    * 类型
    * CommonEnum.MonsterCategory category
    * 
    */
    public CommonEnum.MonsterCategory getCategory(){
        return category;
    }

    /**
    * 品质
    * CommonEnum.SceneObjQuality quality
    * 
    */
    public CommonEnum.SceneObjQuality getQuality(){
        return quality;
    }

    /**
    * 投放方式
    * CommonEnum.BigSceneMonsterSpawnType strategy
    * 
    */
    public CommonEnum.BigSceneMonsterSpawnType getStrategy(){
        return strategy;
    }
    /**
    * 定时器
    * int schedule
    * 
    */
    public int getSchedule(){
        return schedule;
    }

    /**
    * 存活时间(秒)
    * int lifeTime
    * 
    */
    public int getLifeTime(){
        return lifeTime;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_英雄表_New.xlsx", node="hero_rh.xml")
public class HeroRhTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 稀有度
    * int rarity
    * 
    */
    @ResAttribute("rarity")
    private  int rarity;

    /**
    * 职业
    * int career
    * 
    */
    @ResAttribute("career")
    private  int career;

    /**
    * 关联整卡道具
    * int related_card_item
    * 
    */
    @ResAttribute("related_card_item")
    private  int related_card_item;

    /**
    * 关联碎片道具
    * int related_shard_item
    * 
    */
    @ResAttribute("related_shard_item")
    private  int related_shard_item;

    /**
    * 招募需求碎片数量
    * int recruit_shard_amount
    * 
    */
    @ResAttribute("recruit_shard_amount")
    private  int recruit_shard_amount;

    /**
    * 重复获取碎片数量
    * int get_shard_amount
    * 
    */
    @ResAttribute("get_shard_amount")
    private  int get_shard_amount;

    /**
    * 技能1（默认技能）
    * pair skill1
    * 
    */
    @ResAttribute("skill1")
    private IntPairType skill1Pair;

    /**
    * 技能2
    * pair skill2
    * 
    */
    @ResAttribute("skill2")
    private IntPairType skill2Pair;

    /**
    * 技能3
    * pair skill3
    * 
    */
    @ResAttribute("skill3")
    private IntPairType skill3Pair;

    /**
    * 技能4
    * pair skill4
    * 
    */
    @ResAttribute("skill4")
    private IntPairType skill4Pair;

    /**
    * 自身兵种
    * int self_unit
    * 
    */
    @ResAttribute("self_unit")
    private  int self_unit;

    /**
    * 关联兵种
    * int related_unit
    * 
    */
    @ResAttribute("related_unit")
    private  int related_unit;

    /**
    * 关联兵种（临时）
    * int related_unit_temp
    * 
    */
    @ResAttribute("related_unit_temp")
    private  int related_unit_temp;

    /**
    * 关联英雄（旧）
    * int related_hero_old
    * 
    */
    @ResAttribute("related_hero_old")
    private  int related_hero_old;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 稀有度
    * int rarity
    * 
    */
    public int getRarity(){
        return rarity;
    }

    /**
    * 职业
    * int career
    * 
    */
    public int getCareer(){
        return career;
    }

    /**
    * 关联整卡道具
    * int related_card_item
    * 
    */
    public int getRelatedCardItem(){
        return related_card_item;
    }

    /**
    * 关联碎片道具
    * int related_shard_item
    * 
    */
    public int getRelatedShardItem(){
        return related_shard_item;
    }

    /**
    * 招募需求碎片数量
    * int recruit_shard_amount
    * 
    */
    public int getRecruitShardAmount(){
        return recruit_shard_amount;
    }

    /**
    * 重复获取碎片数量
    * int get_shard_amount
    * 
    */
    public int getGetShardAmount(){
        return get_shard_amount;
    }


    /**
    * 技能1（默认技能）
    * pair skill1
    * 
    */
    public IntPairType getSkill1Pair(){
        return skill1Pair;
    }

    /**
    * 技能2
    * pair skill2
    * 
    */
    public IntPairType getSkill2Pair(){
        return skill2Pair;
    }

    /**
    * 技能3
    * pair skill3
    * 
    */
    public IntPairType getSkill3Pair(){
        return skill3Pair;
    }

    /**
    * 技能4
    * pair skill4
    * 
    */
    public IntPairType getSkill4Pair(){
        return skill4Pair;
    }
    /**
    * 自身兵种
    * int self_unit
    * 
    */
    public int getSelfUnit(){
        return self_unit;
    }

    /**
    * 关联兵种
    * int related_unit
    * 
    */
    public int getRelatedUnit(){
        return related_unit;
    }

    /**
    * 关联兵种（临时）
    * int related_unit_temp
    * 
    */
    public int getRelatedUnitTemp(){
        return related_unit_temp;
    }

    /**
    * 关联英雄（旧）
    * int related_hero_old
    * 
    */
    public int getRelatedHeroOld(){
        return related_hero_old;
    }


}
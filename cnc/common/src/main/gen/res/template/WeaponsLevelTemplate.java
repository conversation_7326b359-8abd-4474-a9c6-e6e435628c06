package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="W_武器配置表【RH】.xlsx", node="weapons_level.xml")
public class WeaponsLevelTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 武器ID
    * int WeaponID
    * 
    */
    @ResAttribute("WeaponID")
    private  int WeaponID;

    /**
    * 等级
    * int Level
    * 
    */
    @ResAttribute("Level")
    private  int Level;

    /**
    * 伤害
    * int Damage
    * 
    */
    @ResAttribute("Damage")
    private  int Damage;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 武器ID
    * int WeaponID
    * 
    */
    public int getWeaponID(){
        return WeaponID;
    }

    /**
    * 等级
    * int Level
    * 
    */
    public int getLevel(){
        return Level;
    }

    /**
    * 伤害
    * int Damage
    * 
    */
    public int getDamage(){
        return Damage;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="W_无极缩放.xlsx", node="world_layer.xml")
public class WorldLayerTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 层级id
    * int layerId
    * 
    */
    @ResAttribute("layerId")
    private  int layerId;

    /**
    * 最小
    * int min
    * 
    */
    @ResAttribute("min")
    private  int min;

    /**
    * 最大
    * int max
    * 
    */
    @ResAttribute("max")
    private  int max;

    /**
    * AOI视野宽度修正(-+10%)
    * pair aoiWidth
    * 
    */
    @ResAttribute("aoiWidth")
    private IntPairType aoiWidthPair;

    /**
    * AOI视野高度修正-+10%
    * pair aoiHeight
    * 
    */
    @ResAttribute("aoiHeight")
    private IntPairType aoiHeightPair;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 层级id
    * int layerId
    * 
    */
    public int getLayerId(){
        return layerId;
    }

    /**
    * 最小
    * int min
    * 
    */
    public int getMin(){
        return min;
    }

    /**
    * 最大
    * int max
    * 
    */
    public int getMax(){
        return max;
    }


    /**
    * AOI视野宽度修正(-+10%)
    * pair aoiWidth
    * 
    */
    public IntPairType getAoiWidthPair(){
        return aoiWidthPair;
    }

    /**
    * AOI视野高度修正-+10%
    * pair aoiHeight
    * 
    */
    public IntPairType getAoiHeightPair(){
        return aoiHeightPair;
    }

}
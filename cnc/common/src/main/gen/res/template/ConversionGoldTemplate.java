package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="S_商城表.xlsx", node="conversion_gold.xml")
public class ConversionGoldTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 兑换类型
    * int type
    * 
    */
    @ResAttribute("type")
    private  int type;

    /**
    * 兑换数值
    * int value
    * 
    */
    @ResAttribute("value")
    private  int value;

    /**
    * 金条价格类型
    * int priceType
    * 
    */
    @ResAttribute("priceType")
    private  int priceType;

    /**
    * 价格数值
    * int priceValue
    * 
    */
    @ResAttribute("priceValue")
    private  int priceValue;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 兑换类型
    * int type
    * 
    */
    public int getType(){
        return type;
    }

    /**
    * 兑换数值
    * int value
    * 
    */
    public int getValue(){
        return value;
    }

    /**
    * 金条价格类型
    * int priceType
    * 
    */
    public int getPriceType(){
        return priceType;
    }

    /**
    * 价格数值
    * int priceValue
    * 
    */
    public int getPriceValue(){
        return priceValue;
    }


}
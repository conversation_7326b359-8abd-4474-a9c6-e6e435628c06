package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_战役配置表【RH】.xlsx", node="campaign_unit_level.xml")
public class CampaignUnitLevelTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 单位类型
    * int unitType
    * 
    */
    @ResAttribute("unitType")
    private  int unitType;

    /**
    * 单位等级
    * int unitLevel
    * 
    */
    @ResAttribute("unitLevel")
    private  int unitLevel;

    /**
    * 单位配置表ID
    * int unitId
    * 
    */
    @ResAttribute("unitId")
    private  int unitId;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 单位类型
    * int unitType
    * 
    */
    public int getUnitType(){
        return unitType;
    }

    /**
    * 单位等级
    * int unitLevel
    * 
    */
    public int getUnitLevel(){
        return unitLevel;
    }

    /**
    * 单位配置表ID
    * int unitId
    * 
    */
    public int getUnitId(){
        return unitId;
    }


}
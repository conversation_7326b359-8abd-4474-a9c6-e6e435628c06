package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "S_设置表.xlsx", node = "const_setting.xml", isConst = true)
public class ConstSettingTemplate implements IResConstTemplate  {

    /**
     * 表情持续时间/s
     */
    private int expressionLastTime = 0;
    /**
     * 关闭二级密码等待时间/s
     */
    private int closeSpwTime = 0;
    /**
     * 默认表情
     */
    private List<Integer> defaultExpress;
    /**
     * 邮件屏蔽上限
     */
    private int BlockMailLimit = 0;
    /**
     * 二级密码取消邮件
     */
    private int SecondaryPasswordCloseMail = 0;
    /**
     * 二级密码每天错误次数
     */
    private int SecondaryPasswordDailyWrong = 0;
    /**
     * 二级密码最少字符
     */
    private int SecondaryPasswordLessWord = 0;
    /**
     * 二级密码最多字符
     */
    private int SecondaryPasswordMostWord = 0;
    /**
     * 异形屏适配最大范围（单位%）
     */
    private int SpecialScreenMax = 0;


    public int getExpressionLastTime(){
        return this.expressionLastTime;
    }

    public int getCloseSpwTime(){
        return this.closeSpwTime;
    }

    public List<Integer> getDefaultExpress(){
        return this.defaultExpress;
    }

    public int getBlockMailLimit(){
        return this.BlockMailLimit;
    }

    public int getSecondaryPasswordCloseMail(){
        return this.SecondaryPasswordCloseMail;
    }

    public int getSecondaryPasswordDailyWrong(){
        return this.SecondaryPasswordDailyWrong;
    }

    public int getSecondaryPasswordLessWord(){
        return this.SecondaryPasswordLessWord;
    }

    public int getSecondaryPasswordMostWord(){
        return this.SecondaryPasswordMostWord;
    }

    public int getSpecialScreenMax(){
        return this.SpecialScreenMax;
    }

    @Override
    public int getId() {
        return 0;
    }
}

package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="F_服务器多语言.xlsx", node="server_language.xml")
public class ServerLanguageTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 中文
    * string zh
    * 
    */
    @ResAttribute("zh")
    private  String zh;

    /**
    * 英文
    * string en
    * 
    */
    @ResAttribute("en")
    private  String en;

    /**
    * 德语
    * string de
    * 
    */
    @ResAttribute("de")
    private  String de;

    /**
    * 俄语
    * string ru
    * 
    */
    @ResAttribute("ru")
    private  String ru;

    /**
    * 泰语
    * string th
    * 
    */
    @ResAttribute("th")
    private  String th;

    /**
    * 印尼
    * string ide
    * 
    */
    @ResAttribute("ide")
    private  String ide;

    /**
    * 越南语
    * string vi
    * 
    */
    @ResAttribute("vi")
    private  String vi;

    /**
    * 韩语
    * string ko
    * 
    */
    @ResAttribute("ko")
    private  String ko;

    /**
    * 日语
    * string ja
    * 
    */
    @ResAttribute("ja")
    private  String ja;

    /**
    * 繁体
    * string zh_tw
    * 
    */
    @ResAttribute("zh_tw")
    private  String zh_tw;

    /**
    * 意语
    * string it
    * 
    */
    @ResAttribute("it")
    private  String it;

    /**
    * 西语
    * string es
    * 
    */
    @ResAttribute("es")
    private  String es;

    /**
    * 葡语
    * string pt
    * 
    */
    @ResAttribute("pt")
    private  String pt;

    /**
    * 土耳其语
    * string tr
    * 
    */
    @ResAttribute("tr")
    private  String tr;

    /**
    * 阿语
    * string ar
    * 
    */
    @ResAttribute("ar")
    private  String ar;

    /**
    * 其他
    * string other
    * 
    */
    @ResAttribute("other")
    private  String other;

    /**
    * 法语
    * string fr
    * 
    */
    @ResAttribute("fr")
    private  String fr;

    /**
    * 波兰语
    * string pl
    * 
    */
    @ResAttribute("pl")
    private  String pl;

    /**
    * 荷兰语
    * string nl
    * 
    */
    @ResAttribute("nl")
    private  String nl;

    /**
    * 瑞典语
    * string se
    * 
    */
    @ResAttribute("se")
    private  String se;

    /**
    * 匈牙利语
    * string hu
    * 
    */
    @ResAttribute("hu")
    private  String hu;

    /**
    * 马来语
    * string ms
    * 
    */
    @ResAttribute("ms")
    private  String ms;

    /**
    * 印度语
    * string as_in
    * 
    */
    @ResAttribute("as_in")
    private  String as_in;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 中文
    * string zh
    * 
    */
    public String getZh(){
        return zh;
    }
    /**
    * 英文
    * string en
    * 
    */
    public String getEn(){
        return en;
    }
    /**
    * 德语
    * string de
    * 
    */
    public String getDe(){
        return de;
    }
    /**
    * 俄语
    * string ru
    * 
    */
    public String getRu(){
        return ru;
    }
    /**
    * 泰语
    * string th
    * 
    */
    public String getTh(){
        return th;
    }
    /**
    * 印尼
    * string ide
    * 
    */
    public String getIde(){
        return ide;
    }
    /**
    * 越南语
    * string vi
    * 
    */
    public String getVi(){
        return vi;
    }
    /**
    * 韩语
    * string ko
    * 
    */
    public String getKo(){
        return ko;
    }
    /**
    * 日语
    * string ja
    * 
    */
    public String getJa(){
        return ja;
    }
    /**
    * 繁体
    * string zh_tw
    * 
    */
    public String getZhTw(){
        return zh_tw;
    }
    /**
    * 意语
    * string it
    * 
    */
    public String getIt(){
        return it;
    }
    /**
    * 西语
    * string es
    * 
    */
    public String getEs(){
        return es;
    }
    /**
    * 葡语
    * string pt
    * 
    */
    public String getPt(){
        return pt;
    }
    /**
    * 土耳其语
    * string tr
    * 
    */
    public String getTr(){
        return tr;
    }
    /**
    * 阿语
    * string ar
    * 
    */
    public String getAr(){
        return ar;
    }
    /**
    * 其他
    * string other
    * 
    */
    public String getOther(){
        return other;
    }
    /**
    * 法语
    * string fr
    * 
    */
    public String getFr(){
        return fr;
    }
    /**
    * 波兰语
    * string pl
    * 
    */
    public String getPl(){
        return pl;
    }
    /**
    * 荷兰语
    * string nl
    * 
    */
    public String getNl(){
        return nl;
    }
    /**
    * 瑞典语
    * string se
    * 
    */
    public String getSe(){
        return se;
    }
    /**
    * 匈牙利语
    * string hu
    * 
    */
    public String getHu(){
        return hu;
    }
    /**
    * 马来语
    * string ms
    * 
    */
    public String getMs(){
        return ms;
    }
    /**
    * 印度语
    * string as_in
    * 
    */
    public String getAsIn(){
        return as_in;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_大世界野怪投放区域.xlsx", node="monster_parameters.xml")
public class MonsterParametersTemplate implements IResTemplate  {

    /**
    * 标识id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 剧本id
    * int storyId
    * 
    */
    @ResAttribute("storyId")
    private  int storyId;

    /**
    * 野怪最大可搜索等级
    * int maxKvkMonsterSearchLevel
    * 
    */
    @ResAttribute("maxKvkMonsterSearchLevel")
    private  int maxKvkMonsterSearchLevel;

    /**
    * 野怪最小可搜索等级
    * int minKvkMonsterSearchLevel
    * 
    */
    @ResAttribute("minKvkMonsterSearchLevel")
    private  int minKvkMonsterSearchLevel;



    /**
    * 标识id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 剧本id
    * int storyId
    * 
    */
    public int getStoryId(){
        return storyId;
    }

    /**
    * 野怪最大可搜索等级
    * int maxKvkMonsterSearchLevel
    * 
    */
    public int getMaxKvkMonsterSearchLevel(){
        return maxKvkMonsterSearchLevel;
    }

    /**
    * 野怪最小可搜索等级
    * int minKvkMonsterSearchLevel
    * 
    */
    public int getMinKvkMonsterSearchLevel(){
        return minKvkMonsterSearchLevel;
    }


}
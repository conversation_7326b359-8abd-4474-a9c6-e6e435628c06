package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动表.xlsx", node="activity_schedule.xml")
public class ActivityScheduleTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 活动id
    * int activityId
    * 
    */
    @ResAttribute("activityId")
    private  int activityId;

    /**
    * 活动开放类型（自开服起，自创角起，特定日期）
    * CommonEnum.ActivityOpenType openType
    * 
    */
    @ResAttribute("openType")
    private CommonEnum.ActivityOpenType openType;

    /**
    * 计时器类型活动（自开服起，自创角起）开放时间，0为首日
    * int daysForOpen
    * 
    */
    @ResAttribute("daysForOpen")
    private  int daysForOpen;

    /**
    * 赛季阶段参数
    * triple SeasonStateParam
    * 
    */
    @ResAttribute("SeasonStateParam")
    private IntTripleType SeasonStateParamTriple;

    /**
    * 正式服日期类型活动（on_date类）开放时间
    * date dateForOpen
    * 
    */
    @ResAttribute("dateForOpen")
    private Date dateForOpenDt;
    /**
    * 要求玩家等级（不填默认无要求）
    * pairarray requiredPlayerLevel
    * 
    */
    @ResAttribute("requiredPlayerLevel")
    private List<IntPairType> requiredPlayerLevelPairList;

    /**
    * 开服天数限制（不填默认无限制）
    * pairarray serverOpenLimit
    * 
    */
    @ResAttribute("serverOpenLimit")
    private List<IntPairType> serverOpenLimitPairList;

    /**
    * 开服天数满足后能否中途开启
    * bool ServerInsertOpen
    * 
    */
    @ResAttribute("ServerInsertOpen")
    private  boolean ServerInsertOpen;

    /**
    * 禁止中途插入时段
    * pair banInsertPeriod
    * 
    */
    @ResAttribute("banInsertPeriod")
    private IntPairType banInsertPeriodPair;

    /**
    * 循环类型（每天，每周，每月）
    * string loopType
    * 
    */
    @ResAttribute("loopType")
    private  String loopType;

    /**
    * 循环间隔
    * int loopCycle
    * 
    */
    @ResAttribute("loopCycle")
    private  int loopCycle;

    /**
    * 循环参数
    * int loopParam
    * 
    */
    @ResAttribute("loopParam")
    private  int loopParam;

    /**
    * 玩家等级中途满足后能否接取
    * bool halfwayOpen
    * 
    */
    @ResAttribute("halfwayOpen")
    private  boolean halfwayOpen;

    /**
    * 活动序列
    * pairarray activitySeq
    * 
    */
    @ResAttribute("activitySeq")
    private List<IntPairType> activitySeqPairList;

    /**
    * 是否仅在玩家出生服开启
    * bool originalServerOpen
    * 
    */
    @ResAttribute("originalServerOpen")
    private  boolean originalServerOpen;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 活动id
    * int activityId
    * 
    */
    public int getActivityId(){
        return activityId;
    }


    /**
    * 活动开放类型（自开服起，自创角起，特定日期）
    * CommonEnum.ActivityOpenType openType
    * 
    */
    public CommonEnum.ActivityOpenType getOpenType(){
        return openType;
    }
    /**
    * 计时器类型活动（自开服起，自创角起）开放时间，0为首日
    * int daysForOpen
    * 
    */
    public int getDaysForOpen(){
        return daysForOpen;
    }


    /**
    * 赛季阶段参数
    * triple SeasonStateParam
    * 
    */
    public IntTripleType getSeasonStateParamTriple(){
        return SeasonStateParamTriple;
    }

    /**
    * 正式服日期类型活动（on_date类）开放时间
    * date dateForOpen
    * 
    */
    public Date getDateForOpenDt(){
        return dateForOpenDt;
    }

    /**
    * 要求玩家等级（不填默认无要求）
    * pairarray requiredPlayerLevel
    * 
    */
    public List<IntPairType> getRequiredPlayerLevelPairList(){
        return requiredPlayerLevelPairList;
    }

    /**
    * 开服天数限制（不填默认无限制）
    * pairarray serverOpenLimit
    * 
    */
    public List<IntPairType> getServerOpenLimitPairList(){
        return serverOpenLimitPairList;
    }

    /**
    * 开服天数满足后能否中途开启
    * bool ServerInsertOpen
    * 
    */
    public boolean getServerInsertOpen(){
        return ServerInsertOpen;
    }

    /**
    * 禁止中途插入时段
    * pair banInsertPeriod
    * 
    */
    public IntPairType getBanInsertPeriodPair(){
        return banInsertPeriodPair;
    }
    /**
    * 循环类型（每天，每周，每月）
    * string loopType
    * 
    */
    public String getLoopType(){
        return loopType;
    }
    /**
    * 循环间隔
    * int loopCycle
    * 
    */
    public int getLoopCycle(){
        return loopCycle;
    }

    /**
    * 循环参数
    * int loopParam
    * 
    */
    public int getLoopParam(){
        return loopParam;
    }


    /**
    * 玩家等级中途满足后能否接取
    * bool halfwayOpen
    * 
    */
    public boolean getHalfwayOpen(){
        return halfwayOpen;
    }

    /**
    * 活动序列
    * pairarray activitySeq
    * 
    */
    public List<IntPairType> getActivitySeqPairList(){
        return activitySeqPairList;
    }

    /**
    * 是否仅在玩家出生服开启
    * bool originalServerOpen
    * 
    */
    public boolean getOriginalServerOpen(){
        return originalServerOpen;
    }

}
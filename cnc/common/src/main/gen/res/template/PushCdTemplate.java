package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="X_消息推送.xlsx", node="push_CD.xml")
public class PushCdTemplate implements IResTemplate  {

    /**
    * 推送间隔id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 间隔时间（单位：秒）
    * int coldTime
    * 
    */
    @ResAttribute("coldTime")
    private  int coldTime;

    /**
    * 间隔次数
    * int coldNum
    * 
    */
    @ResAttribute("coldNum")
    private  int coldNum;



    /**
    * 推送间隔id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 间隔时间（单位：秒）
    * int coldTime
    * 
    */
    public int getColdTime(){
        return coldTime;
    }

    /**
    * 间隔次数
    * int coldNum
    * 
    */
    public int getColdNum(){
        return coldNum;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_大世界野怪投放区域.xlsx", node="area_refresh_rules.xml")
public class AreaRefreshRulesTemplate implements IResTemplate  {

    /**
    * 州ID序号
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 分组片区数量
    * int areaGrooupNum
    * 
    */
    @ResAttribute("areaGrooupNum")
    private  int areaGrooupNum;

    /**
    * 刷新比例
    * float grooupRatio
    * 
    */

    @ResAttribute("grooupRatio")
    private  float grooupRatio;


    /**
    * 州ID序号
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 分组片区数量
    * int areaGrooupNum
    * 
    */
    public int getAreaGrooupNum(){
        return areaGrooupNum;
    }

    /**
    * 刷新比例
    * float grooupRatio
    * 
    */
    public float getGrooupRatio(){
        return grooupRatio;
    }


}
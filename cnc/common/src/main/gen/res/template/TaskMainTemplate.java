package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="R_任务.xlsx", node="task_main.xml")
public class TaskMainTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 任务ID
    * int taskId
    * 
    */
    @ResAttribute("taskId")
    private  int taskId;

    /**
    * 任务奖励
    * pairarray reward
    * 
    */
    @ResAttribute("reward")
    private List<IntPairType> rewardPairList;

    /**
    * 解锁条件
    * int mainCityunlock
    * 
    */
    @ResAttribute("mainCityunlock")
    private  int mainCityunlock;

    /**
    * 后置任务
    * int frontTask
    * 
    */
    @ResAttribute("frontTask")
    private  int frontTask;

    /**
    * 优先级
    * int priority
    * 
    */
    @ResAttribute("priority")
    private  int priority;

    /**
    * 任务接取触发引导
    * int taskGetGuide
    * 
    */
    @ResAttribute("taskGetGuide")
    private  int taskGetGuide;

    /**
    * 任务完成触发引导
    * int taskCompleteGuide
    * 
    */
    @ResAttribute("taskCompleteGuide")
    private  int taskCompleteGuide;

    /**
    * 任务领奖触发引导
    * int taskDoneGuide
    * 
    */
    @ResAttribute("taskDoneGuide")
    private  int taskDoneGuide;

    /**
    * 任务接取去除引导
    * int taskGetDeleteGuide
    * 
    */
    @ResAttribute("taskGetDeleteGuide")
    private  int taskGetDeleteGuide;

    /**
    * 任务完成去除引导
    * int taskCompleteDeleteGuide
    * 
    */
    @ResAttribute("taskCompleteDeleteGuide")
    private  int taskCompleteDeleteGuide;

    /**
    * 任务领奖去除引导
    * int taskDoneDeleteGuide
    * 
    */
    @ResAttribute("taskDoneDeleteGuide")
    private  int taskDoneDeleteGuide;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 任务ID
    * int taskId
    * 
    */
    public int getTaskId(){
        return taskId;
    }


    /**
    * 任务奖励
    * pairarray reward
    * 
    */
    public List<IntPairType> getRewardPairList(){
        return rewardPairList;
    }
    /**
    * 解锁条件
    * int mainCityunlock
    * 
    */
    public int getMainCityunlock(){
        return mainCityunlock;
    }

    /**
    * 后置任务
    * int frontTask
    * 
    */
    public int getFrontTask(){
        return frontTask;
    }

    /**
    * 优先级
    * int priority
    * 
    */
    public int getPriority(){
        return priority;
    }

    /**
    * 任务接取触发引导
    * int taskGetGuide
    * 
    */
    public int getTaskGetGuide(){
        return taskGetGuide;
    }

    /**
    * 任务完成触发引导
    * int taskCompleteGuide
    * 
    */
    public int getTaskCompleteGuide(){
        return taskCompleteGuide;
    }

    /**
    * 任务领奖触发引导
    * int taskDoneGuide
    * 
    */
    public int getTaskDoneGuide(){
        return taskDoneGuide;
    }

    /**
    * 任务接取去除引导
    * int taskGetDeleteGuide
    * 
    */
    public int getTaskGetDeleteGuide(){
        return taskGetDeleteGuide;
    }

    /**
    * 任务完成去除引导
    * int taskCompleteDeleteGuide
    * 
    */
    public int getTaskCompleteDeleteGuide(){
        return taskCompleteDeleteGuide;
    }

    /**
    * 任务领奖去除引导
    * int taskDoneDeleteGuide
    * 
    */
    public int getTaskDoneDeleteGuide(){
        return taskDoneDeleteGuide;
    }


}
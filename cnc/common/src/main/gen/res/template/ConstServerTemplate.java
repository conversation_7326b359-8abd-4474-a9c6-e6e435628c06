package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "F_服务器多服和移民表.xlsx", node = "const_server.xml", isConst = true)
public class ConstServerTemplate implements IResConstTemplate  {

    /**
     * 判定服务器列表的拥挤状态：开服前N天服务器默认爆满
     */
    private int serverFullDay = 0;
    /**
     * 判定服务器列表的拥挤状态：单区预计注册人数：48000人
     */
    private int serverMaxRegisterPlayerNum = 0;
    /**
     * 判定服务器列表的拥挤状态：在线活跃人数上限百分比预估（数值是百分比）
     */
    private int serverOnlinePlayerPercent = 0;
    /**
     * 判定服务器列表的拥挤状态：拥挤注册人数百分比（数值是百分比）
     */
    private int serverFullRegisterPercent = 0;
    /**
     * 判定服务器列表的拥挤状态：拥挤在线人数百分比下限（数值是百分比）
     */
    private int serverFullOnlinePlayerPercent = 0;
    /**
     * 判定服务器列表的繁忙状态：繁忙在线人数百分比下限（数值是百分比）
     */
    private int serverBusyOnlinePlayerPercent = 0;
    /**
     * 高战战区统计前X%
     */
    private int mulServer_high_preNum = 0;
    /**
     * 默认跳转坐标
     */
    private String jump_sever_center = "";     
    /**
     * 弃用（山洞奖励领取次数触发低级奖励的次数标准）
     */
    private int fogCaveRewardTimeRecord = 0;
    /**
     * 弃用（低级山洞奖励物ID）
     */
    private int fogCaveRewardLowId = 0;
    /**
     * 里程碑id为26的时候，迷雾全开。小于26时，有迷雾。大于等于26没有迷雾。
     */
    private int milestoneFogOpenId = 0;
    /**
     * 探索迷雾的山洞奖励数量限制
     */
    private int frogWaveRewardLimitedNum = 0;
    /**
     * 探索迷雾的山洞奖励，奖励超出数量后发送的邮件ID
     */
    private int frogWaveRewardLimitedMailId = 0;
    /**
     * 高战服务器，统计战力的计算方式：战斗力前300名的玩家
     */
    private int highPowerServerPlayerNum = 0;
    /**
     * 高战服务器，统计战力的计算方式：战斗力前1.5亿的玩家
     */
    private int highPowerServerRankingPower = 0;
    /**
     * 高战服务器，统计战力的计算方式：每隔7200秒统计一次
     */
    private int highPowerServerRankingRereshInterval = 0;
    /**
     * 服务器物理人数上限
     */
    private int server_physicalMaxNum = 0;


    public int getServerFullDay(){
        return this.serverFullDay;
    }

    public int getServerMaxRegisterPlayerNum(){
        return this.serverMaxRegisterPlayerNum;
    }

    public int getServerOnlinePlayerPercent(){
        return this.serverOnlinePlayerPercent;
    }

    public int getServerFullRegisterPercent(){
        return this.serverFullRegisterPercent;
    }

    public int getServerFullOnlinePlayerPercent(){
        return this.serverFullOnlinePlayerPercent;
    }

    public int getServerBusyOnlinePlayerPercent(){
        return this.serverBusyOnlinePlayerPercent;
    }

    public int getMulserverHighPrenum(){
        return this.mulServer_high_preNum;
    }

    public String getJumpSeverCenter(){
        return this.jump_sever_center;
    }   

    public int getFogCaveRewardTimeRecord(){
        return this.fogCaveRewardTimeRecord;
    }

    public int getFogCaveRewardLowId(){
        return this.fogCaveRewardLowId;
    }

    public int getMilestoneFogOpenId(){
        return this.milestoneFogOpenId;
    }

    public int getFrogWaveRewardLimitedNum(){
        return this.frogWaveRewardLimitedNum;
    }

    public int getFrogWaveRewardLimitedMailId(){
        return this.frogWaveRewardLimitedMailId;
    }

    public int getHighPowerServerPlayerNum(){
        return this.highPowerServerPlayerNum;
    }

    public int getHighPowerServerRankingPower(){
        return this.highPowerServerRankingPower;
    }

    public int getHighPowerServerRankingRereshInterval(){
        return this.highPowerServerRankingRereshInterval;
    }

    public int getServerPhysicalmaxnum(){
        return this.server_physicalMaxNum;
    }

    @Override
    public int getId() {
        return 0;
    }
}

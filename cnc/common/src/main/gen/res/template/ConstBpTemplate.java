package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "T_通行证.xlsx", node = "const_bp.xml", isConst = true)
public class ConstBpTemplate implements IResConstTemplate  {

    /**
     * 等级升级经验
     */
    private int BpLevelUpExperience = 0;
    /**
     * 每日宝箱可领取活跃度门槛
     */
    private int BPDailyTreasureThreshold = 0;
    /**
     * 每日宝箱经验值
     */
    private int BPDailyTreasureReward = 0;
    /**
     * 主界面tips文本
     */
    private String BPMainInterfaceTips = "";     
    /**
     * 低档位(4.99$)礼包ID
     */
    private int BPSmallGiftID = 0;
    /**
     * 中档位(15.99$)礼包2ID
     */
    private int BPmidGiftID = 0;
    /**
     * 高档位(19.99$)礼包3ID
     */
    private int BPBigGiftID = 0;
    /**
     * 金币购买等级消耗
     */
    private int BPGoldBuy = 0;
    /**
     * 购买进阶礼包提升等级
     */
    private int BPGoldProLevelUp = 0;
    /**
     * 通行证普通奖励等级
     */
    private int BpBaseRewardLevel = 0;
    /**
     * 购买进阶礼包提升额外任务完成经验
     */
    private int BPGoldProExtraExpRate = 0;
    /**
     * 奖励补发邮件ID
     */
    private int ReissueOfRewardMail = 0;
    /**
     * 低档位(4.99$)礼包ID
     */
    private int SeasonBPSmallGiftID = 0;
    /**
     * 中档位(15.99$)礼包2ID
     */
    private int SeasonBPmidGiftID = 0;
    /**
     * 高档位(19.99$)礼包3ID
     */
    private int SeasonBPBigGiftID = 0;


    public int getBpLevelUpExperience(){
        return this.BpLevelUpExperience;
    }

    public int getBPDailyTreasureThreshold(){
        return this.BPDailyTreasureThreshold;
    }

    public int getBPDailyTreasureReward(){
        return this.BPDailyTreasureReward;
    }

    public String getBPMainInterfaceTips(){
        return this.BPMainInterfaceTips;
    }   

    public int getBPSmallGiftID(){
        return this.BPSmallGiftID;
    }

    public int getBPmidGiftID(){
        return this.BPmidGiftID;
    }

    public int getBPBigGiftID(){
        return this.BPBigGiftID;
    }

    public int getBPGoldBuy(){
        return this.BPGoldBuy;
    }

    public int getBPGoldProLevelUp(){
        return this.BPGoldProLevelUp;
    }

    public int getBpBaseRewardLevel(){
        return this.BpBaseRewardLevel;
    }

    public int getBPGoldProExtraExpRate(){
        return this.BPGoldProExtraExpRate;
    }

    public int getReissueOfRewardMail(){
        return this.ReissueOfRewardMail;
    }

    public int getSeasonBPSmallGiftID(){
        return this.SeasonBPSmallGiftID;
    }

    public int getSeasonBPmidGiftID(){
        return this.SeasonBPmidGiftID;
    }

    public int getSeasonBPBigGiftID(){
        return this.SeasonBPBigGiftID;
    }

    @Override
    public int getId() {
        return 0;
    }
}

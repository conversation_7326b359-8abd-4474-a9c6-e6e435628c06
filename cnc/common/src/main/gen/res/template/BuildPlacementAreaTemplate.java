package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_城建表.xlsx", node="build_placement_area.xml")
public class BuildPlacementAreaTemplate implements IResTemplate  {

    /**
    * 摆放列数
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 摆放行数1
    * int X_1
    * 
    */
    @ResAttribute("X_1")
    private  int X_1;

    /**
    * 摆放行数2
    * int X_2
    * 
    */
    @ResAttribute("X_2")
    private  int X_2;

    /**
    * 摆放行数3
    * int X_3
    * 
    */
    @ResAttribute("X_3")
    private  int X_3;

    /**
    * 摆放行数4
    * int X_4
    * 
    */
    @ResAttribute("X_4")
    private  int X_4;

    /**
    * 摆放行数5
    * int X_5
    * 
    */
    @ResAttribute("X_5")
    private  int X_5;

    /**
    * 摆放行数6
    * int X_6
    * 
    */
    @ResAttribute("X_6")
    private  int X_6;

    /**
    * 摆放行数7
    * int X_7
    * 
    */
    @ResAttribute("X_7")
    private  int X_7;

    /**
    * 摆放行数8
    * int X_8
    * 
    */
    @ResAttribute("X_8")
    private  int X_8;

    /**
    * 摆放行数9
    * int X_9
    * 
    */
    @ResAttribute("X_9")
    private  int X_9;

    /**
    * 摆放行数10
    * int X_10
    * 
    */
    @ResAttribute("X_10")
    private  int X_10;

    /**
    * 摆放行数11
    * int X_11
    * 
    */
    @ResAttribute("X_11")
    private  int X_11;

    /**
    * 摆放行数12
    * int X_12
    * 
    */
    @ResAttribute("X_12")
    private  int X_12;

    /**
    * 摆放行数13
    * int X_13
    * 
    */
    @ResAttribute("X_13")
    private  int X_13;

    /**
    * 摆放行数14
    * int X_14
    * 
    */
    @ResAttribute("X_14")
    private  int X_14;

    /**
    * 摆放行数15
    * int X_15
    * 
    */
    @ResAttribute("X_15")
    private  int X_15;

    /**
    * 摆放行数16
    * int X_16
    * 
    */
    @ResAttribute("X_16")
    private  int X_16;

    /**
    * 摆放行数17
    * int X_17
    * 
    */
    @ResAttribute("X_17")
    private  int X_17;

    /**
    * 摆放行数18
    * int X_18
    * 
    */
    @ResAttribute("X_18")
    private  int X_18;

    /**
    * 摆放行数19
    * int X_19
    * 
    */
    @ResAttribute("X_19")
    private  int X_19;

    /**
    * 摆放行数20
    * int X_20
    * 
    */
    @ResAttribute("X_20")
    private  int X_20;

    /**
    * 摆放行数21
    * int X_21
    * 
    */
    @ResAttribute("X_21")
    private  int X_21;

    /**
    * 摆放行数22
    * int X_22
    * 
    */
    @ResAttribute("X_22")
    private  int X_22;

    /**
    * 摆放行数23
    * int X_23
    * 
    */
    @ResAttribute("X_23")
    private  int X_23;

    /**
    * 摆放行数24
    * int X_24
    * 
    */
    @ResAttribute("X_24")
    private  int X_24;

    /**
    * 摆放行数25
    * int X_25
    * 
    */
    @ResAttribute("X_25")
    private  int X_25;

    /**
    * 摆放行数26
    * int X_26
    * 
    */
    @ResAttribute("X_26")
    private  int X_26;

    /**
    * 摆放行数27
    * int X_27
    * 
    */
    @ResAttribute("X_27")
    private  int X_27;

    /**
    * 摆放行数28
    * int X_28
    * 
    */
    @ResAttribute("X_28")
    private  int X_28;

    /**
    * 摆放行数29
    * int X_29
    * 
    */
    @ResAttribute("X_29")
    private  int X_29;

    /**
    * 摆放行数30
    * int X_30
    * 
    */
    @ResAttribute("X_30")
    private  int X_30;

    /**
    * 摆放行数31
    * int X_31
    * 
    */
    @ResAttribute("X_31")
    private  int X_31;

    /**
    * 摆放行数32
    * int X_32
    * 
    */
    @ResAttribute("X_32")
    private  int X_32;

    /**
    * 摆放行数33
    * int X_33
    * 
    */
    @ResAttribute("X_33")
    private  int X_33;

    /**
    * 摆放行数34
    * int X_34
    * 
    */
    @ResAttribute("X_34")
    private  int X_34;

    /**
    * 摆放行数35
    * int X_35
    * 
    */
    @ResAttribute("X_35")
    private  int X_35;

    /**
    * 摆放行数36
    * int X_36
    * 
    */
    @ResAttribute("X_36")
    private  int X_36;

    /**
    * 摆放行数37
    * int X_37
    * 
    */
    @ResAttribute("X_37")
    private  int X_37;

    /**
    * 摆放行数38
    * int X_38
    * 
    */
    @ResAttribute("X_38")
    private  int X_38;

    /**
    * 摆放行数39
    * int X_39
    * 
    */
    @ResAttribute("X_39")
    private  int X_39;

    /**
    * 摆放行数40
    * int X_40
    * 
    */
    @ResAttribute("X_40")
    private  int X_40;

    /**
    * 摆放行数41
    * int X_41
    * 
    */
    @ResAttribute("X_41")
    private  int X_41;

    /**
    * 摆放行数42
    * int X_42
    * 
    */
    @ResAttribute("X_42")
    private  int X_42;

    /**
    * 摆放行数43
    * int X_43
    * 
    */
    @ResAttribute("X_43")
    private  int X_43;

    /**
    * 摆放行数44
    * int X_44
    * 
    */
    @ResAttribute("X_44")
    private  int X_44;

    /**
    * 摆放行数45
    * int X_45
    * 
    */
    @ResAttribute("X_45")
    private  int X_45;

    /**
    * 摆放行数46
    * int X_46
    * 
    */
    @ResAttribute("X_46")
    private  int X_46;

    /**
    * 摆放行数47
    * int X_47
    * 
    */
    @ResAttribute("X_47")
    private  int X_47;

    /**
    * 摆放行数48
    * int X_48
    * 
    */
    @ResAttribute("X_48")
    private  int X_48;

    /**
    * 摆放行数49
    * int X_49
    * 
    */
    @ResAttribute("X_49")
    private  int X_49;

    /**
    * 摆放行数50
    * int X_50
    * 
    */
    @ResAttribute("X_50")
    private  int X_50;

    /**
    * 摆放行数51
    * int X_51
    * 
    */
    @ResAttribute("X_51")
    private  int X_51;

    /**
    * 摆放行数52
    * int X_52
    * 
    */
    @ResAttribute("X_52")
    private  int X_52;

    /**
    * 摆放行数53
    * int X_53
    * 
    */
    @ResAttribute("X_53")
    private  int X_53;

    /**
    * 摆放行数54
    * int X_54
    * 
    */
    @ResAttribute("X_54")
    private  int X_54;

    /**
    * 摆放行数55
    * int X_55
    * 
    */
    @ResAttribute("X_55")
    private  int X_55;

    /**
    * 摆放行数56
    * int X_56
    * 
    */
    @ResAttribute("X_56")
    private  int X_56;

    /**
    * 摆放行数57
    * int X_57
    * 
    */
    @ResAttribute("X_57")
    private  int X_57;

    /**
    * 摆放行数58
    * int X_58
    * 
    */
    @ResAttribute("X_58")
    private  int X_58;

    /**
    * 摆放行数59
    * int X_59
    * 
    */
    @ResAttribute("X_59")
    private  int X_59;

    /**
    * 摆放行数60
    * int X_60
    * 
    */
    @ResAttribute("X_60")
    private  int X_60;

    /**
    * 摆放行数61
    * int X_61
    * 
    */
    @ResAttribute("X_61")
    private  int X_61;

    /**
    * 摆放行数62
    * int X_62
    * 
    */
    @ResAttribute("X_62")
    private  int X_62;

    /**
    * 摆放行数63
    * int X_63
    * 
    */
    @ResAttribute("X_63")
    private  int X_63;

    /**
    * 摆放行数64
    * int X_64
    * 
    */
    @ResAttribute("X_64")
    private  int X_64;

    /**
    * 摆放行数65
    * int X_65
    * 
    */
    @ResAttribute("X_65")
    private  int X_65;

    /**
    * 摆放行数66
    * int X_66
    * 
    */
    @ResAttribute("X_66")
    private  int X_66;

    /**
    * 摆放行数67
    * int X_67
    * 
    */
    @ResAttribute("X_67")
    private  int X_67;

    /**
    * 摆放行数68
    * int X_68
    * 
    */
    @ResAttribute("X_68")
    private  int X_68;

    /**
    * 摆放行数69
    * int X_69
    * 
    */
    @ResAttribute("X_69")
    private  int X_69;

    /**
    * 摆放行数70
    * int X_70
    * 
    */
    @ResAttribute("X_70")
    private  int X_70;

    /**
    * 摆放行数71
    * int X_71
    * 
    */
    @ResAttribute("X_71")
    private  int X_71;

    /**
    * 摆放行数72
    * int X_72
    * 
    */
    @ResAttribute("X_72")
    private  int X_72;

    /**
    * 摆放行数73
    * int X_73
    * 
    */
    @ResAttribute("X_73")
    private  int X_73;

    /**
    * 摆放行数74
    * int X_74
    * 
    */
    @ResAttribute("X_74")
    private  int X_74;

    /**
    * 摆放行数75
    * int X_75
    * 
    */
    @ResAttribute("X_75")
    private  int X_75;

    /**
    * 摆放行数76
    * int X_76
    * 
    */
    @ResAttribute("X_76")
    private  int X_76;

    /**
    * 摆放行数77
    * int X_77
    * 
    */
    @ResAttribute("X_77")
    private  int X_77;

    /**
    * 摆放行数78
    * int X_78
    * 
    */
    @ResAttribute("X_78")
    private  int X_78;

    /**
    * 摆放行数79
    * int X_79
    * 
    */
    @ResAttribute("X_79")
    private  int X_79;

    /**
    * 摆放行数80
    * int X_80
    * 
    */
    @ResAttribute("X_80")
    private  int X_80;

    /**
    * 摆放行数81
    * int X_81
    * 
    */
    @ResAttribute("X_81")
    private  int X_81;

    /**
    * 摆放行数82
    * int X_82
    * 
    */
    @ResAttribute("X_82")
    private  int X_82;

    /**
    * 摆放行数83
    * int X_83
    * 
    */
    @ResAttribute("X_83")
    private  int X_83;

    /**
    * 摆放行数84
    * int X_84
    * 
    */
    @ResAttribute("X_84")
    private  int X_84;

    /**
    * 摆放行数85
    * int X_85
    * 
    */
    @ResAttribute("X_85")
    private  int X_85;

    /**
    * 摆放行数86
    * int X_86
    * 
    */
    @ResAttribute("X_86")
    private  int X_86;

    /**
    * 摆放行数87
    * int X_87
    * 
    */
    @ResAttribute("X_87")
    private  int X_87;

    /**
    * 摆放行数88
    * int X_88
    * 
    */
    @ResAttribute("X_88")
    private  int X_88;

    /**
    * 摆放行数89
    * int X_89
    * 
    */
    @ResAttribute("X_89")
    private  int X_89;

    /**
    * 摆放行数90
    * int X_90
    * 
    */
    @ResAttribute("X_90")
    private  int X_90;

    /**
    * 摆放行数91
    * int X_91
    * 
    */
    @ResAttribute("X_91")
    private  int X_91;

    /**
    * 摆放行数92
    * int X_92
    * 
    */
    @ResAttribute("X_92")
    private  int X_92;

    /**
    * 摆放行数93
    * int X_93
    * 
    */
    @ResAttribute("X_93")
    private  int X_93;

    /**
    * 摆放行数94
    * int X_94
    * 
    */
    @ResAttribute("X_94")
    private  int X_94;

    /**
    * 摆放行数95
    * int X_95
    * 
    */
    @ResAttribute("X_95")
    private  int X_95;

    /**
    * 摆放行数96
    * int X_96
    * 
    */
    @ResAttribute("X_96")
    private  int X_96;

    /**
    * 摆放行数97
    * int X_97
    * 
    */
    @ResAttribute("X_97")
    private  int X_97;

    /**
    * 摆放行数98
    * int X_98
    * 
    */
    @ResAttribute("X_98")
    private  int X_98;

    /**
    * 摆放行数99
    * int X_99
    * 
    */
    @ResAttribute("X_99")
    private  int X_99;

    /**
    * 摆放行数100
    * int X_100
    * 
    */
    @ResAttribute("X_100")
    private  int X_100;

    /**
    * 摆放行数101
    * int X_101
    * 
    */
    @ResAttribute("X_101")
    private  int X_101;

    /**
    * 摆放行数102
    * int X_102
    * 
    */
    @ResAttribute("X_102")
    private  int X_102;

    /**
    * 摆放行数103
    * int X_103
    * 
    */
    @ResAttribute("X_103")
    private  int X_103;

    /**
    * 摆放行数104
    * int X_104
    * 
    */
    @ResAttribute("X_104")
    private  int X_104;

    /**
    * 摆放行数105
    * int X_105
    * 
    */
    @ResAttribute("X_105")
    private  int X_105;

    /**
    * 摆放行数106
    * int X_106
    * 
    */
    @ResAttribute("X_106")
    private  int X_106;

    /**
    * 摆放行数107
    * int X_107
    * 
    */
    @ResAttribute("X_107")
    private  int X_107;

    /**
    * 摆放行数108
    * int X_108
    * 
    */
    @ResAttribute("X_108")
    private  int X_108;

    /**
    * 摆放行数109
    * int X_109
    * 
    */
    @ResAttribute("X_109")
    private  int X_109;

    /**
    * 摆放行数110
    * int X_110
    * 
    */
    @ResAttribute("X_110")
    private  int X_110;

    /**
    * 摆放行数111
    * int X_111
    * 
    */
    @ResAttribute("X_111")
    private  int X_111;

    /**
    * 摆放行数112
    * int X_112
    * 
    */
    @ResAttribute("X_112")
    private  int X_112;

    /**
    * 摆放行数113
    * int X_113
    * 
    */
    @ResAttribute("X_113")
    private  int X_113;

    /**
    * 摆放行数114
    * int X_114
    * 
    */
    @ResAttribute("X_114")
    private  int X_114;

    /**
    * 摆放行数115
    * int X_115
    * 
    */
    @ResAttribute("X_115")
    private  int X_115;

    /**
    * 摆放行数116
    * int X_116
    * 
    */
    @ResAttribute("X_116")
    private  int X_116;

    /**
    * 摆放行数117
    * int X_117
    * 
    */
    @ResAttribute("X_117")
    private  int X_117;

    /**
    * 摆放行数118
    * int X_118
    * 
    */
    @ResAttribute("X_118")
    private  int X_118;

    /**
    * 摆放行数119
    * int X_119
    * 
    */
    @ResAttribute("X_119")
    private  int X_119;

    /**
    * 摆放行数120
    * int X_120
    * 
    */
    @ResAttribute("X_120")
    private  int X_120;

    /**
    * 摆放行数121
    * int X_121
    * 
    */
    @ResAttribute("X_121")
    private  int X_121;

    /**
    * 摆放行数122
    * int X_122
    * 
    */
    @ResAttribute("X_122")
    private  int X_122;

    /**
    * 摆放行数123
    * int X_123
    * 
    */
    @ResAttribute("X_123")
    private  int X_123;

    /**
    * 摆放行数124
    * int X_124
    * 
    */
    @ResAttribute("X_124")
    private  int X_124;

    /**
    * 摆放行数125
    * int X_125
    * 
    */
    @ResAttribute("X_125")
    private  int X_125;

    /**
    * 摆放行数126
    * int X_126
    * 
    */
    @ResAttribute("X_126")
    private  int X_126;

    /**
    * 摆放行数127
    * int X_127
    * 
    */
    @ResAttribute("X_127")
    private  int X_127;

    /**
    * 摆放行数128
    * int X_128
    * 
    */
    @ResAttribute("X_128")
    private  int X_128;

    /**
    * 摆放行数129
    * int X_129
    * 
    */
    @ResAttribute("X_129")
    private  int X_129;

    /**
    * 摆放行数130
    * int X_130
    * 
    */
    @ResAttribute("X_130")
    private  int X_130;

    /**
    * 摆放行数131
    * int X_131
    * 
    */
    @ResAttribute("X_131")
    private  int X_131;

    /**
    * 摆放行数132
    * int X_132
    * 
    */
    @ResAttribute("X_132")
    private  int X_132;

    /**
    * 摆放行数133
    * int X_133
    * 
    */
    @ResAttribute("X_133")
    private  int X_133;

    /**
    * 摆放行数134
    * int X_134
    * 
    */
    @ResAttribute("X_134")
    private  int X_134;

    /**
    * 摆放行数135
    * int X_135
    * 
    */
    @ResAttribute("X_135")
    private  int X_135;

    /**
    * 摆放行数136
    * int X_136
    * 
    */
    @ResAttribute("X_136")
    private  int X_136;

    /**
    * 摆放行数137
    * int X_137
    * 
    */
    @ResAttribute("X_137")
    private  int X_137;

    /**
    * 摆放行数138
    * int X_138
    * 
    */
    @ResAttribute("X_138")
    private  int X_138;

    /**
    * 摆放行数139
    * int X_139
    * 
    */
    @ResAttribute("X_139")
    private  int X_139;

    /**
    * 摆放行数140
    * int X_140
    * 
    */
    @ResAttribute("X_140")
    private  int X_140;

    /**
    * 摆放行数141
    * int X_141
    * 
    */
    @ResAttribute("X_141")
    private  int X_141;

    /**
    * 摆放行数142
    * int X_142
    * 
    */
    @ResAttribute("X_142")
    private  int X_142;

    /**
    * 摆放行数143
    * int X_143
    * 
    */
    @ResAttribute("X_143")
    private  int X_143;

    /**
    * 摆放行数144
    * int X_144
    * 
    */
    @ResAttribute("X_144")
    private  int X_144;

    /**
    * 摆放行数145
    * int X_145
    * 
    */
    @ResAttribute("X_145")
    private  int X_145;

    /**
    * 摆放行数146
    * int X_146
    * 
    */
    @ResAttribute("X_146")
    private  int X_146;

    /**
    * 摆放行数147
    * int X_147
    * 
    */
    @ResAttribute("X_147")
    private  int X_147;

    /**
    * 摆放行数148
    * int X_148
    * 
    */
    @ResAttribute("X_148")
    private  int X_148;

    /**
    * 摆放行数149
    * int X_149
    * 
    */
    @ResAttribute("X_149")
    private  int X_149;

    /**
    * 摆放行数150
    * int X_150
    * 
    */
    @ResAttribute("X_150")
    private  int X_150;

    /**
    * 摆放行数151
    * int X_151
    * 
    */
    @ResAttribute("X_151")
    private  int X_151;

    /**
    * 摆放行数152
    * int X_152
    * 
    */
    @ResAttribute("X_152")
    private  int X_152;

    /**
    * 摆放行数153
    * int X_153
    * 
    */
    @ResAttribute("X_153")
    private  int X_153;

    /**
    * 摆放行数154
    * int X_154
    * 
    */
    @ResAttribute("X_154")
    private  int X_154;

    /**
    * 摆放行数155
    * int X_155
    * 
    */
    @ResAttribute("X_155")
    private  int X_155;

    /**
    * 摆放行数156
    * int X_156
    * 
    */
    @ResAttribute("X_156")
    private  int X_156;

    /**
    * 摆放行数157
    * int X_157
    * 
    */
    @ResAttribute("X_157")
    private  int X_157;

    /**
    * 摆放行数158
    * int X_158
    * 
    */
    @ResAttribute("X_158")
    private  int X_158;

    /**
    * 摆放行数159
    * int X_159
    * 
    */
    @ResAttribute("X_159")
    private  int X_159;

    /**
    * 摆放行数160
    * int X_160
    * 
    */
    @ResAttribute("X_160")
    private  int X_160;

    /**
    * 摆放行数161
    * int X_161
    * 
    */
    @ResAttribute("X_161")
    private  int X_161;



    /**
    * 摆放列数
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 摆放行数1
    * int X_1
    * 
    */
    public int getX1(){
        return X_1;
    }

    /**
    * 摆放行数2
    * int X_2
    * 
    */
    public int getX2(){
        return X_2;
    }

    /**
    * 摆放行数3
    * int X_3
    * 
    */
    public int getX3(){
        return X_3;
    }

    /**
    * 摆放行数4
    * int X_4
    * 
    */
    public int getX4(){
        return X_4;
    }

    /**
    * 摆放行数5
    * int X_5
    * 
    */
    public int getX5(){
        return X_5;
    }

    /**
    * 摆放行数6
    * int X_6
    * 
    */
    public int getX6(){
        return X_6;
    }

    /**
    * 摆放行数7
    * int X_7
    * 
    */
    public int getX7(){
        return X_7;
    }

    /**
    * 摆放行数8
    * int X_8
    * 
    */
    public int getX8(){
        return X_8;
    }

    /**
    * 摆放行数9
    * int X_9
    * 
    */
    public int getX9(){
        return X_9;
    }

    /**
    * 摆放行数10
    * int X_10
    * 
    */
    public int getX10(){
        return X_10;
    }

    /**
    * 摆放行数11
    * int X_11
    * 
    */
    public int getX11(){
        return X_11;
    }

    /**
    * 摆放行数12
    * int X_12
    * 
    */
    public int getX12(){
        return X_12;
    }

    /**
    * 摆放行数13
    * int X_13
    * 
    */
    public int getX13(){
        return X_13;
    }

    /**
    * 摆放行数14
    * int X_14
    * 
    */
    public int getX14(){
        return X_14;
    }

    /**
    * 摆放行数15
    * int X_15
    * 
    */
    public int getX15(){
        return X_15;
    }

    /**
    * 摆放行数16
    * int X_16
    * 
    */
    public int getX16(){
        return X_16;
    }

    /**
    * 摆放行数17
    * int X_17
    * 
    */
    public int getX17(){
        return X_17;
    }

    /**
    * 摆放行数18
    * int X_18
    * 
    */
    public int getX18(){
        return X_18;
    }

    /**
    * 摆放行数19
    * int X_19
    * 
    */
    public int getX19(){
        return X_19;
    }

    /**
    * 摆放行数20
    * int X_20
    * 
    */
    public int getX20(){
        return X_20;
    }

    /**
    * 摆放行数21
    * int X_21
    * 
    */
    public int getX21(){
        return X_21;
    }

    /**
    * 摆放行数22
    * int X_22
    * 
    */
    public int getX22(){
        return X_22;
    }

    /**
    * 摆放行数23
    * int X_23
    * 
    */
    public int getX23(){
        return X_23;
    }

    /**
    * 摆放行数24
    * int X_24
    * 
    */
    public int getX24(){
        return X_24;
    }

    /**
    * 摆放行数25
    * int X_25
    * 
    */
    public int getX25(){
        return X_25;
    }

    /**
    * 摆放行数26
    * int X_26
    * 
    */
    public int getX26(){
        return X_26;
    }

    /**
    * 摆放行数27
    * int X_27
    * 
    */
    public int getX27(){
        return X_27;
    }

    /**
    * 摆放行数28
    * int X_28
    * 
    */
    public int getX28(){
        return X_28;
    }

    /**
    * 摆放行数29
    * int X_29
    * 
    */
    public int getX29(){
        return X_29;
    }

    /**
    * 摆放行数30
    * int X_30
    * 
    */
    public int getX30(){
        return X_30;
    }

    /**
    * 摆放行数31
    * int X_31
    * 
    */
    public int getX31(){
        return X_31;
    }

    /**
    * 摆放行数32
    * int X_32
    * 
    */
    public int getX32(){
        return X_32;
    }

    /**
    * 摆放行数33
    * int X_33
    * 
    */
    public int getX33(){
        return X_33;
    }

    /**
    * 摆放行数34
    * int X_34
    * 
    */
    public int getX34(){
        return X_34;
    }

    /**
    * 摆放行数35
    * int X_35
    * 
    */
    public int getX35(){
        return X_35;
    }

    /**
    * 摆放行数36
    * int X_36
    * 
    */
    public int getX36(){
        return X_36;
    }

    /**
    * 摆放行数37
    * int X_37
    * 
    */
    public int getX37(){
        return X_37;
    }

    /**
    * 摆放行数38
    * int X_38
    * 
    */
    public int getX38(){
        return X_38;
    }

    /**
    * 摆放行数39
    * int X_39
    * 
    */
    public int getX39(){
        return X_39;
    }

    /**
    * 摆放行数40
    * int X_40
    * 
    */
    public int getX40(){
        return X_40;
    }

    /**
    * 摆放行数41
    * int X_41
    * 
    */
    public int getX41(){
        return X_41;
    }

    /**
    * 摆放行数42
    * int X_42
    * 
    */
    public int getX42(){
        return X_42;
    }

    /**
    * 摆放行数43
    * int X_43
    * 
    */
    public int getX43(){
        return X_43;
    }

    /**
    * 摆放行数44
    * int X_44
    * 
    */
    public int getX44(){
        return X_44;
    }

    /**
    * 摆放行数45
    * int X_45
    * 
    */
    public int getX45(){
        return X_45;
    }

    /**
    * 摆放行数46
    * int X_46
    * 
    */
    public int getX46(){
        return X_46;
    }

    /**
    * 摆放行数47
    * int X_47
    * 
    */
    public int getX47(){
        return X_47;
    }

    /**
    * 摆放行数48
    * int X_48
    * 
    */
    public int getX48(){
        return X_48;
    }

    /**
    * 摆放行数49
    * int X_49
    * 
    */
    public int getX49(){
        return X_49;
    }

    /**
    * 摆放行数50
    * int X_50
    * 
    */
    public int getX50(){
        return X_50;
    }

    /**
    * 摆放行数51
    * int X_51
    * 
    */
    public int getX51(){
        return X_51;
    }

    /**
    * 摆放行数52
    * int X_52
    * 
    */
    public int getX52(){
        return X_52;
    }

    /**
    * 摆放行数53
    * int X_53
    * 
    */
    public int getX53(){
        return X_53;
    }

    /**
    * 摆放行数54
    * int X_54
    * 
    */
    public int getX54(){
        return X_54;
    }

    /**
    * 摆放行数55
    * int X_55
    * 
    */
    public int getX55(){
        return X_55;
    }

    /**
    * 摆放行数56
    * int X_56
    * 
    */
    public int getX56(){
        return X_56;
    }

    /**
    * 摆放行数57
    * int X_57
    * 
    */
    public int getX57(){
        return X_57;
    }

    /**
    * 摆放行数58
    * int X_58
    * 
    */
    public int getX58(){
        return X_58;
    }

    /**
    * 摆放行数59
    * int X_59
    * 
    */
    public int getX59(){
        return X_59;
    }

    /**
    * 摆放行数60
    * int X_60
    * 
    */
    public int getX60(){
        return X_60;
    }

    /**
    * 摆放行数61
    * int X_61
    * 
    */
    public int getX61(){
        return X_61;
    }

    /**
    * 摆放行数62
    * int X_62
    * 
    */
    public int getX62(){
        return X_62;
    }

    /**
    * 摆放行数63
    * int X_63
    * 
    */
    public int getX63(){
        return X_63;
    }

    /**
    * 摆放行数64
    * int X_64
    * 
    */
    public int getX64(){
        return X_64;
    }

    /**
    * 摆放行数65
    * int X_65
    * 
    */
    public int getX65(){
        return X_65;
    }

    /**
    * 摆放行数66
    * int X_66
    * 
    */
    public int getX66(){
        return X_66;
    }

    /**
    * 摆放行数67
    * int X_67
    * 
    */
    public int getX67(){
        return X_67;
    }

    /**
    * 摆放行数68
    * int X_68
    * 
    */
    public int getX68(){
        return X_68;
    }

    /**
    * 摆放行数69
    * int X_69
    * 
    */
    public int getX69(){
        return X_69;
    }

    /**
    * 摆放行数70
    * int X_70
    * 
    */
    public int getX70(){
        return X_70;
    }

    /**
    * 摆放行数71
    * int X_71
    * 
    */
    public int getX71(){
        return X_71;
    }

    /**
    * 摆放行数72
    * int X_72
    * 
    */
    public int getX72(){
        return X_72;
    }

    /**
    * 摆放行数73
    * int X_73
    * 
    */
    public int getX73(){
        return X_73;
    }

    /**
    * 摆放行数74
    * int X_74
    * 
    */
    public int getX74(){
        return X_74;
    }

    /**
    * 摆放行数75
    * int X_75
    * 
    */
    public int getX75(){
        return X_75;
    }

    /**
    * 摆放行数76
    * int X_76
    * 
    */
    public int getX76(){
        return X_76;
    }

    /**
    * 摆放行数77
    * int X_77
    * 
    */
    public int getX77(){
        return X_77;
    }

    /**
    * 摆放行数78
    * int X_78
    * 
    */
    public int getX78(){
        return X_78;
    }

    /**
    * 摆放行数79
    * int X_79
    * 
    */
    public int getX79(){
        return X_79;
    }

    /**
    * 摆放行数80
    * int X_80
    * 
    */
    public int getX80(){
        return X_80;
    }

    /**
    * 摆放行数81
    * int X_81
    * 
    */
    public int getX81(){
        return X_81;
    }

    /**
    * 摆放行数82
    * int X_82
    * 
    */
    public int getX82(){
        return X_82;
    }

    /**
    * 摆放行数83
    * int X_83
    * 
    */
    public int getX83(){
        return X_83;
    }

    /**
    * 摆放行数84
    * int X_84
    * 
    */
    public int getX84(){
        return X_84;
    }

    /**
    * 摆放行数85
    * int X_85
    * 
    */
    public int getX85(){
        return X_85;
    }

    /**
    * 摆放行数86
    * int X_86
    * 
    */
    public int getX86(){
        return X_86;
    }

    /**
    * 摆放行数87
    * int X_87
    * 
    */
    public int getX87(){
        return X_87;
    }

    /**
    * 摆放行数88
    * int X_88
    * 
    */
    public int getX88(){
        return X_88;
    }

    /**
    * 摆放行数89
    * int X_89
    * 
    */
    public int getX89(){
        return X_89;
    }

    /**
    * 摆放行数90
    * int X_90
    * 
    */
    public int getX90(){
        return X_90;
    }

    /**
    * 摆放行数91
    * int X_91
    * 
    */
    public int getX91(){
        return X_91;
    }

    /**
    * 摆放行数92
    * int X_92
    * 
    */
    public int getX92(){
        return X_92;
    }

    /**
    * 摆放行数93
    * int X_93
    * 
    */
    public int getX93(){
        return X_93;
    }

    /**
    * 摆放行数94
    * int X_94
    * 
    */
    public int getX94(){
        return X_94;
    }

    /**
    * 摆放行数95
    * int X_95
    * 
    */
    public int getX95(){
        return X_95;
    }

    /**
    * 摆放行数96
    * int X_96
    * 
    */
    public int getX96(){
        return X_96;
    }

    /**
    * 摆放行数97
    * int X_97
    * 
    */
    public int getX97(){
        return X_97;
    }

    /**
    * 摆放行数98
    * int X_98
    * 
    */
    public int getX98(){
        return X_98;
    }

    /**
    * 摆放行数99
    * int X_99
    * 
    */
    public int getX99(){
        return X_99;
    }

    /**
    * 摆放行数100
    * int X_100
    * 
    */
    public int getX100(){
        return X_100;
    }

    /**
    * 摆放行数101
    * int X_101
    * 
    */
    public int getX101(){
        return X_101;
    }

    /**
    * 摆放行数102
    * int X_102
    * 
    */
    public int getX102(){
        return X_102;
    }

    /**
    * 摆放行数103
    * int X_103
    * 
    */
    public int getX103(){
        return X_103;
    }

    /**
    * 摆放行数104
    * int X_104
    * 
    */
    public int getX104(){
        return X_104;
    }

    /**
    * 摆放行数105
    * int X_105
    * 
    */
    public int getX105(){
        return X_105;
    }

    /**
    * 摆放行数106
    * int X_106
    * 
    */
    public int getX106(){
        return X_106;
    }

    /**
    * 摆放行数107
    * int X_107
    * 
    */
    public int getX107(){
        return X_107;
    }

    /**
    * 摆放行数108
    * int X_108
    * 
    */
    public int getX108(){
        return X_108;
    }

    /**
    * 摆放行数109
    * int X_109
    * 
    */
    public int getX109(){
        return X_109;
    }

    /**
    * 摆放行数110
    * int X_110
    * 
    */
    public int getX110(){
        return X_110;
    }

    /**
    * 摆放行数111
    * int X_111
    * 
    */
    public int getX111(){
        return X_111;
    }

    /**
    * 摆放行数112
    * int X_112
    * 
    */
    public int getX112(){
        return X_112;
    }

    /**
    * 摆放行数113
    * int X_113
    * 
    */
    public int getX113(){
        return X_113;
    }

    /**
    * 摆放行数114
    * int X_114
    * 
    */
    public int getX114(){
        return X_114;
    }

    /**
    * 摆放行数115
    * int X_115
    * 
    */
    public int getX115(){
        return X_115;
    }

    /**
    * 摆放行数116
    * int X_116
    * 
    */
    public int getX116(){
        return X_116;
    }

    /**
    * 摆放行数117
    * int X_117
    * 
    */
    public int getX117(){
        return X_117;
    }

    /**
    * 摆放行数118
    * int X_118
    * 
    */
    public int getX118(){
        return X_118;
    }

    /**
    * 摆放行数119
    * int X_119
    * 
    */
    public int getX119(){
        return X_119;
    }

    /**
    * 摆放行数120
    * int X_120
    * 
    */
    public int getX120(){
        return X_120;
    }

    /**
    * 摆放行数121
    * int X_121
    * 
    */
    public int getX121(){
        return X_121;
    }

    /**
    * 摆放行数122
    * int X_122
    * 
    */
    public int getX122(){
        return X_122;
    }

    /**
    * 摆放行数123
    * int X_123
    * 
    */
    public int getX123(){
        return X_123;
    }

    /**
    * 摆放行数124
    * int X_124
    * 
    */
    public int getX124(){
        return X_124;
    }

    /**
    * 摆放行数125
    * int X_125
    * 
    */
    public int getX125(){
        return X_125;
    }

    /**
    * 摆放行数126
    * int X_126
    * 
    */
    public int getX126(){
        return X_126;
    }

    /**
    * 摆放行数127
    * int X_127
    * 
    */
    public int getX127(){
        return X_127;
    }

    /**
    * 摆放行数128
    * int X_128
    * 
    */
    public int getX128(){
        return X_128;
    }

    /**
    * 摆放行数129
    * int X_129
    * 
    */
    public int getX129(){
        return X_129;
    }

    /**
    * 摆放行数130
    * int X_130
    * 
    */
    public int getX130(){
        return X_130;
    }

    /**
    * 摆放行数131
    * int X_131
    * 
    */
    public int getX131(){
        return X_131;
    }

    /**
    * 摆放行数132
    * int X_132
    * 
    */
    public int getX132(){
        return X_132;
    }

    /**
    * 摆放行数133
    * int X_133
    * 
    */
    public int getX133(){
        return X_133;
    }

    /**
    * 摆放行数134
    * int X_134
    * 
    */
    public int getX134(){
        return X_134;
    }

    /**
    * 摆放行数135
    * int X_135
    * 
    */
    public int getX135(){
        return X_135;
    }

    /**
    * 摆放行数136
    * int X_136
    * 
    */
    public int getX136(){
        return X_136;
    }

    /**
    * 摆放行数137
    * int X_137
    * 
    */
    public int getX137(){
        return X_137;
    }

    /**
    * 摆放行数138
    * int X_138
    * 
    */
    public int getX138(){
        return X_138;
    }

    /**
    * 摆放行数139
    * int X_139
    * 
    */
    public int getX139(){
        return X_139;
    }

    /**
    * 摆放行数140
    * int X_140
    * 
    */
    public int getX140(){
        return X_140;
    }

    /**
    * 摆放行数141
    * int X_141
    * 
    */
    public int getX141(){
        return X_141;
    }

    /**
    * 摆放行数142
    * int X_142
    * 
    */
    public int getX142(){
        return X_142;
    }

    /**
    * 摆放行数143
    * int X_143
    * 
    */
    public int getX143(){
        return X_143;
    }

    /**
    * 摆放行数144
    * int X_144
    * 
    */
    public int getX144(){
        return X_144;
    }

    /**
    * 摆放行数145
    * int X_145
    * 
    */
    public int getX145(){
        return X_145;
    }

    /**
    * 摆放行数146
    * int X_146
    * 
    */
    public int getX146(){
        return X_146;
    }

    /**
    * 摆放行数147
    * int X_147
    * 
    */
    public int getX147(){
        return X_147;
    }

    /**
    * 摆放行数148
    * int X_148
    * 
    */
    public int getX148(){
        return X_148;
    }

    /**
    * 摆放行数149
    * int X_149
    * 
    */
    public int getX149(){
        return X_149;
    }

    /**
    * 摆放行数150
    * int X_150
    * 
    */
    public int getX150(){
        return X_150;
    }

    /**
    * 摆放行数151
    * int X_151
    * 
    */
    public int getX151(){
        return X_151;
    }

    /**
    * 摆放行数152
    * int X_152
    * 
    */
    public int getX152(){
        return X_152;
    }

    /**
    * 摆放行数153
    * int X_153
    * 
    */
    public int getX153(){
        return X_153;
    }

    /**
    * 摆放行数154
    * int X_154
    * 
    */
    public int getX154(){
        return X_154;
    }

    /**
    * 摆放行数155
    * int X_155
    * 
    */
    public int getX155(){
        return X_155;
    }

    /**
    * 摆放行数156
    * int X_156
    * 
    */
    public int getX156(){
        return X_156;
    }

    /**
    * 摆放行数157
    * int X_157
    * 
    */
    public int getX157(){
        return X_157;
    }

    /**
    * 摆放行数158
    * int X_158
    * 
    */
    public int getX158(){
        return X_158;
    }

    /**
    * 摆放行数159
    * int X_159
    * 
    */
    public int getX159(){
        return X_159;
    }

    /**
    * 摆放行数160
    * int X_160
    * 
    */
    public int getX160(){
        return X_160;
    }

    /**
    * 摆放行数161
    * int X_161
    * 
    */
    public int getX161(){
        return X_161;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_战斗单位配置表【RH】.xlsx", node="restraint_factor.xml")
public class RestraintFactorTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 克制描述
    * string RestraintDescription
    * 
    */
    @ResAttribute("RestraintDescription")
    private  String RestraintDescription;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 克制描述
    * string RestraintDescription
    * 
    */
    public String getRestraintDescription(){
        return RestraintDescription;
    }

}
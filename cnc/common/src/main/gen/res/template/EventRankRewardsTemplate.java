package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动排行表.xlsx", node="event_rank_rewards.xml")
public class EventRankRewardsTemplate implements IResTemplate  {

    /**
    * 排行奖励ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 排行榜id(干掉右边这个枚举)
    * int activityRankId
    * 
    */
    @ResAttribute("activityRankId")
    private  int activityRankId;

    /**
    * 活动排行榜id
    * CommonEnum.RankType activityRankType
    * 
    */
    @ResAttribute("activityRankType")
    private CommonEnum.RankType activityRankType;

    /**
    * 奖励区间
    * pair rewardRange
    * 
    */
    @ResAttribute("rewardRange")
    private IntPairType rewardRangePair;

    /**
    * 奖励id
    * pairarray reward
    * 
    */
    @ResAttribute("reward")
    private List<IntPairType> rewardPairList;

    /**
    * 自选奖励数量
    * int optionalRewardNum
    * 
    */
    @ResAttribute("optionalRewardNum")
    private  int optionalRewardNum;



    /**
    * 排行奖励ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 排行榜id(干掉右边这个枚举)
    * int activityRankId
    * 
    */
    public int getActivityRankId(){
        return activityRankId;
    }


    /**
    * 活动排行榜id
    * CommonEnum.RankType activityRankType
    * 
    */
    public CommonEnum.RankType getActivityRankType(){
        return activityRankType;
    }

    /**
    * 奖励区间
    * pair rewardRange
    * 
    */
    public IntPairType getRewardRangePair(){
        return rewardRangePair;
    }

    /**
    * 奖励id
    * pairarray reward
    * 
    */
    public List<IntPairType> getRewardPairList(){
        return rewardPairList;
    }
    /**
    * 自选奖励数量
    * int optionalRewardNum
    * 
    */
    public int getOptionalRewardNum(){
        return optionalRewardNum;
    }


}
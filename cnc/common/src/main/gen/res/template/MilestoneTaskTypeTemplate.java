package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_里程碑.xlsx", node="milestone_task_type.xml")
public class MilestoneTaskTypeTemplate implements IResTemplate  {

    /**
    * 任务typeId
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 计数方式
    * CommonEnum.MileStoneStatisticType statisticType
    * 
    */
    @ResAttribute("statisticType")
    private CommonEnum.MileStoneStatisticType statisticType;



    /**
    * 任务typeId
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 计数方式
    * CommonEnum.MileStoneStatisticType statisticType
    * 
    */
    public CommonEnum.MileStoneStatisticType getStatisticType(){
        return statisticType;
    }

}
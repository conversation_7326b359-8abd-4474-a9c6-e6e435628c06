package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_联盟配置表.xlsx", node="clan_store.xml")
public class ClanStoreTemplate implements IResTemplate  {

    /**
    * 道具id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 所属类型
    * int itemtype
    * 
    */
    @ResAttribute("itemtype")
    private  int itemtype;

    /**
    * 进货所需联盟积分
    * int clanScore
    * 
    */
    @ResAttribute("clanScore")
    private  int clanScore;

    /**
    * 购买所需个人积分
    * int price
    * 
    */
    @ResAttribute("price")
    private  int price;

    /**
    * 所需军团势力等级
    * int needPowerLv
    * 
    */
    @ResAttribute("needPowerLv")
    private  int needPowerLv;

    /**
    * 是否从进货列表中下架
    * bool remove
    * 
    */
    @ResAttribute("remove")
    private  boolean remove;



    /**
    * 道具id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 所属类型
    * int itemtype
    * 
    */
    public int getItemtype(){
        return itemtype;
    }

    /**
    * 进货所需联盟积分
    * int clanScore
    * 
    */
    public int getClanScore(){
        return clanScore;
    }

    /**
    * 购买所需个人积分
    * int price
    * 
    */
    public int getPrice(){
        return price;
    }

    /**
    * 所需军团势力等级
    * int needPowerLv
    * 
    */
    public int getNeedPowerLv(){
        return needPowerLv;
    }


    /**
    * 是否从进货列表中下架
    * bool remove
    * 
    */
    public boolean getRemove(){
        return remove;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="V_VIP.xlsx", node="vip_common.xml")
public class VipCommonTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 等级
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;

    /**
    * 经验
    * int exp
    * 
    */
    @ResAttribute("exp")
    private  int exp;

    /**
    * 加成列表
    * pairarray additions
    * 
    */
    @ResAttribute("additions")
    private List<IntPairType> additionsPairList;

    /**
    * 每日免费宝箱
    * pairarray dailyBoxItems
    * 
    */
    @ResAttribute("dailyBoxItems")
    private List<IntPairType> dailyBoxItemsPairList;

    /**
    * 尊享宝箱
    * int exclusiveBoxId
    * 
    */
    @ResAttribute("exclusiveBoxId")
    private  int exclusiveBoxId;

    /**
    * 自选英雄凭证数量
    * int optional
    * 
    */
    @ResAttribute("optional")
    private  int optional;

    /**
    * 自选英雄凭证品质
    * int quality
    * 
    */
    @ResAttribute("quality")
    private  int quality;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 等级
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }

    /**
    * 经验
    * int exp
    * 
    */
    public int getExp(){
        return exp;
    }


    /**
    * 加成列表
    * pairarray additions
    * 
    */
    public List<IntPairType> getAdditionsPairList(){
        return additionsPairList;
    }

    /**
    * 每日免费宝箱
    * pairarray dailyBoxItems
    * 
    */
    public List<IntPairType> getDailyBoxItemsPairList(){
        return dailyBoxItemsPairList;
    }
    /**
    * 尊享宝箱
    * int exclusiveBoxId
    * 
    */
    public int getExclusiveBoxId(){
        return exclusiveBoxId;
    }

    /**
    * 自选英雄凭证数量
    * int optional
    * 
    */
    public int getOptional(){
        return optional;
    }

    /**
    * 自选英雄凭证品质
    * int quality
    * 
    */
    public int getQuality(){
        return quality;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="G_国家旗帜.xlsx", node="private_flag.xml")
public class PrivateFlagTemplate implements IResTemplate  {

    /**
    * 序号
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 旗帜所属类型
    * CommonEnum.FlagType type
    * 
    */
    @ResAttribute("type")
    private CommonEnum.FlagType type;



    /**
    * 序号
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 旗帜所属类型
    * CommonEnum.FlagType type
    * 
    */
    public CommonEnum.FlagType getType(){
        return type;
    }

}
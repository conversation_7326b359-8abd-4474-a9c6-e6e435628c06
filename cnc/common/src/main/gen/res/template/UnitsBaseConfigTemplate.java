package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_单位模板【RH】.xlsx", node="units_base_config.xml")
public class UnitsBaseConfigTemplate implements IResTemplate  {

    /**
    * 模板ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 相关兵种单位
    * int RelatedUnit
    * 
    */
    @ResAttribute("RelatedUnit")
    private  int RelatedUnit;

    /**
    * 单位数量
    * int Amount
    * 
    */
    @ResAttribute("Amount")
    private  int Amount;



    /**
    * 模板ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 相关兵种单位
    * int RelatedUnit
    * 
    */
    public int getRelatedUnit(){
        return RelatedUnit;
    }

    /**
    * 单位数量
    * int Amount
    * 
    */
    public int getAmount(){
        return Amount;
    }


}
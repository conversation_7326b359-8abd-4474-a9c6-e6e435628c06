package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_招募.xlsx", node="recruit.xml")
public class RecruitTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 招募奖池类型
    * CommonEnum.RecruitPoolType recruitPoolType
    * 
    */
    @ResAttribute("recruitPoolType")
    private CommonEnum.RecruitPoolType recruitPoolType;

    /**
    * 奖池id
    * int poolId
    * 
    */
    @ResAttribute("poolId")
    private  int poolId;

    /**
    * 每日招募上限
    * int dailyRecruitLimit
    * 
    */
    @ResAttribute("dailyRecruitLimit")
    private  int dailyRecruitLimit;

    /**
    * 单次抽取消耗道具id
    * int costItemId
    * 
    */
    @ResAttribute("costItemId")
    private  int costItemId;

    /**
    * 单次抽取消耗道具数量
    * int costItemNum
    * 
    */
    @ResAttribute("costItemNum")
    private  int costItemNum;

    /**
    * 首次招募必出奖励项（多文明时期改为pair（文明->奖励项id））
    * intarray firstRecruit
    * 
    */
    @ResAttribute("firstRecruit")
    private List<Integer> firstRecruitList;

    /**
    * 单次招募获得奖励次数
    * int randomNum
    * 
    */
    @ResAttribute("randomNum")
    private  int randomNum;

    /**
    * 免费刷新cd下限，单位为s
    * int freeRefreshCd
    * 
    */
    @ResAttribute("freeRefreshCd")
    private  int freeRefreshCd;

    /**
    * 每日免费招募次数上限
    * int dailyFreeRecruit
    * 
    */
    @ResAttribute("dailyFreeRecruit")
    private  int dailyFreeRecruit;

    /**
    * 保底奖池
    * pairarray RecruitGuarantee
    * 
    */
    @ResAttribute("RecruitGuarantee")
    private List<IntPairType> RecruitGuaranteePairList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 招募奖池类型
    * CommonEnum.RecruitPoolType recruitPoolType
    * 
    */
    public CommonEnum.RecruitPoolType getRecruitPoolType(){
        return recruitPoolType;
    }
    /**
    * 奖池id
    * int poolId
    * 
    */
    public int getPoolId(){
        return poolId;
    }

    /**
    * 每日招募上限
    * int dailyRecruitLimit
    * 
    */
    public int getDailyRecruitLimit(){
        return dailyRecruitLimit;
    }

    /**
    * 单次抽取消耗道具id
    * int costItemId
    * 
    */
    public int getCostItemId(){
        return costItemId;
    }

    /**
    * 单次抽取消耗道具数量
    * int costItemNum
    * 
    */
    public int getCostItemNum(){
        return costItemNum;
    }


    /**
    * 首次招募必出奖励项（多文明时期改为pair（文明->奖励项id））
    * intarray firstRecruit
    * 
    */
    public List<Integer> getFirstRecruitList(){
        return firstRecruitList;
    }

    /**
    * 单次招募获得奖励次数
    * int randomNum
    * 
    */
    public int getRandomNum(){
        return randomNum;
    }

    /**
    * 免费刷新cd下限，单位为s
    * int freeRefreshCd
    * 
    */
    public int getFreeRefreshCd(){
        return freeRefreshCd;
    }

    /**
    * 每日免费招募次数上限
    * int dailyFreeRecruit
    * 
    */
    public int getDailyFreeRecruit(){
        return dailyFreeRecruit;
    }


    /**
    * 保底奖池
    * pairarray RecruitGuarantee
    * 
    */
    public List<IntPairType> getRecruitGuaranteePairList(){
        return RecruitGuaranteePairList;
    }

}
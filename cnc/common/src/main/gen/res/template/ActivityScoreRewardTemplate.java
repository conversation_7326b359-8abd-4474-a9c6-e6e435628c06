package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动表_策划分表.xlsx", node="activity_score_reward.xml")
public class ActivityScoreRewardTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 活动id
    * string unitId
    * 
    */
    @ResAttribute("unitId")
    private  String unitId;

    /**
    * 玩家等级区间
    * pair cityLevel
    * 
    */
    @ResAttribute("cityLevel")
    private IntPairType cityLevelPair;

    /**
    * 开服天数区间
    * pair serverOpenDay
    * 
    */
    @ResAttribute("serverOpenDay")
    private IntPairType serverOpenDayPair;

    /**
    * 积分
    * int score
    * 
    */
    @ResAttribute("score")
    private  int score;

    /**
    * 积分来源途径，关联到活动积分表
    * intarray pointsTemplateIds
    * 
    */
    @ResAttribute("pointsTemplateIds")
    private List<Integer> pointsTemplateIdsList;

    /**
    * 奖励
    * pairarray reward
    * 
    */
    @ResAttribute("reward")
    private List<IntPairType> rewardPairList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 活动id
    * string unitId
    * 
    */
    public String getUnitId(){
        return unitId;
    }

    /**
    * 玩家等级区间
    * pair cityLevel
    * 
    */
    public IntPairType getCityLevelPair(){
        return cityLevelPair;
    }

    /**
    * 开服天数区间
    * pair serverOpenDay
    * 
    */
    public IntPairType getServerOpenDayPair(){
        return serverOpenDayPair;
    }
    /**
    * 积分
    * int score
    * 
    */
    public int getScore(){
        return score;
    }


    /**
    * 积分来源途径，关联到活动积分表
    * intarray pointsTemplateIds
    * 
    */
    public List<Integer> getPointsTemplateIdsList(){
        return pointsTemplateIdsList;
    }


    /**
    * 奖励
    * pairarray reward
    * 
    */
    public List<IntPairType> getRewardPairList(){
        return rewardPairList;
    }

}
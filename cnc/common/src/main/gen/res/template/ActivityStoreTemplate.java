package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="H_活动商店表.xlsx", node="activity_store.xml")
public class ActivityStoreTemplate implements IResTemplate  {

    /**
    * 商店id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 商品id
    * intarray goodsId
    * 
    */
    @ResAttribute("goodsId")
    private List<Integer> goodsIdList;

    /**
    * 商店刷新类型
    * CommonEnum.RefreshType refreshType
    * 
    */
    @ResAttribute("refreshType")
    private CommonEnum.RefreshType refreshType;

    /**
    * 兑换次数上限
    * int changeLimit
    * 
    */
    @ResAttribute("changeLimit")
    private  int changeLimit;



    /**
    * 商店id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 商品id
    * intarray goodsId
    * 
    */
    public List<Integer> getGoodsIdList(){
        return goodsIdList;
    }


    /**
    * 商店刷新类型
    * CommonEnum.RefreshType refreshType
    * 
    */
    public CommonEnum.RefreshType getRefreshType(){
        return refreshType;
    }
    /**
    * 兑换次数上限
    * int changeLimit
    * 
    */
    public int getChangeLimit(){
        return changeLimit;
    }


}
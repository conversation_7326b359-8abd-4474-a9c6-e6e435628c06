package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_战役配置表【RH】.xlsx", node="campaign_mission_reward.xml")
public class CampaignMissionRewardTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 难度等级
    * int difficultylevel
    * 
    */
    @ResAttribute("difficultylevel")
    private  int difficultylevel;

    /**
    * 奖励档位
    * int rewardLevel
    * 
    */
    @ResAttribute("rewardLevel")
    private  int rewardLevel;

    /**
    * 保底奖励
    * pairarray guaranteedReward
    * 
    */
    @ResAttribute("guaranteedReward")
    private List<IntPairType> guaranteedRewardPairList;

    /**
    * 任务类型
    * int taskType
    * 
    */
    @ResAttribute("taskType")
    private  int taskType;

    /**
    * 任务奖励道具ID（掉落池）
    * int dropPit
    * 
    */
    @ResAttribute("dropPit")
    private  int dropPit;

    /**
    * 战斗胜利给几个额外箱子
    * int lootDropCount
    * 
    */
    @ResAttribute("lootDropCount")
    private  int lootDropCount;

    /**
    * 额外箱子掉落组ID
    * int lootGroupID
    * 
    */
    @ResAttribute("lootGroupID")
    private  int lootGroupID;

    /**
    * 升级几率%
    * int rankUPChance
    * 
    */
    @ResAttribute("rankUPChance")
    private  int rankUPChance;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 难度等级
    * int difficultylevel
    * 
    */
    public int getDifficultylevel(){
        return difficultylevel;
    }

    /**
    * 奖励档位
    * int rewardLevel
    * 
    */
    public int getRewardLevel(){
        return rewardLevel;
    }


    /**
    * 保底奖励
    * pairarray guaranteedReward
    * 
    */
    public List<IntPairType> getGuaranteedRewardPairList(){
        return guaranteedRewardPairList;
    }
    /**
    * 任务类型
    * int taskType
    * 
    */
    public int getTaskType(){
        return taskType;
    }

    /**
    * 任务奖励道具ID（掉落池）
    * int dropPit
    * 
    */
    public int getDropPit(){
        return dropPit;
    }

    /**
    * 战斗胜利给几个额外箱子
    * int lootDropCount
    * 
    */
    public int getLootDropCount(){
        return lootDropCount;
    }

    /**
    * 额外箱子掉落组ID
    * int lootGroupID
    * 
    */
    public int getLootGroupID(){
        return lootGroupID;
    }

    /**
    * 升级几率%
    * int rankUPChance
    * 
    */
    public int getRankUPChance(){
        return rankUPChance;
    }


}
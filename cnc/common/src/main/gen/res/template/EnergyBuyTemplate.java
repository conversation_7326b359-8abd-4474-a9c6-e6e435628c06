package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_指挥官.xlsx", node="energy_buy.xml")
public class EnergyBuyTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 本日购买体力次数
    * int buyTimes
    * 
    */
    @ResAttribute("buyTimes")
    private  int buyTimes;

    /**
    * 钻石档位
    * int costDiamond
    * 
    */
    @ResAttribute("costDiamond")
    private  int costDiamond;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 本日购买体力次数
    * int buyTimes
    * 
    */
    public int getBuyTimes(){
        return buyTimes;
    }

    /**
    * 钻石档位
    * int costDiamond
    * 
    */
    public int getCostDiamond(){
        return costDiamond;
    }


}
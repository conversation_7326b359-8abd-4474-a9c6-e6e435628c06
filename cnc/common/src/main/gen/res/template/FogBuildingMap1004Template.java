package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="M_迷雾建筑_map1004.xlsx", node="fog_building_map1004.xml")
public class FogBuildingMap1004Template implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 所属州
    * int regionId
    * 
    */
    @ResAttribute("regionId")
    private  int regionId;

    /**
    * 建筑配置id
    * int buildingId
    * 
    */
    @ResAttribute("buildingId")
    private  int buildingId;

    /**
    * 建筑X坐标
    * int posX
    * 
    */
    @ResAttribute("posX")
    private  int posX;

    /**
    * 建筑Y坐标
    * int posY
    * 
    */
    @ResAttribute("posY")
    private  int posY;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 所属州
    * int regionId
    * 
    */
    public int getRegionId(){
        return regionId;
    }

    /**
    * 建筑配置id
    * int buildingId
    * 
    */
    public int getBuildingId(){
        return buildingId;
    }

    /**
    * 建筑X坐标
    * int posX
    * 
    */
    public int getPosX(){
        return posX;
    }

    /**
    * 建筑Y坐标
    * int posY
    * 
    */
    public int getPosY(){
        return posY;
    }


}
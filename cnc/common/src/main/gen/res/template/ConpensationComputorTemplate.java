package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="X_系统补偿配置.xlsx", node="conpensation_computor.xml")
public class ConpensationComputorTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 主城等级
    * int cityLevel
    * 
    */
    @ResAttribute("cityLevel")
    private  int cityLevel;

    /**
    * 兵种等级
    * int soildierLevel
    * 
    */
    @ResAttribute("soildierLevel")
    private  int soildierLevel;

    /**
    * 补偿资源：万分比
    * int resource
    * 
    */
    @ResAttribute("resource")
    private  int resource;

    /**
    * 治疗加速补偿：万分比
    * int healTime
    * 
    */
    @ResAttribute("healTime")
    private  int healTime;

    /**
    * 训练加速补偿：万分比
    * int trainingTime
    * 
    */
    @ResAttribute("trainingTime")
    private  int trainingTime;

    /**
    * 本周补偿士兵数量上限
    * int soildierMaxEveryWeek
    * 
    */
    @ResAttribute("soildierMaxEveryWeek")
    private  int soildierMaxEveryWeek;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 主城等级
    * int cityLevel
    * 
    */
    public int getCityLevel(){
        return cityLevel;
    }

    /**
    * 兵种等级
    * int soildierLevel
    * 
    */
    public int getSoildierLevel(){
        return soildierLevel;
    }

    /**
    * 补偿资源：万分比
    * int resource
    * 
    */
    public int getResource(){
        return resource;
    }

    /**
    * 治疗加速补偿：万分比
    * int healTime
    * 
    */
    public int getHealTime(){
        return healTime;
    }

    /**
    * 训练加速补偿：万分比
    * int trainingTime
    * 
    */
    public int getTrainingTime(){
        return trainingTime;
    }

    /**
    * 本周补偿士兵数量上限
    * int soildierMaxEveryWeek
    * 
    */
    public int getSoildierMaxEveryWeek(){
        return soildierMaxEveryWeek;
    }


}
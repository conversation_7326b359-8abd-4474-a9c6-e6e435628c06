package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_技能表_效果.xlsx", node="skill_effect.xml")
public class SkillEffectTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 技能效果
    * CommonEnum.SkillEffectType type
    * 
    */
    @ResAttribute("type")
    private CommonEnum.SkillEffectType type;

    /**
    * 对应参数1
    * int value1
    * 
    */
    @ResAttribute("value1")
    private  int value1;

    /**
    * 对应参数2
    * int value2
    * 
    */
    @ResAttribute("value2")
    private  int value2;

    /**
    * 对应参数3
    * int value3
    * 
    */
    @ResAttribute("value3")
    private  int value3;

    /**
    * 对应参数4
    * int value4
    * 
    */
    @ResAttribute("value4")
    private  int value4;

    /**
    * 维持战斗关系
    * int keepBattle
    * 
    */
    @ResAttribute("keepBattle")
    private  int keepBattle;

    /**
    * 触发条件
    * CommonEnum.TriggerType trigger
    * 
    */
    @ResAttribute("trigger")
    private CommonEnum.TriggerType trigger;

    /**
    * 触发概率
    * int triggerValue
    * 
    */
    @ResAttribute("triggerValue")
    private  int triggerValue;

    /**
    * 自身状态1
    * CommonEnum.StatusType selfTroopsState1
    * 
    */
    @ResAttribute("selfTroopsState1")
    private CommonEnum.StatusType selfTroopsState1;

    /**
    * 自身状态值1
    * string selfTroopsStateValue1
    * 
    */
    @ResAttribute("selfTroopsStateValue1")
    private  String selfTroopsStateValue1;

    /**
    * 自身状态2
    * CommonEnum.StatusType selfTroopsState2
    * 
    */
    @ResAttribute("selfTroopsState2")
    private CommonEnum.StatusType selfTroopsState2;

    /**
    * 自身状态值2
    * string selfTroopsStateValue2
    * 
    */
    @ResAttribute("selfTroopsStateValue2")
    private  String selfTroopsStateValue2;

    /**
    * 技能目标点
    * CommonEnum.TargetType targetType
    * 
    */
    @ResAttribute("targetType")
    private CommonEnum.TargetType targetType;

    /**
    * 目标阵营类型
    * CommonEnum.EnemyRelation enemyRelation
    * 
    */
    @ResAttribute("enemyRelation")
    private CommonEnum.EnemyRelation enemyRelation;

    /**
    * 必中类型
    * intarray mustType
    * 
    */
    @ResAttribute("mustType")
    private List<Integer> mustTypeList;

    /**
    * 目标类型
    * intarray enemyType
    * 
    */
    @ResAttribute("enemyType")
    private List<Integer> enemyTypeList;

    /**
    * 目标状态
    * CommonEnum.StatusType troopsState
    * 
    */
    @ResAttribute("troopsState")
    private CommonEnum.StatusType troopsState;

    /**
    * 目标状态值
    * string troopsStateValue
    * 
    */
    @ResAttribute("troopsStateValue")
    private  String troopsStateValue;

    /**
    * 形状范围
    * int range
    * 
    */
    @ResAttribute("range")
    private  int range;

    /**
    * CD
    * int cd
    * 
    */
    @ResAttribute("cd")
    private  int cd;

    /**
    * 独立概率BUFF组
    * pairarray groupBuff1
    * 
    */
    @ResAttribute("groupBuff1")
    private List<IntPairType> groupBuff1PairList;

    /**
    * 共同概率BUFF组
    * pairarray groupBuff2
    * 
    */
    @ResAttribute("groupBuff2")
    private List<IntPairType> groupBuff2PairList;

    /**
    * 增益BUFF组(万分比）
    * pairarray additionGroup
    * 
    */
    @ResAttribute("additionGroup")
    private List<IntPairType> additionGroupPairList;

    /**
    * 技能效果表现
    * int timelineID
    * 
    */
    @ResAttribute("timelineID")
    private  int timelineID;

    /**
    * 战斗协议
    * bool CombatAgreement
    * 
    */
    @ResAttribute("CombatAgreement")
    private  boolean CombatAgreement;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 技能效果
    * CommonEnum.SkillEffectType type
    * 
    */
    public CommonEnum.SkillEffectType getType(){
        return type;
    }
    /**
    * 对应参数1
    * int value1
    * 
    */
    public int getValue1(){
        return value1;
    }

    /**
    * 对应参数2
    * int value2
    * 
    */
    public int getValue2(){
        return value2;
    }

    /**
    * 对应参数3
    * int value3
    * 
    */
    public int getValue3(){
        return value3;
    }

    /**
    * 对应参数4
    * int value4
    * 
    */
    public int getValue4(){
        return value4;
    }

    /**
    * 维持战斗关系
    * int keepBattle
    * 
    */
    public int getKeepBattle(){
        return keepBattle;
    }


    /**
    * 触发条件
    * CommonEnum.TriggerType trigger
    * 
    */
    public CommonEnum.TriggerType getTrigger(){
        return trigger;
    }
    /**
    * 触发概率
    * int triggerValue
    * 
    */
    public int getTriggerValue(){
        return triggerValue;
    }


    /**
    * 自身状态1
    * CommonEnum.StatusType selfTroopsState1
    * 
    */
    public CommonEnum.StatusType getSelfTroopsState1(){
        return selfTroopsState1;
    }
    /**
    * 自身状态值1
    * string selfTroopsStateValue1
    * 
    */
    public String getSelfTroopsStateValue1(){
        return selfTroopsStateValue1;
    }

    /**
    * 自身状态2
    * CommonEnum.StatusType selfTroopsState2
    * 
    */
    public CommonEnum.StatusType getSelfTroopsState2(){
        return selfTroopsState2;
    }
    /**
    * 自身状态值2
    * string selfTroopsStateValue2
    * 
    */
    public String getSelfTroopsStateValue2(){
        return selfTroopsStateValue2;
    }

    /**
    * 技能目标点
    * CommonEnum.TargetType targetType
    * 
    */
    public CommonEnum.TargetType getTargetType(){
        return targetType;
    }

    /**
    * 目标阵营类型
    * CommonEnum.EnemyRelation enemyRelation
    * 
    */
    public CommonEnum.EnemyRelation getEnemyRelation(){
        return enemyRelation;
    }

    /**
    * 必中类型
    * intarray mustType
    * 
    */
    public List<Integer> getMustTypeList(){
        return mustTypeList;
    }


    /**
    * 目标类型
    * intarray enemyType
    * 
    */
    public List<Integer> getEnemyTypeList(){
        return enemyTypeList;
    }


    /**
    * 目标状态
    * CommonEnum.StatusType troopsState
    * 
    */
    public CommonEnum.StatusType getTroopsState(){
        return troopsState;
    }
    /**
    * 目标状态值
    * string troopsStateValue
    * 
    */
    public String getTroopsStateValue(){
        return troopsStateValue;
    }
    /**
    * 形状范围
    * int range
    * 
    */
    public int getRange(){
        return range;
    }

    /**
    * CD
    * int cd
    * 
    */
    public int getCd(){
        return cd;
    }


    /**
    * 独立概率BUFF组
    * pairarray groupBuff1
    * 
    */
    public List<IntPairType> getGroupBuff1PairList(){
        return groupBuff1PairList;
    }

    /**
    * 共同概率BUFF组
    * pairarray groupBuff2
    * 
    */
    public List<IntPairType> getGroupBuff2PairList(){
        return groupBuff2PairList;
    }

    /**
    * 增益BUFF组(万分比）
    * pairarray additionGroup
    * 
    */
    public List<IntPairType> getAdditionGroupPairList(){
        return additionGroupPairList;
    }
    /**
    * 技能效果表现
    * int timelineID
    * 
    */
    public int getTimelineID(){
        return timelineID;
    }


    /**
    * 战斗协议
    * bool CombatAgreement
    * 
    */
    public boolean getCombatAgreement(){
        return CombatAgreement;
    }

}
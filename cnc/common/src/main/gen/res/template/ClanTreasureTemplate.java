package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_联盟配置表.xlsx", node="clan_treasure.xml")
public class ClanTreasureTemplate implements IResTemplate  {

    /**
    * 珍藏礼物等级
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 开启礼物所需钥匙
    * int key
    * 
    */
    @ResAttribute("key")
    private  int key;

    /**
    * 奖励id（关联reward表）
    * int rewardId
    * 
    */
    @ResAttribute("rewardId")
    private  int rewardId;



    /**
    * 珍藏礼物等级
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 开启礼物所需钥匙
    * int key
    * 
    */
    public int getKey(){
        return key;
    }

    /**
    * 奖励id（关联reward表）
    * int rewardId
    * 
    */
    public int getRewardId(){
        return rewardId;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="T_天网.xlsx", node="skynet_task.xml")
public class SkynetTaskTemplate implements IResTemplate  {

    /**
    * 任务id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 任务类型
    * CommonEnum.SkynetTaskType taskType
    * 
    */
    @ResAttribute("taskType")
    private CommonEnum.SkynetTaskType taskType;

    /**
    * 任务参数
    * int param
    * 
    */
    @ResAttribute("param")
    private  int param;

    /**
    * 天网等级
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;

    /**
    * 品质
    * int quality
    * 
    */
    @ResAttribute("quality")
    private  int quality;

    /**
    * 情报任务奖励
    * int rewardID
    * 
    */
    @ResAttribute("rewardID")
    private  int rewardID;

    /**
    * 任务完成奖励
    * pairarray extraReward
    * 
    */
    @ResAttribute("extraReward")
    private List<IntPairType> extraRewardPairList;

    /**
    * 邮件id
    * int mailID
    * 
    */
    @ResAttribute("mailID")
    private  int mailID;

    /**
    * 任务时效（秒）
    * int time
    * 
    */
    @ResAttribute("time")
    private  int time;

    /**
    * 体力消耗
    * int needPower
    * 
    */
    @ResAttribute("needPower")
    private  int needPower;



    /**
    * 任务id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 任务类型
    * CommonEnum.SkynetTaskType taskType
    * 
    */
    public CommonEnum.SkynetTaskType getTaskType(){
        return taskType;
    }
    /**
    * 任务参数
    * int param
    * 
    */
    public int getParam(){
        return param;
    }

    /**
    * 天网等级
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }

    /**
    * 品质
    * int quality
    * 
    */
    public int getQuality(){
        return quality;
    }

    /**
    * 情报任务奖励
    * int rewardID
    * 
    */
    public int getRewardID(){
        return rewardID;
    }


    /**
    * 任务完成奖励
    * pairarray extraReward
    * 
    */
    public List<IntPairType> getExtraRewardPairList(){
        return extraRewardPairList;
    }
    /**
    * 邮件id
    * int mailID
    * 
    */
    public int getMailID(){
        return mailID;
    }

    /**
    * 任务时效（秒）
    * int time
    * 
    */
    public int getTime(){
        return time;
    }

    /**
    * 体力消耗
    * int needPower
    * 
    */
    public int getNeedPower(){
        return needPower;
    }


}
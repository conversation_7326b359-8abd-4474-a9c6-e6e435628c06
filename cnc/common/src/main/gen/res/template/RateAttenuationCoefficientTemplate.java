package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="J_节日活动.xlsx", node="rate_attenuation_coefficient.xml")
public class RateAttenuationCoefficientTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 衰减次数阈值
    * int threshold
    * 
    */
    @ResAttribute("threshold")
    private  int threshold;

    /**
    * 爆率衰减系数
    * int coefficient
    * 
    */
    @ResAttribute("coefficient")
    private  int coefficient;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 衰减次数阈值
    * int threshold
    * 
    */
    public int getThreshold(){
        return threshold;
    }

    /**
    * 爆率衰减系数
    * int coefficient
    * 
    */
    public int getCoefficient(){
        return coefficient;
    }


}
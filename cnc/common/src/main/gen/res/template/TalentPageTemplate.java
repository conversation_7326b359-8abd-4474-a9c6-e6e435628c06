package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_英雄表.xlsx", node="talent_page.xml")
public class TalentPageTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 天赋页数
    * int pageNum
    * 
    */
    @ResAttribute("pageNum")
    private  int pageNum;

    /**
    * 默认名称
    * string name
    * 
    */
    @ResAttribute("name")
    private  String name;

    /**
    * 重置道具
    * pair resetCost
    * 
    */
    @ResAttribute("resetCost")
    private IntPairType resetCostPair;

    /**
    * 切换消耗
    * pair switchCost
    * 
    */
    @ResAttribute("switchCost")
    private IntPairType switchCostPair;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 天赋页数
    * int pageNum
    * 
    */
    public int getPageNum(){
        return pageNum;
    }

    /**
    * 默认名称
    * string name
    * 
    */
    public String getName(){
        return name;
    }

    /**
    * 重置道具
    * pair resetCost
    * 
    */
    public IntPairType getResetCostPair(){
        return resetCostPair;
    }

    /**
    * 切换消耗
    * pair switchCost
    * 
    */
    public IntPairType getSwitchCostPair(){
        return switchCostPair;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_联盟配置表.xlsx", node="clan_recycle.xml")
public class ClanRecycleTemplate implements IResTemplate  {

    /**
    * 道具id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 回收获得个人积分
    * int score
    * 
    */
    @ResAttribute("score")
    private  int score;



    /**
    * 道具id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 回收获得个人积分
    * int score
    * 
    */
    public int getScore(){
        return score;
    }


}
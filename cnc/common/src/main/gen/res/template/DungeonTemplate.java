package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="F_副本.xlsx", node="dungeon.xml")
public class DungeonTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 地图
    * int mapId
    * 
    */
    @ResAttribute("mapId")
    private  int mapId;

    /**
    * 副本类型
    * int type
    * 
    */
    @ResAttribute("type")
    private  int type;

    /**
    * 主城坐标
    * pair mainCity
    * 
    */
    @ResAttribute("mainCity")
    private IntPairType mainCityPair;

    /**
    * 红队主城坐标
    * pairarray redTeamMainCityPos
    * 
    */
    @ResAttribute("redTeamMainCityPos")
    private List<IntPairType> redTeamMainCityPosPairList;

    /**
    * 蓝队主城坐标
    * pairarray blueTeamMainCityPos
    * 
    */
    @ResAttribute("blueTeamMainCityPos")
    private List<IntPairType> blueTeamMainCityPosPairList;

    /**
    * 地图信息表
    * string mapInfo
    * 
    */
    @ResAttribute("mapInfo")
    private  String mapInfo;

    /**
    * 副本目标
    * int goalGroup
    * 
    */
    @ResAttribute("goalGroup")
    private  int goalGroup;

    /**
    * 通关目标
    * intarray successGoal
    * 
    */
    @ResAttribute("successGoal")
    private List<Integer> successGoalList;

    /**
    * 副本持续时长
    * int dungeonLastTime
    * 
    */
    @ResAttribute("dungeonLastTime")
    private  int dungeonLastTime;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 地图
    * int mapId
    * 
    */
    public int getMapId(){
        return mapId;
    }

    /**
    * 副本类型
    * int type
    * 
    */
    public int getType(){
        return type;
    }


    /**
    * 主城坐标
    * pair mainCity
    * 
    */
    public IntPairType getMainCityPair(){
        return mainCityPair;
    }

    /**
    * 红队主城坐标
    * pairarray redTeamMainCityPos
    * 
    */
    public List<IntPairType> getRedTeamMainCityPosPairList(){
        return redTeamMainCityPosPairList;
    }

    /**
    * 蓝队主城坐标
    * pairarray blueTeamMainCityPos
    * 
    */
    public List<IntPairType> getBlueTeamMainCityPosPairList(){
        return blueTeamMainCityPosPairList;
    }
    /**
    * 地图信息表
    * string mapInfo
    * 
    */
    public String getMapInfo(){
        return mapInfo;
    }
    /**
    * 副本目标
    * int goalGroup
    * 
    */
    public int getGoalGroup(){
        return goalGroup;
    }


    /**
    * 通关目标
    * intarray successGoal
    * 
    */
    public List<Integer> getSuccessGoalList(){
        return successGoalList;
    }

    /**
    * 副本持续时长
    * int dungeonLastTime
    * 
    */
    public int getDungeonLastTime(){
        return dungeonLastTime;
    }


}
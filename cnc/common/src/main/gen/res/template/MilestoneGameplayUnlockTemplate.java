package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_里程碑.xlsx", node="milestone_gameplay_unlock.xml")
public class MilestoneGameplayUnlockTemplate implements IResTemplate  {

    /**
    * 玩法类型
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 普通解锁或特殊解锁生效时间
    * CommonEnum.MileStoneFuncUnlockTimeType unlockTimeType
    * 
    */
    @ResAttribute("unlockTimeType")
    private CommonEnum.MileStoneFuncUnlockTimeType unlockTimeType;

    /**
    * 玩法解锁类型
    * CommonEnum.MileStoneFuncUnlockType unlockGameplayType
    * 
    */
    @ResAttribute("unlockGameplayType")
    private CommonEnum.MileStoneFuncUnlockType unlockGameplayType;

    /**
    * 玩法解锁类型对应参数
（用_来分割类型；用，来分割同一个类型下的多个枚举）
    * string unlockGameplayConst
    * 
    */
    @ResAttribute("unlockGameplayConst")
    private  String unlockGameplayConst;



    /**
    * 玩法类型
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 普通解锁或特殊解锁生效时间
    * CommonEnum.MileStoneFuncUnlockTimeType unlockTimeType
    * 
    */
    public CommonEnum.MileStoneFuncUnlockTimeType getUnlockTimeType(){
        return unlockTimeType;
    }

    /**
    * 玩法解锁类型
    * CommonEnum.MileStoneFuncUnlockType unlockGameplayType
    * 
    */
    public CommonEnum.MileStoneFuncUnlockType getUnlockGameplayType(){
        return unlockGameplayType;
    }
    /**
    * 玩法解锁类型对应参数
（用_来分割类型；用，来分割同一个类型下的多个枚举）
    * string unlockGameplayConst
    * 
    */
    public String getUnlockGameplayConst(){
        return unlockGameplayConst;
    }

}
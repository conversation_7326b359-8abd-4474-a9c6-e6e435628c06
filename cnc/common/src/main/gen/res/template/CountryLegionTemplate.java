package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="G_国家阵营表.xlsx", node="country_legion.xml")
public class CountryLegionTemplate implements IResTemplate  {

    /**
    * 国家序号
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 所属的阵营
1：盟军，2：联军
    * int campType
    * 
    */
    @ResAttribute("campType")
    private  int campType;

    /**
    * 国家特色兵种类型
1：步兵，2：坦克，3：飞机
    * int specialSoldierType
    * 
    */
    @ResAttribute("specialSoldierType")
    private  int specialSoldierType;

    /**
    * 国家选择界面的国家Buff的实际idList
    * string buffIdList
    * 
    */
    @ResAttribute("buffIdList")
    private  String buffIdList;



    /**
    * 国家序号
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 所属的阵营
1：盟军，2：联军
    * int campType
    * 
    */
    public int getCampType(){
        return campType;
    }

    /**
    * 国家特色兵种类型
1：步兵，2：坦克，3：飞机
    * int specialSoldierType
    * 
    */
    public int getSpecialSoldierType(){
        return specialSoldierType;
    }

    /**
    * 国家选择界面的国家Buff的实际idList
    * string buffIdList
    * 
    */
    public String getBuffIdList(){
        return buffIdList;
    }

}
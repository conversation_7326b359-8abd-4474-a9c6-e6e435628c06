package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_资源田.xlsx", node="resource.xml")
public class ResourceTemplate implements IResTemplate  {

    /**
    * ID序号
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 资源种类
    * CommonEnum.CurrencyType resType
    * 
    */
    @ResAttribute("resType")
    private CommonEnum.CurrencyType resType;

    /**
    * 剧本id
    * int storyId
    * 
    */
    @ResAttribute("storyId")
    private  int storyId;

    /**
    * 资源等级
    * int resLevel
    * 
    */
    @ResAttribute("resLevel")
    private  int resLevel;

    /**
    * 初始采集速度
    * float resSpeed
    * 
    */

    @ResAttribute("resSpeed")
    private  float resSpeed;
    /**
    * 默认带有资源量
    * int resNum
    * 
    */
    @ResAttribute("resNum")
    private  int resNum;

    /**
    * 模型ID
    * int resID
    * 
    */
    @ResAttribute("resID")
    private  int resID;



    /**
    * ID序号
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 资源种类
    * CommonEnum.CurrencyType resType
    * 
    */
    public CommonEnum.CurrencyType getResType(){
        return resType;
    }
    /**
    * 剧本id
    * int storyId
    * 
    */
    public int getStoryId(){
        return storyId;
    }

    /**
    * 资源等级
    * int resLevel
    * 
    */
    public int getResLevel(){
        return resLevel;
    }

    /**
    * 初始采集速度
    * float resSpeed
    * 
    */
    public float getResSpeed(){
        return resSpeed;
    }

    /**
    * 默认带有资源量
    * int resNum
    * 
    */
    public int getResNum(){
        return resNum;
    }

    /**
    * 模型ID
    * int resID
    * 
    */
    public int getResID(){
        return resID;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_城池导量表.xlsx", node="born_area_select.xml")
public class BornAreaSelectTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 区域等级
    * int areaLevel
    * 
    */
    @ResAttribute("areaLevel")
    private  int areaLevel;

    /**
    * 区域类型
    * CommonEnum.MapAreaType areaType
    * 
    */
    @ResAttribute("areaType")
    private CommonEnum.MapAreaType areaType;

    /**
    * 优先级
    * int priority
    * 
    */
    @ResAttribute("priority")
    private  int priority;

    /**
    * 导量上限
    * int limit
    * 
    */
    @ResAttribute("limit")
    private  int limit;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 区域等级
    * int areaLevel
    * 
    */
    public int getAreaLevel(){
        return areaLevel;
    }


    /**
    * 区域类型
    * CommonEnum.MapAreaType areaType
    * 
    */
    public CommonEnum.MapAreaType getAreaType(){
        return areaType;
    }
    /**
    * 优先级
    * int priority
    * 
    */
    public int getPriority(){
        return priority;
    }

    /**
    * 导量上限
    * int limit
    * 
    */
    public int getLimit(){
        return limit;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="N_内城地图_RH.xlsx", node="PVE_mission_RH.xml")
public class PveMissionRhTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 前置关卡
    * intarray prevMission
    * 
    */
    @ResAttribute("prevMission")
    private List<Integer> prevMissionList;

    /**
    * 关卡类型
    * CommonEnum.MissionType missionType
    * 
    */
    @ResAttribute("missionType")
    private CommonEnum.MissionType missionType;

    /**
    * 主线关卡
    * int mainMission
    * 
    */
    @ResAttribute("mainMission")
    private  int mainMission;

    /**
    * 地图坐标
    * pair missionLocation
    * 
    */
    @ResAttribute("missionLocation")
    private IntPairType missionLocationPair;

    /**
    * 敌军配置
    * triplearray battleEnemy
    * 
    */
    @ResAttribute("battleEnemy")
    private List<IntTripleType> battleEnemyTripleList;

    /**
    * 战斗奖励
    * intarray battleReward
    * 
    */
    @ResAttribute("battleReward")
    private List<Integer> battleRewardList;

    /**
    * 宝箱配置
    * intarray cratesReward
    * 
    */
    @ResAttribute("cratesReward")
    private List<Integer> cratesRewardList;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 前置关卡
    * intarray prevMission
    * 
    */
    public List<Integer> getPrevMissionList(){
        return prevMissionList;
    }


    /**
    * 关卡类型
    * CommonEnum.MissionType missionType
    * 
    */
    public CommonEnum.MissionType getMissionType(){
        return missionType;
    }
    /**
    * 主线关卡
    * int mainMission
    * 
    */
    public int getMainMission(){
        return mainMission;
    }


    /**
    * 地图坐标
    * pair missionLocation
    * 
    */
    public IntPairType getMissionLocationPair(){
        return missionLocationPair;
    }

    /**
    * 敌军配置
    * triplearray battleEnemy
    * 
    */
    public List<IntTripleType> getBattleEnemyTripleList(){
        return battleEnemyTripleList;
    }       

    /**
    * 战斗奖励
    * intarray battleReward
    * 
    */
    public List<Integer> getBattleRewardList(){
        return battleRewardList;
    }


    /**
    * 宝箱配置
    * intarray cratesReward
    * 
    */
    public List<Integer> getCratesRewardList(){
        return cratesRewardList;
    }


}
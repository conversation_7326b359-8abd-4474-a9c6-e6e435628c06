package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "N_内城地图_RH.xlsx", node = "const_innermap_RH.xml", isConst = true)
public class ConstInnermapRhTemplate implements IResConstTemplate  {

    /**
     * 资源贫瘠时在储量标准值上调整值（百分比）
     */
    private int resourcesPoor = 0;
    /**
     * 资源良好时在储量标准值上调整值（百分比）
     */
    private int resourcesGood = 0;
    /**
     * 资源丰富时在储量标准值上调整值（百分比）
     */
    private int resourcesRich = 0;
    /**
     * 玩家初始内城资源采集速度（单位：小时）
     */
    private List<IntPairType> initialResourcesProductivity;
    /**
     * 玩家初始内城资源采集上限
     */
    private List<IntPairType> resourcesStorage;
    /**
     * 玩家内城资源收取最小间隔时间（单位：秒）
     */
    private int resourcesCollectGap = 0;
    /**
     * 玩家新手内城建筑位置
     */
    private List<IntTripleType> initialInnerBuild;
    /**
     * 玩家离线采集资源最长时间上限（单位：秒）
     */
    private int offlineResourcestTimeLimit = 0;
    /**
     * 初始内城迷雾视野范围
     */
    private IntPairType initialMistUnlockRange;
    /**
     * 关卡完成后打开视野范围
     */
    private IntPairType missionMistUnlockRange;
    /**
     * 新手pve关卡，不判定编队
     */
    private List<Integer> newbieMissionId;


    public int getResourcesPoor(){
        return this.resourcesPoor;
    }

    public int getResourcesGood(){
        return this.resourcesGood;
    }

    public int getResourcesRich(){
        return this.resourcesRich;
    }

    public List<IntPairType> getInitialResourcesProductivity(){
        return this.initialResourcesProductivity;
    }

    public List<IntPairType> getResourcesStorage(){
        return this.resourcesStorage;
    }

    public int getResourcesCollectGap(){
        return this.resourcesCollectGap;
    }

    public List<IntTripleType> getInitialInnerBuild(){
        return this.initialInnerBuild;
    }

    public int getOfflineResourcestTimeLimit(){
        return this.offlineResourcestTimeLimit;
    }

    public IntPairType getInitialMistUnlockRange(){
        return this.initialMistUnlockRange;
    }

    public IntPairType getMissionMistUnlockRange(){
        return this.missionMistUnlockRange;
    }

    public List<Integer> getNewbieMissionId(){
        return this.newbieMissionId;
    }

    @Override
    public int getId() {
        return 0;
    }
}

package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_联盟配置表.xlsx", node="clan_gift_lv.xml")
public class ClanGiftLvTemplate implements IResTemplate  {

    /**
    * 礼物等级
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 升级需要礼物点数
    * int upgradePoint
    * 
    */
    @ResAttribute("upgradePoint")
    private  int upgradePoint;

    /**
    * 对应珍藏礼物id
    * int treasureId
    * 
    */
    @ResAttribute("treasureId")
    private  int treasureId;



    /**
    * 礼物等级
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 升级需要礼物点数
    * int upgradePoint
    * 
    */
    public int getUpgradePoint(){
        return upgradePoint;
    }

    /**
    * 对应珍藏礼物id
    * int treasureId
    * 
    */
    public int getTreasureId(){
        return treasureId;
    }


}
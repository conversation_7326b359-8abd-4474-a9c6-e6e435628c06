package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="N_内城地图_RH.xlsx", node="mist_unlock_RH.xml")
public class MistUnlockRhTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 迷雾解锁范围长宽
    * pair unlockRange
    * 
    */
    @ResAttribute("unlockRange")
    private IntPairType unlockRangePair;

    /**
    * 迷雾解锁雷达塔等级
    * int unlockCondition
    * 
    */
    @ResAttribute("unlockCondition")
    private  int unlockCondition;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 迷雾解锁范围长宽
    * pair unlockRange
    * 
    */
    public IntPairType getUnlockRangePair(){
        return unlockRangePair;
    }
    /**
    * 迷雾解锁雷达塔等级
    * int unlockCondition
    * 
    */
    public int getUnlockCondition(){
        return unlockCondition;
    }


}
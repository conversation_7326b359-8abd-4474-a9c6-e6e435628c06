package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="X_系统补偿配置.xlsx", node="conpensation_baselevel.xml")
public class ConpensationBaselevelTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 主城等级
    * int cityLevel
    * 
    */
    @ResAttribute("cityLevel")
    private  int cityLevel;

    /**
    * 战损损失累计触发线
    * int battleLose
    * 
    */
    @ResAttribute("battleLose")
    private  int battleLose;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 主城等级
    * int cityLevel
    * 
    */
    public int getCityLevel(){
        return cityLevel;
    }

    /**
    * 战损损失累计触发线
    * int battleLose
    * 
    */
    public int getBattleLose(){
        return battleLose;
    }


}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_英雄表.xlsx", node="hero_skill_consume.xml")
public class HeroSkillConsumeTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 技能升级次数
    * int upgradeSkillTimes
    * 
    */
    @ResAttribute("upgradeSkillTimes")
    private  int upgradeSkillTimes;

    /**
    * 蓝色升级消耗
    * pairarray consumeNum
    * 
    */
    @ResAttribute("consumeNum")
    private List<IntPairType> consumeNumPairList;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 技能升级次数
    * int upgradeSkillTimes
    * 
    */
    public int getUpgradeSkillTimes(){
        return upgradeSkillTimes;
    }


    /**
    * 蓝色升级消耗
    * pairarray consumeNum
    * 
    */
    public List<IntPairType> getConsumeNumPairList(){
        return consumeNumPairList;
    }

}
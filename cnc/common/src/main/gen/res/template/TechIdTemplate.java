package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="K_科技.xlsx", node="tech_id.xml")
public class TechIdTemplate implements IResTemplate  {

    /**
    * 科技ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 类型ID
    * int techType
    * 
    */
    @ResAttribute("techType")
    private  int techType;



    /**
    * 科技ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 类型ID
    * int techType
    * 
    */
    public int getTechType(){
        return techType;
    }


}
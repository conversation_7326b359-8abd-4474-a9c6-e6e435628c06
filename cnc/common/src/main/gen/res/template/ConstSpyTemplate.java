package res.template;

import java.util.*;
import com.yorha.common.resource.IResConstTemplate;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel = "Z_侦察.xlsx", node = "const_spy.xml", isConst = true)
public class ConstSpyTemplate implements IResConstTemplate  {

    /**
     * 侦查方邮件：基地侦查失败，被侦察方基地开保护盾
     */
    private int MailBaseSpyFailShield = 0;
    /**
     * 被侦查方邮件：基地被侦查失败，被侦察方基地开保护盾
     */
    private int MailBaseBeSpiedFailShield = 0;
    /**
     * 侦查方邮件：侦查单人部队成功
     */
    private int MailArmySpySuccess = 0;
    /**
     * 侦查方邮件：侦查玩家基地成功
     */
    private int MailBaseSpySuccess = 0;
    /**
     * 侦查方邮件：侦查集结部队成功
     */
    private int MailGrouparmySpySuccess = 0;
    /**
     * 侦查方邮件：侦查联盟建筑成功
     */
    private int MailBuildingSpySuccess = 0;
    /**
     * 被侦查方邮件：单人部队被侦查成功
     */
    private int MailArmyBeSpiedSuccess = 0;
    /**
     * 被侦查方邮件：基地被侦查成功
     */
    private int MailBaseBeSpiedSuccess = 0;
    /**
     * 被侦查方邮件：增援的友方基地被侦查成功
     */
    private int MailHelpBaseBeSpiedSuccess = 0;
    /**
     * 被侦查方邮件：参与的集结部队被侦查成功
     */
    private int MailGrouparmyBeSpiedSuccess = 0;
    /**
     * 被侦查方邮件：增援的大地图建筑/联盟建筑被侦查成功
     */
    private int MailBuildingBeSpiedSuccess = 0;
    /**
     * 侦查方邮件：侦查玩家基地失败（基地位移）
     */
    private int MailBaseSpyFailLost = 0;
    /**
     * 侦查方邮件：侦查玩家部队失败（部队丢失）
     */
    private int MailArmySpyFailLost = 0;
    /**
     * 侦查方邮件：侦查集结部队失败（部队丢失）
     */
    private int MailGrouparmySpyFailLost = 0;
    /**
     * 侦查方邮件：侦查大地图建筑失败（保护中）
     */
    private int MailBuildingSpyFailShield = 0;
    /**
     * 侦查方邮件：侦查基地、部队失败（主堡等级差>10）
     */
    private int MailSpyFailLevelLow = 0;
    /**
     * 侦查方邮件：侦查基地、部队失败（对方进入我方联盟）
     */
    private int MailSpyFailSameLegion = 0;
    /**
     * 侦查方邮件：反侦察战报
     */
    private int MailSpyFailDespy = 0;
    /**
     * 被侦查方邮件：单人部队被侦查成功（等级差较大）
     */
    private int MailArmyBeSpiedSuccessLevel = 0;
    /**
     * 被侦查方邮件：基地被侦查成功（等级差较大）
     */
    private int MailBaseBeSpiedSuccessLevel = 0;
    /**
     * 被侦查方邮件：基地被侦查成功（基地开启反侦察）
     */
    private int MailBaseBeSpiedSuccessDespy = 0;
    /**
     * 限制侦查主堡等级差
     */
    private int SpyMaxBaseLevelDifference = 0;
    /**
     * 被等级较高的人侦查提示的等级差
     */
    private int BeSpiedMaxBaseLevelDifference = 0;
    /**
     * 一次可探索迷雾格子上限
     */
    private int MaxFogExplore = 0;
    /**
     * 迷雾探索完毕提示多语言key
     */
    private String FogExploreFinishTips = "";     
    /**
     * 被侦查邮件模板邮件
     */
    private List<Integer> SpyMailTriggerType_SpyDefend;
    /**
     * 侦查失败邮件模板邮件
     */
    private List<Integer> SpyMailTriggerType_SpyFailed;
    /**
     * 侦查单人部队成功模板邮件
     */
    private List<Integer> SpyMailTriggerType_PlayerArmy;
    /**
     * 侦查基地成功模板邮件
     */
    private List<Integer> SpyMailTriggerType_City;
    /**
     * 侦查集结部队成功模板邮件
     */
    private List<Integer> SpyMailTriggerType_RallyArmy;
    /**
     * 侦查大地图、联盟建筑成功模板邮件
     */
    private List<Integer> SpyMailTriggerType_Building;
    /**
     * 侦察机模型半径（CM）
     */
    private int SpyPlaneRadius = 0;
    /**
     * 侦察兵力值模糊下限
     */
    private int SpyFuzzyMin = 0;
    /**
     * 侦察兵力值模糊上限
     */
    private int SpyFuzzyMax = 0;
    /**
     * 起飞动画时长
     */
    private int AniTakeOffMs = 0;
    /**
     * 降落动画时长
     */
    private int AniLandingMs = 0;
    /**
     * gvg侦察建筑成功邮件
     */
    private int GvgScoutBuildingSuccess = 0;


    public int getMailBaseSpyFailShield(){
        return this.MailBaseSpyFailShield;
    }

    public int getMailBaseBeSpiedFailShield(){
        return this.MailBaseBeSpiedFailShield;
    }

    public int getMailArmySpySuccess(){
        return this.MailArmySpySuccess;
    }

    public int getMailBaseSpySuccess(){
        return this.MailBaseSpySuccess;
    }

    public int getMailGrouparmySpySuccess(){
        return this.MailGrouparmySpySuccess;
    }

    public int getMailBuildingSpySuccess(){
        return this.MailBuildingSpySuccess;
    }

    public int getMailArmyBeSpiedSuccess(){
        return this.MailArmyBeSpiedSuccess;
    }

    public int getMailBaseBeSpiedSuccess(){
        return this.MailBaseBeSpiedSuccess;
    }

    public int getMailHelpBaseBeSpiedSuccess(){
        return this.MailHelpBaseBeSpiedSuccess;
    }

    public int getMailGrouparmyBeSpiedSuccess(){
        return this.MailGrouparmyBeSpiedSuccess;
    }

    public int getMailBuildingBeSpiedSuccess(){
        return this.MailBuildingBeSpiedSuccess;
    }

    public int getMailBaseSpyFailLost(){
        return this.MailBaseSpyFailLost;
    }

    public int getMailArmySpyFailLost(){
        return this.MailArmySpyFailLost;
    }

    public int getMailGrouparmySpyFailLost(){
        return this.MailGrouparmySpyFailLost;
    }

    public int getMailBuildingSpyFailShield(){
        return this.MailBuildingSpyFailShield;
    }

    public int getMailSpyFailLevelLow(){
        return this.MailSpyFailLevelLow;
    }

    public int getMailSpyFailSameLegion(){
        return this.MailSpyFailSameLegion;
    }

    public int getMailSpyFailDespy(){
        return this.MailSpyFailDespy;
    }

    public int getMailArmyBeSpiedSuccessLevel(){
        return this.MailArmyBeSpiedSuccessLevel;
    }

    public int getMailBaseBeSpiedSuccessLevel(){
        return this.MailBaseBeSpiedSuccessLevel;
    }

    public int getMailBaseBeSpiedSuccessDespy(){
        return this.MailBaseBeSpiedSuccessDespy;
    }

    public int getSpyMaxBaseLevelDifference(){
        return this.SpyMaxBaseLevelDifference;
    }

    public int getBeSpiedMaxBaseLevelDifference(){
        return this.BeSpiedMaxBaseLevelDifference;
    }

    public int getMaxFogExplore(){
        return this.MaxFogExplore;
    }

    public String getFogExploreFinishTips(){
        return this.FogExploreFinishTips;
    }   

    public List<Integer> getSpymailtriggertypeSpydefend(){
        return this.SpyMailTriggerType_SpyDefend;
    }

    public List<Integer> getSpymailtriggertypeSpyfailed(){
        return this.SpyMailTriggerType_SpyFailed;
    }

    public List<Integer> getSpymailtriggertypePlayerarmy(){
        return this.SpyMailTriggerType_PlayerArmy;
    }

    public List<Integer> getSpymailtriggertypeCity(){
        return this.SpyMailTriggerType_City;
    }

    public List<Integer> getSpymailtriggertypeRallyarmy(){
        return this.SpyMailTriggerType_RallyArmy;
    }

    public List<Integer> getSpymailtriggertypeBuilding(){
        return this.SpyMailTriggerType_Building;
    }

    public int getSpyPlaneRadius(){
        return this.SpyPlaneRadius;
    }

    public int getSpyFuzzyMin(){
        return this.SpyFuzzyMin;
    }

    public int getSpyFuzzyMax(){
        return this.SpyFuzzyMax;
    }

    public int getAniTakeOffMs(){
        return this.AniTakeOffMs;
    }

    public int getAniLandingMs(){
        return this.AniLandingMs;
    }

    public int getGvgScoutBuildingSuccess(){
        return this.GvgScoutBuildingSuccess;
    }

    @Override
    public int getId() {
        return 0;
    }
}

package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="K_空军.xlsx", node="transport_plane.xml")
public class TransportPlaneTemplate implements IResTemplate  {

    /**
    * 空指部id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 运输机速度
    * int speed
    * 
    */
    @ResAttribute("speed")
    private  int speed;

    /**
    * 冷却时间(秒）
    * int cd
    * 
    */
    @ResAttribute("cd")
    private  int cd;



    /**
    * 空指部id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 运输机速度
    * int speed
    * 
    */
    public int getSpeed(){
        return speed;
    }

    /**
    * 冷却时间(秒）
    * int cd
    * 
    */
    public int getCd(){
        return cd;
    }


}
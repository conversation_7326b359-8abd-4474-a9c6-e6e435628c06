package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_联盟领地表.xlsx", node="territory_building.xml")
public class TerritoryBuildingTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 建筑类型
    * CommonEnum.MapBuildingType type
    * 
    */
    @ResAttribute("type")
    private CommonEnum.MapBuildingType type;

    /**
    * 势力值
    * int powerScore
    * 
    */
    @ResAttribute("powerScore")
    private  int powerScore;

    /**
    * 占领总时间
    * int occupyTime
    * 
    */
    @ResAttribute("occupyTime")
    private  int occupyTime;

    /**
    * 
坚守时间(秒）

    * int stickTime
    * 
    */
    @ResAttribute("stickTime")
    private  int stickTime;

    /**
    * 保护时间
(秒）
    * int protectTime
    * 
    */
    @ResAttribute("protectTime")
    private  int protectTime;

    /**
    * 荒废时间
(秒)
    * int discardTime
    * 
    */
    @ResAttribute("discardTime")
    private  int discardTime;

    /**
    * 等级
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;

    /**
    * 基础buff
    * intarray baseBuff
    * 
    */
    @ResAttribute("baseBuff")
    private List<Integer> baseBuffList;

    /**
    * 纳入指挥网后效果
    * intarray incorporateBuff
    * 
    */
    @ResAttribute("incorporateBuff")
    private List<Integer> incorporateBuffList;

    /**
    * 首次占领奖励邮件
    * int firstOwnMailId
    * 
    */
    @ResAttribute("firstOwnMailId")
    private  int firstOwnMailId;

    /**
    * 单兵重建速度加成
(每n个兵提供1速度)
    * int SoldierRebuildRatio
    * 
    */
    @ResAttribute("SoldierRebuildRatio")
    private  int SoldierRebuildRatio;

    /**
    * 数量上限
    * int limit
    * 
    */
    @ResAttribute("limit")
    private  int limit;

    /**
    * 建筑最大耐久度
    * int maxHp
    * 
    */
    @ResAttribute("maxHp")
    private  int maxHp;

    /**
    * 基础建造速度
    * int basicSpeed
    * 
    */
    @ResAttribute("basicSpeed")
    private  int basicSpeed;

    /**
    * 建造工程总量
    * int totalWorks
    * 
    */
    @ResAttribute("totalWorks")
    private  int totalWorks;

    /**
    * 积分获取系数
    * int coefficient
    * 
    */
    @ResAttribute("coefficient")
    private  int coefficient;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 建筑类型
    * CommonEnum.MapBuildingType type
    * 
    */
    public CommonEnum.MapBuildingType getType(){
        return type;
    }
    /**
    * 势力值
    * int powerScore
    * 
    */
    public int getPowerScore(){
        return powerScore;
    }

    /**
    * 占领总时间
    * int occupyTime
    * 
    */
    public int getOccupyTime(){
        return occupyTime;
    }

    /**
    * 
坚守时间(秒）

    * int stickTime
    * 
    */
    public int getStickTime(){
        return stickTime;
    }

    /**
    * 保护时间
(秒）
    * int protectTime
    * 
    */
    public int getProtectTime(){
        return protectTime;
    }

    /**
    * 荒废时间
(秒)
    * int discardTime
    * 
    */
    public int getDiscardTime(){
        return discardTime;
    }

    /**
    * 等级
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }


    /**
    * 基础buff
    * intarray baseBuff
    * 
    */
    public List<Integer> getBaseBuffList(){
        return baseBuffList;
    }


    /**
    * 纳入指挥网后效果
    * intarray incorporateBuff
    * 
    */
    public List<Integer> getIncorporateBuffList(){
        return incorporateBuffList;
    }

    /**
    * 首次占领奖励邮件
    * int firstOwnMailId
    * 
    */
    public int getFirstOwnMailId(){
        return firstOwnMailId;
    }

    /**
    * 单兵重建速度加成
(每n个兵提供1速度)
    * int SoldierRebuildRatio
    * 
    */
    public int getSoldierRebuildRatio(){
        return SoldierRebuildRatio;
    }

    /**
    * 数量上限
    * int limit
    * 
    */
    public int getLimit(){
        return limit;
    }

    /**
    * 建筑最大耐久度
    * int maxHp
    * 
    */
    public int getMaxHp(){
        return maxHp;
    }

    /**
    * 基础建造速度
    * int basicSpeed
    * 
    */
    public int getBasicSpeed(){
        return basicSpeed;
    }

    /**
    * 建造工程总量
    * int totalWorks
    * 
    */
    public int getTotalWorks(){
        return totalWorks;
    }

    /**
    * 积分获取系数
    * int coefficient
    * 
    */
    public int getCoefficient(){
        return coefficient;
    }


}
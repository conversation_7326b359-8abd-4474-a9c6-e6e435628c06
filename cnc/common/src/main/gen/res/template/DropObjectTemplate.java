package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_掉落物.xlsx", node="drop_object.xml")
public class DropObjectTemplate implements IResTemplate  {

    /**
    * 掉落物ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * HUD面板类型
    * CommonEnum.DropObjectType dropObjectType
    * 
    */
    @ResAttribute("dropObjectType")
    private CommonEnum.DropObjectType dropObjectType;

    /**
    * 拾取时间（毫秒）
    * int pickUpTime
    * 
    */
    @ResAttribute("pickUpTime")
    private  int pickUpTime;

    /**
    * 拾取物出生动画时长:毫秒
影响客户端表现动画
影响服务器做不可拾取状态
    * int apprearAnimationTime
    * 
    */
    @ResAttribute("apprearAnimationTime")
    private  int apprearAnimationTime;

    /**
    * 生命周期
    * int lifeTime
    * 
    */
    @ResAttribute("lifeTime")
    private  int lifeTime;

    /**
    * 拾取物共享类型
    * string ownType
    * 
    */
    @ResAttribute("ownType")
    private  String ownType;

    /**
    * 可拾取次数
    * int pickUpCount
    * 
    */
    @ResAttribute("pickUpCount")
    private  int pickUpCount;

    /**
    * 奖励id
宝箱关联reward表
科研试剂关联符文表
    * int reward
    * 
    */
    @ResAttribute("reward")
    private  int reward;

    /**
    * 邮件id
    * int mailId
    * 
    */
    @ResAttribute("mailId")
    private  int mailId;

    /**
    * 服务器交互距离半径(厘米)
    * int modelCircle
    * 
    */
    @ResAttribute("modelCircle")
    private  int modelCircle;

    /**
    * 体力消耗
    * int cost
    * 
    */
    @ResAttribute("cost")
    private  int cost;

    /**
    * 消耗道具
    * pairarray costItems
    * 
    */
    @ResAttribute("costItems")
    private List<IntPairType> costItemsPairList;

    /**
    * 自动领取
    * bool autoCollect
    * 
    */
    @ResAttribute("autoCollect")
    private  boolean autoCollect;



    /**
    * 掉落物ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * HUD面板类型
    * CommonEnum.DropObjectType dropObjectType
    * 
    */
    public CommonEnum.DropObjectType getDropObjectType(){
        return dropObjectType;
    }
    /**
    * 拾取时间（毫秒）
    * int pickUpTime
    * 
    */
    public int getPickUpTime(){
        return pickUpTime;
    }

    /**
    * 拾取物出生动画时长:毫秒
影响客户端表现动画
影响服务器做不可拾取状态
    * int apprearAnimationTime
    * 
    */
    public int getApprearAnimationTime(){
        return apprearAnimationTime;
    }

    /**
    * 生命周期
    * int lifeTime
    * 
    */
    public int getLifeTime(){
        return lifeTime;
    }

    /**
    * 拾取物共享类型
    * string ownType
    * 
    */
    public String getOwnType(){
        return ownType;
    }
    /**
    * 可拾取次数
    * int pickUpCount
    * 
    */
    public int getPickUpCount(){
        return pickUpCount;
    }

    /**
    * 奖励id
宝箱关联reward表
科研试剂关联符文表
    * int reward
    * 
    */
    public int getReward(){
        return reward;
    }

    /**
    * 邮件id
    * int mailId
    * 
    */
    public int getMailId(){
        return mailId;
    }

    /**
    * 服务器交互距离半径(厘米)
    * int modelCircle
    * 
    */
    public int getModelCircle(){
        return modelCircle;
    }

    /**
    * 体力消耗
    * int cost
    * 
    */
    public int getCost(){
        return cost;
    }


    /**
    * 消耗道具
    * pairarray costItems
    * 
    */
    public List<IntPairType> getCostItemsPairList(){
        return costItemsPairList;
    }

    /**
    * 自动领取
    * bool autoCollect
    * 
    */
    public boolean getAutoCollect(){
        return autoCollect;
    }

}
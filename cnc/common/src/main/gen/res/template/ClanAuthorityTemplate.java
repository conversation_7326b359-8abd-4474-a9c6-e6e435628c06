package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_联盟配置表.xlsx", node="clan_authority.xml")
public class ClanAuthorityTemplate implements IResTemplate  {

    /**
    * 联盟权限ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 操作内容枚举
    * CommonEnum.ClanOperationType opType
    * 
    */
    @ResAttribute("opType")
    private CommonEnum.ClanOperationType opType;

    /**
    * 默认允许操作的成员等级
    * intarray defaultAllowLevel
    * 
    */
    @ResAttribute("defaultAllowLevel")
    private List<Integer> defaultAllowLevelList;

    /**
    * 最大允许操作的成员等级范围
    * intarray maxAllowLevelList
    * 
    */
    @ResAttribute("maxAllowLevelList")
    private List<Integer> maxAllowLevelListList;

    /**
    * 最小允许操作的成员等级范围
    * intarray minAllowLevelList
    * 
    */
    @ResAttribute("minAllowLevelList")
    private List<Integer> minAllowLevelListList;

    /**
    * 是否已有头像官员专属
    * bool officiallyExclusive
    * 
    */
    @ResAttribute("officiallyExclusive")
    private  boolean officiallyExclusive;



    /**
    * 联盟权限ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 操作内容枚举
    * CommonEnum.ClanOperationType opType
    * 
    */
    public CommonEnum.ClanOperationType getOpType(){
        return opType;
    }

    /**
    * 默认允许操作的成员等级
    * intarray defaultAllowLevel
    * 
    */
    public List<Integer> getDefaultAllowLevelList(){
        return defaultAllowLevelList;
    }


    /**
    * 最大允许操作的成员等级范围
    * intarray maxAllowLevelList
    * 
    */
    public List<Integer> getMaxAllowLevelListList(){
        return maxAllowLevelListList;
    }


    /**
    * 最小允许操作的成员等级范围
    * intarray minAllowLevelList
    * 
    */
    public List<Integer> getMinAllowLevelListList(){
        return minAllowLevelListList;
    }


    /**
    * 是否已有头像官员专属
    * bool officiallyExclusive
    * 
    */
    public boolean getOfficiallyExclusive(){
        return officiallyExclusive;
    }

}
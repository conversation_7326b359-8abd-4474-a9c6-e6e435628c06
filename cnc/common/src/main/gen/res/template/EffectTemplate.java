package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_战斗技能.xlsx", node="effect.xml")
public class EffectTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 效果类型
    * string EffectType
    * 
    */
    @ResAttribute("EffectType")
    private  String EffectType;

    /**
    * 加成属性ID
    * string BonusAttributeID
    * 
    */
    @ResAttribute("BonusAttributeID")
    private  String BonusAttributeID;

    /**
    * 次级属性ID
    * string SecondaryAttributeID
    * 
    */
    @ResAttribute("SecondaryAttributeID")
    private  String SecondaryAttributeID;

    /**
    * 效果类别
    * string EffectCategories
    * 
    */
    @ResAttribute("EffectCategories")
    private  String EffectCategories;

    /**
    * 特殊标记
    * string SpecialMarkings
    * 
    */
    @ResAttribute("SpecialMarkings")
    private  String SpecialMarkings;

    /**
    * 不同技能来源叠加规则
    * string StackingRulesForDifferentSkillSource
    * 
    */
    @ResAttribute("StackingRulesForDifferentSkillSource")
    private  String StackingRulesForDifferentSkillSource;

    /**
    * 效果参数0意义
    * string EffectParameterMeaning0
    * 
    */
    @ResAttribute("EffectParameterMeaning0")
    private  String EffectParameterMeaning0;

    /**
    * 效果参数1意义
    * string EffectParameterMeaning1
    * 
    */
    @ResAttribute("EffectParameterMeaning1")
    private  String EffectParameterMeaning1;

    /**
    * 效果参数2意义
    * string EffectParameterMeaning2
    * 
    */
    @ResAttribute("EffectParameterMeaning2")
    private  String EffectParameterMeaning2;

    /**
    * 效果参数3意义
    * string EffectParameterMeaning3
    * 
    */
    @ResAttribute("EffectParameterMeaning3")
    private  String EffectParameterMeaning3;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 效果类型
    * string EffectType
    * 
    */
    public String getEffectType(){
        return EffectType;
    }
    /**
    * 加成属性ID
    * string BonusAttributeID
    * 
    */
    public String getBonusAttributeID(){
        return BonusAttributeID;
    }
    /**
    * 次级属性ID
    * string SecondaryAttributeID
    * 
    */
    public String getSecondaryAttributeID(){
        return SecondaryAttributeID;
    }
    /**
    * 效果类别
    * string EffectCategories
    * 
    */
    public String getEffectCategories(){
        return EffectCategories;
    }
    /**
    * 特殊标记
    * string SpecialMarkings
    * 
    */
    public String getSpecialMarkings(){
        return SpecialMarkings;
    }
    /**
    * 不同技能来源叠加规则
    * string StackingRulesForDifferentSkillSource
    * 
    */
    public String getStackingRulesForDifferentSkillSource(){
        return StackingRulesForDifferentSkillSource;
    }
    /**
    * 效果参数0意义
    * string EffectParameterMeaning0
    * 
    */
    public String getEffectParameterMeaning0(){
        return EffectParameterMeaning0;
    }
    /**
    * 效果参数1意义
    * string EffectParameterMeaning1
    * 
    */
    public String getEffectParameterMeaning1(){
        return EffectParameterMeaning1;
    }
    /**
    * 效果参数2意义
    * string EffectParameterMeaning2
    * 
    */
    public String getEffectParameterMeaning2(){
        return EffectParameterMeaning2;
    }
    /**
    * 效果参数3意义
    * string EffectParameterMeaning3
    * 
    */
    public String getEffectParameterMeaning3(){
        return EffectParameterMeaning3;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_地缘区域限制.xlsx", node="open_region.xml")
public class OpenRegionTemplate implements IResTemplate  {

    /**
    * 序号
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 州id
    * int regionId
    * 
    */
    @ResAttribute("regionId")
    private  int regionId;

    /**
    * 是否开放
    * bool isOpen
    * 
    */
    @ResAttribute("isOpen")
    private  boolean isOpen;



    /**
    * 序号
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 州id
    * int regionId
    * 
    */
    public int getRegionId(){
        return regionId;
    }


    /**
    * 是否开放
    * bool isOpen
    * 
    */
    public boolean getIsOpen(){
        return isOpen;
    }

}
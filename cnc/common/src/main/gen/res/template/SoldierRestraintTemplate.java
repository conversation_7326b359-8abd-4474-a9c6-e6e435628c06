package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="S_士兵表_兵种克制.xlsx", node="soldier_restraint.xml")
public class SoldierRestraintTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 攻击的兵种
    * int attacker
    * 
    */
    @ResAttribute("attacker")
    private  int attacker;

    /**
    * 防御方兵种
    * int defencer
    * 
    */
    @ResAttribute("defencer")
    private  int defencer;

    /**
    * 克制效果
    * int value
    * 
    */
    @ResAttribute("value")
    private  int value;

    /**
    * 说明
    * string des
    * 
    */
    @ResAttribute("des")
    private  String des;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 攻击的兵种
    * int attacker
    * 
    */
    public int getAttacker(){
        return attacker;
    }

    /**
    * 防御方兵种
    * int defencer
    * 
    */
    public int getDefencer(){
        return defencer;
    }

    /**
    * 克制效果
    * int value
    * 
    */
    public int getValue(){
        return value;
    }

    /**
    * 说明
    * string des
    * 
    */
    public String getDes(){
        return des;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="L_连锁礼包.xlsx", node="continues_gift.xml")
public class ContinuesGiftTemplate implements IResTemplate  {

    /**
    * 配置id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 是否免费
    * bool isFree
    * 
    */
    @ResAttribute("isFree")
    private  boolean isFree;

    /**
    * 礼包id
    * int goodsId
    * 
    */
    @ResAttribute("goodsId")
    private  int goodsId;

    /**
    * 折扣礼包id
    * int discountGoodsId
    * 
    */
    @ResAttribute("discountGoodsId")
    private  int discountGoodsId;

    /**
    * 道具奖励
    * pairarray itemList
    * 
    */
    @ResAttribute("itemList")
    private List<IntPairType> itemListPairList;

    /**
    * 是否需要补发
    * bool needReissue
    * 
    */
    @ResAttribute("needReissue")
    private  boolean needReissue;



    /**
    * 配置id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 是否免费
    * bool isFree
    * 
    */
    public boolean getIsFree(){
        return isFree;
    }
    /**
    * 礼包id
    * int goodsId
    * 
    */
    public int getGoodsId(){
        return goodsId;
    }

    /**
    * 折扣礼包id
    * int discountGoodsId
    * 
    */
    public int getDiscountGoodsId(){
        return discountGoodsId;
    }


    /**
    * 道具奖励
    * pairarray itemList
    * 
    */
    public List<IntPairType> getItemListPairList(){
        return itemListPairList;
    }

    /**
    * 是否需要补发
    * bool needReissue
    * 
    */
    public boolean getNeedReissue(){
        return needReissue;
    }

}
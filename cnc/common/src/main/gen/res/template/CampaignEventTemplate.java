package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_战役配置表【RH】.xlsx", node="campaign_event.xml")
public class CampaignEventTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 事件类型
    * int type
    * 
    */
    @ResAttribute("type")
    private  int type;

    /**
    * 事件触发权重
    * int eventWeight
    * 
    */
    @ResAttribute("eventWeight")
    private  int eventWeight;

    /**
    * 参数
    * intarray parameter
    * 
    */
    @ResAttribute("parameter")
    private List<Integer> parameterList;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 事件类型
    * int type
    * 
    */
    public int getType(){
        return type;
    }

    /**
    * 事件触发权重
    * int eventWeight
    * 
    */
    public int getEventWeight(){
        return eventWeight;
    }


    /**
    * 参数
    * intarray parameter
    * 
    */
    public List<Integer> getParameterList(){
        return parameterList;
    }


}
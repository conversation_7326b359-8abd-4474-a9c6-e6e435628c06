package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Y_英雄表.xlsx", node="talent.xml")
public class TalentTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 天赋组
    * int group
    * 
    */
    @ResAttribute("group")
    private  int group;

    /**
    * 等级
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;

    /**
    * 下一级天赋id
    * int nextlevel
    * 
    */
    @ResAttribute("nextlevel")
    private  int nextlevel;

    /**
    * 天赋效果（填技能效果id）
    * intarray effect
    * 
    */
    @ResAttribute("effect")
    private List<Integer> effectList;

    /**
    * 战斗力
    * int power
    * 
    */
    @ResAttribute("power")
    private  int power;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 天赋组
    * int group
    * 
    */
    public int getGroup(){
        return group;
    }

    /**
    * 等级
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }

    /**
    * 下一级天赋id
    * int nextlevel
    * 
    */
    public int getNextlevel(){
        return nextlevel;
    }


    /**
    * 天赋效果（填技能效果id）
    * intarray effect
    * 
    */
    public List<Integer> getEffectList(){
        return effectList;
    }

    /**
    * 战斗力
    * int power
    * 
    */
    public int getPower(){
        return power;
    }


}
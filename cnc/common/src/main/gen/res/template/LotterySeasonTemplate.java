package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="C_抽奖活动表.xlsx", node="lottery_season.xml")
public class LotterySeasonTemplate implements IResTemplate  {

    /**
    * 配置id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 活动阶段
    * CommonEnum.CommanderActivtyStage SeasonStatelottery
    * 
    */
    @ResAttribute("SeasonStatelottery")
    private CommonEnum.CommanderActivtyStage SeasonStatelottery;

    /**
    * 循环期数（左闭右开）
    * pair volume
    * 
    */
    @ResAttribute("volume")
    private IntPairType volumePair;

    /**
    * 奖励英雄
    * intarray heroRewardIds
    * 
    */
    @ResAttribute("heroRewardIds")
    private List<Integer> heroRewardIdsList;



    /**
    * 配置id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 活动阶段
    * CommonEnum.CommanderActivtyStage SeasonStatelottery
    * 
    */
    public CommonEnum.CommanderActivtyStage getSeasonStatelottery(){
        return SeasonStatelottery;
    }

    /**
    * 循环期数（左闭右开）
    * pair volume
    * 
    */
    public IntPairType getVolumePair(){
        return volumePair;
    }

    /**
    * 奖励英雄
    * intarray heroRewardIds
    * 
    */
    public List<Integer> getHeroRewardIdsList(){
        return heroRewardIdsList;
    }


}
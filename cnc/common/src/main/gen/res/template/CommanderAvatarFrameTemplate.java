package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Z_指挥官.xlsx", node="commander_avatar_frame.xml")
public class CommanderAvatarFrameTemplate implements IResTemplate  {

    /**
    * 头像框ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 解锁方式
    * CommonEnum.AvatarUnlockType unlock
    * 
    */
    @ResAttribute("unlock")
    private CommonEnum.AvatarUnlockType unlock;



    /**
    * 头像框ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }


    /**
    * 解锁方式
    * CommonEnum.AvatarUnlockType unlock
    * 
    */
    public CommonEnum.AvatarUnlockType getUnlock(){
        return unlock;
    }

}
package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="T_通行证.xlsx", node="bp_task.xml")
public class BpTaskTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 任务Id
    * int taskId
    * 
    */
    @ResAttribute("taskId")
    private  int taskId;

    /**
    * 任务积分
    * int integral
    * 
    */
    @ResAttribute("integral")
    private  int integral;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 任务Id
    * int taskId
    * 
    */
    public int getTaskId(){
        return taskId;
    }

    /**
    * 任务积分
    * int integral
    * 
    */
    public int getIntegral(){
        return integral;
    }


}
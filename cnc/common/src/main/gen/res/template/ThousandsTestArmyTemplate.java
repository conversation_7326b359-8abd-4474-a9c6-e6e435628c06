package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="Q_千人同屏.xlsx", node="thousands_test_army.xml")
public class ThousandsTestArmyTemplate implements IResTemplate  {

    /**
    * ID
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 主将
    * int mainHero
    * 
    */
    @ResAttribute("mainHero")
    private  int mainHero;

    /**
    * 副将
    * int deputyHero
    * 
    */
    @ResAttribute("deputyHero")
    private  int deputyHero;

    /**
    * 兵力
    * pairarray soldier
    * 
    */
    @ResAttribute("soldier")
    private List<IntPairType> soldierPairList;

    /**
    * 机甲
    * int mecha
    * 
    */
    @ResAttribute("mecha")
    private  int mecha;

    /**
    * 阵营编号
    * int leagueId
    * 
    */
    @ResAttribute("leagueId")
    private  int leagueId;



    /**
    * ID
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 主将
    * int mainHero
    * 
    */
    public int getMainHero(){
        return mainHero;
    }

    /**
    * 副将
    * int deputyHero
    * 
    */
    public int getDeputyHero(){
        return deputyHero;
    }


    /**
    * 兵力
    * pairarray soldier
    * 
    */
    public List<IntPairType> getSoldierPairList(){
        return soldierPairList;
    }
    /**
    * 机甲
    * int mecha
    * 
    */
    public int getMecha(){
        return mecha;
    }

    /**
    * 阵营编号
    * int leagueId
    * 
    */
    public int getLeagueId(){
        return leagueId;
    }


}
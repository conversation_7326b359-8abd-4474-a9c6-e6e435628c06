package res.template;

import java.util.*;
import com.yorha.common.resource.IResTemplate;
import com.yorha.common.resource.annotation.ResAttribute;
import com.yorha.common.resource.annotation.ResXml;
import com.yorha.common.resource.datatype.*;
import com.yorha.proto.CommonEnum;



/** 此文件由工具生成 手动修改无效
* Copyright Yorhagames Co., Ltd
* All rights reserved.
* <AUTHOR> yorhagames table tools
* @version : ver 1.0
*/
@ResXml(excel="D_地图玩法初始化.xlsx", node="initialization_wild_monster.xml")
public class InitializationWildMonsterTemplate implements IResTemplate  {

    /**
    * id
    * int id
    * 
    */
    @ResAttribute("id")
    private  int id;

    /**
    * 刷新等级
    * int level
    * 
    */
    @ResAttribute("level")
    private  int level;

    /**
    * 所属玩法建筑ID
    * int buildingID
    * 
    */
    @ResAttribute("buildingID")
    private  int buildingID;

    /**
    * 野怪ID
    * int monsterId
    * 
    */
    @ResAttribute("monsterId")
    private  int monsterId;

    /**
    * 是否是boos
    * bool isBoss
    * 
    */
    @ResAttribute("isBoss")
    private  boolean isBoss;

    /**
    * 相对X坐标
    * int coordinateX
    * 
    */
    @ResAttribute("coordinateX")
    private  int coordinateX;

    /**
    * 相对Y坐标
    * int coordinateY
    * 
    */
    @ResAttribute("coordinateY")
    private  int coordinateY;

    /**
    * 刷新万分比
    * int radio
    * 
    */
    @ResAttribute("radio")
    private  int radio;



    /**
    * id
    * int id
    * 
    */
    @Override
    public int getId(){
        return id;
    }

    /**
    * 刷新等级
    * int level
    * 
    */
    public int getLevel(){
        return level;
    }

    /**
    * 所属玩法建筑ID
    * int buildingID
    * 
    */
    public int getBuildingID(){
        return buildingID;
    }

    /**
    * 野怪ID
    * int monsterId
    * 
    */
    public int getMonsterId(){
        return monsterId;
    }


    /**
    * 是否是boos
    * bool isBoss
    * 
    */
    public boolean getIsBoss(){
        return isBoss;
    }
    /**
    * 相对X坐标
    * int coordinateX
    * 
    */
    public int getCoordinateX(){
        return coordinateX;
    }

    /**
    * 相对Y坐标
    * int coordinateY
    * 
    */
    public int getCoordinateY(){
        return coordinateY;
    }

    /**
    * 刷新万分比
    * int radio
    * 
    */
    public int getRadio(){
        return radio;
    }


}
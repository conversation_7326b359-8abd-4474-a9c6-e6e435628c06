package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Clan.ClanMemberHistoryModel;
import com.yorha.proto.StructClan;
import com.yorha.proto.ClanPB.ClanMemberHistoryModelPB;
import com.yorha.proto.StructClanPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanMemberHistoryModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_MEMBERS = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int64ClanMemberHistoryMapProp members = null;

    public ClanMemberHistoryModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanMemberHistoryModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get members
     *
     * @return members value
     */
    public Int64ClanMemberHistoryMapProp getMembers() {
        if (this.members == null) {
            this.members = new Int64ClanMemberHistoryMapProp(this, FIELD_INDEX_MEMBERS);
        }
        return this.members;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putMembersV(ClanMemberHistoryProp v) {
        this.getMembers().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ClanMemberHistoryProp addEmptyMembers(Long k) {
        return this.getMembers().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getMembersSize() {
        if (this.members == null) {
            return 0;
        }
        return this.members.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isMembersEmpty() {
        if (this.members == null) {
            return true;
        }
        return this.members.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ClanMemberHistoryProp getMembersV(Long k) {
        if (this.members == null || !this.members.containsKey(k)) {
            return null;
        }
        return this.members.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearMembers() {
        if (this.members != null) {
            this.members.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeMembersV(Long k) {
        if (this.members != null) {
            this.members.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanMemberHistoryModelPB.Builder getCopyCsBuilder() {
        final ClanMemberHistoryModelPB.Builder builder = ClanMemberHistoryModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanMemberHistoryModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.members != null) {
            StructClanPB.Int64ClanMemberHistoryMapPB.Builder tmpBuilder = StructClanPB.Int64ClanMemberHistoryMapPB.newBuilder();
            final int tmpFieldCnt = this.members.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMembers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMembers();
            }
        }  else if (builder.hasMembers()) {
            // 清理Members
            builder.clearMembers();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanMemberHistoryModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MEMBERS) && this.members != null) {
            final boolean needClear = !builder.hasMembers();
            final int tmpFieldCnt = this.members.copyChangeToCs(builder.getMembersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMembers();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanMemberHistoryModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MEMBERS) && this.members != null) {
            final boolean needClear = !builder.hasMembers();
            final int tmpFieldCnt = this.members.copyChangeToAndClearDeleteKeysCs(builder.getMembersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMembers();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanMemberHistoryModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMembers()) {
            this.getMembers().mergeFromCs(proto.getMembers());
        } else {
            if (this.members != null) {
                this.members.mergeFromCs(proto.getMembers());
            }
        }
        this.markAll();
        return ClanMemberHistoryModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanMemberHistoryModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMembers()) {
            this.getMembers().mergeChangeFromCs(proto.getMembers());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanMemberHistoryModel.Builder getCopyDbBuilder() {
        final ClanMemberHistoryModel.Builder builder = ClanMemberHistoryModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanMemberHistoryModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.members != null) {
            StructClan.Int64ClanMemberHistoryMap.Builder tmpBuilder = StructClan.Int64ClanMemberHistoryMap.newBuilder();
            final int tmpFieldCnt = this.members.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMembers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMembers();
            }
        }  else if (builder.hasMembers()) {
            // 清理Members
            builder.clearMembers();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanMemberHistoryModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MEMBERS) && this.members != null) {
            final boolean needClear = !builder.hasMembers();
            final int tmpFieldCnt = this.members.copyChangeToDb(builder.getMembersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMembers();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanMemberHistoryModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMembers()) {
            this.getMembers().mergeFromDb(proto.getMembers());
        } else {
            if (this.members != null) {
                this.members.mergeFromDb(proto.getMembers());
            }
        }
        this.markAll();
        return ClanMemberHistoryModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanMemberHistoryModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMembers()) {
            this.getMembers().mergeChangeFromDb(proto.getMembers());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanMemberHistoryModel.Builder getCopySsBuilder() {
        final ClanMemberHistoryModel.Builder builder = ClanMemberHistoryModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanMemberHistoryModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.members != null) {
            StructClan.Int64ClanMemberHistoryMap.Builder tmpBuilder = StructClan.Int64ClanMemberHistoryMap.newBuilder();
            final int tmpFieldCnt = this.members.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMembers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMembers();
            }
        }  else if (builder.hasMembers()) {
            // 清理Members
            builder.clearMembers();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanMemberHistoryModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MEMBERS) && this.members != null) {
            final boolean needClear = !builder.hasMembers();
            final int tmpFieldCnt = this.members.copyChangeToSs(builder.getMembersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMembers();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanMemberHistoryModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMembers()) {
            this.getMembers().mergeFromSs(proto.getMembers());
        } else {
            if (this.members != null) {
                this.members.mergeFromSs(proto.getMembers());
            }
        }
        this.markAll();
        return ClanMemberHistoryModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanMemberHistoryModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMembers()) {
            this.getMembers().mergeChangeFromSs(proto.getMembers());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanMemberHistoryModel.Builder builder = ClanMemberHistoryModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_MEMBERS) && this.members != null) {
            this.members.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.members != null) {
            this.members.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanMemberHistoryModelProp)) {
            return false;
        }
        final ClanMemberHistoryModelProp otherNode = (ClanMemberHistoryModelProp) node;
        if (!this.getMembers().compareDataTo(otherNode.getMembers())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
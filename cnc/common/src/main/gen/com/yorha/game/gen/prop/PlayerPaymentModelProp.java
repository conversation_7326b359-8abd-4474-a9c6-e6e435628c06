package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerPaymentModel;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.PlayerPaymentModelPB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerPaymentModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_GEN_BALANCE = 0;
    public static final int FIELD_INDEX_FIRST_SAVE = 1;
    public static final int FIELD_INDEX_SAVE_AMT = 2;
    public static final int FIELD_INDEX_SAVE_SUM = 3;
    public static final int FIELD_INDEX_COST_SUM = 4;
    public static final int FIELD_INDEX_PRESENT_SUM = 5;
    public static final int FIELD_INDEX_GOODSORDERS = 6;
    public static final int FIELD_INDEX_GOODSHISTORY = 7;
    public static final int FIELD_INDEX_DONEGOODSORDERS = 8;
    public static final int FIELD_INDEX_LASTSHOWMIDASURLTSMS = 9;

    public static final int FIELD_COUNT = 10;

    private long markBits0 = 0L;

    private long gen_balance = Constant.DEFAULT_LONG_VALUE;
    private int first_save = Constant.DEFAULT_INT_VALUE;
    private long save_amt = Constant.DEFAULT_LONG_VALUE;
    private long save_sum = Constant.DEFAULT_LONG_VALUE;
    private long cost_sum = Constant.DEFAULT_LONG_VALUE;
    private long present_sum = Constant.DEFAULT_LONG_VALUE;
    private StringPlayerGoodsOrderMapProp goodsOrders = null;
    private Int32PlayerGoodsHistoryMapProp goodsHistory = null;
    private StringPlayerGoodsInfoMapProp doneGoodsOrders = null;
    private long lastShowMidasUrlTsMs = Constant.DEFAULT_LONG_VALUE;

    public PlayerPaymentModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerPaymentModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get gen_balance
     *
     * @return gen_balance value
     */
    public long getGenBalance() {
        return this.gen_balance;
    }

    /**
     * set gen_balance && set marked
     *
     * @param gen_balance new value
     * @return current object
     */
    public PlayerPaymentModelProp setGenBalance(long gen_balance) {
        if (this.gen_balance != gen_balance) {
            this.mark(FIELD_INDEX_GEN_BALANCE);
            this.gen_balance = gen_balance;
        }
        return this;
    }

    /**
     * inner set gen_balance
     *
     * @param gen_balance new value
     */
    private void innerSetGenBalance(long gen_balance) {
        this.gen_balance = gen_balance;
    }

    /**
     * get first_save
     *
     * @return first_save value
     */
    public int getFirstSave() {
        return this.first_save;
    }

    /**
     * set first_save && set marked
     *
     * @param first_save new value
     * @return current object
     */
    public PlayerPaymentModelProp setFirstSave(int first_save) {
        if (this.first_save != first_save) {
            this.mark(FIELD_INDEX_FIRST_SAVE);
            this.first_save = first_save;
        }
        return this;
    }

    /**
     * inner set first_save
     *
     * @param first_save new value
     */
    private void innerSetFirstSave(int first_save) {
        this.first_save = first_save;
    }

    /**
     * get save_amt
     *
     * @return save_amt value
     */
    public long getSaveAmt() {
        return this.save_amt;
    }

    /**
     * set save_amt && set marked
     *
     * @param save_amt new value
     * @return current object
     */
    public PlayerPaymentModelProp setSaveAmt(long save_amt) {
        if (this.save_amt != save_amt) {
            this.mark(FIELD_INDEX_SAVE_AMT);
            this.save_amt = save_amt;
        }
        return this;
    }

    /**
     * inner set save_amt
     *
     * @param save_amt new value
     */
    private void innerSetSaveAmt(long save_amt) {
        this.save_amt = save_amt;
    }

    /**
     * get save_sum
     *
     * @return save_sum value
     */
    public long getSaveSum() {
        return this.save_sum;
    }

    /**
     * set save_sum && set marked
     *
     * @param save_sum new value
     * @return current object
     */
    public PlayerPaymentModelProp setSaveSum(long save_sum) {
        if (this.save_sum != save_sum) {
            this.mark(FIELD_INDEX_SAVE_SUM);
            this.save_sum = save_sum;
        }
        return this;
    }

    /**
     * inner set save_sum
     *
     * @param save_sum new value
     */
    private void innerSetSaveSum(long save_sum) {
        this.save_sum = save_sum;
    }

    /**
     * get cost_sum
     *
     * @return cost_sum value
     */
    public long getCostSum() {
        return this.cost_sum;
    }

    /**
     * set cost_sum && set marked
     *
     * @param cost_sum new value
     * @return current object
     */
    public PlayerPaymentModelProp setCostSum(long cost_sum) {
        if (this.cost_sum != cost_sum) {
            this.mark(FIELD_INDEX_COST_SUM);
            this.cost_sum = cost_sum;
        }
        return this;
    }

    /**
     * inner set cost_sum
     *
     * @param cost_sum new value
     */
    private void innerSetCostSum(long cost_sum) {
        this.cost_sum = cost_sum;
    }

    /**
     * get present_sum
     *
     * @return present_sum value
     */
    public long getPresentSum() {
        return this.present_sum;
    }

    /**
     * set present_sum && set marked
     *
     * @param present_sum new value
     * @return current object
     */
    public PlayerPaymentModelProp setPresentSum(long present_sum) {
        if (this.present_sum != present_sum) {
            this.mark(FIELD_INDEX_PRESENT_SUM);
            this.present_sum = present_sum;
        }
        return this;
    }

    /**
     * inner set present_sum
     *
     * @param present_sum new value
     */
    private void innerSetPresentSum(long present_sum) {
        this.present_sum = present_sum;
    }

    /**
     * get goodsOrders
     *
     * @return goodsOrders value
     */
    public StringPlayerGoodsOrderMapProp getGoodsOrders() {
        if (this.goodsOrders == null) {
            this.goodsOrders = new StringPlayerGoodsOrderMapProp(this, FIELD_INDEX_GOODSORDERS);
        }
        return this.goodsOrders;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putGoodsOrdersV(PlayerGoodsOrderProp v) {
        this.getGoodsOrders().put(v.getToken(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerGoodsOrderProp addEmptyGoodsOrders(String k) {
        return this.getGoodsOrders().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getGoodsOrdersSize() {
        if (this.goodsOrders == null) {
            return 0;
        }
        return this.goodsOrders.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isGoodsOrdersEmpty() {
        if (this.goodsOrders == null) {
            return true;
        }
        return this.goodsOrders.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerGoodsOrderProp getGoodsOrdersV(String k) {
        if (this.goodsOrders == null || !this.goodsOrders.containsKey(k)) {
            return null;
        }
        return this.goodsOrders.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearGoodsOrders() {
        if (this.goodsOrders != null) {
            this.goodsOrders.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeGoodsOrdersV(String k) {
        if (this.goodsOrders != null) {
            this.goodsOrders.remove(k);
        }
    }
    /**
     * get goodsHistory
     *
     * @return goodsHistory value
     */
    public Int32PlayerGoodsHistoryMapProp getGoodsHistory() {
        if (this.goodsHistory == null) {
            this.goodsHistory = new Int32PlayerGoodsHistoryMapProp(this, FIELD_INDEX_GOODSHISTORY);
        }
        return this.goodsHistory;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putGoodsHistoryV(PlayerGoodsHistoryProp v) {
        this.getGoodsHistory().put(v.getGoodsId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerGoodsHistoryProp addEmptyGoodsHistory(Integer k) {
        return this.getGoodsHistory().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getGoodsHistorySize() {
        if (this.goodsHistory == null) {
            return 0;
        }
        return this.goodsHistory.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isGoodsHistoryEmpty() {
        if (this.goodsHistory == null) {
            return true;
        }
        return this.goodsHistory.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerGoodsHistoryProp getGoodsHistoryV(Integer k) {
        if (this.goodsHistory == null || !this.goodsHistory.containsKey(k)) {
            return null;
        }
        return this.goodsHistory.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearGoodsHistory() {
        if (this.goodsHistory != null) {
            this.goodsHistory.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeGoodsHistoryV(Integer k) {
        if (this.goodsHistory != null) {
            this.goodsHistory.remove(k);
        }
    }
    /**
     * get doneGoodsOrders
     *
     * @return doneGoodsOrders value
     */
    public StringPlayerGoodsInfoMapProp getDoneGoodsOrders() {
        if (this.doneGoodsOrders == null) {
            this.doneGoodsOrders = new StringPlayerGoodsInfoMapProp(this, FIELD_INDEX_DONEGOODSORDERS);
        }
        return this.doneGoodsOrders;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putDoneGoodsOrdersV(PlayerGoodsInfoProp v) {
        this.getDoneGoodsOrders().put(v.getKey(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerGoodsInfoProp addEmptyDoneGoodsOrders(String k) {
        return this.getDoneGoodsOrders().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getDoneGoodsOrdersSize() {
        if (this.doneGoodsOrders == null) {
            return 0;
        }
        return this.doneGoodsOrders.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isDoneGoodsOrdersEmpty() {
        if (this.doneGoodsOrders == null) {
            return true;
        }
        return this.doneGoodsOrders.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerGoodsInfoProp getDoneGoodsOrdersV(String k) {
        if (this.doneGoodsOrders == null || !this.doneGoodsOrders.containsKey(k)) {
            return null;
        }
        return this.doneGoodsOrders.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearDoneGoodsOrders() {
        if (this.doneGoodsOrders != null) {
            this.doneGoodsOrders.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeDoneGoodsOrdersV(String k) {
        if (this.doneGoodsOrders != null) {
            this.doneGoodsOrders.remove(k);
        }
    }
    /**
     * get lastShowMidasUrlTsMs
     *
     * @return lastShowMidasUrlTsMs value
     */
    public long getLastShowMidasUrlTsMs() {
        return this.lastShowMidasUrlTsMs;
    }

    /**
     * set lastShowMidasUrlTsMs && set marked
     *
     * @param lastShowMidasUrlTsMs new value
     * @return current object
     */
    public PlayerPaymentModelProp setLastShowMidasUrlTsMs(long lastShowMidasUrlTsMs) {
        if (this.lastShowMidasUrlTsMs != lastShowMidasUrlTsMs) {
            this.mark(FIELD_INDEX_LASTSHOWMIDASURLTSMS);
            this.lastShowMidasUrlTsMs = lastShowMidasUrlTsMs;
        }
        return this;
    }

    /**
     * inner set lastShowMidasUrlTsMs
     *
     * @param lastShowMidasUrlTsMs new value
     */
    private void innerSetLastShowMidasUrlTsMs(long lastShowMidasUrlTsMs) {
        this.lastShowMidasUrlTsMs = lastShowMidasUrlTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerPaymentModelPB.Builder getCopyCsBuilder() {
        final PlayerPaymentModelPB.Builder builder = PlayerPaymentModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerPaymentModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGenBalance() != 0L) {
            builder.setGenBalance(this.getGenBalance());
            fieldCnt++;
        }  else if (builder.hasGenBalance()) {
            // 清理GenBalance
            builder.clearGenBalance();
            fieldCnt++;
        }
        if (this.getFirstSave() != 0) {
            builder.setFirstSave(this.getFirstSave());
            fieldCnt++;
        }  else if (builder.hasFirstSave()) {
            // 清理FirstSave
            builder.clearFirstSave();
            fieldCnt++;
        }
        if (this.getSaveAmt() != 0L) {
            builder.setSaveAmt(this.getSaveAmt());
            fieldCnt++;
        }  else if (builder.hasSaveAmt()) {
            // 清理SaveAmt
            builder.clearSaveAmt();
            fieldCnt++;
        }
        if (this.getSaveSum() != 0L) {
            builder.setSaveSum(this.getSaveSum());
            fieldCnt++;
        }  else if (builder.hasSaveSum()) {
            // 清理SaveSum
            builder.clearSaveSum();
            fieldCnt++;
        }
        if (this.getCostSum() != 0L) {
            builder.setCostSum(this.getCostSum());
            fieldCnt++;
        }  else if (builder.hasCostSum()) {
            // 清理CostSum
            builder.clearCostSum();
            fieldCnt++;
        }
        if (this.getPresentSum() != 0L) {
            builder.setPresentSum(this.getPresentSum());
            fieldCnt++;
        }  else if (builder.hasPresentSum()) {
            // 清理PresentSum
            builder.clearPresentSum();
            fieldCnt++;
        }
        if (this.getLastShowMidasUrlTsMs() != 0L) {
            builder.setLastShowMidasUrlTsMs(this.getLastShowMidasUrlTsMs());
            fieldCnt++;
        }  else if (builder.hasLastShowMidasUrlTsMs()) {
            // 清理LastShowMidasUrlTsMs
            builder.clearLastShowMidasUrlTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerPaymentModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GEN_BALANCE)) {
            builder.setGenBalance(this.getGenBalance());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FIRST_SAVE)) {
            builder.setFirstSave(this.getFirstSave());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SAVE_AMT)) {
            builder.setSaveAmt(this.getSaveAmt());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SAVE_SUM)) {
            builder.setSaveSum(this.getSaveSum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COST_SUM)) {
            builder.setCostSum(this.getCostSum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PRESENT_SUM)) {
            builder.setPresentSum(this.getPresentSum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTSHOWMIDASURLTSMS)) {
            builder.setLastShowMidasUrlTsMs(this.getLastShowMidasUrlTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerPaymentModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GEN_BALANCE)) {
            builder.setGenBalance(this.getGenBalance());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FIRST_SAVE)) {
            builder.setFirstSave(this.getFirstSave());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SAVE_AMT)) {
            builder.setSaveAmt(this.getSaveAmt());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SAVE_SUM)) {
            builder.setSaveSum(this.getSaveSum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COST_SUM)) {
            builder.setCostSum(this.getCostSum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PRESENT_SUM)) {
            builder.setPresentSum(this.getPresentSum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTSHOWMIDASURLTSMS)) {
            builder.setLastShowMidasUrlTsMs(this.getLastShowMidasUrlTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerPaymentModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGenBalance()) {
            this.innerSetGenBalance(proto.getGenBalance());
        } else {
            this.innerSetGenBalance(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasFirstSave()) {
            this.innerSetFirstSave(proto.getFirstSave());
        } else {
            this.innerSetFirstSave(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSaveAmt()) {
            this.innerSetSaveAmt(proto.getSaveAmt());
        } else {
            this.innerSetSaveAmt(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSaveSum()) {
            this.innerSetSaveSum(proto.getSaveSum());
        } else {
            this.innerSetSaveSum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCostSum()) {
            this.innerSetCostSum(proto.getCostSum());
        } else {
            this.innerSetCostSum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPresentSum()) {
            this.innerSetPresentSum(proto.getPresentSum());
        } else {
            this.innerSetPresentSum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastShowMidasUrlTsMs()) {
            this.innerSetLastShowMidasUrlTsMs(proto.getLastShowMidasUrlTsMs());
        } else {
            this.innerSetLastShowMidasUrlTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerPaymentModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerPaymentModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGenBalance()) {
            this.setGenBalance(proto.getGenBalance());
            fieldCnt++;
        }
        if (proto.hasFirstSave()) {
            this.setFirstSave(proto.getFirstSave());
            fieldCnt++;
        }
        if (proto.hasSaveAmt()) {
            this.setSaveAmt(proto.getSaveAmt());
            fieldCnt++;
        }
        if (proto.hasSaveSum()) {
            this.setSaveSum(proto.getSaveSum());
            fieldCnt++;
        }
        if (proto.hasCostSum()) {
            this.setCostSum(proto.getCostSum());
            fieldCnt++;
        }
        if (proto.hasPresentSum()) {
            this.setPresentSum(proto.getPresentSum());
            fieldCnt++;
        }
        if (proto.hasLastShowMidasUrlTsMs()) {
            this.setLastShowMidasUrlTsMs(proto.getLastShowMidasUrlTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerPaymentModel.Builder getCopyDbBuilder() {
        final PlayerPaymentModel.Builder builder = PlayerPaymentModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerPaymentModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGenBalance() != 0L) {
            builder.setGenBalance(this.getGenBalance());
            fieldCnt++;
        }  else if (builder.hasGenBalance()) {
            // 清理GenBalance
            builder.clearGenBalance();
            fieldCnt++;
        }
        if (this.getFirstSave() != 0) {
            builder.setFirstSave(this.getFirstSave());
            fieldCnt++;
        }  else if (builder.hasFirstSave()) {
            // 清理FirstSave
            builder.clearFirstSave();
            fieldCnt++;
        }
        if (this.getSaveAmt() != 0L) {
            builder.setSaveAmt(this.getSaveAmt());
            fieldCnt++;
        }  else if (builder.hasSaveAmt()) {
            // 清理SaveAmt
            builder.clearSaveAmt();
            fieldCnt++;
        }
        if (this.getSaveSum() != 0L) {
            builder.setSaveSum(this.getSaveSum());
            fieldCnt++;
        }  else if (builder.hasSaveSum()) {
            // 清理SaveSum
            builder.clearSaveSum();
            fieldCnt++;
        }
        if (this.getCostSum() != 0L) {
            builder.setCostSum(this.getCostSum());
            fieldCnt++;
        }  else if (builder.hasCostSum()) {
            // 清理CostSum
            builder.clearCostSum();
            fieldCnt++;
        }
        if (this.getPresentSum() != 0L) {
            builder.setPresentSum(this.getPresentSum());
            fieldCnt++;
        }  else if (builder.hasPresentSum()) {
            // 清理PresentSum
            builder.clearPresentSum();
            fieldCnt++;
        }
        if (this.goodsOrders != null) {
            Player.StringPlayerGoodsOrderMap.Builder tmpBuilder = Player.StringPlayerGoodsOrderMap.newBuilder();
            final int tmpFieldCnt = this.goodsOrders.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGoodsOrders(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGoodsOrders();
            }
        }  else if (builder.hasGoodsOrders()) {
            // 清理GoodsOrders
            builder.clearGoodsOrders();
            fieldCnt++;
        }
        if (this.goodsHistory != null) {
            Player.Int32PlayerGoodsHistoryMap.Builder tmpBuilder = Player.Int32PlayerGoodsHistoryMap.newBuilder();
            final int tmpFieldCnt = this.goodsHistory.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGoodsHistory(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGoodsHistory();
            }
        }  else if (builder.hasGoodsHistory()) {
            // 清理GoodsHistory
            builder.clearGoodsHistory();
            fieldCnt++;
        }
        if (this.doneGoodsOrders != null) {
            Player.StringPlayerGoodsInfoMap.Builder tmpBuilder = Player.StringPlayerGoodsInfoMap.newBuilder();
            final int tmpFieldCnt = this.doneGoodsOrders.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDoneGoodsOrders(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDoneGoodsOrders();
            }
        }  else if (builder.hasDoneGoodsOrders()) {
            // 清理DoneGoodsOrders
            builder.clearDoneGoodsOrders();
            fieldCnt++;
        }
        if (this.getLastShowMidasUrlTsMs() != 0L) {
            builder.setLastShowMidasUrlTsMs(this.getLastShowMidasUrlTsMs());
            fieldCnt++;
        }  else if (builder.hasLastShowMidasUrlTsMs()) {
            // 清理LastShowMidasUrlTsMs
            builder.clearLastShowMidasUrlTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerPaymentModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GEN_BALANCE)) {
            builder.setGenBalance(this.getGenBalance());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FIRST_SAVE)) {
            builder.setFirstSave(this.getFirstSave());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SAVE_AMT)) {
            builder.setSaveAmt(this.getSaveAmt());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SAVE_SUM)) {
            builder.setSaveSum(this.getSaveSum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COST_SUM)) {
            builder.setCostSum(this.getCostSum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PRESENT_SUM)) {
            builder.setPresentSum(this.getPresentSum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GOODSORDERS) && this.goodsOrders != null) {
            final boolean needClear = !builder.hasGoodsOrders();
            final int tmpFieldCnt = this.goodsOrders.copyChangeToDb(builder.getGoodsOrdersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGoodsOrders();
            }
        }
        if (this.hasMark(FIELD_INDEX_GOODSHISTORY) && this.goodsHistory != null) {
            final boolean needClear = !builder.hasGoodsHistory();
            final int tmpFieldCnt = this.goodsHistory.copyChangeToDb(builder.getGoodsHistoryBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGoodsHistory();
            }
        }
        if (this.hasMark(FIELD_INDEX_DONEGOODSORDERS) && this.doneGoodsOrders != null) {
            final boolean needClear = !builder.hasDoneGoodsOrders();
            final int tmpFieldCnt = this.doneGoodsOrders.copyChangeToDb(builder.getDoneGoodsOrdersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDoneGoodsOrders();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTSHOWMIDASURLTSMS)) {
            builder.setLastShowMidasUrlTsMs(this.getLastShowMidasUrlTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerPaymentModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGenBalance()) {
            this.innerSetGenBalance(proto.getGenBalance());
        } else {
            this.innerSetGenBalance(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasFirstSave()) {
            this.innerSetFirstSave(proto.getFirstSave());
        } else {
            this.innerSetFirstSave(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSaveAmt()) {
            this.innerSetSaveAmt(proto.getSaveAmt());
        } else {
            this.innerSetSaveAmt(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSaveSum()) {
            this.innerSetSaveSum(proto.getSaveSum());
        } else {
            this.innerSetSaveSum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCostSum()) {
            this.innerSetCostSum(proto.getCostSum());
        } else {
            this.innerSetCostSum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPresentSum()) {
            this.innerSetPresentSum(proto.getPresentSum());
        } else {
            this.innerSetPresentSum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasGoodsOrders()) {
            this.getGoodsOrders().mergeFromDb(proto.getGoodsOrders());
        } else {
            if (this.goodsOrders != null) {
                this.goodsOrders.mergeFromDb(proto.getGoodsOrders());
            }
        }
        if (proto.hasGoodsHistory()) {
            this.getGoodsHistory().mergeFromDb(proto.getGoodsHistory());
        } else {
            if (this.goodsHistory != null) {
                this.goodsHistory.mergeFromDb(proto.getGoodsHistory());
            }
        }
        if (proto.hasDoneGoodsOrders()) {
            this.getDoneGoodsOrders().mergeFromDb(proto.getDoneGoodsOrders());
        } else {
            if (this.doneGoodsOrders != null) {
                this.doneGoodsOrders.mergeFromDb(proto.getDoneGoodsOrders());
            }
        }
        if (proto.hasLastShowMidasUrlTsMs()) {
            this.innerSetLastShowMidasUrlTsMs(proto.getLastShowMidasUrlTsMs());
        } else {
            this.innerSetLastShowMidasUrlTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerPaymentModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerPaymentModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGenBalance()) {
            this.setGenBalance(proto.getGenBalance());
            fieldCnt++;
        }
        if (proto.hasFirstSave()) {
            this.setFirstSave(proto.getFirstSave());
            fieldCnt++;
        }
        if (proto.hasSaveAmt()) {
            this.setSaveAmt(proto.getSaveAmt());
            fieldCnt++;
        }
        if (proto.hasSaveSum()) {
            this.setSaveSum(proto.getSaveSum());
            fieldCnt++;
        }
        if (proto.hasCostSum()) {
            this.setCostSum(proto.getCostSum());
            fieldCnt++;
        }
        if (proto.hasPresentSum()) {
            this.setPresentSum(proto.getPresentSum());
            fieldCnt++;
        }
        if (proto.hasGoodsOrders()) {
            this.getGoodsOrders().mergeChangeFromDb(proto.getGoodsOrders());
            fieldCnt++;
        }
        if (proto.hasGoodsHistory()) {
            this.getGoodsHistory().mergeChangeFromDb(proto.getGoodsHistory());
            fieldCnt++;
        }
        if (proto.hasDoneGoodsOrders()) {
            this.getDoneGoodsOrders().mergeChangeFromDb(proto.getDoneGoodsOrders());
            fieldCnt++;
        }
        if (proto.hasLastShowMidasUrlTsMs()) {
            this.setLastShowMidasUrlTsMs(proto.getLastShowMidasUrlTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerPaymentModel.Builder getCopySsBuilder() {
        final PlayerPaymentModel.Builder builder = PlayerPaymentModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerPaymentModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGenBalance() != 0L) {
            builder.setGenBalance(this.getGenBalance());
            fieldCnt++;
        }  else if (builder.hasGenBalance()) {
            // 清理GenBalance
            builder.clearGenBalance();
            fieldCnt++;
        }
        if (this.getFirstSave() != 0) {
            builder.setFirstSave(this.getFirstSave());
            fieldCnt++;
        }  else if (builder.hasFirstSave()) {
            // 清理FirstSave
            builder.clearFirstSave();
            fieldCnt++;
        }
        if (this.getSaveAmt() != 0L) {
            builder.setSaveAmt(this.getSaveAmt());
            fieldCnt++;
        }  else if (builder.hasSaveAmt()) {
            // 清理SaveAmt
            builder.clearSaveAmt();
            fieldCnt++;
        }
        if (this.getSaveSum() != 0L) {
            builder.setSaveSum(this.getSaveSum());
            fieldCnt++;
        }  else if (builder.hasSaveSum()) {
            // 清理SaveSum
            builder.clearSaveSum();
            fieldCnt++;
        }
        if (this.getCostSum() != 0L) {
            builder.setCostSum(this.getCostSum());
            fieldCnt++;
        }  else if (builder.hasCostSum()) {
            // 清理CostSum
            builder.clearCostSum();
            fieldCnt++;
        }
        if (this.getPresentSum() != 0L) {
            builder.setPresentSum(this.getPresentSum());
            fieldCnt++;
        }  else if (builder.hasPresentSum()) {
            // 清理PresentSum
            builder.clearPresentSum();
            fieldCnt++;
        }
        if (this.goodsOrders != null) {
            Player.StringPlayerGoodsOrderMap.Builder tmpBuilder = Player.StringPlayerGoodsOrderMap.newBuilder();
            final int tmpFieldCnt = this.goodsOrders.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGoodsOrders(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGoodsOrders();
            }
        }  else if (builder.hasGoodsOrders()) {
            // 清理GoodsOrders
            builder.clearGoodsOrders();
            fieldCnt++;
        }
        if (this.goodsHistory != null) {
            Player.Int32PlayerGoodsHistoryMap.Builder tmpBuilder = Player.Int32PlayerGoodsHistoryMap.newBuilder();
            final int tmpFieldCnt = this.goodsHistory.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGoodsHistory(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGoodsHistory();
            }
        }  else if (builder.hasGoodsHistory()) {
            // 清理GoodsHistory
            builder.clearGoodsHistory();
            fieldCnt++;
        }
        if (this.doneGoodsOrders != null) {
            Player.StringPlayerGoodsInfoMap.Builder tmpBuilder = Player.StringPlayerGoodsInfoMap.newBuilder();
            final int tmpFieldCnt = this.doneGoodsOrders.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDoneGoodsOrders(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDoneGoodsOrders();
            }
        }  else if (builder.hasDoneGoodsOrders()) {
            // 清理DoneGoodsOrders
            builder.clearDoneGoodsOrders();
            fieldCnt++;
        }
        if (this.getLastShowMidasUrlTsMs() != 0L) {
            builder.setLastShowMidasUrlTsMs(this.getLastShowMidasUrlTsMs());
            fieldCnt++;
        }  else if (builder.hasLastShowMidasUrlTsMs()) {
            // 清理LastShowMidasUrlTsMs
            builder.clearLastShowMidasUrlTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerPaymentModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GEN_BALANCE)) {
            builder.setGenBalance(this.getGenBalance());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FIRST_SAVE)) {
            builder.setFirstSave(this.getFirstSave());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SAVE_AMT)) {
            builder.setSaveAmt(this.getSaveAmt());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SAVE_SUM)) {
            builder.setSaveSum(this.getSaveSum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COST_SUM)) {
            builder.setCostSum(this.getCostSum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PRESENT_SUM)) {
            builder.setPresentSum(this.getPresentSum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GOODSORDERS) && this.goodsOrders != null) {
            final boolean needClear = !builder.hasGoodsOrders();
            final int tmpFieldCnt = this.goodsOrders.copyChangeToSs(builder.getGoodsOrdersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGoodsOrders();
            }
        }
        if (this.hasMark(FIELD_INDEX_GOODSHISTORY) && this.goodsHistory != null) {
            final boolean needClear = !builder.hasGoodsHistory();
            final int tmpFieldCnt = this.goodsHistory.copyChangeToSs(builder.getGoodsHistoryBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGoodsHistory();
            }
        }
        if (this.hasMark(FIELD_INDEX_DONEGOODSORDERS) && this.doneGoodsOrders != null) {
            final boolean needClear = !builder.hasDoneGoodsOrders();
            final int tmpFieldCnt = this.doneGoodsOrders.copyChangeToSs(builder.getDoneGoodsOrdersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDoneGoodsOrders();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTSHOWMIDASURLTSMS)) {
            builder.setLastShowMidasUrlTsMs(this.getLastShowMidasUrlTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerPaymentModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGenBalance()) {
            this.innerSetGenBalance(proto.getGenBalance());
        } else {
            this.innerSetGenBalance(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasFirstSave()) {
            this.innerSetFirstSave(proto.getFirstSave());
        } else {
            this.innerSetFirstSave(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSaveAmt()) {
            this.innerSetSaveAmt(proto.getSaveAmt());
        } else {
            this.innerSetSaveAmt(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSaveSum()) {
            this.innerSetSaveSum(proto.getSaveSum());
        } else {
            this.innerSetSaveSum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCostSum()) {
            this.innerSetCostSum(proto.getCostSum());
        } else {
            this.innerSetCostSum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPresentSum()) {
            this.innerSetPresentSum(proto.getPresentSum());
        } else {
            this.innerSetPresentSum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasGoodsOrders()) {
            this.getGoodsOrders().mergeFromSs(proto.getGoodsOrders());
        } else {
            if (this.goodsOrders != null) {
                this.goodsOrders.mergeFromSs(proto.getGoodsOrders());
            }
        }
        if (proto.hasGoodsHistory()) {
            this.getGoodsHistory().mergeFromSs(proto.getGoodsHistory());
        } else {
            if (this.goodsHistory != null) {
                this.goodsHistory.mergeFromSs(proto.getGoodsHistory());
            }
        }
        if (proto.hasDoneGoodsOrders()) {
            this.getDoneGoodsOrders().mergeFromSs(proto.getDoneGoodsOrders());
        } else {
            if (this.doneGoodsOrders != null) {
                this.doneGoodsOrders.mergeFromSs(proto.getDoneGoodsOrders());
            }
        }
        if (proto.hasLastShowMidasUrlTsMs()) {
            this.innerSetLastShowMidasUrlTsMs(proto.getLastShowMidasUrlTsMs());
        } else {
            this.innerSetLastShowMidasUrlTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerPaymentModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerPaymentModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGenBalance()) {
            this.setGenBalance(proto.getGenBalance());
            fieldCnt++;
        }
        if (proto.hasFirstSave()) {
            this.setFirstSave(proto.getFirstSave());
            fieldCnt++;
        }
        if (proto.hasSaveAmt()) {
            this.setSaveAmt(proto.getSaveAmt());
            fieldCnt++;
        }
        if (proto.hasSaveSum()) {
            this.setSaveSum(proto.getSaveSum());
            fieldCnt++;
        }
        if (proto.hasCostSum()) {
            this.setCostSum(proto.getCostSum());
            fieldCnt++;
        }
        if (proto.hasPresentSum()) {
            this.setPresentSum(proto.getPresentSum());
            fieldCnt++;
        }
        if (proto.hasGoodsOrders()) {
            this.getGoodsOrders().mergeChangeFromSs(proto.getGoodsOrders());
            fieldCnt++;
        }
        if (proto.hasGoodsHistory()) {
            this.getGoodsHistory().mergeChangeFromSs(proto.getGoodsHistory());
            fieldCnt++;
        }
        if (proto.hasDoneGoodsOrders()) {
            this.getDoneGoodsOrders().mergeChangeFromSs(proto.getDoneGoodsOrders());
            fieldCnt++;
        }
        if (proto.hasLastShowMidasUrlTsMs()) {
            this.setLastShowMidasUrlTsMs(proto.getLastShowMidasUrlTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerPaymentModel.Builder builder = PlayerPaymentModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_GOODSORDERS) && this.goodsOrders != null) {
            this.goodsOrders.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_GOODSHISTORY) && this.goodsHistory != null) {
            this.goodsHistory.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DONEGOODSORDERS) && this.doneGoodsOrders != null) {
            this.doneGoodsOrders.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.goodsOrders != null) {
            this.goodsOrders.markAll();
        }
        if (this.goodsHistory != null) {
            this.goodsHistory.markAll();
        }
        if (this.doneGoodsOrders != null) {
            this.doneGoodsOrders.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerPaymentModelProp)) {
            return false;
        }
        final PlayerPaymentModelProp otherNode = (PlayerPaymentModelProp) node;
        if (this.gen_balance != otherNode.gen_balance) {
            return false;
        }
        if (this.first_save != otherNode.first_save) {
            return false;
        }
        if (this.save_amt != otherNode.save_amt) {
            return false;
        }
        if (this.save_sum != otherNode.save_sum) {
            return false;
        }
        if (this.cost_sum != otherNode.cost_sum) {
            return false;
        }
        if (this.present_sum != otherNode.present_sum) {
            return false;
        }
        if (!this.getGoodsOrders().compareDataTo(otherNode.getGoodsOrders())) {
            return false;
        }
        if (!this.getGoodsHistory().compareDataTo(otherNode.getGoodsHistory())) {
            return false;
        }
        if (!this.getDoneGoodsOrders().compareDataTo(otherNode.getDoneGoodsOrders())) {
            return false;
        }
        if (this.lastShowMidasUrlTsMs != otherNode.lastShowMidasUrlTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 54;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Struct.Activity;
import com.yorha.proto.Basic;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ActivityPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_STARTTSSEC = 1;
    public static final int FIELD_INDEX_ENDTSSEC = 2;
    public static final int FIELD_INDEX_STATUS = 3;
    public static final int FIELD_INDEX_STATUSENTERTSSEC = 4;
    public static final int FIELD_INDEX_CHILDACTIVITIES = 5;
    public static final int FIELD_INDEX_MAXUNITID = 6;
    public static final int FIELD_INDEX_CLOSEDCHILDACTIVITIES = 7;
    public static final int FIELD_INDEX_SPECUNIT = 8;
    public static final int FIELD_INDEX_COMMONTASKUNIT = 9;
    public static final int FIELD_INDEX_COMMONSCOREREWARDUNIT = 10;
    public static final int FIELD_INDEX_TIMERREWARDUNIT = 11;
    public static final int FIELD_INDEX_CHARGEGOODSCHAINUNIT = 12;
    public static final int FIELD_INDEX_COMMONSTOREUNIT = 13;
    public static final int FIELD_INDEX_SCORERANKUNIT = 14;

    public static final int FIELD_COUNT = 15;

    private long markBits0 = 0L;

    private int id = Constant.DEFAULT_INT_VALUE;
    private int startTsSec = Constant.DEFAULT_INT_VALUE;
    private int endTsSec = Constant.DEFAULT_INT_VALUE;
    private ActivityStatus status = ActivityStatus.forNumber(0);
    private int statusEnterTsSec = Constant.DEFAULT_INT_VALUE;
    private Int32ActivityMapProp childActivities = null;
    private int maxUnitId = Constant.DEFAULT_INT_VALUE;
    private Int32SetProp closedChildActivities = null;
    private ActivityUnitProp specUnit = null;
    private ActivityUnitProp commonTaskUnit = null;
    private ActivityUnitProp commonScoreRewardUnit = null;
    private ActivityUnitProp timerRewardUnit = null;
    private ActivityUnitProp chargeGoodsChainUnit = null;
    private ActivityUnitProp commonStoreUnit = null;
    private ActivityUnitProp scoreRankUnit = null;

    public ActivityProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public int getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public ActivityProp setId(int id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(int id) {
        this.id = id;
    }

    /**
     * get startTsSec
     *
     * @return startTsSec value
     */
    public int getStartTsSec() {
        return this.startTsSec;
    }

    /**
     * set startTsSec && set marked
     *
     * @param startTsSec new value
     * @return current object
     */
    public ActivityProp setStartTsSec(int startTsSec) {
        if (this.startTsSec != startTsSec) {
            this.mark(FIELD_INDEX_STARTTSSEC);
            this.startTsSec = startTsSec;
        }
        return this;
    }

    /**
     * inner set startTsSec
     *
     * @param startTsSec new value
     */
    private void innerSetStartTsSec(int startTsSec) {
        this.startTsSec = startTsSec;
    }

    /**
     * get endTsSec
     *
     * @return endTsSec value
     */
    public int getEndTsSec() {
        return this.endTsSec;
    }

    /**
     * set endTsSec && set marked
     *
     * @param endTsSec new value
     * @return current object
     */
    public ActivityProp setEndTsSec(int endTsSec) {
        if (this.endTsSec != endTsSec) {
            this.mark(FIELD_INDEX_ENDTSSEC);
            this.endTsSec = endTsSec;
        }
        return this;
    }

    /**
     * inner set endTsSec
     *
     * @param endTsSec new value
     */
    private void innerSetEndTsSec(int endTsSec) {
        this.endTsSec = endTsSec;
    }

    /**
     * get status
     *
     * @return status value
     */
    public ActivityStatus getStatus() {
        return this.status;
    }

    /**
     * set status && set marked
     *
     * @param status new value
     * @return current object
     */
    public ActivityProp setStatus(ActivityStatus status) {
        if (status == null) {
            throw new NullPointerException();
        }
        if (this.status != status) {
            this.mark(FIELD_INDEX_STATUS);
            this.status = status;
        }
        return this;
    }

    /**
     * inner set status
     *
     * @param status new value
     */
    private void innerSetStatus(ActivityStatus status) {
        this.status = status;
    }

    /**
     * get statusEnterTsSec
     *
     * @return statusEnterTsSec value
     */
    public int getStatusEnterTsSec() {
        return this.statusEnterTsSec;
    }

    /**
     * set statusEnterTsSec && set marked
     *
     * @param statusEnterTsSec new value
     * @return current object
     */
    public ActivityProp setStatusEnterTsSec(int statusEnterTsSec) {
        if (this.statusEnterTsSec != statusEnterTsSec) {
            this.mark(FIELD_INDEX_STATUSENTERTSSEC);
            this.statusEnterTsSec = statusEnterTsSec;
        }
        return this;
    }

    /**
     * inner set statusEnterTsSec
     *
     * @param statusEnterTsSec new value
     */
    private void innerSetStatusEnterTsSec(int statusEnterTsSec) {
        this.statusEnterTsSec = statusEnterTsSec;
    }

    /**
     * get childActivities
     *
     * @return childActivities value
     */
    public Int32ActivityMapProp getChildActivities() {
        if (this.childActivities == null) {
            this.childActivities = new Int32ActivityMapProp(this, FIELD_INDEX_CHILDACTIVITIES);
        }
        return this.childActivities;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putChildActivitiesV(ActivityProp v) {
        this.getChildActivities().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ActivityProp addEmptyChildActivities(Integer k) {
        return this.getChildActivities().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getChildActivitiesSize() {
        if (this.childActivities == null) {
            return 0;
        }
        return this.childActivities.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isChildActivitiesEmpty() {
        if (this.childActivities == null) {
            return true;
        }
        return this.childActivities.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ActivityProp getChildActivitiesV(Integer k) {
        if (this.childActivities == null || !this.childActivities.containsKey(k)) {
            return null;
        }
        return this.childActivities.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearChildActivities() {
        if (this.childActivities != null) {
            this.childActivities.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeChildActivitiesV(Integer k) {
        if (this.childActivities != null) {
            this.childActivities.remove(k);
        }
    }
    /**
     * get maxUnitId
     *
     * @return maxUnitId value
     */
    public int getMaxUnitId() {
        return this.maxUnitId;
    }

    /**
     * set maxUnitId && set marked
     *
     * @param maxUnitId new value
     * @return current object
     */
    public ActivityProp setMaxUnitId(int maxUnitId) {
        if (this.maxUnitId != maxUnitId) {
            this.mark(FIELD_INDEX_MAXUNITID);
            this.maxUnitId = maxUnitId;
        }
        return this;
    }

    /**
     * inner set maxUnitId
     *
     * @param maxUnitId new value
     */
    private void innerSetMaxUnitId(int maxUnitId) {
        this.maxUnitId = maxUnitId;
    }

    /**
     * get closedChildActivities
     *
     * @return closedChildActivities value
     */
    public Int32SetProp getClosedChildActivities() {
        if (this.closedChildActivities == null) {
            this.closedChildActivities = new Int32SetProp(this, FIELD_INDEX_CLOSEDCHILDACTIVITIES);
        }
        return this.closedChildActivities;
    }


    /**
     * add value to set
     *
     * @param e value
     */
    public void addClosedChildActivities(Integer e) {
        this.getClosedChildActivities().add(e);
    }

    /**
     * remove the specified element in set
     *
     * @param e element
     * @return e if success or null by fail
     */
    public Integer removeClosedChildActivities(Integer e) {
        if (this.closedChildActivities == null) {
            return null;
        }
        if(this.closedChildActivities.remove(e)) {
            return e;
        }
        return null;
    }

    /**
     * get set size
     *
     * @return set size
     */
    public int getClosedChildActivitiesSize() {
        if (this.closedChildActivities == null) {
            return 0;
        }
        return this.closedChildActivities.size();
    }

    /**
     * get is a empty set
     *
     * @return true if empty
     */
    public boolean isClosedChildActivitiesEmpty() {
        if (this.closedChildActivities == null) {
            return true;
        }
        return this.getClosedChildActivities().isEmpty();
    }

    /**
     * clear set
     */
    public void clearClosedChildActivities() {
        this.getClosedChildActivities().clear();
    }

    /**
     * Returns true if this set contains the specified element.
     *
     * @param e elem
     * @return true if this set contains the specified element, false otherwise
     */
    public boolean isClosedChildActivitiesContains(Integer e) {
        return this.closedChildActivities != null && this.closedChildActivities.contains(e);
    }

    /**
     * get specUnit
     *
     * @return specUnit value
     */
    public ActivityUnitProp getSpecUnit() {
        if (this.specUnit == null) {
            this.specUnit = new ActivityUnitProp(this, FIELD_INDEX_SPECUNIT);
        }
        return this.specUnit;
    }

    /**
     * get commonTaskUnit
     *
     * @return commonTaskUnit value
     */
    public ActivityUnitProp getCommonTaskUnit() {
        if (this.commonTaskUnit == null) {
            this.commonTaskUnit = new ActivityUnitProp(this, FIELD_INDEX_COMMONTASKUNIT);
        }
        return this.commonTaskUnit;
    }

    /**
     * get commonScoreRewardUnit
     *
     * @return commonScoreRewardUnit value
     */
    public ActivityUnitProp getCommonScoreRewardUnit() {
        if (this.commonScoreRewardUnit == null) {
            this.commonScoreRewardUnit = new ActivityUnitProp(this, FIELD_INDEX_COMMONSCOREREWARDUNIT);
        }
        return this.commonScoreRewardUnit;
    }

    /**
     * get timerRewardUnit
     *
     * @return timerRewardUnit value
     */
    public ActivityUnitProp getTimerRewardUnit() {
        if (this.timerRewardUnit == null) {
            this.timerRewardUnit = new ActivityUnitProp(this, FIELD_INDEX_TIMERREWARDUNIT);
        }
        return this.timerRewardUnit;
    }

    /**
     * get chargeGoodsChainUnit
     *
     * @return chargeGoodsChainUnit value
     */
    public ActivityUnitProp getChargeGoodsChainUnit() {
        if (this.chargeGoodsChainUnit == null) {
            this.chargeGoodsChainUnit = new ActivityUnitProp(this, FIELD_INDEX_CHARGEGOODSCHAINUNIT);
        }
        return this.chargeGoodsChainUnit;
    }

    /**
     * get commonStoreUnit
     *
     * @return commonStoreUnit value
     */
    public ActivityUnitProp getCommonStoreUnit() {
        if (this.commonStoreUnit == null) {
            this.commonStoreUnit = new ActivityUnitProp(this, FIELD_INDEX_COMMONSTOREUNIT);
        }
        return this.commonStoreUnit;
    }

    /**
     * get scoreRankUnit
     *
     * @return scoreRankUnit value
     */
    public ActivityUnitProp getScoreRankUnit() {
        if (this.scoreRankUnit == null) {
            this.scoreRankUnit = new ActivityUnitProp(this, FIELD_INDEX_SCORERANKUNIT);
        }
        return this.scoreRankUnit;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityPB.Builder getCopyCsBuilder() {
        final ActivityPB.Builder builder = ActivityPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getStartTsSec() != 0) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }  else if (builder.hasStartTsSec()) {
            // 清理StartTsSec
            builder.clearStartTsSec();
            fieldCnt++;
        }
        if (this.getEndTsSec() != 0) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }  else if (builder.hasEndTsSec()) {
            // 清理EndTsSec
            builder.clearEndTsSec();
            fieldCnt++;
        }
        if (this.getStatus() != ActivityStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.getStatusEnterTsSec() != 0) {
            builder.setStatusEnterTsSec(this.getStatusEnterTsSec());
            fieldCnt++;
        }  else if (builder.hasStatusEnterTsSec()) {
            // 清理StatusEnterTsSec
            builder.clearStatusEnterTsSec();
            fieldCnt++;
        }
        if (this.childActivities != null) {
            StructPB.Int32ActivityMapPB.Builder tmpBuilder = StructPB.Int32ActivityMapPB.newBuilder();
            final int tmpFieldCnt = this.childActivities.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChildActivities(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChildActivities();
            }
        }  else if (builder.hasChildActivities()) {
            // 清理ChildActivities
            builder.clearChildActivities();
            fieldCnt++;
        }
        if (this.specUnit != null) {
            StructPB.ActivityUnitPB.Builder tmpBuilder = StructPB.ActivityUnitPB.newBuilder();
            final int tmpFieldCnt = this.specUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpecUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpecUnit();
            }
        }  else if (builder.hasSpecUnit()) {
            // 清理SpecUnit
            builder.clearSpecUnit();
            fieldCnt++;
        }
        if (this.commonTaskUnit != null) {
            StructPB.ActivityUnitPB.Builder tmpBuilder = StructPB.ActivityUnitPB.newBuilder();
            final int tmpFieldCnt = this.commonTaskUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCommonTaskUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCommonTaskUnit();
            }
        }  else if (builder.hasCommonTaskUnit()) {
            // 清理CommonTaskUnit
            builder.clearCommonTaskUnit();
            fieldCnt++;
        }
        if (this.commonScoreRewardUnit != null) {
            StructPB.ActivityUnitPB.Builder tmpBuilder = StructPB.ActivityUnitPB.newBuilder();
            final int tmpFieldCnt = this.commonScoreRewardUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCommonScoreRewardUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCommonScoreRewardUnit();
            }
        }  else if (builder.hasCommonScoreRewardUnit()) {
            // 清理CommonScoreRewardUnit
            builder.clearCommonScoreRewardUnit();
            fieldCnt++;
        }
        if (this.timerRewardUnit != null) {
            StructPB.ActivityUnitPB.Builder tmpBuilder = StructPB.ActivityUnitPB.newBuilder();
            final int tmpFieldCnt = this.timerRewardUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTimerRewardUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTimerRewardUnit();
            }
        }  else if (builder.hasTimerRewardUnit()) {
            // 清理TimerRewardUnit
            builder.clearTimerRewardUnit();
            fieldCnt++;
        }
        if (this.chargeGoodsChainUnit != null) {
            StructPB.ActivityUnitPB.Builder tmpBuilder = StructPB.ActivityUnitPB.newBuilder();
            final int tmpFieldCnt = this.chargeGoodsChainUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChargeGoodsChainUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChargeGoodsChainUnit();
            }
        }  else if (builder.hasChargeGoodsChainUnit()) {
            // 清理ChargeGoodsChainUnit
            builder.clearChargeGoodsChainUnit();
            fieldCnt++;
        }
        if (this.commonStoreUnit != null) {
            StructPB.ActivityUnitPB.Builder tmpBuilder = StructPB.ActivityUnitPB.newBuilder();
            final int tmpFieldCnt = this.commonStoreUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCommonStoreUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCommonStoreUnit();
            }
        }  else if (builder.hasCommonStoreUnit()) {
            // 清理CommonStoreUnit
            builder.clearCommonStoreUnit();
            fieldCnt++;
        }
        if (this.scoreRankUnit != null) {
            StructPB.ActivityUnitPB.Builder tmpBuilder = StructPB.ActivityUnitPB.newBuilder();
            final int tmpFieldCnt = this.scoreRankUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScoreRankUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScoreRankUnit();
            }
        }  else if (builder.hasScoreRankUnit()) {
            // 清理ScoreRankUnit
            builder.clearScoreRankUnit();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSSEC)) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSSEC)) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUSENTERTSSEC)) {
            builder.setStatusEnterTsSec(this.getStatusEnterTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CHILDACTIVITIES) && this.childActivities != null) {
            final boolean needClear = !builder.hasChildActivities();
            final int tmpFieldCnt = this.childActivities.copyChangeToCs(builder.getChildActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChildActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPECUNIT) && this.specUnit != null) {
            final boolean needClear = !builder.hasSpecUnit();
            final int tmpFieldCnt = this.specUnit.copyChangeToCs(builder.getSpecUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpecUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_COMMONTASKUNIT) && this.commonTaskUnit != null) {
            final boolean needClear = !builder.hasCommonTaskUnit();
            final int tmpFieldCnt = this.commonTaskUnit.copyChangeToCs(builder.getCommonTaskUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCommonTaskUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_COMMONSCOREREWARDUNIT) && this.commonScoreRewardUnit != null) {
            final boolean needClear = !builder.hasCommonScoreRewardUnit();
            final int tmpFieldCnt = this.commonScoreRewardUnit.copyChangeToCs(builder.getCommonScoreRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCommonScoreRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_TIMERREWARDUNIT) && this.timerRewardUnit != null) {
            final boolean needClear = !builder.hasTimerRewardUnit();
            final int tmpFieldCnt = this.timerRewardUnit.copyChangeToCs(builder.getTimerRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTimerRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CHARGEGOODSCHAINUNIT) && this.chargeGoodsChainUnit != null) {
            final boolean needClear = !builder.hasChargeGoodsChainUnit();
            final int tmpFieldCnt = this.chargeGoodsChainUnit.copyChangeToCs(builder.getChargeGoodsChainUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChargeGoodsChainUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_COMMONSTOREUNIT) && this.commonStoreUnit != null) {
            final boolean needClear = !builder.hasCommonStoreUnit();
            final int tmpFieldCnt = this.commonStoreUnit.copyChangeToCs(builder.getCommonStoreUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCommonStoreUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCORERANKUNIT) && this.scoreRankUnit != null) {
            final boolean needClear = !builder.hasScoreRankUnit();
            final int tmpFieldCnt = this.scoreRankUnit.copyChangeToCs(builder.getScoreRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreRankUnit();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSSEC)) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSSEC)) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUSENTERTSSEC)) {
            builder.setStatusEnterTsSec(this.getStatusEnterTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CHILDACTIVITIES) && this.childActivities != null) {
            final boolean needClear = !builder.hasChildActivities();
            final int tmpFieldCnt = this.childActivities.copyChangeToAndClearDeleteKeysCs(builder.getChildActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChildActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPECUNIT) && this.specUnit != null) {
            final boolean needClear = !builder.hasSpecUnit();
            final int tmpFieldCnt = this.specUnit.copyChangeToAndClearDeleteKeysCs(builder.getSpecUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpecUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_COMMONTASKUNIT) && this.commonTaskUnit != null) {
            final boolean needClear = !builder.hasCommonTaskUnit();
            final int tmpFieldCnt = this.commonTaskUnit.copyChangeToAndClearDeleteKeysCs(builder.getCommonTaskUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCommonTaskUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_COMMONSCOREREWARDUNIT) && this.commonScoreRewardUnit != null) {
            final boolean needClear = !builder.hasCommonScoreRewardUnit();
            final int tmpFieldCnt = this.commonScoreRewardUnit.copyChangeToAndClearDeleteKeysCs(builder.getCommonScoreRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCommonScoreRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_TIMERREWARDUNIT) && this.timerRewardUnit != null) {
            final boolean needClear = !builder.hasTimerRewardUnit();
            final int tmpFieldCnt = this.timerRewardUnit.copyChangeToAndClearDeleteKeysCs(builder.getTimerRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTimerRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CHARGEGOODSCHAINUNIT) && this.chargeGoodsChainUnit != null) {
            final boolean needClear = !builder.hasChargeGoodsChainUnit();
            final int tmpFieldCnt = this.chargeGoodsChainUnit.copyChangeToAndClearDeleteKeysCs(builder.getChargeGoodsChainUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChargeGoodsChainUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_COMMONSTOREUNIT) && this.commonStoreUnit != null) {
            final boolean needClear = !builder.hasCommonStoreUnit();
            final int tmpFieldCnt = this.commonStoreUnit.copyChangeToAndClearDeleteKeysCs(builder.getCommonStoreUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCommonStoreUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCORERANKUNIT) && this.scoreRankUnit != null) {
            final boolean needClear = !builder.hasScoreRankUnit();
            final int tmpFieldCnt = this.scoreRankUnit.copyChangeToAndClearDeleteKeysCs(builder.getScoreRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreRankUnit();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartTsSec()) {
            this.innerSetStartTsSec(proto.getStartTsSec());
        } else {
            this.innerSetStartTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEndTsSec()) {
            this.innerSetEndTsSec(proto.getEndTsSec());
        } else {
            this.innerSetEndTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(ActivityStatus.forNumber(0));
        }
        if (proto.hasStatusEnterTsSec()) {
            this.innerSetStatusEnterTsSec(proto.getStatusEnterTsSec());
        } else {
            this.innerSetStatusEnterTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasChildActivities()) {
            this.getChildActivities().mergeFromCs(proto.getChildActivities());
        } else {
            if (this.childActivities != null) {
                this.childActivities.mergeFromCs(proto.getChildActivities());
            }
        }
        if (proto.hasSpecUnit()) {
            this.getSpecUnit().mergeFromCs(proto.getSpecUnit());
        } else {
            if (this.specUnit != null) {
                this.specUnit.mergeFromCs(proto.getSpecUnit());
            }
        }
        if (proto.hasCommonTaskUnit()) {
            this.getCommonTaskUnit().mergeFromCs(proto.getCommonTaskUnit());
        } else {
            if (this.commonTaskUnit != null) {
                this.commonTaskUnit.mergeFromCs(proto.getCommonTaskUnit());
            }
        }
        if (proto.hasCommonScoreRewardUnit()) {
            this.getCommonScoreRewardUnit().mergeFromCs(proto.getCommonScoreRewardUnit());
        } else {
            if (this.commonScoreRewardUnit != null) {
                this.commonScoreRewardUnit.mergeFromCs(proto.getCommonScoreRewardUnit());
            }
        }
        if (proto.hasTimerRewardUnit()) {
            this.getTimerRewardUnit().mergeFromCs(proto.getTimerRewardUnit());
        } else {
            if (this.timerRewardUnit != null) {
                this.timerRewardUnit.mergeFromCs(proto.getTimerRewardUnit());
            }
        }
        if (proto.hasChargeGoodsChainUnit()) {
            this.getChargeGoodsChainUnit().mergeFromCs(proto.getChargeGoodsChainUnit());
        } else {
            if (this.chargeGoodsChainUnit != null) {
                this.chargeGoodsChainUnit.mergeFromCs(proto.getChargeGoodsChainUnit());
            }
        }
        if (proto.hasCommonStoreUnit()) {
            this.getCommonStoreUnit().mergeFromCs(proto.getCommonStoreUnit());
        } else {
            if (this.commonStoreUnit != null) {
                this.commonStoreUnit.mergeFromCs(proto.getCommonStoreUnit());
            }
        }
        if (proto.hasScoreRankUnit()) {
            this.getScoreRankUnit().mergeFromCs(proto.getScoreRankUnit());
        } else {
            if (this.scoreRankUnit != null) {
                this.scoreRankUnit.mergeFromCs(proto.getScoreRankUnit());
            }
        }
        this.markAll();
        return ActivityProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasStartTsSec()) {
            this.setStartTsSec(proto.getStartTsSec());
            fieldCnt++;
        }
        if (proto.hasEndTsSec()) {
            this.setEndTsSec(proto.getEndTsSec());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasStatusEnterTsSec()) {
            this.setStatusEnterTsSec(proto.getStatusEnterTsSec());
            fieldCnt++;
        }
        if (proto.hasChildActivities()) {
            this.getChildActivities().mergeChangeFromCs(proto.getChildActivities());
            fieldCnt++;
        }
        if (proto.hasSpecUnit()) {
            this.getSpecUnit().mergeChangeFromCs(proto.getSpecUnit());
            fieldCnt++;
        }
        if (proto.hasCommonTaskUnit()) {
            this.getCommonTaskUnit().mergeChangeFromCs(proto.getCommonTaskUnit());
            fieldCnt++;
        }
        if (proto.hasCommonScoreRewardUnit()) {
            this.getCommonScoreRewardUnit().mergeChangeFromCs(proto.getCommonScoreRewardUnit());
            fieldCnt++;
        }
        if (proto.hasTimerRewardUnit()) {
            this.getTimerRewardUnit().mergeChangeFromCs(proto.getTimerRewardUnit());
            fieldCnt++;
        }
        if (proto.hasChargeGoodsChainUnit()) {
            this.getChargeGoodsChainUnit().mergeChangeFromCs(proto.getChargeGoodsChainUnit());
            fieldCnt++;
        }
        if (proto.hasCommonStoreUnit()) {
            this.getCommonStoreUnit().mergeChangeFromCs(proto.getCommonStoreUnit());
            fieldCnt++;
        }
        if (proto.hasScoreRankUnit()) {
            this.getScoreRankUnit().mergeChangeFromCs(proto.getScoreRankUnit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Activity.Builder getCopyDbBuilder() {
        final Activity.Builder builder = Activity.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(Activity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getStartTsSec() != 0) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }  else if (builder.hasStartTsSec()) {
            // 清理StartTsSec
            builder.clearStartTsSec();
            fieldCnt++;
        }
        if (this.getEndTsSec() != 0) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }  else if (builder.hasEndTsSec()) {
            // 清理EndTsSec
            builder.clearEndTsSec();
            fieldCnt++;
        }
        if (this.getStatus() != ActivityStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.getStatusEnterTsSec() != 0) {
            builder.setStatusEnterTsSec(this.getStatusEnterTsSec());
            fieldCnt++;
        }  else if (builder.hasStatusEnterTsSec()) {
            // 清理StatusEnterTsSec
            builder.clearStatusEnterTsSec();
            fieldCnt++;
        }
        if (this.childActivities != null) {
            Struct.Int32ActivityMap.Builder tmpBuilder = Struct.Int32ActivityMap.newBuilder();
            final int tmpFieldCnt = this.childActivities.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChildActivities(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChildActivities();
            }
        }  else if (builder.hasChildActivities()) {
            // 清理ChildActivities
            builder.clearChildActivities();
            fieldCnt++;
        }
        if (this.getMaxUnitId() != 0) {
            builder.setMaxUnitId(this.getMaxUnitId());
            fieldCnt++;
        }  else if (builder.hasMaxUnitId()) {
            // 清理MaxUnitId
            builder.clearMaxUnitId();
            fieldCnt++;
        }
        if (this.closedChildActivities != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.closedChildActivities.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClosedChildActivities(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClosedChildActivities();
            }
        }  else if (builder.hasClosedChildActivities()) {
            // 清理ClosedChildActivities
            builder.clearClosedChildActivities();
            fieldCnt++;
        }
        if (this.specUnit != null) {
            Struct.ActivityUnit.Builder tmpBuilder = Struct.ActivityUnit.newBuilder();
            final int tmpFieldCnt = this.specUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpecUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpecUnit();
            }
        }  else if (builder.hasSpecUnit()) {
            // 清理SpecUnit
            builder.clearSpecUnit();
            fieldCnt++;
        }
        if (this.commonTaskUnit != null) {
            Struct.ActivityUnit.Builder tmpBuilder = Struct.ActivityUnit.newBuilder();
            final int tmpFieldCnt = this.commonTaskUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCommonTaskUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCommonTaskUnit();
            }
        }  else if (builder.hasCommonTaskUnit()) {
            // 清理CommonTaskUnit
            builder.clearCommonTaskUnit();
            fieldCnt++;
        }
        if (this.commonScoreRewardUnit != null) {
            Struct.ActivityUnit.Builder tmpBuilder = Struct.ActivityUnit.newBuilder();
            final int tmpFieldCnt = this.commonScoreRewardUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCommonScoreRewardUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCommonScoreRewardUnit();
            }
        }  else if (builder.hasCommonScoreRewardUnit()) {
            // 清理CommonScoreRewardUnit
            builder.clearCommonScoreRewardUnit();
            fieldCnt++;
        }
        if (this.timerRewardUnit != null) {
            Struct.ActivityUnit.Builder tmpBuilder = Struct.ActivityUnit.newBuilder();
            final int tmpFieldCnt = this.timerRewardUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTimerRewardUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTimerRewardUnit();
            }
        }  else if (builder.hasTimerRewardUnit()) {
            // 清理TimerRewardUnit
            builder.clearTimerRewardUnit();
            fieldCnt++;
        }
        if (this.chargeGoodsChainUnit != null) {
            Struct.ActivityUnit.Builder tmpBuilder = Struct.ActivityUnit.newBuilder();
            final int tmpFieldCnt = this.chargeGoodsChainUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChargeGoodsChainUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChargeGoodsChainUnit();
            }
        }  else if (builder.hasChargeGoodsChainUnit()) {
            // 清理ChargeGoodsChainUnit
            builder.clearChargeGoodsChainUnit();
            fieldCnt++;
        }
        if (this.commonStoreUnit != null) {
            Struct.ActivityUnit.Builder tmpBuilder = Struct.ActivityUnit.newBuilder();
            final int tmpFieldCnt = this.commonStoreUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCommonStoreUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCommonStoreUnit();
            }
        }  else if (builder.hasCommonStoreUnit()) {
            // 清理CommonStoreUnit
            builder.clearCommonStoreUnit();
            fieldCnt++;
        }
        if (this.scoreRankUnit != null) {
            Struct.ActivityUnit.Builder tmpBuilder = Struct.ActivityUnit.newBuilder();
            final int tmpFieldCnt = this.scoreRankUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScoreRankUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScoreRankUnit();
            }
        }  else if (builder.hasScoreRankUnit()) {
            // 清理ScoreRankUnit
            builder.clearScoreRankUnit();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(Activity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSSEC)) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSSEC)) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUSENTERTSSEC)) {
            builder.setStatusEnterTsSec(this.getStatusEnterTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CHILDACTIVITIES) && this.childActivities != null) {
            final boolean needClear = !builder.hasChildActivities();
            final int tmpFieldCnt = this.childActivities.copyChangeToDb(builder.getChildActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChildActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAXUNITID)) {
            builder.setMaxUnitId(this.getMaxUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLOSEDCHILDACTIVITIES) && this.closedChildActivities != null) {
            final boolean needClear = !builder.hasClosedChildActivities();
            final int tmpFieldCnt = this.closedChildActivities.copyChangeToDb(builder.getClosedChildActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClosedChildActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPECUNIT) && this.specUnit != null) {
            final boolean needClear = !builder.hasSpecUnit();
            final int tmpFieldCnt = this.specUnit.copyChangeToDb(builder.getSpecUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpecUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_COMMONTASKUNIT) && this.commonTaskUnit != null) {
            final boolean needClear = !builder.hasCommonTaskUnit();
            final int tmpFieldCnt = this.commonTaskUnit.copyChangeToDb(builder.getCommonTaskUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCommonTaskUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_COMMONSCOREREWARDUNIT) && this.commonScoreRewardUnit != null) {
            final boolean needClear = !builder.hasCommonScoreRewardUnit();
            final int tmpFieldCnt = this.commonScoreRewardUnit.copyChangeToDb(builder.getCommonScoreRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCommonScoreRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_TIMERREWARDUNIT) && this.timerRewardUnit != null) {
            final boolean needClear = !builder.hasTimerRewardUnit();
            final int tmpFieldCnt = this.timerRewardUnit.copyChangeToDb(builder.getTimerRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTimerRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CHARGEGOODSCHAINUNIT) && this.chargeGoodsChainUnit != null) {
            final boolean needClear = !builder.hasChargeGoodsChainUnit();
            final int tmpFieldCnt = this.chargeGoodsChainUnit.copyChangeToDb(builder.getChargeGoodsChainUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChargeGoodsChainUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_COMMONSTOREUNIT) && this.commonStoreUnit != null) {
            final boolean needClear = !builder.hasCommonStoreUnit();
            final int tmpFieldCnt = this.commonStoreUnit.copyChangeToDb(builder.getCommonStoreUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCommonStoreUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCORERANKUNIT) && this.scoreRankUnit != null) {
            final boolean needClear = !builder.hasScoreRankUnit();
            final int tmpFieldCnt = this.scoreRankUnit.copyChangeToDb(builder.getScoreRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreRankUnit();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(Activity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartTsSec()) {
            this.innerSetStartTsSec(proto.getStartTsSec());
        } else {
            this.innerSetStartTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEndTsSec()) {
            this.innerSetEndTsSec(proto.getEndTsSec());
        } else {
            this.innerSetEndTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(ActivityStatus.forNumber(0));
        }
        if (proto.hasStatusEnterTsSec()) {
            this.innerSetStatusEnterTsSec(proto.getStatusEnterTsSec());
        } else {
            this.innerSetStatusEnterTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasChildActivities()) {
            this.getChildActivities().mergeFromDb(proto.getChildActivities());
        } else {
            if (this.childActivities != null) {
                this.childActivities.mergeFromDb(proto.getChildActivities());
            }
        }
        if (proto.hasMaxUnitId()) {
            this.innerSetMaxUnitId(proto.getMaxUnitId());
        } else {
            this.innerSetMaxUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasClosedChildActivities()) {
            this.getClosedChildActivities().mergeFromDb(proto.getClosedChildActivities());
        } else {
            if (this.closedChildActivities != null) {
                this.closedChildActivities.mergeFromDb(proto.getClosedChildActivities());
            }
        }
        if (proto.hasSpecUnit()) {
            this.getSpecUnit().mergeFromDb(proto.getSpecUnit());
        } else {
            if (this.specUnit != null) {
                this.specUnit.mergeFromDb(proto.getSpecUnit());
            }
        }
        if (proto.hasCommonTaskUnit()) {
            this.getCommonTaskUnit().mergeFromDb(proto.getCommonTaskUnit());
        } else {
            if (this.commonTaskUnit != null) {
                this.commonTaskUnit.mergeFromDb(proto.getCommonTaskUnit());
            }
        }
        if (proto.hasCommonScoreRewardUnit()) {
            this.getCommonScoreRewardUnit().mergeFromDb(proto.getCommonScoreRewardUnit());
        } else {
            if (this.commonScoreRewardUnit != null) {
                this.commonScoreRewardUnit.mergeFromDb(proto.getCommonScoreRewardUnit());
            }
        }
        if (proto.hasTimerRewardUnit()) {
            this.getTimerRewardUnit().mergeFromDb(proto.getTimerRewardUnit());
        } else {
            if (this.timerRewardUnit != null) {
                this.timerRewardUnit.mergeFromDb(proto.getTimerRewardUnit());
            }
        }
        if (proto.hasChargeGoodsChainUnit()) {
            this.getChargeGoodsChainUnit().mergeFromDb(proto.getChargeGoodsChainUnit());
        } else {
            if (this.chargeGoodsChainUnit != null) {
                this.chargeGoodsChainUnit.mergeFromDb(proto.getChargeGoodsChainUnit());
            }
        }
        if (proto.hasCommonStoreUnit()) {
            this.getCommonStoreUnit().mergeFromDb(proto.getCommonStoreUnit());
        } else {
            if (this.commonStoreUnit != null) {
                this.commonStoreUnit.mergeFromDb(proto.getCommonStoreUnit());
            }
        }
        if (proto.hasScoreRankUnit()) {
            this.getScoreRankUnit().mergeFromDb(proto.getScoreRankUnit());
        } else {
            if (this.scoreRankUnit != null) {
                this.scoreRankUnit.mergeFromDb(proto.getScoreRankUnit());
            }
        }
        this.markAll();
        return ActivityProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(Activity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasStartTsSec()) {
            this.setStartTsSec(proto.getStartTsSec());
            fieldCnt++;
        }
        if (proto.hasEndTsSec()) {
            this.setEndTsSec(proto.getEndTsSec());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasStatusEnterTsSec()) {
            this.setStatusEnterTsSec(proto.getStatusEnterTsSec());
            fieldCnt++;
        }
        if (proto.hasChildActivities()) {
            this.getChildActivities().mergeChangeFromDb(proto.getChildActivities());
            fieldCnt++;
        }
        if (proto.hasMaxUnitId()) {
            this.setMaxUnitId(proto.getMaxUnitId());
            fieldCnt++;
        }
        if (proto.hasClosedChildActivities()) {
            this.getClosedChildActivities().mergeChangeFromDb(proto.getClosedChildActivities());
            fieldCnt++;
        }
        if (proto.hasSpecUnit()) {
            this.getSpecUnit().mergeChangeFromDb(proto.getSpecUnit());
            fieldCnt++;
        }
        if (proto.hasCommonTaskUnit()) {
            this.getCommonTaskUnit().mergeChangeFromDb(proto.getCommonTaskUnit());
            fieldCnt++;
        }
        if (proto.hasCommonScoreRewardUnit()) {
            this.getCommonScoreRewardUnit().mergeChangeFromDb(proto.getCommonScoreRewardUnit());
            fieldCnt++;
        }
        if (proto.hasTimerRewardUnit()) {
            this.getTimerRewardUnit().mergeChangeFromDb(proto.getTimerRewardUnit());
            fieldCnt++;
        }
        if (proto.hasChargeGoodsChainUnit()) {
            this.getChargeGoodsChainUnit().mergeChangeFromDb(proto.getChargeGoodsChainUnit());
            fieldCnt++;
        }
        if (proto.hasCommonStoreUnit()) {
            this.getCommonStoreUnit().mergeChangeFromDb(proto.getCommonStoreUnit());
            fieldCnt++;
        }
        if (proto.hasScoreRankUnit()) {
            this.getScoreRankUnit().mergeChangeFromDb(proto.getScoreRankUnit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Activity.Builder getCopySsBuilder() {
        final Activity.Builder builder = Activity.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(Activity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getStartTsSec() != 0) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }  else if (builder.hasStartTsSec()) {
            // 清理StartTsSec
            builder.clearStartTsSec();
            fieldCnt++;
        }
        if (this.getEndTsSec() != 0) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }  else if (builder.hasEndTsSec()) {
            // 清理EndTsSec
            builder.clearEndTsSec();
            fieldCnt++;
        }
        if (this.getStatus() != ActivityStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.getStatusEnterTsSec() != 0) {
            builder.setStatusEnterTsSec(this.getStatusEnterTsSec());
            fieldCnt++;
        }  else if (builder.hasStatusEnterTsSec()) {
            // 清理StatusEnterTsSec
            builder.clearStatusEnterTsSec();
            fieldCnt++;
        }
        if (this.childActivities != null) {
            Struct.Int32ActivityMap.Builder tmpBuilder = Struct.Int32ActivityMap.newBuilder();
            final int tmpFieldCnt = this.childActivities.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChildActivities(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChildActivities();
            }
        }  else if (builder.hasChildActivities()) {
            // 清理ChildActivities
            builder.clearChildActivities();
            fieldCnt++;
        }
        if (this.getMaxUnitId() != 0) {
            builder.setMaxUnitId(this.getMaxUnitId());
            fieldCnt++;
        }  else if (builder.hasMaxUnitId()) {
            // 清理MaxUnitId
            builder.clearMaxUnitId();
            fieldCnt++;
        }
        if (this.closedChildActivities != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.closedChildActivities.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClosedChildActivities(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClosedChildActivities();
            }
        }  else if (builder.hasClosedChildActivities()) {
            // 清理ClosedChildActivities
            builder.clearClosedChildActivities();
            fieldCnt++;
        }
        if (this.specUnit != null) {
            Struct.ActivityUnit.Builder tmpBuilder = Struct.ActivityUnit.newBuilder();
            final int tmpFieldCnt = this.specUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpecUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpecUnit();
            }
        }  else if (builder.hasSpecUnit()) {
            // 清理SpecUnit
            builder.clearSpecUnit();
            fieldCnt++;
        }
        if (this.commonTaskUnit != null) {
            Struct.ActivityUnit.Builder tmpBuilder = Struct.ActivityUnit.newBuilder();
            final int tmpFieldCnt = this.commonTaskUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCommonTaskUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCommonTaskUnit();
            }
        }  else if (builder.hasCommonTaskUnit()) {
            // 清理CommonTaskUnit
            builder.clearCommonTaskUnit();
            fieldCnt++;
        }
        if (this.commonScoreRewardUnit != null) {
            Struct.ActivityUnit.Builder tmpBuilder = Struct.ActivityUnit.newBuilder();
            final int tmpFieldCnt = this.commonScoreRewardUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCommonScoreRewardUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCommonScoreRewardUnit();
            }
        }  else if (builder.hasCommonScoreRewardUnit()) {
            // 清理CommonScoreRewardUnit
            builder.clearCommonScoreRewardUnit();
            fieldCnt++;
        }
        if (this.timerRewardUnit != null) {
            Struct.ActivityUnit.Builder tmpBuilder = Struct.ActivityUnit.newBuilder();
            final int tmpFieldCnt = this.timerRewardUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTimerRewardUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTimerRewardUnit();
            }
        }  else if (builder.hasTimerRewardUnit()) {
            // 清理TimerRewardUnit
            builder.clearTimerRewardUnit();
            fieldCnt++;
        }
        if (this.chargeGoodsChainUnit != null) {
            Struct.ActivityUnit.Builder tmpBuilder = Struct.ActivityUnit.newBuilder();
            final int tmpFieldCnt = this.chargeGoodsChainUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChargeGoodsChainUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChargeGoodsChainUnit();
            }
        }  else if (builder.hasChargeGoodsChainUnit()) {
            // 清理ChargeGoodsChainUnit
            builder.clearChargeGoodsChainUnit();
            fieldCnt++;
        }
        if (this.commonStoreUnit != null) {
            Struct.ActivityUnit.Builder tmpBuilder = Struct.ActivityUnit.newBuilder();
            final int tmpFieldCnt = this.commonStoreUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCommonStoreUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCommonStoreUnit();
            }
        }  else if (builder.hasCommonStoreUnit()) {
            // 清理CommonStoreUnit
            builder.clearCommonStoreUnit();
            fieldCnt++;
        }
        if (this.scoreRankUnit != null) {
            Struct.ActivityUnit.Builder tmpBuilder = Struct.ActivityUnit.newBuilder();
            final int tmpFieldCnt = this.scoreRankUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScoreRankUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScoreRankUnit();
            }
        }  else if (builder.hasScoreRankUnit()) {
            // 清理ScoreRankUnit
            builder.clearScoreRankUnit();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(Activity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSSEC)) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSSEC)) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUSENTERTSSEC)) {
            builder.setStatusEnterTsSec(this.getStatusEnterTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CHILDACTIVITIES) && this.childActivities != null) {
            final boolean needClear = !builder.hasChildActivities();
            final int tmpFieldCnt = this.childActivities.copyChangeToSs(builder.getChildActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChildActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAXUNITID)) {
            builder.setMaxUnitId(this.getMaxUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLOSEDCHILDACTIVITIES) && this.closedChildActivities != null) {
            final boolean needClear = !builder.hasClosedChildActivities();
            final int tmpFieldCnt = this.closedChildActivities.copyChangeToSs(builder.getClosedChildActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClosedChildActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPECUNIT) && this.specUnit != null) {
            final boolean needClear = !builder.hasSpecUnit();
            final int tmpFieldCnt = this.specUnit.copyChangeToSs(builder.getSpecUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpecUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_COMMONTASKUNIT) && this.commonTaskUnit != null) {
            final boolean needClear = !builder.hasCommonTaskUnit();
            final int tmpFieldCnt = this.commonTaskUnit.copyChangeToSs(builder.getCommonTaskUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCommonTaskUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_COMMONSCOREREWARDUNIT) && this.commonScoreRewardUnit != null) {
            final boolean needClear = !builder.hasCommonScoreRewardUnit();
            final int tmpFieldCnt = this.commonScoreRewardUnit.copyChangeToSs(builder.getCommonScoreRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCommonScoreRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_TIMERREWARDUNIT) && this.timerRewardUnit != null) {
            final boolean needClear = !builder.hasTimerRewardUnit();
            final int tmpFieldCnt = this.timerRewardUnit.copyChangeToSs(builder.getTimerRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTimerRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CHARGEGOODSCHAINUNIT) && this.chargeGoodsChainUnit != null) {
            final boolean needClear = !builder.hasChargeGoodsChainUnit();
            final int tmpFieldCnt = this.chargeGoodsChainUnit.copyChangeToSs(builder.getChargeGoodsChainUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChargeGoodsChainUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_COMMONSTOREUNIT) && this.commonStoreUnit != null) {
            final boolean needClear = !builder.hasCommonStoreUnit();
            final int tmpFieldCnt = this.commonStoreUnit.copyChangeToSs(builder.getCommonStoreUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCommonStoreUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCORERANKUNIT) && this.scoreRankUnit != null) {
            final boolean needClear = !builder.hasScoreRankUnit();
            final int tmpFieldCnt = this.scoreRankUnit.copyChangeToSs(builder.getScoreRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreRankUnit();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(Activity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartTsSec()) {
            this.innerSetStartTsSec(proto.getStartTsSec());
        } else {
            this.innerSetStartTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEndTsSec()) {
            this.innerSetEndTsSec(proto.getEndTsSec());
        } else {
            this.innerSetEndTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(ActivityStatus.forNumber(0));
        }
        if (proto.hasStatusEnterTsSec()) {
            this.innerSetStatusEnterTsSec(proto.getStatusEnterTsSec());
        } else {
            this.innerSetStatusEnterTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasChildActivities()) {
            this.getChildActivities().mergeFromSs(proto.getChildActivities());
        } else {
            if (this.childActivities != null) {
                this.childActivities.mergeFromSs(proto.getChildActivities());
            }
        }
        if (proto.hasMaxUnitId()) {
            this.innerSetMaxUnitId(proto.getMaxUnitId());
        } else {
            this.innerSetMaxUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasClosedChildActivities()) {
            this.getClosedChildActivities().mergeFromSs(proto.getClosedChildActivities());
        } else {
            if (this.closedChildActivities != null) {
                this.closedChildActivities.mergeFromSs(proto.getClosedChildActivities());
            }
        }
        if (proto.hasSpecUnit()) {
            this.getSpecUnit().mergeFromSs(proto.getSpecUnit());
        } else {
            if (this.specUnit != null) {
                this.specUnit.mergeFromSs(proto.getSpecUnit());
            }
        }
        if (proto.hasCommonTaskUnit()) {
            this.getCommonTaskUnit().mergeFromSs(proto.getCommonTaskUnit());
        } else {
            if (this.commonTaskUnit != null) {
                this.commonTaskUnit.mergeFromSs(proto.getCommonTaskUnit());
            }
        }
        if (proto.hasCommonScoreRewardUnit()) {
            this.getCommonScoreRewardUnit().mergeFromSs(proto.getCommonScoreRewardUnit());
        } else {
            if (this.commonScoreRewardUnit != null) {
                this.commonScoreRewardUnit.mergeFromSs(proto.getCommonScoreRewardUnit());
            }
        }
        if (proto.hasTimerRewardUnit()) {
            this.getTimerRewardUnit().mergeFromSs(proto.getTimerRewardUnit());
        } else {
            if (this.timerRewardUnit != null) {
                this.timerRewardUnit.mergeFromSs(proto.getTimerRewardUnit());
            }
        }
        if (proto.hasChargeGoodsChainUnit()) {
            this.getChargeGoodsChainUnit().mergeFromSs(proto.getChargeGoodsChainUnit());
        } else {
            if (this.chargeGoodsChainUnit != null) {
                this.chargeGoodsChainUnit.mergeFromSs(proto.getChargeGoodsChainUnit());
            }
        }
        if (proto.hasCommonStoreUnit()) {
            this.getCommonStoreUnit().mergeFromSs(proto.getCommonStoreUnit());
        } else {
            if (this.commonStoreUnit != null) {
                this.commonStoreUnit.mergeFromSs(proto.getCommonStoreUnit());
            }
        }
        if (proto.hasScoreRankUnit()) {
            this.getScoreRankUnit().mergeFromSs(proto.getScoreRankUnit());
        } else {
            if (this.scoreRankUnit != null) {
                this.scoreRankUnit.mergeFromSs(proto.getScoreRankUnit());
            }
        }
        this.markAll();
        return ActivityProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(Activity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasStartTsSec()) {
            this.setStartTsSec(proto.getStartTsSec());
            fieldCnt++;
        }
        if (proto.hasEndTsSec()) {
            this.setEndTsSec(proto.getEndTsSec());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasStatusEnterTsSec()) {
            this.setStatusEnterTsSec(proto.getStatusEnterTsSec());
            fieldCnt++;
        }
        if (proto.hasChildActivities()) {
            this.getChildActivities().mergeChangeFromSs(proto.getChildActivities());
            fieldCnt++;
        }
        if (proto.hasMaxUnitId()) {
            this.setMaxUnitId(proto.getMaxUnitId());
            fieldCnt++;
        }
        if (proto.hasClosedChildActivities()) {
            this.getClosedChildActivities().mergeChangeFromSs(proto.getClosedChildActivities());
            fieldCnt++;
        }
        if (proto.hasSpecUnit()) {
            this.getSpecUnit().mergeChangeFromSs(proto.getSpecUnit());
            fieldCnt++;
        }
        if (proto.hasCommonTaskUnit()) {
            this.getCommonTaskUnit().mergeChangeFromSs(proto.getCommonTaskUnit());
            fieldCnt++;
        }
        if (proto.hasCommonScoreRewardUnit()) {
            this.getCommonScoreRewardUnit().mergeChangeFromSs(proto.getCommonScoreRewardUnit());
            fieldCnt++;
        }
        if (proto.hasTimerRewardUnit()) {
            this.getTimerRewardUnit().mergeChangeFromSs(proto.getTimerRewardUnit());
            fieldCnt++;
        }
        if (proto.hasChargeGoodsChainUnit()) {
            this.getChargeGoodsChainUnit().mergeChangeFromSs(proto.getChargeGoodsChainUnit());
            fieldCnt++;
        }
        if (proto.hasCommonStoreUnit()) {
            this.getCommonStoreUnit().mergeChangeFromSs(proto.getCommonStoreUnit());
            fieldCnt++;
        }
        if (proto.hasScoreRankUnit()) {
            this.getScoreRankUnit().mergeChangeFromSs(proto.getScoreRankUnit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        Activity.Builder builder = Activity.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CHILDACTIVITIES) && this.childActivities != null) {
            this.childActivities.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CLOSEDCHILDACTIVITIES) && this.closedChildActivities != null) {
            this.closedChildActivities.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SPECUNIT) && this.specUnit != null) {
            this.specUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_COMMONTASKUNIT) && this.commonTaskUnit != null) {
            this.commonTaskUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_COMMONSCOREREWARDUNIT) && this.commonScoreRewardUnit != null) {
            this.commonScoreRewardUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TIMERREWARDUNIT) && this.timerRewardUnit != null) {
            this.timerRewardUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CHARGEGOODSCHAINUNIT) && this.chargeGoodsChainUnit != null) {
            this.chargeGoodsChainUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_COMMONSTOREUNIT) && this.commonStoreUnit != null) {
            this.commonStoreUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SCORERANKUNIT) && this.scoreRankUnit != null) {
            this.scoreRankUnit.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.childActivities != null) {
            this.childActivities.markAll();
        }
        if (this.closedChildActivities != null) {
            this.closedChildActivities.markAll();
        }
        if (this.specUnit != null) {
            this.specUnit.markAll();
        }
        if (this.commonTaskUnit != null) {
            this.commonTaskUnit.markAll();
        }
        if (this.commonScoreRewardUnit != null) {
            this.commonScoreRewardUnit.markAll();
        }
        if (this.timerRewardUnit != null) {
            this.timerRewardUnit.markAll();
        }
        if (this.chargeGoodsChainUnit != null) {
            this.chargeGoodsChainUnit.markAll();
        }
        if (this.commonStoreUnit != null) {
            this.commonStoreUnit.markAll();
        }
        if (this.scoreRankUnit != null) {
            this.scoreRankUnit.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityProp)) {
            return false;
        }
        final ActivityProp otherNode = (ActivityProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.startTsSec != otherNode.startTsSec) {
            return false;
        }
        if (this.endTsSec != otherNode.endTsSec) {
            return false;
        }
        if (this.status != otherNode.status) {
            return false;
        }
        if (this.statusEnterTsSec != otherNode.statusEnterTsSec) {
            return false;
        }
        if (!this.getChildActivities().compareDataTo(otherNode.getChildActivities())) {
            return false;
        }
        if (this.maxUnitId != otherNode.maxUnitId) {
            return false;
        }
        if (!this.getClosedChildActivities().compareDataTo(otherNode.getClosedChildActivities())) {
            return false;
        }
        if (!this.getSpecUnit().compareDataTo(otherNode.getSpecUnit())) {
            return false;
        }
        if (!this.getCommonTaskUnit().compareDataTo(otherNode.getCommonTaskUnit())) {
            return false;
        }
        if (!this.getCommonScoreRewardUnit().compareDataTo(otherNode.getCommonScoreRewardUnit())) {
            return false;
        }
        if (!this.getTimerRewardUnit().compareDataTo(otherNode.getTimerRewardUnit())) {
            return false;
        }
        if (!this.getChargeGoodsChainUnit().compareDataTo(otherNode.getChargeGoodsChainUnit())) {
            return false;
        }
        if (!this.getCommonStoreUnit().compareDataTo(otherNode.getCommonStoreUnit())) {
            return false;
        }
        if (!this.getScoreRankUnit().compareDataTo(otherNode.getScoreRankUnit())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 49;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
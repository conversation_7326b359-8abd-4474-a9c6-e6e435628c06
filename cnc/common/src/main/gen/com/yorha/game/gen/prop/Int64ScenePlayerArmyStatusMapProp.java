 
package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.AbstractMapNode;
import com.yorha.proto.PlayerPB.Int64ScenePlayerArmyStatusMapPB;
import com.yorha.proto.PlayerPB.ScenePlayerArmyStatusPB;
import com.yorha.proto.Player.Int64ScenePlayerArmyStatusMap;
import com.yorha.proto.Player.ScenePlayerArmyStatus;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> auto gen
 */
public class Int64ScenePlayerArmyStatusMapProp extends AbstractMapNode<Long, ScenePlayerArmyStatusProp> {
    /**
     * Creates a Int64ScenePlayerArmyStatusMapProp container
     *
     * @param parent     parent node
     * @param fieldIndex field index in parent node
     */
    public Int64ScenePlayerArmyStatusMapProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to Int64ScenePlayerArmyStatusMapProp
     *
     * @param k map key
     * @return new object
     */
    @Override
    public ScenePlayerArmyStatusProp addEmptyValue(Long k) {
        ScenePlayerArmyStatusProp newProp = new ScenePlayerArmyStatusProp(null, DEFAULT_FIELD_INDEX);
        newProp.setArmyId(k);
        this.put(k, newProp);
        return newProp;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Int64ScenePlayerArmyStatusMapPB.Builder getCopyCsBuilder() {
        final Int64ScenePlayerArmyStatusMapPB.Builder builder = Int64ScenePlayerArmyStatusMapPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB.
     *
     * @param builder builder for protobuf PB
     * @return copied field count
     */
    public int copyToCs(Int64ScenePlayerArmyStatusMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0 || builder.hasClearFlag() || builder.getDeleteKeysCount() != 0) {
                builder.clear();
                return Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final Map.Entry<Long, ScenePlayerArmyStatusProp> entry : this.entrySet()) {
            ScenePlayerArmyStatusPB.Builder itemBuilder = ScenePlayerArmyStatusPB.newBuilder();
            entry.getValue().copyToCs(itemBuilder);
            builder.putDatas(entry.getKey(), itemBuilder.build());
        }
        return Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
    }

    /**
     * copy data change to protobuf PB. clear first, then refresh, add at last.
     *
     * @param builder builder for protobuf PB
     * @return copied field count
     */
    public int copyChangeToCs(Int64ScenePlayerArmyStatusMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        if (this.isClearFlag()) {
            // clear builder, when clear flag true
            builder.clear();
            // set clear flag when not clear.
            builder.setClearFlag(this.isClearFlag());
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            final Set<Long> deleteKeys = !builder.getClearFlag() ? new HashSet<>(builder.getDeleteKeysList()) : null;
            isChanged = true;
            boolean isDeleteKeysChanged = false;
            for(final Long key : this.getRefreshKeys()) {
                builder.removeDatas(key);
                if (deleteKeys != null) {
                    isDeleteKeysChanged = deleteKeys.add(key) || isDeleteKeysChanged;
                }
            }
            // update delete key when builder not clear.
            if (isDeleteKeysChanged) {
                builder.clearDeleteKeys();
                if (!deleteKeys.isEmpty()) {
                    builder.addAllDeleteKeys(deleteKeys);
                }
            }
        }
        // put data when dirty
        for (final Long key : this.getExistDirtyKeys()) {
            final ScenePlayerArmyStatusProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final ScenePlayerArmyStatusPB.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : ScenePlayerArmyStatusPB.newBuilder();
            final int changeCnt = oldValue.copyChangeToCs(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        return isChanged? Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT: 0;
    }

    /**
     * copy data change to protobuf PB. clear first, then refresh, add at last. it wll clear clearFlag and deleteKeys.
     *
     * @param builder builder for protobuf PB
     * @return copied field count
     */
    public int copyChangeToAndClearDeleteKeysCs(Int64ScenePlayerArmyStatusMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        // clear builder, when clear flag true
        if (this.isClearFlag()) {
            builder.clear();
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            isChanged = true;
            for(final Long key : this.getRefreshKeys()) {
                builder.removeDatas(key);
            }
        }
        // put data when dirty
        for (final Long key : this.getExistDirtyKeys()) {
            final ScenePlayerArmyStatusProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final ScenePlayerArmyStatusPB.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : ScenePlayerArmyStatusPB.newBuilder();
            final int changeCnt = oldValue.copyChangeToAndClearDeleteKeysCs(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        if (isChanged) {
            builder.clearDeleteKeys();
            builder.clearClearFlag();
        }
        return isChanged? Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT: 0;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(Int64ScenePlayerArmyStatusMapPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (final Map.Entry<Long, ScenePlayerArmyStatusPB> entry : proto.getDatasMap().entrySet()) {
            this.addEmptyValue(entry.getKey()).mergeFromCs(entry.getValue());
        }
        this.markAll();
        return Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
    }

    /**
     * merge data change from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeChangeFromCs(Int64ScenePlayerArmyStatusMapPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int changeCnt = 0;
        if (proto.hasClearFlag() && proto.getClearFlag()) {
            this.clear();
            changeCnt = Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
        }
        if (proto.getDeleteKeysCount() > 0) {
            this.removeAll(proto.getDeleteKeysList());
            changeCnt = Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
        }
        for (final Map.Entry<Long, ScenePlayerArmyStatusPB> entry : proto.getDatasMap().entrySet()) {
            if (this.containsKey(entry.getKey())) {
                this.get(entry.getKey()).mergeChangeFromCs(entry.getValue());
                changeCnt = Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
                continue;
            }
            this.addEmptyValue(entry.getKey()).mergeChangeFromCs(entry.getValue());
            changeCnt = Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
        }
        return changeCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Int64ScenePlayerArmyStatusMap.Builder getCopyDbBuilder() {
        final Int64ScenePlayerArmyStatusMap.Builder builder = Int64ScenePlayerArmyStatusMap.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf .
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyToDb(Int64ScenePlayerArmyStatusMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0 || builder.hasClearFlag() || builder.getDeleteKeysCount() != 0) {
                builder.clear();
                return Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final Map.Entry<Long, ScenePlayerArmyStatusProp> entry : this.entrySet()) {
            ScenePlayerArmyStatus.Builder itemBuilder = ScenePlayerArmyStatus.newBuilder();
            entry.getValue().copyToDb(itemBuilder);
            builder.putDatas(entry.getKey(), itemBuilder.build());
        }
        return Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
    }

    /**
     * copy data change to protobuf . clear first, then refresh, add at last.
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyChangeToDb(Int64ScenePlayerArmyStatusMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        if (this.isClearFlag()) {
            // clear builder, when clear flag true
            builder.clear();
            // set clear flag when not clear.
            builder.setClearFlag(this.isClearFlag());
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            final Set<Long> deleteKeys = !builder.getClearFlag() ? new HashSet<>(builder.getDeleteKeysList()) : null;
            isChanged = true;
            boolean isDeleteKeysChanged = false;
            for(final Long key : this.getRefreshKeys()) {
                builder.removeDatas(key);
                if (deleteKeys != null) {
                    isDeleteKeysChanged = deleteKeys.add(key) || isDeleteKeysChanged;
                }
            }
            // update delete key when builder not clear.
            if (isDeleteKeysChanged) {
                builder.clearDeleteKeys();
                if (!deleteKeys.isEmpty()) {
                    builder.addAllDeleteKeys(deleteKeys);
                }
            }
        }
        // put data when dirty
        for (final Long key : this.getExistDirtyKeys()) {
            final ScenePlayerArmyStatusProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final ScenePlayerArmyStatus.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : ScenePlayerArmyStatus.newBuilder();
            final int changeCnt = oldValue.copyChangeToDb(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        return isChanged? Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT: 0;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(Int64ScenePlayerArmyStatusMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (final Map.Entry<Long, ScenePlayerArmyStatus> entry : proto.getDatasMap().entrySet()) {
            this.addEmptyValue(entry.getKey()).mergeFromDb(entry.getValue());
        }
        this.markAll();
        return Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
    }

    /**
     * merge data change from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeChangeFromDb(Int64ScenePlayerArmyStatusMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int changeCnt = 0;
        if (proto.hasClearFlag() && proto.getClearFlag()) {
            this.clear();
            changeCnt = Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
        }
        if (proto.getDeleteKeysCount() > 0) {
            this.removeAll(proto.getDeleteKeysList());
            changeCnt = Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
        }
        for (final Map.Entry<Long, ScenePlayerArmyStatus> entry : proto.getDatasMap().entrySet()) {
            if (this.containsKey(entry.getKey())) {
                this.get(entry.getKey()).mergeChangeFromDb(entry.getValue());
                changeCnt = Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
                continue;
            }
            this.addEmptyValue(entry.getKey()).mergeChangeFromDb(entry.getValue());
            changeCnt = Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
        }
        return changeCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Int64ScenePlayerArmyStatusMap.Builder getCopySsBuilder() {
        final Int64ScenePlayerArmyStatusMap.Builder builder = Int64ScenePlayerArmyStatusMap.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf .
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyToSs(Int64ScenePlayerArmyStatusMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0 || builder.hasClearFlag() || builder.getDeleteKeysCount() != 0) {
                builder.clear();
                return Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final Map.Entry<Long, ScenePlayerArmyStatusProp> entry : this.entrySet()) {
            ScenePlayerArmyStatus.Builder itemBuilder = ScenePlayerArmyStatus.newBuilder();
            entry.getValue().copyToSs(itemBuilder);
            builder.putDatas(entry.getKey(), itemBuilder.build());
        }
        return Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
    }

    /**
     * copy data change to protobuf . clear first, then refresh, add at last.
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyChangeToSs(Int64ScenePlayerArmyStatusMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        if (this.isClearFlag()) {
            // clear builder, when clear flag true
            builder.clear();
            // set clear flag when not clear.
            builder.setClearFlag(this.isClearFlag());
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            final Set<Long> deleteKeys = !builder.getClearFlag() ? new HashSet<>(builder.getDeleteKeysList()) : null;
            isChanged = true;
            boolean isDeleteKeysChanged = false;
            for(final Long key : this.getRefreshKeys()) {
                builder.removeDatas(key);
                if (deleteKeys != null) {
                    isDeleteKeysChanged = deleteKeys.add(key) || isDeleteKeysChanged;
                }
            }
            // update delete key when builder not clear.
            if (isDeleteKeysChanged) {
                builder.clearDeleteKeys();
                if (!deleteKeys.isEmpty()) {
                    builder.addAllDeleteKeys(deleteKeys);
                }
            }
        }
        // put data when dirty
        for (final Long key : this.getExistDirtyKeys()) {
            final ScenePlayerArmyStatusProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final ScenePlayerArmyStatus.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : ScenePlayerArmyStatus.newBuilder();
            final int changeCnt = oldValue.copyChangeToSs(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        return isChanged? Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT: 0;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(Int64ScenePlayerArmyStatusMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (final Map.Entry<Long, ScenePlayerArmyStatus> entry : proto.getDatasMap().entrySet()) {
            this.addEmptyValue(entry.getKey()).mergeFromSs(entry.getValue());
        }
        this.markAll();
        return Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
    }

    /**
     * merge data change from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeChangeFromSs(Int64ScenePlayerArmyStatusMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int changeCnt = 0;
        if (proto.hasClearFlag() && proto.getClearFlag()) {
            this.clear();
            changeCnt = Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
        }
        if (proto.getDeleteKeysCount() > 0) {
            this.removeAll(proto.getDeleteKeysList());
            changeCnt = Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
        }
        for (final Map.Entry<Long, ScenePlayerArmyStatus> entry : proto.getDatasMap().entrySet()) {
            if (this.containsKey(entry.getKey())) {
                this.get(entry.getKey()).mergeChangeFromSs(entry.getValue());
                changeCnt = Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
                continue;
            }
            this.addEmptyValue(entry.getKey()).mergeChangeFromSs(entry.getValue());
            changeCnt = Int64ScenePlayerArmyStatusMapProp.FIELD_COUNT;
        }
        return changeCnt;
    }

    @Override
    public String toString() {
        Int64ScenePlayerArmyStatusMap.Builder builder = Int64ScenePlayerArmyStatusMap.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
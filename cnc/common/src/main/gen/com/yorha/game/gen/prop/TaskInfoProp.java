package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Struct.TaskInfo;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.TaskInfoPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class TaskInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_PROCESS = 1;
    public static final int FIELD_INDEX_STARTMS = 2;
    public static final int FIELD_INDEX_ENDMS = 3;
    public static final int FIELD_INDEX_LASTUPDATEMS = 4;
    public static final int FIELD_INDEX_STATUS = 5;
    public static final int FIELD_INDEX_EXTRAINFO = 6;
    public static final int FIELD_INDEX_ISMAINTASK = 7;

    public static final int FIELD_COUNT = 8;

    private long markBits0 = 0L;

    private int id = Constant.DEFAULT_INT_VALUE;
    private int process = Constant.DEFAULT_INT_VALUE;
    private long startMs = Constant.DEFAULT_LONG_VALUE;
    private long endMs = Constant.DEFAULT_LONG_VALUE;
    private long lastUpdateMs = Constant.DEFAULT_LONG_VALUE;
    private TaskStatus status = TaskStatus.forNumber(0);
    private TaskExtraInfoProp extraInfo = null;
    private boolean isMainTask = Constant.DEFAULT_BOOLEAN_VALUE;

    public TaskInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public TaskInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public int getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public TaskInfoProp setId(int id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(int id) {
        this.id = id;
    }

    /**
     * get process
     *
     * @return process value
     */
    public int getProcess() {
        return this.process;
    }

    /**
     * set process && set marked
     *
     * @param process new value
     * @return current object
     */
    public TaskInfoProp setProcess(int process) {
        if (this.process != process) {
            this.mark(FIELD_INDEX_PROCESS);
            this.process = process;
        }
        return this;
    }

    /**
     * inner set process
     *
     * @param process new value
     */
    private void innerSetProcess(int process) {
        this.process = process;
    }

    /**
     * get startMs
     *
     * @return startMs value
     */
    public long getStartMs() {
        return this.startMs;
    }

    /**
     * set startMs && set marked
     *
     * @param startMs new value
     * @return current object
     */
    public TaskInfoProp setStartMs(long startMs) {
        if (this.startMs != startMs) {
            this.mark(FIELD_INDEX_STARTMS);
            this.startMs = startMs;
        }
        return this;
    }

    /**
     * inner set startMs
     *
     * @param startMs new value
     */
    private void innerSetStartMs(long startMs) {
        this.startMs = startMs;
    }

    /**
     * get endMs
     *
     * @return endMs value
     */
    public long getEndMs() {
        return this.endMs;
    }

    /**
     * set endMs && set marked
     *
     * @param endMs new value
     * @return current object
     */
    public TaskInfoProp setEndMs(long endMs) {
        if (this.endMs != endMs) {
            this.mark(FIELD_INDEX_ENDMS);
            this.endMs = endMs;
        }
        return this;
    }

    /**
     * inner set endMs
     *
     * @param endMs new value
     */
    private void innerSetEndMs(long endMs) {
        this.endMs = endMs;
    }

    /**
     * get lastUpdateMs
     *
     * @return lastUpdateMs value
     */
    public long getLastUpdateMs() {
        return this.lastUpdateMs;
    }

    /**
     * set lastUpdateMs && set marked
     *
     * @param lastUpdateMs new value
     * @return current object
     */
    public TaskInfoProp setLastUpdateMs(long lastUpdateMs) {
        if (this.lastUpdateMs != lastUpdateMs) {
            this.mark(FIELD_INDEX_LASTUPDATEMS);
            this.lastUpdateMs = lastUpdateMs;
        }
        return this;
    }

    /**
     * inner set lastUpdateMs
     *
     * @param lastUpdateMs new value
     */
    private void innerSetLastUpdateMs(long lastUpdateMs) {
        this.lastUpdateMs = lastUpdateMs;
    }

    /**
     * get status
     *
     * @return status value
     */
    public TaskStatus getStatus() {
        return this.status;
    }

    /**
     * set status && set marked
     *
     * @param status new value
     * @return current object
     */
    public TaskInfoProp setStatus(TaskStatus status) {
        if (status == null) {
            throw new NullPointerException();
        }
        if (this.status != status) {
            this.mark(FIELD_INDEX_STATUS);
            this.status = status;
        }
        return this;
    }

    /**
     * inner set status
     *
     * @param status new value
     */
    private void innerSetStatus(TaskStatus status) {
        this.status = status;
    }

    /**
     * get extraInfo
     *
     * @return extraInfo value
     */
    public TaskExtraInfoProp getExtraInfo() {
        if (this.extraInfo == null) {
            this.extraInfo = new TaskExtraInfoProp(this, FIELD_INDEX_EXTRAINFO);
        }
        return this.extraInfo;
    }

    /**
     * get isMainTask
     *
     * @return isMainTask value
     */
    public boolean getIsMainTask() {
        return this.isMainTask;
    }

    /**
     * set isMainTask && set marked
     *
     * @param isMainTask new value
     * @return current object
     */
    public TaskInfoProp setIsMainTask(boolean isMainTask) {
        if (this.isMainTask != isMainTask) {
            this.mark(FIELD_INDEX_ISMAINTASK);
            this.isMainTask = isMainTask;
        }
        return this;
    }

    /**
     * inner set isMainTask
     *
     * @param isMainTask new value
     */
    private void innerSetIsMainTask(boolean isMainTask) {
        this.isMainTask = isMainTask;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TaskInfoPB.Builder getCopyCsBuilder() {
        final TaskInfoPB.Builder builder = TaskInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(TaskInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getProcess() != 0) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }  else if (builder.hasProcess()) {
            // 清理Process
            builder.clearProcess();
            fieldCnt++;
        }
        if (this.getStartMs() != 0L) {
            builder.setStartMs(this.getStartMs());
            fieldCnt++;
        }  else if (builder.hasStartMs()) {
            // 清理StartMs
            builder.clearStartMs();
            fieldCnt++;
        }
        if (this.getEndMs() != 0L) {
            builder.setEndMs(this.getEndMs());
            fieldCnt++;
        }  else if (builder.hasEndMs()) {
            // 清理EndMs
            builder.clearEndMs();
            fieldCnt++;
        }
        if (this.getLastUpdateMs() != 0L) {
            builder.setLastUpdateMs(this.getLastUpdateMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateMs()) {
            // 清理LastUpdateMs
            builder.clearLastUpdateMs();
            fieldCnt++;
        }
        if (this.getStatus() != TaskStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.getIsMainTask()) {
            builder.setIsMainTask(this.getIsMainTask());
            fieldCnt++;
        }  else if (builder.hasIsMainTask()) {
            // 清理IsMainTask
            builder.clearIsMainTask();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(TaskInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PROCESS)) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTMS)) {
            builder.setStartMs(this.getStartMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDMS)) {
            builder.setEndMs(this.getEndMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATEMS)) {
            builder.setLastUpdateMs(this.getLastUpdateMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISMAINTASK)) {
            builder.setIsMainTask(this.getIsMainTask());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(TaskInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PROCESS)) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTMS)) {
            builder.setStartMs(this.getStartMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDMS)) {
            builder.setEndMs(this.getEndMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATEMS)) {
            builder.setLastUpdateMs(this.getLastUpdateMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISMAINTASK)) {
            builder.setIsMainTask(this.getIsMainTask());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(TaskInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasProcess()) {
            this.innerSetProcess(proto.getProcess());
        } else {
            this.innerSetProcess(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartMs()) {
            this.innerSetStartMs(proto.getStartMs());
        } else {
            this.innerSetStartMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndMs()) {
            this.innerSetEndMs(proto.getEndMs());
        } else {
            this.innerSetEndMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastUpdateMs()) {
            this.innerSetLastUpdateMs(proto.getLastUpdateMs());
        } else {
            this.innerSetLastUpdateMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(TaskStatus.forNumber(0));
        }
        if (proto.hasIsMainTask()) {
            this.innerSetIsMainTask(proto.getIsMainTask());
        } else {
            this.innerSetIsMainTask(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return TaskInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(TaskInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasProcess()) {
            this.setProcess(proto.getProcess());
            fieldCnt++;
        }
        if (proto.hasStartMs()) {
            this.setStartMs(proto.getStartMs());
            fieldCnt++;
        }
        if (proto.hasEndMs()) {
            this.setEndMs(proto.getEndMs());
            fieldCnt++;
        }
        if (proto.hasLastUpdateMs()) {
            this.setLastUpdateMs(proto.getLastUpdateMs());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasIsMainTask()) {
            this.setIsMainTask(proto.getIsMainTask());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TaskInfo.Builder getCopyDbBuilder() {
        final TaskInfo.Builder builder = TaskInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(TaskInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getProcess() != 0) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }  else if (builder.hasProcess()) {
            // 清理Process
            builder.clearProcess();
            fieldCnt++;
        }
        if (this.getStartMs() != 0L) {
            builder.setStartMs(this.getStartMs());
            fieldCnt++;
        }  else if (builder.hasStartMs()) {
            // 清理StartMs
            builder.clearStartMs();
            fieldCnt++;
        }
        if (this.getEndMs() != 0L) {
            builder.setEndMs(this.getEndMs());
            fieldCnt++;
        }  else if (builder.hasEndMs()) {
            // 清理EndMs
            builder.clearEndMs();
            fieldCnt++;
        }
        if (this.getLastUpdateMs() != 0L) {
            builder.setLastUpdateMs(this.getLastUpdateMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateMs()) {
            // 清理LastUpdateMs
            builder.clearLastUpdateMs();
            fieldCnt++;
        }
        if (this.getStatus() != TaskStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.extraInfo != null) {
            Struct.TaskExtraInfo.Builder tmpBuilder = Struct.TaskExtraInfo.newBuilder();
            final int tmpFieldCnt = this.extraInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExtraInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExtraInfo();
            }
        }  else if (builder.hasExtraInfo()) {
            // 清理ExtraInfo
            builder.clearExtraInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(TaskInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PROCESS)) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTMS)) {
            builder.setStartMs(this.getStartMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDMS)) {
            builder.setEndMs(this.getEndMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATEMS)) {
            builder.setLastUpdateMs(this.getLastUpdateMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXTRAINFO) && this.extraInfo != null) {
            final boolean needClear = !builder.hasExtraInfo();
            final int tmpFieldCnt = this.extraInfo.copyChangeToDb(builder.getExtraInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExtraInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(TaskInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasProcess()) {
            this.innerSetProcess(proto.getProcess());
        } else {
            this.innerSetProcess(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartMs()) {
            this.innerSetStartMs(proto.getStartMs());
        } else {
            this.innerSetStartMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndMs()) {
            this.innerSetEndMs(proto.getEndMs());
        } else {
            this.innerSetEndMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastUpdateMs()) {
            this.innerSetLastUpdateMs(proto.getLastUpdateMs());
        } else {
            this.innerSetLastUpdateMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(TaskStatus.forNumber(0));
        }
        if (proto.hasExtraInfo()) {
            this.getExtraInfo().mergeFromDb(proto.getExtraInfo());
        } else {
            if (this.extraInfo != null) {
                this.extraInfo.mergeFromDb(proto.getExtraInfo());
            }
        }
        this.markAll();
        return TaskInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(TaskInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasProcess()) {
            this.setProcess(proto.getProcess());
            fieldCnt++;
        }
        if (proto.hasStartMs()) {
            this.setStartMs(proto.getStartMs());
            fieldCnt++;
        }
        if (proto.hasEndMs()) {
            this.setEndMs(proto.getEndMs());
            fieldCnt++;
        }
        if (proto.hasLastUpdateMs()) {
            this.setLastUpdateMs(proto.getLastUpdateMs());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasExtraInfo()) {
            this.getExtraInfo().mergeChangeFromDb(proto.getExtraInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TaskInfo.Builder getCopySsBuilder() {
        final TaskInfo.Builder builder = TaskInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(TaskInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getProcess() != 0) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }  else if (builder.hasProcess()) {
            // 清理Process
            builder.clearProcess();
            fieldCnt++;
        }
        if (this.getStartMs() != 0L) {
            builder.setStartMs(this.getStartMs());
            fieldCnt++;
        }  else if (builder.hasStartMs()) {
            // 清理StartMs
            builder.clearStartMs();
            fieldCnt++;
        }
        if (this.getEndMs() != 0L) {
            builder.setEndMs(this.getEndMs());
            fieldCnt++;
        }  else if (builder.hasEndMs()) {
            // 清理EndMs
            builder.clearEndMs();
            fieldCnt++;
        }
        if (this.getLastUpdateMs() != 0L) {
            builder.setLastUpdateMs(this.getLastUpdateMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateMs()) {
            // 清理LastUpdateMs
            builder.clearLastUpdateMs();
            fieldCnt++;
        }
        if (this.getStatus() != TaskStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.extraInfo != null) {
            Struct.TaskExtraInfo.Builder tmpBuilder = Struct.TaskExtraInfo.newBuilder();
            final int tmpFieldCnt = this.extraInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExtraInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExtraInfo();
            }
        }  else if (builder.hasExtraInfo()) {
            // 清理ExtraInfo
            builder.clearExtraInfo();
            fieldCnt++;
        }
        if (this.getIsMainTask()) {
            builder.setIsMainTask(this.getIsMainTask());
            fieldCnt++;
        }  else if (builder.hasIsMainTask()) {
            // 清理IsMainTask
            builder.clearIsMainTask();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(TaskInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PROCESS)) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTMS)) {
            builder.setStartMs(this.getStartMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDMS)) {
            builder.setEndMs(this.getEndMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATEMS)) {
            builder.setLastUpdateMs(this.getLastUpdateMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXTRAINFO) && this.extraInfo != null) {
            final boolean needClear = !builder.hasExtraInfo();
            final int tmpFieldCnt = this.extraInfo.copyChangeToSs(builder.getExtraInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExtraInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISMAINTASK)) {
            builder.setIsMainTask(this.getIsMainTask());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(TaskInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasProcess()) {
            this.innerSetProcess(proto.getProcess());
        } else {
            this.innerSetProcess(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartMs()) {
            this.innerSetStartMs(proto.getStartMs());
        } else {
            this.innerSetStartMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndMs()) {
            this.innerSetEndMs(proto.getEndMs());
        } else {
            this.innerSetEndMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastUpdateMs()) {
            this.innerSetLastUpdateMs(proto.getLastUpdateMs());
        } else {
            this.innerSetLastUpdateMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(TaskStatus.forNumber(0));
        }
        if (proto.hasExtraInfo()) {
            this.getExtraInfo().mergeFromSs(proto.getExtraInfo());
        } else {
            if (this.extraInfo != null) {
                this.extraInfo.mergeFromSs(proto.getExtraInfo());
            }
        }
        if (proto.hasIsMainTask()) {
            this.innerSetIsMainTask(proto.getIsMainTask());
        } else {
            this.innerSetIsMainTask(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return TaskInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(TaskInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasProcess()) {
            this.setProcess(proto.getProcess());
            fieldCnt++;
        }
        if (proto.hasStartMs()) {
            this.setStartMs(proto.getStartMs());
            fieldCnt++;
        }
        if (proto.hasEndMs()) {
            this.setEndMs(proto.getEndMs());
            fieldCnt++;
        }
        if (proto.hasLastUpdateMs()) {
            this.setLastUpdateMs(proto.getLastUpdateMs());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasExtraInfo()) {
            this.getExtraInfo().mergeChangeFromSs(proto.getExtraInfo());
            fieldCnt++;
        }
        if (proto.hasIsMainTask()) {
            this.setIsMainTask(proto.getIsMainTask());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        TaskInfo.Builder builder = TaskInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_EXTRAINFO) && this.extraInfo != null) {
            this.extraInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.extraInfo != null) {
            this.extraInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof TaskInfoProp)) {
            return false;
        }
        final TaskInfoProp otherNode = (TaskInfoProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.process != otherNode.process) {
            return false;
        }
        if (this.startMs != otherNode.startMs) {
            return false;
        }
        if (this.endMs != otherNode.endMs) {
            return false;
        }
        if (this.lastUpdateMs != otherNode.lastUpdateMs) {
            return false;
        }
        if (this.status != otherNode.status) {
            return false;
        }
        if (!this.getExtraInfo().compareDataTo(otherNode.getExtraInfo())) {
            return false;
        }
        if (this.isMainTask != otherNode.isMainTask) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 56;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
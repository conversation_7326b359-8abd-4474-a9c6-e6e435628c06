package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.SpyBuildingData;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.SpyBuildingDataPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class SpyBuildingDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CURHP = 0;
    public static final int FIELD_INDEX_MAXHP = 1;
    public static final int FIELD_INDEX_RALLYARMYDATA = 2;
    public static final int FIELD_INDEX_TEMPLATEID = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int curHp = Constant.DEFAULT_INT_VALUE;
    private int maxHp = Constant.DEFAULT_INT_VALUE;
    private SpyArmyDataProp rallyArmyData = null;
    private int templateId = Constant.DEFAULT_INT_VALUE;

    public SpyBuildingDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SpyBuildingDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get curHp
     *
     * @return curHp value
     */
    public int getCurHp() {
        return this.curHp;
    }

    /**
     * set curHp && set marked
     *
     * @param curHp new value
     * @return current object
     */
    public SpyBuildingDataProp setCurHp(int curHp) {
        if (this.curHp != curHp) {
            this.mark(FIELD_INDEX_CURHP);
            this.curHp = curHp;
        }
        return this;
    }

    /**
     * inner set curHp
     *
     * @param curHp new value
     */
    private void innerSetCurHp(int curHp) {
        this.curHp = curHp;
    }

    /**
     * get maxHp
     *
     * @return maxHp value
     */
    public int getMaxHp() {
        return this.maxHp;
    }

    /**
     * set maxHp && set marked
     *
     * @param maxHp new value
     * @return current object
     */
    public SpyBuildingDataProp setMaxHp(int maxHp) {
        if (this.maxHp != maxHp) {
            this.mark(FIELD_INDEX_MAXHP);
            this.maxHp = maxHp;
        }
        return this;
    }

    /**
     * inner set maxHp
     *
     * @param maxHp new value
     */
    private void innerSetMaxHp(int maxHp) {
        this.maxHp = maxHp;
    }

    /**
     * get rallyArmyData
     *
     * @return rallyArmyData value
     */
    public SpyArmyDataProp getRallyArmyData() {
        if (this.rallyArmyData == null) {
            this.rallyArmyData = new SpyArmyDataProp(this, FIELD_INDEX_RALLYARMYDATA);
        }
        return this.rallyArmyData;
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public SpyBuildingDataProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SpyBuildingDataPB.Builder getCopyCsBuilder() {
        final SpyBuildingDataPB.Builder builder = SpyBuildingDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SpyBuildingDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurHp() != 0) {
            builder.setCurHp(this.getCurHp());
            fieldCnt++;
        }  else if (builder.hasCurHp()) {
            // 清理CurHp
            builder.clearCurHp();
            fieldCnt++;
        }
        if (this.getMaxHp() != 0) {
            builder.setMaxHp(this.getMaxHp());
            fieldCnt++;
        }  else if (builder.hasMaxHp()) {
            // 清理MaxHp
            builder.clearMaxHp();
            fieldCnt++;
        }
        if (this.rallyArmyData != null) {
            StructPB.SpyArmyDataPB.Builder tmpBuilder = StructPB.SpyArmyDataPB.newBuilder();
            final int tmpFieldCnt = this.rallyArmyData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRallyArmyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRallyArmyData();
            }
        }  else if (builder.hasRallyArmyData()) {
            // 清理RallyArmyData
            builder.clearRallyArmyData();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SpyBuildingDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURHP)) {
            builder.setCurHp(this.getCurHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXHP)) {
            builder.setMaxHp(this.getMaxHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLYARMYDATA) && this.rallyArmyData != null) {
            final boolean needClear = !builder.hasRallyArmyData();
            final int tmpFieldCnt = this.rallyArmyData.copyChangeToCs(builder.getRallyArmyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRallyArmyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SpyBuildingDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURHP)) {
            builder.setCurHp(this.getCurHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXHP)) {
            builder.setMaxHp(this.getMaxHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLYARMYDATA) && this.rallyArmyData != null) {
            final boolean needClear = !builder.hasRallyArmyData();
            final int tmpFieldCnt = this.rallyArmyData.copyChangeToAndClearDeleteKeysCs(builder.getRallyArmyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRallyArmyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SpyBuildingDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurHp()) {
            this.innerSetCurHp(proto.getCurHp());
        } else {
            this.innerSetCurHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMaxHp()) {
            this.innerSetMaxHp(proto.getMaxHp());
        } else {
            this.innerSetMaxHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRallyArmyData()) {
            this.getRallyArmyData().mergeFromCs(proto.getRallyArmyData());
        } else {
            if (this.rallyArmyData != null) {
                this.rallyArmyData.mergeFromCs(proto.getRallyArmyData());
            }
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SpyBuildingDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SpyBuildingDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurHp()) {
            this.setCurHp(proto.getCurHp());
            fieldCnt++;
        }
        if (proto.hasMaxHp()) {
            this.setMaxHp(proto.getMaxHp());
            fieldCnt++;
        }
        if (proto.hasRallyArmyData()) {
            this.getRallyArmyData().mergeChangeFromCs(proto.getRallyArmyData());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SpyBuildingData.Builder getCopyDbBuilder() {
        final SpyBuildingData.Builder builder = SpyBuildingData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SpyBuildingData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurHp() != 0) {
            builder.setCurHp(this.getCurHp());
            fieldCnt++;
        }  else if (builder.hasCurHp()) {
            // 清理CurHp
            builder.clearCurHp();
            fieldCnt++;
        }
        if (this.getMaxHp() != 0) {
            builder.setMaxHp(this.getMaxHp());
            fieldCnt++;
        }  else if (builder.hasMaxHp()) {
            // 清理MaxHp
            builder.clearMaxHp();
            fieldCnt++;
        }
        if (this.rallyArmyData != null) {
            Struct.SpyArmyData.Builder tmpBuilder = Struct.SpyArmyData.newBuilder();
            final int tmpFieldCnt = this.rallyArmyData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRallyArmyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRallyArmyData();
            }
        }  else if (builder.hasRallyArmyData()) {
            // 清理RallyArmyData
            builder.clearRallyArmyData();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SpyBuildingData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURHP)) {
            builder.setCurHp(this.getCurHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXHP)) {
            builder.setMaxHp(this.getMaxHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLYARMYDATA) && this.rallyArmyData != null) {
            final boolean needClear = !builder.hasRallyArmyData();
            final int tmpFieldCnt = this.rallyArmyData.copyChangeToDb(builder.getRallyArmyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRallyArmyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SpyBuildingData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurHp()) {
            this.innerSetCurHp(proto.getCurHp());
        } else {
            this.innerSetCurHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMaxHp()) {
            this.innerSetMaxHp(proto.getMaxHp());
        } else {
            this.innerSetMaxHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRallyArmyData()) {
            this.getRallyArmyData().mergeFromDb(proto.getRallyArmyData());
        } else {
            if (this.rallyArmyData != null) {
                this.rallyArmyData.mergeFromDb(proto.getRallyArmyData());
            }
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SpyBuildingDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SpyBuildingData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurHp()) {
            this.setCurHp(proto.getCurHp());
            fieldCnt++;
        }
        if (proto.hasMaxHp()) {
            this.setMaxHp(proto.getMaxHp());
            fieldCnt++;
        }
        if (proto.hasRallyArmyData()) {
            this.getRallyArmyData().mergeChangeFromDb(proto.getRallyArmyData());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SpyBuildingData.Builder getCopySsBuilder() {
        final SpyBuildingData.Builder builder = SpyBuildingData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SpyBuildingData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurHp() != 0) {
            builder.setCurHp(this.getCurHp());
            fieldCnt++;
        }  else if (builder.hasCurHp()) {
            // 清理CurHp
            builder.clearCurHp();
            fieldCnt++;
        }
        if (this.getMaxHp() != 0) {
            builder.setMaxHp(this.getMaxHp());
            fieldCnt++;
        }  else if (builder.hasMaxHp()) {
            // 清理MaxHp
            builder.clearMaxHp();
            fieldCnt++;
        }
        if (this.rallyArmyData != null) {
            Struct.SpyArmyData.Builder tmpBuilder = Struct.SpyArmyData.newBuilder();
            final int tmpFieldCnt = this.rallyArmyData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRallyArmyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRallyArmyData();
            }
        }  else if (builder.hasRallyArmyData()) {
            // 清理RallyArmyData
            builder.clearRallyArmyData();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SpyBuildingData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURHP)) {
            builder.setCurHp(this.getCurHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXHP)) {
            builder.setMaxHp(this.getMaxHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLYARMYDATA) && this.rallyArmyData != null) {
            final boolean needClear = !builder.hasRallyArmyData();
            final int tmpFieldCnt = this.rallyArmyData.copyChangeToSs(builder.getRallyArmyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRallyArmyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SpyBuildingData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurHp()) {
            this.innerSetCurHp(proto.getCurHp());
        } else {
            this.innerSetCurHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMaxHp()) {
            this.innerSetMaxHp(proto.getMaxHp());
        } else {
            this.innerSetMaxHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRallyArmyData()) {
            this.getRallyArmyData().mergeFromSs(proto.getRallyArmyData());
        } else {
            if (this.rallyArmyData != null) {
                this.rallyArmyData.mergeFromSs(proto.getRallyArmyData());
            }
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SpyBuildingDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SpyBuildingData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurHp()) {
            this.setCurHp(proto.getCurHp());
            fieldCnt++;
        }
        if (proto.hasMaxHp()) {
            this.setMaxHp(proto.getMaxHp());
            fieldCnt++;
        }
        if (proto.hasRallyArmyData()) {
            this.getRallyArmyData().mergeChangeFromSs(proto.getRallyArmyData());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SpyBuildingData.Builder builder = SpyBuildingData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_RALLYARMYDATA) && this.rallyArmyData != null) {
            this.rallyArmyData.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.rallyArmyData != null) {
            this.rallyArmyData.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SpyBuildingDataProp)) {
            return false;
        }
        final SpyBuildingDataProp otherNode = (SpyBuildingDataProp) node;
        if (this.curHp != otherNode.curHp) {
            return false;
        }
        if (this.maxHp != otherNode.maxHp) {
            return false;
        }
        if (!this.getRallyArmyData().compareDataTo(otherNode.getRallyArmyData())) {
            return false;
        }
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
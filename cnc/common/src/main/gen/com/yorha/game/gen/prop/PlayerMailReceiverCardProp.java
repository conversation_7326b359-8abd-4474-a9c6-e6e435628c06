package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructMail.PlayerMailReceiverCard;
import com.yorha.proto.StructMailPB.PlayerMailReceiverCardPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerMailReceiverCardProp extends AbstractPropNode {

    public static final int FIELD_INDEX_RECEIVERID = 0;
    public static final int FIELD_INDEX_PIC = 1;
    public static final int FIELD_INDEX_PICFRAME = 2;
    public static final int FIELD_INDEX_PICURL = 3;
    public static final int FIELD_INDEX_ZONEID = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private long receiverId = Constant.DEFAULT_LONG_VALUE;
    private int pic = Constant.DEFAULT_INT_VALUE;
    private int picFrame = Constant.DEFAULT_INT_VALUE;
    private String picUrl = Constant.DEFAULT_STR_VALUE;
    private int zoneId = Constant.DEFAULT_INT_VALUE;

    public PlayerMailReceiverCardProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerMailReceiverCardProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get receiverId
     *
     * @return receiverId value
     */
    public long getReceiverId() {
        return this.receiverId;
    }

    /**
     * set receiverId && set marked
     *
     * @param receiverId new value
     * @return current object
     */
    public PlayerMailReceiverCardProp setReceiverId(long receiverId) {
        if (this.receiverId != receiverId) {
            this.mark(FIELD_INDEX_RECEIVERID);
            this.receiverId = receiverId;
        }
        return this;
    }

    /**
     * inner set receiverId
     *
     * @param receiverId new value
     */
    private void innerSetReceiverId(long receiverId) {
        this.receiverId = receiverId;
    }

    /**
     * get pic
     *
     * @return pic value
     */
    public int getPic() {
        return this.pic;
    }

    /**
     * set pic && set marked
     *
     * @param pic new value
     * @return current object
     */
    public PlayerMailReceiverCardProp setPic(int pic) {
        if (this.pic != pic) {
            this.mark(FIELD_INDEX_PIC);
            this.pic = pic;
        }
        return this;
    }

    /**
     * inner set pic
     *
     * @param pic new value
     */
    private void innerSetPic(int pic) {
        this.pic = pic;
    }

    /**
     * get picFrame
     *
     * @return picFrame value
     */
    public int getPicFrame() {
        return this.picFrame;
    }

    /**
     * set picFrame && set marked
     *
     * @param picFrame new value
     * @return current object
     */
    public PlayerMailReceiverCardProp setPicFrame(int picFrame) {
        if (this.picFrame != picFrame) {
            this.mark(FIELD_INDEX_PICFRAME);
            this.picFrame = picFrame;
        }
        return this;
    }

    /**
     * inner set picFrame
     *
     * @param picFrame new value
     */
    private void innerSetPicFrame(int picFrame) {
        this.picFrame = picFrame;
    }

    /**
     * get picUrl
     *
     * @return picUrl value
     */
    public String getPicUrl() {
        return this.picUrl;
    }

    /**
     * set picUrl && set marked
     *
     * @param picUrl new value
     * @return current object
     */
    public PlayerMailReceiverCardProp setPicUrl(String picUrl) {
        if (picUrl == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.picUrl, picUrl)) {
            this.mark(FIELD_INDEX_PICURL);
            this.picUrl = picUrl;
        }
        return this;
    }

    /**
     * inner set picUrl
     *
     * @param picUrl new value
     */
    private void innerSetPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    /**
     * get zoneId
     *
     * @return zoneId value
     */
    public int getZoneId() {
        return this.zoneId;
    }

    /**
     * set zoneId && set marked
     *
     * @param zoneId new value
     * @return current object
     */
    public PlayerMailReceiverCardProp setZoneId(int zoneId) {
        if (this.zoneId != zoneId) {
            this.mark(FIELD_INDEX_ZONEID);
            this.zoneId = zoneId;
        }
        return this;
    }

    /**
     * inner set zoneId
     *
     * @param zoneId new value
     */
    private void innerSetZoneId(int zoneId) {
        this.zoneId = zoneId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMailReceiverCardPB.Builder getCopyCsBuilder() {
        final PlayerMailReceiverCardPB.Builder builder = PlayerMailReceiverCardPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerMailReceiverCardPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getReceiverId() != 0L) {
            builder.setReceiverId(this.getReceiverId());
            fieldCnt++;
        }  else if (builder.hasReceiverId()) {
            // 清理ReceiverId
            builder.clearReceiverId();
            fieldCnt++;
        }
        if (this.getPic() != 0) {
            builder.setPic(this.getPic());
            fieldCnt++;
        }  else if (builder.hasPic()) {
            // 清理Pic
            builder.clearPic();
            fieldCnt++;
        }
        if (this.getPicFrame() != 0) {
            builder.setPicFrame(this.getPicFrame());
            fieldCnt++;
        }  else if (builder.hasPicFrame()) {
            // 清理PicFrame
            builder.clearPicFrame();
            fieldCnt++;
        }
        if (!this.getPicUrl().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setPicUrl(this.getPicUrl());
            fieldCnt++;
        }  else if (builder.hasPicUrl()) {
            // 清理PicUrl
            builder.clearPicUrl();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerMailReceiverCardPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECEIVERID)) {
            builder.setReceiverId(this.getReceiverId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PIC)) {
            builder.setPic(this.getPic());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICFRAME)) {
            builder.setPicFrame(this.getPicFrame());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICURL)) {
            builder.setPicUrl(this.getPicUrl());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerMailReceiverCardPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECEIVERID)) {
            builder.setReceiverId(this.getReceiverId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PIC)) {
            builder.setPic(this.getPic());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICFRAME)) {
            builder.setPicFrame(this.getPicFrame());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICURL)) {
            builder.setPicUrl(this.getPicUrl());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerMailReceiverCardPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasReceiverId()) {
            this.innerSetReceiverId(proto.getReceiverId());
        } else {
            this.innerSetReceiverId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPic()) {
            this.innerSetPic(proto.getPic());
        } else {
            this.innerSetPic(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPicFrame()) {
            this.innerSetPicFrame(proto.getPicFrame());
        } else {
            this.innerSetPicFrame(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPicUrl()) {
            this.innerSetPicUrl(proto.getPicUrl());
        } else {
            this.innerSetPicUrl(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerMailReceiverCardProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerMailReceiverCardPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasReceiverId()) {
            this.setReceiverId(proto.getReceiverId());
            fieldCnt++;
        }
        if (proto.hasPic()) {
            this.setPic(proto.getPic());
            fieldCnt++;
        }
        if (proto.hasPicFrame()) {
            this.setPicFrame(proto.getPicFrame());
            fieldCnt++;
        }
        if (proto.hasPicUrl()) {
            this.setPicUrl(proto.getPicUrl());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMailReceiverCard.Builder getCopyDbBuilder() {
        final PlayerMailReceiverCard.Builder builder = PlayerMailReceiverCard.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerMailReceiverCard.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getReceiverId() != 0L) {
            builder.setReceiverId(this.getReceiverId());
            fieldCnt++;
        }  else if (builder.hasReceiverId()) {
            // 清理ReceiverId
            builder.clearReceiverId();
            fieldCnt++;
        }
        if (this.getPic() != 0) {
            builder.setPic(this.getPic());
            fieldCnt++;
        }  else if (builder.hasPic()) {
            // 清理Pic
            builder.clearPic();
            fieldCnt++;
        }
        if (this.getPicFrame() != 0) {
            builder.setPicFrame(this.getPicFrame());
            fieldCnt++;
        }  else if (builder.hasPicFrame()) {
            // 清理PicFrame
            builder.clearPicFrame();
            fieldCnt++;
        }
        if (!this.getPicUrl().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setPicUrl(this.getPicUrl());
            fieldCnt++;
        }  else if (builder.hasPicUrl()) {
            // 清理PicUrl
            builder.clearPicUrl();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerMailReceiverCard.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECEIVERID)) {
            builder.setReceiverId(this.getReceiverId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PIC)) {
            builder.setPic(this.getPic());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICFRAME)) {
            builder.setPicFrame(this.getPicFrame());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICURL)) {
            builder.setPicUrl(this.getPicUrl());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerMailReceiverCard proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasReceiverId()) {
            this.innerSetReceiverId(proto.getReceiverId());
        } else {
            this.innerSetReceiverId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPic()) {
            this.innerSetPic(proto.getPic());
        } else {
            this.innerSetPic(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPicFrame()) {
            this.innerSetPicFrame(proto.getPicFrame());
        } else {
            this.innerSetPicFrame(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPicUrl()) {
            this.innerSetPicUrl(proto.getPicUrl());
        } else {
            this.innerSetPicUrl(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerMailReceiverCardProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerMailReceiverCard proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasReceiverId()) {
            this.setReceiverId(proto.getReceiverId());
            fieldCnt++;
        }
        if (proto.hasPic()) {
            this.setPic(proto.getPic());
            fieldCnt++;
        }
        if (proto.hasPicFrame()) {
            this.setPicFrame(proto.getPicFrame());
            fieldCnt++;
        }
        if (proto.hasPicUrl()) {
            this.setPicUrl(proto.getPicUrl());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMailReceiverCard.Builder getCopySsBuilder() {
        final PlayerMailReceiverCard.Builder builder = PlayerMailReceiverCard.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerMailReceiverCard.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getReceiverId() != 0L) {
            builder.setReceiverId(this.getReceiverId());
            fieldCnt++;
        }  else if (builder.hasReceiverId()) {
            // 清理ReceiverId
            builder.clearReceiverId();
            fieldCnt++;
        }
        if (this.getPic() != 0) {
            builder.setPic(this.getPic());
            fieldCnt++;
        }  else if (builder.hasPic()) {
            // 清理Pic
            builder.clearPic();
            fieldCnt++;
        }
        if (this.getPicFrame() != 0) {
            builder.setPicFrame(this.getPicFrame());
            fieldCnt++;
        }  else if (builder.hasPicFrame()) {
            // 清理PicFrame
            builder.clearPicFrame();
            fieldCnt++;
        }
        if (!this.getPicUrl().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setPicUrl(this.getPicUrl());
            fieldCnt++;
        }  else if (builder.hasPicUrl()) {
            // 清理PicUrl
            builder.clearPicUrl();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerMailReceiverCard.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECEIVERID)) {
            builder.setReceiverId(this.getReceiverId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PIC)) {
            builder.setPic(this.getPic());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICFRAME)) {
            builder.setPicFrame(this.getPicFrame());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICURL)) {
            builder.setPicUrl(this.getPicUrl());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerMailReceiverCard proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasReceiverId()) {
            this.innerSetReceiverId(proto.getReceiverId());
        } else {
            this.innerSetReceiverId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPic()) {
            this.innerSetPic(proto.getPic());
        } else {
            this.innerSetPic(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPicFrame()) {
            this.innerSetPicFrame(proto.getPicFrame());
        } else {
            this.innerSetPicFrame(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPicUrl()) {
            this.innerSetPicUrl(proto.getPicUrl());
        } else {
            this.innerSetPicUrl(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerMailReceiverCardProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerMailReceiverCard proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasReceiverId()) {
            this.setReceiverId(proto.getReceiverId());
            fieldCnt++;
        }
        if (proto.hasPic()) {
            this.setPic(proto.getPic());
            fieldCnt++;
        }
        if (proto.hasPicFrame()) {
            this.setPicFrame(proto.getPicFrame());
            fieldCnt++;
        }
        if (proto.hasPicUrl()) {
            this.setPicUrl(proto.getPicUrl());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerMailReceiverCard.Builder builder = PlayerMailReceiverCard.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerMailReceiverCardProp)) {
            return false;
        }
        final PlayerMailReceiverCardProp otherNode = (PlayerMailReceiverCardProp) node;
        if (this.receiverId != otherNode.receiverId) {
            return false;
        }
        if (this.pic != otherNode.pic) {
            return false;
        }
        if (this.picFrame != otherNode.picFrame) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.picUrl, otherNode.picUrl)) {
            return false;
        }
        if (this.zoneId != otherNode.zoneId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
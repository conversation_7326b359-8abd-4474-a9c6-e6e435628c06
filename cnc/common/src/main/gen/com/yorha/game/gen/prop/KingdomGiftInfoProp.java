package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zone.KingdomGiftInfo;
import com.yorha.proto.Zone;
import com.yorha.proto.ZonePB.KingdomGiftInfoPB;
import com.yorha.proto.ZonePB;


/**
 * <AUTHOR> auto gen
 */
public class KingdomGiftInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_GIFTID = 0;
    public static final int FIELD_INDEX_SENTGIFTINFO = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int giftId = Constant.DEFAULT_INT_VALUE;
    private Int64KingdomSentGiftInfoMapProp sentGiftInfo = null;

    public KingdomGiftInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public KingdomGiftInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get giftId
     *
     * @return giftId value
     */
    public int getGiftId() {
        return this.giftId;
    }

    /**
     * set giftId && set marked
     *
     * @param giftId new value
     * @return current object
     */
    public KingdomGiftInfoProp setGiftId(int giftId) {
        if (this.giftId != giftId) {
            this.mark(FIELD_INDEX_GIFTID);
            this.giftId = giftId;
        }
        return this;
    }

    /**
     * inner set giftId
     *
     * @param giftId new value
     */
    private void innerSetGiftId(int giftId) {
        this.giftId = giftId;
    }

    /**
     * get sentGiftInfo
     *
     * @return sentGiftInfo value
     */
    public Int64KingdomSentGiftInfoMapProp getSentGiftInfo() {
        if (this.sentGiftInfo == null) {
            this.sentGiftInfo = new Int64KingdomSentGiftInfoMapProp(this, FIELD_INDEX_SENTGIFTINFO);
        }
        return this.sentGiftInfo;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putSentGiftInfoV(KingdomSentGiftInfoProp v) {
        this.getSentGiftInfo().put(v.getPlayerId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public KingdomSentGiftInfoProp addEmptySentGiftInfo(Long k) {
        return this.getSentGiftInfo().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getSentGiftInfoSize() {
        if (this.sentGiftInfo == null) {
            return 0;
        }
        return this.sentGiftInfo.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isSentGiftInfoEmpty() {
        if (this.sentGiftInfo == null) {
            return true;
        }
        return this.sentGiftInfo.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public KingdomSentGiftInfoProp getSentGiftInfoV(Long k) {
        if (this.sentGiftInfo == null || !this.sentGiftInfo.containsKey(k)) {
            return null;
        }
        return this.sentGiftInfo.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearSentGiftInfo() {
        if (this.sentGiftInfo != null) {
            this.sentGiftInfo.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeSentGiftInfoV(Long k) {
        if (this.sentGiftInfo != null) {
            this.sentGiftInfo.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomGiftInfoPB.Builder getCopyCsBuilder() {
        final KingdomGiftInfoPB.Builder builder = KingdomGiftInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(KingdomGiftInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGiftId() != 0) {
            builder.setGiftId(this.getGiftId());
            fieldCnt++;
        }  else if (builder.hasGiftId()) {
            // 清理GiftId
            builder.clearGiftId();
            fieldCnt++;
        }
        if (this.sentGiftInfo != null) {
            ZonePB.Int64KingdomSentGiftInfoMapPB.Builder tmpBuilder = ZonePB.Int64KingdomSentGiftInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.sentGiftInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSentGiftInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSentGiftInfo();
            }
        }  else if (builder.hasSentGiftInfo()) {
            // 清理SentGiftInfo
            builder.clearSentGiftInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(KingdomGiftInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GIFTID)) {
            builder.setGiftId(this.getGiftId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SENTGIFTINFO) && this.sentGiftInfo != null) {
            final boolean needClear = !builder.hasSentGiftInfo();
            final int tmpFieldCnt = this.sentGiftInfo.copyChangeToCs(builder.getSentGiftInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSentGiftInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(KingdomGiftInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GIFTID)) {
            builder.setGiftId(this.getGiftId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SENTGIFTINFO) && this.sentGiftInfo != null) {
            final boolean needClear = !builder.hasSentGiftInfo();
            final int tmpFieldCnt = this.sentGiftInfo.copyChangeToAndClearDeleteKeysCs(builder.getSentGiftInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSentGiftInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(KingdomGiftInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGiftId()) {
            this.innerSetGiftId(proto.getGiftId());
        } else {
            this.innerSetGiftId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSentGiftInfo()) {
            this.getSentGiftInfo().mergeFromCs(proto.getSentGiftInfo());
        } else {
            if (this.sentGiftInfo != null) {
                this.sentGiftInfo.mergeFromCs(proto.getSentGiftInfo());
            }
        }
        this.markAll();
        return KingdomGiftInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(KingdomGiftInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGiftId()) {
            this.setGiftId(proto.getGiftId());
            fieldCnt++;
        }
        if (proto.hasSentGiftInfo()) {
            this.getSentGiftInfo().mergeChangeFromCs(proto.getSentGiftInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomGiftInfo.Builder getCopyDbBuilder() {
        final KingdomGiftInfo.Builder builder = KingdomGiftInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(KingdomGiftInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGiftId() != 0) {
            builder.setGiftId(this.getGiftId());
            fieldCnt++;
        }  else if (builder.hasGiftId()) {
            // 清理GiftId
            builder.clearGiftId();
            fieldCnt++;
        }
        if (this.sentGiftInfo != null) {
            Zone.Int64KingdomSentGiftInfoMap.Builder tmpBuilder = Zone.Int64KingdomSentGiftInfoMap.newBuilder();
            final int tmpFieldCnt = this.sentGiftInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSentGiftInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSentGiftInfo();
            }
        }  else if (builder.hasSentGiftInfo()) {
            // 清理SentGiftInfo
            builder.clearSentGiftInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(KingdomGiftInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GIFTID)) {
            builder.setGiftId(this.getGiftId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SENTGIFTINFO) && this.sentGiftInfo != null) {
            final boolean needClear = !builder.hasSentGiftInfo();
            final int tmpFieldCnt = this.sentGiftInfo.copyChangeToDb(builder.getSentGiftInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSentGiftInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(KingdomGiftInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGiftId()) {
            this.innerSetGiftId(proto.getGiftId());
        } else {
            this.innerSetGiftId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSentGiftInfo()) {
            this.getSentGiftInfo().mergeFromDb(proto.getSentGiftInfo());
        } else {
            if (this.sentGiftInfo != null) {
                this.sentGiftInfo.mergeFromDb(proto.getSentGiftInfo());
            }
        }
        this.markAll();
        return KingdomGiftInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(KingdomGiftInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGiftId()) {
            this.setGiftId(proto.getGiftId());
            fieldCnt++;
        }
        if (proto.hasSentGiftInfo()) {
            this.getSentGiftInfo().mergeChangeFromDb(proto.getSentGiftInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomGiftInfo.Builder getCopySsBuilder() {
        final KingdomGiftInfo.Builder builder = KingdomGiftInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(KingdomGiftInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGiftId() != 0) {
            builder.setGiftId(this.getGiftId());
            fieldCnt++;
        }  else if (builder.hasGiftId()) {
            // 清理GiftId
            builder.clearGiftId();
            fieldCnt++;
        }
        if (this.sentGiftInfo != null) {
            Zone.Int64KingdomSentGiftInfoMap.Builder tmpBuilder = Zone.Int64KingdomSentGiftInfoMap.newBuilder();
            final int tmpFieldCnt = this.sentGiftInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSentGiftInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSentGiftInfo();
            }
        }  else if (builder.hasSentGiftInfo()) {
            // 清理SentGiftInfo
            builder.clearSentGiftInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(KingdomGiftInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GIFTID)) {
            builder.setGiftId(this.getGiftId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SENTGIFTINFO) && this.sentGiftInfo != null) {
            final boolean needClear = !builder.hasSentGiftInfo();
            final int tmpFieldCnt = this.sentGiftInfo.copyChangeToSs(builder.getSentGiftInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSentGiftInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(KingdomGiftInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGiftId()) {
            this.innerSetGiftId(proto.getGiftId());
        } else {
            this.innerSetGiftId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSentGiftInfo()) {
            this.getSentGiftInfo().mergeFromSs(proto.getSentGiftInfo());
        } else {
            if (this.sentGiftInfo != null) {
                this.sentGiftInfo.mergeFromSs(proto.getSentGiftInfo());
            }
        }
        this.markAll();
        return KingdomGiftInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(KingdomGiftInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGiftId()) {
            this.setGiftId(proto.getGiftId());
            fieldCnt++;
        }
        if (proto.hasSentGiftInfo()) {
            this.getSentGiftInfo().mergeChangeFromSs(proto.getSentGiftInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        KingdomGiftInfo.Builder builder = KingdomGiftInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SENTGIFTINFO) && this.sentGiftInfo != null) {
            this.sentGiftInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.sentGiftInfo != null) {
            this.sentGiftInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.giftId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof KingdomGiftInfoProp)) {
            return false;
        }
        final KingdomGiftInfoProp otherNode = (KingdomGiftInfoProp) node;
        if (this.giftId != otherNode.giftId) {
            return false;
        }
        if (!this.getSentGiftInfo().compareDataTo(otherNode.getSentGiftInfo())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
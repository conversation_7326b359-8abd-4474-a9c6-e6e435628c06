package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerNewbieModel;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.PlayerNewbieModelPB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerNewbieModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ISNEWBIE = 0;
    public static final int FIELD_INDEX_STEPINDEX = 1;
    public static final int FIELD_INDEX_MAPID = 2;
    public static final int FIELD_INDEX_STEPNICKNAME = 3;
    public static final int FIELD_INDEX_STAGEINFO = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private boolean isNewbie = Constant.DEFAULT_BOOLEAN_VALUE;
    private int stepIndex = Constant.DEFAULT_INT_VALUE;
    private int mapId = Constant.DEFAULT_INT_VALUE;
    private String stepNickname = Constant.DEFAULT_STR_VALUE;
    private Int32ClientStageInfoMapProp stageInfo = null;

    public PlayerNewbieModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerNewbieModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get isNewbie
     *
     * @return isNewbie value
     */
    public boolean getIsNewbie() {
        return this.isNewbie;
    }

    /**
     * set isNewbie && set marked
     *
     * @param isNewbie new value
     * @return current object
     */
    public PlayerNewbieModelProp setIsNewbie(boolean isNewbie) {
        if (this.isNewbie != isNewbie) {
            this.mark(FIELD_INDEX_ISNEWBIE);
            this.isNewbie = isNewbie;
        }
        return this;
    }

    /**
     * inner set isNewbie
     *
     * @param isNewbie new value
     */
    private void innerSetIsNewbie(boolean isNewbie) {
        this.isNewbie = isNewbie;
    }

    /**
     * get stepIndex
     *
     * @return stepIndex value
     */
    public int getStepIndex() {
        return this.stepIndex;
    }

    /**
     * set stepIndex && set marked
     *
     * @param stepIndex new value
     * @return current object
     */
    public PlayerNewbieModelProp setStepIndex(int stepIndex) {
        if (this.stepIndex != stepIndex) {
            this.mark(FIELD_INDEX_STEPINDEX);
            this.stepIndex = stepIndex;
        }
        return this;
    }

    /**
     * inner set stepIndex
     *
     * @param stepIndex new value
     */
    private void innerSetStepIndex(int stepIndex) {
        this.stepIndex = stepIndex;
    }

    /**
     * get mapId
     *
     * @return mapId value
     */
    public int getMapId() {
        return this.mapId;
    }

    /**
     * set mapId && set marked
     *
     * @param mapId new value
     * @return current object
     */
    public PlayerNewbieModelProp setMapId(int mapId) {
        if (this.mapId != mapId) {
            this.mark(FIELD_INDEX_MAPID);
            this.mapId = mapId;
        }
        return this;
    }

    /**
     * inner set mapId
     *
     * @param mapId new value
     */
    private void innerSetMapId(int mapId) {
        this.mapId = mapId;
    }

    /**
     * get stepNickname
     *
     * @return stepNickname value
     */
    public String getStepNickname() {
        return this.stepNickname;
    }

    /**
     * set stepNickname && set marked
     *
     * @param stepNickname new value
     * @return current object
     */
    public PlayerNewbieModelProp setStepNickname(String stepNickname) {
        if (stepNickname == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.stepNickname, stepNickname)) {
            this.mark(FIELD_INDEX_STEPNICKNAME);
            this.stepNickname = stepNickname;
        }
        return this;
    }

    /**
     * inner set stepNickname
     *
     * @param stepNickname new value
     */
    private void innerSetStepNickname(String stepNickname) {
        this.stepNickname = stepNickname;
    }

    /**
     * get stageInfo
     *
     * @return stageInfo value
     */
    public Int32ClientStageInfoMapProp getStageInfo() {
        if (this.stageInfo == null) {
            this.stageInfo = new Int32ClientStageInfoMapProp(this, FIELD_INDEX_STAGEINFO);
        }
        return this.stageInfo;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putStageInfoV(ClientStageInfoProp v) {
        this.getStageInfo().put(v.getGroupId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ClientStageInfoProp addEmptyStageInfo(Integer k) {
        return this.getStageInfo().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getStageInfoSize() {
        if (this.stageInfo == null) {
            return 0;
        }
        return this.stageInfo.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isStageInfoEmpty() {
        if (this.stageInfo == null) {
            return true;
        }
        return this.stageInfo.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ClientStageInfoProp getStageInfoV(Integer k) {
        if (this.stageInfo == null || !this.stageInfo.containsKey(k)) {
            return null;
        }
        return this.stageInfo.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearStageInfo() {
        if (this.stageInfo != null) {
            this.stageInfo.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeStageInfoV(Integer k) {
        if (this.stageInfo != null) {
            this.stageInfo.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerNewbieModelPB.Builder getCopyCsBuilder() {
        final PlayerNewbieModelPB.Builder builder = PlayerNewbieModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerNewbieModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIsNewbie()) {
            builder.setIsNewbie(this.getIsNewbie());
            fieldCnt++;
        }  else if (builder.hasIsNewbie()) {
            // 清理IsNewbie
            builder.clearIsNewbie();
            fieldCnt++;
        }
        if (this.getStepIndex() != 0) {
            builder.setStepIndex(this.getStepIndex());
            fieldCnt++;
        }  else if (builder.hasStepIndex()) {
            // 清理StepIndex
            builder.clearStepIndex();
            fieldCnt++;
        }
        if (this.getMapId() != 0) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }  else if (builder.hasMapId()) {
            // 清理MapId
            builder.clearMapId();
            fieldCnt++;
        }
        if (!this.getStepNickname().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setStepNickname(this.getStepNickname());
            fieldCnt++;
        }  else if (builder.hasStepNickname()) {
            // 清理StepNickname
            builder.clearStepNickname();
            fieldCnt++;
        }
        if (this.stageInfo != null) {
            PlayerPB.Int32ClientStageInfoMapPB.Builder tmpBuilder = PlayerPB.Int32ClientStageInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.stageInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStageInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStageInfo();
            }
        }  else if (builder.hasStageInfo()) {
            // 清理StageInfo
            builder.clearStageInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerNewbieModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISNEWBIE)) {
            builder.setIsNewbie(this.getIsNewbie());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STEPINDEX)) {
            builder.setStepIndex(this.getStepIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPID)) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STEPNICKNAME)) {
            builder.setStepNickname(this.getStepNickname());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGEINFO) && this.stageInfo != null) {
            final boolean needClear = !builder.hasStageInfo();
            final int tmpFieldCnt = this.stageInfo.copyChangeToCs(builder.getStageInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStageInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerNewbieModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISNEWBIE)) {
            builder.setIsNewbie(this.getIsNewbie());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STEPINDEX)) {
            builder.setStepIndex(this.getStepIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPID)) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STEPNICKNAME)) {
            builder.setStepNickname(this.getStepNickname());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGEINFO) && this.stageInfo != null) {
            final boolean needClear = !builder.hasStageInfo();
            final int tmpFieldCnt = this.stageInfo.copyChangeToAndClearDeleteKeysCs(builder.getStageInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStageInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerNewbieModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIsNewbie()) {
            this.innerSetIsNewbie(proto.getIsNewbie());
        } else {
            this.innerSetIsNewbie(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasStepIndex()) {
            this.innerSetStepIndex(proto.getStepIndex());
        } else {
            this.innerSetStepIndex(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMapId()) {
            this.innerSetMapId(proto.getMapId());
        } else {
            this.innerSetMapId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStepNickname()) {
            this.innerSetStepNickname(proto.getStepNickname());
        } else {
            this.innerSetStepNickname(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasStageInfo()) {
            this.getStageInfo().mergeFromCs(proto.getStageInfo());
        } else {
            if (this.stageInfo != null) {
                this.stageInfo.mergeFromCs(proto.getStageInfo());
            }
        }
        this.markAll();
        return PlayerNewbieModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerNewbieModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIsNewbie()) {
            this.setIsNewbie(proto.getIsNewbie());
            fieldCnt++;
        }
        if (proto.hasStepIndex()) {
            this.setStepIndex(proto.getStepIndex());
            fieldCnt++;
        }
        if (proto.hasMapId()) {
            this.setMapId(proto.getMapId());
            fieldCnt++;
        }
        if (proto.hasStepNickname()) {
            this.setStepNickname(proto.getStepNickname());
            fieldCnt++;
        }
        if (proto.hasStageInfo()) {
            this.getStageInfo().mergeChangeFromCs(proto.getStageInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerNewbieModel.Builder getCopyDbBuilder() {
        final PlayerNewbieModel.Builder builder = PlayerNewbieModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerNewbieModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIsNewbie()) {
            builder.setIsNewbie(this.getIsNewbie());
            fieldCnt++;
        }  else if (builder.hasIsNewbie()) {
            // 清理IsNewbie
            builder.clearIsNewbie();
            fieldCnt++;
        }
        if (this.getStepIndex() != 0) {
            builder.setStepIndex(this.getStepIndex());
            fieldCnt++;
        }  else if (builder.hasStepIndex()) {
            // 清理StepIndex
            builder.clearStepIndex();
            fieldCnt++;
        }
        if (this.getMapId() != 0) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }  else if (builder.hasMapId()) {
            // 清理MapId
            builder.clearMapId();
            fieldCnt++;
        }
        if (!this.getStepNickname().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setStepNickname(this.getStepNickname());
            fieldCnt++;
        }  else if (builder.hasStepNickname()) {
            // 清理StepNickname
            builder.clearStepNickname();
            fieldCnt++;
        }
        if (this.stageInfo != null) {
            Player.Int32ClientStageInfoMap.Builder tmpBuilder = Player.Int32ClientStageInfoMap.newBuilder();
            final int tmpFieldCnt = this.stageInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStageInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStageInfo();
            }
        }  else if (builder.hasStageInfo()) {
            // 清理StageInfo
            builder.clearStageInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerNewbieModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISNEWBIE)) {
            builder.setIsNewbie(this.getIsNewbie());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STEPINDEX)) {
            builder.setStepIndex(this.getStepIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPID)) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STEPNICKNAME)) {
            builder.setStepNickname(this.getStepNickname());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGEINFO) && this.stageInfo != null) {
            final boolean needClear = !builder.hasStageInfo();
            final int tmpFieldCnt = this.stageInfo.copyChangeToDb(builder.getStageInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStageInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerNewbieModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIsNewbie()) {
            this.innerSetIsNewbie(proto.getIsNewbie());
        } else {
            this.innerSetIsNewbie(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasStepIndex()) {
            this.innerSetStepIndex(proto.getStepIndex());
        } else {
            this.innerSetStepIndex(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMapId()) {
            this.innerSetMapId(proto.getMapId());
        } else {
            this.innerSetMapId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStepNickname()) {
            this.innerSetStepNickname(proto.getStepNickname());
        } else {
            this.innerSetStepNickname(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasStageInfo()) {
            this.getStageInfo().mergeFromDb(proto.getStageInfo());
        } else {
            if (this.stageInfo != null) {
                this.stageInfo.mergeFromDb(proto.getStageInfo());
            }
        }
        this.markAll();
        return PlayerNewbieModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerNewbieModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIsNewbie()) {
            this.setIsNewbie(proto.getIsNewbie());
            fieldCnt++;
        }
        if (proto.hasStepIndex()) {
            this.setStepIndex(proto.getStepIndex());
            fieldCnt++;
        }
        if (proto.hasMapId()) {
            this.setMapId(proto.getMapId());
            fieldCnt++;
        }
        if (proto.hasStepNickname()) {
            this.setStepNickname(proto.getStepNickname());
            fieldCnt++;
        }
        if (proto.hasStageInfo()) {
            this.getStageInfo().mergeChangeFromDb(proto.getStageInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerNewbieModel.Builder getCopySsBuilder() {
        final PlayerNewbieModel.Builder builder = PlayerNewbieModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerNewbieModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIsNewbie()) {
            builder.setIsNewbie(this.getIsNewbie());
            fieldCnt++;
        }  else if (builder.hasIsNewbie()) {
            // 清理IsNewbie
            builder.clearIsNewbie();
            fieldCnt++;
        }
        if (this.getStepIndex() != 0) {
            builder.setStepIndex(this.getStepIndex());
            fieldCnt++;
        }  else if (builder.hasStepIndex()) {
            // 清理StepIndex
            builder.clearStepIndex();
            fieldCnt++;
        }
        if (this.getMapId() != 0) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }  else if (builder.hasMapId()) {
            // 清理MapId
            builder.clearMapId();
            fieldCnt++;
        }
        if (!this.getStepNickname().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setStepNickname(this.getStepNickname());
            fieldCnt++;
        }  else if (builder.hasStepNickname()) {
            // 清理StepNickname
            builder.clearStepNickname();
            fieldCnt++;
        }
        if (this.stageInfo != null) {
            Player.Int32ClientStageInfoMap.Builder tmpBuilder = Player.Int32ClientStageInfoMap.newBuilder();
            final int tmpFieldCnt = this.stageInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStageInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStageInfo();
            }
        }  else if (builder.hasStageInfo()) {
            // 清理StageInfo
            builder.clearStageInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerNewbieModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISNEWBIE)) {
            builder.setIsNewbie(this.getIsNewbie());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STEPINDEX)) {
            builder.setStepIndex(this.getStepIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPID)) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STEPNICKNAME)) {
            builder.setStepNickname(this.getStepNickname());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGEINFO) && this.stageInfo != null) {
            final boolean needClear = !builder.hasStageInfo();
            final int tmpFieldCnt = this.stageInfo.copyChangeToSs(builder.getStageInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStageInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerNewbieModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIsNewbie()) {
            this.innerSetIsNewbie(proto.getIsNewbie());
        } else {
            this.innerSetIsNewbie(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasStepIndex()) {
            this.innerSetStepIndex(proto.getStepIndex());
        } else {
            this.innerSetStepIndex(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMapId()) {
            this.innerSetMapId(proto.getMapId());
        } else {
            this.innerSetMapId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStepNickname()) {
            this.innerSetStepNickname(proto.getStepNickname());
        } else {
            this.innerSetStepNickname(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasStageInfo()) {
            this.getStageInfo().mergeFromSs(proto.getStageInfo());
        } else {
            if (this.stageInfo != null) {
                this.stageInfo.mergeFromSs(proto.getStageInfo());
            }
        }
        this.markAll();
        return PlayerNewbieModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerNewbieModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIsNewbie()) {
            this.setIsNewbie(proto.getIsNewbie());
            fieldCnt++;
        }
        if (proto.hasStepIndex()) {
            this.setStepIndex(proto.getStepIndex());
            fieldCnt++;
        }
        if (proto.hasMapId()) {
            this.setMapId(proto.getMapId());
            fieldCnt++;
        }
        if (proto.hasStepNickname()) {
            this.setStepNickname(proto.getStepNickname());
            fieldCnt++;
        }
        if (proto.hasStageInfo()) {
            this.getStageInfo().mergeChangeFromSs(proto.getStageInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerNewbieModel.Builder builder = PlayerNewbieModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_STAGEINFO) && this.stageInfo != null) {
            this.stageInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.stageInfo != null) {
            this.stageInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerNewbieModelProp)) {
            return false;
        }
        final PlayerNewbieModelProp otherNode = (PlayerNewbieModelProp) node;
        if (this.isNewbie != otherNode.isNewbie) {
            return false;
        }
        if (this.stepIndex != otherNode.stepIndex) {
            return false;
        }
        if (this.mapId != otherNode.mapId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.stepNickname, otherNode.stepNickname)) {
            return false;
        }
        if (!this.getStageInfo().compareDataTo(otherNode.getStageInfo())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
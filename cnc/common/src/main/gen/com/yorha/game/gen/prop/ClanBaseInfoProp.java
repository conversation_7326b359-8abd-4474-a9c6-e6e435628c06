package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructClan.ClanBaseInfo;
import com.yorha.proto.StructClanPB.ClanBaseInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanBaseInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_SNAME = 0;
    public static final int FIELD_INDEX_NAME = 1;
    public static final int FIELD_INDEX_TERRITORYCOLOR = 2;
    public static final int FIELD_INDEX_FLAGCOLOR = 3;
    public static final int FIELD_INDEX_FLAGSHADING = 4;
    public static final int FIELD_INDEX_FLAGSIGN = 5;
    public static final int FIELD_INDEX_DESCRIBE = 6;
    public static final int FIELD_INDEX_LANGUAGE = 7;
    public static final int FIELD_INDEX_REQUIRE = 8;
    public static final int FIELD_INDEX_DESCRIBEMODFLAG = 9;
    public static final int FIELD_INDEX_WELCOMELETTER = 10;
    public static final int FIELD_INDEX_NATIONFLAGID = 11;

    public static final int FIELD_COUNT = 12;

    private long markBits0 = 0L;

    private String sname = Constant.DEFAULT_STR_VALUE;
    private String name = Constant.DEFAULT_STR_VALUE;
    private int territoryColor = Constant.DEFAULT_INT_VALUE;
    private int flagColor = Constant.DEFAULT_INT_VALUE;
    private int flagShading = Constant.DEFAULT_INT_VALUE;
    private int flagSign = Constant.DEFAULT_INT_VALUE;
    private String describe = Constant.DEFAULT_STR_VALUE;
    private Language language = Language.forNumber(0);
    private ClanEnterRequire require = ClanEnterRequire.forNumber(0);
    private boolean describeModFlag = Constant.DEFAULT_BOOLEAN_VALUE;
    private String welcomeLetter = Constant.DEFAULT_STR_VALUE;
    private int nationFlagId = Constant.DEFAULT_INT_VALUE;

    public ClanBaseInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanBaseInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get sname
     *
     * @return sname value
     */
    public String getSname() {
        return this.sname;
    }

    /**
     * set sname && set marked
     *
     * @param sname new value
     * @return current object
     */
    public ClanBaseInfoProp setSname(String sname) {
        if (sname == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.sname, sname)) {
            this.mark(FIELD_INDEX_SNAME);
            this.sname = sname;
        }
        return this;
    }

    /**
     * inner set sname
     *
     * @param sname new value
     */
    private void innerSetSname(String sname) {
        this.sname = sname;
    }

    /**
     * get name
     *
     * @return name value
     */
    public String getName() {
        return this.name;
    }

    /**
     * set name && set marked
     *
     * @param name new value
     * @return current object
     */
    public ClanBaseInfoProp setName(String name) {
        if (name == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.name, name)) {
            this.mark(FIELD_INDEX_NAME);
            this.name = name;
        }
        return this;
    }

    /**
     * inner set name
     *
     * @param name new value
     */
    private void innerSetName(String name) {
        this.name = name;
    }

    /**
     * get territoryColor
     *
     * @return territoryColor value
     */
    public int getTerritoryColor() {
        return this.territoryColor;
    }

    /**
     * set territoryColor && set marked
     *
     * @param territoryColor new value
     * @return current object
     */
    public ClanBaseInfoProp setTerritoryColor(int territoryColor) {
        if (this.territoryColor != territoryColor) {
            this.mark(FIELD_INDEX_TERRITORYCOLOR);
            this.territoryColor = territoryColor;
        }
        return this;
    }

    /**
     * inner set territoryColor
     *
     * @param territoryColor new value
     */
    private void innerSetTerritoryColor(int territoryColor) {
        this.territoryColor = territoryColor;
    }

    /**
     * get flagColor
     *
     * @return flagColor value
     */
    public int getFlagColor() {
        return this.flagColor;
    }

    /**
     * set flagColor && set marked
     *
     * @param flagColor new value
     * @return current object
     */
    public ClanBaseInfoProp setFlagColor(int flagColor) {
        if (this.flagColor != flagColor) {
            this.mark(FIELD_INDEX_FLAGCOLOR);
            this.flagColor = flagColor;
        }
        return this;
    }

    /**
     * inner set flagColor
     *
     * @param flagColor new value
     */
    private void innerSetFlagColor(int flagColor) {
        this.flagColor = flagColor;
    }

    /**
     * get flagShading
     *
     * @return flagShading value
     */
    public int getFlagShading() {
        return this.flagShading;
    }

    /**
     * set flagShading && set marked
     *
     * @param flagShading new value
     * @return current object
     */
    public ClanBaseInfoProp setFlagShading(int flagShading) {
        if (this.flagShading != flagShading) {
            this.mark(FIELD_INDEX_FLAGSHADING);
            this.flagShading = flagShading;
        }
        return this;
    }

    /**
     * inner set flagShading
     *
     * @param flagShading new value
     */
    private void innerSetFlagShading(int flagShading) {
        this.flagShading = flagShading;
    }

    /**
     * get flagSign
     *
     * @return flagSign value
     */
    public int getFlagSign() {
        return this.flagSign;
    }

    /**
     * set flagSign && set marked
     *
     * @param flagSign new value
     * @return current object
     */
    public ClanBaseInfoProp setFlagSign(int flagSign) {
        if (this.flagSign != flagSign) {
            this.mark(FIELD_INDEX_FLAGSIGN);
            this.flagSign = flagSign;
        }
        return this;
    }

    /**
     * inner set flagSign
     *
     * @param flagSign new value
     */
    private void innerSetFlagSign(int flagSign) {
        this.flagSign = flagSign;
    }

    /**
     * get describe
     *
     * @return describe value
     */
    public String getDescribe() {
        return this.describe;
    }

    /**
     * set describe && set marked
     *
     * @param describe new value
     * @return current object
     */
    public ClanBaseInfoProp setDescribe(String describe) {
        if (describe == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.describe, describe)) {
            this.mark(FIELD_INDEX_DESCRIBE);
            this.describe = describe;
        }
        return this;
    }

    /**
     * inner set describe
     *
     * @param describe new value
     */
    private void innerSetDescribe(String describe) {
        this.describe = describe;
    }

    /**
     * get language
     *
     * @return language value
     */
    public Language getLanguage() {
        return this.language;
    }

    /**
     * set language && set marked
     *
     * @param language new value
     * @return current object
     */
    public ClanBaseInfoProp setLanguage(Language language) {
        if (language == null) {
            throw new NullPointerException();
        }
        if (this.language != language) {
            this.mark(FIELD_INDEX_LANGUAGE);
            this.language = language;
        }
        return this;
    }

    /**
     * inner set language
     *
     * @param language new value
     */
    private void innerSetLanguage(Language language) {
        this.language = language;
    }

    /**
     * get require
     *
     * @return require value
     */
    public ClanEnterRequire getRequire() {
        return this.require;
    }

    /**
     * set require && set marked
     *
     * @param require new value
     * @return current object
     */
    public ClanBaseInfoProp setRequire(ClanEnterRequire require) {
        if (require == null) {
            throw new NullPointerException();
        }
        if (this.require != require) {
            this.mark(FIELD_INDEX_REQUIRE);
            this.require = require;
        }
        return this;
    }

    /**
     * inner set require
     *
     * @param require new value
     */
    private void innerSetRequire(ClanEnterRequire require) {
        this.require = require;
    }

    /**
     * get describeModFlag
     *
     * @return describeModFlag value
     */
    public boolean getDescribeModFlag() {
        return this.describeModFlag;
    }

    /**
     * set describeModFlag && set marked
     *
     * @param describeModFlag new value
     * @return current object
     */
    public ClanBaseInfoProp setDescribeModFlag(boolean describeModFlag) {
        if (this.describeModFlag != describeModFlag) {
            this.mark(FIELD_INDEX_DESCRIBEMODFLAG);
            this.describeModFlag = describeModFlag;
        }
        return this;
    }

    /**
     * inner set describeModFlag
     *
     * @param describeModFlag new value
     */
    private void innerSetDescribeModFlag(boolean describeModFlag) {
        this.describeModFlag = describeModFlag;
    }

    /**
     * get welcomeLetter
     *
     * @return welcomeLetter value
     */
    public String getWelcomeLetter() {
        return this.welcomeLetter;
    }

    /**
     * set welcomeLetter && set marked
     *
     * @param welcomeLetter new value
     * @return current object
     */
    public ClanBaseInfoProp setWelcomeLetter(String welcomeLetter) {
        if (welcomeLetter == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.welcomeLetter, welcomeLetter)) {
            this.mark(FIELD_INDEX_WELCOMELETTER);
            this.welcomeLetter = welcomeLetter;
        }
        return this;
    }

    /**
     * inner set welcomeLetter
     *
     * @param welcomeLetter new value
     */
    private void innerSetWelcomeLetter(String welcomeLetter) {
        this.welcomeLetter = welcomeLetter;
    }

    /**
     * get nationFlagId
     *
     * @return nationFlagId value
     */
    public int getNationFlagId() {
        return this.nationFlagId;
    }

    /**
     * set nationFlagId && set marked
     *
     * @param nationFlagId new value
     * @return current object
     */
    public ClanBaseInfoProp setNationFlagId(int nationFlagId) {
        if (this.nationFlagId != nationFlagId) {
            this.mark(FIELD_INDEX_NATIONFLAGID);
            this.nationFlagId = nationFlagId;
        }
        return this;
    }

    /**
     * inner set nationFlagId
     *
     * @param nationFlagId new value
     */
    private void innerSetNationFlagId(int nationFlagId) {
        this.nationFlagId = nationFlagId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanBaseInfoPB.Builder getCopyCsBuilder() {
        final ClanBaseInfoPB.Builder builder = ClanBaseInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanBaseInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getSname().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSname(this.getSname());
            fieldCnt++;
        }  else if (builder.hasSname()) {
            // 清理Sname
            builder.clearSname();
            fieldCnt++;
        }
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        if (this.getTerritoryColor() != 0) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }  else if (builder.hasTerritoryColor()) {
            // 清理TerritoryColor
            builder.clearTerritoryColor();
            fieldCnt++;
        }
        if (this.getFlagColor() != 0) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }  else if (builder.hasFlagColor()) {
            // 清理FlagColor
            builder.clearFlagColor();
            fieldCnt++;
        }
        if (this.getFlagShading() != 0) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }  else if (builder.hasFlagShading()) {
            // 清理FlagShading
            builder.clearFlagShading();
            fieldCnt++;
        }
        if (this.getFlagSign() != 0) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }  else if (builder.hasFlagSign()) {
            // 清理FlagSign
            builder.clearFlagSign();
            fieldCnt++;
        }
        if (!this.getDescribe().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setDescribe(this.getDescribe());
            fieldCnt++;
        }  else if (builder.hasDescribe()) {
            // 清理Describe
            builder.clearDescribe();
            fieldCnt++;
        }
        if (this.getLanguage() != Language.forNumber(0)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }  else if (builder.hasLanguage()) {
            // 清理Language
            builder.clearLanguage();
            fieldCnt++;
        }
        if (this.getRequire() != ClanEnterRequire.forNumber(0)) {
            builder.setRequire(this.getRequire());
            fieldCnt++;
        }  else if (builder.hasRequire()) {
            // 清理Require
            builder.clearRequire();
            fieldCnt++;
        }
        if (this.getDescribeModFlag()) {
            builder.setDescribeModFlag(this.getDescribeModFlag());
            fieldCnt++;
        }  else if (builder.hasDescribeModFlag()) {
            // 清理DescribeModFlag
            builder.clearDescribeModFlag();
            fieldCnt++;
        }
        if (!this.getWelcomeLetter().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setWelcomeLetter(this.getWelcomeLetter());
            fieldCnt++;
        }  else if (builder.hasWelcomeLetter()) {
            // 清理WelcomeLetter
            builder.clearWelcomeLetter();
            fieldCnt++;
        }
        if (this.getNationFlagId() != 0) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }  else if (builder.hasNationFlagId()) {
            // 清理NationFlagId
            builder.clearNationFlagId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanBaseInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SNAME)) {
            builder.setSname(this.getSname());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TERRITORYCOLOR)) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGCOLOR)) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSHADING)) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSIGN)) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DESCRIBE)) {
            builder.setDescribe(this.getDescribe());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LANGUAGE)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REQUIRE)) {
            builder.setRequire(this.getRequire());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DESCRIBEMODFLAG)) {
            builder.setDescribeModFlag(this.getDescribeModFlag());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WELCOMELETTER)) {
            builder.setWelcomeLetter(this.getWelcomeLetter());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanBaseInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SNAME)) {
            builder.setSname(this.getSname());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TERRITORYCOLOR)) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGCOLOR)) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSHADING)) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSIGN)) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DESCRIBE)) {
            builder.setDescribe(this.getDescribe());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LANGUAGE)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REQUIRE)) {
            builder.setRequire(this.getRequire());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DESCRIBEMODFLAG)) {
            builder.setDescribeModFlag(this.getDescribeModFlag());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WELCOMELETTER)) {
            builder.setWelcomeLetter(this.getWelcomeLetter());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanBaseInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSname()) {
            this.innerSetSname(proto.getSname());
        } else {
            this.innerSetSname(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTerritoryColor()) {
            this.innerSetTerritoryColor(proto.getTerritoryColor());
        } else {
            this.innerSetTerritoryColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagColor()) {
            this.innerSetFlagColor(proto.getFlagColor());
        } else {
            this.innerSetFlagColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagShading()) {
            this.innerSetFlagShading(proto.getFlagShading());
        } else {
            this.innerSetFlagShading(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagSign()) {
            this.innerSetFlagSign(proto.getFlagSign());
        } else {
            this.innerSetFlagSign(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDescribe()) {
            this.innerSetDescribe(proto.getDescribe());
        } else {
            this.innerSetDescribe(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasLanguage()) {
            this.innerSetLanguage(proto.getLanguage());
        } else {
            this.innerSetLanguage(Language.forNumber(0));
        }
        if (proto.hasRequire()) {
            this.innerSetRequire(proto.getRequire());
        } else {
            this.innerSetRequire(ClanEnterRequire.forNumber(0));
        }
        if (proto.hasDescribeModFlag()) {
            this.innerSetDescribeModFlag(proto.getDescribeModFlag());
        } else {
            this.innerSetDescribeModFlag(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasWelcomeLetter()) {
            this.innerSetWelcomeLetter(proto.getWelcomeLetter());
        } else {
            this.innerSetWelcomeLetter(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasNationFlagId()) {
            this.innerSetNationFlagId(proto.getNationFlagId());
        } else {
            this.innerSetNationFlagId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanBaseInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanBaseInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSname()) {
            this.setSname(proto.getSname());
            fieldCnt++;
        }
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        if (proto.hasTerritoryColor()) {
            this.setTerritoryColor(proto.getTerritoryColor());
            fieldCnt++;
        }
        if (proto.hasFlagColor()) {
            this.setFlagColor(proto.getFlagColor());
            fieldCnt++;
        }
        if (proto.hasFlagShading()) {
            this.setFlagShading(proto.getFlagShading());
            fieldCnt++;
        }
        if (proto.hasFlagSign()) {
            this.setFlagSign(proto.getFlagSign());
            fieldCnt++;
        }
        if (proto.hasDescribe()) {
            this.setDescribe(proto.getDescribe());
            fieldCnt++;
        }
        if (proto.hasLanguage()) {
            this.setLanguage(proto.getLanguage());
            fieldCnt++;
        }
        if (proto.hasRequire()) {
            this.setRequire(proto.getRequire());
            fieldCnt++;
        }
        if (proto.hasDescribeModFlag()) {
            this.setDescribeModFlag(proto.getDescribeModFlag());
            fieldCnt++;
        }
        if (proto.hasWelcomeLetter()) {
            this.setWelcomeLetter(proto.getWelcomeLetter());
            fieldCnt++;
        }
        if (proto.hasNationFlagId()) {
            this.setNationFlagId(proto.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanBaseInfo.Builder getCopyDbBuilder() {
        final ClanBaseInfo.Builder builder = ClanBaseInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanBaseInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getSname().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSname(this.getSname());
            fieldCnt++;
        }  else if (builder.hasSname()) {
            // 清理Sname
            builder.clearSname();
            fieldCnt++;
        }
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        if (this.getTerritoryColor() != 0) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }  else if (builder.hasTerritoryColor()) {
            // 清理TerritoryColor
            builder.clearTerritoryColor();
            fieldCnt++;
        }
        if (this.getFlagColor() != 0) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }  else if (builder.hasFlagColor()) {
            // 清理FlagColor
            builder.clearFlagColor();
            fieldCnt++;
        }
        if (this.getFlagShading() != 0) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }  else if (builder.hasFlagShading()) {
            // 清理FlagShading
            builder.clearFlagShading();
            fieldCnt++;
        }
        if (this.getFlagSign() != 0) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }  else if (builder.hasFlagSign()) {
            // 清理FlagSign
            builder.clearFlagSign();
            fieldCnt++;
        }
        if (!this.getDescribe().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setDescribe(this.getDescribe());
            fieldCnt++;
        }  else if (builder.hasDescribe()) {
            // 清理Describe
            builder.clearDescribe();
            fieldCnt++;
        }
        if (this.getLanguage() != Language.forNumber(0)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }  else if (builder.hasLanguage()) {
            // 清理Language
            builder.clearLanguage();
            fieldCnt++;
        }
        if (this.getRequire() != ClanEnterRequire.forNumber(0)) {
            builder.setRequire(this.getRequire());
            fieldCnt++;
        }  else if (builder.hasRequire()) {
            // 清理Require
            builder.clearRequire();
            fieldCnt++;
        }
        if (this.getDescribeModFlag()) {
            builder.setDescribeModFlag(this.getDescribeModFlag());
            fieldCnt++;
        }  else if (builder.hasDescribeModFlag()) {
            // 清理DescribeModFlag
            builder.clearDescribeModFlag();
            fieldCnt++;
        }
        if (!this.getWelcomeLetter().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setWelcomeLetter(this.getWelcomeLetter());
            fieldCnt++;
        }  else if (builder.hasWelcomeLetter()) {
            // 清理WelcomeLetter
            builder.clearWelcomeLetter();
            fieldCnt++;
        }
        if (this.getNationFlagId() != 0) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }  else if (builder.hasNationFlagId()) {
            // 清理NationFlagId
            builder.clearNationFlagId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanBaseInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SNAME)) {
            builder.setSname(this.getSname());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TERRITORYCOLOR)) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGCOLOR)) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSHADING)) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSIGN)) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DESCRIBE)) {
            builder.setDescribe(this.getDescribe());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LANGUAGE)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REQUIRE)) {
            builder.setRequire(this.getRequire());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DESCRIBEMODFLAG)) {
            builder.setDescribeModFlag(this.getDescribeModFlag());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WELCOMELETTER)) {
            builder.setWelcomeLetter(this.getWelcomeLetter());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanBaseInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSname()) {
            this.innerSetSname(proto.getSname());
        } else {
            this.innerSetSname(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTerritoryColor()) {
            this.innerSetTerritoryColor(proto.getTerritoryColor());
        } else {
            this.innerSetTerritoryColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagColor()) {
            this.innerSetFlagColor(proto.getFlagColor());
        } else {
            this.innerSetFlagColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagShading()) {
            this.innerSetFlagShading(proto.getFlagShading());
        } else {
            this.innerSetFlagShading(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagSign()) {
            this.innerSetFlagSign(proto.getFlagSign());
        } else {
            this.innerSetFlagSign(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDescribe()) {
            this.innerSetDescribe(proto.getDescribe());
        } else {
            this.innerSetDescribe(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasLanguage()) {
            this.innerSetLanguage(proto.getLanguage());
        } else {
            this.innerSetLanguage(Language.forNumber(0));
        }
        if (proto.hasRequire()) {
            this.innerSetRequire(proto.getRequire());
        } else {
            this.innerSetRequire(ClanEnterRequire.forNumber(0));
        }
        if (proto.hasDescribeModFlag()) {
            this.innerSetDescribeModFlag(proto.getDescribeModFlag());
        } else {
            this.innerSetDescribeModFlag(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasWelcomeLetter()) {
            this.innerSetWelcomeLetter(proto.getWelcomeLetter());
        } else {
            this.innerSetWelcomeLetter(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasNationFlagId()) {
            this.innerSetNationFlagId(proto.getNationFlagId());
        } else {
            this.innerSetNationFlagId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanBaseInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanBaseInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSname()) {
            this.setSname(proto.getSname());
            fieldCnt++;
        }
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        if (proto.hasTerritoryColor()) {
            this.setTerritoryColor(proto.getTerritoryColor());
            fieldCnt++;
        }
        if (proto.hasFlagColor()) {
            this.setFlagColor(proto.getFlagColor());
            fieldCnt++;
        }
        if (proto.hasFlagShading()) {
            this.setFlagShading(proto.getFlagShading());
            fieldCnt++;
        }
        if (proto.hasFlagSign()) {
            this.setFlagSign(proto.getFlagSign());
            fieldCnt++;
        }
        if (proto.hasDescribe()) {
            this.setDescribe(proto.getDescribe());
            fieldCnt++;
        }
        if (proto.hasLanguage()) {
            this.setLanguage(proto.getLanguage());
            fieldCnt++;
        }
        if (proto.hasRequire()) {
            this.setRequire(proto.getRequire());
            fieldCnt++;
        }
        if (proto.hasDescribeModFlag()) {
            this.setDescribeModFlag(proto.getDescribeModFlag());
            fieldCnt++;
        }
        if (proto.hasWelcomeLetter()) {
            this.setWelcomeLetter(proto.getWelcomeLetter());
            fieldCnt++;
        }
        if (proto.hasNationFlagId()) {
            this.setNationFlagId(proto.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanBaseInfo.Builder getCopySsBuilder() {
        final ClanBaseInfo.Builder builder = ClanBaseInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanBaseInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getSname().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSname(this.getSname());
            fieldCnt++;
        }  else if (builder.hasSname()) {
            // 清理Sname
            builder.clearSname();
            fieldCnt++;
        }
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        if (this.getTerritoryColor() != 0) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }  else if (builder.hasTerritoryColor()) {
            // 清理TerritoryColor
            builder.clearTerritoryColor();
            fieldCnt++;
        }
        if (this.getFlagColor() != 0) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }  else if (builder.hasFlagColor()) {
            // 清理FlagColor
            builder.clearFlagColor();
            fieldCnt++;
        }
        if (this.getFlagShading() != 0) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }  else if (builder.hasFlagShading()) {
            // 清理FlagShading
            builder.clearFlagShading();
            fieldCnt++;
        }
        if (this.getFlagSign() != 0) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }  else if (builder.hasFlagSign()) {
            // 清理FlagSign
            builder.clearFlagSign();
            fieldCnt++;
        }
        if (!this.getDescribe().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setDescribe(this.getDescribe());
            fieldCnt++;
        }  else if (builder.hasDescribe()) {
            // 清理Describe
            builder.clearDescribe();
            fieldCnt++;
        }
        if (this.getLanguage() != Language.forNumber(0)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }  else if (builder.hasLanguage()) {
            // 清理Language
            builder.clearLanguage();
            fieldCnt++;
        }
        if (this.getRequire() != ClanEnterRequire.forNumber(0)) {
            builder.setRequire(this.getRequire());
            fieldCnt++;
        }  else if (builder.hasRequire()) {
            // 清理Require
            builder.clearRequire();
            fieldCnt++;
        }
        if (this.getDescribeModFlag()) {
            builder.setDescribeModFlag(this.getDescribeModFlag());
            fieldCnt++;
        }  else if (builder.hasDescribeModFlag()) {
            // 清理DescribeModFlag
            builder.clearDescribeModFlag();
            fieldCnt++;
        }
        if (!this.getWelcomeLetter().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setWelcomeLetter(this.getWelcomeLetter());
            fieldCnt++;
        }  else if (builder.hasWelcomeLetter()) {
            // 清理WelcomeLetter
            builder.clearWelcomeLetter();
            fieldCnt++;
        }
        if (this.getNationFlagId() != 0) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }  else if (builder.hasNationFlagId()) {
            // 清理NationFlagId
            builder.clearNationFlagId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanBaseInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SNAME)) {
            builder.setSname(this.getSname());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TERRITORYCOLOR)) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGCOLOR)) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSHADING)) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSIGN)) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DESCRIBE)) {
            builder.setDescribe(this.getDescribe());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LANGUAGE)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REQUIRE)) {
            builder.setRequire(this.getRequire());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DESCRIBEMODFLAG)) {
            builder.setDescribeModFlag(this.getDescribeModFlag());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WELCOMELETTER)) {
            builder.setWelcomeLetter(this.getWelcomeLetter());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanBaseInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSname()) {
            this.innerSetSname(proto.getSname());
        } else {
            this.innerSetSname(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTerritoryColor()) {
            this.innerSetTerritoryColor(proto.getTerritoryColor());
        } else {
            this.innerSetTerritoryColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagColor()) {
            this.innerSetFlagColor(proto.getFlagColor());
        } else {
            this.innerSetFlagColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagShading()) {
            this.innerSetFlagShading(proto.getFlagShading());
        } else {
            this.innerSetFlagShading(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagSign()) {
            this.innerSetFlagSign(proto.getFlagSign());
        } else {
            this.innerSetFlagSign(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDescribe()) {
            this.innerSetDescribe(proto.getDescribe());
        } else {
            this.innerSetDescribe(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasLanguage()) {
            this.innerSetLanguage(proto.getLanguage());
        } else {
            this.innerSetLanguage(Language.forNumber(0));
        }
        if (proto.hasRequire()) {
            this.innerSetRequire(proto.getRequire());
        } else {
            this.innerSetRequire(ClanEnterRequire.forNumber(0));
        }
        if (proto.hasDescribeModFlag()) {
            this.innerSetDescribeModFlag(proto.getDescribeModFlag());
        } else {
            this.innerSetDescribeModFlag(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasWelcomeLetter()) {
            this.innerSetWelcomeLetter(proto.getWelcomeLetter());
        } else {
            this.innerSetWelcomeLetter(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasNationFlagId()) {
            this.innerSetNationFlagId(proto.getNationFlagId());
        } else {
            this.innerSetNationFlagId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanBaseInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanBaseInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSname()) {
            this.setSname(proto.getSname());
            fieldCnt++;
        }
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        if (proto.hasTerritoryColor()) {
            this.setTerritoryColor(proto.getTerritoryColor());
            fieldCnt++;
        }
        if (proto.hasFlagColor()) {
            this.setFlagColor(proto.getFlagColor());
            fieldCnt++;
        }
        if (proto.hasFlagShading()) {
            this.setFlagShading(proto.getFlagShading());
            fieldCnt++;
        }
        if (proto.hasFlagSign()) {
            this.setFlagSign(proto.getFlagSign());
            fieldCnt++;
        }
        if (proto.hasDescribe()) {
            this.setDescribe(proto.getDescribe());
            fieldCnt++;
        }
        if (proto.hasLanguage()) {
            this.setLanguage(proto.getLanguage());
            fieldCnt++;
        }
        if (proto.hasRequire()) {
            this.setRequire(proto.getRequire());
            fieldCnt++;
        }
        if (proto.hasDescribeModFlag()) {
            this.setDescribeModFlag(proto.getDescribeModFlag());
            fieldCnt++;
        }
        if (proto.hasWelcomeLetter()) {
            this.setWelcomeLetter(proto.getWelcomeLetter());
            fieldCnt++;
        }
        if (proto.hasNationFlagId()) {
            this.setNationFlagId(proto.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanBaseInfo.Builder builder = ClanBaseInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanBaseInfoProp)) {
            return false;
        }
        final ClanBaseInfoProp otherNode = (ClanBaseInfoProp) node;
        if (!com.yorha.gemini.utils.StringUtils.equals(this.sname, otherNode.sname)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.name, otherNode.name)) {
            return false;
        }
        if (this.territoryColor != otherNode.territoryColor) {
            return false;
        }
        if (this.flagColor != otherNode.flagColor) {
            return false;
        }
        if (this.flagShading != otherNode.flagShading) {
            return false;
        }
        if (this.flagSign != otherNode.flagSign) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.describe, otherNode.describe)) {
            return false;
        }
        if (this.language != otherNode.language) {
            return false;
        }
        if (this.require != otherNode.require) {
            return false;
        }
        if (this.describeModFlag != otherNode.describeModFlag) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.welcomeLetter, otherNode.welcomeLetter)) {
            return false;
        }
        if (this.nationFlagId != otherNode.nationFlagId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 52;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
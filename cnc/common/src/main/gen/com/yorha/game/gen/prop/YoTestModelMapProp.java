package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.YoTest.YoTestModelMap;
import com.yorha.proto.YoTest;
import com.yorha.proto.YoTestPB.YoTestModelMapPB;
import com.yorha.proto.YoTestPB;


/**
 * <AUTHOR> auto gen
 */
public class YoTestModelMapProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_TESTMODELMAPINTFIELD = 1;
    public static final int FIELD_INDEX_TESTINNERMAPFIELD = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int id = Constant.DEFAULT_INT_VALUE;
    private int testModelMapIntField = Constant.DEFAULT_INT_VALUE;
    private Int32YoTestDeepestInnerMapMapProp testInnerMapField = null;

    public YoTestModelMapProp() {
        super(null, 0, FIELD_COUNT);
    }

    public YoTestModelMapProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public int getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public YoTestModelMapProp setId(int id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(int id) {
        this.id = id;
    }

    /**
     * get testModelMapIntField
     *
     * @return testModelMapIntField value
     */
    public int getTestModelMapIntField() {
        return this.testModelMapIntField;
    }

    /**
     * set testModelMapIntField && set marked
     *
     * @param testModelMapIntField new value
     * @return current object
     */
    public YoTestModelMapProp setTestModelMapIntField(int testModelMapIntField) {
        if (this.testModelMapIntField != testModelMapIntField) {
            this.mark(FIELD_INDEX_TESTMODELMAPINTFIELD);
            this.testModelMapIntField = testModelMapIntField;
        }
        return this;
    }

    /**
     * inner set testModelMapIntField
     *
     * @param testModelMapIntField new value
     */
    private void innerSetTestModelMapIntField(int testModelMapIntField) {
        this.testModelMapIntField = testModelMapIntField;
    }

    /**
     * get testInnerMapField
     *
     * @return testInnerMapField value
     */
    public Int32YoTestDeepestInnerMapMapProp getTestInnerMapField() {
        if (this.testInnerMapField == null) {
            this.testInnerMapField = new Int32YoTestDeepestInnerMapMapProp(this, FIELD_INDEX_TESTINNERMAPFIELD);
        }
        return this.testInnerMapField;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTestInnerMapFieldV(YoTestDeepestInnerMapProp v) {
        this.getTestInnerMapField().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public YoTestDeepestInnerMapProp addEmptyTestInnerMapField(Integer k) {
        return this.getTestInnerMapField().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTestInnerMapFieldSize() {
        if (this.testInnerMapField == null) {
            return 0;
        }
        return this.testInnerMapField.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTestInnerMapFieldEmpty() {
        if (this.testInnerMapField == null) {
            return true;
        }
        return this.testInnerMapField.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public YoTestDeepestInnerMapProp getTestInnerMapFieldV(Integer k) {
        if (this.testInnerMapField == null || !this.testInnerMapField.containsKey(k)) {
            return null;
        }
        return this.testInnerMapField.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTestInnerMapField() {
        if (this.testInnerMapField != null) {
            this.testInnerMapField.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTestInnerMapFieldV(Integer k) {
        if (this.testInnerMapField != null) {
            this.testInnerMapField.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestModelMapPB.Builder getCopyCsBuilder() {
        final YoTestModelMapPB.Builder builder = YoTestModelMapPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(YoTestModelMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getTestModelMapIntField() != 0) {
            builder.setTestModelMapIntField(this.getTestModelMapIntField());
            fieldCnt++;
        }  else if (builder.hasTestModelMapIntField()) {
            // 清理TestModelMapIntField
            builder.clearTestModelMapIntField();
            fieldCnt++;
        }
        if (this.testInnerMapField != null) {
            YoTestPB.Int32YoTestDeepestInnerMapMapPB.Builder tmpBuilder = YoTestPB.Int32YoTestDeepestInnerMapMapPB.newBuilder();
            final int tmpFieldCnt = this.testInnerMapField.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTestInnerMapField(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTestInnerMapField();
            }
        }  else if (builder.hasTestInnerMapField()) {
            // 清理TestInnerMapField
            builder.clearTestInnerMapField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(YoTestModelMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTMODELMAPINTFIELD)) {
            builder.setTestModelMapIntField(this.getTestModelMapIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTINNERMAPFIELD) && this.testInnerMapField != null) {
            final boolean needClear = !builder.hasTestInnerMapField();
            final int tmpFieldCnt = this.testInnerMapField.copyChangeToCs(builder.getTestInnerMapFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTestInnerMapField();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(YoTestModelMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTMODELMAPINTFIELD)) {
            builder.setTestModelMapIntField(this.getTestModelMapIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTINNERMAPFIELD) && this.testInnerMapField != null) {
            final boolean needClear = !builder.hasTestInnerMapField();
            final int tmpFieldCnt = this.testInnerMapField.copyChangeToAndClearDeleteKeysCs(builder.getTestInnerMapFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTestInnerMapField();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(YoTestModelMapPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTestModelMapIntField()) {
            this.innerSetTestModelMapIntField(proto.getTestModelMapIntField());
        } else {
            this.innerSetTestModelMapIntField(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTestInnerMapField()) {
            this.getTestInnerMapField().mergeFromCs(proto.getTestInnerMapField());
        } else {
            if (this.testInnerMapField != null) {
                this.testInnerMapField.mergeFromCs(proto.getTestInnerMapField());
            }
        }
        this.markAll();
        return YoTestModelMapProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(YoTestModelMapPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasTestModelMapIntField()) {
            this.setTestModelMapIntField(proto.getTestModelMapIntField());
            fieldCnt++;
        }
        if (proto.hasTestInnerMapField()) {
            this.getTestInnerMapField().mergeChangeFromCs(proto.getTestInnerMapField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestModelMap.Builder getCopyDbBuilder() {
        final YoTestModelMap.Builder builder = YoTestModelMap.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(YoTestModelMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getTestModelMapIntField() != 0) {
            builder.setTestModelMapIntField(this.getTestModelMapIntField());
            fieldCnt++;
        }  else if (builder.hasTestModelMapIntField()) {
            // 清理TestModelMapIntField
            builder.clearTestModelMapIntField();
            fieldCnt++;
        }
        if (this.testInnerMapField != null) {
            YoTest.Int32YoTestDeepestInnerMapMap.Builder tmpBuilder = YoTest.Int32YoTestDeepestInnerMapMap.newBuilder();
            final int tmpFieldCnt = this.testInnerMapField.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTestInnerMapField(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTestInnerMapField();
            }
        }  else if (builder.hasTestInnerMapField()) {
            // 清理TestInnerMapField
            builder.clearTestInnerMapField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(YoTestModelMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTMODELMAPINTFIELD)) {
            builder.setTestModelMapIntField(this.getTestModelMapIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTINNERMAPFIELD) && this.testInnerMapField != null) {
            final boolean needClear = !builder.hasTestInnerMapField();
            final int tmpFieldCnt = this.testInnerMapField.copyChangeToDb(builder.getTestInnerMapFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTestInnerMapField();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(YoTestModelMap proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTestModelMapIntField()) {
            this.innerSetTestModelMapIntField(proto.getTestModelMapIntField());
        } else {
            this.innerSetTestModelMapIntField(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTestInnerMapField()) {
            this.getTestInnerMapField().mergeFromDb(proto.getTestInnerMapField());
        } else {
            if (this.testInnerMapField != null) {
                this.testInnerMapField.mergeFromDb(proto.getTestInnerMapField());
            }
        }
        this.markAll();
        return YoTestModelMapProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(YoTestModelMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasTestModelMapIntField()) {
            this.setTestModelMapIntField(proto.getTestModelMapIntField());
            fieldCnt++;
        }
        if (proto.hasTestInnerMapField()) {
            this.getTestInnerMapField().mergeChangeFromDb(proto.getTestInnerMapField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestModelMap.Builder getCopySsBuilder() {
        final YoTestModelMap.Builder builder = YoTestModelMap.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(YoTestModelMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getTestModelMapIntField() != 0) {
            builder.setTestModelMapIntField(this.getTestModelMapIntField());
            fieldCnt++;
        }  else if (builder.hasTestModelMapIntField()) {
            // 清理TestModelMapIntField
            builder.clearTestModelMapIntField();
            fieldCnt++;
        }
        if (this.testInnerMapField != null) {
            YoTest.Int32YoTestDeepestInnerMapMap.Builder tmpBuilder = YoTest.Int32YoTestDeepestInnerMapMap.newBuilder();
            final int tmpFieldCnt = this.testInnerMapField.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTestInnerMapField(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTestInnerMapField();
            }
        }  else if (builder.hasTestInnerMapField()) {
            // 清理TestInnerMapField
            builder.clearTestInnerMapField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(YoTestModelMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTMODELMAPINTFIELD)) {
            builder.setTestModelMapIntField(this.getTestModelMapIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTINNERMAPFIELD) && this.testInnerMapField != null) {
            final boolean needClear = !builder.hasTestInnerMapField();
            final int tmpFieldCnt = this.testInnerMapField.copyChangeToSs(builder.getTestInnerMapFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTestInnerMapField();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(YoTestModelMap proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTestModelMapIntField()) {
            this.innerSetTestModelMapIntField(proto.getTestModelMapIntField());
        } else {
            this.innerSetTestModelMapIntField(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTestInnerMapField()) {
            this.getTestInnerMapField().mergeFromSs(proto.getTestInnerMapField());
        } else {
            if (this.testInnerMapField != null) {
                this.testInnerMapField.mergeFromSs(proto.getTestInnerMapField());
            }
        }
        this.markAll();
        return YoTestModelMapProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(YoTestModelMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasTestModelMapIntField()) {
            this.setTestModelMapIntField(proto.getTestModelMapIntField());
            fieldCnt++;
        }
        if (proto.hasTestInnerMapField()) {
            this.getTestInnerMapField().mergeChangeFromSs(proto.getTestInnerMapField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        YoTestModelMap.Builder builder = YoTestModelMap.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TESTINNERMAPFIELD) && this.testInnerMapField != null) {
            this.testInnerMapField.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.testInnerMapField != null) {
            this.testInnerMapField.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof YoTestModelMapProp)) {
            return false;
        }
        final YoTestModelMapProp otherNode = (YoTestModelMapProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.testModelMapIntField != otherNode.testModelMapIntField) {
            return false;
        }
        if (!this.getTestInnerMapField().compareDataTo(otherNode.getTestInnerMapField())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
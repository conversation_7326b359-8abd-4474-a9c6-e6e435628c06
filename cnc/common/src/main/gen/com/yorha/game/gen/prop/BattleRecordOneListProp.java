package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructBattlePB.BattleRecordOneListPB;
import com.yorha.proto.StructBattle.BattleRecordOneList;
import com.yorha.proto.StructBattlePB.BattleRecordOnePB;
import com.yorha.proto.StructBattle.BattleRecordOne;

/**
 * <AUTHOR> auto gen
 */
public class BattleRecordOneListProp extends AbstractListNode<BattleRecordOneProp> {
    /**
     * Create a BattleRecordOneListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public BattleRecordOneListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to BattleRecordOneListProp
     *
     * @return new object
     */
    @Override
    public BattleRecordOneProp addEmptyValue() {
        final BattleRecordOneProp newProp = new BattleRecordOneProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordOneListPB.Builder getCopyCsBuilder() {
        final BattleRecordOneListPB.Builder builder = BattleRecordOneListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(BattleRecordOneListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return BattleRecordOneListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final BattleRecordOneProp v : this) {
            final BattleRecordOnePB.Builder itemBuilder = BattleRecordOnePB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return BattleRecordOneListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(BattleRecordOneListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return BattleRecordOneListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(BattleRecordOneListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (BattleRecordOnePB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return BattleRecordOneListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(BattleRecordOneListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordOneList.Builder getCopyDbBuilder() {
        final BattleRecordOneList.Builder builder = BattleRecordOneList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(BattleRecordOneList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return BattleRecordOneListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final BattleRecordOneProp v : this) {
            final BattleRecordOne.Builder itemBuilder = BattleRecordOne.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return BattleRecordOneListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(BattleRecordOneList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return BattleRecordOneListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(BattleRecordOneList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (BattleRecordOne v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return BattleRecordOneListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(BattleRecordOneList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordOneList.Builder getCopySsBuilder() {
        final BattleRecordOneList.Builder builder = BattleRecordOneList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(BattleRecordOneList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return BattleRecordOneListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final BattleRecordOneProp v : this) {
            final BattleRecordOne.Builder itemBuilder = BattleRecordOne.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return BattleRecordOneListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(BattleRecordOneList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return BattleRecordOneListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(BattleRecordOneList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (BattleRecordOne v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return BattleRecordOneListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(BattleRecordOneList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        BattleRecordOneList.Builder builder = BattleRecordOneList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
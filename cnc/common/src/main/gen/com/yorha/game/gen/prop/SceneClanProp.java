package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Struct.SceneClan;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.SceneClanPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class SceneClanProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CLANID = 0;
    public static final int FIELD_INDEX_CLANSIMPLENAME = 1;
    public static final int FIELD_INDEX_CLANNAME = 2;
    public static final int FIELD_INDEX_TERRITORYCOLOR = 3;
    public static final int FIELD_INDEX_FLAGCOLOR = 4;
    public static final int FIELD_INDEX_FLAGSHADING = 5;
    public static final int FIELD_INDEX_FLAGSIGN = 6;
    public static final int FIELD_INDEX_OWNERID = 7;
    public static final int FIELD_INDEX_OWNERNAME = 8;
    public static final int FIELD_INDEX_ADDITIONSYS = 9;
    public static final int FIELD_INDEX_CLANPOSMARKMAP = 10;
    public static final int FIELD_INDEX_STAGE = 11;
    public static final int FIELD_INDEX_POWER = 12;
    public static final int FIELD_INDEX_REQUIRE = 13;
    public static final int FIELD_INDEX_REGIONID = 14;
    public static final int FIELD_INDEX_NATIONFLAGID = 15;
    public static final int FIELD_INDEX_ISACTIVE = 16;

    public static final int FIELD_COUNT = 17;

    private long markBits0 = 0L;

    private long clanId = Constant.DEFAULT_LONG_VALUE;
    private String clanSimpleName = Constant.DEFAULT_STR_VALUE;
    private String clanName = Constant.DEFAULT_STR_VALUE;
    private int territoryColor = Constant.DEFAULT_INT_VALUE;
    private int flagColor = Constant.DEFAULT_INT_VALUE;
    private int flagShading = Constant.DEFAULT_INT_VALUE;
    private int flagSign = Constant.DEFAULT_INT_VALUE;
    private long ownerId = Constant.DEFAULT_LONG_VALUE;
    private String ownerName = Constant.DEFAULT_STR_VALUE;
    private AdditionSysProp additionSys = null;
    private Int64PositionMarkInfoMapProp clanPosMarkMap = null;
    private ClanStageType stage = ClanStageType.forNumber(0);
    private long power = Constant.DEFAULT_LONG_VALUE;
    private ClanEnterRequire require = ClanEnterRequire.forNumber(0);
    private int regionId = Constant.DEFAULT_INT_VALUE;
    private int nationFlagId = Constant.DEFAULT_INT_VALUE;
    private boolean isActive = Constant.DEFAULT_BOOLEAN_VALUE;

    public SceneClanProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SceneClanProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get clanId
     *
     * @return clanId value
     */
    public long getClanId() {
        return this.clanId;
    }

    /**
     * set clanId && set marked
     *
     * @param clanId new value
     * @return current object
     */
    public SceneClanProp setClanId(long clanId) {
        if (this.clanId != clanId) {
            this.mark(FIELD_INDEX_CLANID);
            this.clanId = clanId;
        }
        return this;
    }

    /**
     * inner set clanId
     *
     * @param clanId new value
     */
    private void innerSetClanId(long clanId) {
        this.clanId = clanId;
    }

    /**
     * get clanSimpleName
     *
     * @return clanSimpleName value
     */
    public String getClanSimpleName() {
        return this.clanSimpleName;
    }

    /**
     * set clanSimpleName && set marked
     *
     * @param clanSimpleName new value
     * @return current object
     */
    public SceneClanProp setClanSimpleName(String clanSimpleName) {
        if (clanSimpleName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanSimpleName, clanSimpleName)) {
            this.mark(FIELD_INDEX_CLANSIMPLENAME);
            this.clanSimpleName = clanSimpleName;
        }
        return this;
    }

    /**
     * inner set clanSimpleName
     *
     * @param clanSimpleName new value
     */
    private void innerSetClanSimpleName(String clanSimpleName) {
        this.clanSimpleName = clanSimpleName;
    }

    /**
     * get clanName
     *
     * @return clanName value
     */
    public String getClanName() {
        return this.clanName;
    }

    /**
     * set clanName && set marked
     *
     * @param clanName new value
     * @return current object
     */
    public SceneClanProp setClanName(String clanName) {
        if (clanName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanName, clanName)) {
            this.mark(FIELD_INDEX_CLANNAME);
            this.clanName = clanName;
        }
        return this;
    }

    /**
     * inner set clanName
     *
     * @param clanName new value
     */
    private void innerSetClanName(String clanName) {
        this.clanName = clanName;
    }

    /**
     * get territoryColor
     *
     * @return territoryColor value
     */
    public int getTerritoryColor() {
        return this.territoryColor;
    }

    /**
     * set territoryColor && set marked
     *
     * @param territoryColor new value
     * @return current object
     */
    public SceneClanProp setTerritoryColor(int territoryColor) {
        if (this.territoryColor != territoryColor) {
            this.mark(FIELD_INDEX_TERRITORYCOLOR);
            this.territoryColor = territoryColor;
        }
        return this;
    }

    /**
     * inner set territoryColor
     *
     * @param territoryColor new value
     */
    private void innerSetTerritoryColor(int territoryColor) {
        this.territoryColor = territoryColor;
    }

    /**
     * get flagColor
     *
     * @return flagColor value
     */
    public int getFlagColor() {
        return this.flagColor;
    }

    /**
     * set flagColor && set marked
     *
     * @param flagColor new value
     * @return current object
     */
    public SceneClanProp setFlagColor(int flagColor) {
        if (this.flagColor != flagColor) {
            this.mark(FIELD_INDEX_FLAGCOLOR);
            this.flagColor = flagColor;
        }
        return this;
    }

    /**
     * inner set flagColor
     *
     * @param flagColor new value
     */
    private void innerSetFlagColor(int flagColor) {
        this.flagColor = flagColor;
    }

    /**
     * get flagShading
     *
     * @return flagShading value
     */
    public int getFlagShading() {
        return this.flagShading;
    }

    /**
     * set flagShading && set marked
     *
     * @param flagShading new value
     * @return current object
     */
    public SceneClanProp setFlagShading(int flagShading) {
        if (this.flagShading != flagShading) {
            this.mark(FIELD_INDEX_FLAGSHADING);
            this.flagShading = flagShading;
        }
        return this;
    }

    /**
     * inner set flagShading
     *
     * @param flagShading new value
     */
    private void innerSetFlagShading(int flagShading) {
        this.flagShading = flagShading;
    }

    /**
     * get flagSign
     *
     * @return flagSign value
     */
    public int getFlagSign() {
        return this.flagSign;
    }

    /**
     * set flagSign && set marked
     *
     * @param flagSign new value
     * @return current object
     */
    public SceneClanProp setFlagSign(int flagSign) {
        if (this.flagSign != flagSign) {
            this.mark(FIELD_INDEX_FLAGSIGN);
            this.flagSign = flagSign;
        }
        return this;
    }

    /**
     * inner set flagSign
     *
     * @param flagSign new value
     */
    private void innerSetFlagSign(int flagSign) {
        this.flagSign = flagSign;
    }

    /**
     * get ownerId
     *
     * @return ownerId value
     */
    public long getOwnerId() {
        return this.ownerId;
    }

    /**
     * set ownerId && set marked
     *
     * @param ownerId new value
     * @return current object
     */
    public SceneClanProp setOwnerId(long ownerId) {
        if (this.ownerId != ownerId) {
            this.mark(FIELD_INDEX_OWNERID);
            this.ownerId = ownerId;
        }
        return this;
    }

    /**
     * inner set ownerId
     *
     * @param ownerId new value
     */
    private void innerSetOwnerId(long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * get ownerName
     *
     * @return ownerName value
     */
    public String getOwnerName() {
        return this.ownerName;
    }

    /**
     * set ownerName && set marked
     *
     * @param ownerName new value
     * @return current object
     */
    public SceneClanProp setOwnerName(String ownerName) {
        if (ownerName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.ownerName, ownerName)) {
            this.mark(FIELD_INDEX_OWNERNAME);
            this.ownerName = ownerName;
        }
        return this;
    }

    /**
     * inner set ownerName
     *
     * @param ownerName new value
     */
    private void innerSetOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    /**
     * get additionSys
     *
     * @return additionSys value
     */
    public AdditionSysProp getAdditionSys() {
        if (this.additionSys == null) {
            this.additionSys = new AdditionSysProp(this, FIELD_INDEX_ADDITIONSYS);
        }
        return this.additionSys;
    }

    /**
     * get clanPosMarkMap
     *
     * @return clanPosMarkMap value
     */
    public Int64PositionMarkInfoMapProp getClanPosMarkMap() {
        if (this.clanPosMarkMap == null) {
            this.clanPosMarkMap = new Int64PositionMarkInfoMapProp(this, FIELD_INDEX_CLANPOSMARKMAP);
        }
        return this.clanPosMarkMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putClanPosMarkMapV(PositionMarkInfoProp v) {
        this.getClanPosMarkMap().put(v.getMarkId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PositionMarkInfoProp addEmptyClanPosMarkMap(Long k) {
        return this.getClanPosMarkMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getClanPosMarkMapSize() {
        if (this.clanPosMarkMap == null) {
            return 0;
        }
        return this.clanPosMarkMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isClanPosMarkMapEmpty() {
        if (this.clanPosMarkMap == null) {
            return true;
        }
        return this.clanPosMarkMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PositionMarkInfoProp getClanPosMarkMapV(Long k) {
        if (this.clanPosMarkMap == null || !this.clanPosMarkMap.containsKey(k)) {
            return null;
        }
        return this.clanPosMarkMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearClanPosMarkMap() {
        if (this.clanPosMarkMap != null) {
            this.clanPosMarkMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeClanPosMarkMapV(Long k) {
        if (this.clanPosMarkMap != null) {
            this.clanPosMarkMap.remove(k);
        }
    }
    /**
     * get stage
     *
     * @return stage value
     */
    public ClanStageType getStage() {
        return this.stage;
    }

    /**
     * set stage && set marked
     *
     * @param stage new value
     * @return current object
     */
    public SceneClanProp setStage(ClanStageType stage) {
        if (stage == null) {
            throw new NullPointerException();
        }
        if (this.stage != stage) {
            this.mark(FIELD_INDEX_STAGE);
            this.stage = stage;
        }
        return this;
    }

    /**
     * inner set stage
     *
     * @param stage new value
     */
    private void innerSetStage(ClanStageType stage) {
        this.stage = stage;
    }

    /**
     * get power
     *
     * @return power value
     */
    public long getPower() {
        return this.power;
    }

    /**
     * set power && set marked
     *
     * @param power new value
     * @return current object
     */
    public SceneClanProp setPower(long power) {
        if (this.power != power) {
            this.mark(FIELD_INDEX_POWER);
            this.power = power;
        }
        return this;
    }

    /**
     * inner set power
     *
     * @param power new value
     */
    private void innerSetPower(long power) {
        this.power = power;
    }

    /**
     * get require
     *
     * @return require value
     */
    public ClanEnterRequire getRequire() {
        return this.require;
    }

    /**
     * set require && set marked
     *
     * @param require new value
     * @return current object
     */
    public SceneClanProp setRequire(ClanEnterRequire require) {
        if (require == null) {
            throw new NullPointerException();
        }
        if (this.require != require) {
            this.mark(FIELD_INDEX_REQUIRE);
            this.require = require;
        }
        return this;
    }

    /**
     * inner set require
     *
     * @param require new value
     */
    private void innerSetRequire(ClanEnterRequire require) {
        this.require = require;
    }

    /**
     * get regionId
     *
     * @return regionId value
     */
    public int getRegionId() {
        return this.regionId;
    }

    /**
     * set regionId && set marked
     *
     * @param regionId new value
     * @return current object
     */
    public SceneClanProp setRegionId(int regionId) {
        if (this.regionId != regionId) {
            this.mark(FIELD_INDEX_REGIONID);
            this.regionId = regionId;
        }
        return this;
    }

    /**
     * inner set regionId
     *
     * @param regionId new value
     */
    private void innerSetRegionId(int regionId) {
        this.regionId = regionId;
    }

    /**
     * get nationFlagId
     *
     * @return nationFlagId value
     */
    public int getNationFlagId() {
        return this.nationFlagId;
    }

    /**
     * set nationFlagId && set marked
     *
     * @param nationFlagId new value
     * @return current object
     */
    public SceneClanProp setNationFlagId(int nationFlagId) {
        if (this.nationFlagId != nationFlagId) {
            this.mark(FIELD_INDEX_NATIONFLAGID);
            this.nationFlagId = nationFlagId;
        }
        return this;
    }

    /**
     * inner set nationFlagId
     *
     * @param nationFlagId new value
     */
    private void innerSetNationFlagId(int nationFlagId) {
        this.nationFlagId = nationFlagId;
    }

    /**
     * get isActive
     *
     * @return isActive value
     */
    public boolean getIsActive() {
        return this.isActive;
    }

    /**
     * set isActive && set marked
     *
     * @param isActive new value
     * @return current object
     */
    public SceneClanProp setIsActive(boolean isActive) {
        if (this.isActive != isActive) {
            this.mark(FIELD_INDEX_ISACTIVE);
            this.isActive = isActive;
        }
        return this;
    }

    /**
     * inner set isActive
     *
     * @param isActive new value
     */
    private void innerSetIsActive(boolean isActive) {
        this.isActive = isActive;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneClanPB.Builder getCopyCsBuilder() {
        final SceneClanPB.Builder builder = SceneClanPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SceneClanPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasClanSimpleName()) {
            // 清理ClanSimpleName
            builder.clearClanSimpleName();
            fieldCnt++;
        }
        if (!this.getClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }  else if (builder.hasClanName()) {
            // 清理ClanName
            builder.clearClanName();
            fieldCnt++;
        }
        if (this.getTerritoryColor() != 0) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }  else if (builder.hasTerritoryColor()) {
            // 清理TerritoryColor
            builder.clearTerritoryColor();
            fieldCnt++;
        }
        if (this.getFlagColor() != 0) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }  else if (builder.hasFlagColor()) {
            // 清理FlagColor
            builder.clearFlagColor();
            fieldCnt++;
        }
        if (this.getFlagShading() != 0) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }  else if (builder.hasFlagShading()) {
            // 清理FlagShading
            builder.clearFlagShading();
            fieldCnt++;
        }
        if (this.getFlagSign() != 0) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }  else if (builder.hasFlagSign()) {
            // 清理FlagSign
            builder.clearFlagSign();
            fieldCnt++;
        }
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (!this.getOwnerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setOwnerName(this.getOwnerName());
            fieldCnt++;
        }  else if (builder.hasOwnerName()) {
            // 清理OwnerName
            builder.clearOwnerName();
            fieldCnt++;
        }
        if (this.clanPosMarkMap != null) {
            StructPB.Int64PositionMarkInfoMapPB.Builder tmpBuilder = StructPB.Int64PositionMarkInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.clanPosMarkMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanPosMarkMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanPosMarkMap();
            }
        }  else if (builder.hasClanPosMarkMap()) {
            // 清理ClanPosMarkMap
            builder.clearClanPosMarkMap();
            fieldCnt++;
        }
        if (this.getStage() != ClanStageType.forNumber(0)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }  else if (builder.hasStage()) {
            // 清理Stage
            builder.clearStage();
            fieldCnt++;
        }
        if (this.getPower() != 0L) {
            builder.setPower(this.getPower());
            fieldCnt++;
        }  else if (builder.hasPower()) {
            // 清理Power
            builder.clearPower();
            fieldCnt++;
        }
        if (this.getRequire() != ClanEnterRequire.forNumber(0)) {
            builder.setRequire(this.getRequire());
            fieldCnt++;
        }  else if (builder.hasRequire()) {
            // 清理Require
            builder.clearRequire();
            fieldCnt++;
        }
        if (this.getRegionId() != 0) {
            builder.setRegionId(this.getRegionId());
            fieldCnt++;
        }  else if (builder.hasRegionId()) {
            // 清理RegionId
            builder.clearRegionId();
            fieldCnt++;
        }
        if (this.getNationFlagId() != 0) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }  else if (builder.hasNationFlagId()) {
            // 清理NationFlagId
            builder.clearNationFlagId();
            fieldCnt++;
        }
        if (this.getIsActive()) {
            builder.setIsActive(this.getIsActive());
            fieldCnt++;
        }  else if (builder.hasIsActive()) {
            // 清理IsActive
            builder.clearIsActive();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SceneClanPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TERRITORYCOLOR)) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGCOLOR)) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSHADING)) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSIGN)) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERNAME)) {
            builder.setOwnerName(this.getOwnerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANPOSMARKMAP) && this.clanPosMarkMap != null) {
            final boolean needClear = !builder.hasClanPosMarkMap();
            final int tmpFieldCnt = this.clanPosMarkMap.copyChangeToCs(builder.getClanPosMarkMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanPosMarkMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_STAGE)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POWER)) {
            builder.setPower(this.getPower());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REQUIRE)) {
            builder.setRequire(this.getRequire());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REGIONID)) {
            builder.setRegionId(this.getRegionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISACTIVE)) {
            builder.setIsActive(this.getIsActive());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SceneClanPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TERRITORYCOLOR)) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGCOLOR)) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSHADING)) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSIGN)) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERNAME)) {
            builder.setOwnerName(this.getOwnerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANPOSMARKMAP) && this.clanPosMarkMap != null) {
            final boolean needClear = !builder.hasClanPosMarkMap();
            final int tmpFieldCnt = this.clanPosMarkMap.copyChangeToAndClearDeleteKeysCs(builder.getClanPosMarkMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanPosMarkMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_STAGE)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POWER)) {
            builder.setPower(this.getPower());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REQUIRE)) {
            builder.setRequire(this.getRequire());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REGIONID)) {
            builder.setRegionId(this.getRegionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISACTIVE)) {
            builder.setIsActive(this.getIsActive());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SceneClanPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanSimpleName()) {
            this.innerSetClanSimpleName(proto.getClanSimpleName());
        } else {
            this.innerSetClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanName()) {
            this.innerSetClanName(proto.getClanName());
        } else {
            this.innerSetClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTerritoryColor()) {
            this.innerSetTerritoryColor(proto.getTerritoryColor());
        } else {
            this.innerSetTerritoryColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagColor()) {
            this.innerSetFlagColor(proto.getFlagColor());
        } else {
            this.innerSetFlagColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagShading()) {
            this.innerSetFlagShading(proto.getFlagShading());
        } else {
            this.innerSetFlagShading(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagSign()) {
            this.innerSetFlagSign(proto.getFlagSign());
        } else {
            this.innerSetFlagSign(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOwnerName()) {
            this.innerSetOwnerName(proto.getOwnerName());
        } else {
            this.innerSetOwnerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanPosMarkMap()) {
            this.getClanPosMarkMap().mergeFromCs(proto.getClanPosMarkMap());
        } else {
            if (this.clanPosMarkMap != null) {
                this.clanPosMarkMap.mergeFromCs(proto.getClanPosMarkMap());
            }
        }
        if (proto.hasStage()) {
            this.innerSetStage(proto.getStage());
        } else {
            this.innerSetStage(ClanStageType.forNumber(0));
        }
        if (proto.hasPower()) {
            this.innerSetPower(proto.getPower());
        } else {
            this.innerSetPower(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRequire()) {
            this.innerSetRequire(proto.getRequire());
        } else {
            this.innerSetRequire(ClanEnterRequire.forNumber(0));
        }
        if (proto.hasRegionId()) {
            this.innerSetRegionId(proto.getRegionId());
        } else {
            this.innerSetRegionId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNationFlagId()) {
            this.innerSetNationFlagId(proto.getNationFlagId());
        } else {
            this.innerSetNationFlagId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIsActive()) {
            this.innerSetIsActive(proto.getIsActive());
        } else {
            this.innerSetIsActive(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return SceneClanProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SceneClanPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasClanSimpleName()) {
            this.setClanSimpleName(proto.getClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasClanName()) {
            this.setClanName(proto.getClanName());
            fieldCnt++;
        }
        if (proto.hasTerritoryColor()) {
            this.setTerritoryColor(proto.getTerritoryColor());
            fieldCnt++;
        }
        if (proto.hasFlagColor()) {
            this.setFlagColor(proto.getFlagColor());
            fieldCnt++;
        }
        if (proto.hasFlagShading()) {
            this.setFlagShading(proto.getFlagShading());
            fieldCnt++;
        }
        if (proto.hasFlagSign()) {
            this.setFlagSign(proto.getFlagSign());
            fieldCnt++;
        }
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasOwnerName()) {
            this.setOwnerName(proto.getOwnerName());
            fieldCnt++;
        }
        if (proto.hasClanPosMarkMap()) {
            this.getClanPosMarkMap().mergeChangeFromCs(proto.getClanPosMarkMap());
            fieldCnt++;
        }
        if (proto.hasStage()) {
            this.setStage(proto.getStage());
            fieldCnt++;
        }
        if (proto.hasPower()) {
            this.setPower(proto.getPower());
            fieldCnt++;
        }
        if (proto.hasRequire()) {
            this.setRequire(proto.getRequire());
            fieldCnt++;
        }
        if (proto.hasRegionId()) {
            this.setRegionId(proto.getRegionId());
            fieldCnt++;
        }
        if (proto.hasNationFlagId()) {
            this.setNationFlagId(proto.getNationFlagId());
            fieldCnt++;
        }
        if (proto.hasIsActive()) {
            this.setIsActive(proto.getIsActive());
            fieldCnt++;
        }
        return fieldCnt;
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneClan.Builder getCopySsBuilder() {
        final SceneClan.Builder builder = SceneClan.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SceneClan.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasClanSimpleName()) {
            // 清理ClanSimpleName
            builder.clearClanSimpleName();
            fieldCnt++;
        }
        if (!this.getClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }  else if (builder.hasClanName()) {
            // 清理ClanName
            builder.clearClanName();
            fieldCnt++;
        }
        if (this.getTerritoryColor() != 0) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }  else if (builder.hasTerritoryColor()) {
            // 清理TerritoryColor
            builder.clearTerritoryColor();
            fieldCnt++;
        }
        if (this.getFlagColor() != 0) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }  else if (builder.hasFlagColor()) {
            // 清理FlagColor
            builder.clearFlagColor();
            fieldCnt++;
        }
        if (this.getFlagShading() != 0) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }  else if (builder.hasFlagShading()) {
            // 清理FlagShading
            builder.clearFlagShading();
            fieldCnt++;
        }
        if (this.getFlagSign() != 0) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }  else if (builder.hasFlagSign()) {
            // 清理FlagSign
            builder.clearFlagSign();
            fieldCnt++;
        }
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (!this.getOwnerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setOwnerName(this.getOwnerName());
            fieldCnt++;
        }  else if (builder.hasOwnerName()) {
            // 清理OwnerName
            builder.clearOwnerName();
            fieldCnt++;
        }
        if (this.additionSys != null) {
            Struct.AdditionSys.Builder tmpBuilder = Struct.AdditionSys.newBuilder();
            final int tmpFieldCnt = this.additionSys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionSys();
            }
        }  else if (builder.hasAdditionSys()) {
            // 清理AdditionSys
            builder.clearAdditionSys();
            fieldCnt++;
        }
        if (this.clanPosMarkMap != null) {
            Struct.Int64PositionMarkInfoMap.Builder tmpBuilder = Struct.Int64PositionMarkInfoMap.newBuilder();
            final int tmpFieldCnt = this.clanPosMarkMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanPosMarkMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanPosMarkMap();
            }
        }  else if (builder.hasClanPosMarkMap()) {
            // 清理ClanPosMarkMap
            builder.clearClanPosMarkMap();
            fieldCnt++;
        }
        if (this.getStage() != ClanStageType.forNumber(0)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }  else if (builder.hasStage()) {
            // 清理Stage
            builder.clearStage();
            fieldCnt++;
        }
        if (this.getPower() != 0L) {
            builder.setPower(this.getPower());
            fieldCnt++;
        }  else if (builder.hasPower()) {
            // 清理Power
            builder.clearPower();
            fieldCnt++;
        }
        if (this.getRequire() != ClanEnterRequire.forNumber(0)) {
            builder.setRequire(this.getRequire());
            fieldCnt++;
        }  else if (builder.hasRequire()) {
            // 清理Require
            builder.clearRequire();
            fieldCnt++;
        }
        if (this.getRegionId() != 0) {
            builder.setRegionId(this.getRegionId());
            fieldCnt++;
        }  else if (builder.hasRegionId()) {
            // 清理RegionId
            builder.clearRegionId();
            fieldCnt++;
        }
        if (this.getNationFlagId() != 0) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }  else if (builder.hasNationFlagId()) {
            // 清理NationFlagId
            builder.clearNationFlagId();
            fieldCnt++;
        }
        if (this.getIsActive()) {
            builder.setIsActive(this.getIsActive());
            fieldCnt++;
        }  else if (builder.hasIsActive()) {
            // 清理IsActive
            builder.clearIsActive();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SceneClan.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TERRITORYCOLOR)) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGCOLOR)) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSHADING)) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSIGN)) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERNAME)) {
            builder.setOwnerName(this.getOwnerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            final boolean needClear = !builder.hasAdditionSys();
            final int tmpFieldCnt = this.additionSys.copyChangeToSs(builder.getAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANPOSMARKMAP) && this.clanPosMarkMap != null) {
            final boolean needClear = !builder.hasClanPosMarkMap();
            final int tmpFieldCnt = this.clanPosMarkMap.copyChangeToSs(builder.getClanPosMarkMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanPosMarkMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_STAGE)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POWER)) {
            builder.setPower(this.getPower());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REQUIRE)) {
            builder.setRequire(this.getRequire());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REGIONID)) {
            builder.setRegionId(this.getRegionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISACTIVE)) {
            builder.setIsActive(this.getIsActive());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SceneClan proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanSimpleName()) {
            this.innerSetClanSimpleName(proto.getClanSimpleName());
        } else {
            this.innerSetClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanName()) {
            this.innerSetClanName(proto.getClanName());
        } else {
            this.innerSetClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTerritoryColor()) {
            this.innerSetTerritoryColor(proto.getTerritoryColor());
        } else {
            this.innerSetTerritoryColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagColor()) {
            this.innerSetFlagColor(proto.getFlagColor());
        } else {
            this.innerSetFlagColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagShading()) {
            this.innerSetFlagShading(proto.getFlagShading());
        } else {
            this.innerSetFlagShading(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagSign()) {
            this.innerSetFlagSign(proto.getFlagSign());
        } else {
            this.innerSetFlagSign(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOwnerName()) {
            this.innerSetOwnerName(proto.getOwnerName());
        } else {
            this.innerSetOwnerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeFromSs(proto.getAdditionSys());
        } else {
            if (this.additionSys != null) {
                this.additionSys.mergeFromSs(proto.getAdditionSys());
            }
        }
        if (proto.hasClanPosMarkMap()) {
            this.getClanPosMarkMap().mergeFromSs(proto.getClanPosMarkMap());
        } else {
            if (this.clanPosMarkMap != null) {
                this.clanPosMarkMap.mergeFromSs(proto.getClanPosMarkMap());
            }
        }
        if (proto.hasStage()) {
            this.innerSetStage(proto.getStage());
        } else {
            this.innerSetStage(ClanStageType.forNumber(0));
        }
        if (proto.hasPower()) {
            this.innerSetPower(proto.getPower());
        } else {
            this.innerSetPower(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRequire()) {
            this.innerSetRequire(proto.getRequire());
        } else {
            this.innerSetRequire(ClanEnterRequire.forNumber(0));
        }
        if (proto.hasRegionId()) {
            this.innerSetRegionId(proto.getRegionId());
        } else {
            this.innerSetRegionId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNationFlagId()) {
            this.innerSetNationFlagId(proto.getNationFlagId());
        } else {
            this.innerSetNationFlagId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIsActive()) {
            this.innerSetIsActive(proto.getIsActive());
        } else {
            this.innerSetIsActive(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return SceneClanProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SceneClan proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasClanSimpleName()) {
            this.setClanSimpleName(proto.getClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasClanName()) {
            this.setClanName(proto.getClanName());
            fieldCnt++;
        }
        if (proto.hasTerritoryColor()) {
            this.setTerritoryColor(proto.getTerritoryColor());
            fieldCnt++;
        }
        if (proto.hasFlagColor()) {
            this.setFlagColor(proto.getFlagColor());
            fieldCnt++;
        }
        if (proto.hasFlagShading()) {
            this.setFlagShading(proto.getFlagShading());
            fieldCnt++;
        }
        if (proto.hasFlagSign()) {
            this.setFlagSign(proto.getFlagSign());
            fieldCnt++;
        }
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasOwnerName()) {
            this.setOwnerName(proto.getOwnerName());
            fieldCnt++;
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeChangeFromSs(proto.getAdditionSys());
            fieldCnt++;
        }
        if (proto.hasClanPosMarkMap()) {
            this.getClanPosMarkMap().mergeChangeFromSs(proto.getClanPosMarkMap());
            fieldCnt++;
        }
        if (proto.hasStage()) {
            this.setStage(proto.getStage());
            fieldCnt++;
        }
        if (proto.hasPower()) {
            this.setPower(proto.getPower());
            fieldCnt++;
        }
        if (proto.hasRequire()) {
            this.setRequire(proto.getRequire());
            fieldCnt++;
        }
        if (proto.hasRegionId()) {
            this.setRegionId(proto.getRegionId());
            fieldCnt++;
        }
        if (proto.hasNationFlagId()) {
            this.setNationFlagId(proto.getNationFlagId());
            fieldCnt++;
        }
        if (proto.hasIsActive()) {
            this.setIsActive(proto.getIsActive());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SceneClan.Builder builder = SceneClan.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            this.additionSys.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CLANPOSMARKMAP) && this.clanPosMarkMap != null) {
            this.clanPosMarkMap.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.additionSys != null) {
            this.additionSys.markAll();
        }
        if (this.clanPosMarkMap != null) {
            this.clanPosMarkMap.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SceneClanProp)) {
            return false;
        }
        final SceneClanProp otherNode = (SceneClanProp) node;
        if (this.clanId != otherNode.clanId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanSimpleName, otherNode.clanSimpleName)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanName, otherNode.clanName)) {
            return false;
        }
        if (this.territoryColor != otherNode.territoryColor) {
            return false;
        }
        if (this.flagColor != otherNode.flagColor) {
            return false;
        }
        if (this.flagShading != otherNode.flagShading) {
            return false;
        }
        if (this.flagSign != otherNode.flagSign) {
            return false;
        }
        if (this.ownerId != otherNode.ownerId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.ownerName, otherNode.ownerName)) {
            return false;
        }
        if (!this.getAdditionSys().compareDataTo(otherNode.getAdditionSys())) {
            return false;
        }
        if (!this.getClanPosMarkMap().compareDataTo(otherNode.getClanPosMarkMap())) {
            return false;
        }
        if (this.stage != otherNode.stage) {
            return false;
        }
        if (this.power != otherNode.power) {
            return false;
        }
        if (this.require != otherNode.require) {
            return false;
        }
        if (this.regionId != otherNode.regionId) {
            return false;
        }
        if (this.nationFlagId != otherNode.nationFlagId) {
            return false;
        }
        if (this.isActive != otherNode.isActive) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 47;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
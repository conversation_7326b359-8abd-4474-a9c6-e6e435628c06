package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerAvatarModel;
import com.yorha.proto.Player;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.PlayerAvatarModelPB;
import com.yorha.proto.PlayerPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerAvatarModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_PICLIST = 0;
    public static final int FIELD_INDEX_PICFRAMELIST = 1;
    public static final int FIELD_INDEX_CARDHEAD = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private Int32PicInfoMapProp picList = null;
    private Int32PicInfoMapProp picFrameList = null;
    private PlayerCardHeadProp cardHead = null;

    public PlayerAvatarModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerAvatarModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get picList
     *
     * @return picList value
     */
    public Int32PicInfoMapProp getPicList() {
        if (this.picList == null) {
            this.picList = new Int32PicInfoMapProp(this, FIELD_INDEX_PICLIST);
        }
        return this.picList;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putPicListV(PicInfoProp v) {
        this.getPicList().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PicInfoProp addEmptyPicList(Integer k) {
        return this.getPicList().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getPicListSize() {
        if (this.picList == null) {
            return 0;
        }
        return this.picList.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isPicListEmpty() {
        if (this.picList == null) {
            return true;
        }
        return this.picList.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PicInfoProp getPicListV(Integer k) {
        if (this.picList == null || !this.picList.containsKey(k)) {
            return null;
        }
        return this.picList.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearPicList() {
        if (this.picList != null) {
            this.picList.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removePicListV(Integer k) {
        if (this.picList != null) {
            this.picList.remove(k);
        }
    }
    /**
     * get picFrameList
     *
     * @return picFrameList value
     */
    public Int32PicInfoMapProp getPicFrameList() {
        if (this.picFrameList == null) {
            this.picFrameList = new Int32PicInfoMapProp(this, FIELD_INDEX_PICFRAMELIST);
        }
        return this.picFrameList;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putPicFrameListV(PicInfoProp v) {
        this.getPicFrameList().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PicInfoProp addEmptyPicFrameList(Integer k) {
        return this.getPicFrameList().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getPicFrameListSize() {
        if (this.picFrameList == null) {
            return 0;
        }
        return this.picFrameList.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isPicFrameListEmpty() {
        if (this.picFrameList == null) {
            return true;
        }
        return this.picFrameList.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PicInfoProp getPicFrameListV(Integer k) {
        if (this.picFrameList == null || !this.picFrameList.containsKey(k)) {
            return null;
        }
        return this.picFrameList.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearPicFrameList() {
        if (this.picFrameList != null) {
            this.picFrameList.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removePicFrameListV(Integer k) {
        if (this.picFrameList != null) {
            this.picFrameList.remove(k);
        }
    }
    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerAvatarModelPB.Builder getCopyCsBuilder() {
        final PlayerAvatarModelPB.Builder builder = PlayerAvatarModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerAvatarModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.picList != null) {
            PlayerPB.Int32PicInfoMapPB.Builder tmpBuilder = PlayerPB.Int32PicInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.picList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPicList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPicList();
            }
        }  else if (builder.hasPicList()) {
            // 清理PicList
            builder.clearPicList();
            fieldCnt++;
        }
        if (this.picFrameList != null) {
            PlayerPB.Int32PicInfoMapPB.Builder tmpBuilder = PlayerPB.Int32PicInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.picFrameList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPicFrameList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPicFrameList();
            }
        }  else if (builder.hasPicFrameList()) {
            // 清理PicFrameList
            builder.clearPicFrameList();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerAvatarModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PICLIST) && this.picList != null) {
            final boolean needClear = !builder.hasPicList();
            final int tmpFieldCnt = this.picList.copyChangeToCs(builder.getPicListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPicList();
            }
        }
        if (this.hasMark(FIELD_INDEX_PICFRAMELIST) && this.picFrameList != null) {
            final boolean needClear = !builder.hasPicFrameList();
            final int tmpFieldCnt = this.picFrameList.copyChangeToCs(builder.getPicFrameListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPicFrameList();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerAvatarModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PICLIST) && this.picList != null) {
            final boolean needClear = !builder.hasPicList();
            final int tmpFieldCnt = this.picList.copyChangeToAndClearDeleteKeysCs(builder.getPicListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPicList();
            }
        }
        if (this.hasMark(FIELD_INDEX_PICFRAMELIST) && this.picFrameList != null) {
            final boolean needClear = !builder.hasPicFrameList();
            final int tmpFieldCnt = this.picFrameList.copyChangeToAndClearDeleteKeysCs(builder.getPicFrameListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPicFrameList();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToAndClearDeleteKeysCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerAvatarModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPicList()) {
            this.getPicList().mergeFromCs(proto.getPicList());
        } else {
            if (this.picList != null) {
                this.picList.mergeFromCs(proto.getPicList());
            }
        }
        if (proto.hasPicFrameList()) {
            this.getPicFrameList().mergeFromCs(proto.getPicFrameList());
        } else {
            if (this.picFrameList != null) {
                this.picFrameList.mergeFromCs(proto.getPicFrameList());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromCs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromCs(proto.getCardHead());
            }
        }
        this.markAll();
        return PlayerAvatarModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerAvatarModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPicList()) {
            this.getPicList().mergeChangeFromCs(proto.getPicList());
            fieldCnt++;
        }
        if (proto.hasPicFrameList()) {
            this.getPicFrameList().mergeChangeFromCs(proto.getPicFrameList());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromCs(proto.getCardHead());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerAvatarModel.Builder getCopyDbBuilder() {
        final PlayerAvatarModel.Builder builder = PlayerAvatarModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerAvatarModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.picList != null) {
            Player.Int32PicInfoMap.Builder tmpBuilder = Player.Int32PicInfoMap.newBuilder();
            final int tmpFieldCnt = this.picList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPicList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPicList();
            }
        }  else if (builder.hasPicList()) {
            // 清理PicList
            builder.clearPicList();
            fieldCnt++;
        }
        if (this.picFrameList != null) {
            Player.Int32PicInfoMap.Builder tmpBuilder = Player.Int32PicInfoMap.newBuilder();
            final int tmpFieldCnt = this.picFrameList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPicFrameList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPicFrameList();
            }
        }  else if (builder.hasPicFrameList()) {
            // 清理PicFrameList
            builder.clearPicFrameList();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerAvatarModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PICLIST) && this.picList != null) {
            final boolean needClear = !builder.hasPicList();
            final int tmpFieldCnt = this.picList.copyChangeToDb(builder.getPicListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPicList();
            }
        }
        if (this.hasMark(FIELD_INDEX_PICFRAMELIST) && this.picFrameList != null) {
            final boolean needClear = !builder.hasPicFrameList();
            final int tmpFieldCnt = this.picFrameList.copyChangeToDb(builder.getPicFrameListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPicFrameList();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToDb(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerAvatarModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPicList()) {
            this.getPicList().mergeFromDb(proto.getPicList());
        } else {
            if (this.picList != null) {
                this.picList.mergeFromDb(proto.getPicList());
            }
        }
        if (proto.hasPicFrameList()) {
            this.getPicFrameList().mergeFromDb(proto.getPicFrameList());
        } else {
            if (this.picFrameList != null) {
                this.picFrameList.mergeFromDb(proto.getPicFrameList());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromDb(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromDb(proto.getCardHead());
            }
        }
        this.markAll();
        return PlayerAvatarModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerAvatarModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPicList()) {
            this.getPicList().mergeChangeFromDb(proto.getPicList());
            fieldCnt++;
        }
        if (proto.hasPicFrameList()) {
            this.getPicFrameList().mergeChangeFromDb(proto.getPicFrameList());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromDb(proto.getCardHead());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerAvatarModel.Builder getCopySsBuilder() {
        final PlayerAvatarModel.Builder builder = PlayerAvatarModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerAvatarModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.picList != null) {
            Player.Int32PicInfoMap.Builder tmpBuilder = Player.Int32PicInfoMap.newBuilder();
            final int tmpFieldCnt = this.picList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPicList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPicList();
            }
        }  else if (builder.hasPicList()) {
            // 清理PicList
            builder.clearPicList();
            fieldCnt++;
        }
        if (this.picFrameList != null) {
            Player.Int32PicInfoMap.Builder tmpBuilder = Player.Int32PicInfoMap.newBuilder();
            final int tmpFieldCnt = this.picFrameList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPicFrameList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPicFrameList();
            }
        }  else if (builder.hasPicFrameList()) {
            // 清理PicFrameList
            builder.clearPicFrameList();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerAvatarModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PICLIST) && this.picList != null) {
            final boolean needClear = !builder.hasPicList();
            final int tmpFieldCnt = this.picList.copyChangeToSs(builder.getPicListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPicList();
            }
        }
        if (this.hasMark(FIELD_INDEX_PICFRAMELIST) && this.picFrameList != null) {
            final boolean needClear = !builder.hasPicFrameList();
            final int tmpFieldCnt = this.picFrameList.copyChangeToSs(builder.getPicFrameListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPicFrameList();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerAvatarModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPicList()) {
            this.getPicList().mergeFromSs(proto.getPicList());
        } else {
            if (this.picList != null) {
                this.picList.mergeFromSs(proto.getPicList());
            }
        }
        if (proto.hasPicFrameList()) {
            this.getPicFrameList().mergeFromSs(proto.getPicFrameList());
        } else {
            if (this.picFrameList != null) {
                this.picFrameList.mergeFromSs(proto.getPicFrameList());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        this.markAll();
        return PlayerAvatarModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerAvatarModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPicList()) {
            this.getPicList().mergeChangeFromSs(proto.getPicList());
            fieldCnt++;
        }
        if (proto.hasPicFrameList()) {
            this.getPicFrameList().mergeChangeFromSs(proto.getPicFrameList());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerAvatarModel.Builder builder = PlayerAvatarModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_PICLIST) && this.picList != null) {
            this.picList.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PICFRAMELIST) && this.picFrameList != null) {
            this.picFrameList.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.picList != null) {
            this.picList.markAll();
        }
        if (this.picFrameList != null) {
            this.picFrameList.markAll();
        }
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerAvatarModelProp)) {
            return false;
        }
        final PlayerAvatarModelProp otherNode = (PlayerAvatarModelProp) node;
        if (!this.getPicList().compareDataTo(otherNode.getPicList())) {
            return false;
        }
        if (!this.getPicFrameList().compareDataTo(otherNode.getPicFrameList())) {
            return false;
        }
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
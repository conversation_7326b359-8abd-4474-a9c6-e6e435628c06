package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.TaskSystem;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.TaskSystemPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class TaskSystemProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TASKMAIN = 0;
    public static final int FIELD_INDEX_TASKDAILY = 1;
    public static final int FIELD_INDEX_TASKBRANCH = 2;
    public static final int FIELD_INDEX_HEATVALUE = 3;
    public static final int FIELD_INDEX_TASKBATTLEPASS = 4;
    public static final int FIELD_INDEX_TASKCHAPTER = 5;
    public static final int FIELD_INDEX_CHAPTER = 6;
    public static final int FIELD_INDEX_TASKPVE = 7;
    public static final int FIELD_INDEX_PVETASKSTARTTSMS = 8;
    public static final int FIELD_INDEX_PVECHAPTER = 9;

    public static final int FIELD_COUNT = 10;

    private long markBits0 = 0L;

    private Int32TaskInfoMapProp taskMain = null;
    private Int32TaskInfoMapProp taskDaily = null;
    private Int32TaskInfoMapProp taskBranch = null;
    private int heatValue = Constant.DEFAULT_INT_VALUE;
    private Int32TaskInfoMapProp taskBattlePass = null;
    private Int32TaskInfoMapProp taskChapter = null;
    private int chapter = Constant.DEFAULT_INT_VALUE;
    private Int32TaskInfoMapProp taskPve = null;
    private long pveTaskStartTsMs = Constant.DEFAULT_LONG_VALUE;
    private int pveChapter = Constant.DEFAULT_INT_VALUE;

    public TaskSystemProp() {
        super(null, 0, FIELD_COUNT);
    }

    public TaskSystemProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get taskMain
     *
     * @return taskMain value
     */
    public Int32TaskInfoMapProp getTaskMain() {
        if (this.taskMain == null) {
            this.taskMain = new Int32TaskInfoMapProp(this, FIELD_INDEX_TASKMAIN);
        }
        return this.taskMain;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTaskMainV(TaskInfoProp v) {
        this.getTaskMain().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public TaskInfoProp addEmptyTaskMain(Integer k) {
        return this.getTaskMain().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTaskMainSize() {
        if (this.taskMain == null) {
            return 0;
        }
        return this.taskMain.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTaskMainEmpty() {
        if (this.taskMain == null) {
            return true;
        }
        return this.taskMain.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public TaskInfoProp getTaskMainV(Integer k) {
        if (this.taskMain == null || !this.taskMain.containsKey(k)) {
            return null;
        }
        return this.taskMain.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTaskMain() {
        if (this.taskMain != null) {
            this.taskMain.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTaskMainV(Integer k) {
        if (this.taskMain != null) {
            this.taskMain.remove(k);
        }
    }
    /**
     * get taskDaily
     *
     * @return taskDaily value
     */
    public Int32TaskInfoMapProp getTaskDaily() {
        if (this.taskDaily == null) {
            this.taskDaily = new Int32TaskInfoMapProp(this, FIELD_INDEX_TASKDAILY);
        }
        return this.taskDaily;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTaskDailyV(TaskInfoProp v) {
        this.getTaskDaily().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public TaskInfoProp addEmptyTaskDaily(Integer k) {
        return this.getTaskDaily().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTaskDailySize() {
        if (this.taskDaily == null) {
            return 0;
        }
        return this.taskDaily.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTaskDailyEmpty() {
        if (this.taskDaily == null) {
            return true;
        }
        return this.taskDaily.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public TaskInfoProp getTaskDailyV(Integer k) {
        if (this.taskDaily == null || !this.taskDaily.containsKey(k)) {
            return null;
        }
        return this.taskDaily.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTaskDaily() {
        if (this.taskDaily != null) {
            this.taskDaily.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTaskDailyV(Integer k) {
        if (this.taskDaily != null) {
            this.taskDaily.remove(k);
        }
    }
    /**
     * get taskBranch
     *
     * @return taskBranch value
     */
    public Int32TaskInfoMapProp getTaskBranch() {
        if (this.taskBranch == null) {
            this.taskBranch = new Int32TaskInfoMapProp(this, FIELD_INDEX_TASKBRANCH);
        }
        return this.taskBranch;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTaskBranchV(TaskInfoProp v) {
        this.getTaskBranch().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public TaskInfoProp addEmptyTaskBranch(Integer k) {
        return this.getTaskBranch().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTaskBranchSize() {
        if (this.taskBranch == null) {
            return 0;
        }
        return this.taskBranch.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTaskBranchEmpty() {
        if (this.taskBranch == null) {
            return true;
        }
        return this.taskBranch.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public TaskInfoProp getTaskBranchV(Integer k) {
        if (this.taskBranch == null || !this.taskBranch.containsKey(k)) {
            return null;
        }
        return this.taskBranch.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTaskBranch() {
        if (this.taskBranch != null) {
            this.taskBranch.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTaskBranchV(Integer k) {
        if (this.taskBranch != null) {
            this.taskBranch.remove(k);
        }
    }
    /**
     * get heatValue
     *
     * @return heatValue value
     */
    public int getHeatValue() {
        return this.heatValue;
    }

    /**
     * set heatValue && set marked
     *
     * @param heatValue new value
     * @return current object
     */
    public TaskSystemProp setHeatValue(int heatValue) {
        if (this.heatValue != heatValue) {
            this.mark(FIELD_INDEX_HEATVALUE);
            this.heatValue = heatValue;
        }
        return this;
    }

    /**
     * inner set heatValue
     *
     * @param heatValue new value
     */
    private void innerSetHeatValue(int heatValue) {
        this.heatValue = heatValue;
    }

    /**
     * get taskBattlePass
     *
     * @return taskBattlePass value
     */
    public Int32TaskInfoMapProp getTaskBattlePass() {
        if (this.taskBattlePass == null) {
            this.taskBattlePass = new Int32TaskInfoMapProp(this, FIELD_INDEX_TASKBATTLEPASS);
        }
        return this.taskBattlePass;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTaskBattlePassV(TaskInfoProp v) {
        this.getTaskBattlePass().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public TaskInfoProp addEmptyTaskBattlePass(Integer k) {
        return this.getTaskBattlePass().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTaskBattlePassSize() {
        if (this.taskBattlePass == null) {
            return 0;
        }
        return this.taskBattlePass.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTaskBattlePassEmpty() {
        if (this.taskBattlePass == null) {
            return true;
        }
        return this.taskBattlePass.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public TaskInfoProp getTaskBattlePassV(Integer k) {
        if (this.taskBattlePass == null || !this.taskBattlePass.containsKey(k)) {
            return null;
        }
        return this.taskBattlePass.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTaskBattlePass() {
        if (this.taskBattlePass != null) {
            this.taskBattlePass.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTaskBattlePassV(Integer k) {
        if (this.taskBattlePass != null) {
            this.taskBattlePass.remove(k);
        }
    }
    /**
     * get taskChapter
     *
     * @return taskChapter value
     */
    public Int32TaskInfoMapProp getTaskChapter() {
        if (this.taskChapter == null) {
            this.taskChapter = new Int32TaskInfoMapProp(this, FIELD_INDEX_TASKCHAPTER);
        }
        return this.taskChapter;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTaskChapterV(TaskInfoProp v) {
        this.getTaskChapter().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public TaskInfoProp addEmptyTaskChapter(Integer k) {
        return this.getTaskChapter().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTaskChapterSize() {
        if (this.taskChapter == null) {
            return 0;
        }
        return this.taskChapter.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTaskChapterEmpty() {
        if (this.taskChapter == null) {
            return true;
        }
        return this.taskChapter.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public TaskInfoProp getTaskChapterV(Integer k) {
        if (this.taskChapter == null || !this.taskChapter.containsKey(k)) {
            return null;
        }
        return this.taskChapter.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTaskChapter() {
        if (this.taskChapter != null) {
            this.taskChapter.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTaskChapterV(Integer k) {
        if (this.taskChapter != null) {
            this.taskChapter.remove(k);
        }
    }
    /**
     * get chapter
     *
     * @return chapter value
     */
    public int getChapter() {
        return this.chapter;
    }

    /**
     * set chapter && set marked
     *
     * @param chapter new value
     * @return current object
     */
    public TaskSystemProp setChapter(int chapter) {
        if (this.chapter != chapter) {
            this.mark(FIELD_INDEX_CHAPTER);
            this.chapter = chapter;
        }
        return this;
    }

    /**
     * inner set chapter
     *
     * @param chapter new value
     */
    private void innerSetChapter(int chapter) {
        this.chapter = chapter;
    }

    /**
     * get taskPve
     *
     * @return taskPve value
     */
    public Int32TaskInfoMapProp getTaskPve() {
        if (this.taskPve == null) {
            this.taskPve = new Int32TaskInfoMapProp(this, FIELD_INDEX_TASKPVE);
        }
        return this.taskPve;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTaskPveV(TaskInfoProp v) {
        this.getTaskPve().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public TaskInfoProp addEmptyTaskPve(Integer k) {
        return this.getTaskPve().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTaskPveSize() {
        if (this.taskPve == null) {
            return 0;
        }
        return this.taskPve.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTaskPveEmpty() {
        if (this.taskPve == null) {
            return true;
        }
        return this.taskPve.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public TaskInfoProp getTaskPveV(Integer k) {
        if (this.taskPve == null || !this.taskPve.containsKey(k)) {
            return null;
        }
        return this.taskPve.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTaskPve() {
        if (this.taskPve != null) {
            this.taskPve.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTaskPveV(Integer k) {
        if (this.taskPve != null) {
            this.taskPve.remove(k);
        }
    }
    /**
     * get pveTaskStartTsMs
     *
     * @return pveTaskStartTsMs value
     */
    public long getPveTaskStartTsMs() {
        return this.pveTaskStartTsMs;
    }

    /**
     * set pveTaskStartTsMs && set marked
     *
     * @param pveTaskStartTsMs new value
     * @return current object
     */
    public TaskSystemProp setPveTaskStartTsMs(long pveTaskStartTsMs) {
        if (this.pveTaskStartTsMs != pveTaskStartTsMs) {
            this.mark(FIELD_INDEX_PVETASKSTARTTSMS);
            this.pveTaskStartTsMs = pveTaskStartTsMs;
        }
        return this;
    }

    /**
     * inner set pveTaskStartTsMs
     *
     * @param pveTaskStartTsMs new value
     */
    private void innerSetPveTaskStartTsMs(long pveTaskStartTsMs) {
        this.pveTaskStartTsMs = pveTaskStartTsMs;
    }

    /**
     * get pveChapter
     *
     * @return pveChapter value
     */
    public int getPveChapter() {
        return this.pveChapter;
    }

    /**
     * set pveChapter && set marked
     *
     * @param pveChapter new value
     * @return current object
     */
    public TaskSystemProp setPveChapter(int pveChapter) {
        if (this.pveChapter != pveChapter) {
            this.mark(FIELD_INDEX_PVECHAPTER);
            this.pveChapter = pveChapter;
        }
        return this;
    }

    /**
     * inner set pveChapter
     *
     * @param pveChapter new value
     */
    private void innerSetPveChapter(int pveChapter) {
        this.pveChapter = pveChapter;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TaskSystemPB.Builder getCopyCsBuilder() {
        final TaskSystemPB.Builder builder = TaskSystemPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(TaskSystemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.taskMain != null) {
            StructPB.Int32TaskInfoMapPB.Builder tmpBuilder = StructPB.Int32TaskInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.taskMain.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskMain(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskMain();
            }
        }  else if (builder.hasTaskMain()) {
            // 清理TaskMain
            builder.clearTaskMain();
            fieldCnt++;
        }
        if (this.taskDaily != null) {
            StructPB.Int32TaskInfoMapPB.Builder tmpBuilder = StructPB.Int32TaskInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.taskDaily.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskDaily(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskDaily();
            }
        }  else if (builder.hasTaskDaily()) {
            // 清理TaskDaily
            builder.clearTaskDaily();
            fieldCnt++;
        }
        if (this.taskBranch != null) {
            StructPB.Int32TaskInfoMapPB.Builder tmpBuilder = StructPB.Int32TaskInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.taskBranch.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskBranch(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskBranch();
            }
        }  else if (builder.hasTaskBranch()) {
            // 清理TaskBranch
            builder.clearTaskBranch();
            fieldCnt++;
        }
        if (this.getHeatValue() != 0) {
            builder.setHeatValue(this.getHeatValue());
            fieldCnt++;
        }  else if (builder.hasHeatValue()) {
            // 清理HeatValue
            builder.clearHeatValue();
            fieldCnt++;
        }
        if (this.taskBattlePass != null) {
            StructPB.Int32TaskInfoMapPB.Builder tmpBuilder = StructPB.Int32TaskInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.taskBattlePass.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskBattlePass(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskBattlePass();
            }
        }  else if (builder.hasTaskBattlePass()) {
            // 清理TaskBattlePass
            builder.clearTaskBattlePass();
            fieldCnt++;
        }
        if (this.taskChapter != null) {
            StructPB.Int32TaskInfoMapPB.Builder tmpBuilder = StructPB.Int32TaskInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.taskChapter.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskChapter(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskChapter();
            }
        }  else if (builder.hasTaskChapter()) {
            // 清理TaskChapter
            builder.clearTaskChapter();
            fieldCnt++;
        }
        if (this.getChapter() != 0) {
            builder.setChapter(this.getChapter());
            fieldCnt++;
        }  else if (builder.hasChapter()) {
            // 清理Chapter
            builder.clearChapter();
            fieldCnt++;
        }
        if (this.taskPve != null) {
            StructPB.Int32TaskInfoMapPB.Builder tmpBuilder = StructPB.Int32TaskInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.taskPve.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskPve(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskPve();
            }
        }  else if (builder.hasTaskPve()) {
            // 清理TaskPve
            builder.clearTaskPve();
            fieldCnt++;
        }
        if (this.getPveTaskStartTsMs() != 0L) {
            builder.setPveTaskStartTsMs(this.getPveTaskStartTsMs());
            fieldCnt++;
        }  else if (builder.hasPveTaskStartTsMs()) {
            // 清理PveTaskStartTsMs
            builder.clearPveTaskStartTsMs();
            fieldCnt++;
        }
        if (this.getPveChapter() != 0) {
            builder.setPveChapter(this.getPveChapter());
            fieldCnt++;
        }  else if (builder.hasPveChapter()) {
            // 清理PveChapter
            builder.clearPveChapter();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(TaskSystemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TASKMAIN) && this.taskMain != null) {
            final boolean needClear = !builder.hasTaskMain();
            final int tmpFieldCnt = this.taskMain.copyChangeToCs(builder.getTaskMainBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskMain();
            }
        }
        if (this.hasMark(FIELD_INDEX_TASKDAILY) && this.taskDaily != null) {
            final boolean needClear = !builder.hasTaskDaily();
            final int tmpFieldCnt = this.taskDaily.copyChangeToCs(builder.getTaskDailyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskDaily();
            }
        }
        if (this.hasMark(FIELD_INDEX_TASKBRANCH) && this.taskBranch != null) {
            final boolean needClear = !builder.hasTaskBranch();
            final int tmpFieldCnt = this.taskBranch.copyChangeToCs(builder.getTaskBranchBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskBranch();
            }
        }
        if (this.hasMark(FIELD_INDEX_HEATVALUE)) {
            builder.setHeatValue(this.getHeatValue());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKBATTLEPASS) && this.taskBattlePass != null) {
            final boolean needClear = !builder.hasTaskBattlePass();
            final int tmpFieldCnt = this.taskBattlePass.copyChangeToCs(builder.getTaskBattlePassBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskBattlePass();
            }
        }
        if (this.hasMark(FIELD_INDEX_TASKCHAPTER) && this.taskChapter != null) {
            final boolean needClear = !builder.hasTaskChapter();
            final int tmpFieldCnt = this.taskChapter.copyChangeToCs(builder.getTaskChapterBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskChapter();
            }
        }
        if (this.hasMark(FIELD_INDEX_CHAPTER)) {
            builder.setChapter(this.getChapter());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKPVE) && this.taskPve != null) {
            final boolean needClear = !builder.hasTaskPve();
            final int tmpFieldCnt = this.taskPve.copyChangeToCs(builder.getTaskPveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskPve();
            }
        }
        if (this.hasMark(FIELD_INDEX_PVETASKSTARTTSMS)) {
            builder.setPveTaskStartTsMs(this.getPveTaskStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PVECHAPTER)) {
            builder.setPveChapter(this.getPveChapter());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(TaskSystemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TASKMAIN) && this.taskMain != null) {
            final boolean needClear = !builder.hasTaskMain();
            final int tmpFieldCnt = this.taskMain.copyChangeToAndClearDeleteKeysCs(builder.getTaskMainBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskMain();
            }
        }
        if (this.hasMark(FIELD_INDEX_TASKDAILY) && this.taskDaily != null) {
            final boolean needClear = !builder.hasTaskDaily();
            final int tmpFieldCnt = this.taskDaily.copyChangeToAndClearDeleteKeysCs(builder.getTaskDailyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskDaily();
            }
        }
        if (this.hasMark(FIELD_INDEX_TASKBRANCH) && this.taskBranch != null) {
            final boolean needClear = !builder.hasTaskBranch();
            final int tmpFieldCnt = this.taskBranch.copyChangeToAndClearDeleteKeysCs(builder.getTaskBranchBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskBranch();
            }
        }
        if (this.hasMark(FIELD_INDEX_HEATVALUE)) {
            builder.setHeatValue(this.getHeatValue());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKBATTLEPASS) && this.taskBattlePass != null) {
            final boolean needClear = !builder.hasTaskBattlePass();
            final int tmpFieldCnt = this.taskBattlePass.copyChangeToAndClearDeleteKeysCs(builder.getTaskBattlePassBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskBattlePass();
            }
        }
        if (this.hasMark(FIELD_INDEX_TASKCHAPTER) && this.taskChapter != null) {
            final boolean needClear = !builder.hasTaskChapter();
            final int tmpFieldCnt = this.taskChapter.copyChangeToAndClearDeleteKeysCs(builder.getTaskChapterBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskChapter();
            }
        }
        if (this.hasMark(FIELD_INDEX_CHAPTER)) {
            builder.setChapter(this.getChapter());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKPVE) && this.taskPve != null) {
            final boolean needClear = !builder.hasTaskPve();
            final int tmpFieldCnt = this.taskPve.copyChangeToAndClearDeleteKeysCs(builder.getTaskPveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskPve();
            }
        }
        if (this.hasMark(FIELD_INDEX_PVETASKSTARTTSMS)) {
            builder.setPveTaskStartTsMs(this.getPveTaskStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PVECHAPTER)) {
            builder.setPveChapter(this.getPveChapter());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(TaskSystemPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTaskMain()) {
            this.getTaskMain().mergeFromCs(proto.getTaskMain());
        } else {
            if (this.taskMain != null) {
                this.taskMain.mergeFromCs(proto.getTaskMain());
            }
        }
        if (proto.hasTaskDaily()) {
            this.getTaskDaily().mergeFromCs(proto.getTaskDaily());
        } else {
            if (this.taskDaily != null) {
                this.taskDaily.mergeFromCs(proto.getTaskDaily());
            }
        }
        if (proto.hasTaskBranch()) {
            this.getTaskBranch().mergeFromCs(proto.getTaskBranch());
        } else {
            if (this.taskBranch != null) {
                this.taskBranch.mergeFromCs(proto.getTaskBranch());
            }
        }
        if (proto.hasHeatValue()) {
            this.innerSetHeatValue(proto.getHeatValue());
        } else {
            this.innerSetHeatValue(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTaskBattlePass()) {
            this.getTaskBattlePass().mergeFromCs(proto.getTaskBattlePass());
        } else {
            if (this.taskBattlePass != null) {
                this.taskBattlePass.mergeFromCs(proto.getTaskBattlePass());
            }
        }
        if (proto.hasTaskChapter()) {
            this.getTaskChapter().mergeFromCs(proto.getTaskChapter());
        } else {
            if (this.taskChapter != null) {
                this.taskChapter.mergeFromCs(proto.getTaskChapter());
            }
        }
        if (proto.hasChapter()) {
            this.innerSetChapter(proto.getChapter());
        } else {
            this.innerSetChapter(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTaskPve()) {
            this.getTaskPve().mergeFromCs(proto.getTaskPve());
        } else {
            if (this.taskPve != null) {
                this.taskPve.mergeFromCs(proto.getTaskPve());
            }
        }
        if (proto.hasPveTaskStartTsMs()) {
            this.innerSetPveTaskStartTsMs(proto.getPveTaskStartTsMs());
        } else {
            this.innerSetPveTaskStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPveChapter()) {
            this.innerSetPveChapter(proto.getPveChapter());
        } else {
            this.innerSetPveChapter(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return TaskSystemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(TaskSystemPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTaskMain()) {
            this.getTaskMain().mergeChangeFromCs(proto.getTaskMain());
            fieldCnt++;
        }
        if (proto.hasTaskDaily()) {
            this.getTaskDaily().mergeChangeFromCs(proto.getTaskDaily());
            fieldCnt++;
        }
        if (proto.hasTaskBranch()) {
            this.getTaskBranch().mergeChangeFromCs(proto.getTaskBranch());
            fieldCnt++;
        }
        if (proto.hasHeatValue()) {
            this.setHeatValue(proto.getHeatValue());
            fieldCnt++;
        }
        if (proto.hasTaskBattlePass()) {
            this.getTaskBattlePass().mergeChangeFromCs(proto.getTaskBattlePass());
            fieldCnt++;
        }
        if (proto.hasTaskChapter()) {
            this.getTaskChapter().mergeChangeFromCs(proto.getTaskChapter());
            fieldCnt++;
        }
        if (proto.hasChapter()) {
            this.setChapter(proto.getChapter());
            fieldCnt++;
        }
        if (proto.hasTaskPve()) {
            this.getTaskPve().mergeChangeFromCs(proto.getTaskPve());
            fieldCnt++;
        }
        if (proto.hasPveTaskStartTsMs()) {
            this.setPveTaskStartTsMs(proto.getPveTaskStartTsMs());
            fieldCnt++;
        }
        if (proto.hasPveChapter()) {
            this.setPveChapter(proto.getPveChapter());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TaskSystem.Builder getCopyDbBuilder() {
        final TaskSystem.Builder builder = TaskSystem.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(TaskSystem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.taskMain != null) {
            Struct.Int32TaskInfoMap.Builder tmpBuilder = Struct.Int32TaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.taskMain.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskMain(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskMain();
            }
        }  else if (builder.hasTaskMain()) {
            // 清理TaskMain
            builder.clearTaskMain();
            fieldCnt++;
        }
        if (this.taskDaily != null) {
            Struct.Int32TaskInfoMap.Builder tmpBuilder = Struct.Int32TaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.taskDaily.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskDaily(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskDaily();
            }
        }  else if (builder.hasTaskDaily()) {
            // 清理TaskDaily
            builder.clearTaskDaily();
            fieldCnt++;
        }
        if (this.taskBranch != null) {
            Struct.Int32TaskInfoMap.Builder tmpBuilder = Struct.Int32TaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.taskBranch.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskBranch(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskBranch();
            }
        }  else if (builder.hasTaskBranch()) {
            // 清理TaskBranch
            builder.clearTaskBranch();
            fieldCnt++;
        }
        if (this.getHeatValue() != 0) {
            builder.setHeatValue(this.getHeatValue());
            fieldCnt++;
        }  else if (builder.hasHeatValue()) {
            // 清理HeatValue
            builder.clearHeatValue();
            fieldCnt++;
        }
        if (this.taskBattlePass != null) {
            Struct.Int32TaskInfoMap.Builder tmpBuilder = Struct.Int32TaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.taskBattlePass.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskBattlePass(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskBattlePass();
            }
        }  else if (builder.hasTaskBattlePass()) {
            // 清理TaskBattlePass
            builder.clearTaskBattlePass();
            fieldCnt++;
        }
        if (this.taskChapter != null) {
            Struct.Int32TaskInfoMap.Builder tmpBuilder = Struct.Int32TaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.taskChapter.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskChapter(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskChapter();
            }
        }  else if (builder.hasTaskChapter()) {
            // 清理TaskChapter
            builder.clearTaskChapter();
            fieldCnt++;
        }
        if (this.getChapter() != 0) {
            builder.setChapter(this.getChapter());
            fieldCnt++;
        }  else if (builder.hasChapter()) {
            // 清理Chapter
            builder.clearChapter();
            fieldCnt++;
        }
        if (this.taskPve != null) {
            Struct.Int32TaskInfoMap.Builder tmpBuilder = Struct.Int32TaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.taskPve.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskPve(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskPve();
            }
        }  else if (builder.hasTaskPve()) {
            // 清理TaskPve
            builder.clearTaskPve();
            fieldCnt++;
        }
        if (this.getPveTaskStartTsMs() != 0L) {
            builder.setPveTaskStartTsMs(this.getPveTaskStartTsMs());
            fieldCnt++;
        }  else if (builder.hasPveTaskStartTsMs()) {
            // 清理PveTaskStartTsMs
            builder.clearPveTaskStartTsMs();
            fieldCnt++;
        }
        if (this.getPveChapter() != 0) {
            builder.setPveChapter(this.getPveChapter());
            fieldCnt++;
        }  else if (builder.hasPveChapter()) {
            // 清理PveChapter
            builder.clearPveChapter();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(TaskSystem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TASKMAIN) && this.taskMain != null) {
            final boolean needClear = !builder.hasTaskMain();
            final int tmpFieldCnt = this.taskMain.copyChangeToDb(builder.getTaskMainBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskMain();
            }
        }
        if (this.hasMark(FIELD_INDEX_TASKDAILY) && this.taskDaily != null) {
            final boolean needClear = !builder.hasTaskDaily();
            final int tmpFieldCnt = this.taskDaily.copyChangeToDb(builder.getTaskDailyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskDaily();
            }
        }
        if (this.hasMark(FIELD_INDEX_TASKBRANCH) && this.taskBranch != null) {
            final boolean needClear = !builder.hasTaskBranch();
            final int tmpFieldCnt = this.taskBranch.copyChangeToDb(builder.getTaskBranchBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskBranch();
            }
        }
        if (this.hasMark(FIELD_INDEX_HEATVALUE)) {
            builder.setHeatValue(this.getHeatValue());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKBATTLEPASS) && this.taskBattlePass != null) {
            final boolean needClear = !builder.hasTaskBattlePass();
            final int tmpFieldCnt = this.taskBattlePass.copyChangeToDb(builder.getTaskBattlePassBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskBattlePass();
            }
        }
        if (this.hasMark(FIELD_INDEX_TASKCHAPTER) && this.taskChapter != null) {
            final boolean needClear = !builder.hasTaskChapter();
            final int tmpFieldCnt = this.taskChapter.copyChangeToDb(builder.getTaskChapterBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskChapter();
            }
        }
        if (this.hasMark(FIELD_INDEX_CHAPTER)) {
            builder.setChapter(this.getChapter());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKPVE) && this.taskPve != null) {
            final boolean needClear = !builder.hasTaskPve();
            final int tmpFieldCnt = this.taskPve.copyChangeToDb(builder.getTaskPveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskPve();
            }
        }
        if (this.hasMark(FIELD_INDEX_PVETASKSTARTTSMS)) {
            builder.setPveTaskStartTsMs(this.getPveTaskStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PVECHAPTER)) {
            builder.setPveChapter(this.getPveChapter());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(TaskSystem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTaskMain()) {
            this.getTaskMain().mergeFromDb(proto.getTaskMain());
        } else {
            if (this.taskMain != null) {
                this.taskMain.mergeFromDb(proto.getTaskMain());
            }
        }
        if (proto.hasTaskDaily()) {
            this.getTaskDaily().mergeFromDb(proto.getTaskDaily());
        } else {
            if (this.taskDaily != null) {
                this.taskDaily.mergeFromDb(proto.getTaskDaily());
            }
        }
        if (proto.hasTaskBranch()) {
            this.getTaskBranch().mergeFromDb(proto.getTaskBranch());
        } else {
            if (this.taskBranch != null) {
                this.taskBranch.mergeFromDb(proto.getTaskBranch());
            }
        }
        if (proto.hasHeatValue()) {
            this.innerSetHeatValue(proto.getHeatValue());
        } else {
            this.innerSetHeatValue(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTaskBattlePass()) {
            this.getTaskBattlePass().mergeFromDb(proto.getTaskBattlePass());
        } else {
            if (this.taskBattlePass != null) {
                this.taskBattlePass.mergeFromDb(proto.getTaskBattlePass());
            }
        }
        if (proto.hasTaskChapter()) {
            this.getTaskChapter().mergeFromDb(proto.getTaskChapter());
        } else {
            if (this.taskChapter != null) {
                this.taskChapter.mergeFromDb(proto.getTaskChapter());
            }
        }
        if (proto.hasChapter()) {
            this.innerSetChapter(proto.getChapter());
        } else {
            this.innerSetChapter(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTaskPve()) {
            this.getTaskPve().mergeFromDb(proto.getTaskPve());
        } else {
            if (this.taskPve != null) {
                this.taskPve.mergeFromDb(proto.getTaskPve());
            }
        }
        if (proto.hasPveTaskStartTsMs()) {
            this.innerSetPveTaskStartTsMs(proto.getPveTaskStartTsMs());
        } else {
            this.innerSetPveTaskStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPveChapter()) {
            this.innerSetPveChapter(proto.getPveChapter());
        } else {
            this.innerSetPveChapter(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return TaskSystemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(TaskSystem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTaskMain()) {
            this.getTaskMain().mergeChangeFromDb(proto.getTaskMain());
            fieldCnt++;
        }
        if (proto.hasTaskDaily()) {
            this.getTaskDaily().mergeChangeFromDb(proto.getTaskDaily());
            fieldCnt++;
        }
        if (proto.hasTaskBranch()) {
            this.getTaskBranch().mergeChangeFromDb(proto.getTaskBranch());
            fieldCnt++;
        }
        if (proto.hasHeatValue()) {
            this.setHeatValue(proto.getHeatValue());
            fieldCnt++;
        }
        if (proto.hasTaskBattlePass()) {
            this.getTaskBattlePass().mergeChangeFromDb(proto.getTaskBattlePass());
            fieldCnt++;
        }
        if (proto.hasTaskChapter()) {
            this.getTaskChapter().mergeChangeFromDb(proto.getTaskChapter());
            fieldCnt++;
        }
        if (proto.hasChapter()) {
            this.setChapter(proto.getChapter());
            fieldCnt++;
        }
        if (proto.hasTaskPve()) {
            this.getTaskPve().mergeChangeFromDb(proto.getTaskPve());
            fieldCnt++;
        }
        if (proto.hasPveTaskStartTsMs()) {
            this.setPveTaskStartTsMs(proto.getPveTaskStartTsMs());
            fieldCnt++;
        }
        if (proto.hasPveChapter()) {
            this.setPveChapter(proto.getPveChapter());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TaskSystem.Builder getCopySsBuilder() {
        final TaskSystem.Builder builder = TaskSystem.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(TaskSystem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.taskMain != null) {
            Struct.Int32TaskInfoMap.Builder tmpBuilder = Struct.Int32TaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.taskMain.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskMain(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskMain();
            }
        }  else if (builder.hasTaskMain()) {
            // 清理TaskMain
            builder.clearTaskMain();
            fieldCnt++;
        }
        if (this.taskDaily != null) {
            Struct.Int32TaskInfoMap.Builder tmpBuilder = Struct.Int32TaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.taskDaily.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskDaily(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskDaily();
            }
        }  else if (builder.hasTaskDaily()) {
            // 清理TaskDaily
            builder.clearTaskDaily();
            fieldCnt++;
        }
        if (this.taskBranch != null) {
            Struct.Int32TaskInfoMap.Builder tmpBuilder = Struct.Int32TaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.taskBranch.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskBranch(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskBranch();
            }
        }  else if (builder.hasTaskBranch()) {
            // 清理TaskBranch
            builder.clearTaskBranch();
            fieldCnt++;
        }
        if (this.getHeatValue() != 0) {
            builder.setHeatValue(this.getHeatValue());
            fieldCnt++;
        }  else if (builder.hasHeatValue()) {
            // 清理HeatValue
            builder.clearHeatValue();
            fieldCnt++;
        }
        if (this.taskBattlePass != null) {
            Struct.Int32TaskInfoMap.Builder tmpBuilder = Struct.Int32TaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.taskBattlePass.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskBattlePass(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskBattlePass();
            }
        }  else if (builder.hasTaskBattlePass()) {
            // 清理TaskBattlePass
            builder.clearTaskBattlePass();
            fieldCnt++;
        }
        if (this.taskChapter != null) {
            Struct.Int32TaskInfoMap.Builder tmpBuilder = Struct.Int32TaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.taskChapter.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskChapter(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskChapter();
            }
        }  else if (builder.hasTaskChapter()) {
            // 清理TaskChapter
            builder.clearTaskChapter();
            fieldCnt++;
        }
        if (this.getChapter() != 0) {
            builder.setChapter(this.getChapter());
            fieldCnt++;
        }  else if (builder.hasChapter()) {
            // 清理Chapter
            builder.clearChapter();
            fieldCnt++;
        }
        if (this.taskPve != null) {
            Struct.Int32TaskInfoMap.Builder tmpBuilder = Struct.Int32TaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.taskPve.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskPve(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskPve();
            }
        }  else if (builder.hasTaskPve()) {
            // 清理TaskPve
            builder.clearTaskPve();
            fieldCnt++;
        }
        if (this.getPveTaskStartTsMs() != 0L) {
            builder.setPveTaskStartTsMs(this.getPveTaskStartTsMs());
            fieldCnt++;
        }  else if (builder.hasPveTaskStartTsMs()) {
            // 清理PveTaskStartTsMs
            builder.clearPveTaskStartTsMs();
            fieldCnt++;
        }
        if (this.getPveChapter() != 0) {
            builder.setPveChapter(this.getPveChapter());
            fieldCnt++;
        }  else if (builder.hasPveChapter()) {
            // 清理PveChapter
            builder.clearPveChapter();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(TaskSystem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TASKMAIN) && this.taskMain != null) {
            final boolean needClear = !builder.hasTaskMain();
            final int tmpFieldCnt = this.taskMain.copyChangeToSs(builder.getTaskMainBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskMain();
            }
        }
        if (this.hasMark(FIELD_INDEX_TASKDAILY) && this.taskDaily != null) {
            final boolean needClear = !builder.hasTaskDaily();
            final int tmpFieldCnt = this.taskDaily.copyChangeToSs(builder.getTaskDailyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskDaily();
            }
        }
        if (this.hasMark(FIELD_INDEX_TASKBRANCH) && this.taskBranch != null) {
            final boolean needClear = !builder.hasTaskBranch();
            final int tmpFieldCnt = this.taskBranch.copyChangeToSs(builder.getTaskBranchBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskBranch();
            }
        }
        if (this.hasMark(FIELD_INDEX_HEATVALUE)) {
            builder.setHeatValue(this.getHeatValue());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKBATTLEPASS) && this.taskBattlePass != null) {
            final boolean needClear = !builder.hasTaskBattlePass();
            final int tmpFieldCnt = this.taskBattlePass.copyChangeToSs(builder.getTaskBattlePassBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskBattlePass();
            }
        }
        if (this.hasMark(FIELD_INDEX_TASKCHAPTER) && this.taskChapter != null) {
            final boolean needClear = !builder.hasTaskChapter();
            final int tmpFieldCnt = this.taskChapter.copyChangeToSs(builder.getTaskChapterBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskChapter();
            }
        }
        if (this.hasMark(FIELD_INDEX_CHAPTER)) {
            builder.setChapter(this.getChapter());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKPVE) && this.taskPve != null) {
            final boolean needClear = !builder.hasTaskPve();
            final int tmpFieldCnt = this.taskPve.copyChangeToSs(builder.getTaskPveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskPve();
            }
        }
        if (this.hasMark(FIELD_INDEX_PVETASKSTARTTSMS)) {
            builder.setPveTaskStartTsMs(this.getPveTaskStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PVECHAPTER)) {
            builder.setPveChapter(this.getPveChapter());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(TaskSystem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTaskMain()) {
            this.getTaskMain().mergeFromSs(proto.getTaskMain());
        } else {
            if (this.taskMain != null) {
                this.taskMain.mergeFromSs(proto.getTaskMain());
            }
        }
        if (proto.hasTaskDaily()) {
            this.getTaskDaily().mergeFromSs(proto.getTaskDaily());
        } else {
            if (this.taskDaily != null) {
                this.taskDaily.mergeFromSs(proto.getTaskDaily());
            }
        }
        if (proto.hasTaskBranch()) {
            this.getTaskBranch().mergeFromSs(proto.getTaskBranch());
        } else {
            if (this.taskBranch != null) {
                this.taskBranch.mergeFromSs(proto.getTaskBranch());
            }
        }
        if (proto.hasHeatValue()) {
            this.innerSetHeatValue(proto.getHeatValue());
        } else {
            this.innerSetHeatValue(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTaskBattlePass()) {
            this.getTaskBattlePass().mergeFromSs(proto.getTaskBattlePass());
        } else {
            if (this.taskBattlePass != null) {
                this.taskBattlePass.mergeFromSs(proto.getTaskBattlePass());
            }
        }
        if (proto.hasTaskChapter()) {
            this.getTaskChapter().mergeFromSs(proto.getTaskChapter());
        } else {
            if (this.taskChapter != null) {
                this.taskChapter.mergeFromSs(proto.getTaskChapter());
            }
        }
        if (proto.hasChapter()) {
            this.innerSetChapter(proto.getChapter());
        } else {
            this.innerSetChapter(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTaskPve()) {
            this.getTaskPve().mergeFromSs(proto.getTaskPve());
        } else {
            if (this.taskPve != null) {
                this.taskPve.mergeFromSs(proto.getTaskPve());
            }
        }
        if (proto.hasPveTaskStartTsMs()) {
            this.innerSetPveTaskStartTsMs(proto.getPveTaskStartTsMs());
        } else {
            this.innerSetPveTaskStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPveChapter()) {
            this.innerSetPveChapter(proto.getPveChapter());
        } else {
            this.innerSetPveChapter(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return TaskSystemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(TaskSystem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTaskMain()) {
            this.getTaskMain().mergeChangeFromSs(proto.getTaskMain());
            fieldCnt++;
        }
        if (proto.hasTaskDaily()) {
            this.getTaskDaily().mergeChangeFromSs(proto.getTaskDaily());
            fieldCnt++;
        }
        if (proto.hasTaskBranch()) {
            this.getTaskBranch().mergeChangeFromSs(proto.getTaskBranch());
            fieldCnt++;
        }
        if (proto.hasHeatValue()) {
            this.setHeatValue(proto.getHeatValue());
            fieldCnt++;
        }
        if (proto.hasTaskBattlePass()) {
            this.getTaskBattlePass().mergeChangeFromSs(proto.getTaskBattlePass());
            fieldCnt++;
        }
        if (proto.hasTaskChapter()) {
            this.getTaskChapter().mergeChangeFromSs(proto.getTaskChapter());
            fieldCnt++;
        }
        if (proto.hasChapter()) {
            this.setChapter(proto.getChapter());
            fieldCnt++;
        }
        if (proto.hasTaskPve()) {
            this.getTaskPve().mergeChangeFromSs(proto.getTaskPve());
            fieldCnt++;
        }
        if (proto.hasPveTaskStartTsMs()) {
            this.setPveTaskStartTsMs(proto.getPveTaskStartTsMs());
            fieldCnt++;
        }
        if (proto.hasPveChapter()) {
            this.setPveChapter(proto.getPveChapter());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        TaskSystem.Builder builder = TaskSystem.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TASKMAIN) && this.taskMain != null) {
            this.taskMain.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TASKDAILY) && this.taskDaily != null) {
            this.taskDaily.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TASKBRANCH) && this.taskBranch != null) {
            this.taskBranch.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TASKBATTLEPASS) && this.taskBattlePass != null) {
            this.taskBattlePass.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TASKCHAPTER) && this.taskChapter != null) {
            this.taskChapter.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TASKPVE) && this.taskPve != null) {
            this.taskPve.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.taskMain != null) {
            this.taskMain.markAll();
        }
        if (this.taskDaily != null) {
            this.taskDaily.markAll();
        }
        if (this.taskBranch != null) {
            this.taskBranch.markAll();
        }
        if (this.taskBattlePass != null) {
            this.taskBattlePass.markAll();
        }
        if (this.taskChapter != null) {
            this.taskChapter.markAll();
        }
        if (this.taskPve != null) {
            this.taskPve.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof TaskSystemProp)) {
            return false;
        }
        final TaskSystemProp otherNode = (TaskSystemProp) node;
        if (!this.getTaskMain().compareDataTo(otherNode.getTaskMain())) {
            return false;
        }
        if (!this.getTaskDaily().compareDataTo(otherNode.getTaskDaily())) {
            return false;
        }
        if (!this.getTaskBranch().compareDataTo(otherNode.getTaskBranch())) {
            return false;
        }
        if (this.heatValue != otherNode.heatValue) {
            return false;
        }
        if (!this.getTaskBattlePass().compareDataTo(otherNode.getTaskBattlePass())) {
            return false;
        }
        if (!this.getTaskChapter().compareDataTo(otherNode.getTaskChapter())) {
            return false;
        }
        if (this.chapter != otherNode.chapter) {
            return false;
        }
        if (!this.getTaskPve().compareDataTo(otherNode.getTaskPve())) {
            return false;
        }
        if (this.pveTaskStartTsMs != otherNode.pveTaskStartTsMs) {
            return false;
        }
        if (this.pveChapter != otherNode.pveChapter) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 54;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
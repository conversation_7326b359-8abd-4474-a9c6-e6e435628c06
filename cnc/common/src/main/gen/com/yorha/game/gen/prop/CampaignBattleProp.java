package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.CampaignBattle;
import com.yorha.proto.Basic;
import com.yorha.proto.PlayerPB.CampaignBattlePB;
import com.yorha.proto.BasicPB;


/**
 * <AUTHOR> auto gen
 */
public class CampaignBattleProp extends AbstractPropNode {

    public static final int FIELD_INDEX_MISSIONINDEX = 0;
    public static final int FIELD_INDEX_UNITS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int missionIndex = Constant.DEFAULT_INT_VALUE;
    private Int32ListProp units = null;

    public CampaignBattleProp() {
        super(null, 0, FIELD_COUNT);
    }

    public CampaignBattleProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get missionIndex
     *
     * @return missionIndex value
     */
    public int getMissionIndex() {
        return this.missionIndex;
    }

    /**
     * set missionIndex && set marked
     *
     * @param missionIndex new value
     * @return current object
     */
    public CampaignBattleProp setMissionIndex(int missionIndex) {
        if (this.missionIndex != missionIndex) {
            this.mark(FIELD_INDEX_MISSIONINDEX);
            this.missionIndex = missionIndex;
        }
        return this;
    }

    /**
     * inner set missionIndex
     *
     * @param missionIndex new value
     */
    private void innerSetMissionIndex(int missionIndex) {
        this.missionIndex = missionIndex;
    }

    /**
     * get units
     *
     * @return units value
     */
    public Int32ListProp getUnits() {
        if (this.units == null) {
            this.units = new Int32ListProp(this, FIELD_INDEX_UNITS);
        }
        return this.units;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addUnits(Integer v) {
        this.getUnits().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getUnitsIndex(int index) {
        return this.getUnits().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeUnits(Integer v) {
        if (this.units == null) {
            return null;
        }
        if(this.units.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getUnitsSize() {
        if (this.units == null) {
            return 0;
        }
        return this.units.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isUnitsEmpty() {
        if (this.units == null) {
            return true;
        }
        return this.getUnits().isEmpty();
    }

    /**
     * clear list
     */
    public void clearUnits() {
        this.getUnits().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeUnitsIndex(int index) {
        return this.getUnits().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setUnitsIndex(int index, Integer v) {
        return this.getUnits().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignBattlePB.Builder getCopyCsBuilder() {
        final CampaignBattlePB.Builder builder = CampaignBattlePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(CampaignBattlePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMissionIndex() != 0) {
            builder.setMissionIndex(this.getMissionIndex());
            fieldCnt++;
        }  else if (builder.hasMissionIndex()) {
            // 清理MissionIndex
            builder.clearMissionIndex();
            fieldCnt++;
        }
        if (this.units != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.units.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnits(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnits();
            }
        }  else if (builder.hasUnits()) {
            // 清理Units
            builder.clearUnits();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(CampaignBattlePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MISSIONINDEX)) {
            builder.setMissionIndex(this.getMissionIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITS) && this.units != null) {
            final boolean needClear = !builder.hasUnits();
            final int tmpFieldCnt = this.units.copyChangeToCs(builder.getUnitsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnits();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(CampaignBattlePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MISSIONINDEX)) {
            builder.setMissionIndex(this.getMissionIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITS) && this.units != null) {
            final boolean needClear = !builder.hasUnits();
            final int tmpFieldCnt = this.units.copyChangeToCs(builder.getUnitsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnits();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(CampaignBattlePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMissionIndex()) {
            this.innerSetMissionIndex(proto.getMissionIndex());
        } else {
            this.innerSetMissionIndex(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnits()) {
            this.getUnits().mergeFromCs(proto.getUnits());
        } else {
            if (this.units != null) {
                this.units.mergeFromCs(proto.getUnits());
            }
        }
        this.markAll();
        return CampaignBattleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(CampaignBattlePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMissionIndex()) {
            this.setMissionIndex(proto.getMissionIndex());
            fieldCnt++;
        }
        if (proto.hasUnits()) {
            this.getUnits().mergeChangeFromCs(proto.getUnits());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignBattle.Builder getCopyDbBuilder() {
        final CampaignBattle.Builder builder = CampaignBattle.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(CampaignBattle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMissionIndex() != 0) {
            builder.setMissionIndex(this.getMissionIndex());
            fieldCnt++;
        }  else if (builder.hasMissionIndex()) {
            // 清理MissionIndex
            builder.clearMissionIndex();
            fieldCnt++;
        }
        if (this.units != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.units.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnits(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnits();
            }
        }  else if (builder.hasUnits()) {
            // 清理Units
            builder.clearUnits();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(CampaignBattle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MISSIONINDEX)) {
            builder.setMissionIndex(this.getMissionIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITS) && this.units != null) {
            final boolean needClear = !builder.hasUnits();
            final int tmpFieldCnt = this.units.copyChangeToDb(builder.getUnitsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnits();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(CampaignBattle proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMissionIndex()) {
            this.innerSetMissionIndex(proto.getMissionIndex());
        } else {
            this.innerSetMissionIndex(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnits()) {
            this.getUnits().mergeFromDb(proto.getUnits());
        } else {
            if (this.units != null) {
                this.units.mergeFromDb(proto.getUnits());
            }
        }
        this.markAll();
        return CampaignBattleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(CampaignBattle proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMissionIndex()) {
            this.setMissionIndex(proto.getMissionIndex());
            fieldCnt++;
        }
        if (proto.hasUnits()) {
            this.getUnits().mergeChangeFromDb(proto.getUnits());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignBattle.Builder getCopySsBuilder() {
        final CampaignBattle.Builder builder = CampaignBattle.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(CampaignBattle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMissionIndex() != 0) {
            builder.setMissionIndex(this.getMissionIndex());
            fieldCnt++;
        }  else if (builder.hasMissionIndex()) {
            // 清理MissionIndex
            builder.clearMissionIndex();
            fieldCnt++;
        }
        if (this.units != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.units.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnits(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnits();
            }
        }  else if (builder.hasUnits()) {
            // 清理Units
            builder.clearUnits();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(CampaignBattle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MISSIONINDEX)) {
            builder.setMissionIndex(this.getMissionIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITS) && this.units != null) {
            final boolean needClear = !builder.hasUnits();
            final int tmpFieldCnt = this.units.copyChangeToSs(builder.getUnitsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnits();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(CampaignBattle proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMissionIndex()) {
            this.innerSetMissionIndex(proto.getMissionIndex());
        } else {
            this.innerSetMissionIndex(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnits()) {
            this.getUnits().mergeFromSs(proto.getUnits());
        } else {
            if (this.units != null) {
                this.units.mergeFromSs(proto.getUnits());
            }
        }
        this.markAll();
        return CampaignBattleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(CampaignBattle proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMissionIndex()) {
            this.setMissionIndex(proto.getMissionIndex());
            fieldCnt++;
        }
        if (proto.hasUnits()) {
            this.getUnits().mergeChangeFromSs(proto.getUnits());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        CampaignBattle.Builder builder = CampaignBattle.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_UNITS) && this.units != null) {
            this.units.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.units != null) {
            this.units.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof CampaignBattleProp)) {
            return false;
        }
        final CampaignBattleProp otherNode = (CampaignBattleProp) node;
        if (this.missionIndex != otherNode.missionIndex) {
            return false;
        }
        if (!this.getUnits().compareDataTo(otherNode.getUnits())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivitySelectGoodsInfo;
import com.yorha.proto.StructPB.ActivitySelectGoodsInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivitySelectGoodsInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_REWARDID = 0;
    public static final int FIELD_INDEX_INDEX = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int rewardId = Constant.DEFAULT_INT_VALUE;
    private int index = Constant.DEFAULT_INT_VALUE;

    public ActivitySelectGoodsInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivitySelectGoodsInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get rewardId
     *
     * @return rewardId value
     */
    public int getRewardId() {
        return this.rewardId;
    }

    /**
     * set rewardId && set marked
     *
     * @param rewardId new value
     * @return current object
     */
    public ActivitySelectGoodsInfoProp setRewardId(int rewardId) {
        if (this.rewardId != rewardId) {
            this.mark(FIELD_INDEX_REWARDID);
            this.rewardId = rewardId;
        }
        return this;
    }

    /**
     * inner set rewardId
     *
     * @param rewardId new value
     */
    private void innerSetRewardId(int rewardId) {
        this.rewardId = rewardId;
    }

    /**
     * get index
     *
     * @return index value
     */
    public int getIndex() {
        return this.index;
    }

    /**
     * set index && set marked
     *
     * @param index new value
     * @return current object
     */
    public ActivitySelectGoodsInfoProp setIndex(int index) {
        if (this.index != index) {
            this.mark(FIELD_INDEX_INDEX);
            this.index = index;
        }
        return this;
    }

    /**
     * inner set index
     *
     * @param index new value
     */
    private void innerSetIndex(int index) {
        this.index = index;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivitySelectGoodsInfoPB.Builder getCopyCsBuilder() {
        final ActivitySelectGoodsInfoPB.Builder builder = ActivitySelectGoodsInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivitySelectGoodsInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRewardId() != 0) {
            builder.setRewardId(this.getRewardId());
            fieldCnt++;
        }  else if (builder.hasRewardId()) {
            // 清理RewardId
            builder.clearRewardId();
            fieldCnt++;
        }
        if (this.getIndex() != 0) {
            builder.setIndex(this.getIndex());
            fieldCnt++;
        }  else if (builder.hasIndex()) {
            // 清理Index
            builder.clearIndex();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivitySelectGoodsInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDID)) {
            builder.setRewardId(this.getRewardId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INDEX)) {
            builder.setIndex(this.getIndex());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivitySelectGoodsInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDID)) {
            builder.setRewardId(this.getRewardId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INDEX)) {
            builder.setIndex(this.getIndex());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivitySelectGoodsInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardId()) {
            this.innerSetRewardId(proto.getRewardId());
        } else {
            this.innerSetRewardId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIndex()) {
            this.innerSetIndex(proto.getIndex());
        } else {
            this.innerSetIndex(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivitySelectGoodsInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivitySelectGoodsInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardId()) {
            this.setRewardId(proto.getRewardId());
            fieldCnt++;
        }
        if (proto.hasIndex()) {
            this.setIndex(proto.getIndex());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivitySelectGoodsInfo.Builder getCopyDbBuilder() {
        final ActivitySelectGoodsInfo.Builder builder = ActivitySelectGoodsInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivitySelectGoodsInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRewardId() != 0) {
            builder.setRewardId(this.getRewardId());
            fieldCnt++;
        }  else if (builder.hasRewardId()) {
            // 清理RewardId
            builder.clearRewardId();
            fieldCnt++;
        }
        if (this.getIndex() != 0) {
            builder.setIndex(this.getIndex());
            fieldCnt++;
        }  else if (builder.hasIndex()) {
            // 清理Index
            builder.clearIndex();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivitySelectGoodsInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDID)) {
            builder.setRewardId(this.getRewardId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INDEX)) {
            builder.setIndex(this.getIndex());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivitySelectGoodsInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardId()) {
            this.innerSetRewardId(proto.getRewardId());
        } else {
            this.innerSetRewardId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIndex()) {
            this.innerSetIndex(proto.getIndex());
        } else {
            this.innerSetIndex(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivitySelectGoodsInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivitySelectGoodsInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardId()) {
            this.setRewardId(proto.getRewardId());
            fieldCnt++;
        }
        if (proto.hasIndex()) {
            this.setIndex(proto.getIndex());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivitySelectGoodsInfo.Builder getCopySsBuilder() {
        final ActivitySelectGoodsInfo.Builder builder = ActivitySelectGoodsInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivitySelectGoodsInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRewardId() != 0) {
            builder.setRewardId(this.getRewardId());
            fieldCnt++;
        }  else if (builder.hasRewardId()) {
            // 清理RewardId
            builder.clearRewardId();
            fieldCnt++;
        }
        if (this.getIndex() != 0) {
            builder.setIndex(this.getIndex());
            fieldCnt++;
        }  else if (builder.hasIndex()) {
            // 清理Index
            builder.clearIndex();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivitySelectGoodsInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDID)) {
            builder.setRewardId(this.getRewardId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INDEX)) {
            builder.setIndex(this.getIndex());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivitySelectGoodsInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardId()) {
            this.innerSetRewardId(proto.getRewardId());
        } else {
            this.innerSetRewardId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIndex()) {
            this.innerSetIndex(proto.getIndex());
        } else {
            this.innerSetIndex(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivitySelectGoodsInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivitySelectGoodsInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardId()) {
            this.setRewardId(proto.getRewardId());
            fieldCnt++;
        }
        if (proto.hasIndex()) {
            this.setIndex(proto.getIndex());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivitySelectGoodsInfo.Builder builder = ActivitySelectGoodsInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.rewardId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivitySelectGoodsInfoProp)) {
            return false;
        }
        final ActivitySelectGoodsInfoProp otherNode = (ActivitySelectGoodsInfoProp) node;
        if (this.rewardId != otherNode.rewardId) {
            return false;
        }
        if (this.index != otherNode.index) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
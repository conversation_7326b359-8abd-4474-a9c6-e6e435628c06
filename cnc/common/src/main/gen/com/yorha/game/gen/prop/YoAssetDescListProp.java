package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructPB.YoAssetDescListPB;
import com.yorha.proto.Struct.YoAssetDescList;
import com.yorha.proto.StructPB.YoAssetDescPB;
import com.yorha.proto.Struct.YoAssetDesc;

/**
 * <AUTHOR> auto gen
 */
public class YoAssetDescListProp extends AbstractListNode<YoAssetDescProp> {
    /**
     * Create a YoAssetDescListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public YoAssetDescListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to YoAssetDescListProp
     *
     * @return new object
     */
    @Override
    public YoAssetDescProp addEmptyValue() {
        final YoAssetDescProp newProp = new YoAssetDescProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoAssetDescListPB.Builder getCopyCsBuilder() {
        final YoAssetDescListPB.Builder builder = YoAssetDescListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(YoAssetDescListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return YoAssetDescListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final YoAssetDescProp v : this) {
            final YoAssetDescPB.Builder itemBuilder = YoAssetDescPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return YoAssetDescListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(YoAssetDescListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return YoAssetDescListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(YoAssetDescListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (YoAssetDescPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return YoAssetDescListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(YoAssetDescListPB proto) {
        return mergeFromCs(proto);
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoAssetDescList.Builder getCopySsBuilder() {
        final YoAssetDescList.Builder builder = YoAssetDescList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(YoAssetDescList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return YoAssetDescListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final YoAssetDescProp v : this) {
            final YoAssetDesc.Builder itemBuilder = YoAssetDesc.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return YoAssetDescListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(YoAssetDescList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return YoAssetDescListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(YoAssetDescList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (YoAssetDesc v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return YoAssetDescListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(YoAssetDescList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        YoAssetDescList.Builder builder = YoAssetDescList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zoneside.BestCommanderHistoryRankModel;
import com.yorha.proto.Zoneside;
import com.yorha.proto.ZonesidePB.BestCommanderHistoryRankModelPB;
import com.yorha.proto.ZonesidePB;


/**
 * <AUTHOR> auto gen
 */
public class BestCommanderHistoryRankModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_HISTORYMAP = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32BestCommanderHistoryRankRecordMapProp historyMap = null;

    public BestCommanderHistoryRankModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BestCommanderHistoryRankModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get historyMap
     *
     * @return historyMap value
     */
    public Int32BestCommanderHistoryRankRecordMapProp getHistoryMap() {
        if (this.historyMap == null) {
            this.historyMap = new Int32BestCommanderHistoryRankRecordMapProp(this, FIELD_INDEX_HISTORYMAP);
        }
        return this.historyMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putHistoryMapV(BestCommanderHistoryRankRecordProp v) {
        this.getHistoryMap().put(v.getAge(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public BestCommanderHistoryRankRecordProp addEmptyHistoryMap(Integer k) {
        return this.getHistoryMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getHistoryMapSize() {
        if (this.historyMap == null) {
            return 0;
        }
        return this.historyMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isHistoryMapEmpty() {
        if (this.historyMap == null) {
            return true;
        }
        return this.historyMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public BestCommanderHistoryRankRecordProp getHistoryMapV(Integer k) {
        if (this.historyMap == null || !this.historyMap.containsKey(k)) {
            return null;
        }
        return this.historyMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearHistoryMap() {
        if (this.historyMap != null) {
            this.historyMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeHistoryMapV(Integer k) {
        if (this.historyMap != null) {
            this.historyMap.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BestCommanderHistoryRankModelPB.Builder getCopyCsBuilder() {
        final BestCommanderHistoryRankModelPB.Builder builder = BestCommanderHistoryRankModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BestCommanderHistoryRankModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.historyMap != null) {
            ZonesidePB.Int32BestCommanderHistoryRankRecordMapPB.Builder tmpBuilder = ZonesidePB.Int32BestCommanderHistoryRankRecordMapPB.newBuilder();
            final int tmpFieldCnt = this.historyMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHistoryMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHistoryMap();
            }
        }  else if (builder.hasHistoryMap()) {
            // 清理HistoryMap
            builder.clearHistoryMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BestCommanderHistoryRankModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HISTORYMAP) && this.historyMap != null) {
            final boolean needClear = !builder.hasHistoryMap();
            final int tmpFieldCnt = this.historyMap.copyChangeToCs(builder.getHistoryMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHistoryMap();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BestCommanderHistoryRankModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HISTORYMAP) && this.historyMap != null) {
            final boolean needClear = !builder.hasHistoryMap();
            final int tmpFieldCnt = this.historyMap.copyChangeToAndClearDeleteKeysCs(builder.getHistoryMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHistoryMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BestCommanderHistoryRankModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasHistoryMap()) {
            this.getHistoryMap().mergeFromCs(proto.getHistoryMap());
        } else {
            if (this.historyMap != null) {
                this.historyMap.mergeFromCs(proto.getHistoryMap());
            }
        }
        this.markAll();
        return BestCommanderHistoryRankModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BestCommanderHistoryRankModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasHistoryMap()) {
            this.getHistoryMap().mergeChangeFromCs(proto.getHistoryMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BestCommanderHistoryRankModel.Builder getCopyDbBuilder() {
        final BestCommanderHistoryRankModel.Builder builder = BestCommanderHistoryRankModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(BestCommanderHistoryRankModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.historyMap != null) {
            Zoneside.Int32BestCommanderHistoryRankRecordMap.Builder tmpBuilder = Zoneside.Int32BestCommanderHistoryRankRecordMap.newBuilder();
            final int tmpFieldCnt = this.historyMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHistoryMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHistoryMap();
            }
        }  else if (builder.hasHistoryMap()) {
            // 清理HistoryMap
            builder.clearHistoryMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(BestCommanderHistoryRankModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HISTORYMAP) && this.historyMap != null) {
            final boolean needClear = !builder.hasHistoryMap();
            final int tmpFieldCnt = this.historyMap.copyChangeToDb(builder.getHistoryMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHistoryMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(BestCommanderHistoryRankModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasHistoryMap()) {
            this.getHistoryMap().mergeFromDb(proto.getHistoryMap());
        } else {
            if (this.historyMap != null) {
                this.historyMap.mergeFromDb(proto.getHistoryMap());
            }
        }
        this.markAll();
        return BestCommanderHistoryRankModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(BestCommanderHistoryRankModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasHistoryMap()) {
            this.getHistoryMap().mergeChangeFromDb(proto.getHistoryMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BestCommanderHistoryRankModel.Builder getCopySsBuilder() {
        final BestCommanderHistoryRankModel.Builder builder = BestCommanderHistoryRankModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(BestCommanderHistoryRankModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.historyMap != null) {
            Zoneside.Int32BestCommanderHistoryRankRecordMap.Builder tmpBuilder = Zoneside.Int32BestCommanderHistoryRankRecordMap.newBuilder();
            final int tmpFieldCnt = this.historyMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHistoryMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHistoryMap();
            }
        }  else if (builder.hasHistoryMap()) {
            // 清理HistoryMap
            builder.clearHistoryMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(BestCommanderHistoryRankModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HISTORYMAP) && this.historyMap != null) {
            final boolean needClear = !builder.hasHistoryMap();
            final int tmpFieldCnt = this.historyMap.copyChangeToSs(builder.getHistoryMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHistoryMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(BestCommanderHistoryRankModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasHistoryMap()) {
            this.getHistoryMap().mergeFromSs(proto.getHistoryMap());
        } else {
            if (this.historyMap != null) {
                this.historyMap.mergeFromSs(proto.getHistoryMap());
            }
        }
        this.markAll();
        return BestCommanderHistoryRankModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(BestCommanderHistoryRankModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasHistoryMap()) {
            this.getHistoryMap().mergeChangeFromSs(proto.getHistoryMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        BestCommanderHistoryRankModel.Builder builder = BestCommanderHistoryRankModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_HISTORYMAP) && this.historyMap != null) {
            this.historyMap.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.historyMap != null) {
            this.historyMap.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BestCommanderHistoryRankModelProp)) {
            return false;
        }
        final BestCommanderHistoryRankModelProp otherNode = (BestCommanderHistoryRankModelProp) node;
        if (!this.getHistoryMap().compareDataTo(otherNode.getHistoryMap())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
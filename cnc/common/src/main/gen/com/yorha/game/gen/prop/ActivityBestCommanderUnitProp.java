package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Struct.ActivityBestCommanderUnit;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ActivityBestCommanderUnitPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityBestCommanderUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_DAILYDATAMAP = 0;
    public static final int FIELD_INDEX_VOLUMECURSEASON = 1;
    public static final int FIELD_INDEX_ACTSTAGE = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private Int32ActivityBestCommanderDailyDataMapProp dailyDataMap = null;
    private int volumeCurSeason = Constant.DEFAULT_INT_VALUE;
    private CommanderActivtyStage actStage = CommanderActivtyStage.forNumber(0);

    public ActivityBestCommanderUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityBestCommanderUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get dailyDataMap
     *
     * @return dailyDataMap value
     */
    public Int32ActivityBestCommanderDailyDataMapProp getDailyDataMap() {
        if (this.dailyDataMap == null) {
            this.dailyDataMap = new Int32ActivityBestCommanderDailyDataMapProp(this, FIELD_INDEX_DAILYDATAMAP);
        }
        return this.dailyDataMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putDailyDataMapV(ActivityBestCommanderDailyDataProp v) {
        this.getDailyDataMap().put(v.getActId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ActivityBestCommanderDailyDataProp addEmptyDailyDataMap(Integer k) {
        return this.getDailyDataMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getDailyDataMapSize() {
        if (this.dailyDataMap == null) {
            return 0;
        }
        return this.dailyDataMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isDailyDataMapEmpty() {
        if (this.dailyDataMap == null) {
            return true;
        }
        return this.dailyDataMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ActivityBestCommanderDailyDataProp getDailyDataMapV(Integer k) {
        if (this.dailyDataMap == null || !this.dailyDataMap.containsKey(k)) {
            return null;
        }
        return this.dailyDataMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearDailyDataMap() {
        if (this.dailyDataMap != null) {
            this.dailyDataMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeDailyDataMapV(Integer k) {
        if (this.dailyDataMap != null) {
            this.dailyDataMap.remove(k);
        }
    }
    /**
     * get volumeCurSeason
     *
     * @return volumeCurSeason value
     */
    public int getVolumeCurSeason() {
        return this.volumeCurSeason;
    }

    /**
     * set volumeCurSeason && set marked
     *
     * @param volumeCurSeason new value
     * @return current object
     */
    public ActivityBestCommanderUnitProp setVolumeCurSeason(int volumeCurSeason) {
        if (this.volumeCurSeason != volumeCurSeason) {
            this.mark(FIELD_INDEX_VOLUMECURSEASON);
            this.volumeCurSeason = volumeCurSeason;
        }
        return this;
    }

    /**
     * inner set volumeCurSeason
     *
     * @param volumeCurSeason new value
     */
    private void innerSetVolumeCurSeason(int volumeCurSeason) {
        this.volumeCurSeason = volumeCurSeason;
    }

    /**
     * get actStage
     *
     * @return actStage value
     */
    public CommanderActivtyStage getActStage() {
        return this.actStage;
    }

    /**
     * set actStage && set marked
     *
     * @param actStage new value
     * @return current object
     */
    public ActivityBestCommanderUnitProp setActStage(CommanderActivtyStage actStage) {
        if (actStage == null) {
            throw new NullPointerException();
        }
        if (this.actStage != actStage) {
            this.mark(FIELD_INDEX_ACTSTAGE);
            this.actStage = actStage;
        }
        return this;
    }

    /**
     * inner set actStage
     *
     * @param actStage new value
     */
    private void innerSetActStage(CommanderActivtyStage actStage) {
        this.actStage = actStage;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityBestCommanderUnitPB.Builder getCopyCsBuilder() {
        final ActivityBestCommanderUnitPB.Builder builder = ActivityBestCommanderUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityBestCommanderUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.dailyDataMap != null) {
            StructPB.Int32ActivityBestCommanderDailyDataMapPB.Builder tmpBuilder = StructPB.Int32ActivityBestCommanderDailyDataMapPB.newBuilder();
            final int tmpFieldCnt = this.dailyDataMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDailyDataMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDailyDataMap();
            }
        }  else if (builder.hasDailyDataMap()) {
            // 清理DailyDataMap
            builder.clearDailyDataMap();
            fieldCnt++;
        }
        if (this.getVolumeCurSeason() != 0) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }  else if (builder.hasVolumeCurSeason()) {
            // 清理VolumeCurSeason
            builder.clearVolumeCurSeason();
            fieldCnt++;
        }
        if (this.getActStage() != CommanderActivtyStage.forNumber(0)) {
            builder.setActStage(this.getActStage());
            fieldCnt++;
        }  else if (builder.hasActStage()) {
            // 清理ActStage
            builder.clearActStage();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityBestCommanderUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DAILYDATAMAP) && this.dailyDataMap != null) {
            final boolean needClear = !builder.hasDailyDataMap();
            final int tmpFieldCnt = this.dailyDataMap.copyChangeToCs(builder.getDailyDataMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDailyDataMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_VOLUMECURSEASON)) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTSTAGE)) {
            builder.setActStage(this.getActStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityBestCommanderUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DAILYDATAMAP) && this.dailyDataMap != null) {
            final boolean needClear = !builder.hasDailyDataMap();
            final int tmpFieldCnt = this.dailyDataMap.copyChangeToAndClearDeleteKeysCs(builder.getDailyDataMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDailyDataMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_VOLUMECURSEASON)) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTSTAGE)) {
            builder.setActStage(this.getActStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityBestCommanderUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDailyDataMap()) {
            this.getDailyDataMap().mergeFromCs(proto.getDailyDataMap());
        } else {
            if (this.dailyDataMap != null) {
                this.dailyDataMap.mergeFromCs(proto.getDailyDataMap());
            }
        }
        if (proto.hasVolumeCurSeason()) {
            this.innerSetVolumeCurSeason(proto.getVolumeCurSeason());
        } else {
            this.innerSetVolumeCurSeason(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasActStage()) {
            this.innerSetActStage(proto.getActStage());
        } else {
            this.innerSetActStage(CommanderActivtyStage.forNumber(0));
        }
        this.markAll();
        return ActivityBestCommanderUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityBestCommanderUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDailyDataMap()) {
            this.getDailyDataMap().mergeChangeFromCs(proto.getDailyDataMap());
            fieldCnt++;
        }
        if (proto.hasVolumeCurSeason()) {
            this.setVolumeCurSeason(proto.getVolumeCurSeason());
            fieldCnt++;
        }
        if (proto.hasActStage()) {
            this.setActStage(proto.getActStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityBestCommanderUnit.Builder getCopyDbBuilder() {
        final ActivityBestCommanderUnit.Builder builder = ActivityBestCommanderUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityBestCommanderUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.dailyDataMap != null) {
            Struct.Int32ActivityBestCommanderDailyDataMap.Builder tmpBuilder = Struct.Int32ActivityBestCommanderDailyDataMap.newBuilder();
            final int tmpFieldCnt = this.dailyDataMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDailyDataMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDailyDataMap();
            }
        }  else if (builder.hasDailyDataMap()) {
            // 清理DailyDataMap
            builder.clearDailyDataMap();
            fieldCnt++;
        }
        if (this.getVolumeCurSeason() != 0) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }  else if (builder.hasVolumeCurSeason()) {
            // 清理VolumeCurSeason
            builder.clearVolumeCurSeason();
            fieldCnt++;
        }
        if (this.getActStage() != CommanderActivtyStage.forNumber(0)) {
            builder.setActStage(this.getActStage());
            fieldCnt++;
        }  else if (builder.hasActStage()) {
            // 清理ActStage
            builder.clearActStage();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityBestCommanderUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DAILYDATAMAP) && this.dailyDataMap != null) {
            final boolean needClear = !builder.hasDailyDataMap();
            final int tmpFieldCnt = this.dailyDataMap.copyChangeToDb(builder.getDailyDataMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDailyDataMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_VOLUMECURSEASON)) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTSTAGE)) {
            builder.setActStage(this.getActStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityBestCommanderUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDailyDataMap()) {
            this.getDailyDataMap().mergeFromDb(proto.getDailyDataMap());
        } else {
            if (this.dailyDataMap != null) {
                this.dailyDataMap.mergeFromDb(proto.getDailyDataMap());
            }
        }
        if (proto.hasVolumeCurSeason()) {
            this.innerSetVolumeCurSeason(proto.getVolumeCurSeason());
        } else {
            this.innerSetVolumeCurSeason(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasActStage()) {
            this.innerSetActStage(proto.getActStage());
        } else {
            this.innerSetActStage(CommanderActivtyStage.forNumber(0));
        }
        this.markAll();
        return ActivityBestCommanderUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityBestCommanderUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDailyDataMap()) {
            this.getDailyDataMap().mergeChangeFromDb(proto.getDailyDataMap());
            fieldCnt++;
        }
        if (proto.hasVolumeCurSeason()) {
            this.setVolumeCurSeason(proto.getVolumeCurSeason());
            fieldCnt++;
        }
        if (proto.hasActStage()) {
            this.setActStage(proto.getActStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityBestCommanderUnit.Builder getCopySsBuilder() {
        final ActivityBestCommanderUnit.Builder builder = ActivityBestCommanderUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityBestCommanderUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.dailyDataMap != null) {
            Struct.Int32ActivityBestCommanderDailyDataMap.Builder tmpBuilder = Struct.Int32ActivityBestCommanderDailyDataMap.newBuilder();
            final int tmpFieldCnt = this.dailyDataMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDailyDataMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDailyDataMap();
            }
        }  else if (builder.hasDailyDataMap()) {
            // 清理DailyDataMap
            builder.clearDailyDataMap();
            fieldCnt++;
        }
        if (this.getVolumeCurSeason() != 0) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }  else if (builder.hasVolumeCurSeason()) {
            // 清理VolumeCurSeason
            builder.clearVolumeCurSeason();
            fieldCnt++;
        }
        if (this.getActStage() != CommanderActivtyStage.forNumber(0)) {
            builder.setActStage(this.getActStage());
            fieldCnt++;
        }  else if (builder.hasActStage()) {
            // 清理ActStage
            builder.clearActStage();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityBestCommanderUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DAILYDATAMAP) && this.dailyDataMap != null) {
            final boolean needClear = !builder.hasDailyDataMap();
            final int tmpFieldCnt = this.dailyDataMap.copyChangeToSs(builder.getDailyDataMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDailyDataMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_VOLUMECURSEASON)) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTSTAGE)) {
            builder.setActStage(this.getActStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityBestCommanderUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDailyDataMap()) {
            this.getDailyDataMap().mergeFromSs(proto.getDailyDataMap());
        } else {
            if (this.dailyDataMap != null) {
                this.dailyDataMap.mergeFromSs(proto.getDailyDataMap());
            }
        }
        if (proto.hasVolumeCurSeason()) {
            this.innerSetVolumeCurSeason(proto.getVolumeCurSeason());
        } else {
            this.innerSetVolumeCurSeason(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasActStage()) {
            this.innerSetActStage(proto.getActStage());
        } else {
            this.innerSetActStage(CommanderActivtyStage.forNumber(0));
        }
        this.markAll();
        return ActivityBestCommanderUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityBestCommanderUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDailyDataMap()) {
            this.getDailyDataMap().mergeChangeFromSs(proto.getDailyDataMap());
            fieldCnt++;
        }
        if (proto.hasVolumeCurSeason()) {
            this.setVolumeCurSeason(proto.getVolumeCurSeason());
            fieldCnt++;
        }
        if (proto.hasActStage()) {
            this.setActStage(proto.getActStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityBestCommanderUnit.Builder builder = ActivityBestCommanderUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_DAILYDATAMAP) && this.dailyDataMap != null) {
            this.dailyDataMap.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.dailyDataMap != null) {
            this.dailyDataMap.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityBestCommanderUnitProp)) {
            return false;
        }
        final ActivityBestCommanderUnitProp otherNode = (ActivityBestCommanderUnitProp) node;
        if (!this.getDailyDataMap().compareDataTo(otherNode.getDailyDataMap())) {
            return false;
        }
        if (this.volumeCurSeason != otherNode.volumeCurSeason) {
            return false;
        }
        if (this.actStage != otherNode.actStage) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
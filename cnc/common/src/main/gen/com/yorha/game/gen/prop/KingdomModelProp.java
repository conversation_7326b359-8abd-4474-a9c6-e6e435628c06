package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zone.KingdomModel;
import com.yorha.proto.Zone;
import com.yorha.proto.ZonePB.KingdomModelPB;
import com.yorha.proto.ZonePB;


/**
 * <AUTHOR> auto gen
 */
public class KingdomModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_OFFICEMODEL = 0;
    public static final int FIELD_INDEX_GIFTMODEL = 1;
    public static final int FIELD_INDEX_BUFFMODEL = 2;
    public static final int FIELD_INDEX_SKILLMODEL = 3;
    public static final int FIELD_INDEX_NOWKINGNUMBER = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private KingdomOfficeModelProp officeModel = null;
    private KingdomGiftModelProp giftModel = null;
    private KingdomBuffModelProp buffModel = null;
    private KingdomSkillModelProp skillModel = null;
    private int nowKingNumber = Constant.DEFAULT_INT_VALUE;

    public KingdomModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public KingdomModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get officeModel
     *
     * @return officeModel value
     */
    public KingdomOfficeModelProp getOfficeModel() {
        if (this.officeModel == null) {
            this.officeModel = new KingdomOfficeModelProp(this, FIELD_INDEX_OFFICEMODEL);
        }
        return this.officeModel;
    }

    /**
     * get giftModel
     *
     * @return giftModel value
     */
    public KingdomGiftModelProp getGiftModel() {
        if (this.giftModel == null) {
            this.giftModel = new KingdomGiftModelProp(this, FIELD_INDEX_GIFTMODEL);
        }
        return this.giftModel;
    }

    /**
     * get buffModel
     *
     * @return buffModel value
     */
    public KingdomBuffModelProp getBuffModel() {
        if (this.buffModel == null) {
            this.buffModel = new KingdomBuffModelProp(this, FIELD_INDEX_BUFFMODEL);
        }
        return this.buffModel;
    }

    /**
     * get skillModel
     *
     * @return skillModel value
     */
    public KingdomSkillModelProp getSkillModel() {
        if (this.skillModel == null) {
            this.skillModel = new KingdomSkillModelProp(this, FIELD_INDEX_SKILLMODEL);
        }
        return this.skillModel;
    }

    /**
     * get nowKingNumber
     *
     * @return nowKingNumber value
     */
    public int getNowKingNumber() {
        return this.nowKingNumber;
    }

    /**
     * set nowKingNumber && set marked
     *
     * @param nowKingNumber new value
     * @return current object
     */
    public KingdomModelProp setNowKingNumber(int nowKingNumber) {
        if (this.nowKingNumber != nowKingNumber) {
            this.mark(FIELD_INDEX_NOWKINGNUMBER);
            this.nowKingNumber = nowKingNumber;
        }
        return this;
    }

    /**
     * inner set nowKingNumber
     *
     * @param nowKingNumber new value
     */
    private void innerSetNowKingNumber(int nowKingNumber) {
        this.nowKingNumber = nowKingNumber;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomModelPB.Builder getCopyCsBuilder() {
        final KingdomModelPB.Builder builder = KingdomModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(KingdomModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.buffModel != null) {
            ZonePB.KingdomBuffModelPB.Builder tmpBuilder = ZonePB.KingdomBuffModelPB.newBuilder();
            final int tmpFieldCnt = this.buffModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuffModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuffModel();
            }
        }  else if (builder.hasBuffModel()) {
            // 清理BuffModel
            builder.clearBuffModel();
            fieldCnt++;
        }
        if (this.skillModel != null) {
            ZonePB.KingdomSkillModelPB.Builder tmpBuilder = ZonePB.KingdomSkillModelPB.newBuilder();
            final int tmpFieldCnt = this.skillModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkillModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkillModel();
            }
        }  else if (builder.hasSkillModel()) {
            // 清理SkillModel
            builder.clearSkillModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(KingdomModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUFFMODEL) && this.buffModel != null) {
            final boolean needClear = !builder.hasBuffModel();
            final int tmpFieldCnt = this.buffModel.copyChangeToCs(builder.getBuffModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuffModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKILLMODEL) && this.skillModel != null) {
            final boolean needClear = !builder.hasSkillModel();
            final int tmpFieldCnt = this.skillModel.copyChangeToCs(builder.getSkillModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillModel();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(KingdomModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUFFMODEL) && this.buffModel != null) {
            final boolean needClear = !builder.hasBuffModel();
            final int tmpFieldCnt = this.buffModel.copyChangeToAndClearDeleteKeysCs(builder.getBuffModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuffModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKILLMODEL) && this.skillModel != null) {
            final boolean needClear = !builder.hasSkillModel();
            final int tmpFieldCnt = this.skillModel.copyChangeToAndClearDeleteKeysCs(builder.getSkillModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(KingdomModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBuffModel()) {
            this.getBuffModel().mergeFromCs(proto.getBuffModel());
        } else {
            if (this.buffModel != null) {
                this.buffModel.mergeFromCs(proto.getBuffModel());
            }
        }
        if (proto.hasSkillModel()) {
            this.getSkillModel().mergeFromCs(proto.getSkillModel());
        } else {
            if (this.skillModel != null) {
                this.skillModel.mergeFromCs(proto.getSkillModel());
            }
        }
        this.markAll();
        return KingdomModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(KingdomModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBuffModel()) {
            this.getBuffModel().mergeChangeFromCs(proto.getBuffModel());
            fieldCnt++;
        }
        if (proto.hasSkillModel()) {
            this.getSkillModel().mergeChangeFromCs(proto.getSkillModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomModel.Builder getCopyDbBuilder() {
        final KingdomModel.Builder builder = KingdomModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(KingdomModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.officeModel != null) {
            Zone.KingdomOfficeModel.Builder tmpBuilder = Zone.KingdomOfficeModel.newBuilder();
            final int tmpFieldCnt = this.officeModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOfficeModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOfficeModel();
            }
        }  else if (builder.hasOfficeModel()) {
            // 清理OfficeModel
            builder.clearOfficeModel();
            fieldCnt++;
        }
        if (this.giftModel != null) {
            Zone.KingdomGiftModel.Builder tmpBuilder = Zone.KingdomGiftModel.newBuilder();
            final int tmpFieldCnt = this.giftModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGiftModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGiftModel();
            }
        }  else if (builder.hasGiftModel()) {
            // 清理GiftModel
            builder.clearGiftModel();
            fieldCnt++;
        }
        if (this.buffModel != null) {
            Zone.KingdomBuffModel.Builder tmpBuilder = Zone.KingdomBuffModel.newBuilder();
            final int tmpFieldCnt = this.buffModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuffModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuffModel();
            }
        }  else if (builder.hasBuffModel()) {
            // 清理BuffModel
            builder.clearBuffModel();
            fieldCnt++;
        }
        if (this.skillModel != null) {
            Zone.KingdomSkillModel.Builder tmpBuilder = Zone.KingdomSkillModel.newBuilder();
            final int tmpFieldCnt = this.skillModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkillModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkillModel();
            }
        }  else if (builder.hasSkillModel()) {
            // 清理SkillModel
            builder.clearSkillModel();
            fieldCnt++;
        }
        if (this.getNowKingNumber() != 0) {
            builder.setNowKingNumber(this.getNowKingNumber());
            fieldCnt++;
        }  else if (builder.hasNowKingNumber()) {
            // 清理NowKingNumber
            builder.clearNowKingNumber();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(KingdomModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OFFICEMODEL) && this.officeModel != null) {
            final boolean needClear = !builder.hasOfficeModel();
            final int tmpFieldCnt = this.officeModel.copyChangeToDb(builder.getOfficeModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOfficeModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_GIFTMODEL) && this.giftModel != null) {
            final boolean needClear = !builder.hasGiftModel();
            final int tmpFieldCnt = this.giftModel.copyChangeToDb(builder.getGiftModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUFFMODEL) && this.buffModel != null) {
            final boolean needClear = !builder.hasBuffModel();
            final int tmpFieldCnt = this.buffModel.copyChangeToDb(builder.getBuffModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuffModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKILLMODEL) && this.skillModel != null) {
            final boolean needClear = !builder.hasSkillModel();
            final int tmpFieldCnt = this.skillModel.copyChangeToDb(builder.getSkillModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_NOWKINGNUMBER)) {
            builder.setNowKingNumber(this.getNowKingNumber());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(KingdomModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOfficeModel()) {
            this.getOfficeModel().mergeFromDb(proto.getOfficeModel());
        } else {
            if (this.officeModel != null) {
                this.officeModel.mergeFromDb(proto.getOfficeModel());
            }
        }
        if (proto.hasGiftModel()) {
            this.getGiftModel().mergeFromDb(proto.getGiftModel());
        } else {
            if (this.giftModel != null) {
                this.giftModel.mergeFromDb(proto.getGiftModel());
            }
        }
        if (proto.hasBuffModel()) {
            this.getBuffModel().mergeFromDb(proto.getBuffModel());
        } else {
            if (this.buffModel != null) {
                this.buffModel.mergeFromDb(proto.getBuffModel());
            }
        }
        if (proto.hasSkillModel()) {
            this.getSkillModel().mergeFromDb(proto.getSkillModel());
        } else {
            if (this.skillModel != null) {
                this.skillModel.mergeFromDb(proto.getSkillModel());
            }
        }
        if (proto.hasNowKingNumber()) {
            this.innerSetNowKingNumber(proto.getNowKingNumber());
        } else {
            this.innerSetNowKingNumber(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return KingdomModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(KingdomModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOfficeModel()) {
            this.getOfficeModel().mergeChangeFromDb(proto.getOfficeModel());
            fieldCnt++;
        }
        if (proto.hasGiftModel()) {
            this.getGiftModel().mergeChangeFromDb(proto.getGiftModel());
            fieldCnt++;
        }
        if (proto.hasBuffModel()) {
            this.getBuffModel().mergeChangeFromDb(proto.getBuffModel());
            fieldCnt++;
        }
        if (proto.hasSkillModel()) {
            this.getSkillModel().mergeChangeFromDb(proto.getSkillModel());
            fieldCnt++;
        }
        if (proto.hasNowKingNumber()) {
            this.setNowKingNumber(proto.getNowKingNumber());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomModel.Builder getCopySsBuilder() {
        final KingdomModel.Builder builder = KingdomModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(KingdomModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.officeModel != null) {
            Zone.KingdomOfficeModel.Builder tmpBuilder = Zone.KingdomOfficeModel.newBuilder();
            final int tmpFieldCnt = this.officeModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOfficeModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOfficeModel();
            }
        }  else if (builder.hasOfficeModel()) {
            // 清理OfficeModel
            builder.clearOfficeModel();
            fieldCnt++;
        }
        if (this.giftModel != null) {
            Zone.KingdomGiftModel.Builder tmpBuilder = Zone.KingdomGiftModel.newBuilder();
            final int tmpFieldCnt = this.giftModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGiftModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGiftModel();
            }
        }  else if (builder.hasGiftModel()) {
            // 清理GiftModel
            builder.clearGiftModel();
            fieldCnt++;
        }
        if (this.buffModel != null) {
            Zone.KingdomBuffModel.Builder tmpBuilder = Zone.KingdomBuffModel.newBuilder();
            final int tmpFieldCnt = this.buffModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuffModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuffModel();
            }
        }  else if (builder.hasBuffModel()) {
            // 清理BuffModel
            builder.clearBuffModel();
            fieldCnt++;
        }
        if (this.skillModel != null) {
            Zone.KingdomSkillModel.Builder tmpBuilder = Zone.KingdomSkillModel.newBuilder();
            final int tmpFieldCnt = this.skillModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkillModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkillModel();
            }
        }  else if (builder.hasSkillModel()) {
            // 清理SkillModel
            builder.clearSkillModel();
            fieldCnt++;
        }
        if (this.getNowKingNumber() != 0) {
            builder.setNowKingNumber(this.getNowKingNumber());
            fieldCnt++;
        }  else if (builder.hasNowKingNumber()) {
            // 清理NowKingNumber
            builder.clearNowKingNumber();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(KingdomModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OFFICEMODEL) && this.officeModel != null) {
            final boolean needClear = !builder.hasOfficeModel();
            final int tmpFieldCnt = this.officeModel.copyChangeToSs(builder.getOfficeModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOfficeModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_GIFTMODEL) && this.giftModel != null) {
            final boolean needClear = !builder.hasGiftModel();
            final int tmpFieldCnt = this.giftModel.copyChangeToSs(builder.getGiftModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUFFMODEL) && this.buffModel != null) {
            final boolean needClear = !builder.hasBuffModel();
            final int tmpFieldCnt = this.buffModel.copyChangeToSs(builder.getBuffModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuffModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKILLMODEL) && this.skillModel != null) {
            final boolean needClear = !builder.hasSkillModel();
            final int tmpFieldCnt = this.skillModel.copyChangeToSs(builder.getSkillModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_NOWKINGNUMBER)) {
            builder.setNowKingNumber(this.getNowKingNumber());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(KingdomModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOfficeModel()) {
            this.getOfficeModel().mergeFromSs(proto.getOfficeModel());
        } else {
            if (this.officeModel != null) {
                this.officeModel.mergeFromSs(proto.getOfficeModel());
            }
        }
        if (proto.hasGiftModel()) {
            this.getGiftModel().mergeFromSs(proto.getGiftModel());
        } else {
            if (this.giftModel != null) {
                this.giftModel.mergeFromSs(proto.getGiftModel());
            }
        }
        if (proto.hasBuffModel()) {
            this.getBuffModel().mergeFromSs(proto.getBuffModel());
        } else {
            if (this.buffModel != null) {
                this.buffModel.mergeFromSs(proto.getBuffModel());
            }
        }
        if (proto.hasSkillModel()) {
            this.getSkillModel().mergeFromSs(proto.getSkillModel());
        } else {
            if (this.skillModel != null) {
                this.skillModel.mergeFromSs(proto.getSkillModel());
            }
        }
        if (proto.hasNowKingNumber()) {
            this.innerSetNowKingNumber(proto.getNowKingNumber());
        } else {
            this.innerSetNowKingNumber(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return KingdomModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(KingdomModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOfficeModel()) {
            this.getOfficeModel().mergeChangeFromSs(proto.getOfficeModel());
            fieldCnt++;
        }
        if (proto.hasGiftModel()) {
            this.getGiftModel().mergeChangeFromSs(proto.getGiftModel());
            fieldCnt++;
        }
        if (proto.hasBuffModel()) {
            this.getBuffModel().mergeChangeFromSs(proto.getBuffModel());
            fieldCnt++;
        }
        if (proto.hasSkillModel()) {
            this.getSkillModel().mergeChangeFromSs(proto.getSkillModel());
            fieldCnt++;
        }
        if (proto.hasNowKingNumber()) {
            this.setNowKingNumber(proto.getNowKingNumber());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        KingdomModel.Builder builder = KingdomModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_OFFICEMODEL) && this.officeModel != null) {
            this.officeModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_GIFTMODEL) && this.giftModel != null) {
            this.giftModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BUFFMODEL) && this.buffModel != null) {
            this.buffModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SKILLMODEL) && this.skillModel != null) {
            this.skillModel.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.officeModel != null) {
            this.officeModel.markAll();
        }
        if (this.giftModel != null) {
            this.giftModel.markAll();
        }
        if (this.buffModel != null) {
            this.buffModel.markAll();
        }
        if (this.skillModel != null) {
            this.skillModel.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof KingdomModelProp)) {
            return false;
        }
        final KingdomModelProp otherNode = (KingdomModelProp) node;
        if (!this.getOfficeModel().compareDataTo(otherNode.getOfficeModel())) {
            return false;
        }
        if (!this.getGiftModel().compareDataTo(otherNode.getGiftModel())) {
            return false;
        }
        if (!this.getBuffModel().compareDataTo(otherNode.getBuffModel())) {
            return false;
        }
        if (!this.getSkillModel().compareDataTo(otherNode.getSkillModel())) {
            return false;
        }
        if (this.nowKingNumber != otherNode.nowKingNumber) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityStoreUnit;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ActivityStoreUnitPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityStoreUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_GOODRECORD = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32ActivityStoreGoodRecordMapProp goodRecord = null;

    public ActivityStoreUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityStoreUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get goodRecord
     *
     * @return goodRecord value
     */
    public Int32ActivityStoreGoodRecordMapProp getGoodRecord() {
        if (this.goodRecord == null) {
            this.goodRecord = new Int32ActivityStoreGoodRecordMapProp(this, FIELD_INDEX_GOODRECORD);
        }
        return this.goodRecord;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putGoodRecordV(ActivityStoreGoodRecordProp v) {
        this.getGoodRecord().put(v.getGoodId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ActivityStoreGoodRecordProp addEmptyGoodRecord(Integer k) {
        return this.getGoodRecord().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getGoodRecordSize() {
        if (this.goodRecord == null) {
            return 0;
        }
        return this.goodRecord.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isGoodRecordEmpty() {
        if (this.goodRecord == null) {
            return true;
        }
        return this.goodRecord.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ActivityStoreGoodRecordProp getGoodRecordV(Integer k) {
        if (this.goodRecord == null || !this.goodRecord.containsKey(k)) {
            return null;
        }
        return this.goodRecord.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearGoodRecord() {
        if (this.goodRecord != null) {
            this.goodRecord.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeGoodRecordV(Integer k) {
        if (this.goodRecord != null) {
            this.goodRecord.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityStoreUnitPB.Builder getCopyCsBuilder() {
        final ActivityStoreUnitPB.Builder builder = ActivityStoreUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityStoreUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.goodRecord != null) {
            StructPB.Int32ActivityStoreGoodRecordMapPB.Builder tmpBuilder = StructPB.Int32ActivityStoreGoodRecordMapPB.newBuilder();
            final int tmpFieldCnt = this.goodRecord.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGoodRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGoodRecord();
            }
        }  else if (builder.hasGoodRecord()) {
            // 清理GoodRecord
            builder.clearGoodRecord();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityStoreUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODRECORD) && this.goodRecord != null) {
            final boolean needClear = !builder.hasGoodRecord();
            final int tmpFieldCnt = this.goodRecord.copyChangeToCs(builder.getGoodRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGoodRecord();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityStoreUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODRECORD) && this.goodRecord != null) {
            final boolean needClear = !builder.hasGoodRecord();
            final int tmpFieldCnt = this.goodRecord.copyChangeToAndClearDeleteKeysCs(builder.getGoodRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGoodRecord();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityStoreUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGoodRecord()) {
            this.getGoodRecord().mergeFromCs(proto.getGoodRecord());
        } else {
            if (this.goodRecord != null) {
                this.goodRecord.mergeFromCs(proto.getGoodRecord());
            }
        }
        this.markAll();
        return ActivityStoreUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityStoreUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGoodRecord()) {
            this.getGoodRecord().mergeChangeFromCs(proto.getGoodRecord());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityStoreUnit.Builder getCopyDbBuilder() {
        final ActivityStoreUnit.Builder builder = ActivityStoreUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityStoreUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.goodRecord != null) {
            Struct.Int32ActivityStoreGoodRecordMap.Builder tmpBuilder = Struct.Int32ActivityStoreGoodRecordMap.newBuilder();
            final int tmpFieldCnt = this.goodRecord.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGoodRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGoodRecord();
            }
        }  else if (builder.hasGoodRecord()) {
            // 清理GoodRecord
            builder.clearGoodRecord();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityStoreUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODRECORD) && this.goodRecord != null) {
            final boolean needClear = !builder.hasGoodRecord();
            final int tmpFieldCnt = this.goodRecord.copyChangeToDb(builder.getGoodRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGoodRecord();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityStoreUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGoodRecord()) {
            this.getGoodRecord().mergeFromDb(proto.getGoodRecord());
        } else {
            if (this.goodRecord != null) {
                this.goodRecord.mergeFromDb(proto.getGoodRecord());
            }
        }
        this.markAll();
        return ActivityStoreUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityStoreUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGoodRecord()) {
            this.getGoodRecord().mergeChangeFromDb(proto.getGoodRecord());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityStoreUnit.Builder getCopySsBuilder() {
        final ActivityStoreUnit.Builder builder = ActivityStoreUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityStoreUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.goodRecord != null) {
            Struct.Int32ActivityStoreGoodRecordMap.Builder tmpBuilder = Struct.Int32ActivityStoreGoodRecordMap.newBuilder();
            final int tmpFieldCnt = this.goodRecord.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGoodRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGoodRecord();
            }
        }  else if (builder.hasGoodRecord()) {
            // 清理GoodRecord
            builder.clearGoodRecord();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityStoreUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODRECORD) && this.goodRecord != null) {
            final boolean needClear = !builder.hasGoodRecord();
            final int tmpFieldCnt = this.goodRecord.copyChangeToSs(builder.getGoodRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGoodRecord();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityStoreUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGoodRecord()) {
            this.getGoodRecord().mergeFromSs(proto.getGoodRecord());
        } else {
            if (this.goodRecord != null) {
                this.goodRecord.mergeFromSs(proto.getGoodRecord());
            }
        }
        this.markAll();
        return ActivityStoreUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityStoreUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGoodRecord()) {
            this.getGoodRecord().mergeChangeFromSs(proto.getGoodRecord());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityStoreUnit.Builder builder = ActivityStoreUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_GOODRECORD) && this.goodRecord != null) {
            this.goodRecord.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.goodRecord != null) {
            this.goodRecord.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityStoreUnitProp)) {
            return false;
        }
        final ActivityStoreUnitProp otherNode = (ActivityStoreUnitProp) node;
        if (!this.getGoodRecord().compareDataTo(otherNode.getGoodRecord())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructMail.MailClanContributionRankingData;
import com.yorha.proto.StructMail;
import com.yorha.proto.StructMailPB.MailClanContributionRankingDataPB;
import com.yorha.proto.StructMailPB;


/**
 * <AUTHOR> auto gen
 */
public class MailClanContributionRankingDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ITEMS = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private MailClanContributionRankingItemListProp items = null;

    public MailClanContributionRankingDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MailClanContributionRankingDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get items
     *
     * @return items value
     */
    public MailClanContributionRankingItemListProp getItems() {
        if (this.items == null) {
            this.items = new MailClanContributionRankingItemListProp(this, FIELD_INDEX_ITEMS);
        }
        return this.items;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addItems(MailClanContributionRankingItemProp v) {
        this.getItems().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public MailClanContributionRankingItemProp getItemsIndex(int index) {
        return this.getItems().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public MailClanContributionRankingItemProp removeItems(MailClanContributionRankingItemProp v) {
        if (this.items == null) {
            return null;
        }
        if(this.items.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getItemsSize() {
        if (this.items == null) {
            return 0;
        }
        return this.items.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isItemsEmpty() {
        if (this.items == null) {
            return true;
        }
        return this.getItems().isEmpty();
    }

    /**
     * clear list
     */
    public void clearItems() {
        this.getItems().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public MailClanContributionRankingItemProp removeItemsIndex(int index) {
        return this.getItems().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public MailClanContributionRankingItemProp setItemsIndex(int index, MailClanContributionRankingItemProp v) {
        return this.getItems().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailClanContributionRankingDataPB.Builder getCopyCsBuilder() {
        final MailClanContributionRankingDataPB.Builder builder = MailClanContributionRankingDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MailClanContributionRankingDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.items != null) {
            StructMailPB.MailClanContributionRankingItemListPB.Builder tmpBuilder = StructMailPB.MailClanContributionRankingItemListPB.newBuilder();
            final int tmpFieldCnt = this.items.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItems();
            }
        }  else if (builder.hasItems()) {
            // 清理Items
            builder.clearItems();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MailClanContributionRankingDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ITEMS) && this.items != null) {
            final boolean needClear = !builder.hasItems();
            final int tmpFieldCnt = this.items.copyChangeToCs(builder.getItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItems();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MailClanContributionRankingDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ITEMS) && this.items != null) {
            final boolean needClear = !builder.hasItems();
            final int tmpFieldCnt = this.items.copyChangeToCs(builder.getItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItems();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MailClanContributionRankingDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasItems()) {
            this.getItems().mergeFromCs(proto.getItems());
        } else {
            if (this.items != null) {
                this.items.mergeFromCs(proto.getItems());
            }
        }
        this.markAll();
        return MailClanContributionRankingDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MailClanContributionRankingDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasItems()) {
            this.getItems().mergeChangeFromCs(proto.getItems());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailClanContributionRankingData.Builder getCopyDbBuilder() {
        final MailClanContributionRankingData.Builder builder = MailClanContributionRankingData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MailClanContributionRankingData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.items != null) {
            StructMail.MailClanContributionRankingItemList.Builder tmpBuilder = StructMail.MailClanContributionRankingItemList.newBuilder();
            final int tmpFieldCnt = this.items.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItems();
            }
        }  else if (builder.hasItems()) {
            // 清理Items
            builder.clearItems();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MailClanContributionRankingData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ITEMS) && this.items != null) {
            final boolean needClear = !builder.hasItems();
            final int tmpFieldCnt = this.items.copyChangeToDb(builder.getItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItems();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MailClanContributionRankingData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasItems()) {
            this.getItems().mergeFromDb(proto.getItems());
        } else {
            if (this.items != null) {
                this.items.mergeFromDb(proto.getItems());
            }
        }
        this.markAll();
        return MailClanContributionRankingDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MailClanContributionRankingData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasItems()) {
            this.getItems().mergeChangeFromDb(proto.getItems());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailClanContributionRankingData.Builder getCopySsBuilder() {
        final MailClanContributionRankingData.Builder builder = MailClanContributionRankingData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MailClanContributionRankingData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.items != null) {
            StructMail.MailClanContributionRankingItemList.Builder tmpBuilder = StructMail.MailClanContributionRankingItemList.newBuilder();
            final int tmpFieldCnt = this.items.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItems();
            }
        }  else if (builder.hasItems()) {
            // 清理Items
            builder.clearItems();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MailClanContributionRankingData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ITEMS) && this.items != null) {
            final boolean needClear = !builder.hasItems();
            final int tmpFieldCnt = this.items.copyChangeToSs(builder.getItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItems();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MailClanContributionRankingData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasItems()) {
            this.getItems().mergeFromSs(proto.getItems());
        } else {
            if (this.items != null) {
                this.items.mergeFromSs(proto.getItems());
            }
        }
        this.markAll();
        return MailClanContributionRankingDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MailClanContributionRankingData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasItems()) {
            this.getItems().mergeChangeFromSs(proto.getItems());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MailClanContributionRankingData.Builder builder = MailClanContributionRankingData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ITEMS) && this.items != null) {
            this.items.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.items != null) {
            this.items.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MailClanContributionRankingDataProp)) {
            return false;
        }
        final MailClanContributionRankingDataProp otherNode = (MailClanContributionRankingDataProp) node;
        if (!this.getItems().compareDataTo(otherNode.getItems())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerZoneModel;
import com.yorha.proto.PlayerPB.PlayerZoneModelPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerZoneModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ZONEID = 0;
    public static final int FIELD_INDEX_BORNZONEID = 1;
    public static final int FIELD_INDEX_BORNZONEINEX = 2;
    public static final int FIELD_INDEX_BORNZONEOPENTSMS = 3;
    public static final int FIELD_INDEX_TARGETFINALSEASON = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private int zoneId = Constant.DEFAULT_INT_VALUE;
    private int bornZoneId = Constant.DEFAULT_INT_VALUE;
    private int bornZoneInex = Constant.DEFAULT_INT_VALUE;
    private long bornZoneOpenTsMs = Constant.DEFAULT_LONG_VALUE;
    private int targetFinalSeason = Constant.DEFAULT_INT_VALUE;

    public PlayerZoneModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerZoneModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get zoneId
     *
     * @return zoneId value
     */
    public int getZoneId() {
        return this.zoneId;
    }

    /**
     * set zoneId && set marked
     *
     * @param zoneId new value
     * @return current object
     */
    public PlayerZoneModelProp setZoneId(int zoneId) {
        if (this.zoneId != zoneId) {
            this.mark(FIELD_INDEX_ZONEID);
            this.zoneId = zoneId;
        }
        return this;
    }

    /**
     * inner set zoneId
     *
     * @param zoneId new value
     */
    private void innerSetZoneId(int zoneId) {
        this.zoneId = zoneId;
    }

    /**
     * get bornZoneId
     *
     * @return bornZoneId value
     */
    public int getBornZoneId() {
        return this.bornZoneId;
    }

    /**
     * set bornZoneId && set marked
     *
     * @param bornZoneId new value
     * @return current object
     */
    public PlayerZoneModelProp setBornZoneId(int bornZoneId) {
        if (this.bornZoneId != bornZoneId) {
            this.mark(FIELD_INDEX_BORNZONEID);
            this.bornZoneId = bornZoneId;
        }
        return this;
    }

    /**
     * inner set bornZoneId
     *
     * @param bornZoneId new value
     */
    private void innerSetBornZoneId(int bornZoneId) {
        this.bornZoneId = bornZoneId;
    }

    /**
     * get bornZoneInex
     *
     * @return bornZoneInex value
     */
    public int getBornZoneInex() {
        return this.bornZoneInex;
    }

    /**
     * set bornZoneInex && set marked
     *
     * @param bornZoneInex new value
     * @return current object
     */
    public PlayerZoneModelProp setBornZoneInex(int bornZoneInex) {
        if (this.bornZoneInex != bornZoneInex) {
            this.mark(FIELD_INDEX_BORNZONEINEX);
            this.bornZoneInex = bornZoneInex;
        }
        return this;
    }

    /**
     * inner set bornZoneInex
     *
     * @param bornZoneInex new value
     */
    private void innerSetBornZoneInex(int bornZoneInex) {
        this.bornZoneInex = bornZoneInex;
    }

    /**
     * get bornZoneOpenTsMs
     *
     * @return bornZoneOpenTsMs value
     */
    public long getBornZoneOpenTsMs() {
        return this.bornZoneOpenTsMs;
    }

    /**
     * set bornZoneOpenTsMs && set marked
     *
     * @param bornZoneOpenTsMs new value
     * @return current object
     */
    public PlayerZoneModelProp setBornZoneOpenTsMs(long bornZoneOpenTsMs) {
        if (this.bornZoneOpenTsMs != bornZoneOpenTsMs) {
            this.mark(FIELD_INDEX_BORNZONEOPENTSMS);
            this.bornZoneOpenTsMs = bornZoneOpenTsMs;
        }
        return this;
    }

    /**
     * inner set bornZoneOpenTsMs
     *
     * @param bornZoneOpenTsMs new value
     */
    private void innerSetBornZoneOpenTsMs(long bornZoneOpenTsMs) {
        this.bornZoneOpenTsMs = bornZoneOpenTsMs;
    }

    /**
     * get targetFinalSeason
     *
     * @return targetFinalSeason value
     */
    public int getTargetFinalSeason() {
        return this.targetFinalSeason;
    }

    /**
     * set targetFinalSeason && set marked
     *
     * @param targetFinalSeason new value
     * @return current object
     */
    public PlayerZoneModelProp setTargetFinalSeason(int targetFinalSeason) {
        if (this.targetFinalSeason != targetFinalSeason) {
            this.mark(FIELD_INDEX_TARGETFINALSEASON);
            this.targetFinalSeason = targetFinalSeason;
        }
        return this;
    }

    /**
     * inner set targetFinalSeason
     *
     * @param targetFinalSeason new value
     */
    private void innerSetTargetFinalSeason(int targetFinalSeason) {
        this.targetFinalSeason = targetFinalSeason;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerZoneModelPB.Builder getCopyCsBuilder() {
        final PlayerZoneModelPB.Builder builder = PlayerZoneModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerZoneModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        if (this.getBornZoneOpenTsMs() != 0L) {
            builder.setBornZoneOpenTsMs(this.getBornZoneOpenTsMs());
            fieldCnt++;
        }  else if (builder.hasBornZoneOpenTsMs()) {
            // 清理BornZoneOpenTsMs
            builder.clearBornZoneOpenTsMs();
            fieldCnt++;
        }
        if (this.getTargetFinalSeason() != 0) {
            builder.setTargetFinalSeason(this.getTargetFinalSeason());
            fieldCnt++;
        }  else if (builder.hasTargetFinalSeason()) {
            // 清理TargetFinalSeason
            builder.clearTargetFinalSeason();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerZoneModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BORNZONEOPENTSMS)) {
            builder.setBornZoneOpenTsMs(this.getBornZoneOpenTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETFINALSEASON)) {
            builder.setTargetFinalSeason(this.getTargetFinalSeason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerZoneModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BORNZONEOPENTSMS)) {
            builder.setBornZoneOpenTsMs(this.getBornZoneOpenTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETFINALSEASON)) {
            builder.setTargetFinalSeason(this.getTargetFinalSeason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerZoneModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBornZoneOpenTsMs()) {
            this.innerSetBornZoneOpenTsMs(proto.getBornZoneOpenTsMs());
        } else {
            this.innerSetBornZoneOpenTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTargetFinalSeason()) {
            this.innerSetTargetFinalSeason(proto.getTargetFinalSeason());
        } else {
            this.innerSetTargetFinalSeason(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerZoneModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerZoneModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        if (proto.hasBornZoneOpenTsMs()) {
            this.setBornZoneOpenTsMs(proto.getBornZoneOpenTsMs());
            fieldCnt++;
        }
        if (proto.hasTargetFinalSeason()) {
            this.setTargetFinalSeason(proto.getTargetFinalSeason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerZoneModel.Builder getCopyDbBuilder() {
        final PlayerZoneModel.Builder builder = PlayerZoneModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerZoneModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        if (this.getBornZoneId() != 0) {
            builder.setBornZoneId(this.getBornZoneId());
            fieldCnt++;
        }  else if (builder.hasBornZoneId()) {
            // 清理BornZoneId
            builder.clearBornZoneId();
            fieldCnt++;
        }
        if (this.getBornZoneInex() != 0) {
            builder.setBornZoneInex(this.getBornZoneInex());
            fieldCnt++;
        }  else if (builder.hasBornZoneInex()) {
            // 清理BornZoneInex
            builder.clearBornZoneInex();
            fieldCnt++;
        }
        if (this.getBornZoneOpenTsMs() != 0L) {
            builder.setBornZoneOpenTsMs(this.getBornZoneOpenTsMs());
            fieldCnt++;
        }  else if (builder.hasBornZoneOpenTsMs()) {
            // 清理BornZoneOpenTsMs
            builder.clearBornZoneOpenTsMs();
            fieldCnt++;
        }
        if (this.getTargetFinalSeason() != 0) {
            builder.setTargetFinalSeason(this.getTargetFinalSeason());
            fieldCnt++;
        }  else if (builder.hasTargetFinalSeason()) {
            // 清理TargetFinalSeason
            builder.clearTargetFinalSeason();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerZoneModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BORNZONEID)) {
            builder.setBornZoneId(this.getBornZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BORNZONEINEX)) {
            builder.setBornZoneInex(this.getBornZoneInex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BORNZONEOPENTSMS)) {
            builder.setBornZoneOpenTsMs(this.getBornZoneOpenTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETFINALSEASON)) {
            builder.setTargetFinalSeason(this.getTargetFinalSeason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerZoneModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBornZoneId()) {
            this.innerSetBornZoneId(proto.getBornZoneId());
        } else {
            this.innerSetBornZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBornZoneInex()) {
            this.innerSetBornZoneInex(proto.getBornZoneInex());
        } else {
            this.innerSetBornZoneInex(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBornZoneOpenTsMs()) {
            this.innerSetBornZoneOpenTsMs(proto.getBornZoneOpenTsMs());
        } else {
            this.innerSetBornZoneOpenTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTargetFinalSeason()) {
            this.innerSetTargetFinalSeason(proto.getTargetFinalSeason());
        } else {
            this.innerSetTargetFinalSeason(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerZoneModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerZoneModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        if (proto.hasBornZoneId()) {
            this.setBornZoneId(proto.getBornZoneId());
            fieldCnt++;
        }
        if (proto.hasBornZoneInex()) {
            this.setBornZoneInex(proto.getBornZoneInex());
            fieldCnt++;
        }
        if (proto.hasBornZoneOpenTsMs()) {
            this.setBornZoneOpenTsMs(proto.getBornZoneOpenTsMs());
            fieldCnt++;
        }
        if (proto.hasTargetFinalSeason()) {
            this.setTargetFinalSeason(proto.getTargetFinalSeason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerZoneModel.Builder getCopySsBuilder() {
        final PlayerZoneModel.Builder builder = PlayerZoneModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerZoneModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        if (this.getBornZoneId() != 0) {
            builder.setBornZoneId(this.getBornZoneId());
            fieldCnt++;
        }  else if (builder.hasBornZoneId()) {
            // 清理BornZoneId
            builder.clearBornZoneId();
            fieldCnt++;
        }
        if (this.getBornZoneInex() != 0) {
            builder.setBornZoneInex(this.getBornZoneInex());
            fieldCnt++;
        }  else if (builder.hasBornZoneInex()) {
            // 清理BornZoneInex
            builder.clearBornZoneInex();
            fieldCnt++;
        }
        if (this.getBornZoneOpenTsMs() != 0L) {
            builder.setBornZoneOpenTsMs(this.getBornZoneOpenTsMs());
            fieldCnt++;
        }  else if (builder.hasBornZoneOpenTsMs()) {
            // 清理BornZoneOpenTsMs
            builder.clearBornZoneOpenTsMs();
            fieldCnt++;
        }
        if (this.getTargetFinalSeason() != 0) {
            builder.setTargetFinalSeason(this.getTargetFinalSeason());
            fieldCnt++;
        }  else if (builder.hasTargetFinalSeason()) {
            // 清理TargetFinalSeason
            builder.clearTargetFinalSeason();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerZoneModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BORNZONEID)) {
            builder.setBornZoneId(this.getBornZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BORNZONEINEX)) {
            builder.setBornZoneInex(this.getBornZoneInex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BORNZONEOPENTSMS)) {
            builder.setBornZoneOpenTsMs(this.getBornZoneOpenTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETFINALSEASON)) {
            builder.setTargetFinalSeason(this.getTargetFinalSeason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerZoneModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBornZoneId()) {
            this.innerSetBornZoneId(proto.getBornZoneId());
        } else {
            this.innerSetBornZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBornZoneInex()) {
            this.innerSetBornZoneInex(proto.getBornZoneInex());
        } else {
            this.innerSetBornZoneInex(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBornZoneOpenTsMs()) {
            this.innerSetBornZoneOpenTsMs(proto.getBornZoneOpenTsMs());
        } else {
            this.innerSetBornZoneOpenTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTargetFinalSeason()) {
            this.innerSetTargetFinalSeason(proto.getTargetFinalSeason());
        } else {
            this.innerSetTargetFinalSeason(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerZoneModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerZoneModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        if (proto.hasBornZoneId()) {
            this.setBornZoneId(proto.getBornZoneId());
            fieldCnt++;
        }
        if (proto.hasBornZoneInex()) {
            this.setBornZoneInex(proto.getBornZoneInex());
            fieldCnt++;
        }
        if (proto.hasBornZoneOpenTsMs()) {
            this.setBornZoneOpenTsMs(proto.getBornZoneOpenTsMs());
            fieldCnt++;
        }
        if (proto.hasTargetFinalSeason()) {
            this.setTargetFinalSeason(proto.getTargetFinalSeason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerZoneModel.Builder builder = PlayerZoneModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerZoneModelProp)) {
            return false;
        }
        final PlayerZoneModelProp otherNode = (PlayerZoneModelProp) node;
        if (this.zoneId != otherNode.zoneId) {
            return false;
        }
        if (this.bornZoneId != otherNode.bornZoneId) {
            return false;
        }
        if (this.bornZoneInex != otherNode.bornZoneInex) {
            return false;
        }
        if (this.bornZoneOpenTsMs != otherNode.bornZoneOpenTsMs) {
            return false;
        }
        if (this.targetFinalSeason != otherNode.targetFinalSeason) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ClanStoreItemInfo;
import com.yorha.proto.StructPB.ClanStoreItemInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanStoreItemInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ITEMID = 0;
    public static final int FIELD_INDEX_ITEMNUM = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int itemId = Constant.DEFAULT_INT_VALUE;
    private int itemNum = Constant.DEFAULT_INT_VALUE;

    public ClanStoreItemInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanStoreItemInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get itemId
     *
     * @return itemId value
     */
    public int getItemId() {
        return this.itemId;
    }

    /**
     * set itemId && set marked
     *
     * @param itemId new value
     * @return current object
     */
    public ClanStoreItemInfoProp setItemId(int itemId) {
        if (this.itemId != itemId) {
            this.mark(FIELD_INDEX_ITEMID);
            this.itemId = itemId;
        }
        return this;
    }

    /**
     * inner set itemId
     *
     * @param itemId new value
     */
    private void innerSetItemId(int itemId) {
        this.itemId = itemId;
    }

    /**
     * get itemNum
     *
     * @return itemNum value
     */
    public int getItemNum() {
        return this.itemNum;
    }

    /**
     * set itemNum && set marked
     *
     * @param itemNum new value
     * @return current object
     */
    public ClanStoreItemInfoProp setItemNum(int itemNum) {
        if (this.itemNum != itemNum) {
            this.mark(FIELD_INDEX_ITEMNUM);
            this.itemNum = itemNum;
        }
        return this;
    }

    /**
     * inner set itemNum
     *
     * @param itemNum new value
     */
    private void innerSetItemNum(int itemNum) {
        this.itemNum = itemNum;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanStoreItemInfoPB.Builder getCopyCsBuilder() {
        final ClanStoreItemInfoPB.Builder builder = ClanStoreItemInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanStoreItemInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getItemId() != 0) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }  else if (builder.hasItemId()) {
            // 清理ItemId
            builder.clearItemId();
            fieldCnt++;
        }
        if (this.getItemNum() != 0) {
            builder.setItemNum(this.getItemNum());
            fieldCnt++;
        }  else if (builder.hasItemNum()) {
            // 清理ItemNum
            builder.clearItemNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanStoreItemInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ITEMID)) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMNUM)) {
            builder.setItemNum(this.getItemNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanStoreItemInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ITEMID)) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMNUM)) {
            builder.setItemNum(this.getItemNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanStoreItemInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasItemId()) {
            this.innerSetItemId(proto.getItemId());
        } else {
            this.innerSetItemId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasItemNum()) {
            this.innerSetItemNum(proto.getItemNum());
        } else {
            this.innerSetItemNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanStoreItemInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanStoreItemInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasItemId()) {
            this.setItemId(proto.getItemId());
            fieldCnt++;
        }
        if (proto.hasItemNum()) {
            this.setItemNum(proto.getItemNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanStoreItemInfo.Builder getCopyDbBuilder() {
        final ClanStoreItemInfo.Builder builder = ClanStoreItemInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanStoreItemInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getItemId() != 0) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }  else if (builder.hasItemId()) {
            // 清理ItemId
            builder.clearItemId();
            fieldCnt++;
        }
        if (this.getItemNum() != 0) {
            builder.setItemNum(this.getItemNum());
            fieldCnt++;
        }  else if (builder.hasItemNum()) {
            // 清理ItemNum
            builder.clearItemNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanStoreItemInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ITEMID)) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMNUM)) {
            builder.setItemNum(this.getItemNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanStoreItemInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasItemId()) {
            this.innerSetItemId(proto.getItemId());
        } else {
            this.innerSetItemId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasItemNum()) {
            this.innerSetItemNum(proto.getItemNum());
        } else {
            this.innerSetItemNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanStoreItemInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanStoreItemInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasItemId()) {
            this.setItemId(proto.getItemId());
            fieldCnt++;
        }
        if (proto.hasItemNum()) {
            this.setItemNum(proto.getItemNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanStoreItemInfo.Builder getCopySsBuilder() {
        final ClanStoreItemInfo.Builder builder = ClanStoreItemInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanStoreItemInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getItemId() != 0) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }  else if (builder.hasItemId()) {
            // 清理ItemId
            builder.clearItemId();
            fieldCnt++;
        }
        if (this.getItemNum() != 0) {
            builder.setItemNum(this.getItemNum());
            fieldCnt++;
        }  else if (builder.hasItemNum()) {
            // 清理ItemNum
            builder.clearItemNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanStoreItemInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ITEMID)) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMNUM)) {
            builder.setItemNum(this.getItemNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanStoreItemInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasItemId()) {
            this.innerSetItemId(proto.getItemId());
        } else {
            this.innerSetItemId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasItemNum()) {
            this.innerSetItemNum(proto.getItemNum());
        } else {
            this.innerSetItemNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanStoreItemInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanStoreItemInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasItemId()) {
            this.setItemId(proto.getItemId());
            fieldCnt++;
        }
        if (proto.hasItemNum()) {
            this.setItemNum(proto.getItemNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanStoreItemInfo.Builder builder = ClanStoreItemInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.itemId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanStoreItemInfoProp)) {
            return false;
        }
        final ClanStoreItemInfoProp otherNode = (ClanStoreItemInfoProp) node;
        if (this.itemId != otherNode.itemId) {
            return false;
        }
        if (this.itemNum != otherNode.itemNum) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
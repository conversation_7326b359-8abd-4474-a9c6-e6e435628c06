package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zoneside.ZoneSideBestCommanderTotalRankUnit;
import com.yorha.proto.Zoneside;
import com.yorha.proto.ZonesidePB.ZoneSideBestCommanderTotalRankUnitPB;
import com.yorha.proto.ZonesidePB;


/**
 * <AUTHOR> auto gen
 */
public class ZoneSideBestCommanderTotalRankUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CHOOSEITEMS = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int64PlayerChooseItemMapProp chooseItems = null;

    public ZoneSideBestCommanderTotalRankUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ZoneSideBestCommanderTotalRankUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get chooseItems
     *
     * @return chooseItems value
     */
    public Int64PlayerChooseItemMapProp getChooseItems() {
        if (this.chooseItems == null) {
            this.chooseItems = new Int64PlayerChooseItemMapProp(this, FIELD_INDEX_CHOOSEITEMS);
        }
        return this.chooseItems;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putChooseItemsV(PlayerChooseItemProp v) {
        this.getChooseItems().put(v.getPlayerId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerChooseItemProp addEmptyChooseItems(Long k) {
        return this.getChooseItems().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getChooseItemsSize() {
        if (this.chooseItems == null) {
            return 0;
        }
        return this.chooseItems.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isChooseItemsEmpty() {
        if (this.chooseItems == null) {
            return true;
        }
        return this.chooseItems.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerChooseItemProp getChooseItemsV(Long k) {
        if (this.chooseItems == null || !this.chooseItems.containsKey(k)) {
            return null;
        }
        return this.chooseItems.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearChooseItems() {
        if (this.chooseItems != null) {
            this.chooseItems.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeChooseItemsV(Long k) {
        if (this.chooseItems != null) {
            this.chooseItems.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSideBestCommanderTotalRankUnitPB.Builder getCopyCsBuilder() {
        final ZoneSideBestCommanderTotalRankUnitPB.Builder builder = ZoneSideBestCommanderTotalRankUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ZoneSideBestCommanderTotalRankUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.chooseItems != null) {
            ZonesidePB.Int64PlayerChooseItemMapPB.Builder tmpBuilder = ZonesidePB.Int64PlayerChooseItemMapPB.newBuilder();
            final int tmpFieldCnt = this.chooseItems.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChooseItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChooseItems();
            }
        }  else if (builder.hasChooseItems()) {
            // 清理ChooseItems
            builder.clearChooseItems();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ZoneSideBestCommanderTotalRankUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHOOSEITEMS) && this.chooseItems != null) {
            final boolean needClear = !builder.hasChooseItems();
            final int tmpFieldCnt = this.chooseItems.copyChangeToCs(builder.getChooseItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChooseItems();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ZoneSideBestCommanderTotalRankUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHOOSEITEMS) && this.chooseItems != null) {
            final boolean needClear = !builder.hasChooseItems();
            final int tmpFieldCnt = this.chooseItems.copyChangeToAndClearDeleteKeysCs(builder.getChooseItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChooseItems();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ZoneSideBestCommanderTotalRankUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChooseItems()) {
            this.getChooseItems().mergeFromCs(proto.getChooseItems());
        } else {
            if (this.chooseItems != null) {
                this.chooseItems.mergeFromCs(proto.getChooseItems());
            }
        }
        this.markAll();
        return ZoneSideBestCommanderTotalRankUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ZoneSideBestCommanderTotalRankUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChooseItems()) {
            this.getChooseItems().mergeChangeFromCs(proto.getChooseItems());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSideBestCommanderTotalRankUnit.Builder getCopyDbBuilder() {
        final ZoneSideBestCommanderTotalRankUnit.Builder builder = ZoneSideBestCommanderTotalRankUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ZoneSideBestCommanderTotalRankUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.chooseItems != null) {
            Zoneside.Int64PlayerChooseItemMap.Builder tmpBuilder = Zoneside.Int64PlayerChooseItemMap.newBuilder();
            final int tmpFieldCnt = this.chooseItems.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChooseItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChooseItems();
            }
        }  else if (builder.hasChooseItems()) {
            // 清理ChooseItems
            builder.clearChooseItems();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ZoneSideBestCommanderTotalRankUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHOOSEITEMS) && this.chooseItems != null) {
            final boolean needClear = !builder.hasChooseItems();
            final int tmpFieldCnt = this.chooseItems.copyChangeToDb(builder.getChooseItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChooseItems();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ZoneSideBestCommanderTotalRankUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChooseItems()) {
            this.getChooseItems().mergeFromDb(proto.getChooseItems());
        } else {
            if (this.chooseItems != null) {
                this.chooseItems.mergeFromDb(proto.getChooseItems());
            }
        }
        this.markAll();
        return ZoneSideBestCommanderTotalRankUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ZoneSideBestCommanderTotalRankUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChooseItems()) {
            this.getChooseItems().mergeChangeFromDb(proto.getChooseItems());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSideBestCommanderTotalRankUnit.Builder getCopySsBuilder() {
        final ZoneSideBestCommanderTotalRankUnit.Builder builder = ZoneSideBestCommanderTotalRankUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ZoneSideBestCommanderTotalRankUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.chooseItems != null) {
            Zoneside.Int64PlayerChooseItemMap.Builder tmpBuilder = Zoneside.Int64PlayerChooseItemMap.newBuilder();
            final int tmpFieldCnt = this.chooseItems.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChooseItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChooseItems();
            }
        }  else if (builder.hasChooseItems()) {
            // 清理ChooseItems
            builder.clearChooseItems();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ZoneSideBestCommanderTotalRankUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHOOSEITEMS) && this.chooseItems != null) {
            final boolean needClear = !builder.hasChooseItems();
            final int tmpFieldCnt = this.chooseItems.copyChangeToSs(builder.getChooseItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChooseItems();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ZoneSideBestCommanderTotalRankUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChooseItems()) {
            this.getChooseItems().mergeFromSs(proto.getChooseItems());
        } else {
            if (this.chooseItems != null) {
                this.chooseItems.mergeFromSs(proto.getChooseItems());
            }
        }
        this.markAll();
        return ZoneSideBestCommanderTotalRankUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ZoneSideBestCommanderTotalRankUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChooseItems()) {
            this.getChooseItems().mergeChangeFromSs(proto.getChooseItems());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ZoneSideBestCommanderTotalRankUnit.Builder builder = ZoneSideBestCommanderTotalRankUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CHOOSEITEMS) && this.chooseItems != null) {
            this.chooseItems.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.chooseItems != null) {
            this.chooseItems.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ZoneSideBestCommanderTotalRankUnitProp)) {
            return false;
        }
        final ZoneSideBestCommanderTotalRankUnitProp otherNode = (ZoneSideBestCommanderTotalRankUnitProp) node;
        if (!this.getChooseItems().compareDataTo(otherNode.getChooseItems())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
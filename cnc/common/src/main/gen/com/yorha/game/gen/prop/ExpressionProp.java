package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.Expression;
import com.yorha.proto.StructPB.ExpressionPB;


/**
 * <AUTHOR> auto gen
 */
public class ExpressionProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_EXPRESSIONID = 0;
    public static final int FIELD_INDEX_TIME = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int expressionId = Constant.DEFAULT_INT_VALUE;
    private long time = Constant.DEFAULT_LONG_VALUE;

    public ExpressionProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ExpressionProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get expressionId
     *
     * @return expressionId value
     */
    public int getExpressionId() {
        return this.expressionId;
    }

    /**
     * set expressionId && set marked
     *
     * @param expressionId new value
     * @return current object
     */
    public ExpressionProp setExpressionId(int expressionId) {
        if (this.expressionId != expressionId) {
            this.mark(FIELD_INDEX_EXPRESSIONID);
            this.expressionId = expressionId;
        }
        return this;
    }

    /**
     * inner set expressionId
     *
     * @param expressionId new value
     */
    private void innerSetExpressionId(int expressionId) {
        this.expressionId = expressionId;
    }

    /**
     * get time
     *
     * @return time value
     */
    public long getTime() {
        return this.time;
    }

    /**
     * set time && set marked
     *
     * @param time new value
     * @return current object
     */
    public ExpressionProp setTime(long time) {
        if (this.time != time) {
            this.mark(FIELD_INDEX_TIME);
            this.time = time;
        }
        return this;
    }

    /**
     * inner set time
     *
     * @param time new value
     */
    private void innerSetTime(long time) {
        this.time = time;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ExpressionPB.Builder getCopyCsBuilder() {
        final ExpressionPB.Builder builder = ExpressionPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ExpressionPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getExpressionId() != 0) {
            builder.setExpressionId(this.getExpressionId());
            fieldCnt++;
        }  else if (builder.hasExpressionId()) {
            // 清理ExpressionId
            builder.clearExpressionId();
            fieldCnt++;
        }
        if (this.getTime() != 0L) {
            builder.setTime(this.getTime());
            fieldCnt++;
        }  else if (builder.hasTime()) {
            // 清理Time
            builder.clearTime();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ExpressionPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_EXPRESSIONID)) {
            builder.setExpressionId(this.getExpressionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TIME)) {
            builder.setTime(this.getTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ExpressionPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_EXPRESSIONID)) {
            builder.setExpressionId(this.getExpressionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TIME)) {
            builder.setTime(this.getTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ExpressionPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasExpressionId()) {
            this.innerSetExpressionId(proto.getExpressionId());
        } else {
            this.innerSetExpressionId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTime()) {
            this.innerSetTime(proto.getTime());
        } else {
            this.innerSetTime(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ExpressionProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ExpressionPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasExpressionId()) {
            this.setExpressionId(proto.getExpressionId());
            fieldCnt++;
        }
        if (proto.hasTime()) {
            this.setTime(proto.getTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Expression.Builder getCopyDbBuilder() {
        final Expression.Builder builder = Expression.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(Expression.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getExpressionId() != 0) {
            builder.setExpressionId(this.getExpressionId());
            fieldCnt++;
        }  else if (builder.hasExpressionId()) {
            // 清理ExpressionId
            builder.clearExpressionId();
            fieldCnt++;
        }
        if (this.getTime() != 0L) {
            builder.setTime(this.getTime());
            fieldCnt++;
        }  else if (builder.hasTime()) {
            // 清理Time
            builder.clearTime();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(Expression.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_EXPRESSIONID)) {
            builder.setExpressionId(this.getExpressionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TIME)) {
            builder.setTime(this.getTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(Expression proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasExpressionId()) {
            this.innerSetExpressionId(proto.getExpressionId());
        } else {
            this.innerSetExpressionId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTime()) {
            this.innerSetTime(proto.getTime());
        } else {
            this.innerSetTime(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ExpressionProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(Expression proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasExpressionId()) {
            this.setExpressionId(proto.getExpressionId());
            fieldCnt++;
        }
        if (proto.hasTime()) {
            this.setTime(proto.getTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Expression.Builder getCopySsBuilder() {
        final Expression.Builder builder = Expression.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(Expression.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getExpressionId() != 0) {
            builder.setExpressionId(this.getExpressionId());
            fieldCnt++;
        }  else if (builder.hasExpressionId()) {
            // 清理ExpressionId
            builder.clearExpressionId();
            fieldCnt++;
        }
        if (this.getTime() != 0L) {
            builder.setTime(this.getTime());
            fieldCnt++;
        }  else if (builder.hasTime()) {
            // 清理Time
            builder.clearTime();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(Expression.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_EXPRESSIONID)) {
            builder.setExpressionId(this.getExpressionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TIME)) {
            builder.setTime(this.getTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(Expression proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasExpressionId()) {
            this.innerSetExpressionId(proto.getExpressionId());
        } else {
            this.innerSetExpressionId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTime()) {
            this.innerSetTime(proto.getTime());
        } else {
            this.innerSetTime(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ExpressionProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(Expression proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasExpressionId()) {
            this.setExpressionId(proto.getExpressionId());
            fieldCnt++;
        }
        if (proto.hasTime()) {
            this.setTime(proto.getTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        Expression.Builder builder = Expression.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.expressionId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ExpressionProp)) {
            return false;
        }
        final ExpressionProp otherNode = (ExpressionProp) node;
        if (this.expressionId != otherNode.expressionId) {
            return false;
        }
        if (this.time != otherNode.time) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
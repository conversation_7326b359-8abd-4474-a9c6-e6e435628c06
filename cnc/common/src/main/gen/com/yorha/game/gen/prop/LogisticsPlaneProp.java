package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.LogisticsPlane.LogisticsPlaneEntity;
import com.yorha.proto.Struct;
import com.yorha.proto.LogisticsPlanePB.LogisticsPlaneEntityPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class LogisticsPlaneProp extends AbstractPropNode {

    public static final int FIELD_INDEX_OWNERID = 0;
    public static final int FIELD_INDEX_MOVE = 1;
    public static final int FIELD_INDEX_TEMPLATEID = 2;
    public static final int FIELD_INDEX_OWNERNAME = 3;
    public static final int FIELD_INDEX_CLANID = 4;
    public static final int FIELD_INDEX_BRIEFCLANNAME = 5;
    public static final int FIELD_INDEX_STATE = 6;
    public static final int FIELD_INDEX_ENTERSTATETS = 7;
    public static final int FIELD_INDEX_ZONEID = 8;

    public static final int FIELD_COUNT = 9;

    private long markBits0 = 0L;
    private PropertyChangeListener listener;

    private long ownerId = Constant.DEFAULT_LONG_VALUE;
    private MoveProp move = null;
    private int templateId = Constant.DEFAULT_INT_VALUE;
    private String ownerName = Constant.DEFAULT_STR_VALUE;
    private long clanId = Constant.DEFAULT_LONG_VALUE;
    private String briefClanName = Constant.DEFAULT_STR_VALUE;
    private LogisticsPlaneState state = LogisticsPlaneState.forNumber(0);
    private long enterStateTs = Constant.DEFAULT_LONG_VALUE;
    private int zoneId = Constant.DEFAULT_INT_VALUE;

    public LogisticsPlaneProp() {
        super(null, 0, FIELD_COUNT);
    }

    public LogisticsPlaneProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get ownerId
     *
     * @return ownerId value
     */
    public long getOwnerId() {
        return this.ownerId;
    }

    /**
     * set ownerId && set marked
     *
     * @param ownerId new value
     * @return current object
     */
    public LogisticsPlaneProp setOwnerId(long ownerId) {
        if (this.ownerId != ownerId) {
            this.mark(FIELD_INDEX_OWNERID);
            this.ownerId = ownerId;
        }
        return this;
    }

    /**
     * inner set ownerId
     *
     * @param ownerId new value
     */
    private void innerSetOwnerId(long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * get move
     *
     * @return move value
     */
    public MoveProp getMove() {
        if (this.move == null) {
            this.move = new MoveProp(this, FIELD_INDEX_MOVE);
        }
        return this.move;
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public LogisticsPlaneProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }

    /**
     * get ownerName
     *
     * @return ownerName value
     */
    public String getOwnerName() {
        return this.ownerName;
    }

    /**
     * set ownerName && set marked
     *
     * @param ownerName new value
     * @return current object
     */
    public LogisticsPlaneProp setOwnerName(String ownerName) {
        if (ownerName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.ownerName, ownerName)) {
            this.mark(FIELD_INDEX_OWNERNAME);
            this.ownerName = ownerName;
        }
        return this;
    }

    /**
     * inner set ownerName
     *
     * @param ownerName new value
     */
    private void innerSetOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    /**
     * get clanId
     *
     * @return clanId value
     */
    public long getClanId() {
        return this.clanId;
    }

    /**
     * set clanId && set marked
     *
     * @param clanId new value
     * @return current object
     */
    public LogisticsPlaneProp setClanId(long clanId) {
        if (this.clanId != clanId) {
            this.mark(FIELD_INDEX_CLANID);
            this.clanId = clanId;
        }
        return this;
    }

    /**
     * inner set clanId
     *
     * @param clanId new value
     */
    private void innerSetClanId(long clanId) {
        this.clanId = clanId;
    }

    /**
     * get briefClanName
     *
     * @return briefClanName value
     */
    public String getBriefClanName() {
        return this.briefClanName;
    }

    /**
     * set briefClanName && set marked
     *
     * @param briefClanName new value
     * @return current object
     */
    public LogisticsPlaneProp setBriefClanName(String briefClanName) {
        if (briefClanName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.briefClanName, briefClanName)) {
            this.mark(FIELD_INDEX_BRIEFCLANNAME);
            this.briefClanName = briefClanName;
        }
        return this;
    }

    /**
     * inner set briefClanName
     *
     * @param briefClanName new value
     */
    private void innerSetBriefClanName(String briefClanName) {
        this.briefClanName = briefClanName;
    }

    /**
     * get state
     *
     * @return state value
     */
    public LogisticsPlaneState getState() {
        return this.state;
    }

    /**
     * set state && set marked
     *
     * @param state new value
     * @return current object
     */
    public LogisticsPlaneProp setState(LogisticsPlaneState state) {
        if (state == null) {
            throw new NullPointerException();
        }
        if (this.state != state) {
            this.mark(FIELD_INDEX_STATE);
            this.state = state;
        }
        return this;
    }

    /**
     * inner set state
     *
     * @param state new value
     */
    private void innerSetState(LogisticsPlaneState state) {
        this.state = state;
    }

    /**
     * get enterStateTs
     *
     * @return enterStateTs value
     */
    public long getEnterStateTs() {
        return this.enterStateTs;
    }

    /**
     * set enterStateTs && set marked
     *
     * @param enterStateTs new value
     * @return current object
     */
    public LogisticsPlaneProp setEnterStateTs(long enterStateTs) {
        if (this.enterStateTs != enterStateTs) {
            this.mark(FIELD_INDEX_ENTERSTATETS);
            this.enterStateTs = enterStateTs;
        }
        return this;
    }

    /**
     * inner set enterStateTs
     *
     * @param enterStateTs new value
     */
    private void innerSetEnterStateTs(long enterStateTs) {
        this.enterStateTs = enterStateTs;
    }

    /**
     * get zoneId
     *
     * @return zoneId value
     */
    public int getZoneId() {
        return this.zoneId;
    }

    /**
     * set zoneId && set marked
     *
     * @param zoneId new value
     * @return current object
     */
    public LogisticsPlaneProp setZoneId(int zoneId) {
        if (this.zoneId != zoneId) {
            this.mark(FIELD_INDEX_ZONEID);
            this.zoneId = zoneId;
        }
        return this;
    }

    /**
     * inner set zoneId
     *
     * @param zoneId new value
     */
    private void innerSetZoneId(int zoneId) {
        this.zoneId = zoneId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public LogisticsPlaneEntityPB.Builder getCopyCsBuilder() {
        final LogisticsPlaneEntityPB.Builder builder = LogisticsPlaneEntityPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(LogisticsPlaneEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.move != null) {
            StructPB.MovePB.Builder tmpBuilder = StructPB.MovePB.newBuilder();
            final int tmpFieldCnt = this.move.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMove(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMove();
            }
        }  else if (builder.hasMove()) {
            // 清理Move
            builder.clearMove();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (!this.getOwnerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setOwnerName(this.getOwnerName());
            fieldCnt++;
        }  else if (builder.hasOwnerName()) {
            // 清理OwnerName
            builder.clearOwnerName();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getBriefClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setBriefClanName(this.getBriefClanName());
            fieldCnt++;
        }  else if (builder.hasBriefClanName()) {
            // 清理BriefClanName
            builder.clearBriefClanName();
            fieldCnt++;
        }
        if (this.getState() != LogisticsPlaneState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.getEnterStateTs() != 0L) {
            builder.setEnterStateTs(this.getEnterStateTs());
            fieldCnt++;
        }  else if (builder.hasEnterStateTs()) {
            // 清理EnterStateTs
            builder.clearEnterStateTs();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(LogisticsPlaneEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            final boolean needClear = !builder.hasMove();
            final int tmpFieldCnt = this.move.copyChangeToCs(builder.getMoveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMove();
            }
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERNAME)) {
            builder.setOwnerName(this.getOwnerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BRIEFCLANNAME)) {
            builder.setBriefClanName(this.getBriefClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERSTATETS)) {
            builder.setEnterStateTs(this.getEnterStateTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(LogisticsPlaneEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            final boolean needClear = !builder.hasMove();
            final int tmpFieldCnt = this.move.copyChangeToAndClearDeleteKeysCs(builder.getMoveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMove();
            }
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERNAME)) {
            builder.setOwnerName(this.getOwnerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BRIEFCLANNAME)) {
            builder.setBriefClanName(this.getBriefClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERSTATETS)) {
            builder.setEnterStateTs(this.getEnterStateTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(LogisticsPlaneEntityPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMove()) {
            this.getMove().mergeFromCs(proto.getMove());
        } else {
            if (this.move != null) {
                this.move.mergeFromCs(proto.getMove());
            }
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOwnerName()) {
            this.innerSetOwnerName(proto.getOwnerName());
        } else {
            this.innerSetOwnerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBriefClanName()) {
            this.innerSetBriefClanName(proto.getBriefClanName());
        } else {
            this.innerSetBriefClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(LogisticsPlaneState.forNumber(0));
        }
        if (proto.hasEnterStateTs()) {
            this.innerSetEnterStateTs(proto.getEnterStateTs());
        } else {
            this.innerSetEnterStateTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return LogisticsPlaneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(LogisticsPlaneEntityPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasMove()) {
            this.getMove().mergeChangeFromCs(proto.getMove());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasOwnerName()) {
            this.setOwnerName(proto.getOwnerName());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasBriefClanName()) {
            this.setBriefClanName(proto.getBriefClanName());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasEnterStateTs()) {
            this.setEnterStateTs(proto.getEnterStateTs());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public LogisticsPlaneEntity.Builder getCopyDbBuilder() {
        final LogisticsPlaneEntity.Builder builder = LogisticsPlaneEntity.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(LogisticsPlaneEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.move != null) {
            Struct.Move.Builder tmpBuilder = Struct.Move.newBuilder();
            final int tmpFieldCnt = this.move.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMove(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMove();
            }
        }  else if (builder.hasMove()) {
            // 清理Move
            builder.clearMove();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (!this.getOwnerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setOwnerName(this.getOwnerName());
            fieldCnt++;
        }  else if (builder.hasOwnerName()) {
            // 清理OwnerName
            builder.clearOwnerName();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getBriefClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setBriefClanName(this.getBriefClanName());
            fieldCnt++;
        }  else if (builder.hasBriefClanName()) {
            // 清理BriefClanName
            builder.clearBriefClanName();
            fieldCnt++;
        }
        if (this.getState() != LogisticsPlaneState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.getEnterStateTs() != 0L) {
            builder.setEnterStateTs(this.getEnterStateTs());
            fieldCnt++;
        }  else if (builder.hasEnterStateTs()) {
            // 清理EnterStateTs
            builder.clearEnterStateTs();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(LogisticsPlaneEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            final boolean needClear = !builder.hasMove();
            final int tmpFieldCnt = this.move.copyChangeToDb(builder.getMoveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMove();
            }
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERNAME)) {
            builder.setOwnerName(this.getOwnerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BRIEFCLANNAME)) {
            builder.setBriefClanName(this.getBriefClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERSTATETS)) {
            builder.setEnterStateTs(this.getEnterStateTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(LogisticsPlaneEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMove()) {
            this.getMove().mergeFromDb(proto.getMove());
        } else {
            if (this.move != null) {
                this.move.mergeFromDb(proto.getMove());
            }
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOwnerName()) {
            this.innerSetOwnerName(proto.getOwnerName());
        } else {
            this.innerSetOwnerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBriefClanName()) {
            this.innerSetBriefClanName(proto.getBriefClanName());
        } else {
            this.innerSetBriefClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(LogisticsPlaneState.forNumber(0));
        }
        if (proto.hasEnterStateTs()) {
            this.innerSetEnterStateTs(proto.getEnterStateTs());
        } else {
            this.innerSetEnterStateTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return LogisticsPlaneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(LogisticsPlaneEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasMove()) {
            this.getMove().mergeChangeFromDb(proto.getMove());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasOwnerName()) {
            this.setOwnerName(proto.getOwnerName());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasBriefClanName()) {
            this.setBriefClanName(proto.getBriefClanName());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasEnterStateTs()) {
            this.setEnterStateTs(proto.getEnterStateTs());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public LogisticsPlaneEntity.Builder getCopySsBuilder() {
        final LogisticsPlaneEntity.Builder builder = LogisticsPlaneEntity.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(LogisticsPlaneEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.move != null) {
            Struct.Move.Builder tmpBuilder = Struct.Move.newBuilder();
            final int tmpFieldCnt = this.move.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMove(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMove();
            }
        }  else if (builder.hasMove()) {
            // 清理Move
            builder.clearMove();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (!this.getOwnerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setOwnerName(this.getOwnerName());
            fieldCnt++;
        }  else if (builder.hasOwnerName()) {
            // 清理OwnerName
            builder.clearOwnerName();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getBriefClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setBriefClanName(this.getBriefClanName());
            fieldCnt++;
        }  else if (builder.hasBriefClanName()) {
            // 清理BriefClanName
            builder.clearBriefClanName();
            fieldCnt++;
        }
        if (this.getState() != LogisticsPlaneState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.getEnterStateTs() != 0L) {
            builder.setEnterStateTs(this.getEnterStateTs());
            fieldCnt++;
        }  else if (builder.hasEnterStateTs()) {
            // 清理EnterStateTs
            builder.clearEnterStateTs();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(LogisticsPlaneEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            final boolean needClear = !builder.hasMove();
            final int tmpFieldCnt = this.move.copyChangeToSs(builder.getMoveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMove();
            }
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERNAME)) {
            builder.setOwnerName(this.getOwnerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BRIEFCLANNAME)) {
            builder.setBriefClanName(this.getBriefClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERSTATETS)) {
            builder.setEnterStateTs(this.getEnterStateTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(LogisticsPlaneEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMove()) {
            this.getMove().mergeFromSs(proto.getMove());
        } else {
            if (this.move != null) {
                this.move.mergeFromSs(proto.getMove());
            }
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOwnerName()) {
            this.innerSetOwnerName(proto.getOwnerName());
        } else {
            this.innerSetOwnerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBriefClanName()) {
            this.innerSetBriefClanName(proto.getBriefClanName());
        } else {
            this.innerSetBriefClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(LogisticsPlaneState.forNumber(0));
        }
        if (proto.hasEnterStateTs()) {
            this.innerSetEnterStateTs(proto.getEnterStateTs());
        } else {
            this.innerSetEnterStateTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return LogisticsPlaneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(LogisticsPlaneEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasMove()) {
            this.getMove().mergeChangeFromSs(proto.getMove());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasOwnerName()) {
            this.setOwnerName(proto.getOwnerName());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasBriefClanName()) {
            this.setBriefClanName(proto.getBriefClanName());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasEnterStateTs()) {
            this.setEnterStateTs(proto.getEnterStateTs());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        LogisticsPlaneEntity.Builder builder = LogisticsPlaneEntity.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            this.move.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.move != null) {
            this.move.markAll();
        }
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("LogisticsPlaneProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("LogisticsPlaneProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof LogisticsPlaneProp)) {
            return false;
        }
        final LogisticsPlaneProp otherNode = (LogisticsPlaneProp) node;
        if (this.ownerId != otherNode.ownerId) {
            return false;
        }
        if (!this.getMove().compareDataTo(otherNode.getMove())) {
            return false;
        }
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.ownerName, otherNode.ownerName)) {
            return false;
        }
        if (this.clanId != otherNode.clanId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.briefClanName, otherNode.briefClanName)) {
            return false;
        }
        if (this.state != otherNode.state) {
            return false;
        }
        if (this.enterStateTs != otherNode.enterStateTs) {
            return false;
        }
        if (this.zoneId != otherNode.zoneId) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    public static LogisticsPlaneProp of(LogisticsPlaneEntity fullAttrDb, LogisticsPlaneEntity changeAttrDb) {
        LogisticsPlaneProp prop = new LogisticsPlaneProp();
        prop.mergeFromDb(fullAttrDb);
        prop.mergeChangeFromDb(changeAttrDb);
        prop.unMarkAll();
        return prop;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 55;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
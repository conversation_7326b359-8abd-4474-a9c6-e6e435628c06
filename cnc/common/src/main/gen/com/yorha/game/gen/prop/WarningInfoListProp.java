package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructPlayerPB.WarningInfoListPB;
import com.yorha.proto.StructPlayer.WarningInfoList;
import com.yorha.proto.StructPlayerPB.WarningInfoPB;
import com.yorha.proto.StructPlayer.WarningInfo;

/**
 * <AUTHOR> auto gen
 */
public class WarningInfoListProp extends AbstractListNode<WarningInfoProp> {
    /**
     * Create a WarningInfoListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public WarningInfoListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to WarningInfoListProp
     *
     * @return new object
     */
    @Override
    public WarningInfoProp addEmptyValue() {
        final WarningInfoProp newProp = new WarningInfoProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public WarningInfoListPB.Builder getCopyCsBuilder() {
        final WarningInfoListPB.Builder builder = WarningInfoListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(WarningInfoListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return WarningInfoListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final WarningInfoProp v : this) {
            final WarningInfoPB.Builder itemBuilder = WarningInfoPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return WarningInfoListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(WarningInfoListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return WarningInfoListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(WarningInfoListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (WarningInfoPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return WarningInfoListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(WarningInfoListPB proto) {
        return mergeFromCs(proto);
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public WarningInfoList.Builder getCopySsBuilder() {
        final WarningInfoList.Builder builder = WarningInfoList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(WarningInfoList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return WarningInfoListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final WarningInfoProp v : this) {
            final WarningInfo.Builder itemBuilder = WarningInfo.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return WarningInfoListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(WarningInfoList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return WarningInfoListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(WarningInfoList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (WarningInfo v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return WarningInfoListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(WarningInfoList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        WarningInfoList.Builder builder = WarningInfoList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ScenePlayerHospitalModel;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ScenePlayerHospitalModelPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ScenePlayerHospitalModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_WAITINGSOLDIERS = 0;
    public static final int FIELD_INDEX_INTREATMENTSOLDIERS = 1;
    public static final int FIELD_INDEX_TREATOVERSOLDIERS = 2;
    public static final int FIELD_INDEX_EXCLUSIVEREGION = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private Int32PlayerHospitalSoldierMapProp waitingSoldiers = null;
    private Int32PlayerHospitalSoldierMapProp inTreatmentSoldiers = null;
    private Int32PlayerHospitalSoldierMapProp treatOverSoldiers = null;
    private ScenePlayerHospitalExclusiveRegionProp exclusiveRegion = null;

    public ScenePlayerHospitalModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ScenePlayerHospitalModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get waitingSoldiers
     *
     * @return waitingSoldiers value
     */
    public Int32PlayerHospitalSoldierMapProp getWaitingSoldiers() {
        if (this.waitingSoldiers == null) {
            this.waitingSoldiers = new Int32PlayerHospitalSoldierMapProp(this, FIELD_INDEX_WAITINGSOLDIERS);
        }
        return this.waitingSoldiers;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putWaitingSoldiersV(PlayerHospitalSoldierProp v) {
        this.getWaitingSoldiers().put(v.getSoldierId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerHospitalSoldierProp addEmptyWaitingSoldiers(Integer k) {
        return this.getWaitingSoldiers().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getWaitingSoldiersSize() {
        if (this.waitingSoldiers == null) {
            return 0;
        }
        return this.waitingSoldiers.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isWaitingSoldiersEmpty() {
        if (this.waitingSoldiers == null) {
            return true;
        }
        return this.waitingSoldiers.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerHospitalSoldierProp getWaitingSoldiersV(Integer k) {
        if (this.waitingSoldiers == null || !this.waitingSoldiers.containsKey(k)) {
            return null;
        }
        return this.waitingSoldiers.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearWaitingSoldiers() {
        if (this.waitingSoldiers != null) {
            this.waitingSoldiers.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeWaitingSoldiersV(Integer k) {
        if (this.waitingSoldiers != null) {
            this.waitingSoldiers.remove(k);
        }
    }
    /**
     * get inTreatmentSoldiers
     *
     * @return inTreatmentSoldiers value
     */
    public Int32PlayerHospitalSoldierMapProp getInTreatmentSoldiers() {
        if (this.inTreatmentSoldiers == null) {
            this.inTreatmentSoldiers = new Int32PlayerHospitalSoldierMapProp(this, FIELD_INDEX_INTREATMENTSOLDIERS);
        }
        return this.inTreatmentSoldiers;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putInTreatmentSoldiersV(PlayerHospitalSoldierProp v) {
        this.getInTreatmentSoldiers().put(v.getSoldierId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerHospitalSoldierProp addEmptyInTreatmentSoldiers(Integer k) {
        return this.getInTreatmentSoldiers().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getInTreatmentSoldiersSize() {
        if (this.inTreatmentSoldiers == null) {
            return 0;
        }
        return this.inTreatmentSoldiers.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isInTreatmentSoldiersEmpty() {
        if (this.inTreatmentSoldiers == null) {
            return true;
        }
        return this.inTreatmentSoldiers.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerHospitalSoldierProp getInTreatmentSoldiersV(Integer k) {
        if (this.inTreatmentSoldiers == null || !this.inTreatmentSoldiers.containsKey(k)) {
            return null;
        }
        return this.inTreatmentSoldiers.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearInTreatmentSoldiers() {
        if (this.inTreatmentSoldiers != null) {
            this.inTreatmentSoldiers.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeInTreatmentSoldiersV(Integer k) {
        if (this.inTreatmentSoldiers != null) {
            this.inTreatmentSoldiers.remove(k);
        }
    }
    /**
     * get treatOverSoldiers
     *
     * @return treatOverSoldiers value
     */
    public Int32PlayerHospitalSoldierMapProp getTreatOverSoldiers() {
        if (this.treatOverSoldiers == null) {
            this.treatOverSoldiers = new Int32PlayerHospitalSoldierMapProp(this, FIELD_INDEX_TREATOVERSOLDIERS);
        }
        return this.treatOverSoldiers;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTreatOverSoldiersV(PlayerHospitalSoldierProp v) {
        this.getTreatOverSoldiers().put(v.getSoldierId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerHospitalSoldierProp addEmptyTreatOverSoldiers(Integer k) {
        return this.getTreatOverSoldiers().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTreatOverSoldiersSize() {
        if (this.treatOverSoldiers == null) {
            return 0;
        }
        return this.treatOverSoldiers.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTreatOverSoldiersEmpty() {
        if (this.treatOverSoldiers == null) {
            return true;
        }
        return this.treatOverSoldiers.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerHospitalSoldierProp getTreatOverSoldiersV(Integer k) {
        if (this.treatOverSoldiers == null || !this.treatOverSoldiers.containsKey(k)) {
            return null;
        }
        return this.treatOverSoldiers.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTreatOverSoldiers() {
        if (this.treatOverSoldiers != null) {
            this.treatOverSoldiers.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTreatOverSoldiersV(Integer k) {
        if (this.treatOverSoldiers != null) {
            this.treatOverSoldiers.remove(k);
        }
    }
    /**
     * get exclusiveRegion
     *
     * @return exclusiveRegion value
     */
    public ScenePlayerHospitalExclusiveRegionProp getExclusiveRegion() {
        if (this.exclusiveRegion == null) {
            this.exclusiveRegion = new ScenePlayerHospitalExclusiveRegionProp(this, FIELD_INDEX_EXCLUSIVEREGION);
        }
        return this.exclusiveRegion;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerHospitalModelPB.Builder getCopyCsBuilder() {
        final ScenePlayerHospitalModelPB.Builder builder = ScenePlayerHospitalModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ScenePlayerHospitalModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.waitingSoldiers != null) {
            StructPB.Int32PlayerHospitalSoldierMapPB.Builder tmpBuilder = StructPB.Int32PlayerHospitalSoldierMapPB.newBuilder();
            final int tmpFieldCnt = this.waitingSoldiers.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWaitingSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWaitingSoldiers();
            }
        }  else if (builder.hasWaitingSoldiers()) {
            // 清理WaitingSoldiers
            builder.clearWaitingSoldiers();
            fieldCnt++;
        }
        if (this.inTreatmentSoldiers != null) {
            StructPB.Int32PlayerHospitalSoldierMapPB.Builder tmpBuilder = StructPB.Int32PlayerHospitalSoldierMapPB.newBuilder();
            final int tmpFieldCnt = this.inTreatmentSoldiers.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInTreatmentSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInTreatmentSoldiers();
            }
        }  else if (builder.hasInTreatmentSoldiers()) {
            // 清理InTreatmentSoldiers
            builder.clearInTreatmentSoldiers();
            fieldCnt++;
        }
        if (this.treatOverSoldiers != null) {
            StructPB.Int32PlayerHospitalSoldierMapPB.Builder tmpBuilder = StructPB.Int32PlayerHospitalSoldierMapPB.newBuilder();
            final int tmpFieldCnt = this.treatOverSoldiers.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTreatOverSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTreatOverSoldiers();
            }
        }  else if (builder.hasTreatOverSoldiers()) {
            // 清理TreatOverSoldiers
            builder.clearTreatOverSoldiers();
            fieldCnt++;
        }
        if (this.exclusiveRegion != null) {
            StructPB.ScenePlayerHospitalExclusiveRegionPB.Builder tmpBuilder = StructPB.ScenePlayerHospitalExclusiveRegionPB.newBuilder();
            final int tmpFieldCnt = this.exclusiveRegion.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExclusiveRegion(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExclusiveRegion();
            }
        }  else if (builder.hasExclusiveRegion()) {
            // 清理ExclusiveRegion
            builder.clearExclusiveRegion();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ScenePlayerHospitalModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_WAITINGSOLDIERS) && this.waitingSoldiers != null) {
            final boolean needClear = !builder.hasWaitingSoldiers();
            final int tmpFieldCnt = this.waitingSoldiers.copyChangeToCs(builder.getWaitingSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWaitingSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_INTREATMENTSOLDIERS) && this.inTreatmentSoldiers != null) {
            final boolean needClear = !builder.hasInTreatmentSoldiers();
            final int tmpFieldCnt = this.inTreatmentSoldiers.copyChangeToCs(builder.getInTreatmentSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInTreatmentSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_TREATOVERSOLDIERS) && this.treatOverSoldiers != null) {
            final boolean needClear = !builder.hasTreatOverSoldiers();
            final int tmpFieldCnt = this.treatOverSoldiers.copyChangeToCs(builder.getTreatOverSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTreatOverSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXCLUSIVEREGION) && this.exclusiveRegion != null) {
            final boolean needClear = !builder.hasExclusiveRegion();
            final int tmpFieldCnt = this.exclusiveRegion.copyChangeToCs(builder.getExclusiveRegionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExclusiveRegion();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ScenePlayerHospitalModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_WAITINGSOLDIERS) && this.waitingSoldiers != null) {
            final boolean needClear = !builder.hasWaitingSoldiers();
            final int tmpFieldCnt = this.waitingSoldiers.copyChangeToAndClearDeleteKeysCs(builder.getWaitingSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWaitingSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_INTREATMENTSOLDIERS) && this.inTreatmentSoldiers != null) {
            final boolean needClear = !builder.hasInTreatmentSoldiers();
            final int tmpFieldCnt = this.inTreatmentSoldiers.copyChangeToAndClearDeleteKeysCs(builder.getInTreatmentSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInTreatmentSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_TREATOVERSOLDIERS) && this.treatOverSoldiers != null) {
            final boolean needClear = !builder.hasTreatOverSoldiers();
            final int tmpFieldCnt = this.treatOverSoldiers.copyChangeToAndClearDeleteKeysCs(builder.getTreatOverSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTreatOverSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXCLUSIVEREGION) && this.exclusiveRegion != null) {
            final boolean needClear = !builder.hasExclusiveRegion();
            final int tmpFieldCnt = this.exclusiveRegion.copyChangeToAndClearDeleteKeysCs(builder.getExclusiveRegionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExclusiveRegion();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ScenePlayerHospitalModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasWaitingSoldiers()) {
            this.getWaitingSoldiers().mergeFromCs(proto.getWaitingSoldiers());
        } else {
            if (this.waitingSoldiers != null) {
                this.waitingSoldiers.mergeFromCs(proto.getWaitingSoldiers());
            }
        }
        if (proto.hasInTreatmentSoldiers()) {
            this.getInTreatmentSoldiers().mergeFromCs(proto.getInTreatmentSoldiers());
        } else {
            if (this.inTreatmentSoldiers != null) {
                this.inTreatmentSoldiers.mergeFromCs(proto.getInTreatmentSoldiers());
            }
        }
        if (proto.hasTreatOverSoldiers()) {
            this.getTreatOverSoldiers().mergeFromCs(proto.getTreatOverSoldiers());
        } else {
            if (this.treatOverSoldiers != null) {
                this.treatOverSoldiers.mergeFromCs(proto.getTreatOverSoldiers());
            }
        }
        if (proto.hasExclusiveRegion()) {
            this.getExclusiveRegion().mergeFromCs(proto.getExclusiveRegion());
        } else {
            if (this.exclusiveRegion != null) {
                this.exclusiveRegion.mergeFromCs(proto.getExclusiveRegion());
            }
        }
        this.markAll();
        return ScenePlayerHospitalModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ScenePlayerHospitalModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasWaitingSoldiers()) {
            this.getWaitingSoldiers().mergeChangeFromCs(proto.getWaitingSoldiers());
            fieldCnt++;
        }
        if (proto.hasInTreatmentSoldiers()) {
            this.getInTreatmentSoldiers().mergeChangeFromCs(proto.getInTreatmentSoldiers());
            fieldCnt++;
        }
        if (proto.hasTreatOverSoldiers()) {
            this.getTreatOverSoldiers().mergeChangeFromCs(proto.getTreatOverSoldiers());
            fieldCnt++;
        }
        if (proto.hasExclusiveRegion()) {
            this.getExclusiveRegion().mergeChangeFromCs(proto.getExclusiveRegion());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerHospitalModel.Builder getCopyDbBuilder() {
        final ScenePlayerHospitalModel.Builder builder = ScenePlayerHospitalModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ScenePlayerHospitalModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.waitingSoldiers != null) {
            Struct.Int32PlayerHospitalSoldierMap.Builder tmpBuilder = Struct.Int32PlayerHospitalSoldierMap.newBuilder();
            final int tmpFieldCnt = this.waitingSoldiers.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWaitingSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWaitingSoldiers();
            }
        }  else if (builder.hasWaitingSoldiers()) {
            // 清理WaitingSoldiers
            builder.clearWaitingSoldiers();
            fieldCnt++;
        }
        if (this.inTreatmentSoldiers != null) {
            Struct.Int32PlayerHospitalSoldierMap.Builder tmpBuilder = Struct.Int32PlayerHospitalSoldierMap.newBuilder();
            final int tmpFieldCnt = this.inTreatmentSoldiers.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInTreatmentSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInTreatmentSoldiers();
            }
        }  else if (builder.hasInTreatmentSoldiers()) {
            // 清理InTreatmentSoldiers
            builder.clearInTreatmentSoldiers();
            fieldCnt++;
        }
        if (this.treatOverSoldiers != null) {
            Struct.Int32PlayerHospitalSoldierMap.Builder tmpBuilder = Struct.Int32PlayerHospitalSoldierMap.newBuilder();
            final int tmpFieldCnt = this.treatOverSoldiers.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTreatOverSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTreatOverSoldiers();
            }
        }  else if (builder.hasTreatOverSoldiers()) {
            // 清理TreatOverSoldiers
            builder.clearTreatOverSoldiers();
            fieldCnt++;
        }
        if (this.exclusiveRegion != null) {
            Struct.ScenePlayerHospitalExclusiveRegion.Builder tmpBuilder = Struct.ScenePlayerHospitalExclusiveRegion.newBuilder();
            final int tmpFieldCnt = this.exclusiveRegion.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExclusiveRegion(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExclusiveRegion();
            }
        }  else if (builder.hasExclusiveRegion()) {
            // 清理ExclusiveRegion
            builder.clearExclusiveRegion();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ScenePlayerHospitalModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_WAITINGSOLDIERS) && this.waitingSoldiers != null) {
            final boolean needClear = !builder.hasWaitingSoldiers();
            final int tmpFieldCnt = this.waitingSoldiers.copyChangeToDb(builder.getWaitingSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWaitingSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_INTREATMENTSOLDIERS) && this.inTreatmentSoldiers != null) {
            final boolean needClear = !builder.hasInTreatmentSoldiers();
            final int tmpFieldCnt = this.inTreatmentSoldiers.copyChangeToDb(builder.getInTreatmentSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInTreatmentSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_TREATOVERSOLDIERS) && this.treatOverSoldiers != null) {
            final boolean needClear = !builder.hasTreatOverSoldiers();
            final int tmpFieldCnt = this.treatOverSoldiers.copyChangeToDb(builder.getTreatOverSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTreatOverSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXCLUSIVEREGION) && this.exclusiveRegion != null) {
            final boolean needClear = !builder.hasExclusiveRegion();
            final int tmpFieldCnt = this.exclusiveRegion.copyChangeToDb(builder.getExclusiveRegionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExclusiveRegion();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ScenePlayerHospitalModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasWaitingSoldiers()) {
            this.getWaitingSoldiers().mergeFromDb(proto.getWaitingSoldiers());
        } else {
            if (this.waitingSoldiers != null) {
                this.waitingSoldiers.mergeFromDb(proto.getWaitingSoldiers());
            }
        }
        if (proto.hasInTreatmentSoldiers()) {
            this.getInTreatmentSoldiers().mergeFromDb(proto.getInTreatmentSoldiers());
        } else {
            if (this.inTreatmentSoldiers != null) {
                this.inTreatmentSoldiers.mergeFromDb(proto.getInTreatmentSoldiers());
            }
        }
        if (proto.hasTreatOverSoldiers()) {
            this.getTreatOverSoldiers().mergeFromDb(proto.getTreatOverSoldiers());
        } else {
            if (this.treatOverSoldiers != null) {
                this.treatOverSoldiers.mergeFromDb(proto.getTreatOverSoldiers());
            }
        }
        if (proto.hasExclusiveRegion()) {
            this.getExclusiveRegion().mergeFromDb(proto.getExclusiveRegion());
        } else {
            if (this.exclusiveRegion != null) {
                this.exclusiveRegion.mergeFromDb(proto.getExclusiveRegion());
            }
        }
        this.markAll();
        return ScenePlayerHospitalModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ScenePlayerHospitalModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasWaitingSoldiers()) {
            this.getWaitingSoldiers().mergeChangeFromDb(proto.getWaitingSoldiers());
            fieldCnt++;
        }
        if (proto.hasInTreatmentSoldiers()) {
            this.getInTreatmentSoldiers().mergeChangeFromDb(proto.getInTreatmentSoldiers());
            fieldCnt++;
        }
        if (proto.hasTreatOverSoldiers()) {
            this.getTreatOverSoldiers().mergeChangeFromDb(proto.getTreatOverSoldiers());
            fieldCnt++;
        }
        if (proto.hasExclusiveRegion()) {
            this.getExclusiveRegion().mergeChangeFromDb(proto.getExclusiveRegion());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerHospitalModel.Builder getCopySsBuilder() {
        final ScenePlayerHospitalModel.Builder builder = ScenePlayerHospitalModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ScenePlayerHospitalModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.waitingSoldiers != null) {
            Struct.Int32PlayerHospitalSoldierMap.Builder tmpBuilder = Struct.Int32PlayerHospitalSoldierMap.newBuilder();
            final int tmpFieldCnt = this.waitingSoldiers.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWaitingSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWaitingSoldiers();
            }
        }  else if (builder.hasWaitingSoldiers()) {
            // 清理WaitingSoldiers
            builder.clearWaitingSoldiers();
            fieldCnt++;
        }
        if (this.inTreatmentSoldiers != null) {
            Struct.Int32PlayerHospitalSoldierMap.Builder tmpBuilder = Struct.Int32PlayerHospitalSoldierMap.newBuilder();
            final int tmpFieldCnt = this.inTreatmentSoldiers.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInTreatmentSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInTreatmentSoldiers();
            }
        }  else if (builder.hasInTreatmentSoldiers()) {
            // 清理InTreatmentSoldiers
            builder.clearInTreatmentSoldiers();
            fieldCnt++;
        }
        if (this.treatOverSoldiers != null) {
            Struct.Int32PlayerHospitalSoldierMap.Builder tmpBuilder = Struct.Int32PlayerHospitalSoldierMap.newBuilder();
            final int tmpFieldCnt = this.treatOverSoldiers.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTreatOverSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTreatOverSoldiers();
            }
        }  else if (builder.hasTreatOverSoldiers()) {
            // 清理TreatOverSoldiers
            builder.clearTreatOverSoldiers();
            fieldCnt++;
        }
        if (this.exclusiveRegion != null) {
            Struct.ScenePlayerHospitalExclusiveRegion.Builder tmpBuilder = Struct.ScenePlayerHospitalExclusiveRegion.newBuilder();
            final int tmpFieldCnt = this.exclusiveRegion.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExclusiveRegion(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExclusiveRegion();
            }
        }  else if (builder.hasExclusiveRegion()) {
            // 清理ExclusiveRegion
            builder.clearExclusiveRegion();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ScenePlayerHospitalModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_WAITINGSOLDIERS) && this.waitingSoldiers != null) {
            final boolean needClear = !builder.hasWaitingSoldiers();
            final int tmpFieldCnt = this.waitingSoldiers.copyChangeToSs(builder.getWaitingSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWaitingSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_INTREATMENTSOLDIERS) && this.inTreatmentSoldiers != null) {
            final boolean needClear = !builder.hasInTreatmentSoldiers();
            final int tmpFieldCnt = this.inTreatmentSoldiers.copyChangeToSs(builder.getInTreatmentSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInTreatmentSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_TREATOVERSOLDIERS) && this.treatOverSoldiers != null) {
            final boolean needClear = !builder.hasTreatOverSoldiers();
            final int tmpFieldCnt = this.treatOverSoldiers.copyChangeToSs(builder.getTreatOverSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTreatOverSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXCLUSIVEREGION) && this.exclusiveRegion != null) {
            final boolean needClear = !builder.hasExclusiveRegion();
            final int tmpFieldCnt = this.exclusiveRegion.copyChangeToSs(builder.getExclusiveRegionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExclusiveRegion();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ScenePlayerHospitalModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasWaitingSoldiers()) {
            this.getWaitingSoldiers().mergeFromSs(proto.getWaitingSoldiers());
        } else {
            if (this.waitingSoldiers != null) {
                this.waitingSoldiers.mergeFromSs(proto.getWaitingSoldiers());
            }
        }
        if (proto.hasInTreatmentSoldiers()) {
            this.getInTreatmentSoldiers().mergeFromSs(proto.getInTreatmentSoldiers());
        } else {
            if (this.inTreatmentSoldiers != null) {
                this.inTreatmentSoldiers.mergeFromSs(proto.getInTreatmentSoldiers());
            }
        }
        if (proto.hasTreatOverSoldiers()) {
            this.getTreatOverSoldiers().mergeFromSs(proto.getTreatOverSoldiers());
        } else {
            if (this.treatOverSoldiers != null) {
                this.treatOverSoldiers.mergeFromSs(proto.getTreatOverSoldiers());
            }
        }
        if (proto.hasExclusiveRegion()) {
            this.getExclusiveRegion().mergeFromSs(proto.getExclusiveRegion());
        } else {
            if (this.exclusiveRegion != null) {
                this.exclusiveRegion.mergeFromSs(proto.getExclusiveRegion());
            }
        }
        this.markAll();
        return ScenePlayerHospitalModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ScenePlayerHospitalModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasWaitingSoldiers()) {
            this.getWaitingSoldiers().mergeChangeFromSs(proto.getWaitingSoldiers());
            fieldCnt++;
        }
        if (proto.hasInTreatmentSoldiers()) {
            this.getInTreatmentSoldiers().mergeChangeFromSs(proto.getInTreatmentSoldiers());
            fieldCnt++;
        }
        if (proto.hasTreatOverSoldiers()) {
            this.getTreatOverSoldiers().mergeChangeFromSs(proto.getTreatOverSoldiers());
            fieldCnt++;
        }
        if (proto.hasExclusiveRegion()) {
            this.getExclusiveRegion().mergeChangeFromSs(proto.getExclusiveRegion());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ScenePlayerHospitalModel.Builder builder = ScenePlayerHospitalModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_WAITINGSOLDIERS) && this.waitingSoldiers != null) {
            this.waitingSoldiers.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_INTREATMENTSOLDIERS) && this.inTreatmentSoldiers != null) {
            this.inTreatmentSoldiers.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TREATOVERSOLDIERS) && this.treatOverSoldiers != null) {
            this.treatOverSoldiers.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_EXCLUSIVEREGION) && this.exclusiveRegion != null) {
            this.exclusiveRegion.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.waitingSoldiers != null) {
            this.waitingSoldiers.markAll();
        }
        if (this.inTreatmentSoldiers != null) {
            this.inTreatmentSoldiers.markAll();
        }
        if (this.treatOverSoldiers != null) {
            this.treatOverSoldiers.markAll();
        }
        if (this.exclusiveRegion != null) {
            this.exclusiveRegion.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ScenePlayerHospitalModelProp)) {
            return false;
        }
        final ScenePlayerHospitalModelProp otherNode = (ScenePlayerHospitalModelProp) node;
        if (!this.getWaitingSoldiers().compareDataTo(otherNode.getWaitingSoldiers())) {
            return false;
        }
        if (!this.getInTreatmentSoldiers().compareDataTo(otherNode.getInTreatmentSoldiers())) {
            return false;
        }
        if (!this.getTreatOverSoldiers().compareDataTo(otherNode.getTreatOverSoldiers())) {
            return false;
        }
        if (!this.getExclusiveRegion().compareDataTo(otherNode.getExclusiveRegion())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
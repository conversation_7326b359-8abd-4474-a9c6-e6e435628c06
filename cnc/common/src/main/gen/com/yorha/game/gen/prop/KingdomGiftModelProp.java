package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zone.KingdomGiftModel;
import com.yorha.proto.Zone;
import com.yorha.proto.ZonePB.KingdomGiftModelPB;
import com.yorha.proto.ZonePB;


/**
 * <AUTHOR> auto gen
 */
public class KingdomGiftModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_GIFTINFO = 0;
    public static final int FIELD_INDEX_REFRESHGIFTTSMS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private Int32KingdomGiftInfoMapProp giftInfo = null;
    private long refreshGiftTsMs = Constant.DEFAULT_LONG_VALUE;

    public KingdomGiftModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public KingdomGiftModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get giftInfo
     *
     * @return giftInfo value
     */
    public Int32KingdomGiftInfoMapProp getGiftInfo() {
        if (this.giftInfo == null) {
            this.giftInfo = new Int32KingdomGiftInfoMapProp(this, FIELD_INDEX_GIFTINFO);
        }
        return this.giftInfo;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putGiftInfoV(KingdomGiftInfoProp v) {
        this.getGiftInfo().put(v.getGiftId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public KingdomGiftInfoProp addEmptyGiftInfo(Integer k) {
        return this.getGiftInfo().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getGiftInfoSize() {
        if (this.giftInfo == null) {
            return 0;
        }
        return this.giftInfo.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isGiftInfoEmpty() {
        if (this.giftInfo == null) {
            return true;
        }
        return this.giftInfo.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public KingdomGiftInfoProp getGiftInfoV(Integer k) {
        if (this.giftInfo == null || !this.giftInfo.containsKey(k)) {
            return null;
        }
        return this.giftInfo.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearGiftInfo() {
        if (this.giftInfo != null) {
            this.giftInfo.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeGiftInfoV(Integer k) {
        if (this.giftInfo != null) {
            this.giftInfo.remove(k);
        }
    }
    /**
     * get refreshGiftTsMs
     *
     * @return refreshGiftTsMs value
     */
    public long getRefreshGiftTsMs() {
        return this.refreshGiftTsMs;
    }

    /**
     * set refreshGiftTsMs && set marked
     *
     * @param refreshGiftTsMs new value
     * @return current object
     */
    public KingdomGiftModelProp setRefreshGiftTsMs(long refreshGiftTsMs) {
        if (this.refreshGiftTsMs != refreshGiftTsMs) {
            this.mark(FIELD_INDEX_REFRESHGIFTTSMS);
            this.refreshGiftTsMs = refreshGiftTsMs;
        }
        return this;
    }

    /**
     * inner set refreshGiftTsMs
     *
     * @param refreshGiftTsMs new value
     */
    private void innerSetRefreshGiftTsMs(long refreshGiftTsMs) {
        this.refreshGiftTsMs = refreshGiftTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomGiftModelPB.Builder getCopyCsBuilder() {
        final KingdomGiftModelPB.Builder builder = KingdomGiftModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(KingdomGiftModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.giftInfo != null) {
            ZonePB.Int32KingdomGiftInfoMapPB.Builder tmpBuilder = ZonePB.Int32KingdomGiftInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.giftInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGiftInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGiftInfo();
            }
        }  else if (builder.hasGiftInfo()) {
            // 清理GiftInfo
            builder.clearGiftInfo();
            fieldCnt++;
        }
        if (this.getRefreshGiftTsMs() != 0L) {
            builder.setRefreshGiftTsMs(this.getRefreshGiftTsMs());
            fieldCnt++;
        }  else if (builder.hasRefreshGiftTsMs()) {
            // 清理RefreshGiftTsMs
            builder.clearRefreshGiftTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(KingdomGiftModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GIFTINFO) && this.giftInfo != null) {
            final boolean needClear = !builder.hasGiftInfo();
            final int tmpFieldCnt = this.giftInfo.copyChangeToCs(builder.getGiftInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_REFRESHGIFTTSMS)) {
            builder.setRefreshGiftTsMs(this.getRefreshGiftTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(KingdomGiftModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GIFTINFO) && this.giftInfo != null) {
            final boolean needClear = !builder.hasGiftInfo();
            final int tmpFieldCnt = this.giftInfo.copyChangeToAndClearDeleteKeysCs(builder.getGiftInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_REFRESHGIFTTSMS)) {
            builder.setRefreshGiftTsMs(this.getRefreshGiftTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(KingdomGiftModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGiftInfo()) {
            this.getGiftInfo().mergeFromCs(proto.getGiftInfo());
        } else {
            if (this.giftInfo != null) {
                this.giftInfo.mergeFromCs(proto.getGiftInfo());
            }
        }
        if (proto.hasRefreshGiftTsMs()) {
            this.innerSetRefreshGiftTsMs(proto.getRefreshGiftTsMs());
        } else {
            this.innerSetRefreshGiftTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return KingdomGiftModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(KingdomGiftModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGiftInfo()) {
            this.getGiftInfo().mergeChangeFromCs(proto.getGiftInfo());
            fieldCnt++;
        }
        if (proto.hasRefreshGiftTsMs()) {
            this.setRefreshGiftTsMs(proto.getRefreshGiftTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomGiftModel.Builder getCopyDbBuilder() {
        final KingdomGiftModel.Builder builder = KingdomGiftModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(KingdomGiftModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.giftInfo != null) {
            Zone.Int32KingdomGiftInfoMap.Builder tmpBuilder = Zone.Int32KingdomGiftInfoMap.newBuilder();
            final int tmpFieldCnt = this.giftInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGiftInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGiftInfo();
            }
        }  else if (builder.hasGiftInfo()) {
            // 清理GiftInfo
            builder.clearGiftInfo();
            fieldCnt++;
        }
        if (this.getRefreshGiftTsMs() != 0L) {
            builder.setRefreshGiftTsMs(this.getRefreshGiftTsMs());
            fieldCnt++;
        }  else if (builder.hasRefreshGiftTsMs()) {
            // 清理RefreshGiftTsMs
            builder.clearRefreshGiftTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(KingdomGiftModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GIFTINFO) && this.giftInfo != null) {
            final boolean needClear = !builder.hasGiftInfo();
            final int tmpFieldCnt = this.giftInfo.copyChangeToDb(builder.getGiftInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_REFRESHGIFTTSMS)) {
            builder.setRefreshGiftTsMs(this.getRefreshGiftTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(KingdomGiftModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGiftInfo()) {
            this.getGiftInfo().mergeFromDb(proto.getGiftInfo());
        } else {
            if (this.giftInfo != null) {
                this.giftInfo.mergeFromDb(proto.getGiftInfo());
            }
        }
        if (proto.hasRefreshGiftTsMs()) {
            this.innerSetRefreshGiftTsMs(proto.getRefreshGiftTsMs());
        } else {
            this.innerSetRefreshGiftTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return KingdomGiftModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(KingdomGiftModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGiftInfo()) {
            this.getGiftInfo().mergeChangeFromDb(proto.getGiftInfo());
            fieldCnt++;
        }
        if (proto.hasRefreshGiftTsMs()) {
            this.setRefreshGiftTsMs(proto.getRefreshGiftTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomGiftModel.Builder getCopySsBuilder() {
        final KingdomGiftModel.Builder builder = KingdomGiftModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(KingdomGiftModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.giftInfo != null) {
            Zone.Int32KingdomGiftInfoMap.Builder tmpBuilder = Zone.Int32KingdomGiftInfoMap.newBuilder();
            final int tmpFieldCnt = this.giftInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGiftInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGiftInfo();
            }
        }  else if (builder.hasGiftInfo()) {
            // 清理GiftInfo
            builder.clearGiftInfo();
            fieldCnt++;
        }
        if (this.getRefreshGiftTsMs() != 0L) {
            builder.setRefreshGiftTsMs(this.getRefreshGiftTsMs());
            fieldCnt++;
        }  else if (builder.hasRefreshGiftTsMs()) {
            // 清理RefreshGiftTsMs
            builder.clearRefreshGiftTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(KingdomGiftModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GIFTINFO) && this.giftInfo != null) {
            final boolean needClear = !builder.hasGiftInfo();
            final int tmpFieldCnt = this.giftInfo.copyChangeToSs(builder.getGiftInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_REFRESHGIFTTSMS)) {
            builder.setRefreshGiftTsMs(this.getRefreshGiftTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(KingdomGiftModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGiftInfo()) {
            this.getGiftInfo().mergeFromSs(proto.getGiftInfo());
        } else {
            if (this.giftInfo != null) {
                this.giftInfo.mergeFromSs(proto.getGiftInfo());
            }
        }
        if (proto.hasRefreshGiftTsMs()) {
            this.innerSetRefreshGiftTsMs(proto.getRefreshGiftTsMs());
        } else {
            this.innerSetRefreshGiftTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return KingdomGiftModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(KingdomGiftModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGiftInfo()) {
            this.getGiftInfo().mergeChangeFromSs(proto.getGiftInfo());
            fieldCnt++;
        }
        if (proto.hasRefreshGiftTsMs()) {
            this.setRefreshGiftTsMs(proto.getRefreshGiftTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        KingdomGiftModel.Builder builder = KingdomGiftModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_GIFTINFO) && this.giftInfo != null) {
            this.giftInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.giftInfo != null) {
            this.giftInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof KingdomGiftModelProp)) {
            return false;
        }
        final KingdomGiftModelProp otherNode = (KingdomGiftModelProp) node;
        if (!this.getGiftInfo().compareDataTo(otherNode.getGiftInfo())) {
            return false;
        }
        if (this.refreshGiftTsMs != otherNode.refreshGiftTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
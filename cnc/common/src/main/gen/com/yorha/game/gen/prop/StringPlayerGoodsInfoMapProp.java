 
package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.AbstractMapNode;
import com.yorha.proto.PlayerPB.StringPlayerGoodsInfoMapPB;
import com.yorha.proto.PlayerPB.PlayerGoodsInfoPB;
import com.yorha.proto.Player.StringPlayerGoodsInfoMap;
import com.yorha.proto.Player.PlayerGoodsInfo;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> auto gen
 */
public class StringPlayerGoodsInfoMapProp extends AbstractMapNode<String, PlayerGoodsInfoProp> {
    /**
     * Creates a StringPlayerGoodsInfoMapProp container
     *
     * @param parent     parent node
     * @param fieldIndex field index in parent node
     */
    public StringPlayerGoodsInfoMapProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to StringPlayerGoodsInfoMapProp
     *
     * @param k map key
     * @return new object
     */
    @Override
    public PlayerGoodsInfoProp addEmptyValue(String k) {
        PlayerGoodsInfoProp newProp = new PlayerGoodsInfoProp(null, DEFAULT_FIELD_INDEX);
        newProp.setKey(k);
        this.put(k, newProp);
        return newProp;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public StringPlayerGoodsInfoMapPB.Builder getCopyCsBuilder() {
        final StringPlayerGoodsInfoMapPB.Builder builder = StringPlayerGoodsInfoMapPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB.
     *
     * @param builder builder for protobuf PB
     * @return copied field count
     */
    public int copyToCs(StringPlayerGoodsInfoMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0 || builder.hasClearFlag() || builder.getDeleteKeysCount() != 0) {
                builder.clear();
                return StringPlayerGoodsInfoMapProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final Map.Entry<String, PlayerGoodsInfoProp> entry : this.entrySet()) {
            PlayerGoodsInfoPB.Builder itemBuilder = PlayerGoodsInfoPB.newBuilder();
            entry.getValue().copyToCs(itemBuilder);
            builder.putDatas(entry.getKey(), itemBuilder.build());
        }
        return StringPlayerGoodsInfoMapProp.FIELD_COUNT;
    }

    /**
     * copy data change to protobuf PB. clear first, then refresh, add at last.
     *
     * @param builder builder for protobuf PB
     * @return copied field count
     */
    public int copyChangeToCs(StringPlayerGoodsInfoMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        if (this.isClearFlag()) {
            // clear builder, when clear flag true
            builder.clear();
            // set clear flag when not clear.
            builder.setClearFlag(this.isClearFlag());
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            final Set<String> deleteKeys = !builder.getClearFlag() ? new HashSet<>(builder.getDeleteKeysList()) : null;
            isChanged = true;
            boolean isDeleteKeysChanged = false;
            for(final String key : this.getRefreshKeys()) {
                builder.removeDatas(key);
                if (deleteKeys != null) {
                    isDeleteKeysChanged = deleteKeys.add(key) || isDeleteKeysChanged;
                }
            }
            // update delete key when builder not clear.
            if (isDeleteKeysChanged) {
                builder.clearDeleteKeys();
                if (!deleteKeys.isEmpty()) {
                    builder.addAllDeleteKeys(deleteKeys);
                }
            }
        }
        // put data when dirty
        for (final String key : this.getExistDirtyKeys()) {
            final PlayerGoodsInfoProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final PlayerGoodsInfoPB.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : PlayerGoodsInfoPB.newBuilder();
            final int changeCnt = oldValue.copyChangeToCs(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        return isChanged? StringPlayerGoodsInfoMapProp.FIELD_COUNT: 0;
    }

    /**
     * copy data change to protobuf PB. clear first, then refresh, add at last. it wll clear clearFlag and deleteKeys.
     *
     * @param builder builder for protobuf PB
     * @return copied field count
     */
    public int copyChangeToAndClearDeleteKeysCs(StringPlayerGoodsInfoMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        // clear builder, when clear flag true
        if (this.isClearFlag()) {
            builder.clear();
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            isChanged = true;
            for(final String key : this.getRefreshKeys()) {
                builder.removeDatas(key);
            }
        }
        // put data when dirty
        for (final String key : this.getExistDirtyKeys()) {
            final PlayerGoodsInfoProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final PlayerGoodsInfoPB.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : PlayerGoodsInfoPB.newBuilder();
            final int changeCnt = oldValue.copyChangeToAndClearDeleteKeysCs(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        if (isChanged) {
            builder.clearDeleteKeys();
            builder.clearClearFlag();
        }
        return isChanged? StringPlayerGoodsInfoMapProp.FIELD_COUNT: 0;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(StringPlayerGoodsInfoMapPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (final Map.Entry<String, PlayerGoodsInfoPB> entry : proto.getDatasMap().entrySet()) {
            this.addEmptyValue(entry.getKey()).mergeFromCs(entry.getValue());
        }
        this.markAll();
        return StringPlayerGoodsInfoMapProp.FIELD_COUNT;
    }

    /**
     * merge data change from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeChangeFromCs(StringPlayerGoodsInfoMapPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int changeCnt = 0;
        if (proto.hasClearFlag() && proto.getClearFlag()) {
            this.clear();
            changeCnt = StringPlayerGoodsInfoMapProp.FIELD_COUNT;
        }
        if (proto.getDeleteKeysCount() > 0) {
            this.removeAll(proto.getDeleteKeysList());
            changeCnt = StringPlayerGoodsInfoMapProp.FIELD_COUNT;
        }
        for (final Map.Entry<String, PlayerGoodsInfoPB> entry : proto.getDatasMap().entrySet()) {
            if (this.containsKey(entry.getKey())) {
                this.get(entry.getKey()).mergeChangeFromCs(entry.getValue());
                changeCnt = StringPlayerGoodsInfoMapProp.FIELD_COUNT;
                continue;
            }
            this.addEmptyValue(entry.getKey()).mergeChangeFromCs(entry.getValue());
            changeCnt = StringPlayerGoodsInfoMapProp.FIELD_COUNT;
        }
        return changeCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public StringPlayerGoodsInfoMap.Builder getCopyDbBuilder() {
        final StringPlayerGoodsInfoMap.Builder builder = StringPlayerGoodsInfoMap.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf .
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyToDb(StringPlayerGoodsInfoMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0 || builder.hasClearFlag() || builder.getDeleteKeysCount() != 0) {
                builder.clear();
                return StringPlayerGoodsInfoMapProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final Map.Entry<String, PlayerGoodsInfoProp> entry : this.entrySet()) {
            PlayerGoodsInfo.Builder itemBuilder = PlayerGoodsInfo.newBuilder();
            entry.getValue().copyToDb(itemBuilder);
            builder.putDatas(entry.getKey(), itemBuilder.build());
        }
        return StringPlayerGoodsInfoMapProp.FIELD_COUNT;
    }

    /**
     * copy data change to protobuf . clear first, then refresh, add at last.
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyChangeToDb(StringPlayerGoodsInfoMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        if (this.isClearFlag()) {
            // clear builder, when clear flag true
            builder.clear();
            // set clear flag when not clear.
            builder.setClearFlag(this.isClearFlag());
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            final Set<String> deleteKeys = !builder.getClearFlag() ? new HashSet<>(builder.getDeleteKeysList()) : null;
            isChanged = true;
            boolean isDeleteKeysChanged = false;
            for(final String key : this.getRefreshKeys()) {
                builder.removeDatas(key);
                if (deleteKeys != null) {
                    isDeleteKeysChanged = deleteKeys.add(key) || isDeleteKeysChanged;
                }
            }
            // update delete key when builder not clear.
            if (isDeleteKeysChanged) {
                builder.clearDeleteKeys();
                if (!deleteKeys.isEmpty()) {
                    builder.addAllDeleteKeys(deleteKeys);
                }
            }
        }
        // put data when dirty
        for (final String key : this.getExistDirtyKeys()) {
            final PlayerGoodsInfoProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final PlayerGoodsInfo.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : PlayerGoodsInfo.newBuilder();
            final int changeCnt = oldValue.copyChangeToDb(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        return isChanged? StringPlayerGoodsInfoMapProp.FIELD_COUNT: 0;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(StringPlayerGoodsInfoMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (final Map.Entry<String, PlayerGoodsInfo> entry : proto.getDatasMap().entrySet()) {
            this.addEmptyValue(entry.getKey()).mergeFromDb(entry.getValue());
        }
        this.markAll();
        return StringPlayerGoodsInfoMapProp.FIELD_COUNT;
    }

    /**
     * merge data change from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeChangeFromDb(StringPlayerGoodsInfoMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int changeCnt = 0;
        if (proto.hasClearFlag() && proto.getClearFlag()) {
            this.clear();
            changeCnt = StringPlayerGoodsInfoMapProp.FIELD_COUNT;
        }
        if (proto.getDeleteKeysCount() > 0) {
            this.removeAll(proto.getDeleteKeysList());
            changeCnt = StringPlayerGoodsInfoMapProp.FIELD_COUNT;
        }
        for (final Map.Entry<String, PlayerGoodsInfo> entry : proto.getDatasMap().entrySet()) {
            if (this.containsKey(entry.getKey())) {
                this.get(entry.getKey()).mergeChangeFromDb(entry.getValue());
                changeCnt = StringPlayerGoodsInfoMapProp.FIELD_COUNT;
                continue;
            }
            this.addEmptyValue(entry.getKey()).mergeChangeFromDb(entry.getValue());
            changeCnt = StringPlayerGoodsInfoMapProp.FIELD_COUNT;
        }
        return changeCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public StringPlayerGoodsInfoMap.Builder getCopySsBuilder() {
        final StringPlayerGoodsInfoMap.Builder builder = StringPlayerGoodsInfoMap.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf .
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyToSs(StringPlayerGoodsInfoMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0 || builder.hasClearFlag() || builder.getDeleteKeysCount() != 0) {
                builder.clear();
                return StringPlayerGoodsInfoMapProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final Map.Entry<String, PlayerGoodsInfoProp> entry : this.entrySet()) {
            PlayerGoodsInfo.Builder itemBuilder = PlayerGoodsInfo.newBuilder();
            entry.getValue().copyToSs(itemBuilder);
            builder.putDatas(entry.getKey(), itemBuilder.build());
        }
        return StringPlayerGoodsInfoMapProp.FIELD_COUNT;
    }

    /**
     * copy data change to protobuf . clear first, then refresh, add at last.
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyChangeToSs(StringPlayerGoodsInfoMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        if (this.isClearFlag()) {
            // clear builder, when clear flag true
            builder.clear();
            // set clear flag when not clear.
            builder.setClearFlag(this.isClearFlag());
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            final Set<String> deleteKeys = !builder.getClearFlag() ? new HashSet<>(builder.getDeleteKeysList()) : null;
            isChanged = true;
            boolean isDeleteKeysChanged = false;
            for(final String key : this.getRefreshKeys()) {
                builder.removeDatas(key);
                if (deleteKeys != null) {
                    isDeleteKeysChanged = deleteKeys.add(key) || isDeleteKeysChanged;
                }
            }
            // update delete key when builder not clear.
            if (isDeleteKeysChanged) {
                builder.clearDeleteKeys();
                if (!deleteKeys.isEmpty()) {
                    builder.addAllDeleteKeys(deleteKeys);
                }
            }
        }
        // put data when dirty
        for (final String key : this.getExistDirtyKeys()) {
            final PlayerGoodsInfoProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final PlayerGoodsInfo.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : PlayerGoodsInfo.newBuilder();
            final int changeCnt = oldValue.copyChangeToSs(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        return isChanged? StringPlayerGoodsInfoMapProp.FIELD_COUNT: 0;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(StringPlayerGoodsInfoMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (final Map.Entry<String, PlayerGoodsInfo> entry : proto.getDatasMap().entrySet()) {
            this.addEmptyValue(entry.getKey()).mergeFromSs(entry.getValue());
        }
        this.markAll();
        return StringPlayerGoodsInfoMapProp.FIELD_COUNT;
    }

    /**
     * merge data change from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeChangeFromSs(StringPlayerGoodsInfoMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int changeCnt = 0;
        if (proto.hasClearFlag() && proto.getClearFlag()) {
            this.clear();
            changeCnt = StringPlayerGoodsInfoMapProp.FIELD_COUNT;
        }
        if (proto.getDeleteKeysCount() > 0) {
            this.removeAll(proto.getDeleteKeysList());
            changeCnt = StringPlayerGoodsInfoMapProp.FIELD_COUNT;
        }
        for (final Map.Entry<String, PlayerGoodsInfo> entry : proto.getDatasMap().entrySet()) {
            if (this.containsKey(entry.getKey())) {
                this.get(entry.getKey()).mergeChangeFromSs(entry.getValue());
                changeCnt = StringPlayerGoodsInfoMapProp.FIELD_COUNT;
                continue;
            }
            this.addEmptyValue(entry.getKey()).mergeChangeFromSs(entry.getValue());
            changeCnt = StringPlayerGoodsInfoMapProp.FIELD_COUNT;
        }
        return changeCnt;
    }

    @Override
    public String toString() {
        StringPlayerGoodsInfoMap.Builder builder = StringPlayerGoodsInfoMap.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
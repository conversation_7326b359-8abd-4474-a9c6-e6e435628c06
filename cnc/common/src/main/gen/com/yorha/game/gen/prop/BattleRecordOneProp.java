package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructBattle.BattleRecordOne;
import com.yorha.proto.Basic;
import com.yorha.proto.Struct;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructBattlePB.BattleRecordOnePB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructBattlePB;


/**
 * <AUTHOR> auto gen
 */
public class BattleRecordOneProp extends AbstractPropNode {

    public static final int FIELD_INDEX_LEFTROLE = 0;
    public static final int FIELD_INDEX_RIGHTROLE = 1;
    public static final int FIELD_INDEX_STARTMILLIS = 2;
    public static final int FIELD_INDEX_ENDMILLIS = 3;
    public static final int FIELD_INDEX_LOCATION = 4;
    public static final int FIELD_INDEX_BATTLELOGIDLIST = 5;
    public static final int FIELD_INDEX_ISLEFTROLE = 6;
    public static final int FIELD_INDEX_ISALLLOSS = 7;
    public static final int FIELD_INDEX_BATTLEID = 8;

    public static final int FIELD_COUNT = 9;

    private long markBits0 = 0L;

    private BattleRecordRoleProp leftRole = null;
    private BattleRecordRoleProp rightRole = null;
    private long startMillis = Constant.DEFAULT_LONG_VALUE;
    private long endMillis = Constant.DEFAULT_LONG_VALUE;
    private PointProp location = null;
    private Int64ListProp battleLogIdList = null;
    private boolean isLeftRole = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean isAllLoss = Constant.DEFAULT_BOOLEAN_VALUE;
    private long battleId = Constant.DEFAULT_LONG_VALUE;

    public BattleRecordOneProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BattleRecordOneProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get leftRole
     *
     * @return leftRole value
     */
    public BattleRecordRoleProp getLeftRole() {
        if (this.leftRole == null) {
            this.leftRole = new BattleRecordRoleProp(this, FIELD_INDEX_LEFTROLE);
        }
        return this.leftRole;
    }

    /**
     * get rightRole
     *
     * @return rightRole value
     */
    public BattleRecordRoleProp getRightRole() {
        if (this.rightRole == null) {
            this.rightRole = new BattleRecordRoleProp(this, FIELD_INDEX_RIGHTROLE);
        }
        return this.rightRole;
    }

    /**
     * get startMillis
     *
     * @return startMillis value
     */
    public long getStartMillis() {
        return this.startMillis;
    }

    /**
     * set startMillis && set marked
     *
     * @param startMillis new value
     * @return current object
     */
    public BattleRecordOneProp setStartMillis(long startMillis) {
        if (this.startMillis != startMillis) {
            this.mark(FIELD_INDEX_STARTMILLIS);
            this.startMillis = startMillis;
        }
        return this;
    }

    /**
     * inner set startMillis
     *
     * @param startMillis new value
     */
    private void innerSetStartMillis(long startMillis) {
        this.startMillis = startMillis;
    }

    /**
     * get endMillis
     *
     * @return endMillis value
     */
    public long getEndMillis() {
        return this.endMillis;
    }

    /**
     * set endMillis && set marked
     *
     * @param endMillis new value
     * @return current object
     */
    public BattleRecordOneProp setEndMillis(long endMillis) {
        if (this.endMillis != endMillis) {
            this.mark(FIELD_INDEX_ENDMILLIS);
            this.endMillis = endMillis;
        }
        return this;
    }

    /**
     * inner set endMillis
     *
     * @param endMillis new value
     */
    private void innerSetEndMillis(long endMillis) {
        this.endMillis = endMillis;
    }

    /**
     * get location
     *
     * @return location value
     */
    public PointProp getLocation() {
        if (this.location == null) {
            this.location = new PointProp(this, FIELD_INDEX_LOCATION);
        }
        return this.location;
    }

    /**
     * get battleLogIdList
     *
     * @return battleLogIdList value
     */
    public Int64ListProp getBattleLogIdList() {
        if (this.battleLogIdList == null) {
            this.battleLogIdList = new Int64ListProp(this, FIELD_INDEX_BATTLELOGIDLIST);
        }
        return this.battleLogIdList;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addBattleLogIdList(Long v) {
        this.getBattleLogIdList().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long getBattleLogIdListIndex(int index) {
        return this.getBattleLogIdList().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Long removeBattleLogIdList(Long v) {
        if (this.battleLogIdList == null) {
            return null;
        }
        if(this.battleLogIdList.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getBattleLogIdListSize() {
        if (this.battleLogIdList == null) {
            return 0;
        }
        return this.battleLogIdList.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isBattleLogIdListEmpty() {
        if (this.battleLogIdList == null) {
            return true;
        }
        return this.getBattleLogIdList().isEmpty();
    }

    /**
     * clear list
     */
    public void clearBattleLogIdList() {
        this.getBattleLogIdList().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long removeBattleLogIdListIndex(int index) {
        return this.getBattleLogIdList().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Long setBattleLogIdListIndex(int index, Long v) {
        return this.getBattleLogIdList().set(index, v);
    }
    /**
     * get isLeftRole
     *
     * @return isLeftRole value
     */
    public boolean getIsLeftRole() {
        return this.isLeftRole;
    }

    /**
     * set isLeftRole && set marked
     *
     * @param isLeftRole new value
     * @return current object
     */
    public BattleRecordOneProp setIsLeftRole(boolean isLeftRole) {
        if (this.isLeftRole != isLeftRole) {
            this.mark(FIELD_INDEX_ISLEFTROLE);
            this.isLeftRole = isLeftRole;
        }
        return this;
    }

    /**
     * inner set isLeftRole
     *
     * @param isLeftRole new value
     */
    private void innerSetIsLeftRole(boolean isLeftRole) {
        this.isLeftRole = isLeftRole;
    }

    /**
     * get isAllLoss
     *
     * @return isAllLoss value
     */
    public boolean getIsAllLoss() {
        return this.isAllLoss;
    }

    /**
     * set isAllLoss && set marked
     *
     * @param isAllLoss new value
     * @return current object
     */
    public BattleRecordOneProp setIsAllLoss(boolean isAllLoss) {
        if (this.isAllLoss != isAllLoss) {
            this.mark(FIELD_INDEX_ISALLLOSS);
            this.isAllLoss = isAllLoss;
        }
        return this;
    }

    /**
     * inner set isAllLoss
     *
     * @param isAllLoss new value
     */
    private void innerSetIsAllLoss(boolean isAllLoss) {
        this.isAllLoss = isAllLoss;
    }

    /**
     * get battleId
     *
     * @return battleId value
     */
    public long getBattleId() {
        return this.battleId;
    }

    /**
     * set battleId && set marked
     *
     * @param battleId new value
     * @return current object
     */
    public BattleRecordOneProp setBattleId(long battleId) {
        if (this.battleId != battleId) {
            this.mark(FIELD_INDEX_BATTLEID);
            this.battleId = battleId;
        }
        return this;
    }

    /**
     * inner set battleId
     *
     * @param battleId new value
     */
    private void innerSetBattleId(long battleId) {
        this.battleId = battleId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordOnePB.Builder getCopyCsBuilder() {
        final BattleRecordOnePB.Builder builder = BattleRecordOnePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BattleRecordOnePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.leftRole != null) {
            StructBattlePB.BattleRecordRolePB.Builder tmpBuilder = StructBattlePB.BattleRecordRolePB.newBuilder();
            final int tmpFieldCnt = this.leftRole.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLeftRole(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLeftRole();
            }
        }  else if (builder.hasLeftRole()) {
            // 清理LeftRole
            builder.clearLeftRole();
            fieldCnt++;
        }
        if (this.rightRole != null) {
            StructBattlePB.BattleRecordRolePB.Builder tmpBuilder = StructBattlePB.BattleRecordRolePB.newBuilder();
            final int tmpFieldCnt = this.rightRole.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRightRole(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRightRole();
            }
        }  else if (builder.hasRightRole()) {
            // 清理RightRole
            builder.clearRightRole();
            fieldCnt++;
        }
        if (this.getStartMillis() != 0L) {
            builder.setStartMillis(this.getStartMillis());
            fieldCnt++;
        }  else if (builder.hasStartMillis()) {
            // 清理StartMillis
            builder.clearStartMillis();
            fieldCnt++;
        }
        if (this.getEndMillis() != 0L) {
            builder.setEndMillis(this.getEndMillis());
            fieldCnt++;
        }  else if (builder.hasEndMillis()) {
            // 清理EndMillis
            builder.clearEndMillis();
            fieldCnt++;
        }
        if (this.location != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.location.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLocation(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLocation();
            }
        }  else if (builder.hasLocation()) {
            // 清理Location
            builder.clearLocation();
            fieldCnt++;
        }
        if (this.battleLogIdList != null) {
            BasicPB.Int64ListPB.Builder tmpBuilder = BasicPB.Int64ListPB.newBuilder();
            final int tmpFieldCnt = this.battleLogIdList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattleLogIdList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattleLogIdList();
            }
        }  else if (builder.hasBattleLogIdList()) {
            // 清理BattleLogIdList
            builder.clearBattleLogIdList();
            fieldCnt++;
        }
        if (this.getIsLeftRole()) {
            builder.setIsLeftRole(this.getIsLeftRole());
            fieldCnt++;
        }  else if (builder.hasIsLeftRole()) {
            // 清理IsLeftRole
            builder.clearIsLeftRole();
            fieldCnt++;
        }
        if (this.getIsAllLoss()) {
            builder.setIsAllLoss(this.getIsAllLoss());
            fieldCnt++;
        }  else if (builder.hasIsAllLoss()) {
            // 清理IsAllLoss
            builder.clearIsAllLoss();
            fieldCnt++;
        }
        if (this.getBattleId() != 0L) {
            builder.setBattleId(this.getBattleId());
            fieldCnt++;
        }  else if (builder.hasBattleId()) {
            // 清理BattleId
            builder.clearBattleId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BattleRecordOnePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LEFTROLE) && this.leftRole != null) {
            final boolean needClear = !builder.hasLeftRole();
            final int tmpFieldCnt = this.leftRole.copyChangeToCs(builder.getLeftRoleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLeftRole();
            }
        }
        if (this.hasMark(FIELD_INDEX_RIGHTROLE) && this.rightRole != null) {
            final boolean needClear = !builder.hasRightRole();
            final int tmpFieldCnt = this.rightRole.copyChangeToCs(builder.getRightRoleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRightRole();
            }
        }
        if (this.hasMark(FIELD_INDEX_STARTMILLIS)) {
            builder.setStartMillis(this.getStartMillis());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDMILLIS)) {
            builder.setEndMillis(this.getEndMillis());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOCATION) && this.location != null) {
            final boolean needClear = !builder.hasLocation();
            final int tmpFieldCnt = this.location.copyChangeToCs(builder.getLocationBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLocation();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLELOGIDLIST) && this.battleLogIdList != null) {
            final boolean needClear = !builder.hasBattleLogIdList();
            final int tmpFieldCnt = this.battleLogIdList.copyChangeToCs(builder.getBattleLogIdListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattleLogIdList();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISLEFTROLE)) {
            builder.setIsLeftRole(this.getIsLeftRole());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISALLLOSS)) {
            builder.setIsAllLoss(this.getIsAllLoss());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLEID)) {
            builder.setBattleId(this.getBattleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BattleRecordOnePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LEFTROLE) && this.leftRole != null) {
            final boolean needClear = !builder.hasLeftRole();
            final int tmpFieldCnt = this.leftRole.copyChangeToAndClearDeleteKeysCs(builder.getLeftRoleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLeftRole();
            }
        }
        if (this.hasMark(FIELD_INDEX_RIGHTROLE) && this.rightRole != null) {
            final boolean needClear = !builder.hasRightRole();
            final int tmpFieldCnt = this.rightRole.copyChangeToAndClearDeleteKeysCs(builder.getRightRoleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRightRole();
            }
        }
        if (this.hasMark(FIELD_INDEX_STARTMILLIS)) {
            builder.setStartMillis(this.getStartMillis());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDMILLIS)) {
            builder.setEndMillis(this.getEndMillis());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOCATION) && this.location != null) {
            final boolean needClear = !builder.hasLocation();
            final int tmpFieldCnt = this.location.copyChangeToAndClearDeleteKeysCs(builder.getLocationBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLocation();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLELOGIDLIST) && this.battleLogIdList != null) {
            final boolean needClear = !builder.hasBattleLogIdList();
            final int tmpFieldCnt = this.battleLogIdList.copyChangeToCs(builder.getBattleLogIdListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattleLogIdList();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISLEFTROLE)) {
            builder.setIsLeftRole(this.getIsLeftRole());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISALLLOSS)) {
            builder.setIsAllLoss(this.getIsAllLoss());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLEID)) {
            builder.setBattleId(this.getBattleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BattleRecordOnePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLeftRole()) {
            this.getLeftRole().mergeFromCs(proto.getLeftRole());
        } else {
            if (this.leftRole != null) {
                this.leftRole.mergeFromCs(proto.getLeftRole());
            }
        }
        if (proto.hasRightRole()) {
            this.getRightRole().mergeFromCs(proto.getRightRole());
        } else {
            if (this.rightRole != null) {
                this.rightRole.mergeFromCs(proto.getRightRole());
            }
        }
        if (proto.hasStartMillis()) {
            this.innerSetStartMillis(proto.getStartMillis());
        } else {
            this.innerSetStartMillis(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndMillis()) {
            this.innerSetEndMillis(proto.getEndMillis());
        } else {
            this.innerSetEndMillis(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLocation()) {
            this.getLocation().mergeFromCs(proto.getLocation());
        } else {
            if (this.location != null) {
                this.location.mergeFromCs(proto.getLocation());
            }
        }
        if (proto.hasBattleLogIdList()) {
            this.getBattleLogIdList().mergeFromCs(proto.getBattleLogIdList());
        } else {
            if (this.battleLogIdList != null) {
                this.battleLogIdList.mergeFromCs(proto.getBattleLogIdList());
            }
        }
        if (proto.hasIsLeftRole()) {
            this.innerSetIsLeftRole(proto.getIsLeftRole());
        } else {
            this.innerSetIsLeftRole(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasIsAllLoss()) {
            this.innerSetIsAllLoss(proto.getIsAllLoss());
        } else {
            this.innerSetIsAllLoss(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasBattleId()) {
            this.innerSetBattleId(proto.getBattleId());
        } else {
            this.innerSetBattleId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return BattleRecordOneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BattleRecordOnePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLeftRole()) {
            this.getLeftRole().mergeChangeFromCs(proto.getLeftRole());
            fieldCnt++;
        }
        if (proto.hasRightRole()) {
            this.getRightRole().mergeChangeFromCs(proto.getRightRole());
            fieldCnt++;
        }
        if (proto.hasStartMillis()) {
            this.setStartMillis(proto.getStartMillis());
            fieldCnt++;
        }
        if (proto.hasEndMillis()) {
            this.setEndMillis(proto.getEndMillis());
            fieldCnt++;
        }
        if (proto.hasLocation()) {
            this.getLocation().mergeChangeFromCs(proto.getLocation());
            fieldCnt++;
        }
        if (proto.hasBattleLogIdList()) {
            this.getBattleLogIdList().mergeChangeFromCs(proto.getBattleLogIdList());
            fieldCnt++;
        }
        if (proto.hasIsLeftRole()) {
            this.setIsLeftRole(proto.getIsLeftRole());
            fieldCnt++;
        }
        if (proto.hasIsAllLoss()) {
            this.setIsAllLoss(proto.getIsAllLoss());
            fieldCnt++;
        }
        if (proto.hasBattleId()) {
            this.setBattleId(proto.getBattleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordOne.Builder getCopyDbBuilder() {
        final BattleRecordOne.Builder builder = BattleRecordOne.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(BattleRecordOne.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.leftRole != null) {
            StructBattle.BattleRecordRole.Builder tmpBuilder = StructBattle.BattleRecordRole.newBuilder();
            final int tmpFieldCnt = this.leftRole.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLeftRole(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLeftRole();
            }
        }  else if (builder.hasLeftRole()) {
            // 清理LeftRole
            builder.clearLeftRole();
            fieldCnt++;
        }
        if (this.rightRole != null) {
            StructBattle.BattleRecordRole.Builder tmpBuilder = StructBattle.BattleRecordRole.newBuilder();
            final int tmpFieldCnt = this.rightRole.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRightRole(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRightRole();
            }
        }  else if (builder.hasRightRole()) {
            // 清理RightRole
            builder.clearRightRole();
            fieldCnt++;
        }
        if (this.getStartMillis() != 0L) {
            builder.setStartMillis(this.getStartMillis());
            fieldCnt++;
        }  else if (builder.hasStartMillis()) {
            // 清理StartMillis
            builder.clearStartMillis();
            fieldCnt++;
        }
        if (this.getEndMillis() != 0L) {
            builder.setEndMillis(this.getEndMillis());
            fieldCnt++;
        }  else if (builder.hasEndMillis()) {
            // 清理EndMillis
            builder.clearEndMillis();
            fieldCnt++;
        }
        if (this.location != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.location.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLocation(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLocation();
            }
        }  else if (builder.hasLocation()) {
            // 清理Location
            builder.clearLocation();
            fieldCnt++;
        }
        if (this.battleLogIdList != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.battleLogIdList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattleLogIdList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattleLogIdList();
            }
        }  else if (builder.hasBattleLogIdList()) {
            // 清理BattleLogIdList
            builder.clearBattleLogIdList();
            fieldCnt++;
        }
        if (this.getIsLeftRole()) {
            builder.setIsLeftRole(this.getIsLeftRole());
            fieldCnt++;
        }  else if (builder.hasIsLeftRole()) {
            // 清理IsLeftRole
            builder.clearIsLeftRole();
            fieldCnt++;
        }
        if (this.getIsAllLoss()) {
            builder.setIsAllLoss(this.getIsAllLoss());
            fieldCnt++;
        }  else if (builder.hasIsAllLoss()) {
            // 清理IsAllLoss
            builder.clearIsAllLoss();
            fieldCnt++;
        }
        if (this.getBattleId() != 0L) {
            builder.setBattleId(this.getBattleId());
            fieldCnt++;
        }  else if (builder.hasBattleId()) {
            // 清理BattleId
            builder.clearBattleId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(BattleRecordOne.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LEFTROLE) && this.leftRole != null) {
            final boolean needClear = !builder.hasLeftRole();
            final int tmpFieldCnt = this.leftRole.copyChangeToDb(builder.getLeftRoleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLeftRole();
            }
        }
        if (this.hasMark(FIELD_INDEX_RIGHTROLE) && this.rightRole != null) {
            final boolean needClear = !builder.hasRightRole();
            final int tmpFieldCnt = this.rightRole.copyChangeToDb(builder.getRightRoleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRightRole();
            }
        }
        if (this.hasMark(FIELD_INDEX_STARTMILLIS)) {
            builder.setStartMillis(this.getStartMillis());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDMILLIS)) {
            builder.setEndMillis(this.getEndMillis());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOCATION) && this.location != null) {
            final boolean needClear = !builder.hasLocation();
            final int tmpFieldCnt = this.location.copyChangeToDb(builder.getLocationBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLocation();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLELOGIDLIST) && this.battleLogIdList != null) {
            final boolean needClear = !builder.hasBattleLogIdList();
            final int tmpFieldCnt = this.battleLogIdList.copyChangeToDb(builder.getBattleLogIdListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattleLogIdList();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISLEFTROLE)) {
            builder.setIsLeftRole(this.getIsLeftRole());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISALLLOSS)) {
            builder.setIsAllLoss(this.getIsAllLoss());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLEID)) {
            builder.setBattleId(this.getBattleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(BattleRecordOne proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLeftRole()) {
            this.getLeftRole().mergeFromDb(proto.getLeftRole());
        } else {
            if (this.leftRole != null) {
                this.leftRole.mergeFromDb(proto.getLeftRole());
            }
        }
        if (proto.hasRightRole()) {
            this.getRightRole().mergeFromDb(proto.getRightRole());
        } else {
            if (this.rightRole != null) {
                this.rightRole.mergeFromDb(proto.getRightRole());
            }
        }
        if (proto.hasStartMillis()) {
            this.innerSetStartMillis(proto.getStartMillis());
        } else {
            this.innerSetStartMillis(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndMillis()) {
            this.innerSetEndMillis(proto.getEndMillis());
        } else {
            this.innerSetEndMillis(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLocation()) {
            this.getLocation().mergeFromDb(proto.getLocation());
        } else {
            if (this.location != null) {
                this.location.mergeFromDb(proto.getLocation());
            }
        }
        if (proto.hasBattleLogIdList()) {
            this.getBattleLogIdList().mergeFromDb(proto.getBattleLogIdList());
        } else {
            if (this.battleLogIdList != null) {
                this.battleLogIdList.mergeFromDb(proto.getBattleLogIdList());
            }
        }
        if (proto.hasIsLeftRole()) {
            this.innerSetIsLeftRole(proto.getIsLeftRole());
        } else {
            this.innerSetIsLeftRole(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasIsAllLoss()) {
            this.innerSetIsAllLoss(proto.getIsAllLoss());
        } else {
            this.innerSetIsAllLoss(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasBattleId()) {
            this.innerSetBattleId(proto.getBattleId());
        } else {
            this.innerSetBattleId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return BattleRecordOneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(BattleRecordOne proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLeftRole()) {
            this.getLeftRole().mergeChangeFromDb(proto.getLeftRole());
            fieldCnt++;
        }
        if (proto.hasRightRole()) {
            this.getRightRole().mergeChangeFromDb(proto.getRightRole());
            fieldCnt++;
        }
        if (proto.hasStartMillis()) {
            this.setStartMillis(proto.getStartMillis());
            fieldCnt++;
        }
        if (proto.hasEndMillis()) {
            this.setEndMillis(proto.getEndMillis());
            fieldCnt++;
        }
        if (proto.hasLocation()) {
            this.getLocation().mergeChangeFromDb(proto.getLocation());
            fieldCnt++;
        }
        if (proto.hasBattleLogIdList()) {
            this.getBattleLogIdList().mergeChangeFromDb(proto.getBattleLogIdList());
            fieldCnt++;
        }
        if (proto.hasIsLeftRole()) {
            this.setIsLeftRole(proto.getIsLeftRole());
            fieldCnt++;
        }
        if (proto.hasIsAllLoss()) {
            this.setIsAllLoss(proto.getIsAllLoss());
            fieldCnt++;
        }
        if (proto.hasBattleId()) {
            this.setBattleId(proto.getBattleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordOne.Builder getCopySsBuilder() {
        final BattleRecordOne.Builder builder = BattleRecordOne.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(BattleRecordOne.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.leftRole != null) {
            StructBattle.BattleRecordRole.Builder tmpBuilder = StructBattle.BattleRecordRole.newBuilder();
            final int tmpFieldCnt = this.leftRole.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLeftRole(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLeftRole();
            }
        }  else if (builder.hasLeftRole()) {
            // 清理LeftRole
            builder.clearLeftRole();
            fieldCnt++;
        }
        if (this.rightRole != null) {
            StructBattle.BattleRecordRole.Builder tmpBuilder = StructBattle.BattleRecordRole.newBuilder();
            final int tmpFieldCnt = this.rightRole.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRightRole(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRightRole();
            }
        }  else if (builder.hasRightRole()) {
            // 清理RightRole
            builder.clearRightRole();
            fieldCnt++;
        }
        if (this.getStartMillis() != 0L) {
            builder.setStartMillis(this.getStartMillis());
            fieldCnt++;
        }  else if (builder.hasStartMillis()) {
            // 清理StartMillis
            builder.clearStartMillis();
            fieldCnt++;
        }
        if (this.getEndMillis() != 0L) {
            builder.setEndMillis(this.getEndMillis());
            fieldCnt++;
        }  else if (builder.hasEndMillis()) {
            // 清理EndMillis
            builder.clearEndMillis();
            fieldCnt++;
        }
        if (this.location != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.location.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLocation(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLocation();
            }
        }  else if (builder.hasLocation()) {
            // 清理Location
            builder.clearLocation();
            fieldCnt++;
        }
        if (this.battleLogIdList != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.battleLogIdList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattleLogIdList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattleLogIdList();
            }
        }  else if (builder.hasBattleLogIdList()) {
            // 清理BattleLogIdList
            builder.clearBattleLogIdList();
            fieldCnt++;
        }
        if (this.getIsLeftRole()) {
            builder.setIsLeftRole(this.getIsLeftRole());
            fieldCnt++;
        }  else if (builder.hasIsLeftRole()) {
            // 清理IsLeftRole
            builder.clearIsLeftRole();
            fieldCnt++;
        }
        if (this.getIsAllLoss()) {
            builder.setIsAllLoss(this.getIsAllLoss());
            fieldCnt++;
        }  else if (builder.hasIsAllLoss()) {
            // 清理IsAllLoss
            builder.clearIsAllLoss();
            fieldCnt++;
        }
        if (this.getBattleId() != 0L) {
            builder.setBattleId(this.getBattleId());
            fieldCnt++;
        }  else if (builder.hasBattleId()) {
            // 清理BattleId
            builder.clearBattleId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(BattleRecordOne.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LEFTROLE) && this.leftRole != null) {
            final boolean needClear = !builder.hasLeftRole();
            final int tmpFieldCnt = this.leftRole.copyChangeToSs(builder.getLeftRoleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLeftRole();
            }
        }
        if (this.hasMark(FIELD_INDEX_RIGHTROLE) && this.rightRole != null) {
            final boolean needClear = !builder.hasRightRole();
            final int tmpFieldCnt = this.rightRole.copyChangeToSs(builder.getRightRoleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRightRole();
            }
        }
        if (this.hasMark(FIELD_INDEX_STARTMILLIS)) {
            builder.setStartMillis(this.getStartMillis());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDMILLIS)) {
            builder.setEndMillis(this.getEndMillis());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOCATION) && this.location != null) {
            final boolean needClear = !builder.hasLocation();
            final int tmpFieldCnt = this.location.copyChangeToSs(builder.getLocationBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLocation();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLELOGIDLIST) && this.battleLogIdList != null) {
            final boolean needClear = !builder.hasBattleLogIdList();
            final int tmpFieldCnt = this.battleLogIdList.copyChangeToSs(builder.getBattleLogIdListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattleLogIdList();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISLEFTROLE)) {
            builder.setIsLeftRole(this.getIsLeftRole());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISALLLOSS)) {
            builder.setIsAllLoss(this.getIsAllLoss());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLEID)) {
            builder.setBattleId(this.getBattleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(BattleRecordOne proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLeftRole()) {
            this.getLeftRole().mergeFromSs(proto.getLeftRole());
        } else {
            if (this.leftRole != null) {
                this.leftRole.mergeFromSs(proto.getLeftRole());
            }
        }
        if (proto.hasRightRole()) {
            this.getRightRole().mergeFromSs(proto.getRightRole());
        } else {
            if (this.rightRole != null) {
                this.rightRole.mergeFromSs(proto.getRightRole());
            }
        }
        if (proto.hasStartMillis()) {
            this.innerSetStartMillis(proto.getStartMillis());
        } else {
            this.innerSetStartMillis(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndMillis()) {
            this.innerSetEndMillis(proto.getEndMillis());
        } else {
            this.innerSetEndMillis(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLocation()) {
            this.getLocation().mergeFromSs(proto.getLocation());
        } else {
            if (this.location != null) {
                this.location.mergeFromSs(proto.getLocation());
            }
        }
        if (proto.hasBattleLogIdList()) {
            this.getBattleLogIdList().mergeFromSs(proto.getBattleLogIdList());
        } else {
            if (this.battleLogIdList != null) {
                this.battleLogIdList.mergeFromSs(proto.getBattleLogIdList());
            }
        }
        if (proto.hasIsLeftRole()) {
            this.innerSetIsLeftRole(proto.getIsLeftRole());
        } else {
            this.innerSetIsLeftRole(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasIsAllLoss()) {
            this.innerSetIsAllLoss(proto.getIsAllLoss());
        } else {
            this.innerSetIsAllLoss(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasBattleId()) {
            this.innerSetBattleId(proto.getBattleId());
        } else {
            this.innerSetBattleId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return BattleRecordOneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(BattleRecordOne proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLeftRole()) {
            this.getLeftRole().mergeChangeFromSs(proto.getLeftRole());
            fieldCnt++;
        }
        if (proto.hasRightRole()) {
            this.getRightRole().mergeChangeFromSs(proto.getRightRole());
            fieldCnt++;
        }
        if (proto.hasStartMillis()) {
            this.setStartMillis(proto.getStartMillis());
            fieldCnt++;
        }
        if (proto.hasEndMillis()) {
            this.setEndMillis(proto.getEndMillis());
            fieldCnt++;
        }
        if (proto.hasLocation()) {
            this.getLocation().mergeChangeFromSs(proto.getLocation());
            fieldCnt++;
        }
        if (proto.hasBattleLogIdList()) {
            this.getBattleLogIdList().mergeChangeFromSs(proto.getBattleLogIdList());
            fieldCnt++;
        }
        if (proto.hasIsLeftRole()) {
            this.setIsLeftRole(proto.getIsLeftRole());
            fieldCnt++;
        }
        if (proto.hasIsAllLoss()) {
            this.setIsAllLoss(proto.getIsAllLoss());
            fieldCnt++;
        }
        if (proto.hasBattleId()) {
            this.setBattleId(proto.getBattleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        BattleRecordOne.Builder builder = BattleRecordOne.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_LEFTROLE) && this.leftRole != null) {
            this.leftRole.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RIGHTROLE) && this.rightRole != null) {
            this.rightRole.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_LOCATION) && this.location != null) {
            this.location.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BATTLELOGIDLIST) && this.battleLogIdList != null) {
            this.battleLogIdList.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.leftRole != null) {
            this.leftRole.markAll();
        }
        if (this.rightRole != null) {
            this.rightRole.markAll();
        }
        if (this.location != null) {
            this.location.markAll();
        }
        if (this.battleLogIdList != null) {
            this.battleLogIdList.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BattleRecordOneProp)) {
            return false;
        }
        final BattleRecordOneProp otherNode = (BattleRecordOneProp) node;
        if (!this.getLeftRole().compareDataTo(otherNode.getLeftRole())) {
            return false;
        }
        if (!this.getRightRole().compareDataTo(otherNode.getRightRole())) {
            return false;
        }
        if (this.startMillis != otherNode.startMillis) {
            return false;
        }
        if (this.endMillis != otherNode.endMillis) {
            return false;
        }
        if (!this.getLocation().compareDataTo(otherNode.getLocation())) {
            return false;
        }
        if (!this.getBattleLogIdList().compareDataTo(otherNode.getBattleLogIdList())) {
            return false;
        }
        if (this.isLeftRole != otherNode.isLeftRole) {
            return false;
        }
        if (this.isAllLoss != otherNode.isAllLoss) {
            return false;
        }
        if (this.battleId != otherNode.battleId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 55;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructPB.PickLimitInfoListPB;
import com.yorha.proto.Struct.PickLimitInfoList;
import com.yorha.proto.StructPB.PickLimitInfoPB;
import com.yorha.proto.Struct.PickLimitInfo;

/**
 * <AUTHOR> auto gen
 */
public class PickLimitInfoListProp extends AbstractListNode<PickLimitInfoProp> {
    /**
     * Create a PickLimitInfoListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public PickLimitInfoListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to PickLimitInfoListProp
     *
     * @return new object
     */
    @Override
    public PickLimitInfoProp addEmptyValue() {
        final PickLimitInfoProp newProp = new PickLimitInfoProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PickLimitInfoListPB.Builder getCopyCsBuilder() {
        final PickLimitInfoListPB.Builder builder = PickLimitInfoListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(PickLimitInfoListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return PickLimitInfoListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final PickLimitInfoProp v : this) {
            final PickLimitInfoPB.Builder itemBuilder = PickLimitInfoPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return PickLimitInfoListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(PickLimitInfoListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return PickLimitInfoListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(PickLimitInfoListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (PickLimitInfoPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return PickLimitInfoListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(PickLimitInfoListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PickLimitInfoList.Builder getCopyDbBuilder() {
        final PickLimitInfoList.Builder builder = PickLimitInfoList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(PickLimitInfoList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return PickLimitInfoListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final PickLimitInfoProp v : this) {
            final PickLimitInfo.Builder itemBuilder = PickLimitInfo.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return PickLimitInfoListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(PickLimitInfoList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return PickLimitInfoListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(PickLimitInfoList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (PickLimitInfo v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return PickLimitInfoListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(PickLimitInfoList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PickLimitInfoList.Builder getCopySsBuilder() {
        final PickLimitInfoList.Builder builder = PickLimitInfoList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(PickLimitInfoList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return PickLimitInfoListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final PickLimitInfoProp v : this) {
            final PickLimitInfo.Builder itemBuilder = PickLimitInfo.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return PickLimitInfoListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(PickLimitInfoList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return PickLimitInfoListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(PickLimitInfoList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (PickLimitInfo v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return PickLimitInfoListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(PickLimitInfoList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        PickLimitInfoList.Builder builder = PickLimitInfoList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
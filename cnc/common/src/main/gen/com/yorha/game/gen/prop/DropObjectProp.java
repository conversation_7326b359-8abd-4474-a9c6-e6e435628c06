package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.DropObject.DropObjectEntity;
import com.yorha.proto.Basic;
import com.yorha.proto.Struct;
import com.yorha.proto.DropObjectPB.DropObjectEntityPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class DropObjectProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TEMPLATEID = 0;
    public static final int FIELD_INDEX_POINT = 1;
    public static final int FIELD_INDEX_PICKUPPLAYERS = 2;
    public static final int FIELD_INDEX_PICKUPTIMES = 3;
    public static final int FIELD_INDEX_BORNTIME = 4;
    public static final int FIELD_INDEX_OWNERID = 5;
    public static final int FIELD_INDEX_PICKUPARMY = 6;
    public static final int FIELD_INDEX_ITEMREWARD = 7;
    public static final int FIELD_INDEX_GROUPLIMIT = 8;

    public static final int FIELD_COUNT = 9;

    private long markBits0 = 0L;
    private PropertyChangeListener listener;

    private int templateId = Constant.DEFAULT_INT_VALUE;
    private PointProp point = null;
    private Int64ListProp pickUpPlayers = null;
    private int pickUpTimes = Constant.DEFAULT_INT_VALUE;
    private long bornTime = Constant.DEFAULT_LONG_VALUE;
    private long ownerId = Constant.DEFAULT_LONG_VALUE;
    private Int64ListProp pickUpArmy = null;
    private ItemListProp itemReward = null;
    private DropGroupLimitProp groupLimit = null;

    public DropObjectProp() {
        super(null, 0, FIELD_COUNT);
    }

    public DropObjectProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public DropObjectProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }

    /**
     * get point
     *
     * @return point value
     */
    public PointProp getPoint() {
        if (this.point == null) {
            this.point = new PointProp(this, FIELD_INDEX_POINT);
        }
        return this.point;
    }

    /**
     * get pickUpPlayers
     *
     * @return pickUpPlayers value
     */
    public Int64ListProp getPickUpPlayers() {
        if (this.pickUpPlayers == null) {
            this.pickUpPlayers = new Int64ListProp(this, FIELD_INDEX_PICKUPPLAYERS);
        }
        return this.pickUpPlayers;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addPickUpPlayers(Long v) {
        this.getPickUpPlayers().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long getPickUpPlayersIndex(int index) {
        return this.getPickUpPlayers().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Long removePickUpPlayers(Long v) {
        if (this.pickUpPlayers == null) {
            return null;
        }
        if(this.pickUpPlayers.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getPickUpPlayersSize() {
        if (this.pickUpPlayers == null) {
            return 0;
        }
        return this.pickUpPlayers.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isPickUpPlayersEmpty() {
        if (this.pickUpPlayers == null) {
            return true;
        }
        return this.getPickUpPlayers().isEmpty();
    }

    /**
     * clear list
     */
    public void clearPickUpPlayers() {
        this.getPickUpPlayers().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long removePickUpPlayersIndex(int index) {
        return this.getPickUpPlayers().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Long setPickUpPlayersIndex(int index, Long v) {
        return this.getPickUpPlayers().set(index, v);
    }
    /**
     * get pickUpTimes
     *
     * @return pickUpTimes value
     */
    public int getPickUpTimes() {
        return this.pickUpTimes;
    }

    /**
     * set pickUpTimes && set marked
     *
     * @param pickUpTimes new value
     * @return current object
     */
    public DropObjectProp setPickUpTimes(int pickUpTimes) {
        if (this.pickUpTimes != pickUpTimes) {
            this.mark(FIELD_INDEX_PICKUPTIMES);
            this.pickUpTimes = pickUpTimes;
        }
        return this;
    }

    /**
     * inner set pickUpTimes
     *
     * @param pickUpTimes new value
     */
    private void innerSetPickUpTimes(int pickUpTimes) {
        this.pickUpTimes = pickUpTimes;
    }

    /**
     * get bornTime
     *
     * @return bornTime value
     */
    public long getBornTime() {
        return this.bornTime;
    }

    /**
     * set bornTime && set marked
     *
     * @param bornTime new value
     * @return current object
     */
    public DropObjectProp setBornTime(long bornTime) {
        if (this.bornTime != bornTime) {
            this.mark(FIELD_INDEX_BORNTIME);
            this.bornTime = bornTime;
        }
        return this;
    }

    /**
     * inner set bornTime
     *
     * @param bornTime new value
     */
    private void innerSetBornTime(long bornTime) {
        this.bornTime = bornTime;
    }

    /**
     * get ownerId
     *
     * @return ownerId value
     */
    public long getOwnerId() {
        return this.ownerId;
    }

    /**
     * set ownerId && set marked
     *
     * @param ownerId new value
     * @return current object
     */
    public DropObjectProp setOwnerId(long ownerId) {
        if (this.ownerId != ownerId) {
            this.mark(FIELD_INDEX_OWNERID);
            this.ownerId = ownerId;
        }
        return this;
    }

    /**
     * inner set ownerId
     *
     * @param ownerId new value
     */
    private void innerSetOwnerId(long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * get pickUpArmy
     *
     * @return pickUpArmy value
     */
    public Int64ListProp getPickUpArmy() {
        if (this.pickUpArmy == null) {
            this.pickUpArmy = new Int64ListProp(this, FIELD_INDEX_PICKUPARMY);
        }
        return this.pickUpArmy;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addPickUpArmy(Long v) {
        this.getPickUpArmy().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long getPickUpArmyIndex(int index) {
        return this.getPickUpArmy().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Long removePickUpArmy(Long v) {
        if (this.pickUpArmy == null) {
            return null;
        }
        if(this.pickUpArmy.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getPickUpArmySize() {
        if (this.pickUpArmy == null) {
            return 0;
        }
        return this.pickUpArmy.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isPickUpArmyEmpty() {
        if (this.pickUpArmy == null) {
            return true;
        }
        return this.getPickUpArmy().isEmpty();
    }

    /**
     * clear list
     */
    public void clearPickUpArmy() {
        this.getPickUpArmy().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long removePickUpArmyIndex(int index) {
        return this.getPickUpArmy().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Long setPickUpArmyIndex(int index, Long v) {
        return this.getPickUpArmy().set(index, v);
    }
    /**
     * get itemReward
     *
     * @return itemReward value
     */
    public ItemListProp getItemReward() {
        if (this.itemReward == null) {
            this.itemReward = new ItemListProp(this, FIELD_INDEX_ITEMREWARD);
        }
        return this.itemReward;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addItemReward(ItemProp v) {
        this.getItemReward().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ItemProp getItemRewardIndex(int index) {
        return this.getItemReward().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public ItemProp removeItemReward(ItemProp v) {
        if (this.itemReward == null) {
            return null;
        }
        if(this.itemReward.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getItemRewardSize() {
        if (this.itemReward == null) {
            return 0;
        }
        return this.itemReward.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isItemRewardEmpty() {
        if (this.itemReward == null) {
            return true;
        }
        return this.getItemReward().isEmpty();
    }

    /**
     * clear list
     */
    public void clearItemReward() {
        this.getItemReward().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ItemProp removeItemRewardIndex(int index) {
        return this.getItemReward().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public ItemProp setItemRewardIndex(int index, ItemProp v) {
        return this.getItemReward().set(index, v);
    }
    /**
     * get groupLimit
     *
     * @return groupLimit value
     */
    public DropGroupLimitProp getGroupLimit() {
        if (this.groupLimit == null) {
            this.groupLimit = new DropGroupLimitProp(this, FIELD_INDEX_GROUPLIMIT);
        }
        return this.groupLimit;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DropObjectEntityPB.Builder getCopyCsBuilder() {
        final DropObjectEntityPB.Builder builder = DropObjectEntityPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(DropObjectEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.point != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.point.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getPickUpTimes() != 0) {
            builder.setPickUpTimes(this.getPickUpTimes());
            fieldCnt++;
        }  else if (builder.hasPickUpTimes()) {
            // 清理PickUpTimes
            builder.clearPickUpTimes();
            fieldCnt++;
        }
        if (this.getBornTime() != 0L) {
            builder.setBornTime(this.getBornTime());
            fieldCnt++;
        }  else if (builder.hasBornTime()) {
            // 清理BornTime
            builder.clearBornTime();
            fieldCnt++;
        }
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.pickUpArmy != null) {
            BasicPB.Int64ListPB.Builder tmpBuilder = BasicPB.Int64ListPB.newBuilder();
            final int tmpFieldCnt = this.pickUpArmy.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPickUpArmy(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPickUpArmy();
            }
        }  else if (builder.hasPickUpArmy()) {
            // 清理PickUpArmy
            builder.clearPickUpArmy();
            fieldCnt++;
        }
        if (this.itemReward != null) {
            StructPB.ItemListPB.Builder tmpBuilder = StructPB.ItemListPB.newBuilder();
            final int tmpFieldCnt = this.itemReward.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItemReward(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItemReward();
            }
        }  else if (builder.hasItemReward()) {
            // 清理ItemReward
            builder.clearItemReward();
            fieldCnt++;
        }
        if (this.groupLimit != null) {
            StructPB.DropGroupLimitPB.Builder tmpBuilder = StructPB.DropGroupLimitPB.newBuilder();
            final int tmpFieldCnt = this.groupLimit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGroupLimit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGroupLimit();
            }
        }  else if (builder.hasGroupLimit()) {
            // 清理GroupLimit
            builder.clearGroupLimit();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(DropObjectEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_PICKUPTIMES)) {
            builder.setPickUpTimes(this.getPickUpTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BORNTIME)) {
            builder.setBornTime(this.getBornTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICKUPARMY) && this.pickUpArmy != null) {
            final boolean needClear = !builder.hasPickUpArmy();
            final int tmpFieldCnt = this.pickUpArmy.copyChangeToCs(builder.getPickUpArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPickUpArmy();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            final boolean needClear = !builder.hasItemReward();
            final int tmpFieldCnt = this.itemReward.copyChangeToCs(builder.getItemRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemReward();
            }
        }
        if (this.hasMark(FIELD_INDEX_GROUPLIMIT) && this.groupLimit != null) {
            final boolean needClear = !builder.hasGroupLimit();
            final int tmpFieldCnt = this.groupLimit.copyChangeToCs(builder.getGroupLimitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGroupLimit();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(DropObjectEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToAndClearDeleteKeysCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_PICKUPTIMES)) {
            builder.setPickUpTimes(this.getPickUpTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BORNTIME)) {
            builder.setBornTime(this.getBornTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICKUPARMY) && this.pickUpArmy != null) {
            final boolean needClear = !builder.hasPickUpArmy();
            final int tmpFieldCnt = this.pickUpArmy.copyChangeToCs(builder.getPickUpArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPickUpArmy();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            final boolean needClear = !builder.hasItemReward();
            final int tmpFieldCnt = this.itemReward.copyChangeToCs(builder.getItemRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemReward();
            }
        }
        if (this.hasMark(FIELD_INDEX_GROUPLIMIT) && this.groupLimit != null) {
            final boolean needClear = !builder.hasGroupLimit();
            final int tmpFieldCnt = this.groupLimit.copyChangeToAndClearDeleteKeysCs(builder.getGroupLimitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGroupLimit();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(DropObjectEntityPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromCs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromCs(proto.getPoint());
            }
        }
        if (proto.hasPickUpTimes()) {
            this.innerSetPickUpTimes(proto.getPickUpTimes());
        } else {
            this.innerSetPickUpTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBornTime()) {
            this.innerSetBornTime(proto.getBornTime());
        } else {
            this.innerSetBornTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPickUpArmy()) {
            this.getPickUpArmy().mergeFromCs(proto.getPickUpArmy());
        } else {
            if (this.pickUpArmy != null) {
                this.pickUpArmy.mergeFromCs(proto.getPickUpArmy());
            }
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeFromCs(proto.getItemReward());
        } else {
            if (this.itemReward != null) {
                this.itemReward.mergeFromCs(proto.getItemReward());
            }
        }
        if (proto.hasGroupLimit()) {
            this.getGroupLimit().mergeFromCs(proto.getGroupLimit());
        } else {
            if (this.groupLimit != null) {
                this.groupLimit.mergeFromCs(proto.getGroupLimit());
            }
        }
        this.markAll();
        return DropObjectProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(DropObjectEntityPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromCs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasPickUpTimes()) {
            this.setPickUpTimes(proto.getPickUpTimes());
            fieldCnt++;
        }
        if (proto.hasBornTime()) {
            this.setBornTime(proto.getBornTime());
            fieldCnt++;
        }
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasPickUpArmy()) {
            this.getPickUpArmy().mergeChangeFromCs(proto.getPickUpArmy());
            fieldCnt++;
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeChangeFromCs(proto.getItemReward());
            fieldCnt++;
        }
        if (proto.hasGroupLimit()) {
            this.getGroupLimit().mergeChangeFromCs(proto.getGroupLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DropObjectEntity.Builder getCopyDbBuilder() {
        final DropObjectEntity.Builder builder = DropObjectEntity.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(DropObjectEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.pickUpPlayers != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.pickUpPlayers.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPickUpPlayers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPickUpPlayers();
            }
        }  else if (builder.hasPickUpPlayers()) {
            // 清理PickUpPlayers
            builder.clearPickUpPlayers();
            fieldCnt++;
        }
        if (this.getPickUpTimes() != 0) {
            builder.setPickUpTimes(this.getPickUpTimes());
            fieldCnt++;
        }  else if (builder.hasPickUpTimes()) {
            // 清理PickUpTimes
            builder.clearPickUpTimes();
            fieldCnt++;
        }
        if (this.getBornTime() != 0L) {
            builder.setBornTime(this.getBornTime());
            fieldCnt++;
        }  else if (builder.hasBornTime()) {
            // 清理BornTime
            builder.clearBornTime();
            fieldCnt++;
        }
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.pickUpArmy != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.pickUpArmy.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPickUpArmy(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPickUpArmy();
            }
        }  else if (builder.hasPickUpArmy()) {
            // 清理PickUpArmy
            builder.clearPickUpArmy();
            fieldCnt++;
        }
        if (this.itemReward != null) {
            Struct.ItemList.Builder tmpBuilder = Struct.ItemList.newBuilder();
            final int tmpFieldCnt = this.itemReward.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItemReward(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItemReward();
            }
        }  else if (builder.hasItemReward()) {
            // 清理ItemReward
            builder.clearItemReward();
            fieldCnt++;
        }
        if (this.groupLimit != null) {
            Struct.DropGroupLimit.Builder tmpBuilder = Struct.DropGroupLimit.newBuilder();
            final int tmpFieldCnt = this.groupLimit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGroupLimit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGroupLimit();
            }
        }  else if (builder.hasGroupLimit()) {
            // 清理GroupLimit
            builder.clearGroupLimit();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(DropObjectEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToDb(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_PICKUPPLAYERS) && this.pickUpPlayers != null) {
            final boolean needClear = !builder.hasPickUpPlayers();
            final int tmpFieldCnt = this.pickUpPlayers.copyChangeToDb(builder.getPickUpPlayersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPickUpPlayers();
            }
        }
        if (this.hasMark(FIELD_INDEX_PICKUPTIMES)) {
            builder.setPickUpTimes(this.getPickUpTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BORNTIME)) {
            builder.setBornTime(this.getBornTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICKUPARMY) && this.pickUpArmy != null) {
            final boolean needClear = !builder.hasPickUpArmy();
            final int tmpFieldCnt = this.pickUpArmy.copyChangeToDb(builder.getPickUpArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPickUpArmy();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            final boolean needClear = !builder.hasItemReward();
            final int tmpFieldCnt = this.itemReward.copyChangeToDb(builder.getItemRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemReward();
            }
        }
        if (this.hasMark(FIELD_INDEX_GROUPLIMIT) && this.groupLimit != null) {
            final boolean needClear = !builder.hasGroupLimit();
            final int tmpFieldCnt = this.groupLimit.copyChangeToDb(builder.getGroupLimitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGroupLimit();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(DropObjectEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromDb(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromDb(proto.getPoint());
            }
        }
        if (proto.hasPickUpPlayers()) {
            this.getPickUpPlayers().mergeFromDb(proto.getPickUpPlayers());
        } else {
            if (this.pickUpPlayers != null) {
                this.pickUpPlayers.mergeFromDb(proto.getPickUpPlayers());
            }
        }
        if (proto.hasPickUpTimes()) {
            this.innerSetPickUpTimes(proto.getPickUpTimes());
        } else {
            this.innerSetPickUpTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBornTime()) {
            this.innerSetBornTime(proto.getBornTime());
        } else {
            this.innerSetBornTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPickUpArmy()) {
            this.getPickUpArmy().mergeFromDb(proto.getPickUpArmy());
        } else {
            if (this.pickUpArmy != null) {
                this.pickUpArmy.mergeFromDb(proto.getPickUpArmy());
            }
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeFromDb(proto.getItemReward());
        } else {
            if (this.itemReward != null) {
                this.itemReward.mergeFromDb(proto.getItemReward());
            }
        }
        if (proto.hasGroupLimit()) {
            this.getGroupLimit().mergeFromDb(proto.getGroupLimit());
        } else {
            if (this.groupLimit != null) {
                this.groupLimit.mergeFromDb(proto.getGroupLimit());
            }
        }
        this.markAll();
        return DropObjectProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(DropObjectEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromDb(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasPickUpPlayers()) {
            this.getPickUpPlayers().mergeChangeFromDb(proto.getPickUpPlayers());
            fieldCnt++;
        }
        if (proto.hasPickUpTimes()) {
            this.setPickUpTimes(proto.getPickUpTimes());
            fieldCnt++;
        }
        if (proto.hasBornTime()) {
            this.setBornTime(proto.getBornTime());
            fieldCnt++;
        }
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasPickUpArmy()) {
            this.getPickUpArmy().mergeChangeFromDb(proto.getPickUpArmy());
            fieldCnt++;
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeChangeFromDb(proto.getItemReward());
            fieldCnt++;
        }
        if (proto.hasGroupLimit()) {
            this.getGroupLimit().mergeChangeFromDb(proto.getGroupLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DropObjectEntity.Builder getCopySsBuilder() {
        final DropObjectEntity.Builder builder = DropObjectEntity.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(DropObjectEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.pickUpPlayers != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.pickUpPlayers.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPickUpPlayers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPickUpPlayers();
            }
        }  else if (builder.hasPickUpPlayers()) {
            // 清理PickUpPlayers
            builder.clearPickUpPlayers();
            fieldCnt++;
        }
        if (this.getPickUpTimes() != 0) {
            builder.setPickUpTimes(this.getPickUpTimes());
            fieldCnt++;
        }  else if (builder.hasPickUpTimes()) {
            // 清理PickUpTimes
            builder.clearPickUpTimes();
            fieldCnt++;
        }
        if (this.getBornTime() != 0L) {
            builder.setBornTime(this.getBornTime());
            fieldCnt++;
        }  else if (builder.hasBornTime()) {
            // 清理BornTime
            builder.clearBornTime();
            fieldCnt++;
        }
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.pickUpArmy != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.pickUpArmy.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPickUpArmy(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPickUpArmy();
            }
        }  else if (builder.hasPickUpArmy()) {
            // 清理PickUpArmy
            builder.clearPickUpArmy();
            fieldCnt++;
        }
        if (this.itemReward != null) {
            Struct.ItemList.Builder tmpBuilder = Struct.ItemList.newBuilder();
            final int tmpFieldCnt = this.itemReward.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItemReward(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItemReward();
            }
        }  else if (builder.hasItemReward()) {
            // 清理ItemReward
            builder.clearItemReward();
            fieldCnt++;
        }
        if (this.groupLimit != null) {
            Struct.DropGroupLimit.Builder tmpBuilder = Struct.DropGroupLimit.newBuilder();
            final int tmpFieldCnt = this.groupLimit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGroupLimit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGroupLimit();
            }
        }  else if (builder.hasGroupLimit()) {
            // 清理GroupLimit
            builder.clearGroupLimit();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(DropObjectEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToSs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_PICKUPPLAYERS) && this.pickUpPlayers != null) {
            final boolean needClear = !builder.hasPickUpPlayers();
            final int tmpFieldCnt = this.pickUpPlayers.copyChangeToSs(builder.getPickUpPlayersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPickUpPlayers();
            }
        }
        if (this.hasMark(FIELD_INDEX_PICKUPTIMES)) {
            builder.setPickUpTimes(this.getPickUpTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BORNTIME)) {
            builder.setBornTime(this.getBornTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICKUPARMY) && this.pickUpArmy != null) {
            final boolean needClear = !builder.hasPickUpArmy();
            final int tmpFieldCnt = this.pickUpArmy.copyChangeToSs(builder.getPickUpArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPickUpArmy();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            final boolean needClear = !builder.hasItemReward();
            final int tmpFieldCnt = this.itemReward.copyChangeToSs(builder.getItemRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemReward();
            }
        }
        if (this.hasMark(FIELD_INDEX_GROUPLIMIT) && this.groupLimit != null) {
            final boolean needClear = !builder.hasGroupLimit();
            final int tmpFieldCnt = this.groupLimit.copyChangeToSs(builder.getGroupLimitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGroupLimit();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(DropObjectEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromSs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromSs(proto.getPoint());
            }
        }
        if (proto.hasPickUpPlayers()) {
            this.getPickUpPlayers().mergeFromSs(proto.getPickUpPlayers());
        } else {
            if (this.pickUpPlayers != null) {
                this.pickUpPlayers.mergeFromSs(proto.getPickUpPlayers());
            }
        }
        if (proto.hasPickUpTimes()) {
            this.innerSetPickUpTimes(proto.getPickUpTimes());
        } else {
            this.innerSetPickUpTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBornTime()) {
            this.innerSetBornTime(proto.getBornTime());
        } else {
            this.innerSetBornTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPickUpArmy()) {
            this.getPickUpArmy().mergeFromSs(proto.getPickUpArmy());
        } else {
            if (this.pickUpArmy != null) {
                this.pickUpArmy.mergeFromSs(proto.getPickUpArmy());
            }
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeFromSs(proto.getItemReward());
        } else {
            if (this.itemReward != null) {
                this.itemReward.mergeFromSs(proto.getItemReward());
            }
        }
        if (proto.hasGroupLimit()) {
            this.getGroupLimit().mergeFromSs(proto.getGroupLimit());
        } else {
            if (this.groupLimit != null) {
                this.groupLimit.mergeFromSs(proto.getGroupLimit());
            }
        }
        this.markAll();
        return DropObjectProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(DropObjectEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromSs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasPickUpPlayers()) {
            this.getPickUpPlayers().mergeChangeFromSs(proto.getPickUpPlayers());
            fieldCnt++;
        }
        if (proto.hasPickUpTimes()) {
            this.setPickUpTimes(proto.getPickUpTimes());
            fieldCnt++;
        }
        if (proto.hasBornTime()) {
            this.setBornTime(proto.getBornTime());
            fieldCnt++;
        }
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasPickUpArmy()) {
            this.getPickUpArmy().mergeChangeFromSs(proto.getPickUpArmy());
            fieldCnt++;
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeChangeFromSs(proto.getItemReward());
            fieldCnt++;
        }
        if (proto.hasGroupLimit()) {
            this.getGroupLimit().mergeChangeFromSs(proto.getGroupLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        DropObjectEntity.Builder builder = DropObjectEntity.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            this.point.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PICKUPPLAYERS) && this.pickUpPlayers != null) {
            this.pickUpPlayers.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PICKUPARMY) && this.pickUpArmy != null) {
            this.pickUpArmy.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            this.itemReward.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_GROUPLIMIT) && this.groupLimit != null) {
            this.groupLimit.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.point != null) {
            this.point.markAll();
        }
        if (this.pickUpPlayers != null) {
            this.pickUpPlayers.markAll();
        }
        if (this.pickUpArmy != null) {
            this.pickUpArmy.markAll();
        }
        if (this.itemReward != null) {
            this.itemReward.markAll();
        }
        if (this.groupLimit != null) {
            this.groupLimit.markAll();
        }
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("DropObjectProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("DropObjectProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof DropObjectProp)) {
            return false;
        }
        final DropObjectProp otherNode = (DropObjectProp) node;
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        if (!this.getPoint().compareDataTo(otherNode.getPoint())) {
            return false;
        }
        if (!this.getPickUpPlayers().compareDataTo(otherNode.getPickUpPlayers())) {
            return false;
        }
        if (this.pickUpTimes != otherNode.pickUpTimes) {
            return false;
        }
        if (this.bornTime != otherNode.bornTime) {
            return false;
        }
        if (this.ownerId != otherNode.ownerId) {
            return false;
        }
        if (!this.getPickUpArmy().compareDataTo(otherNode.getPickUpArmy())) {
            return false;
        }
        if (!this.getItemReward().compareDataTo(otherNode.getItemReward())) {
            return false;
        }
        if (!this.getGroupLimit().compareDataTo(otherNode.getGroupLimit())) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    public static DropObjectProp of(DropObjectEntity fullAttrDb, DropObjectEntity changeAttrDb) {
        DropObjectProp prop = new DropObjectProp();
        prop.mergeFromDb(fullAttrDb);
        prop.mergeChangeFromDb(changeAttrDb);
        prop.unMarkAll();
        return prop;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 55;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
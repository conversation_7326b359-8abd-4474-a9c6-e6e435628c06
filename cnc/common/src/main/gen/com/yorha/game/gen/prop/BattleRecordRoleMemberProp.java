package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructBattle.BattleRecordRoleMember;
import com.yorha.proto.Struct;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructBattlePB.BattleRecordRoleMemberPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructBattlePB;


/**
 * <AUTHOR> auto gen
 */
public class BattleRecordRoleMemberProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_ROLEID = 0;
    public static final int FIELD_INDEX_CLANNAME = 1;
    public static final int FIELD_INDEX_PLUNDER = 2;
    public static final int FIELD_INDEX_MAINHERO = 3;
    public static final int FIELD_INDEX_DEPUTYHERO = 4;
    public static final int FIELD_INDEX_SOLDIERDETAIL = 5;
    public static final int FIELD_INDEX_PLANE = 6;
    public static final int FIELD_INDEX_CARDHEAD = 7;

    public static final int FIELD_COUNT = 8;

    private long markBits0 = 0L;

    private long roleId = Constant.DEFAULT_LONG_VALUE;
    private String clanName = Constant.DEFAULT_STR_VALUE;
    private PlunderProp plunder = null;
    private BattleRecordHeroSummaryProp mainHero = null;
    private BattleRecordHeroSummaryProp deputyHero = null;
    private BattleSoldierDetailListProp soldierDetail = null;
    private BattleRecordPlaneSummaryProp plane = null;
    private PlayerCardHeadProp cardHead = null;

    public BattleRecordRoleMemberProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BattleRecordRoleMemberProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get roleId
     *
     * @return roleId value
     */
    public long getRoleId() {
        return this.roleId;
    }

    /**
     * set roleId && set marked
     *
     * @param roleId new value
     * @return current object
     */
    public BattleRecordRoleMemberProp setRoleId(long roleId) {
        if (this.roleId != roleId) {
            this.mark(FIELD_INDEX_ROLEID);
            this.roleId = roleId;
        }
        return this;
    }

    /**
     * inner set roleId
     *
     * @param roleId new value
     */
    private void innerSetRoleId(long roleId) {
        this.roleId = roleId;
    }

    /**
     * get clanName
     *
     * @return clanName value
     */
    public String getClanName() {
        return this.clanName;
    }

    /**
     * set clanName && set marked
     *
     * @param clanName new value
     * @return current object
     */
    public BattleRecordRoleMemberProp setClanName(String clanName) {
        if (clanName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanName, clanName)) {
            this.mark(FIELD_INDEX_CLANNAME);
            this.clanName = clanName;
        }
        return this;
    }

    /**
     * inner set clanName
     *
     * @param clanName new value
     */
    private void innerSetClanName(String clanName) {
        this.clanName = clanName;
    }

    /**
     * get plunder
     *
     * @return plunder value
     */
    public PlunderProp getPlunder() {
        if (this.plunder == null) {
            this.plunder = new PlunderProp(this, FIELD_INDEX_PLUNDER);
        }
        return this.plunder;
    }

    /**
     * get mainHero
     *
     * @return mainHero value
     */
    public BattleRecordHeroSummaryProp getMainHero() {
        if (this.mainHero == null) {
            this.mainHero = new BattleRecordHeroSummaryProp(this, FIELD_INDEX_MAINHERO);
        }
        return this.mainHero;
    }

    /**
     * get deputyHero
     *
     * @return deputyHero value
     */
    public BattleRecordHeroSummaryProp getDeputyHero() {
        if (this.deputyHero == null) {
            this.deputyHero = new BattleRecordHeroSummaryProp(this, FIELD_INDEX_DEPUTYHERO);
        }
        return this.deputyHero;
    }

    /**
     * get soldierDetail
     *
     * @return soldierDetail value
     */
    public BattleSoldierDetailListProp getSoldierDetail() {
        if (this.soldierDetail == null) {
            this.soldierDetail = new BattleSoldierDetailListProp(this, FIELD_INDEX_SOLDIERDETAIL);
        }
        return this.soldierDetail;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addSoldierDetail(BattleSoldierDetailProp v) {
        this.getSoldierDetail().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public BattleSoldierDetailProp getSoldierDetailIndex(int index) {
        return this.getSoldierDetail().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public BattleSoldierDetailProp removeSoldierDetail(BattleSoldierDetailProp v) {
        if (this.soldierDetail == null) {
            return null;
        }
        if(this.soldierDetail.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getSoldierDetailSize() {
        if (this.soldierDetail == null) {
            return 0;
        }
        return this.soldierDetail.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isSoldierDetailEmpty() {
        if (this.soldierDetail == null) {
            return true;
        }
        return this.getSoldierDetail().isEmpty();
    }

    /**
     * clear list
     */
    public void clearSoldierDetail() {
        this.getSoldierDetail().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public BattleSoldierDetailProp removeSoldierDetailIndex(int index) {
        return this.getSoldierDetail().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public BattleSoldierDetailProp setSoldierDetailIndex(int index, BattleSoldierDetailProp v) {
        return this.getSoldierDetail().set(index, v);
    }
    /**
     * get plane
     *
     * @return plane value
     */
    public BattleRecordPlaneSummaryProp getPlane() {
        if (this.plane == null) {
            this.plane = new BattleRecordPlaneSummaryProp(this, FIELD_INDEX_PLANE);
        }
        return this.plane;
    }

    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRoleMemberPB.Builder getCopyCsBuilder() {
        final BattleRecordRoleMemberPB.Builder builder = BattleRecordRoleMemberPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BattleRecordRoleMemberPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRoleId() != 0L) {
            builder.setRoleId(this.getRoleId());
            fieldCnt++;
        }  else if (builder.hasRoleId()) {
            // 清理RoleId
            builder.clearRoleId();
            fieldCnt++;
        }
        if (!this.getClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }  else if (builder.hasClanName()) {
            // 清理ClanName
            builder.clearClanName();
            fieldCnt++;
        }
        if (this.plunder != null) {
            StructBattlePB.PlunderPB.Builder tmpBuilder = StructBattlePB.PlunderPB.newBuilder();
            final int tmpFieldCnt = this.plunder.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlunder(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlunder();
            }
        }  else if (builder.hasPlunder()) {
            // 清理Plunder
            builder.clearPlunder();
            fieldCnt++;
        }
        if (this.mainHero != null) {
            StructBattlePB.BattleRecordHeroSummaryPB.Builder tmpBuilder = StructBattlePB.BattleRecordHeroSummaryPB.newBuilder();
            final int tmpFieldCnt = this.mainHero.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMainHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMainHero();
            }
        }  else if (builder.hasMainHero()) {
            // 清理MainHero
            builder.clearMainHero();
            fieldCnt++;
        }
        if (this.deputyHero != null) {
            StructBattlePB.BattleRecordHeroSummaryPB.Builder tmpBuilder = StructBattlePB.BattleRecordHeroSummaryPB.newBuilder();
            final int tmpFieldCnt = this.deputyHero.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDeputyHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDeputyHero();
            }
        }  else if (builder.hasDeputyHero()) {
            // 清理DeputyHero
            builder.clearDeputyHero();
            fieldCnt++;
        }
        if (this.soldierDetail != null) {
            StructBattlePB.BattleSoldierDetailListPB.Builder tmpBuilder = StructBattlePB.BattleSoldierDetailListPB.newBuilder();
            final int tmpFieldCnt = this.soldierDetail.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldierDetail(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldierDetail();
            }
        }  else if (builder.hasSoldierDetail()) {
            // 清理SoldierDetail
            builder.clearSoldierDetail();
            fieldCnt++;
        }
        if (this.plane != null) {
            StructBattlePB.BattleRecordPlaneSummaryPB.Builder tmpBuilder = StructBattlePB.BattleRecordPlaneSummaryPB.newBuilder();
            final int tmpFieldCnt = this.plane.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlane();
            }
        }  else if (builder.hasPlane()) {
            // 清理Plane
            builder.clearPlane();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BattleRecordRoleMemberPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ROLEID)) {
            builder.setRoleId(this.getRoleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLUNDER) && this.plunder != null) {
            final boolean needClear = !builder.hasPlunder();
            final int tmpFieldCnt = this.plunder.copyChangeToCs(builder.getPlunderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlunder();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAINHERO) && this.mainHero != null) {
            final boolean needClear = !builder.hasMainHero();
            final int tmpFieldCnt = this.mainHero.copyChangeToCs(builder.getMainHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMainHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHERO) && this.deputyHero != null) {
            final boolean needClear = !builder.hasDeputyHero();
            final int tmpFieldCnt = this.deputyHero.copyChangeToCs(builder.getDeputyHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDeputyHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERDETAIL) && this.soldierDetail != null) {
            final boolean needClear = !builder.hasSoldierDetail();
            final int tmpFieldCnt = this.soldierDetail.copyChangeToCs(builder.getSoldierDetailBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldierDetail();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLANE) && this.plane != null) {
            final boolean needClear = !builder.hasPlane();
            final int tmpFieldCnt = this.plane.copyChangeToCs(builder.getPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BattleRecordRoleMemberPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ROLEID)) {
            builder.setRoleId(this.getRoleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLUNDER) && this.plunder != null) {
            final boolean needClear = !builder.hasPlunder();
            final int tmpFieldCnt = this.plunder.copyChangeToAndClearDeleteKeysCs(builder.getPlunderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlunder();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAINHERO) && this.mainHero != null) {
            final boolean needClear = !builder.hasMainHero();
            final int tmpFieldCnt = this.mainHero.copyChangeToAndClearDeleteKeysCs(builder.getMainHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMainHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHERO) && this.deputyHero != null) {
            final boolean needClear = !builder.hasDeputyHero();
            final int tmpFieldCnt = this.deputyHero.copyChangeToAndClearDeleteKeysCs(builder.getDeputyHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDeputyHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERDETAIL) && this.soldierDetail != null) {
            final boolean needClear = !builder.hasSoldierDetail();
            final int tmpFieldCnt = this.soldierDetail.copyChangeToCs(builder.getSoldierDetailBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldierDetail();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLANE) && this.plane != null) {
            final boolean needClear = !builder.hasPlane();
            final int tmpFieldCnt = this.plane.copyChangeToAndClearDeleteKeysCs(builder.getPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToAndClearDeleteKeysCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BattleRecordRoleMemberPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRoleId()) {
            this.innerSetRoleId(proto.getRoleId());
        } else {
            this.innerSetRoleId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanName()) {
            this.innerSetClanName(proto.getClanName());
        } else {
            this.innerSetClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasPlunder()) {
            this.getPlunder().mergeFromCs(proto.getPlunder());
        } else {
            if (this.plunder != null) {
                this.plunder.mergeFromCs(proto.getPlunder());
            }
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeFromCs(proto.getMainHero());
        } else {
            if (this.mainHero != null) {
                this.mainHero.mergeFromCs(proto.getMainHero());
            }
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeFromCs(proto.getDeputyHero());
        } else {
            if (this.deputyHero != null) {
                this.deputyHero.mergeFromCs(proto.getDeputyHero());
            }
        }
        if (proto.hasSoldierDetail()) {
            this.getSoldierDetail().mergeFromCs(proto.getSoldierDetail());
        } else {
            if (this.soldierDetail != null) {
                this.soldierDetail.mergeFromCs(proto.getSoldierDetail());
            }
        }
        if (proto.hasPlane()) {
            this.getPlane().mergeFromCs(proto.getPlane());
        } else {
            if (this.plane != null) {
                this.plane.mergeFromCs(proto.getPlane());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromCs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromCs(proto.getCardHead());
            }
        }
        this.markAll();
        return BattleRecordRoleMemberProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BattleRecordRoleMemberPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRoleId()) {
            this.setRoleId(proto.getRoleId());
            fieldCnt++;
        }
        if (proto.hasClanName()) {
            this.setClanName(proto.getClanName());
            fieldCnt++;
        }
        if (proto.hasPlunder()) {
            this.getPlunder().mergeChangeFromCs(proto.getPlunder());
            fieldCnt++;
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeChangeFromCs(proto.getMainHero());
            fieldCnt++;
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeChangeFromCs(proto.getDeputyHero());
            fieldCnt++;
        }
        if (proto.hasSoldierDetail()) {
            this.getSoldierDetail().mergeChangeFromCs(proto.getSoldierDetail());
            fieldCnt++;
        }
        if (proto.hasPlane()) {
            this.getPlane().mergeChangeFromCs(proto.getPlane());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromCs(proto.getCardHead());
            fieldCnt++;
        }
        return fieldCnt;
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRoleMember.Builder getCopySsBuilder() {
        final BattleRecordRoleMember.Builder builder = BattleRecordRoleMember.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(BattleRecordRoleMember.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRoleId() != 0L) {
            builder.setRoleId(this.getRoleId());
            fieldCnt++;
        }  else if (builder.hasRoleId()) {
            // 清理RoleId
            builder.clearRoleId();
            fieldCnt++;
        }
        if (!this.getClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }  else if (builder.hasClanName()) {
            // 清理ClanName
            builder.clearClanName();
            fieldCnt++;
        }
        if (this.plunder != null) {
            StructBattle.Plunder.Builder tmpBuilder = StructBattle.Plunder.newBuilder();
            final int tmpFieldCnt = this.plunder.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlunder(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlunder();
            }
        }  else if (builder.hasPlunder()) {
            // 清理Plunder
            builder.clearPlunder();
            fieldCnt++;
        }
        if (this.mainHero != null) {
            StructBattle.BattleRecordHeroSummary.Builder tmpBuilder = StructBattle.BattleRecordHeroSummary.newBuilder();
            final int tmpFieldCnt = this.mainHero.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMainHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMainHero();
            }
        }  else if (builder.hasMainHero()) {
            // 清理MainHero
            builder.clearMainHero();
            fieldCnt++;
        }
        if (this.deputyHero != null) {
            StructBattle.BattleRecordHeroSummary.Builder tmpBuilder = StructBattle.BattleRecordHeroSummary.newBuilder();
            final int tmpFieldCnt = this.deputyHero.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDeputyHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDeputyHero();
            }
        }  else if (builder.hasDeputyHero()) {
            // 清理DeputyHero
            builder.clearDeputyHero();
            fieldCnt++;
        }
        if (this.soldierDetail != null) {
            StructBattle.BattleSoldierDetailList.Builder tmpBuilder = StructBattle.BattleSoldierDetailList.newBuilder();
            final int tmpFieldCnt = this.soldierDetail.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldierDetail(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldierDetail();
            }
        }  else if (builder.hasSoldierDetail()) {
            // 清理SoldierDetail
            builder.clearSoldierDetail();
            fieldCnt++;
        }
        if (this.plane != null) {
            StructBattle.BattleRecordPlaneSummary.Builder tmpBuilder = StructBattle.BattleRecordPlaneSummary.newBuilder();
            final int tmpFieldCnt = this.plane.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlane();
            }
        }  else if (builder.hasPlane()) {
            // 清理Plane
            builder.clearPlane();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(BattleRecordRoleMember.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ROLEID)) {
            builder.setRoleId(this.getRoleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLUNDER) && this.plunder != null) {
            final boolean needClear = !builder.hasPlunder();
            final int tmpFieldCnt = this.plunder.copyChangeToSs(builder.getPlunderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlunder();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAINHERO) && this.mainHero != null) {
            final boolean needClear = !builder.hasMainHero();
            final int tmpFieldCnt = this.mainHero.copyChangeToSs(builder.getMainHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMainHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHERO) && this.deputyHero != null) {
            final boolean needClear = !builder.hasDeputyHero();
            final int tmpFieldCnt = this.deputyHero.copyChangeToSs(builder.getDeputyHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDeputyHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERDETAIL) && this.soldierDetail != null) {
            final boolean needClear = !builder.hasSoldierDetail();
            final int tmpFieldCnt = this.soldierDetail.copyChangeToSs(builder.getSoldierDetailBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldierDetail();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLANE) && this.plane != null) {
            final boolean needClear = !builder.hasPlane();
            final int tmpFieldCnt = this.plane.copyChangeToSs(builder.getPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(BattleRecordRoleMember proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRoleId()) {
            this.innerSetRoleId(proto.getRoleId());
        } else {
            this.innerSetRoleId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanName()) {
            this.innerSetClanName(proto.getClanName());
        } else {
            this.innerSetClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasPlunder()) {
            this.getPlunder().mergeFromSs(proto.getPlunder());
        } else {
            if (this.plunder != null) {
                this.plunder.mergeFromSs(proto.getPlunder());
            }
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeFromSs(proto.getMainHero());
        } else {
            if (this.mainHero != null) {
                this.mainHero.mergeFromSs(proto.getMainHero());
            }
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeFromSs(proto.getDeputyHero());
        } else {
            if (this.deputyHero != null) {
                this.deputyHero.mergeFromSs(proto.getDeputyHero());
            }
        }
        if (proto.hasSoldierDetail()) {
            this.getSoldierDetail().mergeFromSs(proto.getSoldierDetail());
        } else {
            if (this.soldierDetail != null) {
                this.soldierDetail.mergeFromSs(proto.getSoldierDetail());
            }
        }
        if (proto.hasPlane()) {
            this.getPlane().mergeFromSs(proto.getPlane());
        } else {
            if (this.plane != null) {
                this.plane.mergeFromSs(proto.getPlane());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        this.markAll();
        return BattleRecordRoleMemberProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(BattleRecordRoleMember proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRoleId()) {
            this.setRoleId(proto.getRoleId());
            fieldCnt++;
        }
        if (proto.hasClanName()) {
            this.setClanName(proto.getClanName());
            fieldCnt++;
        }
        if (proto.hasPlunder()) {
            this.getPlunder().mergeChangeFromSs(proto.getPlunder());
            fieldCnt++;
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeChangeFromSs(proto.getMainHero());
            fieldCnt++;
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeChangeFromSs(proto.getDeputyHero());
            fieldCnt++;
        }
        if (proto.hasSoldierDetail()) {
            this.getSoldierDetail().mergeChangeFromSs(proto.getSoldierDetail());
            fieldCnt++;
        }
        if (proto.hasPlane()) {
            this.getPlane().mergeChangeFromSs(proto.getPlane());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        BattleRecordRoleMember.Builder builder = BattleRecordRoleMember.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_PLUNDER) && this.plunder != null) {
            this.plunder.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_MAINHERO) && this.mainHero != null) {
            this.mainHero.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHERO) && this.deputyHero != null) {
            this.deputyHero.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERDETAIL) && this.soldierDetail != null) {
            this.soldierDetail.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLANE) && this.plane != null) {
            this.plane.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.plunder != null) {
            this.plunder.markAll();
        }
        if (this.mainHero != null) {
            this.mainHero.markAll();
        }
        if (this.deputyHero != null) {
            this.deputyHero.markAll();
        }
        if (this.soldierDetail != null) {
            this.soldierDetail.markAll();
        }
        if (this.plane != null) {
            this.plane.markAll();
        }
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.roleId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BattleRecordRoleMemberProp)) {
            return false;
        }
        final BattleRecordRoleMemberProp otherNode = (BattleRecordRoleMemberProp) node;
        if (this.roleId != otherNode.roleId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanName, otherNode.clanName)) {
            return false;
        }
        if (!this.getPlunder().compareDataTo(otherNode.getPlunder())) {
            return false;
        }
        if (!this.getMainHero().compareDataTo(otherNode.getMainHero())) {
            return false;
        }
        if (!this.getDeputyHero().compareDataTo(otherNode.getDeputyHero())) {
            return false;
        }
        if (!this.getSoldierDetail().compareDataTo(otherNode.getSoldierDetail())) {
            return false;
        }
        if (!this.getPlane().compareDataTo(otherNode.getPlane())) {
            return false;
        }
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 56;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityBestCommanderDailyData;
import com.yorha.proto.StructPB.ActivityBestCommanderDailyDataPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityBestCommanderDailyDataProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ACTID = 0;
    public static final int FIELD_INDEX_MYRANK = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int actId = Constant.DEFAULT_INT_VALUE;
    private int myRank = Constant.DEFAULT_INT_VALUE;

    public ActivityBestCommanderDailyDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityBestCommanderDailyDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get actId
     *
     * @return actId value
     */
    public int getActId() {
        return this.actId;
    }

    /**
     * set actId && set marked
     *
     * @param actId new value
     * @return current object
     */
    public ActivityBestCommanderDailyDataProp setActId(int actId) {
        if (this.actId != actId) {
            this.mark(FIELD_INDEX_ACTID);
            this.actId = actId;
        }
        return this;
    }

    /**
     * inner set actId
     *
     * @param actId new value
     */
    private void innerSetActId(int actId) {
        this.actId = actId;
    }

    /**
     * get myRank
     *
     * @return myRank value
     */
    public int getMyRank() {
        return this.myRank;
    }

    /**
     * set myRank && set marked
     *
     * @param myRank new value
     * @return current object
     */
    public ActivityBestCommanderDailyDataProp setMyRank(int myRank) {
        if (this.myRank != myRank) {
            this.mark(FIELD_INDEX_MYRANK);
            this.myRank = myRank;
        }
        return this;
    }

    /**
     * inner set myRank
     *
     * @param myRank new value
     */
    private void innerSetMyRank(int myRank) {
        this.myRank = myRank;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityBestCommanderDailyDataPB.Builder getCopyCsBuilder() {
        final ActivityBestCommanderDailyDataPB.Builder builder = ActivityBestCommanderDailyDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityBestCommanderDailyDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActId() != 0) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }  else if (builder.hasActId()) {
            // 清理ActId
            builder.clearActId();
            fieldCnt++;
        }
        if (this.getMyRank() != 0) {
            builder.setMyRank(this.getMyRank());
            fieldCnt++;
        }  else if (builder.hasMyRank()) {
            // 清理MyRank
            builder.clearMyRank();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityBestCommanderDailyDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MYRANK)) {
            builder.setMyRank(this.getMyRank());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityBestCommanderDailyDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MYRANK)) {
            builder.setMyRank(this.getMyRank());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityBestCommanderDailyDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActId()) {
            this.innerSetActId(proto.getActId());
        } else {
            this.innerSetActId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMyRank()) {
            this.innerSetMyRank(proto.getMyRank());
        } else {
            this.innerSetMyRank(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivityBestCommanderDailyDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityBestCommanderDailyDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActId()) {
            this.setActId(proto.getActId());
            fieldCnt++;
        }
        if (proto.hasMyRank()) {
            this.setMyRank(proto.getMyRank());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityBestCommanderDailyData.Builder getCopyDbBuilder() {
        final ActivityBestCommanderDailyData.Builder builder = ActivityBestCommanderDailyData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityBestCommanderDailyData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActId() != 0) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }  else if (builder.hasActId()) {
            // 清理ActId
            builder.clearActId();
            fieldCnt++;
        }
        if (this.getMyRank() != 0) {
            builder.setMyRank(this.getMyRank());
            fieldCnt++;
        }  else if (builder.hasMyRank()) {
            // 清理MyRank
            builder.clearMyRank();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityBestCommanderDailyData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MYRANK)) {
            builder.setMyRank(this.getMyRank());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityBestCommanderDailyData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActId()) {
            this.innerSetActId(proto.getActId());
        } else {
            this.innerSetActId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMyRank()) {
            this.innerSetMyRank(proto.getMyRank());
        } else {
            this.innerSetMyRank(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivityBestCommanderDailyDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityBestCommanderDailyData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActId()) {
            this.setActId(proto.getActId());
            fieldCnt++;
        }
        if (proto.hasMyRank()) {
            this.setMyRank(proto.getMyRank());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityBestCommanderDailyData.Builder getCopySsBuilder() {
        final ActivityBestCommanderDailyData.Builder builder = ActivityBestCommanderDailyData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityBestCommanderDailyData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActId() != 0) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }  else if (builder.hasActId()) {
            // 清理ActId
            builder.clearActId();
            fieldCnt++;
        }
        if (this.getMyRank() != 0) {
            builder.setMyRank(this.getMyRank());
            fieldCnt++;
        }  else if (builder.hasMyRank()) {
            // 清理MyRank
            builder.clearMyRank();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityBestCommanderDailyData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MYRANK)) {
            builder.setMyRank(this.getMyRank());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityBestCommanderDailyData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActId()) {
            this.innerSetActId(proto.getActId());
        } else {
            this.innerSetActId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMyRank()) {
            this.innerSetMyRank(proto.getMyRank());
        } else {
            this.innerSetMyRank(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivityBestCommanderDailyDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityBestCommanderDailyData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActId()) {
            this.setActId(proto.getActId());
            fieldCnt++;
        }
        if (proto.hasMyRank()) {
            this.setMyRank(proto.getMyRank());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityBestCommanderDailyData.Builder builder = ActivityBestCommanderDailyData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.actId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityBestCommanderDailyDataProp)) {
            return false;
        }
        final ActivityBestCommanderDailyDataProp otherNode = (ActivityBestCommanderDailyDataProp) node;
        if (this.actId != otherNode.actId) {
            return false;
        }
        if (this.myRank != otherNode.myRank) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
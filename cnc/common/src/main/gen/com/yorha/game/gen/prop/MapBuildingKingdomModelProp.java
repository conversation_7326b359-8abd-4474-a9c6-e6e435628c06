package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.MapBuilding.MapBuildingKingdomModel;
import com.yorha.proto.Struct;
import com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class MapBuildingKingdomModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_KINGCARDHEAD = 0;
    public static final int FIELD_INDEX_SELECTEDCOLOR = 1;
    public static final int FIELD_INDEX_DESTROYZONEID = 2;
    public static final int FIELD_INDEX_DESTROYCLANNAME = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private PlayerCardHeadProp kingCardHead = null;
    private int selectedColor = Constant.DEFAULT_INT_VALUE;
    private int destroyZoneId = Constant.DEFAULT_INT_VALUE;
    private String destroyClanName = Constant.DEFAULT_STR_VALUE;

    public MapBuildingKingdomModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MapBuildingKingdomModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get kingCardHead
     *
     * @return kingCardHead value
     */
    public PlayerCardHeadProp getKingCardHead() {
        if (this.kingCardHead == null) {
            this.kingCardHead = new PlayerCardHeadProp(this, FIELD_INDEX_KINGCARDHEAD);
        }
        return this.kingCardHead;
    }

    /**
     * get selectedColor
     *
     * @return selectedColor value
     */
    public int getSelectedColor() {
        return this.selectedColor;
    }

    /**
     * set selectedColor && set marked
     *
     * @param selectedColor new value
     * @return current object
     */
    public MapBuildingKingdomModelProp setSelectedColor(int selectedColor) {
        if (this.selectedColor != selectedColor) {
            this.mark(FIELD_INDEX_SELECTEDCOLOR);
            this.selectedColor = selectedColor;
        }
        return this;
    }

    /**
     * inner set selectedColor
     *
     * @param selectedColor new value
     */
    private void innerSetSelectedColor(int selectedColor) {
        this.selectedColor = selectedColor;
    }

    /**
     * get destroyZoneId
     *
     * @return destroyZoneId value
     */
    public int getDestroyZoneId() {
        return this.destroyZoneId;
    }

    /**
     * set destroyZoneId && set marked
     *
     * @param destroyZoneId new value
     * @return current object
     */
    public MapBuildingKingdomModelProp setDestroyZoneId(int destroyZoneId) {
        if (this.destroyZoneId != destroyZoneId) {
            this.mark(FIELD_INDEX_DESTROYZONEID);
            this.destroyZoneId = destroyZoneId;
        }
        return this;
    }

    /**
     * inner set destroyZoneId
     *
     * @param destroyZoneId new value
     */
    private void innerSetDestroyZoneId(int destroyZoneId) {
        this.destroyZoneId = destroyZoneId;
    }

    /**
     * get destroyClanName
     *
     * @return destroyClanName value
     */
    public String getDestroyClanName() {
        return this.destroyClanName;
    }

    /**
     * set destroyClanName && set marked
     *
     * @param destroyClanName new value
     * @return current object
     */
    public MapBuildingKingdomModelProp setDestroyClanName(String destroyClanName) {
        if (destroyClanName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.destroyClanName, destroyClanName)) {
            this.mark(FIELD_INDEX_DESTROYCLANNAME);
            this.destroyClanName = destroyClanName;
        }
        return this;
    }

    /**
     * inner set destroyClanName
     *
     * @param destroyClanName new value
     */
    private void innerSetDestroyClanName(String destroyClanName) {
        this.destroyClanName = destroyClanName;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MapBuildingKingdomModelPB.Builder getCopyCsBuilder() {
        final MapBuildingKingdomModelPB.Builder builder = MapBuildingKingdomModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MapBuildingKingdomModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.kingCardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.kingCardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKingCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKingCardHead();
            }
        }  else if (builder.hasKingCardHead()) {
            // 清理KingCardHead
            builder.clearKingCardHead();
            fieldCnt++;
        }
        if (this.getSelectedColor() != 0) {
            builder.setSelectedColor(this.getSelectedColor());
            fieldCnt++;
        }  else if (builder.hasSelectedColor()) {
            // 清理SelectedColor
            builder.clearSelectedColor();
            fieldCnt++;
        }
        if (this.getDestroyZoneId() != 0) {
            builder.setDestroyZoneId(this.getDestroyZoneId());
            fieldCnt++;
        }  else if (builder.hasDestroyZoneId()) {
            // 清理DestroyZoneId
            builder.clearDestroyZoneId();
            fieldCnt++;
        }
        if (!this.getDestroyClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setDestroyClanName(this.getDestroyClanName());
            fieldCnt++;
        }  else if (builder.hasDestroyClanName()) {
            // 清理DestroyClanName
            builder.clearDestroyClanName();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MapBuildingKingdomModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KINGCARDHEAD) && this.kingCardHead != null) {
            final boolean needClear = !builder.hasKingCardHead();
            final int tmpFieldCnt = this.kingCardHead.copyChangeToCs(builder.getKingCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_SELECTEDCOLOR)) {
            builder.setSelectedColor(this.getSelectedColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DESTROYZONEID)) {
            builder.setDestroyZoneId(this.getDestroyZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DESTROYCLANNAME)) {
            builder.setDestroyClanName(this.getDestroyClanName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MapBuildingKingdomModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KINGCARDHEAD) && this.kingCardHead != null) {
            final boolean needClear = !builder.hasKingCardHead();
            final int tmpFieldCnt = this.kingCardHead.copyChangeToAndClearDeleteKeysCs(builder.getKingCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_SELECTEDCOLOR)) {
            builder.setSelectedColor(this.getSelectedColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DESTROYZONEID)) {
            builder.setDestroyZoneId(this.getDestroyZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DESTROYCLANNAME)) {
            builder.setDestroyClanName(this.getDestroyClanName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MapBuildingKingdomModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKingCardHead()) {
            this.getKingCardHead().mergeFromCs(proto.getKingCardHead());
        } else {
            if (this.kingCardHead != null) {
                this.kingCardHead.mergeFromCs(proto.getKingCardHead());
            }
        }
        if (proto.hasSelectedColor()) {
            this.innerSetSelectedColor(proto.getSelectedColor());
        } else {
            this.innerSetSelectedColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDestroyZoneId()) {
            this.innerSetDestroyZoneId(proto.getDestroyZoneId());
        } else {
            this.innerSetDestroyZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDestroyClanName()) {
            this.innerSetDestroyClanName(proto.getDestroyClanName());
        } else {
            this.innerSetDestroyClanName(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return MapBuildingKingdomModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MapBuildingKingdomModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKingCardHead()) {
            this.getKingCardHead().mergeChangeFromCs(proto.getKingCardHead());
            fieldCnt++;
        }
        if (proto.hasSelectedColor()) {
            this.setSelectedColor(proto.getSelectedColor());
            fieldCnt++;
        }
        if (proto.hasDestroyZoneId()) {
            this.setDestroyZoneId(proto.getDestroyZoneId());
            fieldCnt++;
        }
        if (proto.hasDestroyClanName()) {
            this.setDestroyClanName(proto.getDestroyClanName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MapBuildingKingdomModel.Builder getCopyDbBuilder() {
        final MapBuildingKingdomModel.Builder builder = MapBuildingKingdomModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MapBuildingKingdomModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.kingCardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.kingCardHead.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKingCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKingCardHead();
            }
        }  else if (builder.hasKingCardHead()) {
            // 清理KingCardHead
            builder.clearKingCardHead();
            fieldCnt++;
        }
        if (this.getSelectedColor() != 0) {
            builder.setSelectedColor(this.getSelectedColor());
            fieldCnt++;
        }  else if (builder.hasSelectedColor()) {
            // 清理SelectedColor
            builder.clearSelectedColor();
            fieldCnt++;
        }
        if (this.getDestroyZoneId() != 0) {
            builder.setDestroyZoneId(this.getDestroyZoneId());
            fieldCnt++;
        }  else if (builder.hasDestroyZoneId()) {
            // 清理DestroyZoneId
            builder.clearDestroyZoneId();
            fieldCnt++;
        }
        if (!this.getDestroyClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setDestroyClanName(this.getDestroyClanName());
            fieldCnt++;
        }  else if (builder.hasDestroyClanName()) {
            // 清理DestroyClanName
            builder.clearDestroyClanName();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MapBuildingKingdomModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KINGCARDHEAD) && this.kingCardHead != null) {
            final boolean needClear = !builder.hasKingCardHead();
            final int tmpFieldCnt = this.kingCardHead.copyChangeToDb(builder.getKingCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_SELECTEDCOLOR)) {
            builder.setSelectedColor(this.getSelectedColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DESTROYZONEID)) {
            builder.setDestroyZoneId(this.getDestroyZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DESTROYCLANNAME)) {
            builder.setDestroyClanName(this.getDestroyClanName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MapBuildingKingdomModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKingCardHead()) {
            this.getKingCardHead().mergeFromDb(proto.getKingCardHead());
        } else {
            if (this.kingCardHead != null) {
                this.kingCardHead.mergeFromDb(proto.getKingCardHead());
            }
        }
        if (proto.hasSelectedColor()) {
            this.innerSetSelectedColor(proto.getSelectedColor());
        } else {
            this.innerSetSelectedColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDestroyZoneId()) {
            this.innerSetDestroyZoneId(proto.getDestroyZoneId());
        } else {
            this.innerSetDestroyZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDestroyClanName()) {
            this.innerSetDestroyClanName(proto.getDestroyClanName());
        } else {
            this.innerSetDestroyClanName(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return MapBuildingKingdomModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MapBuildingKingdomModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKingCardHead()) {
            this.getKingCardHead().mergeChangeFromDb(proto.getKingCardHead());
            fieldCnt++;
        }
        if (proto.hasSelectedColor()) {
            this.setSelectedColor(proto.getSelectedColor());
            fieldCnt++;
        }
        if (proto.hasDestroyZoneId()) {
            this.setDestroyZoneId(proto.getDestroyZoneId());
            fieldCnt++;
        }
        if (proto.hasDestroyClanName()) {
            this.setDestroyClanName(proto.getDestroyClanName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MapBuildingKingdomModel.Builder getCopySsBuilder() {
        final MapBuildingKingdomModel.Builder builder = MapBuildingKingdomModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MapBuildingKingdomModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.kingCardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.kingCardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKingCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKingCardHead();
            }
        }  else if (builder.hasKingCardHead()) {
            // 清理KingCardHead
            builder.clearKingCardHead();
            fieldCnt++;
        }
        if (this.getSelectedColor() != 0) {
            builder.setSelectedColor(this.getSelectedColor());
            fieldCnt++;
        }  else if (builder.hasSelectedColor()) {
            // 清理SelectedColor
            builder.clearSelectedColor();
            fieldCnt++;
        }
        if (this.getDestroyZoneId() != 0) {
            builder.setDestroyZoneId(this.getDestroyZoneId());
            fieldCnt++;
        }  else if (builder.hasDestroyZoneId()) {
            // 清理DestroyZoneId
            builder.clearDestroyZoneId();
            fieldCnt++;
        }
        if (!this.getDestroyClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setDestroyClanName(this.getDestroyClanName());
            fieldCnt++;
        }  else if (builder.hasDestroyClanName()) {
            // 清理DestroyClanName
            builder.clearDestroyClanName();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MapBuildingKingdomModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KINGCARDHEAD) && this.kingCardHead != null) {
            final boolean needClear = !builder.hasKingCardHead();
            final int tmpFieldCnt = this.kingCardHead.copyChangeToSs(builder.getKingCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_SELECTEDCOLOR)) {
            builder.setSelectedColor(this.getSelectedColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DESTROYZONEID)) {
            builder.setDestroyZoneId(this.getDestroyZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DESTROYCLANNAME)) {
            builder.setDestroyClanName(this.getDestroyClanName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MapBuildingKingdomModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKingCardHead()) {
            this.getKingCardHead().mergeFromSs(proto.getKingCardHead());
        } else {
            if (this.kingCardHead != null) {
                this.kingCardHead.mergeFromSs(proto.getKingCardHead());
            }
        }
        if (proto.hasSelectedColor()) {
            this.innerSetSelectedColor(proto.getSelectedColor());
        } else {
            this.innerSetSelectedColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDestroyZoneId()) {
            this.innerSetDestroyZoneId(proto.getDestroyZoneId());
        } else {
            this.innerSetDestroyZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDestroyClanName()) {
            this.innerSetDestroyClanName(proto.getDestroyClanName());
        } else {
            this.innerSetDestroyClanName(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return MapBuildingKingdomModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MapBuildingKingdomModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKingCardHead()) {
            this.getKingCardHead().mergeChangeFromSs(proto.getKingCardHead());
            fieldCnt++;
        }
        if (proto.hasSelectedColor()) {
            this.setSelectedColor(proto.getSelectedColor());
            fieldCnt++;
        }
        if (proto.hasDestroyZoneId()) {
            this.setDestroyZoneId(proto.getDestroyZoneId());
            fieldCnt++;
        }
        if (proto.hasDestroyClanName()) {
            this.setDestroyClanName(proto.getDestroyClanName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MapBuildingKingdomModel.Builder builder = MapBuildingKingdomModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_KINGCARDHEAD) && this.kingCardHead != null) {
            this.kingCardHead.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.kingCardHead != null) {
            this.kingCardHead.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MapBuildingKingdomModelProp)) {
            return false;
        }
        final MapBuildingKingdomModelProp otherNode = (MapBuildingKingdomModelProp) node;
        if (!this.getKingCardHead().compareDataTo(otherNode.getKingCardHead())) {
            return false;
        }
        if (this.selectedColor != otherNode.selectedColor) {
            return false;
        }
        if (this.destroyZoneId != otherNode.destroyZoneId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.destroyClanName, otherNode.destroyClanName)) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
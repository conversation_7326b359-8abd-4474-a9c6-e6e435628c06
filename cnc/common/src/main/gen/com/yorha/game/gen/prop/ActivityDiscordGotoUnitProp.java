package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityDiscordGotoUnit;
import com.yorha.proto.StructPB.ActivityDiscordGotoUnitPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityDiscordGotoUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_REWARDTAKEN = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private boolean rewardTaken = Constant.DEFAULT_BOOLEAN_VALUE;

    public ActivityDiscordGotoUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityDiscordGotoUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get rewardTaken
     *
     * @return rewardTaken value
     */
    public boolean getRewardTaken() {
        return this.rewardTaken;
    }

    /**
     * set rewardTaken && set marked
     *
     * @param rewardTaken new value
     * @return current object
     */
    public ActivityDiscordGotoUnitProp setRewardTaken(boolean rewardTaken) {
        if (this.rewardTaken != rewardTaken) {
            this.mark(FIELD_INDEX_REWARDTAKEN);
            this.rewardTaken = rewardTaken;
        }
        return this;
    }

    /**
     * inner set rewardTaken
     *
     * @param rewardTaken new value
     */
    private void innerSetRewardTaken(boolean rewardTaken) {
        this.rewardTaken = rewardTaken;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityDiscordGotoUnitPB.Builder getCopyCsBuilder() {
        final ActivityDiscordGotoUnitPB.Builder builder = ActivityDiscordGotoUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityDiscordGotoUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRewardTaken()) {
            builder.setRewardTaken(this.getRewardTaken());
            fieldCnt++;
        }  else if (builder.hasRewardTaken()) {
            // 清理RewardTaken
            builder.clearRewardTaken();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityDiscordGotoUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDTAKEN)) {
            builder.setRewardTaken(this.getRewardTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityDiscordGotoUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDTAKEN)) {
            builder.setRewardTaken(this.getRewardTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityDiscordGotoUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardTaken()) {
            this.innerSetRewardTaken(proto.getRewardTaken());
        } else {
            this.innerSetRewardTaken(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ActivityDiscordGotoUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityDiscordGotoUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardTaken()) {
            this.setRewardTaken(proto.getRewardTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityDiscordGotoUnit.Builder getCopyDbBuilder() {
        final ActivityDiscordGotoUnit.Builder builder = ActivityDiscordGotoUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityDiscordGotoUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRewardTaken()) {
            builder.setRewardTaken(this.getRewardTaken());
            fieldCnt++;
        }  else if (builder.hasRewardTaken()) {
            // 清理RewardTaken
            builder.clearRewardTaken();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityDiscordGotoUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDTAKEN)) {
            builder.setRewardTaken(this.getRewardTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityDiscordGotoUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardTaken()) {
            this.innerSetRewardTaken(proto.getRewardTaken());
        } else {
            this.innerSetRewardTaken(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ActivityDiscordGotoUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityDiscordGotoUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardTaken()) {
            this.setRewardTaken(proto.getRewardTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityDiscordGotoUnit.Builder getCopySsBuilder() {
        final ActivityDiscordGotoUnit.Builder builder = ActivityDiscordGotoUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityDiscordGotoUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRewardTaken()) {
            builder.setRewardTaken(this.getRewardTaken());
            fieldCnt++;
        }  else if (builder.hasRewardTaken()) {
            // 清理RewardTaken
            builder.clearRewardTaken();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityDiscordGotoUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDTAKEN)) {
            builder.setRewardTaken(this.getRewardTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityDiscordGotoUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardTaken()) {
            this.innerSetRewardTaken(proto.getRewardTaken());
        } else {
            this.innerSetRewardTaken(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ActivityDiscordGotoUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityDiscordGotoUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardTaken()) {
            this.setRewardTaken(proto.getRewardTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityDiscordGotoUnit.Builder builder = ActivityDiscordGotoUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityDiscordGotoUnitProp)) {
            return false;
        }
        final ActivityDiscordGotoUnitProp otherNode = (ActivityDiscordGotoUnitProp) node;
        if (this.rewardTaken != otherNode.rewardTaken) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Clan.ClanActCommonScore;
import com.yorha.proto.ClanPB.ClanActCommonScorePB;


/**
 * <AUTHOR> auto gen
 */
public class ClanActCommonScoreProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CURACTID = 0;
    public static final int FIELD_INDEX_SCORE = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private long curActId = Constant.DEFAULT_LONG_VALUE;
    private long score = Constant.DEFAULT_LONG_VALUE;

    public ClanActCommonScoreProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanActCommonScoreProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get curActId
     *
     * @return curActId value
     */
    public long getCurActId() {
        return this.curActId;
    }

    /**
     * set curActId && set marked
     *
     * @param curActId new value
     * @return current object
     */
    public ClanActCommonScoreProp setCurActId(long curActId) {
        if (this.curActId != curActId) {
            this.mark(FIELD_INDEX_CURACTID);
            this.curActId = curActId;
        }
        return this;
    }

    /**
     * inner set curActId
     *
     * @param curActId new value
     */
    private void innerSetCurActId(long curActId) {
        this.curActId = curActId;
    }

    /**
     * get score
     *
     * @return score value
     */
    public long getScore() {
        return this.score;
    }

    /**
     * set score && set marked
     *
     * @param score new value
     * @return current object
     */
    public ClanActCommonScoreProp setScore(long score) {
        if (this.score != score) {
            this.mark(FIELD_INDEX_SCORE);
            this.score = score;
        }
        return this;
    }

    /**
     * inner set score
     *
     * @param score new value
     */
    private void innerSetScore(long score) {
        this.score = score;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanActCommonScorePB.Builder getCopyCsBuilder() {
        final ClanActCommonScorePB.Builder builder = ClanActCommonScorePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanActCommonScorePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurActId() != 0L) {
            builder.setCurActId(this.getCurActId());
            fieldCnt++;
        }  else if (builder.hasCurActId()) {
            // 清理CurActId
            builder.clearCurActId();
            fieldCnt++;
        }
        if (this.getScore() != 0L) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanActCommonScorePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURACTID)) {
            builder.setCurActId(this.getCurActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanActCommonScorePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURACTID)) {
            builder.setCurActId(this.getCurActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanActCommonScorePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurActId()) {
            this.innerSetCurActId(proto.getCurActId());
        } else {
            this.innerSetCurActId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanActCommonScoreProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanActCommonScorePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurActId()) {
            this.setCurActId(proto.getCurActId());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanActCommonScore.Builder getCopyDbBuilder() {
        final ClanActCommonScore.Builder builder = ClanActCommonScore.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanActCommonScore.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurActId() != 0L) {
            builder.setCurActId(this.getCurActId());
            fieldCnt++;
        }  else if (builder.hasCurActId()) {
            // 清理CurActId
            builder.clearCurActId();
            fieldCnt++;
        }
        if (this.getScore() != 0L) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanActCommonScore.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURACTID)) {
            builder.setCurActId(this.getCurActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanActCommonScore proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurActId()) {
            this.innerSetCurActId(proto.getCurActId());
        } else {
            this.innerSetCurActId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanActCommonScoreProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanActCommonScore proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurActId()) {
            this.setCurActId(proto.getCurActId());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanActCommonScore.Builder getCopySsBuilder() {
        final ClanActCommonScore.Builder builder = ClanActCommonScore.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanActCommonScore.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurActId() != 0L) {
            builder.setCurActId(this.getCurActId());
            fieldCnt++;
        }  else if (builder.hasCurActId()) {
            // 清理CurActId
            builder.clearCurActId();
            fieldCnt++;
        }
        if (this.getScore() != 0L) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanActCommonScore.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURACTID)) {
            builder.setCurActId(this.getCurActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanActCommonScore proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurActId()) {
            this.innerSetCurActId(proto.getCurActId());
        } else {
            this.innerSetCurActId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanActCommonScoreProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanActCommonScore proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurActId()) {
            this.setCurActId(proto.getCurActId());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanActCommonScore.Builder builder = ClanActCommonScore.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanActCommonScoreProp)) {
            return false;
        }
        final ClanActCommonScoreProp otherNode = (ClanActCommonScoreProp) node;
        if (this.curActId != otherNode.curActId) {
            return false;
        }
        if (this.score != otherNode.score) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
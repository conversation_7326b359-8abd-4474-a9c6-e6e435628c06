package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityChargeGoodsChainUnit;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ActivityChargeGoodsChainUnitPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityChargeGoodsChainUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CHAINS = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32GoodsChainInfoMapProp chains = null;

    public ActivityChargeGoodsChainUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityChargeGoodsChainUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get chains
     *
     * @return chains value
     */
    public Int32GoodsChainInfoMapProp getChains() {
        if (this.chains == null) {
            this.chains = new Int32GoodsChainInfoMapProp(this, FIELD_INDEX_CHAINS);
        }
        return this.chains;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putChainsV(GoodsChainInfoProp v) {
        this.getChains().put(v.getChainId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public GoodsChainInfoProp addEmptyChains(Integer k) {
        return this.getChains().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getChainsSize() {
        if (this.chains == null) {
            return 0;
        }
        return this.chains.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isChainsEmpty() {
        if (this.chains == null) {
            return true;
        }
        return this.chains.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public GoodsChainInfoProp getChainsV(Integer k) {
        if (this.chains == null || !this.chains.containsKey(k)) {
            return null;
        }
        return this.chains.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearChains() {
        if (this.chains != null) {
            this.chains.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeChainsV(Integer k) {
        if (this.chains != null) {
            this.chains.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityChargeGoodsChainUnitPB.Builder getCopyCsBuilder() {
        final ActivityChargeGoodsChainUnitPB.Builder builder = ActivityChargeGoodsChainUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityChargeGoodsChainUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.chains != null) {
            StructPB.Int32GoodsChainInfoMapPB.Builder tmpBuilder = StructPB.Int32GoodsChainInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.chains.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChains(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChains();
            }
        }  else if (builder.hasChains()) {
            // 清理Chains
            builder.clearChains();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityChargeGoodsChainUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHAINS) && this.chains != null) {
            final boolean needClear = !builder.hasChains();
            final int tmpFieldCnt = this.chains.copyChangeToCs(builder.getChainsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChains();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityChargeGoodsChainUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHAINS) && this.chains != null) {
            final boolean needClear = !builder.hasChains();
            final int tmpFieldCnt = this.chains.copyChangeToAndClearDeleteKeysCs(builder.getChainsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChains();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityChargeGoodsChainUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChains()) {
            this.getChains().mergeFromCs(proto.getChains());
        } else {
            if (this.chains != null) {
                this.chains.mergeFromCs(proto.getChains());
            }
        }
        this.markAll();
        return ActivityChargeGoodsChainUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityChargeGoodsChainUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChains()) {
            this.getChains().mergeChangeFromCs(proto.getChains());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityChargeGoodsChainUnit.Builder getCopyDbBuilder() {
        final ActivityChargeGoodsChainUnit.Builder builder = ActivityChargeGoodsChainUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityChargeGoodsChainUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.chains != null) {
            Struct.Int32GoodsChainInfoMap.Builder tmpBuilder = Struct.Int32GoodsChainInfoMap.newBuilder();
            final int tmpFieldCnt = this.chains.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChains(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChains();
            }
        }  else if (builder.hasChains()) {
            // 清理Chains
            builder.clearChains();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityChargeGoodsChainUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHAINS) && this.chains != null) {
            final boolean needClear = !builder.hasChains();
            final int tmpFieldCnt = this.chains.copyChangeToDb(builder.getChainsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChains();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityChargeGoodsChainUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChains()) {
            this.getChains().mergeFromDb(proto.getChains());
        } else {
            if (this.chains != null) {
                this.chains.mergeFromDb(proto.getChains());
            }
        }
        this.markAll();
        return ActivityChargeGoodsChainUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityChargeGoodsChainUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChains()) {
            this.getChains().mergeChangeFromDb(proto.getChains());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityChargeGoodsChainUnit.Builder getCopySsBuilder() {
        final ActivityChargeGoodsChainUnit.Builder builder = ActivityChargeGoodsChainUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityChargeGoodsChainUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.chains != null) {
            Struct.Int32GoodsChainInfoMap.Builder tmpBuilder = Struct.Int32GoodsChainInfoMap.newBuilder();
            final int tmpFieldCnt = this.chains.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChains(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChains();
            }
        }  else if (builder.hasChains()) {
            // 清理Chains
            builder.clearChains();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityChargeGoodsChainUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHAINS) && this.chains != null) {
            final boolean needClear = !builder.hasChains();
            final int tmpFieldCnt = this.chains.copyChangeToSs(builder.getChainsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChains();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityChargeGoodsChainUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChains()) {
            this.getChains().mergeFromSs(proto.getChains());
        } else {
            if (this.chains != null) {
                this.chains.mergeFromSs(proto.getChains());
            }
        }
        this.markAll();
        return ActivityChargeGoodsChainUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityChargeGoodsChainUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChains()) {
            this.getChains().mergeChangeFromSs(proto.getChains());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityChargeGoodsChainUnit.Builder builder = ActivityChargeGoodsChainUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CHAINS) && this.chains != null) {
            this.chains.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.chains != null) {
            this.chains.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityChargeGoodsChainUnitProp)) {
            return false;
        }
        final ActivityChargeGoodsChainUnitProp otherNode = (ActivityChargeGoodsChainUnitProp) node;
        if (!this.getChains().compareDataTo(otherNode.getChains())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
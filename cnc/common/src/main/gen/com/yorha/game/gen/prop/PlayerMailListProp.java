package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructMailPB.PlayerMailListPB;
import com.yorha.proto.StructMail.PlayerMailList;
import com.yorha.proto.StructMailPB.PlayerMailPB;
import com.yorha.proto.StructMail.PlayerMail;

/**
 * <AUTHOR> auto gen
 */
public class PlayerMailListProp extends AbstractListNode<PlayerMailProp> {
    /**
     * Create a PlayerMailListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public PlayerMailListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to PlayerMailListProp
     *
     * @return new object
     */
    @Override
    public PlayerMailProp addEmptyValue() {
        final PlayerMailProp newProp = new PlayerMailProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMailListPB.Builder getCopyCsBuilder() {
        final PlayerMailListPB.Builder builder = PlayerMailListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(PlayerMailListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return PlayerMailListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final PlayerMailProp v : this) {
            final PlayerMailPB.Builder itemBuilder = PlayerMailPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return PlayerMailListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(PlayerMailListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return PlayerMailListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(PlayerMailListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (PlayerMailPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return PlayerMailListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(PlayerMailListPB proto) {
        return mergeFromCs(proto);
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMailList.Builder getCopySsBuilder() {
        final PlayerMailList.Builder builder = PlayerMailList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(PlayerMailList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return PlayerMailListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final PlayerMailProp v : this) {
            final PlayerMail.Builder itemBuilder = PlayerMail.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return PlayerMailListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(PlayerMailList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return PlayerMailListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(PlayerMailList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (PlayerMail v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return PlayerMailListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(PlayerMailList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        PlayerMailList.Builder builder = PlayerMailList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
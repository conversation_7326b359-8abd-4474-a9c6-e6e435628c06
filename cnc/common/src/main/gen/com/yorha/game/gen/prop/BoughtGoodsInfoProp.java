package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.BoughtGoodsInfo;
import com.yorha.proto.PlayerPB.BoughtGoodsInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class BoughtGoodsInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_CONFIGGOODSID = 0;
    public static final int FIELD_INDEX_BOUGHTNUM = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int configGoodsId = Constant.DEFAULT_INT_VALUE;
    private int boughtNum = Constant.DEFAULT_INT_VALUE;

    public BoughtGoodsInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BoughtGoodsInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get configGoodsId
     *
     * @return configGoodsId value
     */
    public int getConfigGoodsId() {
        return this.configGoodsId;
    }

    /**
     * set configGoodsId && set marked
     *
     * @param configGoodsId new value
     * @return current object
     */
    public BoughtGoodsInfoProp setConfigGoodsId(int configGoodsId) {
        if (this.configGoodsId != configGoodsId) {
            this.mark(FIELD_INDEX_CONFIGGOODSID);
            this.configGoodsId = configGoodsId;
        }
        return this;
    }

    /**
     * inner set configGoodsId
     *
     * @param configGoodsId new value
     */
    private void innerSetConfigGoodsId(int configGoodsId) {
        this.configGoodsId = configGoodsId;
    }

    /**
     * get boughtNum
     *
     * @return boughtNum value
     */
    public int getBoughtNum() {
        return this.boughtNum;
    }

    /**
     * set boughtNum && set marked
     *
     * @param boughtNum new value
     * @return current object
     */
    public BoughtGoodsInfoProp setBoughtNum(int boughtNum) {
        if (this.boughtNum != boughtNum) {
            this.mark(FIELD_INDEX_BOUGHTNUM);
            this.boughtNum = boughtNum;
        }
        return this;
    }

    /**
     * inner set boughtNum
     *
     * @param boughtNum new value
     */
    private void innerSetBoughtNum(int boughtNum) {
        this.boughtNum = boughtNum;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BoughtGoodsInfoPB.Builder getCopyCsBuilder() {
        final BoughtGoodsInfoPB.Builder builder = BoughtGoodsInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BoughtGoodsInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getConfigGoodsId() != 0) {
            builder.setConfigGoodsId(this.getConfigGoodsId());
            fieldCnt++;
        }  else if (builder.hasConfigGoodsId()) {
            // 清理ConfigGoodsId
            builder.clearConfigGoodsId();
            fieldCnt++;
        }
        if (this.getBoughtNum() != 0) {
            builder.setBoughtNum(this.getBoughtNum());
            fieldCnt++;
        }  else if (builder.hasBoughtNum()) {
            // 清理BoughtNum
            builder.clearBoughtNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BoughtGoodsInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONFIGGOODSID)) {
            builder.setConfigGoodsId(this.getConfigGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHTNUM)) {
            builder.setBoughtNum(this.getBoughtNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BoughtGoodsInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONFIGGOODSID)) {
            builder.setConfigGoodsId(this.getConfigGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHTNUM)) {
            builder.setBoughtNum(this.getBoughtNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BoughtGoodsInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasConfigGoodsId()) {
            this.innerSetConfigGoodsId(proto.getConfigGoodsId());
        } else {
            this.innerSetConfigGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBoughtNum()) {
            this.innerSetBoughtNum(proto.getBoughtNum());
        } else {
            this.innerSetBoughtNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return BoughtGoodsInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BoughtGoodsInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasConfigGoodsId()) {
            this.setConfigGoodsId(proto.getConfigGoodsId());
            fieldCnt++;
        }
        if (proto.hasBoughtNum()) {
            this.setBoughtNum(proto.getBoughtNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BoughtGoodsInfo.Builder getCopyDbBuilder() {
        final BoughtGoodsInfo.Builder builder = BoughtGoodsInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(BoughtGoodsInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getConfigGoodsId() != 0) {
            builder.setConfigGoodsId(this.getConfigGoodsId());
            fieldCnt++;
        }  else if (builder.hasConfigGoodsId()) {
            // 清理ConfigGoodsId
            builder.clearConfigGoodsId();
            fieldCnt++;
        }
        if (this.getBoughtNum() != 0) {
            builder.setBoughtNum(this.getBoughtNum());
            fieldCnt++;
        }  else if (builder.hasBoughtNum()) {
            // 清理BoughtNum
            builder.clearBoughtNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(BoughtGoodsInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONFIGGOODSID)) {
            builder.setConfigGoodsId(this.getConfigGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHTNUM)) {
            builder.setBoughtNum(this.getBoughtNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(BoughtGoodsInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasConfigGoodsId()) {
            this.innerSetConfigGoodsId(proto.getConfigGoodsId());
        } else {
            this.innerSetConfigGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBoughtNum()) {
            this.innerSetBoughtNum(proto.getBoughtNum());
        } else {
            this.innerSetBoughtNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return BoughtGoodsInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(BoughtGoodsInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasConfigGoodsId()) {
            this.setConfigGoodsId(proto.getConfigGoodsId());
            fieldCnt++;
        }
        if (proto.hasBoughtNum()) {
            this.setBoughtNum(proto.getBoughtNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BoughtGoodsInfo.Builder getCopySsBuilder() {
        final BoughtGoodsInfo.Builder builder = BoughtGoodsInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(BoughtGoodsInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getConfigGoodsId() != 0) {
            builder.setConfigGoodsId(this.getConfigGoodsId());
            fieldCnt++;
        }  else if (builder.hasConfigGoodsId()) {
            // 清理ConfigGoodsId
            builder.clearConfigGoodsId();
            fieldCnt++;
        }
        if (this.getBoughtNum() != 0) {
            builder.setBoughtNum(this.getBoughtNum());
            fieldCnt++;
        }  else if (builder.hasBoughtNum()) {
            // 清理BoughtNum
            builder.clearBoughtNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(BoughtGoodsInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONFIGGOODSID)) {
            builder.setConfigGoodsId(this.getConfigGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHTNUM)) {
            builder.setBoughtNum(this.getBoughtNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(BoughtGoodsInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasConfigGoodsId()) {
            this.innerSetConfigGoodsId(proto.getConfigGoodsId());
        } else {
            this.innerSetConfigGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBoughtNum()) {
            this.innerSetBoughtNum(proto.getBoughtNum());
        } else {
            this.innerSetBoughtNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return BoughtGoodsInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(BoughtGoodsInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasConfigGoodsId()) {
            this.setConfigGoodsId(proto.getConfigGoodsId());
            fieldCnt++;
        }
        if (proto.hasBoughtNum()) {
            this.setBoughtNum(proto.getBoughtNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        BoughtGoodsInfo.Builder builder = BoughtGoodsInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.configGoodsId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BoughtGoodsInfoProp)) {
            return false;
        }
        final BoughtGoodsInfoProp otherNode = (BoughtGoodsInfoProp) node;
        if (this.configGoodsId != otherNode.configGoodsId) {
            return false;
        }
        if (this.boughtNum != otherNode.boughtNum) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
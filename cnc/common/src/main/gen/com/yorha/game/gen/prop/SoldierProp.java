package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.Soldier;
import com.yorha.proto.StructPB.SoldierPB;


/**
 * <AUTHOR> auto gen
 */
public class SoldierProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_SOLDIERID = 0;
    public static final int FIELD_INDEX_NUM = 1;
    public static final int FIELD_INDEX_SLIGHTWOUNDNUM = 2;
    public static final int FIELD_INDEX_SEVEREWOUNDNUM = 3;
    public static final int FIELD_INDEX_DEADNUM = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private int soldierId = Constant.DEFAULT_INT_VALUE;
    private int num = Constant.DEFAULT_INT_VALUE;
    private int slightWoundNum = Constant.DEFAULT_INT_VALUE;
    private int severeWoundNum = Constant.DEFAULT_INT_VALUE;
    private int deadNum = Constant.DEFAULT_INT_VALUE;

    public SoldierProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SoldierProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get soldierId
     *
     * @return soldierId value
     */
    public int getSoldierId() {
        return this.soldierId;
    }

    /**
     * set soldierId && set marked
     *
     * @param soldierId new value
     * @return current object
     */
    public SoldierProp setSoldierId(int soldierId) {
        if (this.soldierId != soldierId) {
            this.mark(FIELD_INDEX_SOLDIERID);
            this.soldierId = soldierId;
        }
        return this;
    }

    /**
     * inner set soldierId
     *
     * @param soldierId new value
     */
    private void innerSetSoldierId(int soldierId) {
        this.soldierId = soldierId;
    }

    /**
     * get num
     *
     * @return num value
     */
    public int getNum() {
        return this.num;
    }

    /**
     * set num && set marked
     *
     * @param num new value
     * @return current object
     */
    public SoldierProp setNum(int num) {
        if (this.num != num) {
            this.mark(FIELD_INDEX_NUM);
            this.num = num;
        }
        return this;
    }

    /**
     * inner set num
     *
     * @param num new value
     */
    private void innerSetNum(int num) {
        this.num = num;
    }

    /**
     * get slightWoundNum
     *
     * @return slightWoundNum value
     */
    public int getSlightWoundNum() {
        return this.slightWoundNum;
    }

    /**
     * set slightWoundNum && set marked
     *
     * @param slightWoundNum new value
     * @return current object
     */
    public SoldierProp setSlightWoundNum(int slightWoundNum) {
        if (this.slightWoundNum != slightWoundNum) {
            this.mark(FIELD_INDEX_SLIGHTWOUNDNUM);
            this.slightWoundNum = slightWoundNum;
        }
        return this;
    }

    /**
     * inner set slightWoundNum
     *
     * @param slightWoundNum new value
     */
    private void innerSetSlightWoundNum(int slightWoundNum) {
        this.slightWoundNum = slightWoundNum;
    }

    /**
     * get severeWoundNum
     *
     * @return severeWoundNum value
     */
    public int getSevereWoundNum() {
        return this.severeWoundNum;
    }

    /**
     * set severeWoundNum && set marked
     *
     * @param severeWoundNum new value
     * @return current object
     */
    public SoldierProp setSevereWoundNum(int severeWoundNum) {
        if (this.severeWoundNum != severeWoundNum) {
            this.mark(FIELD_INDEX_SEVEREWOUNDNUM);
            this.severeWoundNum = severeWoundNum;
        }
        return this;
    }

    /**
     * inner set severeWoundNum
     *
     * @param severeWoundNum new value
     */
    private void innerSetSevereWoundNum(int severeWoundNum) {
        this.severeWoundNum = severeWoundNum;
    }

    /**
     * get deadNum
     *
     * @return deadNum value
     */
    public int getDeadNum() {
        return this.deadNum;
    }

    /**
     * set deadNum && set marked
     *
     * @param deadNum new value
     * @return current object
     */
    public SoldierProp setDeadNum(int deadNum) {
        if (this.deadNum != deadNum) {
            this.mark(FIELD_INDEX_DEADNUM);
            this.deadNum = deadNum;
        }
        return this;
    }

    /**
     * inner set deadNum
     *
     * @param deadNum new value
     */
    private void innerSetDeadNum(int deadNum) {
        this.deadNum = deadNum;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SoldierPB.Builder getCopyCsBuilder() {
        final SoldierPB.Builder builder = SoldierPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SoldierPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSoldierId() != 0) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }  else if (builder.hasSoldierId()) {
            // 清理SoldierId
            builder.clearSoldierId();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        if (this.getSlightWoundNum() != 0) {
            builder.setSlightWoundNum(this.getSlightWoundNum());
            fieldCnt++;
        }  else if (builder.hasSlightWoundNum()) {
            // 清理SlightWoundNum
            builder.clearSlightWoundNum();
            fieldCnt++;
        }
        if (this.getSevereWoundNum() != 0) {
            builder.setSevereWoundNum(this.getSevereWoundNum());
            fieldCnt++;
        }  else if (builder.hasSevereWoundNum()) {
            // 清理SevereWoundNum
            builder.clearSevereWoundNum();
            fieldCnt++;
        }
        if (this.getDeadNum() != 0) {
            builder.setDeadNum(this.getDeadNum());
            fieldCnt++;
        }  else if (builder.hasDeadNum()) {
            // 清理DeadNum
            builder.clearDeadNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SoldierPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERID)) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SLIGHTWOUNDNUM)) {
            builder.setSlightWoundNum(this.getSlightWoundNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SEVEREWOUNDNUM)) {
            builder.setSevereWoundNum(this.getSevereWoundNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEADNUM)) {
            builder.setDeadNum(this.getDeadNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SoldierPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERID)) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SLIGHTWOUNDNUM)) {
            builder.setSlightWoundNum(this.getSlightWoundNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SEVEREWOUNDNUM)) {
            builder.setSevereWoundNum(this.getSevereWoundNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEADNUM)) {
            builder.setDeadNum(this.getDeadNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SoldierPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierId()) {
            this.innerSetSoldierId(proto.getSoldierId());
        } else {
            this.innerSetSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSlightWoundNum()) {
            this.innerSetSlightWoundNum(proto.getSlightWoundNum());
        } else {
            this.innerSetSlightWoundNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSevereWoundNum()) {
            this.innerSetSevereWoundNum(proto.getSevereWoundNum());
        } else {
            this.innerSetSevereWoundNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDeadNum()) {
            this.innerSetDeadNum(proto.getDeadNum());
        } else {
            this.innerSetDeadNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SoldierProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SoldierPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierId()) {
            this.setSoldierId(proto.getSoldierId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        if (proto.hasSlightWoundNum()) {
            this.setSlightWoundNum(proto.getSlightWoundNum());
            fieldCnt++;
        }
        if (proto.hasSevereWoundNum()) {
            this.setSevereWoundNum(proto.getSevereWoundNum());
            fieldCnt++;
        }
        if (proto.hasDeadNum()) {
            this.setDeadNum(proto.getDeadNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Soldier.Builder getCopyDbBuilder() {
        final Soldier.Builder builder = Soldier.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(Soldier.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSoldierId() != 0) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }  else if (builder.hasSoldierId()) {
            // 清理SoldierId
            builder.clearSoldierId();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        if (this.getSlightWoundNum() != 0) {
            builder.setSlightWoundNum(this.getSlightWoundNum());
            fieldCnt++;
        }  else if (builder.hasSlightWoundNum()) {
            // 清理SlightWoundNum
            builder.clearSlightWoundNum();
            fieldCnt++;
        }
        if (this.getSevereWoundNum() != 0) {
            builder.setSevereWoundNum(this.getSevereWoundNum());
            fieldCnt++;
        }  else if (builder.hasSevereWoundNum()) {
            // 清理SevereWoundNum
            builder.clearSevereWoundNum();
            fieldCnt++;
        }
        if (this.getDeadNum() != 0) {
            builder.setDeadNum(this.getDeadNum());
            fieldCnt++;
        }  else if (builder.hasDeadNum()) {
            // 清理DeadNum
            builder.clearDeadNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(Soldier.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERID)) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SLIGHTWOUNDNUM)) {
            builder.setSlightWoundNum(this.getSlightWoundNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SEVEREWOUNDNUM)) {
            builder.setSevereWoundNum(this.getSevereWoundNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEADNUM)) {
            builder.setDeadNum(this.getDeadNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(Soldier proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierId()) {
            this.innerSetSoldierId(proto.getSoldierId());
        } else {
            this.innerSetSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSlightWoundNum()) {
            this.innerSetSlightWoundNum(proto.getSlightWoundNum());
        } else {
            this.innerSetSlightWoundNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSevereWoundNum()) {
            this.innerSetSevereWoundNum(proto.getSevereWoundNum());
        } else {
            this.innerSetSevereWoundNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDeadNum()) {
            this.innerSetDeadNum(proto.getDeadNum());
        } else {
            this.innerSetDeadNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SoldierProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(Soldier proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierId()) {
            this.setSoldierId(proto.getSoldierId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        if (proto.hasSlightWoundNum()) {
            this.setSlightWoundNum(proto.getSlightWoundNum());
            fieldCnt++;
        }
        if (proto.hasSevereWoundNum()) {
            this.setSevereWoundNum(proto.getSevereWoundNum());
            fieldCnt++;
        }
        if (proto.hasDeadNum()) {
            this.setDeadNum(proto.getDeadNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Soldier.Builder getCopySsBuilder() {
        final Soldier.Builder builder = Soldier.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(Soldier.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSoldierId() != 0) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }  else if (builder.hasSoldierId()) {
            // 清理SoldierId
            builder.clearSoldierId();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        if (this.getSlightWoundNum() != 0) {
            builder.setSlightWoundNum(this.getSlightWoundNum());
            fieldCnt++;
        }  else if (builder.hasSlightWoundNum()) {
            // 清理SlightWoundNum
            builder.clearSlightWoundNum();
            fieldCnt++;
        }
        if (this.getSevereWoundNum() != 0) {
            builder.setSevereWoundNum(this.getSevereWoundNum());
            fieldCnt++;
        }  else if (builder.hasSevereWoundNum()) {
            // 清理SevereWoundNum
            builder.clearSevereWoundNum();
            fieldCnt++;
        }
        if (this.getDeadNum() != 0) {
            builder.setDeadNum(this.getDeadNum());
            fieldCnt++;
        }  else if (builder.hasDeadNum()) {
            // 清理DeadNum
            builder.clearDeadNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(Soldier.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERID)) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SLIGHTWOUNDNUM)) {
            builder.setSlightWoundNum(this.getSlightWoundNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SEVEREWOUNDNUM)) {
            builder.setSevereWoundNum(this.getSevereWoundNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEADNUM)) {
            builder.setDeadNum(this.getDeadNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(Soldier proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierId()) {
            this.innerSetSoldierId(proto.getSoldierId());
        } else {
            this.innerSetSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSlightWoundNum()) {
            this.innerSetSlightWoundNum(proto.getSlightWoundNum());
        } else {
            this.innerSetSlightWoundNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSevereWoundNum()) {
            this.innerSetSevereWoundNum(proto.getSevereWoundNum());
        } else {
            this.innerSetSevereWoundNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDeadNum()) {
            this.innerSetDeadNum(proto.getDeadNum());
        } else {
            this.innerSetDeadNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SoldierProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(Soldier proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierId()) {
            this.setSoldierId(proto.getSoldierId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        if (proto.hasSlightWoundNum()) {
            this.setSlightWoundNum(proto.getSlightWoundNum());
            fieldCnt++;
        }
        if (proto.hasSevereWoundNum()) {
            this.setSevereWoundNum(proto.getSevereWoundNum());
            fieldCnt++;
        }
        if (proto.hasDeadNum()) {
            this.setDeadNum(proto.getDeadNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        Soldier.Builder builder = Soldier.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.soldierId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SoldierProp)) {
            return false;
        }
        final SoldierProp otherNode = (SoldierProp) node;
        if (this.soldierId != otherNode.soldierId) {
            return false;
        }
        if (this.num != otherNode.num) {
            return false;
        }
        if (this.slightWoundNum != otherNode.slightWoundNum) {
            return false;
        }
        if (this.severeWoundNum != otherNode.severeWoundNum) {
            return false;
        }
        if (this.deadNum != otherNode.deadNum) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
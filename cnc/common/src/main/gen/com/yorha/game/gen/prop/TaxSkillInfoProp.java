package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zone.TaxSkillInfo;
import com.yorha.proto.Zone;
import com.yorha.proto.ZonePB.TaxSkillInfoPB;
import com.yorha.proto.ZonePB;


/**
 * <AUTHOR> auto gen
 */
public class TaxSkillInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_USEPLAYERID = 0;
    public static final int FIELD_INDEX_TAXRESOURCE = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private long usePlayerId = Constant.DEFAULT_LONG_VALUE;
    private Int32TaxGetResourceInfoMapProp taxResource = null;

    public TaxSkillInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public TaxSkillInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get usePlayerId
     *
     * @return usePlayerId value
     */
    public long getUsePlayerId() {
        return this.usePlayerId;
    }

    /**
     * set usePlayerId && set marked
     *
     * @param usePlayerId new value
     * @return current object
     */
    public TaxSkillInfoProp setUsePlayerId(long usePlayerId) {
        if (this.usePlayerId != usePlayerId) {
            this.mark(FIELD_INDEX_USEPLAYERID);
            this.usePlayerId = usePlayerId;
        }
        return this;
    }

    /**
     * inner set usePlayerId
     *
     * @param usePlayerId new value
     */
    private void innerSetUsePlayerId(long usePlayerId) {
        this.usePlayerId = usePlayerId;
    }

    /**
     * get taxResource
     *
     * @return taxResource value
     */
    public Int32TaxGetResourceInfoMapProp getTaxResource() {
        if (this.taxResource == null) {
            this.taxResource = new Int32TaxGetResourceInfoMapProp(this, FIELD_INDEX_TAXRESOURCE);
        }
        return this.taxResource;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTaxResourceV(TaxGetResourceInfoProp v) {
        this.getTaxResource().put(v.getType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public TaxGetResourceInfoProp addEmptyTaxResource(Integer k) {
        return this.getTaxResource().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTaxResourceSize() {
        if (this.taxResource == null) {
            return 0;
        }
        return this.taxResource.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTaxResourceEmpty() {
        if (this.taxResource == null) {
            return true;
        }
        return this.taxResource.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public TaxGetResourceInfoProp getTaxResourceV(Integer k) {
        if (this.taxResource == null || !this.taxResource.containsKey(k)) {
            return null;
        }
        return this.taxResource.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTaxResource() {
        if (this.taxResource != null) {
            this.taxResource.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTaxResourceV(Integer k) {
        if (this.taxResource != null) {
            this.taxResource.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TaxSkillInfoPB.Builder getCopyCsBuilder() {
        final TaxSkillInfoPB.Builder builder = TaxSkillInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(TaxSkillInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUsePlayerId() != 0L) {
            builder.setUsePlayerId(this.getUsePlayerId());
            fieldCnt++;
        }  else if (builder.hasUsePlayerId()) {
            // 清理UsePlayerId
            builder.clearUsePlayerId();
            fieldCnt++;
        }
        if (this.taxResource != null) {
            ZonePB.Int32TaxGetResourceInfoMapPB.Builder tmpBuilder = ZonePB.Int32TaxGetResourceInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.taxResource.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaxResource(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaxResource();
            }
        }  else if (builder.hasTaxResource()) {
            // 清理TaxResource
            builder.clearTaxResource();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(TaxSkillInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_USEPLAYERID)) {
            builder.setUsePlayerId(this.getUsePlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TAXRESOURCE) && this.taxResource != null) {
            final boolean needClear = !builder.hasTaxResource();
            final int tmpFieldCnt = this.taxResource.copyChangeToCs(builder.getTaxResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaxResource();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(TaxSkillInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_USEPLAYERID)) {
            builder.setUsePlayerId(this.getUsePlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TAXRESOURCE) && this.taxResource != null) {
            final boolean needClear = !builder.hasTaxResource();
            final int tmpFieldCnt = this.taxResource.copyChangeToAndClearDeleteKeysCs(builder.getTaxResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaxResource();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(TaxSkillInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUsePlayerId()) {
            this.innerSetUsePlayerId(proto.getUsePlayerId());
        } else {
            this.innerSetUsePlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTaxResource()) {
            this.getTaxResource().mergeFromCs(proto.getTaxResource());
        } else {
            if (this.taxResource != null) {
                this.taxResource.mergeFromCs(proto.getTaxResource());
            }
        }
        this.markAll();
        return TaxSkillInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(TaxSkillInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUsePlayerId()) {
            this.setUsePlayerId(proto.getUsePlayerId());
            fieldCnt++;
        }
        if (proto.hasTaxResource()) {
            this.getTaxResource().mergeChangeFromCs(proto.getTaxResource());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TaxSkillInfo.Builder getCopyDbBuilder() {
        final TaxSkillInfo.Builder builder = TaxSkillInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(TaxSkillInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUsePlayerId() != 0L) {
            builder.setUsePlayerId(this.getUsePlayerId());
            fieldCnt++;
        }  else if (builder.hasUsePlayerId()) {
            // 清理UsePlayerId
            builder.clearUsePlayerId();
            fieldCnt++;
        }
        if (this.taxResource != null) {
            Zone.Int32TaxGetResourceInfoMap.Builder tmpBuilder = Zone.Int32TaxGetResourceInfoMap.newBuilder();
            final int tmpFieldCnt = this.taxResource.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaxResource(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaxResource();
            }
        }  else if (builder.hasTaxResource()) {
            // 清理TaxResource
            builder.clearTaxResource();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(TaxSkillInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_USEPLAYERID)) {
            builder.setUsePlayerId(this.getUsePlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TAXRESOURCE) && this.taxResource != null) {
            final boolean needClear = !builder.hasTaxResource();
            final int tmpFieldCnt = this.taxResource.copyChangeToDb(builder.getTaxResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaxResource();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(TaxSkillInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUsePlayerId()) {
            this.innerSetUsePlayerId(proto.getUsePlayerId());
        } else {
            this.innerSetUsePlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTaxResource()) {
            this.getTaxResource().mergeFromDb(proto.getTaxResource());
        } else {
            if (this.taxResource != null) {
                this.taxResource.mergeFromDb(proto.getTaxResource());
            }
        }
        this.markAll();
        return TaxSkillInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(TaxSkillInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUsePlayerId()) {
            this.setUsePlayerId(proto.getUsePlayerId());
            fieldCnt++;
        }
        if (proto.hasTaxResource()) {
            this.getTaxResource().mergeChangeFromDb(proto.getTaxResource());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TaxSkillInfo.Builder getCopySsBuilder() {
        final TaxSkillInfo.Builder builder = TaxSkillInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(TaxSkillInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUsePlayerId() != 0L) {
            builder.setUsePlayerId(this.getUsePlayerId());
            fieldCnt++;
        }  else if (builder.hasUsePlayerId()) {
            // 清理UsePlayerId
            builder.clearUsePlayerId();
            fieldCnt++;
        }
        if (this.taxResource != null) {
            Zone.Int32TaxGetResourceInfoMap.Builder tmpBuilder = Zone.Int32TaxGetResourceInfoMap.newBuilder();
            final int tmpFieldCnt = this.taxResource.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaxResource(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaxResource();
            }
        }  else if (builder.hasTaxResource()) {
            // 清理TaxResource
            builder.clearTaxResource();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(TaxSkillInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_USEPLAYERID)) {
            builder.setUsePlayerId(this.getUsePlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TAXRESOURCE) && this.taxResource != null) {
            final boolean needClear = !builder.hasTaxResource();
            final int tmpFieldCnt = this.taxResource.copyChangeToSs(builder.getTaxResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaxResource();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(TaxSkillInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUsePlayerId()) {
            this.innerSetUsePlayerId(proto.getUsePlayerId());
        } else {
            this.innerSetUsePlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTaxResource()) {
            this.getTaxResource().mergeFromSs(proto.getTaxResource());
        } else {
            if (this.taxResource != null) {
                this.taxResource.mergeFromSs(proto.getTaxResource());
            }
        }
        this.markAll();
        return TaxSkillInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(TaxSkillInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUsePlayerId()) {
            this.setUsePlayerId(proto.getUsePlayerId());
            fieldCnt++;
        }
        if (proto.hasTaxResource()) {
            this.getTaxResource().mergeChangeFromSs(proto.getTaxResource());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        TaxSkillInfo.Builder builder = TaxSkillInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TAXRESOURCE) && this.taxResource != null) {
            this.taxResource.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.taxResource != null) {
            this.taxResource.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof TaxSkillInfoProp)) {
            return false;
        }
        final TaxSkillInfoProp otherNode = (TaxSkillInfoProp) node;
        if (this.usePlayerId != otherNode.usePlayerId) {
            return false;
        }
        if (!this.getTaxResource().compareDataTo(otherNode.getTaxResource())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
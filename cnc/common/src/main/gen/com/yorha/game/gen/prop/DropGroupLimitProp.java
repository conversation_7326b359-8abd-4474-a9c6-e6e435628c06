package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.DropGroupLimit;
import com.yorha.proto.StructPB.DropGroupLimitPB;


/**
 * <AUTHOR> auto gen
 */
public class DropGroupLimitProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_GROUPID = 0;
    public static final int FIELD_INDEX_OVERTIME = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int groupId = Constant.DEFAULT_INT_VALUE;
    private long overTime = Constant.DEFAULT_LONG_VALUE;

    public DropGroupLimitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public DropGroupLimitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get groupId
     *
     * @return groupId value
     */
    public int getGroupId() {
        return this.groupId;
    }

    /**
     * set groupId && set marked
     *
     * @param groupId new value
     * @return current object
     */
    public DropGroupLimitProp setGroupId(int groupId) {
        if (this.groupId != groupId) {
            this.mark(FIELD_INDEX_GROUPID);
            this.groupId = groupId;
        }
        return this;
    }

    /**
     * inner set groupId
     *
     * @param groupId new value
     */
    private void innerSetGroupId(int groupId) {
        this.groupId = groupId;
    }

    /**
     * get overTime
     *
     * @return overTime value
     */
    public long getOverTime() {
        return this.overTime;
    }

    /**
     * set overTime && set marked
     *
     * @param overTime new value
     * @return current object
     */
    public DropGroupLimitProp setOverTime(long overTime) {
        if (this.overTime != overTime) {
            this.mark(FIELD_INDEX_OVERTIME);
            this.overTime = overTime;
        }
        return this;
    }

    /**
     * inner set overTime
     *
     * @param overTime new value
     */
    private void innerSetOverTime(long overTime) {
        this.overTime = overTime;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DropGroupLimitPB.Builder getCopyCsBuilder() {
        final DropGroupLimitPB.Builder builder = DropGroupLimitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(DropGroupLimitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGroupId() != 0) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }  else if (builder.hasGroupId()) {
            // 清理GroupId
            builder.clearGroupId();
            fieldCnt++;
        }
        if (this.getOverTime() != 0L) {
            builder.setOverTime(this.getOverTime());
            fieldCnt++;
        }  else if (builder.hasOverTime()) {
            // 清理OverTime
            builder.clearOverTime();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(DropGroupLimitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OVERTIME)) {
            builder.setOverTime(this.getOverTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(DropGroupLimitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OVERTIME)) {
            builder.setOverTime(this.getOverTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(DropGroupLimitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGroupId()) {
            this.innerSetGroupId(proto.getGroupId());
        } else {
            this.innerSetGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOverTime()) {
            this.innerSetOverTime(proto.getOverTime());
        } else {
            this.innerSetOverTime(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return DropGroupLimitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(DropGroupLimitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGroupId()) {
            this.setGroupId(proto.getGroupId());
            fieldCnt++;
        }
        if (proto.hasOverTime()) {
            this.setOverTime(proto.getOverTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DropGroupLimit.Builder getCopyDbBuilder() {
        final DropGroupLimit.Builder builder = DropGroupLimit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(DropGroupLimit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGroupId() != 0) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }  else if (builder.hasGroupId()) {
            // 清理GroupId
            builder.clearGroupId();
            fieldCnt++;
        }
        if (this.getOverTime() != 0L) {
            builder.setOverTime(this.getOverTime());
            fieldCnt++;
        }  else if (builder.hasOverTime()) {
            // 清理OverTime
            builder.clearOverTime();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(DropGroupLimit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OVERTIME)) {
            builder.setOverTime(this.getOverTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(DropGroupLimit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGroupId()) {
            this.innerSetGroupId(proto.getGroupId());
        } else {
            this.innerSetGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOverTime()) {
            this.innerSetOverTime(proto.getOverTime());
        } else {
            this.innerSetOverTime(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return DropGroupLimitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(DropGroupLimit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGroupId()) {
            this.setGroupId(proto.getGroupId());
            fieldCnt++;
        }
        if (proto.hasOverTime()) {
            this.setOverTime(proto.getOverTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DropGroupLimit.Builder getCopySsBuilder() {
        final DropGroupLimit.Builder builder = DropGroupLimit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(DropGroupLimit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGroupId() != 0) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }  else if (builder.hasGroupId()) {
            // 清理GroupId
            builder.clearGroupId();
            fieldCnt++;
        }
        if (this.getOverTime() != 0L) {
            builder.setOverTime(this.getOverTime());
            fieldCnt++;
        }  else if (builder.hasOverTime()) {
            // 清理OverTime
            builder.clearOverTime();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(DropGroupLimit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OVERTIME)) {
            builder.setOverTime(this.getOverTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(DropGroupLimit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGroupId()) {
            this.innerSetGroupId(proto.getGroupId());
        } else {
            this.innerSetGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOverTime()) {
            this.innerSetOverTime(proto.getOverTime());
        } else {
            this.innerSetOverTime(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return DropGroupLimitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(DropGroupLimit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGroupId()) {
            this.setGroupId(proto.getGroupId());
            fieldCnt++;
        }
        if (proto.hasOverTime()) {
            this.setOverTime(proto.getOverTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        DropGroupLimit.Builder builder = DropGroupLimit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.groupId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof DropGroupLimitProp)) {
            return false;
        }
        final DropGroupLimitProp otherNode = (DropGroupLimitProp) node;
        if (this.groupId != otherNode.groupId) {
            return false;
        }
        if (this.overTime != otherNode.overTime) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructBattle.BattleSoldierDetail;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructBattlePB.BattleSoldierDetailPB;
import com.yorha.proto.StructBattlePB;


/**
 * <AUTHOR> auto gen
 */
public class BattleSoldierDetailProp extends AbstractPropNode {

    public static final int FIELD_INDEX_SOLDIERID = 0;
    public static final int FIELD_INDEX_SOLDIERREPORT = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int soldierId = Constant.DEFAULT_INT_VALUE;
    private BattleRecordSoldierReportProp soldierReport = null;

    public BattleSoldierDetailProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BattleSoldierDetailProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get soldierId
     *
     * @return soldierId value
     */
    public int getSoldierId() {
        return this.soldierId;
    }

    /**
     * set soldierId && set marked
     *
     * @param soldierId new value
     * @return current object
     */
    public BattleSoldierDetailProp setSoldierId(int soldierId) {
        if (this.soldierId != soldierId) {
            this.mark(FIELD_INDEX_SOLDIERID);
            this.soldierId = soldierId;
        }
        return this;
    }

    /**
     * inner set soldierId
     *
     * @param soldierId new value
     */
    private void innerSetSoldierId(int soldierId) {
        this.soldierId = soldierId;
    }

    /**
     * get soldierReport
     *
     * @return soldierReport value
     */
    public BattleRecordSoldierReportProp getSoldierReport() {
        if (this.soldierReport == null) {
            this.soldierReport = new BattleRecordSoldierReportProp(this, FIELD_INDEX_SOLDIERREPORT);
        }
        return this.soldierReport;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleSoldierDetailPB.Builder getCopyCsBuilder() {
        final BattleSoldierDetailPB.Builder builder = BattleSoldierDetailPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BattleSoldierDetailPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSoldierId() != 0) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }  else if (builder.hasSoldierId()) {
            // 清理SoldierId
            builder.clearSoldierId();
            fieldCnt++;
        }
        if (this.soldierReport != null) {
            StructBattlePB.BattleRecordSoldierReportPB.Builder tmpBuilder = StructBattlePB.BattleRecordSoldierReportPB.newBuilder();
            final int tmpFieldCnt = this.soldierReport.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldierReport(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldierReport();
            }
        }  else if (builder.hasSoldierReport()) {
            // 清理SoldierReport
            builder.clearSoldierReport();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BattleSoldierDetailPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERID)) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERREPORT) && this.soldierReport != null) {
            final boolean needClear = !builder.hasSoldierReport();
            final int tmpFieldCnt = this.soldierReport.copyChangeToCs(builder.getSoldierReportBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldierReport();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BattleSoldierDetailPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERID)) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERREPORT) && this.soldierReport != null) {
            final boolean needClear = !builder.hasSoldierReport();
            final int tmpFieldCnt = this.soldierReport.copyChangeToAndClearDeleteKeysCs(builder.getSoldierReportBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldierReport();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BattleSoldierDetailPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierId()) {
            this.innerSetSoldierId(proto.getSoldierId());
        } else {
            this.innerSetSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSoldierReport()) {
            this.getSoldierReport().mergeFromCs(proto.getSoldierReport());
        } else {
            if (this.soldierReport != null) {
                this.soldierReport.mergeFromCs(proto.getSoldierReport());
            }
        }
        this.markAll();
        return BattleSoldierDetailProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BattleSoldierDetailPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierId()) {
            this.setSoldierId(proto.getSoldierId());
            fieldCnt++;
        }
        if (proto.hasSoldierReport()) {
            this.getSoldierReport().mergeChangeFromCs(proto.getSoldierReport());
            fieldCnt++;
        }
        return fieldCnt;
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleSoldierDetail.Builder getCopySsBuilder() {
        final BattleSoldierDetail.Builder builder = BattleSoldierDetail.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(BattleSoldierDetail.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSoldierId() != 0) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }  else if (builder.hasSoldierId()) {
            // 清理SoldierId
            builder.clearSoldierId();
            fieldCnt++;
        }
        if (this.soldierReport != null) {
            StructBattle.BattleRecordSoldierReport.Builder tmpBuilder = StructBattle.BattleRecordSoldierReport.newBuilder();
            final int tmpFieldCnt = this.soldierReport.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldierReport(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldierReport();
            }
        }  else if (builder.hasSoldierReport()) {
            // 清理SoldierReport
            builder.clearSoldierReport();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(BattleSoldierDetail.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERID)) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERREPORT) && this.soldierReport != null) {
            final boolean needClear = !builder.hasSoldierReport();
            final int tmpFieldCnt = this.soldierReport.copyChangeToSs(builder.getSoldierReportBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldierReport();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(BattleSoldierDetail proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierId()) {
            this.innerSetSoldierId(proto.getSoldierId());
        } else {
            this.innerSetSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSoldierReport()) {
            this.getSoldierReport().mergeFromSs(proto.getSoldierReport());
        } else {
            if (this.soldierReport != null) {
                this.soldierReport.mergeFromSs(proto.getSoldierReport());
            }
        }
        this.markAll();
        return BattleSoldierDetailProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(BattleSoldierDetail proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierId()) {
            this.setSoldierId(proto.getSoldierId());
            fieldCnt++;
        }
        if (proto.hasSoldierReport()) {
            this.getSoldierReport().mergeChangeFromSs(proto.getSoldierReport());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        BattleSoldierDetail.Builder builder = BattleSoldierDetail.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERREPORT) && this.soldierReport != null) {
            this.soldierReport.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.soldierReport != null) {
            this.soldierReport.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BattleSoldierDetailProp)) {
            return false;
        }
        final BattleSoldierDetailProp otherNode = (BattleSoldierDetailProp) node;
        if (this.soldierId != otherNode.soldierId) {
            return false;
        }
        if (!this.getSoldierReport().compareDataTo(otherNode.getSoldierReport())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
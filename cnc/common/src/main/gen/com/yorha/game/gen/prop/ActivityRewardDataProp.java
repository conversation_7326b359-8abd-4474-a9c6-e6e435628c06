package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityRewardData;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ActivityRewardDataPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityRewardDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_REWARDRECORDS = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int64RewardRecordMapProp rewardRecords = null;

    public ActivityRewardDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityRewardDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get rewardRecords
     *
     * @return rewardRecords value
     */
    public Int64RewardRecordMapProp getRewardRecords() {
        if (this.rewardRecords == null) {
            this.rewardRecords = new Int64RewardRecordMapProp(this, FIELD_INDEX_REWARDRECORDS);
        }
        return this.rewardRecords;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putRewardRecordsV(RewardRecordProp v) {
        this.getRewardRecords().put(v.getPlayerId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public RewardRecordProp addEmptyRewardRecords(Long k) {
        return this.getRewardRecords().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getRewardRecordsSize() {
        if (this.rewardRecords == null) {
            return 0;
        }
        return this.rewardRecords.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isRewardRecordsEmpty() {
        if (this.rewardRecords == null) {
            return true;
        }
        return this.rewardRecords.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public RewardRecordProp getRewardRecordsV(Long k) {
        if (this.rewardRecords == null || !this.rewardRecords.containsKey(k)) {
            return null;
        }
        return this.rewardRecords.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearRewardRecords() {
        if (this.rewardRecords != null) {
            this.rewardRecords.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeRewardRecordsV(Long k) {
        if (this.rewardRecords != null) {
            this.rewardRecords.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityRewardDataPB.Builder getCopyCsBuilder() {
        final ActivityRewardDataPB.Builder builder = ActivityRewardDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityRewardDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.rewardRecords != null) {
            StructPB.Int64RewardRecordMapPB.Builder tmpBuilder = StructPB.Int64RewardRecordMapPB.newBuilder();
            final int tmpFieldCnt = this.rewardRecords.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardRecords();
            }
        }  else if (builder.hasRewardRecords()) {
            // 清理RewardRecords
            builder.clearRewardRecords();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityRewardDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDRECORDS) && this.rewardRecords != null) {
            final boolean needClear = !builder.hasRewardRecords();
            final int tmpFieldCnt = this.rewardRecords.copyChangeToCs(builder.getRewardRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardRecords();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityRewardDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDRECORDS) && this.rewardRecords != null) {
            final boolean needClear = !builder.hasRewardRecords();
            final int tmpFieldCnt = this.rewardRecords.copyChangeToAndClearDeleteKeysCs(builder.getRewardRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardRecords();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityRewardDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardRecords()) {
            this.getRewardRecords().mergeFromCs(proto.getRewardRecords());
        } else {
            if (this.rewardRecords != null) {
                this.rewardRecords.mergeFromCs(proto.getRewardRecords());
            }
        }
        this.markAll();
        return ActivityRewardDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityRewardDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardRecords()) {
            this.getRewardRecords().mergeChangeFromCs(proto.getRewardRecords());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityRewardData.Builder getCopyDbBuilder() {
        final ActivityRewardData.Builder builder = ActivityRewardData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityRewardData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.rewardRecords != null) {
            Struct.Int64RewardRecordMap.Builder tmpBuilder = Struct.Int64RewardRecordMap.newBuilder();
            final int tmpFieldCnt = this.rewardRecords.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardRecords();
            }
        }  else if (builder.hasRewardRecords()) {
            // 清理RewardRecords
            builder.clearRewardRecords();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityRewardData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDRECORDS) && this.rewardRecords != null) {
            final boolean needClear = !builder.hasRewardRecords();
            final int tmpFieldCnt = this.rewardRecords.copyChangeToDb(builder.getRewardRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardRecords();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityRewardData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardRecords()) {
            this.getRewardRecords().mergeFromDb(proto.getRewardRecords());
        } else {
            if (this.rewardRecords != null) {
                this.rewardRecords.mergeFromDb(proto.getRewardRecords());
            }
        }
        this.markAll();
        return ActivityRewardDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityRewardData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardRecords()) {
            this.getRewardRecords().mergeChangeFromDb(proto.getRewardRecords());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityRewardData.Builder getCopySsBuilder() {
        final ActivityRewardData.Builder builder = ActivityRewardData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityRewardData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.rewardRecords != null) {
            Struct.Int64RewardRecordMap.Builder tmpBuilder = Struct.Int64RewardRecordMap.newBuilder();
            final int tmpFieldCnt = this.rewardRecords.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardRecords();
            }
        }  else if (builder.hasRewardRecords()) {
            // 清理RewardRecords
            builder.clearRewardRecords();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityRewardData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDRECORDS) && this.rewardRecords != null) {
            final boolean needClear = !builder.hasRewardRecords();
            final int tmpFieldCnt = this.rewardRecords.copyChangeToSs(builder.getRewardRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardRecords();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityRewardData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardRecords()) {
            this.getRewardRecords().mergeFromSs(proto.getRewardRecords());
        } else {
            if (this.rewardRecords != null) {
                this.rewardRecords.mergeFromSs(proto.getRewardRecords());
            }
        }
        this.markAll();
        return ActivityRewardDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityRewardData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardRecords()) {
            this.getRewardRecords().mergeChangeFromSs(proto.getRewardRecords());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityRewardData.Builder builder = ActivityRewardData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_REWARDRECORDS) && this.rewardRecords != null) {
            this.rewardRecords.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.rewardRecords != null) {
            this.rewardRecords.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityRewardDataProp)) {
            return false;
        }
        final ActivityRewardDataProp otherNode = (ActivityRewardDataProp) node;
        if (!this.getRewardRecords().compareDataTo(otherNode.getRewardRecords())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructPlayer.ArmyActionInfo;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPlayerPB.ArmyActionInfoPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ArmyActionInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ARMYACTIONTYPE = 0;
    public static final int FIELD_INDEX_TARGETID = 1;
    public static final int FIELD_INDEX_TARGETPOINT = 2;
    public static final int FIELD_INDEX_ISSTAYAFTERBATTLE = 3;
    public static final int FIELD_INDEX_WAITSECONDSTIME = 4;
    public static final int FIELD_INDEX_DEBUGFASTMOVE = 5;
    public static final int FIELD_INDEX_ISRETURNAFTERASSIST = 6;
    public static final int FIELD_INDEX_TRANSPORTPLANE = 7;

    public static final int FIELD_COUNT = 8;

    private long markBits0 = 0L;

    private ArmyActionType armyActionType = ArmyActionType.forNumber(0);
    private long targetId = Constant.DEFAULT_LONG_VALUE;
    private PointProp targetPoint = null;
    private boolean isStayAfterBattle = Constant.DEFAULT_BOOLEAN_VALUE;
    private int waitSecondsTime = Constant.DEFAULT_INT_VALUE;
    private boolean debugFastMove = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean isReturnAfterAssist = Constant.DEFAULT_BOOLEAN_VALUE;
    private SceneTransportPlaneProp transportPlane = null;

    public ArmyActionInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ArmyActionInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get armyActionType
     *
     * @return armyActionType value
     */
    public ArmyActionType getArmyActionType() {
        return this.armyActionType;
    }

    /**
     * set armyActionType && set marked
     *
     * @param armyActionType new value
     * @return current object
     */
    public ArmyActionInfoProp setArmyActionType(ArmyActionType armyActionType) {
        if (armyActionType == null) {
            throw new NullPointerException();
        }
        if (this.armyActionType != armyActionType) {
            this.mark(FIELD_INDEX_ARMYACTIONTYPE);
            this.armyActionType = armyActionType;
        }
        return this;
    }

    /**
     * inner set armyActionType
     *
     * @param armyActionType new value
     */
    private void innerSetArmyActionType(ArmyActionType armyActionType) {
        this.armyActionType = armyActionType;
    }

    /**
     * get targetId
     *
     * @return targetId value
     */
    public long getTargetId() {
        return this.targetId;
    }

    /**
     * set targetId && set marked
     *
     * @param targetId new value
     * @return current object
     */
    public ArmyActionInfoProp setTargetId(long targetId) {
        if (this.targetId != targetId) {
            this.mark(FIELD_INDEX_TARGETID);
            this.targetId = targetId;
        }
        return this;
    }

    /**
     * inner set targetId
     *
     * @param targetId new value
     */
    private void innerSetTargetId(long targetId) {
        this.targetId = targetId;
    }

    /**
     * get targetPoint
     *
     * @return targetPoint value
     */
    public PointProp getTargetPoint() {
        if (this.targetPoint == null) {
            this.targetPoint = new PointProp(this, FIELD_INDEX_TARGETPOINT);
        }
        return this.targetPoint;
    }

    /**
     * get isStayAfterBattle
     *
     * @return isStayAfterBattle value
     */
    public boolean getIsStayAfterBattle() {
        return this.isStayAfterBattle;
    }

    /**
     * set isStayAfterBattle && set marked
     *
     * @param isStayAfterBattle new value
     * @return current object
     */
    public ArmyActionInfoProp setIsStayAfterBattle(boolean isStayAfterBattle) {
        if (this.isStayAfterBattle != isStayAfterBattle) {
            this.mark(FIELD_INDEX_ISSTAYAFTERBATTLE);
            this.isStayAfterBattle = isStayAfterBattle;
        }
        return this;
    }

    /**
     * inner set isStayAfterBattle
     *
     * @param isStayAfterBattle new value
     */
    private void innerSetIsStayAfterBattle(boolean isStayAfterBattle) {
        this.isStayAfterBattle = isStayAfterBattle;
    }

    /**
     * get waitSecondsTime
     *
     * @return waitSecondsTime value
     */
    public int getWaitSecondsTime() {
        return this.waitSecondsTime;
    }

    /**
     * set waitSecondsTime && set marked
     *
     * @param waitSecondsTime new value
     * @return current object
     */
    public ArmyActionInfoProp setWaitSecondsTime(int waitSecondsTime) {
        if (this.waitSecondsTime != waitSecondsTime) {
            this.mark(FIELD_INDEX_WAITSECONDSTIME);
            this.waitSecondsTime = waitSecondsTime;
        }
        return this;
    }

    /**
     * inner set waitSecondsTime
     *
     * @param waitSecondsTime new value
     */
    private void innerSetWaitSecondsTime(int waitSecondsTime) {
        this.waitSecondsTime = waitSecondsTime;
    }

    /**
     * get debugFastMove
     *
     * @return debugFastMove value
     */
    public boolean getDebugFastMove() {
        return this.debugFastMove;
    }

    /**
     * set debugFastMove && set marked
     *
     * @param debugFastMove new value
     * @return current object
     */
    public ArmyActionInfoProp setDebugFastMove(boolean debugFastMove) {
        if (this.debugFastMove != debugFastMove) {
            this.mark(FIELD_INDEX_DEBUGFASTMOVE);
            this.debugFastMove = debugFastMove;
        }
        return this;
    }

    /**
     * inner set debugFastMove
     *
     * @param debugFastMove new value
     */
    private void innerSetDebugFastMove(boolean debugFastMove) {
        this.debugFastMove = debugFastMove;
    }

    /**
     * get isReturnAfterAssist
     *
     * @return isReturnAfterAssist value
     */
    public boolean getIsReturnAfterAssist() {
        return this.isReturnAfterAssist;
    }

    /**
     * set isReturnAfterAssist && set marked
     *
     * @param isReturnAfterAssist new value
     * @return current object
     */
    public ArmyActionInfoProp setIsReturnAfterAssist(boolean isReturnAfterAssist) {
        if (this.isReturnAfterAssist != isReturnAfterAssist) {
            this.mark(FIELD_INDEX_ISRETURNAFTERASSIST);
            this.isReturnAfterAssist = isReturnAfterAssist;
        }
        return this;
    }

    /**
     * inner set isReturnAfterAssist
     *
     * @param isReturnAfterAssist new value
     */
    private void innerSetIsReturnAfterAssist(boolean isReturnAfterAssist) {
        this.isReturnAfterAssist = isReturnAfterAssist;
    }

    /**
     * get transportPlane
     *
     * @return transportPlane value
     */
    public SceneTransportPlaneProp getTransportPlane() {
        if (this.transportPlane == null) {
            this.transportPlane = new SceneTransportPlaneProp(this, FIELD_INDEX_TRANSPORTPLANE);
        }
        return this.transportPlane;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArmyActionInfoPB.Builder getCopyCsBuilder() {
        final ArmyActionInfoPB.Builder builder = ArmyActionInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ArmyActionInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getArmyActionType() != ArmyActionType.forNumber(0)) {
            builder.setArmyActionType(this.getArmyActionType());
            fieldCnt++;
        }  else if (builder.hasArmyActionType()) {
            // 清理ArmyActionType
            builder.clearArmyActionType();
            fieldCnt++;
        }
        if (this.getTargetId() != 0L) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }  else if (builder.hasTargetId()) {
            // 清理TargetId
            builder.clearTargetId();
            fieldCnt++;
        }
        if (this.targetPoint != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.targetPoint.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTargetPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTargetPoint();
            }
        }  else if (builder.hasTargetPoint()) {
            // 清理TargetPoint
            builder.clearTargetPoint();
            fieldCnt++;
        }
        if (this.getIsStayAfterBattle()) {
            builder.setIsStayAfterBattle(this.getIsStayAfterBattle());
            fieldCnt++;
        }  else if (builder.hasIsStayAfterBattle()) {
            // 清理IsStayAfterBattle
            builder.clearIsStayAfterBattle();
            fieldCnt++;
        }
        if (this.getWaitSecondsTime() != 0) {
            builder.setWaitSecondsTime(this.getWaitSecondsTime());
            fieldCnt++;
        }  else if (builder.hasWaitSecondsTime()) {
            // 清理WaitSecondsTime
            builder.clearWaitSecondsTime();
            fieldCnt++;
        }
        if (this.getDebugFastMove()) {
            builder.setDebugFastMove(this.getDebugFastMove());
            fieldCnt++;
        }  else if (builder.hasDebugFastMove()) {
            // 清理DebugFastMove
            builder.clearDebugFastMove();
            fieldCnt++;
        }
        if (this.getIsReturnAfterAssist()) {
            builder.setIsReturnAfterAssist(this.getIsReturnAfterAssist());
            fieldCnt++;
        }  else if (builder.hasIsReturnAfterAssist()) {
            // 清理IsReturnAfterAssist
            builder.clearIsReturnAfterAssist();
            fieldCnt++;
        }
        if (this.transportPlane != null) {
            StructPB.SceneTransportPlanePB.Builder tmpBuilder = StructPB.SceneTransportPlanePB.newBuilder();
            final int tmpFieldCnt = this.transportPlane.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTransportPlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTransportPlane();
            }
        }  else if (builder.hasTransportPlane()) {
            // 清理TransportPlane
            builder.clearTransportPlane();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ArmyActionInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYACTIONTYPE)) {
            builder.setArmyActionType(this.getArmyActionType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETPOINT) && this.targetPoint != null) {
            final boolean needClear = !builder.hasTargetPoint();
            final int tmpFieldCnt = this.targetPoint.copyChangeToCs(builder.getTargetPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTargetPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISSTAYAFTERBATTLE)) {
            builder.setIsStayAfterBattle(this.getIsStayAfterBattle());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WAITSECONDSTIME)) {
            builder.setWaitSecondsTime(this.getWaitSecondsTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEBUGFASTMOVE)) {
            builder.setDebugFastMove(this.getDebugFastMove());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISRETURNAFTERASSIST)) {
            builder.setIsReturnAfterAssist(this.getIsReturnAfterAssist());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRANSPORTPLANE) && this.transportPlane != null) {
            final boolean needClear = !builder.hasTransportPlane();
            final int tmpFieldCnt = this.transportPlane.copyChangeToCs(builder.getTransportPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTransportPlane();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ArmyActionInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYACTIONTYPE)) {
            builder.setArmyActionType(this.getArmyActionType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETPOINT) && this.targetPoint != null) {
            final boolean needClear = !builder.hasTargetPoint();
            final int tmpFieldCnt = this.targetPoint.copyChangeToAndClearDeleteKeysCs(builder.getTargetPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTargetPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISSTAYAFTERBATTLE)) {
            builder.setIsStayAfterBattle(this.getIsStayAfterBattle());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WAITSECONDSTIME)) {
            builder.setWaitSecondsTime(this.getWaitSecondsTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEBUGFASTMOVE)) {
            builder.setDebugFastMove(this.getDebugFastMove());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISRETURNAFTERASSIST)) {
            builder.setIsReturnAfterAssist(this.getIsReturnAfterAssist());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRANSPORTPLANE) && this.transportPlane != null) {
            final boolean needClear = !builder.hasTransportPlane();
            final int tmpFieldCnt = this.transportPlane.copyChangeToAndClearDeleteKeysCs(builder.getTransportPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTransportPlane();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ArmyActionInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasArmyActionType()) {
            this.innerSetArmyActionType(proto.getArmyActionType());
        } else {
            this.innerSetArmyActionType(ArmyActionType.forNumber(0));
        }
        if (proto.hasTargetId()) {
            this.innerSetTargetId(proto.getTargetId());
        } else {
            this.innerSetTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTargetPoint()) {
            this.getTargetPoint().mergeFromCs(proto.getTargetPoint());
        } else {
            if (this.targetPoint != null) {
                this.targetPoint.mergeFromCs(proto.getTargetPoint());
            }
        }
        if (proto.hasIsStayAfterBattle()) {
            this.innerSetIsStayAfterBattle(proto.getIsStayAfterBattle());
        } else {
            this.innerSetIsStayAfterBattle(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasWaitSecondsTime()) {
            this.innerSetWaitSecondsTime(proto.getWaitSecondsTime());
        } else {
            this.innerSetWaitSecondsTime(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDebugFastMove()) {
            this.innerSetDebugFastMove(proto.getDebugFastMove());
        } else {
            this.innerSetDebugFastMove(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasIsReturnAfterAssist()) {
            this.innerSetIsReturnAfterAssist(proto.getIsReturnAfterAssist());
        } else {
            this.innerSetIsReturnAfterAssist(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasTransportPlane()) {
            this.getTransportPlane().mergeFromCs(proto.getTransportPlane());
        } else {
            if (this.transportPlane != null) {
                this.transportPlane.mergeFromCs(proto.getTransportPlane());
            }
        }
        this.markAll();
        return ArmyActionInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ArmyActionInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasArmyActionType()) {
            this.setArmyActionType(proto.getArmyActionType());
            fieldCnt++;
        }
        if (proto.hasTargetId()) {
            this.setTargetId(proto.getTargetId());
            fieldCnt++;
        }
        if (proto.hasTargetPoint()) {
            this.getTargetPoint().mergeChangeFromCs(proto.getTargetPoint());
            fieldCnt++;
        }
        if (proto.hasIsStayAfterBattle()) {
            this.setIsStayAfterBattle(proto.getIsStayAfterBattle());
            fieldCnt++;
        }
        if (proto.hasWaitSecondsTime()) {
            this.setWaitSecondsTime(proto.getWaitSecondsTime());
            fieldCnt++;
        }
        if (proto.hasDebugFastMove()) {
            this.setDebugFastMove(proto.getDebugFastMove());
            fieldCnt++;
        }
        if (proto.hasIsReturnAfterAssist()) {
            this.setIsReturnAfterAssist(proto.getIsReturnAfterAssist());
            fieldCnt++;
        }
        if (proto.hasTransportPlane()) {
            this.getTransportPlane().mergeChangeFromCs(proto.getTransportPlane());
            fieldCnt++;
        }
        return fieldCnt;
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArmyActionInfo.Builder getCopySsBuilder() {
        final ArmyActionInfo.Builder builder = ArmyActionInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ArmyActionInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getArmyActionType() != ArmyActionType.forNumber(0)) {
            builder.setArmyActionType(this.getArmyActionType());
            fieldCnt++;
        }  else if (builder.hasArmyActionType()) {
            // 清理ArmyActionType
            builder.clearArmyActionType();
            fieldCnt++;
        }
        if (this.getTargetId() != 0L) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }  else if (builder.hasTargetId()) {
            // 清理TargetId
            builder.clearTargetId();
            fieldCnt++;
        }
        if (this.targetPoint != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.targetPoint.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTargetPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTargetPoint();
            }
        }  else if (builder.hasTargetPoint()) {
            // 清理TargetPoint
            builder.clearTargetPoint();
            fieldCnt++;
        }
        if (this.getIsStayAfterBattle()) {
            builder.setIsStayAfterBattle(this.getIsStayAfterBattle());
            fieldCnt++;
        }  else if (builder.hasIsStayAfterBattle()) {
            // 清理IsStayAfterBattle
            builder.clearIsStayAfterBattle();
            fieldCnt++;
        }
        if (this.getWaitSecondsTime() != 0) {
            builder.setWaitSecondsTime(this.getWaitSecondsTime());
            fieldCnt++;
        }  else if (builder.hasWaitSecondsTime()) {
            // 清理WaitSecondsTime
            builder.clearWaitSecondsTime();
            fieldCnt++;
        }
        if (this.getDebugFastMove()) {
            builder.setDebugFastMove(this.getDebugFastMove());
            fieldCnt++;
        }  else if (builder.hasDebugFastMove()) {
            // 清理DebugFastMove
            builder.clearDebugFastMove();
            fieldCnt++;
        }
        if (this.getIsReturnAfterAssist()) {
            builder.setIsReturnAfterAssist(this.getIsReturnAfterAssist());
            fieldCnt++;
        }  else if (builder.hasIsReturnAfterAssist()) {
            // 清理IsReturnAfterAssist
            builder.clearIsReturnAfterAssist();
            fieldCnt++;
        }
        if (this.transportPlane != null) {
            Struct.SceneTransportPlane.Builder tmpBuilder = Struct.SceneTransportPlane.newBuilder();
            final int tmpFieldCnt = this.transportPlane.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTransportPlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTransportPlane();
            }
        }  else if (builder.hasTransportPlane()) {
            // 清理TransportPlane
            builder.clearTransportPlane();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ArmyActionInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYACTIONTYPE)) {
            builder.setArmyActionType(this.getArmyActionType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETPOINT) && this.targetPoint != null) {
            final boolean needClear = !builder.hasTargetPoint();
            final int tmpFieldCnt = this.targetPoint.copyChangeToSs(builder.getTargetPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTargetPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISSTAYAFTERBATTLE)) {
            builder.setIsStayAfterBattle(this.getIsStayAfterBattle());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WAITSECONDSTIME)) {
            builder.setWaitSecondsTime(this.getWaitSecondsTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEBUGFASTMOVE)) {
            builder.setDebugFastMove(this.getDebugFastMove());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISRETURNAFTERASSIST)) {
            builder.setIsReturnAfterAssist(this.getIsReturnAfterAssist());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRANSPORTPLANE) && this.transportPlane != null) {
            final boolean needClear = !builder.hasTransportPlane();
            final int tmpFieldCnt = this.transportPlane.copyChangeToSs(builder.getTransportPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTransportPlane();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ArmyActionInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasArmyActionType()) {
            this.innerSetArmyActionType(proto.getArmyActionType());
        } else {
            this.innerSetArmyActionType(ArmyActionType.forNumber(0));
        }
        if (proto.hasTargetId()) {
            this.innerSetTargetId(proto.getTargetId());
        } else {
            this.innerSetTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTargetPoint()) {
            this.getTargetPoint().mergeFromSs(proto.getTargetPoint());
        } else {
            if (this.targetPoint != null) {
                this.targetPoint.mergeFromSs(proto.getTargetPoint());
            }
        }
        if (proto.hasIsStayAfterBattle()) {
            this.innerSetIsStayAfterBattle(proto.getIsStayAfterBattle());
        } else {
            this.innerSetIsStayAfterBattle(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasWaitSecondsTime()) {
            this.innerSetWaitSecondsTime(proto.getWaitSecondsTime());
        } else {
            this.innerSetWaitSecondsTime(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDebugFastMove()) {
            this.innerSetDebugFastMove(proto.getDebugFastMove());
        } else {
            this.innerSetDebugFastMove(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasIsReturnAfterAssist()) {
            this.innerSetIsReturnAfterAssist(proto.getIsReturnAfterAssist());
        } else {
            this.innerSetIsReturnAfterAssist(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasTransportPlane()) {
            this.getTransportPlane().mergeFromSs(proto.getTransportPlane());
        } else {
            if (this.transportPlane != null) {
                this.transportPlane.mergeFromSs(proto.getTransportPlane());
            }
        }
        this.markAll();
        return ArmyActionInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ArmyActionInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasArmyActionType()) {
            this.setArmyActionType(proto.getArmyActionType());
            fieldCnt++;
        }
        if (proto.hasTargetId()) {
            this.setTargetId(proto.getTargetId());
            fieldCnt++;
        }
        if (proto.hasTargetPoint()) {
            this.getTargetPoint().mergeChangeFromSs(proto.getTargetPoint());
            fieldCnt++;
        }
        if (proto.hasIsStayAfterBattle()) {
            this.setIsStayAfterBattle(proto.getIsStayAfterBattle());
            fieldCnt++;
        }
        if (proto.hasWaitSecondsTime()) {
            this.setWaitSecondsTime(proto.getWaitSecondsTime());
            fieldCnt++;
        }
        if (proto.hasDebugFastMove()) {
            this.setDebugFastMove(proto.getDebugFastMove());
            fieldCnt++;
        }
        if (proto.hasIsReturnAfterAssist()) {
            this.setIsReturnAfterAssist(proto.getIsReturnAfterAssist());
            fieldCnt++;
        }
        if (proto.hasTransportPlane()) {
            this.getTransportPlane().mergeChangeFromSs(proto.getTransportPlane());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ArmyActionInfo.Builder builder = ArmyActionInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TARGETPOINT) && this.targetPoint != null) {
            this.targetPoint.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TRANSPORTPLANE) && this.transportPlane != null) {
            this.transportPlane.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.targetPoint != null) {
            this.targetPoint.markAll();
        }
        if (this.transportPlane != null) {
            this.transportPlane.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ArmyActionInfoProp)) {
            return false;
        }
        final ArmyActionInfoProp otherNode = (ArmyActionInfoProp) node;
        if (this.armyActionType != otherNode.armyActionType) {
            return false;
        }
        if (this.targetId != otherNode.targetId) {
            return false;
        }
        if (!this.getTargetPoint().compareDataTo(otherNode.getTargetPoint())) {
            return false;
        }
        if (this.isStayAfterBattle != otherNode.isStayAfterBattle) {
            return false;
        }
        if (this.waitSecondsTime != otherNode.waitSecondsTime) {
            return false;
        }
        if (this.debugFastMove != otherNode.debugFastMove) {
            return false;
        }
        if (this.isReturnAfterAssist != otherNode.isReturnAfterAssist) {
            return false;
        }
        if (!this.getTransportPlane().compareDataTo(otherNode.getTransportPlane())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 56;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructPB.ItemPairListPB;
import com.yorha.proto.Struct.ItemPairList;
import com.yorha.proto.StructPB.ItemPairPB;
import com.yorha.proto.Struct.ItemPair;

/**
 * <AUTHOR> auto gen
 */
public class ItemPairListProp extends AbstractListNode<ItemPairProp> {
    /**
     * Create a ItemPairListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public ItemPairListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to ItemPairListProp
     *
     * @return new object
     */
    @Override
    public ItemPairProp addEmptyValue() {
        final ItemPairProp newProp = new ItemPairProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ItemPairListPB.Builder getCopyCsBuilder() {
        final ItemPairListPB.Builder builder = ItemPairListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(ItemPairListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return ItemPairListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final ItemPairProp v : this) {
            final ItemPairPB.Builder itemBuilder = ItemPairPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return ItemPairListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(ItemPairListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return ItemPairListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(ItemPairListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (ItemPairPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return ItemPairListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(ItemPairListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ItemPairList.Builder getCopyDbBuilder() {
        final ItemPairList.Builder builder = ItemPairList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(ItemPairList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return ItemPairListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final ItemPairProp v : this) {
            final ItemPair.Builder itemBuilder = ItemPair.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return ItemPairListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(ItemPairList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return ItemPairListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(ItemPairList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (ItemPair v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return ItemPairListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(ItemPairList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ItemPairList.Builder getCopySsBuilder() {
        final ItemPairList.Builder builder = ItemPairList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(ItemPairList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return ItemPairListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final ItemPairProp v : this) {
            final ItemPair.Builder itemBuilder = ItemPair.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return ItemPairListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(ItemPairList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return ItemPairListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(ItemPairList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (ItemPair v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return ItemPairListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(ItemPairList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        ItemPairList.Builder builder = ItemPairList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
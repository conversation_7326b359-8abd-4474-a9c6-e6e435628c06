package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerStoreModel;
import com.yorha.proto.Player;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.PlayerStoreModelPB;
import com.yorha.proto.PlayerPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerStoreModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_PLAYERCLANSTOREMODEL = 0;
    public static final int FIELD_INDEX_COMMISSARIATSHOPBUYINFO = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private PlayerClanStoreModelProp playerClanStoreModel = null;
    private Int32ShopBuyInfoMapProp commissariatShopBuyInfo = null;

    public PlayerStoreModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerStoreModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get playerClanStoreModel
     *
     * @return playerClanStoreModel value
     */
    public PlayerClanStoreModelProp getPlayerClanStoreModel() {
        if (this.playerClanStoreModel == null) {
            this.playerClanStoreModel = new PlayerClanStoreModelProp(this, FIELD_INDEX_PLAYERCLANSTOREMODEL);
        }
        return this.playerClanStoreModel;
    }

    /**
     * get commissariatShopBuyInfo
     *
     * @return commissariatShopBuyInfo value
     */
    public Int32ShopBuyInfoMapProp getCommissariatShopBuyInfo() {
        if (this.commissariatShopBuyInfo == null) {
            this.commissariatShopBuyInfo = new Int32ShopBuyInfoMapProp(this, FIELD_INDEX_COMMISSARIATSHOPBUYINFO);
        }
        return this.commissariatShopBuyInfo;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putCommissariatShopBuyInfoV(ShopBuyInfoProp v) {
        this.getCommissariatShopBuyInfo().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ShopBuyInfoProp addEmptyCommissariatShopBuyInfo(Integer k) {
        return this.getCommissariatShopBuyInfo().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getCommissariatShopBuyInfoSize() {
        if (this.commissariatShopBuyInfo == null) {
            return 0;
        }
        return this.commissariatShopBuyInfo.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isCommissariatShopBuyInfoEmpty() {
        if (this.commissariatShopBuyInfo == null) {
            return true;
        }
        return this.commissariatShopBuyInfo.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ShopBuyInfoProp getCommissariatShopBuyInfoV(Integer k) {
        if (this.commissariatShopBuyInfo == null || !this.commissariatShopBuyInfo.containsKey(k)) {
            return null;
        }
        return this.commissariatShopBuyInfo.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearCommissariatShopBuyInfo() {
        if (this.commissariatShopBuyInfo != null) {
            this.commissariatShopBuyInfo.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeCommissariatShopBuyInfoV(Integer k) {
        if (this.commissariatShopBuyInfo != null) {
            this.commissariatShopBuyInfo.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerStoreModelPB.Builder getCopyCsBuilder() {
        final PlayerStoreModelPB.Builder builder = PlayerStoreModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerStoreModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.playerClanStoreModel != null) {
            PlayerPB.PlayerClanStoreModelPB.Builder tmpBuilder = PlayerPB.PlayerClanStoreModelPB.newBuilder();
            final int tmpFieldCnt = this.playerClanStoreModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerClanStoreModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerClanStoreModel();
            }
        }  else if (builder.hasPlayerClanStoreModel()) {
            // 清理PlayerClanStoreModel
            builder.clearPlayerClanStoreModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerStoreModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERCLANSTOREMODEL) && this.playerClanStoreModel != null) {
            final boolean needClear = !builder.hasPlayerClanStoreModel();
            final int tmpFieldCnt = this.playerClanStoreModel.copyChangeToCs(builder.getPlayerClanStoreModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerClanStoreModel();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerStoreModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERCLANSTOREMODEL) && this.playerClanStoreModel != null) {
            final boolean needClear = !builder.hasPlayerClanStoreModel();
            final int tmpFieldCnt = this.playerClanStoreModel.copyChangeToAndClearDeleteKeysCs(builder.getPlayerClanStoreModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerClanStoreModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerStoreModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerClanStoreModel()) {
            this.getPlayerClanStoreModel().mergeFromCs(proto.getPlayerClanStoreModel());
        } else {
            if (this.playerClanStoreModel != null) {
                this.playerClanStoreModel.mergeFromCs(proto.getPlayerClanStoreModel());
            }
        }
        this.markAll();
        return PlayerStoreModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerStoreModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerClanStoreModel()) {
            this.getPlayerClanStoreModel().mergeChangeFromCs(proto.getPlayerClanStoreModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerStoreModel.Builder getCopyDbBuilder() {
        final PlayerStoreModel.Builder builder = PlayerStoreModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerStoreModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.playerClanStoreModel != null) {
            Player.PlayerClanStoreModel.Builder tmpBuilder = Player.PlayerClanStoreModel.newBuilder();
            final int tmpFieldCnt = this.playerClanStoreModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerClanStoreModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerClanStoreModel();
            }
        }  else if (builder.hasPlayerClanStoreModel()) {
            // 清理PlayerClanStoreModel
            builder.clearPlayerClanStoreModel();
            fieldCnt++;
        }
        if (this.commissariatShopBuyInfo != null) {
            Struct.Int32ShopBuyInfoMap.Builder tmpBuilder = Struct.Int32ShopBuyInfoMap.newBuilder();
            final int tmpFieldCnt = this.commissariatShopBuyInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCommissariatShopBuyInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCommissariatShopBuyInfo();
            }
        }  else if (builder.hasCommissariatShopBuyInfo()) {
            // 清理CommissariatShopBuyInfo
            builder.clearCommissariatShopBuyInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerStoreModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERCLANSTOREMODEL) && this.playerClanStoreModel != null) {
            final boolean needClear = !builder.hasPlayerClanStoreModel();
            final int tmpFieldCnt = this.playerClanStoreModel.copyChangeToDb(builder.getPlayerClanStoreModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerClanStoreModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_COMMISSARIATSHOPBUYINFO) && this.commissariatShopBuyInfo != null) {
            final boolean needClear = !builder.hasCommissariatShopBuyInfo();
            final int tmpFieldCnt = this.commissariatShopBuyInfo.copyChangeToDb(builder.getCommissariatShopBuyInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCommissariatShopBuyInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerStoreModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerClanStoreModel()) {
            this.getPlayerClanStoreModel().mergeFromDb(proto.getPlayerClanStoreModel());
        } else {
            if (this.playerClanStoreModel != null) {
                this.playerClanStoreModel.mergeFromDb(proto.getPlayerClanStoreModel());
            }
        }
        if (proto.hasCommissariatShopBuyInfo()) {
            this.getCommissariatShopBuyInfo().mergeFromDb(proto.getCommissariatShopBuyInfo());
        } else {
            if (this.commissariatShopBuyInfo != null) {
                this.commissariatShopBuyInfo.mergeFromDb(proto.getCommissariatShopBuyInfo());
            }
        }
        this.markAll();
        return PlayerStoreModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerStoreModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerClanStoreModel()) {
            this.getPlayerClanStoreModel().mergeChangeFromDb(proto.getPlayerClanStoreModel());
            fieldCnt++;
        }
        if (proto.hasCommissariatShopBuyInfo()) {
            this.getCommissariatShopBuyInfo().mergeChangeFromDb(proto.getCommissariatShopBuyInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerStoreModel.Builder getCopySsBuilder() {
        final PlayerStoreModel.Builder builder = PlayerStoreModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerStoreModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.playerClanStoreModel != null) {
            Player.PlayerClanStoreModel.Builder tmpBuilder = Player.PlayerClanStoreModel.newBuilder();
            final int tmpFieldCnt = this.playerClanStoreModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerClanStoreModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerClanStoreModel();
            }
        }  else if (builder.hasPlayerClanStoreModel()) {
            // 清理PlayerClanStoreModel
            builder.clearPlayerClanStoreModel();
            fieldCnt++;
        }
        if (this.commissariatShopBuyInfo != null) {
            Struct.Int32ShopBuyInfoMap.Builder tmpBuilder = Struct.Int32ShopBuyInfoMap.newBuilder();
            final int tmpFieldCnt = this.commissariatShopBuyInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCommissariatShopBuyInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCommissariatShopBuyInfo();
            }
        }  else if (builder.hasCommissariatShopBuyInfo()) {
            // 清理CommissariatShopBuyInfo
            builder.clearCommissariatShopBuyInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerStoreModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERCLANSTOREMODEL) && this.playerClanStoreModel != null) {
            final boolean needClear = !builder.hasPlayerClanStoreModel();
            final int tmpFieldCnt = this.playerClanStoreModel.copyChangeToSs(builder.getPlayerClanStoreModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerClanStoreModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_COMMISSARIATSHOPBUYINFO) && this.commissariatShopBuyInfo != null) {
            final boolean needClear = !builder.hasCommissariatShopBuyInfo();
            final int tmpFieldCnt = this.commissariatShopBuyInfo.copyChangeToSs(builder.getCommissariatShopBuyInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCommissariatShopBuyInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerStoreModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerClanStoreModel()) {
            this.getPlayerClanStoreModel().mergeFromSs(proto.getPlayerClanStoreModel());
        } else {
            if (this.playerClanStoreModel != null) {
                this.playerClanStoreModel.mergeFromSs(proto.getPlayerClanStoreModel());
            }
        }
        if (proto.hasCommissariatShopBuyInfo()) {
            this.getCommissariatShopBuyInfo().mergeFromSs(proto.getCommissariatShopBuyInfo());
        } else {
            if (this.commissariatShopBuyInfo != null) {
                this.commissariatShopBuyInfo.mergeFromSs(proto.getCommissariatShopBuyInfo());
            }
        }
        this.markAll();
        return PlayerStoreModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerStoreModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerClanStoreModel()) {
            this.getPlayerClanStoreModel().mergeChangeFromSs(proto.getPlayerClanStoreModel());
            fieldCnt++;
        }
        if (proto.hasCommissariatShopBuyInfo()) {
            this.getCommissariatShopBuyInfo().mergeChangeFromSs(proto.getCommissariatShopBuyInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerStoreModel.Builder builder = PlayerStoreModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCLANSTOREMODEL) && this.playerClanStoreModel != null) {
            this.playerClanStoreModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_COMMISSARIATSHOPBUYINFO) && this.commissariatShopBuyInfo != null) {
            this.commissariatShopBuyInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.playerClanStoreModel != null) {
            this.playerClanStoreModel.markAll();
        }
        if (this.commissariatShopBuyInfo != null) {
            this.commissariatShopBuyInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerStoreModelProp)) {
            return false;
        }
        final PlayerStoreModelProp otherNode = (PlayerStoreModelProp) node;
        if (!this.getPlayerClanStoreModel().compareDataTo(otherNode.getPlayerClanStoreModel())) {
            return false;
        }
        if (!this.getCommissariatShopBuyInfo().compareDataTo(otherNode.getCommissariatShopBuyInfo())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
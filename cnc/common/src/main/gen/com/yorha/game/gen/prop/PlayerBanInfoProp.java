package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerBanInfo;
import com.yorha.proto.PlayerPB.PlayerBanInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerBanInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_BANENDTIME = 0;
    public static final int FIELD_INDEX_LASTBANREASONID = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private long banEndTime = Constant.DEFAULT_LONG_VALUE;
    private int lastBanReasonId = Constant.DEFAULT_INT_VALUE;

    public PlayerBanInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerBanInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get banEndTime
     *
     * @return banEndTime value
     */
    public long getBanEndTime() {
        return this.banEndTime;
    }

    /**
     * set banEndTime && set marked
     *
     * @param banEndTime new value
     * @return current object
     */
    public PlayerBanInfoProp setBanEndTime(long banEndTime) {
        if (this.banEndTime != banEndTime) {
            this.mark(FIELD_INDEX_BANENDTIME);
            this.banEndTime = banEndTime;
        }
        return this;
    }

    /**
     * inner set banEndTime
     *
     * @param banEndTime new value
     */
    private void innerSetBanEndTime(long banEndTime) {
        this.banEndTime = banEndTime;
    }

    /**
     * get lastBanReasonId
     *
     * @return lastBanReasonId value
     */
    public int getLastBanReasonId() {
        return this.lastBanReasonId;
    }

    /**
     * set lastBanReasonId && set marked
     *
     * @param lastBanReasonId new value
     * @return current object
     */
    public PlayerBanInfoProp setLastBanReasonId(int lastBanReasonId) {
        if (this.lastBanReasonId != lastBanReasonId) {
            this.mark(FIELD_INDEX_LASTBANREASONID);
            this.lastBanReasonId = lastBanReasonId;
        }
        return this;
    }

    /**
     * inner set lastBanReasonId
     *
     * @param lastBanReasonId new value
     */
    private void innerSetLastBanReasonId(int lastBanReasonId) {
        this.lastBanReasonId = lastBanReasonId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerBanInfoPB.Builder getCopyCsBuilder() {
        final PlayerBanInfoPB.Builder builder = PlayerBanInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerBanInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBanEndTime() != 0L) {
            builder.setBanEndTime(this.getBanEndTime());
            fieldCnt++;
        }  else if (builder.hasBanEndTime()) {
            // 清理BanEndTime
            builder.clearBanEndTime();
            fieldCnt++;
        }
        if (this.getLastBanReasonId() != 0) {
            builder.setLastBanReasonId(this.getLastBanReasonId());
            fieldCnt++;
        }  else if (builder.hasLastBanReasonId()) {
            // 清理LastBanReasonId
            builder.clearLastBanReasonId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerBanInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BANENDTIME)) {
            builder.setBanEndTime(this.getBanEndTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTBANREASONID)) {
            builder.setLastBanReasonId(this.getLastBanReasonId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerBanInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BANENDTIME)) {
            builder.setBanEndTime(this.getBanEndTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTBANREASONID)) {
            builder.setLastBanReasonId(this.getLastBanReasonId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerBanInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBanEndTime()) {
            this.innerSetBanEndTime(proto.getBanEndTime());
        } else {
            this.innerSetBanEndTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastBanReasonId()) {
            this.innerSetLastBanReasonId(proto.getLastBanReasonId());
        } else {
            this.innerSetLastBanReasonId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerBanInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerBanInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBanEndTime()) {
            this.setBanEndTime(proto.getBanEndTime());
            fieldCnt++;
        }
        if (proto.hasLastBanReasonId()) {
            this.setLastBanReasonId(proto.getLastBanReasonId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerBanInfo.Builder getCopyDbBuilder() {
        final PlayerBanInfo.Builder builder = PlayerBanInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerBanInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBanEndTime() != 0L) {
            builder.setBanEndTime(this.getBanEndTime());
            fieldCnt++;
        }  else if (builder.hasBanEndTime()) {
            // 清理BanEndTime
            builder.clearBanEndTime();
            fieldCnt++;
        }
        if (this.getLastBanReasonId() != 0) {
            builder.setLastBanReasonId(this.getLastBanReasonId());
            fieldCnt++;
        }  else if (builder.hasLastBanReasonId()) {
            // 清理LastBanReasonId
            builder.clearLastBanReasonId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerBanInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BANENDTIME)) {
            builder.setBanEndTime(this.getBanEndTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTBANREASONID)) {
            builder.setLastBanReasonId(this.getLastBanReasonId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerBanInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBanEndTime()) {
            this.innerSetBanEndTime(proto.getBanEndTime());
        } else {
            this.innerSetBanEndTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastBanReasonId()) {
            this.innerSetLastBanReasonId(proto.getLastBanReasonId());
        } else {
            this.innerSetLastBanReasonId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerBanInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerBanInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBanEndTime()) {
            this.setBanEndTime(proto.getBanEndTime());
            fieldCnt++;
        }
        if (proto.hasLastBanReasonId()) {
            this.setLastBanReasonId(proto.getLastBanReasonId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerBanInfo.Builder getCopySsBuilder() {
        final PlayerBanInfo.Builder builder = PlayerBanInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerBanInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBanEndTime() != 0L) {
            builder.setBanEndTime(this.getBanEndTime());
            fieldCnt++;
        }  else if (builder.hasBanEndTime()) {
            // 清理BanEndTime
            builder.clearBanEndTime();
            fieldCnt++;
        }
        if (this.getLastBanReasonId() != 0) {
            builder.setLastBanReasonId(this.getLastBanReasonId());
            fieldCnt++;
        }  else if (builder.hasLastBanReasonId()) {
            // 清理LastBanReasonId
            builder.clearLastBanReasonId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerBanInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BANENDTIME)) {
            builder.setBanEndTime(this.getBanEndTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTBANREASONID)) {
            builder.setLastBanReasonId(this.getLastBanReasonId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerBanInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBanEndTime()) {
            this.innerSetBanEndTime(proto.getBanEndTime());
        } else {
            this.innerSetBanEndTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastBanReasonId()) {
            this.innerSetLastBanReasonId(proto.getLastBanReasonId());
        } else {
            this.innerSetLastBanReasonId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerBanInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerBanInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBanEndTime()) {
            this.setBanEndTime(proto.getBanEndTime());
            fieldCnt++;
        }
        if (proto.hasLastBanReasonId()) {
            this.setLastBanReasonId(proto.getLastBanReasonId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerBanInfo.Builder builder = PlayerBanInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerBanInfoProp)) {
            return false;
        }
        final PlayerBanInfoProp otherNode = (PlayerBanInfoProp) node;
        if (this.banEndTime != otherNode.banEndTime) {
            return false;
        }
        if (this.lastBanReasonId != otherNode.lastBanReasonId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructMail.MailShowTitle;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMailPB.MailShowTitlePB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class MailShowTitleProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TITLE = 0;
    public static final int FIELD_INDEX_SUBTITLE = 1;
    public static final int FIELD_INDEX_TITLEDATA = 2;
    public static final int FIELD_INDEX_SUBTITLEDATA = 3;
    public static final int FIELD_INDEX_TITLEKEY = 4;
    public static final int FIELD_INDEX_SUBTITLEKEY = 5;
    public static final int FIELD_INDEX_EXTRANOTIFYDATA = 6;

    public static final int FIELD_COUNT = 7;

    private long markBits0 = 0L;

    private String title = Constant.DEFAULT_STR_VALUE;
    private String subTitle = Constant.DEFAULT_STR_VALUE;
    private DisplayDataProp titleData = null;
    private DisplayDataProp subTitleData = null;
    private String titleKey = Constant.DEFAULT_STR_VALUE;
    private String subTitleKey = Constant.DEFAULT_STR_VALUE;
    private DisplayDataProp extraNotifyData = null;

    public MailShowTitleProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MailShowTitleProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get title
     *
     * @return title value
     */
    public String getTitle() {
        return this.title;
    }

    /**
     * set title && set marked
     *
     * @param title new value
     * @return current object
     */
    public MailShowTitleProp setTitle(String title) {
        if (title == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.title, title)) {
            this.mark(FIELD_INDEX_TITLE);
            this.title = title;
        }
        return this;
    }

    /**
     * inner set title
     *
     * @param title new value
     */
    private void innerSetTitle(String title) {
        this.title = title;
    }

    /**
     * get subTitle
     *
     * @return subTitle value
     */
    public String getSubTitle() {
        return this.subTitle;
    }

    /**
     * set subTitle && set marked
     *
     * @param subTitle new value
     * @return current object
     */
    public MailShowTitleProp setSubTitle(String subTitle) {
        if (subTitle == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.subTitle, subTitle)) {
            this.mark(FIELD_INDEX_SUBTITLE);
            this.subTitle = subTitle;
        }
        return this;
    }

    /**
     * inner set subTitle
     *
     * @param subTitle new value
     */
    private void innerSetSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    /**
     * get titleData
     *
     * @return titleData value
     */
    public DisplayDataProp getTitleData() {
        if (this.titleData == null) {
            this.titleData = new DisplayDataProp(this, FIELD_INDEX_TITLEDATA);
        }
        return this.titleData;
    }

    /**
     * get subTitleData
     *
     * @return subTitleData value
     */
    public DisplayDataProp getSubTitleData() {
        if (this.subTitleData == null) {
            this.subTitleData = new DisplayDataProp(this, FIELD_INDEX_SUBTITLEDATA);
        }
        return this.subTitleData;
    }

    /**
     * get titleKey
     *
     * @return titleKey value
     */
    public String getTitleKey() {
        return this.titleKey;
    }

    /**
     * set titleKey && set marked
     *
     * @param titleKey new value
     * @return current object
     */
    public MailShowTitleProp setTitleKey(String titleKey) {
        if (titleKey == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.titleKey, titleKey)) {
            this.mark(FIELD_INDEX_TITLEKEY);
            this.titleKey = titleKey;
        }
        return this;
    }

    /**
     * inner set titleKey
     *
     * @param titleKey new value
     */
    private void innerSetTitleKey(String titleKey) {
        this.titleKey = titleKey;
    }

    /**
     * get subTitleKey
     *
     * @return subTitleKey value
     */
    public String getSubTitleKey() {
        return this.subTitleKey;
    }

    /**
     * set subTitleKey && set marked
     *
     * @param subTitleKey new value
     * @return current object
     */
    public MailShowTitleProp setSubTitleKey(String subTitleKey) {
        if (subTitleKey == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.subTitleKey, subTitleKey)) {
            this.mark(FIELD_INDEX_SUBTITLEKEY);
            this.subTitleKey = subTitleKey;
        }
        return this;
    }

    /**
     * inner set subTitleKey
     *
     * @param subTitleKey new value
     */
    private void innerSetSubTitleKey(String subTitleKey) {
        this.subTitleKey = subTitleKey;
    }

    /**
     * get extraNotifyData
     *
     * @return extraNotifyData value
     */
    public DisplayDataProp getExtraNotifyData() {
        if (this.extraNotifyData == null) {
            this.extraNotifyData = new DisplayDataProp(this, FIELD_INDEX_EXTRANOTIFYDATA);
        }
        return this.extraNotifyData;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailShowTitlePB.Builder getCopyCsBuilder() {
        final MailShowTitlePB.Builder builder = MailShowTitlePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MailShowTitlePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getTitle().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTitle(this.getTitle());
            fieldCnt++;
        }  else if (builder.hasTitle()) {
            // 清理Title
            builder.clearTitle();
            fieldCnt++;
        }
        if (!this.getSubTitle().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSubTitle(this.getSubTitle());
            fieldCnt++;
        }  else if (builder.hasSubTitle()) {
            // 清理SubTitle
            builder.clearSubTitle();
            fieldCnt++;
        }
        if (this.titleData != null) {
            StructPB.DisplayDataPB.Builder tmpBuilder = StructPB.DisplayDataPB.newBuilder();
            final int tmpFieldCnt = this.titleData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTitleData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTitleData();
            }
        }  else if (builder.hasTitleData()) {
            // 清理TitleData
            builder.clearTitleData();
            fieldCnt++;
        }
        if (this.subTitleData != null) {
            StructPB.DisplayDataPB.Builder tmpBuilder = StructPB.DisplayDataPB.newBuilder();
            final int tmpFieldCnt = this.subTitleData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSubTitleData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSubTitleData();
            }
        }  else if (builder.hasSubTitleData()) {
            // 清理SubTitleData
            builder.clearSubTitleData();
            fieldCnt++;
        }
        if (!this.getTitleKey().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTitleKey(this.getTitleKey());
            fieldCnt++;
        }  else if (builder.hasTitleKey()) {
            // 清理TitleKey
            builder.clearTitleKey();
            fieldCnt++;
        }
        if (!this.getSubTitleKey().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSubTitleKey(this.getSubTitleKey());
            fieldCnt++;
        }  else if (builder.hasSubTitleKey()) {
            // 清理SubTitleKey
            builder.clearSubTitleKey();
            fieldCnt++;
        }
        if (this.extraNotifyData != null) {
            StructPB.DisplayDataPB.Builder tmpBuilder = StructPB.DisplayDataPB.newBuilder();
            final int tmpFieldCnt = this.extraNotifyData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExtraNotifyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExtraNotifyData();
            }
        }  else if (builder.hasExtraNotifyData()) {
            // 清理ExtraNotifyData
            builder.clearExtraNotifyData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MailShowTitlePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TITLE)) {
            builder.setTitle(this.getTitle());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SUBTITLE)) {
            builder.setSubTitle(this.getSubTitle());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TITLEDATA) && this.titleData != null) {
            final boolean needClear = !builder.hasTitleData();
            final int tmpFieldCnt = this.titleData.copyChangeToCs(builder.getTitleDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTitleData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SUBTITLEDATA) && this.subTitleData != null) {
            final boolean needClear = !builder.hasSubTitleData();
            final int tmpFieldCnt = this.subTitleData.copyChangeToCs(builder.getSubTitleDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSubTitleData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TITLEKEY)) {
            builder.setTitleKey(this.getTitleKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SUBTITLEKEY)) {
            builder.setSubTitleKey(this.getSubTitleKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXTRANOTIFYDATA) && this.extraNotifyData != null) {
            final boolean needClear = !builder.hasExtraNotifyData();
            final int tmpFieldCnt = this.extraNotifyData.copyChangeToCs(builder.getExtraNotifyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExtraNotifyData();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MailShowTitlePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TITLE)) {
            builder.setTitle(this.getTitle());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SUBTITLE)) {
            builder.setSubTitle(this.getSubTitle());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TITLEDATA) && this.titleData != null) {
            final boolean needClear = !builder.hasTitleData();
            final int tmpFieldCnt = this.titleData.copyChangeToAndClearDeleteKeysCs(builder.getTitleDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTitleData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SUBTITLEDATA) && this.subTitleData != null) {
            final boolean needClear = !builder.hasSubTitleData();
            final int tmpFieldCnt = this.subTitleData.copyChangeToAndClearDeleteKeysCs(builder.getSubTitleDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSubTitleData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TITLEKEY)) {
            builder.setTitleKey(this.getTitleKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SUBTITLEKEY)) {
            builder.setSubTitleKey(this.getSubTitleKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXTRANOTIFYDATA) && this.extraNotifyData != null) {
            final boolean needClear = !builder.hasExtraNotifyData();
            final int tmpFieldCnt = this.extraNotifyData.copyChangeToAndClearDeleteKeysCs(builder.getExtraNotifyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExtraNotifyData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MailShowTitlePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTitle()) {
            this.innerSetTitle(proto.getTitle());
        } else {
            this.innerSetTitle(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasSubTitle()) {
            this.innerSetSubTitle(proto.getSubTitle());
        } else {
            this.innerSetSubTitle(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTitleData()) {
            this.getTitleData().mergeFromCs(proto.getTitleData());
        } else {
            if (this.titleData != null) {
                this.titleData.mergeFromCs(proto.getTitleData());
            }
        }
        if (proto.hasSubTitleData()) {
            this.getSubTitleData().mergeFromCs(proto.getSubTitleData());
        } else {
            if (this.subTitleData != null) {
                this.subTitleData.mergeFromCs(proto.getSubTitleData());
            }
        }
        if (proto.hasTitleKey()) {
            this.innerSetTitleKey(proto.getTitleKey());
        } else {
            this.innerSetTitleKey(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasSubTitleKey()) {
            this.innerSetSubTitleKey(proto.getSubTitleKey());
        } else {
            this.innerSetSubTitleKey(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasExtraNotifyData()) {
            this.getExtraNotifyData().mergeFromCs(proto.getExtraNotifyData());
        } else {
            if (this.extraNotifyData != null) {
                this.extraNotifyData.mergeFromCs(proto.getExtraNotifyData());
            }
        }
        this.markAll();
        return MailShowTitleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MailShowTitlePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTitle()) {
            this.setTitle(proto.getTitle());
            fieldCnt++;
        }
        if (proto.hasSubTitle()) {
            this.setSubTitle(proto.getSubTitle());
            fieldCnt++;
        }
        if (proto.hasTitleData()) {
            this.getTitleData().mergeChangeFromCs(proto.getTitleData());
            fieldCnt++;
        }
        if (proto.hasSubTitleData()) {
            this.getSubTitleData().mergeChangeFromCs(proto.getSubTitleData());
            fieldCnt++;
        }
        if (proto.hasTitleKey()) {
            this.setTitleKey(proto.getTitleKey());
            fieldCnt++;
        }
        if (proto.hasSubTitleKey()) {
            this.setSubTitleKey(proto.getSubTitleKey());
            fieldCnt++;
        }
        if (proto.hasExtraNotifyData()) {
            this.getExtraNotifyData().mergeChangeFromCs(proto.getExtraNotifyData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailShowTitle.Builder getCopyDbBuilder() {
        final MailShowTitle.Builder builder = MailShowTitle.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MailShowTitle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getTitle().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTitle(this.getTitle());
            fieldCnt++;
        }  else if (builder.hasTitle()) {
            // 清理Title
            builder.clearTitle();
            fieldCnt++;
        }
        if (!this.getSubTitle().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSubTitle(this.getSubTitle());
            fieldCnt++;
        }  else if (builder.hasSubTitle()) {
            // 清理SubTitle
            builder.clearSubTitle();
            fieldCnt++;
        }
        if (this.titleData != null) {
            Struct.DisplayData.Builder tmpBuilder = Struct.DisplayData.newBuilder();
            final int tmpFieldCnt = this.titleData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTitleData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTitleData();
            }
        }  else if (builder.hasTitleData()) {
            // 清理TitleData
            builder.clearTitleData();
            fieldCnt++;
        }
        if (this.subTitleData != null) {
            Struct.DisplayData.Builder tmpBuilder = Struct.DisplayData.newBuilder();
            final int tmpFieldCnt = this.subTitleData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSubTitleData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSubTitleData();
            }
        }  else if (builder.hasSubTitleData()) {
            // 清理SubTitleData
            builder.clearSubTitleData();
            fieldCnt++;
        }
        if (!this.getTitleKey().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTitleKey(this.getTitleKey());
            fieldCnt++;
        }  else if (builder.hasTitleKey()) {
            // 清理TitleKey
            builder.clearTitleKey();
            fieldCnt++;
        }
        if (!this.getSubTitleKey().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSubTitleKey(this.getSubTitleKey());
            fieldCnt++;
        }  else if (builder.hasSubTitleKey()) {
            // 清理SubTitleKey
            builder.clearSubTitleKey();
            fieldCnt++;
        }
        if (this.extraNotifyData != null) {
            Struct.DisplayData.Builder tmpBuilder = Struct.DisplayData.newBuilder();
            final int tmpFieldCnt = this.extraNotifyData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExtraNotifyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExtraNotifyData();
            }
        }  else if (builder.hasExtraNotifyData()) {
            // 清理ExtraNotifyData
            builder.clearExtraNotifyData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MailShowTitle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TITLE)) {
            builder.setTitle(this.getTitle());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SUBTITLE)) {
            builder.setSubTitle(this.getSubTitle());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TITLEDATA) && this.titleData != null) {
            final boolean needClear = !builder.hasTitleData();
            final int tmpFieldCnt = this.titleData.copyChangeToDb(builder.getTitleDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTitleData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SUBTITLEDATA) && this.subTitleData != null) {
            final boolean needClear = !builder.hasSubTitleData();
            final int tmpFieldCnt = this.subTitleData.copyChangeToDb(builder.getSubTitleDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSubTitleData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TITLEKEY)) {
            builder.setTitleKey(this.getTitleKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SUBTITLEKEY)) {
            builder.setSubTitleKey(this.getSubTitleKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXTRANOTIFYDATA) && this.extraNotifyData != null) {
            final boolean needClear = !builder.hasExtraNotifyData();
            final int tmpFieldCnt = this.extraNotifyData.copyChangeToDb(builder.getExtraNotifyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExtraNotifyData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MailShowTitle proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTitle()) {
            this.innerSetTitle(proto.getTitle());
        } else {
            this.innerSetTitle(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasSubTitle()) {
            this.innerSetSubTitle(proto.getSubTitle());
        } else {
            this.innerSetSubTitle(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTitleData()) {
            this.getTitleData().mergeFromDb(proto.getTitleData());
        } else {
            if (this.titleData != null) {
                this.titleData.mergeFromDb(proto.getTitleData());
            }
        }
        if (proto.hasSubTitleData()) {
            this.getSubTitleData().mergeFromDb(proto.getSubTitleData());
        } else {
            if (this.subTitleData != null) {
                this.subTitleData.mergeFromDb(proto.getSubTitleData());
            }
        }
        if (proto.hasTitleKey()) {
            this.innerSetTitleKey(proto.getTitleKey());
        } else {
            this.innerSetTitleKey(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasSubTitleKey()) {
            this.innerSetSubTitleKey(proto.getSubTitleKey());
        } else {
            this.innerSetSubTitleKey(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasExtraNotifyData()) {
            this.getExtraNotifyData().mergeFromDb(proto.getExtraNotifyData());
        } else {
            if (this.extraNotifyData != null) {
                this.extraNotifyData.mergeFromDb(proto.getExtraNotifyData());
            }
        }
        this.markAll();
        return MailShowTitleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MailShowTitle proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTitle()) {
            this.setTitle(proto.getTitle());
            fieldCnt++;
        }
        if (proto.hasSubTitle()) {
            this.setSubTitle(proto.getSubTitle());
            fieldCnt++;
        }
        if (proto.hasTitleData()) {
            this.getTitleData().mergeChangeFromDb(proto.getTitleData());
            fieldCnt++;
        }
        if (proto.hasSubTitleData()) {
            this.getSubTitleData().mergeChangeFromDb(proto.getSubTitleData());
            fieldCnt++;
        }
        if (proto.hasTitleKey()) {
            this.setTitleKey(proto.getTitleKey());
            fieldCnt++;
        }
        if (proto.hasSubTitleKey()) {
            this.setSubTitleKey(proto.getSubTitleKey());
            fieldCnt++;
        }
        if (proto.hasExtraNotifyData()) {
            this.getExtraNotifyData().mergeChangeFromDb(proto.getExtraNotifyData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailShowTitle.Builder getCopySsBuilder() {
        final MailShowTitle.Builder builder = MailShowTitle.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MailShowTitle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getTitle().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTitle(this.getTitle());
            fieldCnt++;
        }  else if (builder.hasTitle()) {
            // 清理Title
            builder.clearTitle();
            fieldCnt++;
        }
        if (!this.getSubTitle().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSubTitle(this.getSubTitle());
            fieldCnt++;
        }  else if (builder.hasSubTitle()) {
            // 清理SubTitle
            builder.clearSubTitle();
            fieldCnt++;
        }
        if (this.titleData != null) {
            Struct.DisplayData.Builder tmpBuilder = Struct.DisplayData.newBuilder();
            final int tmpFieldCnt = this.titleData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTitleData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTitleData();
            }
        }  else if (builder.hasTitleData()) {
            // 清理TitleData
            builder.clearTitleData();
            fieldCnt++;
        }
        if (this.subTitleData != null) {
            Struct.DisplayData.Builder tmpBuilder = Struct.DisplayData.newBuilder();
            final int tmpFieldCnt = this.subTitleData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSubTitleData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSubTitleData();
            }
        }  else if (builder.hasSubTitleData()) {
            // 清理SubTitleData
            builder.clearSubTitleData();
            fieldCnt++;
        }
        if (!this.getTitleKey().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTitleKey(this.getTitleKey());
            fieldCnt++;
        }  else if (builder.hasTitleKey()) {
            // 清理TitleKey
            builder.clearTitleKey();
            fieldCnt++;
        }
        if (!this.getSubTitleKey().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSubTitleKey(this.getSubTitleKey());
            fieldCnt++;
        }  else if (builder.hasSubTitleKey()) {
            // 清理SubTitleKey
            builder.clearSubTitleKey();
            fieldCnt++;
        }
        if (this.extraNotifyData != null) {
            Struct.DisplayData.Builder tmpBuilder = Struct.DisplayData.newBuilder();
            final int tmpFieldCnt = this.extraNotifyData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExtraNotifyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExtraNotifyData();
            }
        }  else if (builder.hasExtraNotifyData()) {
            // 清理ExtraNotifyData
            builder.clearExtraNotifyData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MailShowTitle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TITLE)) {
            builder.setTitle(this.getTitle());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SUBTITLE)) {
            builder.setSubTitle(this.getSubTitle());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TITLEDATA) && this.titleData != null) {
            final boolean needClear = !builder.hasTitleData();
            final int tmpFieldCnt = this.titleData.copyChangeToSs(builder.getTitleDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTitleData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SUBTITLEDATA) && this.subTitleData != null) {
            final boolean needClear = !builder.hasSubTitleData();
            final int tmpFieldCnt = this.subTitleData.copyChangeToSs(builder.getSubTitleDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSubTitleData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TITLEKEY)) {
            builder.setTitleKey(this.getTitleKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SUBTITLEKEY)) {
            builder.setSubTitleKey(this.getSubTitleKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXTRANOTIFYDATA) && this.extraNotifyData != null) {
            final boolean needClear = !builder.hasExtraNotifyData();
            final int tmpFieldCnt = this.extraNotifyData.copyChangeToSs(builder.getExtraNotifyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExtraNotifyData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MailShowTitle proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTitle()) {
            this.innerSetTitle(proto.getTitle());
        } else {
            this.innerSetTitle(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasSubTitle()) {
            this.innerSetSubTitle(proto.getSubTitle());
        } else {
            this.innerSetSubTitle(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTitleData()) {
            this.getTitleData().mergeFromSs(proto.getTitleData());
        } else {
            if (this.titleData != null) {
                this.titleData.mergeFromSs(proto.getTitleData());
            }
        }
        if (proto.hasSubTitleData()) {
            this.getSubTitleData().mergeFromSs(proto.getSubTitleData());
        } else {
            if (this.subTitleData != null) {
                this.subTitleData.mergeFromSs(proto.getSubTitleData());
            }
        }
        if (proto.hasTitleKey()) {
            this.innerSetTitleKey(proto.getTitleKey());
        } else {
            this.innerSetTitleKey(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasSubTitleKey()) {
            this.innerSetSubTitleKey(proto.getSubTitleKey());
        } else {
            this.innerSetSubTitleKey(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasExtraNotifyData()) {
            this.getExtraNotifyData().mergeFromSs(proto.getExtraNotifyData());
        } else {
            if (this.extraNotifyData != null) {
                this.extraNotifyData.mergeFromSs(proto.getExtraNotifyData());
            }
        }
        this.markAll();
        return MailShowTitleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MailShowTitle proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTitle()) {
            this.setTitle(proto.getTitle());
            fieldCnt++;
        }
        if (proto.hasSubTitle()) {
            this.setSubTitle(proto.getSubTitle());
            fieldCnt++;
        }
        if (proto.hasTitleData()) {
            this.getTitleData().mergeChangeFromSs(proto.getTitleData());
            fieldCnt++;
        }
        if (proto.hasSubTitleData()) {
            this.getSubTitleData().mergeChangeFromSs(proto.getSubTitleData());
            fieldCnt++;
        }
        if (proto.hasTitleKey()) {
            this.setTitleKey(proto.getTitleKey());
            fieldCnt++;
        }
        if (proto.hasSubTitleKey()) {
            this.setSubTitleKey(proto.getSubTitleKey());
            fieldCnt++;
        }
        if (proto.hasExtraNotifyData()) {
            this.getExtraNotifyData().mergeChangeFromSs(proto.getExtraNotifyData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MailShowTitle.Builder builder = MailShowTitle.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TITLEDATA) && this.titleData != null) {
            this.titleData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SUBTITLEDATA) && this.subTitleData != null) {
            this.subTitleData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_EXTRANOTIFYDATA) && this.extraNotifyData != null) {
            this.extraNotifyData.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.titleData != null) {
            this.titleData.markAll();
        }
        if (this.subTitleData != null) {
            this.subTitleData.markAll();
        }
        if (this.extraNotifyData != null) {
            this.extraNotifyData.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MailShowTitleProp)) {
            return false;
        }
        final MailShowTitleProp otherNode = (MailShowTitleProp) node;
        if (!com.yorha.gemini.utils.StringUtils.equals(this.title, otherNode.title)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.subTitle, otherNode.subTitle)) {
            return false;
        }
        if (!this.getTitleData().compareDataTo(otherNode.getTitleData())) {
            return false;
        }
        if (!this.getSubTitleData().compareDataTo(otherNode.getSubTitleData())) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.titleKey, otherNode.titleKey)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.subTitleKey, otherNode.subTitleKey)) {
            return false;
        }
        if (!this.getExtraNotifyData().compareDataTo(otherNode.getExtraNotifyData())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 57;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.YoTest.YoTestDeepestInnerMap;
import com.yorha.proto.YoTestPB.YoTestDeepestInnerMapPB;


/**
 * <AUTHOR> auto gen
 */
public class YoTestDeepestInnerMapProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_ENUMFIELD = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int id = Constant.DEFAULT_INT_VALUE;
    private YoTestEnumType enumField = YoTestEnumType.forNumber(0);

    public YoTestDeepestInnerMapProp() {
        super(null, 0, FIELD_COUNT);
    }

    public YoTestDeepestInnerMapProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public int getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public YoTestDeepestInnerMapProp setId(int id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(int id) {
        this.id = id;
    }

    /**
     * get enumField
     *
     * @return enumField value
     */
    public YoTestEnumType getEnumField() {
        return this.enumField;
    }

    /**
     * set enumField && set marked
     *
     * @param enumField new value
     * @return current object
     */
    public YoTestDeepestInnerMapProp setEnumField(YoTestEnumType enumField) {
        if (enumField == null) {
            throw new NullPointerException();
        }
        if (this.enumField != enumField) {
            this.mark(FIELD_INDEX_ENUMFIELD);
            this.enumField = enumField;
        }
        return this;
    }

    /**
     * inner set enumField
     *
     * @param enumField new value
     */
    private void innerSetEnumField(YoTestEnumType enumField) {
        this.enumField = enumField;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestDeepestInnerMapPB.Builder getCopyCsBuilder() {
        final YoTestDeepestInnerMapPB.Builder builder = YoTestDeepestInnerMapPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(YoTestDeepestInnerMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getEnumField() != YoTestEnumType.forNumber(0)) {
            builder.setEnumField(this.getEnumField());
            fieldCnt++;
        }  else if (builder.hasEnumField()) {
            // 清理EnumField
            builder.clearEnumField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(YoTestDeepestInnerMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENUMFIELD)) {
            builder.setEnumField(this.getEnumField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(YoTestDeepestInnerMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENUMFIELD)) {
            builder.setEnumField(this.getEnumField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(YoTestDeepestInnerMapPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEnumField()) {
            this.innerSetEnumField(proto.getEnumField());
        } else {
            this.innerSetEnumField(YoTestEnumType.forNumber(0));
        }
        this.markAll();
        return YoTestDeepestInnerMapProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(YoTestDeepestInnerMapPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasEnumField()) {
            this.setEnumField(proto.getEnumField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestDeepestInnerMap.Builder getCopyDbBuilder() {
        final YoTestDeepestInnerMap.Builder builder = YoTestDeepestInnerMap.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(YoTestDeepestInnerMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getEnumField() != YoTestEnumType.forNumber(0)) {
            builder.setEnumField(this.getEnumField());
            fieldCnt++;
        }  else if (builder.hasEnumField()) {
            // 清理EnumField
            builder.clearEnumField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(YoTestDeepestInnerMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENUMFIELD)) {
            builder.setEnumField(this.getEnumField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(YoTestDeepestInnerMap proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEnumField()) {
            this.innerSetEnumField(proto.getEnumField());
        } else {
            this.innerSetEnumField(YoTestEnumType.forNumber(0));
        }
        this.markAll();
        return YoTestDeepestInnerMapProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(YoTestDeepestInnerMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasEnumField()) {
            this.setEnumField(proto.getEnumField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestDeepestInnerMap.Builder getCopySsBuilder() {
        final YoTestDeepestInnerMap.Builder builder = YoTestDeepestInnerMap.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(YoTestDeepestInnerMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getEnumField() != YoTestEnumType.forNumber(0)) {
            builder.setEnumField(this.getEnumField());
            fieldCnt++;
        }  else if (builder.hasEnumField()) {
            // 清理EnumField
            builder.clearEnumField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(YoTestDeepestInnerMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENUMFIELD)) {
            builder.setEnumField(this.getEnumField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(YoTestDeepestInnerMap proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEnumField()) {
            this.innerSetEnumField(proto.getEnumField());
        } else {
            this.innerSetEnumField(YoTestEnumType.forNumber(0));
        }
        this.markAll();
        return YoTestDeepestInnerMapProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(YoTestDeepestInnerMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasEnumField()) {
            this.setEnumField(proto.getEnumField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        YoTestDeepestInnerMap.Builder builder = YoTestDeepestInnerMap.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof YoTestDeepestInnerMapProp)) {
            return false;
        }
        final YoTestDeepestInnerMapProp otherNode = (YoTestDeepestInnerMapProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.enumField != otherNode.enumField) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
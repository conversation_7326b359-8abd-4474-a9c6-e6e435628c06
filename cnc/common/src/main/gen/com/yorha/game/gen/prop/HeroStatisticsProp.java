package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.HeroStatistics;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.HeroStatisticsPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class HeroStatisticsProp extends AbstractPropNode {

    public static final int FIELD_INDEX_UNLOCKTSMS = 0;
    public static final int FIELD_INDEX_SKILLHISTORYCOST = 1;
    public static final int FIELD_INDEX_STARHISTORYCOST = 2;
    public static final int FIELD_INDEX_TOTALKILLSOLDIER = 3;
    public static final int FIELD_INDEX_TOTALKILLMONSTER = 4;
    public static final int FIELD_INDEX_AWAKENTSMS = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private long unlockTSMs = Constant.DEFAULT_LONG_VALUE;
    private Int32ItemPairMapProp skillHistoryCost = null;
    private Int32ItemPairMapProp starHistoryCost = null;
    private long totalKillSoldier = Constant.DEFAULT_LONG_VALUE;
    private long totalKillMonster = Constant.DEFAULT_LONG_VALUE;
    private long awakenTSMs = Constant.DEFAULT_LONG_VALUE;

    public HeroStatisticsProp() {
        super(null, 0, FIELD_COUNT);
    }

    public HeroStatisticsProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get unlockTSMs
     *
     * @return unlockTSMs value
     */
    public long getUnlockTSMs() {
        return this.unlockTSMs;
    }

    /**
     * set unlockTSMs && set marked
     *
     * @param unlockTSMs new value
     * @return current object
     */
    public HeroStatisticsProp setUnlockTSMs(long unlockTSMs) {
        if (this.unlockTSMs != unlockTSMs) {
            this.mark(FIELD_INDEX_UNLOCKTSMS);
            this.unlockTSMs = unlockTSMs;
        }
        return this;
    }

    /**
     * inner set unlockTSMs
     *
     * @param unlockTSMs new value
     */
    private void innerSetUnlockTSMs(long unlockTSMs) {
        this.unlockTSMs = unlockTSMs;
    }

    /**
     * get skillHistoryCost
     *
     * @return skillHistoryCost value
     */
    public Int32ItemPairMapProp getSkillHistoryCost() {
        if (this.skillHistoryCost == null) {
            this.skillHistoryCost = new Int32ItemPairMapProp(this, FIELD_INDEX_SKILLHISTORYCOST);
        }
        return this.skillHistoryCost;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putSkillHistoryCostV(ItemPairProp v) {
        this.getSkillHistoryCost().put(v.getItemTemplateId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ItemPairProp addEmptySkillHistoryCost(Integer k) {
        return this.getSkillHistoryCost().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getSkillHistoryCostSize() {
        if (this.skillHistoryCost == null) {
            return 0;
        }
        return this.skillHistoryCost.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isSkillHistoryCostEmpty() {
        if (this.skillHistoryCost == null) {
            return true;
        }
        return this.skillHistoryCost.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ItemPairProp getSkillHistoryCostV(Integer k) {
        if (this.skillHistoryCost == null || !this.skillHistoryCost.containsKey(k)) {
            return null;
        }
        return this.skillHistoryCost.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearSkillHistoryCost() {
        if (this.skillHistoryCost != null) {
            this.skillHistoryCost.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeSkillHistoryCostV(Integer k) {
        if (this.skillHistoryCost != null) {
            this.skillHistoryCost.remove(k);
        }
    }
    /**
     * get starHistoryCost
     *
     * @return starHistoryCost value
     */
    public Int32ItemPairMapProp getStarHistoryCost() {
        if (this.starHistoryCost == null) {
            this.starHistoryCost = new Int32ItemPairMapProp(this, FIELD_INDEX_STARHISTORYCOST);
        }
        return this.starHistoryCost;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putStarHistoryCostV(ItemPairProp v) {
        this.getStarHistoryCost().put(v.getItemTemplateId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ItemPairProp addEmptyStarHistoryCost(Integer k) {
        return this.getStarHistoryCost().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getStarHistoryCostSize() {
        if (this.starHistoryCost == null) {
            return 0;
        }
        return this.starHistoryCost.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isStarHistoryCostEmpty() {
        if (this.starHistoryCost == null) {
            return true;
        }
        return this.starHistoryCost.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ItemPairProp getStarHistoryCostV(Integer k) {
        if (this.starHistoryCost == null || !this.starHistoryCost.containsKey(k)) {
            return null;
        }
        return this.starHistoryCost.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearStarHistoryCost() {
        if (this.starHistoryCost != null) {
            this.starHistoryCost.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeStarHistoryCostV(Integer k) {
        if (this.starHistoryCost != null) {
            this.starHistoryCost.remove(k);
        }
    }
    /**
     * get totalKillSoldier
     *
     * @return totalKillSoldier value
     */
    public long getTotalKillSoldier() {
        return this.totalKillSoldier;
    }

    /**
     * set totalKillSoldier && set marked
     *
     * @param totalKillSoldier new value
     * @return current object
     */
    public HeroStatisticsProp setTotalKillSoldier(long totalKillSoldier) {
        if (this.totalKillSoldier != totalKillSoldier) {
            this.mark(FIELD_INDEX_TOTALKILLSOLDIER);
            this.totalKillSoldier = totalKillSoldier;
        }
        return this;
    }

    /**
     * inner set totalKillSoldier
     *
     * @param totalKillSoldier new value
     */
    private void innerSetTotalKillSoldier(long totalKillSoldier) {
        this.totalKillSoldier = totalKillSoldier;
    }

    /**
     * get totalKillMonster
     *
     * @return totalKillMonster value
     */
    public long getTotalKillMonster() {
        return this.totalKillMonster;
    }

    /**
     * set totalKillMonster && set marked
     *
     * @param totalKillMonster new value
     * @return current object
     */
    public HeroStatisticsProp setTotalKillMonster(long totalKillMonster) {
        if (this.totalKillMonster != totalKillMonster) {
            this.mark(FIELD_INDEX_TOTALKILLMONSTER);
            this.totalKillMonster = totalKillMonster;
        }
        return this;
    }

    /**
     * inner set totalKillMonster
     *
     * @param totalKillMonster new value
     */
    private void innerSetTotalKillMonster(long totalKillMonster) {
        this.totalKillMonster = totalKillMonster;
    }

    /**
     * get awakenTSMs
     *
     * @return awakenTSMs value
     */
    public long getAwakenTSMs() {
        return this.awakenTSMs;
    }

    /**
     * set awakenTSMs && set marked
     *
     * @param awakenTSMs new value
     * @return current object
     */
    public HeroStatisticsProp setAwakenTSMs(long awakenTSMs) {
        if (this.awakenTSMs != awakenTSMs) {
            this.mark(FIELD_INDEX_AWAKENTSMS);
            this.awakenTSMs = awakenTSMs;
        }
        return this;
    }

    /**
     * inner set awakenTSMs
     *
     * @param awakenTSMs new value
     */
    private void innerSetAwakenTSMs(long awakenTSMs) {
        this.awakenTSMs = awakenTSMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public HeroStatisticsPB.Builder getCopyCsBuilder() {
        final HeroStatisticsPB.Builder builder = HeroStatisticsPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(HeroStatisticsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUnlockTSMs() != 0L) {
            builder.setUnlockTSMs(this.getUnlockTSMs());
            fieldCnt++;
        }  else if (builder.hasUnlockTSMs()) {
            // 清理UnlockTSMs
            builder.clearUnlockTSMs();
            fieldCnt++;
        }
        if (this.skillHistoryCost != null) {
            StructPB.Int32ItemPairMapPB.Builder tmpBuilder = StructPB.Int32ItemPairMapPB.newBuilder();
            final int tmpFieldCnt = this.skillHistoryCost.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkillHistoryCost(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkillHistoryCost();
            }
        }  else if (builder.hasSkillHistoryCost()) {
            // 清理SkillHistoryCost
            builder.clearSkillHistoryCost();
            fieldCnt++;
        }
        if (this.starHistoryCost != null) {
            StructPB.Int32ItemPairMapPB.Builder tmpBuilder = StructPB.Int32ItemPairMapPB.newBuilder();
            final int tmpFieldCnt = this.starHistoryCost.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStarHistoryCost(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStarHistoryCost();
            }
        }  else if (builder.hasStarHistoryCost()) {
            // 清理StarHistoryCost
            builder.clearStarHistoryCost();
            fieldCnt++;
        }
        if (this.getTotalKillSoldier() != 0L) {
            builder.setTotalKillSoldier(this.getTotalKillSoldier());
            fieldCnt++;
        }  else if (builder.hasTotalKillSoldier()) {
            // 清理TotalKillSoldier
            builder.clearTotalKillSoldier();
            fieldCnt++;
        }
        if (this.getTotalKillMonster() != 0L) {
            builder.setTotalKillMonster(this.getTotalKillMonster());
            fieldCnt++;
        }  else if (builder.hasTotalKillMonster()) {
            // 清理TotalKillMonster
            builder.clearTotalKillMonster();
            fieldCnt++;
        }
        if (this.getAwakenTSMs() != 0L) {
            builder.setAwakenTSMs(this.getAwakenTSMs());
            fieldCnt++;
        }  else if (builder.hasAwakenTSMs()) {
            // 清理AwakenTSMs
            builder.clearAwakenTSMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(HeroStatisticsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNLOCKTSMS)) {
            builder.setUnlockTSMs(this.getUnlockTSMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLHISTORYCOST) && this.skillHistoryCost != null) {
            final boolean needClear = !builder.hasSkillHistoryCost();
            final int tmpFieldCnt = this.skillHistoryCost.copyChangeToCs(builder.getSkillHistoryCostBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillHistoryCost();
            }
        }
        if (this.hasMark(FIELD_INDEX_STARHISTORYCOST) && this.starHistoryCost != null) {
            final boolean needClear = !builder.hasStarHistoryCost();
            final int tmpFieldCnt = this.starHistoryCost.copyChangeToCs(builder.getStarHistoryCostBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStarHistoryCost();
            }
        }
        if (this.hasMark(FIELD_INDEX_TOTALKILLSOLDIER)) {
            builder.setTotalKillSoldier(this.getTotalKillSoldier());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALKILLMONSTER)) {
            builder.setTotalKillMonster(this.getTotalKillMonster());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_AWAKENTSMS)) {
            builder.setAwakenTSMs(this.getAwakenTSMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(HeroStatisticsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNLOCKTSMS)) {
            builder.setUnlockTSMs(this.getUnlockTSMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLHISTORYCOST) && this.skillHistoryCost != null) {
            final boolean needClear = !builder.hasSkillHistoryCost();
            final int tmpFieldCnt = this.skillHistoryCost.copyChangeToAndClearDeleteKeysCs(builder.getSkillHistoryCostBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillHistoryCost();
            }
        }
        if (this.hasMark(FIELD_INDEX_STARHISTORYCOST) && this.starHistoryCost != null) {
            final boolean needClear = !builder.hasStarHistoryCost();
            final int tmpFieldCnt = this.starHistoryCost.copyChangeToAndClearDeleteKeysCs(builder.getStarHistoryCostBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStarHistoryCost();
            }
        }
        if (this.hasMark(FIELD_INDEX_TOTALKILLSOLDIER)) {
            builder.setTotalKillSoldier(this.getTotalKillSoldier());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALKILLMONSTER)) {
            builder.setTotalKillMonster(this.getTotalKillMonster());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_AWAKENTSMS)) {
            builder.setAwakenTSMs(this.getAwakenTSMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(HeroStatisticsPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnlockTSMs()) {
            this.innerSetUnlockTSMs(proto.getUnlockTSMs());
        } else {
            this.innerSetUnlockTSMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSkillHistoryCost()) {
            this.getSkillHistoryCost().mergeFromCs(proto.getSkillHistoryCost());
        } else {
            if (this.skillHistoryCost != null) {
                this.skillHistoryCost.mergeFromCs(proto.getSkillHistoryCost());
            }
        }
        if (proto.hasStarHistoryCost()) {
            this.getStarHistoryCost().mergeFromCs(proto.getStarHistoryCost());
        } else {
            if (this.starHistoryCost != null) {
                this.starHistoryCost.mergeFromCs(proto.getStarHistoryCost());
            }
        }
        if (proto.hasTotalKillSoldier()) {
            this.innerSetTotalKillSoldier(proto.getTotalKillSoldier());
        } else {
            this.innerSetTotalKillSoldier(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTotalKillMonster()) {
            this.innerSetTotalKillMonster(proto.getTotalKillMonster());
        } else {
            this.innerSetTotalKillMonster(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasAwakenTSMs()) {
            this.innerSetAwakenTSMs(proto.getAwakenTSMs());
        } else {
            this.innerSetAwakenTSMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return HeroStatisticsProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(HeroStatisticsPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnlockTSMs()) {
            this.setUnlockTSMs(proto.getUnlockTSMs());
            fieldCnt++;
        }
        if (proto.hasSkillHistoryCost()) {
            this.getSkillHistoryCost().mergeChangeFromCs(proto.getSkillHistoryCost());
            fieldCnt++;
        }
        if (proto.hasStarHistoryCost()) {
            this.getStarHistoryCost().mergeChangeFromCs(proto.getStarHistoryCost());
            fieldCnt++;
        }
        if (proto.hasTotalKillSoldier()) {
            this.setTotalKillSoldier(proto.getTotalKillSoldier());
            fieldCnt++;
        }
        if (proto.hasTotalKillMonster()) {
            this.setTotalKillMonster(proto.getTotalKillMonster());
            fieldCnt++;
        }
        if (proto.hasAwakenTSMs()) {
            this.setAwakenTSMs(proto.getAwakenTSMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public HeroStatistics.Builder getCopyDbBuilder() {
        final HeroStatistics.Builder builder = HeroStatistics.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(HeroStatistics.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUnlockTSMs() != 0L) {
            builder.setUnlockTSMs(this.getUnlockTSMs());
            fieldCnt++;
        }  else if (builder.hasUnlockTSMs()) {
            // 清理UnlockTSMs
            builder.clearUnlockTSMs();
            fieldCnt++;
        }
        if (this.skillHistoryCost != null) {
            Struct.Int32ItemPairMap.Builder tmpBuilder = Struct.Int32ItemPairMap.newBuilder();
            final int tmpFieldCnt = this.skillHistoryCost.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkillHistoryCost(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkillHistoryCost();
            }
        }  else if (builder.hasSkillHistoryCost()) {
            // 清理SkillHistoryCost
            builder.clearSkillHistoryCost();
            fieldCnt++;
        }
        if (this.starHistoryCost != null) {
            Struct.Int32ItemPairMap.Builder tmpBuilder = Struct.Int32ItemPairMap.newBuilder();
            final int tmpFieldCnt = this.starHistoryCost.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStarHistoryCost(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStarHistoryCost();
            }
        }  else if (builder.hasStarHistoryCost()) {
            // 清理StarHistoryCost
            builder.clearStarHistoryCost();
            fieldCnt++;
        }
        if (this.getTotalKillSoldier() != 0L) {
            builder.setTotalKillSoldier(this.getTotalKillSoldier());
            fieldCnt++;
        }  else if (builder.hasTotalKillSoldier()) {
            // 清理TotalKillSoldier
            builder.clearTotalKillSoldier();
            fieldCnt++;
        }
        if (this.getTotalKillMonster() != 0L) {
            builder.setTotalKillMonster(this.getTotalKillMonster());
            fieldCnt++;
        }  else if (builder.hasTotalKillMonster()) {
            // 清理TotalKillMonster
            builder.clearTotalKillMonster();
            fieldCnt++;
        }
        if (this.getAwakenTSMs() != 0L) {
            builder.setAwakenTSMs(this.getAwakenTSMs());
            fieldCnt++;
        }  else if (builder.hasAwakenTSMs()) {
            // 清理AwakenTSMs
            builder.clearAwakenTSMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(HeroStatistics.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNLOCKTSMS)) {
            builder.setUnlockTSMs(this.getUnlockTSMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLHISTORYCOST) && this.skillHistoryCost != null) {
            final boolean needClear = !builder.hasSkillHistoryCost();
            final int tmpFieldCnt = this.skillHistoryCost.copyChangeToDb(builder.getSkillHistoryCostBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillHistoryCost();
            }
        }
        if (this.hasMark(FIELD_INDEX_STARHISTORYCOST) && this.starHistoryCost != null) {
            final boolean needClear = !builder.hasStarHistoryCost();
            final int tmpFieldCnt = this.starHistoryCost.copyChangeToDb(builder.getStarHistoryCostBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStarHistoryCost();
            }
        }
        if (this.hasMark(FIELD_INDEX_TOTALKILLSOLDIER)) {
            builder.setTotalKillSoldier(this.getTotalKillSoldier());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALKILLMONSTER)) {
            builder.setTotalKillMonster(this.getTotalKillMonster());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_AWAKENTSMS)) {
            builder.setAwakenTSMs(this.getAwakenTSMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(HeroStatistics proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnlockTSMs()) {
            this.innerSetUnlockTSMs(proto.getUnlockTSMs());
        } else {
            this.innerSetUnlockTSMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSkillHistoryCost()) {
            this.getSkillHistoryCost().mergeFromDb(proto.getSkillHistoryCost());
        } else {
            if (this.skillHistoryCost != null) {
                this.skillHistoryCost.mergeFromDb(proto.getSkillHistoryCost());
            }
        }
        if (proto.hasStarHistoryCost()) {
            this.getStarHistoryCost().mergeFromDb(proto.getStarHistoryCost());
        } else {
            if (this.starHistoryCost != null) {
                this.starHistoryCost.mergeFromDb(proto.getStarHistoryCost());
            }
        }
        if (proto.hasTotalKillSoldier()) {
            this.innerSetTotalKillSoldier(proto.getTotalKillSoldier());
        } else {
            this.innerSetTotalKillSoldier(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTotalKillMonster()) {
            this.innerSetTotalKillMonster(proto.getTotalKillMonster());
        } else {
            this.innerSetTotalKillMonster(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasAwakenTSMs()) {
            this.innerSetAwakenTSMs(proto.getAwakenTSMs());
        } else {
            this.innerSetAwakenTSMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return HeroStatisticsProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(HeroStatistics proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnlockTSMs()) {
            this.setUnlockTSMs(proto.getUnlockTSMs());
            fieldCnt++;
        }
        if (proto.hasSkillHistoryCost()) {
            this.getSkillHistoryCost().mergeChangeFromDb(proto.getSkillHistoryCost());
            fieldCnt++;
        }
        if (proto.hasStarHistoryCost()) {
            this.getStarHistoryCost().mergeChangeFromDb(proto.getStarHistoryCost());
            fieldCnt++;
        }
        if (proto.hasTotalKillSoldier()) {
            this.setTotalKillSoldier(proto.getTotalKillSoldier());
            fieldCnt++;
        }
        if (proto.hasTotalKillMonster()) {
            this.setTotalKillMonster(proto.getTotalKillMonster());
            fieldCnt++;
        }
        if (proto.hasAwakenTSMs()) {
            this.setAwakenTSMs(proto.getAwakenTSMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public HeroStatistics.Builder getCopySsBuilder() {
        final HeroStatistics.Builder builder = HeroStatistics.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(HeroStatistics.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUnlockTSMs() != 0L) {
            builder.setUnlockTSMs(this.getUnlockTSMs());
            fieldCnt++;
        }  else if (builder.hasUnlockTSMs()) {
            // 清理UnlockTSMs
            builder.clearUnlockTSMs();
            fieldCnt++;
        }
        if (this.skillHistoryCost != null) {
            Struct.Int32ItemPairMap.Builder tmpBuilder = Struct.Int32ItemPairMap.newBuilder();
            final int tmpFieldCnt = this.skillHistoryCost.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkillHistoryCost(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkillHistoryCost();
            }
        }  else if (builder.hasSkillHistoryCost()) {
            // 清理SkillHistoryCost
            builder.clearSkillHistoryCost();
            fieldCnt++;
        }
        if (this.starHistoryCost != null) {
            Struct.Int32ItemPairMap.Builder tmpBuilder = Struct.Int32ItemPairMap.newBuilder();
            final int tmpFieldCnt = this.starHistoryCost.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStarHistoryCost(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStarHistoryCost();
            }
        }  else if (builder.hasStarHistoryCost()) {
            // 清理StarHistoryCost
            builder.clearStarHistoryCost();
            fieldCnt++;
        }
        if (this.getTotalKillSoldier() != 0L) {
            builder.setTotalKillSoldier(this.getTotalKillSoldier());
            fieldCnt++;
        }  else if (builder.hasTotalKillSoldier()) {
            // 清理TotalKillSoldier
            builder.clearTotalKillSoldier();
            fieldCnt++;
        }
        if (this.getTotalKillMonster() != 0L) {
            builder.setTotalKillMonster(this.getTotalKillMonster());
            fieldCnt++;
        }  else if (builder.hasTotalKillMonster()) {
            // 清理TotalKillMonster
            builder.clearTotalKillMonster();
            fieldCnt++;
        }
        if (this.getAwakenTSMs() != 0L) {
            builder.setAwakenTSMs(this.getAwakenTSMs());
            fieldCnt++;
        }  else if (builder.hasAwakenTSMs()) {
            // 清理AwakenTSMs
            builder.clearAwakenTSMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(HeroStatistics.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNLOCKTSMS)) {
            builder.setUnlockTSMs(this.getUnlockTSMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLHISTORYCOST) && this.skillHistoryCost != null) {
            final boolean needClear = !builder.hasSkillHistoryCost();
            final int tmpFieldCnt = this.skillHistoryCost.copyChangeToSs(builder.getSkillHistoryCostBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillHistoryCost();
            }
        }
        if (this.hasMark(FIELD_INDEX_STARHISTORYCOST) && this.starHistoryCost != null) {
            final boolean needClear = !builder.hasStarHistoryCost();
            final int tmpFieldCnt = this.starHistoryCost.copyChangeToSs(builder.getStarHistoryCostBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStarHistoryCost();
            }
        }
        if (this.hasMark(FIELD_INDEX_TOTALKILLSOLDIER)) {
            builder.setTotalKillSoldier(this.getTotalKillSoldier());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALKILLMONSTER)) {
            builder.setTotalKillMonster(this.getTotalKillMonster());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_AWAKENTSMS)) {
            builder.setAwakenTSMs(this.getAwakenTSMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(HeroStatistics proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnlockTSMs()) {
            this.innerSetUnlockTSMs(proto.getUnlockTSMs());
        } else {
            this.innerSetUnlockTSMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSkillHistoryCost()) {
            this.getSkillHistoryCost().mergeFromSs(proto.getSkillHistoryCost());
        } else {
            if (this.skillHistoryCost != null) {
                this.skillHistoryCost.mergeFromSs(proto.getSkillHistoryCost());
            }
        }
        if (proto.hasStarHistoryCost()) {
            this.getStarHistoryCost().mergeFromSs(proto.getStarHistoryCost());
        } else {
            if (this.starHistoryCost != null) {
                this.starHistoryCost.mergeFromSs(proto.getStarHistoryCost());
            }
        }
        if (proto.hasTotalKillSoldier()) {
            this.innerSetTotalKillSoldier(proto.getTotalKillSoldier());
        } else {
            this.innerSetTotalKillSoldier(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTotalKillMonster()) {
            this.innerSetTotalKillMonster(proto.getTotalKillMonster());
        } else {
            this.innerSetTotalKillMonster(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasAwakenTSMs()) {
            this.innerSetAwakenTSMs(proto.getAwakenTSMs());
        } else {
            this.innerSetAwakenTSMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return HeroStatisticsProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(HeroStatistics proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnlockTSMs()) {
            this.setUnlockTSMs(proto.getUnlockTSMs());
            fieldCnt++;
        }
        if (proto.hasSkillHistoryCost()) {
            this.getSkillHistoryCost().mergeChangeFromSs(proto.getSkillHistoryCost());
            fieldCnt++;
        }
        if (proto.hasStarHistoryCost()) {
            this.getStarHistoryCost().mergeChangeFromSs(proto.getStarHistoryCost());
            fieldCnt++;
        }
        if (proto.hasTotalKillSoldier()) {
            this.setTotalKillSoldier(proto.getTotalKillSoldier());
            fieldCnt++;
        }
        if (proto.hasTotalKillMonster()) {
            this.setTotalKillMonster(proto.getTotalKillMonster());
            fieldCnt++;
        }
        if (proto.hasAwakenTSMs()) {
            this.setAwakenTSMs(proto.getAwakenTSMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        HeroStatistics.Builder builder = HeroStatistics.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SKILLHISTORYCOST) && this.skillHistoryCost != null) {
            this.skillHistoryCost.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_STARHISTORYCOST) && this.starHistoryCost != null) {
            this.starHistoryCost.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.skillHistoryCost != null) {
            this.skillHistoryCost.markAll();
        }
        if (this.starHistoryCost != null) {
            this.starHistoryCost.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof HeroStatisticsProp)) {
            return false;
        }
        final HeroStatisticsProp otherNode = (HeroStatisticsProp) node;
        if (this.unlockTSMs != otherNode.unlockTSMs) {
            return false;
        }
        if (!this.getSkillHistoryCost().compareDataTo(otherNode.getSkillHistoryCost())) {
            return false;
        }
        if (!this.getStarHistoryCost().compareDataTo(otherNode.getStarHistoryCost())) {
            return false;
        }
        if (this.totalKillSoldier != otherNode.totalKillSoldier) {
            return false;
        }
        if (this.totalKillMonster != otherNode.totalKillMonster) {
            return false;
        }
        if (this.awakenTSMs != otherNode.awakenTSMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
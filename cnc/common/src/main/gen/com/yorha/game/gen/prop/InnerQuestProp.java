package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Player.InnerQuest;
import com.yorha.proto.PlayerPB.InnerQuestPB;


/**
 * <AUTHOR> auto gen
 */
public class InnerQuestProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_CONFIGID = 0;
    public static final int FIELD_INDEX_ANSWERID = 1;
    public static final int FIELD_INDEX_STATUS = 2;
    public static final int FIELD_INDEX_STATUSSWITCHTSMS = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int configId = Constant.DEFAULT_INT_VALUE;
    private int answerId = Constant.DEFAULT_INT_VALUE;
    private QuestStatus status = QuestStatus.forNumber(0);
    private long statusSwitchTsMs = Constant.DEFAULT_LONG_VALUE;

    public InnerQuestProp() {
        super(null, 0, FIELD_COUNT);
    }

    public InnerQuestProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get configId
     *
     * @return configId value
     */
    public int getConfigId() {
        return this.configId;
    }

    /**
     * set configId && set marked
     *
     * @param configId new value
     * @return current object
     */
    public InnerQuestProp setConfigId(int configId) {
        if (this.configId != configId) {
            this.mark(FIELD_INDEX_CONFIGID);
            this.configId = configId;
        }
        return this;
    }

    /**
     * inner set configId
     *
     * @param configId new value
     */
    private void innerSetConfigId(int configId) {
        this.configId = configId;
    }

    /**
     * get answerId
     *
     * @return answerId value
     */
    public int getAnswerId() {
        return this.answerId;
    }

    /**
     * set answerId && set marked
     *
     * @param answerId new value
     * @return current object
     */
    public InnerQuestProp setAnswerId(int answerId) {
        if (this.answerId != answerId) {
            this.mark(FIELD_INDEX_ANSWERID);
            this.answerId = answerId;
        }
        return this;
    }

    /**
     * inner set answerId
     *
     * @param answerId new value
     */
    private void innerSetAnswerId(int answerId) {
        this.answerId = answerId;
    }

    /**
     * get status
     *
     * @return status value
     */
    public QuestStatus getStatus() {
        return this.status;
    }

    /**
     * set status && set marked
     *
     * @param status new value
     * @return current object
     */
    public InnerQuestProp setStatus(QuestStatus status) {
        if (status == null) {
            throw new NullPointerException();
        }
        if (this.status != status) {
            this.mark(FIELD_INDEX_STATUS);
            this.status = status;
        }
        return this;
    }

    /**
     * inner set status
     *
     * @param status new value
     */
    private void innerSetStatus(QuestStatus status) {
        this.status = status;
    }

    /**
     * get statusSwitchTsMs
     *
     * @return statusSwitchTsMs value
     */
    public long getStatusSwitchTsMs() {
        return this.statusSwitchTsMs;
    }

    /**
     * set statusSwitchTsMs && set marked
     *
     * @param statusSwitchTsMs new value
     * @return current object
     */
    public InnerQuestProp setStatusSwitchTsMs(long statusSwitchTsMs) {
        if (this.statusSwitchTsMs != statusSwitchTsMs) {
            this.mark(FIELD_INDEX_STATUSSWITCHTSMS);
            this.statusSwitchTsMs = statusSwitchTsMs;
        }
        return this;
    }

    /**
     * inner set statusSwitchTsMs
     *
     * @param statusSwitchTsMs new value
     */
    private void innerSetStatusSwitchTsMs(long statusSwitchTsMs) {
        this.statusSwitchTsMs = statusSwitchTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public InnerQuestPB.Builder getCopyCsBuilder() {
        final InnerQuestPB.Builder builder = InnerQuestPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(InnerQuestPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getConfigId() != 0) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }  else if (builder.hasConfigId()) {
            // 清理ConfigId
            builder.clearConfigId();
            fieldCnt++;
        }
        if (this.getAnswerId() != 0) {
            builder.setAnswerId(this.getAnswerId());
            fieldCnt++;
        }  else if (builder.hasAnswerId()) {
            // 清理AnswerId
            builder.clearAnswerId();
            fieldCnt++;
        }
        if (this.getStatus() != QuestStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.getStatusSwitchTsMs() != 0L) {
            builder.setStatusSwitchTsMs(this.getStatusSwitchTsMs());
            fieldCnt++;
        }  else if (builder.hasStatusSwitchTsMs()) {
            // 清理StatusSwitchTsMs
            builder.clearStatusSwitchTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(InnerQuestPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONFIGID)) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ANSWERID)) {
            builder.setAnswerId(this.getAnswerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUSSWITCHTSMS)) {
            builder.setStatusSwitchTsMs(this.getStatusSwitchTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(InnerQuestPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONFIGID)) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ANSWERID)) {
            builder.setAnswerId(this.getAnswerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUSSWITCHTSMS)) {
            builder.setStatusSwitchTsMs(this.getStatusSwitchTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(InnerQuestPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasConfigId()) {
            this.innerSetConfigId(proto.getConfigId());
        } else {
            this.innerSetConfigId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAnswerId()) {
            this.innerSetAnswerId(proto.getAnswerId());
        } else {
            this.innerSetAnswerId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(QuestStatus.forNumber(0));
        }
        if (proto.hasStatusSwitchTsMs()) {
            this.innerSetStatusSwitchTsMs(proto.getStatusSwitchTsMs());
        } else {
            this.innerSetStatusSwitchTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return InnerQuestProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(InnerQuestPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasConfigId()) {
            this.setConfigId(proto.getConfigId());
            fieldCnt++;
        }
        if (proto.hasAnswerId()) {
            this.setAnswerId(proto.getAnswerId());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasStatusSwitchTsMs()) {
            this.setStatusSwitchTsMs(proto.getStatusSwitchTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public InnerQuest.Builder getCopyDbBuilder() {
        final InnerQuest.Builder builder = InnerQuest.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(InnerQuest.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getConfigId() != 0) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }  else if (builder.hasConfigId()) {
            // 清理ConfigId
            builder.clearConfigId();
            fieldCnt++;
        }
        if (this.getAnswerId() != 0) {
            builder.setAnswerId(this.getAnswerId());
            fieldCnt++;
        }  else if (builder.hasAnswerId()) {
            // 清理AnswerId
            builder.clearAnswerId();
            fieldCnt++;
        }
        if (this.getStatus() != QuestStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.getStatusSwitchTsMs() != 0L) {
            builder.setStatusSwitchTsMs(this.getStatusSwitchTsMs());
            fieldCnt++;
        }  else if (builder.hasStatusSwitchTsMs()) {
            // 清理StatusSwitchTsMs
            builder.clearStatusSwitchTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(InnerQuest.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONFIGID)) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ANSWERID)) {
            builder.setAnswerId(this.getAnswerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUSSWITCHTSMS)) {
            builder.setStatusSwitchTsMs(this.getStatusSwitchTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(InnerQuest proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasConfigId()) {
            this.innerSetConfigId(proto.getConfigId());
        } else {
            this.innerSetConfigId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAnswerId()) {
            this.innerSetAnswerId(proto.getAnswerId());
        } else {
            this.innerSetAnswerId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(QuestStatus.forNumber(0));
        }
        if (proto.hasStatusSwitchTsMs()) {
            this.innerSetStatusSwitchTsMs(proto.getStatusSwitchTsMs());
        } else {
            this.innerSetStatusSwitchTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return InnerQuestProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(InnerQuest proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasConfigId()) {
            this.setConfigId(proto.getConfigId());
            fieldCnt++;
        }
        if (proto.hasAnswerId()) {
            this.setAnswerId(proto.getAnswerId());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasStatusSwitchTsMs()) {
            this.setStatusSwitchTsMs(proto.getStatusSwitchTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public InnerQuest.Builder getCopySsBuilder() {
        final InnerQuest.Builder builder = InnerQuest.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(InnerQuest.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getConfigId() != 0) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }  else if (builder.hasConfigId()) {
            // 清理ConfigId
            builder.clearConfigId();
            fieldCnt++;
        }
        if (this.getAnswerId() != 0) {
            builder.setAnswerId(this.getAnswerId());
            fieldCnt++;
        }  else if (builder.hasAnswerId()) {
            // 清理AnswerId
            builder.clearAnswerId();
            fieldCnt++;
        }
        if (this.getStatus() != QuestStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.getStatusSwitchTsMs() != 0L) {
            builder.setStatusSwitchTsMs(this.getStatusSwitchTsMs());
            fieldCnt++;
        }  else if (builder.hasStatusSwitchTsMs()) {
            // 清理StatusSwitchTsMs
            builder.clearStatusSwitchTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(InnerQuest.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONFIGID)) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ANSWERID)) {
            builder.setAnswerId(this.getAnswerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUSSWITCHTSMS)) {
            builder.setStatusSwitchTsMs(this.getStatusSwitchTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(InnerQuest proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasConfigId()) {
            this.innerSetConfigId(proto.getConfigId());
        } else {
            this.innerSetConfigId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAnswerId()) {
            this.innerSetAnswerId(proto.getAnswerId());
        } else {
            this.innerSetAnswerId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(QuestStatus.forNumber(0));
        }
        if (proto.hasStatusSwitchTsMs()) {
            this.innerSetStatusSwitchTsMs(proto.getStatusSwitchTsMs());
        } else {
            this.innerSetStatusSwitchTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return InnerQuestProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(InnerQuest proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasConfigId()) {
            this.setConfigId(proto.getConfigId());
            fieldCnt++;
        }
        if (proto.hasAnswerId()) {
            this.setAnswerId(proto.getAnswerId());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasStatusSwitchTsMs()) {
            this.setStatusSwitchTsMs(proto.getStatusSwitchTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        InnerQuest.Builder builder = InnerQuest.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.configId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof InnerQuestProp)) {
            return false;
        }
        final InnerQuestProp otherNode = (InnerQuestProp) node;
        if (this.configId != otherNode.configId) {
            return false;
        }
        if (this.answerId != otherNode.answerId) {
            return false;
        }
        if (this.status != otherNode.status) {
            return false;
        }
        if (this.statusSwitchTsMs != otherNode.statusSwitchTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Clan.ClanLogBasicInfo;
import com.yorha.proto.ClanPB.ClanLogBasicInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanLogBasicInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_MAXRECORDNUM = 0;
    public static final int FIELD_INDEX_NEXTRECORDID = 1;
    public static final int FIELD_INDEX_BEGINRECORDID = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private long maxRecordNum = Constant.DEFAULT_LONG_VALUE;
    private long nextRecordId = Constant.DEFAULT_LONG_VALUE;
    private long beginRecordId = Constant.DEFAULT_LONG_VALUE;

    public ClanLogBasicInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanLogBasicInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get maxRecordNum
     *
     * @return maxRecordNum value
     */
    public long getMaxRecordNum() {
        return this.maxRecordNum;
    }

    /**
     * set maxRecordNum && set marked
     *
     * @param maxRecordNum new value
     * @return current object
     */
    public ClanLogBasicInfoProp setMaxRecordNum(long maxRecordNum) {
        if (this.maxRecordNum != maxRecordNum) {
            this.mark(FIELD_INDEX_MAXRECORDNUM);
            this.maxRecordNum = maxRecordNum;
        }
        return this;
    }

    /**
     * inner set maxRecordNum
     *
     * @param maxRecordNum new value
     */
    private void innerSetMaxRecordNum(long maxRecordNum) {
        this.maxRecordNum = maxRecordNum;
    }

    /**
     * get nextRecordId
     *
     * @return nextRecordId value
     */
    public long getNextRecordId() {
        return this.nextRecordId;
    }

    /**
     * set nextRecordId && set marked
     *
     * @param nextRecordId new value
     * @return current object
     */
    public ClanLogBasicInfoProp setNextRecordId(long nextRecordId) {
        if (this.nextRecordId != nextRecordId) {
            this.mark(FIELD_INDEX_NEXTRECORDID);
            this.nextRecordId = nextRecordId;
        }
        return this;
    }

    /**
     * inner set nextRecordId
     *
     * @param nextRecordId new value
     */
    private void innerSetNextRecordId(long nextRecordId) {
        this.nextRecordId = nextRecordId;
    }

    /**
     * get beginRecordId
     *
     * @return beginRecordId value
     */
    public long getBeginRecordId() {
        return this.beginRecordId;
    }

    /**
     * set beginRecordId && set marked
     *
     * @param beginRecordId new value
     * @return current object
     */
    public ClanLogBasicInfoProp setBeginRecordId(long beginRecordId) {
        if (this.beginRecordId != beginRecordId) {
            this.mark(FIELD_INDEX_BEGINRECORDID);
            this.beginRecordId = beginRecordId;
        }
        return this;
    }

    /**
     * inner set beginRecordId
     *
     * @param beginRecordId new value
     */
    private void innerSetBeginRecordId(long beginRecordId) {
        this.beginRecordId = beginRecordId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanLogBasicInfoPB.Builder getCopyCsBuilder() {
        final ClanLogBasicInfoPB.Builder builder = ClanLogBasicInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanLogBasicInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMaxRecordNum() != 0L) {
            builder.setMaxRecordNum(this.getMaxRecordNum());
            fieldCnt++;
        }  else if (builder.hasMaxRecordNum()) {
            // 清理MaxRecordNum
            builder.clearMaxRecordNum();
            fieldCnt++;
        }
        if (this.getNextRecordId() != 0L) {
            builder.setNextRecordId(this.getNextRecordId());
            fieldCnt++;
        }  else if (builder.hasNextRecordId()) {
            // 清理NextRecordId
            builder.clearNextRecordId();
            fieldCnt++;
        }
        if (this.getBeginRecordId() != 0L) {
            builder.setBeginRecordId(this.getBeginRecordId());
            fieldCnt++;
        }  else if (builder.hasBeginRecordId()) {
            // 清理BeginRecordId
            builder.clearBeginRecordId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanLogBasicInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAXRECORDNUM)) {
            builder.setMaxRecordNum(this.getMaxRecordNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTRECORDID)) {
            builder.setNextRecordId(this.getNextRecordId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BEGINRECORDID)) {
            builder.setBeginRecordId(this.getBeginRecordId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanLogBasicInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAXRECORDNUM)) {
            builder.setMaxRecordNum(this.getMaxRecordNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTRECORDID)) {
            builder.setNextRecordId(this.getNextRecordId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BEGINRECORDID)) {
            builder.setBeginRecordId(this.getBeginRecordId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanLogBasicInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMaxRecordNum()) {
            this.innerSetMaxRecordNum(proto.getMaxRecordNum());
        } else {
            this.innerSetMaxRecordNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasNextRecordId()) {
            this.innerSetNextRecordId(proto.getNextRecordId());
        } else {
            this.innerSetNextRecordId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBeginRecordId()) {
            this.innerSetBeginRecordId(proto.getBeginRecordId());
        } else {
            this.innerSetBeginRecordId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanLogBasicInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanLogBasicInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMaxRecordNum()) {
            this.setMaxRecordNum(proto.getMaxRecordNum());
            fieldCnt++;
        }
        if (proto.hasNextRecordId()) {
            this.setNextRecordId(proto.getNextRecordId());
            fieldCnt++;
        }
        if (proto.hasBeginRecordId()) {
            this.setBeginRecordId(proto.getBeginRecordId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanLogBasicInfo.Builder getCopyDbBuilder() {
        final ClanLogBasicInfo.Builder builder = ClanLogBasicInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanLogBasicInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMaxRecordNum() != 0L) {
            builder.setMaxRecordNum(this.getMaxRecordNum());
            fieldCnt++;
        }  else if (builder.hasMaxRecordNum()) {
            // 清理MaxRecordNum
            builder.clearMaxRecordNum();
            fieldCnt++;
        }
        if (this.getNextRecordId() != 0L) {
            builder.setNextRecordId(this.getNextRecordId());
            fieldCnt++;
        }  else if (builder.hasNextRecordId()) {
            // 清理NextRecordId
            builder.clearNextRecordId();
            fieldCnt++;
        }
        if (this.getBeginRecordId() != 0L) {
            builder.setBeginRecordId(this.getBeginRecordId());
            fieldCnt++;
        }  else if (builder.hasBeginRecordId()) {
            // 清理BeginRecordId
            builder.clearBeginRecordId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanLogBasicInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAXRECORDNUM)) {
            builder.setMaxRecordNum(this.getMaxRecordNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTRECORDID)) {
            builder.setNextRecordId(this.getNextRecordId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BEGINRECORDID)) {
            builder.setBeginRecordId(this.getBeginRecordId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanLogBasicInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMaxRecordNum()) {
            this.innerSetMaxRecordNum(proto.getMaxRecordNum());
        } else {
            this.innerSetMaxRecordNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasNextRecordId()) {
            this.innerSetNextRecordId(proto.getNextRecordId());
        } else {
            this.innerSetNextRecordId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBeginRecordId()) {
            this.innerSetBeginRecordId(proto.getBeginRecordId());
        } else {
            this.innerSetBeginRecordId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanLogBasicInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanLogBasicInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMaxRecordNum()) {
            this.setMaxRecordNum(proto.getMaxRecordNum());
            fieldCnt++;
        }
        if (proto.hasNextRecordId()) {
            this.setNextRecordId(proto.getNextRecordId());
            fieldCnt++;
        }
        if (proto.hasBeginRecordId()) {
            this.setBeginRecordId(proto.getBeginRecordId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanLogBasicInfo.Builder getCopySsBuilder() {
        final ClanLogBasicInfo.Builder builder = ClanLogBasicInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanLogBasicInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMaxRecordNum() != 0L) {
            builder.setMaxRecordNum(this.getMaxRecordNum());
            fieldCnt++;
        }  else if (builder.hasMaxRecordNum()) {
            // 清理MaxRecordNum
            builder.clearMaxRecordNum();
            fieldCnt++;
        }
        if (this.getNextRecordId() != 0L) {
            builder.setNextRecordId(this.getNextRecordId());
            fieldCnt++;
        }  else if (builder.hasNextRecordId()) {
            // 清理NextRecordId
            builder.clearNextRecordId();
            fieldCnt++;
        }
        if (this.getBeginRecordId() != 0L) {
            builder.setBeginRecordId(this.getBeginRecordId());
            fieldCnt++;
        }  else if (builder.hasBeginRecordId()) {
            // 清理BeginRecordId
            builder.clearBeginRecordId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanLogBasicInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAXRECORDNUM)) {
            builder.setMaxRecordNum(this.getMaxRecordNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTRECORDID)) {
            builder.setNextRecordId(this.getNextRecordId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BEGINRECORDID)) {
            builder.setBeginRecordId(this.getBeginRecordId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanLogBasicInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMaxRecordNum()) {
            this.innerSetMaxRecordNum(proto.getMaxRecordNum());
        } else {
            this.innerSetMaxRecordNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasNextRecordId()) {
            this.innerSetNextRecordId(proto.getNextRecordId());
        } else {
            this.innerSetNextRecordId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBeginRecordId()) {
            this.innerSetBeginRecordId(proto.getBeginRecordId());
        } else {
            this.innerSetBeginRecordId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanLogBasicInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanLogBasicInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMaxRecordNum()) {
            this.setMaxRecordNum(proto.getMaxRecordNum());
            fieldCnt++;
        }
        if (proto.hasNextRecordId()) {
            this.setNextRecordId(proto.getNextRecordId());
            fieldCnt++;
        }
        if (proto.hasBeginRecordId()) {
            this.setBeginRecordId(proto.getBeginRecordId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanLogBasicInfo.Builder builder = ClanLogBasicInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanLogBasicInfoProp)) {
            return false;
        }
        final ClanLogBasicInfoProp otherNode = (ClanLogBasicInfoProp) node;
        if (this.maxRecordNum != otherNode.maxRecordNum) {
            return false;
        }
        if (this.nextRecordId != otherNode.nextRecordId) {
            return false;
        }
        if (this.beginRecordId != otherNode.beginRecordId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
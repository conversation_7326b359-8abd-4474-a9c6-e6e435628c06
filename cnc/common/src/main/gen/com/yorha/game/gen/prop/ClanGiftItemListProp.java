package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructClanPB.ClanGiftItemListPB;
import com.yorha.proto.StructClan.ClanGiftItemList;
import com.yorha.proto.StructClanPB.ClanGiftItemPB;
import com.yorha.proto.StructClan.ClanGiftItem;

/**
 * <AUTHOR> auto gen
 */
public class ClanGiftItemListProp extends AbstractListNode<ClanGiftItemProp> {
    /**
     * Create a ClanGiftItemListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public ClanGiftItemListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to ClanGiftItemListProp
     *
     * @return new object
     */
    @Override
    public ClanGiftItemProp addEmptyValue() {
        final ClanGiftItemProp newProp = new ClanGiftItemProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanGiftItemListPB.Builder getCopyCsBuilder() {
        final ClanGiftItemListPB.Builder builder = ClanGiftItemListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(ClanGiftItemListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return ClanGiftItemListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final ClanGiftItemProp v : this) {
            final ClanGiftItemPB.Builder itemBuilder = ClanGiftItemPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return ClanGiftItemListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(ClanGiftItemListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return ClanGiftItemListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(ClanGiftItemListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (ClanGiftItemPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return ClanGiftItemListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(ClanGiftItemListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanGiftItemList.Builder getCopyDbBuilder() {
        final ClanGiftItemList.Builder builder = ClanGiftItemList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(ClanGiftItemList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return ClanGiftItemListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final ClanGiftItemProp v : this) {
            final ClanGiftItem.Builder itemBuilder = ClanGiftItem.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return ClanGiftItemListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(ClanGiftItemList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return ClanGiftItemListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(ClanGiftItemList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (ClanGiftItem v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return ClanGiftItemListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(ClanGiftItemList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanGiftItemList.Builder getCopySsBuilder() {
        final ClanGiftItemList.Builder builder = ClanGiftItemList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(ClanGiftItemList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return ClanGiftItemListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final ClanGiftItemProp v : this) {
            final ClanGiftItem.Builder itemBuilder = ClanGiftItem.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return ClanGiftItemListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(ClanGiftItemList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return ClanGiftItemListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(ClanGiftItemList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (ClanGiftItem v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return ClanGiftItemListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(ClanGiftItemList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        ClanGiftItemList.Builder builder = ClanGiftItemList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
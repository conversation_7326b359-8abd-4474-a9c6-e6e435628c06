package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ScenePlayerRallyBase;
import com.yorha.proto.StructPB.ScenePlayerRallyBasePB;


/**
 * <AUTHOR> auto gen
 */
public class ScenePlayerRallyBaseProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CURRALLYID = 0;
    public static final int FIELD_INDEX_RALLYMAXCAP = 1;
    public static final int FIELD_INDEX_BEAIDEDMAXCAP = 2;
    public static final int FIELD_INDEX_ASSISTARMYNUM = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private long curRallyId = Constant.DEFAULT_LONG_VALUE;
    private int rallyMaxCap = Constant.DEFAULT_INT_VALUE;
    private int beAidedMaxCap = Constant.DEFAULT_INT_VALUE;
    private int assistArmyNum = Constant.DEFAULT_INT_VALUE;

    public ScenePlayerRallyBaseProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ScenePlayerRallyBaseProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get curRallyId
     *
     * @return curRallyId value
     */
    public long getCurRallyId() {
        return this.curRallyId;
    }

    /**
     * set curRallyId && set marked
     *
     * @param curRallyId new value
     * @return current object
     */
    public ScenePlayerRallyBaseProp setCurRallyId(long curRallyId) {
        if (this.curRallyId != curRallyId) {
            this.mark(FIELD_INDEX_CURRALLYID);
            this.curRallyId = curRallyId;
        }
        return this;
    }

    /**
     * inner set curRallyId
     *
     * @param curRallyId new value
     */
    private void innerSetCurRallyId(long curRallyId) {
        this.curRallyId = curRallyId;
    }

    /**
     * get rallyMaxCap
     *
     * @return rallyMaxCap value
     */
    public int getRallyMaxCap() {
        return this.rallyMaxCap;
    }

    /**
     * set rallyMaxCap && set marked
     *
     * @param rallyMaxCap new value
     * @return current object
     */
    public ScenePlayerRallyBaseProp setRallyMaxCap(int rallyMaxCap) {
        if (this.rallyMaxCap != rallyMaxCap) {
            this.mark(FIELD_INDEX_RALLYMAXCAP);
            this.rallyMaxCap = rallyMaxCap;
        }
        return this;
    }

    /**
     * inner set rallyMaxCap
     *
     * @param rallyMaxCap new value
     */
    private void innerSetRallyMaxCap(int rallyMaxCap) {
        this.rallyMaxCap = rallyMaxCap;
    }

    /**
     * get beAidedMaxCap
     *
     * @return beAidedMaxCap value
     */
    public int getBeAidedMaxCap() {
        return this.beAidedMaxCap;
    }

    /**
     * set beAidedMaxCap && set marked
     *
     * @param beAidedMaxCap new value
     * @return current object
     */
    public ScenePlayerRallyBaseProp setBeAidedMaxCap(int beAidedMaxCap) {
        if (this.beAidedMaxCap != beAidedMaxCap) {
            this.mark(FIELD_INDEX_BEAIDEDMAXCAP);
            this.beAidedMaxCap = beAidedMaxCap;
        }
        return this;
    }

    /**
     * inner set beAidedMaxCap
     *
     * @param beAidedMaxCap new value
     */
    private void innerSetBeAidedMaxCap(int beAidedMaxCap) {
        this.beAidedMaxCap = beAidedMaxCap;
    }

    /**
     * get assistArmyNum
     *
     * @return assistArmyNum value
     */
    public int getAssistArmyNum() {
        return this.assistArmyNum;
    }

    /**
     * set assistArmyNum && set marked
     *
     * @param assistArmyNum new value
     * @return current object
     */
    public ScenePlayerRallyBaseProp setAssistArmyNum(int assistArmyNum) {
        if (this.assistArmyNum != assistArmyNum) {
            this.mark(FIELD_INDEX_ASSISTARMYNUM);
            this.assistArmyNum = assistArmyNum;
        }
        return this;
    }

    /**
     * inner set assistArmyNum
     *
     * @param assistArmyNum new value
     */
    private void innerSetAssistArmyNum(int assistArmyNum) {
        this.assistArmyNum = assistArmyNum;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerRallyBasePB.Builder getCopyCsBuilder() {
        final ScenePlayerRallyBasePB.Builder builder = ScenePlayerRallyBasePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ScenePlayerRallyBasePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurRallyId() != 0L) {
            builder.setCurRallyId(this.getCurRallyId());
            fieldCnt++;
        }  else if (builder.hasCurRallyId()) {
            // 清理CurRallyId
            builder.clearCurRallyId();
            fieldCnt++;
        }
        if (this.getRallyMaxCap() != 0) {
            builder.setRallyMaxCap(this.getRallyMaxCap());
            fieldCnt++;
        }  else if (builder.hasRallyMaxCap()) {
            // 清理RallyMaxCap
            builder.clearRallyMaxCap();
            fieldCnt++;
        }
        if (this.getBeAidedMaxCap() != 0) {
            builder.setBeAidedMaxCap(this.getBeAidedMaxCap());
            fieldCnt++;
        }  else if (builder.hasBeAidedMaxCap()) {
            // 清理BeAidedMaxCap
            builder.clearBeAidedMaxCap();
            fieldCnt++;
        }
        if (this.getAssistArmyNum() != 0) {
            builder.setAssistArmyNum(this.getAssistArmyNum());
            fieldCnt++;
        }  else if (builder.hasAssistArmyNum()) {
            // 清理AssistArmyNum
            builder.clearAssistArmyNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ScenePlayerRallyBasePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURRALLYID)) {
            builder.setCurRallyId(this.getCurRallyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLYMAXCAP)) {
            builder.setRallyMaxCap(this.getRallyMaxCap());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BEAIDEDMAXCAP)) {
            builder.setBeAidedMaxCap(this.getBeAidedMaxCap());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ASSISTARMYNUM)) {
            builder.setAssistArmyNum(this.getAssistArmyNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ScenePlayerRallyBasePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURRALLYID)) {
            builder.setCurRallyId(this.getCurRallyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLYMAXCAP)) {
            builder.setRallyMaxCap(this.getRallyMaxCap());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BEAIDEDMAXCAP)) {
            builder.setBeAidedMaxCap(this.getBeAidedMaxCap());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ASSISTARMYNUM)) {
            builder.setAssistArmyNum(this.getAssistArmyNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ScenePlayerRallyBasePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurRallyId()) {
            this.innerSetCurRallyId(proto.getCurRallyId());
        } else {
            this.innerSetCurRallyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRallyMaxCap()) {
            this.innerSetRallyMaxCap(proto.getRallyMaxCap());
        } else {
            this.innerSetRallyMaxCap(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBeAidedMaxCap()) {
            this.innerSetBeAidedMaxCap(proto.getBeAidedMaxCap());
        } else {
            this.innerSetBeAidedMaxCap(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAssistArmyNum()) {
            this.innerSetAssistArmyNum(proto.getAssistArmyNum());
        } else {
            this.innerSetAssistArmyNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ScenePlayerRallyBaseProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ScenePlayerRallyBasePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurRallyId()) {
            this.setCurRallyId(proto.getCurRallyId());
            fieldCnt++;
        }
        if (proto.hasRallyMaxCap()) {
            this.setRallyMaxCap(proto.getRallyMaxCap());
            fieldCnt++;
        }
        if (proto.hasBeAidedMaxCap()) {
            this.setBeAidedMaxCap(proto.getBeAidedMaxCap());
            fieldCnt++;
        }
        if (proto.hasAssistArmyNum()) {
            this.setAssistArmyNum(proto.getAssistArmyNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerRallyBase.Builder getCopyDbBuilder() {
        final ScenePlayerRallyBase.Builder builder = ScenePlayerRallyBase.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ScenePlayerRallyBase.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurRallyId() != 0L) {
            builder.setCurRallyId(this.getCurRallyId());
            fieldCnt++;
        }  else if (builder.hasCurRallyId()) {
            // 清理CurRallyId
            builder.clearCurRallyId();
            fieldCnt++;
        }
        if (this.getRallyMaxCap() != 0) {
            builder.setRallyMaxCap(this.getRallyMaxCap());
            fieldCnt++;
        }  else if (builder.hasRallyMaxCap()) {
            // 清理RallyMaxCap
            builder.clearRallyMaxCap();
            fieldCnt++;
        }
        if (this.getBeAidedMaxCap() != 0) {
            builder.setBeAidedMaxCap(this.getBeAidedMaxCap());
            fieldCnt++;
        }  else if (builder.hasBeAidedMaxCap()) {
            // 清理BeAidedMaxCap
            builder.clearBeAidedMaxCap();
            fieldCnt++;
        }
        if (this.getAssistArmyNum() != 0) {
            builder.setAssistArmyNum(this.getAssistArmyNum());
            fieldCnt++;
        }  else if (builder.hasAssistArmyNum()) {
            // 清理AssistArmyNum
            builder.clearAssistArmyNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ScenePlayerRallyBase.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURRALLYID)) {
            builder.setCurRallyId(this.getCurRallyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLYMAXCAP)) {
            builder.setRallyMaxCap(this.getRallyMaxCap());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BEAIDEDMAXCAP)) {
            builder.setBeAidedMaxCap(this.getBeAidedMaxCap());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ASSISTARMYNUM)) {
            builder.setAssistArmyNum(this.getAssistArmyNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ScenePlayerRallyBase proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurRallyId()) {
            this.innerSetCurRallyId(proto.getCurRallyId());
        } else {
            this.innerSetCurRallyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRallyMaxCap()) {
            this.innerSetRallyMaxCap(proto.getRallyMaxCap());
        } else {
            this.innerSetRallyMaxCap(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBeAidedMaxCap()) {
            this.innerSetBeAidedMaxCap(proto.getBeAidedMaxCap());
        } else {
            this.innerSetBeAidedMaxCap(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAssistArmyNum()) {
            this.innerSetAssistArmyNum(proto.getAssistArmyNum());
        } else {
            this.innerSetAssistArmyNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ScenePlayerRallyBaseProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ScenePlayerRallyBase proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurRallyId()) {
            this.setCurRallyId(proto.getCurRallyId());
            fieldCnt++;
        }
        if (proto.hasRallyMaxCap()) {
            this.setRallyMaxCap(proto.getRallyMaxCap());
            fieldCnt++;
        }
        if (proto.hasBeAidedMaxCap()) {
            this.setBeAidedMaxCap(proto.getBeAidedMaxCap());
            fieldCnt++;
        }
        if (proto.hasAssistArmyNum()) {
            this.setAssistArmyNum(proto.getAssistArmyNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerRallyBase.Builder getCopySsBuilder() {
        final ScenePlayerRallyBase.Builder builder = ScenePlayerRallyBase.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ScenePlayerRallyBase.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurRallyId() != 0L) {
            builder.setCurRallyId(this.getCurRallyId());
            fieldCnt++;
        }  else if (builder.hasCurRallyId()) {
            // 清理CurRallyId
            builder.clearCurRallyId();
            fieldCnt++;
        }
        if (this.getRallyMaxCap() != 0) {
            builder.setRallyMaxCap(this.getRallyMaxCap());
            fieldCnt++;
        }  else if (builder.hasRallyMaxCap()) {
            // 清理RallyMaxCap
            builder.clearRallyMaxCap();
            fieldCnt++;
        }
        if (this.getBeAidedMaxCap() != 0) {
            builder.setBeAidedMaxCap(this.getBeAidedMaxCap());
            fieldCnt++;
        }  else if (builder.hasBeAidedMaxCap()) {
            // 清理BeAidedMaxCap
            builder.clearBeAidedMaxCap();
            fieldCnt++;
        }
        if (this.getAssistArmyNum() != 0) {
            builder.setAssistArmyNum(this.getAssistArmyNum());
            fieldCnt++;
        }  else if (builder.hasAssistArmyNum()) {
            // 清理AssistArmyNum
            builder.clearAssistArmyNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ScenePlayerRallyBase.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURRALLYID)) {
            builder.setCurRallyId(this.getCurRallyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLYMAXCAP)) {
            builder.setRallyMaxCap(this.getRallyMaxCap());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BEAIDEDMAXCAP)) {
            builder.setBeAidedMaxCap(this.getBeAidedMaxCap());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ASSISTARMYNUM)) {
            builder.setAssistArmyNum(this.getAssistArmyNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ScenePlayerRallyBase proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurRallyId()) {
            this.innerSetCurRallyId(proto.getCurRallyId());
        } else {
            this.innerSetCurRallyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRallyMaxCap()) {
            this.innerSetRallyMaxCap(proto.getRallyMaxCap());
        } else {
            this.innerSetRallyMaxCap(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBeAidedMaxCap()) {
            this.innerSetBeAidedMaxCap(proto.getBeAidedMaxCap());
        } else {
            this.innerSetBeAidedMaxCap(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAssistArmyNum()) {
            this.innerSetAssistArmyNum(proto.getAssistArmyNum());
        } else {
            this.innerSetAssistArmyNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ScenePlayerRallyBaseProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ScenePlayerRallyBase proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurRallyId()) {
            this.setCurRallyId(proto.getCurRallyId());
            fieldCnt++;
        }
        if (proto.hasRallyMaxCap()) {
            this.setRallyMaxCap(proto.getRallyMaxCap());
            fieldCnt++;
        }
        if (proto.hasBeAidedMaxCap()) {
            this.setBeAidedMaxCap(proto.getBeAidedMaxCap());
            fieldCnt++;
        }
        if (proto.hasAssistArmyNum()) {
            this.setAssistArmyNum(proto.getAssistArmyNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ScenePlayerRallyBase.Builder builder = ScenePlayerRallyBase.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ScenePlayerRallyBaseProp)) {
            return false;
        }
        final ScenePlayerRallyBaseProp otherNode = (ScenePlayerRallyBaseProp) node;
        if (this.curRallyId != otherNode.curRallyId) {
            return false;
        }
        if (this.rallyMaxCap != otherNode.rallyMaxCap) {
            return false;
        }
        if (this.beAidedMaxCap != otherNode.beAidedMaxCap) {
            return false;
        }
        if (this.assistArmyNum != otherNode.assistArmyNum) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
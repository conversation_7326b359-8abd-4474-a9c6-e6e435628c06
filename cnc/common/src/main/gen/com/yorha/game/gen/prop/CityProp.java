package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.City.CityEntity;
import com.yorha.proto.City;
import com.yorha.proto.Struct;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.CityPB.CityEntityPB;
import com.yorha.proto.CityPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructBattlePB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class CityProp extends AbstractPropNode {

    public static final int FIELD_INDEX_POINT = 0;
    public static final int FIELD_INDEX_OWNERID = 1;
    public static final int FIELD_INDEX_LEVEL = 2;
    public static final int FIELD_INDEX_CAMP = 3;
    public static final int FIELD_INDEX_WALLSTATE = 4;
    public static final int FIELD_INDEX_TROOP = 5;
    public static final int FIELD_INDEX_BATTLE = 6;
    public static final int FIELD_INDEX_CLANSNAME = 7;
    public static final int FIELD_INDEX_CLANID = 8;
    public static final int FIELD_INDEX_INNERARMY = 9;
    public static final int FIELD_INDEX_GARRISON = 10;
    public static final int FIELD_INDEX_BUFFSYS = 11;
    public static final int FIELD_INDEX_PEACESHIELD = 12;
    public static final int FIELD_INDEX_DEVBUFFSYS = 13;
    public static final int FIELD_INDEX_ASCENDREASON = 14;
    public static final int FIELD_INDEX_SAFEGUARD = 15;
    public static final int FIELD_INDEX_CARDHEAD = 16;
    public static final int FIELD_INDEX_ARROW = 17;
    public static final int FIELD_INDEX_ERALEVEL = 18;
    public static final int FIELD_INDEX_TEMPLATEID = 19;
    public static final int FIELD_INDEX_GUARDTOWERHP = 20;
    public static final int FIELD_INDEX_EXPRESSION = 21;
    public static final int FIELD_INDEX_PFLAGID = 22;
    public static final int FIELD_INDEX_DRESSTEMPLATEID = 23;
    public static final int FIELD_INDEX_DRESSTIMEOUTTSMS = 24;
    public static final int FIELD_INDEX_CITYKINGDOMMODEL = 25;
    public static final int FIELD_INDEX_NAMEPLATEID = 26;
    public static final int FIELD_INDEX_NAMEPLATETIMEOUTTSMS = 27;
    public static final int FIELD_INDEX_ZONEID = 28;

    public static final int FIELD_COUNT = 29;

    private long markBits0 = 0L;
    private PropertyChangeListener listener;

    private PointProp point = null;
    private long ownerId = Constant.DEFAULT_LONG_VALUE;
    private int level = Constant.DEFAULT_INT_VALUE;
    private Camp camp = Camp.forNumber(0);
    private CityWallState wallState = CityWallState.forNumber(0);
    private TroopProp troop = null;
    private BattleProp battle = null;
    private String clanSname = Constant.DEFAULT_STR_VALUE;
    private long clanId = Constant.DEFAULT_LONG_VALUE;
    private CityInnerArmyProp innerArmy = null;
    private CityGarrisonProp garrison = null;
    private BuffSysProp buffSys = null;
    private PeaceShieldProp peaceShield = null;
    private SceneDevBuffSysProp devBuffSys = null;
    private CityAscendReason ascendReason = CityAscendReason.forNumber(0);
    private SpecialSafeGuardProp safeGuard = null;
    private PlayerCardHeadProp cardHead = null;
    private Int64ArmyArrowItemMapProp arrow = null;
    private int eraLevel = Constant.DEFAULT_INT_VALUE;
    private int templateId = Constant.DEFAULT_INT_VALUE;
    private int guardTowerHp = Constant.DEFAULT_INT_VALUE;
    private ExpressionProp expression = null;
    private int pFlagId = Constant.DEFAULT_INT_VALUE;
    private int dressTemplateId = Constant.DEFAULT_INT_VALUE;
    private long dressTimeoutTsMs = Constant.DEFAULT_LONG_VALUE;
    private CityKingdomModelProp cityKingdomModel = null;
    private int nameplateId = Constant.DEFAULT_INT_VALUE;
    private long nameplateTimeoutTsMs = Constant.DEFAULT_LONG_VALUE;
    private int zoneId = Constant.DEFAULT_INT_VALUE;

    public CityProp() {
        super(null, 0, FIELD_COUNT);
    }

    public CityProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get point
     *
     * @return point value
     */
    public PointProp getPoint() {
        if (this.point == null) {
            this.point = new PointProp(this, FIELD_INDEX_POINT);
        }
        return this.point;
    }

    /**
     * get ownerId
     *
     * @return ownerId value
     */
    public long getOwnerId() {
        return this.ownerId;
    }

    /**
     * set ownerId && set marked
     *
     * @param ownerId new value
     * @return current object
     */
    public CityProp setOwnerId(long ownerId) {
        if (this.ownerId != ownerId) {
            this.mark(FIELD_INDEX_OWNERID);
            this.ownerId = ownerId;
        }
        return this;
    }

    /**
     * inner set ownerId
     *
     * @param ownerId new value
     */
    private void innerSetOwnerId(long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * get level
     *
     * @return level value
     */
    public int getLevel() {
        return this.level;
    }

    /**
     * set level && set marked
     *
     * @param level new value
     * @return current object
     */
    public CityProp setLevel(int level) {
        if (this.level != level) {
            this.mark(FIELD_INDEX_LEVEL);
            this.level = level;
        }
        return this;
    }

    /**
     * inner set level
     *
     * @param level new value
     */
    private void innerSetLevel(int level) {
        this.level = level;
    }

    /**
     * get camp
     *
     * @return camp value
     */
    public Camp getCamp() {
        return this.camp;
    }

    /**
     * set camp && set marked
     *
     * @param camp new value
     * @return current object
     */
    public CityProp setCamp(Camp camp) {
        if (camp == null) {
            throw new NullPointerException();
        }
        if (this.camp != camp) {
            this.mark(FIELD_INDEX_CAMP);
            this.camp = camp;
        }
        return this;
    }

    /**
     * inner set camp
     *
     * @param camp new value
     */
    private void innerSetCamp(Camp camp) {
        this.camp = camp;
    }

    /**
     * get wallState
     *
     * @return wallState value
     */
    public CityWallState getWallState() {
        return this.wallState;
    }

    /**
     * set wallState && set marked
     *
     * @param wallState new value
     * @return current object
     */
    public CityProp setWallState(CityWallState wallState) {
        if (wallState == null) {
            throw new NullPointerException();
        }
        if (this.wallState != wallState) {
            this.mark(FIELD_INDEX_WALLSTATE);
            this.wallState = wallState;
        }
        return this;
    }

    /**
     * inner set wallState
     *
     * @param wallState new value
     */
    private void innerSetWallState(CityWallState wallState) {
        this.wallState = wallState;
    }

    /**
     * get troop
     *
     * @return troop value
     */
    public TroopProp getTroop() {
        if (this.troop == null) {
            this.troop = new TroopProp(this, FIELD_INDEX_TROOP);
        }
        return this.troop;
    }

    /**
     * get battle
     *
     * @return battle value
     */
    public BattleProp getBattle() {
        if (this.battle == null) {
            this.battle = new BattleProp(this, FIELD_INDEX_BATTLE);
        }
        return this.battle;
    }

    /**
     * get clanSname
     *
     * @return clanSname value
     */
    public String getClanSname() {
        return this.clanSname;
    }

    /**
     * set clanSname && set marked
     *
     * @param clanSname new value
     * @return current object
     */
    public CityProp setClanSname(String clanSname) {
        if (clanSname == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanSname, clanSname)) {
            this.mark(FIELD_INDEX_CLANSNAME);
            this.clanSname = clanSname;
        }
        return this;
    }

    /**
     * inner set clanSname
     *
     * @param clanSname new value
     */
    private void innerSetClanSname(String clanSname) {
        this.clanSname = clanSname;
    }

    /**
     * get clanId
     *
     * @return clanId value
     */
    public long getClanId() {
        return this.clanId;
    }

    /**
     * set clanId && set marked
     *
     * @param clanId new value
     * @return current object
     */
    public CityProp setClanId(long clanId) {
        if (this.clanId != clanId) {
            this.mark(FIELD_INDEX_CLANID);
            this.clanId = clanId;
        }
        return this;
    }

    /**
     * inner set clanId
     *
     * @param clanId new value
     */
    private void innerSetClanId(long clanId) {
        this.clanId = clanId;
    }

    /**
     * get innerArmy
     *
     * @return innerArmy value
     */
    public CityInnerArmyProp getInnerArmy() {
        if (this.innerArmy == null) {
            this.innerArmy = new CityInnerArmyProp(this, FIELD_INDEX_INNERARMY);
        }
        return this.innerArmy;
    }

    /**
     * get garrison
     *
     * @return garrison value
     */
    public CityGarrisonProp getGarrison() {
        if (this.garrison == null) {
            this.garrison = new CityGarrisonProp(this, FIELD_INDEX_GARRISON);
        }
        return this.garrison;
    }

    /**
     * get buffSys
     *
     * @return buffSys value
     */
    public BuffSysProp getBuffSys() {
        if (this.buffSys == null) {
            this.buffSys = new BuffSysProp(this, FIELD_INDEX_BUFFSYS);
        }
        return this.buffSys;
    }

    /**
     * get peaceShield
     *
     * @return peaceShield value
     */
    public PeaceShieldProp getPeaceShield() {
        if (this.peaceShield == null) {
            this.peaceShield = new PeaceShieldProp(this, FIELD_INDEX_PEACESHIELD);
        }
        return this.peaceShield;
    }

    /**
     * get devBuffSys
     *
     * @return devBuffSys value
     */
    public SceneDevBuffSysProp getDevBuffSys() {
        if (this.devBuffSys == null) {
            this.devBuffSys = new SceneDevBuffSysProp(this, FIELD_INDEX_DEVBUFFSYS);
        }
        return this.devBuffSys;
    }

    /**
     * get ascendReason
     *
     * @return ascendReason value
     */
    public CityAscendReason getAscendReason() {
        return this.ascendReason;
    }

    /**
     * set ascendReason && set marked
     *
     * @param ascendReason new value
     * @return current object
     */
    public CityProp setAscendReason(CityAscendReason ascendReason) {
        if (ascendReason == null) {
            throw new NullPointerException();
        }
        if (this.ascendReason != ascendReason) {
            this.mark(FIELD_INDEX_ASCENDREASON);
            this.ascendReason = ascendReason;
        }
        return this;
    }

    /**
     * inner set ascendReason
     *
     * @param ascendReason new value
     */
    private void innerSetAscendReason(CityAscendReason ascendReason) {
        this.ascendReason = ascendReason;
    }

    /**
     * get safeGuard
     *
     * @return safeGuard value
     */
    public SpecialSafeGuardProp getSafeGuard() {
        if (this.safeGuard == null) {
            this.safeGuard = new SpecialSafeGuardProp(this, FIELD_INDEX_SAFEGUARD);
        }
        return this.safeGuard;
    }

    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }

    /**
     * get arrow
     *
     * @return arrow value
     */
    public Int64ArmyArrowItemMapProp getArrow() {
        if (this.arrow == null) {
            this.arrow = new Int64ArmyArrowItemMapProp(this, FIELD_INDEX_ARROW);
        }
        return this.arrow;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putArrowV(ArmyArrowItemProp v) {
        this.getArrow().put(v.getArmyId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ArmyArrowItemProp addEmptyArrow(Long k) {
        return this.getArrow().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getArrowSize() {
        if (this.arrow == null) {
            return 0;
        }
        return this.arrow.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isArrowEmpty() {
        if (this.arrow == null) {
            return true;
        }
        return this.arrow.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ArmyArrowItemProp getArrowV(Long k) {
        if (this.arrow == null || !this.arrow.containsKey(k)) {
            return null;
        }
        return this.arrow.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearArrow() {
        if (this.arrow != null) {
            this.arrow.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeArrowV(Long k) {
        if (this.arrow != null) {
            this.arrow.remove(k);
        }
    }
    /**
     * get eraLevel
     *
     * @return eraLevel value
     */
    public int getEraLevel() {
        return this.eraLevel;
    }

    /**
     * set eraLevel && set marked
     *
     * @param eraLevel new value
     * @return current object
     */
    public CityProp setEraLevel(int eraLevel) {
        if (this.eraLevel != eraLevel) {
            this.mark(FIELD_INDEX_ERALEVEL);
            this.eraLevel = eraLevel;
        }
        return this;
    }

    /**
     * inner set eraLevel
     *
     * @param eraLevel new value
     */
    private void innerSetEraLevel(int eraLevel) {
        this.eraLevel = eraLevel;
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public CityProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }

    /**
     * get guardTowerHp
     *
     * @return guardTowerHp value
     */
    public int getGuardTowerHp() {
        return this.guardTowerHp;
    }

    /**
     * set guardTowerHp && set marked
     *
     * @param guardTowerHp new value
     * @return current object
     */
    public CityProp setGuardTowerHp(int guardTowerHp) {
        if (this.guardTowerHp != guardTowerHp) {
            this.mark(FIELD_INDEX_GUARDTOWERHP);
            this.guardTowerHp = guardTowerHp;
        }
        return this;
    }

    /**
     * inner set guardTowerHp
     *
     * @param guardTowerHp new value
     */
    private void innerSetGuardTowerHp(int guardTowerHp) {
        this.guardTowerHp = guardTowerHp;
    }

    /**
     * get expression
     *
     * @return expression value
     */
    public ExpressionProp getExpression() {
        if (this.expression == null) {
            this.expression = new ExpressionProp(this, FIELD_INDEX_EXPRESSION);
        }
        return this.expression;
    }

    /**
     * get pFlagId
     *
     * @return pFlagId value
     */
    public int getPFlagId() {
        return this.pFlagId;
    }

    /**
     * set pFlagId && set marked
     *
     * @param pFlagId new value
     * @return current object
     */
    public CityProp setPFlagId(int pFlagId) {
        if (this.pFlagId != pFlagId) {
            this.mark(FIELD_INDEX_PFLAGID);
            this.pFlagId = pFlagId;
        }
        return this;
    }

    /**
     * inner set pFlagId
     *
     * @param pFlagId new value
     */
    private void innerSetPFlagId(int pFlagId) {
        this.pFlagId = pFlagId;
    }

    /**
     * get dressTemplateId
     *
     * @return dressTemplateId value
     */
    public int getDressTemplateId() {
        return this.dressTemplateId;
    }

    /**
     * set dressTemplateId && set marked
     *
     * @param dressTemplateId new value
     * @return current object
     */
    public CityProp setDressTemplateId(int dressTemplateId) {
        if (this.dressTemplateId != dressTemplateId) {
            this.mark(FIELD_INDEX_DRESSTEMPLATEID);
            this.dressTemplateId = dressTemplateId;
        }
        return this;
    }

    /**
     * inner set dressTemplateId
     *
     * @param dressTemplateId new value
     */
    private void innerSetDressTemplateId(int dressTemplateId) {
        this.dressTemplateId = dressTemplateId;
    }

    /**
     * get dressTimeoutTsMs
     *
     * @return dressTimeoutTsMs value
     */
    public long getDressTimeoutTsMs() {
        return this.dressTimeoutTsMs;
    }

    /**
     * set dressTimeoutTsMs && set marked
     *
     * @param dressTimeoutTsMs new value
     * @return current object
     */
    public CityProp setDressTimeoutTsMs(long dressTimeoutTsMs) {
        if (this.dressTimeoutTsMs != dressTimeoutTsMs) {
            this.mark(FIELD_INDEX_DRESSTIMEOUTTSMS);
            this.dressTimeoutTsMs = dressTimeoutTsMs;
        }
        return this;
    }

    /**
     * inner set dressTimeoutTsMs
     *
     * @param dressTimeoutTsMs new value
     */
    private void innerSetDressTimeoutTsMs(long dressTimeoutTsMs) {
        this.dressTimeoutTsMs = dressTimeoutTsMs;
    }

    /**
     * get cityKingdomModel
     *
     * @return cityKingdomModel value
     */
    public CityKingdomModelProp getCityKingdomModel() {
        if (this.cityKingdomModel == null) {
            this.cityKingdomModel = new CityKingdomModelProp(this, FIELD_INDEX_CITYKINGDOMMODEL);
        }
        return this.cityKingdomModel;
    }

    /**
     * get nameplateId
     *
     * @return nameplateId value
     */
    public int getNameplateId() {
        return this.nameplateId;
    }

    /**
     * set nameplateId && set marked
     *
     * @param nameplateId new value
     * @return current object
     */
    public CityProp setNameplateId(int nameplateId) {
        if (this.nameplateId != nameplateId) {
            this.mark(FIELD_INDEX_NAMEPLATEID);
            this.nameplateId = nameplateId;
        }
        return this;
    }

    /**
     * inner set nameplateId
     *
     * @param nameplateId new value
     */
    private void innerSetNameplateId(int nameplateId) {
        this.nameplateId = nameplateId;
    }

    /**
     * get nameplateTimeoutTsMs
     *
     * @return nameplateTimeoutTsMs value
     */
    public long getNameplateTimeoutTsMs() {
        return this.nameplateTimeoutTsMs;
    }

    /**
     * set nameplateTimeoutTsMs && set marked
     *
     * @param nameplateTimeoutTsMs new value
     * @return current object
     */
    public CityProp setNameplateTimeoutTsMs(long nameplateTimeoutTsMs) {
        if (this.nameplateTimeoutTsMs != nameplateTimeoutTsMs) {
            this.mark(FIELD_INDEX_NAMEPLATETIMEOUTTSMS);
            this.nameplateTimeoutTsMs = nameplateTimeoutTsMs;
        }
        return this;
    }

    /**
     * inner set nameplateTimeoutTsMs
     *
     * @param nameplateTimeoutTsMs new value
     */
    private void innerSetNameplateTimeoutTsMs(long nameplateTimeoutTsMs) {
        this.nameplateTimeoutTsMs = nameplateTimeoutTsMs;
    }

    /**
     * get zoneId
     *
     * @return zoneId value
     */
    public int getZoneId() {
        return this.zoneId;
    }

    /**
     * set zoneId && set marked
     *
     * @param zoneId new value
     * @return current object
     */
    public CityProp setZoneId(int zoneId) {
        if (this.zoneId != zoneId) {
            this.mark(FIELD_INDEX_ZONEID);
            this.zoneId = zoneId;
        }
        return this;
    }

    /**
     * inner set zoneId
     *
     * @param zoneId new value
     */
    private void innerSetZoneId(int zoneId) {
        this.zoneId = zoneId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CityEntityPB.Builder getCopyCsBuilder() {
        final CityEntityPB.Builder builder = CityEntityPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(CityEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.point != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.point.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getCamp() != Camp.forNumber(0)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }  else if (builder.hasCamp()) {
            // 清理Camp
            builder.clearCamp();
            fieldCnt++;
        }
        if (this.getWallState() != CityWallState.forNumber(0)) {
            builder.setWallState(this.getWallState());
            fieldCnt++;
        }  else if (builder.hasWallState()) {
            // 清理WallState
            builder.clearWallState();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayerPB.TroopPB.Builder tmpBuilder = StructPlayerPB.TroopPB.newBuilder();
            final int tmpFieldCnt = this.troop.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.battle != null) {
            StructBattlePB.BattlePB.Builder tmpBuilder = StructBattlePB.BattlePB.newBuilder();
            final int tmpFieldCnt = this.battle.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattle();
            }
        }  else if (builder.hasBattle()) {
            // 清理Battle
            builder.clearBattle();
            fieldCnt++;
        }
        if (!this.getClanSname().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSname(this.getClanSname());
            fieldCnt++;
        }  else if (builder.hasClanSname()) {
            // 清理ClanSname
            builder.clearClanSname();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (this.garrison != null) {
            StructPB.CityGarrisonPB.Builder tmpBuilder = StructPB.CityGarrisonPB.newBuilder();
            final int tmpFieldCnt = this.garrison.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGarrison(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGarrison();
            }
        }  else if (builder.hasGarrison()) {
            // 清理Garrison
            builder.clearGarrison();
            fieldCnt++;
        }
        if (this.buffSys != null) {
            StructBattlePB.BuffSysPB.Builder tmpBuilder = StructBattlePB.BuffSysPB.newBuilder();
            final int tmpFieldCnt = this.buffSys.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuffSys();
            }
        }  else if (builder.hasBuffSys()) {
            // 清理BuffSys
            builder.clearBuffSys();
            fieldCnt++;
        }
        if (this.peaceShield != null) {
            StructBattlePB.PeaceShieldPB.Builder tmpBuilder = StructBattlePB.PeaceShieldPB.newBuilder();
            final int tmpFieldCnt = this.peaceShield.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPeaceShield(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPeaceShield();
            }
        }  else if (builder.hasPeaceShield()) {
            // 清理PeaceShield
            builder.clearPeaceShield();
            fieldCnt++;
        }
        if (this.devBuffSys != null) {
            StructBattlePB.SceneDevBuffSysPB.Builder tmpBuilder = StructBattlePB.SceneDevBuffSysPB.newBuilder();
            final int tmpFieldCnt = this.devBuffSys.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSys();
            }
        }  else if (builder.hasDevBuffSys()) {
            // 清理DevBuffSys
            builder.clearDevBuffSys();
            fieldCnt++;
        }
        if (this.getAscendReason() != CityAscendReason.forNumber(0)) {
            builder.setAscendReason(this.getAscendReason());
            fieldCnt++;
        }  else if (builder.hasAscendReason()) {
            // 清理AscendReason
            builder.clearAscendReason();
            fieldCnt++;
        }
        if (this.safeGuard != null) {
            StructPB.SpecialSafeGuardPB.Builder tmpBuilder = StructPB.SpecialSafeGuardPB.newBuilder();
            final int tmpFieldCnt = this.safeGuard.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSafeGuard(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSafeGuard();
            }
        }  else if (builder.hasSafeGuard()) {
            // 清理SafeGuard
            builder.clearSafeGuard();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.arrow != null) {
            StructPB.Int64ArmyArrowItemMapPB.Builder tmpBuilder = StructPB.Int64ArmyArrowItemMapPB.newBuilder();
            final int tmpFieldCnt = this.arrow.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArrow(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArrow();
            }
        }  else if (builder.hasArrow()) {
            // 清理Arrow
            builder.clearArrow();
            fieldCnt++;
        }
        if (this.getEraLevel() != 0) {
            builder.setEraLevel(this.getEraLevel());
            fieldCnt++;
        }  else if (builder.hasEraLevel()) {
            // 清理EraLevel
            builder.clearEraLevel();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getGuardTowerHp() != 0) {
            builder.setGuardTowerHp(this.getGuardTowerHp());
            fieldCnt++;
        }  else if (builder.hasGuardTowerHp()) {
            // 清理GuardTowerHp
            builder.clearGuardTowerHp();
            fieldCnt++;
        }
        if (this.expression != null) {
            StructPB.ExpressionPB.Builder tmpBuilder = StructPB.ExpressionPB.newBuilder();
            final int tmpFieldCnt = this.expression.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExpression(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExpression();
            }
        }  else if (builder.hasExpression()) {
            // 清理Expression
            builder.clearExpression();
            fieldCnt++;
        }
        if (this.getPFlagId() != 0) {
            builder.setPFlagId(this.getPFlagId());
            fieldCnt++;
        }  else if (builder.hasPFlagId()) {
            // 清理PFlagId
            builder.clearPFlagId();
            fieldCnt++;
        }
        if (this.getDressTemplateId() != 0) {
            builder.setDressTemplateId(this.getDressTemplateId());
            fieldCnt++;
        }  else if (builder.hasDressTemplateId()) {
            // 清理DressTemplateId
            builder.clearDressTemplateId();
            fieldCnt++;
        }
        if (this.cityKingdomModel != null) {
            CityPB.CityKingdomModelPB.Builder tmpBuilder = CityPB.CityKingdomModelPB.newBuilder();
            final int tmpFieldCnt = this.cityKingdomModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCityKingdomModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCityKingdomModel();
            }
        }  else if (builder.hasCityKingdomModel()) {
            // 清理CityKingdomModel
            builder.clearCityKingdomModel();
            fieldCnt++;
        }
        if (this.getNameplateId() != 0) {
            builder.setNameplateId(this.getNameplateId());
            fieldCnt++;
        }  else if (builder.hasNameplateId()) {
            // 清理NameplateId
            builder.clearNameplateId();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(CityEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WALLSTATE)) {
            builder.setWallState(this.getWallState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToCs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToCs(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANSNAME)) {
            builder.setClanSname(this.getClanSname());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GARRISON) && this.garrison != null) {
            final boolean needClear = !builder.hasGarrison();
            final int tmpFieldCnt = this.garrison.copyChangeToCs(builder.getGarrisonBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGarrison();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUFFSYS) && this.buffSys != null) {
            final boolean needClear = !builder.hasBuffSys();
            final int tmpFieldCnt = this.buffSys.copyChangeToCs(builder.getBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_PEACESHIELD) && this.peaceShield != null) {
            final boolean needClear = !builder.hasPeaceShield();
            final int tmpFieldCnt = this.peaceShield.copyChangeToCs(builder.getPeaceShieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPeaceShield();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToCs(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_ASCENDREASON)) {
            builder.setAscendReason(this.getAscendReason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SAFEGUARD) && this.safeGuard != null) {
            final boolean needClear = !builder.hasSafeGuard();
            final int tmpFieldCnt = this.safeGuard.copyChangeToCs(builder.getSafeGuardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSafeGuard();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARROW) && this.arrow != null) {
            final boolean needClear = !builder.hasArrow();
            final int tmpFieldCnt = this.arrow.copyChangeToCs(builder.getArrowBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArrow();
            }
        }
        if (this.hasMark(FIELD_INDEX_ERALEVEL)) {
            builder.setEraLevel(this.getEraLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GUARDTOWERHP)) {
            builder.setGuardTowerHp(this.getGuardTowerHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            final boolean needClear = !builder.hasExpression();
            final int tmpFieldCnt = this.expression.copyChangeToCs(builder.getExpressionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpression();
            }
        }
        if (this.hasMark(FIELD_INDEX_PFLAGID)) {
            builder.setPFlagId(this.getPFlagId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DRESSTEMPLATEID)) {
            builder.setDressTemplateId(this.getDressTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CITYKINGDOMMODEL) && this.cityKingdomModel != null) {
            final boolean needClear = !builder.hasCityKingdomModel();
            final int tmpFieldCnt = this.cityKingdomModel.copyChangeToCs(builder.getCityKingdomModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCityKingdomModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_NAMEPLATEID)) {
            builder.setNameplateId(this.getNameplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(CityEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToAndClearDeleteKeysCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WALLSTATE)) {
            builder.setWallState(this.getWallState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToAndClearDeleteKeysCs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToAndClearDeleteKeysCs(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANSNAME)) {
            builder.setClanSname(this.getClanSname());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GARRISON) && this.garrison != null) {
            final boolean needClear = !builder.hasGarrison();
            final int tmpFieldCnt = this.garrison.copyChangeToAndClearDeleteKeysCs(builder.getGarrisonBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGarrison();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUFFSYS) && this.buffSys != null) {
            final boolean needClear = !builder.hasBuffSys();
            final int tmpFieldCnt = this.buffSys.copyChangeToAndClearDeleteKeysCs(builder.getBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_PEACESHIELD) && this.peaceShield != null) {
            final boolean needClear = !builder.hasPeaceShield();
            final int tmpFieldCnt = this.peaceShield.copyChangeToAndClearDeleteKeysCs(builder.getPeaceShieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPeaceShield();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToAndClearDeleteKeysCs(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_ASCENDREASON)) {
            builder.setAscendReason(this.getAscendReason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SAFEGUARD) && this.safeGuard != null) {
            final boolean needClear = !builder.hasSafeGuard();
            final int tmpFieldCnt = this.safeGuard.copyChangeToAndClearDeleteKeysCs(builder.getSafeGuardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSafeGuard();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToAndClearDeleteKeysCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARROW) && this.arrow != null) {
            final boolean needClear = !builder.hasArrow();
            final int tmpFieldCnt = this.arrow.copyChangeToAndClearDeleteKeysCs(builder.getArrowBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArrow();
            }
        }
        if (this.hasMark(FIELD_INDEX_ERALEVEL)) {
            builder.setEraLevel(this.getEraLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GUARDTOWERHP)) {
            builder.setGuardTowerHp(this.getGuardTowerHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            final boolean needClear = !builder.hasExpression();
            final int tmpFieldCnt = this.expression.copyChangeToAndClearDeleteKeysCs(builder.getExpressionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpression();
            }
        }
        if (this.hasMark(FIELD_INDEX_PFLAGID)) {
            builder.setPFlagId(this.getPFlagId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DRESSTEMPLATEID)) {
            builder.setDressTemplateId(this.getDressTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CITYKINGDOMMODEL) && this.cityKingdomModel != null) {
            final boolean needClear = !builder.hasCityKingdomModel();
            final int tmpFieldCnt = this.cityKingdomModel.copyChangeToAndClearDeleteKeysCs(builder.getCityKingdomModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCityKingdomModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_NAMEPLATEID)) {
            builder.setNameplateId(this.getNameplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(CityEntityPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromCs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromCs(proto.getPoint());
            }
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCamp()) {
            this.innerSetCamp(proto.getCamp());
        } else {
            this.innerSetCamp(Camp.forNumber(0));
        }
        if (proto.hasWallState()) {
            this.innerSetWallState(proto.getWallState());
        } else {
            this.innerSetWallState(CityWallState.forNumber(0));
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromCs(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromCs(proto.getTroop());
            }
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeFromCs(proto.getBattle());
        } else {
            if (this.battle != null) {
                this.battle.mergeFromCs(proto.getBattle());
            }
        }
        if (proto.hasClanSname()) {
            this.innerSetClanSname(proto.getClanSname());
        } else {
            this.innerSetClanSname(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasGarrison()) {
            this.getGarrison().mergeFromCs(proto.getGarrison());
        } else {
            if (this.garrison != null) {
                this.garrison.mergeFromCs(proto.getGarrison());
            }
        }
        if (proto.hasBuffSys()) {
            this.getBuffSys().mergeFromCs(proto.getBuffSys());
        } else {
            if (this.buffSys != null) {
                this.buffSys.mergeFromCs(proto.getBuffSys());
            }
        }
        if (proto.hasPeaceShield()) {
            this.getPeaceShield().mergeFromCs(proto.getPeaceShield());
        } else {
            if (this.peaceShield != null) {
                this.peaceShield.mergeFromCs(proto.getPeaceShield());
            }
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeFromCs(proto.getDevBuffSys());
        } else {
            if (this.devBuffSys != null) {
                this.devBuffSys.mergeFromCs(proto.getDevBuffSys());
            }
        }
        if (proto.hasAscendReason()) {
            this.innerSetAscendReason(proto.getAscendReason());
        } else {
            this.innerSetAscendReason(CityAscendReason.forNumber(0));
        }
        if (proto.hasSafeGuard()) {
            this.getSafeGuard().mergeFromCs(proto.getSafeGuard());
        } else {
            if (this.safeGuard != null) {
                this.safeGuard.mergeFromCs(proto.getSafeGuard());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromCs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromCs(proto.getCardHead());
            }
        }
        if (proto.hasArrow()) {
            this.getArrow().mergeFromCs(proto.getArrow());
        } else {
            if (this.arrow != null) {
                this.arrow.mergeFromCs(proto.getArrow());
            }
        }
        if (proto.hasEraLevel()) {
            this.innerSetEraLevel(proto.getEraLevel());
        } else {
            this.innerSetEraLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasGuardTowerHp()) {
            this.innerSetGuardTowerHp(proto.getGuardTowerHp());
        } else {
            this.innerSetGuardTowerHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeFromCs(proto.getExpression());
        } else {
            if (this.expression != null) {
                this.expression.mergeFromCs(proto.getExpression());
            }
        }
        if (proto.hasPFlagId()) {
            this.innerSetPFlagId(proto.getPFlagId());
        } else {
            this.innerSetPFlagId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDressTemplateId()) {
            this.innerSetDressTemplateId(proto.getDressTemplateId());
        } else {
            this.innerSetDressTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCityKingdomModel()) {
            this.getCityKingdomModel().mergeFromCs(proto.getCityKingdomModel());
        } else {
            if (this.cityKingdomModel != null) {
                this.cityKingdomModel.mergeFromCs(proto.getCityKingdomModel());
            }
        }
        if (proto.hasNameplateId()) {
            this.innerSetNameplateId(proto.getNameplateId());
        } else {
            this.innerSetNameplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return CityProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(CityEntityPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromCs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasCamp()) {
            this.setCamp(proto.getCamp());
            fieldCnt++;
        }
        if (proto.hasWallState()) {
            this.setWallState(proto.getWallState());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromCs(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeChangeFromCs(proto.getBattle());
            fieldCnt++;
        }
        if (proto.hasClanSname()) {
            this.setClanSname(proto.getClanSname());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasGarrison()) {
            this.getGarrison().mergeChangeFromCs(proto.getGarrison());
            fieldCnt++;
        }
        if (proto.hasBuffSys()) {
            this.getBuffSys().mergeChangeFromCs(proto.getBuffSys());
            fieldCnt++;
        }
        if (proto.hasPeaceShield()) {
            this.getPeaceShield().mergeChangeFromCs(proto.getPeaceShield());
            fieldCnt++;
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeChangeFromCs(proto.getDevBuffSys());
            fieldCnt++;
        }
        if (proto.hasAscendReason()) {
            this.setAscendReason(proto.getAscendReason());
            fieldCnt++;
        }
        if (proto.hasSafeGuard()) {
            this.getSafeGuard().mergeChangeFromCs(proto.getSafeGuard());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromCs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasArrow()) {
            this.getArrow().mergeChangeFromCs(proto.getArrow());
            fieldCnt++;
        }
        if (proto.hasEraLevel()) {
            this.setEraLevel(proto.getEraLevel());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasGuardTowerHp()) {
            this.setGuardTowerHp(proto.getGuardTowerHp());
            fieldCnt++;
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeChangeFromCs(proto.getExpression());
            fieldCnt++;
        }
        if (proto.hasPFlagId()) {
            this.setPFlagId(proto.getPFlagId());
            fieldCnt++;
        }
        if (proto.hasDressTemplateId()) {
            this.setDressTemplateId(proto.getDressTemplateId());
            fieldCnt++;
        }
        if (proto.hasCityKingdomModel()) {
            this.getCityKingdomModel().mergeChangeFromCs(proto.getCityKingdomModel());
            fieldCnt++;
        }
        if (proto.hasNameplateId()) {
            this.setNameplateId(proto.getNameplateId());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CityEntity.Builder getCopyDbBuilder() {
        final CityEntity.Builder builder = CityEntity.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(CityEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getCamp() != Camp.forNumber(0)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }  else if (builder.hasCamp()) {
            // 清理Camp
            builder.clearCamp();
            fieldCnt++;
        }
        if (this.getWallState() != CityWallState.forNumber(0)) {
            builder.setWallState(this.getWallState());
            fieldCnt++;
        }  else if (builder.hasWallState()) {
            // 清理WallState
            builder.clearWallState();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayer.Troop.Builder tmpBuilder = StructPlayer.Troop.newBuilder();
            final int tmpFieldCnt = this.troop.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.battle != null) {
            StructBattle.Battle.Builder tmpBuilder = StructBattle.Battle.newBuilder();
            final int tmpFieldCnt = this.battle.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattle();
            }
        }  else if (builder.hasBattle()) {
            // 清理Battle
            builder.clearBattle();
            fieldCnt++;
        }
        if (!this.getClanSname().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSname(this.getClanSname());
            fieldCnt++;
        }  else if (builder.hasClanSname()) {
            // 清理ClanSname
            builder.clearClanSname();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (this.innerArmy != null) {
            Struct.CityInnerArmy.Builder tmpBuilder = Struct.CityInnerArmy.newBuilder();
            final int tmpFieldCnt = this.innerArmy.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInnerArmy(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInnerArmy();
            }
        }  else if (builder.hasInnerArmy()) {
            // 清理InnerArmy
            builder.clearInnerArmy();
            fieldCnt++;
        }
        if (this.garrison != null) {
            Struct.CityGarrison.Builder tmpBuilder = Struct.CityGarrison.newBuilder();
            final int tmpFieldCnt = this.garrison.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGarrison(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGarrison();
            }
        }  else if (builder.hasGarrison()) {
            // 清理Garrison
            builder.clearGarrison();
            fieldCnt++;
        }
        if (this.buffSys != null) {
            StructBattle.BuffSys.Builder tmpBuilder = StructBattle.BuffSys.newBuilder();
            final int tmpFieldCnt = this.buffSys.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuffSys();
            }
        }  else if (builder.hasBuffSys()) {
            // 清理BuffSys
            builder.clearBuffSys();
            fieldCnt++;
        }
        if (this.peaceShield != null) {
            StructBattle.PeaceShield.Builder tmpBuilder = StructBattle.PeaceShield.newBuilder();
            final int tmpFieldCnt = this.peaceShield.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPeaceShield(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPeaceShield();
            }
        }  else if (builder.hasPeaceShield()) {
            // 清理PeaceShield
            builder.clearPeaceShield();
            fieldCnt++;
        }
        if (this.devBuffSys != null) {
            StructBattle.SceneDevBuffSys.Builder tmpBuilder = StructBattle.SceneDevBuffSys.newBuilder();
            final int tmpFieldCnt = this.devBuffSys.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSys();
            }
        }  else if (builder.hasDevBuffSys()) {
            // 清理DevBuffSys
            builder.clearDevBuffSys();
            fieldCnt++;
        }
        if (this.getAscendReason() != CityAscendReason.forNumber(0)) {
            builder.setAscendReason(this.getAscendReason());
            fieldCnt++;
        }  else if (builder.hasAscendReason()) {
            // 清理AscendReason
            builder.clearAscendReason();
            fieldCnt++;
        }
        if (this.safeGuard != null) {
            Struct.SpecialSafeGuard.Builder tmpBuilder = Struct.SpecialSafeGuard.newBuilder();
            final int tmpFieldCnt = this.safeGuard.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSafeGuard(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSafeGuard();
            }
        }  else if (builder.hasSafeGuard()) {
            // 清理SafeGuard
            builder.clearSafeGuard();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.getEraLevel() != 0) {
            builder.setEraLevel(this.getEraLevel());
            fieldCnt++;
        }  else if (builder.hasEraLevel()) {
            // 清理EraLevel
            builder.clearEraLevel();
            fieldCnt++;
        }
        if (this.expression != null) {
            Struct.Expression.Builder tmpBuilder = Struct.Expression.newBuilder();
            final int tmpFieldCnt = this.expression.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExpression(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExpression();
            }
        }  else if (builder.hasExpression()) {
            // 清理Expression
            builder.clearExpression();
            fieldCnt++;
        }
        if (this.getPFlagId() != 0) {
            builder.setPFlagId(this.getPFlagId());
            fieldCnt++;
        }  else if (builder.hasPFlagId()) {
            // 清理PFlagId
            builder.clearPFlagId();
            fieldCnt++;
        }
        if (this.getDressTemplateId() != 0) {
            builder.setDressTemplateId(this.getDressTemplateId());
            fieldCnt++;
        }  else if (builder.hasDressTemplateId()) {
            // 清理DressTemplateId
            builder.clearDressTemplateId();
            fieldCnt++;
        }
        if (this.getDressTimeoutTsMs() != 0L) {
            builder.setDressTimeoutTsMs(this.getDressTimeoutTsMs());
            fieldCnt++;
        }  else if (builder.hasDressTimeoutTsMs()) {
            // 清理DressTimeoutTsMs
            builder.clearDressTimeoutTsMs();
            fieldCnt++;
        }
        if (this.cityKingdomModel != null) {
            City.CityKingdomModel.Builder tmpBuilder = City.CityKingdomModel.newBuilder();
            final int tmpFieldCnt = this.cityKingdomModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCityKingdomModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCityKingdomModel();
            }
        }  else if (builder.hasCityKingdomModel()) {
            // 清理CityKingdomModel
            builder.clearCityKingdomModel();
            fieldCnt++;
        }
        if (this.getNameplateId() != 0) {
            builder.setNameplateId(this.getNameplateId());
            fieldCnt++;
        }  else if (builder.hasNameplateId()) {
            // 清理NameplateId
            builder.clearNameplateId();
            fieldCnt++;
        }
        if (this.getNameplateTimeoutTsMs() != 0L) {
            builder.setNameplateTimeoutTsMs(this.getNameplateTimeoutTsMs());
            fieldCnt++;
        }  else if (builder.hasNameplateTimeoutTsMs()) {
            // 清理NameplateTimeoutTsMs
            builder.clearNameplateTimeoutTsMs();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(CityEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToDb(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WALLSTATE)) {
            builder.setWallState(this.getWallState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToDb(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToDb(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANSNAME)) {
            builder.setClanSname(this.getClanSname());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INNERARMY) && this.innerArmy != null) {
            final boolean needClear = !builder.hasInnerArmy();
            final int tmpFieldCnt = this.innerArmy.copyChangeToDb(builder.getInnerArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerArmy();
            }
        }
        if (this.hasMark(FIELD_INDEX_GARRISON) && this.garrison != null) {
            final boolean needClear = !builder.hasGarrison();
            final int tmpFieldCnt = this.garrison.copyChangeToDb(builder.getGarrisonBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGarrison();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUFFSYS) && this.buffSys != null) {
            final boolean needClear = !builder.hasBuffSys();
            final int tmpFieldCnt = this.buffSys.copyChangeToDb(builder.getBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_PEACESHIELD) && this.peaceShield != null) {
            final boolean needClear = !builder.hasPeaceShield();
            final int tmpFieldCnt = this.peaceShield.copyChangeToDb(builder.getPeaceShieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPeaceShield();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToDb(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_ASCENDREASON)) {
            builder.setAscendReason(this.getAscendReason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SAFEGUARD) && this.safeGuard != null) {
            final boolean needClear = !builder.hasSafeGuard();
            final int tmpFieldCnt = this.safeGuard.copyChangeToDb(builder.getSafeGuardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSafeGuard();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToDb(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_ERALEVEL)) {
            builder.setEraLevel(this.getEraLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            final boolean needClear = !builder.hasExpression();
            final int tmpFieldCnt = this.expression.copyChangeToDb(builder.getExpressionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpression();
            }
        }
        if (this.hasMark(FIELD_INDEX_PFLAGID)) {
            builder.setPFlagId(this.getPFlagId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DRESSTEMPLATEID)) {
            builder.setDressTemplateId(this.getDressTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DRESSTIMEOUTTSMS)) {
            builder.setDressTimeoutTsMs(this.getDressTimeoutTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CITYKINGDOMMODEL) && this.cityKingdomModel != null) {
            final boolean needClear = !builder.hasCityKingdomModel();
            final int tmpFieldCnt = this.cityKingdomModel.copyChangeToDb(builder.getCityKingdomModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCityKingdomModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_NAMEPLATEID)) {
            builder.setNameplateId(this.getNameplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAMEPLATETIMEOUTTSMS)) {
            builder.setNameplateTimeoutTsMs(this.getNameplateTimeoutTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(CityEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromDb(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromDb(proto.getPoint());
            }
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCamp()) {
            this.innerSetCamp(proto.getCamp());
        } else {
            this.innerSetCamp(Camp.forNumber(0));
        }
        if (proto.hasWallState()) {
            this.innerSetWallState(proto.getWallState());
        } else {
            this.innerSetWallState(CityWallState.forNumber(0));
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromDb(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromDb(proto.getTroop());
            }
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeFromDb(proto.getBattle());
        } else {
            if (this.battle != null) {
                this.battle.mergeFromDb(proto.getBattle());
            }
        }
        if (proto.hasClanSname()) {
            this.innerSetClanSname(proto.getClanSname());
        } else {
            this.innerSetClanSname(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasInnerArmy()) {
            this.getInnerArmy().mergeFromDb(proto.getInnerArmy());
        } else {
            if (this.innerArmy != null) {
                this.innerArmy.mergeFromDb(proto.getInnerArmy());
            }
        }
        if (proto.hasGarrison()) {
            this.getGarrison().mergeFromDb(proto.getGarrison());
        } else {
            if (this.garrison != null) {
                this.garrison.mergeFromDb(proto.getGarrison());
            }
        }
        if (proto.hasBuffSys()) {
            this.getBuffSys().mergeFromDb(proto.getBuffSys());
        } else {
            if (this.buffSys != null) {
                this.buffSys.mergeFromDb(proto.getBuffSys());
            }
        }
        if (proto.hasPeaceShield()) {
            this.getPeaceShield().mergeFromDb(proto.getPeaceShield());
        } else {
            if (this.peaceShield != null) {
                this.peaceShield.mergeFromDb(proto.getPeaceShield());
            }
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeFromDb(proto.getDevBuffSys());
        } else {
            if (this.devBuffSys != null) {
                this.devBuffSys.mergeFromDb(proto.getDevBuffSys());
            }
        }
        if (proto.hasAscendReason()) {
            this.innerSetAscendReason(proto.getAscendReason());
        } else {
            this.innerSetAscendReason(CityAscendReason.forNumber(0));
        }
        if (proto.hasSafeGuard()) {
            this.getSafeGuard().mergeFromDb(proto.getSafeGuard());
        } else {
            if (this.safeGuard != null) {
                this.safeGuard.mergeFromDb(proto.getSafeGuard());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromDb(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromDb(proto.getCardHead());
            }
        }
        if (proto.hasEraLevel()) {
            this.innerSetEraLevel(proto.getEraLevel());
        } else {
            this.innerSetEraLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeFromDb(proto.getExpression());
        } else {
            if (this.expression != null) {
                this.expression.mergeFromDb(proto.getExpression());
            }
        }
        if (proto.hasPFlagId()) {
            this.innerSetPFlagId(proto.getPFlagId());
        } else {
            this.innerSetPFlagId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDressTemplateId()) {
            this.innerSetDressTemplateId(proto.getDressTemplateId());
        } else {
            this.innerSetDressTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDressTimeoutTsMs()) {
            this.innerSetDressTimeoutTsMs(proto.getDressTimeoutTsMs());
        } else {
            this.innerSetDressTimeoutTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCityKingdomModel()) {
            this.getCityKingdomModel().mergeFromDb(proto.getCityKingdomModel());
        } else {
            if (this.cityKingdomModel != null) {
                this.cityKingdomModel.mergeFromDb(proto.getCityKingdomModel());
            }
        }
        if (proto.hasNameplateId()) {
            this.innerSetNameplateId(proto.getNameplateId());
        } else {
            this.innerSetNameplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNameplateTimeoutTsMs()) {
            this.innerSetNameplateTimeoutTsMs(proto.getNameplateTimeoutTsMs());
        } else {
            this.innerSetNameplateTimeoutTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return CityProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(CityEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromDb(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasCamp()) {
            this.setCamp(proto.getCamp());
            fieldCnt++;
        }
        if (proto.hasWallState()) {
            this.setWallState(proto.getWallState());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromDb(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeChangeFromDb(proto.getBattle());
            fieldCnt++;
        }
        if (proto.hasClanSname()) {
            this.setClanSname(proto.getClanSname());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasInnerArmy()) {
            this.getInnerArmy().mergeChangeFromDb(proto.getInnerArmy());
            fieldCnt++;
        }
        if (proto.hasGarrison()) {
            this.getGarrison().mergeChangeFromDb(proto.getGarrison());
            fieldCnt++;
        }
        if (proto.hasBuffSys()) {
            this.getBuffSys().mergeChangeFromDb(proto.getBuffSys());
            fieldCnt++;
        }
        if (proto.hasPeaceShield()) {
            this.getPeaceShield().mergeChangeFromDb(proto.getPeaceShield());
            fieldCnt++;
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeChangeFromDb(proto.getDevBuffSys());
            fieldCnt++;
        }
        if (proto.hasAscendReason()) {
            this.setAscendReason(proto.getAscendReason());
            fieldCnt++;
        }
        if (proto.hasSafeGuard()) {
            this.getSafeGuard().mergeChangeFromDb(proto.getSafeGuard());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromDb(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasEraLevel()) {
            this.setEraLevel(proto.getEraLevel());
            fieldCnt++;
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeChangeFromDb(proto.getExpression());
            fieldCnt++;
        }
        if (proto.hasPFlagId()) {
            this.setPFlagId(proto.getPFlagId());
            fieldCnt++;
        }
        if (proto.hasDressTemplateId()) {
            this.setDressTemplateId(proto.getDressTemplateId());
            fieldCnt++;
        }
        if (proto.hasDressTimeoutTsMs()) {
            this.setDressTimeoutTsMs(proto.getDressTimeoutTsMs());
            fieldCnt++;
        }
        if (proto.hasCityKingdomModel()) {
            this.getCityKingdomModel().mergeChangeFromDb(proto.getCityKingdomModel());
            fieldCnt++;
        }
        if (proto.hasNameplateId()) {
            this.setNameplateId(proto.getNameplateId());
            fieldCnt++;
        }
        if (proto.hasNameplateTimeoutTsMs()) {
            this.setNameplateTimeoutTsMs(proto.getNameplateTimeoutTsMs());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CityEntity.Builder getCopySsBuilder() {
        final CityEntity.Builder builder = CityEntity.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(CityEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getCamp() != Camp.forNumber(0)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }  else if (builder.hasCamp()) {
            // 清理Camp
            builder.clearCamp();
            fieldCnt++;
        }
        if (this.getWallState() != CityWallState.forNumber(0)) {
            builder.setWallState(this.getWallState());
            fieldCnt++;
        }  else if (builder.hasWallState()) {
            // 清理WallState
            builder.clearWallState();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayer.Troop.Builder tmpBuilder = StructPlayer.Troop.newBuilder();
            final int tmpFieldCnt = this.troop.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.battle != null) {
            StructBattle.Battle.Builder tmpBuilder = StructBattle.Battle.newBuilder();
            final int tmpFieldCnt = this.battle.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattle();
            }
        }  else if (builder.hasBattle()) {
            // 清理Battle
            builder.clearBattle();
            fieldCnt++;
        }
        if (!this.getClanSname().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSname(this.getClanSname());
            fieldCnt++;
        }  else if (builder.hasClanSname()) {
            // 清理ClanSname
            builder.clearClanSname();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (this.innerArmy != null) {
            Struct.CityInnerArmy.Builder tmpBuilder = Struct.CityInnerArmy.newBuilder();
            final int tmpFieldCnt = this.innerArmy.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInnerArmy(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInnerArmy();
            }
        }  else if (builder.hasInnerArmy()) {
            // 清理InnerArmy
            builder.clearInnerArmy();
            fieldCnt++;
        }
        if (this.garrison != null) {
            Struct.CityGarrison.Builder tmpBuilder = Struct.CityGarrison.newBuilder();
            final int tmpFieldCnt = this.garrison.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGarrison(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGarrison();
            }
        }  else if (builder.hasGarrison()) {
            // 清理Garrison
            builder.clearGarrison();
            fieldCnt++;
        }
        if (this.buffSys != null) {
            StructBattle.BuffSys.Builder tmpBuilder = StructBattle.BuffSys.newBuilder();
            final int tmpFieldCnt = this.buffSys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuffSys();
            }
        }  else if (builder.hasBuffSys()) {
            // 清理BuffSys
            builder.clearBuffSys();
            fieldCnt++;
        }
        if (this.peaceShield != null) {
            StructBattle.PeaceShield.Builder tmpBuilder = StructBattle.PeaceShield.newBuilder();
            final int tmpFieldCnt = this.peaceShield.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPeaceShield(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPeaceShield();
            }
        }  else if (builder.hasPeaceShield()) {
            // 清理PeaceShield
            builder.clearPeaceShield();
            fieldCnt++;
        }
        if (this.devBuffSys != null) {
            StructBattle.SceneDevBuffSys.Builder tmpBuilder = StructBattle.SceneDevBuffSys.newBuilder();
            final int tmpFieldCnt = this.devBuffSys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSys();
            }
        }  else if (builder.hasDevBuffSys()) {
            // 清理DevBuffSys
            builder.clearDevBuffSys();
            fieldCnt++;
        }
        if (this.getAscendReason() != CityAscendReason.forNumber(0)) {
            builder.setAscendReason(this.getAscendReason());
            fieldCnt++;
        }  else if (builder.hasAscendReason()) {
            // 清理AscendReason
            builder.clearAscendReason();
            fieldCnt++;
        }
        if (this.safeGuard != null) {
            Struct.SpecialSafeGuard.Builder tmpBuilder = Struct.SpecialSafeGuard.newBuilder();
            final int tmpFieldCnt = this.safeGuard.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSafeGuard(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSafeGuard();
            }
        }  else if (builder.hasSafeGuard()) {
            // 清理SafeGuard
            builder.clearSafeGuard();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.arrow != null) {
            Struct.Int64ArmyArrowItemMap.Builder tmpBuilder = Struct.Int64ArmyArrowItemMap.newBuilder();
            final int tmpFieldCnt = this.arrow.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArrow(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArrow();
            }
        }  else if (builder.hasArrow()) {
            // 清理Arrow
            builder.clearArrow();
            fieldCnt++;
        }
        if (this.getEraLevel() != 0) {
            builder.setEraLevel(this.getEraLevel());
            fieldCnt++;
        }  else if (builder.hasEraLevel()) {
            // 清理EraLevel
            builder.clearEraLevel();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getGuardTowerHp() != 0) {
            builder.setGuardTowerHp(this.getGuardTowerHp());
            fieldCnt++;
        }  else if (builder.hasGuardTowerHp()) {
            // 清理GuardTowerHp
            builder.clearGuardTowerHp();
            fieldCnt++;
        }
        if (this.expression != null) {
            Struct.Expression.Builder tmpBuilder = Struct.Expression.newBuilder();
            final int tmpFieldCnt = this.expression.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExpression(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExpression();
            }
        }  else if (builder.hasExpression()) {
            // 清理Expression
            builder.clearExpression();
            fieldCnt++;
        }
        if (this.getPFlagId() != 0) {
            builder.setPFlagId(this.getPFlagId());
            fieldCnt++;
        }  else if (builder.hasPFlagId()) {
            // 清理PFlagId
            builder.clearPFlagId();
            fieldCnt++;
        }
        if (this.getDressTemplateId() != 0) {
            builder.setDressTemplateId(this.getDressTemplateId());
            fieldCnt++;
        }  else if (builder.hasDressTemplateId()) {
            // 清理DressTemplateId
            builder.clearDressTemplateId();
            fieldCnt++;
        }
        if (this.getDressTimeoutTsMs() != 0L) {
            builder.setDressTimeoutTsMs(this.getDressTimeoutTsMs());
            fieldCnt++;
        }  else if (builder.hasDressTimeoutTsMs()) {
            // 清理DressTimeoutTsMs
            builder.clearDressTimeoutTsMs();
            fieldCnt++;
        }
        if (this.cityKingdomModel != null) {
            City.CityKingdomModel.Builder tmpBuilder = City.CityKingdomModel.newBuilder();
            final int tmpFieldCnt = this.cityKingdomModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCityKingdomModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCityKingdomModel();
            }
        }  else if (builder.hasCityKingdomModel()) {
            // 清理CityKingdomModel
            builder.clearCityKingdomModel();
            fieldCnt++;
        }
        if (this.getNameplateId() != 0) {
            builder.setNameplateId(this.getNameplateId());
            fieldCnt++;
        }  else if (builder.hasNameplateId()) {
            // 清理NameplateId
            builder.clearNameplateId();
            fieldCnt++;
        }
        if (this.getNameplateTimeoutTsMs() != 0L) {
            builder.setNameplateTimeoutTsMs(this.getNameplateTimeoutTsMs());
            fieldCnt++;
        }  else if (builder.hasNameplateTimeoutTsMs()) {
            // 清理NameplateTimeoutTsMs
            builder.clearNameplateTimeoutTsMs();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(CityEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToSs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WALLSTATE)) {
            builder.setWallState(this.getWallState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToSs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToSs(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANSNAME)) {
            builder.setClanSname(this.getClanSname());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INNERARMY) && this.innerArmy != null) {
            final boolean needClear = !builder.hasInnerArmy();
            final int tmpFieldCnt = this.innerArmy.copyChangeToSs(builder.getInnerArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerArmy();
            }
        }
        if (this.hasMark(FIELD_INDEX_GARRISON) && this.garrison != null) {
            final boolean needClear = !builder.hasGarrison();
            final int tmpFieldCnt = this.garrison.copyChangeToSs(builder.getGarrisonBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGarrison();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUFFSYS) && this.buffSys != null) {
            final boolean needClear = !builder.hasBuffSys();
            final int tmpFieldCnt = this.buffSys.copyChangeToSs(builder.getBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_PEACESHIELD) && this.peaceShield != null) {
            final boolean needClear = !builder.hasPeaceShield();
            final int tmpFieldCnt = this.peaceShield.copyChangeToSs(builder.getPeaceShieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPeaceShield();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToSs(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_ASCENDREASON)) {
            builder.setAscendReason(this.getAscendReason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SAFEGUARD) && this.safeGuard != null) {
            final boolean needClear = !builder.hasSafeGuard();
            final int tmpFieldCnt = this.safeGuard.copyChangeToSs(builder.getSafeGuardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSafeGuard();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARROW) && this.arrow != null) {
            final boolean needClear = !builder.hasArrow();
            final int tmpFieldCnt = this.arrow.copyChangeToSs(builder.getArrowBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArrow();
            }
        }
        if (this.hasMark(FIELD_INDEX_ERALEVEL)) {
            builder.setEraLevel(this.getEraLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GUARDTOWERHP)) {
            builder.setGuardTowerHp(this.getGuardTowerHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            final boolean needClear = !builder.hasExpression();
            final int tmpFieldCnt = this.expression.copyChangeToSs(builder.getExpressionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpression();
            }
        }
        if (this.hasMark(FIELD_INDEX_PFLAGID)) {
            builder.setPFlagId(this.getPFlagId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DRESSTEMPLATEID)) {
            builder.setDressTemplateId(this.getDressTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DRESSTIMEOUTTSMS)) {
            builder.setDressTimeoutTsMs(this.getDressTimeoutTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CITYKINGDOMMODEL) && this.cityKingdomModel != null) {
            final boolean needClear = !builder.hasCityKingdomModel();
            final int tmpFieldCnt = this.cityKingdomModel.copyChangeToSs(builder.getCityKingdomModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCityKingdomModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_NAMEPLATEID)) {
            builder.setNameplateId(this.getNameplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAMEPLATETIMEOUTTSMS)) {
            builder.setNameplateTimeoutTsMs(this.getNameplateTimeoutTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(CityEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromSs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromSs(proto.getPoint());
            }
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCamp()) {
            this.innerSetCamp(proto.getCamp());
        } else {
            this.innerSetCamp(Camp.forNumber(0));
        }
        if (proto.hasWallState()) {
            this.innerSetWallState(proto.getWallState());
        } else {
            this.innerSetWallState(CityWallState.forNumber(0));
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromSs(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromSs(proto.getTroop());
            }
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeFromSs(proto.getBattle());
        } else {
            if (this.battle != null) {
                this.battle.mergeFromSs(proto.getBattle());
            }
        }
        if (proto.hasClanSname()) {
            this.innerSetClanSname(proto.getClanSname());
        } else {
            this.innerSetClanSname(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasInnerArmy()) {
            this.getInnerArmy().mergeFromSs(proto.getInnerArmy());
        } else {
            if (this.innerArmy != null) {
                this.innerArmy.mergeFromSs(proto.getInnerArmy());
            }
        }
        if (proto.hasGarrison()) {
            this.getGarrison().mergeFromSs(proto.getGarrison());
        } else {
            if (this.garrison != null) {
                this.garrison.mergeFromSs(proto.getGarrison());
            }
        }
        if (proto.hasBuffSys()) {
            this.getBuffSys().mergeFromSs(proto.getBuffSys());
        } else {
            if (this.buffSys != null) {
                this.buffSys.mergeFromSs(proto.getBuffSys());
            }
        }
        if (proto.hasPeaceShield()) {
            this.getPeaceShield().mergeFromSs(proto.getPeaceShield());
        } else {
            if (this.peaceShield != null) {
                this.peaceShield.mergeFromSs(proto.getPeaceShield());
            }
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeFromSs(proto.getDevBuffSys());
        } else {
            if (this.devBuffSys != null) {
                this.devBuffSys.mergeFromSs(proto.getDevBuffSys());
            }
        }
        if (proto.hasAscendReason()) {
            this.innerSetAscendReason(proto.getAscendReason());
        } else {
            this.innerSetAscendReason(CityAscendReason.forNumber(0));
        }
        if (proto.hasSafeGuard()) {
            this.getSafeGuard().mergeFromSs(proto.getSafeGuard());
        } else {
            if (this.safeGuard != null) {
                this.safeGuard.mergeFromSs(proto.getSafeGuard());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        if (proto.hasArrow()) {
            this.getArrow().mergeFromSs(proto.getArrow());
        } else {
            if (this.arrow != null) {
                this.arrow.mergeFromSs(proto.getArrow());
            }
        }
        if (proto.hasEraLevel()) {
            this.innerSetEraLevel(proto.getEraLevel());
        } else {
            this.innerSetEraLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasGuardTowerHp()) {
            this.innerSetGuardTowerHp(proto.getGuardTowerHp());
        } else {
            this.innerSetGuardTowerHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeFromSs(proto.getExpression());
        } else {
            if (this.expression != null) {
                this.expression.mergeFromSs(proto.getExpression());
            }
        }
        if (proto.hasPFlagId()) {
            this.innerSetPFlagId(proto.getPFlagId());
        } else {
            this.innerSetPFlagId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDressTemplateId()) {
            this.innerSetDressTemplateId(proto.getDressTemplateId());
        } else {
            this.innerSetDressTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDressTimeoutTsMs()) {
            this.innerSetDressTimeoutTsMs(proto.getDressTimeoutTsMs());
        } else {
            this.innerSetDressTimeoutTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCityKingdomModel()) {
            this.getCityKingdomModel().mergeFromSs(proto.getCityKingdomModel());
        } else {
            if (this.cityKingdomModel != null) {
                this.cityKingdomModel.mergeFromSs(proto.getCityKingdomModel());
            }
        }
        if (proto.hasNameplateId()) {
            this.innerSetNameplateId(proto.getNameplateId());
        } else {
            this.innerSetNameplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNameplateTimeoutTsMs()) {
            this.innerSetNameplateTimeoutTsMs(proto.getNameplateTimeoutTsMs());
        } else {
            this.innerSetNameplateTimeoutTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return CityProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(CityEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromSs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasCamp()) {
            this.setCamp(proto.getCamp());
            fieldCnt++;
        }
        if (proto.hasWallState()) {
            this.setWallState(proto.getWallState());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromSs(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeChangeFromSs(proto.getBattle());
            fieldCnt++;
        }
        if (proto.hasClanSname()) {
            this.setClanSname(proto.getClanSname());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasInnerArmy()) {
            this.getInnerArmy().mergeChangeFromSs(proto.getInnerArmy());
            fieldCnt++;
        }
        if (proto.hasGarrison()) {
            this.getGarrison().mergeChangeFromSs(proto.getGarrison());
            fieldCnt++;
        }
        if (proto.hasBuffSys()) {
            this.getBuffSys().mergeChangeFromSs(proto.getBuffSys());
            fieldCnt++;
        }
        if (proto.hasPeaceShield()) {
            this.getPeaceShield().mergeChangeFromSs(proto.getPeaceShield());
            fieldCnt++;
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeChangeFromSs(proto.getDevBuffSys());
            fieldCnt++;
        }
        if (proto.hasAscendReason()) {
            this.setAscendReason(proto.getAscendReason());
            fieldCnt++;
        }
        if (proto.hasSafeGuard()) {
            this.getSafeGuard().mergeChangeFromSs(proto.getSafeGuard());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasArrow()) {
            this.getArrow().mergeChangeFromSs(proto.getArrow());
            fieldCnt++;
        }
        if (proto.hasEraLevel()) {
            this.setEraLevel(proto.getEraLevel());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasGuardTowerHp()) {
            this.setGuardTowerHp(proto.getGuardTowerHp());
            fieldCnt++;
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeChangeFromSs(proto.getExpression());
            fieldCnt++;
        }
        if (proto.hasPFlagId()) {
            this.setPFlagId(proto.getPFlagId());
            fieldCnt++;
        }
        if (proto.hasDressTemplateId()) {
            this.setDressTemplateId(proto.getDressTemplateId());
            fieldCnt++;
        }
        if (proto.hasDressTimeoutTsMs()) {
            this.setDressTimeoutTsMs(proto.getDressTimeoutTsMs());
            fieldCnt++;
        }
        if (proto.hasCityKingdomModel()) {
            this.getCityKingdomModel().mergeChangeFromSs(proto.getCityKingdomModel());
            fieldCnt++;
        }
        if (proto.hasNameplateId()) {
            this.setNameplateId(proto.getNameplateId());
            fieldCnt++;
        }
        if (proto.hasNameplateTimeoutTsMs()) {
            this.setNameplateTimeoutTsMs(proto.getNameplateTimeoutTsMs());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        CityEntity.Builder builder = CityEntity.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            this.point.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            this.troop.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            this.battle.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_INNERARMY) && this.innerArmy != null) {
            this.innerArmy.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_GARRISON) && this.garrison != null) {
            this.garrison.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BUFFSYS) && this.buffSys != null) {
            this.buffSys.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PEACESHIELD) && this.peaceShield != null) {
            this.peaceShield.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            this.devBuffSys.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SAFEGUARD) && this.safeGuard != null) {
            this.safeGuard.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ARROW) && this.arrow != null) {
            this.arrow.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            this.expression.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CITYKINGDOMMODEL) && this.cityKingdomModel != null) {
            this.cityKingdomModel.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.point != null) {
            this.point.markAll();
        }
        if (this.troop != null) {
            this.troop.markAll();
        }
        if (this.battle != null) {
            this.battle.markAll();
        }
        if (this.innerArmy != null) {
            this.innerArmy.markAll();
        }
        if (this.garrison != null) {
            this.garrison.markAll();
        }
        if (this.buffSys != null) {
            this.buffSys.markAll();
        }
        if (this.peaceShield != null) {
            this.peaceShield.markAll();
        }
        if (this.devBuffSys != null) {
            this.devBuffSys.markAll();
        }
        if (this.safeGuard != null) {
            this.safeGuard.markAll();
        }
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        if (this.arrow != null) {
            this.arrow.markAll();
        }
        if (this.expression != null) {
            this.expression.markAll();
        }
        if (this.cityKingdomModel != null) {
            this.cityKingdomModel.markAll();
        }
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("CityProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("CityProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof CityProp)) {
            return false;
        }
        final CityProp otherNode = (CityProp) node;
        if (!this.getPoint().compareDataTo(otherNode.getPoint())) {
            return false;
        }
        if (this.ownerId != otherNode.ownerId) {
            return false;
        }
        if (this.level != otherNode.level) {
            return false;
        }
        if (this.camp != otherNode.camp) {
            return false;
        }
        if (this.wallState != otherNode.wallState) {
            return false;
        }
        if (!this.getTroop().compareDataTo(otherNode.getTroop())) {
            return false;
        }
        if (!this.getBattle().compareDataTo(otherNode.getBattle())) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanSname, otherNode.clanSname)) {
            return false;
        }
        if (this.clanId != otherNode.clanId) {
            return false;
        }
        if (!this.getInnerArmy().compareDataTo(otherNode.getInnerArmy())) {
            return false;
        }
        if (!this.getGarrison().compareDataTo(otherNode.getGarrison())) {
            return false;
        }
        if (!this.getBuffSys().compareDataTo(otherNode.getBuffSys())) {
            return false;
        }
        if (!this.getPeaceShield().compareDataTo(otherNode.getPeaceShield())) {
            return false;
        }
        if (!this.getDevBuffSys().compareDataTo(otherNode.getDevBuffSys())) {
            return false;
        }
        if (this.ascendReason != otherNode.ascendReason) {
            return false;
        }
        if (!this.getSafeGuard().compareDataTo(otherNode.getSafeGuard())) {
            return false;
        }
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        if (!this.getArrow().compareDataTo(otherNode.getArrow())) {
            return false;
        }
        if (this.eraLevel != otherNode.eraLevel) {
            return false;
        }
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        if (this.guardTowerHp != otherNode.guardTowerHp) {
            return false;
        }
        if (!this.getExpression().compareDataTo(otherNode.getExpression())) {
            return false;
        }
        if (this.pFlagId != otherNode.pFlagId) {
            return false;
        }
        if (this.dressTemplateId != otherNode.dressTemplateId) {
            return false;
        }
        if (this.dressTimeoutTsMs != otherNode.dressTimeoutTsMs) {
            return false;
        }
        if (!this.getCityKingdomModel().compareDataTo(otherNode.getCityKingdomModel())) {
            return false;
        }
        if (this.nameplateId != otherNode.nameplateId) {
            return false;
        }
        if (this.nameplateTimeoutTsMs != otherNode.nameplateTimeoutTsMs) {
            return false;
        }
        if (this.zoneId != otherNode.zoneId) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    public static CityProp of(CityEntity fullAttrDb, CityEntity changeAttrDb) {
        CityProp prop = new CityProp();
        prop.mergeFromDb(fullAttrDb);
        prop.mergeChangeFromDb(changeAttrDb);
        prop.unMarkAll();
        return prop;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 35;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActTimingCell;
import com.yorha.proto.StructPB.ActTimingCellPB;


/**
 * <AUTHOR> auto gen
 */
public class ActTimingCellProp extends AbstractPropNode {

    public static final int FIELD_INDEX_STARTTSSEC = 0;
    public static final int FIELD_INDEX_ENDTSSEC = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private long startTsSec = Constant.DEFAULT_LONG_VALUE;
    private long endTsSec = Constant.DEFAULT_LONG_VALUE;

    public ActTimingCellProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActTimingCellProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get startTsSec
     *
     * @return startTsSec value
     */
    public long getStartTsSec() {
        return this.startTsSec;
    }

    /**
     * set startTsSec && set marked
     *
     * @param startTsSec new value
     * @return current object
     */
    public ActTimingCellProp setStartTsSec(long startTsSec) {
        if (this.startTsSec != startTsSec) {
            this.mark(FIELD_INDEX_STARTTSSEC);
            this.startTsSec = startTsSec;
        }
        return this;
    }

    /**
     * inner set startTsSec
     *
     * @param startTsSec new value
     */
    private void innerSetStartTsSec(long startTsSec) {
        this.startTsSec = startTsSec;
    }

    /**
     * get endTsSec
     *
     * @return endTsSec value
     */
    public long getEndTsSec() {
        return this.endTsSec;
    }

    /**
     * set endTsSec && set marked
     *
     * @param endTsSec new value
     * @return current object
     */
    public ActTimingCellProp setEndTsSec(long endTsSec) {
        if (this.endTsSec != endTsSec) {
            this.mark(FIELD_INDEX_ENDTSSEC);
            this.endTsSec = endTsSec;
        }
        return this;
    }

    /**
     * inner set endTsSec
     *
     * @param endTsSec new value
     */
    private void innerSetEndTsSec(long endTsSec) {
        this.endTsSec = endTsSec;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActTimingCellPB.Builder getCopyCsBuilder() {
        final ActTimingCellPB.Builder builder = ActTimingCellPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActTimingCellPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getStartTsSec() != 0L) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }  else if (builder.hasStartTsSec()) {
            // 清理StartTsSec
            builder.clearStartTsSec();
            fieldCnt++;
        }
        if (this.getEndTsSec() != 0L) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }  else if (builder.hasEndTsSec()) {
            // 清理EndTsSec
            builder.clearEndTsSec();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActTimingCellPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STARTTSSEC)) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSSEC)) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActTimingCellPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STARTTSSEC)) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSSEC)) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActTimingCellPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasStartTsSec()) {
            this.innerSetStartTsSec(proto.getStartTsSec());
        } else {
            this.innerSetStartTsSec(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTsSec()) {
            this.innerSetEndTsSec(proto.getEndTsSec());
        } else {
            this.innerSetEndTsSec(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ActTimingCellProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActTimingCellPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasStartTsSec()) {
            this.setStartTsSec(proto.getStartTsSec());
            fieldCnt++;
        }
        if (proto.hasEndTsSec()) {
            this.setEndTsSec(proto.getEndTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActTimingCell.Builder getCopyDbBuilder() {
        final ActTimingCell.Builder builder = ActTimingCell.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActTimingCell.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getStartTsSec() != 0L) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }  else if (builder.hasStartTsSec()) {
            // 清理StartTsSec
            builder.clearStartTsSec();
            fieldCnt++;
        }
        if (this.getEndTsSec() != 0L) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }  else if (builder.hasEndTsSec()) {
            // 清理EndTsSec
            builder.clearEndTsSec();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActTimingCell.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STARTTSSEC)) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSSEC)) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActTimingCell proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasStartTsSec()) {
            this.innerSetStartTsSec(proto.getStartTsSec());
        } else {
            this.innerSetStartTsSec(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTsSec()) {
            this.innerSetEndTsSec(proto.getEndTsSec());
        } else {
            this.innerSetEndTsSec(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ActTimingCellProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActTimingCell proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasStartTsSec()) {
            this.setStartTsSec(proto.getStartTsSec());
            fieldCnt++;
        }
        if (proto.hasEndTsSec()) {
            this.setEndTsSec(proto.getEndTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActTimingCell.Builder getCopySsBuilder() {
        final ActTimingCell.Builder builder = ActTimingCell.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActTimingCell.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getStartTsSec() != 0L) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }  else if (builder.hasStartTsSec()) {
            // 清理StartTsSec
            builder.clearStartTsSec();
            fieldCnt++;
        }
        if (this.getEndTsSec() != 0L) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }  else if (builder.hasEndTsSec()) {
            // 清理EndTsSec
            builder.clearEndTsSec();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActTimingCell.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STARTTSSEC)) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSSEC)) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActTimingCell proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasStartTsSec()) {
            this.innerSetStartTsSec(proto.getStartTsSec());
        } else {
            this.innerSetStartTsSec(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTsSec()) {
            this.innerSetEndTsSec(proto.getEndTsSec());
        } else {
            this.innerSetEndTsSec(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ActTimingCellProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActTimingCell proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasStartTsSec()) {
            this.setStartTsSec(proto.getStartTsSec());
            fieldCnt++;
        }
        if (proto.hasEndTsSec()) {
            this.setEndTsSec(proto.getEndTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActTimingCell.Builder builder = ActTimingCell.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActTimingCellProp)) {
            return false;
        }
        final ActTimingCellProp otherNode = (ActTimingCellProp) node;
        if (this.startTsSec != otherNode.startTsSec) {
            return false;
        }
        if (this.endTsSec != otherNode.endTsSec) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
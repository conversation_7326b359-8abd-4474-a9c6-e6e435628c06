package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;

/**
 * <AUTHOR> auto gen
 */
public class NewMailCacheListProp extends AbstractListNode<NewMailCacheProp> {
    /**
     * Create a NewMailCacheListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public NewMailCacheListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to NewMailCacheListProp
     *
     * @return new object
     */
    @Override
    public NewMailCacheProp addEmptyValue() {
        final NewMailCacheProp newProp = new NewMailCacheProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }

                        
}
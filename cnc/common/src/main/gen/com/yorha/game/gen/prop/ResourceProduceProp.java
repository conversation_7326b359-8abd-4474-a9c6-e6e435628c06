package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructPlayer.ResourceProduce;
import com.yorha.proto.StructPlayerPB.ResourceProducePB;


/**
 * <AUTHOR> auto gen
 */
public class ResourceProduceProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_RESOURCEID = 0;
    public static final int FIELD_INDEX_LASTRECEIVEMS = 1;
    public static final int FIELD_INDEX_LASTCLIENTRECEIVEMS = 2;
    public static final int FIELD_INDEX_RESOURCEMARGIN = 3;
    public static final int FIELD_INDEX_REACHLIMITMS = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private int resourceId = Constant.DEFAULT_INT_VALUE;
    private long lastReceiveMs = Constant.DEFAULT_LONG_VALUE;
    private long lastClientReceiveMs = Constant.DEFAULT_LONG_VALUE;
    private long resourceMargin = Constant.DEFAULT_LONG_VALUE;
    private long reachLimitMs = Constant.DEFAULT_LONG_VALUE;

    public ResourceProduceProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ResourceProduceProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get resourceId
     *
     * @return resourceId value
     */
    public int getResourceId() {
        return this.resourceId;
    }

    /**
     * set resourceId && set marked
     *
     * @param resourceId new value
     * @return current object
     */
    public ResourceProduceProp setResourceId(int resourceId) {
        if (this.resourceId != resourceId) {
            this.mark(FIELD_INDEX_RESOURCEID);
            this.resourceId = resourceId;
        }
        return this;
    }

    /**
     * inner set resourceId
     *
     * @param resourceId new value
     */
    private void innerSetResourceId(int resourceId) {
        this.resourceId = resourceId;
    }

    /**
     * get lastReceiveMs
     *
     * @return lastReceiveMs value
     */
    public long getLastReceiveMs() {
        return this.lastReceiveMs;
    }

    /**
     * set lastReceiveMs && set marked
     *
     * @param lastReceiveMs new value
     * @return current object
     */
    public ResourceProduceProp setLastReceiveMs(long lastReceiveMs) {
        if (this.lastReceiveMs != lastReceiveMs) {
            this.mark(FIELD_INDEX_LASTRECEIVEMS);
            this.lastReceiveMs = lastReceiveMs;
        }
        return this;
    }

    /**
     * inner set lastReceiveMs
     *
     * @param lastReceiveMs new value
     */
    private void innerSetLastReceiveMs(long lastReceiveMs) {
        this.lastReceiveMs = lastReceiveMs;
    }

    /**
     * get lastClientReceiveMs
     *
     * @return lastClientReceiveMs value
     */
    public long getLastClientReceiveMs() {
        return this.lastClientReceiveMs;
    }

    /**
     * set lastClientReceiveMs && set marked
     *
     * @param lastClientReceiveMs new value
     * @return current object
     */
    public ResourceProduceProp setLastClientReceiveMs(long lastClientReceiveMs) {
        if (this.lastClientReceiveMs != lastClientReceiveMs) {
            this.mark(FIELD_INDEX_LASTCLIENTRECEIVEMS);
            this.lastClientReceiveMs = lastClientReceiveMs;
        }
        return this;
    }

    /**
     * inner set lastClientReceiveMs
     *
     * @param lastClientReceiveMs new value
     */
    private void innerSetLastClientReceiveMs(long lastClientReceiveMs) {
        this.lastClientReceiveMs = lastClientReceiveMs;
    }

    /**
     * get resourceMargin
     *
     * @return resourceMargin value
     */
    public long getResourceMargin() {
        return this.resourceMargin;
    }

    /**
     * set resourceMargin && set marked
     *
     * @param resourceMargin new value
     * @return current object
     */
    public ResourceProduceProp setResourceMargin(long resourceMargin) {
        if (this.resourceMargin != resourceMargin) {
            this.mark(FIELD_INDEX_RESOURCEMARGIN);
            this.resourceMargin = resourceMargin;
        }
        return this;
    }

    /**
     * inner set resourceMargin
     *
     * @param resourceMargin new value
     */
    private void innerSetResourceMargin(long resourceMargin) {
        this.resourceMargin = resourceMargin;
    }

    /**
     * get reachLimitMs
     *
     * @return reachLimitMs value
     */
    public long getReachLimitMs() {
        return this.reachLimitMs;
    }

    /**
     * set reachLimitMs && set marked
     *
     * @param reachLimitMs new value
     * @return current object
     */
    public ResourceProduceProp setReachLimitMs(long reachLimitMs) {
        if (this.reachLimitMs != reachLimitMs) {
            this.mark(FIELD_INDEX_REACHLIMITMS);
            this.reachLimitMs = reachLimitMs;
        }
        return this;
    }

    /**
     * inner set reachLimitMs
     *
     * @param reachLimitMs new value
     */
    private void innerSetReachLimitMs(long reachLimitMs) {
        this.reachLimitMs = reachLimitMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ResourceProducePB.Builder getCopyCsBuilder() {
        final ResourceProducePB.Builder builder = ResourceProducePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ResourceProducePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getResourceId() != 0) {
            builder.setResourceId(this.getResourceId());
            fieldCnt++;
        }  else if (builder.hasResourceId()) {
            // 清理ResourceId
            builder.clearResourceId();
            fieldCnt++;
        }
        if (this.getLastReceiveMs() != 0L) {
            builder.setLastReceiveMs(this.getLastReceiveMs());
            fieldCnt++;
        }  else if (builder.hasLastReceiveMs()) {
            // 清理LastReceiveMs
            builder.clearLastReceiveMs();
            fieldCnt++;
        }
        if (this.getLastClientReceiveMs() != 0L) {
            builder.setLastClientReceiveMs(this.getLastClientReceiveMs());
            fieldCnt++;
        }  else if (builder.hasLastClientReceiveMs()) {
            // 清理LastClientReceiveMs
            builder.clearLastClientReceiveMs();
            fieldCnt++;
        }
        if (this.getResourceMargin() != 0L) {
            builder.setResourceMargin(this.getResourceMargin());
            fieldCnt++;
        }  else if (builder.hasResourceMargin()) {
            // 清理ResourceMargin
            builder.clearResourceMargin();
            fieldCnt++;
        }
        if (this.getReachLimitMs() != 0L) {
            builder.setReachLimitMs(this.getReachLimitMs());
            fieldCnt++;
        }  else if (builder.hasReachLimitMs()) {
            // 清理ReachLimitMs
            builder.clearReachLimitMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ResourceProducePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RESOURCEID)) {
            builder.setResourceId(this.getResourceId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTRECEIVEMS)) {
            builder.setLastReceiveMs(this.getLastReceiveMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTCLIENTRECEIVEMS)) {
            builder.setLastClientReceiveMs(this.getLastClientReceiveMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCEMARGIN)) {
            builder.setResourceMargin(this.getResourceMargin());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REACHLIMITMS)) {
            builder.setReachLimitMs(this.getReachLimitMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ResourceProducePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RESOURCEID)) {
            builder.setResourceId(this.getResourceId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTRECEIVEMS)) {
            builder.setLastReceiveMs(this.getLastReceiveMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTCLIENTRECEIVEMS)) {
            builder.setLastClientReceiveMs(this.getLastClientReceiveMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCEMARGIN)) {
            builder.setResourceMargin(this.getResourceMargin());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REACHLIMITMS)) {
            builder.setReachLimitMs(this.getReachLimitMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ResourceProducePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasResourceId()) {
            this.innerSetResourceId(proto.getResourceId());
        } else {
            this.innerSetResourceId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastReceiveMs()) {
            this.innerSetLastReceiveMs(proto.getLastReceiveMs());
        } else {
            this.innerSetLastReceiveMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastClientReceiveMs()) {
            this.innerSetLastClientReceiveMs(proto.getLastClientReceiveMs());
        } else {
            this.innerSetLastClientReceiveMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasResourceMargin()) {
            this.innerSetResourceMargin(proto.getResourceMargin());
        } else {
            this.innerSetResourceMargin(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasReachLimitMs()) {
            this.innerSetReachLimitMs(proto.getReachLimitMs());
        } else {
            this.innerSetReachLimitMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ResourceProduceProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ResourceProducePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasResourceId()) {
            this.setResourceId(proto.getResourceId());
            fieldCnt++;
        }
        if (proto.hasLastReceiveMs()) {
            this.setLastReceiveMs(proto.getLastReceiveMs());
            fieldCnt++;
        }
        if (proto.hasLastClientReceiveMs()) {
            this.setLastClientReceiveMs(proto.getLastClientReceiveMs());
            fieldCnt++;
        }
        if (proto.hasResourceMargin()) {
            this.setResourceMargin(proto.getResourceMargin());
            fieldCnt++;
        }
        if (proto.hasReachLimitMs()) {
            this.setReachLimitMs(proto.getReachLimitMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ResourceProduce.Builder getCopyDbBuilder() {
        final ResourceProduce.Builder builder = ResourceProduce.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ResourceProduce.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getResourceId() != 0) {
            builder.setResourceId(this.getResourceId());
            fieldCnt++;
        }  else if (builder.hasResourceId()) {
            // 清理ResourceId
            builder.clearResourceId();
            fieldCnt++;
        }
        if (this.getLastReceiveMs() != 0L) {
            builder.setLastReceiveMs(this.getLastReceiveMs());
            fieldCnt++;
        }  else if (builder.hasLastReceiveMs()) {
            // 清理LastReceiveMs
            builder.clearLastReceiveMs();
            fieldCnt++;
        }
        if (this.getLastClientReceiveMs() != 0L) {
            builder.setLastClientReceiveMs(this.getLastClientReceiveMs());
            fieldCnt++;
        }  else if (builder.hasLastClientReceiveMs()) {
            // 清理LastClientReceiveMs
            builder.clearLastClientReceiveMs();
            fieldCnt++;
        }
        if (this.getResourceMargin() != 0L) {
            builder.setResourceMargin(this.getResourceMargin());
            fieldCnt++;
        }  else if (builder.hasResourceMargin()) {
            // 清理ResourceMargin
            builder.clearResourceMargin();
            fieldCnt++;
        }
        if (this.getReachLimitMs() != 0L) {
            builder.setReachLimitMs(this.getReachLimitMs());
            fieldCnt++;
        }  else if (builder.hasReachLimitMs()) {
            // 清理ReachLimitMs
            builder.clearReachLimitMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ResourceProduce.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RESOURCEID)) {
            builder.setResourceId(this.getResourceId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTRECEIVEMS)) {
            builder.setLastReceiveMs(this.getLastReceiveMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTCLIENTRECEIVEMS)) {
            builder.setLastClientReceiveMs(this.getLastClientReceiveMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCEMARGIN)) {
            builder.setResourceMargin(this.getResourceMargin());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REACHLIMITMS)) {
            builder.setReachLimitMs(this.getReachLimitMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ResourceProduce proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasResourceId()) {
            this.innerSetResourceId(proto.getResourceId());
        } else {
            this.innerSetResourceId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastReceiveMs()) {
            this.innerSetLastReceiveMs(proto.getLastReceiveMs());
        } else {
            this.innerSetLastReceiveMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastClientReceiveMs()) {
            this.innerSetLastClientReceiveMs(proto.getLastClientReceiveMs());
        } else {
            this.innerSetLastClientReceiveMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasResourceMargin()) {
            this.innerSetResourceMargin(proto.getResourceMargin());
        } else {
            this.innerSetResourceMargin(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasReachLimitMs()) {
            this.innerSetReachLimitMs(proto.getReachLimitMs());
        } else {
            this.innerSetReachLimitMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ResourceProduceProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ResourceProduce proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasResourceId()) {
            this.setResourceId(proto.getResourceId());
            fieldCnt++;
        }
        if (proto.hasLastReceiveMs()) {
            this.setLastReceiveMs(proto.getLastReceiveMs());
            fieldCnt++;
        }
        if (proto.hasLastClientReceiveMs()) {
            this.setLastClientReceiveMs(proto.getLastClientReceiveMs());
            fieldCnt++;
        }
        if (proto.hasResourceMargin()) {
            this.setResourceMargin(proto.getResourceMargin());
            fieldCnt++;
        }
        if (proto.hasReachLimitMs()) {
            this.setReachLimitMs(proto.getReachLimitMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ResourceProduce.Builder getCopySsBuilder() {
        final ResourceProduce.Builder builder = ResourceProduce.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ResourceProduce.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getResourceId() != 0) {
            builder.setResourceId(this.getResourceId());
            fieldCnt++;
        }  else if (builder.hasResourceId()) {
            // 清理ResourceId
            builder.clearResourceId();
            fieldCnt++;
        }
        if (this.getLastReceiveMs() != 0L) {
            builder.setLastReceiveMs(this.getLastReceiveMs());
            fieldCnt++;
        }  else if (builder.hasLastReceiveMs()) {
            // 清理LastReceiveMs
            builder.clearLastReceiveMs();
            fieldCnt++;
        }
        if (this.getLastClientReceiveMs() != 0L) {
            builder.setLastClientReceiveMs(this.getLastClientReceiveMs());
            fieldCnt++;
        }  else if (builder.hasLastClientReceiveMs()) {
            // 清理LastClientReceiveMs
            builder.clearLastClientReceiveMs();
            fieldCnt++;
        }
        if (this.getResourceMargin() != 0L) {
            builder.setResourceMargin(this.getResourceMargin());
            fieldCnt++;
        }  else if (builder.hasResourceMargin()) {
            // 清理ResourceMargin
            builder.clearResourceMargin();
            fieldCnt++;
        }
        if (this.getReachLimitMs() != 0L) {
            builder.setReachLimitMs(this.getReachLimitMs());
            fieldCnt++;
        }  else if (builder.hasReachLimitMs()) {
            // 清理ReachLimitMs
            builder.clearReachLimitMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ResourceProduce.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RESOURCEID)) {
            builder.setResourceId(this.getResourceId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTRECEIVEMS)) {
            builder.setLastReceiveMs(this.getLastReceiveMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTCLIENTRECEIVEMS)) {
            builder.setLastClientReceiveMs(this.getLastClientReceiveMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCEMARGIN)) {
            builder.setResourceMargin(this.getResourceMargin());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REACHLIMITMS)) {
            builder.setReachLimitMs(this.getReachLimitMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ResourceProduce proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasResourceId()) {
            this.innerSetResourceId(proto.getResourceId());
        } else {
            this.innerSetResourceId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastReceiveMs()) {
            this.innerSetLastReceiveMs(proto.getLastReceiveMs());
        } else {
            this.innerSetLastReceiveMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastClientReceiveMs()) {
            this.innerSetLastClientReceiveMs(proto.getLastClientReceiveMs());
        } else {
            this.innerSetLastClientReceiveMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasResourceMargin()) {
            this.innerSetResourceMargin(proto.getResourceMargin());
        } else {
            this.innerSetResourceMargin(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasReachLimitMs()) {
            this.innerSetReachLimitMs(proto.getReachLimitMs());
        } else {
            this.innerSetReachLimitMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ResourceProduceProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ResourceProduce proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasResourceId()) {
            this.setResourceId(proto.getResourceId());
            fieldCnt++;
        }
        if (proto.hasLastReceiveMs()) {
            this.setLastReceiveMs(proto.getLastReceiveMs());
            fieldCnt++;
        }
        if (proto.hasLastClientReceiveMs()) {
            this.setLastClientReceiveMs(proto.getLastClientReceiveMs());
            fieldCnt++;
        }
        if (proto.hasResourceMargin()) {
            this.setResourceMargin(proto.getResourceMargin());
            fieldCnt++;
        }
        if (proto.hasReachLimitMs()) {
            this.setReachLimitMs(proto.getReachLimitMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ResourceProduce.Builder builder = ResourceProduce.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.resourceId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ResourceProduceProp)) {
            return false;
        }
        final ResourceProduceProp otherNode = (ResourceProduceProp) node;
        if (this.resourceId != otherNode.resourceId) {
            return false;
        }
        if (this.lastReceiveMs != otherNode.lastReceiveMs) {
            return false;
        }
        if (this.lastClientReceiveMs != otherNode.lastClientReceiveMs) {
            return false;
        }
        if (this.resourceMargin != otherNode.resourceMargin) {
            return false;
        }
        if (this.reachLimitMs != otherNode.reachLimitMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
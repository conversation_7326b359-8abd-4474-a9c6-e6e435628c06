package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Army.ArmyCollectResource;
import com.yorha.proto.Struct;
import com.yorha.proto.ArmyPB.ArmyCollectResourcePB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ArmyCollectResourceProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_TEMPLATEID = 1;
    public static final int FIELD_INDEX_NUM = 2;
    public static final int FIELD_INDEX_COLLECTENDTSMS = 3;
    public static final int FIELD_INDEX_POINT = 4;
    public static final int FIELD_INDEX_OUTTIMES = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private long id = Constant.DEFAULT_LONG_VALUE;
    private int templateId = Constant.DEFAULT_INT_VALUE;
    private long num = Constant.DEFAULT_LONG_VALUE;
    private long collectEndTsMs = Constant.DEFAULT_LONG_VALUE;
    private PointProp point = null;
    private int outTimes = Constant.DEFAULT_INT_VALUE;

    public ArmyCollectResourceProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ArmyCollectResourceProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public long getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public ArmyCollectResourceProp setId(long id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(long id) {
        this.id = id;
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public ArmyCollectResourceProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }

    /**
     * get num
     *
     * @return num value
     */
    public long getNum() {
        return this.num;
    }

    /**
     * set num && set marked
     *
     * @param num new value
     * @return current object
     */
    public ArmyCollectResourceProp setNum(long num) {
        if (this.num != num) {
            this.mark(FIELD_INDEX_NUM);
            this.num = num;
        }
        return this;
    }

    /**
     * inner set num
     *
     * @param num new value
     */
    private void innerSetNum(long num) {
        this.num = num;
    }

    /**
     * get collectEndTsMs
     *
     * @return collectEndTsMs value
     */
    public long getCollectEndTsMs() {
        return this.collectEndTsMs;
    }

    /**
     * set collectEndTsMs && set marked
     *
     * @param collectEndTsMs new value
     * @return current object
     */
    public ArmyCollectResourceProp setCollectEndTsMs(long collectEndTsMs) {
        if (this.collectEndTsMs != collectEndTsMs) {
            this.mark(FIELD_INDEX_COLLECTENDTSMS);
            this.collectEndTsMs = collectEndTsMs;
        }
        return this;
    }

    /**
     * inner set collectEndTsMs
     *
     * @param collectEndTsMs new value
     */
    private void innerSetCollectEndTsMs(long collectEndTsMs) {
        this.collectEndTsMs = collectEndTsMs;
    }

    /**
     * get point
     *
     * @return point value
     */
    public PointProp getPoint() {
        if (this.point == null) {
            this.point = new PointProp(this, FIELD_INDEX_POINT);
        }
        return this.point;
    }

    /**
     * get outTimes
     *
     * @return outTimes value
     */
    public int getOutTimes() {
        return this.outTimes;
    }

    /**
     * set outTimes && set marked
     *
     * @param outTimes new value
     * @return current object
     */
    public ArmyCollectResourceProp setOutTimes(int outTimes) {
        if (this.outTimes != outTimes) {
            this.mark(FIELD_INDEX_OUTTIMES);
            this.outTimes = outTimes;
        }
        return this;
    }

    /**
     * inner set outTimes
     *
     * @param outTimes new value
     */
    private void innerSetOutTimes(int outTimes) {
        this.outTimes = outTimes;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArmyCollectResourcePB.Builder getCopyCsBuilder() {
        final ArmyCollectResourcePB.Builder builder = ArmyCollectResourcePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ArmyCollectResourcePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getNum() != 0L) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        if (this.getCollectEndTsMs() != 0L) {
            builder.setCollectEndTsMs(this.getCollectEndTsMs());
            fieldCnt++;
        }  else if (builder.hasCollectEndTsMs()) {
            // 清理CollectEndTsMs
            builder.clearCollectEndTsMs();
            fieldCnt++;
        }
        if (this.point != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.point.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getOutTimes() != 0) {
            builder.setOutTimes(this.getOutTimes());
            fieldCnt++;
        }  else if (builder.hasOutTimes()) {
            // 清理OutTimes
            builder.clearOutTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ArmyCollectResourcePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COLLECTENDTSMS)) {
            builder.setCollectEndTsMs(this.getCollectEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_OUTTIMES)) {
            builder.setOutTimes(this.getOutTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ArmyCollectResourcePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COLLECTENDTSMS)) {
            builder.setCollectEndTsMs(this.getCollectEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToAndClearDeleteKeysCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_OUTTIMES)) {
            builder.setOutTimes(this.getOutTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ArmyCollectResourcePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCollectEndTsMs()) {
            this.innerSetCollectEndTsMs(proto.getCollectEndTsMs());
        } else {
            this.innerSetCollectEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromCs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromCs(proto.getPoint());
            }
        }
        if (proto.hasOutTimes()) {
            this.innerSetOutTimes(proto.getOutTimes());
        } else {
            this.innerSetOutTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ArmyCollectResourceProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ArmyCollectResourcePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        if (proto.hasCollectEndTsMs()) {
            this.setCollectEndTsMs(proto.getCollectEndTsMs());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromCs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasOutTimes()) {
            this.setOutTimes(proto.getOutTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArmyCollectResource.Builder getCopyDbBuilder() {
        final ArmyCollectResource.Builder builder = ArmyCollectResource.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ArmyCollectResource.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getNum() != 0L) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        if (this.getCollectEndTsMs() != 0L) {
            builder.setCollectEndTsMs(this.getCollectEndTsMs());
            fieldCnt++;
        }  else if (builder.hasCollectEndTsMs()) {
            // 清理CollectEndTsMs
            builder.clearCollectEndTsMs();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getOutTimes() != 0) {
            builder.setOutTimes(this.getOutTimes());
            fieldCnt++;
        }  else if (builder.hasOutTimes()) {
            // 清理OutTimes
            builder.clearOutTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ArmyCollectResource.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COLLECTENDTSMS)) {
            builder.setCollectEndTsMs(this.getCollectEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToDb(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_OUTTIMES)) {
            builder.setOutTimes(this.getOutTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ArmyCollectResource proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCollectEndTsMs()) {
            this.innerSetCollectEndTsMs(proto.getCollectEndTsMs());
        } else {
            this.innerSetCollectEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromDb(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromDb(proto.getPoint());
            }
        }
        if (proto.hasOutTimes()) {
            this.innerSetOutTimes(proto.getOutTimes());
        } else {
            this.innerSetOutTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ArmyCollectResourceProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ArmyCollectResource proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        if (proto.hasCollectEndTsMs()) {
            this.setCollectEndTsMs(proto.getCollectEndTsMs());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromDb(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasOutTimes()) {
            this.setOutTimes(proto.getOutTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArmyCollectResource.Builder getCopySsBuilder() {
        final ArmyCollectResource.Builder builder = ArmyCollectResource.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ArmyCollectResource.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getNum() != 0L) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        if (this.getCollectEndTsMs() != 0L) {
            builder.setCollectEndTsMs(this.getCollectEndTsMs());
            fieldCnt++;
        }  else if (builder.hasCollectEndTsMs()) {
            // 清理CollectEndTsMs
            builder.clearCollectEndTsMs();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getOutTimes() != 0) {
            builder.setOutTimes(this.getOutTimes());
            fieldCnt++;
        }  else if (builder.hasOutTimes()) {
            // 清理OutTimes
            builder.clearOutTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ArmyCollectResource.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COLLECTENDTSMS)) {
            builder.setCollectEndTsMs(this.getCollectEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToSs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_OUTTIMES)) {
            builder.setOutTimes(this.getOutTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ArmyCollectResource proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCollectEndTsMs()) {
            this.innerSetCollectEndTsMs(proto.getCollectEndTsMs());
        } else {
            this.innerSetCollectEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromSs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromSs(proto.getPoint());
            }
        }
        if (proto.hasOutTimes()) {
            this.innerSetOutTimes(proto.getOutTimes());
        } else {
            this.innerSetOutTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ArmyCollectResourceProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ArmyCollectResource proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        if (proto.hasCollectEndTsMs()) {
            this.setCollectEndTsMs(proto.getCollectEndTsMs());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromSs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasOutTimes()) {
            this.setOutTimes(proto.getOutTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ArmyCollectResource.Builder builder = ArmyCollectResource.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            this.point.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.point != null) {
            this.point.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ArmyCollectResourceProp)) {
            return false;
        }
        final ArmyCollectResourceProp otherNode = (ArmyCollectResourceProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        if (this.num != otherNode.num) {
            return false;
        }
        if (this.collectEndTsMs != otherNode.collectEndTsMs) {
            return false;
        }
        if (!this.getPoint().compareDataTo(otherNode.getPoint())) {
            return false;
        }
        if (this.outTimes != otherNode.outTimes) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ItemPair;
import com.yorha.proto.StructPB.ItemPairPB;


/**
 * <AUTHOR> auto gen
 */
public class ItemPairProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ITEMTEMPLATEID = 0;
    public static final int FIELD_INDEX_COUNT = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int itemTemplateId = Constant.DEFAULT_INT_VALUE;
    private int count = Constant.DEFAULT_INT_VALUE;

    public ItemPairProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ItemPairProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get itemTemplateId
     *
     * @return itemTemplateId value
     */
    public int getItemTemplateId() {
        return this.itemTemplateId;
    }

    /**
     * set itemTemplateId && set marked
     *
     * @param itemTemplateId new value
     * @return current object
     */
    public ItemPairProp setItemTemplateId(int itemTemplateId) {
        if (this.itemTemplateId != itemTemplateId) {
            this.mark(FIELD_INDEX_ITEMTEMPLATEID);
            this.itemTemplateId = itemTemplateId;
        }
        return this;
    }

    /**
     * inner set itemTemplateId
     *
     * @param itemTemplateId new value
     */
    private void innerSetItemTemplateId(int itemTemplateId) {
        this.itemTemplateId = itemTemplateId;
    }

    /**
     * get count
     *
     * @return count value
     */
    public int getCount() {
        return this.count;
    }

    /**
     * set count && set marked
     *
     * @param count new value
     * @return current object
     */
    public ItemPairProp setCount(int count) {
        if (this.count != count) {
            this.mark(FIELD_INDEX_COUNT);
            this.count = count;
        }
        return this;
    }

    /**
     * inner set count
     *
     * @param count new value
     */
    private void innerSetCount(int count) {
        this.count = count;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ItemPairPB.Builder getCopyCsBuilder() {
        final ItemPairPB.Builder builder = ItemPairPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ItemPairPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getItemTemplateId() != 0) {
            builder.setItemTemplateId(this.getItemTemplateId());
            fieldCnt++;
        }  else if (builder.hasItemTemplateId()) {
            // 清理ItemTemplateId
            builder.clearItemTemplateId();
            fieldCnt++;
        }
        if (this.getCount() != 0) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }  else if (builder.hasCount()) {
            // 清理Count
            builder.clearCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ItemPairPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ITEMTEMPLATEID)) {
            builder.setItemTemplateId(this.getItemTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ItemPairPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ITEMTEMPLATEID)) {
            builder.setItemTemplateId(this.getItemTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ItemPairPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasItemTemplateId()) {
            this.innerSetItemTemplateId(proto.getItemTemplateId());
        } else {
            this.innerSetItemTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCount()) {
            this.innerSetCount(proto.getCount());
        } else {
            this.innerSetCount(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ItemPairProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ItemPairPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasItemTemplateId()) {
            this.setItemTemplateId(proto.getItemTemplateId());
            fieldCnt++;
        }
        if (proto.hasCount()) {
            this.setCount(proto.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ItemPair.Builder getCopyDbBuilder() {
        final ItemPair.Builder builder = ItemPair.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ItemPair.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getItemTemplateId() != 0) {
            builder.setItemTemplateId(this.getItemTemplateId());
            fieldCnt++;
        }  else if (builder.hasItemTemplateId()) {
            // 清理ItemTemplateId
            builder.clearItemTemplateId();
            fieldCnt++;
        }
        if (this.getCount() != 0) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }  else if (builder.hasCount()) {
            // 清理Count
            builder.clearCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ItemPair.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ITEMTEMPLATEID)) {
            builder.setItemTemplateId(this.getItemTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ItemPair proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasItemTemplateId()) {
            this.innerSetItemTemplateId(proto.getItemTemplateId());
        } else {
            this.innerSetItemTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCount()) {
            this.innerSetCount(proto.getCount());
        } else {
            this.innerSetCount(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ItemPairProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ItemPair proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasItemTemplateId()) {
            this.setItemTemplateId(proto.getItemTemplateId());
            fieldCnt++;
        }
        if (proto.hasCount()) {
            this.setCount(proto.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ItemPair.Builder getCopySsBuilder() {
        final ItemPair.Builder builder = ItemPair.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ItemPair.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getItemTemplateId() != 0) {
            builder.setItemTemplateId(this.getItemTemplateId());
            fieldCnt++;
        }  else if (builder.hasItemTemplateId()) {
            // 清理ItemTemplateId
            builder.clearItemTemplateId();
            fieldCnt++;
        }
        if (this.getCount() != 0) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }  else if (builder.hasCount()) {
            // 清理Count
            builder.clearCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ItemPair.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ITEMTEMPLATEID)) {
            builder.setItemTemplateId(this.getItemTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ItemPair proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasItemTemplateId()) {
            this.innerSetItemTemplateId(proto.getItemTemplateId());
        } else {
            this.innerSetItemTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCount()) {
            this.innerSetCount(proto.getCount());
        } else {
            this.innerSetCount(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ItemPairProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ItemPair proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasItemTemplateId()) {
            this.setItemTemplateId(proto.getItemTemplateId());
            fieldCnt++;
        }
        if (proto.hasCount()) {
            this.setCount(proto.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ItemPair.Builder builder = ItemPair.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.itemTemplateId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ItemPairProp)) {
            return false;
        }
        final ItemPairProp otherNode = (ItemPairProp) node;
        if (this.itemTemplateId != otherNode.itemTemplateId) {
            return false;
        }
        if (this.count != otherNode.count) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Army.ArmyResourcesModel;
import com.yorha.proto.Army;
import com.yorha.proto.Struct;
import com.yorha.proto.ArmyPB.ArmyResourcesModelPB;
import com.yorha.proto.ArmyPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ArmyResourcesModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_PLUNDER = 0;
    public static final int FIELD_INDEX_COLLECT = 1;
    public static final int FIELD_INDEX_OUTTIMES = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private Int32CurrencyMapProp plunder = null;
    private ArmyCollectResourceListProp collect = null;
    private int outTimes = Constant.DEFAULT_INT_VALUE;

    public ArmyResourcesModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ArmyResourcesModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get plunder
     *
     * @return plunder value
     */
    public Int32CurrencyMapProp getPlunder() {
        if (this.plunder == null) {
            this.plunder = new Int32CurrencyMapProp(this, FIELD_INDEX_PLUNDER);
        }
        return this.plunder;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putPlunderV(CurrencyProp v) {
        this.getPlunder().put(v.getType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public CurrencyProp addEmptyPlunder(Integer k) {
        return this.getPlunder().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getPlunderSize() {
        if (this.plunder == null) {
            return 0;
        }
        return this.plunder.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isPlunderEmpty() {
        if (this.plunder == null) {
            return true;
        }
        return this.plunder.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public CurrencyProp getPlunderV(Integer k) {
        if (this.plunder == null || !this.plunder.containsKey(k)) {
            return null;
        }
        return this.plunder.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearPlunder() {
        if (this.plunder != null) {
            this.plunder.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removePlunderV(Integer k) {
        if (this.plunder != null) {
            this.plunder.remove(k);
        }
    }
    /**
     * get collect
     *
     * @return collect value
     */
    public ArmyCollectResourceListProp getCollect() {
        if (this.collect == null) {
            this.collect = new ArmyCollectResourceListProp(this, FIELD_INDEX_COLLECT);
        }
        return this.collect;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addCollect(ArmyCollectResourceProp v) {
        this.getCollect().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ArmyCollectResourceProp getCollectIndex(int index) {
        return this.getCollect().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public ArmyCollectResourceProp removeCollect(ArmyCollectResourceProp v) {
        if (this.collect == null) {
            return null;
        }
        if(this.collect.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getCollectSize() {
        if (this.collect == null) {
            return 0;
        }
        return this.collect.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isCollectEmpty() {
        if (this.collect == null) {
            return true;
        }
        return this.getCollect().isEmpty();
    }

    /**
     * clear list
     */
    public void clearCollect() {
        this.getCollect().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ArmyCollectResourceProp removeCollectIndex(int index) {
        return this.getCollect().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public ArmyCollectResourceProp setCollectIndex(int index, ArmyCollectResourceProp v) {
        return this.getCollect().set(index, v);
    }
    /**
     * get outTimes
     *
     * @return outTimes value
     */
    public int getOutTimes() {
        return this.outTimes;
    }

    /**
     * set outTimes && set marked
     *
     * @param outTimes new value
     * @return current object
     */
    public ArmyResourcesModelProp setOutTimes(int outTimes) {
        if (this.outTimes != outTimes) {
            this.mark(FIELD_INDEX_OUTTIMES);
            this.outTimes = outTimes;
        }
        return this;
    }

    /**
     * inner set outTimes
     *
     * @param outTimes new value
     */
    private void innerSetOutTimes(int outTimes) {
        this.outTimes = outTimes;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArmyResourcesModelPB.Builder getCopyCsBuilder() {
        final ArmyResourcesModelPB.Builder builder = ArmyResourcesModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ArmyResourcesModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.plunder != null) {
            StructPB.Int32CurrencyMapPB.Builder tmpBuilder = StructPB.Int32CurrencyMapPB.newBuilder();
            final int tmpFieldCnt = this.plunder.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlunder(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlunder();
            }
        }  else if (builder.hasPlunder()) {
            // 清理Plunder
            builder.clearPlunder();
            fieldCnt++;
        }
        if (this.collect != null) {
            ArmyPB.ArmyCollectResourceListPB.Builder tmpBuilder = ArmyPB.ArmyCollectResourceListPB.newBuilder();
            final int tmpFieldCnt = this.collect.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCollect(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCollect();
            }
        }  else if (builder.hasCollect()) {
            // 清理Collect
            builder.clearCollect();
            fieldCnt++;
        }
        if (this.getOutTimes() != 0) {
            builder.setOutTimes(this.getOutTimes());
            fieldCnt++;
        }  else if (builder.hasOutTimes()) {
            // 清理OutTimes
            builder.clearOutTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ArmyResourcesModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLUNDER) && this.plunder != null) {
            final boolean needClear = !builder.hasPlunder();
            final int tmpFieldCnt = this.plunder.copyChangeToCs(builder.getPlunderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlunder();
            }
        }
        if (this.hasMark(FIELD_INDEX_COLLECT) && this.collect != null) {
            final boolean needClear = !builder.hasCollect();
            final int tmpFieldCnt = this.collect.copyChangeToCs(builder.getCollectBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCollect();
            }
        }
        if (this.hasMark(FIELD_INDEX_OUTTIMES)) {
            builder.setOutTimes(this.getOutTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ArmyResourcesModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLUNDER) && this.plunder != null) {
            final boolean needClear = !builder.hasPlunder();
            final int tmpFieldCnt = this.plunder.copyChangeToAndClearDeleteKeysCs(builder.getPlunderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlunder();
            }
        }
        if (this.hasMark(FIELD_INDEX_COLLECT) && this.collect != null) {
            final boolean needClear = !builder.hasCollect();
            final int tmpFieldCnt = this.collect.copyChangeToCs(builder.getCollectBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCollect();
            }
        }
        if (this.hasMark(FIELD_INDEX_OUTTIMES)) {
            builder.setOutTimes(this.getOutTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ArmyResourcesModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlunder()) {
            this.getPlunder().mergeFromCs(proto.getPlunder());
        } else {
            if (this.plunder != null) {
                this.plunder.mergeFromCs(proto.getPlunder());
            }
        }
        if (proto.hasCollect()) {
            this.getCollect().mergeFromCs(proto.getCollect());
        } else {
            if (this.collect != null) {
                this.collect.mergeFromCs(proto.getCollect());
            }
        }
        if (proto.hasOutTimes()) {
            this.innerSetOutTimes(proto.getOutTimes());
        } else {
            this.innerSetOutTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ArmyResourcesModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ArmyResourcesModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlunder()) {
            this.getPlunder().mergeChangeFromCs(proto.getPlunder());
            fieldCnt++;
        }
        if (proto.hasCollect()) {
            this.getCollect().mergeChangeFromCs(proto.getCollect());
            fieldCnt++;
        }
        if (proto.hasOutTimes()) {
            this.setOutTimes(proto.getOutTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArmyResourcesModel.Builder getCopyDbBuilder() {
        final ArmyResourcesModel.Builder builder = ArmyResourcesModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ArmyResourcesModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.plunder != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.plunder.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlunder(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlunder();
            }
        }  else if (builder.hasPlunder()) {
            // 清理Plunder
            builder.clearPlunder();
            fieldCnt++;
        }
        if (this.collect != null) {
            Army.ArmyCollectResourceList.Builder tmpBuilder = Army.ArmyCollectResourceList.newBuilder();
            final int tmpFieldCnt = this.collect.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCollect(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCollect();
            }
        }  else if (builder.hasCollect()) {
            // 清理Collect
            builder.clearCollect();
            fieldCnt++;
        }
        if (this.getOutTimes() != 0) {
            builder.setOutTimes(this.getOutTimes());
            fieldCnt++;
        }  else if (builder.hasOutTimes()) {
            // 清理OutTimes
            builder.clearOutTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ArmyResourcesModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLUNDER) && this.plunder != null) {
            final boolean needClear = !builder.hasPlunder();
            final int tmpFieldCnt = this.plunder.copyChangeToDb(builder.getPlunderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlunder();
            }
        }
        if (this.hasMark(FIELD_INDEX_COLLECT) && this.collect != null) {
            final boolean needClear = !builder.hasCollect();
            final int tmpFieldCnt = this.collect.copyChangeToDb(builder.getCollectBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCollect();
            }
        }
        if (this.hasMark(FIELD_INDEX_OUTTIMES)) {
            builder.setOutTimes(this.getOutTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ArmyResourcesModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlunder()) {
            this.getPlunder().mergeFromDb(proto.getPlunder());
        } else {
            if (this.plunder != null) {
                this.plunder.mergeFromDb(proto.getPlunder());
            }
        }
        if (proto.hasCollect()) {
            this.getCollect().mergeFromDb(proto.getCollect());
        } else {
            if (this.collect != null) {
                this.collect.mergeFromDb(proto.getCollect());
            }
        }
        if (proto.hasOutTimes()) {
            this.innerSetOutTimes(proto.getOutTimes());
        } else {
            this.innerSetOutTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ArmyResourcesModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ArmyResourcesModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlunder()) {
            this.getPlunder().mergeChangeFromDb(proto.getPlunder());
            fieldCnt++;
        }
        if (proto.hasCollect()) {
            this.getCollect().mergeChangeFromDb(proto.getCollect());
            fieldCnt++;
        }
        if (proto.hasOutTimes()) {
            this.setOutTimes(proto.getOutTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArmyResourcesModel.Builder getCopySsBuilder() {
        final ArmyResourcesModel.Builder builder = ArmyResourcesModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ArmyResourcesModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.plunder != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.plunder.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlunder(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlunder();
            }
        }  else if (builder.hasPlunder()) {
            // 清理Plunder
            builder.clearPlunder();
            fieldCnt++;
        }
        if (this.collect != null) {
            Army.ArmyCollectResourceList.Builder tmpBuilder = Army.ArmyCollectResourceList.newBuilder();
            final int tmpFieldCnt = this.collect.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCollect(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCollect();
            }
        }  else if (builder.hasCollect()) {
            // 清理Collect
            builder.clearCollect();
            fieldCnt++;
        }
        if (this.getOutTimes() != 0) {
            builder.setOutTimes(this.getOutTimes());
            fieldCnt++;
        }  else if (builder.hasOutTimes()) {
            // 清理OutTimes
            builder.clearOutTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ArmyResourcesModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLUNDER) && this.plunder != null) {
            final boolean needClear = !builder.hasPlunder();
            final int tmpFieldCnt = this.plunder.copyChangeToSs(builder.getPlunderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlunder();
            }
        }
        if (this.hasMark(FIELD_INDEX_COLLECT) && this.collect != null) {
            final boolean needClear = !builder.hasCollect();
            final int tmpFieldCnt = this.collect.copyChangeToSs(builder.getCollectBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCollect();
            }
        }
        if (this.hasMark(FIELD_INDEX_OUTTIMES)) {
            builder.setOutTimes(this.getOutTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ArmyResourcesModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlunder()) {
            this.getPlunder().mergeFromSs(proto.getPlunder());
        } else {
            if (this.plunder != null) {
                this.plunder.mergeFromSs(proto.getPlunder());
            }
        }
        if (proto.hasCollect()) {
            this.getCollect().mergeFromSs(proto.getCollect());
        } else {
            if (this.collect != null) {
                this.collect.mergeFromSs(proto.getCollect());
            }
        }
        if (proto.hasOutTimes()) {
            this.innerSetOutTimes(proto.getOutTimes());
        } else {
            this.innerSetOutTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ArmyResourcesModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ArmyResourcesModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlunder()) {
            this.getPlunder().mergeChangeFromSs(proto.getPlunder());
            fieldCnt++;
        }
        if (proto.hasCollect()) {
            this.getCollect().mergeChangeFromSs(proto.getCollect());
            fieldCnt++;
        }
        if (proto.hasOutTimes()) {
            this.setOutTimes(proto.getOutTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ArmyResourcesModel.Builder builder = ArmyResourcesModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_PLUNDER) && this.plunder != null) {
            this.plunder.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_COLLECT) && this.collect != null) {
            this.collect.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.plunder != null) {
            this.plunder.markAll();
        }
        if (this.collect != null) {
            this.collect.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ArmyResourcesModelProp)) {
            return false;
        }
        final ArmyResourcesModelProp otherNode = (ArmyResourcesModelProp) node;
        if (!this.getPlunder().compareDataTo(otherNode.getPlunder())) {
            return false;
        }
        if (!this.getCollect().compareDataTo(otherNode.getCollect())) {
            return false;
        }
        if (this.outTimes != otherNode.outTimes) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerInnerBuildRHModel;
import com.yorha.proto.Basic;
import com.yorha.proto.Player;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.PlayerPB.PlayerInnerBuildRHModelPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.PlayerPB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerInnerBuildRHModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_INNERBUILDRH = 0;
    public static final int FIELD_INDEX_VIEWID = 1;
    public static final int FIELD_INDEX_BASEID = 2;
    public static final int FIELD_INDEX_CONSTRUCTIONPOINT = 3;
    public static final int FIELD_INDEX_UPGRADES = 4;
    public static final int FIELD_INDEX_UNITS = 5;
    public static final int FIELD_INDEX_SKILLS = 6;
    public static final int FIELD_INDEX_AREAS = 7;
    public static final int FIELD_INDEX_DEFEND = 8;
    public static final int FIELD_INDEX_BASEEXPANDID = 9;

    public static final int FIELD_COUNT = 10;

    private long markBits0 = 0L;

    private Int32InnerBuildRHMapProp innerBuildRH = null;
    private int viewId = Constant.DEFAULT_INT_VALUE;
    private int baseId = Constant.DEFAULT_INT_VALUE;
    private int constructionPoint = Constant.DEFAULT_INT_VALUE;
    private Int32PlayerInnerBuildUpgradeMapProp upgrades = null;
    private Int32ListProp units = null;
    private Int32ListProp skills = null;
    private Int32BaseAreaMapProp areas = null;
    private PlayerDefendInfoProp defend = null;
    private int baseExpandId = Constant.DEFAULT_INT_VALUE;

    public PlayerInnerBuildRHModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerInnerBuildRHModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get innerBuildRH
     *
     * @return innerBuildRH value
     */
    public Int32InnerBuildRHMapProp getInnerBuildRH() {
        if (this.innerBuildRH == null) {
            this.innerBuildRH = new Int32InnerBuildRHMapProp(this, FIELD_INDEX_INNERBUILDRH);
        }
        return this.innerBuildRH;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putInnerBuildRHV(InnerBuildRHProp v) {
        this.getInnerBuildRH().put(v.getBuildId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public InnerBuildRHProp addEmptyInnerBuildRH(Integer k) {
        return this.getInnerBuildRH().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getInnerBuildRHSize() {
        if (this.innerBuildRH == null) {
            return 0;
        }
        return this.innerBuildRH.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isInnerBuildRHEmpty() {
        if (this.innerBuildRH == null) {
            return true;
        }
        return this.innerBuildRH.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public InnerBuildRHProp getInnerBuildRHV(Integer k) {
        if (this.innerBuildRH == null || !this.innerBuildRH.containsKey(k)) {
            return null;
        }
        return this.innerBuildRH.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearInnerBuildRH() {
        if (this.innerBuildRH != null) {
            this.innerBuildRH.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeInnerBuildRHV(Integer k) {
        if (this.innerBuildRH != null) {
            this.innerBuildRH.remove(k);
        }
    }
    /**
     * get viewId
     *
     * @return viewId value
     */
    public int getViewId() {
        return this.viewId;
    }

    /**
     * set viewId && set marked
     *
     * @param viewId new value
     * @return current object
     */
    public PlayerInnerBuildRHModelProp setViewId(int viewId) {
        if (this.viewId != viewId) {
            this.mark(FIELD_INDEX_VIEWID);
            this.viewId = viewId;
        }
        return this;
    }

    /**
     * inner set viewId
     *
     * @param viewId new value
     */
    private void innerSetViewId(int viewId) {
        this.viewId = viewId;
    }

    /**
     * get baseId
     *
     * @return baseId value
     */
    public int getBaseId() {
        return this.baseId;
    }

    /**
     * set baseId && set marked
     *
     * @param baseId new value
     * @return current object
     */
    public PlayerInnerBuildRHModelProp setBaseId(int baseId) {
        if (this.baseId != baseId) {
            this.mark(FIELD_INDEX_BASEID);
            this.baseId = baseId;
        }
        return this;
    }

    /**
     * inner set baseId
     *
     * @param baseId new value
     */
    private void innerSetBaseId(int baseId) {
        this.baseId = baseId;
    }

    /**
     * get constructionPoint
     *
     * @return constructionPoint value
     */
    public int getConstructionPoint() {
        return this.constructionPoint;
    }

    /**
     * set constructionPoint && set marked
     *
     * @param constructionPoint new value
     * @return current object
     */
    public PlayerInnerBuildRHModelProp setConstructionPoint(int constructionPoint) {
        if (this.constructionPoint != constructionPoint) {
            this.mark(FIELD_INDEX_CONSTRUCTIONPOINT);
            this.constructionPoint = constructionPoint;
        }
        return this;
    }

    /**
     * inner set constructionPoint
     *
     * @param constructionPoint new value
     */
    private void innerSetConstructionPoint(int constructionPoint) {
        this.constructionPoint = constructionPoint;
    }

    /**
     * get upgrades
     *
     * @return upgrades value
     */
    public Int32PlayerInnerBuildUpgradeMapProp getUpgrades() {
        if (this.upgrades == null) {
            this.upgrades = new Int32PlayerInnerBuildUpgradeMapProp(this, FIELD_INDEX_UPGRADES);
        }
        return this.upgrades;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putUpgradesV(PlayerInnerBuildUpgradeProp v) {
        this.getUpgrades().put(v.getUpgradeId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerInnerBuildUpgradeProp addEmptyUpgrades(Integer k) {
        return this.getUpgrades().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getUpgradesSize() {
        if (this.upgrades == null) {
            return 0;
        }
        return this.upgrades.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isUpgradesEmpty() {
        if (this.upgrades == null) {
            return true;
        }
        return this.upgrades.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerInnerBuildUpgradeProp getUpgradesV(Integer k) {
        if (this.upgrades == null || !this.upgrades.containsKey(k)) {
            return null;
        }
        return this.upgrades.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearUpgrades() {
        if (this.upgrades != null) {
            this.upgrades.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeUpgradesV(Integer k) {
        if (this.upgrades != null) {
            this.upgrades.remove(k);
        }
    }
    /**
     * get units
     *
     * @return units value
     */
    public Int32ListProp getUnits() {
        if (this.units == null) {
            this.units = new Int32ListProp(this, FIELD_INDEX_UNITS);
        }
        return this.units;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addUnits(Integer v) {
        this.getUnits().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getUnitsIndex(int index) {
        return this.getUnits().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeUnits(Integer v) {
        if (this.units == null) {
            return null;
        }
        if(this.units.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getUnitsSize() {
        if (this.units == null) {
            return 0;
        }
        return this.units.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isUnitsEmpty() {
        if (this.units == null) {
            return true;
        }
        return this.getUnits().isEmpty();
    }

    /**
     * clear list
     */
    public void clearUnits() {
        this.getUnits().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeUnitsIndex(int index) {
        return this.getUnits().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setUnitsIndex(int index, Integer v) {
        return this.getUnits().set(index, v);
    }
    /**
     * get skills
     *
     * @return skills value
     */
    public Int32ListProp getSkills() {
        if (this.skills == null) {
            this.skills = new Int32ListProp(this, FIELD_INDEX_SKILLS);
        }
        return this.skills;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addSkills(Integer v) {
        this.getSkills().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getSkillsIndex(int index) {
        return this.getSkills().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeSkills(Integer v) {
        if (this.skills == null) {
            return null;
        }
        if(this.skills.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getSkillsSize() {
        if (this.skills == null) {
            return 0;
        }
        return this.skills.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isSkillsEmpty() {
        if (this.skills == null) {
            return true;
        }
        return this.getSkills().isEmpty();
    }

    /**
     * clear list
     */
    public void clearSkills() {
        this.getSkills().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeSkillsIndex(int index) {
        return this.getSkills().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setSkillsIndex(int index, Integer v) {
        return this.getSkills().set(index, v);
    }
    /**
     * get areas
     *
     * @return areas value
     */
    public Int32BaseAreaMapProp getAreas() {
        if (this.areas == null) {
            this.areas = new Int32BaseAreaMapProp(this, FIELD_INDEX_AREAS);
        }
        return this.areas;
    }


    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putAreasV(BaseAreaProp v) {
        this.getAreas().put(v.getAreaId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public BaseAreaProp addEmptyAreas(Integer k) {
        return this.getAreas().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getAreasSize() {
        if (this.areas == null) {
            return 0;
        }
        return this.areas.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isAreasEmpty() {
        if (this.areas == null) {
            return true;
        }
        return this.areas.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public BaseAreaProp getAreasV(Integer k) {
        if (this.areas == null || !this.areas.containsKey(k)) {
            return null;
        }
        return this.areas.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearAreas() {
        if (this.areas != null) {
            this.areas.clear();
        }
    }

    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeAreasV(Integer k) {
        if (this.areas != null) {
            this.areas.remove(k);
        }
    }
    /**
     * get defend
     *
     * @return defend value
     */
    public PlayerDefendInfoProp getDefend() {
        if (this.defend == null) {
            this.defend = new PlayerDefendInfoProp(this, FIELD_INDEX_DEFEND);
        }
        return this.defend;
    }

    /**
     * get baseExpandId
     *
     * @return baseExpandId value
     */
    public int getBaseExpandId() {
        return this.baseExpandId;
    }

    /**
     * set baseExpandId && set marked
     *
     * @param baseExpandId new value
     * @return current object
     */
    public PlayerInnerBuildRHModelProp setBaseExpandId(int baseExpandId) {
        if (this.baseExpandId != baseExpandId) {
            this.mark(FIELD_INDEX_BASEEXPANDID);
            this.baseExpandId = baseExpandId;
        }
        return this;
    }

    /**
     * inner set baseExpandId
     *
     * @param baseExpandId new value
     */
    private void innerSetBaseExpandId(int baseExpandId) {
        this.baseExpandId = baseExpandId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerInnerBuildRHModelPB.Builder getCopyCsBuilder() {
        final PlayerInnerBuildRHModelPB.Builder builder = PlayerInnerBuildRHModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerInnerBuildRHModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.innerBuildRH != null) {
            StructPlayerPB.Int32InnerBuildRHMapPB.Builder tmpBuilder = StructPlayerPB.Int32InnerBuildRHMapPB.newBuilder();
            final int tmpFieldCnt = this.innerBuildRH.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInnerBuildRH(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInnerBuildRH();
            }
        }  else if (builder.hasInnerBuildRH()) {
            // 清理InnerBuildRH
            builder.clearInnerBuildRH();
            fieldCnt++;
        }
        if (this.getViewId() != 0) {
            builder.setViewId(this.getViewId());
            fieldCnt++;
        }  else if (builder.hasViewId()) {
            // 清理ViewId
            builder.clearViewId();
            fieldCnt++;
        }
        if (this.getBaseId() != 0) {
            builder.setBaseId(this.getBaseId());
            fieldCnt++;
        }  else if (builder.hasBaseId()) {
            // 清理BaseId
            builder.clearBaseId();
            fieldCnt++;
        }
        if (this.getConstructionPoint() != 0) {
            builder.setConstructionPoint(this.getConstructionPoint());
            fieldCnt++;
        }  else if (builder.hasConstructionPoint()) {
            // 清理ConstructionPoint
            builder.clearConstructionPoint();
            fieldCnt++;
        }
        if (this.upgrades != null) {
            PlayerPB.Int32PlayerInnerBuildUpgradeMapPB.Builder tmpBuilder = PlayerPB.Int32PlayerInnerBuildUpgradeMapPB.newBuilder();
            final int tmpFieldCnt = this.upgrades.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUpgrades(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUpgrades();
            }
        }  else if (builder.hasUpgrades()) {
            // 清理Upgrades
            builder.clearUpgrades();
            fieldCnt++;
        }
        if (this.units != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.units.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnits(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnits();
            }
        }  else if (builder.hasUnits()) {
            // 清理Units
            builder.clearUnits();
            fieldCnt++;
        }
        if (this.skills != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.skills.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkills(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkills();
            }
        }  else if (builder.hasSkills()) {
            // 清理Skills
            builder.clearSkills();
            fieldCnt++;
        }
        if (this.areas != null) {
            StructPlayerPB.Int32BaseAreaMapPB.Builder tmpBuilder = StructPlayerPB.Int32BaseAreaMapPB.newBuilder();
            final int tmpFieldCnt = this.areas.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAreas(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAreas();
            }
        } else if (builder.hasAreas()) {
            // 清理Areas
            builder.clearAreas();
            fieldCnt++;
        }
        if (this.defend != null) {
            PlayerPB.PlayerDefendInfoPB.Builder tmpBuilder = PlayerPB.PlayerDefendInfoPB.newBuilder();
            final int tmpFieldCnt = this.defend.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDefend(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDefend();
            }
        }  else if (builder.hasDefend()) {
            // 清理Defend
            builder.clearDefend();
            fieldCnt++;
        }
        if (this.getBaseExpandId() != 0) {
            builder.setBaseExpandId(this.getBaseExpandId());
            fieldCnt++;
        }  else if (builder.hasBaseExpandId()) {
            // 清理BaseExpandId
            builder.clearBaseExpandId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerInnerBuildRHModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INNERBUILDRH) && this.innerBuildRH != null) {
            final boolean needClear = !builder.hasInnerBuildRH();
            final int tmpFieldCnt = this.innerBuildRH.copyChangeToCs(builder.getInnerBuildRHBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerBuildRH();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIEWID)) {
            builder.setViewId(this.getViewId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASEID)) {
            builder.setBaseId(this.getBaseId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONSTRUCTIONPOINT)) {
            builder.setConstructionPoint(this.getConstructionPoint());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UPGRADES) && this.upgrades != null) {
            final boolean needClear = !builder.hasUpgrades();
            final int tmpFieldCnt = this.upgrades.copyChangeToCs(builder.getUpgradesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUpgrades();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNITS) && this.units != null) {
            final boolean needClear = !builder.hasUnits();
            final int tmpFieldCnt = this.units.copyChangeToCs(builder.getUnitsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnits();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKILLS) && this.skills != null) {
            final boolean needClear = !builder.hasSkills();
            final int tmpFieldCnt = this.skills.copyChangeToCs(builder.getSkillsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkills();
            }
        }
       if (this.hasMark(FIELD_INDEX_AREAS) && this.areas != null) {
           final boolean needClear = !builder.hasAreas();
           final int tmpFieldCnt = this.areas.copyChangeToCs(builder.getAreasBuilder());
           if (tmpFieldCnt > 0) {
               fieldCnt++;
           } else if (needClear) {
               builder.clearAreas();
           }
        }
        if (this.hasMark(FIELD_INDEX_DEFEND) && this.defend != null) {
            final boolean needClear = !builder.hasDefend();
            final int tmpFieldCnt = this.defend.copyChangeToCs(builder.getDefendBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDefend();
            }
        }
        if (this.hasMark(FIELD_INDEX_BASEEXPANDID)) {
            builder.setBaseExpandId(this.getBaseExpandId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerInnerBuildRHModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INNERBUILDRH) && this.innerBuildRH != null) {
            final boolean needClear = !builder.hasInnerBuildRH();
            final int tmpFieldCnt = this.innerBuildRH.copyChangeToAndClearDeleteKeysCs(builder.getInnerBuildRHBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerBuildRH();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIEWID)) {
            builder.setViewId(this.getViewId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASEID)) {
            builder.setBaseId(this.getBaseId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONSTRUCTIONPOINT)) {
            builder.setConstructionPoint(this.getConstructionPoint());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UPGRADES) && this.upgrades != null) {
            final boolean needClear = !builder.hasUpgrades();
            final int tmpFieldCnt = this.upgrades.copyChangeToAndClearDeleteKeysCs(builder.getUpgradesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUpgrades();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNITS) && this.units != null) {
            final boolean needClear = !builder.hasUnits();
            final int tmpFieldCnt = this.units.copyChangeToCs(builder.getUnitsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnits();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKILLS) && this.skills != null) {
            final boolean needClear = !builder.hasSkills();
            final int tmpFieldCnt = this.skills.copyChangeToCs(builder.getSkillsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkills();
            }
        }
       if (this.hasMark(FIELD_INDEX_AREAS) && this.areas != null) {
           final boolean needClear = !builder.hasAreas();
           final int tmpFieldCnt = this.areas.copyChangeToAndClearDeleteKeysCs(builder.getAreasBuilder());
           if (tmpFieldCnt > 0) {
               fieldCnt++;
           } else if (needClear) {
               builder.clearAreas();
           }
        }
        if (this.hasMark(FIELD_INDEX_DEFEND) && this.defend != null) {
            final boolean needClear = !builder.hasDefend();
            final int tmpFieldCnt = this.defend.copyChangeToAndClearDeleteKeysCs(builder.getDefendBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDefend();
            }
        }
        if (this.hasMark(FIELD_INDEX_BASEEXPANDID)) {
            builder.setBaseExpandId(this.getBaseExpandId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerInnerBuildRHModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasInnerBuildRH()) {
            this.getInnerBuildRH().mergeFromCs(proto.getInnerBuildRH());
        } else {
            if (this.innerBuildRH != null) {
                this.innerBuildRH.mergeFromCs(proto.getInnerBuildRH());
            }
        }
        if (proto.hasViewId()) {
            this.innerSetViewId(proto.getViewId());
        } else {
            this.innerSetViewId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBaseId()) {
            this.innerSetBaseId(proto.getBaseId());
        } else {
            this.innerSetBaseId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasConstructionPoint()) {
            this.innerSetConstructionPoint(proto.getConstructionPoint());
        } else {
            this.innerSetConstructionPoint(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUpgrades()) {
            this.getUpgrades().mergeFromCs(proto.getUpgrades());
        } else {
            if (this.upgrades != null) {
                this.upgrades.mergeFromCs(proto.getUpgrades());
            }
        }
        if (proto.hasUnits()) {
            this.getUnits().mergeFromCs(proto.getUnits());
        } else {
            if (this.units != null) {
                this.units.mergeFromCs(proto.getUnits());
            }
        }
        if (proto.hasSkills()) {
            this.getSkills().mergeFromCs(proto.getSkills());
        } else {
            if (this.skills != null) {
                this.skills.mergeFromCs(proto.getSkills());
            }
        }
        if (proto.hasAreas()) {
            this.getAreas().mergeFromCs(proto.getAreas());
        } else {
            if (this.areas != null) {
                this.areas.mergeFromCs(proto.getAreas());
            }
        }
        if (proto.hasDefend()) {
            this.getDefend().mergeFromCs(proto.getDefend());
        } else {
            if (this.defend != null) {
                this.defend.mergeFromCs(proto.getDefend());
            }
        }
        if (proto.hasBaseExpandId()) {
            this.innerSetBaseExpandId(proto.getBaseExpandId());
        } else {
            this.innerSetBaseExpandId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerInnerBuildRHModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerInnerBuildRHModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasInnerBuildRH()) {
            this.getInnerBuildRH().mergeChangeFromCs(proto.getInnerBuildRH());
            fieldCnt++;
        }
        if (proto.hasViewId()) {
            this.setViewId(proto.getViewId());
            fieldCnt++;
        }
        if (proto.hasBaseId()) {
            this.setBaseId(proto.getBaseId());
            fieldCnt++;
        }
        if (proto.hasConstructionPoint()) {
            this.setConstructionPoint(proto.getConstructionPoint());
            fieldCnt++;
        }
        if (proto.hasUpgrades()) {
            this.getUpgrades().mergeChangeFromCs(proto.getUpgrades());
            fieldCnt++;
        }
        if (proto.hasUnits()) {
            this.getUnits().mergeChangeFromCs(proto.getUnits());
            fieldCnt++;
        }
        if (proto.hasSkills()) {
            this.getSkills().mergeChangeFromCs(proto.getSkills());
            fieldCnt++;
        }
        if (proto.hasAreas()) {
            this.getAreas().mergeChangeFromCs(proto.getAreas());
            fieldCnt++;
        }
        if (proto.hasDefend()) {
            this.getDefend().mergeChangeFromCs(proto.getDefend());
            fieldCnt++;
        }
        if (proto.hasBaseExpandId()) {
            this.setBaseExpandId(proto.getBaseExpandId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerInnerBuildRHModel.Builder getCopyDbBuilder() {
        final PlayerInnerBuildRHModel.Builder builder = PlayerInnerBuildRHModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerInnerBuildRHModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.innerBuildRH != null) {
            StructPlayer.Int32InnerBuildRHMap.Builder tmpBuilder = StructPlayer.Int32InnerBuildRHMap.newBuilder();
            final int tmpFieldCnt = this.innerBuildRH.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInnerBuildRH(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInnerBuildRH();
            }
        }  else if (builder.hasInnerBuildRH()) {
            // 清理InnerBuildRH
            builder.clearInnerBuildRH();
            fieldCnt++;
        }
        if (this.getViewId() != 0) {
            builder.setViewId(this.getViewId());
            fieldCnt++;
        }  else if (builder.hasViewId()) {
            // 清理ViewId
            builder.clearViewId();
            fieldCnt++;
        }
        if (this.getBaseId() != 0) {
            builder.setBaseId(this.getBaseId());
            fieldCnt++;
        }  else if (builder.hasBaseId()) {
            // 清理BaseId
            builder.clearBaseId();
            fieldCnt++;
        }
        if (this.getConstructionPoint() != 0) {
            builder.setConstructionPoint(this.getConstructionPoint());
            fieldCnt++;
        }  else if (builder.hasConstructionPoint()) {
            // 清理ConstructionPoint
            builder.clearConstructionPoint();
            fieldCnt++;
        }
        if (this.upgrades != null) {
            Player.Int32PlayerInnerBuildUpgradeMap.Builder tmpBuilder = Player.Int32PlayerInnerBuildUpgradeMap.newBuilder();
            final int tmpFieldCnt = this.upgrades.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUpgrades(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUpgrades();
            }
        }  else if (builder.hasUpgrades()) {
            // 清理Upgrades
            builder.clearUpgrades();
            fieldCnt++;
        }
        if (this.units != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.units.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnits(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnits();
            }
        }  else if (builder.hasUnits()) {
            // 清理Units
            builder.clearUnits();
            fieldCnt++;
        }
        if (this.skills != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.skills.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkills(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkills();
            }
        }  else if (builder.hasSkills()) {
            // 清理Skills
            builder.clearSkills();
            fieldCnt++;
        }
        if (this.areas != null) {
            StructPlayer.Int32BaseAreaMap.Builder tmpBuilder = StructPlayer.Int32BaseAreaMap.newBuilder();
            final int tmpFieldCnt = this.areas.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAreas(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAreas();
            }
        } else if (builder.hasAreas()) {
            // 清理Areas
            builder.clearAreas();
            fieldCnt++;
        }
        if (this.defend != null) {
            Player.PlayerDefendInfo.Builder tmpBuilder = Player.PlayerDefendInfo.newBuilder();
            final int tmpFieldCnt = this.defend.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDefend(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDefend();
            }
        }  else if (builder.hasDefend()) {
            // 清理Defend
            builder.clearDefend();
            fieldCnt++;
        }
        if (this.getBaseExpandId() != 0) {
            builder.setBaseExpandId(this.getBaseExpandId());
            fieldCnt++;
        }  else if (builder.hasBaseExpandId()) {
            // 清理BaseExpandId
            builder.clearBaseExpandId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerInnerBuildRHModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INNERBUILDRH) && this.innerBuildRH != null) {
            final boolean needClear = !builder.hasInnerBuildRH();
            final int tmpFieldCnt = this.innerBuildRH.copyChangeToDb(builder.getInnerBuildRHBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerBuildRH();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIEWID)) {
            builder.setViewId(this.getViewId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASEID)) {
            builder.setBaseId(this.getBaseId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONSTRUCTIONPOINT)) {
            builder.setConstructionPoint(this.getConstructionPoint());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UPGRADES) && this.upgrades != null) {
            final boolean needClear = !builder.hasUpgrades();
            final int tmpFieldCnt = this.upgrades.copyChangeToDb(builder.getUpgradesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUpgrades();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNITS) && this.units != null) {
            final boolean needClear = !builder.hasUnits();
            final int tmpFieldCnt = this.units.copyChangeToDb(builder.getUnitsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnits();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKILLS) && this.skills != null) {
            final boolean needClear = !builder.hasSkills();
            final int tmpFieldCnt = this.skills.copyChangeToDb(builder.getSkillsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkills();
            }
        }
       if (this.hasMark(FIELD_INDEX_AREAS) && this.areas != null) {
           final boolean needClear = !builder.hasAreas();
           final int tmpFieldCnt = this.areas.copyChangeToDb(builder.getAreasBuilder());
           if (tmpFieldCnt > 0) {
               fieldCnt++;
           } else if (needClear) {
               builder.clearAreas();
           }
        }
        if (this.hasMark(FIELD_INDEX_DEFEND) && this.defend != null) {
            final boolean needClear = !builder.hasDefend();
            final int tmpFieldCnt = this.defend.copyChangeToDb(builder.getDefendBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDefend();
            }
        }
        if (this.hasMark(FIELD_INDEX_BASEEXPANDID)) {
            builder.setBaseExpandId(this.getBaseExpandId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerInnerBuildRHModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasInnerBuildRH()) {
            this.getInnerBuildRH().mergeFromDb(proto.getInnerBuildRH());
        } else {
            if (this.innerBuildRH != null) {
                this.innerBuildRH.mergeFromDb(proto.getInnerBuildRH());
            }
        }
        if (proto.hasViewId()) {
            this.innerSetViewId(proto.getViewId());
        } else {
            this.innerSetViewId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBaseId()) {
            this.innerSetBaseId(proto.getBaseId());
        } else {
            this.innerSetBaseId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasConstructionPoint()) {
            this.innerSetConstructionPoint(proto.getConstructionPoint());
        } else {
            this.innerSetConstructionPoint(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUpgrades()) {
            this.getUpgrades().mergeFromDb(proto.getUpgrades());
        } else {
            if (this.upgrades != null) {
                this.upgrades.mergeFromDb(proto.getUpgrades());
            }
        }
        if (proto.hasUnits()) {
            this.getUnits().mergeFromDb(proto.getUnits());
        } else {
            if (this.units != null) {
                this.units.mergeFromDb(proto.getUnits());
            }
        }
        if (proto.hasSkills()) {
            this.getSkills().mergeFromDb(proto.getSkills());
        } else {
            if (this.skills != null) {
                this.skills.mergeFromDb(proto.getSkills());
            }
        }
        if (proto.hasAreas()) {
            this.getAreas().mergeFromDb(proto.getAreas());
        } else {
            if (this.areas != null) {
                this.areas.mergeFromDb(proto.getAreas());
            }
        }
        if (proto.hasDefend()) {
            this.getDefend().mergeFromDb(proto.getDefend());
        } else {
            if (this.defend != null) {
                this.defend.mergeFromDb(proto.getDefend());
            }
        }
        if (proto.hasBaseExpandId()) {
            this.innerSetBaseExpandId(proto.getBaseExpandId());
        } else {
            this.innerSetBaseExpandId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerInnerBuildRHModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerInnerBuildRHModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasInnerBuildRH()) {
            this.getInnerBuildRH().mergeChangeFromDb(proto.getInnerBuildRH());
            fieldCnt++;
        }
        if (proto.hasViewId()) {
            this.setViewId(proto.getViewId());
            fieldCnt++;
        }
        if (proto.hasBaseId()) {
            this.setBaseId(proto.getBaseId());
            fieldCnt++;
        }
        if (proto.hasConstructionPoint()) {
            this.setConstructionPoint(proto.getConstructionPoint());
            fieldCnt++;
        }
        if (proto.hasUpgrades()) {
            this.getUpgrades().mergeChangeFromDb(proto.getUpgrades());
            fieldCnt++;
        }
        if (proto.hasUnits()) {
            this.getUnits().mergeChangeFromDb(proto.getUnits());
            fieldCnt++;
        }
        if (proto.hasSkills()) {
            this.getSkills().mergeChangeFromDb(proto.getSkills());
            fieldCnt++;
        }
        if (proto.hasAreas()) {
            this.getAreas().mergeChangeFromDb(proto.getAreas());
            fieldCnt++;
        }
        if (proto.hasDefend()) {
            this.getDefend().mergeChangeFromDb(proto.getDefend());
            fieldCnt++;
        }
        if (proto.hasBaseExpandId()) {
            this.setBaseExpandId(proto.getBaseExpandId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerInnerBuildRHModel.Builder getCopySsBuilder() {
        final PlayerInnerBuildRHModel.Builder builder = PlayerInnerBuildRHModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerInnerBuildRHModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.innerBuildRH != null) {
            StructPlayer.Int32InnerBuildRHMap.Builder tmpBuilder = StructPlayer.Int32InnerBuildRHMap.newBuilder();
            final int tmpFieldCnt = this.innerBuildRH.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInnerBuildRH(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInnerBuildRH();
            }
        }  else if (builder.hasInnerBuildRH()) {
            // 清理InnerBuildRH
            builder.clearInnerBuildRH();
            fieldCnt++;
        }
        if (this.getViewId() != 0) {
            builder.setViewId(this.getViewId());
            fieldCnt++;
        }  else if (builder.hasViewId()) {
            // 清理ViewId
            builder.clearViewId();
            fieldCnt++;
        }
        if (this.getBaseId() != 0) {
            builder.setBaseId(this.getBaseId());
            fieldCnt++;
        }  else if (builder.hasBaseId()) {
            // 清理BaseId
            builder.clearBaseId();
            fieldCnt++;
        }
        if (this.getConstructionPoint() != 0) {
            builder.setConstructionPoint(this.getConstructionPoint());
            fieldCnt++;
        }  else if (builder.hasConstructionPoint()) {
            // 清理ConstructionPoint
            builder.clearConstructionPoint();
            fieldCnt++;
        }
        if (this.upgrades != null) {
            Player.Int32PlayerInnerBuildUpgradeMap.Builder tmpBuilder = Player.Int32PlayerInnerBuildUpgradeMap.newBuilder();
            final int tmpFieldCnt = this.upgrades.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUpgrades(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUpgrades();
            }
        }  else if (builder.hasUpgrades()) {
            // 清理Upgrades
            builder.clearUpgrades();
            fieldCnt++;
        }
        if (this.units != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.units.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnits(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnits();
            }
        }  else if (builder.hasUnits()) {
            // 清理Units
            builder.clearUnits();
            fieldCnt++;
        }
        if (this.skills != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.skills.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkills(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkills();
            }
        }  else if (builder.hasSkills()) {
            // 清理Skills
            builder.clearSkills();
            fieldCnt++;
        }
        if (this.areas != null) {
            StructPlayer.Int32BaseAreaMap.Builder tmpBuilder = StructPlayer.Int32BaseAreaMap.newBuilder();
            final int tmpFieldCnt = this.areas.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAreas(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAreas();
            }
        } else if (builder.hasAreas()) {
            // 清理Areas
            builder.clearAreas();
            fieldCnt++;
        }
        if (this.defend != null) {
            Player.PlayerDefendInfo.Builder tmpBuilder = Player.PlayerDefendInfo.newBuilder();
            final int tmpFieldCnt = this.defend.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDefend(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDefend();
            }
        }  else if (builder.hasDefend()) {
            // 清理Defend
            builder.clearDefend();
            fieldCnt++;
        }
        if (this.getBaseExpandId() != 0) {
            builder.setBaseExpandId(this.getBaseExpandId());
            fieldCnt++;
        }  else if (builder.hasBaseExpandId()) {
            // 清理BaseExpandId
            builder.clearBaseExpandId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerInnerBuildRHModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INNERBUILDRH) && this.innerBuildRH != null) {
            final boolean needClear = !builder.hasInnerBuildRH();
            final int tmpFieldCnt = this.innerBuildRH.copyChangeToSs(builder.getInnerBuildRHBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerBuildRH();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIEWID)) {
            builder.setViewId(this.getViewId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASEID)) {
            builder.setBaseId(this.getBaseId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONSTRUCTIONPOINT)) {
            builder.setConstructionPoint(this.getConstructionPoint());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UPGRADES) && this.upgrades != null) {
            final boolean needClear = !builder.hasUpgrades();
            final int tmpFieldCnt = this.upgrades.copyChangeToSs(builder.getUpgradesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUpgrades();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNITS) && this.units != null) {
            final boolean needClear = !builder.hasUnits();
            final int tmpFieldCnt = this.units.copyChangeToSs(builder.getUnitsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnits();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKILLS) && this.skills != null) {
            final boolean needClear = !builder.hasSkills();
            final int tmpFieldCnt = this.skills.copyChangeToSs(builder.getSkillsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkills();
            }
        }
       if (this.hasMark(FIELD_INDEX_AREAS) && this.areas != null) {
           final boolean needClear = !builder.hasAreas();
           final int tmpFieldCnt = this.areas.copyChangeToSs(builder.getAreasBuilder());
           if (tmpFieldCnt > 0) {
               fieldCnt++;
           } else if (needClear) {
               builder.clearAreas();
           }
        }
        if (this.hasMark(FIELD_INDEX_DEFEND) && this.defend != null) {
            final boolean needClear = !builder.hasDefend();
            final int tmpFieldCnt = this.defend.copyChangeToSs(builder.getDefendBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDefend();
            }
        }
        if (this.hasMark(FIELD_INDEX_BASEEXPANDID)) {
            builder.setBaseExpandId(this.getBaseExpandId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerInnerBuildRHModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasInnerBuildRH()) {
            this.getInnerBuildRH().mergeFromSs(proto.getInnerBuildRH());
        } else {
            if (this.innerBuildRH != null) {
                this.innerBuildRH.mergeFromSs(proto.getInnerBuildRH());
            }
        }
        if (proto.hasViewId()) {
            this.innerSetViewId(proto.getViewId());
        } else {
            this.innerSetViewId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBaseId()) {
            this.innerSetBaseId(proto.getBaseId());
        } else {
            this.innerSetBaseId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasConstructionPoint()) {
            this.innerSetConstructionPoint(proto.getConstructionPoint());
        } else {
            this.innerSetConstructionPoint(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUpgrades()) {
            this.getUpgrades().mergeFromSs(proto.getUpgrades());
        } else {
            if (this.upgrades != null) {
                this.upgrades.mergeFromSs(proto.getUpgrades());
            }
        }
        if (proto.hasUnits()) {
            this.getUnits().mergeFromSs(proto.getUnits());
        } else {
            if (this.units != null) {
                this.units.mergeFromSs(proto.getUnits());
            }
        }
        if (proto.hasSkills()) {
            this.getSkills().mergeFromSs(proto.getSkills());
        } else {
            if (this.skills != null) {
                this.skills.mergeFromSs(proto.getSkills());
            }
        }
        if (proto.hasAreas()) {
            this.getAreas().mergeFromSs(proto.getAreas());
        } else {
            if (this.areas != null) {
                this.areas.mergeFromSs(proto.getAreas());
            }
        }
        if (proto.hasDefend()) {
            this.getDefend().mergeFromSs(proto.getDefend());
        } else {
            if (this.defend != null) {
                this.defend.mergeFromSs(proto.getDefend());
            }
        }
        if (proto.hasBaseExpandId()) {
            this.innerSetBaseExpandId(proto.getBaseExpandId());
        } else {
            this.innerSetBaseExpandId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerInnerBuildRHModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerInnerBuildRHModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasInnerBuildRH()) {
            this.getInnerBuildRH().mergeChangeFromSs(proto.getInnerBuildRH());
            fieldCnt++;
        }
        if (proto.hasViewId()) {
            this.setViewId(proto.getViewId());
            fieldCnt++;
        }
        if (proto.hasBaseId()) {
            this.setBaseId(proto.getBaseId());
            fieldCnt++;
        }
        if (proto.hasConstructionPoint()) {
            this.setConstructionPoint(proto.getConstructionPoint());
            fieldCnt++;
        }
        if (proto.hasUpgrades()) {
            this.getUpgrades().mergeChangeFromSs(proto.getUpgrades());
            fieldCnt++;
        }
        if (proto.hasUnits()) {
            this.getUnits().mergeChangeFromSs(proto.getUnits());
            fieldCnt++;
        }
        if (proto.hasSkills()) {
            this.getSkills().mergeChangeFromSs(proto.getSkills());
            fieldCnt++;
        }
        if (proto.hasAreas()) {
            this.getAreas().mergeChangeFromSs(proto.getAreas());
            fieldCnt++;
        }
        if (proto.hasDefend()) {
            this.getDefend().mergeChangeFromSs(proto.getDefend());
            fieldCnt++;
        }
        if (proto.hasBaseExpandId()) {
            this.setBaseExpandId(proto.getBaseExpandId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerInnerBuildRHModel.Builder builder = PlayerInnerBuildRHModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_INNERBUILDRH) && this.innerBuildRH != null) {
            this.innerBuildRH.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_UPGRADES) && this.upgrades != null) {
            this.upgrades.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_UNITS) && this.units != null) {
            this.units.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SKILLS) && this.skills != null) {
            this.skills.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_AREAS) && this.areas != null) {
            this.areas.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DEFEND) && this.defend != null) {
            this.defend.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.innerBuildRH != null) {
            this.innerBuildRH.markAll();
        }
        if (this.upgrades != null) {
            this.upgrades.markAll();
        }
        if (this.units != null) {
            this.units.markAll();
        }
        if (this.skills != null) {
            this.skills.markAll();
        }
        if (this.areas != null) {
            this.areas.markAll();
        }
        if (this.defend != null) {
            this.defend.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerInnerBuildRHModelProp)) {
            return false;
        }
        final PlayerInnerBuildRHModelProp otherNode = (PlayerInnerBuildRHModelProp) node;
        if (!this.getInnerBuildRH().compareDataTo(otherNode.getInnerBuildRH())) {
            return false;
        }
        if (this.viewId != otherNode.viewId) {
            return false;
        }
        if (this.baseId != otherNode.baseId) {
            return false;
        }
        if (this.constructionPoint != otherNode.constructionPoint) {
            return false;
        }
        if (!this.getUpgrades().compareDataTo(otherNode.getUpgrades())) {
            return false;
        }
        if (!this.getUnits().compareDataTo(otherNode.getUnits())) {
            return false;
        }
        if (!this.getSkills().compareDataTo(otherNode.getSkills())) {
            return false;
        }
        if (!this.getAreas().compareDataTo(otherNode.getAreas())) {
            return false;
        }
        if (!this.getDefend().compareDataTo(otherNode.getDefend())) {
            return false;
        }
        if (this.baseExpandId != otherNode.baseExpandId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 54;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
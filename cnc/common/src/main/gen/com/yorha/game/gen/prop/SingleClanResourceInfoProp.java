package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.SingleClanResourceInfo;
import com.yorha.proto.StructPB.SingleClanResourceInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class SingleClanResourceInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_TYPE = 0;
    public static final int FIELD_INDEX_COUNT = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int type = Constant.DEFAULT_INT_VALUE;
    private long count = Constant.DEFAULT_LONG_VALUE;

    public SingleClanResourceInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SingleClanResourceInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get type
     *
     * @return type value
     */
    public int getType() {
        return this.type;
    }

    /**
     * set type && set marked
     *
     * @param type new value
     * @return current object
     */
    public SingleClanResourceInfoProp setType(int type) {
        if (this.type != type) {
            this.mark(FIELD_INDEX_TYPE);
            this.type = type;
        }
        return this;
    }

    /**
     * inner set type
     *
     * @param type new value
     */
    private void innerSetType(int type) {
        this.type = type;
    }

    /**
     * get count
     *
     * @return count value
     */
    public long getCount() {
        return this.count;
    }

    /**
     * set count && set marked
     *
     * @param count new value
     * @return current object
     */
    public SingleClanResourceInfoProp setCount(long count) {
        if (this.count != count) {
            this.mark(FIELD_INDEX_COUNT);
            this.count = count;
        }
        return this;
    }

    /**
     * inner set count
     *
     * @param count new value
     */
    private void innerSetCount(long count) {
        this.count = count;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SingleClanResourceInfoPB.Builder getCopyCsBuilder() {
        final SingleClanResourceInfoPB.Builder builder = SingleClanResourceInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SingleClanResourceInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != 0) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getCount() != 0L) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }  else if (builder.hasCount()) {
            // 清理Count
            builder.clearCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SingleClanResourceInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SingleClanResourceInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SingleClanResourceInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCount()) {
            this.innerSetCount(proto.getCount());
        } else {
            this.innerSetCount(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return SingleClanResourceInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SingleClanResourceInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasCount()) {
            this.setCount(proto.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SingleClanResourceInfo.Builder getCopyDbBuilder() {
        final SingleClanResourceInfo.Builder builder = SingleClanResourceInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SingleClanResourceInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != 0) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getCount() != 0L) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }  else if (builder.hasCount()) {
            // 清理Count
            builder.clearCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SingleClanResourceInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SingleClanResourceInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCount()) {
            this.innerSetCount(proto.getCount());
        } else {
            this.innerSetCount(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return SingleClanResourceInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SingleClanResourceInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasCount()) {
            this.setCount(proto.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SingleClanResourceInfo.Builder getCopySsBuilder() {
        final SingleClanResourceInfo.Builder builder = SingleClanResourceInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SingleClanResourceInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != 0) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getCount() != 0L) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }  else if (builder.hasCount()) {
            // 清理Count
            builder.clearCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SingleClanResourceInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SingleClanResourceInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCount()) {
            this.innerSetCount(proto.getCount());
        } else {
            this.innerSetCount(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return SingleClanResourceInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SingleClanResourceInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasCount()) {
            this.setCount(proto.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SingleClanResourceInfo.Builder builder = SingleClanResourceInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.type;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SingleClanResourceInfoProp)) {
            return false;
        }
        final SingleClanResourceInfoProp otherNode = (SingleClanResourceInfoProp) node;
        if (this.type != otherNode.type) {
            return false;
        }
        if (this.count != otherNode.count) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerClanHelpModel;
import com.yorha.proto.PlayerPB.PlayerClanHelpModelPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerClanHelpModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_REFRESHTIME = 0;
    public static final int FIELD_INDEX_TODAYCUREHELP = 1;
    public static final int FIELD_INDEX_CANNOTCREATECUREHELP = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private long refreshTime = Constant.DEFAULT_LONG_VALUE;
    private int todayCureHelp = Constant.DEFAULT_INT_VALUE;
    private boolean cannotCreateCureHelp = Constant.DEFAULT_BOOLEAN_VALUE;

    public PlayerClanHelpModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerClanHelpModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get refreshTime
     *
     * @return refreshTime value
     */
    public long getRefreshTime() {
        return this.refreshTime;
    }

    /**
     * set refreshTime && set marked
     *
     * @param refreshTime new value
     * @return current object
     */
    public PlayerClanHelpModelProp setRefreshTime(long refreshTime) {
        if (this.refreshTime != refreshTime) {
            this.mark(FIELD_INDEX_REFRESHTIME);
            this.refreshTime = refreshTime;
        }
        return this;
    }

    /**
     * inner set refreshTime
     *
     * @param refreshTime new value
     */
    private void innerSetRefreshTime(long refreshTime) {
        this.refreshTime = refreshTime;
    }

    /**
     * get todayCureHelp
     *
     * @return todayCureHelp value
     */
    public int getTodayCureHelp() {
        return this.todayCureHelp;
    }

    /**
     * set todayCureHelp && set marked
     *
     * @param todayCureHelp new value
     * @return current object
     */
    public PlayerClanHelpModelProp setTodayCureHelp(int todayCureHelp) {
        if (this.todayCureHelp != todayCureHelp) {
            this.mark(FIELD_INDEX_TODAYCUREHELP);
            this.todayCureHelp = todayCureHelp;
        }
        return this;
    }

    /**
     * inner set todayCureHelp
     *
     * @param todayCureHelp new value
     */
    private void innerSetTodayCureHelp(int todayCureHelp) {
        this.todayCureHelp = todayCureHelp;
    }

    /**
     * get cannotCreateCureHelp
     *
     * @return cannotCreateCureHelp value
     */
    public boolean getCannotCreateCureHelp() {
        return this.cannotCreateCureHelp;
    }

    /**
     * set cannotCreateCureHelp && set marked
     *
     * @param cannotCreateCureHelp new value
     * @return current object
     */
    public PlayerClanHelpModelProp setCannotCreateCureHelp(boolean cannotCreateCureHelp) {
        if (this.cannotCreateCureHelp != cannotCreateCureHelp) {
            this.mark(FIELD_INDEX_CANNOTCREATECUREHELP);
            this.cannotCreateCureHelp = cannotCreateCureHelp;
        }
        return this;
    }

    /**
     * inner set cannotCreateCureHelp
     *
     * @param cannotCreateCureHelp new value
     */
    private void innerSetCannotCreateCureHelp(boolean cannotCreateCureHelp) {
        this.cannotCreateCureHelp = cannotCreateCureHelp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerClanHelpModelPB.Builder getCopyCsBuilder() {
        final PlayerClanHelpModelPB.Builder builder = PlayerClanHelpModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerClanHelpModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCannotCreateCureHelp()) {
            builder.setCannotCreateCureHelp(this.getCannotCreateCureHelp());
            fieldCnt++;
        }  else if (builder.hasCannotCreateCureHelp()) {
            // 清理CannotCreateCureHelp
            builder.clearCannotCreateCureHelp();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerClanHelpModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CANNOTCREATECUREHELP)) {
            builder.setCannotCreateCureHelp(this.getCannotCreateCureHelp());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerClanHelpModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CANNOTCREATECUREHELP)) {
            builder.setCannotCreateCureHelp(this.getCannotCreateCureHelp());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerClanHelpModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCannotCreateCureHelp()) {
            this.innerSetCannotCreateCureHelp(proto.getCannotCreateCureHelp());
        } else {
            this.innerSetCannotCreateCureHelp(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return PlayerClanHelpModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerClanHelpModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCannotCreateCureHelp()) {
            this.setCannotCreateCureHelp(proto.getCannotCreateCureHelp());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerClanHelpModel.Builder getCopyDbBuilder() {
        final PlayerClanHelpModel.Builder builder = PlayerClanHelpModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerClanHelpModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRefreshTime() != 0L) {
            builder.setRefreshTime(this.getRefreshTime());
            fieldCnt++;
        }  else if (builder.hasRefreshTime()) {
            // 清理RefreshTime
            builder.clearRefreshTime();
            fieldCnt++;
        }
        if (this.getTodayCureHelp() != 0) {
            builder.setTodayCureHelp(this.getTodayCureHelp());
            fieldCnt++;
        }  else if (builder.hasTodayCureHelp()) {
            // 清理TodayCureHelp
            builder.clearTodayCureHelp();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerClanHelpModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REFRESHTIME)) {
            builder.setRefreshTime(this.getRefreshTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TODAYCUREHELP)) {
            builder.setTodayCureHelp(this.getTodayCureHelp());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerClanHelpModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRefreshTime()) {
            this.innerSetRefreshTime(proto.getRefreshTime());
        } else {
            this.innerSetRefreshTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTodayCureHelp()) {
            this.innerSetTodayCureHelp(proto.getTodayCureHelp());
        } else {
            this.innerSetTodayCureHelp(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerClanHelpModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerClanHelpModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRefreshTime()) {
            this.setRefreshTime(proto.getRefreshTime());
            fieldCnt++;
        }
        if (proto.hasTodayCureHelp()) {
            this.setTodayCureHelp(proto.getTodayCureHelp());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerClanHelpModel.Builder getCopySsBuilder() {
        final PlayerClanHelpModel.Builder builder = PlayerClanHelpModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerClanHelpModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRefreshTime() != 0L) {
            builder.setRefreshTime(this.getRefreshTime());
            fieldCnt++;
        }  else if (builder.hasRefreshTime()) {
            // 清理RefreshTime
            builder.clearRefreshTime();
            fieldCnt++;
        }
        if (this.getTodayCureHelp() != 0) {
            builder.setTodayCureHelp(this.getTodayCureHelp());
            fieldCnt++;
        }  else if (builder.hasTodayCureHelp()) {
            // 清理TodayCureHelp
            builder.clearTodayCureHelp();
            fieldCnt++;
        }
        if (this.getCannotCreateCureHelp()) {
            builder.setCannotCreateCureHelp(this.getCannotCreateCureHelp());
            fieldCnt++;
        }  else if (builder.hasCannotCreateCureHelp()) {
            // 清理CannotCreateCureHelp
            builder.clearCannotCreateCureHelp();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerClanHelpModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REFRESHTIME)) {
            builder.setRefreshTime(this.getRefreshTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TODAYCUREHELP)) {
            builder.setTodayCureHelp(this.getTodayCureHelp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANNOTCREATECUREHELP)) {
            builder.setCannotCreateCureHelp(this.getCannotCreateCureHelp());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerClanHelpModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRefreshTime()) {
            this.innerSetRefreshTime(proto.getRefreshTime());
        } else {
            this.innerSetRefreshTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTodayCureHelp()) {
            this.innerSetTodayCureHelp(proto.getTodayCureHelp());
        } else {
            this.innerSetTodayCureHelp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCannotCreateCureHelp()) {
            this.innerSetCannotCreateCureHelp(proto.getCannotCreateCureHelp());
        } else {
            this.innerSetCannotCreateCureHelp(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return PlayerClanHelpModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerClanHelpModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRefreshTime()) {
            this.setRefreshTime(proto.getRefreshTime());
            fieldCnt++;
        }
        if (proto.hasTodayCureHelp()) {
            this.setTodayCureHelp(proto.getTodayCureHelp());
            fieldCnt++;
        }
        if (proto.hasCannotCreateCureHelp()) {
            this.setCannotCreateCureHelp(proto.getCannotCreateCureHelp());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerClanHelpModel.Builder builder = PlayerClanHelpModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerClanHelpModelProp)) {
            return false;
        }
        final PlayerClanHelpModelProp otherNode = (PlayerClanHelpModelProp) node;
        if (this.refreshTime != otherNode.refreshTime) {
            return false;
        }
        if (this.todayCureHelp != otherNode.todayCureHelp) {
            return false;
        }
        if (this.cannotCreateCureHelp != otherNode.cannotCreateCureHelp) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
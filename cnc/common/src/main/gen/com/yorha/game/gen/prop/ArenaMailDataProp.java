package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ArenaMailData;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ArenaMailDataPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ArenaMailDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_SELFRANK = 0;
    public static final int FIELD_INDEX_PLAYERINFO = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int selfRank = Constant.DEFAULT_INT_VALUE;
    private ArenaMailPlayerInfoListProp playerInfo = null;

    public ArenaMailDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ArenaMailDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get selfRank
     *
     * @return selfRank value
     */
    public int getSelfRank() {
        return this.selfRank;
    }

    /**
     * set selfRank && set marked
     *
     * @param selfRank new value
     * @return current object
     */
    public ArenaMailDataProp setSelfRank(int selfRank) {
        if (this.selfRank != selfRank) {
            this.mark(FIELD_INDEX_SELFRANK);
            this.selfRank = selfRank;
        }
        return this;
    }

    /**
     * inner set selfRank
     *
     * @param selfRank new value
     */
    private void innerSetSelfRank(int selfRank) {
        this.selfRank = selfRank;
    }

    /**
     * get playerInfo
     *
     * @return playerInfo value
     */
    public ArenaMailPlayerInfoListProp getPlayerInfo() {
        if (this.playerInfo == null) {
            this.playerInfo = new ArenaMailPlayerInfoListProp(this, FIELD_INDEX_PLAYERINFO);
        }
        return this.playerInfo;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addPlayerInfo(ArenaMailPlayerInfoProp v) {
        this.getPlayerInfo().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ArenaMailPlayerInfoProp getPlayerInfoIndex(int index) {
        return this.getPlayerInfo().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public ArenaMailPlayerInfoProp removePlayerInfo(ArenaMailPlayerInfoProp v) {
        if (this.playerInfo == null) {
            return null;
        }
        if(this.playerInfo.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getPlayerInfoSize() {
        if (this.playerInfo == null) {
            return 0;
        }
        return this.playerInfo.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isPlayerInfoEmpty() {
        if (this.playerInfo == null) {
            return true;
        }
        return this.getPlayerInfo().isEmpty();
    }

    /**
     * clear list
     */
    public void clearPlayerInfo() {
        this.getPlayerInfo().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ArenaMailPlayerInfoProp removePlayerInfoIndex(int index) {
        return this.getPlayerInfo().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public ArenaMailPlayerInfoProp setPlayerInfoIndex(int index, ArenaMailPlayerInfoProp v) {
        return this.getPlayerInfo().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArenaMailDataPB.Builder getCopyCsBuilder() {
        final ArenaMailDataPB.Builder builder = ArenaMailDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ArenaMailDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSelfRank() != 0) {
            builder.setSelfRank(this.getSelfRank());
            fieldCnt++;
        }  else if (builder.hasSelfRank()) {
            // 清理SelfRank
            builder.clearSelfRank();
            fieldCnt++;
        }
        if (this.playerInfo != null) {
            StructPB.ArenaMailPlayerInfoListPB.Builder tmpBuilder = StructPB.ArenaMailPlayerInfoListPB.newBuilder();
            final int tmpFieldCnt = this.playerInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerInfo();
            }
        }  else if (builder.hasPlayerInfo()) {
            // 清理PlayerInfo
            builder.clearPlayerInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ArenaMailDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SELFRANK)) {
            builder.setSelfRank(this.getSelfRank());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERINFO) && this.playerInfo != null) {
            final boolean needClear = !builder.hasPlayerInfo();
            final int tmpFieldCnt = this.playerInfo.copyChangeToCs(builder.getPlayerInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ArenaMailDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SELFRANK)) {
            builder.setSelfRank(this.getSelfRank());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERINFO) && this.playerInfo != null) {
            final boolean needClear = !builder.hasPlayerInfo();
            final int tmpFieldCnt = this.playerInfo.copyChangeToCs(builder.getPlayerInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ArenaMailDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSelfRank()) {
            this.innerSetSelfRank(proto.getSelfRank());
        } else {
            this.innerSetSelfRank(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPlayerInfo()) {
            this.getPlayerInfo().mergeFromCs(proto.getPlayerInfo());
        } else {
            if (this.playerInfo != null) {
                this.playerInfo.mergeFromCs(proto.getPlayerInfo());
            }
        }
        this.markAll();
        return ArenaMailDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ArenaMailDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSelfRank()) {
            this.setSelfRank(proto.getSelfRank());
            fieldCnt++;
        }
        if (proto.hasPlayerInfo()) {
            this.getPlayerInfo().mergeChangeFromCs(proto.getPlayerInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArenaMailData.Builder getCopyDbBuilder() {
        final ArenaMailData.Builder builder = ArenaMailData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ArenaMailData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSelfRank() != 0) {
            builder.setSelfRank(this.getSelfRank());
            fieldCnt++;
        }  else if (builder.hasSelfRank()) {
            // 清理SelfRank
            builder.clearSelfRank();
            fieldCnt++;
        }
        if (this.playerInfo != null) {
            Struct.ArenaMailPlayerInfoList.Builder tmpBuilder = Struct.ArenaMailPlayerInfoList.newBuilder();
            final int tmpFieldCnt = this.playerInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerInfo();
            }
        }  else if (builder.hasPlayerInfo()) {
            // 清理PlayerInfo
            builder.clearPlayerInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ArenaMailData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SELFRANK)) {
            builder.setSelfRank(this.getSelfRank());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERINFO) && this.playerInfo != null) {
            final boolean needClear = !builder.hasPlayerInfo();
            final int tmpFieldCnt = this.playerInfo.copyChangeToDb(builder.getPlayerInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ArenaMailData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSelfRank()) {
            this.innerSetSelfRank(proto.getSelfRank());
        } else {
            this.innerSetSelfRank(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPlayerInfo()) {
            this.getPlayerInfo().mergeFromDb(proto.getPlayerInfo());
        } else {
            if (this.playerInfo != null) {
                this.playerInfo.mergeFromDb(proto.getPlayerInfo());
            }
        }
        this.markAll();
        return ArenaMailDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ArenaMailData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSelfRank()) {
            this.setSelfRank(proto.getSelfRank());
            fieldCnt++;
        }
        if (proto.hasPlayerInfo()) {
            this.getPlayerInfo().mergeChangeFromDb(proto.getPlayerInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArenaMailData.Builder getCopySsBuilder() {
        final ArenaMailData.Builder builder = ArenaMailData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ArenaMailData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSelfRank() != 0) {
            builder.setSelfRank(this.getSelfRank());
            fieldCnt++;
        }  else if (builder.hasSelfRank()) {
            // 清理SelfRank
            builder.clearSelfRank();
            fieldCnt++;
        }
        if (this.playerInfo != null) {
            Struct.ArenaMailPlayerInfoList.Builder tmpBuilder = Struct.ArenaMailPlayerInfoList.newBuilder();
            final int tmpFieldCnt = this.playerInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerInfo();
            }
        }  else if (builder.hasPlayerInfo()) {
            // 清理PlayerInfo
            builder.clearPlayerInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ArenaMailData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SELFRANK)) {
            builder.setSelfRank(this.getSelfRank());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERINFO) && this.playerInfo != null) {
            final boolean needClear = !builder.hasPlayerInfo();
            final int tmpFieldCnt = this.playerInfo.copyChangeToSs(builder.getPlayerInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ArenaMailData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSelfRank()) {
            this.innerSetSelfRank(proto.getSelfRank());
        } else {
            this.innerSetSelfRank(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPlayerInfo()) {
            this.getPlayerInfo().mergeFromSs(proto.getPlayerInfo());
        } else {
            if (this.playerInfo != null) {
                this.playerInfo.mergeFromSs(proto.getPlayerInfo());
            }
        }
        this.markAll();
        return ArenaMailDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ArenaMailData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSelfRank()) {
            this.setSelfRank(proto.getSelfRank());
            fieldCnt++;
        }
        if (proto.hasPlayerInfo()) {
            this.getPlayerInfo().mergeChangeFromSs(proto.getPlayerInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ArenaMailData.Builder builder = ArenaMailData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERINFO) && this.playerInfo != null) {
            this.playerInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.playerInfo != null) {
            this.playerInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ArenaMailDataProp)) {
            return false;
        }
        final ArenaMailDataProp otherNode = (ArenaMailDataProp) node;
        if (this.selfRank != otherNode.selfRank) {
            return false;
        }
        if (!this.getPlayerInfo().compareDataTo(otherNode.getPlayerInfo())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
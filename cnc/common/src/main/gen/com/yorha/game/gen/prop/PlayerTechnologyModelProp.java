package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerTechnologyModel;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.PlayerTechnologyModelPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerTechnologyModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ALLFINTECH = 0;
    public static final int FIELD_INDEX_CANRECEIVETECH = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private Int32TechInfoMapProp allFinTech = null;
    private int canReceiveTech = Constant.DEFAULT_INT_VALUE;

    public PlayerTechnologyModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerTechnologyModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get allFinTech
     *
     * @return allFinTech value
     */
    public Int32TechInfoMapProp getAllFinTech() {
        if (this.allFinTech == null) {
            this.allFinTech = new Int32TechInfoMapProp(this, FIELD_INDEX_ALLFINTECH);
        }
        return this.allFinTech;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putAllFinTechV(TechInfoProp v) {
        this.getAllFinTech().put(v.getTechId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public TechInfoProp addEmptyAllFinTech(Integer k) {
        return this.getAllFinTech().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getAllFinTechSize() {
        if (this.allFinTech == null) {
            return 0;
        }
        return this.allFinTech.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isAllFinTechEmpty() {
        if (this.allFinTech == null) {
            return true;
        }
        return this.allFinTech.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public TechInfoProp getAllFinTechV(Integer k) {
        if (this.allFinTech == null || !this.allFinTech.containsKey(k)) {
            return null;
        }
        return this.allFinTech.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearAllFinTech() {
        if (this.allFinTech != null) {
            this.allFinTech.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeAllFinTechV(Integer k) {
        if (this.allFinTech != null) {
            this.allFinTech.remove(k);
        }
    }
    /**
     * get canReceiveTech
     *
     * @return canReceiveTech value
     */
    public int getCanReceiveTech() {
        return this.canReceiveTech;
    }

    /**
     * set canReceiveTech && set marked
     *
     * @param canReceiveTech new value
     * @return current object
     */
    public PlayerTechnologyModelProp setCanReceiveTech(int canReceiveTech) {
        if (this.canReceiveTech != canReceiveTech) {
            this.mark(FIELD_INDEX_CANRECEIVETECH);
            this.canReceiveTech = canReceiveTech;
        }
        return this;
    }

    /**
     * inner set canReceiveTech
     *
     * @param canReceiveTech new value
     */
    private void innerSetCanReceiveTech(int canReceiveTech) {
        this.canReceiveTech = canReceiveTech;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerTechnologyModelPB.Builder getCopyCsBuilder() {
        final PlayerTechnologyModelPB.Builder builder = PlayerTechnologyModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerTechnologyModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.allFinTech != null) {
            StructPB.Int32TechInfoMapPB.Builder tmpBuilder = StructPB.Int32TechInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.allFinTech.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAllFinTech(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAllFinTech();
            }
        }  else if (builder.hasAllFinTech()) {
            // 清理AllFinTech
            builder.clearAllFinTech();
            fieldCnt++;
        }
        if (this.getCanReceiveTech() != 0) {
            builder.setCanReceiveTech(this.getCanReceiveTech());
            fieldCnt++;
        }  else if (builder.hasCanReceiveTech()) {
            // 清理CanReceiveTech
            builder.clearCanReceiveTech();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerTechnologyModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ALLFINTECH) && this.allFinTech != null) {
            final boolean needClear = !builder.hasAllFinTech();
            final int tmpFieldCnt = this.allFinTech.copyChangeToCs(builder.getAllFinTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAllFinTech();
            }
        }
        if (this.hasMark(FIELD_INDEX_CANRECEIVETECH)) {
            builder.setCanReceiveTech(this.getCanReceiveTech());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerTechnologyModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ALLFINTECH) && this.allFinTech != null) {
            final boolean needClear = !builder.hasAllFinTech();
            final int tmpFieldCnt = this.allFinTech.copyChangeToAndClearDeleteKeysCs(builder.getAllFinTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAllFinTech();
            }
        }
        if (this.hasMark(FIELD_INDEX_CANRECEIVETECH)) {
            builder.setCanReceiveTech(this.getCanReceiveTech());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerTechnologyModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAllFinTech()) {
            this.getAllFinTech().mergeFromCs(proto.getAllFinTech());
        } else {
            if (this.allFinTech != null) {
                this.allFinTech.mergeFromCs(proto.getAllFinTech());
            }
        }
        if (proto.hasCanReceiveTech()) {
            this.innerSetCanReceiveTech(proto.getCanReceiveTech());
        } else {
            this.innerSetCanReceiveTech(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerTechnologyModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerTechnologyModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAllFinTech()) {
            this.getAllFinTech().mergeChangeFromCs(proto.getAllFinTech());
            fieldCnt++;
        }
        if (proto.hasCanReceiveTech()) {
            this.setCanReceiveTech(proto.getCanReceiveTech());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerTechnologyModel.Builder getCopyDbBuilder() {
        final PlayerTechnologyModel.Builder builder = PlayerTechnologyModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerTechnologyModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.allFinTech != null) {
            Struct.Int32TechInfoMap.Builder tmpBuilder = Struct.Int32TechInfoMap.newBuilder();
            final int tmpFieldCnt = this.allFinTech.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAllFinTech(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAllFinTech();
            }
        }  else if (builder.hasAllFinTech()) {
            // 清理AllFinTech
            builder.clearAllFinTech();
            fieldCnt++;
        }
        if (this.getCanReceiveTech() != 0) {
            builder.setCanReceiveTech(this.getCanReceiveTech());
            fieldCnt++;
        }  else if (builder.hasCanReceiveTech()) {
            // 清理CanReceiveTech
            builder.clearCanReceiveTech();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerTechnologyModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ALLFINTECH) && this.allFinTech != null) {
            final boolean needClear = !builder.hasAllFinTech();
            final int tmpFieldCnt = this.allFinTech.copyChangeToDb(builder.getAllFinTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAllFinTech();
            }
        }
        if (this.hasMark(FIELD_INDEX_CANRECEIVETECH)) {
            builder.setCanReceiveTech(this.getCanReceiveTech());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerTechnologyModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAllFinTech()) {
            this.getAllFinTech().mergeFromDb(proto.getAllFinTech());
        } else {
            if (this.allFinTech != null) {
                this.allFinTech.mergeFromDb(proto.getAllFinTech());
            }
        }
        if (proto.hasCanReceiveTech()) {
            this.innerSetCanReceiveTech(proto.getCanReceiveTech());
        } else {
            this.innerSetCanReceiveTech(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerTechnologyModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerTechnologyModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAllFinTech()) {
            this.getAllFinTech().mergeChangeFromDb(proto.getAllFinTech());
            fieldCnt++;
        }
        if (proto.hasCanReceiveTech()) {
            this.setCanReceiveTech(proto.getCanReceiveTech());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerTechnologyModel.Builder getCopySsBuilder() {
        final PlayerTechnologyModel.Builder builder = PlayerTechnologyModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerTechnologyModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.allFinTech != null) {
            Struct.Int32TechInfoMap.Builder tmpBuilder = Struct.Int32TechInfoMap.newBuilder();
            final int tmpFieldCnt = this.allFinTech.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAllFinTech(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAllFinTech();
            }
        }  else if (builder.hasAllFinTech()) {
            // 清理AllFinTech
            builder.clearAllFinTech();
            fieldCnt++;
        }
        if (this.getCanReceiveTech() != 0) {
            builder.setCanReceiveTech(this.getCanReceiveTech());
            fieldCnt++;
        }  else if (builder.hasCanReceiveTech()) {
            // 清理CanReceiveTech
            builder.clearCanReceiveTech();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerTechnologyModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ALLFINTECH) && this.allFinTech != null) {
            final boolean needClear = !builder.hasAllFinTech();
            final int tmpFieldCnt = this.allFinTech.copyChangeToSs(builder.getAllFinTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAllFinTech();
            }
        }
        if (this.hasMark(FIELD_INDEX_CANRECEIVETECH)) {
            builder.setCanReceiveTech(this.getCanReceiveTech());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerTechnologyModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAllFinTech()) {
            this.getAllFinTech().mergeFromSs(proto.getAllFinTech());
        } else {
            if (this.allFinTech != null) {
                this.allFinTech.mergeFromSs(proto.getAllFinTech());
            }
        }
        if (proto.hasCanReceiveTech()) {
            this.innerSetCanReceiveTech(proto.getCanReceiveTech());
        } else {
            this.innerSetCanReceiveTech(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerTechnologyModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerTechnologyModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAllFinTech()) {
            this.getAllFinTech().mergeChangeFromSs(proto.getAllFinTech());
            fieldCnt++;
        }
        if (proto.hasCanReceiveTech()) {
            this.setCanReceiveTech(proto.getCanReceiveTech());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerTechnologyModel.Builder builder = PlayerTechnologyModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ALLFINTECH) && this.allFinTech != null) {
            this.allFinTech.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.allFinTech != null) {
            this.allFinTech.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerTechnologyModelProp)) {
            return false;
        }
        final PlayerTechnologyModelProp otherNode = (PlayerTechnologyModelProp) node;
        if (!this.getAllFinTech().compareDataTo(otherNode.getAllFinTech())) {
            return false;
        }
        if (this.canReceiveTech != otherNode.canReceiveTech) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Struct.ClanHelpItem;
import com.yorha.proto.Basic;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ClanHelpItemPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class Clan<PERSON>elpItemProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_HELPTYPE = 1;
    public static final int FIELD_INDEX_ALREADYHELPTIMES = 2;
    public static final int FIELD_INDEX_TOTALHELPTIMES = 3;
    public static final int FIELD_INDEX_TOTALREDUCTIONTIME = 4;
    public static final int FIELD_INDEX_HELPRECORD = 5;
    public static final int FIELD_INDEX_PLAYERID = 6;
    public static final int FIELD_INDEX_ALREADYHELPPLAYERIDS = 7;
    public static final int FIELD_INDEX_TARGETLEVEL = 8;
    public static final int FIELD_INDEX_BUILDID = 9;
    public static final int FIELD_INDEX_CREATETSMS = 10;
    public static final int FIELD_INDEX_ISCANCELED = 11;
    public static final int FIELD_INDEX_HELPPLAYERNAME = 12;
    public static final int FIELD_INDEX_CARDHEAD = 13;

    public static final int FIELD_COUNT = 14;

    private long markBits0 = 0L;

    private long id = Constant.DEFAULT_LONG_VALUE;
    private QueueTaskType helpType = QueueTaskType.forNumber(0);
    private int alreadyHelpTimes = Constant.DEFAULT_INT_VALUE;
    private int totalHelpTimes = Constant.DEFAULT_INT_VALUE;
    private long totalReductionTime = Constant.DEFAULT_LONG_VALUE;
    private ClanRecordProp helpRecord = null;
    private long playerId = Constant.DEFAULT_LONG_VALUE;
    private Int64ListProp alreadyHelpPlayerIds = null;
    private int targetLevel = Constant.DEFAULT_INT_VALUE;
    private long buildId = Constant.DEFAULT_LONG_VALUE;
    private long createTsMs = Constant.DEFAULT_LONG_VALUE;
    private boolean isCanceled = Constant.DEFAULT_BOOLEAN_VALUE;
    private String helpPlayerName = Constant.DEFAULT_STR_VALUE;
    private PlayerCardHeadProp cardHead = null;

    public ClanHelpItemProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanHelpItemProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public long getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public ClanHelpItemProp setId(long id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(long id) {
        this.id = id;
    }

    /**
     * get helpType
     *
     * @return helpType value
     */
    public QueueTaskType getHelpType() {
        return this.helpType;
    }

    /**
     * set helpType && set marked
     *
     * @param helpType new value
     * @return current object
     */
    public ClanHelpItemProp setHelpType(QueueTaskType helpType) {
        if (helpType == null) {
            throw new NullPointerException();
        }
        if (this.helpType != helpType) {
            this.mark(FIELD_INDEX_HELPTYPE);
            this.helpType = helpType;
        }
        return this;
    }

    /**
     * inner set helpType
     *
     * @param helpType new value
     */
    private void innerSetHelpType(QueueTaskType helpType) {
        this.helpType = helpType;
    }

    /**
     * get alreadyHelpTimes
     *
     * @return alreadyHelpTimes value
     */
    public int getAlreadyHelpTimes() {
        return this.alreadyHelpTimes;
    }

    /**
     * set alreadyHelpTimes && set marked
     *
     * @param alreadyHelpTimes new value
     * @return current object
     */
    public ClanHelpItemProp setAlreadyHelpTimes(int alreadyHelpTimes) {
        if (this.alreadyHelpTimes != alreadyHelpTimes) {
            this.mark(FIELD_INDEX_ALREADYHELPTIMES);
            this.alreadyHelpTimes = alreadyHelpTimes;
        }
        return this;
    }

    /**
     * inner set alreadyHelpTimes
     *
     * @param alreadyHelpTimes new value
     */
    private void innerSetAlreadyHelpTimes(int alreadyHelpTimes) {
        this.alreadyHelpTimes = alreadyHelpTimes;
    }

    /**
     * get totalHelpTimes
     *
     * @return totalHelpTimes value
     */
    public int getTotalHelpTimes() {
        return this.totalHelpTimes;
    }

    /**
     * set totalHelpTimes && set marked
     *
     * @param totalHelpTimes new value
     * @return current object
     */
    public ClanHelpItemProp setTotalHelpTimes(int totalHelpTimes) {
        if (this.totalHelpTimes != totalHelpTimes) {
            this.mark(FIELD_INDEX_TOTALHELPTIMES);
            this.totalHelpTimes = totalHelpTimes;
        }
        return this;
    }

    /**
     * inner set totalHelpTimes
     *
     * @param totalHelpTimes new value
     */
    private void innerSetTotalHelpTimes(int totalHelpTimes) {
        this.totalHelpTimes = totalHelpTimes;
    }

    /**
     * get totalReductionTime
     *
     * @return totalReductionTime value
     */
    public long getTotalReductionTime() {
        return this.totalReductionTime;
    }

    /**
     * set totalReductionTime && set marked
     *
     * @param totalReductionTime new value
     * @return current object
     */
    public ClanHelpItemProp setTotalReductionTime(long totalReductionTime) {
        if (this.totalReductionTime != totalReductionTime) {
            this.mark(FIELD_INDEX_TOTALREDUCTIONTIME);
            this.totalReductionTime = totalReductionTime;
        }
        return this;
    }

    /**
     * inner set totalReductionTime
     *
     * @param totalReductionTime new value
     */
    private void innerSetTotalReductionTime(long totalReductionTime) {
        this.totalReductionTime = totalReductionTime;
    }

    /**
     * get helpRecord
     *
     * @return helpRecord value
     */
    public ClanRecordProp getHelpRecord() {
        if (this.helpRecord == null) {
            this.helpRecord = new ClanRecordProp(this, FIELD_INDEX_HELPRECORD);
        }
        return this.helpRecord;
    }

    /**
     * get playerId
     *
     * @return playerId value
     */
    public long getPlayerId() {
        return this.playerId;
    }

    /**
     * set playerId && set marked
     *
     * @param playerId new value
     * @return current object
     */
    public ClanHelpItemProp setPlayerId(long playerId) {
        if (this.playerId != playerId) {
            this.mark(FIELD_INDEX_PLAYERID);
            this.playerId = playerId;
        }
        return this;
    }

    /**
     * inner set playerId
     *
     * @param playerId new value
     */
    private void innerSetPlayerId(long playerId) {
        this.playerId = playerId;
    }

    /**
     * get alreadyHelpPlayerIds
     *
     * @return alreadyHelpPlayerIds value
     */
    public Int64ListProp getAlreadyHelpPlayerIds() {
        if (this.alreadyHelpPlayerIds == null) {
            this.alreadyHelpPlayerIds = new Int64ListProp(this, FIELD_INDEX_ALREADYHELPPLAYERIDS);
        }
        return this.alreadyHelpPlayerIds;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addAlreadyHelpPlayerIds(Long v) {
        this.getAlreadyHelpPlayerIds().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long getAlreadyHelpPlayerIdsIndex(int index) {
        return this.getAlreadyHelpPlayerIds().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Long removeAlreadyHelpPlayerIds(Long v) {
        if (this.alreadyHelpPlayerIds == null) {
            return null;
        }
        if(this.alreadyHelpPlayerIds.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getAlreadyHelpPlayerIdsSize() {
        if (this.alreadyHelpPlayerIds == null) {
            return 0;
        }
        return this.alreadyHelpPlayerIds.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isAlreadyHelpPlayerIdsEmpty() {
        if (this.alreadyHelpPlayerIds == null) {
            return true;
        }
        return this.getAlreadyHelpPlayerIds().isEmpty();
    }

    /**
     * clear list
     */
    public void clearAlreadyHelpPlayerIds() {
        this.getAlreadyHelpPlayerIds().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long removeAlreadyHelpPlayerIdsIndex(int index) {
        return this.getAlreadyHelpPlayerIds().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Long setAlreadyHelpPlayerIdsIndex(int index, Long v) {
        return this.getAlreadyHelpPlayerIds().set(index, v);
    }
    /**
     * get targetLevel
     *
     * @return targetLevel value
     */
    public int getTargetLevel() {
        return this.targetLevel;
    }

    /**
     * set targetLevel && set marked
     *
     * @param targetLevel new value
     * @return current object
     */
    public ClanHelpItemProp setTargetLevel(int targetLevel) {
        if (this.targetLevel != targetLevel) {
            this.mark(FIELD_INDEX_TARGETLEVEL);
            this.targetLevel = targetLevel;
        }
        return this;
    }

    /**
     * inner set targetLevel
     *
     * @param targetLevel new value
     */
    private void innerSetTargetLevel(int targetLevel) {
        this.targetLevel = targetLevel;
    }

    /**
     * get buildId
     *
     * @return buildId value
     */
    public long getBuildId() {
        return this.buildId;
    }

    /**
     * set buildId && set marked
     *
     * @param buildId new value
     * @return current object
     */
    public ClanHelpItemProp setBuildId(long buildId) {
        if (this.buildId != buildId) {
            this.mark(FIELD_INDEX_BUILDID);
            this.buildId = buildId;
        }
        return this;
    }

    /**
     * inner set buildId
     *
     * @param buildId new value
     */
    private void innerSetBuildId(long buildId) {
        this.buildId = buildId;
    }

    /**
     * get createTsMs
     *
     * @return createTsMs value
     */
    public long getCreateTsMs() {
        return this.createTsMs;
    }

    /**
     * set createTsMs && set marked
     *
     * @param createTsMs new value
     * @return current object
     */
    public ClanHelpItemProp setCreateTsMs(long createTsMs) {
        if (this.createTsMs != createTsMs) {
            this.mark(FIELD_INDEX_CREATETSMS);
            this.createTsMs = createTsMs;
        }
        return this;
    }

    /**
     * inner set createTsMs
     *
     * @param createTsMs new value
     */
    private void innerSetCreateTsMs(long createTsMs) {
        this.createTsMs = createTsMs;
    }

    /**
     * get isCanceled
     *
     * @return isCanceled value
     */
    public boolean getIsCanceled() {
        return this.isCanceled;
    }

    /**
     * set isCanceled && set marked
     *
     * @param isCanceled new value
     * @return current object
     */
    public ClanHelpItemProp setIsCanceled(boolean isCanceled) {
        if (this.isCanceled != isCanceled) {
            this.mark(FIELD_INDEX_ISCANCELED);
            this.isCanceled = isCanceled;
        }
        return this;
    }

    /**
     * inner set isCanceled
     *
     * @param isCanceled new value
     */
    private void innerSetIsCanceled(boolean isCanceled) {
        this.isCanceled = isCanceled;
    }

    /**
     * get helpPlayerName
     *
     * @return helpPlayerName value
     */
    public String getHelpPlayerName() {
        return this.helpPlayerName;
    }

    /**
     * set helpPlayerName && set marked
     *
     * @param helpPlayerName new value
     * @return current object
     */
    public ClanHelpItemProp setHelpPlayerName(String helpPlayerName) {
        if (helpPlayerName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.helpPlayerName, helpPlayerName)) {
            this.mark(FIELD_INDEX_HELPPLAYERNAME);
            this.helpPlayerName = helpPlayerName;
        }
        return this;
    }

    /**
     * inner set helpPlayerName
     *
     * @param helpPlayerName new value
     */
    private void innerSetHelpPlayerName(String helpPlayerName) {
        this.helpPlayerName = helpPlayerName;
    }

    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanHelpItemPB.Builder getCopyCsBuilder() {
        final ClanHelpItemPB.Builder builder = ClanHelpItemPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanHelpItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getHelpType() != QueueTaskType.forNumber(0)) {
            builder.setHelpType(this.getHelpType());
            fieldCnt++;
        }  else if (builder.hasHelpType()) {
            // 清理HelpType
            builder.clearHelpType();
            fieldCnt++;
        }
        if (this.getAlreadyHelpTimes() != 0) {
            builder.setAlreadyHelpTimes(this.getAlreadyHelpTimes());
            fieldCnt++;
        }  else if (builder.hasAlreadyHelpTimes()) {
            // 清理AlreadyHelpTimes
            builder.clearAlreadyHelpTimes();
            fieldCnt++;
        }
        if (this.getTotalHelpTimes() != 0) {
            builder.setTotalHelpTimes(this.getTotalHelpTimes());
            fieldCnt++;
        }  else if (builder.hasTotalHelpTimes()) {
            // 清理TotalHelpTimes
            builder.clearTotalHelpTimes();
            fieldCnt++;
        }
        if (this.getTotalReductionTime() != 0L) {
            builder.setTotalReductionTime(this.getTotalReductionTime());
            fieldCnt++;
        }  else if (builder.hasTotalReductionTime()) {
            // 清理TotalReductionTime
            builder.clearTotalReductionTime();
            fieldCnt++;
        }
        if (this.helpRecord != null) {
            StructPB.ClanRecordPB.Builder tmpBuilder = StructPB.ClanRecordPB.newBuilder();
            final int tmpFieldCnt = this.helpRecord.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHelpRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHelpRecord();
            }
        }  else if (builder.hasHelpRecord()) {
            // 清理HelpRecord
            builder.clearHelpRecord();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.alreadyHelpPlayerIds != null) {
            BasicPB.Int64ListPB.Builder tmpBuilder = BasicPB.Int64ListPB.newBuilder();
            final int tmpFieldCnt = this.alreadyHelpPlayerIds.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAlreadyHelpPlayerIds(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAlreadyHelpPlayerIds();
            }
        }  else if (builder.hasAlreadyHelpPlayerIds()) {
            // 清理AlreadyHelpPlayerIds
            builder.clearAlreadyHelpPlayerIds();
            fieldCnt++;
        }
        if (this.getCreateTsMs() != 0L) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }  else if (builder.hasCreateTsMs()) {
            // 清理CreateTsMs
            builder.clearCreateTsMs();
            fieldCnt++;
        }
        if (this.getIsCanceled()) {
            builder.setIsCanceled(this.getIsCanceled());
            fieldCnt++;
        }  else if (builder.hasIsCanceled()) {
            // 清理IsCanceled
            builder.clearIsCanceled();
            fieldCnt++;
        }
        if (!this.getHelpPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setHelpPlayerName(this.getHelpPlayerName());
            fieldCnt++;
        }  else if (builder.hasHelpPlayerName()) {
            // 清理HelpPlayerName
            builder.clearHelpPlayerName();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanHelpItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HELPTYPE)) {
            builder.setHelpType(this.getHelpType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALREADYHELPTIMES)) {
            builder.setAlreadyHelpTimes(this.getAlreadyHelpTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALHELPTIMES)) {
            builder.setTotalHelpTimes(this.getTotalHelpTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALREDUCTIONTIME)) {
            builder.setTotalReductionTime(this.getTotalReductionTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HELPRECORD) && this.helpRecord != null) {
            final boolean needClear = !builder.hasHelpRecord();
            final int tmpFieldCnt = this.helpRecord.copyChangeToCs(builder.getHelpRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHelpRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALREADYHELPPLAYERIDS) && this.alreadyHelpPlayerIds != null) {
            final boolean needClear = !builder.hasAlreadyHelpPlayerIds();
            final int tmpFieldCnt = this.alreadyHelpPlayerIds.copyChangeToCs(builder.getAlreadyHelpPlayerIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAlreadyHelpPlayerIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISCANCELED)) {
            builder.setIsCanceled(this.getIsCanceled());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HELPPLAYERNAME)) {
            builder.setHelpPlayerName(this.getHelpPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanHelpItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HELPTYPE)) {
            builder.setHelpType(this.getHelpType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALREADYHELPTIMES)) {
            builder.setAlreadyHelpTimes(this.getAlreadyHelpTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALHELPTIMES)) {
            builder.setTotalHelpTimes(this.getTotalHelpTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALREDUCTIONTIME)) {
            builder.setTotalReductionTime(this.getTotalReductionTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HELPRECORD) && this.helpRecord != null) {
            final boolean needClear = !builder.hasHelpRecord();
            final int tmpFieldCnt = this.helpRecord.copyChangeToAndClearDeleteKeysCs(builder.getHelpRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHelpRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALREADYHELPPLAYERIDS) && this.alreadyHelpPlayerIds != null) {
            final boolean needClear = !builder.hasAlreadyHelpPlayerIds();
            final int tmpFieldCnt = this.alreadyHelpPlayerIds.copyChangeToCs(builder.getAlreadyHelpPlayerIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAlreadyHelpPlayerIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISCANCELED)) {
            builder.setIsCanceled(this.getIsCanceled());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HELPPLAYERNAME)) {
            builder.setHelpPlayerName(this.getHelpPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToAndClearDeleteKeysCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanHelpItemPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasHelpType()) {
            this.innerSetHelpType(proto.getHelpType());
        } else {
            this.innerSetHelpType(QueueTaskType.forNumber(0));
        }
        if (proto.hasAlreadyHelpTimes()) {
            this.innerSetAlreadyHelpTimes(proto.getAlreadyHelpTimes());
        } else {
            this.innerSetAlreadyHelpTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTotalHelpTimes()) {
            this.innerSetTotalHelpTimes(proto.getTotalHelpTimes());
        } else {
            this.innerSetTotalHelpTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTotalReductionTime()) {
            this.innerSetTotalReductionTime(proto.getTotalReductionTime());
        } else {
            this.innerSetTotalReductionTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasHelpRecord()) {
            this.getHelpRecord().mergeFromCs(proto.getHelpRecord());
        } else {
            if (this.helpRecord != null) {
                this.helpRecord.mergeFromCs(proto.getHelpRecord());
            }
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasAlreadyHelpPlayerIds()) {
            this.getAlreadyHelpPlayerIds().mergeFromCs(proto.getAlreadyHelpPlayerIds());
        } else {
            if (this.alreadyHelpPlayerIds != null) {
                this.alreadyHelpPlayerIds.mergeFromCs(proto.getAlreadyHelpPlayerIds());
            }
        }
        if (proto.hasCreateTsMs()) {
            this.innerSetCreateTsMs(proto.getCreateTsMs());
        } else {
            this.innerSetCreateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIsCanceled()) {
            this.innerSetIsCanceled(proto.getIsCanceled());
        } else {
            this.innerSetIsCanceled(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasHelpPlayerName()) {
            this.innerSetHelpPlayerName(proto.getHelpPlayerName());
        } else {
            this.innerSetHelpPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromCs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromCs(proto.getCardHead());
            }
        }
        this.markAll();
        return ClanHelpItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanHelpItemPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasHelpType()) {
            this.setHelpType(proto.getHelpType());
            fieldCnt++;
        }
        if (proto.hasAlreadyHelpTimes()) {
            this.setAlreadyHelpTimes(proto.getAlreadyHelpTimes());
            fieldCnt++;
        }
        if (proto.hasTotalHelpTimes()) {
            this.setTotalHelpTimes(proto.getTotalHelpTimes());
            fieldCnt++;
        }
        if (proto.hasTotalReductionTime()) {
            this.setTotalReductionTime(proto.getTotalReductionTime());
            fieldCnt++;
        }
        if (proto.hasHelpRecord()) {
            this.getHelpRecord().mergeChangeFromCs(proto.getHelpRecord());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasAlreadyHelpPlayerIds()) {
            this.getAlreadyHelpPlayerIds().mergeChangeFromCs(proto.getAlreadyHelpPlayerIds());
            fieldCnt++;
        }
        if (proto.hasCreateTsMs()) {
            this.setCreateTsMs(proto.getCreateTsMs());
            fieldCnt++;
        }
        if (proto.hasIsCanceled()) {
            this.setIsCanceled(proto.getIsCanceled());
            fieldCnt++;
        }
        if (proto.hasHelpPlayerName()) {
            this.setHelpPlayerName(proto.getHelpPlayerName());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromCs(proto.getCardHead());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanHelpItem.Builder getCopyDbBuilder() {
        final ClanHelpItem.Builder builder = ClanHelpItem.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanHelpItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getHelpType() != QueueTaskType.forNumber(0)) {
            builder.setHelpType(this.getHelpType());
            fieldCnt++;
        }  else if (builder.hasHelpType()) {
            // 清理HelpType
            builder.clearHelpType();
            fieldCnt++;
        }
        if (this.getAlreadyHelpTimes() != 0) {
            builder.setAlreadyHelpTimes(this.getAlreadyHelpTimes());
            fieldCnt++;
        }  else if (builder.hasAlreadyHelpTimes()) {
            // 清理AlreadyHelpTimes
            builder.clearAlreadyHelpTimes();
            fieldCnt++;
        }
        if (this.getTotalHelpTimes() != 0) {
            builder.setTotalHelpTimes(this.getTotalHelpTimes());
            fieldCnt++;
        }  else if (builder.hasTotalHelpTimes()) {
            // 清理TotalHelpTimes
            builder.clearTotalHelpTimes();
            fieldCnt++;
        }
        if (this.getTotalReductionTime() != 0L) {
            builder.setTotalReductionTime(this.getTotalReductionTime());
            fieldCnt++;
        }  else if (builder.hasTotalReductionTime()) {
            // 清理TotalReductionTime
            builder.clearTotalReductionTime();
            fieldCnt++;
        }
        if (this.helpRecord != null) {
            Struct.ClanRecord.Builder tmpBuilder = Struct.ClanRecord.newBuilder();
            final int tmpFieldCnt = this.helpRecord.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHelpRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHelpRecord();
            }
        }  else if (builder.hasHelpRecord()) {
            // 清理HelpRecord
            builder.clearHelpRecord();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.alreadyHelpPlayerIds != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.alreadyHelpPlayerIds.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAlreadyHelpPlayerIds(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAlreadyHelpPlayerIds();
            }
        }  else if (builder.hasAlreadyHelpPlayerIds()) {
            // 清理AlreadyHelpPlayerIds
            builder.clearAlreadyHelpPlayerIds();
            fieldCnt++;
        }
        if (this.getTargetLevel() != 0) {
            builder.setTargetLevel(this.getTargetLevel());
            fieldCnt++;
        }  else if (builder.hasTargetLevel()) {
            // 清理TargetLevel
            builder.clearTargetLevel();
            fieldCnt++;
        }
        if (this.getBuildId() != 0L) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }  else if (builder.hasBuildId()) {
            // 清理BuildId
            builder.clearBuildId();
            fieldCnt++;
        }
        if (this.getCreateTsMs() != 0L) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }  else if (builder.hasCreateTsMs()) {
            // 清理CreateTsMs
            builder.clearCreateTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanHelpItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HELPTYPE)) {
            builder.setHelpType(this.getHelpType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALREADYHELPTIMES)) {
            builder.setAlreadyHelpTimes(this.getAlreadyHelpTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALHELPTIMES)) {
            builder.setTotalHelpTimes(this.getTotalHelpTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALREDUCTIONTIME)) {
            builder.setTotalReductionTime(this.getTotalReductionTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HELPRECORD) && this.helpRecord != null) {
            final boolean needClear = !builder.hasHelpRecord();
            final int tmpFieldCnt = this.helpRecord.copyChangeToDb(builder.getHelpRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHelpRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALREADYHELPPLAYERIDS) && this.alreadyHelpPlayerIds != null) {
            final boolean needClear = !builder.hasAlreadyHelpPlayerIds();
            final int tmpFieldCnt = this.alreadyHelpPlayerIds.copyChangeToDb(builder.getAlreadyHelpPlayerIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAlreadyHelpPlayerIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETLEVEL)) {
            builder.setTargetLevel(this.getTargetLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDID)) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanHelpItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasHelpType()) {
            this.innerSetHelpType(proto.getHelpType());
        } else {
            this.innerSetHelpType(QueueTaskType.forNumber(0));
        }
        if (proto.hasAlreadyHelpTimes()) {
            this.innerSetAlreadyHelpTimes(proto.getAlreadyHelpTimes());
        } else {
            this.innerSetAlreadyHelpTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTotalHelpTimes()) {
            this.innerSetTotalHelpTimes(proto.getTotalHelpTimes());
        } else {
            this.innerSetTotalHelpTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTotalReductionTime()) {
            this.innerSetTotalReductionTime(proto.getTotalReductionTime());
        } else {
            this.innerSetTotalReductionTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasHelpRecord()) {
            this.getHelpRecord().mergeFromDb(proto.getHelpRecord());
        } else {
            if (this.helpRecord != null) {
                this.helpRecord.mergeFromDb(proto.getHelpRecord());
            }
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasAlreadyHelpPlayerIds()) {
            this.getAlreadyHelpPlayerIds().mergeFromDb(proto.getAlreadyHelpPlayerIds());
        } else {
            if (this.alreadyHelpPlayerIds != null) {
                this.alreadyHelpPlayerIds.mergeFromDb(proto.getAlreadyHelpPlayerIds());
            }
        }
        if (proto.hasTargetLevel()) {
            this.innerSetTargetLevel(proto.getTargetLevel());
        } else {
            this.innerSetTargetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuildId()) {
            this.innerSetBuildId(proto.getBuildId());
        } else {
            this.innerSetBuildId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCreateTsMs()) {
            this.innerSetCreateTsMs(proto.getCreateTsMs());
        } else {
            this.innerSetCreateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanHelpItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanHelpItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasHelpType()) {
            this.setHelpType(proto.getHelpType());
            fieldCnt++;
        }
        if (proto.hasAlreadyHelpTimes()) {
            this.setAlreadyHelpTimes(proto.getAlreadyHelpTimes());
            fieldCnt++;
        }
        if (proto.hasTotalHelpTimes()) {
            this.setTotalHelpTimes(proto.getTotalHelpTimes());
            fieldCnt++;
        }
        if (proto.hasTotalReductionTime()) {
            this.setTotalReductionTime(proto.getTotalReductionTime());
            fieldCnt++;
        }
        if (proto.hasHelpRecord()) {
            this.getHelpRecord().mergeChangeFromDb(proto.getHelpRecord());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasAlreadyHelpPlayerIds()) {
            this.getAlreadyHelpPlayerIds().mergeChangeFromDb(proto.getAlreadyHelpPlayerIds());
            fieldCnt++;
        }
        if (proto.hasTargetLevel()) {
            this.setTargetLevel(proto.getTargetLevel());
            fieldCnt++;
        }
        if (proto.hasBuildId()) {
            this.setBuildId(proto.getBuildId());
            fieldCnt++;
        }
        if (proto.hasCreateTsMs()) {
            this.setCreateTsMs(proto.getCreateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanHelpItem.Builder getCopySsBuilder() {
        final ClanHelpItem.Builder builder = ClanHelpItem.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanHelpItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getHelpType() != QueueTaskType.forNumber(0)) {
            builder.setHelpType(this.getHelpType());
            fieldCnt++;
        }  else if (builder.hasHelpType()) {
            // 清理HelpType
            builder.clearHelpType();
            fieldCnt++;
        }
        if (this.getAlreadyHelpTimes() != 0) {
            builder.setAlreadyHelpTimes(this.getAlreadyHelpTimes());
            fieldCnt++;
        }  else if (builder.hasAlreadyHelpTimes()) {
            // 清理AlreadyHelpTimes
            builder.clearAlreadyHelpTimes();
            fieldCnt++;
        }
        if (this.getTotalHelpTimes() != 0) {
            builder.setTotalHelpTimes(this.getTotalHelpTimes());
            fieldCnt++;
        }  else if (builder.hasTotalHelpTimes()) {
            // 清理TotalHelpTimes
            builder.clearTotalHelpTimes();
            fieldCnt++;
        }
        if (this.getTotalReductionTime() != 0L) {
            builder.setTotalReductionTime(this.getTotalReductionTime());
            fieldCnt++;
        }  else if (builder.hasTotalReductionTime()) {
            // 清理TotalReductionTime
            builder.clearTotalReductionTime();
            fieldCnt++;
        }
        if (this.helpRecord != null) {
            Struct.ClanRecord.Builder tmpBuilder = Struct.ClanRecord.newBuilder();
            final int tmpFieldCnt = this.helpRecord.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHelpRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHelpRecord();
            }
        }  else if (builder.hasHelpRecord()) {
            // 清理HelpRecord
            builder.clearHelpRecord();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.alreadyHelpPlayerIds != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.alreadyHelpPlayerIds.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAlreadyHelpPlayerIds(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAlreadyHelpPlayerIds();
            }
        }  else if (builder.hasAlreadyHelpPlayerIds()) {
            // 清理AlreadyHelpPlayerIds
            builder.clearAlreadyHelpPlayerIds();
            fieldCnt++;
        }
        if (this.getTargetLevel() != 0) {
            builder.setTargetLevel(this.getTargetLevel());
            fieldCnt++;
        }  else if (builder.hasTargetLevel()) {
            // 清理TargetLevel
            builder.clearTargetLevel();
            fieldCnt++;
        }
        if (this.getBuildId() != 0L) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }  else if (builder.hasBuildId()) {
            // 清理BuildId
            builder.clearBuildId();
            fieldCnt++;
        }
        if (this.getCreateTsMs() != 0L) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }  else if (builder.hasCreateTsMs()) {
            // 清理CreateTsMs
            builder.clearCreateTsMs();
            fieldCnt++;
        }
        if (this.getIsCanceled()) {
            builder.setIsCanceled(this.getIsCanceled());
            fieldCnt++;
        }  else if (builder.hasIsCanceled()) {
            // 清理IsCanceled
            builder.clearIsCanceled();
            fieldCnt++;
        }
        if (!this.getHelpPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setHelpPlayerName(this.getHelpPlayerName());
            fieldCnt++;
        }  else if (builder.hasHelpPlayerName()) {
            // 清理HelpPlayerName
            builder.clearHelpPlayerName();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanHelpItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HELPTYPE)) {
            builder.setHelpType(this.getHelpType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALREADYHELPTIMES)) {
            builder.setAlreadyHelpTimes(this.getAlreadyHelpTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALHELPTIMES)) {
            builder.setTotalHelpTimes(this.getTotalHelpTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALREDUCTIONTIME)) {
            builder.setTotalReductionTime(this.getTotalReductionTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HELPRECORD) && this.helpRecord != null) {
            final boolean needClear = !builder.hasHelpRecord();
            final int tmpFieldCnt = this.helpRecord.copyChangeToSs(builder.getHelpRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHelpRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALREADYHELPPLAYERIDS) && this.alreadyHelpPlayerIds != null) {
            final boolean needClear = !builder.hasAlreadyHelpPlayerIds();
            final int tmpFieldCnt = this.alreadyHelpPlayerIds.copyChangeToSs(builder.getAlreadyHelpPlayerIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAlreadyHelpPlayerIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETLEVEL)) {
            builder.setTargetLevel(this.getTargetLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDID)) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISCANCELED)) {
            builder.setIsCanceled(this.getIsCanceled());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HELPPLAYERNAME)) {
            builder.setHelpPlayerName(this.getHelpPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanHelpItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasHelpType()) {
            this.innerSetHelpType(proto.getHelpType());
        } else {
            this.innerSetHelpType(QueueTaskType.forNumber(0));
        }
        if (proto.hasAlreadyHelpTimes()) {
            this.innerSetAlreadyHelpTimes(proto.getAlreadyHelpTimes());
        } else {
            this.innerSetAlreadyHelpTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTotalHelpTimes()) {
            this.innerSetTotalHelpTimes(proto.getTotalHelpTimes());
        } else {
            this.innerSetTotalHelpTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTotalReductionTime()) {
            this.innerSetTotalReductionTime(proto.getTotalReductionTime());
        } else {
            this.innerSetTotalReductionTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasHelpRecord()) {
            this.getHelpRecord().mergeFromSs(proto.getHelpRecord());
        } else {
            if (this.helpRecord != null) {
                this.helpRecord.mergeFromSs(proto.getHelpRecord());
            }
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasAlreadyHelpPlayerIds()) {
            this.getAlreadyHelpPlayerIds().mergeFromSs(proto.getAlreadyHelpPlayerIds());
        } else {
            if (this.alreadyHelpPlayerIds != null) {
                this.alreadyHelpPlayerIds.mergeFromSs(proto.getAlreadyHelpPlayerIds());
            }
        }
        if (proto.hasTargetLevel()) {
            this.innerSetTargetLevel(proto.getTargetLevel());
        } else {
            this.innerSetTargetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuildId()) {
            this.innerSetBuildId(proto.getBuildId());
        } else {
            this.innerSetBuildId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCreateTsMs()) {
            this.innerSetCreateTsMs(proto.getCreateTsMs());
        } else {
            this.innerSetCreateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIsCanceled()) {
            this.innerSetIsCanceled(proto.getIsCanceled());
        } else {
            this.innerSetIsCanceled(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasHelpPlayerName()) {
            this.innerSetHelpPlayerName(proto.getHelpPlayerName());
        } else {
            this.innerSetHelpPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        this.markAll();
        return ClanHelpItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanHelpItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasHelpType()) {
            this.setHelpType(proto.getHelpType());
            fieldCnt++;
        }
        if (proto.hasAlreadyHelpTimes()) {
            this.setAlreadyHelpTimes(proto.getAlreadyHelpTimes());
            fieldCnt++;
        }
        if (proto.hasTotalHelpTimes()) {
            this.setTotalHelpTimes(proto.getTotalHelpTimes());
            fieldCnt++;
        }
        if (proto.hasTotalReductionTime()) {
            this.setTotalReductionTime(proto.getTotalReductionTime());
            fieldCnt++;
        }
        if (proto.hasHelpRecord()) {
            this.getHelpRecord().mergeChangeFromSs(proto.getHelpRecord());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasAlreadyHelpPlayerIds()) {
            this.getAlreadyHelpPlayerIds().mergeChangeFromSs(proto.getAlreadyHelpPlayerIds());
            fieldCnt++;
        }
        if (proto.hasTargetLevel()) {
            this.setTargetLevel(proto.getTargetLevel());
            fieldCnt++;
        }
        if (proto.hasBuildId()) {
            this.setBuildId(proto.getBuildId());
            fieldCnt++;
        }
        if (proto.hasCreateTsMs()) {
            this.setCreateTsMs(proto.getCreateTsMs());
            fieldCnt++;
        }
        if (proto.hasIsCanceled()) {
            this.setIsCanceled(proto.getIsCanceled());
            fieldCnt++;
        }
        if (proto.hasHelpPlayerName()) {
            this.setHelpPlayerName(proto.getHelpPlayerName());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanHelpItem.Builder builder = ClanHelpItem.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_HELPRECORD) && this.helpRecord != null) {
            this.helpRecord.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ALREADYHELPPLAYERIDS) && this.alreadyHelpPlayerIds != null) {
            this.alreadyHelpPlayerIds.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.helpRecord != null) {
            this.helpRecord.markAll();
        }
        if (this.alreadyHelpPlayerIds != null) {
            this.alreadyHelpPlayerIds.markAll();
        }
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanHelpItemProp)) {
            return false;
        }
        final ClanHelpItemProp otherNode = (ClanHelpItemProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.helpType != otherNode.helpType) {
            return false;
        }
        if (this.alreadyHelpTimes != otherNode.alreadyHelpTimes) {
            return false;
        }
        if (this.totalHelpTimes != otherNode.totalHelpTimes) {
            return false;
        }
        if (this.totalReductionTime != otherNode.totalReductionTime) {
            return false;
        }
        if (!this.getHelpRecord().compareDataTo(otherNode.getHelpRecord())) {
            return false;
        }
        if (this.playerId != otherNode.playerId) {
            return false;
        }
        if (!this.getAlreadyHelpPlayerIds().compareDataTo(otherNode.getAlreadyHelpPlayerIds())) {
            return false;
        }
        if (this.targetLevel != otherNode.targetLevel) {
            return false;
        }
        if (this.buildId != otherNode.buildId) {
            return false;
        }
        if (this.createTsMs != otherNode.createTsMs) {
            return false;
        }
        if (this.isCanceled != otherNode.isCanceled) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.helpPlayerName, otherNode.helpPlayerName)) {
            return false;
        }
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 50;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructPlayer.QueueExtraParam;
import com.yorha.proto.StructPlayerPB.QueueExtraParamPB;


/**
 * <AUTHOR> auto gen
 */
public class QueueExtraParamProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ISSOLDIERLEVELUP = 0;
    public static final int FIELD_INDEX_ISSOLDIERRUSH = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private boolean isSoldierLevelup = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean isSoldierRush = Constant.DEFAULT_BOOLEAN_VALUE;

    public QueueExtraParamProp() {
        super(null, 0, FIELD_COUNT);
    }

    public QueueExtraParamProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get isSoldierLevelup
     *
     * @return isSoldierLevelup value
     */
    public boolean getIsSoldierLevelup() {
        return this.isSoldierLevelup;
    }

    /**
     * set isSoldierLevelup && set marked
     *
     * @param isSoldierLevelup new value
     * @return current object
     */
    public QueueExtraParamProp setIsSoldierLevelup(boolean isSoldierLevelup) {
        if (this.isSoldierLevelup != isSoldierLevelup) {
            this.mark(FIELD_INDEX_ISSOLDIERLEVELUP);
            this.isSoldierLevelup = isSoldierLevelup;
        }
        return this;
    }

    /**
     * inner set isSoldierLevelup
     *
     * @param isSoldierLevelup new value
     */
    private void innerSetIsSoldierLevelup(boolean isSoldierLevelup) {
        this.isSoldierLevelup = isSoldierLevelup;
    }

    /**
     * get isSoldierRush
     *
     * @return isSoldierRush value
     */
    public boolean getIsSoldierRush() {
        return this.isSoldierRush;
    }

    /**
     * set isSoldierRush && set marked
     *
     * @param isSoldierRush new value
     * @return current object
     */
    public QueueExtraParamProp setIsSoldierRush(boolean isSoldierRush) {
        if (this.isSoldierRush != isSoldierRush) {
            this.mark(FIELD_INDEX_ISSOLDIERRUSH);
            this.isSoldierRush = isSoldierRush;
        }
        return this;
    }

    /**
     * inner set isSoldierRush
     *
     * @param isSoldierRush new value
     */
    private void innerSetIsSoldierRush(boolean isSoldierRush) {
        this.isSoldierRush = isSoldierRush;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public QueueExtraParamPB.Builder getCopyCsBuilder() {
        final QueueExtraParamPB.Builder builder = QueueExtraParamPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(QueueExtraParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIsSoldierLevelup()) {
            builder.setIsSoldierLevelup(this.getIsSoldierLevelup());
            fieldCnt++;
        }  else if (builder.hasIsSoldierLevelup()) {
            // 清理IsSoldierLevelup
            builder.clearIsSoldierLevelup();
            fieldCnt++;
        }
        if (this.getIsSoldierRush()) {
            builder.setIsSoldierRush(this.getIsSoldierRush());
            fieldCnt++;
        }  else if (builder.hasIsSoldierRush()) {
            // 清理IsSoldierRush
            builder.clearIsSoldierRush();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(QueueExtraParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISSOLDIERLEVELUP)) {
            builder.setIsSoldierLevelup(this.getIsSoldierLevelup());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISSOLDIERRUSH)) {
            builder.setIsSoldierRush(this.getIsSoldierRush());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(QueueExtraParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISSOLDIERLEVELUP)) {
            builder.setIsSoldierLevelup(this.getIsSoldierLevelup());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISSOLDIERRUSH)) {
            builder.setIsSoldierRush(this.getIsSoldierRush());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(QueueExtraParamPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIsSoldierLevelup()) {
            this.innerSetIsSoldierLevelup(proto.getIsSoldierLevelup());
        } else {
            this.innerSetIsSoldierLevelup(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasIsSoldierRush()) {
            this.innerSetIsSoldierRush(proto.getIsSoldierRush());
        } else {
            this.innerSetIsSoldierRush(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return QueueExtraParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(QueueExtraParamPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIsSoldierLevelup()) {
            this.setIsSoldierLevelup(proto.getIsSoldierLevelup());
            fieldCnt++;
        }
        if (proto.hasIsSoldierRush()) {
            this.setIsSoldierRush(proto.getIsSoldierRush());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public QueueExtraParam.Builder getCopyDbBuilder() {
        final QueueExtraParam.Builder builder = QueueExtraParam.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(QueueExtraParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIsSoldierLevelup()) {
            builder.setIsSoldierLevelup(this.getIsSoldierLevelup());
            fieldCnt++;
        }  else if (builder.hasIsSoldierLevelup()) {
            // 清理IsSoldierLevelup
            builder.clearIsSoldierLevelup();
            fieldCnt++;
        }
        if (this.getIsSoldierRush()) {
            builder.setIsSoldierRush(this.getIsSoldierRush());
            fieldCnt++;
        }  else if (builder.hasIsSoldierRush()) {
            // 清理IsSoldierRush
            builder.clearIsSoldierRush();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(QueueExtraParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISSOLDIERLEVELUP)) {
            builder.setIsSoldierLevelup(this.getIsSoldierLevelup());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISSOLDIERRUSH)) {
            builder.setIsSoldierRush(this.getIsSoldierRush());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(QueueExtraParam proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIsSoldierLevelup()) {
            this.innerSetIsSoldierLevelup(proto.getIsSoldierLevelup());
        } else {
            this.innerSetIsSoldierLevelup(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasIsSoldierRush()) {
            this.innerSetIsSoldierRush(proto.getIsSoldierRush());
        } else {
            this.innerSetIsSoldierRush(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return QueueExtraParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(QueueExtraParam proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIsSoldierLevelup()) {
            this.setIsSoldierLevelup(proto.getIsSoldierLevelup());
            fieldCnt++;
        }
        if (proto.hasIsSoldierRush()) {
            this.setIsSoldierRush(proto.getIsSoldierRush());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public QueueExtraParam.Builder getCopySsBuilder() {
        final QueueExtraParam.Builder builder = QueueExtraParam.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(QueueExtraParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIsSoldierLevelup()) {
            builder.setIsSoldierLevelup(this.getIsSoldierLevelup());
            fieldCnt++;
        }  else if (builder.hasIsSoldierLevelup()) {
            // 清理IsSoldierLevelup
            builder.clearIsSoldierLevelup();
            fieldCnt++;
        }
        if (this.getIsSoldierRush()) {
            builder.setIsSoldierRush(this.getIsSoldierRush());
            fieldCnt++;
        }  else if (builder.hasIsSoldierRush()) {
            // 清理IsSoldierRush
            builder.clearIsSoldierRush();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(QueueExtraParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISSOLDIERLEVELUP)) {
            builder.setIsSoldierLevelup(this.getIsSoldierLevelup());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISSOLDIERRUSH)) {
            builder.setIsSoldierRush(this.getIsSoldierRush());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(QueueExtraParam proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIsSoldierLevelup()) {
            this.innerSetIsSoldierLevelup(proto.getIsSoldierLevelup());
        } else {
            this.innerSetIsSoldierLevelup(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasIsSoldierRush()) {
            this.innerSetIsSoldierRush(proto.getIsSoldierRush());
        } else {
            this.innerSetIsSoldierRush(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return QueueExtraParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(QueueExtraParam proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIsSoldierLevelup()) {
            this.setIsSoldierLevelup(proto.getIsSoldierLevelup());
            fieldCnt++;
        }
        if (proto.hasIsSoldierRush()) {
            this.setIsSoldierRush(proto.getIsSoldierRush());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        QueueExtraParam.Builder builder = QueueExtraParam.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof QueueExtraParamProp)) {
            return false;
        }
        final QueueExtraParamProp otherNode = (QueueExtraParamProp) node;
        if (this.isSoldierLevelup != otherNode.isSoldierLevelup) {
            return false;
        }
        if (this.isSoldierRush != otherNode.isSoldierRush) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
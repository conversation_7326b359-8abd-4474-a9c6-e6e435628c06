package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructPB.SimpleHeroListPB;
import com.yorha.proto.Struct.SimpleHeroList;
import com.yorha.proto.StructPB.SimpleHeroPB;
import com.yorha.proto.Struct.SimpleHero;

/**
 * <AUTHOR> auto gen
 */
public class SimpleHeroListProp extends AbstractListNode<SimpleHeroProp> {
    /**
     * Create a SimpleHeroListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public SimpleHeroListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to SimpleHeroListProp
     *
     * @return new object
     */
    @Override
    public SimpleHeroProp addEmptyValue() {
        final SimpleHeroProp newProp = new SimpleHeroProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SimpleHeroListPB.Builder getCopyCsBuilder() {
        final SimpleHeroListPB.Builder builder = SimpleHeroListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(SimpleHeroListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return SimpleHeroListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final SimpleHeroProp v : this) {
            final SimpleHeroPB.Builder itemBuilder = SimpleHeroPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return SimpleHeroListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(SimpleHeroListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return SimpleHeroListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(SimpleHeroListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (SimpleHeroPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return SimpleHeroListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(SimpleHeroListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SimpleHeroList.Builder getCopyDbBuilder() {
        final SimpleHeroList.Builder builder = SimpleHeroList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(SimpleHeroList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return SimpleHeroListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final SimpleHeroProp v : this) {
            final SimpleHero.Builder itemBuilder = SimpleHero.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return SimpleHeroListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(SimpleHeroList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return SimpleHeroListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(SimpleHeroList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (SimpleHero v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return SimpleHeroListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(SimpleHeroList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SimpleHeroList.Builder getCopySsBuilder() {
        final SimpleHeroList.Builder builder = SimpleHeroList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(SimpleHeroList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return SimpleHeroListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final SimpleHeroProp v : this) {
            final SimpleHero.Builder itemBuilder = SimpleHero.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return SimpleHeroListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(SimpleHeroList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return SimpleHeroListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(SimpleHeroList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (SimpleHero v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return SimpleHeroListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(SimpleHeroList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        SimpleHeroList.Builder builder = SimpleHeroList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
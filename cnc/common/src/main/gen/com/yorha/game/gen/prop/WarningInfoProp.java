package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructPlayer.WarningInfo;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.StructPlayerPB.WarningInfoPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class WarningInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ARMYID = 0;
    public static final int FIELD_INDEX_TYPE = 1;
    public static final int FIELD_INDEX_ENTITYTYPE = 2;
    public static final int FIELD_INDEX_TARGETID = 3;
    public static final int FIELD_INDEX_TARGETPLAYERNAME = 4;
    public static final int FIELD_INDEX_TARGETCLANSIMPLENAME = 5;
    public static final int FIELD_INDEX_PLAYERNAME = 6;
    public static final int FIELD_INDEX_CLANSIMPLENAME = 7;
    public static final int FIELD_INDEX_ARRIVETS = 8;
    public static final int FIELD_INDEX_TROOP = 9;
    public static final int FIELD_INDEX_SPYTYPE = 10;
    public static final int FIELD_INDEX_TEMPLATEID = 11;
    public static final int FIELD_INDEX_RESOURCES = 12;

    public static final int FIELD_COUNT = 13;

    private long markBits0 = 0L;

    private long armyId = Constant.DEFAULT_LONG_VALUE;
    private WarningType type = WarningType.forNumber(0);
    private int entityType = Constant.DEFAULT_INT_VALUE;
    private long targetId = Constant.DEFAULT_LONG_VALUE;
    private String targetPlayerName = Constant.DEFAULT_STR_VALUE;
    private String targetClanSimpleName = Constant.DEFAULT_STR_VALUE;
    private String playerName = Constant.DEFAULT_STR_VALUE;
    private String clanSimpleName = Constant.DEFAULT_STR_VALUE;
    private long arriveTs = Constant.DEFAULT_LONG_VALUE;
    private TroopProp troop = null;
    private SpyType spyType = SpyType.forNumber(0);
    private long templateId = Constant.DEFAULT_LONG_VALUE;
    private CurrencyListProp resources = null;

    public WarningInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public WarningInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get armyId
     *
     * @return armyId value
     */
    public long getArmyId() {
        return this.armyId;
    }

    /**
     * set armyId && set marked
     *
     * @param armyId new value
     * @return current object
     */
    public WarningInfoProp setArmyId(long armyId) {
        if (this.armyId != armyId) {
            this.mark(FIELD_INDEX_ARMYID);
            this.armyId = armyId;
        }
        return this;
    }

    /**
     * inner set armyId
     *
     * @param armyId new value
     */
    private void innerSetArmyId(long armyId) {
        this.armyId = armyId;
    }

    /**
     * get type
     *
     * @return type value
     */
    public WarningType getType() {
        return this.type;
    }

    /**
     * set type && set marked
     *
     * @param type new value
     * @return current object
     */
    public WarningInfoProp setType(WarningType type) {
        if (type == null) {
            throw new NullPointerException();
        }
        if (this.type != type) {
            this.mark(FIELD_INDEX_TYPE);
            this.type = type;
        }
        return this;
    }

    /**
     * inner set type
     *
     * @param type new value
     */
    private void innerSetType(WarningType type) {
        this.type = type;
    }

    /**
     * get entityType
     *
     * @return entityType value
     */
    public int getEntityType() {
        return this.entityType;
    }

    /**
     * set entityType && set marked
     *
     * @param entityType new value
     * @return current object
     */
    public WarningInfoProp setEntityType(int entityType) {
        if (this.entityType != entityType) {
            this.mark(FIELD_INDEX_ENTITYTYPE);
            this.entityType = entityType;
        }
        return this;
    }

    /**
     * inner set entityType
     *
     * @param entityType new value
     */
    private void innerSetEntityType(int entityType) {
        this.entityType = entityType;
    }

    /**
     * get targetId
     *
     * @return targetId value
     */
    public long getTargetId() {
        return this.targetId;
    }

    /**
     * set targetId && set marked
     *
     * @param targetId new value
     * @return current object
     */
    public WarningInfoProp setTargetId(long targetId) {
        if (this.targetId != targetId) {
            this.mark(FIELD_INDEX_TARGETID);
            this.targetId = targetId;
        }
        return this;
    }

    /**
     * inner set targetId
     *
     * @param targetId new value
     */
    private void innerSetTargetId(long targetId) {
        this.targetId = targetId;
    }

    /**
     * get targetPlayerName
     *
     * @return targetPlayerName value
     */
    public String getTargetPlayerName() {
        return this.targetPlayerName;
    }

    /**
     * set targetPlayerName && set marked
     *
     * @param targetPlayerName new value
     * @return current object
     */
    public WarningInfoProp setTargetPlayerName(String targetPlayerName) {
        if (targetPlayerName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.targetPlayerName, targetPlayerName)) {
            this.mark(FIELD_INDEX_TARGETPLAYERNAME);
            this.targetPlayerName = targetPlayerName;
        }
        return this;
    }

    /**
     * inner set targetPlayerName
     *
     * @param targetPlayerName new value
     */
    private void innerSetTargetPlayerName(String targetPlayerName) {
        this.targetPlayerName = targetPlayerName;
    }

    /**
     * get targetClanSimpleName
     *
     * @return targetClanSimpleName value
     */
    public String getTargetClanSimpleName() {
        return this.targetClanSimpleName;
    }

    /**
     * set targetClanSimpleName && set marked
     *
     * @param targetClanSimpleName new value
     * @return current object
     */
    public WarningInfoProp setTargetClanSimpleName(String targetClanSimpleName) {
        if (targetClanSimpleName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.targetClanSimpleName, targetClanSimpleName)) {
            this.mark(FIELD_INDEX_TARGETCLANSIMPLENAME);
            this.targetClanSimpleName = targetClanSimpleName;
        }
        return this;
    }

    /**
     * inner set targetClanSimpleName
     *
     * @param targetClanSimpleName new value
     */
    private void innerSetTargetClanSimpleName(String targetClanSimpleName) {
        this.targetClanSimpleName = targetClanSimpleName;
    }

    /**
     * get playerName
     *
     * @return playerName value
     */
    public String getPlayerName() {
        return this.playerName;
    }

    /**
     * set playerName && set marked
     *
     * @param playerName new value
     * @return current object
     */
    public WarningInfoProp setPlayerName(String playerName) {
        if (playerName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.playerName, playerName)) {
            this.mark(FIELD_INDEX_PLAYERNAME);
            this.playerName = playerName;
        }
        return this;
    }

    /**
     * inner set playerName
     *
     * @param playerName new value
     */
    private void innerSetPlayerName(String playerName) {
        this.playerName = playerName;
    }

    /**
     * get clanSimpleName
     *
     * @return clanSimpleName value
     */
    public String getClanSimpleName() {
        return this.clanSimpleName;
    }

    /**
     * set clanSimpleName && set marked
     *
     * @param clanSimpleName new value
     * @return current object
     */
    public WarningInfoProp setClanSimpleName(String clanSimpleName) {
        if (clanSimpleName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanSimpleName, clanSimpleName)) {
            this.mark(FIELD_INDEX_CLANSIMPLENAME);
            this.clanSimpleName = clanSimpleName;
        }
        return this;
    }

    /**
     * inner set clanSimpleName
     *
     * @param clanSimpleName new value
     */
    private void innerSetClanSimpleName(String clanSimpleName) {
        this.clanSimpleName = clanSimpleName;
    }

    /**
     * get arriveTs
     *
     * @return arriveTs value
     */
    public long getArriveTs() {
        return this.arriveTs;
    }

    /**
     * set arriveTs && set marked
     *
     * @param arriveTs new value
     * @return current object
     */
    public WarningInfoProp setArriveTs(long arriveTs) {
        if (this.arriveTs != arriveTs) {
            this.mark(FIELD_INDEX_ARRIVETS);
            this.arriveTs = arriveTs;
        }
        return this;
    }

    /**
     * inner set arriveTs
     *
     * @param arriveTs new value
     */
    private void innerSetArriveTs(long arriveTs) {
        this.arriveTs = arriveTs;
    }

    /**
     * get troop
     *
     * @return troop value
     */
    public TroopProp getTroop() {
        if (this.troop == null) {
            this.troop = new TroopProp(this, FIELD_INDEX_TROOP);
        }
        return this.troop;
    }

    /**
     * get spyType
     *
     * @return spyType value
     */
    public SpyType getSpyType() {
        return this.spyType;
    }

    /**
     * set spyType && set marked
     *
     * @param spyType new value
     * @return current object
     */
    public WarningInfoProp setSpyType(SpyType spyType) {
        if (spyType == null) {
            throw new NullPointerException();
        }
        if (this.spyType != spyType) {
            this.mark(FIELD_INDEX_SPYTYPE);
            this.spyType = spyType;
        }
        return this;
    }

    /**
     * inner set spyType
     *
     * @param spyType new value
     */
    private void innerSetSpyType(SpyType spyType) {
        this.spyType = spyType;
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public long getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public WarningInfoProp setTemplateId(long templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(long templateId) {
        this.templateId = templateId;
    }

    /**
     * get resources
     *
     * @return resources value
     */
    public CurrencyListProp getResources() {
        if (this.resources == null) {
            this.resources = new CurrencyListProp(this, FIELD_INDEX_RESOURCES);
        }
        return this.resources;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addResources(CurrencyProp v) {
        this.getResources().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public CurrencyProp getResourcesIndex(int index) {
        return this.getResources().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public CurrencyProp removeResources(CurrencyProp v) {
        if (this.resources == null) {
            return null;
        }
        if(this.resources.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getResourcesSize() {
        if (this.resources == null) {
            return 0;
        }
        return this.resources.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isResourcesEmpty() {
        if (this.resources == null) {
            return true;
        }
        return this.getResources().isEmpty();
    }

    /**
     * clear list
     */
    public void clearResources() {
        this.getResources().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public CurrencyProp removeResourcesIndex(int index) {
        return this.getResources().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public CurrencyProp setResourcesIndex(int index, CurrencyProp v) {
        return this.getResources().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public WarningInfoPB.Builder getCopyCsBuilder() {
        final WarningInfoPB.Builder builder = WarningInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(WarningInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getArmyId() != 0L) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }  else if (builder.hasArmyId()) {
            // 清理ArmyId
            builder.clearArmyId();
            fieldCnt++;
        }
        if (this.getType() != WarningType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getEntityType() != 0) {
            builder.setEntityType(this.getEntityType());
            fieldCnt++;
        }  else if (builder.hasEntityType()) {
            // 清理EntityType
            builder.clearEntityType();
            fieldCnt++;
        }
        if (this.getTargetId() != 0L) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }  else if (builder.hasTargetId()) {
            // 清理TargetId
            builder.clearTargetId();
            fieldCnt++;
        }
        if (!this.getTargetPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTargetPlayerName(this.getTargetPlayerName());
            fieldCnt++;
        }  else if (builder.hasTargetPlayerName()) {
            // 清理TargetPlayerName
            builder.clearTargetPlayerName();
            fieldCnt++;
        }
        if (!this.getTargetClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTargetClanSimpleName(this.getTargetClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasTargetClanSimpleName()) {
            // 清理TargetClanSimpleName
            builder.clearTargetClanSimpleName();
            fieldCnt++;
        }
        if (!this.getPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setPlayerName(this.getPlayerName());
            fieldCnt++;
        }  else if (builder.hasPlayerName()) {
            // 清理PlayerName
            builder.clearPlayerName();
            fieldCnt++;
        }
        if (!this.getClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasClanSimpleName()) {
            // 清理ClanSimpleName
            builder.clearClanSimpleName();
            fieldCnt++;
        }
        if (this.getArriveTs() != 0L) {
            builder.setArriveTs(this.getArriveTs());
            fieldCnt++;
        }  else if (builder.hasArriveTs()) {
            // 清理ArriveTs
            builder.clearArriveTs();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayerPB.TroopPB.Builder tmpBuilder = StructPlayerPB.TroopPB.newBuilder();
            final int tmpFieldCnt = this.troop.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.getSpyType() != SpyType.forNumber(0)) {
            builder.setSpyType(this.getSpyType());
            fieldCnt++;
        }  else if (builder.hasSpyType()) {
            // 清理SpyType
            builder.clearSpyType();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0L) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.resources != null) {
            StructPB.CurrencyListPB.Builder tmpBuilder = StructPB.CurrencyListPB.newBuilder();
            final int tmpFieldCnt = this.resources.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResources(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResources();
            }
        }  else if (builder.hasResources()) {
            // 清理Resources
            builder.clearResources();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(WarningInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTITYTYPE)) {
            builder.setEntityType(this.getEntityType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETPLAYERNAME)) {
            builder.setTargetPlayerName(this.getTargetPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETCLANSIMPLENAME)) {
            builder.setTargetClanSimpleName(this.getTargetClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERNAME)) {
            builder.setPlayerName(this.getPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ARRIVETS)) {
            builder.setArriveTs(this.getArriveTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToCs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYTYPE)) {
            builder.setSpyType(this.getSpyType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            final boolean needClear = !builder.hasResources();
            final int tmpFieldCnt = this.resources.copyChangeToCs(builder.getResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResources();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(WarningInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTITYTYPE)) {
            builder.setEntityType(this.getEntityType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETPLAYERNAME)) {
            builder.setTargetPlayerName(this.getTargetPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETCLANSIMPLENAME)) {
            builder.setTargetClanSimpleName(this.getTargetClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERNAME)) {
            builder.setPlayerName(this.getPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ARRIVETS)) {
            builder.setArriveTs(this.getArriveTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToAndClearDeleteKeysCs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYTYPE)) {
            builder.setSpyType(this.getSpyType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            final boolean needClear = !builder.hasResources();
            final int tmpFieldCnt = this.resources.copyChangeToCs(builder.getResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResources();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(WarningInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasArmyId()) {
            this.innerSetArmyId(proto.getArmyId());
        } else {
            this.innerSetArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(WarningType.forNumber(0));
        }
        if (proto.hasEntityType()) {
            this.innerSetEntityType(proto.getEntityType());
        } else {
            this.innerSetEntityType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTargetId()) {
            this.innerSetTargetId(proto.getTargetId());
        } else {
            this.innerSetTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTargetPlayerName()) {
            this.innerSetTargetPlayerName(proto.getTargetPlayerName());
        } else {
            this.innerSetTargetPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTargetClanSimpleName()) {
            this.innerSetTargetClanSimpleName(proto.getTargetClanSimpleName());
        } else {
            this.innerSetTargetClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasPlayerName()) {
            this.innerSetPlayerName(proto.getPlayerName());
        } else {
            this.innerSetPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanSimpleName()) {
            this.innerSetClanSimpleName(proto.getClanSimpleName());
        } else {
            this.innerSetClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasArriveTs()) {
            this.innerSetArriveTs(proto.getArriveTs());
        } else {
            this.innerSetArriveTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromCs(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromCs(proto.getTroop());
            }
        }
        if (proto.hasSpyType()) {
            this.innerSetSpyType(proto.getSpyType());
        } else {
            this.innerSetSpyType(SpyType.forNumber(0));
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasResources()) {
            this.getResources().mergeFromCs(proto.getResources());
        } else {
            if (this.resources != null) {
                this.resources.mergeFromCs(proto.getResources());
            }
        }
        this.markAll();
        return WarningInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(WarningInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasArmyId()) {
            this.setArmyId(proto.getArmyId());
            fieldCnt++;
        }
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasEntityType()) {
            this.setEntityType(proto.getEntityType());
            fieldCnt++;
        }
        if (proto.hasTargetId()) {
            this.setTargetId(proto.getTargetId());
            fieldCnt++;
        }
        if (proto.hasTargetPlayerName()) {
            this.setTargetPlayerName(proto.getTargetPlayerName());
            fieldCnt++;
        }
        if (proto.hasTargetClanSimpleName()) {
            this.setTargetClanSimpleName(proto.getTargetClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasPlayerName()) {
            this.setPlayerName(proto.getPlayerName());
            fieldCnt++;
        }
        if (proto.hasClanSimpleName()) {
            this.setClanSimpleName(proto.getClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasArriveTs()) {
            this.setArriveTs(proto.getArriveTs());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromCs(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasSpyType()) {
            this.setSpyType(proto.getSpyType());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasResources()) {
            this.getResources().mergeChangeFromCs(proto.getResources());
            fieldCnt++;
        }
        return fieldCnt;
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public WarningInfo.Builder getCopySsBuilder() {
        final WarningInfo.Builder builder = WarningInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(WarningInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getArmyId() != 0L) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }  else if (builder.hasArmyId()) {
            // 清理ArmyId
            builder.clearArmyId();
            fieldCnt++;
        }
        if (this.getType() != WarningType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getEntityType() != 0) {
            builder.setEntityType(this.getEntityType());
            fieldCnt++;
        }  else if (builder.hasEntityType()) {
            // 清理EntityType
            builder.clearEntityType();
            fieldCnt++;
        }
        if (this.getTargetId() != 0L) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }  else if (builder.hasTargetId()) {
            // 清理TargetId
            builder.clearTargetId();
            fieldCnt++;
        }
        if (!this.getTargetPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTargetPlayerName(this.getTargetPlayerName());
            fieldCnt++;
        }  else if (builder.hasTargetPlayerName()) {
            // 清理TargetPlayerName
            builder.clearTargetPlayerName();
            fieldCnt++;
        }
        if (!this.getTargetClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTargetClanSimpleName(this.getTargetClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasTargetClanSimpleName()) {
            // 清理TargetClanSimpleName
            builder.clearTargetClanSimpleName();
            fieldCnt++;
        }
        if (!this.getPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setPlayerName(this.getPlayerName());
            fieldCnt++;
        }  else if (builder.hasPlayerName()) {
            // 清理PlayerName
            builder.clearPlayerName();
            fieldCnt++;
        }
        if (!this.getClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasClanSimpleName()) {
            // 清理ClanSimpleName
            builder.clearClanSimpleName();
            fieldCnt++;
        }
        if (this.getArriveTs() != 0L) {
            builder.setArriveTs(this.getArriveTs());
            fieldCnt++;
        }  else if (builder.hasArriveTs()) {
            // 清理ArriveTs
            builder.clearArriveTs();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayer.Troop.Builder tmpBuilder = StructPlayer.Troop.newBuilder();
            final int tmpFieldCnt = this.troop.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.getSpyType() != SpyType.forNumber(0)) {
            builder.setSpyType(this.getSpyType());
            fieldCnt++;
        }  else if (builder.hasSpyType()) {
            // 清理SpyType
            builder.clearSpyType();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0L) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.resources != null) {
            Struct.CurrencyList.Builder tmpBuilder = Struct.CurrencyList.newBuilder();
            final int tmpFieldCnt = this.resources.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResources(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResources();
            }
        }  else if (builder.hasResources()) {
            // 清理Resources
            builder.clearResources();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(WarningInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTITYTYPE)) {
            builder.setEntityType(this.getEntityType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETPLAYERNAME)) {
            builder.setTargetPlayerName(this.getTargetPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETCLANSIMPLENAME)) {
            builder.setTargetClanSimpleName(this.getTargetClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERNAME)) {
            builder.setPlayerName(this.getPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ARRIVETS)) {
            builder.setArriveTs(this.getArriveTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToSs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYTYPE)) {
            builder.setSpyType(this.getSpyType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            final boolean needClear = !builder.hasResources();
            final int tmpFieldCnt = this.resources.copyChangeToSs(builder.getResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResources();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(WarningInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasArmyId()) {
            this.innerSetArmyId(proto.getArmyId());
        } else {
            this.innerSetArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(WarningType.forNumber(0));
        }
        if (proto.hasEntityType()) {
            this.innerSetEntityType(proto.getEntityType());
        } else {
            this.innerSetEntityType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTargetId()) {
            this.innerSetTargetId(proto.getTargetId());
        } else {
            this.innerSetTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTargetPlayerName()) {
            this.innerSetTargetPlayerName(proto.getTargetPlayerName());
        } else {
            this.innerSetTargetPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTargetClanSimpleName()) {
            this.innerSetTargetClanSimpleName(proto.getTargetClanSimpleName());
        } else {
            this.innerSetTargetClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasPlayerName()) {
            this.innerSetPlayerName(proto.getPlayerName());
        } else {
            this.innerSetPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanSimpleName()) {
            this.innerSetClanSimpleName(proto.getClanSimpleName());
        } else {
            this.innerSetClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasArriveTs()) {
            this.innerSetArriveTs(proto.getArriveTs());
        } else {
            this.innerSetArriveTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromSs(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromSs(proto.getTroop());
            }
        }
        if (proto.hasSpyType()) {
            this.innerSetSpyType(proto.getSpyType());
        } else {
            this.innerSetSpyType(SpyType.forNumber(0));
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasResources()) {
            this.getResources().mergeFromSs(proto.getResources());
        } else {
            if (this.resources != null) {
                this.resources.mergeFromSs(proto.getResources());
            }
        }
        this.markAll();
        return WarningInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(WarningInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasArmyId()) {
            this.setArmyId(proto.getArmyId());
            fieldCnt++;
        }
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasEntityType()) {
            this.setEntityType(proto.getEntityType());
            fieldCnt++;
        }
        if (proto.hasTargetId()) {
            this.setTargetId(proto.getTargetId());
            fieldCnt++;
        }
        if (proto.hasTargetPlayerName()) {
            this.setTargetPlayerName(proto.getTargetPlayerName());
            fieldCnt++;
        }
        if (proto.hasTargetClanSimpleName()) {
            this.setTargetClanSimpleName(proto.getTargetClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasPlayerName()) {
            this.setPlayerName(proto.getPlayerName());
            fieldCnt++;
        }
        if (proto.hasClanSimpleName()) {
            this.setClanSimpleName(proto.getClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasArriveTs()) {
            this.setArriveTs(proto.getArriveTs());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromSs(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasSpyType()) {
            this.setSpyType(proto.getSpyType());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasResources()) {
            this.getResources().mergeChangeFromSs(proto.getResources());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        WarningInfo.Builder builder = WarningInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            this.troop.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            this.resources.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.troop != null) {
            this.troop.markAll();
        }
        if (this.resources != null) {
            this.resources.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof WarningInfoProp)) {
            return false;
        }
        final WarningInfoProp otherNode = (WarningInfoProp) node;
        if (this.armyId != otherNode.armyId) {
            return false;
        }
        if (this.type != otherNode.type) {
            return false;
        }
        if (this.entityType != otherNode.entityType) {
            return false;
        }
        if (this.targetId != otherNode.targetId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.targetPlayerName, otherNode.targetPlayerName)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.targetClanSimpleName, otherNode.targetClanSimpleName)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.playerName, otherNode.playerName)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanSimpleName, otherNode.clanSimpleName)) {
            return false;
        }
        if (this.arriveTs != otherNode.arriveTs) {
            return false;
        }
        if (!this.getTroop().compareDataTo(otherNode.getTroop())) {
            return false;
        }
        if (this.spyType != otherNode.spyType) {
            return false;
        }
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        if (!this.getResources().compareDataTo(otherNode.getResources())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 51;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
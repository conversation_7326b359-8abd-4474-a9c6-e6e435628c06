package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Struct.ClanGiftRecord;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ClanGiftRecordPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanGiftRecordProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TYPE = 0;
    public static final int FIELD_INDEX_DISPLAYDATA = 1;
    public static final int FIELD_INDEX_PLAYERID = 2;
    public static final int FIELD_INDEX_ITEMREWARD = 3;
    public static final int FIELD_INDEX_ISHIDDEN = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private ClanGiftRecordType type = ClanGiftRecordType.forNumber(0);
    private DisplayDataProp displayData = null;
    private long playerId = Constant.DEFAULT_LONG_VALUE;
    private ItemPairListProp itemReward = null;
    private boolean isHidden = Constant.DEFAULT_BOOLEAN_VALUE;

    public ClanGiftRecordProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanGiftRecordProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get type
     *
     * @return type value
     */
    public ClanGiftRecordType getType() {
        return this.type;
    }

    /**
     * set type && set marked
     *
     * @param type new value
     * @return current object
     */
    public ClanGiftRecordProp setType(ClanGiftRecordType type) {
        if (type == null) {
            throw new NullPointerException();
        }
        if (this.type != type) {
            this.mark(FIELD_INDEX_TYPE);
            this.type = type;
        }
        return this;
    }

    /**
     * inner set type
     *
     * @param type new value
     */
    private void innerSetType(ClanGiftRecordType type) {
        this.type = type;
    }

    /**
     * get displayData
     *
     * @return displayData value
     */
    public DisplayDataProp getDisplayData() {
        if (this.displayData == null) {
            this.displayData = new DisplayDataProp(this, FIELD_INDEX_DISPLAYDATA);
        }
        return this.displayData;
    }

    /**
     * get playerId
     *
     * @return playerId value
     */
    public long getPlayerId() {
        return this.playerId;
    }

    /**
     * set playerId && set marked
     *
     * @param playerId new value
     * @return current object
     */
    public ClanGiftRecordProp setPlayerId(long playerId) {
        if (this.playerId != playerId) {
            this.mark(FIELD_INDEX_PLAYERID);
            this.playerId = playerId;
        }
        return this;
    }

    /**
     * inner set playerId
     *
     * @param playerId new value
     */
    private void innerSetPlayerId(long playerId) {
        this.playerId = playerId;
    }

    /**
     * get itemReward
     *
     * @return itemReward value
     */
    public ItemPairListProp getItemReward() {
        if (this.itemReward == null) {
            this.itemReward = new ItemPairListProp(this, FIELD_INDEX_ITEMREWARD);
        }
        return this.itemReward;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addItemReward(ItemPairProp v) {
        this.getItemReward().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ItemPairProp getItemRewardIndex(int index) {
        return this.getItemReward().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public ItemPairProp removeItemReward(ItemPairProp v) {
        if (this.itemReward == null) {
            return null;
        }
        if(this.itemReward.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getItemRewardSize() {
        if (this.itemReward == null) {
            return 0;
        }
        return this.itemReward.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isItemRewardEmpty() {
        if (this.itemReward == null) {
            return true;
        }
        return this.getItemReward().isEmpty();
    }

    /**
     * clear list
     */
    public void clearItemReward() {
        this.getItemReward().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ItemPairProp removeItemRewardIndex(int index) {
        return this.getItemReward().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public ItemPairProp setItemRewardIndex(int index, ItemPairProp v) {
        return this.getItemReward().set(index, v);
    }
    /**
     * get isHidden
     *
     * @return isHidden value
     */
    public boolean getIsHidden() {
        return this.isHidden;
    }

    /**
     * set isHidden && set marked
     *
     * @param isHidden new value
     * @return current object
     */
    public ClanGiftRecordProp setIsHidden(boolean isHidden) {
        if (this.isHidden != isHidden) {
            this.mark(FIELD_INDEX_ISHIDDEN);
            this.isHidden = isHidden;
        }
        return this;
    }

    /**
     * inner set isHidden
     *
     * @param isHidden new value
     */
    private void innerSetIsHidden(boolean isHidden) {
        this.isHidden = isHidden;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanGiftRecordPB.Builder getCopyCsBuilder() {
        final ClanGiftRecordPB.Builder builder = ClanGiftRecordPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanGiftRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != ClanGiftRecordType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.displayData != null) {
            StructPB.DisplayDataPB.Builder tmpBuilder = StructPB.DisplayDataPB.newBuilder();
            final int tmpFieldCnt = this.displayData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDisplayData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDisplayData();
            }
        }  else if (builder.hasDisplayData()) {
            // 清理DisplayData
            builder.clearDisplayData();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.itemReward != null) {
            StructPB.ItemPairListPB.Builder tmpBuilder = StructPB.ItemPairListPB.newBuilder();
            final int tmpFieldCnt = this.itemReward.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItemReward(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItemReward();
            }
        }  else if (builder.hasItemReward()) {
            // 清理ItemReward
            builder.clearItemReward();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanGiftRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DISPLAYDATA) && this.displayData != null) {
            final boolean needClear = !builder.hasDisplayData();
            final int tmpFieldCnt = this.displayData.copyChangeToCs(builder.getDisplayDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDisplayData();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            final boolean needClear = !builder.hasItemReward();
            final int tmpFieldCnt = this.itemReward.copyChangeToCs(builder.getItemRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemReward();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanGiftRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DISPLAYDATA) && this.displayData != null) {
            final boolean needClear = !builder.hasDisplayData();
            final int tmpFieldCnt = this.displayData.copyChangeToAndClearDeleteKeysCs(builder.getDisplayDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDisplayData();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            final boolean needClear = !builder.hasItemReward();
            final int tmpFieldCnt = this.itemReward.copyChangeToCs(builder.getItemRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemReward();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanGiftRecordPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(ClanGiftRecordType.forNumber(0));
        }
        if (proto.hasDisplayData()) {
            this.getDisplayData().mergeFromCs(proto.getDisplayData());
        } else {
            if (this.displayData != null) {
                this.displayData.mergeFromCs(proto.getDisplayData());
            }
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeFromCs(proto.getItemReward());
        } else {
            if (this.itemReward != null) {
                this.itemReward.mergeFromCs(proto.getItemReward());
            }
        }
        this.markAll();
        return ClanGiftRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanGiftRecordPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasDisplayData()) {
            this.getDisplayData().mergeChangeFromCs(proto.getDisplayData());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeChangeFromCs(proto.getItemReward());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanGiftRecord.Builder getCopyDbBuilder() {
        final ClanGiftRecord.Builder builder = ClanGiftRecord.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanGiftRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != ClanGiftRecordType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.displayData != null) {
            Struct.DisplayData.Builder tmpBuilder = Struct.DisplayData.newBuilder();
            final int tmpFieldCnt = this.displayData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDisplayData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDisplayData();
            }
        }  else if (builder.hasDisplayData()) {
            // 清理DisplayData
            builder.clearDisplayData();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.itemReward != null) {
            Struct.ItemPairList.Builder tmpBuilder = Struct.ItemPairList.newBuilder();
            final int tmpFieldCnt = this.itemReward.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItemReward(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItemReward();
            }
        }  else if (builder.hasItemReward()) {
            // 清理ItemReward
            builder.clearItemReward();
            fieldCnt++;
        }
        if (this.getIsHidden()) {
            builder.setIsHidden(this.getIsHidden());
            fieldCnt++;
        }  else if (builder.hasIsHidden()) {
            // 清理IsHidden
            builder.clearIsHidden();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanGiftRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DISPLAYDATA) && this.displayData != null) {
            final boolean needClear = !builder.hasDisplayData();
            final int tmpFieldCnt = this.displayData.copyChangeToDb(builder.getDisplayDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDisplayData();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            final boolean needClear = !builder.hasItemReward();
            final int tmpFieldCnt = this.itemReward.copyChangeToDb(builder.getItemRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemReward();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISHIDDEN)) {
            builder.setIsHidden(this.getIsHidden());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanGiftRecord proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(ClanGiftRecordType.forNumber(0));
        }
        if (proto.hasDisplayData()) {
            this.getDisplayData().mergeFromDb(proto.getDisplayData());
        } else {
            if (this.displayData != null) {
                this.displayData.mergeFromDb(proto.getDisplayData());
            }
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeFromDb(proto.getItemReward());
        } else {
            if (this.itemReward != null) {
                this.itemReward.mergeFromDb(proto.getItemReward());
            }
        }
        if (proto.hasIsHidden()) {
            this.innerSetIsHidden(proto.getIsHidden());
        } else {
            this.innerSetIsHidden(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ClanGiftRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanGiftRecord proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasDisplayData()) {
            this.getDisplayData().mergeChangeFromDb(proto.getDisplayData());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeChangeFromDb(proto.getItemReward());
            fieldCnt++;
        }
        if (proto.hasIsHidden()) {
            this.setIsHidden(proto.getIsHidden());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanGiftRecord.Builder getCopySsBuilder() {
        final ClanGiftRecord.Builder builder = ClanGiftRecord.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanGiftRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != ClanGiftRecordType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.displayData != null) {
            Struct.DisplayData.Builder tmpBuilder = Struct.DisplayData.newBuilder();
            final int tmpFieldCnt = this.displayData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDisplayData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDisplayData();
            }
        }  else if (builder.hasDisplayData()) {
            // 清理DisplayData
            builder.clearDisplayData();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.itemReward != null) {
            Struct.ItemPairList.Builder tmpBuilder = Struct.ItemPairList.newBuilder();
            final int tmpFieldCnt = this.itemReward.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItemReward(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItemReward();
            }
        }  else if (builder.hasItemReward()) {
            // 清理ItemReward
            builder.clearItemReward();
            fieldCnt++;
        }
        if (this.getIsHidden()) {
            builder.setIsHidden(this.getIsHidden());
            fieldCnt++;
        }  else if (builder.hasIsHidden()) {
            // 清理IsHidden
            builder.clearIsHidden();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanGiftRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DISPLAYDATA) && this.displayData != null) {
            final boolean needClear = !builder.hasDisplayData();
            final int tmpFieldCnt = this.displayData.copyChangeToSs(builder.getDisplayDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDisplayData();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            final boolean needClear = !builder.hasItemReward();
            final int tmpFieldCnt = this.itemReward.copyChangeToSs(builder.getItemRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemReward();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISHIDDEN)) {
            builder.setIsHidden(this.getIsHidden());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanGiftRecord proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(ClanGiftRecordType.forNumber(0));
        }
        if (proto.hasDisplayData()) {
            this.getDisplayData().mergeFromSs(proto.getDisplayData());
        } else {
            if (this.displayData != null) {
                this.displayData.mergeFromSs(proto.getDisplayData());
            }
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeFromSs(proto.getItemReward());
        } else {
            if (this.itemReward != null) {
                this.itemReward.mergeFromSs(proto.getItemReward());
            }
        }
        if (proto.hasIsHidden()) {
            this.innerSetIsHidden(proto.getIsHidden());
        } else {
            this.innerSetIsHidden(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ClanGiftRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanGiftRecord proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasDisplayData()) {
            this.getDisplayData().mergeChangeFromSs(proto.getDisplayData());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeChangeFromSs(proto.getItemReward());
            fieldCnt++;
        }
        if (proto.hasIsHidden()) {
            this.setIsHidden(proto.getIsHidden());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanGiftRecord.Builder builder = ClanGiftRecord.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_DISPLAYDATA) && this.displayData != null) {
            this.displayData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            this.itemReward.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.displayData != null) {
            this.displayData.markAll();
        }
        if (this.itemReward != null) {
            this.itemReward.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanGiftRecordProp)) {
            return false;
        }
        final ClanGiftRecordProp otherNode = (ClanGiftRecordProp) node;
        if (this.type != otherNode.type) {
            return false;
        }
        if (!this.getDisplayData().compareDataTo(otherNode.getDisplayData())) {
            return false;
        }
        if (this.playerId != otherNode.playerId) {
            return false;
        }
        if (!this.getItemReward().compareDataTo(otherNode.getItemReward())) {
            return false;
        }
        if (this.isHidden != otherNode.isHidden) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Zone.ZoneSeasonModel;
import com.yorha.proto.ZonePB.ZoneSeasonModelPB;


/**
 * <AUTHOR> auto gen
 */
public class ZoneSeasonModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_SEASON = 0;
    public static final int FIELD_INDEX_PLAYERBORNINDEX = 1;
    public static final int FIELD_INDEX_STAGE = 2;
    public static final int FIELD_INDEX_STAGEENTERTSMS = 3;
    public static final int FIELD_INDEX_POOLSTARTZONE = 4;
    public static final int FIELD_INDEX_POOLENDZONE = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private ZoneSeason season = ZoneSeason.forNumber(0);
    private int playerBornIndex = Constant.DEFAULT_INT_VALUE;
    private ZoneSeasonStage stage = ZoneSeasonStage.forNumber(0);
    private long stageEnterTsMs = Constant.DEFAULT_LONG_VALUE;
    private int poolStartZone = Constant.DEFAULT_INT_VALUE;
    private int poolEndZone = Constant.DEFAULT_INT_VALUE;

    public ZoneSeasonModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ZoneSeasonModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get season
     *
     * @return season value
     */
    public ZoneSeason getSeason() {
        return this.season;
    }

    /**
     * set season && set marked
     *
     * @param season new value
     * @return current object
     */
    public ZoneSeasonModelProp setSeason(ZoneSeason season) {
        if (season == null) {
            throw new NullPointerException();
        }
        if (this.season != season) {
            this.mark(FIELD_INDEX_SEASON);
            this.season = season;
        }
        return this;
    }

    /**
     * inner set season
     *
     * @param season new value
     */
    private void innerSetSeason(ZoneSeason season) {
        this.season = season;
    }

    /**
     * get playerBornIndex
     *
     * @return playerBornIndex value
     */
    public int getPlayerBornIndex() {
        return this.playerBornIndex;
    }

    /**
     * set playerBornIndex && set marked
     *
     * @param playerBornIndex new value
     * @return current object
     */
    public ZoneSeasonModelProp setPlayerBornIndex(int playerBornIndex) {
        if (this.playerBornIndex != playerBornIndex) {
            this.mark(FIELD_INDEX_PLAYERBORNINDEX);
            this.playerBornIndex = playerBornIndex;
        }
        return this;
    }

    /**
     * inner set playerBornIndex
     *
     * @param playerBornIndex new value
     */
    private void innerSetPlayerBornIndex(int playerBornIndex) {
        this.playerBornIndex = playerBornIndex;
    }

    /**
     * get stage
     *
     * @return stage value
     */
    public ZoneSeasonStage getStage() {
        return this.stage;
    }

    /**
     * set stage && set marked
     *
     * @param stage new value
     * @return current object
     */
    public ZoneSeasonModelProp setStage(ZoneSeasonStage stage) {
        if (stage == null) {
            throw new NullPointerException();
        }
        if (this.stage != stage) {
            this.mark(FIELD_INDEX_STAGE);
            this.stage = stage;
        }
        return this;
    }

    /**
     * inner set stage
     *
     * @param stage new value
     */
    private void innerSetStage(ZoneSeasonStage stage) {
        this.stage = stage;
    }

    /**
     * get stageEnterTsMs
     *
     * @return stageEnterTsMs value
     */
    public long getStageEnterTsMs() {
        return this.stageEnterTsMs;
    }

    /**
     * set stageEnterTsMs && set marked
     *
     * @param stageEnterTsMs new value
     * @return current object
     */
    public ZoneSeasonModelProp setStageEnterTsMs(long stageEnterTsMs) {
        if (this.stageEnterTsMs != stageEnterTsMs) {
            this.mark(FIELD_INDEX_STAGEENTERTSMS);
            this.stageEnterTsMs = stageEnterTsMs;
        }
        return this;
    }

    /**
     * inner set stageEnterTsMs
     *
     * @param stageEnterTsMs new value
     */
    private void innerSetStageEnterTsMs(long stageEnterTsMs) {
        this.stageEnterTsMs = stageEnterTsMs;
    }

    /**
     * get poolStartZone
     *
     * @return poolStartZone value
     */
    public int getPoolStartZone() {
        return this.poolStartZone;
    }

    /**
     * set poolStartZone && set marked
     *
     * @param poolStartZone new value
     * @return current object
     */
    public ZoneSeasonModelProp setPoolStartZone(int poolStartZone) {
        if (this.poolStartZone != poolStartZone) {
            this.mark(FIELD_INDEX_POOLSTARTZONE);
            this.poolStartZone = poolStartZone;
        }
        return this;
    }

    /**
     * inner set poolStartZone
     *
     * @param poolStartZone new value
     */
    private void innerSetPoolStartZone(int poolStartZone) {
        this.poolStartZone = poolStartZone;
    }

    /**
     * get poolEndZone
     *
     * @return poolEndZone value
     */
    public int getPoolEndZone() {
        return this.poolEndZone;
    }

    /**
     * set poolEndZone && set marked
     *
     * @param poolEndZone new value
     * @return current object
     */
    public ZoneSeasonModelProp setPoolEndZone(int poolEndZone) {
        if (this.poolEndZone != poolEndZone) {
            this.mark(FIELD_INDEX_POOLENDZONE);
            this.poolEndZone = poolEndZone;
        }
        return this;
    }

    /**
     * inner set poolEndZone
     *
     * @param poolEndZone new value
     */
    private void innerSetPoolEndZone(int poolEndZone) {
        this.poolEndZone = poolEndZone;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSeasonModelPB.Builder getCopyCsBuilder() {
        final ZoneSeasonModelPB.Builder builder = ZoneSeasonModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ZoneSeasonModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSeason() != ZoneSeason.forNumber(0)) {
            builder.setSeason(this.getSeason());
            fieldCnt++;
        }  else if (builder.hasSeason()) {
            // 清理Season
            builder.clearSeason();
            fieldCnt++;
        }
        if (this.getPlayerBornIndex() != 0) {
            builder.setPlayerBornIndex(this.getPlayerBornIndex());
            fieldCnt++;
        }  else if (builder.hasPlayerBornIndex()) {
            // 清理PlayerBornIndex
            builder.clearPlayerBornIndex();
            fieldCnt++;
        }
        if (this.getStage() != ZoneSeasonStage.forNumber(0)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }  else if (builder.hasStage()) {
            // 清理Stage
            builder.clearStage();
            fieldCnt++;
        }
        if (this.getStageEnterTsMs() != 0L) {
            builder.setStageEnterTsMs(this.getStageEnterTsMs());
            fieldCnt++;
        }  else if (builder.hasStageEnterTsMs()) {
            // 清理StageEnterTsMs
            builder.clearStageEnterTsMs();
            fieldCnt++;
        }
        if (this.getPoolStartZone() != 0) {
            builder.setPoolStartZone(this.getPoolStartZone());
            fieldCnt++;
        }  else if (builder.hasPoolStartZone()) {
            // 清理PoolStartZone
            builder.clearPoolStartZone();
            fieldCnt++;
        }
        if (this.getPoolEndZone() != 0) {
            builder.setPoolEndZone(this.getPoolEndZone());
            fieldCnt++;
        }  else if (builder.hasPoolEndZone()) {
            // 清理PoolEndZone
            builder.clearPoolEndZone();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ZoneSeasonModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SEASON)) {
            builder.setSeason(this.getSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERBORNINDEX)) {
            builder.setPlayerBornIndex(this.getPlayerBornIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGE)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGEENTERTSMS)) {
            builder.setStageEnterTsMs(this.getStageEnterTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POOLSTARTZONE)) {
            builder.setPoolStartZone(this.getPoolStartZone());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POOLENDZONE)) {
            builder.setPoolEndZone(this.getPoolEndZone());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ZoneSeasonModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SEASON)) {
            builder.setSeason(this.getSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERBORNINDEX)) {
            builder.setPlayerBornIndex(this.getPlayerBornIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGE)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGEENTERTSMS)) {
            builder.setStageEnterTsMs(this.getStageEnterTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POOLSTARTZONE)) {
            builder.setPoolStartZone(this.getPoolStartZone());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POOLENDZONE)) {
            builder.setPoolEndZone(this.getPoolEndZone());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ZoneSeasonModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSeason()) {
            this.innerSetSeason(proto.getSeason());
        } else {
            this.innerSetSeason(ZoneSeason.forNumber(0));
        }
        if (proto.hasPlayerBornIndex()) {
            this.innerSetPlayerBornIndex(proto.getPlayerBornIndex());
        } else {
            this.innerSetPlayerBornIndex(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStage()) {
            this.innerSetStage(proto.getStage());
        } else {
            this.innerSetStage(ZoneSeasonStage.forNumber(0));
        }
        if (proto.hasStageEnterTsMs()) {
            this.innerSetStageEnterTsMs(proto.getStageEnterTsMs());
        } else {
            this.innerSetStageEnterTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPoolStartZone()) {
            this.innerSetPoolStartZone(proto.getPoolStartZone());
        } else {
            this.innerSetPoolStartZone(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPoolEndZone()) {
            this.innerSetPoolEndZone(proto.getPoolEndZone());
        } else {
            this.innerSetPoolEndZone(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ZoneSeasonModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ZoneSeasonModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSeason()) {
            this.setSeason(proto.getSeason());
            fieldCnt++;
        }
        if (proto.hasPlayerBornIndex()) {
            this.setPlayerBornIndex(proto.getPlayerBornIndex());
            fieldCnt++;
        }
        if (proto.hasStage()) {
            this.setStage(proto.getStage());
            fieldCnt++;
        }
        if (proto.hasStageEnterTsMs()) {
            this.setStageEnterTsMs(proto.getStageEnterTsMs());
            fieldCnt++;
        }
        if (proto.hasPoolStartZone()) {
            this.setPoolStartZone(proto.getPoolStartZone());
            fieldCnt++;
        }
        if (proto.hasPoolEndZone()) {
            this.setPoolEndZone(proto.getPoolEndZone());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSeasonModel.Builder getCopyDbBuilder() {
        final ZoneSeasonModel.Builder builder = ZoneSeasonModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ZoneSeasonModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSeason() != ZoneSeason.forNumber(0)) {
            builder.setSeason(this.getSeason());
            fieldCnt++;
        }  else if (builder.hasSeason()) {
            // 清理Season
            builder.clearSeason();
            fieldCnt++;
        }
        if (this.getPlayerBornIndex() != 0) {
            builder.setPlayerBornIndex(this.getPlayerBornIndex());
            fieldCnt++;
        }  else if (builder.hasPlayerBornIndex()) {
            // 清理PlayerBornIndex
            builder.clearPlayerBornIndex();
            fieldCnt++;
        }
        if (this.getStage() != ZoneSeasonStage.forNumber(0)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }  else if (builder.hasStage()) {
            // 清理Stage
            builder.clearStage();
            fieldCnt++;
        }
        if (this.getStageEnterTsMs() != 0L) {
            builder.setStageEnterTsMs(this.getStageEnterTsMs());
            fieldCnt++;
        }  else if (builder.hasStageEnterTsMs()) {
            // 清理StageEnterTsMs
            builder.clearStageEnterTsMs();
            fieldCnt++;
        }
        if (this.getPoolStartZone() != 0) {
            builder.setPoolStartZone(this.getPoolStartZone());
            fieldCnt++;
        }  else if (builder.hasPoolStartZone()) {
            // 清理PoolStartZone
            builder.clearPoolStartZone();
            fieldCnt++;
        }
        if (this.getPoolEndZone() != 0) {
            builder.setPoolEndZone(this.getPoolEndZone());
            fieldCnt++;
        }  else if (builder.hasPoolEndZone()) {
            // 清理PoolEndZone
            builder.clearPoolEndZone();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ZoneSeasonModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SEASON)) {
            builder.setSeason(this.getSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERBORNINDEX)) {
            builder.setPlayerBornIndex(this.getPlayerBornIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGE)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGEENTERTSMS)) {
            builder.setStageEnterTsMs(this.getStageEnterTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POOLSTARTZONE)) {
            builder.setPoolStartZone(this.getPoolStartZone());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POOLENDZONE)) {
            builder.setPoolEndZone(this.getPoolEndZone());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ZoneSeasonModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSeason()) {
            this.innerSetSeason(proto.getSeason());
        } else {
            this.innerSetSeason(ZoneSeason.forNumber(0));
        }
        if (proto.hasPlayerBornIndex()) {
            this.innerSetPlayerBornIndex(proto.getPlayerBornIndex());
        } else {
            this.innerSetPlayerBornIndex(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStage()) {
            this.innerSetStage(proto.getStage());
        } else {
            this.innerSetStage(ZoneSeasonStage.forNumber(0));
        }
        if (proto.hasStageEnterTsMs()) {
            this.innerSetStageEnterTsMs(proto.getStageEnterTsMs());
        } else {
            this.innerSetStageEnterTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPoolStartZone()) {
            this.innerSetPoolStartZone(proto.getPoolStartZone());
        } else {
            this.innerSetPoolStartZone(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPoolEndZone()) {
            this.innerSetPoolEndZone(proto.getPoolEndZone());
        } else {
            this.innerSetPoolEndZone(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ZoneSeasonModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ZoneSeasonModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSeason()) {
            this.setSeason(proto.getSeason());
            fieldCnt++;
        }
        if (proto.hasPlayerBornIndex()) {
            this.setPlayerBornIndex(proto.getPlayerBornIndex());
            fieldCnt++;
        }
        if (proto.hasStage()) {
            this.setStage(proto.getStage());
            fieldCnt++;
        }
        if (proto.hasStageEnterTsMs()) {
            this.setStageEnterTsMs(proto.getStageEnterTsMs());
            fieldCnt++;
        }
        if (proto.hasPoolStartZone()) {
            this.setPoolStartZone(proto.getPoolStartZone());
            fieldCnt++;
        }
        if (proto.hasPoolEndZone()) {
            this.setPoolEndZone(proto.getPoolEndZone());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSeasonModel.Builder getCopySsBuilder() {
        final ZoneSeasonModel.Builder builder = ZoneSeasonModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ZoneSeasonModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSeason() != ZoneSeason.forNumber(0)) {
            builder.setSeason(this.getSeason());
            fieldCnt++;
        }  else if (builder.hasSeason()) {
            // 清理Season
            builder.clearSeason();
            fieldCnt++;
        }
        if (this.getPlayerBornIndex() != 0) {
            builder.setPlayerBornIndex(this.getPlayerBornIndex());
            fieldCnt++;
        }  else if (builder.hasPlayerBornIndex()) {
            // 清理PlayerBornIndex
            builder.clearPlayerBornIndex();
            fieldCnt++;
        }
        if (this.getStage() != ZoneSeasonStage.forNumber(0)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }  else if (builder.hasStage()) {
            // 清理Stage
            builder.clearStage();
            fieldCnt++;
        }
        if (this.getStageEnterTsMs() != 0L) {
            builder.setStageEnterTsMs(this.getStageEnterTsMs());
            fieldCnt++;
        }  else if (builder.hasStageEnterTsMs()) {
            // 清理StageEnterTsMs
            builder.clearStageEnterTsMs();
            fieldCnt++;
        }
        if (this.getPoolStartZone() != 0) {
            builder.setPoolStartZone(this.getPoolStartZone());
            fieldCnt++;
        }  else if (builder.hasPoolStartZone()) {
            // 清理PoolStartZone
            builder.clearPoolStartZone();
            fieldCnt++;
        }
        if (this.getPoolEndZone() != 0) {
            builder.setPoolEndZone(this.getPoolEndZone());
            fieldCnt++;
        }  else if (builder.hasPoolEndZone()) {
            // 清理PoolEndZone
            builder.clearPoolEndZone();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ZoneSeasonModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SEASON)) {
            builder.setSeason(this.getSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERBORNINDEX)) {
            builder.setPlayerBornIndex(this.getPlayerBornIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGE)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGEENTERTSMS)) {
            builder.setStageEnterTsMs(this.getStageEnterTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POOLSTARTZONE)) {
            builder.setPoolStartZone(this.getPoolStartZone());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POOLENDZONE)) {
            builder.setPoolEndZone(this.getPoolEndZone());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ZoneSeasonModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSeason()) {
            this.innerSetSeason(proto.getSeason());
        } else {
            this.innerSetSeason(ZoneSeason.forNumber(0));
        }
        if (proto.hasPlayerBornIndex()) {
            this.innerSetPlayerBornIndex(proto.getPlayerBornIndex());
        } else {
            this.innerSetPlayerBornIndex(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStage()) {
            this.innerSetStage(proto.getStage());
        } else {
            this.innerSetStage(ZoneSeasonStage.forNumber(0));
        }
        if (proto.hasStageEnterTsMs()) {
            this.innerSetStageEnterTsMs(proto.getStageEnterTsMs());
        } else {
            this.innerSetStageEnterTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPoolStartZone()) {
            this.innerSetPoolStartZone(proto.getPoolStartZone());
        } else {
            this.innerSetPoolStartZone(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPoolEndZone()) {
            this.innerSetPoolEndZone(proto.getPoolEndZone());
        } else {
            this.innerSetPoolEndZone(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ZoneSeasonModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ZoneSeasonModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSeason()) {
            this.setSeason(proto.getSeason());
            fieldCnt++;
        }
        if (proto.hasPlayerBornIndex()) {
            this.setPlayerBornIndex(proto.getPlayerBornIndex());
            fieldCnt++;
        }
        if (proto.hasStage()) {
            this.setStage(proto.getStage());
            fieldCnt++;
        }
        if (proto.hasStageEnterTsMs()) {
            this.setStageEnterTsMs(proto.getStageEnterTsMs());
            fieldCnt++;
        }
        if (proto.hasPoolStartZone()) {
            this.setPoolStartZone(proto.getPoolStartZone());
            fieldCnt++;
        }
        if (proto.hasPoolEndZone()) {
            this.setPoolEndZone(proto.getPoolEndZone());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ZoneSeasonModel.Builder builder = ZoneSeasonModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ZoneSeasonModelProp)) {
            return false;
        }
        final ZoneSeasonModelProp otherNode = (ZoneSeasonModelProp) node;
        if (this.season != otherNode.season) {
            return false;
        }
        if (this.playerBornIndex != otherNode.playerBornIndex) {
            return false;
        }
        if (this.stage != otherNode.stage) {
            return false;
        }
        if (this.stageEnterTsMs != otherNode.stageEnterTsMs) {
            return false;
        }
        if (this.poolStartZone != otherNode.poolStartZone) {
            return false;
        }
        if (this.poolEndZone != otherNode.poolEndZone) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructSkynet.SkynetGuardTask;
import com.yorha.proto.StructSkynet;
import com.yorha.proto.StructSkynetPB.SkynetGuardTaskPB;
import com.yorha.proto.StructSkynetPB;


/**
 * <AUTHOR> auto gen
 */
public class SkynetGuardTaskProp extends AbstractPropNode {

    public static final int FIELD_INDEX_GUARDTASK = 0;
    public static final int FIELD_INDEX_LASTREFRESHTSMS = 1;
    public static final int FIELD_INDEX_TASKPOOLNUM = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private SkynetTaskProp guardTask = null;
    private long lastRefreshTsMs = Constant.DEFAULT_LONG_VALUE;
    private int taskPoolNum = Constant.DEFAULT_INT_VALUE;

    public SkynetGuardTaskProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SkynetGuardTaskProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get guardTask
     *
     * @return guardTask value
     */
    public SkynetTaskProp getGuardTask() {
        if (this.guardTask == null) {
            this.guardTask = new SkynetTaskProp(this, FIELD_INDEX_GUARDTASK);
        }
        return this.guardTask;
    }

    /**
     * get lastRefreshTsMs
     *
     * @return lastRefreshTsMs value
     */
    public long getLastRefreshTsMs() {
        return this.lastRefreshTsMs;
    }

    /**
     * set lastRefreshTsMs && set marked
     *
     * @param lastRefreshTsMs new value
     * @return current object
     */
    public SkynetGuardTaskProp setLastRefreshTsMs(long lastRefreshTsMs) {
        if (this.lastRefreshTsMs != lastRefreshTsMs) {
            this.mark(FIELD_INDEX_LASTREFRESHTSMS);
            this.lastRefreshTsMs = lastRefreshTsMs;
        }
        return this;
    }

    /**
     * inner set lastRefreshTsMs
     *
     * @param lastRefreshTsMs new value
     */
    private void innerSetLastRefreshTsMs(long lastRefreshTsMs) {
        this.lastRefreshTsMs = lastRefreshTsMs;
    }

    /**
     * get taskPoolNum
     *
     * @return taskPoolNum value
     */
    public int getTaskPoolNum() {
        return this.taskPoolNum;
    }

    /**
     * set taskPoolNum && set marked
     *
     * @param taskPoolNum new value
     * @return current object
     */
    public SkynetGuardTaskProp setTaskPoolNum(int taskPoolNum) {
        if (this.taskPoolNum != taskPoolNum) {
            this.mark(FIELD_INDEX_TASKPOOLNUM);
            this.taskPoolNum = taskPoolNum;
        }
        return this;
    }

    /**
     * inner set taskPoolNum
     *
     * @param taskPoolNum new value
     */
    private void innerSetTaskPoolNum(int taskPoolNum) {
        this.taskPoolNum = taskPoolNum;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SkynetGuardTaskPB.Builder getCopyCsBuilder() {
        final SkynetGuardTaskPB.Builder builder = SkynetGuardTaskPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SkynetGuardTaskPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.guardTask != null) {
            StructSkynetPB.SkynetTaskPB.Builder tmpBuilder = StructSkynetPB.SkynetTaskPB.newBuilder();
            final int tmpFieldCnt = this.guardTask.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGuardTask(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGuardTask();
            }
        }  else if (builder.hasGuardTask()) {
            // 清理GuardTask
            builder.clearGuardTask();
            fieldCnt++;
        }
        if (this.getLastRefreshTsMs() != 0L) {
            builder.setLastRefreshTsMs(this.getLastRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasLastRefreshTsMs()) {
            // 清理LastRefreshTsMs
            builder.clearLastRefreshTsMs();
            fieldCnt++;
        }
        if (this.getTaskPoolNum() != 0) {
            builder.setTaskPoolNum(this.getTaskPoolNum());
            fieldCnt++;
        }  else if (builder.hasTaskPoolNum()) {
            // 清理TaskPoolNum
            builder.clearTaskPoolNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SkynetGuardTaskPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GUARDTASK) && this.guardTask != null) {
            final boolean needClear = !builder.hasGuardTask();
            final int tmpFieldCnt = this.guardTask.copyChangeToCs(builder.getGuardTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGuardTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTSMS)) {
            builder.setLastRefreshTsMs(this.getLastRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKPOOLNUM)) {
            builder.setTaskPoolNum(this.getTaskPoolNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SkynetGuardTaskPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GUARDTASK) && this.guardTask != null) {
            final boolean needClear = !builder.hasGuardTask();
            final int tmpFieldCnt = this.guardTask.copyChangeToAndClearDeleteKeysCs(builder.getGuardTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGuardTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTSMS)) {
            builder.setLastRefreshTsMs(this.getLastRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKPOOLNUM)) {
            builder.setTaskPoolNum(this.getTaskPoolNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SkynetGuardTaskPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGuardTask()) {
            this.getGuardTask().mergeFromCs(proto.getGuardTask());
        } else {
            if (this.guardTask != null) {
                this.guardTask.mergeFromCs(proto.getGuardTask());
            }
        }
        if (proto.hasLastRefreshTsMs()) {
            this.innerSetLastRefreshTsMs(proto.getLastRefreshTsMs());
        } else {
            this.innerSetLastRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTaskPoolNum()) {
            this.innerSetTaskPoolNum(proto.getTaskPoolNum());
        } else {
            this.innerSetTaskPoolNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SkynetGuardTaskProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SkynetGuardTaskPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGuardTask()) {
            this.getGuardTask().mergeChangeFromCs(proto.getGuardTask());
            fieldCnt++;
        }
        if (proto.hasLastRefreshTsMs()) {
            this.setLastRefreshTsMs(proto.getLastRefreshTsMs());
            fieldCnt++;
        }
        if (proto.hasTaskPoolNum()) {
            this.setTaskPoolNum(proto.getTaskPoolNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SkynetGuardTask.Builder getCopyDbBuilder() {
        final SkynetGuardTask.Builder builder = SkynetGuardTask.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SkynetGuardTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.guardTask != null) {
            StructSkynet.SkynetTask.Builder tmpBuilder = StructSkynet.SkynetTask.newBuilder();
            final int tmpFieldCnt = this.guardTask.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGuardTask(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGuardTask();
            }
        }  else if (builder.hasGuardTask()) {
            // 清理GuardTask
            builder.clearGuardTask();
            fieldCnt++;
        }
        if (this.getLastRefreshTsMs() != 0L) {
            builder.setLastRefreshTsMs(this.getLastRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasLastRefreshTsMs()) {
            // 清理LastRefreshTsMs
            builder.clearLastRefreshTsMs();
            fieldCnt++;
        }
        if (this.getTaskPoolNum() != 0) {
            builder.setTaskPoolNum(this.getTaskPoolNum());
            fieldCnt++;
        }  else if (builder.hasTaskPoolNum()) {
            // 清理TaskPoolNum
            builder.clearTaskPoolNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SkynetGuardTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GUARDTASK) && this.guardTask != null) {
            final boolean needClear = !builder.hasGuardTask();
            final int tmpFieldCnt = this.guardTask.copyChangeToDb(builder.getGuardTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGuardTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTSMS)) {
            builder.setLastRefreshTsMs(this.getLastRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKPOOLNUM)) {
            builder.setTaskPoolNum(this.getTaskPoolNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SkynetGuardTask proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGuardTask()) {
            this.getGuardTask().mergeFromDb(proto.getGuardTask());
        } else {
            if (this.guardTask != null) {
                this.guardTask.mergeFromDb(proto.getGuardTask());
            }
        }
        if (proto.hasLastRefreshTsMs()) {
            this.innerSetLastRefreshTsMs(proto.getLastRefreshTsMs());
        } else {
            this.innerSetLastRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTaskPoolNum()) {
            this.innerSetTaskPoolNum(proto.getTaskPoolNum());
        } else {
            this.innerSetTaskPoolNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SkynetGuardTaskProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SkynetGuardTask proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGuardTask()) {
            this.getGuardTask().mergeChangeFromDb(proto.getGuardTask());
            fieldCnt++;
        }
        if (proto.hasLastRefreshTsMs()) {
            this.setLastRefreshTsMs(proto.getLastRefreshTsMs());
            fieldCnt++;
        }
        if (proto.hasTaskPoolNum()) {
            this.setTaskPoolNum(proto.getTaskPoolNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SkynetGuardTask.Builder getCopySsBuilder() {
        final SkynetGuardTask.Builder builder = SkynetGuardTask.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SkynetGuardTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.guardTask != null) {
            StructSkynet.SkynetTask.Builder tmpBuilder = StructSkynet.SkynetTask.newBuilder();
            final int tmpFieldCnt = this.guardTask.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGuardTask(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGuardTask();
            }
        }  else if (builder.hasGuardTask()) {
            // 清理GuardTask
            builder.clearGuardTask();
            fieldCnt++;
        }
        if (this.getLastRefreshTsMs() != 0L) {
            builder.setLastRefreshTsMs(this.getLastRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasLastRefreshTsMs()) {
            // 清理LastRefreshTsMs
            builder.clearLastRefreshTsMs();
            fieldCnt++;
        }
        if (this.getTaskPoolNum() != 0) {
            builder.setTaskPoolNum(this.getTaskPoolNum());
            fieldCnt++;
        }  else if (builder.hasTaskPoolNum()) {
            // 清理TaskPoolNum
            builder.clearTaskPoolNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SkynetGuardTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GUARDTASK) && this.guardTask != null) {
            final boolean needClear = !builder.hasGuardTask();
            final int tmpFieldCnt = this.guardTask.copyChangeToSs(builder.getGuardTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGuardTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTSMS)) {
            builder.setLastRefreshTsMs(this.getLastRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKPOOLNUM)) {
            builder.setTaskPoolNum(this.getTaskPoolNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SkynetGuardTask proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGuardTask()) {
            this.getGuardTask().mergeFromSs(proto.getGuardTask());
        } else {
            if (this.guardTask != null) {
                this.guardTask.mergeFromSs(proto.getGuardTask());
            }
        }
        if (proto.hasLastRefreshTsMs()) {
            this.innerSetLastRefreshTsMs(proto.getLastRefreshTsMs());
        } else {
            this.innerSetLastRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTaskPoolNum()) {
            this.innerSetTaskPoolNum(proto.getTaskPoolNum());
        } else {
            this.innerSetTaskPoolNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SkynetGuardTaskProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SkynetGuardTask proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGuardTask()) {
            this.getGuardTask().mergeChangeFromSs(proto.getGuardTask());
            fieldCnt++;
        }
        if (proto.hasLastRefreshTsMs()) {
            this.setLastRefreshTsMs(proto.getLastRefreshTsMs());
            fieldCnt++;
        }
        if (proto.hasTaskPoolNum()) {
            this.setTaskPoolNum(proto.getTaskPoolNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SkynetGuardTask.Builder builder = SkynetGuardTask.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_GUARDTASK) && this.guardTask != null) {
            this.guardTask.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.guardTask != null) {
            this.guardTask.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SkynetGuardTaskProp)) {
            return false;
        }
        final SkynetGuardTaskProp otherNode = (SkynetGuardTaskProp) node;
        if (!this.getGuardTask().compareDataTo(otherNode.getGuardTask())) {
            return false;
        }
        if (this.lastRefreshTsMs != otherNode.lastRefreshTsMs) {
            return false;
        }
        if (this.taskPoolNum != otherNode.taskPoolNum) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
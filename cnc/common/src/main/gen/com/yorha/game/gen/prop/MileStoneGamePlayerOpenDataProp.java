package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.MileStoneGamePlayerOpenData;
import com.yorha.proto.StructPB.MileStoneGamePlayerOpenDataPB;


/**
 * <AUTHOR> auto gen
 */
public class MileStoneGamePlayerOpenDataProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_GAMEPLAYERTYPE = 0;
    public static final int FIELD_INDEX_OPENTSMS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int gamePlayerType = Constant.DEFAULT_INT_VALUE;
    private long openTsMs = Constant.DEFAULT_LONG_VALUE;

    public MileStoneGamePlayerOpenDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MileStoneGamePlayerOpenDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get gamePlayerType
     *
     * @return gamePlayerType value
     */
    public int getGamePlayerType() {
        return this.gamePlayerType;
    }

    /**
     * set gamePlayerType && set marked
     *
     * @param gamePlayerType new value
     * @return current object
     */
    public MileStoneGamePlayerOpenDataProp setGamePlayerType(int gamePlayerType) {
        if (this.gamePlayerType != gamePlayerType) {
            this.mark(FIELD_INDEX_GAMEPLAYERTYPE);
            this.gamePlayerType = gamePlayerType;
        }
        return this;
    }

    /**
     * inner set gamePlayerType
     *
     * @param gamePlayerType new value
     */
    private void innerSetGamePlayerType(int gamePlayerType) {
        this.gamePlayerType = gamePlayerType;
    }

    /**
     * get openTsMs
     *
     * @return openTsMs value
     */
    public long getOpenTsMs() {
        return this.openTsMs;
    }

    /**
     * set openTsMs && set marked
     *
     * @param openTsMs new value
     * @return current object
     */
    public MileStoneGamePlayerOpenDataProp setOpenTsMs(long openTsMs) {
        if (this.openTsMs != openTsMs) {
            this.mark(FIELD_INDEX_OPENTSMS);
            this.openTsMs = openTsMs;
        }
        return this;
    }

    /**
     * inner set openTsMs
     *
     * @param openTsMs new value
     */
    private void innerSetOpenTsMs(long openTsMs) {
        this.openTsMs = openTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneGamePlayerOpenDataPB.Builder getCopyCsBuilder() {
        final MileStoneGamePlayerOpenDataPB.Builder builder = MileStoneGamePlayerOpenDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MileStoneGamePlayerOpenDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGamePlayerType() != 0) {
            builder.setGamePlayerType(this.getGamePlayerType());
            fieldCnt++;
        }  else if (builder.hasGamePlayerType()) {
            // 清理GamePlayerType
            builder.clearGamePlayerType();
            fieldCnt++;
        }
        if (this.getOpenTsMs() != 0L) {
            builder.setOpenTsMs(this.getOpenTsMs());
            fieldCnt++;
        }  else if (builder.hasOpenTsMs()) {
            // 清理OpenTsMs
            builder.clearOpenTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MileStoneGamePlayerOpenDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GAMEPLAYERTYPE)) {
            builder.setGamePlayerType(this.getGamePlayerType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPENTSMS)) {
            builder.setOpenTsMs(this.getOpenTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MileStoneGamePlayerOpenDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GAMEPLAYERTYPE)) {
            builder.setGamePlayerType(this.getGamePlayerType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPENTSMS)) {
            builder.setOpenTsMs(this.getOpenTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MileStoneGamePlayerOpenDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGamePlayerType()) {
            this.innerSetGamePlayerType(proto.getGamePlayerType());
        } else {
            this.innerSetGamePlayerType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOpenTsMs()) {
            this.innerSetOpenTsMs(proto.getOpenTsMs());
        } else {
            this.innerSetOpenTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MileStoneGamePlayerOpenDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MileStoneGamePlayerOpenDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGamePlayerType()) {
            this.setGamePlayerType(proto.getGamePlayerType());
            fieldCnt++;
        }
        if (proto.hasOpenTsMs()) {
            this.setOpenTsMs(proto.getOpenTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneGamePlayerOpenData.Builder getCopyDbBuilder() {
        final MileStoneGamePlayerOpenData.Builder builder = MileStoneGamePlayerOpenData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MileStoneGamePlayerOpenData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGamePlayerType() != 0) {
            builder.setGamePlayerType(this.getGamePlayerType());
            fieldCnt++;
        }  else if (builder.hasGamePlayerType()) {
            // 清理GamePlayerType
            builder.clearGamePlayerType();
            fieldCnt++;
        }
        if (this.getOpenTsMs() != 0L) {
            builder.setOpenTsMs(this.getOpenTsMs());
            fieldCnt++;
        }  else if (builder.hasOpenTsMs()) {
            // 清理OpenTsMs
            builder.clearOpenTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MileStoneGamePlayerOpenData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GAMEPLAYERTYPE)) {
            builder.setGamePlayerType(this.getGamePlayerType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPENTSMS)) {
            builder.setOpenTsMs(this.getOpenTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MileStoneGamePlayerOpenData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGamePlayerType()) {
            this.innerSetGamePlayerType(proto.getGamePlayerType());
        } else {
            this.innerSetGamePlayerType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOpenTsMs()) {
            this.innerSetOpenTsMs(proto.getOpenTsMs());
        } else {
            this.innerSetOpenTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MileStoneGamePlayerOpenDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MileStoneGamePlayerOpenData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGamePlayerType()) {
            this.setGamePlayerType(proto.getGamePlayerType());
            fieldCnt++;
        }
        if (proto.hasOpenTsMs()) {
            this.setOpenTsMs(proto.getOpenTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneGamePlayerOpenData.Builder getCopySsBuilder() {
        final MileStoneGamePlayerOpenData.Builder builder = MileStoneGamePlayerOpenData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MileStoneGamePlayerOpenData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGamePlayerType() != 0) {
            builder.setGamePlayerType(this.getGamePlayerType());
            fieldCnt++;
        }  else if (builder.hasGamePlayerType()) {
            // 清理GamePlayerType
            builder.clearGamePlayerType();
            fieldCnt++;
        }
        if (this.getOpenTsMs() != 0L) {
            builder.setOpenTsMs(this.getOpenTsMs());
            fieldCnt++;
        }  else if (builder.hasOpenTsMs()) {
            // 清理OpenTsMs
            builder.clearOpenTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MileStoneGamePlayerOpenData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GAMEPLAYERTYPE)) {
            builder.setGamePlayerType(this.getGamePlayerType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPENTSMS)) {
            builder.setOpenTsMs(this.getOpenTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MileStoneGamePlayerOpenData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGamePlayerType()) {
            this.innerSetGamePlayerType(proto.getGamePlayerType());
        } else {
            this.innerSetGamePlayerType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOpenTsMs()) {
            this.innerSetOpenTsMs(proto.getOpenTsMs());
        } else {
            this.innerSetOpenTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MileStoneGamePlayerOpenDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MileStoneGamePlayerOpenData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGamePlayerType()) {
            this.setGamePlayerType(proto.getGamePlayerType());
            fieldCnt++;
        }
        if (proto.hasOpenTsMs()) {
            this.setOpenTsMs(proto.getOpenTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MileStoneGamePlayerOpenData.Builder builder = MileStoneGamePlayerOpenData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.gamePlayerType;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MileStoneGamePlayerOpenDataProp)) {
            return false;
        }
        final MileStoneGamePlayerOpenDataProp otherNode = (MileStoneGamePlayerOpenDataProp) node;
        if (this.gamePlayerType != otherNode.gamePlayerType) {
            return false;
        }
        if (this.openTsMs != otherNode.openTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
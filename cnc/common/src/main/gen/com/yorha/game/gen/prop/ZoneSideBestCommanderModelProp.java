package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Zoneside.ZoneSideBestCommanderModel;
import com.yorha.proto.ZonesidePB.ZoneSideBestCommanderModelPB;


/**
 * <AUTHOR> auto gen
 */
public class ZoneSideBestCommanderModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CURSEASON = 0;
    public static final int FIELD_INDEX_VOLUMECURSEASON = 1;
    public static final int FIELD_INDEX_LASTUPDATETSMS = 2;
    public static final int FIELD_INDEX_CURSTAGE = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private ZoneSeason curSeason = ZoneSeason.forNumber(0);
    private int volumeCurSeason = Constant.DEFAULT_INT_VALUE;
    private long lastUpdateTsMs = Constant.DEFAULT_LONG_VALUE;
    private ZoneSeasonStage curStage = ZoneSeasonStage.forNumber(0);

    public ZoneSideBestCommanderModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ZoneSideBestCommanderModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get curSeason
     *
     * @return curSeason value
     */
    public ZoneSeason getCurSeason() {
        return this.curSeason;
    }

    /**
     * set curSeason && set marked
     *
     * @param curSeason new value
     * @return current object
     */
    public ZoneSideBestCommanderModelProp setCurSeason(ZoneSeason curSeason) {
        if (curSeason == null) {
            throw new NullPointerException();
        }
        if (this.curSeason != curSeason) {
            this.mark(FIELD_INDEX_CURSEASON);
            this.curSeason = curSeason;
        }
        return this;
    }

    /**
     * inner set curSeason
     *
     * @param curSeason new value
     */
    private void innerSetCurSeason(ZoneSeason curSeason) {
        this.curSeason = curSeason;
    }

    /**
     * get volumeCurSeason
     *
     * @return volumeCurSeason value
     */
    public int getVolumeCurSeason() {
        return this.volumeCurSeason;
    }

    /**
     * set volumeCurSeason && set marked
     *
     * @param volumeCurSeason new value
     * @return current object
     */
    public ZoneSideBestCommanderModelProp setVolumeCurSeason(int volumeCurSeason) {
        if (this.volumeCurSeason != volumeCurSeason) {
            this.mark(FIELD_INDEX_VOLUMECURSEASON);
            this.volumeCurSeason = volumeCurSeason;
        }
        return this;
    }

    /**
     * inner set volumeCurSeason
     *
     * @param volumeCurSeason new value
     */
    private void innerSetVolumeCurSeason(int volumeCurSeason) {
        this.volumeCurSeason = volumeCurSeason;
    }

    /**
     * get lastUpdateTsMs
     *
     * @return lastUpdateTsMs value
     */
    public long getLastUpdateTsMs() {
        return this.lastUpdateTsMs;
    }

    /**
     * set lastUpdateTsMs && set marked
     *
     * @param lastUpdateTsMs new value
     * @return current object
     */
    public ZoneSideBestCommanderModelProp setLastUpdateTsMs(long lastUpdateTsMs) {
        if (this.lastUpdateTsMs != lastUpdateTsMs) {
            this.mark(FIELD_INDEX_LASTUPDATETSMS);
            this.lastUpdateTsMs = lastUpdateTsMs;
        }
        return this;
    }

    /**
     * inner set lastUpdateTsMs
     *
     * @param lastUpdateTsMs new value
     */
    private void innerSetLastUpdateTsMs(long lastUpdateTsMs) {
        this.lastUpdateTsMs = lastUpdateTsMs;
    }

    /**
     * get curStage
     *
     * @return curStage value
     */
    public ZoneSeasonStage getCurStage() {
        return this.curStage;
    }

    /**
     * set curStage && set marked
     *
     * @param curStage new value
     * @return current object
     */
    public ZoneSideBestCommanderModelProp setCurStage(ZoneSeasonStage curStage) {
        if (curStage == null) {
            throw new NullPointerException();
        }
        if (this.curStage != curStage) {
            this.mark(FIELD_INDEX_CURSTAGE);
            this.curStage = curStage;
        }
        return this;
    }

    /**
     * inner set curStage
     *
     * @param curStage new value
     */
    private void innerSetCurStage(ZoneSeasonStage curStage) {
        this.curStage = curStage;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSideBestCommanderModelPB.Builder getCopyCsBuilder() {
        final ZoneSideBestCommanderModelPB.Builder builder = ZoneSideBestCommanderModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ZoneSideBestCommanderModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurSeason() != ZoneSeason.forNumber(0)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }  else if (builder.hasCurSeason()) {
            // 清理CurSeason
            builder.clearCurSeason();
            fieldCnt++;
        }
        if (this.getVolumeCurSeason() != 0) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }  else if (builder.hasVolumeCurSeason()) {
            // 清理VolumeCurSeason
            builder.clearVolumeCurSeason();
            fieldCnt++;
        }
        if (this.getLastUpdateTsMs() != 0L) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateTsMs()) {
            // 清理LastUpdateTsMs
            builder.clearLastUpdateTsMs();
            fieldCnt++;
        }
        if (this.getCurStage() != ZoneSeasonStage.forNumber(0)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }  else if (builder.hasCurStage()) {
            // 清理CurStage
            builder.clearCurStage();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ZoneSideBestCommanderModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURSEASON)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VOLUMECURSEASON)) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATETSMS)) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSTAGE)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ZoneSideBestCommanderModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURSEASON)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VOLUMECURSEASON)) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATETSMS)) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSTAGE)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ZoneSideBestCommanderModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurSeason()) {
            this.innerSetCurSeason(proto.getCurSeason());
        } else {
            this.innerSetCurSeason(ZoneSeason.forNumber(0));
        }
        if (proto.hasVolumeCurSeason()) {
            this.innerSetVolumeCurSeason(proto.getVolumeCurSeason());
        } else {
            this.innerSetVolumeCurSeason(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastUpdateTsMs()) {
            this.innerSetLastUpdateTsMs(proto.getLastUpdateTsMs());
        } else {
            this.innerSetLastUpdateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCurStage()) {
            this.innerSetCurStage(proto.getCurStage());
        } else {
            this.innerSetCurStage(ZoneSeasonStage.forNumber(0));
        }
        this.markAll();
        return ZoneSideBestCommanderModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ZoneSideBestCommanderModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurSeason()) {
            this.setCurSeason(proto.getCurSeason());
            fieldCnt++;
        }
        if (proto.hasVolumeCurSeason()) {
            this.setVolumeCurSeason(proto.getVolumeCurSeason());
            fieldCnt++;
        }
        if (proto.hasLastUpdateTsMs()) {
            this.setLastUpdateTsMs(proto.getLastUpdateTsMs());
            fieldCnt++;
        }
        if (proto.hasCurStage()) {
            this.setCurStage(proto.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSideBestCommanderModel.Builder getCopyDbBuilder() {
        final ZoneSideBestCommanderModel.Builder builder = ZoneSideBestCommanderModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ZoneSideBestCommanderModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurSeason() != ZoneSeason.forNumber(0)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }  else if (builder.hasCurSeason()) {
            // 清理CurSeason
            builder.clearCurSeason();
            fieldCnt++;
        }
        if (this.getVolumeCurSeason() != 0) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }  else if (builder.hasVolumeCurSeason()) {
            // 清理VolumeCurSeason
            builder.clearVolumeCurSeason();
            fieldCnt++;
        }
        if (this.getLastUpdateTsMs() != 0L) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateTsMs()) {
            // 清理LastUpdateTsMs
            builder.clearLastUpdateTsMs();
            fieldCnt++;
        }
        if (this.getCurStage() != ZoneSeasonStage.forNumber(0)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }  else if (builder.hasCurStage()) {
            // 清理CurStage
            builder.clearCurStage();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ZoneSideBestCommanderModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURSEASON)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VOLUMECURSEASON)) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATETSMS)) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSTAGE)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ZoneSideBestCommanderModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurSeason()) {
            this.innerSetCurSeason(proto.getCurSeason());
        } else {
            this.innerSetCurSeason(ZoneSeason.forNumber(0));
        }
        if (proto.hasVolumeCurSeason()) {
            this.innerSetVolumeCurSeason(proto.getVolumeCurSeason());
        } else {
            this.innerSetVolumeCurSeason(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastUpdateTsMs()) {
            this.innerSetLastUpdateTsMs(proto.getLastUpdateTsMs());
        } else {
            this.innerSetLastUpdateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCurStage()) {
            this.innerSetCurStage(proto.getCurStage());
        } else {
            this.innerSetCurStage(ZoneSeasonStage.forNumber(0));
        }
        this.markAll();
        return ZoneSideBestCommanderModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ZoneSideBestCommanderModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurSeason()) {
            this.setCurSeason(proto.getCurSeason());
            fieldCnt++;
        }
        if (proto.hasVolumeCurSeason()) {
            this.setVolumeCurSeason(proto.getVolumeCurSeason());
            fieldCnt++;
        }
        if (proto.hasLastUpdateTsMs()) {
            this.setLastUpdateTsMs(proto.getLastUpdateTsMs());
            fieldCnt++;
        }
        if (proto.hasCurStage()) {
            this.setCurStage(proto.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSideBestCommanderModel.Builder getCopySsBuilder() {
        final ZoneSideBestCommanderModel.Builder builder = ZoneSideBestCommanderModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ZoneSideBestCommanderModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurSeason() != ZoneSeason.forNumber(0)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }  else if (builder.hasCurSeason()) {
            // 清理CurSeason
            builder.clearCurSeason();
            fieldCnt++;
        }
        if (this.getVolumeCurSeason() != 0) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }  else if (builder.hasVolumeCurSeason()) {
            // 清理VolumeCurSeason
            builder.clearVolumeCurSeason();
            fieldCnt++;
        }
        if (this.getLastUpdateTsMs() != 0L) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateTsMs()) {
            // 清理LastUpdateTsMs
            builder.clearLastUpdateTsMs();
            fieldCnt++;
        }
        if (this.getCurStage() != ZoneSeasonStage.forNumber(0)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }  else if (builder.hasCurStage()) {
            // 清理CurStage
            builder.clearCurStage();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ZoneSideBestCommanderModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURSEASON)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VOLUMECURSEASON)) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATETSMS)) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSTAGE)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ZoneSideBestCommanderModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurSeason()) {
            this.innerSetCurSeason(proto.getCurSeason());
        } else {
            this.innerSetCurSeason(ZoneSeason.forNumber(0));
        }
        if (proto.hasVolumeCurSeason()) {
            this.innerSetVolumeCurSeason(proto.getVolumeCurSeason());
        } else {
            this.innerSetVolumeCurSeason(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastUpdateTsMs()) {
            this.innerSetLastUpdateTsMs(proto.getLastUpdateTsMs());
        } else {
            this.innerSetLastUpdateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCurStage()) {
            this.innerSetCurStage(proto.getCurStage());
        } else {
            this.innerSetCurStage(ZoneSeasonStage.forNumber(0));
        }
        this.markAll();
        return ZoneSideBestCommanderModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ZoneSideBestCommanderModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurSeason()) {
            this.setCurSeason(proto.getCurSeason());
            fieldCnt++;
        }
        if (proto.hasVolumeCurSeason()) {
            this.setVolumeCurSeason(proto.getVolumeCurSeason());
            fieldCnt++;
        }
        if (proto.hasLastUpdateTsMs()) {
            this.setLastUpdateTsMs(proto.getLastUpdateTsMs());
            fieldCnt++;
        }
        if (proto.hasCurStage()) {
            this.setCurStage(proto.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ZoneSideBestCommanderModel.Builder builder = ZoneSideBestCommanderModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ZoneSideBestCommanderModelProp)) {
            return false;
        }
        final ZoneSideBestCommanderModelProp otherNode = (ZoneSideBestCommanderModelProp) node;
        if (this.curSeason != otherNode.curSeason) {
            return false;
        }
        if (this.volumeCurSeason != otherNode.volumeCurSeason) {
            return false;
        }
        if (this.lastUpdateTsMs != otherNode.lastUpdateTsMs) {
            return false;
        }
        if (this.curStage != otherNode.curStage) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
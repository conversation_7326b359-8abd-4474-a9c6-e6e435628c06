package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructBattle.DevBuff;
import com.yorha.proto.StructBattlePB.DevBuffPB;


/**
 * <AUTHOR> auto gen
 */
public class DevBuffProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_DEVBUFFID = 0;
    public static final int FIELD_INDEX_STARTTIME = 1;
    public static final int FIELD_INDEX_ENDTIME = 2;
    public static final int FIELD_INDEX_DEVBUFFTYPE = 3;
    public static final int FIELD_INDEX_LAYER = 4;
    public static final int FIELD_INDEX_SOURCETYPE = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private int devBuffId = Constant.DEFAULT_INT_VALUE;
    private long startTime = Constant.DEFAULT_LONG_VALUE;
    private long endTime = Constant.DEFAULT_LONG_VALUE;
    private DevBuffType devBuffType = DevBuffType.forNumber(0);
    private int layer = Constant.DEFAULT_INT_VALUE;
    private DevBuffSourceType sourceType = DevBuffSourceType.forNumber(0);

    public DevBuffProp() {
        super(null, 0, FIELD_COUNT);
    }

    public DevBuffProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get devBuffId
     *
     * @return devBuffId value
     */
    public int getDevBuffId() {
        return this.devBuffId;
    }

    /**
     * set devBuffId && set marked
     *
     * @param devBuffId new value
     * @return current object
     */
    public DevBuffProp setDevBuffId(int devBuffId) {
        if (this.devBuffId != devBuffId) {
            this.mark(FIELD_INDEX_DEVBUFFID);
            this.devBuffId = devBuffId;
        }
        return this;
    }

    /**
     * inner set devBuffId
     *
     * @param devBuffId new value
     */
    private void innerSetDevBuffId(int devBuffId) {
        this.devBuffId = devBuffId;
    }

    /**
     * get startTime
     *
     * @return startTime value
     */
    public long getStartTime() {
        return this.startTime;
    }

    /**
     * set startTime && set marked
     *
     * @param startTime new value
     * @return current object
     */
    public DevBuffProp setStartTime(long startTime) {
        if (this.startTime != startTime) {
            this.mark(FIELD_INDEX_STARTTIME);
            this.startTime = startTime;
        }
        return this;
    }

    /**
     * inner set startTime
     *
     * @param startTime new value
     */
    private void innerSetStartTime(long startTime) {
        this.startTime = startTime;
    }

    /**
     * get endTime
     *
     * @return endTime value
     */
    public long getEndTime() {
        return this.endTime;
    }

    /**
     * set endTime && set marked
     *
     * @param endTime new value
     * @return current object
     */
    public DevBuffProp setEndTime(long endTime) {
        if (this.endTime != endTime) {
            this.mark(FIELD_INDEX_ENDTIME);
            this.endTime = endTime;
        }
        return this;
    }

    /**
     * inner set endTime
     *
     * @param endTime new value
     */
    private void innerSetEndTime(long endTime) {
        this.endTime = endTime;
    }

    /**
     * get devBuffType
     *
     * @return devBuffType value
     */
    public DevBuffType getDevBuffType() {
        return this.devBuffType;
    }

    /**
     * set devBuffType && set marked
     *
     * @param devBuffType new value
     * @return current object
     */
    public DevBuffProp setDevBuffType(DevBuffType devBuffType) {
        if (devBuffType == null) {
            throw new NullPointerException();
        }
        if (this.devBuffType != devBuffType) {
            this.mark(FIELD_INDEX_DEVBUFFTYPE);
            this.devBuffType = devBuffType;
        }
        return this;
    }

    /**
     * inner set devBuffType
     *
     * @param devBuffType new value
     */
    private void innerSetDevBuffType(DevBuffType devBuffType) {
        this.devBuffType = devBuffType;
    }

    /**
     * get layer
     *
     * @return layer value
     */
    public int getLayer() {
        return this.layer;
    }

    /**
     * set layer && set marked
     *
     * @param layer new value
     * @return current object
     */
    public DevBuffProp setLayer(int layer) {
        if (this.layer != layer) {
            this.mark(FIELD_INDEX_LAYER);
            this.layer = layer;
        }
        return this;
    }

    /**
     * inner set layer
     *
     * @param layer new value
     */
    private void innerSetLayer(int layer) {
        this.layer = layer;
    }

    /**
     * get sourceType
     *
     * @return sourceType value
     */
    public DevBuffSourceType getSourceType() {
        return this.sourceType;
    }

    /**
     * set sourceType && set marked
     *
     * @param sourceType new value
     * @return current object
     */
    public DevBuffProp setSourceType(DevBuffSourceType sourceType) {
        if (sourceType == null) {
            throw new NullPointerException();
        }
        if (this.sourceType != sourceType) {
            this.mark(FIELD_INDEX_SOURCETYPE);
            this.sourceType = sourceType;
        }
        return this;
    }

    /**
     * inner set sourceType
     *
     * @param sourceType new value
     */
    private void innerSetSourceType(DevBuffSourceType sourceType) {
        this.sourceType = sourceType;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DevBuffPB.Builder getCopyCsBuilder() {
        final DevBuffPB.Builder builder = DevBuffPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(DevBuffPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDevBuffId() != 0) {
            builder.setDevBuffId(this.getDevBuffId());
            fieldCnt++;
        }  else if (builder.hasDevBuffId()) {
            // 清理DevBuffId
            builder.clearDevBuffId();
            fieldCnt++;
        }
        if (this.getStartTime() != 0L) {
            builder.setStartTime(this.getStartTime());
            fieldCnt++;
        }  else if (builder.hasStartTime()) {
            // 清理StartTime
            builder.clearStartTime();
            fieldCnt++;
        }
        if (this.getEndTime() != 0L) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }  else if (builder.hasEndTime()) {
            // 清理EndTime
            builder.clearEndTime();
            fieldCnt++;
        }
        if (this.getLayer() != 0) {
            builder.setLayer(this.getLayer());
            fieldCnt++;
        }  else if (builder.hasLayer()) {
            // 清理Layer
            builder.clearLayer();
            fieldCnt++;
        }
        if (this.getSourceType() != DevBuffSourceType.forNumber(0)) {
            builder.setSourceType(this.getSourceType());
            fieldCnt++;
        }  else if (builder.hasSourceType()) {
            // 清理SourceType
            builder.clearSourceType();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(DevBuffPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DEVBUFFID)) {
            builder.setDevBuffId(this.getDevBuffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTIME)) {
            builder.setStartTime(this.getStartTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTIME)) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LAYER)) {
            builder.setLayer(this.getLayer());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOURCETYPE)) {
            builder.setSourceType(this.getSourceType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(DevBuffPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DEVBUFFID)) {
            builder.setDevBuffId(this.getDevBuffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTIME)) {
            builder.setStartTime(this.getStartTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTIME)) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LAYER)) {
            builder.setLayer(this.getLayer());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOURCETYPE)) {
            builder.setSourceType(this.getSourceType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(DevBuffPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDevBuffId()) {
            this.innerSetDevBuffId(proto.getDevBuffId());
        } else {
            this.innerSetDevBuffId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartTime()) {
            this.innerSetStartTime(proto.getStartTime());
        } else {
            this.innerSetStartTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTime()) {
            this.innerSetEndTime(proto.getEndTime());
        } else {
            this.innerSetEndTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLayer()) {
            this.innerSetLayer(proto.getLayer());
        } else {
            this.innerSetLayer(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSourceType()) {
            this.innerSetSourceType(proto.getSourceType());
        } else {
            this.innerSetSourceType(DevBuffSourceType.forNumber(0));
        }
        this.markAll();
        return DevBuffProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(DevBuffPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDevBuffId()) {
            this.setDevBuffId(proto.getDevBuffId());
            fieldCnt++;
        }
        if (proto.hasStartTime()) {
            this.setStartTime(proto.getStartTime());
            fieldCnt++;
        }
        if (proto.hasEndTime()) {
            this.setEndTime(proto.getEndTime());
            fieldCnt++;
        }
        if (proto.hasLayer()) {
            this.setLayer(proto.getLayer());
            fieldCnt++;
        }
        if (proto.hasSourceType()) {
            this.setSourceType(proto.getSourceType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DevBuff.Builder getCopyDbBuilder() {
        final DevBuff.Builder builder = DevBuff.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(DevBuff.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDevBuffId() != 0) {
            builder.setDevBuffId(this.getDevBuffId());
            fieldCnt++;
        }  else if (builder.hasDevBuffId()) {
            // 清理DevBuffId
            builder.clearDevBuffId();
            fieldCnt++;
        }
        if (this.getStartTime() != 0L) {
            builder.setStartTime(this.getStartTime());
            fieldCnt++;
        }  else if (builder.hasStartTime()) {
            // 清理StartTime
            builder.clearStartTime();
            fieldCnt++;
        }
        if (this.getEndTime() != 0L) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }  else if (builder.hasEndTime()) {
            // 清理EndTime
            builder.clearEndTime();
            fieldCnt++;
        }
        if (this.getDevBuffType() != DevBuffType.forNumber(0)) {
            builder.setDevBuffType(this.getDevBuffType());
            fieldCnt++;
        }  else if (builder.hasDevBuffType()) {
            // 清理DevBuffType
            builder.clearDevBuffType();
            fieldCnt++;
        }
        if (this.getLayer() != 0) {
            builder.setLayer(this.getLayer());
            fieldCnt++;
        }  else if (builder.hasLayer()) {
            // 清理Layer
            builder.clearLayer();
            fieldCnt++;
        }
        if (this.getSourceType() != DevBuffSourceType.forNumber(0)) {
            builder.setSourceType(this.getSourceType());
            fieldCnt++;
        }  else if (builder.hasSourceType()) {
            // 清理SourceType
            builder.clearSourceType();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(DevBuff.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DEVBUFFID)) {
            builder.setDevBuffId(this.getDevBuffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTIME)) {
            builder.setStartTime(this.getStartTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTIME)) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFTYPE)) {
            builder.setDevBuffType(this.getDevBuffType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LAYER)) {
            builder.setLayer(this.getLayer());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOURCETYPE)) {
            builder.setSourceType(this.getSourceType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(DevBuff proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDevBuffId()) {
            this.innerSetDevBuffId(proto.getDevBuffId());
        } else {
            this.innerSetDevBuffId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartTime()) {
            this.innerSetStartTime(proto.getStartTime());
        } else {
            this.innerSetStartTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTime()) {
            this.innerSetEndTime(proto.getEndTime());
        } else {
            this.innerSetEndTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasDevBuffType()) {
            this.innerSetDevBuffType(proto.getDevBuffType());
        } else {
            this.innerSetDevBuffType(DevBuffType.forNumber(0));
        }
        if (proto.hasLayer()) {
            this.innerSetLayer(proto.getLayer());
        } else {
            this.innerSetLayer(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSourceType()) {
            this.innerSetSourceType(proto.getSourceType());
        } else {
            this.innerSetSourceType(DevBuffSourceType.forNumber(0));
        }
        this.markAll();
        return DevBuffProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(DevBuff proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDevBuffId()) {
            this.setDevBuffId(proto.getDevBuffId());
            fieldCnt++;
        }
        if (proto.hasStartTime()) {
            this.setStartTime(proto.getStartTime());
            fieldCnt++;
        }
        if (proto.hasEndTime()) {
            this.setEndTime(proto.getEndTime());
            fieldCnt++;
        }
        if (proto.hasDevBuffType()) {
            this.setDevBuffType(proto.getDevBuffType());
            fieldCnt++;
        }
        if (proto.hasLayer()) {
            this.setLayer(proto.getLayer());
            fieldCnt++;
        }
        if (proto.hasSourceType()) {
            this.setSourceType(proto.getSourceType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DevBuff.Builder getCopySsBuilder() {
        final DevBuff.Builder builder = DevBuff.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(DevBuff.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDevBuffId() != 0) {
            builder.setDevBuffId(this.getDevBuffId());
            fieldCnt++;
        }  else if (builder.hasDevBuffId()) {
            // 清理DevBuffId
            builder.clearDevBuffId();
            fieldCnt++;
        }
        if (this.getStartTime() != 0L) {
            builder.setStartTime(this.getStartTime());
            fieldCnt++;
        }  else if (builder.hasStartTime()) {
            // 清理StartTime
            builder.clearStartTime();
            fieldCnt++;
        }
        if (this.getEndTime() != 0L) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }  else if (builder.hasEndTime()) {
            // 清理EndTime
            builder.clearEndTime();
            fieldCnt++;
        }
        if (this.getDevBuffType() != DevBuffType.forNumber(0)) {
            builder.setDevBuffType(this.getDevBuffType());
            fieldCnt++;
        }  else if (builder.hasDevBuffType()) {
            // 清理DevBuffType
            builder.clearDevBuffType();
            fieldCnt++;
        }
        if (this.getLayer() != 0) {
            builder.setLayer(this.getLayer());
            fieldCnt++;
        }  else if (builder.hasLayer()) {
            // 清理Layer
            builder.clearLayer();
            fieldCnt++;
        }
        if (this.getSourceType() != DevBuffSourceType.forNumber(0)) {
            builder.setSourceType(this.getSourceType());
            fieldCnt++;
        }  else if (builder.hasSourceType()) {
            // 清理SourceType
            builder.clearSourceType();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(DevBuff.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DEVBUFFID)) {
            builder.setDevBuffId(this.getDevBuffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTIME)) {
            builder.setStartTime(this.getStartTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTIME)) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFTYPE)) {
            builder.setDevBuffType(this.getDevBuffType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LAYER)) {
            builder.setLayer(this.getLayer());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOURCETYPE)) {
            builder.setSourceType(this.getSourceType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(DevBuff proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDevBuffId()) {
            this.innerSetDevBuffId(proto.getDevBuffId());
        } else {
            this.innerSetDevBuffId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartTime()) {
            this.innerSetStartTime(proto.getStartTime());
        } else {
            this.innerSetStartTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTime()) {
            this.innerSetEndTime(proto.getEndTime());
        } else {
            this.innerSetEndTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasDevBuffType()) {
            this.innerSetDevBuffType(proto.getDevBuffType());
        } else {
            this.innerSetDevBuffType(DevBuffType.forNumber(0));
        }
        if (proto.hasLayer()) {
            this.innerSetLayer(proto.getLayer());
        } else {
            this.innerSetLayer(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSourceType()) {
            this.innerSetSourceType(proto.getSourceType());
        } else {
            this.innerSetSourceType(DevBuffSourceType.forNumber(0));
        }
        this.markAll();
        return DevBuffProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(DevBuff proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDevBuffId()) {
            this.setDevBuffId(proto.getDevBuffId());
            fieldCnt++;
        }
        if (proto.hasStartTime()) {
            this.setStartTime(proto.getStartTime());
            fieldCnt++;
        }
        if (proto.hasEndTime()) {
            this.setEndTime(proto.getEndTime());
            fieldCnt++;
        }
        if (proto.hasDevBuffType()) {
            this.setDevBuffType(proto.getDevBuffType());
            fieldCnt++;
        }
        if (proto.hasLayer()) {
            this.setLayer(proto.getLayer());
            fieldCnt++;
        }
        if (proto.hasSourceType()) {
            this.setSourceType(proto.getSourceType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        DevBuff.Builder builder = DevBuff.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.devBuffId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof DevBuffProp)) {
            return false;
        }
        final DevBuffProp otherNode = (DevBuffProp) node;
        if (this.devBuffId != otherNode.devBuffId) {
            return false;
        }
        if (this.startTime != otherNode.startTime) {
            return false;
        }
        if (this.endTime != otherNode.endTime) {
            return false;
        }
        if (this.devBuffType != otherNode.devBuffType) {
            return false;
        }
        if (this.layer != otherNode.layer) {
            return false;
        }
        if (this.sourceType != otherNode.sourceType) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
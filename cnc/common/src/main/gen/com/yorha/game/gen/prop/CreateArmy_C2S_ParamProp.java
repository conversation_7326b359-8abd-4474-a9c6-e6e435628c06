package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructPlayer.CreateArmy_C2S_Param;
import com.yorha.proto.Basic;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.StructPlayerPB.CreateArmy_C2S_ParamPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class CreateArmy_C2S_ParamProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TROOPINFO = 0;
    public static final int FIELD_INDEX_ARMYACTION = 1;
    public static final int FIELD_INDEX_RECOMMENDSOLDIERTYPELIST = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private TroopProp troopInfo = null;
    private ArmyActionInfoProp armyAction = null;
    private Int32ListProp recommendSoldierTypeList = null;

    public CreateArmy_C2S_ParamProp() {
        super(null, 0, FIELD_COUNT);
    }

    public CreateArmy_C2S_ParamProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get troopInfo
     *
     * @return troopInfo value
     */
    public TroopProp getTroopInfo() {
        if (this.troopInfo == null) {
            this.troopInfo = new TroopProp(this, FIELD_INDEX_TROOPINFO);
        }
        return this.troopInfo;
    }

    /**
     * get armyAction
     *
     * @return armyAction value
     */
    public ArmyActionInfoProp getArmyAction() {
        if (this.armyAction == null) {
            this.armyAction = new ArmyActionInfoProp(this, FIELD_INDEX_ARMYACTION);
        }
        return this.armyAction;
    }

    /**
     * get recommendSoldierTypeList
     *
     * @return recommendSoldierTypeList value
     */
    public Int32ListProp getRecommendSoldierTypeList() {
        if (this.recommendSoldierTypeList == null) {
            this.recommendSoldierTypeList = new Int32ListProp(this, FIELD_INDEX_RECOMMENDSOLDIERTYPELIST);
        }
        return this.recommendSoldierTypeList;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addRecommendSoldierTypeList(Integer v) {
        this.getRecommendSoldierTypeList().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getRecommendSoldierTypeListIndex(int index) {
        return this.getRecommendSoldierTypeList().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeRecommendSoldierTypeList(Integer v) {
        if (this.recommendSoldierTypeList == null) {
            return null;
        }
        if(this.recommendSoldierTypeList.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getRecommendSoldierTypeListSize() {
        if (this.recommendSoldierTypeList == null) {
            return 0;
        }
        return this.recommendSoldierTypeList.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isRecommendSoldierTypeListEmpty() {
        if (this.recommendSoldierTypeList == null) {
            return true;
        }
        return this.getRecommendSoldierTypeList().isEmpty();
    }

    /**
     * clear list
     */
    public void clearRecommendSoldierTypeList() {
        this.getRecommendSoldierTypeList().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeRecommendSoldierTypeListIndex(int index) {
        return this.getRecommendSoldierTypeList().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setRecommendSoldierTypeListIndex(int index, Integer v) {
        return this.getRecommendSoldierTypeList().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CreateArmy_C2S_ParamPB.Builder getCopyCsBuilder() {
        final CreateArmy_C2S_ParamPB.Builder builder = CreateArmy_C2S_ParamPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(CreateArmy_C2S_ParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.troopInfo != null) {
            StructPlayerPB.TroopPB.Builder tmpBuilder = StructPlayerPB.TroopPB.newBuilder();
            final int tmpFieldCnt = this.troopInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroopInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroopInfo();
            }
        }  else if (builder.hasTroopInfo()) {
            // 清理TroopInfo
            builder.clearTroopInfo();
            fieldCnt++;
        }
        if (this.armyAction != null) {
            StructPlayerPB.ArmyActionInfoPB.Builder tmpBuilder = StructPlayerPB.ArmyActionInfoPB.newBuilder();
            final int tmpFieldCnt = this.armyAction.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArmyAction(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArmyAction();
            }
        }  else if (builder.hasArmyAction()) {
            // 清理ArmyAction
            builder.clearArmyAction();
            fieldCnt++;
        }
        if (this.recommendSoldierTypeList != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.recommendSoldierTypeList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRecommendSoldierTypeList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRecommendSoldierTypeList();
            }
        }  else if (builder.hasRecommendSoldierTypeList()) {
            // 清理RecommendSoldierTypeList
            builder.clearRecommendSoldierTypeList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(CreateArmy_C2S_ParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TROOPINFO) && this.troopInfo != null) {
            final boolean needClear = !builder.hasTroopInfo();
            final int tmpFieldCnt = this.troopInfo.copyChangeToCs(builder.getTroopInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroopInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARMYACTION) && this.armyAction != null) {
            final boolean needClear = !builder.hasArmyAction();
            final int tmpFieldCnt = this.armyAction.copyChangeToCs(builder.getArmyActionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmyAction();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECOMMENDSOLDIERTYPELIST) && this.recommendSoldierTypeList != null) {
            final boolean needClear = !builder.hasRecommendSoldierTypeList();
            final int tmpFieldCnt = this.recommendSoldierTypeList.copyChangeToCs(builder.getRecommendSoldierTypeListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRecommendSoldierTypeList();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(CreateArmy_C2S_ParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TROOPINFO) && this.troopInfo != null) {
            final boolean needClear = !builder.hasTroopInfo();
            final int tmpFieldCnt = this.troopInfo.copyChangeToAndClearDeleteKeysCs(builder.getTroopInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroopInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARMYACTION) && this.armyAction != null) {
            final boolean needClear = !builder.hasArmyAction();
            final int tmpFieldCnt = this.armyAction.copyChangeToAndClearDeleteKeysCs(builder.getArmyActionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmyAction();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECOMMENDSOLDIERTYPELIST) && this.recommendSoldierTypeList != null) {
            final boolean needClear = !builder.hasRecommendSoldierTypeList();
            final int tmpFieldCnt = this.recommendSoldierTypeList.copyChangeToCs(builder.getRecommendSoldierTypeListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRecommendSoldierTypeList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(CreateArmy_C2S_ParamPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTroopInfo()) {
            this.getTroopInfo().mergeFromCs(proto.getTroopInfo());
        } else {
            if (this.troopInfo != null) {
                this.troopInfo.mergeFromCs(proto.getTroopInfo());
            }
        }
        if (proto.hasArmyAction()) {
            this.getArmyAction().mergeFromCs(proto.getArmyAction());
        } else {
            if (this.armyAction != null) {
                this.armyAction.mergeFromCs(proto.getArmyAction());
            }
        }
        if (proto.hasRecommendSoldierTypeList()) {
            this.getRecommendSoldierTypeList().mergeFromCs(proto.getRecommendSoldierTypeList());
        } else {
            if (this.recommendSoldierTypeList != null) {
                this.recommendSoldierTypeList.mergeFromCs(proto.getRecommendSoldierTypeList());
            }
        }
        this.markAll();
        return CreateArmy_C2S_ParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(CreateArmy_C2S_ParamPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTroopInfo()) {
            this.getTroopInfo().mergeChangeFromCs(proto.getTroopInfo());
            fieldCnt++;
        }
        if (proto.hasArmyAction()) {
            this.getArmyAction().mergeChangeFromCs(proto.getArmyAction());
            fieldCnt++;
        }
        if (proto.hasRecommendSoldierTypeList()) {
            this.getRecommendSoldierTypeList().mergeChangeFromCs(proto.getRecommendSoldierTypeList());
            fieldCnt++;
        }
        return fieldCnt;
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CreateArmy_C2S_Param.Builder getCopySsBuilder() {
        final CreateArmy_C2S_Param.Builder builder = CreateArmy_C2S_Param.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(CreateArmy_C2S_Param.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.troopInfo != null) {
            StructPlayer.Troop.Builder tmpBuilder = StructPlayer.Troop.newBuilder();
            final int tmpFieldCnt = this.troopInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroopInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroopInfo();
            }
        }  else if (builder.hasTroopInfo()) {
            // 清理TroopInfo
            builder.clearTroopInfo();
            fieldCnt++;
        }
        if (this.armyAction != null) {
            StructPlayer.ArmyActionInfo.Builder tmpBuilder = StructPlayer.ArmyActionInfo.newBuilder();
            final int tmpFieldCnt = this.armyAction.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArmyAction(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArmyAction();
            }
        }  else if (builder.hasArmyAction()) {
            // 清理ArmyAction
            builder.clearArmyAction();
            fieldCnt++;
        }
        if (this.recommendSoldierTypeList != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.recommendSoldierTypeList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRecommendSoldierTypeList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRecommendSoldierTypeList();
            }
        }  else if (builder.hasRecommendSoldierTypeList()) {
            // 清理RecommendSoldierTypeList
            builder.clearRecommendSoldierTypeList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(CreateArmy_C2S_Param.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TROOPINFO) && this.troopInfo != null) {
            final boolean needClear = !builder.hasTroopInfo();
            final int tmpFieldCnt = this.troopInfo.copyChangeToSs(builder.getTroopInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroopInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARMYACTION) && this.armyAction != null) {
            final boolean needClear = !builder.hasArmyAction();
            final int tmpFieldCnt = this.armyAction.copyChangeToSs(builder.getArmyActionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmyAction();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECOMMENDSOLDIERTYPELIST) && this.recommendSoldierTypeList != null) {
            final boolean needClear = !builder.hasRecommendSoldierTypeList();
            final int tmpFieldCnt = this.recommendSoldierTypeList.copyChangeToSs(builder.getRecommendSoldierTypeListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRecommendSoldierTypeList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(CreateArmy_C2S_Param proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTroopInfo()) {
            this.getTroopInfo().mergeFromSs(proto.getTroopInfo());
        } else {
            if (this.troopInfo != null) {
                this.troopInfo.mergeFromSs(proto.getTroopInfo());
            }
        }
        if (proto.hasArmyAction()) {
            this.getArmyAction().mergeFromSs(proto.getArmyAction());
        } else {
            if (this.armyAction != null) {
                this.armyAction.mergeFromSs(proto.getArmyAction());
            }
        }
        if (proto.hasRecommendSoldierTypeList()) {
            this.getRecommendSoldierTypeList().mergeFromSs(proto.getRecommendSoldierTypeList());
        } else {
            if (this.recommendSoldierTypeList != null) {
                this.recommendSoldierTypeList.mergeFromSs(proto.getRecommendSoldierTypeList());
            }
        }
        this.markAll();
        return CreateArmy_C2S_ParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(CreateArmy_C2S_Param proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTroopInfo()) {
            this.getTroopInfo().mergeChangeFromSs(proto.getTroopInfo());
            fieldCnt++;
        }
        if (proto.hasArmyAction()) {
            this.getArmyAction().mergeChangeFromSs(proto.getArmyAction());
            fieldCnt++;
        }
        if (proto.hasRecommendSoldierTypeList()) {
            this.getRecommendSoldierTypeList().mergeChangeFromSs(proto.getRecommendSoldierTypeList());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        CreateArmy_C2S_Param.Builder builder = CreateArmy_C2S_Param.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TROOPINFO) && this.troopInfo != null) {
            this.troopInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ARMYACTION) && this.armyAction != null) {
            this.armyAction.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RECOMMENDSOLDIERTYPELIST) && this.recommendSoldierTypeList != null) {
            this.recommendSoldierTypeList.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.troopInfo != null) {
            this.troopInfo.markAll();
        }
        if (this.armyAction != null) {
            this.armyAction.markAll();
        }
        if (this.recommendSoldierTypeList != null) {
            this.recommendSoldierTypeList.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof CreateArmy_C2S_ParamProp)) {
            return false;
        }
        final CreateArmy_C2S_ParamProp otherNode = (CreateArmy_C2S_ParamProp) node;
        if (!this.getTroopInfo().compareDataTo(otherNode.getTroopInfo())) {
            return false;
        }
        if (!this.getArmyAction().compareDataTo(otherNode.getArmyAction())) {
            return false;
        }
        if (!this.getRecommendSoldierTypeList().compareDataTo(otherNode.getRecommendSoldierTypeList())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
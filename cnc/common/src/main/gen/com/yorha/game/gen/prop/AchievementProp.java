package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Struct.Achievement;
import com.yorha.proto.StructPB.AchievementPB;


/**
 * <AUTHOR> auto gen
 */
public class AchievementProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ACHIEVEMENTID = 0;
    public static final int FIELD_INDEX_STATE = 1;
    public static final int FIELD_INDEX_COMPLETETSMS = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int achievementId = Constant.DEFAULT_INT_VALUE;
    private AchievementState state = AchievementState.forNumber(0);
    private long completeTsMs = Constant.DEFAULT_LONG_VALUE;

    public AchievementProp() {
        super(null, 0, FIELD_COUNT);
    }

    public AchievementProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get achievementId
     *
     * @return achievementId value
     */
    public int getAchievementId() {
        return this.achievementId;
    }

    /**
     * set achievementId && set marked
     *
     * @param achievementId new value
     * @return current object
     */
    public AchievementProp setAchievementId(int achievementId) {
        if (this.achievementId != achievementId) {
            this.mark(FIELD_INDEX_ACHIEVEMENTID);
            this.achievementId = achievementId;
        }
        return this;
    }

    /**
     * inner set achievementId
     *
     * @param achievementId new value
     */
    private void innerSetAchievementId(int achievementId) {
        this.achievementId = achievementId;
    }

    /**
     * get state
     *
     * @return state value
     */
    public AchievementState getState() {
        return this.state;
    }

    /**
     * set state && set marked
     *
     * @param state new value
     * @return current object
     */
    public AchievementProp setState(AchievementState state) {
        if (state == null) {
            throw new NullPointerException();
        }
        if (this.state != state) {
            this.mark(FIELD_INDEX_STATE);
            this.state = state;
        }
        return this;
    }

    /**
     * inner set state
     *
     * @param state new value
     */
    private void innerSetState(AchievementState state) {
        this.state = state;
    }

    /**
     * get completeTsMs
     *
     * @return completeTsMs value
     */
    public long getCompleteTsMs() {
        return this.completeTsMs;
    }

    /**
     * set completeTsMs && set marked
     *
     * @param completeTsMs new value
     * @return current object
     */
    public AchievementProp setCompleteTsMs(long completeTsMs) {
        if (this.completeTsMs != completeTsMs) {
            this.mark(FIELD_INDEX_COMPLETETSMS);
            this.completeTsMs = completeTsMs;
        }
        return this;
    }

    /**
     * inner set completeTsMs
     *
     * @param completeTsMs new value
     */
    private void innerSetCompleteTsMs(long completeTsMs) {
        this.completeTsMs = completeTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public AchievementPB.Builder getCopyCsBuilder() {
        final AchievementPB.Builder builder = AchievementPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(AchievementPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getAchievementId() != 0) {
            builder.setAchievementId(this.getAchievementId());
            fieldCnt++;
        }  else if (builder.hasAchievementId()) {
            // 清理AchievementId
            builder.clearAchievementId();
            fieldCnt++;
        }
        if (this.getState() != AchievementState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.getCompleteTsMs() != 0L) {
            builder.setCompleteTsMs(this.getCompleteTsMs());
            fieldCnt++;
        }  else if (builder.hasCompleteTsMs()) {
            // 清理CompleteTsMs
            builder.clearCompleteTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(AchievementPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTID)) {
            builder.setAchievementId(this.getAchievementId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COMPLETETSMS)) {
            builder.setCompleteTsMs(this.getCompleteTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(AchievementPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTID)) {
            builder.setAchievementId(this.getAchievementId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COMPLETETSMS)) {
            builder.setCompleteTsMs(this.getCompleteTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(AchievementPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAchievementId()) {
            this.innerSetAchievementId(proto.getAchievementId());
        } else {
            this.innerSetAchievementId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(AchievementState.forNumber(0));
        }
        if (proto.hasCompleteTsMs()) {
            this.innerSetCompleteTsMs(proto.getCompleteTsMs());
        } else {
            this.innerSetCompleteTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return AchievementProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(AchievementPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAchievementId()) {
            this.setAchievementId(proto.getAchievementId());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasCompleteTsMs()) {
            this.setCompleteTsMs(proto.getCompleteTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Achievement.Builder getCopyDbBuilder() {
        final Achievement.Builder builder = Achievement.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(Achievement.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getAchievementId() != 0) {
            builder.setAchievementId(this.getAchievementId());
            fieldCnt++;
        }  else if (builder.hasAchievementId()) {
            // 清理AchievementId
            builder.clearAchievementId();
            fieldCnt++;
        }
        if (this.getState() != AchievementState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.getCompleteTsMs() != 0L) {
            builder.setCompleteTsMs(this.getCompleteTsMs());
            fieldCnt++;
        }  else if (builder.hasCompleteTsMs()) {
            // 清理CompleteTsMs
            builder.clearCompleteTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(Achievement.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTID)) {
            builder.setAchievementId(this.getAchievementId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COMPLETETSMS)) {
            builder.setCompleteTsMs(this.getCompleteTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(Achievement proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAchievementId()) {
            this.innerSetAchievementId(proto.getAchievementId());
        } else {
            this.innerSetAchievementId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(AchievementState.forNumber(0));
        }
        if (proto.hasCompleteTsMs()) {
            this.innerSetCompleteTsMs(proto.getCompleteTsMs());
        } else {
            this.innerSetCompleteTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return AchievementProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(Achievement proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAchievementId()) {
            this.setAchievementId(proto.getAchievementId());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasCompleteTsMs()) {
            this.setCompleteTsMs(proto.getCompleteTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Achievement.Builder getCopySsBuilder() {
        final Achievement.Builder builder = Achievement.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(Achievement.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getAchievementId() != 0) {
            builder.setAchievementId(this.getAchievementId());
            fieldCnt++;
        }  else if (builder.hasAchievementId()) {
            // 清理AchievementId
            builder.clearAchievementId();
            fieldCnt++;
        }
        if (this.getState() != AchievementState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.getCompleteTsMs() != 0L) {
            builder.setCompleteTsMs(this.getCompleteTsMs());
            fieldCnt++;
        }  else if (builder.hasCompleteTsMs()) {
            // 清理CompleteTsMs
            builder.clearCompleteTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(Achievement.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTID)) {
            builder.setAchievementId(this.getAchievementId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COMPLETETSMS)) {
            builder.setCompleteTsMs(this.getCompleteTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(Achievement proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAchievementId()) {
            this.innerSetAchievementId(proto.getAchievementId());
        } else {
            this.innerSetAchievementId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(AchievementState.forNumber(0));
        }
        if (proto.hasCompleteTsMs()) {
            this.innerSetCompleteTsMs(proto.getCompleteTsMs());
        } else {
            this.innerSetCompleteTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return AchievementProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(Achievement proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAchievementId()) {
            this.setAchievementId(proto.getAchievementId());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasCompleteTsMs()) {
            this.setCompleteTsMs(proto.getCompleteTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        Achievement.Builder builder = Achievement.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.achievementId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof AchievementProp)) {
            return false;
        }
        final AchievementProp otherNode = (AchievementProp) node;
        if (this.achievementId != otherNode.achievementId) {
            return false;
        }
        if (this.state != otherNode.state) {
            return false;
        }
        if (this.completeTsMs != otherNode.completeTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
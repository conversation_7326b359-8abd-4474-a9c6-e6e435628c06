package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Player.ScenePlayerArmyStatus;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.ScenePlayerArmyStatusPB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class ScenePlayerArmyStatusProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_ARMYID = 0;
    public static final int FIELD_INDEX_STATE = 1;
    public static final int FIELD_INDEX_STARTTSMS = 2;
    public static final int FIELD_INDEX_ENDTSMS = 3;
    public static final int FIELD_INDEX_COLLECTNUM = 4;
    public static final int FIELD_INDEX_CURBURDEN = 5;
    public static final int FIELD_INDEX_TARGET = 6;
    public static final int FIELD_INDEX_ISBATTLE = 7;
    public static final int FIELD_INDEX_ISINPASSING = 8;
    public static final int FIELD_INDEX_HUNTINGMODEL = 9;

    public static final int FIELD_COUNT = 10;

    private long markBits0 = 0L;

    private long armyId = Constant.DEFAULT_LONG_VALUE;
    private ArmyDetailState state = ArmyDetailState.forNumber(0);
    private long startTsMs = Constant.DEFAULT_LONG_VALUE;
    private long endTsMs = Constant.DEFAULT_LONG_VALUE;
    private long collectNum = Constant.DEFAULT_LONG_VALUE;
    private long curBurden = Constant.DEFAULT_LONG_VALUE;
    private ScenePlayerArmyTargetProp target = null;
    private boolean isBattle = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean isInPassing = Constant.DEFAULT_BOOLEAN_VALUE;
    private HuntingModelProp huntingModel = null;

    public ScenePlayerArmyStatusProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ScenePlayerArmyStatusProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get armyId
     *
     * @return armyId value
     */
    public long getArmyId() {
        return this.armyId;
    }

    /**
     * set armyId && set marked
     *
     * @param armyId new value
     * @return current object
     */
    public ScenePlayerArmyStatusProp setArmyId(long armyId) {
        if (this.armyId != armyId) {
            this.mark(FIELD_INDEX_ARMYID);
            this.armyId = armyId;
        }
        return this;
    }

    /**
     * inner set armyId
     *
     * @param armyId new value
     */
    private void innerSetArmyId(long armyId) {
        this.armyId = armyId;
    }

    /**
     * get state
     *
     * @return state value
     */
    public ArmyDetailState getState() {
        return this.state;
    }

    /**
     * set state && set marked
     *
     * @param state new value
     * @return current object
     */
    public ScenePlayerArmyStatusProp setState(ArmyDetailState state) {
        if (state == null) {
            throw new NullPointerException();
        }
        if (this.state != state) {
            this.mark(FIELD_INDEX_STATE);
            this.state = state;
        }
        return this;
    }

    /**
     * inner set state
     *
     * @param state new value
     */
    private void innerSetState(ArmyDetailState state) {
        this.state = state;
    }

    /**
     * get startTsMs
     *
     * @return startTsMs value
     */
    public long getStartTsMs() {
        return this.startTsMs;
    }

    /**
     * set startTsMs && set marked
     *
     * @param startTsMs new value
     * @return current object
     */
    public ScenePlayerArmyStatusProp setStartTsMs(long startTsMs) {
        if (this.startTsMs != startTsMs) {
            this.mark(FIELD_INDEX_STARTTSMS);
            this.startTsMs = startTsMs;
        }
        return this;
    }

    /**
     * inner set startTsMs
     *
     * @param startTsMs new value
     */
    private void innerSetStartTsMs(long startTsMs) {
        this.startTsMs = startTsMs;
    }

    /**
     * get endTsMs
     *
     * @return endTsMs value
     */
    public long getEndTsMs() {
        return this.endTsMs;
    }

    /**
     * set endTsMs && set marked
     *
     * @param endTsMs new value
     * @return current object
     */
    public ScenePlayerArmyStatusProp setEndTsMs(long endTsMs) {
        if (this.endTsMs != endTsMs) {
            this.mark(FIELD_INDEX_ENDTSMS);
            this.endTsMs = endTsMs;
        }
        return this;
    }

    /**
     * inner set endTsMs
     *
     * @param endTsMs new value
     */
    private void innerSetEndTsMs(long endTsMs) {
        this.endTsMs = endTsMs;
    }

    /**
     * get collectNum
     *
     * @return collectNum value
     */
    public long getCollectNum() {
        return this.collectNum;
    }

    /**
     * set collectNum && set marked
     *
     * @param collectNum new value
     * @return current object
     */
    public ScenePlayerArmyStatusProp setCollectNum(long collectNum) {
        if (this.collectNum != collectNum) {
            this.mark(FIELD_INDEX_COLLECTNUM);
            this.collectNum = collectNum;
        }
        return this;
    }

    /**
     * inner set collectNum
     *
     * @param collectNum new value
     */
    private void innerSetCollectNum(long collectNum) {
        this.collectNum = collectNum;
    }

    /**
     * get curBurden
     *
     * @return curBurden value
     */
    public long getCurBurden() {
        return this.curBurden;
    }

    /**
     * set curBurden && set marked
     *
     * @param curBurden new value
     * @return current object
     */
    public ScenePlayerArmyStatusProp setCurBurden(long curBurden) {
        if (this.curBurden != curBurden) {
            this.mark(FIELD_INDEX_CURBURDEN);
            this.curBurden = curBurden;
        }
        return this;
    }

    /**
     * inner set curBurden
     *
     * @param curBurden new value
     */
    private void innerSetCurBurden(long curBurden) {
        this.curBurden = curBurden;
    }

    /**
     * get target
     *
     * @return target value
     */
    public ScenePlayerArmyTargetProp getTarget() {
        if (this.target == null) {
            this.target = new ScenePlayerArmyTargetProp(this, FIELD_INDEX_TARGET);
        }
        return this.target;
    }

    /**
     * get isBattle
     *
     * @return isBattle value
     */
    public boolean getIsBattle() {
        return this.isBattle;
    }

    /**
     * set isBattle && set marked
     *
     * @param isBattle new value
     * @return current object
     */
    public ScenePlayerArmyStatusProp setIsBattle(boolean isBattle) {
        if (this.isBattle != isBattle) {
            this.mark(FIELD_INDEX_ISBATTLE);
            this.isBattle = isBattle;
        }
        return this;
    }

    /**
     * inner set isBattle
     *
     * @param isBattle new value
     */
    private void innerSetIsBattle(boolean isBattle) {
        this.isBattle = isBattle;
    }

    /**
     * get isInPassing
     *
     * @return isInPassing value
     */
    public boolean getIsInPassing() {
        return this.isInPassing;
    }

    /**
     * set isInPassing && set marked
     *
     * @param isInPassing new value
     * @return current object
     */
    public ScenePlayerArmyStatusProp setIsInPassing(boolean isInPassing) {
        if (this.isInPassing != isInPassing) {
            this.mark(FIELD_INDEX_ISINPASSING);
            this.isInPassing = isInPassing;
        }
        return this;
    }

    /**
     * inner set isInPassing
     *
     * @param isInPassing new value
     */
    private void innerSetIsInPassing(boolean isInPassing) {
        this.isInPassing = isInPassing;
    }

    /**
     * get huntingModel
     *
     * @return huntingModel value
     */
    public HuntingModelProp getHuntingModel() {
        if (this.huntingModel == null) {
            this.huntingModel = new HuntingModelProp(this, FIELD_INDEX_HUNTINGMODEL);
        }
        return this.huntingModel;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerArmyStatusPB.Builder getCopyCsBuilder() {
        final ScenePlayerArmyStatusPB.Builder builder = ScenePlayerArmyStatusPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ScenePlayerArmyStatusPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getArmyId() != 0L) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }  else if (builder.hasArmyId()) {
            // 清理ArmyId
            builder.clearArmyId();
            fieldCnt++;
        }
        if (this.getState() != ArmyDetailState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.getStartTsMs() != 0L) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStartTsMs()) {
            // 清理StartTsMs
            builder.clearStartTsMs();
            fieldCnt++;
        }
        if (this.getEndTsMs() != 0L) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }  else if (builder.hasEndTsMs()) {
            // 清理EndTsMs
            builder.clearEndTsMs();
            fieldCnt++;
        }
        if (this.getCollectNum() != 0L) {
            builder.setCollectNum(this.getCollectNum());
            fieldCnt++;
        }  else if (builder.hasCollectNum()) {
            // 清理CollectNum
            builder.clearCollectNum();
            fieldCnt++;
        }
        if (this.getCurBurden() != 0L) {
            builder.setCurBurden(this.getCurBurden());
            fieldCnt++;
        }  else if (builder.hasCurBurden()) {
            // 清理CurBurden
            builder.clearCurBurden();
            fieldCnt++;
        }
        if (this.target != null) {
            PlayerPB.ScenePlayerArmyTargetPB.Builder tmpBuilder = PlayerPB.ScenePlayerArmyTargetPB.newBuilder();
            final int tmpFieldCnt = this.target.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTarget(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTarget();
            }
        }  else if (builder.hasTarget()) {
            // 清理Target
            builder.clearTarget();
            fieldCnt++;
        }
        if (this.getIsBattle()) {
            builder.setIsBattle(this.getIsBattle());
            fieldCnt++;
        }  else if (builder.hasIsBattle()) {
            // 清理IsBattle
            builder.clearIsBattle();
            fieldCnt++;
        }
        if (this.getIsInPassing()) {
            builder.setIsInPassing(this.getIsInPassing());
            fieldCnt++;
        }  else if (builder.hasIsInPassing()) {
            // 清理IsInPassing
            builder.clearIsInPassing();
            fieldCnt++;
        }
        if (this.huntingModel != null) {
            PlayerPB.HuntingModelPB.Builder tmpBuilder = PlayerPB.HuntingModelPB.newBuilder();
            final int tmpFieldCnt = this.huntingModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHuntingModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHuntingModel();
            }
        }  else if (builder.hasHuntingModel()) {
            // 清理HuntingModel
            builder.clearHuntingModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ScenePlayerArmyStatusPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COLLECTNUM)) {
            builder.setCollectNum(this.getCollectNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURBURDEN)) {
            builder.setCurBurden(this.getCurBurden());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGET) && this.target != null) {
            final boolean needClear = !builder.hasTarget();
            final int tmpFieldCnt = this.target.copyChangeToCs(builder.getTargetBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTarget();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISBATTLE)) {
            builder.setIsBattle(this.getIsBattle());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISINPASSING)) {
            builder.setIsInPassing(this.getIsInPassing());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HUNTINGMODEL) && this.huntingModel != null) {
            final boolean needClear = !builder.hasHuntingModel();
            final int tmpFieldCnt = this.huntingModel.copyChangeToCs(builder.getHuntingModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHuntingModel();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ScenePlayerArmyStatusPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COLLECTNUM)) {
            builder.setCollectNum(this.getCollectNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURBURDEN)) {
            builder.setCurBurden(this.getCurBurden());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGET) && this.target != null) {
            final boolean needClear = !builder.hasTarget();
            final int tmpFieldCnt = this.target.copyChangeToAndClearDeleteKeysCs(builder.getTargetBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTarget();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISBATTLE)) {
            builder.setIsBattle(this.getIsBattle());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISINPASSING)) {
            builder.setIsInPassing(this.getIsInPassing());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HUNTINGMODEL) && this.huntingModel != null) {
            final boolean needClear = !builder.hasHuntingModel();
            final int tmpFieldCnt = this.huntingModel.copyChangeToAndClearDeleteKeysCs(builder.getHuntingModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHuntingModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ScenePlayerArmyStatusPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasArmyId()) {
            this.innerSetArmyId(proto.getArmyId());
        } else {
            this.innerSetArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(ArmyDetailState.forNumber(0));
        }
        if (proto.hasStartTsMs()) {
            this.innerSetStartTsMs(proto.getStartTsMs());
        } else {
            this.innerSetStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTsMs()) {
            this.innerSetEndTsMs(proto.getEndTsMs());
        } else {
            this.innerSetEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCollectNum()) {
            this.innerSetCollectNum(proto.getCollectNum());
        } else {
            this.innerSetCollectNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCurBurden()) {
            this.innerSetCurBurden(proto.getCurBurden());
        } else {
            this.innerSetCurBurden(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTarget()) {
            this.getTarget().mergeFromCs(proto.getTarget());
        } else {
            if (this.target != null) {
                this.target.mergeFromCs(proto.getTarget());
            }
        }
        if (proto.hasIsBattle()) {
            this.innerSetIsBattle(proto.getIsBattle());
        } else {
            this.innerSetIsBattle(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasIsInPassing()) {
            this.innerSetIsInPassing(proto.getIsInPassing());
        } else {
            this.innerSetIsInPassing(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasHuntingModel()) {
            this.getHuntingModel().mergeFromCs(proto.getHuntingModel());
        } else {
            if (this.huntingModel != null) {
                this.huntingModel.mergeFromCs(proto.getHuntingModel());
            }
        }
        this.markAll();
        return ScenePlayerArmyStatusProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ScenePlayerArmyStatusPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasArmyId()) {
            this.setArmyId(proto.getArmyId());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasStartTsMs()) {
            this.setStartTsMs(proto.getStartTsMs());
            fieldCnt++;
        }
        if (proto.hasEndTsMs()) {
            this.setEndTsMs(proto.getEndTsMs());
            fieldCnt++;
        }
        if (proto.hasCollectNum()) {
            this.setCollectNum(proto.getCollectNum());
            fieldCnt++;
        }
        if (proto.hasCurBurden()) {
            this.setCurBurden(proto.getCurBurden());
            fieldCnt++;
        }
        if (proto.hasTarget()) {
            this.getTarget().mergeChangeFromCs(proto.getTarget());
            fieldCnt++;
        }
        if (proto.hasIsBattle()) {
            this.setIsBattle(proto.getIsBattle());
            fieldCnt++;
        }
        if (proto.hasIsInPassing()) {
            this.setIsInPassing(proto.getIsInPassing());
            fieldCnt++;
        }
        if (proto.hasHuntingModel()) {
            this.getHuntingModel().mergeChangeFromCs(proto.getHuntingModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerArmyStatus.Builder getCopyDbBuilder() {
        final ScenePlayerArmyStatus.Builder builder = ScenePlayerArmyStatus.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ScenePlayerArmyStatus.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getArmyId() != 0L) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }  else if (builder.hasArmyId()) {
            // 清理ArmyId
            builder.clearArmyId();
            fieldCnt++;
        }
        if (this.getState() != ArmyDetailState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.getStartTsMs() != 0L) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStartTsMs()) {
            // 清理StartTsMs
            builder.clearStartTsMs();
            fieldCnt++;
        }
        if (this.getEndTsMs() != 0L) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }  else if (builder.hasEndTsMs()) {
            // 清理EndTsMs
            builder.clearEndTsMs();
            fieldCnt++;
        }
        if (this.getCollectNum() != 0L) {
            builder.setCollectNum(this.getCollectNum());
            fieldCnt++;
        }  else if (builder.hasCollectNum()) {
            // 清理CollectNum
            builder.clearCollectNum();
            fieldCnt++;
        }
        if (this.getCurBurden() != 0L) {
            builder.setCurBurden(this.getCurBurden());
            fieldCnt++;
        }  else if (builder.hasCurBurden()) {
            // 清理CurBurden
            builder.clearCurBurden();
            fieldCnt++;
        }
        if (this.target != null) {
            Player.ScenePlayerArmyTarget.Builder tmpBuilder = Player.ScenePlayerArmyTarget.newBuilder();
            final int tmpFieldCnt = this.target.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTarget(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTarget();
            }
        }  else if (builder.hasTarget()) {
            // 清理Target
            builder.clearTarget();
            fieldCnt++;
        }
        if (this.getIsBattle()) {
            builder.setIsBattle(this.getIsBattle());
            fieldCnt++;
        }  else if (builder.hasIsBattle()) {
            // 清理IsBattle
            builder.clearIsBattle();
            fieldCnt++;
        }
        if (this.getIsInPassing()) {
            builder.setIsInPassing(this.getIsInPassing());
            fieldCnt++;
        }  else if (builder.hasIsInPassing()) {
            // 清理IsInPassing
            builder.clearIsInPassing();
            fieldCnt++;
        }
        if (this.huntingModel != null) {
            Player.HuntingModel.Builder tmpBuilder = Player.HuntingModel.newBuilder();
            final int tmpFieldCnt = this.huntingModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHuntingModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHuntingModel();
            }
        }  else if (builder.hasHuntingModel()) {
            // 清理HuntingModel
            builder.clearHuntingModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ScenePlayerArmyStatus.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COLLECTNUM)) {
            builder.setCollectNum(this.getCollectNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURBURDEN)) {
            builder.setCurBurden(this.getCurBurden());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGET) && this.target != null) {
            final boolean needClear = !builder.hasTarget();
            final int tmpFieldCnt = this.target.copyChangeToDb(builder.getTargetBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTarget();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISBATTLE)) {
            builder.setIsBattle(this.getIsBattle());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISINPASSING)) {
            builder.setIsInPassing(this.getIsInPassing());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HUNTINGMODEL) && this.huntingModel != null) {
            final boolean needClear = !builder.hasHuntingModel();
            final int tmpFieldCnt = this.huntingModel.copyChangeToDb(builder.getHuntingModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHuntingModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ScenePlayerArmyStatus proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasArmyId()) {
            this.innerSetArmyId(proto.getArmyId());
        } else {
            this.innerSetArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(ArmyDetailState.forNumber(0));
        }
        if (proto.hasStartTsMs()) {
            this.innerSetStartTsMs(proto.getStartTsMs());
        } else {
            this.innerSetStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTsMs()) {
            this.innerSetEndTsMs(proto.getEndTsMs());
        } else {
            this.innerSetEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCollectNum()) {
            this.innerSetCollectNum(proto.getCollectNum());
        } else {
            this.innerSetCollectNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCurBurden()) {
            this.innerSetCurBurden(proto.getCurBurden());
        } else {
            this.innerSetCurBurden(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTarget()) {
            this.getTarget().mergeFromDb(proto.getTarget());
        } else {
            if (this.target != null) {
                this.target.mergeFromDb(proto.getTarget());
            }
        }
        if (proto.hasIsBattle()) {
            this.innerSetIsBattle(proto.getIsBattle());
        } else {
            this.innerSetIsBattle(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasIsInPassing()) {
            this.innerSetIsInPassing(proto.getIsInPassing());
        } else {
            this.innerSetIsInPassing(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasHuntingModel()) {
            this.getHuntingModel().mergeFromDb(proto.getHuntingModel());
        } else {
            if (this.huntingModel != null) {
                this.huntingModel.mergeFromDb(proto.getHuntingModel());
            }
        }
        this.markAll();
        return ScenePlayerArmyStatusProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ScenePlayerArmyStatus proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasArmyId()) {
            this.setArmyId(proto.getArmyId());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasStartTsMs()) {
            this.setStartTsMs(proto.getStartTsMs());
            fieldCnt++;
        }
        if (proto.hasEndTsMs()) {
            this.setEndTsMs(proto.getEndTsMs());
            fieldCnt++;
        }
        if (proto.hasCollectNum()) {
            this.setCollectNum(proto.getCollectNum());
            fieldCnt++;
        }
        if (proto.hasCurBurden()) {
            this.setCurBurden(proto.getCurBurden());
            fieldCnt++;
        }
        if (proto.hasTarget()) {
            this.getTarget().mergeChangeFromDb(proto.getTarget());
            fieldCnt++;
        }
        if (proto.hasIsBattle()) {
            this.setIsBattle(proto.getIsBattle());
            fieldCnt++;
        }
        if (proto.hasIsInPassing()) {
            this.setIsInPassing(proto.getIsInPassing());
            fieldCnt++;
        }
        if (proto.hasHuntingModel()) {
            this.getHuntingModel().mergeChangeFromDb(proto.getHuntingModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerArmyStatus.Builder getCopySsBuilder() {
        final ScenePlayerArmyStatus.Builder builder = ScenePlayerArmyStatus.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ScenePlayerArmyStatus.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getArmyId() != 0L) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }  else if (builder.hasArmyId()) {
            // 清理ArmyId
            builder.clearArmyId();
            fieldCnt++;
        }
        if (this.getState() != ArmyDetailState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.getStartTsMs() != 0L) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStartTsMs()) {
            // 清理StartTsMs
            builder.clearStartTsMs();
            fieldCnt++;
        }
        if (this.getEndTsMs() != 0L) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }  else if (builder.hasEndTsMs()) {
            // 清理EndTsMs
            builder.clearEndTsMs();
            fieldCnt++;
        }
        if (this.getCollectNum() != 0L) {
            builder.setCollectNum(this.getCollectNum());
            fieldCnt++;
        }  else if (builder.hasCollectNum()) {
            // 清理CollectNum
            builder.clearCollectNum();
            fieldCnt++;
        }
        if (this.getCurBurden() != 0L) {
            builder.setCurBurden(this.getCurBurden());
            fieldCnt++;
        }  else if (builder.hasCurBurden()) {
            // 清理CurBurden
            builder.clearCurBurden();
            fieldCnt++;
        }
        if (this.target != null) {
            Player.ScenePlayerArmyTarget.Builder tmpBuilder = Player.ScenePlayerArmyTarget.newBuilder();
            final int tmpFieldCnt = this.target.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTarget(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTarget();
            }
        }  else if (builder.hasTarget()) {
            // 清理Target
            builder.clearTarget();
            fieldCnt++;
        }
        if (this.getIsBattle()) {
            builder.setIsBattle(this.getIsBattle());
            fieldCnt++;
        }  else if (builder.hasIsBattle()) {
            // 清理IsBattle
            builder.clearIsBattle();
            fieldCnt++;
        }
        if (this.getIsInPassing()) {
            builder.setIsInPassing(this.getIsInPassing());
            fieldCnt++;
        }  else if (builder.hasIsInPassing()) {
            // 清理IsInPassing
            builder.clearIsInPassing();
            fieldCnt++;
        }
        if (this.huntingModel != null) {
            Player.HuntingModel.Builder tmpBuilder = Player.HuntingModel.newBuilder();
            final int tmpFieldCnt = this.huntingModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHuntingModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHuntingModel();
            }
        }  else if (builder.hasHuntingModel()) {
            // 清理HuntingModel
            builder.clearHuntingModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ScenePlayerArmyStatus.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COLLECTNUM)) {
            builder.setCollectNum(this.getCollectNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURBURDEN)) {
            builder.setCurBurden(this.getCurBurden());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGET) && this.target != null) {
            final boolean needClear = !builder.hasTarget();
            final int tmpFieldCnt = this.target.copyChangeToSs(builder.getTargetBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTarget();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISBATTLE)) {
            builder.setIsBattle(this.getIsBattle());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISINPASSING)) {
            builder.setIsInPassing(this.getIsInPassing());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HUNTINGMODEL) && this.huntingModel != null) {
            final boolean needClear = !builder.hasHuntingModel();
            final int tmpFieldCnt = this.huntingModel.copyChangeToSs(builder.getHuntingModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHuntingModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ScenePlayerArmyStatus proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasArmyId()) {
            this.innerSetArmyId(proto.getArmyId());
        } else {
            this.innerSetArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(ArmyDetailState.forNumber(0));
        }
        if (proto.hasStartTsMs()) {
            this.innerSetStartTsMs(proto.getStartTsMs());
        } else {
            this.innerSetStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTsMs()) {
            this.innerSetEndTsMs(proto.getEndTsMs());
        } else {
            this.innerSetEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCollectNum()) {
            this.innerSetCollectNum(proto.getCollectNum());
        } else {
            this.innerSetCollectNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCurBurden()) {
            this.innerSetCurBurden(proto.getCurBurden());
        } else {
            this.innerSetCurBurden(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTarget()) {
            this.getTarget().mergeFromSs(proto.getTarget());
        } else {
            if (this.target != null) {
                this.target.mergeFromSs(proto.getTarget());
            }
        }
        if (proto.hasIsBattle()) {
            this.innerSetIsBattle(proto.getIsBattle());
        } else {
            this.innerSetIsBattle(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasIsInPassing()) {
            this.innerSetIsInPassing(proto.getIsInPassing());
        } else {
            this.innerSetIsInPassing(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasHuntingModel()) {
            this.getHuntingModel().mergeFromSs(proto.getHuntingModel());
        } else {
            if (this.huntingModel != null) {
                this.huntingModel.mergeFromSs(proto.getHuntingModel());
            }
        }
        this.markAll();
        return ScenePlayerArmyStatusProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ScenePlayerArmyStatus proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasArmyId()) {
            this.setArmyId(proto.getArmyId());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasStartTsMs()) {
            this.setStartTsMs(proto.getStartTsMs());
            fieldCnt++;
        }
        if (proto.hasEndTsMs()) {
            this.setEndTsMs(proto.getEndTsMs());
            fieldCnt++;
        }
        if (proto.hasCollectNum()) {
            this.setCollectNum(proto.getCollectNum());
            fieldCnt++;
        }
        if (proto.hasCurBurden()) {
            this.setCurBurden(proto.getCurBurden());
            fieldCnt++;
        }
        if (proto.hasTarget()) {
            this.getTarget().mergeChangeFromSs(proto.getTarget());
            fieldCnt++;
        }
        if (proto.hasIsBattle()) {
            this.setIsBattle(proto.getIsBattle());
            fieldCnt++;
        }
        if (proto.hasIsInPassing()) {
            this.setIsInPassing(proto.getIsInPassing());
            fieldCnt++;
        }
        if (proto.hasHuntingModel()) {
            this.getHuntingModel().mergeChangeFromSs(proto.getHuntingModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ScenePlayerArmyStatus.Builder builder = ScenePlayerArmyStatus.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TARGET) && this.target != null) {
            this.target.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_HUNTINGMODEL) && this.huntingModel != null) {
            this.huntingModel.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.target != null) {
            this.target.markAll();
        }
        if (this.huntingModel != null) {
            this.huntingModel.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.armyId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ScenePlayerArmyStatusProp)) {
            return false;
        }
        final ScenePlayerArmyStatusProp otherNode = (ScenePlayerArmyStatusProp) node;
        if (this.armyId != otherNode.armyId) {
            return false;
        }
        if (this.state != otherNode.state) {
            return false;
        }
        if (this.startTsMs != otherNode.startTsMs) {
            return false;
        }
        if (this.endTsMs != otherNode.endTsMs) {
            return false;
        }
        if (this.collectNum != otherNode.collectNum) {
            return false;
        }
        if (this.curBurden != otherNode.curBurden) {
            return false;
        }
        if (!this.getTarget().compareDataTo(otherNode.getTarget())) {
            return false;
        }
        if (this.isBattle != otherNode.isBattle) {
            return false;
        }
        if (this.isInPassing != otherNode.isInPassing) {
            return false;
        }
        if (!this.getHuntingModel().compareDataTo(otherNode.getHuntingModel())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 54;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
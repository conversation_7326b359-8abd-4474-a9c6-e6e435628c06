package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructMail.MailKingBuffOrSkillData;
import com.yorha.proto.StructMailPB.MailKingBuffOrSkillDataPB;


/**
 * <AUTHOR> auto gen
 */
public class MailKingBuffOrSkillDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ISSKILLDATA = 0;
    public static final int FIELD_INDEX_CONFIGID = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private boolean isSkillData = Constant.DEFAULT_BOOLEAN_VALUE;
    private int configId = Constant.DEFAULT_INT_VALUE;

    public MailKingBuffOrSkillDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MailKingBuffOrSkillDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get isSkillData
     *
     * @return isSkillData value
     */
    public boolean getIsSkillData() {
        return this.isSkillData;
    }

    /**
     * set isSkillData && set marked
     *
     * @param isSkillData new value
     * @return current object
     */
    public MailKingBuffOrSkillDataProp setIsSkillData(boolean isSkillData) {
        if (this.isSkillData != isSkillData) {
            this.mark(FIELD_INDEX_ISSKILLDATA);
            this.isSkillData = isSkillData;
        }
        return this;
    }

    /**
     * inner set isSkillData
     *
     * @param isSkillData new value
     */
    private void innerSetIsSkillData(boolean isSkillData) {
        this.isSkillData = isSkillData;
    }

    /**
     * get configId
     *
     * @return configId value
     */
    public int getConfigId() {
        return this.configId;
    }

    /**
     * set configId && set marked
     *
     * @param configId new value
     * @return current object
     */
    public MailKingBuffOrSkillDataProp setConfigId(int configId) {
        if (this.configId != configId) {
            this.mark(FIELD_INDEX_CONFIGID);
            this.configId = configId;
        }
        return this;
    }

    /**
     * inner set configId
     *
     * @param configId new value
     */
    private void innerSetConfigId(int configId) {
        this.configId = configId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailKingBuffOrSkillDataPB.Builder getCopyCsBuilder() {
        final MailKingBuffOrSkillDataPB.Builder builder = MailKingBuffOrSkillDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MailKingBuffOrSkillDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIsSkillData()) {
            builder.setIsSkillData(this.getIsSkillData());
            fieldCnt++;
        }  else if (builder.hasIsSkillData()) {
            // 清理IsSkillData
            builder.clearIsSkillData();
            fieldCnt++;
        }
        if (this.getConfigId() != 0) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }  else if (builder.hasConfigId()) {
            // 清理ConfigId
            builder.clearConfigId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MailKingBuffOrSkillDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISSKILLDATA)) {
            builder.setIsSkillData(this.getIsSkillData());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONFIGID)) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MailKingBuffOrSkillDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISSKILLDATA)) {
            builder.setIsSkillData(this.getIsSkillData());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONFIGID)) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MailKingBuffOrSkillDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIsSkillData()) {
            this.innerSetIsSkillData(proto.getIsSkillData());
        } else {
            this.innerSetIsSkillData(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasConfigId()) {
            this.innerSetConfigId(proto.getConfigId());
        } else {
            this.innerSetConfigId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return MailKingBuffOrSkillDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MailKingBuffOrSkillDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIsSkillData()) {
            this.setIsSkillData(proto.getIsSkillData());
            fieldCnt++;
        }
        if (proto.hasConfigId()) {
            this.setConfigId(proto.getConfigId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailKingBuffOrSkillData.Builder getCopyDbBuilder() {
        final MailKingBuffOrSkillData.Builder builder = MailKingBuffOrSkillData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MailKingBuffOrSkillData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIsSkillData()) {
            builder.setIsSkillData(this.getIsSkillData());
            fieldCnt++;
        }  else if (builder.hasIsSkillData()) {
            // 清理IsSkillData
            builder.clearIsSkillData();
            fieldCnt++;
        }
        if (this.getConfigId() != 0) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }  else if (builder.hasConfigId()) {
            // 清理ConfigId
            builder.clearConfigId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MailKingBuffOrSkillData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISSKILLDATA)) {
            builder.setIsSkillData(this.getIsSkillData());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONFIGID)) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MailKingBuffOrSkillData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIsSkillData()) {
            this.innerSetIsSkillData(proto.getIsSkillData());
        } else {
            this.innerSetIsSkillData(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasConfigId()) {
            this.innerSetConfigId(proto.getConfigId());
        } else {
            this.innerSetConfigId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return MailKingBuffOrSkillDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MailKingBuffOrSkillData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIsSkillData()) {
            this.setIsSkillData(proto.getIsSkillData());
            fieldCnt++;
        }
        if (proto.hasConfigId()) {
            this.setConfigId(proto.getConfigId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailKingBuffOrSkillData.Builder getCopySsBuilder() {
        final MailKingBuffOrSkillData.Builder builder = MailKingBuffOrSkillData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MailKingBuffOrSkillData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIsSkillData()) {
            builder.setIsSkillData(this.getIsSkillData());
            fieldCnt++;
        }  else if (builder.hasIsSkillData()) {
            // 清理IsSkillData
            builder.clearIsSkillData();
            fieldCnt++;
        }
        if (this.getConfigId() != 0) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }  else if (builder.hasConfigId()) {
            // 清理ConfigId
            builder.clearConfigId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MailKingBuffOrSkillData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISSKILLDATA)) {
            builder.setIsSkillData(this.getIsSkillData());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONFIGID)) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MailKingBuffOrSkillData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIsSkillData()) {
            this.innerSetIsSkillData(proto.getIsSkillData());
        } else {
            this.innerSetIsSkillData(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasConfigId()) {
            this.innerSetConfigId(proto.getConfigId());
        } else {
            this.innerSetConfigId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return MailKingBuffOrSkillDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MailKingBuffOrSkillData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIsSkillData()) {
            this.setIsSkillData(proto.getIsSkillData());
            fieldCnt++;
        }
        if (proto.hasConfigId()) {
            this.setConfigId(proto.getConfigId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MailKingBuffOrSkillData.Builder builder = MailKingBuffOrSkillData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MailKingBuffOrSkillDataProp)) {
            return false;
        }
        final MailKingBuffOrSkillDataProp otherNode = (MailKingBuffOrSkillDataProp) node;
        if (this.isSkillData != otherNode.isSkillData) {
            return false;
        }
        if (this.configId != otherNode.configId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
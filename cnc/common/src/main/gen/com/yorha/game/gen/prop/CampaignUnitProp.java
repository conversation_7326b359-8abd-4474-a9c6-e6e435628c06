package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.CampaignUnit;
import com.yorha.proto.StructPB.CampaignUnitPB;


/**
 * <AUTHOR> auto gen
 */
public class CampaignUnitProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_UNITID = 0;
    public static final int FIELD_INDEX_STAR = 1;
    public static final int FIELD_INDEX_FATIGUED = 2;
    public static final int FIELD_INDEX_DISABLE = 3;
    public static final int FIELD_INDEX_INVALID = 4;
    public static final int FIELD_INDEX_HEROID = 5;
    public static final int FIELD_INDEX_BUILDID = 6;
    public static final int FIELD_INDEX_SLOTID = 7;
    public static final int FIELD_INDEX_UNITLEVEL = 8;
    public static final int FIELD_INDEX_HEROLEVEL = 9;

    public static final int FIELD_COUNT = 10;

    private long markBits0 = 0L;

    private int unitId = Constant.DEFAULT_INT_VALUE;
    private int star = Constant.DEFAULT_INT_VALUE;
    private int fatigued = Constant.DEFAULT_INT_VALUE;
    private boolean disable = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean invalid = Constant.DEFAULT_BOOLEAN_VALUE;
    private int heroId = Constant.DEFAULT_INT_VALUE;
    private int buildId = Constant.DEFAULT_INT_VALUE;
    private int slotId = Constant.DEFAULT_INT_VALUE;
    private int unitLevel = Constant.DEFAULT_INT_VALUE;
    private int heroLevel = Constant.DEFAULT_INT_VALUE;

    public CampaignUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public CampaignUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get unitId
     *
     * @return unitId value
     */
    public int getUnitId() {
        return this.unitId;
    }

    /**
     * set unitId && set marked
     *
     * @param unitId new value
     * @return current object
     */
    public CampaignUnitProp setUnitId(int unitId) {
        if (this.unitId != unitId) {
            this.mark(FIELD_INDEX_UNITID);
            this.unitId = unitId;
        }
        return this;
    }

    /**
     * inner set unitId
     *
     * @param unitId new value
     */
    private void innerSetUnitId(int unitId) {
        this.unitId = unitId;
    }

    /**
     * get star
     *
     * @return star value
     */
    public int getStar() {
        return this.star;
    }

    /**
     * set star && set marked
     *
     * @param star new value
     * @return current object
     */
    public CampaignUnitProp setStar(int star) {
        if (this.star != star) {
            this.mark(FIELD_INDEX_STAR);
            this.star = star;
        }
        return this;
    }

    /**
     * inner set star
     *
     * @param star new value
     */
    private void innerSetStar(int star) {
        this.star = star;
    }

    /**
     * get fatigued
     *
     * @return fatigued value
     */
    public int getFatigued() {
        return this.fatigued;
    }

    /**
     * set fatigued && set marked
     *
     * @param fatigued new value
     * @return current object
     */
    public CampaignUnitProp setFatigued(int fatigued) {
        if (this.fatigued != fatigued) {
            this.mark(FIELD_INDEX_FATIGUED);
            this.fatigued = fatigued;
        }
        return this;
    }

    /**
     * inner set fatigued
     *
     * @param fatigued new value
     */
    private void innerSetFatigued(int fatigued) {
        this.fatigued = fatigued;
    }

    /**
     * get disable
     *
     * @return disable value
     */
    public boolean getDisable() {
        return this.disable;
    }

    /**
     * set disable && set marked
     *
     * @param disable new value
     * @return current object
     */
    public CampaignUnitProp setDisable(boolean disable) {
        if (this.disable != disable) {
            this.mark(FIELD_INDEX_DISABLE);
            this.disable = disable;
        }
        return this;
    }

    /**
     * inner set disable
     *
     * @param disable new value
     */
    private void innerSetDisable(boolean disable) {
        this.disable = disable;
    }

    /**
     * get invalid
     *
     * @return invalid value
     */
    public boolean getInvalid() {
        return this.invalid;
    }

    /**
     * set invalid && set marked
     *
     * @param invalid new value
     * @return current object
     */
    public CampaignUnitProp setInvalid(boolean invalid) {
        if (this.invalid != invalid) {
            this.mark(FIELD_INDEX_INVALID);
            this.invalid = invalid;
        }
        return this;
    }

    /**
     * inner set invalid
     *
     * @param invalid new value
     */
    private void innerSetInvalid(boolean invalid) {
        this.invalid = invalid;
    }

    /**
     * get heroId
     *
     * @return heroId value
     */
    public int getHeroId() {
        return this.heroId;
    }

    /**
     * set heroId && set marked
     *
     * @param heroId new value
     * @return current object
     */
    public CampaignUnitProp setHeroId(int heroId) {
        if (this.heroId != heroId) {
            this.mark(FIELD_INDEX_HEROID);
            this.heroId = heroId;
        }
        return this;
    }

    /**
     * inner set heroId
     *
     * @param heroId new value
     */
    private void innerSetHeroId(int heroId) {
        this.heroId = heroId;
    }

    /**
     * get buildId
     *
     * @return buildId value
     */
    public int getBuildId() {
        return this.buildId;
    }

    /**
     * set buildId && set marked
     *
     * @param buildId new value
     * @return current object
     */
    public CampaignUnitProp setBuildId(int buildId) {
        if (this.buildId != buildId) {
            this.mark(FIELD_INDEX_BUILDID);
            this.buildId = buildId;
        }
        return this;
    }

    /**
     * inner set buildId
     *
     * @param buildId new value
     */
    private void innerSetBuildId(int buildId) {
        this.buildId = buildId;
    }

    /**
     * get slotId
     *
     * @return slotId value
     */
    public int getSlotId() {
        return this.slotId;
    }

    /**
     * set slotId && set marked
     *
     * @param slotId new value
     * @return current object
     */
    public CampaignUnitProp setSlotId(int slotId) {
        if (this.slotId != slotId) {
            this.mark(FIELD_INDEX_SLOTID);
            this.slotId = slotId;
        }
        return this;
    }

    /**
     * inner set slotId
     *
     * @param slotId new value
     */
    private void innerSetSlotId(int slotId) {
        this.slotId = slotId;
    }

    /**
     * get unitLevel
     *
     * @return unitLevel value
     */
    public int getUnitLevel() {
        return this.unitLevel;
    }

    /**
     * set unitLevel && set marked
     *
     * @param unitLevel new value
     * @return current object
     */
    public CampaignUnitProp setUnitLevel(int unitLevel) {
        if (this.unitLevel != unitLevel) {
            this.mark(FIELD_INDEX_UNITLEVEL);
            this.unitLevel = unitLevel;
        }
        return this;
    }

    /**
     * inner set unitLevel
     *
     * @param unitLevel new value
     */
    private void innerSetUnitLevel(int unitLevel) {
        this.unitLevel = unitLevel;
    }

    /**
     * get heroLevel
     *
     * @return heroLevel value
     */
    public int getHeroLevel() {
        return this.heroLevel;
    }

    /**
     * set heroLevel && set marked
     *
     * @param heroLevel new value
     * @return current object
     */
    public CampaignUnitProp setHeroLevel(int heroLevel) {
        if (this.heroLevel != heroLevel) {
            this.mark(FIELD_INDEX_HEROLEVEL);
            this.heroLevel = heroLevel;
        }
        return this;
    }

    /**
     * inner set heroLevel
     *
     * @param heroLevel new value
     */
    private void innerSetHeroLevel(int heroLevel) {
        this.heroLevel = heroLevel;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignUnitPB.Builder getCopyCsBuilder() {
        final CampaignUnitPB.Builder builder = CampaignUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(CampaignUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        if (this.getStar() != 0) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }  else if (builder.hasStar()) {
            // 清理Star
            builder.clearStar();
            fieldCnt++;
        }
        if (this.getFatigued() != 0) {
            builder.setFatigued(this.getFatigued());
            fieldCnt++;
        }  else if (builder.hasFatigued()) {
            // 清理Fatigued
            builder.clearFatigued();
            fieldCnt++;
        }
        if (this.getDisable()) {
            builder.setDisable(this.getDisable());
            fieldCnt++;
        }  else if (builder.hasDisable()) {
            // 清理Disable
            builder.clearDisable();
            fieldCnt++;
        }
        if (this.getInvalid()) {
            builder.setInvalid(this.getInvalid());
            fieldCnt++;
        }  else if (builder.hasInvalid()) {
            // 清理Invalid
            builder.clearInvalid();
            fieldCnt++;
        }
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.getBuildId() != 0) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }  else if (builder.hasBuildId()) {
            // 清理BuildId
            builder.clearBuildId();
            fieldCnt++;
        }
        if (this.getSlotId() != 0) {
            builder.setSlotId(this.getSlotId());
            fieldCnt++;
        }  else if (builder.hasSlotId()) {
            // 清理SlotId
            builder.clearSlotId();
            fieldCnt++;
        }
        if (this.getUnitLevel() != 0) {
            builder.setUnitLevel(this.getUnitLevel());
            fieldCnt++;
        }  else if (builder.hasUnitLevel()) {
            // 清理UnitLevel
            builder.clearUnitLevel();
            fieldCnt++;
        }
        if (this.getHeroLevel() != 0) {
            builder.setHeroLevel(this.getHeroLevel());
            fieldCnt++;
        }  else if (builder.hasHeroLevel()) {
            // 清理HeroLevel
            builder.clearHeroLevel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(CampaignUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FATIGUED)) {
            builder.setFatigued(this.getFatigued());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DISABLE)) {
            builder.setDisable(this.getDisable());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVALID)) {
            builder.setInvalid(this.getInvalid());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDID)) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SLOTID)) {
            builder.setSlotId(this.getSlotId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITLEVEL)) {
            builder.setUnitLevel(this.getUnitLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROLEVEL)) {
            builder.setHeroLevel(this.getHeroLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(CampaignUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FATIGUED)) {
            builder.setFatigued(this.getFatigued());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DISABLE)) {
            builder.setDisable(this.getDisable());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVALID)) {
            builder.setInvalid(this.getInvalid());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDID)) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SLOTID)) {
            builder.setSlotId(this.getSlotId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITLEVEL)) {
            builder.setUnitLevel(this.getUnitLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROLEVEL)) {
            builder.setHeroLevel(this.getHeroLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(CampaignUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStar()) {
            this.innerSetStar(proto.getStar());
        } else {
            this.innerSetStar(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFatigued()) {
            this.innerSetFatigued(proto.getFatigued());
        } else {
            this.innerSetFatigued(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDisable()) {
            this.innerSetDisable(proto.getDisable());
        } else {
            this.innerSetDisable(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasInvalid()) {
            this.innerSetInvalid(proto.getInvalid());
        } else {
            this.innerSetInvalid(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuildId()) {
            this.innerSetBuildId(proto.getBuildId());
        } else {
            this.innerSetBuildId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSlotId()) {
            this.innerSetSlotId(proto.getSlotId());
        } else {
            this.innerSetSlotId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitLevel()) {
            this.innerSetUnitLevel(proto.getUnitLevel());
        } else {
            this.innerSetUnitLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHeroLevel()) {
            this.innerSetHeroLevel(proto.getHeroLevel());
        } else {
            this.innerSetHeroLevel(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return CampaignUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(CampaignUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        if (proto.hasStar()) {
            this.setStar(proto.getStar());
            fieldCnt++;
        }
        if (proto.hasFatigued()) {
            this.setFatigued(proto.getFatigued());
            fieldCnt++;
        }
        if (proto.hasDisable()) {
            this.setDisable(proto.getDisable());
            fieldCnt++;
        }
        if (proto.hasInvalid()) {
            this.setInvalid(proto.getInvalid());
            fieldCnt++;
        }
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasBuildId()) {
            this.setBuildId(proto.getBuildId());
            fieldCnt++;
        }
        if (proto.hasSlotId()) {
            this.setSlotId(proto.getSlotId());
            fieldCnt++;
        }
        if (proto.hasUnitLevel()) {
            this.setUnitLevel(proto.getUnitLevel());
            fieldCnt++;
        }
        if (proto.hasHeroLevel()) {
            this.setHeroLevel(proto.getHeroLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignUnit.Builder getCopyDbBuilder() {
        final CampaignUnit.Builder builder = CampaignUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(CampaignUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        if (this.getStar() != 0) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }  else if (builder.hasStar()) {
            // 清理Star
            builder.clearStar();
            fieldCnt++;
        }
        if (this.getFatigued() != 0) {
            builder.setFatigued(this.getFatigued());
            fieldCnt++;
        }  else if (builder.hasFatigued()) {
            // 清理Fatigued
            builder.clearFatigued();
            fieldCnt++;
        }
        if (this.getDisable()) {
            builder.setDisable(this.getDisable());
            fieldCnt++;
        }  else if (builder.hasDisable()) {
            // 清理Disable
            builder.clearDisable();
            fieldCnt++;
        }
        if (this.getInvalid()) {
            builder.setInvalid(this.getInvalid());
            fieldCnt++;
        }  else if (builder.hasInvalid()) {
            // 清理Invalid
            builder.clearInvalid();
            fieldCnt++;
        }
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.getBuildId() != 0) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }  else if (builder.hasBuildId()) {
            // 清理BuildId
            builder.clearBuildId();
            fieldCnt++;
        }
        if (this.getSlotId() != 0) {
            builder.setSlotId(this.getSlotId());
            fieldCnt++;
        }  else if (builder.hasSlotId()) {
            // 清理SlotId
            builder.clearSlotId();
            fieldCnt++;
        }
        if (this.getUnitLevel() != 0) {
            builder.setUnitLevel(this.getUnitLevel());
            fieldCnt++;
        }  else if (builder.hasUnitLevel()) {
            // 清理UnitLevel
            builder.clearUnitLevel();
            fieldCnt++;
        }
        if (this.getHeroLevel() != 0) {
            builder.setHeroLevel(this.getHeroLevel());
            fieldCnt++;
        }  else if (builder.hasHeroLevel()) {
            // 清理HeroLevel
            builder.clearHeroLevel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(CampaignUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FATIGUED)) {
            builder.setFatigued(this.getFatigued());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DISABLE)) {
            builder.setDisable(this.getDisable());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVALID)) {
            builder.setInvalid(this.getInvalid());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDID)) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SLOTID)) {
            builder.setSlotId(this.getSlotId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITLEVEL)) {
            builder.setUnitLevel(this.getUnitLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROLEVEL)) {
            builder.setHeroLevel(this.getHeroLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(CampaignUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStar()) {
            this.innerSetStar(proto.getStar());
        } else {
            this.innerSetStar(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFatigued()) {
            this.innerSetFatigued(proto.getFatigued());
        } else {
            this.innerSetFatigued(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDisable()) {
            this.innerSetDisable(proto.getDisable());
        } else {
            this.innerSetDisable(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasInvalid()) {
            this.innerSetInvalid(proto.getInvalid());
        } else {
            this.innerSetInvalid(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuildId()) {
            this.innerSetBuildId(proto.getBuildId());
        } else {
            this.innerSetBuildId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSlotId()) {
            this.innerSetSlotId(proto.getSlotId());
        } else {
            this.innerSetSlotId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitLevel()) {
            this.innerSetUnitLevel(proto.getUnitLevel());
        } else {
            this.innerSetUnitLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHeroLevel()) {
            this.innerSetHeroLevel(proto.getHeroLevel());
        } else {
            this.innerSetHeroLevel(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return CampaignUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(CampaignUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        if (proto.hasStar()) {
            this.setStar(proto.getStar());
            fieldCnt++;
        }
        if (proto.hasFatigued()) {
            this.setFatigued(proto.getFatigued());
            fieldCnt++;
        }
        if (proto.hasDisable()) {
            this.setDisable(proto.getDisable());
            fieldCnt++;
        }
        if (proto.hasInvalid()) {
            this.setInvalid(proto.getInvalid());
            fieldCnt++;
        }
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasBuildId()) {
            this.setBuildId(proto.getBuildId());
            fieldCnt++;
        }
        if (proto.hasSlotId()) {
            this.setSlotId(proto.getSlotId());
            fieldCnt++;
        }
        if (proto.hasUnitLevel()) {
            this.setUnitLevel(proto.getUnitLevel());
            fieldCnt++;
        }
        if (proto.hasHeroLevel()) {
            this.setHeroLevel(proto.getHeroLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignUnit.Builder getCopySsBuilder() {
        final CampaignUnit.Builder builder = CampaignUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(CampaignUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        if (this.getStar() != 0) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }  else if (builder.hasStar()) {
            // 清理Star
            builder.clearStar();
            fieldCnt++;
        }
        if (this.getFatigued() != 0) {
            builder.setFatigued(this.getFatigued());
            fieldCnt++;
        }  else if (builder.hasFatigued()) {
            // 清理Fatigued
            builder.clearFatigued();
            fieldCnt++;
        }
        if (this.getDisable()) {
            builder.setDisable(this.getDisable());
            fieldCnt++;
        }  else if (builder.hasDisable()) {
            // 清理Disable
            builder.clearDisable();
            fieldCnt++;
        }
        if (this.getInvalid()) {
            builder.setInvalid(this.getInvalid());
            fieldCnt++;
        }  else if (builder.hasInvalid()) {
            // 清理Invalid
            builder.clearInvalid();
            fieldCnt++;
        }
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.getBuildId() != 0) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }  else if (builder.hasBuildId()) {
            // 清理BuildId
            builder.clearBuildId();
            fieldCnt++;
        }
        if (this.getSlotId() != 0) {
            builder.setSlotId(this.getSlotId());
            fieldCnt++;
        }  else if (builder.hasSlotId()) {
            // 清理SlotId
            builder.clearSlotId();
            fieldCnt++;
        }
        if (this.getUnitLevel() != 0) {
            builder.setUnitLevel(this.getUnitLevel());
            fieldCnt++;
        }  else if (builder.hasUnitLevel()) {
            // 清理UnitLevel
            builder.clearUnitLevel();
            fieldCnt++;
        }
        if (this.getHeroLevel() != 0) {
            builder.setHeroLevel(this.getHeroLevel());
            fieldCnt++;
        }  else if (builder.hasHeroLevel()) {
            // 清理HeroLevel
            builder.clearHeroLevel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(CampaignUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FATIGUED)) {
            builder.setFatigued(this.getFatigued());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DISABLE)) {
            builder.setDisable(this.getDisable());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVALID)) {
            builder.setInvalid(this.getInvalid());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDID)) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SLOTID)) {
            builder.setSlotId(this.getSlotId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITLEVEL)) {
            builder.setUnitLevel(this.getUnitLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROLEVEL)) {
            builder.setHeroLevel(this.getHeroLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(CampaignUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStar()) {
            this.innerSetStar(proto.getStar());
        } else {
            this.innerSetStar(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFatigued()) {
            this.innerSetFatigued(proto.getFatigued());
        } else {
            this.innerSetFatigued(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDisable()) {
            this.innerSetDisable(proto.getDisable());
        } else {
            this.innerSetDisable(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasInvalid()) {
            this.innerSetInvalid(proto.getInvalid());
        } else {
            this.innerSetInvalid(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuildId()) {
            this.innerSetBuildId(proto.getBuildId());
        } else {
            this.innerSetBuildId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSlotId()) {
            this.innerSetSlotId(proto.getSlotId());
        } else {
            this.innerSetSlotId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitLevel()) {
            this.innerSetUnitLevel(proto.getUnitLevel());
        } else {
            this.innerSetUnitLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHeroLevel()) {
            this.innerSetHeroLevel(proto.getHeroLevel());
        } else {
            this.innerSetHeroLevel(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return CampaignUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(CampaignUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        if (proto.hasStar()) {
            this.setStar(proto.getStar());
            fieldCnt++;
        }
        if (proto.hasFatigued()) {
            this.setFatigued(proto.getFatigued());
            fieldCnt++;
        }
        if (proto.hasDisable()) {
            this.setDisable(proto.getDisable());
            fieldCnt++;
        }
        if (proto.hasInvalid()) {
            this.setInvalid(proto.getInvalid());
            fieldCnt++;
        }
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasBuildId()) {
            this.setBuildId(proto.getBuildId());
            fieldCnt++;
        }
        if (proto.hasSlotId()) {
            this.setSlotId(proto.getSlotId());
            fieldCnt++;
        }
        if (proto.hasUnitLevel()) {
            this.setUnitLevel(proto.getUnitLevel());
            fieldCnt++;
        }
        if (proto.hasHeroLevel()) {
            this.setHeroLevel(proto.getHeroLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        CampaignUnit.Builder builder = CampaignUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.unitId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof CampaignUnitProp)) {
            return false;
        }
        final CampaignUnitProp otherNode = (CampaignUnitProp) node;
        if (this.unitId != otherNode.unitId) {
            return false;
        }
        if (this.star != otherNode.star) {
            return false;
        }
        if (this.fatigued != otherNode.fatigued) {
            return false;
        }
        if (this.disable != otherNode.disable) {
            return false;
        }
        if (this.invalid != otherNode.invalid) {
            return false;
        }
        if (this.heroId != otherNode.heroId) {
            return false;
        }
        if (this.buildId != otherNode.buildId) {
            return false;
        }
        if (this.slotId != otherNode.slotId) {
            return false;
        }
        if (this.unitLevel != otherNode.unitLevel) {
            return false;
        }
        if (this.heroLevel != otherNode.heroLevel) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 54;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.Player.ChatPlayer;
import com.yorha.proto.Basic;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.ChatPlayerPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class ChatPlayerProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CHANNELINFO = 0;
    public static final int FIELD_INDEX_BANTSMS = 1;
    public static final int FIELD_INDEX_NOTDISTURBLIST = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;
    private PropertyChangeListener listener;

    private Int32ChannelInfoMapProp channelInfo = null;
    private long banTsMs = Constant.DEFAULT_LONG_VALUE;
    private Int64ListProp notDisturbList = null;

    public ChatPlayerProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ChatPlayerProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get channelInfo
     *
     * @return channelInfo value
     */
    public Int32ChannelInfoMapProp getChannelInfo() {
        if (this.channelInfo == null) {
            this.channelInfo = new Int32ChannelInfoMapProp(this, FIELD_INDEX_CHANNELINFO);
        }
        return this.channelInfo;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putChannelInfoV(ChannelInfoProp v) {
        this.getChannelInfo().put(v.getChannel(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ChannelInfoProp addEmptyChannelInfo(Integer k) {
        return this.getChannelInfo().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getChannelInfoSize() {
        if (this.channelInfo == null) {
            return 0;
        }
        return this.channelInfo.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isChannelInfoEmpty() {
        if (this.channelInfo == null) {
            return true;
        }
        return this.channelInfo.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ChannelInfoProp getChannelInfoV(Integer k) {
        if (this.channelInfo == null || !this.channelInfo.containsKey(k)) {
            return null;
        }
        return this.channelInfo.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearChannelInfo() {
        if (this.channelInfo != null) {
            this.channelInfo.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeChannelInfoV(Integer k) {
        if (this.channelInfo != null) {
            this.channelInfo.remove(k);
        }
    }
    /**
     * get banTsMs
     *
     * @return banTsMs value
     */
    public long getBanTsMs() {
        return this.banTsMs;
    }

    /**
     * set banTsMs && set marked
     *
     * @param banTsMs new value
     * @return current object
     */
    public ChatPlayerProp setBanTsMs(long banTsMs) {
        if (this.banTsMs != banTsMs) {
            this.mark(FIELD_INDEX_BANTSMS);
            this.banTsMs = banTsMs;
        }
        return this;
    }

    /**
     * inner set banTsMs
     *
     * @param banTsMs new value
     */
    private void innerSetBanTsMs(long banTsMs) {
        this.banTsMs = banTsMs;
    }

    /**
     * get notDisturbList
     *
     * @return notDisturbList value
     */
    public Int64ListProp getNotDisturbList() {
        if (this.notDisturbList == null) {
            this.notDisturbList = new Int64ListProp(this, FIELD_INDEX_NOTDISTURBLIST);
        }
        return this.notDisturbList;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addNotDisturbList(Long v) {
        this.getNotDisturbList().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long getNotDisturbListIndex(int index) {
        return this.getNotDisturbList().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Long removeNotDisturbList(Long v) {
        if (this.notDisturbList == null) {
            return null;
        }
        if(this.notDisturbList.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getNotDisturbListSize() {
        if (this.notDisturbList == null) {
            return 0;
        }
        return this.notDisturbList.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isNotDisturbListEmpty() {
        if (this.notDisturbList == null) {
            return true;
        }
        return this.getNotDisturbList().isEmpty();
    }

    /**
     * clear list
     */
    public void clearNotDisturbList() {
        this.getNotDisturbList().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long removeNotDisturbListIndex(int index) {
        return this.getNotDisturbList().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Long setNotDisturbListIndex(int index, Long v) {
        return this.getNotDisturbList().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ChatPlayerPB.Builder getCopyCsBuilder() {
        final ChatPlayerPB.Builder builder = ChatPlayerPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ChatPlayerPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.channelInfo != null) {
            PlayerPB.Int32ChannelInfoMapPB.Builder tmpBuilder = PlayerPB.Int32ChannelInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.channelInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChannelInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChannelInfo();
            }
        }  else if (builder.hasChannelInfo()) {
            // 清理ChannelInfo
            builder.clearChannelInfo();
            fieldCnt++;
        }
        if (this.getBanTsMs() != 0L) {
            builder.setBanTsMs(this.getBanTsMs());
            fieldCnt++;
        }  else if (builder.hasBanTsMs()) {
            // 清理BanTsMs
            builder.clearBanTsMs();
            fieldCnt++;
        }
        if (this.notDisturbList != null) {
            BasicPB.Int64ListPB.Builder tmpBuilder = BasicPB.Int64ListPB.newBuilder();
            final int tmpFieldCnt = this.notDisturbList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setNotDisturbList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearNotDisturbList();
            }
        }  else if (builder.hasNotDisturbList()) {
            // 清理NotDisturbList
            builder.clearNotDisturbList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ChatPlayerPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHANNELINFO) && this.channelInfo != null) {
            final boolean needClear = !builder.hasChannelInfo();
            final int tmpFieldCnt = this.channelInfo.copyChangeToCs(builder.getChannelInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChannelInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_BANTSMS)) {
            builder.setBanTsMs(this.getBanTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NOTDISTURBLIST) && this.notDisturbList != null) {
            final boolean needClear = !builder.hasNotDisturbList();
            final int tmpFieldCnt = this.notDisturbList.copyChangeToCs(builder.getNotDisturbListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearNotDisturbList();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ChatPlayerPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHANNELINFO) && this.channelInfo != null) {
            final boolean needClear = !builder.hasChannelInfo();
            final int tmpFieldCnt = this.channelInfo.copyChangeToAndClearDeleteKeysCs(builder.getChannelInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChannelInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_BANTSMS)) {
            builder.setBanTsMs(this.getBanTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NOTDISTURBLIST) && this.notDisturbList != null) {
            final boolean needClear = !builder.hasNotDisturbList();
            final int tmpFieldCnt = this.notDisturbList.copyChangeToCs(builder.getNotDisturbListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearNotDisturbList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ChatPlayerPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChannelInfo()) {
            this.getChannelInfo().mergeFromCs(proto.getChannelInfo());
        } else {
            if (this.channelInfo != null) {
                this.channelInfo.mergeFromCs(proto.getChannelInfo());
            }
        }
        if (proto.hasBanTsMs()) {
            this.innerSetBanTsMs(proto.getBanTsMs());
        } else {
            this.innerSetBanTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasNotDisturbList()) {
            this.getNotDisturbList().mergeFromCs(proto.getNotDisturbList());
        } else {
            if (this.notDisturbList != null) {
                this.notDisturbList.mergeFromCs(proto.getNotDisturbList());
            }
        }
        this.markAll();
        return ChatPlayerProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ChatPlayerPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChannelInfo()) {
            this.getChannelInfo().mergeChangeFromCs(proto.getChannelInfo());
            fieldCnt++;
        }
        if (proto.hasBanTsMs()) {
            this.setBanTsMs(proto.getBanTsMs());
            fieldCnt++;
        }
        if (proto.hasNotDisturbList()) {
            this.getNotDisturbList().mergeChangeFromCs(proto.getNotDisturbList());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ChatPlayer.Builder getCopyDbBuilder() {
        final ChatPlayer.Builder builder = ChatPlayer.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ChatPlayer.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.channelInfo != null) {
            Player.Int32ChannelInfoMap.Builder tmpBuilder = Player.Int32ChannelInfoMap.newBuilder();
            final int tmpFieldCnt = this.channelInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChannelInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChannelInfo();
            }
        }  else if (builder.hasChannelInfo()) {
            // 清理ChannelInfo
            builder.clearChannelInfo();
            fieldCnt++;
        }
        if (this.getBanTsMs() != 0L) {
            builder.setBanTsMs(this.getBanTsMs());
            fieldCnt++;
        }  else if (builder.hasBanTsMs()) {
            // 清理BanTsMs
            builder.clearBanTsMs();
            fieldCnt++;
        }
        if (this.notDisturbList != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.notDisturbList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setNotDisturbList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearNotDisturbList();
            }
        }  else if (builder.hasNotDisturbList()) {
            // 清理NotDisturbList
            builder.clearNotDisturbList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ChatPlayer.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHANNELINFO) && this.channelInfo != null) {
            final boolean needClear = !builder.hasChannelInfo();
            final int tmpFieldCnt = this.channelInfo.copyChangeToDb(builder.getChannelInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChannelInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_BANTSMS)) {
            builder.setBanTsMs(this.getBanTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NOTDISTURBLIST) && this.notDisturbList != null) {
            final boolean needClear = !builder.hasNotDisturbList();
            final int tmpFieldCnt = this.notDisturbList.copyChangeToDb(builder.getNotDisturbListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearNotDisturbList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ChatPlayer proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChannelInfo()) {
            this.getChannelInfo().mergeFromDb(proto.getChannelInfo());
        } else {
            if (this.channelInfo != null) {
                this.channelInfo.mergeFromDb(proto.getChannelInfo());
            }
        }
        if (proto.hasBanTsMs()) {
            this.innerSetBanTsMs(proto.getBanTsMs());
        } else {
            this.innerSetBanTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasNotDisturbList()) {
            this.getNotDisturbList().mergeFromDb(proto.getNotDisturbList());
        } else {
            if (this.notDisturbList != null) {
                this.notDisturbList.mergeFromDb(proto.getNotDisturbList());
            }
        }
        this.markAll();
        return ChatPlayerProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ChatPlayer proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChannelInfo()) {
            this.getChannelInfo().mergeChangeFromDb(proto.getChannelInfo());
            fieldCnt++;
        }
        if (proto.hasBanTsMs()) {
            this.setBanTsMs(proto.getBanTsMs());
            fieldCnt++;
        }
        if (proto.hasNotDisturbList()) {
            this.getNotDisturbList().mergeChangeFromDb(proto.getNotDisturbList());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ChatPlayer.Builder getCopySsBuilder() {
        final ChatPlayer.Builder builder = ChatPlayer.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ChatPlayer.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.channelInfo != null) {
            Player.Int32ChannelInfoMap.Builder tmpBuilder = Player.Int32ChannelInfoMap.newBuilder();
            final int tmpFieldCnt = this.channelInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChannelInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChannelInfo();
            }
        }  else if (builder.hasChannelInfo()) {
            // 清理ChannelInfo
            builder.clearChannelInfo();
            fieldCnt++;
        }
        if (this.getBanTsMs() != 0L) {
            builder.setBanTsMs(this.getBanTsMs());
            fieldCnt++;
        }  else if (builder.hasBanTsMs()) {
            // 清理BanTsMs
            builder.clearBanTsMs();
            fieldCnt++;
        }
        if (this.notDisturbList != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.notDisturbList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setNotDisturbList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearNotDisturbList();
            }
        }  else if (builder.hasNotDisturbList()) {
            // 清理NotDisturbList
            builder.clearNotDisturbList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ChatPlayer.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHANNELINFO) && this.channelInfo != null) {
            final boolean needClear = !builder.hasChannelInfo();
            final int tmpFieldCnt = this.channelInfo.copyChangeToSs(builder.getChannelInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChannelInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_BANTSMS)) {
            builder.setBanTsMs(this.getBanTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NOTDISTURBLIST) && this.notDisturbList != null) {
            final boolean needClear = !builder.hasNotDisturbList();
            final int tmpFieldCnt = this.notDisturbList.copyChangeToSs(builder.getNotDisturbListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearNotDisturbList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ChatPlayer proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChannelInfo()) {
            this.getChannelInfo().mergeFromSs(proto.getChannelInfo());
        } else {
            if (this.channelInfo != null) {
                this.channelInfo.mergeFromSs(proto.getChannelInfo());
            }
        }
        if (proto.hasBanTsMs()) {
            this.innerSetBanTsMs(proto.getBanTsMs());
        } else {
            this.innerSetBanTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasNotDisturbList()) {
            this.getNotDisturbList().mergeFromSs(proto.getNotDisturbList());
        } else {
            if (this.notDisturbList != null) {
                this.notDisturbList.mergeFromSs(proto.getNotDisturbList());
            }
        }
        this.markAll();
        return ChatPlayerProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ChatPlayer proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChannelInfo()) {
            this.getChannelInfo().mergeChangeFromSs(proto.getChannelInfo());
            fieldCnt++;
        }
        if (proto.hasBanTsMs()) {
            this.setBanTsMs(proto.getBanTsMs());
            fieldCnt++;
        }
        if (proto.hasNotDisturbList()) {
            this.getNotDisturbList().mergeChangeFromSs(proto.getNotDisturbList());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ChatPlayer.Builder builder = ChatPlayer.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CHANNELINFO) && this.channelInfo != null) {
            this.channelInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_NOTDISTURBLIST) && this.notDisturbList != null) {
            this.notDisturbList.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.channelInfo != null) {
            this.channelInfo.markAll();
        }
        if (this.notDisturbList != null) {
            this.notDisturbList.markAll();
        }
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("ChatPlayerProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("ChatPlayerProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ChatPlayerProp)) {
            return false;
        }
        final ChatPlayerProp otherNode = (ChatPlayerProp) node;
        if (!this.getChannelInfo().compareDataTo(otherNode.getChannelInfo())) {
            return false;
        }
        if (this.banTsMs != otherNode.banTsMs) {
            return false;
        }
        if (!this.getNotDisturbList().compareDataTo(otherNode.getNotDisturbList())) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    public static ChatPlayerProp of(ChatPlayer fullAttrDb, ChatPlayer changeAttrDb) {
        ChatPlayerProp prop = new ChatPlayerProp();
        prop.mergeFromDb(fullAttrDb);
        prop.mergeChangeFromDb(changeAttrDb);
        prop.unMarkAll();
        return prop;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
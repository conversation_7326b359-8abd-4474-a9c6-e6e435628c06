package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Player.ScenePlayerArmyTarget;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.ScenePlayerArmyTargetPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ScenePlayerArmyTargetProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TARGETTYPE = 0;
    public static final int FIELD_INDEX_TEMPLATEID = 1;
    public static final int FIELD_INDEX_NAME = 2;
    public static final int FIELD_INDEX_CLANSIMPLENAME = 3;
    public static final int FIELD_INDEX_POINT = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private ArmyTargetType targetType = ArmyTargetType.forNumber(0);
    private int templateId = Constant.DEFAULT_INT_VALUE;
    private String name = Constant.DEFAULT_STR_VALUE;
    private String clanSimpleName = Constant.DEFAULT_STR_VALUE;
    private PointProp point = null;

    public ScenePlayerArmyTargetProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ScenePlayerArmyTargetProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get targetType
     *
     * @return targetType value
     */
    public ArmyTargetType getTargetType() {
        return this.targetType;
    }

    /**
     * set targetType && set marked
     *
     * @param targetType new value
     * @return current object
     */
    public ScenePlayerArmyTargetProp setTargetType(ArmyTargetType targetType) {
        if (targetType == null) {
            throw new NullPointerException();
        }
        if (this.targetType != targetType) {
            this.mark(FIELD_INDEX_TARGETTYPE);
            this.targetType = targetType;
        }
        return this;
    }

    /**
     * inner set targetType
     *
     * @param targetType new value
     */
    private void innerSetTargetType(ArmyTargetType targetType) {
        this.targetType = targetType;
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public ScenePlayerArmyTargetProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }

    /**
     * get name
     *
     * @return name value
     */
    public String getName() {
        return this.name;
    }

    /**
     * set name && set marked
     *
     * @param name new value
     * @return current object
     */
    public ScenePlayerArmyTargetProp setName(String name) {
        if (name == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.name, name)) {
            this.mark(FIELD_INDEX_NAME);
            this.name = name;
        }
        return this;
    }

    /**
     * inner set name
     *
     * @param name new value
     */
    private void innerSetName(String name) {
        this.name = name;
    }

    /**
     * get clanSimpleName
     *
     * @return clanSimpleName value
     */
    public String getClanSimpleName() {
        return this.clanSimpleName;
    }

    /**
     * set clanSimpleName && set marked
     *
     * @param clanSimpleName new value
     * @return current object
     */
    public ScenePlayerArmyTargetProp setClanSimpleName(String clanSimpleName) {
        if (clanSimpleName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanSimpleName, clanSimpleName)) {
            this.mark(FIELD_INDEX_CLANSIMPLENAME);
            this.clanSimpleName = clanSimpleName;
        }
        return this;
    }

    /**
     * inner set clanSimpleName
     *
     * @param clanSimpleName new value
     */
    private void innerSetClanSimpleName(String clanSimpleName) {
        this.clanSimpleName = clanSimpleName;
    }

    /**
     * get point
     *
     * @return point value
     */
    public PointProp getPoint() {
        if (this.point == null) {
            this.point = new PointProp(this, FIELD_INDEX_POINT);
        }
        return this.point;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerArmyTargetPB.Builder getCopyCsBuilder() {
        final ScenePlayerArmyTargetPB.Builder builder = ScenePlayerArmyTargetPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ScenePlayerArmyTargetPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTargetType() != ArmyTargetType.forNumber(0)) {
            builder.setTargetType(this.getTargetType());
            fieldCnt++;
        }  else if (builder.hasTargetType()) {
            // 清理TargetType
            builder.clearTargetType();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        if (!this.getClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasClanSimpleName()) {
            // 清理ClanSimpleName
            builder.clearClanSimpleName();
            fieldCnt++;
        }
        if (this.point != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.point.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ScenePlayerArmyTargetPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TARGETTYPE)) {
            builder.setTargetType(this.getTargetType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ScenePlayerArmyTargetPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TARGETTYPE)) {
            builder.setTargetType(this.getTargetType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToAndClearDeleteKeysCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ScenePlayerArmyTargetPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTargetType()) {
            this.innerSetTargetType(proto.getTargetType());
        } else {
            this.innerSetTargetType(ArmyTargetType.forNumber(0));
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanSimpleName()) {
            this.innerSetClanSimpleName(proto.getClanSimpleName());
        } else {
            this.innerSetClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromCs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromCs(proto.getPoint());
            }
        }
        this.markAll();
        return ScenePlayerArmyTargetProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ScenePlayerArmyTargetPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTargetType()) {
            this.setTargetType(proto.getTargetType());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        if (proto.hasClanSimpleName()) {
            this.setClanSimpleName(proto.getClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromCs(proto.getPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerArmyTarget.Builder getCopyDbBuilder() {
        final ScenePlayerArmyTarget.Builder builder = ScenePlayerArmyTarget.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ScenePlayerArmyTarget.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTargetType() != ArmyTargetType.forNumber(0)) {
            builder.setTargetType(this.getTargetType());
            fieldCnt++;
        }  else if (builder.hasTargetType()) {
            // 清理TargetType
            builder.clearTargetType();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        if (!this.getClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasClanSimpleName()) {
            // 清理ClanSimpleName
            builder.clearClanSimpleName();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ScenePlayerArmyTarget.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TARGETTYPE)) {
            builder.setTargetType(this.getTargetType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToDb(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ScenePlayerArmyTarget proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTargetType()) {
            this.innerSetTargetType(proto.getTargetType());
        } else {
            this.innerSetTargetType(ArmyTargetType.forNumber(0));
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanSimpleName()) {
            this.innerSetClanSimpleName(proto.getClanSimpleName());
        } else {
            this.innerSetClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromDb(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromDb(proto.getPoint());
            }
        }
        this.markAll();
        return ScenePlayerArmyTargetProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ScenePlayerArmyTarget proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTargetType()) {
            this.setTargetType(proto.getTargetType());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        if (proto.hasClanSimpleName()) {
            this.setClanSimpleName(proto.getClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromDb(proto.getPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerArmyTarget.Builder getCopySsBuilder() {
        final ScenePlayerArmyTarget.Builder builder = ScenePlayerArmyTarget.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ScenePlayerArmyTarget.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTargetType() != ArmyTargetType.forNumber(0)) {
            builder.setTargetType(this.getTargetType());
            fieldCnt++;
        }  else if (builder.hasTargetType()) {
            // 清理TargetType
            builder.clearTargetType();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        if (!this.getClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasClanSimpleName()) {
            // 清理ClanSimpleName
            builder.clearClanSimpleName();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ScenePlayerArmyTarget.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TARGETTYPE)) {
            builder.setTargetType(this.getTargetType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToSs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ScenePlayerArmyTarget proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTargetType()) {
            this.innerSetTargetType(proto.getTargetType());
        } else {
            this.innerSetTargetType(ArmyTargetType.forNumber(0));
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanSimpleName()) {
            this.innerSetClanSimpleName(proto.getClanSimpleName());
        } else {
            this.innerSetClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromSs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromSs(proto.getPoint());
            }
        }
        this.markAll();
        return ScenePlayerArmyTargetProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ScenePlayerArmyTarget proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTargetType()) {
            this.setTargetType(proto.getTargetType());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        if (proto.hasClanSimpleName()) {
            this.setClanSimpleName(proto.getClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromSs(proto.getPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ScenePlayerArmyTarget.Builder builder = ScenePlayerArmyTarget.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            this.point.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.point != null) {
            this.point.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ScenePlayerArmyTargetProp)) {
            return false;
        }
        final ScenePlayerArmyTargetProp otherNode = (ScenePlayerArmyTargetProp) node;
        if (this.targetType != otherNode.targetType) {
            return false;
        }
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.name, otherNode.name)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanSimpleName, otherNode.clanSimpleName)) {
            return false;
        }
        if (!this.getPoint().compareDataTo(otherNode.getPoint())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
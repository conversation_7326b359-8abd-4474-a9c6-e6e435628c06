package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.PickUpInfo;
import com.yorha.proto.StructPB.PickUpInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class PickUpInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_DROPENTITYID = 0;
    public static final int FIELD_INDEX_ENDTIME = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private long dropEntityId = Constant.DEFAULT_LONG_VALUE;
    private long endTime = Constant.DEFAULT_LONG_VALUE;

    public PickUpInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PickUpInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get dropEntityId
     *
     * @return dropEntityId value
     */
    public long getDropEntityId() {
        return this.dropEntityId;
    }

    /**
     * set dropEntityId && set marked
     *
     * @param dropEntityId new value
     * @return current object
     */
    public PickUpInfoProp setDropEntityId(long dropEntityId) {
        if (this.dropEntityId != dropEntityId) {
            this.mark(FIELD_INDEX_DROPENTITYID);
            this.dropEntityId = dropEntityId;
        }
        return this;
    }

    /**
     * inner set dropEntityId
     *
     * @param dropEntityId new value
     */
    private void innerSetDropEntityId(long dropEntityId) {
        this.dropEntityId = dropEntityId;
    }

    /**
     * get endTime
     *
     * @return endTime value
     */
    public long getEndTime() {
        return this.endTime;
    }

    /**
     * set endTime && set marked
     *
     * @param endTime new value
     * @return current object
     */
    public PickUpInfoProp setEndTime(long endTime) {
        if (this.endTime != endTime) {
            this.mark(FIELD_INDEX_ENDTIME);
            this.endTime = endTime;
        }
        return this;
    }

    /**
     * inner set endTime
     *
     * @param endTime new value
     */
    private void innerSetEndTime(long endTime) {
        this.endTime = endTime;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PickUpInfoPB.Builder getCopyCsBuilder() {
        final PickUpInfoPB.Builder builder = PickUpInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PickUpInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDropEntityId() != 0L) {
            builder.setDropEntityId(this.getDropEntityId());
            fieldCnt++;
        }  else if (builder.hasDropEntityId()) {
            // 清理DropEntityId
            builder.clearDropEntityId();
            fieldCnt++;
        }
        if (this.getEndTime() != 0L) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }  else if (builder.hasEndTime()) {
            // 清理EndTime
            builder.clearEndTime();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PickUpInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DROPENTITYID)) {
            builder.setDropEntityId(this.getDropEntityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTIME)) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PickUpInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DROPENTITYID)) {
            builder.setDropEntityId(this.getDropEntityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTIME)) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PickUpInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDropEntityId()) {
            this.innerSetDropEntityId(proto.getDropEntityId());
        } else {
            this.innerSetDropEntityId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTime()) {
            this.innerSetEndTime(proto.getEndTime());
        } else {
            this.innerSetEndTime(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PickUpInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PickUpInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDropEntityId()) {
            this.setDropEntityId(proto.getDropEntityId());
            fieldCnt++;
        }
        if (proto.hasEndTime()) {
            this.setEndTime(proto.getEndTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PickUpInfo.Builder getCopyDbBuilder() {
        final PickUpInfo.Builder builder = PickUpInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PickUpInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDropEntityId() != 0L) {
            builder.setDropEntityId(this.getDropEntityId());
            fieldCnt++;
        }  else if (builder.hasDropEntityId()) {
            // 清理DropEntityId
            builder.clearDropEntityId();
            fieldCnt++;
        }
        if (this.getEndTime() != 0L) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }  else if (builder.hasEndTime()) {
            // 清理EndTime
            builder.clearEndTime();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PickUpInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DROPENTITYID)) {
            builder.setDropEntityId(this.getDropEntityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTIME)) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PickUpInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDropEntityId()) {
            this.innerSetDropEntityId(proto.getDropEntityId());
        } else {
            this.innerSetDropEntityId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTime()) {
            this.innerSetEndTime(proto.getEndTime());
        } else {
            this.innerSetEndTime(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PickUpInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PickUpInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDropEntityId()) {
            this.setDropEntityId(proto.getDropEntityId());
            fieldCnt++;
        }
        if (proto.hasEndTime()) {
            this.setEndTime(proto.getEndTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PickUpInfo.Builder getCopySsBuilder() {
        final PickUpInfo.Builder builder = PickUpInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PickUpInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDropEntityId() != 0L) {
            builder.setDropEntityId(this.getDropEntityId());
            fieldCnt++;
        }  else if (builder.hasDropEntityId()) {
            // 清理DropEntityId
            builder.clearDropEntityId();
            fieldCnt++;
        }
        if (this.getEndTime() != 0L) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }  else if (builder.hasEndTime()) {
            // 清理EndTime
            builder.clearEndTime();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PickUpInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DROPENTITYID)) {
            builder.setDropEntityId(this.getDropEntityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTIME)) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PickUpInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDropEntityId()) {
            this.innerSetDropEntityId(proto.getDropEntityId());
        } else {
            this.innerSetDropEntityId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTime()) {
            this.innerSetEndTime(proto.getEndTime());
        } else {
            this.innerSetEndTime(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PickUpInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PickUpInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDropEntityId()) {
            this.setDropEntityId(proto.getDropEntityId());
            fieldCnt++;
        }
        if (proto.hasEndTime()) {
            this.setEndTime(proto.getEndTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PickUpInfo.Builder builder = PickUpInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PickUpInfoProp)) {
            return false;
        }
        final PickUpInfoProp otherNode = (PickUpInfoProp) node;
        if (this.dropEntityId != otherNode.dropEntityId) {
            return false;
        }
        if (this.endTime != otherNode.endTime) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
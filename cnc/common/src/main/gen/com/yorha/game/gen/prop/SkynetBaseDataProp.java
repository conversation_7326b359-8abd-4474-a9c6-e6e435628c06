package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructSkynet.SkynetBaseData;
import com.yorha.proto.StructSkynetPB.SkynetBaseDataPB;


/**
 * <AUTHOR> auto gen
 */
public class SkynetBaseDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_SKYNETEXP = 0;
    public static final int FIELD_INDEX_CURLEVEL = 1;
    public static final int FIELD_INDEX_TOTALINFOPOINT = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int skynetExp = Constant.DEFAULT_INT_VALUE;
    private int curLevel = Constant.DEFAULT_INT_VALUE;
    private int totalInfoPoint = Constant.DEFAULT_INT_VALUE;

    public SkynetBaseDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SkynetBaseDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get skynetExp
     *
     * @return skynetExp value
     */
    public int getSkynetExp() {
        return this.skynetExp;
    }

    /**
     * set skynetExp && set marked
     *
     * @param skynetExp new value
     * @return current object
     */
    public SkynetBaseDataProp setSkynetExp(int skynetExp) {
        if (this.skynetExp != skynetExp) {
            this.mark(FIELD_INDEX_SKYNETEXP);
            this.skynetExp = skynetExp;
        }
        return this;
    }

    /**
     * inner set skynetExp
     *
     * @param skynetExp new value
     */
    private void innerSetSkynetExp(int skynetExp) {
        this.skynetExp = skynetExp;
    }

    /**
     * get curLevel
     *
     * @return curLevel value
     */
    public int getCurLevel() {
        return this.curLevel;
    }

    /**
     * set curLevel && set marked
     *
     * @param curLevel new value
     * @return current object
     */
    public SkynetBaseDataProp setCurLevel(int curLevel) {
        if (this.curLevel != curLevel) {
            this.mark(FIELD_INDEX_CURLEVEL);
            this.curLevel = curLevel;
        }
        return this;
    }

    /**
     * inner set curLevel
     *
     * @param curLevel new value
     */
    private void innerSetCurLevel(int curLevel) {
        this.curLevel = curLevel;
    }

    /**
     * get totalInfoPoint
     *
     * @return totalInfoPoint value
     */
    public int getTotalInfoPoint() {
        return this.totalInfoPoint;
    }

    /**
     * set totalInfoPoint && set marked
     *
     * @param totalInfoPoint new value
     * @return current object
     */
    public SkynetBaseDataProp setTotalInfoPoint(int totalInfoPoint) {
        if (this.totalInfoPoint != totalInfoPoint) {
            this.mark(FIELD_INDEX_TOTALINFOPOINT);
            this.totalInfoPoint = totalInfoPoint;
        }
        return this;
    }

    /**
     * inner set totalInfoPoint
     *
     * @param totalInfoPoint new value
     */
    private void innerSetTotalInfoPoint(int totalInfoPoint) {
        this.totalInfoPoint = totalInfoPoint;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SkynetBaseDataPB.Builder getCopyCsBuilder() {
        final SkynetBaseDataPB.Builder builder = SkynetBaseDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SkynetBaseDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSkynetExp() != 0) {
            builder.setSkynetExp(this.getSkynetExp());
            fieldCnt++;
        }  else if (builder.hasSkynetExp()) {
            // 清理SkynetExp
            builder.clearSkynetExp();
            fieldCnt++;
        }
        if (this.getCurLevel() != 0) {
            builder.setCurLevel(this.getCurLevel());
            fieldCnt++;
        }  else if (builder.hasCurLevel()) {
            // 清理CurLevel
            builder.clearCurLevel();
            fieldCnt++;
        }
        if (this.getTotalInfoPoint() != 0) {
            builder.setTotalInfoPoint(this.getTotalInfoPoint());
            fieldCnt++;
        }  else if (builder.hasTotalInfoPoint()) {
            // 清理TotalInfoPoint
            builder.clearTotalInfoPoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SkynetBaseDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKYNETEXP)) {
            builder.setSkynetExp(this.getSkynetExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURLEVEL)) {
            builder.setCurLevel(this.getCurLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALINFOPOINT)) {
            builder.setTotalInfoPoint(this.getTotalInfoPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SkynetBaseDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKYNETEXP)) {
            builder.setSkynetExp(this.getSkynetExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURLEVEL)) {
            builder.setCurLevel(this.getCurLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALINFOPOINT)) {
            builder.setTotalInfoPoint(this.getTotalInfoPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SkynetBaseDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkynetExp()) {
            this.innerSetSkynetExp(proto.getSkynetExp());
        } else {
            this.innerSetSkynetExp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurLevel()) {
            this.innerSetCurLevel(proto.getCurLevel());
        } else {
            this.innerSetCurLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTotalInfoPoint()) {
            this.innerSetTotalInfoPoint(proto.getTotalInfoPoint());
        } else {
            this.innerSetTotalInfoPoint(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SkynetBaseDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SkynetBaseDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkynetExp()) {
            this.setSkynetExp(proto.getSkynetExp());
            fieldCnt++;
        }
        if (proto.hasCurLevel()) {
            this.setCurLevel(proto.getCurLevel());
            fieldCnt++;
        }
        if (proto.hasTotalInfoPoint()) {
            this.setTotalInfoPoint(proto.getTotalInfoPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SkynetBaseData.Builder getCopyDbBuilder() {
        final SkynetBaseData.Builder builder = SkynetBaseData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SkynetBaseData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSkynetExp() != 0) {
            builder.setSkynetExp(this.getSkynetExp());
            fieldCnt++;
        }  else if (builder.hasSkynetExp()) {
            // 清理SkynetExp
            builder.clearSkynetExp();
            fieldCnt++;
        }
        if (this.getCurLevel() != 0) {
            builder.setCurLevel(this.getCurLevel());
            fieldCnt++;
        }  else if (builder.hasCurLevel()) {
            // 清理CurLevel
            builder.clearCurLevel();
            fieldCnt++;
        }
        if (this.getTotalInfoPoint() != 0) {
            builder.setTotalInfoPoint(this.getTotalInfoPoint());
            fieldCnt++;
        }  else if (builder.hasTotalInfoPoint()) {
            // 清理TotalInfoPoint
            builder.clearTotalInfoPoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SkynetBaseData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKYNETEXP)) {
            builder.setSkynetExp(this.getSkynetExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURLEVEL)) {
            builder.setCurLevel(this.getCurLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALINFOPOINT)) {
            builder.setTotalInfoPoint(this.getTotalInfoPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SkynetBaseData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkynetExp()) {
            this.innerSetSkynetExp(proto.getSkynetExp());
        } else {
            this.innerSetSkynetExp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurLevel()) {
            this.innerSetCurLevel(proto.getCurLevel());
        } else {
            this.innerSetCurLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTotalInfoPoint()) {
            this.innerSetTotalInfoPoint(proto.getTotalInfoPoint());
        } else {
            this.innerSetTotalInfoPoint(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SkynetBaseDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SkynetBaseData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkynetExp()) {
            this.setSkynetExp(proto.getSkynetExp());
            fieldCnt++;
        }
        if (proto.hasCurLevel()) {
            this.setCurLevel(proto.getCurLevel());
            fieldCnt++;
        }
        if (proto.hasTotalInfoPoint()) {
            this.setTotalInfoPoint(proto.getTotalInfoPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SkynetBaseData.Builder getCopySsBuilder() {
        final SkynetBaseData.Builder builder = SkynetBaseData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SkynetBaseData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSkynetExp() != 0) {
            builder.setSkynetExp(this.getSkynetExp());
            fieldCnt++;
        }  else if (builder.hasSkynetExp()) {
            // 清理SkynetExp
            builder.clearSkynetExp();
            fieldCnt++;
        }
        if (this.getCurLevel() != 0) {
            builder.setCurLevel(this.getCurLevel());
            fieldCnt++;
        }  else if (builder.hasCurLevel()) {
            // 清理CurLevel
            builder.clearCurLevel();
            fieldCnt++;
        }
        if (this.getTotalInfoPoint() != 0) {
            builder.setTotalInfoPoint(this.getTotalInfoPoint());
            fieldCnt++;
        }  else if (builder.hasTotalInfoPoint()) {
            // 清理TotalInfoPoint
            builder.clearTotalInfoPoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SkynetBaseData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKYNETEXP)) {
            builder.setSkynetExp(this.getSkynetExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURLEVEL)) {
            builder.setCurLevel(this.getCurLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALINFOPOINT)) {
            builder.setTotalInfoPoint(this.getTotalInfoPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SkynetBaseData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkynetExp()) {
            this.innerSetSkynetExp(proto.getSkynetExp());
        } else {
            this.innerSetSkynetExp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurLevel()) {
            this.innerSetCurLevel(proto.getCurLevel());
        } else {
            this.innerSetCurLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTotalInfoPoint()) {
            this.innerSetTotalInfoPoint(proto.getTotalInfoPoint());
        } else {
            this.innerSetTotalInfoPoint(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SkynetBaseDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SkynetBaseData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkynetExp()) {
            this.setSkynetExp(proto.getSkynetExp());
            fieldCnt++;
        }
        if (proto.hasCurLevel()) {
            this.setCurLevel(proto.getCurLevel());
            fieldCnt++;
        }
        if (proto.hasTotalInfoPoint()) {
            this.setTotalInfoPoint(proto.getTotalInfoPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SkynetBaseData.Builder builder = SkynetBaseData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SkynetBaseDataProp)) {
            return false;
        }
        final SkynetBaseDataProp otherNode = (SkynetBaseDataProp) node;
        if (this.skynetExp != otherNode.skynetExp) {
            return false;
        }
        if (this.curLevel != otherNode.curLevel) {
            return false;
        }
        if (this.totalInfoPoint != otherNode.totalInfoPoint) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
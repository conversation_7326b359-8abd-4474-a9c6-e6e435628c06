package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ClanTechInfo;
import com.yorha.proto.StructPB.ClanTechInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanTechInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_TECHID = 0;
    public static final int FIELD_INDEX_TECHSUBID = 1;
    public static final int FIELD_INDEX_POINT = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int techId = Constant.DEFAULT_INT_VALUE;
    private int techSubId = Constant.DEFAULT_INT_VALUE;
    private int point = Constant.DEFAULT_INT_VALUE;

    public ClanTechInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanTechInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get techId
     *
     * @return techId value
     */
    public int getTechId() {
        return this.techId;
    }

    /**
     * set techId && set marked
     *
     * @param techId new value
     * @return current object
     */
    public ClanTechInfoProp setTechId(int techId) {
        if (this.techId != techId) {
            this.mark(FIELD_INDEX_TECHID);
            this.techId = techId;
        }
        return this;
    }

    /**
     * inner set techId
     *
     * @param techId new value
     */
    private void innerSetTechId(int techId) {
        this.techId = techId;
    }

    /**
     * get techSubId
     *
     * @return techSubId value
     */
    public int getTechSubId() {
        return this.techSubId;
    }

    /**
     * set techSubId && set marked
     *
     * @param techSubId new value
     * @return current object
     */
    public ClanTechInfoProp setTechSubId(int techSubId) {
        if (this.techSubId != techSubId) {
            this.mark(FIELD_INDEX_TECHSUBID);
            this.techSubId = techSubId;
        }
        return this;
    }

    /**
     * inner set techSubId
     *
     * @param techSubId new value
     */
    private void innerSetTechSubId(int techSubId) {
        this.techSubId = techSubId;
    }

    /**
     * get point
     *
     * @return point value
     */
    public int getPoint() {
        return this.point;
    }

    /**
     * set point && set marked
     *
     * @param point new value
     * @return current object
     */
    public ClanTechInfoProp setPoint(int point) {
        if (this.point != point) {
            this.mark(FIELD_INDEX_POINT);
            this.point = point;
        }
        return this;
    }

    /**
     * inner set point
     *
     * @param point new value
     */
    private void innerSetPoint(int point) {
        this.point = point;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanTechInfoPB.Builder getCopyCsBuilder() {
        final ClanTechInfoPB.Builder builder = ClanTechInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanTechInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTechId() != 0) {
            builder.setTechId(this.getTechId());
            fieldCnt++;
        }  else if (builder.hasTechId()) {
            // 清理TechId
            builder.clearTechId();
            fieldCnt++;
        }
        if (this.getTechSubId() != 0) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }  else if (builder.hasTechSubId()) {
            // 清理TechSubId
            builder.clearTechSubId();
            fieldCnt++;
        }
        if (this.getPoint() != 0) {
            builder.setPoint(this.getPoint());
            fieldCnt++;
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanTechInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TECHID)) {
            builder.setTechId(this.getTechId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TECHSUBID)) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT)) {
            builder.setPoint(this.getPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanTechInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TECHID)) {
            builder.setTechId(this.getTechId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TECHSUBID)) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT)) {
            builder.setPoint(this.getPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanTechInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTechId()) {
            this.innerSetTechId(proto.getTechId());
        } else {
            this.innerSetTechId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTechSubId()) {
            this.innerSetTechSubId(proto.getTechSubId());
        } else {
            this.innerSetTechSubId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPoint()) {
            this.innerSetPoint(proto.getPoint());
        } else {
            this.innerSetPoint(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanTechInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanTechInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTechId()) {
            this.setTechId(proto.getTechId());
            fieldCnt++;
        }
        if (proto.hasTechSubId()) {
            this.setTechSubId(proto.getTechSubId());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.setPoint(proto.getPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanTechInfo.Builder getCopyDbBuilder() {
        final ClanTechInfo.Builder builder = ClanTechInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanTechInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTechId() != 0) {
            builder.setTechId(this.getTechId());
            fieldCnt++;
        }  else if (builder.hasTechId()) {
            // 清理TechId
            builder.clearTechId();
            fieldCnt++;
        }
        if (this.getTechSubId() != 0) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }  else if (builder.hasTechSubId()) {
            // 清理TechSubId
            builder.clearTechSubId();
            fieldCnt++;
        }
        if (this.getPoint() != 0) {
            builder.setPoint(this.getPoint());
            fieldCnt++;
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanTechInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TECHID)) {
            builder.setTechId(this.getTechId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TECHSUBID)) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT)) {
            builder.setPoint(this.getPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanTechInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTechId()) {
            this.innerSetTechId(proto.getTechId());
        } else {
            this.innerSetTechId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTechSubId()) {
            this.innerSetTechSubId(proto.getTechSubId());
        } else {
            this.innerSetTechSubId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPoint()) {
            this.innerSetPoint(proto.getPoint());
        } else {
            this.innerSetPoint(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanTechInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanTechInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTechId()) {
            this.setTechId(proto.getTechId());
            fieldCnt++;
        }
        if (proto.hasTechSubId()) {
            this.setTechSubId(proto.getTechSubId());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.setPoint(proto.getPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanTechInfo.Builder getCopySsBuilder() {
        final ClanTechInfo.Builder builder = ClanTechInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanTechInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTechId() != 0) {
            builder.setTechId(this.getTechId());
            fieldCnt++;
        }  else if (builder.hasTechId()) {
            // 清理TechId
            builder.clearTechId();
            fieldCnt++;
        }
        if (this.getTechSubId() != 0) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }  else if (builder.hasTechSubId()) {
            // 清理TechSubId
            builder.clearTechSubId();
            fieldCnt++;
        }
        if (this.getPoint() != 0) {
            builder.setPoint(this.getPoint());
            fieldCnt++;
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanTechInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TECHID)) {
            builder.setTechId(this.getTechId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TECHSUBID)) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT)) {
            builder.setPoint(this.getPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanTechInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTechId()) {
            this.innerSetTechId(proto.getTechId());
        } else {
            this.innerSetTechId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTechSubId()) {
            this.innerSetTechSubId(proto.getTechSubId());
        } else {
            this.innerSetTechSubId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPoint()) {
            this.innerSetPoint(proto.getPoint());
        } else {
            this.innerSetPoint(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanTechInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanTechInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTechId()) {
            this.setTechId(proto.getTechId());
            fieldCnt++;
        }
        if (proto.hasTechSubId()) {
            this.setTechSubId(proto.getTechSubId());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.setPoint(proto.getPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanTechInfo.Builder builder = ClanTechInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.techId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanTechInfoProp)) {
            return false;
        }
        final ClanTechInfoProp otherNode = (ClanTechInfoProp) node;
        if (this.techId != otherNode.techId) {
            return false;
        }
        if (this.techSubId != otherNode.techSubId) {
            return false;
        }
        if (this.point != otherNode.point) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
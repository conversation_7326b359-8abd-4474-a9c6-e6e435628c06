package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityLostTriggerBundleUnit;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ActivityLostTriggerBundleUnitPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityLostTriggerBundleUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_RESELLBUNDLES = 0;
    public static final int FIELD_INDEX_LASTINITTSMS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private Int64PlayerResellTriggerBundleMapProp resellBundles = null;
    private long lastInitTsMs = Constant.DEFAULT_LONG_VALUE;

    public ActivityLostTriggerBundleUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityLostTriggerBundleUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get resellBundles
     *
     * @return resellBundles value
     */
    public Int64PlayerResellTriggerBundleMapProp getResellBundles() {
        if (this.resellBundles == null) {
            this.resellBundles = new Int64PlayerResellTriggerBundleMapProp(this, FIELD_INDEX_RESELLBUNDLES);
        }
        return this.resellBundles;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putResellBundlesV(PlayerResellTriggerBundleProp v) {
        this.getResellBundles().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerResellTriggerBundleProp addEmptyResellBundles(Long k) {
        return this.getResellBundles().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getResellBundlesSize() {
        if (this.resellBundles == null) {
            return 0;
        }
        return this.resellBundles.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isResellBundlesEmpty() {
        if (this.resellBundles == null) {
            return true;
        }
        return this.resellBundles.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerResellTriggerBundleProp getResellBundlesV(Long k) {
        if (this.resellBundles == null || !this.resellBundles.containsKey(k)) {
            return null;
        }
        return this.resellBundles.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearResellBundles() {
        if (this.resellBundles != null) {
            this.resellBundles.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeResellBundlesV(Long k) {
        if (this.resellBundles != null) {
            this.resellBundles.remove(k);
        }
    }
    /**
     * get lastInitTsMs
     *
     * @return lastInitTsMs value
     */
    public long getLastInitTsMs() {
        return this.lastInitTsMs;
    }

    /**
     * set lastInitTsMs && set marked
     *
     * @param lastInitTsMs new value
     * @return current object
     */
    public ActivityLostTriggerBundleUnitProp setLastInitTsMs(long lastInitTsMs) {
        if (this.lastInitTsMs != lastInitTsMs) {
            this.mark(FIELD_INDEX_LASTINITTSMS);
            this.lastInitTsMs = lastInitTsMs;
        }
        return this;
    }

    /**
     * inner set lastInitTsMs
     *
     * @param lastInitTsMs new value
     */
    private void innerSetLastInitTsMs(long lastInitTsMs) {
        this.lastInitTsMs = lastInitTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityLostTriggerBundleUnitPB.Builder getCopyCsBuilder() {
        final ActivityLostTriggerBundleUnitPB.Builder builder = ActivityLostTriggerBundleUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityLostTriggerBundleUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.resellBundles != null) {
            StructPB.Int64PlayerResellTriggerBundleMapPB.Builder tmpBuilder = StructPB.Int64PlayerResellTriggerBundleMapPB.newBuilder();
            final int tmpFieldCnt = this.resellBundles.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResellBundles(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResellBundles();
            }
        }  else if (builder.hasResellBundles()) {
            // 清理ResellBundles
            builder.clearResellBundles();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityLostTriggerBundleUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RESELLBUNDLES) && this.resellBundles != null) {
            final boolean needClear = !builder.hasResellBundles();
            final int tmpFieldCnt = this.resellBundles.copyChangeToCs(builder.getResellBundlesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResellBundles();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityLostTriggerBundleUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RESELLBUNDLES) && this.resellBundles != null) {
            final boolean needClear = !builder.hasResellBundles();
            final int tmpFieldCnt = this.resellBundles.copyChangeToAndClearDeleteKeysCs(builder.getResellBundlesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResellBundles();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityLostTriggerBundleUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasResellBundles()) {
            this.getResellBundles().mergeFromCs(proto.getResellBundles());
        } else {
            if (this.resellBundles != null) {
                this.resellBundles.mergeFromCs(proto.getResellBundles());
            }
        }
        this.markAll();
        return ActivityLostTriggerBundleUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityLostTriggerBundleUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasResellBundles()) {
            this.getResellBundles().mergeChangeFromCs(proto.getResellBundles());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityLostTriggerBundleUnit.Builder getCopyDbBuilder() {
        final ActivityLostTriggerBundleUnit.Builder builder = ActivityLostTriggerBundleUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityLostTriggerBundleUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.resellBundles != null) {
            Struct.Int64PlayerResellTriggerBundleMap.Builder tmpBuilder = Struct.Int64PlayerResellTriggerBundleMap.newBuilder();
            final int tmpFieldCnt = this.resellBundles.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResellBundles(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResellBundles();
            }
        }  else if (builder.hasResellBundles()) {
            // 清理ResellBundles
            builder.clearResellBundles();
            fieldCnt++;
        }
        if (this.getLastInitTsMs() != 0L) {
            builder.setLastInitTsMs(this.getLastInitTsMs());
            fieldCnt++;
        }  else if (builder.hasLastInitTsMs()) {
            // 清理LastInitTsMs
            builder.clearLastInitTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityLostTriggerBundleUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RESELLBUNDLES) && this.resellBundles != null) {
            final boolean needClear = !builder.hasResellBundles();
            final int tmpFieldCnt = this.resellBundles.copyChangeToDb(builder.getResellBundlesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResellBundles();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTINITTSMS)) {
            builder.setLastInitTsMs(this.getLastInitTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityLostTriggerBundleUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasResellBundles()) {
            this.getResellBundles().mergeFromDb(proto.getResellBundles());
        } else {
            if (this.resellBundles != null) {
                this.resellBundles.mergeFromDb(proto.getResellBundles());
            }
        }
        if (proto.hasLastInitTsMs()) {
            this.innerSetLastInitTsMs(proto.getLastInitTsMs());
        } else {
            this.innerSetLastInitTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ActivityLostTriggerBundleUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityLostTriggerBundleUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasResellBundles()) {
            this.getResellBundles().mergeChangeFromDb(proto.getResellBundles());
            fieldCnt++;
        }
        if (proto.hasLastInitTsMs()) {
            this.setLastInitTsMs(proto.getLastInitTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityLostTriggerBundleUnit.Builder getCopySsBuilder() {
        final ActivityLostTriggerBundleUnit.Builder builder = ActivityLostTriggerBundleUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityLostTriggerBundleUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.resellBundles != null) {
            Struct.Int64PlayerResellTriggerBundleMap.Builder tmpBuilder = Struct.Int64PlayerResellTriggerBundleMap.newBuilder();
            final int tmpFieldCnt = this.resellBundles.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResellBundles(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResellBundles();
            }
        }  else if (builder.hasResellBundles()) {
            // 清理ResellBundles
            builder.clearResellBundles();
            fieldCnt++;
        }
        if (this.getLastInitTsMs() != 0L) {
            builder.setLastInitTsMs(this.getLastInitTsMs());
            fieldCnt++;
        }  else if (builder.hasLastInitTsMs()) {
            // 清理LastInitTsMs
            builder.clearLastInitTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityLostTriggerBundleUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RESELLBUNDLES) && this.resellBundles != null) {
            final boolean needClear = !builder.hasResellBundles();
            final int tmpFieldCnt = this.resellBundles.copyChangeToSs(builder.getResellBundlesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResellBundles();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTINITTSMS)) {
            builder.setLastInitTsMs(this.getLastInitTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityLostTriggerBundleUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasResellBundles()) {
            this.getResellBundles().mergeFromSs(proto.getResellBundles());
        } else {
            if (this.resellBundles != null) {
                this.resellBundles.mergeFromSs(proto.getResellBundles());
            }
        }
        if (proto.hasLastInitTsMs()) {
            this.innerSetLastInitTsMs(proto.getLastInitTsMs());
        } else {
            this.innerSetLastInitTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ActivityLostTriggerBundleUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityLostTriggerBundleUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasResellBundles()) {
            this.getResellBundles().mergeChangeFromSs(proto.getResellBundles());
            fieldCnt++;
        }
        if (proto.hasLastInitTsMs()) {
            this.setLastInitTsMs(proto.getLastInitTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityLostTriggerBundleUnit.Builder builder = ActivityLostTriggerBundleUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_RESELLBUNDLES) && this.resellBundles != null) {
            this.resellBundles.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.resellBundles != null) {
            this.resellBundles.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityLostTriggerBundleUnitProp)) {
            return false;
        }
        final ActivityLostTriggerBundleUnitProp otherNode = (ActivityLostTriggerBundleUnitProp) node;
        if (!this.getResellBundles().compareDataTo(otherNode.getResellBundles())) {
            return false;
        }
        if (this.lastInitTsMs != otherNode.lastInitTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
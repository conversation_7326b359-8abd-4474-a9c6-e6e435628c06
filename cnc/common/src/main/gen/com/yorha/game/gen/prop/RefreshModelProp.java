package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zone.RefreshModel;
import com.yorha.proto.ZonePB.RefreshModelPB;


/**
 * <AUTHOR> auto gen
 */
public class RefreshModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_REFRESHDAY = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private long refreshDay = Constant.DEFAULT_LONG_VALUE;

    public RefreshModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public RefreshModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get refreshDay
     *
     * @return refreshDay value
     */
    public long getRefreshDay() {
        return this.refreshDay;
    }

    /**
     * set refreshDay && set marked
     *
     * @param refreshDay new value
     * @return current object
     */
    public RefreshModelProp setRefreshDay(long refreshDay) {
        if (this.refreshDay != refreshDay) {
            this.mark(FIELD_INDEX_REFRESHDAY);
            this.refreshDay = refreshDay;
        }
        return this;
    }

    /**
     * inner set refreshDay
     *
     * @param refreshDay new value
     */
    private void innerSetRefreshDay(long refreshDay) {
        this.refreshDay = refreshDay;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RefreshModelPB.Builder getCopyCsBuilder() {
        final RefreshModelPB.Builder builder = RefreshModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(RefreshModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRefreshDay() != 0L) {
            builder.setRefreshDay(this.getRefreshDay());
            fieldCnt++;
        }  else if (builder.hasRefreshDay()) {
            // 清理RefreshDay
            builder.clearRefreshDay();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(RefreshModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REFRESHDAY)) {
            builder.setRefreshDay(this.getRefreshDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(RefreshModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REFRESHDAY)) {
            builder.setRefreshDay(this.getRefreshDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(RefreshModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRefreshDay()) {
            this.innerSetRefreshDay(proto.getRefreshDay());
        } else {
            this.innerSetRefreshDay(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return RefreshModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(RefreshModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRefreshDay()) {
            this.setRefreshDay(proto.getRefreshDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RefreshModel.Builder getCopyDbBuilder() {
        final RefreshModel.Builder builder = RefreshModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(RefreshModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRefreshDay() != 0L) {
            builder.setRefreshDay(this.getRefreshDay());
            fieldCnt++;
        }  else if (builder.hasRefreshDay()) {
            // 清理RefreshDay
            builder.clearRefreshDay();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(RefreshModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REFRESHDAY)) {
            builder.setRefreshDay(this.getRefreshDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(RefreshModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRefreshDay()) {
            this.innerSetRefreshDay(proto.getRefreshDay());
        } else {
            this.innerSetRefreshDay(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return RefreshModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(RefreshModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRefreshDay()) {
            this.setRefreshDay(proto.getRefreshDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RefreshModel.Builder getCopySsBuilder() {
        final RefreshModel.Builder builder = RefreshModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(RefreshModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRefreshDay() != 0L) {
            builder.setRefreshDay(this.getRefreshDay());
            fieldCnt++;
        }  else if (builder.hasRefreshDay()) {
            // 清理RefreshDay
            builder.clearRefreshDay();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(RefreshModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REFRESHDAY)) {
            builder.setRefreshDay(this.getRefreshDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(RefreshModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRefreshDay()) {
            this.innerSetRefreshDay(proto.getRefreshDay());
        } else {
            this.innerSetRefreshDay(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return RefreshModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(RefreshModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRefreshDay()) {
            this.setRefreshDay(proto.getRefreshDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        RefreshModel.Builder builder = RefreshModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof RefreshModelProp)) {
            return false;
        }
        final RefreshModelProp otherNode = (RefreshModelProp) node;
        if (this.refreshDay != otherNode.refreshDay) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerRedDotModel;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.PlayerRedDotModelPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerRedDotModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_REDDOTMAP = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32RedDotDataMapProp redDotMap = null;

    public PlayerRedDotModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerRedDotModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get redDotMap
     *
     * @return redDotMap value
     */
    public Int32RedDotDataMapProp getRedDotMap() {
        if (this.redDotMap == null) {
            this.redDotMap = new Int32RedDotDataMapProp(this, FIELD_INDEX_REDDOTMAP);
        }
        return this.redDotMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putRedDotMapV(RedDotDataProp v) {
        this.getRedDotMap().put(v.getKey(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public RedDotDataProp addEmptyRedDotMap(Integer k) {
        return this.getRedDotMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getRedDotMapSize() {
        if (this.redDotMap == null) {
            return 0;
        }
        return this.redDotMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isRedDotMapEmpty() {
        if (this.redDotMap == null) {
            return true;
        }
        return this.redDotMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public RedDotDataProp getRedDotMapV(Integer k) {
        if (this.redDotMap == null || !this.redDotMap.containsKey(k)) {
            return null;
        }
        return this.redDotMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearRedDotMap() {
        if (this.redDotMap != null) {
            this.redDotMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeRedDotMapV(Integer k) {
        if (this.redDotMap != null) {
            this.redDotMap.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerRedDotModelPB.Builder getCopyCsBuilder() {
        final PlayerRedDotModelPB.Builder builder = PlayerRedDotModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerRedDotModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.redDotMap != null) {
            StructPB.Int32RedDotDataMapPB.Builder tmpBuilder = StructPB.Int32RedDotDataMapPB.newBuilder();
            final int tmpFieldCnt = this.redDotMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRedDotMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRedDotMap();
            }
        }  else if (builder.hasRedDotMap()) {
            // 清理RedDotMap
            builder.clearRedDotMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerRedDotModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REDDOTMAP) && this.redDotMap != null) {
            final boolean needClear = !builder.hasRedDotMap();
            final int tmpFieldCnt = this.redDotMap.copyChangeToCs(builder.getRedDotMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRedDotMap();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerRedDotModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REDDOTMAP) && this.redDotMap != null) {
            final boolean needClear = !builder.hasRedDotMap();
            final int tmpFieldCnt = this.redDotMap.copyChangeToAndClearDeleteKeysCs(builder.getRedDotMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRedDotMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerRedDotModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRedDotMap()) {
            this.getRedDotMap().mergeFromCs(proto.getRedDotMap());
        } else {
            if (this.redDotMap != null) {
                this.redDotMap.mergeFromCs(proto.getRedDotMap());
            }
        }
        this.markAll();
        return PlayerRedDotModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerRedDotModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRedDotMap()) {
            this.getRedDotMap().mergeChangeFromCs(proto.getRedDotMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerRedDotModel.Builder getCopyDbBuilder() {
        final PlayerRedDotModel.Builder builder = PlayerRedDotModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerRedDotModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.redDotMap != null) {
            Struct.Int32RedDotDataMap.Builder tmpBuilder = Struct.Int32RedDotDataMap.newBuilder();
            final int tmpFieldCnt = this.redDotMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRedDotMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRedDotMap();
            }
        }  else if (builder.hasRedDotMap()) {
            // 清理RedDotMap
            builder.clearRedDotMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerRedDotModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REDDOTMAP) && this.redDotMap != null) {
            final boolean needClear = !builder.hasRedDotMap();
            final int tmpFieldCnt = this.redDotMap.copyChangeToDb(builder.getRedDotMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRedDotMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerRedDotModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRedDotMap()) {
            this.getRedDotMap().mergeFromDb(proto.getRedDotMap());
        } else {
            if (this.redDotMap != null) {
                this.redDotMap.mergeFromDb(proto.getRedDotMap());
            }
        }
        this.markAll();
        return PlayerRedDotModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerRedDotModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRedDotMap()) {
            this.getRedDotMap().mergeChangeFromDb(proto.getRedDotMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerRedDotModel.Builder getCopySsBuilder() {
        final PlayerRedDotModel.Builder builder = PlayerRedDotModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerRedDotModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.redDotMap != null) {
            Struct.Int32RedDotDataMap.Builder tmpBuilder = Struct.Int32RedDotDataMap.newBuilder();
            final int tmpFieldCnt = this.redDotMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRedDotMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRedDotMap();
            }
        }  else if (builder.hasRedDotMap()) {
            // 清理RedDotMap
            builder.clearRedDotMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerRedDotModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REDDOTMAP) && this.redDotMap != null) {
            final boolean needClear = !builder.hasRedDotMap();
            final int tmpFieldCnt = this.redDotMap.copyChangeToSs(builder.getRedDotMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRedDotMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerRedDotModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRedDotMap()) {
            this.getRedDotMap().mergeFromSs(proto.getRedDotMap());
        } else {
            if (this.redDotMap != null) {
                this.redDotMap.mergeFromSs(proto.getRedDotMap());
            }
        }
        this.markAll();
        return PlayerRedDotModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerRedDotModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRedDotMap()) {
            this.getRedDotMap().mergeChangeFromSs(proto.getRedDotMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerRedDotModel.Builder builder = PlayerRedDotModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_REDDOTMAP) && this.redDotMap != null) {
            this.redDotMap.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.redDotMap != null) {
            this.redDotMap.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerRedDotModelProp)) {
            return false;
        }
        final PlayerRedDotModelProp otherNode = (PlayerRedDotModelProp) node;
        if (!this.getRedDotMap().compareDataTo(otherNode.getRedDotMap())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
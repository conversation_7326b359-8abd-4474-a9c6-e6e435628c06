package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.MileStoneClanInfo;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.MileStoneClanInfoPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class MileStoneClanInfoProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_CLANID = 0;
    public static final int FIELD_INDEX_SCORE = 1;
    public static final int FIELD_INDEX_SIMPLECLAN = 2;
    public static final int FIELD_INDEX_LASTUPDATETSMS = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private long clanId = Constant.DEFAULT_LONG_VALUE;
    private long score = Constant.DEFAULT_LONG_VALUE;
    private MileStoneSimpleClanProp simpleClan = null;
    private long lastUpdateTsMs = Constant.DEFAULT_LONG_VALUE;

    public MileStoneClanInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MileStoneClanInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get clanId
     *
     * @return clanId value
     */
    public long getClanId() {
        return this.clanId;
    }

    /**
     * set clanId && set marked
     *
     * @param clanId new value
     * @return current object
     */
    public MileStoneClanInfoProp setClanId(long clanId) {
        if (this.clanId != clanId) {
            this.mark(FIELD_INDEX_CLANID);
            this.clanId = clanId;
        }
        return this;
    }

    /**
     * inner set clanId
     *
     * @param clanId new value
     */
    private void innerSetClanId(long clanId) {
        this.clanId = clanId;
    }

    /**
     * get score
     *
     * @return score value
     */
    public long getScore() {
        return this.score;
    }

    /**
     * set score && set marked
     *
     * @param score new value
     * @return current object
     */
    public MileStoneClanInfoProp setScore(long score) {
        if (this.score != score) {
            this.mark(FIELD_INDEX_SCORE);
            this.score = score;
        }
        return this;
    }

    /**
     * inner set score
     *
     * @param score new value
     */
    private void innerSetScore(long score) {
        this.score = score;
    }

    /**
     * get simpleClan
     *
     * @return simpleClan value
     */
    public MileStoneSimpleClanProp getSimpleClan() {
        if (this.simpleClan == null) {
            this.simpleClan = new MileStoneSimpleClanProp(this, FIELD_INDEX_SIMPLECLAN);
        }
        return this.simpleClan;
    }

    /**
     * get lastUpdateTsMs
     *
     * @return lastUpdateTsMs value
     */
    public long getLastUpdateTsMs() {
        return this.lastUpdateTsMs;
    }

    /**
     * set lastUpdateTsMs && set marked
     *
     * @param lastUpdateTsMs new value
     * @return current object
     */
    public MileStoneClanInfoProp setLastUpdateTsMs(long lastUpdateTsMs) {
        if (this.lastUpdateTsMs != lastUpdateTsMs) {
            this.mark(FIELD_INDEX_LASTUPDATETSMS);
            this.lastUpdateTsMs = lastUpdateTsMs;
        }
        return this;
    }

    /**
     * inner set lastUpdateTsMs
     *
     * @param lastUpdateTsMs new value
     */
    private void innerSetLastUpdateTsMs(long lastUpdateTsMs) {
        this.lastUpdateTsMs = lastUpdateTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneClanInfoPB.Builder getCopyCsBuilder() {
        final MileStoneClanInfoPB.Builder builder = MileStoneClanInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MileStoneClanInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (this.getScore() != 0L) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (this.simpleClan != null) {
            StructPB.MileStoneSimpleClanPB.Builder tmpBuilder = StructPB.MileStoneSimpleClanPB.newBuilder();
            final int tmpFieldCnt = this.simpleClan.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSimpleClan(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSimpleClan();
            }
        }  else if (builder.hasSimpleClan()) {
            // 清理SimpleClan
            builder.clearSimpleClan();
            fieldCnt++;
        }
        if (this.getLastUpdateTsMs() != 0L) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateTsMs()) {
            // 清理LastUpdateTsMs
            builder.clearLastUpdateTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MileStoneClanInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SIMPLECLAN) && this.simpleClan != null) {
            final boolean needClear = !builder.hasSimpleClan();
            final int tmpFieldCnt = this.simpleClan.copyChangeToCs(builder.getSimpleClanBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSimpleClan();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATETSMS)) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MileStoneClanInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SIMPLECLAN) && this.simpleClan != null) {
            final boolean needClear = !builder.hasSimpleClan();
            final int tmpFieldCnt = this.simpleClan.copyChangeToAndClearDeleteKeysCs(builder.getSimpleClanBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSimpleClan();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATETSMS)) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MileStoneClanInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSimpleClan()) {
            this.getSimpleClan().mergeFromCs(proto.getSimpleClan());
        } else {
            if (this.simpleClan != null) {
                this.simpleClan.mergeFromCs(proto.getSimpleClan());
            }
        }
        if (proto.hasLastUpdateTsMs()) {
            this.innerSetLastUpdateTsMs(proto.getLastUpdateTsMs());
        } else {
            this.innerSetLastUpdateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MileStoneClanInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MileStoneClanInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasSimpleClan()) {
            this.getSimpleClan().mergeChangeFromCs(proto.getSimpleClan());
            fieldCnt++;
        }
        if (proto.hasLastUpdateTsMs()) {
            this.setLastUpdateTsMs(proto.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneClanInfo.Builder getCopyDbBuilder() {
        final MileStoneClanInfo.Builder builder = MileStoneClanInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MileStoneClanInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (this.getScore() != 0L) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (this.simpleClan != null) {
            Struct.MileStoneSimpleClan.Builder tmpBuilder = Struct.MileStoneSimpleClan.newBuilder();
            final int tmpFieldCnt = this.simpleClan.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSimpleClan(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSimpleClan();
            }
        }  else if (builder.hasSimpleClan()) {
            // 清理SimpleClan
            builder.clearSimpleClan();
            fieldCnt++;
        }
        if (this.getLastUpdateTsMs() != 0L) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateTsMs()) {
            // 清理LastUpdateTsMs
            builder.clearLastUpdateTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MileStoneClanInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SIMPLECLAN) && this.simpleClan != null) {
            final boolean needClear = !builder.hasSimpleClan();
            final int tmpFieldCnt = this.simpleClan.copyChangeToDb(builder.getSimpleClanBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSimpleClan();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATETSMS)) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MileStoneClanInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSimpleClan()) {
            this.getSimpleClan().mergeFromDb(proto.getSimpleClan());
        } else {
            if (this.simpleClan != null) {
                this.simpleClan.mergeFromDb(proto.getSimpleClan());
            }
        }
        if (proto.hasLastUpdateTsMs()) {
            this.innerSetLastUpdateTsMs(proto.getLastUpdateTsMs());
        } else {
            this.innerSetLastUpdateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MileStoneClanInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MileStoneClanInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasSimpleClan()) {
            this.getSimpleClan().mergeChangeFromDb(proto.getSimpleClan());
            fieldCnt++;
        }
        if (proto.hasLastUpdateTsMs()) {
            this.setLastUpdateTsMs(proto.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneClanInfo.Builder getCopySsBuilder() {
        final MileStoneClanInfo.Builder builder = MileStoneClanInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MileStoneClanInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (this.getScore() != 0L) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (this.simpleClan != null) {
            Struct.MileStoneSimpleClan.Builder tmpBuilder = Struct.MileStoneSimpleClan.newBuilder();
            final int tmpFieldCnt = this.simpleClan.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSimpleClan(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSimpleClan();
            }
        }  else if (builder.hasSimpleClan()) {
            // 清理SimpleClan
            builder.clearSimpleClan();
            fieldCnt++;
        }
        if (this.getLastUpdateTsMs() != 0L) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateTsMs()) {
            // 清理LastUpdateTsMs
            builder.clearLastUpdateTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MileStoneClanInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SIMPLECLAN) && this.simpleClan != null) {
            final boolean needClear = !builder.hasSimpleClan();
            final int tmpFieldCnt = this.simpleClan.copyChangeToSs(builder.getSimpleClanBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSimpleClan();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATETSMS)) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MileStoneClanInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSimpleClan()) {
            this.getSimpleClan().mergeFromSs(proto.getSimpleClan());
        } else {
            if (this.simpleClan != null) {
                this.simpleClan.mergeFromSs(proto.getSimpleClan());
            }
        }
        if (proto.hasLastUpdateTsMs()) {
            this.innerSetLastUpdateTsMs(proto.getLastUpdateTsMs());
        } else {
            this.innerSetLastUpdateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MileStoneClanInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MileStoneClanInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasSimpleClan()) {
            this.getSimpleClan().mergeChangeFromSs(proto.getSimpleClan());
            fieldCnt++;
        }
        if (proto.hasLastUpdateTsMs()) {
            this.setLastUpdateTsMs(proto.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MileStoneClanInfo.Builder builder = MileStoneClanInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SIMPLECLAN) && this.simpleClan != null) {
            this.simpleClan.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.simpleClan != null) {
            this.simpleClan.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.clanId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MileStoneClanInfoProp)) {
            return false;
        }
        final MileStoneClanInfoProp otherNode = (MileStoneClanInfoProp) node;
        if (this.clanId != otherNode.clanId) {
            return false;
        }
        if (this.score != otherNode.score) {
            return false;
        }
        if (!this.getSimpleClan().compareDataTo(otherNode.getSimpleClan())) {
            return false;
        }
        if (this.lastUpdateTsMs != otherNode.lastUpdateTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
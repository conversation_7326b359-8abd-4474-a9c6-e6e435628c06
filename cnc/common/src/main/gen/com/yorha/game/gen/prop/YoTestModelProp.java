package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.YoTest.YoTestModel;
import com.yorha.proto.YoTest;
import com.yorha.proto.YoTestPB.YoTestModelPB;
import com.yorha.proto.YoTestPB;


/**
 * <AUTHOR> auto gen
 */
public class YoTestModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TESTMODELINTFIELD = 0;
    public static final int FIELD_INDEX_TESTMODELLONGFIELD = 1;
    public static final int FIELD_INDEX_TESTMODELMAPFIELD = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int testModelIntField = Constant.DEFAULT_INT_VALUE;
    private long testModelLongField = Constant.DEFAULT_LONG_VALUE;
    private Int32YoTestModelMapMapProp testModelMapField = null;

    public YoTestModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public YoTestModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get testModelIntField
     *
     * @return testModelIntField value
     */
    public int getTestModelIntField() {
        return this.testModelIntField;
    }

    /**
     * set testModelIntField && set marked
     *
     * @param testModelIntField new value
     * @return current object
     */
    public YoTestModelProp setTestModelIntField(int testModelIntField) {
        if (this.testModelIntField != testModelIntField) {
            this.mark(FIELD_INDEX_TESTMODELINTFIELD);
            this.testModelIntField = testModelIntField;
        }
        return this;
    }

    /**
     * inner set testModelIntField
     *
     * @param testModelIntField new value
     */
    private void innerSetTestModelIntField(int testModelIntField) {
        this.testModelIntField = testModelIntField;
    }

    /**
     * get testModelLongField
     *
     * @return testModelLongField value
     */
    public long getTestModelLongField() {
        return this.testModelLongField;
    }

    /**
     * set testModelLongField && set marked
     *
     * @param testModelLongField new value
     * @return current object
     */
    public YoTestModelProp setTestModelLongField(long testModelLongField) {
        if (this.testModelLongField != testModelLongField) {
            this.mark(FIELD_INDEX_TESTMODELLONGFIELD);
            this.testModelLongField = testModelLongField;
        }
        return this;
    }

    /**
     * inner set testModelLongField
     *
     * @param testModelLongField new value
     */
    private void innerSetTestModelLongField(long testModelLongField) {
        this.testModelLongField = testModelLongField;
    }

    /**
     * get testModelMapField
     *
     * @return testModelMapField value
     */
    public Int32YoTestModelMapMapProp getTestModelMapField() {
        if (this.testModelMapField == null) {
            this.testModelMapField = new Int32YoTestModelMapMapProp(this, FIELD_INDEX_TESTMODELMAPFIELD);
        }
        return this.testModelMapField;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTestModelMapFieldV(YoTestModelMapProp v) {
        this.getTestModelMapField().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public YoTestModelMapProp addEmptyTestModelMapField(Integer k) {
        return this.getTestModelMapField().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTestModelMapFieldSize() {
        if (this.testModelMapField == null) {
            return 0;
        }
        return this.testModelMapField.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTestModelMapFieldEmpty() {
        if (this.testModelMapField == null) {
            return true;
        }
        return this.testModelMapField.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public YoTestModelMapProp getTestModelMapFieldV(Integer k) {
        if (this.testModelMapField == null || !this.testModelMapField.containsKey(k)) {
            return null;
        }
        return this.testModelMapField.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTestModelMapField() {
        if (this.testModelMapField != null) {
            this.testModelMapField.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTestModelMapFieldV(Integer k) {
        if (this.testModelMapField != null) {
            this.testModelMapField.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestModelPB.Builder getCopyCsBuilder() {
        final YoTestModelPB.Builder builder = YoTestModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(YoTestModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTestModelIntField() != 0) {
            builder.setTestModelIntField(this.getTestModelIntField());
            fieldCnt++;
        }  else if (builder.hasTestModelIntField()) {
            // 清理TestModelIntField
            builder.clearTestModelIntField();
            fieldCnt++;
        }
        if (this.getTestModelLongField() != 0L) {
            builder.setTestModelLongField(this.getTestModelLongField());
            fieldCnt++;
        }  else if (builder.hasTestModelLongField()) {
            // 清理TestModelLongField
            builder.clearTestModelLongField();
            fieldCnt++;
        }
        if (this.testModelMapField != null) {
            YoTestPB.Int32YoTestModelMapMapPB.Builder tmpBuilder = YoTestPB.Int32YoTestModelMapMapPB.newBuilder();
            final int tmpFieldCnt = this.testModelMapField.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTestModelMapField(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTestModelMapField();
            }
        }  else if (builder.hasTestModelMapField()) {
            // 清理TestModelMapField
            builder.clearTestModelMapField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(YoTestModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TESTMODELINTFIELD)) {
            builder.setTestModelIntField(this.getTestModelIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTMODELLONGFIELD)) {
            builder.setTestModelLongField(this.getTestModelLongField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTMODELMAPFIELD) && this.testModelMapField != null) {
            final boolean needClear = !builder.hasTestModelMapField();
            final int tmpFieldCnt = this.testModelMapField.copyChangeToCs(builder.getTestModelMapFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTestModelMapField();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(YoTestModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TESTMODELINTFIELD)) {
            builder.setTestModelIntField(this.getTestModelIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTMODELLONGFIELD)) {
            builder.setTestModelLongField(this.getTestModelLongField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTMODELMAPFIELD) && this.testModelMapField != null) {
            final boolean needClear = !builder.hasTestModelMapField();
            final int tmpFieldCnt = this.testModelMapField.copyChangeToAndClearDeleteKeysCs(builder.getTestModelMapFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTestModelMapField();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(YoTestModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTestModelIntField()) {
            this.innerSetTestModelIntField(proto.getTestModelIntField());
        } else {
            this.innerSetTestModelIntField(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTestModelLongField()) {
            this.innerSetTestModelLongField(proto.getTestModelLongField());
        } else {
            this.innerSetTestModelLongField(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTestModelMapField()) {
            this.getTestModelMapField().mergeFromCs(proto.getTestModelMapField());
        } else {
            if (this.testModelMapField != null) {
                this.testModelMapField.mergeFromCs(proto.getTestModelMapField());
            }
        }
        this.markAll();
        return YoTestModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(YoTestModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTestModelIntField()) {
            this.setTestModelIntField(proto.getTestModelIntField());
            fieldCnt++;
        }
        if (proto.hasTestModelLongField()) {
            this.setTestModelLongField(proto.getTestModelLongField());
            fieldCnt++;
        }
        if (proto.hasTestModelMapField()) {
            this.getTestModelMapField().mergeChangeFromCs(proto.getTestModelMapField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestModel.Builder getCopyDbBuilder() {
        final YoTestModel.Builder builder = YoTestModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(YoTestModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTestModelIntField() != 0) {
            builder.setTestModelIntField(this.getTestModelIntField());
            fieldCnt++;
        }  else if (builder.hasTestModelIntField()) {
            // 清理TestModelIntField
            builder.clearTestModelIntField();
            fieldCnt++;
        }
        if (this.getTestModelLongField() != 0L) {
            builder.setTestModelLongField(this.getTestModelLongField());
            fieldCnt++;
        }  else if (builder.hasTestModelLongField()) {
            // 清理TestModelLongField
            builder.clearTestModelLongField();
            fieldCnt++;
        }
        if (this.testModelMapField != null) {
            YoTest.Int32YoTestModelMapMap.Builder tmpBuilder = YoTest.Int32YoTestModelMapMap.newBuilder();
            final int tmpFieldCnt = this.testModelMapField.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTestModelMapField(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTestModelMapField();
            }
        }  else if (builder.hasTestModelMapField()) {
            // 清理TestModelMapField
            builder.clearTestModelMapField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(YoTestModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TESTMODELINTFIELD)) {
            builder.setTestModelIntField(this.getTestModelIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTMODELLONGFIELD)) {
            builder.setTestModelLongField(this.getTestModelLongField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTMODELMAPFIELD) && this.testModelMapField != null) {
            final boolean needClear = !builder.hasTestModelMapField();
            final int tmpFieldCnt = this.testModelMapField.copyChangeToDb(builder.getTestModelMapFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTestModelMapField();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(YoTestModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTestModelIntField()) {
            this.innerSetTestModelIntField(proto.getTestModelIntField());
        } else {
            this.innerSetTestModelIntField(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTestModelLongField()) {
            this.innerSetTestModelLongField(proto.getTestModelLongField());
        } else {
            this.innerSetTestModelLongField(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTestModelMapField()) {
            this.getTestModelMapField().mergeFromDb(proto.getTestModelMapField());
        } else {
            if (this.testModelMapField != null) {
                this.testModelMapField.mergeFromDb(proto.getTestModelMapField());
            }
        }
        this.markAll();
        return YoTestModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(YoTestModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTestModelIntField()) {
            this.setTestModelIntField(proto.getTestModelIntField());
            fieldCnt++;
        }
        if (proto.hasTestModelLongField()) {
            this.setTestModelLongField(proto.getTestModelLongField());
            fieldCnt++;
        }
        if (proto.hasTestModelMapField()) {
            this.getTestModelMapField().mergeChangeFromDb(proto.getTestModelMapField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestModel.Builder getCopySsBuilder() {
        final YoTestModel.Builder builder = YoTestModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(YoTestModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTestModelIntField() != 0) {
            builder.setTestModelIntField(this.getTestModelIntField());
            fieldCnt++;
        }  else if (builder.hasTestModelIntField()) {
            // 清理TestModelIntField
            builder.clearTestModelIntField();
            fieldCnt++;
        }
        if (this.getTestModelLongField() != 0L) {
            builder.setTestModelLongField(this.getTestModelLongField());
            fieldCnt++;
        }  else if (builder.hasTestModelLongField()) {
            // 清理TestModelLongField
            builder.clearTestModelLongField();
            fieldCnt++;
        }
        if (this.testModelMapField != null) {
            YoTest.Int32YoTestModelMapMap.Builder tmpBuilder = YoTest.Int32YoTestModelMapMap.newBuilder();
            final int tmpFieldCnt = this.testModelMapField.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTestModelMapField(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTestModelMapField();
            }
        }  else if (builder.hasTestModelMapField()) {
            // 清理TestModelMapField
            builder.clearTestModelMapField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(YoTestModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TESTMODELINTFIELD)) {
            builder.setTestModelIntField(this.getTestModelIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTMODELLONGFIELD)) {
            builder.setTestModelLongField(this.getTestModelLongField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTMODELMAPFIELD) && this.testModelMapField != null) {
            final boolean needClear = !builder.hasTestModelMapField();
            final int tmpFieldCnt = this.testModelMapField.copyChangeToSs(builder.getTestModelMapFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTestModelMapField();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(YoTestModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTestModelIntField()) {
            this.innerSetTestModelIntField(proto.getTestModelIntField());
        } else {
            this.innerSetTestModelIntField(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTestModelLongField()) {
            this.innerSetTestModelLongField(proto.getTestModelLongField());
        } else {
            this.innerSetTestModelLongField(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTestModelMapField()) {
            this.getTestModelMapField().mergeFromSs(proto.getTestModelMapField());
        } else {
            if (this.testModelMapField != null) {
                this.testModelMapField.mergeFromSs(proto.getTestModelMapField());
            }
        }
        this.markAll();
        return YoTestModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(YoTestModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTestModelIntField()) {
            this.setTestModelIntField(proto.getTestModelIntField());
            fieldCnt++;
        }
        if (proto.hasTestModelLongField()) {
            this.setTestModelLongField(proto.getTestModelLongField());
            fieldCnt++;
        }
        if (proto.hasTestModelMapField()) {
            this.getTestModelMapField().mergeChangeFromSs(proto.getTestModelMapField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        YoTestModel.Builder builder = YoTestModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TESTMODELMAPFIELD) && this.testModelMapField != null) {
            this.testModelMapField.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.testModelMapField != null) {
            this.testModelMapField.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof YoTestModelProp)) {
            return false;
        }
        final YoTestModelProp otherNode = (YoTestModelProp) node;
        if (this.testModelIntField != otherNode.testModelIntField) {
            return false;
        }
        if (this.testModelLongField != otherNode.testModelLongField) {
            return false;
        }
        if (!this.getTestModelMapField().compareDataTo(otherNode.getTestModelMapField())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
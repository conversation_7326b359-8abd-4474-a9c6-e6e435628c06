package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zone.ActivityInfo;
import com.yorha.proto.ZonePB.ActivityInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ACTIVITYEFFECT = 0;
    public static final int FIELD_INDEX_ACTIVITYENDTSMS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int activityEffect = Constant.DEFAULT_INT_VALUE;
    private long activityEndTsMs = Constant.DEFAULT_LONG_VALUE;

    public ActivityInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get activityEffect
     *
     * @return activityEffect value
     */
    public int getActivityEffect() {
        return this.activityEffect;
    }

    /**
     * set activityEffect && set marked
     *
     * @param activityEffect new value
     * @return current object
     */
    public ActivityInfoProp setActivityEffect(int activityEffect) {
        if (this.activityEffect != activityEffect) {
            this.mark(FIELD_INDEX_ACTIVITYEFFECT);
            this.activityEffect = activityEffect;
        }
        return this;
    }

    /**
     * inner set activityEffect
     *
     * @param activityEffect new value
     */
    private void innerSetActivityEffect(int activityEffect) {
        this.activityEffect = activityEffect;
    }

    /**
     * get activityEndTsMs
     *
     * @return activityEndTsMs value
     */
    public long getActivityEndTsMs() {
        return this.activityEndTsMs;
    }

    /**
     * set activityEndTsMs && set marked
     *
     * @param activityEndTsMs new value
     * @return current object
     */
    public ActivityInfoProp setActivityEndTsMs(long activityEndTsMs) {
        if (this.activityEndTsMs != activityEndTsMs) {
            this.mark(FIELD_INDEX_ACTIVITYENDTSMS);
            this.activityEndTsMs = activityEndTsMs;
        }
        return this;
    }

    /**
     * inner set activityEndTsMs
     *
     * @param activityEndTsMs new value
     */
    private void innerSetActivityEndTsMs(long activityEndTsMs) {
        this.activityEndTsMs = activityEndTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityInfoPB.Builder getCopyCsBuilder() {
        final ActivityInfoPB.Builder builder = ActivityInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActivityEffect() != 0) {
            builder.setActivityEffect(this.getActivityEffect());
            fieldCnt++;
        }  else if (builder.hasActivityEffect()) {
            // 清理ActivityEffect
            builder.clearActivityEffect();
            fieldCnt++;
        }
        if (this.getActivityEndTsMs() != 0L) {
            builder.setActivityEndTsMs(this.getActivityEndTsMs());
            fieldCnt++;
        }  else if (builder.hasActivityEndTsMs()) {
            // 清理ActivityEndTsMs
            builder.clearActivityEndTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITYEFFECT)) {
            builder.setActivityEffect(this.getActivityEffect());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITYENDTSMS)) {
            builder.setActivityEndTsMs(this.getActivityEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITYEFFECT)) {
            builder.setActivityEffect(this.getActivityEffect());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITYENDTSMS)) {
            builder.setActivityEndTsMs(this.getActivityEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActivityEffect()) {
            this.innerSetActivityEffect(proto.getActivityEffect());
        } else {
            this.innerSetActivityEffect(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasActivityEndTsMs()) {
            this.innerSetActivityEndTsMs(proto.getActivityEndTsMs());
        } else {
            this.innerSetActivityEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ActivityInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActivityEffect()) {
            this.setActivityEffect(proto.getActivityEffect());
            fieldCnt++;
        }
        if (proto.hasActivityEndTsMs()) {
            this.setActivityEndTsMs(proto.getActivityEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityInfo.Builder getCopyDbBuilder() {
        final ActivityInfo.Builder builder = ActivityInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActivityEffect() != 0) {
            builder.setActivityEffect(this.getActivityEffect());
            fieldCnt++;
        }  else if (builder.hasActivityEffect()) {
            // 清理ActivityEffect
            builder.clearActivityEffect();
            fieldCnt++;
        }
        if (this.getActivityEndTsMs() != 0L) {
            builder.setActivityEndTsMs(this.getActivityEndTsMs());
            fieldCnt++;
        }  else if (builder.hasActivityEndTsMs()) {
            // 清理ActivityEndTsMs
            builder.clearActivityEndTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITYEFFECT)) {
            builder.setActivityEffect(this.getActivityEffect());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITYENDTSMS)) {
            builder.setActivityEndTsMs(this.getActivityEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActivityEffect()) {
            this.innerSetActivityEffect(proto.getActivityEffect());
        } else {
            this.innerSetActivityEffect(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasActivityEndTsMs()) {
            this.innerSetActivityEndTsMs(proto.getActivityEndTsMs());
        } else {
            this.innerSetActivityEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ActivityInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActivityEffect()) {
            this.setActivityEffect(proto.getActivityEffect());
            fieldCnt++;
        }
        if (proto.hasActivityEndTsMs()) {
            this.setActivityEndTsMs(proto.getActivityEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityInfo.Builder getCopySsBuilder() {
        final ActivityInfo.Builder builder = ActivityInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActivityEffect() != 0) {
            builder.setActivityEffect(this.getActivityEffect());
            fieldCnt++;
        }  else if (builder.hasActivityEffect()) {
            // 清理ActivityEffect
            builder.clearActivityEffect();
            fieldCnt++;
        }
        if (this.getActivityEndTsMs() != 0L) {
            builder.setActivityEndTsMs(this.getActivityEndTsMs());
            fieldCnt++;
        }  else if (builder.hasActivityEndTsMs()) {
            // 清理ActivityEndTsMs
            builder.clearActivityEndTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITYEFFECT)) {
            builder.setActivityEffect(this.getActivityEffect());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITYENDTSMS)) {
            builder.setActivityEndTsMs(this.getActivityEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActivityEffect()) {
            this.innerSetActivityEffect(proto.getActivityEffect());
        } else {
            this.innerSetActivityEffect(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasActivityEndTsMs()) {
            this.innerSetActivityEndTsMs(proto.getActivityEndTsMs());
        } else {
            this.innerSetActivityEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ActivityInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActivityEffect()) {
            this.setActivityEffect(proto.getActivityEffect());
            fieldCnt++;
        }
        if (proto.hasActivityEndTsMs()) {
            this.setActivityEndTsMs(proto.getActivityEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityInfo.Builder builder = ActivityInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.activityEffect;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityInfoProp)) {
            return false;
        }
        final ActivityInfoProp otherNode = (ActivityInfoProp) node;
        if (this.activityEffect != otherNode.activityEffect) {
            return false;
        }
        if (this.activityEndTsMs != otherNode.activityEndTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
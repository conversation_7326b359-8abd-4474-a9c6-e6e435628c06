package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructBattle.BattleRecordRoundEvent;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructBattlePB.BattleRecordRoundEventPB;
import com.yorha.proto.StructBattlePB;


/**
 * <AUTHOR> auto gen
 */
public class BattleRecordRoundEventProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ROUND = 0;
    public static final int FIELD_INDEX_TROOPEVENTLIST = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int round = Constant.DEFAULT_INT_VALUE;
    private BattleRecordTroopEventListProp troopEventList = null;

    public BattleRecordRoundEventProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BattleRecordRoundEventProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get round
     *
     * @return round value
     */
    public int getRound() {
        return this.round;
    }

    /**
     * set round && set marked
     *
     * @param round new value
     * @return current object
     */
    public BattleRecordRoundEventProp setRound(int round) {
        if (this.round != round) {
            this.mark(FIELD_INDEX_ROUND);
            this.round = round;
        }
        return this;
    }

    /**
     * inner set round
     *
     * @param round new value
     */
    private void innerSetRound(int round) {
        this.round = round;
    }

    /**
     * get troopEventList
     *
     * @return troopEventList value
     */
    public BattleRecordTroopEventListProp getTroopEventList() {
        if (this.troopEventList == null) {
            this.troopEventList = new BattleRecordTroopEventListProp(this, FIELD_INDEX_TROOPEVENTLIST);
        }
        return this.troopEventList;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addTroopEventList(BattleRecordTroopEventProp v) {
        this.getTroopEventList().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public BattleRecordTroopEventProp getTroopEventListIndex(int index) {
        return this.getTroopEventList().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public BattleRecordTroopEventProp removeTroopEventList(BattleRecordTroopEventProp v) {
        if (this.troopEventList == null) {
            return null;
        }
        if(this.troopEventList.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getTroopEventListSize() {
        if (this.troopEventList == null) {
            return 0;
        }
        return this.troopEventList.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isTroopEventListEmpty() {
        if (this.troopEventList == null) {
            return true;
        }
        return this.getTroopEventList().isEmpty();
    }

    /**
     * clear list
     */
    public void clearTroopEventList() {
        this.getTroopEventList().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public BattleRecordTroopEventProp removeTroopEventListIndex(int index) {
        return this.getTroopEventList().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public BattleRecordTroopEventProp setTroopEventListIndex(int index, BattleRecordTroopEventProp v) {
        return this.getTroopEventList().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRoundEventPB.Builder getCopyCsBuilder() {
        final BattleRecordRoundEventPB.Builder builder = BattleRecordRoundEventPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BattleRecordRoundEventPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRound() != 0) {
            builder.setRound(this.getRound());
            fieldCnt++;
        }  else if (builder.hasRound()) {
            // 清理Round
            builder.clearRound();
            fieldCnt++;
        }
        if (this.troopEventList != null) {
            StructBattlePB.BattleRecordTroopEventListPB.Builder tmpBuilder = StructBattlePB.BattleRecordTroopEventListPB.newBuilder();
            final int tmpFieldCnt = this.troopEventList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroopEventList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroopEventList();
            }
        }  else if (builder.hasTroopEventList()) {
            // 清理TroopEventList
            builder.clearTroopEventList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BattleRecordRoundEventPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ROUND)) {
            builder.setRound(this.getRound());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOPEVENTLIST) && this.troopEventList != null) {
            final boolean needClear = !builder.hasTroopEventList();
            final int tmpFieldCnt = this.troopEventList.copyChangeToCs(builder.getTroopEventListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroopEventList();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BattleRecordRoundEventPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ROUND)) {
            builder.setRound(this.getRound());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOPEVENTLIST) && this.troopEventList != null) {
            final boolean needClear = !builder.hasTroopEventList();
            final int tmpFieldCnt = this.troopEventList.copyChangeToCs(builder.getTroopEventListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroopEventList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BattleRecordRoundEventPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRound()) {
            this.innerSetRound(proto.getRound());
        } else {
            this.innerSetRound(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTroopEventList()) {
            this.getTroopEventList().mergeFromCs(proto.getTroopEventList());
        } else {
            if (this.troopEventList != null) {
                this.troopEventList.mergeFromCs(proto.getTroopEventList());
            }
        }
        this.markAll();
        return BattleRecordRoundEventProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BattleRecordRoundEventPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRound()) {
            this.setRound(proto.getRound());
            fieldCnt++;
        }
        if (proto.hasTroopEventList()) {
            this.getTroopEventList().mergeChangeFromCs(proto.getTroopEventList());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRoundEvent.Builder getCopyDbBuilder() {
        final BattleRecordRoundEvent.Builder builder = BattleRecordRoundEvent.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(BattleRecordRoundEvent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRound() != 0) {
            builder.setRound(this.getRound());
            fieldCnt++;
        }  else if (builder.hasRound()) {
            // 清理Round
            builder.clearRound();
            fieldCnt++;
        }
        if (this.troopEventList != null) {
            StructBattle.BattleRecordTroopEventList.Builder tmpBuilder = StructBattle.BattleRecordTroopEventList.newBuilder();
            final int tmpFieldCnt = this.troopEventList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroopEventList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroopEventList();
            }
        }  else if (builder.hasTroopEventList()) {
            // 清理TroopEventList
            builder.clearTroopEventList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(BattleRecordRoundEvent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ROUND)) {
            builder.setRound(this.getRound());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOPEVENTLIST) && this.troopEventList != null) {
            final boolean needClear = !builder.hasTroopEventList();
            final int tmpFieldCnt = this.troopEventList.copyChangeToDb(builder.getTroopEventListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroopEventList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(BattleRecordRoundEvent proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRound()) {
            this.innerSetRound(proto.getRound());
        } else {
            this.innerSetRound(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTroopEventList()) {
            this.getTroopEventList().mergeFromDb(proto.getTroopEventList());
        } else {
            if (this.troopEventList != null) {
                this.troopEventList.mergeFromDb(proto.getTroopEventList());
            }
        }
        this.markAll();
        return BattleRecordRoundEventProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(BattleRecordRoundEvent proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRound()) {
            this.setRound(proto.getRound());
            fieldCnt++;
        }
        if (proto.hasTroopEventList()) {
            this.getTroopEventList().mergeChangeFromDb(proto.getTroopEventList());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRoundEvent.Builder getCopySsBuilder() {
        final BattleRecordRoundEvent.Builder builder = BattleRecordRoundEvent.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(BattleRecordRoundEvent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRound() != 0) {
            builder.setRound(this.getRound());
            fieldCnt++;
        }  else if (builder.hasRound()) {
            // 清理Round
            builder.clearRound();
            fieldCnt++;
        }
        if (this.troopEventList != null) {
            StructBattle.BattleRecordTroopEventList.Builder tmpBuilder = StructBattle.BattleRecordTroopEventList.newBuilder();
            final int tmpFieldCnt = this.troopEventList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroopEventList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroopEventList();
            }
        }  else if (builder.hasTroopEventList()) {
            // 清理TroopEventList
            builder.clearTroopEventList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(BattleRecordRoundEvent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ROUND)) {
            builder.setRound(this.getRound());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOPEVENTLIST) && this.troopEventList != null) {
            final boolean needClear = !builder.hasTroopEventList();
            final int tmpFieldCnt = this.troopEventList.copyChangeToSs(builder.getTroopEventListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroopEventList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(BattleRecordRoundEvent proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRound()) {
            this.innerSetRound(proto.getRound());
        } else {
            this.innerSetRound(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTroopEventList()) {
            this.getTroopEventList().mergeFromSs(proto.getTroopEventList());
        } else {
            if (this.troopEventList != null) {
                this.troopEventList.mergeFromSs(proto.getTroopEventList());
            }
        }
        this.markAll();
        return BattleRecordRoundEventProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(BattleRecordRoundEvent proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRound()) {
            this.setRound(proto.getRound());
            fieldCnt++;
        }
        if (proto.hasTroopEventList()) {
            this.getTroopEventList().mergeChangeFromSs(proto.getTroopEventList());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        BattleRecordRoundEvent.Builder builder = BattleRecordRoundEvent.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TROOPEVENTLIST) && this.troopEventList != null) {
            this.troopEventList.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.troopEventList != null) {
            this.troopEventList.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BattleRecordRoundEventProp)) {
            return false;
        }
        final BattleRecordRoundEventProp otherNode = (BattleRecordRoundEventProp) node;
        if (this.round != otherNode.round) {
            return false;
        }
        if (!this.getTroopEventList().compareDataTo(otherNode.getTroopEventList())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructClan.ClanGiftItem;
import com.yorha.proto.Struct;
import com.yorha.proto.StructClanPB.ClanGiftItemPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanGiftItemProp extends AbstractPropNode {

    public static final int FIELD_INDEX_GIFTUNIQUEID = 0;
    public static final int FIELD_INDEX_GIFTID = 1;
    public static final int FIELD_INDEX_STATUS = 2;
    public static final int FIELD_INDEX_GIFTRECORD = 3;
    public static final int FIELD_INDEX_CREATETSSEC = 4;
    public static final int FIELD_INDEX_GIFTLEVEL = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private long giftUniqueId = Constant.DEFAULT_LONG_VALUE;
    private int giftId = Constant.DEFAULT_INT_VALUE;
    private ClanGiftStatus status = ClanGiftStatus.forNumber(0);
    private ClanGiftRecordProp giftRecord = null;
    private int createTsSec = Constant.DEFAULT_INT_VALUE;
    private int giftLevel = Constant.DEFAULT_INT_VALUE;

    public ClanGiftItemProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanGiftItemProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get giftUniqueId
     *
     * @return giftUniqueId value
     */
    public long getGiftUniqueId() {
        return this.giftUniqueId;
    }

    /**
     * set giftUniqueId && set marked
     *
     * @param giftUniqueId new value
     * @return current object
     */
    public ClanGiftItemProp setGiftUniqueId(long giftUniqueId) {
        if (this.giftUniqueId != giftUniqueId) {
            this.mark(FIELD_INDEX_GIFTUNIQUEID);
            this.giftUniqueId = giftUniqueId;
        }
        return this;
    }

    /**
     * inner set giftUniqueId
     *
     * @param giftUniqueId new value
     */
    private void innerSetGiftUniqueId(long giftUniqueId) {
        this.giftUniqueId = giftUniqueId;
    }

    /**
     * get giftId
     *
     * @return giftId value
     */
    public int getGiftId() {
        return this.giftId;
    }

    /**
     * set giftId && set marked
     *
     * @param giftId new value
     * @return current object
     */
    public ClanGiftItemProp setGiftId(int giftId) {
        if (this.giftId != giftId) {
            this.mark(FIELD_INDEX_GIFTID);
            this.giftId = giftId;
        }
        return this;
    }

    /**
     * inner set giftId
     *
     * @param giftId new value
     */
    private void innerSetGiftId(int giftId) {
        this.giftId = giftId;
    }

    /**
     * get status
     *
     * @return status value
     */
    public ClanGiftStatus getStatus() {
        return this.status;
    }

    /**
     * set status && set marked
     *
     * @param status new value
     * @return current object
     */
    public ClanGiftItemProp setStatus(ClanGiftStatus status) {
        if (status == null) {
            throw new NullPointerException();
        }
        if (this.status != status) {
            this.mark(FIELD_INDEX_STATUS);
            this.status = status;
        }
        return this;
    }

    /**
     * inner set status
     *
     * @param status new value
     */
    private void innerSetStatus(ClanGiftStatus status) {
        this.status = status;
    }

    /**
     * get giftRecord
     *
     * @return giftRecord value
     */
    public ClanGiftRecordProp getGiftRecord() {
        if (this.giftRecord == null) {
            this.giftRecord = new ClanGiftRecordProp(this, FIELD_INDEX_GIFTRECORD);
        }
        return this.giftRecord;
    }

    /**
     * get createTsSec
     *
     * @return createTsSec value
     */
    public int getCreateTsSec() {
        return this.createTsSec;
    }

    /**
     * set createTsSec && set marked
     *
     * @param createTsSec new value
     * @return current object
     */
    public ClanGiftItemProp setCreateTsSec(int createTsSec) {
        if (this.createTsSec != createTsSec) {
            this.mark(FIELD_INDEX_CREATETSSEC);
            this.createTsSec = createTsSec;
        }
        return this;
    }

    /**
     * inner set createTsSec
     *
     * @param createTsSec new value
     */
    private void innerSetCreateTsSec(int createTsSec) {
        this.createTsSec = createTsSec;
    }

    /**
     * get giftLevel
     *
     * @return giftLevel value
     */
    public int getGiftLevel() {
        return this.giftLevel;
    }

    /**
     * set giftLevel && set marked
     *
     * @param giftLevel new value
     * @return current object
     */
    public ClanGiftItemProp setGiftLevel(int giftLevel) {
        if (this.giftLevel != giftLevel) {
            this.mark(FIELD_INDEX_GIFTLEVEL);
            this.giftLevel = giftLevel;
        }
        return this;
    }

    /**
     * inner set giftLevel
     *
     * @param giftLevel new value
     */
    private void innerSetGiftLevel(int giftLevel) {
        this.giftLevel = giftLevel;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanGiftItemPB.Builder getCopyCsBuilder() {
        final ClanGiftItemPB.Builder builder = ClanGiftItemPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanGiftItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGiftUniqueId() != 0L) {
            builder.setGiftUniqueId(this.getGiftUniqueId());
            fieldCnt++;
        }  else if (builder.hasGiftUniqueId()) {
            // 清理GiftUniqueId
            builder.clearGiftUniqueId();
            fieldCnt++;
        }
        if (this.getGiftId() != 0) {
            builder.setGiftId(this.getGiftId());
            fieldCnt++;
        }  else if (builder.hasGiftId()) {
            // 清理GiftId
            builder.clearGiftId();
            fieldCnt++;
        }
        if (this.getStatus() != ClanGiftStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.giftRecord != null) {
            StructPB.ClanGiftRecordPB.Builder tmpBuilder = StructPB.ClanGiftRecordPB.newBuilder();
            final int tmpFieldCnt = this.giftRecord.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGiftRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGiftRecord();
            }
        }  else if (builder.hasGiftRecord()) {
            // 清理GiftRecord
            builder.clearGiftRecord();
            fieldCnt++;
        }
        if (this.getCreateTsSec() != 0) {
            builder.setCreateTsSec(this.getCreateTsSec());
            fieldCnt++;
        }  else if (builder.hasCreateTsSec()) {
            // 清理CreateTsSec
            builder.clearCreateTsSec();
            fieldCnt++;
        }
        if (this.getGiftLevel() != 0) {
            builder.setGiftLevel(this.getGiftLevel());
            fieldCnt++;
        }  else if (builder.hasGiftLevel()) {
            // 清理GiftLevel
            builder.clearGiftLevel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanGiftItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GIFTUNIQUEID)) {
            builder.setGiftUniqueId(this.getGiftUniqueId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTID)) {
            builder.setGiftId(this.getGiftId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTRECORD) && this.giftRecord != null) {
            final boolean needClear = !builder.hasGiftRecord();
            final int tmpFieldCnt = this.giftRecord.copyChangeToCs(builder.getGiftRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETSSEC)) {
            builder.setCreateTsSec(this.getCreateTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTLEVEL)) {
            builder.setGiftLevel(this.getGiftLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanGiftItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GIFTUNIQUEID)) {
            builder.setGiftUniqueId(this.getGiftUniqueId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTID)) {
            builder.setGiftId(this.getGiftId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTRECORD) && this.giftRecord != null) {
            final boolean needClear = !builder.hasGiftRecord();
            final int tmpFieldCnt = this.giftRecord.copyChangeToAndClearDeleteKeysCs(builder.getGiftRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETSSEC)) {
            builder.setCreateTsSec(this.getCreateTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTLEVEL)) {
            builder.setGiftLevel(this.getGiftLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanGiftItemPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGiftUniqueId()) {
            this.innerSetGiftUniqueId(proto.getGiftUniqueId());
        } else {
            this.innerSetGiftUniqueId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasGiftId()) {
            this.innerSetGiftId(proto.getGiftId());
        } else {
            this.innerSetGiftId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(ClanGiftStatus.forNumber(0));
        }
        if (proto.hasGiftRecord()) {
            this.getGiftRecord().mergeFromCs(proto.getGiftRecord());
        } else {
            if (this.giftRecord != null) {
                this.giftRecord.mergeFromCs(proto.getGiftRecord());
            }
        }
        if (proto.hasCreateTsSec()) {
            this.innerSetCreateTsSec(proto.getCreateTsSec());
        } else {
            this.innerSetCreateTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasGiftLevel()) {
            this.innerSetGiftLevel(proto.getGiftLevel());
        } else {
            this.innerSetGiftLevel(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanGiftItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanGiftItemPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGiftUniqueId()) {
            this.setGiftUniqueId(proto.getGiftUniqueId());
            fieldCnt++;
        }
        if (proto.hasGiftId()) {
            this.setGiftId(proto.getGiftId());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasGiftRecord()) {
            this.getGiftRecord().mergeChangeFromCs(proto.getGiftRecord());
            fieldCnt++;
        }
        if (proto.hasCreateTsSec()) {
            this.setCreateTsSec(proto.getCreateTsSec());
            fieldCnt++;
        }
        if (proto.hasGiftLevel()) {
            this.setGiftLevel(proto.getGiftLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanGiftItem.Builder getCopyDbBuilder() {
        final ClanGiftItem.Builder builder = ClanGiftItem.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanGiftItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGiftUniqueId() != 0L) {
            builder.setGiftUniqueId(this.getGiftUniqueId());
            fieldCnt++;
        }  else if (builder.hasGiftUniqueId()) {
            // 清理GiftUniqueId
            builder.clearGiftUniqueId();
            fieldCnt++;
        }
        if (this.getGiftId() != 0) {
            builder.setGiftId(this.getGiftId());
            fieldCnt++;
        }  else if (builder.hasGiftId()) {
            // 清理GiftId
            builder.clearGiftId();
            fieldCnt++;
        }
        if (this.getStatus() != ClanGiftStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.giftRecord != null) {
            Struct.ClanGiftRecord.Builder tmpBuilder = Struct.ClanGiftRecord.newBuilder();
            final int tmpFieldCnt = this.giftRecord.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGiftRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGiftRecord();
            }
        }  else if (builder.hasGiftRecord()) {
            // 清理GiftRecord
            builder.clearGiftRecord();
            fieldCnt++;
        }
        if (this.getCreateTsSec() != 0) {
            builder.setCreateTsSec(this.getCreateTsSec());
            fieldCnt++;
        }  else if (builder.hasCreateTsSec()) {
            // 清理CreateTsSec
            builder.clearCreateTsSec();
            fieldCnt++;
        }
        if (this.getGiftLevel() != 0) {
            builder.setGiftLevel(this.getGiftLevel());
            fieldCnt++;
        }  else if (builder.hasGiftLevel()) {
            // 清理GiftLevel
            builder.clearGiftLevel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanGiftItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GIFTUNIQUEID)) {
            builder.setGiftUniqueId(this.getGiftUniqueId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTID)) {
            builder.setGiftId(this.getGiftId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTRECORD) && this.giftRecord != null) {
            final boolean needClear = !builder.hasGiftRecord();
            final int tmpFieldCnt = this.giftRecord.copyChangeToDb(builder.getGiftRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETSSEC)) {
            builder.setCreateTsSec(this.getCreateTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTLEVEL)) {
            builder.setGiftLevel(this.getGiftLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanGiftItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGiftUniqueId()) {
            this.innerSetGiftUniqueId(proto.getGiftUniqueId());
        } else {
            this.innerSetGiftUniqueId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasGiftId()) {
            this.innerSetGiftId(proto.getGiftId());
        } else {
            this.innerSetGiftId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(ClanGiftStatus.forNumber(0));
        }
        if (proto.hasGiftRecord()) {
            this.getGiftRecord().mergeFromDb(proto.getGiftRecord());
        } else {
            if (this.giftRecord != null) {
                this.giftRecord.mergeFromDb(proto.getGiftRecord());
            }
        }
        if (proto.hasCreateTsSec()) {
            this.innerSetCreateTsSec(proto.getCreateTsSec());
        } else {
            this.innerSetCreateTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasGiftLevel()) {
            this.innerSetGiftLevel(proto.getGiftLevel());
        } else {
            this.innerSetGiftLevel(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanGiftItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanGiftItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGiftUniqueId()) {
            this.setGiftUniqueId(proto.getGiftUniqueId());
            fieldCnt++;
        }
        if (proto.hasGiftId()) {
            this.setGiftId(proto.getGiftId());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasGiftRecord()) {
            this.getGiftRecord().mergeChangeFromDb(proto.getGiftRecord());
            fieldCnt++;
        }
        if (proto.hasCreateTsSec()) {
            this.setCreateTsSec(proto.getCreateTsSec());
            fieldCnt++;
        }
        if (proto.hasGiftLevel()) {
            this.setGiftLevel(proto.getGiftLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanGiftItem.Builder getCopySsBuilder() {
        final ClanGiftItem.Builder builder = ClanGiftItem.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanGiftItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGiftUniqueId() != 0L) {
            builder.setGiftUniqueId(this.getGiftUniqueId());
            fieldCnt++;
        }  else if (builder.hasGiftUniqueId()) {
            // 清理GiftUniqueId
            builder.clearGiftUniqueId();
            fieldCnt++;
        }
        if (this.getGiftId() != 0) {
            builder.setGiftId(this.getGiftId());
            fieldCnt++;
        }  else if (builder.hasGiftId()) {
            // 清理GiftId
            builder.clearGiftId();
            fieldCnt++;
        }
        if (this.getStatus() != ClanGiftStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.giftRecord != null) {
            Struct.ClanGiftRecord.Builder tmpBuilder = Struct.ClanGiftRecord.newBuilder();
            final int tmpFieldCnt = this.giftRecord.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGiftRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGiftRecord();
            }
        }  else if (builder.hasGiftRecord()) {
            // 清理GiftRecord
            builder.clearGiftRecord();
            fieldCnt++;
        }
        if (this.getCreateTsSec() != 0) {
            builder.setCreateTsSec(this.getCreateTsSec());
            fieldCnt++;
        }  else if (builder.hasCreateTsSec()) {
            // 清理CreateTsSec
            builder.clearCreateTsSec();
            fieldCnt++;
        }
        if (this.getGiftLevel() != 0) {
            builder.setGiftLevel(this.getGiftLevel());
            fieldCnt++;
        }  else if (builder.hasGiftLevel()) {
            // 清理GiftLevel
            builder.clearGiftLevel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanGiftItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GIFTUNIQUEID)) {
            builder.setGiftUniqueId(this.getGiftUniqueId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTID)) {
            builder.setGiftId(this.getGiftId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTRECORD) && this.giftRecord != null) {
            final boolean needClear = !builder.hasGiftRecord();
            final int tmpFieldCnt = this.giftRecord.copyChangeToSs(builder.getGiftRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETSSEC)) {
            builder.setCreateTsSec(this.getCreateTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTLEVEL)) {
            builder.setGiftLevel(this.getGiftLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanGiftItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGiftUniqueId()) {
            this.innerSetGiftUniqueId(proto.getGiftUniqueId());
        } else {
            this.innerSetGiftUniqueId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasGiftId()) {
            this.innerSetGiftId(proto.getGiftId());
        } else {
            this.innerSetGiftId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(ClanGiftStatus.forNumber(0));
        }
        if (proto.hasGiftRecord()) {
            this.getGiftRecord().mergeFromSs(proto.getGiftRecord());
        } else {
            if (this.giftRecord != null) {
                this.giftRecord.mergeFromSs(proto.getGiftRecord());
            }
        }
        if (proto.hasCreateTsSec()) {
            this.innerSetCreateTsSec(proto.getCreateTsSec());
        } else {
            this.innerSetCreateTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasGiftLevel()) {
            this.innerSetGiftLevel(proto.getGiftLevel());
        } else {
            this.innerSetGiftLevel(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanGiftItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanGiftItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGiftUniqueId()) {
            this.setGiftUniqueId(proto.getGiftUniqueId());
            fieldCnt++;
        }
        if (proto.hasGiftId()) {
            this.setGiftId(proto.getGiftId());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasGiftRecord()) {
            this.getGiftRecord().mergeChangeFromSs(proto.getGiftRecord());
            fieldCnt++;
        }
        if (proto.hasCreateTsSec()) {
            this.setCreateTsSec(proto.getCreateTsSec());
            fieldCnt++;
        }
        if (proto.hasGiftLevel()) {
            this.setGiftLevel(proto.getGiftLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanGiftItem.Builder builder = ClanGiftItem.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_GIFTRECORD) && this.giftRecord != null) {
            this.giftRecord.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.giftRecord != null) {
            this.giftRecord.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanGiftItemProp)) {
            return false;
        }
        final ClanGiftItemProp otherNode = (ClanGiftItemProp) node;
        if (this.giftUniqueId != otherNode.giftUniqueId) {
            return false;
        }
        if (this.giftId != otherNode.giftId) {
            return false;
        }
        if (this.status != otherNode.status) {
            return false;
        }
        if (!this.getGiftRecord().compareDataTo(otherNode.getGiftRecord())) {
            return false;
        }
        if (this.createTsSec != otherNode.createTsSec) {
            return false;
        }
        if (this.giftLevel != otherNode.giftLevel) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
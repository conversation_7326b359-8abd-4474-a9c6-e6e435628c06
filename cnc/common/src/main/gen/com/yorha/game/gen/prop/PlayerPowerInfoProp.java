package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.PlayerPowerInfo;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.PlayerPowerInfoPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerPowerInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TOTALPOWER = 0;
    public static final int FIELD_INDEX_POWERINFO = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private long totalPower = Constant.DEFAULT_LONG_VALUE;
    private Int32PowerInfoUnitMapProp powerInfo = null;

    public PlayerPowerInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerPowerInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get totalPower
     *
     * @return totalPower value
     */
    public long getTotalPower() {
        return this.totalPower;
    }

    /**
     * set totalPower && set marked
     *
     * @param totalPower new value
     * @return current object
     */
    public PlayerPowerInfoProp setTotalPower(long totalPower) {
        if (this.totalPower != totalPower) {
            this.mark(FIELD_INDEX_TOTALPOWER);
            this.totalPower = totalPower;
        }
        return this;
    }

    /**
     * inner set totalPower
     *
     * @param totalPower new value
     */
    private void innerSetTotalPower(long totalPower) {
        this.totalPower = totalPower;
    }

    /**
     * get powerInfo
     *
     * @return powerInfo value
     */
    public Int32PowerInfoUnitMapProp getPowerInfo() {
        if (this.powerInfo == null) {
            this.powerInfo = new Int32PowerInfoUnitMapProp(this, FIELD_INDEX_POWERINFO);
        }
        return this.powerInfo;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putPowerInfoV(PowerInfoUnitProp v) {
        this.getPowerInfo().put(v.getPowerType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PowerInfoUnitProp addEmptyPowerInfo(Integer k) {
        return this.getPowerInfo().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getPowerInfoSize() {
        if (this.powerInfo == null) {
            return 0;
        }
        return this.powerInfo.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isPowerInfoEmpty() {
        if (this.powerInfo == null) {
            return true;
        }
        return this.powerInfo.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PowerInfoUnitProp getPowerInfoV(Integer k) {
        if (this.powerInfo == null || !this.powerInfo.containsKey(k)) {
            return null;
        }
        return this.powerInfo.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearPowerInfo() {
        if (this.powerInfo != null) {
            this.powerInfo.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removePowerInfoV(Integer k) {
        if (this.powerInfo != null) {
            this.powerInfo.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerPowerInfoPB.Builder getCopyCsBuilder() {
        final PlayerPowerInfoPB.Builder builder = PlayerPowerInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerPowerInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTotalPower() != 0L) {
            builder.setTotalPower(this.getTotalPower());
            fieldCnt++;
        }  else if (builder.hasTotalPower()) {
            // 清理TotalPower
            builder.clearTotalPower();
            fieldCnt++;
        }
        if (this.powerInfo != null) {
            StructPB.Int32PowerInfoUnitMapPB.Builder tmpBuilder = StructPB.Int32PowerInfoUnitMapPB.newBuilder();
            final int tmpFieldCnt = this.powerInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPowerInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPowerInfo();
            }
        }  else if (builder.hasPowerInfo()) {
            // 清理PowerInfo
            builder.clearPowerInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerPowerInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOTALPOWER)) {
            builder.setTotalPower(this.getTotalPower());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POWERINFO) && this.powerInfo != null) {
            final boolean needClear = !builder.hasPowerInfo();
            final int tmpFieldCnt = this.powerInfo.copyChangeToCs(builder.getPowerInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPowerInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerPowerInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOTALPOWER)) {
            builder.setTotalPower(this.getTotalPower());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POWERINFO) && this.powerInfo != null) {
            final boolean needClear = !builder.hasPowerInfo();
            final int tmpFieldCnt = this.powerInfo.copyChangeToAndClearDeleteKeysCs(builder.getPowerInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPowerInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerPowerInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTotalPower()) {
            this.innerSetTotalPower(proto.getTotalPower());
        } else {
            this.innerSetTotalPower(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPowerInfo()) {
            this.getPowerInfo().mergeFromCs(proto.getPowerInfo());
        } else {
            if (this.powerInfo != null) {
                this.powerInfo.mergeFromCs(proto.getPowerInfo());
            }
        }
        this.markAll();
        return PlayerPowerInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerPowerInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTotalPower()) {
            this.setTotalPower(proto.getTotalPower());
            fieldCnt++;
        }
        if (proto.hasPowerInfo()) {
            this.getPowerInfo().mergeChangeFromCs(proto.getPowerInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerPowerInfo.Builder getCopyDbBuilder() {
        final PlayerPowerInfo.Builder builder = PlayerPowerInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerPowerInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTotalPower() != 0L) {
            builder.setTotalPower(this.getTotalPower());
            fieldCnt++;
        }  else if (builder.hasTotalPower()) {
            // 清理TotalPower
            builder.clearTotalPower();
            fieldCnt++;
        }
        if (this.powerInfo != null) {
            Struct.Int32PowerInfoUnitMap.Builder tmpBuilder = Struct.Int32PowerInfoUnitMap.newBuilder();
            final int tmpFieldCnt = this.powerInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPowerInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPowerInfo();
            }
        }  else if (builder.hasPowerInfo()) {
            // 清理PowerInfo
            builder.clearPowerInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerPowerInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOTALPOWER)) {
            builder.setTotalPower(this.getTotalPower());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POWERINFO) && this.powerInfo != null) {
            final boolean needClear = !builder.hasPowerInfo();
            final int tmpFieldCnt = this.powerInfo.copyChangeToDb(builder.getPowerInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPowerInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerPowerInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTotalPower()) {
            this.innerSetTotalPower(proto.getTotalPower());
        } else {
            this.innerSetTotalPower(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPowerInfo()) {
            this.getPowerInfo().mergeFromDb(proto.getPowerInfo());
        } else {
            if (this.powerInfo != null) {
                this.powerInfo.mergeFromDb(proto.getPowerInfo());
            }
        }
        this.markAll();
        return PlayerPowerInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerPowerInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTotalPower()) {
            this.setTotalPower(proto.getTotalPower());
            fieldCnt++;
        }
        if (proto.hasPowerInfo()) {
            this.getPowerInfo().mergeChangeFromDb(proto.getPowerInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerPowerInfo.Builder getCopySsBuilder() {
        final PlayerPowerInfo.Builder builder = PlayerPowerInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerPowerInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTotalPower() != 0L) {
            builder.setTotalPower(this.getTotalPower());
            fieldCnt++;
        }  else if (builder.hasTotalPower()) {
            // 清理TotalPower
            builder.clearTotalPower();
            fieldCnt++;
        }
        if (this.powerInfo != null) {
            Struct.Int32PowerInfoUnitMap.Builder tmpBuilder = Struct.Int32PowerInfoUnitMap.newBuilder();
            final int tmpFieldCnt = this.powerInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPowerInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPowerInfo();
            }
        }  else if (builder.hasPowerInfo()) {
            // 清理PowerInfo
            builder.clearPowerInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerPowerInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOTALPOWER)) {
            builder.setTotalPower(this.getTotalPower());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POWERINFO) && this.powerInfo != null) {
            final boolean needClear = !builder.hasPowerInfo();
            final int tmpFieldCnt = this.powerInfo.copyChangeToSs(builder.getPowerInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPowerInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerPowerInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTotalPower()) {
            this.innerSetTotalPower(proto.getTotalPower());
        } else {
            this.innerSetTotalPower(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPowerInfo()) {
            this.getPowerInfo().mergeFromSs(proto.getPowerInfo());
        } else {
            if (this.powerInfo != null) {
                this.powerInfo.mergeFromSs(proto.getPowerInfo());
            }
        }
        this.markAll();
        return PlayerPowerInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerPowerInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTotalPower()) {
            this.setTotalPower(proto.getTotalPower());
            fieldCnt++;
        }
        if (proto.hasPowerInfo()) {
            this.getPowerInfo().mergeChangeFromSs(proto.getPowerInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerPowerInfo.Builder builder = PlayerPowerInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_POWERINFO) && this.powerInfo != null) {
            this.powerInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.powerInfo != null) {
            this.powerInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerPowerInfoProp)) {
            return false;
        }
        final PlayerPowerInfoProp otherNode = (PlayerPowerInfoProp) node;
        if (this.totalPower != otherNode.totalPower) {
            return false;
        }
        if (!this.getPowerInfo().compareDataTo(otherNode.getPowerInfo())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.LoginDaysExtraInfo;
import com.yorha.proto.StructPB.LoginDaysExtraInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class LoginDaysExtraInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_LASTUPDATETSMS = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private long lastUpdateTsMs = Constant.DEFAULT_LONG_VALUE;

    public LoginDaysExtraInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public LoginDaysExtraInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get lastUpdateTsMs
     *
     * @return lastUpdateTsMs value
     */
    public long getLastUpdateTsMs() {
        return this.lastUpdateTsMs;
    }

    /**
     * set lastUpdateTsMs && set marked
     *
     * @param lastUpdateTsMs new value
     * @return current object
     */
    public LoginDaysExtraInfoProp setLastUpdateTsMs(long lastUpdateTsMs) {
        if (this.lastUpdateTsMs != lastUpdateTsMs) {
            this.mark(FIELD_INDEX_LASTUPDATETSMS);
            this.lastUpdateTsMs = lastUpdateTsMs;
        }
        return this;
    }

    /**
     * inner set lastUpdateTsMs
     *
     * @param lastUpdateTsMs new value
     */
    private void innerSetLastUpdateTsMs(long lastUpdateTsMs) {
        this.lastUpdateTsMs = lastUpdateTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public LoginDaysExtraInfoPB.Builder getCopyCsBuilder() {
        final LoginDaysExtraInfoPB.Builder builder = LoginDaysExtraInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(LoginDaysExtraInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLastUpdateTsMs() != 0L) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateTsMs()) {
            // 清理LastUpdateTsMs
            builder.clearLastUpdateTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(LoginDaysExtraInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LASTUPDATETSMS)) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(LoginDaysExtraInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LASTUPDATETSMS)) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(LoginDaysExtraInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLastUpdateTsMs()) {
            this.innerSetLastUpdateTsMs(proto.getLastUpdateTsMs());
        } else {
            this.innerSetLastUpdateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return LoginDaysExtraInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(LoginDaysExtraInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLastUpdateTsMs()) {
            this.setLastUpdateTsMs(proto.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public LoginDaysExtraInfo.Builder getCopyDbBuilder() {
        final LoginDaysExtraInfo.Builder builder = LoginDaysExtraInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(LoginDaysExtraInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLastUpdateTsMs() != 0L) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateTsMs()) {
            // 清理LastUpdateTsMs
            builder.clearLastUpdateTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(LoginDaysExtraInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LASTUPDATETSMS)) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(LoginDaysExtraInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLastUpdateTsMs()) {
            this.innerSetLastUpdateTsMs(proto.getLastUpdateTsMs());
        } else {
            this.innerSetLastUpdateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return LoginDaysExtraInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(LoginDaysExtraInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLastUpdateTsMs()) {
            this.setLastUpdateTsMs(proto.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public LoginDaysExtraInfo.Builder getCopySsBuilder() {
        final LoginDaysExtraInfo.Builder builder = LoginDaysExtraInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(LoginDaysExtraInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLastUpdateTsMs() != 0L) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateTsMs()) {
            // 清理LastUpdateTsMs
            builder.clearLastUpdateTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(LoginDaysExtraInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LASTUPDATETSMS)) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(LoginDaysExtraInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLastUpdateTsMs()) {
            this.innerSetLastUpdateTsMs(proto.getLastUpdateTsMs());
        } else {
            this.innerSetLastUpdateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return LoginDaysExtraInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(LoginDaysExtraInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLastUpdateTsMs()) {
            this.setLastUpdateTsMs(proto.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        LoginDaysExtraInfo.Builder builder = LoginDaysExtraInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof LoginDaysExtraInfoProp)) {
            return false;
        }
        final LoginDaysExtraInfoProp otherNode = (LoginDaysExtraInfoProp) node;
        if (this.lastUpdateTsMs != otherNode.lastUpdateTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.GoodsOrderExtInfo;
import com.yorha.proto.Player;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.GoodsOrderExtInfoPB;
import com.yorha.proto.PlayerPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class GoodsOrderExtInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ACTNORMAL = 0;
    public static final int FIELD_INDEX_TRIGGERBUNDLEORDERPARAM = 1;
    public static final int FIELD_INDEX_RESELLTRIGGERBUNDLEORDERPARAM = 2;
    public static final int FIELD_INDEX_SELECTGOODSPARAM = 3;
    public static final int FIELD_INDEX_BPPARAM = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private ActNormalGoodsExtInfoProp actNormal = null;
    private TriggerBundleOrderParamProp triggerBundleOrderParam = null;
    private ResellTriggerBundleOrderParamProp resellTriggerBundleOrderParam = null;
    private ActivitySelectGoodsParamProp selectGoodsParam = null;
    private FestivalBpParamProp bpParam = null;

    public GoodsOrderExtInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public GoodsOrderExtInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get actNormal
     *
     * @return actNormal value
     */
    public ActNormalGoodsExtInfoProp getActNormal() {
        if (this.actNormal == null) {
            this.actNormal = new ActNormalGoodsExtInfoProp(this, FIELD_INDEX_ACTNORMAL);
        }
        return this.actNormal;
    }

    /**
     * get triggerBundleOrderParam
     *
     * @return triggerBundleOrderParam value
     */
    public TriggerBundleOrderParamProp getTriggerBundleOrderParam() {
        if (this.triggerBundleOrderParam == null) {
            this.triggerBundleOrderParam = new TriggerBundleOrderParamProp(this, FIELD_INDEX_TRIGGERBUNDLEORDERPARAM);
        }
        return this.triggerBundleOrderParam;
    }

    /**
     * get resellTriggerBundleOrderParam
     *
     * @return resellTriggerBundleOrderParam value
     */
    public ResellTriggerBundleOrderParamProp getResellTriggerBundleOrderParam() {
        if (this.resellTriggerBundleOrderParam == null) {
            this.resellTriggerBundleOrderParam = new ResellTriggerBundleOrderParamProp(this, FIELD_INDEX_RESELLTRIGGERBUNDLEORDERPARAM);
        }
        return this.resellTriggerBundleOrderParam;
    }

    /**
     * get selectGoodsParam
     *
     * @return selectGoodsParam value
     */
    public ActivitySelectGoodsParamProp getSelectGoodsParam() {
        if (this.selectGoodsParam == null) {
            this.selectGoodsParam = new ActivitySelectGoodsParamProp(this, FIELD_INDEX_SELECTGOODSPARAM);
        }
        return this.selectGoodsParam;
    }

    /**
     * get bpParam
     *
     * @return bpParam value
     */
    public FestivalBpParamProp getBpParam() {
        if (this.bpParam == null) {
            this.bpParam = new FestivalBpParamProp(this, FIELD_INDEX_BPPARAM);
        }
        return this.bpParam;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public GoodsOrderExtInfoPB.Builder getCopyCsBuilder() {
        final GoodsOrderExtInfoPB.Builder builder = GoodsOrderExtInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(GoodsOrderExtInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.actNormal != null) {
            PlayerPB.ActNormalGoodsExtInfoPB.Builder tmpBuilder = PlayerPB.ActNormalGoodsExtInfoPB.newBuilder();
            final int tmpFieldCnt = this.actNormal.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActNormal(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActNormal();
            }
        }  else if (builder.hasActNormal()) {
            // 清理ActNormal
            builder.clearActNormal();
            fieldCnt++;
        }
        if (this.triggerBundleOrderParam != null) {
            StructPB.TriggerBundleOrderParamPB.Builder tmpBuilder = StructPB.TriggerBundleOrderParamPB.newBuilder();
            final int tmpFieldCnt = this.triggerBundleOrderParam.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTriggerBundleOrderParam(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTriggerBundleOrderParam();
            }
        }  else if (builder.hasTriggerBundleOrderParam()) {
            // 清理TriggerBundleOrderParam
            builder.clearTriggerBundleOrderParam();
            fieldCnt++;
        }
        if (this.resellTriggerBundleOrderParam != null) {
            StructPB.ResellTriggerBundleOrderParamPB.Builder tmpBuilder = StructPB.ResellTriggerBundleOrderParamPB.newBuilder();
            final int tmpFieldCnt = this.resellTriggerBundleOrderParam.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResellTriggerBundleOrderParam(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResellTriggerBundleOrderParam();
            }
        }  else if (builder.hasResellTriggerBundleOrderParam()) {
            // 清理ResellTriggerBundleOrderParam
            builder.clearResellTriggerBundleOrderParam();
            fieldCnt++;
        }
        if (this.selectGoodsParam != null) {
            StructPB.ActivitySelectGoodsParamPB.Builder tmpBuilder = StructPB.ActivitySelectGoodsParamPB.newBuilder();
            final int tmpFieldCnt = this.selectGoodsParam.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSelectGoodsParam(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSelectGoodsParam();
            }
        }  else if (builder.hasSelectGoodsParam()) {
            // 清理SelectGoodsParam
            builder.clearSelectGoodsParam();
            fieldCnt++;
        }
        if (this.bpParam != null) {
            StructPB.FestivalBpParamPB.Builder tmpBuilder = StructPB.FestivalBpParamPB.newBuilder();
            final int tmpFieldCnt = this.bpParam.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBpParam(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBpParam();
            }
        }  else if (builder.hasBpParam()) {
            // 清理BpParam
            builder.clearBpParam();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(GoodsOrderExtInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTNORMAL) && this.actNormal != null) {
            final boolean needClear = !builder.hasActNormal();
            final int tmpFieldCnt = this.actNormal.copyChangeToCs(builder.getActNormalBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActNormal();
            }
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERBUNDLEORDERPARAM) && this.triggerBundleOrderParam != null) {
            final boolean needClear = !builder.hasTriggerBundleOrderParam();
            final int tmpFieldCnt = this.triggerBundleOrderParam.copyChangeToCs(builder.getTriggerBundleOrderParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTriggerBundleOrderParam();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESELLTRIGGERBUNDLEORDERPARAM) && this.resellTriggerBundleOrderParam != null) {
            final boolean needClear = !builder.hasResellTriggerBundleOrderParam();
            final int tmpFieldCnt = this.resellTriggerBundleOrderParam.copyChangeToCs(builder.getResellTriggerBundleOrderParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResellTriggerBundleOrderParam();
            }
        }
        if (this.hasMark(FIELD_INDEX_SELECTGOODSPARAM) && this.selectGoodsParam != null) {
            final boolean needClear = !builder.hasSelectGoodsParam();
            final int tmpFieldCnt = this.selectGoodsParam.copyChangeToCs(builder.getSelectGoodsParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSelectGoodsParam();
            }
        }
        if (this.hasMark(FIELD_INDEX_BPPARAM) && this.bpParam != null) {
            final boolean needClear = !builder.hasBpParam();
            final int tmpFieldCnt = this.bpParam.copyChangeToCs(builder.getBpParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBpParam();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(GoodsOrderExtInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTNORMAL) && this.actNormal != null) {
            final boolean needClear = !builder.hasActNormal();
            final int tmpFieldCnt = this.actNormal.copyChangeToAndClearDeleteKeysCs(builder.getActNormalBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActNormal();
            }
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERBUNDLEORDERPARAM) && this.triggerBundleOrderParam != null) {
            final boolean needClear = !builder.hasTriggerBundleOrderParam();
            final int tmpFieldCnt = this.triggerBundleOrderParam.copyChangeToAndClearDeleteKeysCs(builder.getTriggerBundleOrderParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTriggerBundleOrderParam();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESELLTRIGGERBUNDLEORDERPARAM) && this.resellTriggerBundleOrderParam != null) {
            final boolean needClear = !builder.hasResellTriggerBundleOrderParam();
            final int tmpFieldCnt = this.resellTriggerBundleOrderParam.copyChangeToAndClearDeleteKeysCs(builder.getResellTriggerBundleOrderParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResellTriggerBundleOrderParam();
            }
        }
        if (this.hasMark(FIELD_INDEX_SELECTGOODSPARAM) && this.selectGoodsParam != null) {
            final boolean needClear = !builder.hasSelectGoodsParam();
            final int tmpFieldCnt = this.selectGoodsParam.copyChangeToAndClearDeleteKeysCs(builder.getSelectGoodsParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSelectGoodsParam();
            }
        }
        if (this.hasMark(FIELD_INDEX_BPPARAM) && this.bpParam != null) {
            final boolean needClear = !builder.hasBpParam();
            final int tmpFieldCnt = this.bpParam.copyChangeToAndClearDeleteKeysCs(builder.getBpParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBpParam();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(GoodsOrderExtInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActNormal()) {
            this.getActNormal().mergeFromCs(proto.getActNormal());
        } else {
            if (this.actNormal != null) {
                this.actNormal.mergeFromCs(proto.getActNormal());
            }
        }
        if (proto.hasTriggerBundleOrderParam()) {
            this.getTriggerBundleOrderParam().mergeFromCs(proto.getTriggerBundleOrderParam());
        } else {
            if (this.triggerBundleOrderParam != null) {
                this.triggerBundleOrderParam.mergeFromCs(proto.getTriggerBundleOrderParam());
            }
        }
        if (proto.hasResellTriggerBundleOrderParam()) {
            this.getResellTriggerBundleOrderParam().mergeFromCs(proto.getResellTriggerBundleOrderParam());
        } else {
            if (this.resellTriggerBundleOrderParam != null) {
                this.resellTriggerBundleOrderParam.mergeFromCs(proto.getResellTriggerBundleOrderParam());
            }
        }
        if (proto.hasSelectGoodsParam()) {
            this.getSelectGoodsParam().mergeFromCs(proto.getSelectGoodsParam());
        } else {
            if (this.selectGoodsParam != null) {
                this.selectGoodsParam.mergeFromCs(proto.getSelectGoodsParam());
            }
        }
        if (proto.hasBpParam()) {
            this.getBpParam().mergeFromCs(proto.getBpParam());
        } else {
            if (this.bpParam != null) {
                this.bpParam.mergeFromCs(proto.getBpParam());
            }
        }
        this.markAll();
        return GoodsOrderExtInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(GoodsOrderExtInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActNormal()) {
            this.getActNormal().mergeChangeFromCs(proto.getActNormal());
            fieldCnt++;
        }
        if (proto.hasTriggerBundleOrderParam()) {
            this.getTriggerBundleOrderParam().mergeChangeFromCs(proto.getTriggerBundleOrderParam());
            fieldCnt++;
        }
        if (proto.hasResellTriggerBundleOrderParam()) {
            this.getResellTriggerBundleOrderParam().mergeChangeFromCs(proto.getResellTriggerBundleOrderParam());
            fieldCnt++;
        }
        if (proto.hasSelectGoodsParam()) {
            this.getSelectGoodsParam().mergeChangeFromCs(proto.getSelectGoodsParam());
            fieldCnt++;
        }
        if (proto.hasBpParam()) {
            this.getBpParam().mergeChangeFromCs(proto.getBpParam());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public GoodsOrderExtInfo.Builder getCopyDbBuilder() {
        final GoodsOrderExtInfo.Builder builder = GoodsOrderExtInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(GoodsOrderExtInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.actNormal != null) {
            Player.ActNormalGoodsExtInfo.Builder tmpBuilder = Player.ActNormalGoodsExtInfo.newBuilder();
            final int tmpFieldCnt = this.actNormal.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActNormal(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActNormal();
            }
        }  else if (builder.hasActNormal()) {
            // 清理ActNormal
            builder.clearActNormal();
            fieldCnt++;
        }
        if (this.triggerBundleOrderParam != null) {
            Struct.TriggerBundleOrderParam.Builder tmpBuilder = Struct.TriggerBundleOrderParam.newBuilder();
            final int tmpFieldCnt = this.triggerBundleOrderParam.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTriggerBundleOrderParam(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTriggerBundleOrderParam();
            }
        }  else if (builder.hasTriggerBundleOrderParam()) {
            // 清理TriggerBundleOrderParam
            builder.clearTriggerBundleOrderParam();
            fieldCnt++;
        }
        if (this.resellTriggerBundleOrderParam != null) {
            Struct.ResellTriggerBundleOrderParam.Builder tmpBuilder = Struct.ResellTriggerBundleOrderParam.newBuilder();
            final int tmpFieldCnt = this.resellTriggerBundleOrderParam.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResellTriggerBundleOrderParam(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResellTriggerBundleOrderParam();
            }
        }  else if (builder.hasResellTriggerBundleOrderParam()) {
            // 清理ResellTriggerBundleOrderParam
            builder.clearResellTriggerBundleOrderParam();
            fieldCnt++;
        }
        if (this.selectGoodsParam != null) {
            Struct.ActivitySelectGoodsParam.Builder tmpBuilder = Struct.ActivitySelectGoodsParam.newBuilder();
            final int tmpFieldCnt = this.selectGoodsParam.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSelectGoodsParam(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSelectGoodsParam();
            }
        }  else if (builder.hasSelectGoodsParam()) {
            // 清理SelectGoodsParam
            builder.clearSelectGoodsParam();
            fieldCnt++;
        }
        if (this.bpParam != null) {
            Struct.FestivalBpParam.Builder tmpBuilder = Struct.FestivalBpParam.newBuilder();
            final int tmpFieldCnt = this.bpParam.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBpParam(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBpParam();
            }
        }  else if (builder.hasBpParam()) {
            // 清理BpParam
            builder.clearBpParam();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(GoodsOrderExtInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTNORMAL) && this.actNormal != null) {
            final boolean needClear = !builder.hasActNormal();
            final int tmpFieldCnt = this.actNormal.copyChangeToDb(builder.getActNormalBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActNormal();
            }
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERBUNDLEORDERPARAM) && this.triggerBundleOrderParam != null) {
            final boolean needClear = !builder.hasTriggerBundleOrderParam();
            final int tmpFieldCnt = this.triggerBundleOrderParam.copyChangeToDb(builder.getTriggerBundleOrderParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTriggerBundleOrderParam();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESELLTRIGGERBUNDLEORDERPARAM) && this.resellTriggerBundleOrderParam != null) {
            final boolean needClear = !builder.hasResellTriggerBundleOrderParam();
            final int tmpFieldCnt = this.resellTriggerBundleOrderParam.copyChangeToDb(builder.getResellTriggerBundleOrderParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResellTriggerBundleOrderParam();
            }
        }
        if (this.hasMark(FIELD_INDEX_SELECTGOODSPARAM) && this.selectGoodsParam != null) {
            final boolean needClear = !builder.hasSelectGoodsParam();
            final int tmpFieldCnt = this.selectGoodsParam.copyChangeToDb(builder.getSelectGoodsParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSelectGoodsParam();
            }
        }
        if (this.hasMark(FIELD_INDEX_BPPARAM) && this.bpParam != null) {
            final boolean needClear = !builder.hasBpParam();
            final int tmpFieldCnt = this.bpParam.copyChangeToDb(builder.getBpParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBpParam();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(GoodsOrderExtInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActNormal()) {
            this.getActNormal().mergeFromDb(proto.getActNormal());
        } else {
            if (this.actNormal != null) {
                this.actNormal.mergeFromDb(proto.getActNormal());
            }
        }
        if (proto.hasTriggerBundleOrderParam()) {
            this.getTriggerBundleOrderParam().mergeFromDb(proto.getTriggerBundleOrderParam());
        } else {
            if (this.triggerBundleOrderParam != null) {
                this.triggerBundleOrderParam.mergeFromDb(proto.getTriggerBundleOrderParam());
            }
        }
        if (proto.hasResellTriggerBundleOrderParam()) {
            this.getResellTriggerBundleOrderParam().mergeFromDb(proto.getResellTriggerBundleOrderParam());
        } else {
            if (this.resellTriggerBundleOrderParam != null) {
                this.resellTriggerBundleOrderParam.mergeFromDb(proto.getResellTriggerBundleOrderParam());
            }
        }
        if (proto.hasSelectGoodsParam()) {
            this.getSelectGoodsParam().mergeFromDb(proto.getSelectGoodsParam());
        } else {
            if (this.selectGoodsParam != null) {
                this.selectGoodsParam.mergeFromDb(proto.getSelectGoodsParam());
            }
        }
        if (proto.hasBpParam()) {
            this.getBpParam().mergeFromDb(proto.getBpParam());
        } else {
            if (this.bpParam != null) {
                this.bpParam.mergeFromDb(proto.getBpParam());
            }
        }
        this.markAll();
        return GoodsOrderExtInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(GoodsOrderExtInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActNormal()) {
            this.getActNormal().mergeChangeFromDb(proto.getActNormal());
            fieldCnt++;
        }
        if (proto.hasTriggerBundleOrderParam()) {
            this.getTriggerBundleOrderParam().mergeChangeFromDb(proto.getTriggerBundleOrderParam());
            fieldCnt++;
        }
        if (proto.hasResellTriggerBundleOrderParam()) {
            this.getResellTriggerBundleOrderParam().mergeChangeFromDb(proto.getResellTriggerBundleOrderParam());
            fieldCnt++;
        }
        if (proto.hasSelectGoodsParam()) {
            this.getSelectGoodsParam().mergeChangeFromDb(proto.getSelectGoodsParam());
            fieldCnt++;
        }
        if (proto.hasBpParam()) {
            this.getBpParam().mergeChangeFromDb(proto.getBpParam());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public GoodsOrderExtInfo.Builder getCopySsBuilder() {
        final GoodsOrderExtInfo.Builder builder = GoodsOrderExtInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(GoodsOrderExtInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.actNormal != null) {
            Player.ActNormalGoodsExtInfo.Builder tmpBuilder = Player.ActNormalGoodsExtInfo.newBuilder();
            final int tmpFieldCnt = this.actNormal.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActNormal(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActNormal();
            }
        }  else if (builder.hasActNormal()) {
            // 清理ActNormal
            builder.clearActNormal();
            fieldCnt++;
        }
        if (this.triggerBundleOrderParam != null) {
            Struct.TriggerBundleOrderParam.Builder tmpBuilder = Struct.TriggerBundleOrderParam.newBuilder();
            final int tmpFieldCnt = this.triggerBundleOrderParam.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTriggerBundleOrderParam(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTriggerBundleOrderParam();
            }
        }  else if (builder.hasTriggerBundleOrderParam()) {
            // 清理TriggerBundleOrderParam
            builder.clearTriggerBundleOrderParam();
            fieldCnt++;
        }
        if (this.resellTriggerBundleOrderParam != null) {
            Struct.ResellTriggerBundleOrderParam.Builder tmpBuilder = Struct.ResellTriggerBundleOrderParam.newBuilder();
            final int tmpFieldCnt = this.resellTriggerBundleOrderParam.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResellTriggerBundleOrderParam(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResellTriggerBundleOrderParam();
            }
        }  else if (builder.hasResellTriggerBundleOrderParam()) {
            // 清理ResellTriggerBundleOrderParam
            builder.clearResellTriggerBundleOrderParam();
            fieldCnt++;
        }
        if (this.selectGoodsParam != null) {
            Struct.ActivitySelectGoodsParam.Builder tmpBuilder = Struct.ActivitySelectGoodsParam.newBuilder();
            final int tmpFieldCnt = this.selectGoodsParam.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSelectGoodsParam(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSelectGoodsParam();
            }
        }  else if (builder.hasSelectGoodsParam()) {
            // 清理SelectGoodsParam
            builder.clearSelectGoodsParam();
            fieldCnt++;
        }
        if (this.bpParam != null) {
            Struct.FestivalBpParam.Builder tmpBuilder = Struct.FestivalBpParam.newBuilder();
            final int tmpFieldCnt = this.bpParam.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBpParam(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBpParam();
            }
        }  else if (builder.hasBpParam()) {
            // 清理BpParam
            builder.clearBpParam();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(GoodsOrderExtInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTNORMAL) && this.actNormal != null) {
            final boolean needClear = !builder.hasActNormal();
            final int tmpFieldCnt = this.actNormal.copyChangeToSs(builder.getActNormalBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActNormal();
            }
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERBUNDLEORDERPARAM) && this.triggerBundleOrderParam != null) {
            final boolean needClear = !builder.hasTriggerBundleOrderParam();
            final int tmpFieldCnt = this.triggerBundleOrderParam.copyChangeToSs(builder.getTriggerBundleOrderParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTriggerBundleOrderParam();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESELLTRIGGERBUNDLEORDERPARAM) && this.resellTriggerBundleOrderParam != null) {
            final boolean needClear = !builder.hasResellTriggerBundleOrderParam();
            final int tmpFieldCnt = this.resellTriggerBundleOrderParam.copyChangeToSs(builder.getResellTriggerBundleOrderParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResellTriggerBundleOrderParam();
            }
        }
        if (this.hasMark(FIELD_INDEX_SELECTGOODSPARAM) && this.selectGoodsParam != null) {
            final boolean needClear = !builder.hasSelectGoodsParam();
            final int tmpFieldCnt = this.selectGoodsParam.copyChangeToSs(builder.getSelectGoodsParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSelectGoodsParam();
            }
        }
        if (this.hasMark(FIELD_INDEX_BPPARAM) && this.bpParam != null) {
            final boolean needClear = !builder.hasBpParam();
            final int tmpFieldCnt = this.bpParam.copyChangeToSs(builder.getBpParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBpParam();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(GoodsOrderExtInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActNormal()) {
            this.getActNormal().mergeFromSs(proto.getActNormal());
        } else {
            if (this.actNormal != null) {
                this.actNormal.mergeFromSs(proto.getActNormal());
            }
        }
        if (proto.hasTriggerBundleOrderParam()) {
            this.getTriggerBundleOrderParam().mergeFromSs(proto.getTriggerBundleOrderParam());
        } else {
            if (this.triggerBundleOrderParam != null) {
                this.triggerBundleOrderParam.mergeFromSs(proto.getTriggerBundleOrderParam());
            }
        }
        if (proto.hasResellTriggerBundleOrderParam()) {
            this.getResellTriggerBundleOrderParam().mergeFromSs(proto.getResellTriggerBundleOrderParam());
        } else {
            if (this.resellTriggerBundleOrderParam != null) {
                this.resellTriggerBundleOrderParam.mergeFromSs(proto.getResellTriggerBundleOrderParam());
            }
        }
        if (proto.hasSelectGoodsParam()) {
            this.getSelectGoodsParam().mergeFromSs(proto.getSelectGoodsParam());
        } else {
            if (this.selectGoodsParam != null) {
                this.selectGoodsParam.mergeFromSs(proto.getSelectGoodsParam());
            }
        }
        if (proto.hasBpParam()) {
            this.getBpParam().mergeFromSs(proto.getBpParam());
        } else {
            if (this.bpParam != null) {
                this.bpParam.mergeFromSs(proto.getBpParam());
            }
        }
        this.markAll();
        return GoodsOrderExtInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(GoodsOrderExtInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActNormal()) {
            this.getActNormal().mergeChangeFromSs(proto.getActNormal());
            fieldCnt++;
        }
        if (proto.hasTriggerBundleOrderParam()) {
            this.getTriggerBundleOrderParam().mergeChangeFromSs(proto.getTriggerBundleOrderParam());
            fieldCnt++;
        }
        if (proto.hasResellTriggerBundleOrderParam()) {
            this.getResellTriggerBundleOrderParam().mergeChangeFromSs(proto.getResellTriggerBundleOrderParam());
            fieldCnt++;
        }
        if (proto.hasSelectGoodsParam()) {
            this.getSelectGoodsParam().mergeChangeFromSs(proto.getSelectGoodsParam());
            fieldCnt++;
        }
        if (proto.hasBpParam()) {
            this.getBpParam().mergeChangeFromSs(proto.getBpParam());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        GoodsOrderExtInfo.Builder builder = GoodsOrderExtInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ACTNORMAL) && this.actNormal != null) {
            this.actNormal.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERBUNDLEORDERPARAM) && this.triggerBundleOrderParam != null) {
            this.triggerBundleOrderParam.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RESELLTRIGGERBUNDLEORDERPARAM) && this.resellTriggerBundleOrderParam != null) {
            this.resellTriggerBundleOrderParam.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SELECTGOODSPARAM) && this.selectGoodsParam != null) {
            this.selectGoodsParam.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BPPARAM) && this.bpParam != null) {
            this.bpParam.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.actNormal != null) {
            this.actNormal.markAll();
        }
        if (this.triggerBundleOrderParam != null) {
            this.triggerBundleOrderParam.markAll();
        }
        if (this.resellTriggerBundleOrderParam != null) {
            this.resellTriggerBundleOrderParam.markAll();
        }
        if (this.selectGoodsParam != null) {
            this.selectGoodsParam.markAll();
        }
        if (this.bpParam != null) {
            this.bpParam.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof GoodsOrderExtInfoProp)) {
            return false;
        }
        final GoodsOrderExtInfoProp otherNode = (GoodsOrderExtInfoProp) node;
        if (!this.getActNormal().compareDataTo(otherNode.getActNormal())) {
            return false;
        }
        if (!this.getTriggerBundleOrderParam().compareDataTo(otherNode.getTriggerBundleOrderParam())) {
            return false;
        }
        if (!this.getResellTriggerBundleOrderParam().compareDataTo(otherNode.getResellTriggerBundleOrderParam())) {
            return false;
        }
        if (!this.getSelectGoodsParam().compareDataTo(otherNode.getSelectGoodsParam())) {
            return false;
        }
        if (!this.getBpParam().compareDataTo(otherNode.getBpParam())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
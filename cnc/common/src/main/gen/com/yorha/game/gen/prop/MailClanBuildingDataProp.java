package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructMail.MailClanBuildingData;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMailPB.MailClanBuildingDataPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class MailClanBuildingDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TEMPLATEID = 0;
    public static final int FIELD_INDEX_P = 1;
    public static final int FIELD_INDEX_PLAYERID = 2;
    public static final int FIELD_INDEX_CARDHEAD = 3;
    public static final int FIELD_INDEX_COSTRESOURCE = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private int templateId = Constant.DEFAULT_INT_VALUE;
    private PointProp p = null;
    private long playerId = Constant.DEFAULT_LONG_VALUE;
    private PlayerCardHeadProp cardHead = null;
    private Int32CurrencyMapProp costResource = null;

    public MailClanBuildingDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MailClanBuildingDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public MailClanBuildingDataProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }

    /**
     * get p
     *
     * @return p value
     */
    public PointProp getP() {
        if (this.p == null) {
            this.p = new PointProp(this, FIELD_INDEX_P);
        }
        return this.p;
    }

    /**
     * get playerId
     *
     * @return playerId value
     */
    public long getPlayerId() {
        return this.playerId;
    }

    /**
     * set playerId && set marked
     *
     * @param playerId new value
     * @return current object
     */
    public MailClanBuildingDataProp setPlayerId(long playerId) {
        if (this.playerId != playerId) {
            this.mark(FIELD_INDEX_PLAYERID);
            this.playerId = playerId;
        }
        return this;
    }

    /**
     * inner set playerId
     *
     * @param playerId new value
     */
    private void innerSetPlayerId(long playerId) {
        this.playerId = playerId;
    }

    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }

    /**
     * get costResource
     *
     * @return costResource value
     */
    public Int32CurrencyMapProp getCostResource() {
        if (this.costResource == null) {
            this.costResource = new Int32CurrencyMapProp(this, FIELD_INDEX_COSTRESOURCE);
        }
        return this.costResource;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putCostResourceV(CurrencyProp v) {
        this.getCostResource().put(v.getType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public CurrencyProp addEmptyCostResource(Integer k) {
        return this.getCostResource().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getCostResourceSize() {
        if (this.costResource == null) {
            return 0;
        }
        return this.costResource.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isCostResourceEmpty() {
        if (this.costResource == null) {
            return true;
        }
        return this.costResource.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public CurrencyProp getCostResourceV(Integer k) {
        if (this.costResource == null || !this.costResource.containsKey(k)) {
            return null;
        }
        return this.costResource.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearCostResource() {
        if (this.costResource != null) {
            this.costResource.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeCostResourceV(Integer k) {
        if (this.costResource != null) {
            this.costResource.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailClanBuildingDataPB.Builder getCopyCsBuilder() {
        final MailClanBuildingDataPB.Builder builder = MailClanBuildingDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MailClanBuildingDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.p != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.p.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setP(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearP();
            }
        }  else if (builder.hasP()) {
            // 清理P
            builder.clearP();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.costResource != null) {
            StructPB.Int32CurrencyMapPB.Builder tmpBuilder = StructPB.Int32CurrencyMapPB.newBuilder();
            final int tmpFieldCnt = this.costResource.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCostResource(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCostResource();
            }
        }  else if (builder.hasCostResource()) {
            // 清理CostResource
            builder.clearCostResource();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MailClanBuildingDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_P) && this.p != null) {
            final boolean needClear = !builder.hasP();
            final int tmpFieldCnt = this.p.copyChangeToCs(builder.getPBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearP();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_COSTRESOURCE) && this.costResource != null) {
            final boolean needClear = !builder.hasCostResource();
            final int tmpFieldCnt = this.costResource.copyChangeToCs(builder.getCostResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCostResource();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MailClanBuildingDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_P) && this.p != null) {
            final boolean needClear = !builder.hasP();
            final int tmpFieldCnt = this.p.copyChangeToAndClearDeleteKeysCs(builder.getPBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearP();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToAndClearDeleteKeysCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_COSTRESOURCE) && this.costResource != null) {
            final boolean needClear = !builder.hasCostResource();
            final int tmpFieldCnt = this.costResource.copyChangeToAndClearDeleteKeysCs(builder.getCostResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCostResource();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MailClanBuildingDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasP()) {
            this.getP().mergeFromCs(proto.getP());
        } else {
            if (this.p != null) {
                this.p.mergeFromCs(proto.getP());
            }
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromCs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromCs(proto.getCardHead());
            }
        }
        if (proto.hasCostResource()) {
            this.getCostResource().mergeFromCs(proto.getCostResource());
        } else {
            if (this.costResource != null) {
                this.costResource.mergeFromCs(proto.getCostResource());
            }
        }
        this.markAll();
        return MailClanBuildingDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MailClanBuildingDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasP()) {
            this.getP().mergeChangeFromCs(proto.getP());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromCs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasCostResource()) {
            this.getCostResource().mergeChangeFromCs(proto.getCostResource());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailClanBuildingData.Builder getCopyDbBuilder() {
        final MailClanBuildingData.Builder builder = MailClanBuildingData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MailClanBuildingData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.p != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.p.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setP(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearP();
            }
        }  else if (builder.hasP()) {
            // 清理P
            builder.clearP();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.costResource != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.costResource.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCostResource(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCostResource();
            }
        }  else if (builder.hasCostResource()) {
            // 清理CostResource
            builder.clearCostResource();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MailClanBuildingData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_P) && this.p != null) {
            final boolean needClear = !builder.hasP();
            final int tmpFieldCnt = this.p.copyChangeToDb(builder.getPBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearP();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToDb(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_COSTRESOURCE) && this.costResource != null) {
            final boolean needClear = !builder.hasCostResource();
            final int tmpFieldCnt = this.costResource.copyChangeToDb(builder.getCostResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCostResource();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MailClanBuildingData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasP()) {
            this.getP().mergeFromDb(proto.getP());
        } else {
            if (this.p != null) {
                this.p.mergeFromDb(proto.getP());
            }
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromDb(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromDb(proto.getCardHead());
            }
        }
        if (proto.hasCostResource()) {
            this.getCostResource().mergeFromDb(proto.getCostResource());
        } else {
            if (this.costResource != null) {
                this.costResource.mergeFromDb(proto.getCostResource());
            }
        }
        this.markAll();
        return MailClanBuildingDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MailClanBuildingData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasP()) {
            this.getP().mergeChangeFromDb(proto.getP());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromDb(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasCostResource()) {
            this.getCostResource().mergeChangeFromDb(proto.getCostResource());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailClanBuildingData.Builder getCopySsBuilder() {
        final MailClanBuildingData.Builder builder = MailClanBuildingData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MailClanBuildingData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.p != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.p.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setP(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearP();
            }
        }  else if (builder.hasP()) {
            // 清理P
            builder.clearP();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.costResource != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.costResource.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCostResource(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCostResource();
            }
        }  else if (builder.hasCostResource()) {
            // 清理CostResource
            builder.clearCostResource();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MailClanBuildingData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_P) && this.p != null) {
            final boolean needClear = !builder.hasP();
            final int tmpFieldCnt = this.p.copyChangeToSs(builder.getPBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearP();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_COSTRESOURCE) && this.costResource != null) {
            final boolean needClear = !builder.hasCostResource();
            final int tmpFieldCnt = this.costResource.copyChangeToSs(builder.getCostResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCostResource();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MailClanBuildingData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasP()) {
            this.getP().mergeFromSs(proto.getP());
        } else {
            if (this.p != null) {
                this.p.mergeFromSs(proto.getP());
            }
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        if (proto.hasCostResource()) {
            this.getCostResource().mergeFromSs(proto.getCostResource());
        } else {
            if (this.costResource != null) {
                this.costResource.mergeFromSs(proto.getCostResource());
            }
        }
        this.markAll();
        return MailClanBuildingDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MailClanBuildingData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasP()) {
            this.getP().mergeChangeFromSs(proto.getP());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasCostResource()) {
            this.getCostResource().mergeChangeFromSs(proto.getCostResource());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MailClanBuildingData.Builder builder = MailClanBuildingData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_P) && this.p != null) {
            this.p.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_COSTRESOURCE) && this.costResource != null) {
            this.costResource.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.p != null) {
            this.p.markAll();
        }
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        if (this.costResource != null) {
            this.costResource.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MailClanBuildingDataProp)) {
            return false;
        }
        final MailClanBuildingDataProp otherNode = (MailClanBuildingDataProp) node;
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        if (!this.getP().compareDataTo(otherNode.getP())) {
            return false;
        }
        if (this.playerId != otherNode.playerId) {
            return false;
        }
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        if (!this.getCostResource().compareDataTo(otherNode.getCostResource())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
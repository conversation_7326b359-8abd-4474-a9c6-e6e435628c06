package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructBattle.BattleRecordSoldierReport;
import com.yorha.proto.StructBattlePB.BattleRecordSoldierReportPB;


/**
 * <AUTHOR> auto gen
 */
public class BattleRecordSoldierReportProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TOTAL = 0;
    public static final int FIELD_INDEX_TREATMENT = 1;
    public static final int FIELD_INDEX_DEAD = 2;
    public static final int FIELD_INDEX_SEVEREWOUND = 3;
    public static final int FIELD_INDEX_SLIGHTWOUND = 4;
    public static final int FIELD_INDEX_LEFTALIVE = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private int total = Constant.DEFAULT_INT_VALUE;
    private int treatment = Constant.DEFAULT_INT_VALUE;
    private int dead = Constant.DEFAULT_INT_VALUE;
    private int severeWound = Constant.DEFAULT_INT_VALUE;
    private int slightWound = Constant.DEFAULT_INT_VALUE;
    private int leftAlive = Constant.DEFAULT_INT_VALUE;

    public BattleRecordSoldierReportProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BattleRecordSoldierReportProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get total
     *
     * @return total value
     */
    public int getTotal() {
        return this.total;
    }

    /**
     * set total && set marked
     *
     * @param total new value
     * @return current object
     */
    public BattleRecordSoldierReportProp setTotal(int total) {
        if (this.total != total) {
            this.mark(FIELD_INDEX_TOTAL);
            this.total = total;
        }
        return this;
    }

    /**
     * inner set total
     *
     * @param total new value
     */
    private void innerSetTotal(int total) {
        this.total = total;
    }

    /**
     * get treatment
     *
     * @return treatment value
     */
    public int getTreatment() {
        return this.treatment;
    }

    /**
     * set treatment && set marked
     *
     * @param treatment new value
     * @return current object
     */
    public BattleRecordSoldierReportProp setTreatment(int treatment) {
        if (this.treatment != treatment) {
            this.mark(FIELD_INDEX_TREATMENT);
            this.treatment = treatment;
        }
        return this;
    }

    /**
     * inner set treatment
     *
     * @param treatment new value
     */
    private void innerSetTreatment(int treatment) {
        this.treatment = treatment;
    }

    /**
     * get dead
     *
     * @return dead value
     */
    public int getDead() {
        return this.dead;
    }

    /**
     * set dead && set marked
     *
     * @param dead new value
     * @return current object
     */
    public BattleRecordSoldierReportProp setDead(int dead) {
        if (this.dead != dead) {
            this.mark(FIELD_INDEX_DEAD);
            this.dead = dead;
        }
        return this;
    }

    /**
     * inner set dead
     *
     * @param dead new value
     */
    private void innerSetDead(int dead) {
        this.dead = dead;
    }

    /**
     * get severeWound
     *
     * @return severeWound value
     */
    public int getSevereWound() {
        return this.severeWound;
    }

    /**
     * set severeWound && set marked
     *
     * @param severeWound new value
     * @return current object
     */
    public BattleRecordSoldierReportProp setSevereWound(int severeWound) {
        if (this.severeWound != severeWound) {
            this.mark(FIELD_INDEX_SEVEREWOUND);
            this.severeWound = severeWound;
        }
        return this;
    }

    /**
     * inner set severeWound
     *
     * @param severeWound new value
     */
    private void innerSetSevereWound(int severeWound) {
        this.severeWound = severeWound;
    }

    /**
     * get slightWound
     *
     * @return slightWound value
     */
    public int getSlightWound() {
        return this.slightWound;
    }

    /**
     * set slightWound && set marked
     *
     * @param slightWound new value
     * @return current object
     */
    public BattleRecordSoldierReportProp setSlightWound(int slightWound) {
        if (this.slightWound != slightWound) {
            this.mark(FIELD_INDEX_SLIGHTWOUND);
            this.slightWound = slightWound;
        }
        return this;
    }

    /**
     * inner set slightWound
     *
     * @param slightWound new value
     */
    private void innerSetSlightWound(int slightWound) {
        this.slightWound = slightWound;
    }

    /**
     * get leftAlive
     *
     * @return leftAlive value
     */
    public int getLeftAlive() {
        return this.leftAlive;
    }

    /**
     * set leftAlive && set marked
     *
     * @param leftAlive new value
     * @return current object
     */
    public BattleRecordSoldierReportProp setLeftAlive(int leftAlive) {
        if (this.leftAlive != leftAlive) {
            this.mark(FIELD_INDEX_LEFTALIVE);
            this.leftAlive = leftAlive;
        }
        return this;
    }

    /**
     * inner set leftAlive
     *
     * @param leftAlive new value
     */
    private void innerSetLeftAlive(int leftAlive) {
        this.leftAlive = leftAlive;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordSoldierReportPB.Builder getCopyCsBuilder() {
        final BattleRecordSoldierReportPB.Builder builder = BattleRecordSoldierReportPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BattleRecordSoldierReportPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTotal() != 0) {
            builder.setTotal(this.getTotal());
            fieldCnt++;
        }  else if (builder.hasTotal()) {
            // 清理Total
            builder.clearTotal();
            fieldCnt++;
        }
        if (this.getTreatment() != 0) {
            builder.setTreatment(this.getTreatment());
            fieldCnt++;
        }  else if (builder.hasTreatment()) {
            // 清理Treatment
            builder.clearTreatment();
            fieldCnt++;
        }
        if (this.getDead() != 0) {
            builder.setDead(this.getDead());
            fieldCnt++;
        }  else if (builder.hasDead()) {
            // 清理Dead
            builder.clearDead();
            fieldCnt++;
        }
        if (this.getSevereWound() != 0) {
            builder.setSevereWound(this.getSevereWound());
            fieldCnt++;
        }  else if (builder.hasSevereWound()) {
            // 清理SevereWound
            builder.clearSevereWound();
            fieldCnt++;
        }
        if (this.getSlightWound() != 0) {
            builder.setSlightWound(this.getSlightWound());
            fieldCnt++;
        }  else if (builder.hasSlightWound()) {
            // 清理SlightWound
            builder.clearSlightWound();
            fieldCnt++;
        }
        if (this.getLeftAlive() != 0) {
            builder.setLeftAlive(this.getLeftAlive());
            fieldCnt++;
        }  else if (builder.hasLeftAlive()) {
            // 清理LeftAlive
            builder.clearLeftAlive();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BattleRecordSoldierReportPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOTAL)) {
            builder.setTotal(this.getTotal());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TREATMENT)) {
            builder.setTreatment(this.getTreatment());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEAD)) {
            builder.setDead(this.getDead());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SEVEREWOUND)) {
            builder.setSevereWound(this.getSevereWound());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SLIGHTWOUND)) {
            builder.setSlightWound(this.getSlightWound());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEFTALIVE)) {
            builder.setLeftAlive(this.getLeftAlive());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BattleRecordSoldierReportPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOTAL)) {
            builder.setTotal(this.getTotal());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TREATMENT)) {
            builder.setTreatment(this.getTreatment());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEAD)) {
            builder.setDead(this.getDead());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SEVEREWOUND)) {
            builder.setSevereWound(this.getSevereWound());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SLIGHTWOUND)) {
            builder.setSlightWound(this.getSlightWound());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEFTALIVE)) {
            builder.setLeftAlive(this.getLeftAlive());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BattleRecordSoldierReportPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTotal()) {
            this.innerSetTotal(proto.getTotal());
        } else {
            this.innerSetTotal(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTreatment()) {
            this.innerSetTreatment(proto.getTreatment());
        } else {
            this.innerSetTreatment(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDead()) {
            this.innerSetDead(proto.getDead());
        } else {
            this.innerSetDead(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSevereWound()) {
            this.innerSetSevereWound(proto.getSevereWound());
        } else {
            this.innerSetSevereWound(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSlightWound()) {
            this.innerSetSlightWound(proto.getSlightWound());
        } else {
            this.innerSetSlightWound(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLeftAlive()) {
            this.innerSetLeftAlive(proto.getLeftAlive());
        } else {
            this.innerSetLeftAlive(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return BattleRecordSoldierReportProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BattleRecordSoldierReportPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTotal()) {
            this.setTotal(proto.getTotal());
            fieldCnt++;
        }
        if (proto.hasTreatment()) {
            this.setTreatment(proto.getTreatment());
            fieldCnt++;
        }
        if (proto.hasDead()) {
            this.setDead(proto.getDead());
            fieldCnt++;
        }
        if (proto.hasSevereWound()) {
            this.setSevereWound(proto.getSevereWound());
            fieldCnt++;
        }
        if (proto.hasSlightWound()) {
            this.setSlightWound(proto.getSlightWound());
            fieldCnt++;
        }
        if (proto.hasLeftAlive()) {
            this.setLeftAlive(proto.getLeftAlive());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordSoldierReport.Builder getCopyDbBuilder() {
        final BattleRecordSoldierReport.Builder builder = BattleRecordSoldierReport.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(BattleRecordSoldierReport.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTotal() != 0) {
            builder.setTotal(this.getTotal());
            fieldCnt++;
        }  else if (builder.hasTotal()) {
            // 清理Total
            builder.clearTotal();
            fieldCnt++;
        }
        if (this.getTreatment() != 0) {
            builder.setTreatment(this.getTreatment());
            fieldCnt++;
        }  else if (builder.hasTreatment()) {
            // 清理Treatment
            builder.clearTreatment();
            fieldCnt++;
        }
        if (this.getDead() != 0) {
            builder.setDead(this.getDead());
            fieldCnt++;
        }  else if (builder.hasDead()) {
            // 清理Dead
            builder.clearDead();
            fieldCnt++;
        }
        if (this.getSevereWound() != 0) {
            builder.setSevereWound(this.getSevereWound());
            fieldCnt++;
        }  else if (builder.hasSevereWound()) {
            // 清理SevereWound
            builder.clearSevereWound();
            fieldCnt++;
        }
        if (this.getSlightWound() != 0) {
            builder.setSlightWound(this.getSlightWound());
            fieldCnt++;
        }  else if (builder.hasSlightWound()) {
            // 清理SlightWound
            builder.clearSlightWound();
            fieldCnt++;
        }
        if (this.getLeftAlive() != 0) {
            builder.setLeftAlive(this.getLeftAlive());
            fieldCnt++;
        }  else if (builder.hasLeftAlive()) {
            // 清理LeftAlive
            builder.clearLeftAlive();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(BattleRecordSoldierReport.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOTAL)) {
            builder.setTotal(this.getTotal());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TREATMENT)) {
            builder.setTreatment(this.getTreatment());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEAD)) {
            builder.setDead(this.getDead());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SEVEREWOUND)) {
            builder.setSevereWound(this.getSevereWound());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SLIGHTWOUND)) {
            builder.setSlightWound(this.getSlightWound());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEFTALIVE)) {
            builder.setLeftAlive(this.getLeftAlive());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(BattleRecordSoldierReport proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTotal()) {
            this.innerSetTotal(proto.getTotal());
        } else {
            this.innerSetTotal(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTreatment()) {
            this.innerSetTreatment(proto.getTreatment());
        } else {
            this.innerSetTreatment(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDead()) {
            this.innerSetDead(proto.getDead());
        } else {
            this.innerSetDead(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSevereWound()) {
            this.innerSetSevereWound(proto.getSevereWound());
        } else {
            this.innerSetSevereWound(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSlightWound()) {
            this.innerSetSlightWound(proto.getSlightWound());
        } else {
            this.innerSetSlightWound(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLeftAlive()) {
            this.innerSetLeftAlive(proto.getLeftAlive());
        } else {
            this.innerSetLeftAlive(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return BattleRecordSoldierReportProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(BattleRecordSoldierReport proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTotal()) {
            this.setTotal(proto.getTotal());
            fieldCnt++;
        }
        if (proto.hasTreatment()) {
            this.setTreatment(proto.getTreatment());
            fieldCnt++;
        }
        if (proto.hasDead()) {
            this.setDead(proto.getDead());
            fieldCnt++;
        }
        if (proto.hasSevereWound()) {
            this.setSevereWound(proto.getSevereWound());
            fieldCnt++;
        }
        if (proto.hasSlightWound()) {
            this.setSlightWound(proto.getSlightWound());
            fieldCnt++;
        }
        if (proto.hasLeftAlive()) {
            this.setLeftAlive(proto.getLeftAlive());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordSoldierReport.Builder getCopySsBuilder() {
        final BattleRecordSoldierReport.Builder builder = BattleRecordSoldierReport.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(BattleRecordSoldierReport.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTotal() != 0) {
            builder.setTotal(this.getTotal());
            fieldCnt++;
        }  else if (builder.hasTotal()) {
            // 清理Total
            builder.clearTotal();
            fieldCnt++;
        }
        if (this.getTreatment() != 0) {
            builder.setTreatment(this.getTreatment());
            fieldCnt++;
        }  else if (builder.hasTreatment()) {
            // 清理Treatment
            builder.clearTreatment();
            fieldCnt++;
        }
        if (this.getDead() != 0) {
            builder.setDead(this.getDead());
            fieldCnt++;
        }  else if (builder.hasDead()) {
            // 清理Dead
            builder.clearDead();
            fieldCnt++;
        }
        if (this.getSevereWound() != 0) {
            builder.setSevereWound(this.getSevereWound());
            fieldCnt++;
        }  else if (builder.hasSevereWound()) {
            // 清理SevereWound
            builder.clearSevereWound();
            fieldCnt++;
        }
        if (this.getSlightWound() != 0) {
            builder.setSlightWound(this.getSlightWound());
            fieldCnt++;
        }  else if (builder.hasSlightWound()) {
            // 清理SlightWound
            builder.clearSlightWound();
            fieldCnt++;
        }
        if (this.getLeftAlive() != 0) {
            builder.setLeftAlive(this.getLeftAlive());
            fieldCnt++;
        }  else if (builder.hasLeftAlive()) {
            // 清理LeftAlive
            builder.clearLeftAlive();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(BattleRecordSoldierReport.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOTAL)) {
            builder.setTotal(this.getTotal());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TREATMENT)) {
            builder.setTreatment(this.getTreatment());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEAD)) {
            builder.setDead(this.getDead());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SEVEREWOUND)) {
            builder.setSevereWound(this.getSevereWound());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SLIGHTWOUND)) {
            builder.setSlightWound(this.getSlightWound());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEFTALIVE)) {
            builder.setLeftAlive(this.getLeftAlive());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(BattleRecordSoldierReport proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTotal()) {
            this.innerSetTotal(proto.getTotal());
        } else {
            this.innerSetTotal(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTreatment()) {
            this.innerSetTreatment(proto.getTreatment());
        } else {
            this.innerSetTreatment(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDead()) {
            this.innerSetDead(proto.getDead());
        } else {
            this.innerSetDead(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSevereWound()) {
            this.innerSetSevereWound(proto.getSevereWound());
        } else {
            this.innerSetSevereWound(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSlightWound()) {
            this.innerSetSlightWound(proto.getSlightWound());
        } else {
            this.innerSetSlightWound(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLeftAlive()) {
            this.innerSetLeftAlive(proto.getLeftAlive());
        } else {
            this.innerSetLeftAlive(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return BattleRecordSoldierReportProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(BattleRecordSoldierReport proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTotal()) {
            this.setTotal(proto.getTotal());
            fieldCnt++;
        }
        if (proto.hasTreatment()) {
            this.setTreatment(proto.getTreatment());
            fieldCnt++;
        }
        if (proto.hasDead()) {
            this.setDead(proto.getDead());
            fieldCnt++;
        }
        if (proto.hasSevereWound()) {
            this.setSevereWound(proto.getSevereWound());
            fieldCnt++;
        }
        if (proto.hasSlightWound()) {
            this.setSlightWound(proto.getSlightWound());
            fieldCnt++;
        }
        if (proto.hasLeftAlive()) {
            this.setLeftAlive(proto.getLeftAlive());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        BattleRecordSoldierReport.Builder builder = BattleRecordSoldierReport.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BattleRecordSoldierReportProp)) {
            return false;
        }
        final BattleRecordSoldierReportProp otherNode = (BattleRecordSoldierReportProp) node;
        if (this.total != otherNode.total) {
            return false;
        }
        if (this.treatment != otherNode.treatment) {
            return false;
        }
        if (this.dead != otherNode.dead) {
            return false;
        }
        if (this.severeWound != otherNode.severeWound) {
            return false;
        }
        if (this.slightWound != otherNode.slightWound) {
            return false;
        }
        if (this.leftAlive != otherNode.leftAlive) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zoneside.ZoneActivityModel;
import com.yorha.proto.Zoneside;
import com.yorha.proto.ZonesidePB.ZoneActivityModelPB;
import com.yorha.proto.ZonesidePB;


/**
 * <AUTHOR> auto gen
 */
public class ZoneActivityModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ACTIVITIES = 0;
    public static final int FIELD_INDEX_BESTCOMMANDERHISTORYRANKMODEL = 1;
    public static final int FIELD_INDEX_FORCEOFFACTIVITIES = 2;
    public static final int FIELD_INDEX_BCMODEL = 3;
    public static final int FIELD_INDEX_LOTTERYMODEL = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private Int32ZoneActivityMapProp activities = null;
    private BestCommanderHistoryRankModelProp bestCommanderHistoryRankModel = null;
    private Int32ZoneActivityMapProp forceOffActivities = null;
    private ZoneSideBestCommanderModelProp bcModel = null;
    private ZoneSideLotteryModelProp lotteryModel = null;

    public ZoneActivityModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ZoneActivityModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get activities
     *
     * @return activities value
     */
    public Int32ZoneActivityMapProp getActivities() {
        if (this.activities == null) {
            this.activities = new Int32ZoneActivityMapProp(this, FIELD_INDEX_ACTIVITIES);
        }
        return this.activities;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putActivitiesV(ZoneActivityProp v) {
        this.getActivities().put(v.getActivityId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ZoneActivityProp addEmptyActivities(Integer k) {
        return this.getActivities().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getActivitiesSize() {
        if (this.activities == null) {
            return 0;
        }
        return this.activities.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isActivitiesEmpty() {
        if (this.activities == null) {
            return true;
        }
        return this.activities.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ZoneActivityProp getActivitiesV(Integer k) {
        if (this.activities == null || !this.activities.containsKey(k)) {
            return null;
        }
        return this.activities.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearActivities() {
        if (this.activities != null) {
            this.activities.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeActivitiesV(Integer k) {
        if (this.activities != null) {
            this.activities.remove(k);
        }
    }
    /**
     * get bestCommanderHistoryRankModel
     *
     * @return bestCommanderHistoryRankModel value
     */
    public BestCommanderHistoryRankModelProp getBestCommanderHistoryRankModel() {
        if (this.bestCommanderHistoryRankModel == null) {
            this.bestCommanderHistoryRankModel = new BestCommanderHistoryRankModelProp(this, FIELD_INDEX_BESTCOMMANDERHISTORYRANKMODEL);
        }
        return this.bestCommanderHistoryRankModel;
    }

    /**
     * get forceOffActivities
     *
     * @return forceOffActivities value
     */
    public Int32ZoneActivityMapProp getForceOffActivities() {
        if (this.forceOffActivities == null) {
            this.forceOffActivities = new Int32ZoneActivityMapProp(this, FIELD_INDEX_FORCEOFFACTIVITIES);
        }
        return this.forceOffActivities;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putForceOffActivitiesV(ZoneActivityProp v) {
        this.getForceOffActivities().put(v.getActivityId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ZoneActivityProp addEmptyForceOffActivities(Integer k) {
        return this.getForceOffActivities().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getForceOffActivitiesSize() {
        if (this.forceOffActivities == null) {
            return 0;
        }
        return this.forceOffActivities.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isForceOffActivitiesEmpty() {
        if (this.forceOffActivities == null) {
            return true;
        }
        return this.forceOffActivities.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ZoneActivityProp getForceOffActivitiesV(Integer k) {
        if (this.forceOffActivities == null || !this.forceOffActivities.containsKey(k)) {
            return null;
        }
        return this.forceOffActivities.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearForceOffActivities() {
        if (this.forceOffActivities != null) {
            this.forceOffActivities.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeForceOffActivitiesV(Integer k) {
        if (this.forceOffActivities != null) {
            this.forceOffActivities.remove(k);
        }
    }
    /**
     * get bcModel
     *
     * @return bcModel value
     */
    public ZoneSideBestCommanderModelProp getBcModel() {
        if (this.bcModel == null) {
            this.bcModel = new ZoneSideBestCommanderModelProp(this, FIELD_INDEX_BCMODEL);
        }
        return this.bcModel;
    }

    /**
     * get lotteryModel
     *
     * @return lotteryModel value
     */
    public ZoneSideLotteryModelProp getLotteryModel() {
        if (this.lotteryModel == null) {
            this.lotteryModel = new ZoneSideLotteryModelProp(this, FIELD_INDEX_LOTTERYMODEL);
        }
        return this.lotteryModel;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneActivityModelPB.Builder getCopyCsBuilder() {
        final ZoneActivityModelPB.Builder builder = ZoneActivityModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ZoneActivityModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.activities != null) {
            ZonesidePB.Int32ZoneActivityMapPB.Builder tmpBuilder = ZonesidePB.Int32ZoneActivityMapPB.newBuilder();
            final int tmpFieldCnt = this.activities.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActivities(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActivities();
            }
        }  else if (builder.hasActivities()) {
            // 清理Activities
            builder.clearActivities();
            fieldCnt++;
        }
        if (this.bestCommanderHistoryRankModel != null) {
            ZonesidePB.BestCommanderHistoryRankModelPB.Builder tmpBuilder = ZonesidePB.BestCommanderHistoryRankModelPB.newBuilder();
            final int tmpFieldCnt = this.bestCommanderHistoryRankModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBestCommanderHistoryRankModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBestCommanderHistoryRankModel();
            }
        }  else if (builder.hasBestCommanderHistoryRankModel()) {
            // 清理BestCommanderHistoryRankModel
            builder.clearBestCommanderHistoryRankModel();
            fieldCnt++;
        }
        if (this.forceOffActivities != null) {
            ZonesidePB.Int32ZoneActivityMapPB.Builder tmpBuilder = ZonesidePB.Int32ZoneActivityMapPB.newBuilder();
            final int tmpFieldCnt = this.forceOffActivities.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setForceOffActivities(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearForceOffActivities();
            }
        }  else if (builder.hasForceOffActivities()) {
            // 清理ForceOffActivities
            builder.clearForceOffActivities();
            fieldCnt++;
        }
        if (this.bcModel != null) {
            ZonesidePB.ZoneSideBestCommanderModelPB.Builder tmpBuilder = ZonesidePB.ZoneSideBestCommanderModelPB.newBuilder();
            final int tmpFieldCnt = this.bcModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBcModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBcModel();
            }
        }  else if (builder.hasBcModel()) {
            // 清理BcModel
            builder.clearBcModel();
            fieldCnt++;
        }
        if (this.lotteryModel != null) {
            ZonesidePB.ZoneSideLotteryModelPB.Builder tmpBuilder = ZonesidePB.ZoneSideLotteryModelPB.newBuilder();
            final int tmpFieldCnt = this.lotteryModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLotteryModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLotteryModel();
            }
        }  else if (builder.hasLotteryModel()) {
            // 清理LotteryModel
            builder.clearLotteryModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ZoneActivityModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITIES) && this.activities != null) {
            final boolean needClear = !builder.hasActivities();
            final int tmpFieldCnt = this.activities.copyChangeToCs(builder.getActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERHISTORYRANKMODEL) && this.bestCommanderHistoryRankModel != null) {
            final boolean needClear = !builder.hasBestCommanderHistoryRankModel();
            final int tmpFieldCnt = this.bestCommanderHistoryRankModel.copyChangeToCs(builder.getBestCommanderHistoryRankModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderHistoryRankModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_FORCEOFFACTIVITIES) && this.forceOffActivities != null) {
            final boolean needClear = !builder.hasForceOffActivities();
            final int tmpFieldCnt = this.forceOffActivities.copyChangeToCs(builder.getForceOffActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearForceOffActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_BCMODEL) && this.bcModel != null) {
            final boolean needClear = !builder.hasBcModel();
            final int tmpFieldCnt = this.bcModel.copyChangeToCs(builder.getBcModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBcModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOTTERYMODEL) && this.lotteryModel != null) {
            final boolean needClear = !builder.hasLotteryModel();
            final int tmpFieldCnt = this.lotteryModel.copyChangeToCs(builder.getLotteryModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLotteryModel();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ZoneActivityModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITIES) && this.activities != null) {
            final boolean needClear = !builder.hasActivities();
            final int tmpFieldCnt = this.activities.copyChangeToAndClearDeleteKeysCs(builder.getActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERHISTORYRANKMODEL) && this.bestCommanderHistoryRankModel != null) {
            final boolean needClear = !builder.hasBestCommanderHistoryRankModel();
            final int tmpFieldCnt = this.bestCommanderHistoryRankModel.copyChangeToAndClearDeleteKeysCs(builder.getBestCommanderHistoryRankModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderHistoryRankModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_FORCEOFFACTIVITIES) && this.forceOffActivities != null) {
            final boolean needClear = !builder.hasForceOffActivities();
            final int tmpFieldCnt = this.forceOffActivities.copyChangeToAndClearDeleteKeysCs(builder.getForceOffActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearForceOffActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_BCMODEL) && this.bcModel != null) {
            final boolean needClear = !builder.hasBcModel();
            final int tmpFieldCnt = this.bcModel.copyChangeToAndClearDeleteKeysCs(builder.getBcModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBcModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOTTERYMODEL) && this.lotteryModel != null) {
            final boolean needClear = !builder.hasLotteryModel();
            final int tmpFieldCnt = this.lotteryModel.copyChangeToAndClearDeleteKeysCs(builder.getLotteryModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLotteryModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ZoneActivityModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActivities()) {
            this.getActivities().mergeFromCs(proto.getActivities());
        } else {
            if (this.activities != null) {
                this.activities.mergeFromCs(proto.getActivities());
            }
        }
        if (proto.hasBestCommanderHistoryRankModel()) {
            this.getBestCommanderHistoryRankModel().mergeFromCs(proto.getBestCommanderHistoryRankModel());
        } else {
            if (this.bestCommanderHistoryRankModel != null) {
                this.bestCommanderHistoryRankModel.mergeFromCs(proto.getBestCommanderHistoryRankModel());
            }
        }
        if (proto.hasForceOffActivities()) {
            this.getForceOffActivities().mergeFromCs(proto.getForceOffActivities());
        } else {
            if (this.forceOffActivities != null) {
                this.forceOffActivities.mergeFromCs(proto.getForceOffActivities());
            }
        }
        if (proto.hasBcModel()) {
            this.getBcModel().mergeFromCs(proto.getBcModel());
        } else {
            if (this.bcModel != null) {
                this.bcModel.mergeFromCs(proto.getBcModel());
            }
        }
        if (proto.hasLotteryModel()) {
            this.getLotteryModel().mergeFromCs(proto.getLotteryModel());
        } else {
            if (this.lotteryModel != null) {
                this.lotteryModel.mergeFromCs(proto.getLotteryModel());
            }
        }
        this.markAll();
        return ZoneActivityModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ZoneActivityModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActivities()) {
            this.getActivities().mergeChangeFromCs(proto.getActivities());
            fieldCnt++;
        }
        if (proto.hasBestCommanderHistoryRankModel()) {
            this.getBestCommanderHistoryRankModel().mergeChangeFromCs(proto.getBestCommanderHistoryRankModel());
            fieldCnt++;
        }
        if (proto.hasForceOffActivities()) {
            this.getForceOffActivities().mergeChangeFromCs(proto.getForceOffActivities());
            fieldCnt++;
        }
        if (proto.hasBcModel()) {
            this.getBcModel().mergeChangeFromCs(proto.getBcModel());
            fieldCnt++;
        }
        if (proto.hasLotteryModel()) {
            this.getLotteryModel().mergeChangeFromCs(proto.getLotteryModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneActivityModel.Builder getCopyDbBuilder() {
        final ZoneActivityModel.Builder builder = ZoneActivityModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ZoneActivityModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.activities != null) {
            Zoneside.Int32ZoneActivityMap.Builder tmpBuilder = Zoneside.Int32ZoneActivityMap.newBuilder();
            final int tmpFieldCnt = this.activities.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActivities(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActivities();
            }
        }  else if (builder.hasActivities()) {
            // 清理Activities
            builder.clearActivities();
            fieldCnt++;
        }
        if (this.bestCommanderHistoryRankModel != null) {
            Zoneside.BestCommanderHistoryRankModel.Builder tmpBuilder = Zoneside.BestCommanderHistoryRankModel.newBuilder();
            final int tmpFieldCnt = this.bestCommanderHistoryRankModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBestCommanderHistoryRankModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBestCommanderHistoryRankModel();
            }
        }  else if (builder.hasBestCommanderHistoryRankModel()) {
            // 清理BestCommanderHistoryRankModel
            builder.clearBestCommanderHistoryRankModel();
            fieldCnt++;
        }
        if (this.forceOffActivities != null) {
            Zoneside.Int32ZoneActivityMap.Builder tmpBuilder = Zoneside.Int32ZoneActivityMap.newBuilder();
            final int tmpFieldCnt = this.forceOffActivities.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setForceOffActivities(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearForceOffActivities();
            }
        }  else if (builder.hasForceOffActivities()) {
            // 清理ForceOffActivities
            builder.clearForceOffActivities();
            fieldCnt++;
        }
        if (this.bcModel != null) {
            Zoneside.ZoneSideBestCommanderModel.Builder tmpBuilder = Zoneside.ZoneSideBestCommanderModel.newBuilder();
            final int tmpFieldCnt = this.bcModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBcModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBcModel();
            }
        }  else if (builder.hasBcModel()) {
            // 清理BcModel
            builder.clearBcModel();
            fieldCnt++;
        }
        if (this.lotteryModel != null) {
            Zoneside.ZoneSideLotteryModel.Builder tmpBuilder = Zoneside.ZoneSideLotteryModel.newBuilder();
            final int tmpFieldCnt = this.lotteryModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLotteryModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLotteryModel();
            }
        }  else if (builder.hasLotteryModel()) {
            // 清理LotteryModel
            builder.clearLotteryModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ZoneActivityModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITIES) && this.activities != null) {
            final boolean needClear = !builder.hasActivities();
            final int tmpFieldCnt = this.activities.copyChangeToDb(builder.getActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERHISTORYRANKMODEL) && this.bestCommanderHistoryRankModel != null) {
            final boolean needClear = !builder.hasBestCommanderHistoryRankModel();
            final int tmpFieldCnt = this.bestCommanderHistoryRankModel.copyChangeToDb(builder.getBestCommanderHistoryRankModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderHistoryRankModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_FORCEOFFACTIVITIES) && this.forceOffActivities != null) {
            final boolean needClear = !builder.hasForceOffActivities();
            final int tmpFieldCnt = this.forceOffActivities.copyChangeToDb(builder.getForceOffActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearForceOffActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_BCMODEL) && this.bcModel != null) {
            final boolean needClear = !builder.hasBcModel();
            final int tmpFieldCnt = this.bcModel.copyChangeToDb(builder.getBcModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBcModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOTTERYMODEL) && this.lotteryModel != null) {
            final boolean needClear = !builder.hasLotteryModel();
            final int tmpFieldCnt = this.lotteryModel.copyChangeToDb(builder.getLotteryModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLotteryModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ZoneActivityModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActivities()) {
            this.getActivities().mergeFromDb(proto.getActivities());
        } else {
            if (this.activities != null) {
                this.activities.mergeFromDb(proto.getActivities());
            }
        }
        if (proto.hasBestCommanderHistoryRankModel()) {
            this.getBestCommanderHistoryRankModel().mergeFromDb(proto.getBestCommanderHistoryRankModel());
        } else {
            if (this.bestCommanderHistoryRankModel != null) {
                this.bestCommanderHistoryRankModel.mergeFromDb(proto.getBestCommanderHistoryRankModel());
            }
        }
        if (proto.hasForceOffActivities()) {
            this.getForceOffActivities().mergeFromDb(proto.getForceOffActivities());
        } else {
            if (this.forceOffActivities != null) {
                this.forceOffActivities.mergeFromDb(proto.getForceOffActivities());
            }
        }
        if (proto.hasBcModel()) {
            this.getBcModel().mergeFromDb(proto.getBcModel());
        } else {
            if (this.bcModel != null) {
                this.bcModel.mergeFromDb(proto.getBcModel());
            }
        }
        if (proto.hasLotteryModel()) {
            this.getLotteryModel().mergeFromDb(proto.getLotteryModel());
        } else {
            if (this.lotteryModel != null) {
                this.lotteryModel.mergeFromDb(proto.getLotteryModel());
            }
        }
        this.markAll();
        return ZoneActivityModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ZoneActivityModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActivities()) {
            this.getActivities().mergeChangeFromDb(proto.getActivities());
            fieldCnt++;
        }
        if (proto.hasBestCommanderHistoryRankModel()) {
            this.getBestCommanderHistoryRankModel().mergeChangeFromDb(proto.getBestCommanderHistoryRankModel());
            fieldCnt++;
        }
        if (proto.hasForceOffActivities()) {
            this.getForceOffActivities().mergeChangeFromDb(proto.getForceOffActivities());
            fieldCnt++;
        }
        if (proto.hasBcModel()) {
            this.getBcModel().mergeChangeFromDb(proto.getBcModel());
            fieldCnt++;
        }
        if (proto.hasLotteryModel()) {
            this.getLotteryModel().mergeChangeFromDb(proto.getLotteryModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneActivityModel.Builder getCopySsBuilder() {
        final ZoneActivityModel.Builder builder = ZoneActivityModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ZoneActivityModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.activities != null) {
            Zoneside.Int32ZoneActivityMap.Builder tmpBuilder = Zoneside.Int32ZoneActivityMap.newBuilder();
            final int tmpFieldCnt = this.activities.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActivities(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActivities();
            }
        }  else if (builder.hasActivities()) {
            // 清理Activities
            builder.clearActivities();
            fieldCnt++;
        }
        if (this.bestCommanderHistoryRankModel != null) {
            Zoneside.BestCommanderHistoryRankModel.Builder tmpBuilder = Zoneside.BestCommanderHistoryRankModel.newBuilder();
            final int tmpFieldCnt = this.bestCommanderHistoryRankModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBestCommanderHistoryRankModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBestCommanderHistoryRankModel();
            }
        }  else if (builder.hasBestCommanderHistoryRankModel()) {
            // 清理BestCommanderHistoryRankModel
            builder.clearBestCommanderHistoryRankModel();
            fieldCnt++;
        }
        if (this.forceOffActivities != null) {
            Zoneside.Int32ZoneActivityMap.Builder tmpBuilder = Zoneside.Int32ZoneActivityMap.newBuilder();
            final int tmpFieldCnt = this.forceOffActivities.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setForceOffActivities(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearForceOffActivities();
            }
        }  else if (builder.hasForceOffActivities()) {
            // 清理ForceOffActivities
            builder.clearForceOffActivities();
            fieldCnt++;
        }
        if (this.bcModel != null) {
            Zoneside.ZoneSideBestCommanderModel.Builder tmpBuilder = Zoneside.ZoneSideBestCommanderModel.newBuilder();
            final int tmpFieldCnt = this.bcModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBcModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBcModel();
            }
        }  else if (builder.hasBcModel()) {
            // 清理BcModel
            builder.clearBcModel();
            fieldCnt++;
        }
        if (this.lotteryModel != null) {
            Zoneside.ZoneSideLotteryModel.Builder tmpBuilder = Zoneside.ZoneSideLotteryModel.newBuilder();
            final int tmpFieldCnt = this.lotteryModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLotteryModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLotteryModel();
            }
        }  else if (builder.hasLotteryModel()) {
            // 清理LotteryModel
            builder.clearLotteryModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ZoneActivityModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITIES) && this.activities != null) {
            final boolean needClear = !builder.hasActivities();
            final int tmpFieldCnt = this.activities.copyChangeToSs(builder.getActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERHISTORYRANKMODEL) && this.bestCommanderHistoryRankModel != null) {
            final boolean needClear = !builder.hasBestCommanderHistoryRankModel();
            final int tmpFieldCnt = this.bestCommanderHistoryRankModel.copyChangeToSs(builder.getBestCommanderHistoryRankModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderHistoryRankModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_FORCEOFFACTIVITIES) && this.forceOffActivities != null) {
            final boolean needClear = !builder.hasForceOffActivities();
            final int tmpFieldCnt = this.forceOffActivities.copyChangeToSs(builder.getForceOffActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearForceOffActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_BCMODEL) && this.bcModel != null) {
            final boolean needClear = !builder.hasBcModel();
            final int tmpFieldCnt = this.bcModel.copyChangeToSs(builder.getBcModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBcModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOTTERYMODEL) && this.lotteryModel != null) {
            final boolean needClear = !builder.hasLotteryModel();
            final int tmpFieldCnt = this.lotteryModel.copyChangeToSs(builder.getLotteryModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLotteryModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ZoneActivityModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActivities()) {
            this.getActivities().mergeFromSs(proto.getActivities());
        } else {
            if (this.activities != null) {
                this.activities.mergeFromSs(proto.getActivities());
            }
        }
        if (proto.hasBestCommanderHistoryRankModel()) {
            this.getBestCommanderHistoryRankModel().mergeFromSs(proto.getBestCommanderHistoryRankModel());
        } else {
            if (this.bestCommanderHistoryRankModel != null) {
                this.bestCommanderHistoryRankModel.mergeFromSs(proto.getBestCommanderHistoryRankModel());
            }
        }
        if (proto.hasForceOffActivities()) {
            this.getForceOffActivities().mergeFromSs(proto.getForceOffActivities());
        } else {
            if (this.forceOffActivities != null) {
                this.forceOffActivities.mergeFromSs(proto.getForceOffActivities());
            }
        }
        if (proto.hasBcModel()) {
            this.getBcModel().mergeFromSs(proto.getBcModel());
        } else {
            if (this.bcModel != null) {
                this.bcModel.mergeFromSs(proto.getBcModel());
            }
        }
        if (proto.hasLotteryModel()) {
            this.getLotteryModel().mergeFromSs(proto.getLotteryModel());
        } else {
            if (this.lotteryModel != null) {
                this.lotteryModel.mergeFromSs(proto.getLotteryModel());
            }
        }
        this.markAll();
        return ZoneActivityModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ZoneActivityModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActivities()) {
            this.getActivities().mergeChangeFromSs(proto.getActivities());
            fieldCnt++;
        }
        if (proto.hasBestCommanderHistoryRankModel()) {
            this.getBestCommanderHistoryRankModel().mergeChangeFromSs(proto.getBestCommanderHistoryRankModel());
            fieldCnt++;
        }
        if (proto.hasForceOffActivities()) {
            this.getForceOffActivities().mergeChangeFromSs(proto.getForceOffActivities());
            fieldCnt++;
        }
        if (proto.hasBcModel()) {
            this.getBcModel().mergeChangeFromSs(proto.getBcModel());
            fieldCnt++;
        }
        if (proto.hasLotteryModel()) {
            this.getLotteryModel().mergeChangeFromSs(proto.getLotteryModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ZoneActivityModel.Builder builder = ZoneActivityModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITIES) && this.activities != null) {
            this.activities.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERHISTORYRANKMODEL) && this.bestCommanderHistoryRankModel != null) {
            this.bestCommanderHistoryRankModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_FORCEOFFACTIVITIES) && this.forceOffActivities != null) {
            this.forceOffActivities.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BCMODEL) && this.bcModel != null) {
            this.bcModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_LOTTERYMODEL) && this.lotteryModel != null) {
            this.lotteryModel.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.activities != null) {
            this.activities.markAll();
        }
        if (this.bestCommanderHistoryRankModel != null) {
            this.bestCommanderHistoryRankModel.markAll();
        }
        if (this.forceOffActivities != null) {
            this.forceOffActivities.markAll();
        }
        if (this.bcModel != null) {
            this.bcModel.markAll();
        }
        if (this.lotteryModel != null) {
            this.lotteryModel.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ZoneActivityModelProp)) {
            return false;
        }
        final ZoneActivityModelProp otherNode = (ZoneActivityModelProp) node;
        if (!this.getActivities().compareDataTo(otherNode.getActivities())) {
            return false;
        }
        if (!this.getBestCommanderHistoryRankModel().compareDataTo(otherNode.getBestCommanderHistoryRankModel())) {
            return false;
        }
        if (!this.getForceOffActivities().compareDataTo(otherNode.getForceOffActivities())) {
            return false;
        }
        if (!this.getBcModel().compareDataTo(otherNode.getBcModel())) {
            return false;
        }
        if (!this.getLotteryModel().compareDataTo(otherNode.getLotteryModel())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
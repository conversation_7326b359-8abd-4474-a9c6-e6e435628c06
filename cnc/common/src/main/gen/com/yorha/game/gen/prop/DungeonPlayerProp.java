package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Player.DungeonPlayer;
import com.yorha.proto.Player;
import com.yorha.proto.Struct;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructCommon;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.PlayerPB.DungeonPlayerPB;
import com.yorha.proto.PlayerPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructBattlePB;
import com.yorha.proto.StructCommonPB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class DungeonPlayerProp extends AbstractPropNode {

    public static final int FIELD_INDEX_SOLDIER = 0;
    public static final int FIELD_INDEX_SCENEDEVBUFFSYS = 1;
    public static final int FIELD_INDEX_ADDITIONSYS = 2;
    public static final int FIELD_INDEX_CARDHEAD = 3;
    public static final int FIELD_INDEX_ARMYMODEL = 4;
    public static final int FIELD_INDEX_RALLYMODEL = 5;
    public static final int FIELD_INDEX_DEVBUFFSYS = 6;
    public static final int FIELD_INDEX_CAMP = 7;
    public static final int FIELD_INDEX_MAINCITYID = 8;
    public static final int FIELD_INDEX_DUNGEONSKILL = 9;
    public static final int FIELD_INDEX_WARNING = 10;
    public static final int FIELD_INDEX_WALL = 11;
    public static final int FIELD_INDEX_HOSPITALMODEL = 12;
    public static final int FIELD_INDEX_HEROMODEL = 13;
    public static final int FIELD_INDEX_TECH = 14;
    public static final int FIELD_INDEX_INNERBUILD = 15;
    public static final int FIELD_INDEX_TECHMODEL = 16;

    public static final int FIELD_COUNT = 17;

    private long markBits0 = 0L;
    private PropertyChangeListener listener;

    private ScenePlayerSoldierProp soldier = null;
    private SceneDevBuffSysProp sceneDevBuffSys = null;
    private AdditionSysProp additionSys = null;
    private PlayerCardHeadProp cardHead = null;
    private ScenePlayerArmyModelProp armyModel = null;
    private ScenePlayerRallyBaseProp rallyModel = null;
    private DevBuffSysProp devBuffSys = null;
    private Camp camp = Camp.forNumber(0);
    private long mainCityId = Constant.DEFAULT_LONG_VALUE;
    private DungeonSkillSysProp dungeonSkill = null;
    private WarningItemListProp warning = null;
    private WallInfoProp wall = null;
    private ScenePlayerHospitalModelProp hospitalModel = null;
    private PlayerHeroModelProp heroModel = null;
    private PlayerTechnologyModelProp tech = null;
    private Int64InnerBuildMapProp innerBuild = null;
    private ScenePlayerTechModelProp techModel = null;

    public DungeonPlayerProp() {
        super(null, 0, FIELD_COUNT);
    }

    public DungeonPlayerProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get soldier
     *
     * @return soldier value
     */
    public ScenePlayerSoldierProp getSoldier() {
        if (this.soldier == null) {
            this.soldier = new ScenePlayerSoldierProp(this, FIELD_INDEX_SOLDIER);
        }
        return this.soldier;
    }

    /**
     * get sceneDevBuffSys
     *
     * @return sceneDevBuffSys value
     */
    public SceneDevBuffSysProp getSceneDevBuffSys() {
        if (this.sceneDevBuffSys == null) {
            this.sceneDevBuffSys = new SceneDevBuffSysProp(this, FIELD_INDEX_SCENEDEVBUFFSYS);
        }
        return this.sceneDevBuffSys;
    }

    /**
     * get additionSys
     *
     * @return additionSys value
     */
    public AdditionSysProp getAdditionSys() {
        if (this.additionSys == null) {
            this.additionSys = new AdditionSysProp(this, FIELD_INDEX_ADDITIONSYS);
        }
        return this.additionSys;
    }

    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }

    /**
     * get armyModel
     *
     * @return armyModel value
     */
    public ScenePlayerArmyModelProp getArmyModel() {
        if (this.armyModel == null) {
            this.armyModel = new ScenePlayerArmyModelProp(this, FIELD_INDEX_ARMYMODEL);
        }
        return this.armyModel;
    }

    /**
     * get rallyModel
     *
     * @return rallyModel value
     */
    public ScenePlayerRallyBaseProp getRallyModel() {
        if (this.rallyModel == null) {
            this.rallyModel = new ScenePlayerRallyBaseProp(this, FIELD_INDEX_RALLYMODEL);
        }
        return this.rallyModel;
    }

    /**
     * get devBuffSys
     *
     * @return devBuffSys value
     */
    public DevBuffSysProp getDevBuffSys() {
        if (this.devBuffSys == null) {
            this.devBuffSys = new DevBuffSysProp(this, FIELD_INDEX_DEVBUFFSYS);
        }
        return this.devBuffSys;
    }

    /**
     * get camp
     *
     * @return camp value
     */
    public Camp getCamp() {
        return this.camp;
    }

    /**
     * set camp && set marked
     *
     * @param camp new value
     * @return current object
     */
    public DungeonPlayerProp setCamp(Camp camp) {
        if (camp == null) {
            throw new NullPointerException();
        }
        if (this.camp != camp) {
            this.mark(FIELD_INDEX_CAMP);
            this.camp = camp;
        }
        return this;
    }

    /**
     * inner set camp
     *
     * @param camp new value
     */
    private void innerSetCamp(Camp camp) {
        this.camp = camp;
    }

    /**
     * get mainCityId
     *
     * @return mainCityId value
     */
    public long getMainCityId() {
        return this.mainCityId;
    }

    /**
     * set mainCityId && set marked
     *
     * @param mainCityId new value
     * @return current object
     */
    public DungeonPlayerProp setMainCityId(long mainCityId) {
        if (this.mainCityId != mainCityId) {
            this.mark(FIELD_INDEX_MAINCITYID);
            this.mainCityId = mainCityId;
        }
        return this;
    }

    /**
     * inner set mainCityId
     *
     * @param mainCityId new value
     */
    private void innerSetMainCityId(long mainCityId) {
        this.mainCityId = mainCityId;
    }

    /**
     * get dungeonSkill
     *
     * @return dungeonSkill value
     */
    public DungeonSkillSysProp getDungeonSkill() {
        if (this.dungeonSkill == null) {
            this.dungeonSkill = new DungeonSkillSysProp(this, FIELD_INDEX_DUNGEONSKILL);
        }
        return this.dungeonSkill;
    }

    /**
     * get warning
     *
     * @return warning value
     */
    public WarningItemListProp getWarning() {
        if (this.warning == null) {
            this.warning = new WarningItemListProp(this, FIELD_INDEX_WARNING);
        }
        return this.warning;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addWarning(WarningItemProp v) {
        this.getWarning().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public WarningItemProp getWarningIndex(int index) {
        return this.getWarning().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public WarningItemProp removeWarning(WarningItemProp v) {
        if (this.warning == null) {
            return null;
        }
        if(this.warning.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getWarningSize() {
        if (this.warning == null) {
            return 0;
        }
        return this.warning.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isWarningEmpty() {
        if (this.warning == null) {
            return true;
        }
        return this.getWarning().isEmpty();
    }

    /**
     * clear list
     */
    public void clearWarning() {
        this.getWarning().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public WarningItemProp removeWarningIndex(int index) {
        return this.getWarning().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public WarningItemProp setWarningIndex(int index, WarningItemProp v) {
        return this.getWarning().set(index, v);
    }
    /**
     * get wall
     *
     * @return wall value
     */
    public WallInfoProp getWall() {
        if (this.wall == null) {
            this.wall = new WallInfoProp(this, FIELD_INDEX_WALL);
        }
        return this.wall;
    }

    /**
     * get hospitalModel
     *
     * @return hospitalModel value
     */
    public ScenePlayerHospitalModelProp getHospitalModel() {
        if (this.hospitalModel == null) {
            this.hospitalModel = new ScenePlayerHospitalModelProp(this, FIELD_INDEX_HOSPITALMODEL);
        }
        return this.hospitalModel;
    }

    /**
     * get heroModel
     *
     * @return heroModel value
     */
    public PlayerHeroModelProp getHeroModel() {
        if (this.heroModel == null) {
            this.heroModel = new PlayerHeroModelProp(this, FIELD_INDEX_HEROMODEL);
        }
        return this.heroModel;
    }

    /**
     * get tech
     *
     * @return tech value
     */
    public PlayerTechnologyModelProp getTech() {
        if (this.tech == null) {
            this.tech = new PlayerTechnologyModelProp(this, FIELD_INDEX_TECH);
        }
        return this.tech;
    }

    /**
     * get innerBuild
     *
     * @return innerBuild value
     */
    public Int64InnerBuildMapProp getInnerBuild() {
        if (this.innerBuild == null) {
            this.innerBuild = new Int64InnerBuildMapProp(this, FIELD_INDEX_INNERBUILD);
        }
        return this.innerBuild;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putInnerBuildV(InnerBuildProp v) {
        this.getInnerBuild().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public InnerBuildProp addEmptyInnerBuild(Long k) {
        return this.getInnerBuild().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getInnerBuildSize() {
        if (this.innerBuild == null) {
            return 0;
        }
        return this.innerBuild.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isInnerBuildEmpty() {
        if (this.innerBuild == null) {
            return true;
        }
        return this.innerBuild.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public InnerBuildProp getInnerBuildV(Long k) {
        if (this.innerBuild == null || !this.innerBuild.containsKey(k)) {
            return null;
        }
        return this.innerBuild.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearInnerBuild() {
        if (this.innerBuild != null) {
            this.innerBuild.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeInnerBuildV(Long k) {
        if (this.innerBuild != null) {
            this.innerBuild.remove(k);
        }
    }
    /**
     * get techModel
     *
     * @return techModel value
     */
    public ScenePlayerTechModelProp getTechModel() {
        if (this.techModel == null) {
            this.techModel = new ScenePlayerTechModelProp(this, FIELD_INDEX_TECHMODEL);
        }
        return this.techModel;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DungeonPlayerPB.Builder getCopyCsBuilder() {
        final DungeonPlayerPB.Builder builder = DungeonPlayerPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(DungeonPlayerPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.soldier != null) {
            StructPlayerPB.ScenePlayerSoldierPB.Builder tmpBuilder = StructPlayerPB.ScenePlayerSoldierPB.newBuilder();
            final int tmpFieldCnt = this.soldier.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldier(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldier();
            }
        }  else if (builder.hasSoldier()) {
            // 清理Soldier
            builder.clearSoldier();
            fieldCnt++;
        }
        if (this.sceneDevBuffSys != null) {
            StructBattlePB.SceneDevBuffSysPB.Builder tmpBuilder = StructBattlePB.SceneDevBuffSysPB.newBuilder();
            final int tmpFieldCnt = this.sceneDevBuffSys.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSceneDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSceneDevBuffSys();
            }
        }  else if (builder.hasSceneDevBuffSys()) {
            // 清理SceneDevBuffSys
            builder.clearSceneDevBuffSys();
            fieldCnt++;
        }
        if (this.additionSys != null) {
            StructPB.AdditionSysPB.Builder tmpBuilder = StructPB.AdditionSysPB.newBuilder();
            final int tmpFieldCnt = this.additionSys.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionSys();
            }
        }  else if (builder.hasAdditionSys()) {
            // 清理AdditionSys
            builder.clearAdditionSys();
            fieldCnt++;
        }
        if (this.armyModel != null) {
            PlayerPB.ScenePlayerArmyModelPB.Builder tmpBuilder = PlayerPB.ScenePlayerArmyModelPB.newBuilder();
            final int tmpFieldCnt = this.armyModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArmyModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArmyModel();
            }
        }  else if (builder.hasArmyModel()) {
            // 清理ArmyModel
            builder.clearArmyModel();
            fieldCnt++;
        }
        if (this.rallyModel != null) {
            StructPB.ScenePlayerRallyBasePB.Builder tmpBuilder = StructPB.ScenePlayerRallyBasePB.newBuilder();
            final int tmpFieldCnt = this.rallyModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRallyModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRallyModel();
            }
        }  else if (builder.hasRallyModel()) {
            // 清理RallyModel
            builder.clearRallyModel();
            fieldCnt++;
        }
        if (this.devBuffSys != null) {
            StructBattlePB.DevBuffSysPB.Builder tmpBuilder = StructBattlePB.DevBuffSysPB.newBuilder();
            final int tmpFieldCnt = this.devBuffSys.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSys();
            }
        }  else if (builder.hasDevBuffSys()) {
            // 清理DevBuffSys
            builder.clearDevBuffSys();
            fieldCnt++;
        }
        if (this.getCamp() != Camp.forNumber(0)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }  else if (builder.hasCamp()) {
            // 清理Camp
            builder.clearCamp();
            fieldCnt++;
        }
        if (this.getMainCityId() != 0L) {
            builder.setMainCityId(this.getMainCityId());
            fieldCnt++;
        }  else if (builder.hasMainCityId()) {
            // 清理MainCityId
            builder.clearMainCityId();
            fieldCnt++;
        }
        if (this.dungeonSkill != null) {
            StructCommonPB.DungeonSkillSysPB.Builder tmpBuilder = StructCommonPB.DungeonSkillSysPB.newBuilder();
            final int tmpFieldCnt = this.dungeonSkill.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDungeonSkill(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDungeonSkill();
            }
        }  else if (builder.hasDungeonSkill()) {
            // 清理DungeonSkill
            builder.clearDungeonSkill();
            fieldCnt++;
        }
        if (this.warning != null) {
            StructPB.WarningItemListPB.Builder tmpBuilder = StructPB.WarningItemListPB.newBuilder();
            final int tmpFieldCnt = this.warning.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWarning(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWarning();
            }
        }  else if (builder.hasWarning()) {
            // 清理Warning
            builder.clearWarning();
            fieldCnt++;
        }
        if (this.wall != null) {
            StructPlayerPB.WallInfoPB.Builder tmpBuilder = StructPlayerPB.WallInfoPB.newBuilder();
            final int tmpFieldCnt = this.wall.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWall(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWall();
            }
        }  else if (builder.hasWall()) {
            // 清理Wall
            builder.clearWall();
            fieldCnt++;
        }
        if (this.hospitalModel != null) {
            StructPB.ScenePlayerHospitalModelPB.Builder tmpBuilder = StructPB.ScenePlayerHospitalModelPB.newBuilder();
            final int tmpFieldCnt = this.hospitalModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHospitalModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHospitalModel();
            }
        }  else if (builder.hasHospitalModel()) {
            // 清理HospitalModel
            builder.clearHospitalModel();
            fieldCnt++;
        }
        if (this.heroModel != null) {
            PlayerPB.PlayerHeroModelPB.Builder tmpBuilder = PlayerPB.PlayerHeroModelPB.newBuilder();
            final int tmpFieldCnt = this.heroModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHeroModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHeroModel();
            }
        }  else if (builder.hasHeroModel()) {
            // 清理HeroModel
            builder.clearHeroModel();
            fieldCnt++;
        }
        if (this.tech != null) {
            PlayerPB.PlayerTechnologyModelPB.Builder tmpBuilder = PlayerPB.PlayerTechnologyModelPB.newBuilder();
            final int tmpFieldCnt = this.tech.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTech(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTech();
            }
        }  else if (builder.hasTech()) {
            // 清理Tech
            builder.clearTech();
            fieldCnt++;
        }
        if (this.innerBuild != null) {
            StructPlayerPB.Int64InnerBuildMapPB.Builder tmpBuilder = StructPlayerPB.Int64InnerBuildMapPB.newBuilder();
            final int tmpFieldCnt = this.innerBuild.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInnerBuild(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInnerBuild();
            }
        }  else if (builder.hasInnerBuild()) {
            // 清理InnerBuild
            builder.clearInnerBuild();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(DungeonPlayerPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIER) && this.soldier != null) {
            final boolean needClear = !builder.hasSoldier();
            final int tmpFieldCnt = this.soldier.copyChangeToCs(builder.getSoldierBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldier();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCENEDEVBUFFSYS) && this.sceneDevBuffSys != null) {
            final boolean needClear = !builder.hasSceneDevBuffSys();
            final int tmpFieldCnt = this.sceneDevBuffSys.copyChangeToCs(builder.getSceneDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSceneDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            final boolean needClear = !builder.hasAdditionSys();
            final int tmpFieldCnt = this.additionSys.copyChangeToCs(builder.getAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARMYMODEL) && this.armyModel != null) {
            final boolean needClear = !builder.hasArmyModel();
            final int tmpFieldCnt = this.armyModel.copyChangeToCs(builder.getArmyModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmyModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_RALLYMODEL) && this.rallyModel != null) {
            final boolean needClear = !builder.hasRallyModel();
            final int tmpFieldCnt = this.rallyModel.copyChangeToCs(builder.getRallyModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRallyModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToCs(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAINCITYID)) {
            builder.setMainCityId(this.getMainCityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONSKILL) && this.dungeonSkill != null) {
            final boolean needClear = !builder.hasDungeonSkill();
            final int tmpFieldCnt = this.dungeonSkill.copyChangeToCs(builder.getDungeonSkillBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDungeonSkill();
            }
        }
        if (this.hasMark(FIELD_INDEX_WARNING) && this.warning != null) {
            final boolean needClear = !builder.hasWarning();
            final int tmpFieldCnt = this.warning.copyChangeToCs(builder.getWarningBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWarning();
            }
        }
        if (this.hasMark(FIELD_INDEX_WALL) && this.wall != null) {
            final boolean needClear = !builder.hasWall();
            final int tmpFieldCnt = this.wall.copyChangeToCs(builder.getWallBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWall();
            }
        }
        if (this.hasMark(FIELD_INDEX_HOSPITALMODEL) && this.hospitalModel != null) {
            final boolean needClear = !builder.hasHospitalModel();
            final int tmpFieldCnt = this.hospitalModel.copyChangeToCs(builder.getHospitalModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHospitalModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_HEROMODEL) && this.heroModel != null) {
            final boolean needClear = !builder.hasHeroModel();
            final int tmpFieldCnt = this.heroModel.copyChangeToCs(builder.getHeroModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHeroModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_TECH) && this.tech != null) {
            final boolean needClear = !builder.hasTech();
            final int tmpFieldCnt = this.tech.copyChangeToCs(builder.getTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTech();
            }
        }
        if (this.hasMark(FIELD_INDEX_INNERBUILD) && this.innerBuild != null) {
            final boolean needClear = !builder.hasInnerBuild();
            final int tmpFieldCnt = this.innerBuild.copyChangeToCs(builder.getInnerBuildBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerBuild();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(DungeonPlayerPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIER) && this.soldier != null) {
            final boolean needClear = !builder.hasSoldier();
            final int tmpFieldCnt = this.soldier.copyChangeToAndClearDeleteKeysCs(builder.getSoldierBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldier();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCENEDEVBUFFSYS) && this.sceneDevBuffSys != null) {
            final boolean needClear = !builder.hasSceneDevBuffSys();
            final int tmpFieldCnt = this.sceneDevBuffSys.copyChangeToAndClearDeleteKeysCs(builder.getSceneDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSceneDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            final boolean needClear = !builder.hasAdditionSys();
            final int tmpFieldCnt = this.additionSys.copyChangeToAndClearDeleteKeysCs(builder.getAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARMYMODEL) && this.armyModel != null) {
            final boolean needClear = !builder.hasArmyModel();
            final int tmpFieldCnt = this.armyModel.copyChangeToAndClearDeleteKeysCs(builder.getArmyModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmyModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_RALLYMODEL) && this.rallyModel != null) {
            final boolean needClear = !builder.hasRallyModel();
            final int tmpFieldCnt = this.rallyModel.copyChangeToAndClearDeleteKeysCs(builder.getRallyModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRallyModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToAndClearDeleteKeysCs(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAINCITYID)) {
            builder.setMainCityId(this.getMainCityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONSKILL) && this.dungeonSkill != null) {
            final boolean needClear = !builder.hasDungeonSkill();
            final int tmpFieldCnt = this.dungeonSkill.copyChangeToAndClearDeleteKeysCs(builder.getDungeonSkillBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDungeonSkill();
            }
        }
        if (this.hasMark(FIELD_INDEX_WARNING) && this.warning != null) {
            final boolean needClear = !builder.hasWarning();
            final int tmpFieldCnt = this.warning.copyChangeToCs(builder.getWarningBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWarning();
            }
        }
        if (this.hasMark(FIELD_INDEX_WALL) && this.wall != null) {
            final boolean needClear = !builder.hasWall();
            final int tmpFieldCnt = this.wall.copyChangeToAndClearDeleteKeysCs(builder.getWallBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWall();
            }
        }
        if (this.hasMark(FIELD_INDEX_HOSPITALMODEL) && this.hospitalModel != null) {
            final boolean needClear = !builder.hasHospitalModel();
            final int tmpFieldCnt = this.hospitalModel.copyChangeToAndClearDeleteKeysCs(builder.getHospitalModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHospitalModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_HEROMODEL) && this.heroModel != null) {
            final boolean needClear = !builder.hasHeroModel();
            final int tmpFieldCnt = this.heroModel.copyChangeToAndClearDeleteKeysCs(builder.getHeroModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHeroModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_TECH) && this.tech != null) {
            final boolean needClear = !builder.hasTech();
            final int tmpFieldCnt = this.tech.copyChangeToAndClearDeleteKeysCs(builder.getTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTech();
            }
        }
        if (this.hasMark(FIELD_INDEX_INNERBUILD) && this.innerBuild != null) {
            final boolean needClear = !builder.hasInnerBuild();
            final int tmpFieldCnt = this.innerBuild.copyChangeToAndClearDeleteKeysCs(builder.getInnerBuildBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerBuild();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(DungeonPlayerPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldier()) {
            this.getSoldier().mergeFromCs(proto.getSoldier());
        } else {
            if (this.soldier != null) {
                this.soldier.mergeFromCs(proto.getSoldier());
            }
        }
        if (proto.hasSceneDevBuffSys()) {
            this.getSceneDevBuffSys().mergeFromCs(proto.getSceneDevBuffSys());
        } else {
            if (this.sceneDevBuffSys != null) {
                this.sceneDevBuffSys.mergeFromCs(proto.getSceneDevBuffSys());
            }
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeFromCs(proto.getAdditionSys());
        } else {
            if (this.additionSys != null) {
                this.additionSys.mergeFromCs(proto.getAdditionSys());
            }
        }
        if (proto.hasArmyModel()) {
            this.getArmyModel().mergeFromCs(proto.getArmyModel());
        } else {
            if (this.armyModel != null) {
                this.armyModel.mergeFromCs(proto.getArmyModel());
            }
        }
        if (proto.hasRallyModel()) {
            this.getRallyModel().mergeFromCs(proto.getRallyModel());
        } else {
            if (this.rallyModel != null) {
                this.rallyModel.mergeFromCs(proto.getRallyModel());
            }
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeFromCs(proto.getDevBuffSys());
        } else {
            if (this.devBuffSys != null) {
                this.devBuffSys.mergeFromCs(proto.getDevBuffSys());
            }
        }
        if (proto.hasCamp()) {
            this.innerSetCamp(proto.getCamp());
        } else {
            this.innerSetCamp(Camp.forNumber(0));
        }
        if (proto.hasMainCityId()) {
            this.innerSetMainCityId(proto.getMainCityId());
        } else {
            this.innerSetMainCityId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasDungeonSkill()) {
            this.getDungeonSkill().mergeFromCs(proto.getDungeonSkill());
        } else {
            if (this.dungeonSkill != null) {
                this.dungeonSkill.mergeFromCs(proto.getDungeonSkill());
            }
        }
        if (proto.hasWarning()) {
            this.getWarning().mergeFromCs(proto.getWarning());
        } else {
            if (this.warning != null) {
                this.warning.mergeFromCs(proto.getWarning());
            }
        }
        if (proto.hasWall()) {
            this.getWall().mergeFromCs(proto.getWall());
        } else {
            if (this.wall != null) {
                this.wall.mergeFromCs(proto.getWall());
            }
        }
        if (proto.hasHospitalModel()) {
            this.getHospitalModel().mergeFromCs(proto.getHospitalModel());
        } else {
            if (this.hospitalModel != null) {
                this.hospitalModel.mergeFromCs(proto.getHospitalModel());
            }
        }
        if (proto.hasHeroModel()) {
            this.getHeroModel().mergeFromCs(proto.getHeroModel());
        } else {
            if (this.heroModel != null) {
                this.heroModel.mergeFromCs(proto.getHeroModel());
            }
        }
        if (proto.hasTech()) {
            this.getTech().mergeFromCs(proto.getTech());
        } else {
            if (this.tech != null) {
                this.tech.mergeFromCs(proto.getTech());
            }
        }
        if (proto.hasInnerBuild()) {
            this.getInnerBuild().mergeFromCs(proto.getInnerBuild());
        } else {
            if (this.innerBuild != null) {
                this.innerBuild.mergeFromCs(proto.getInnerBuild());
            }
        }
        this.markAll();
        return DungeonPlayerProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(DungeonPlayerPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldier()) {
            this.getSoldier().mergeChangeFromCs(proto.getSoldier());
            fieldCnt++;
        }
        if (proto.hasSceneDevBuffSys()) {
            this.getSceneDevBuffSys().mergeChangeFromCs(proto.getSceneDevBuffSys());
            fieldCnt++;
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeChangeFromCs(proto.getAdditionSys());
            fieldCnt++;
        }
        if (proto.hasArmyModel()) {
            this.getArmyModel().mergeChangeFromCs(proto.getArmyModel());
            fieldCnt++;
        }
        if (proto.hasRallyModel()) {
            this.getRallyModel().mergeChangeFromCs(proto.getRallyModel());
            fieldCnt++;
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeChangeFromCs(proto.getDevBuffSys());
            fieldCnt++;
        }
        if (proto.hasCamp()) {
            this.setCamp(proto.getCamp());
            fieldCnt++;
        }
        if (proto.hasMainCityId()) {
            this.setMainCityId(proto.getMainCityId());
            fieldCnt++;
        }
        if (proto.hasDungeonSkill()) {
            this.getDungeonSkill().mergeChangeFromCs(proto.getDungeonSkill());
            fieldCnt++;
        }
        if (proto.hasWarning()) {
            this.getWarning().mergeChangeFromCs(proto.getWarning());
            fieldCnt++;
        }
        if (proto.hasWall()) {
            this.getWall().mergeChangeFromCs(proto.getWall());
            fieldCnt++;
        }
        if (proto.hasHospitalModel()) {
            this.getHospitalModel().mergeChangeFromCs(proto.getHospitalModel());
            fieldCnt++;
        }
        if (proto.hasHeroModel()) {
            this.getHeroModel().mergeChangeFromCs(proto.getHeroModel());
            fieldCnt++;
        }
        if (proto.hasTech()) {
            this.getTech().mergeChangeFromCs(proto.getTech());
            fieldCnt++;
        }
        if (proto.hasInnerBuild()) {
            this.getInnerBuild().mergeChangeFromCs(proto.getInnerBuild());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DungeonPlayer.Builder getCopyDbBuilder() {
        final DungeonPlayer.Builder builder = DungeonPlayer.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(DungeonPlayer.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.soldier != null) {
            StructPlayer.ScenePlayerSoldier.Builder tmpBuilder = StructPlayer.ScenePlayerSoldier.newBuilder();
            final int tmpFieldCnt = this.soldier.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldier(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldier();
            }
        }  else if (builder.hasSoldier()) {
            // 清理Soldier
            builder.clearSoldier();
            fieldCnt++;
        }
        if (this.sceneDevBuffSys != null) {
            StructBattle.SceneDevBuffSys.Builder tmpBuilder = StructBattle.SceneDevBuffSys.newBuilder();
            final int tmpFieldCnt = this.sceneDevBuffSys.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSceneDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSceneDevBuffSys();
            }
        }  else if (builder.hasSceneDevBuffSys()) {
            // 清理SceneDevBuffSys
            builder.clearSceneDevBuffSys();
            fieldCnt++;
        }
        if (this.additionSys != null) {
            Struct.AdditionSys.Builder tmpBuilder = Struct.AdditionSys.newBuilder();
            final int tmpFieldCnt = this.additionSys.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionSys();
            }
        }  else if (builder.hasAdditionSys()) {
            // 清理AdditionSys
            builder.clearAdditionSys();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.armyModel != null) {
            Player.ScenePlayerArmyModel.Builder tmpBuilder = Player.ScenePlayerArmyModel.newBuilder();
            final int tmpFieldCnt = this.armyModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArmyModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArmyModel();
            }
        }  else if (builder.hasArmyModel()) {
            // 清理ArmyModel
            builder.clearArmyModel();
            fieldCnt++;
        }
        if (this.rallyModel != null) {
            Struct.ScenePlayerRallyBase.Builder tmpBuilder = Struct.ScenePlayerRallyBase.newBuilder();
            final int tmpFieldCnt = this.rallyModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRallyModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRallyModel();
            }
        }  else if (builder.hasRallyModel()) {
            // 清理RallyModel
            builder.clearRallyModel();
            fieldCnt++;
        }
        if (this.devBuffSys != null) {
            StructBattle.DevBuffSys.Builder tmpBuilder = StructBattle.DevBuffSys.newBuilder();
            final int tmpFieldCnt = this.devBuffSys.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSys();
            }
        }  else if (builder.hasDevBuffSys()) {
            // 清理DevBuffSys
            builder.clearDevBuffSys();
            fieldCnt++;
        }
        if (this.getCamp() != Camp.forNumber(0)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }  else if (builder.hasCamp()) {
            // 清理Camp
            builder.clearCamp();
            fieldCnt++;
        }
        if (this.getMainCityId() != 0L) {
            builder.setMainCityId(this.getMainCityId());
            fieldCnt++;
        }  else if (builder.hasMainCityId()) {
            // 清理MainCityId
            builder.clearMainCityId();
            fieldCnt++;
        }
        if (this.dungeonSkill != null) {
            StructCommon.DungeonSkillSys.Builder tmpBuilder = StructCommon.DungeonSkillSys.newBuilder();
            final int tmpFieldCnt = this.dungeonSkill.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDungeonSkill(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDungeonSkill();
            }
        }  else if (builder.hasDungeonSkill()) {
            // 清理DungeonSkill
            builder.clearDungeonSkill();
            fieldCnt++;
        }
        if (this.wall != null) {
            StructPlayer.WallInfo.Builder tmpBuilder = StructPlayer.WallInfo.newBuilder();
            final int tmpFieldCnt = this.wall.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWall(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWall();
            }
        }  else if (builder.hasWall()) {
            // 清理Wall
            builder.clearWall();
            fieldCnt++;
        }
        if (this.hospitalModel != null) {
            Struct.ScenePlayerHospitalModel.Builder tmpBuilder = Struct.ScenePlayerHospitalModel.newBuilder();
            final int tmpFieldCnt = this.hospitalModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHospitalModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHospitalModel();
            }
        }  else if (builder.hasHospitalModel()) {
            // 清理HospitalModel
            builder.clearHospitalModel();
            fieldCnt++;
        }
        if (this.heroModel != null) {
            Player.PlayerHeroModel.Builder tmpBuilder = Player.PlayerHeroModel.newBuilder();
            final int tmpFieldCnt = this.heroModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHeroModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHeroModel();
            }
        }  else if (builder.hasHeroModel()) {
            // 清理HeroModel
            builder.clearHeroModel();
            fieldCnt++;
        }
        if (this.tech != null) {
            Player.PlayerTechnologyModel.Builder tmpBuilder = Player.PlayerTechnologyModel.newBuilder();
            final int tmpFieldCnt = this.tech.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTech(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTech();
            }
        }  else if (builder.hasTech()) {
            // 清理Tech
            builder.clearTech();
            fieldCnt++;
        }
        if (this.innerBuild != null) {
            StructPlayer.Int64InnerBuildMap.Builder tmpBuilder = StructPlayer.Int64InnerBuildMap.newBuilder();
            final int tmpFieldCnt = this.innerBuild.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInnerBuild(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInnerBuild();
            }
        }  else if (builder.hasInnerBuild()) {
            // 清理InnerBuild
            builder.clearInnerBuild();
            fieldCnt++;
        }
        if (this.techModel != null) {
            StructPlayer.ScenePlayerTechModel.Builder tmpBuilder = StructPlayer.ScenePlayerTechModel.newBuilder();
            final int tmpFieldCnt = this.techModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTechModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTechModel();
            }
        }  else if (builder.hasTechModel()) {
            // 清理TechModel
            builder.clearTechModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(DungeonPlayer.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIER) && this.soldier != null) {
            final boolean needClear = !builder.hasSoldier();
            final int tmpFieldCnt = this.soldier.copyChangeToDb(builder.getSoldierBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldier();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCENEDEVBUFFSYS) && this.sceneDevBuffSys != null) {
            final boolean needClear = !builder.hasSceneDevBuffSys();
            final int tmpFieldCnt = this.sceneDevBuffSys.copyChangeToDb(builder.getSceneDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSceneDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            final boolean needClear = !builder.hasAdditionSys();
            final int tmpFieldCnt = this.additionSys.copyChangeToDb(builder.getAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToDb(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARMYMODEL) && this.armyModel != null) {
            final boolean needClear = !builder.hasArmyModel();
            final int tmpFieldCnt = this.armyModel.copyChangeToDb(builder.getArmyModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmyModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_RALLYMODEL) && this.rallyModel != null) {
            final boolean needClear = !builder.hasRallyModel();
            final int tmpFieldCnt = this.rallyModel.copyChangeToDb(builder.getRallyModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRallyModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToDb(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAINCITYID)) {
            builder.setMainCityId(this.getMainCityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONSKILL) && this.dungeonSkill != null) {
            final boolean needClear = !builder.hasDungeonSkill();
            final int tmpFieldCnt = this.dungeonSkill.copyChangeToDb(builder.getDungeonSkillBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDungeonSkill();
            }
        }
        if (this.hasMark(FIELD_INDEX_WALL) && this.wall != null) {
            final boolean needClear = !builder.hasWall();
            final int tmpFieldCnt = this.wall.copyChangeToDb(builder.getWallBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWall();
            }
        }
        if (this.hasMark(FIELD_INDEX_HOSPITALMODEL) && this.hospitalModel != null) {
            final boolean needClear = !builder.hasHospitalModel();
            final int tmpFieldCnt = this.hospitalModel.copyChangeToDb(builder.getHospitalModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHospitalModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_HEROMODEL) && this.heroModel != null) {
            final boolean needClear = !builder.hasHeroModel();
            final int tmpFieldCnt = this.heroModel.copyChangeToDb(builder.getHeroModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHeroModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_TECH) && this.tech != null) {
            final boolean needClear = !builder.hasTech();
            final int tmpFieldCnt = this.tech.copyChangeToDb(builder.getTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTech();
            }
        }
        if (this.hasMark(FIELD_INDEX_INNERBUILD) && this.innerBuild != null) {
            final boolean needClear = !builder.hasInnerBuild();
            final int tmpFieldCnt = this.innerBuild.copyChangeToDb(builder.getInnerBuildBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerBuild();
            }
        }
        if (this.hasMark(FIELD_INDEX_TECHMODEL) && this.techModel != null) {
            final boolean needClear = !builder.hasTechModel();
            final int tmpFieldCnt = this.techModel.copyChangeToDb(builder.getTechModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTechModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(DungeonPlayer proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldier()) {
            this.getSoldier().mergeFromDb(proto.getSoldier());
        } else {
            if (this.soldier != null) {
                this.soldier.mergeFromDb(proto.getSoldier());
            }
        }
        if (proto.hasSceneDevBuffSys()) {
            this.getSceneDevBuffSys().mergeFromDb(proto.getSceneDevBuffSys());
        } else {
            if (this.sceneDevBuffSys != null) {
                this.sceneDevBuffSys.mergeFromDb(proto.getSceneDevBuffSys());
            }
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeFromDb(proto.getAdditionSys());
        } else {
            if (this.additionSys != null) {
                this.additionSys.mergeFromDb(proto.getAdditionSys());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromDb(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromDb(proto.getCardHead());
            }
        }
        if (proto.hasArmyModel()) {
            this.getArmyModel().mergeFromDb(proto.getArmyModel());
        } else {
            if (this.armyModel != null) {
                this.armyModel.mergeFromDb(proto.getArmyModel());
            }
        }
        if (proto.hasRallyModel()) {
            this.getRallyModel().mergeFromDb(proto.getRallyModel());
        } else {
            if (this.rallyModel != null) {
                this.rallyModel.mergeFromDb(proto.getRallyModel());
            }
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeFromDb(proto.getDevBuffSys());
        } else {
            if (this.devBuffSys != null) {
                this.devBuffSys.mergeFromDb(proto.getDevBuffSys());
            }
        }
        if (proto.hasCamp()) {
            this.innerSetCamp(proto.getCamp());
        } else {
            this.innerSetCamp(Camp.forNumber(0));
        }
        if (proto.hasMainCityId()) {
            this.innerSetMainCityId(proto.getMainCityId());
        } else {
            this.innerSetMainCityId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasDungeonSkill()) {
            this.getDungeonSkill().mergeFromDb(proto.getDungeonSkill());
        } else {
            if (this.dungeonSkill != null) {
                this.dungeonSkill.mergeFromDb(proto.getDungeonSkill());
            }
        }
        if (proto.hasWall()) {
            this.getWall().mergeFromDb(proto.getWall());
        } else {
            if (this.wall != null) {
                this.wall.mergeFromDb(proto.getWall());
            }
        }
        if (proto.hasHospitalModel()) {
            this.getHospitalModel().mergeFromDb(proto.getHospitalModel());
        } else {
            if (this.hospitalModel != null) {
                this.hospitalModel.mergeFromDb(proto.getHospitalModel());
            }
        }
        if (proto.hasHeroModel()) {
            this.getHeroModel().mergeFromDb(proto.getHeroModel());
        } else {
            if (this.heroModel != null) {
                this.heroModel.mergeFromDb(proto.getHeroModel());
            }
        }
        if (proto.hasTech()) {
            this.getTech().mergeFromDb(proto.getTech());
        } else {
            if (this.tech != null) {
                this.tech.mergeFromDb(proto.getTech());
            }
        }
        if (proto.hasInnerBuild()) {
            this.getInnerBuild().mergeFromDb(proto.getInnerBuild());
        } else {
            if (this.innerBuild != null) {
                this.innerBuild.mergeFromDb(proto.getInnerBuild());
            }
        }
        if (proto.hasTechModel()) {
            this.getTechModel().mergeFromDb(proto.getTechModel());
        } else {
            if (this.techModel != null) {
                this.techModel.mergeFromDb(proto.getTechModel());
            }
        }
        this.markAll();
        return DungeonPlayerProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(DungeonPlayer proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldier()) {
            this.getSoldier().mergeChangeFromDb(proto.getSoldier());
            fieldCnt++;
        }
        if (proto.hasSceneDevBuffSys()) {
            this.getSceneDevBuffSys().mergeChangeFromDb(proto.getSceneDevBuffSys());
            fieldCnt++;
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeChangeFromDb(proto.getAdditionSys());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromDb(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasArmyModel()) {
            this.getArmyModel().mergeChangeFromDb(proto.getArmyModel());
            fieldCnt++;
        }
        if (proto.hasRallyModel()) {
            this.getRallyModel().mergeChangeFromDb(proto.getRallyModel());
            fieldCnt++;
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeChangeFromDb(proto.getDevBuffSys());
            fieldCnt++;
        }
        if (proto.hasCamp()) {
            this.setCamp(proto.getCamp());
            fieldCnt++;
        }
        if (proto.hasMainCityId()) {
            this.setMainCityId(proto.getMainCityId());
            fieldCnt++;
        }
        if (proto.hasDungeonSkill()) {
            this.getDungeonSkill().mergeChangeFromDb(proto.getDungeonSkill());
            fieldCnt++;
        }
        if (proto.hasWall()) {
            this.getWall().mergeChangeFromDb(proto.getWall());
            fieldCnt++;
        }
        if (proto.hasHospitalModel()) {
            this.getHospitalModel().mergeChangeFromDb(proto.getHospitalModel());
            fieldCnt++;
        }
        if (proto.hasHeroModel()) {
            this.getHeroModel().mergeChangeFromDb(proto.getHeroModel());
            fieldCnt++;
        }
        if (proto.hasTech()) {
            this.getTech().mergeChangeFromDb(proto.getTech());
            fieldCnt++;
        }
        if (proto.hasInnerBuild()) {
            this.getInnerBuild().mergeChangeFromDb(proto.getInnerBuild());
            fieldCnt++;
        }
        if (proto.hasTechModel()) {
            this.getTechModel().mergeChangeFromDb(proto.getTechModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DungeonPlayer.Builder getCopySsBuilder() {
        final DungeonPlayer.Builder builder = DungeonPlayer.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(DungeonPlayer.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.soldier != null) {
            StructPlayer.ScenePlayerSoldier.Builder tmpBuilder = StructPlayer.ScenePlayerSoldier.newBuilder();
            final int tmpFieldCnt = this.soldier.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldier(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldier();
            }
        }  else if (builder.hasSoldier()) {
            // 清理Soldier
            builder.clearSoldier();
            fieldCnt++;
        }
        if (this.sceneDevBuffSys != null) {
            StructBattle.SceneDevBuffSys.Builder tmpBuilder = StructBattle.SceneDevBuffSys.newBuilder();
            final int tmpFieldCnt = this.sceneDevBuffSys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSceneDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSceneDevBuffSys();
            }
        }  else if (builder.hasSceneDevBuffSys()) {
            // 清理SceneDevBuffSys
            builder.clearSceneDevBuffSys();
            fieldCnt++;
        }
        if (this.additionSys != null) {
            Struct.AdditionSys.Builder tmpBuilder = Struct.AdditionSys.newBuilder();
            final int tmpFieldCnt = this.additionSys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionSys();
            }
        }  else if (builder.hasAdditionSys()) {
            // 清理AdditionSys
            builder.clearAdditionSys();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.armyModel != null) {
            Player.ScenePlayerArmyModel.Builder tmpBuilder = Player.ScenePlayerArmyModel.newBuilder();
            final int tmpFieldCnt = this.armyModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArmyModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArmyModel();
            }
        }  else if (builder.hasArmyModel()) {
            // 清理ArmyModel
            builder.clearArmyModel();
            fieldCnt++;
        }
        if (this.rallyModel != null) {
            Struct.ScenePlayerRallyBase.Builder tmpBuilder = Struct.ScenePlayerRallyBase.newBuilder();
            final int tmpFieldCnt = this.rallyModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRallyModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRallyModel();
            }
        }  else if (builder.hasRallyModel()) {
            // 清理RallyModel
            builder.clearRallyModel();
            fieldCnt++;
        }
        if (this.devBuffSys != null) {
            StructBattle.DevBuffSys.Builder tmpBuilder = StructBattle.DevBuffSys.newBuilder();
            final int tmpFieldCnt = this.devBuffSys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSys();
            }
        }  else if (builder.hasDevBuffSys()) {
            // 清理DevBuffSys
            builder.clearDevBuffSys();
            fieldCnt++;
        }
        if (this.getCamp() != Camp.forNumber(0)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }  else if (builder.hasCamp()) {
            // 清理Camp
            builder.clearCamp();
            fieldCnt++;
        }
        if (this.getMainCityId() != 0L) {
            builder.setMainCityId(this.getMainCityId());
            fieldCnt++;
        }  else if (builder.hasMainCityId()) {
            // 清理MainCityId
            builder.clearMainCityId();
            fieldCnt++;
        }
        if (this.dungeonSkill != null) {
            StructCommon.DungeonSkillSys.Builder tmpBuilder = StructCommon.DungeonSkillSys.newBuilder();
            final int tmpFieldCnt = this.dungeonSkill.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDungeonSkill(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDungeonSkill();
            }
        }  else if (builder.hasDungeonSkill()) {
            // 清理DungeonSkill
            builder.clearDungeonSkill();
            fieldCnt++;
        }
        if (this.warning != null) {
            Struct.WarningItemList.Builder tmpBuilder = Struct.WarningItemList.newBuilder();
            final int tmpFieldCnt = this.warning.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWarning(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWarning();
            }
        }  else if (builder.hasWarning()) {
            // 清理Warning
            builder.clearWarning();
            fieldCnt++;
        }
        if (this.wall != null) {
            StructPlayer.WallInfo.Builder tmpBuilder = StructPlayer.WallInfo.newBuilder();
            final int tmpFieldCnt = this.wall.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWall(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWall();
            }
        }  else if (builder.hasWall()) {
            // 清理Wall
            builder.clearWall();
            fieldCnt++;
        }
        if (this.hospitalModel != null) {
            Struct.ScenePlayerHospitalModel.Builder tmpBuilder = Struct.ScenePlayerHospitalModel.newBuilder();
            final int tmpFieldCnt = this.hospitalModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHospitalModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHospitalModel();
            }
        }  else if (builder.hasHospitalModel()) {
            // 清理HospitalModel
            builder.clearHospitalModel();
            fieldCnt++;
        }
        if (this.heroModel != null) {
            Player.PlayerHeroModel.Builder tmpBuilder = Player.PlayerHeroModel.newBuilder();
            final int tmpFieldCnt = this.heroModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHeroModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHeroModel();
            }
        }  else if (builder.hasHeroModel()) {
            // 清理HeroModel
            builder.clearHeroModel();
            fieldCnt++;
        }
        if (this.tech != null) {
            Player.PlayerTechnologyModel.Builder tmpBuilder = Player.PlayerTechnologyModel.newBuilder();
            final int tmpFieldCnt = this.tech.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTech(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTech();
            }
        }  else if (builder.hasTech()) {
            // 清理Tech
            builder.clearTech();
            fieldCnt++;
        }
        if (this.innerBuild != null) {
            StructPlayer.Int64InnerBuildMap.Builder tmpBuilder = StructPlayer.Int64InnerBuildMap.newBuilder();
            final int tmpFieldCnt = this.innerBuild.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInnerBuild(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInnerBuild();
            }
        }  else if (builder.hasInnerBuild()) {
            // 清理InnerBuild
            builder.clearInnerBuild();
            fieldCnt++;
        }
        if (this.techModel != null) {
            StructPlayer.ScenePlayerTechModel.Builder tmpBuilder = StructPlayer.ScenePlayerTechModel.newBuilder();
            final int tmpFieldCnt = this.techModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTechModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTechModel();
            }
        }  else if (builder.hasTechModel()) {
            // 清理TechModel
            builder.clearTechModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(DungeonPlayer.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIER) && this.soldier != null) {
            final boolean needClear = !builder.hasSoldier();
            final int tmpFieldCnt = this.soldier.copyChangeToSs(builder.getSoldierBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldier();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCENEDEVBUFFSYS) && this.sceneDevBuffSys != null) {
            final boolean needClear = !builder.hasSceneDevBuffSys();
            final int tmpFieldCnt = this.sceneDevBuffSys.copyChangeToSs(builder.getSceneDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSceneDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            final boolean needClear = !builder.hasAdditionSys();
            final int tmpFieldCnt = this.additionSys.copyChangeToSs(builder.getAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARMYMODEL) && this.armyModel != null) {
            final boolean needClear = !builder.hasArmyModel();
            final int tmpFieldCnt = this.armyModel.copyChangeToSs(builder.getArmyModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmyModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_RALLYMODEL) && this.rallyModel != null) {
            final boolean needClear = !builder.hasRallyModel();
            final int tmpFieldCnt = this.rallyModel.copyChangeToSs(builder.getRallyModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRallyModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToSs(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAINCITYID)) {
            builder.setMainCityId(this.getMainCityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONSKILL) && this.dungeonSkill != null) {
            final boolean needClear = !builder.hasDungeonSkill();
            final int tmpFieldCnt = this.dungeonSkill.copyChangeToSs(builder.getDungeonSkillBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDungeonSkill();
            }
        }
        if (this.hasMark(FIELD_INDEX_WARNING) && this.warning != null) {
            final boolean needClear = !builder.hasWarning();
            final int tmpFieldCnt = this.warning.copyChangeToSs(builder.getWarningBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWarning();
            }
        }
        if (this.hasMark(FIELD_INDEX_WALL) && this.wall != null) {
            final boolean needClear = !builder.hasWall();
            final int tmpFieldCnt = this.wall.copyChangeToSs(builder.getWallBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWall();
            }
        }
        if (this.hasMark(FIELD_INDEX_HOSPITALMODEL) && this.hospitalModel != null) {
            final boolean needClear = !builder.hasHospitalModel();
            final int tmpFieldCnt = this.hospitalModel.copyChangeToSs(builder.getHospitalModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHospitalModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_HEROMODEL) && this.heroModel != null) {
            final boolean needClear = !builder.hasHeroModel();
            final int tmpFieldCnt = this.heroModel.copyChangeToSs(builder.getHeroModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHeroModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_TECH) && this.tech != null) {
            final boolean needClear = !builder.hasTech();
            final int tmpFieldCnt = this.tech.copyChangeToSs(builder.getTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTech();
            }
        }
        if (this.hasMark(FIELD_INDEX_INNERBUILD) && this.innerBuild != null) {
            final boolean needClear = !builder.hasInnerBuild();
            final int tmpFieldCnt = this.innerBuild.copyChangeToSs(builder.getInnerBuildBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerBuild();
            }
        }
        if (this.hasMark(FIELD_INDEX_TECHMODEL) && this.techModel != null) {
            final boolean needClear = !builder.hasTechModel();
            final int tmpFieldCnt = this.techModel.copyChangeToSs(builder.getTechModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTechModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(DungeonPlayer proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldier()) {
            this.getSoldier().mergeFromSs(proto.getSoldier());
        } else {
            if (this.soldier != null) {
                this.soldier.mergeFromSs(proto.getSoldier());
            }
        }
        if (proto.hasSceneDevBuffSys()) {
            this.getSceneDevBuffSys().mergeFromSs(proto.getSceneDevBuffSys());
        } else {
            if (this.sceneDevBuffSys != null) {
                this.sceneDevBuffSys.mergeFromSs(proto.getSceneDevBuffSys());
            }
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeFromSs(proto.getAdditionSys());
        } else {
            if (this.additionSys != null) {
                this.additionSys.mergeFromSs(proto.getAdditionSys());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        if (proto.hasArmyModel()) {
            this.getArmyModel().mergeFromSs(proto.getArmyModel());
        } else {
            if (this.armyModel != null) {
                this.armyModel.mergeFromSs(proto.getArmyModel());
            }
        }
        if (proto.hasRallyModel()) {
            this.getRallyModel().mergeFromSs(proto.getRallyModel());
        } else {
            if (this.rallyModel != null) {
                this.rallyModel.mergeFromSs(proto.getRallyModel());
            }
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeFromSs(proto.getDevBuffSys());
        } else {
            if (this.devBuffSys != null) {
                this.devBuffSys.mergeFromSs(proto.getDevBuffSys());
            }
        }
        if (proto.hasCamp()) {
            this.innerSetCamp(proto.getCamp());
        } else {
            this.innerSetCamp(Camp.forNumber(0));
        }
        if (proto.hasMainCityId()) {
            this.innerSetMainCityId(proto.getMainCityId());
        } else {
            this.innerSetMainCityId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasDungeonSkill()) {
            this.getDungeonSkill().mergeFromSs(proto.getDungeonSkill());
        } else {
            if (this.dungeonSkill != null) {
                this.dungeonSkill.mergeFromSs(proto.getDungeonSkill());
            }
        }
        if (proto.hasWarning()) {
            this.getWarning().mergeFromSs(proto.getWarning());
        } else {
            if (this.warning != null) {
                this.warning.mergeFromSs(proto.getWarning());
            }
        }
        if (proto.hasWall()) {
            this.getWall().mergeFromSs(proto.getWall());
        } else {
            if (this.wall != null) {
                this.wall.mergeFromSs(proto.getWall());
            }
        }
        if (proto.hasHospitalModel()) {
            this.getHospitalModel().mergeFromSs(proto.getHospitalModel());
        } else {
            if (this.hospitalModel != null) {
                this.hospitalModel.mergeFromSs(proto.getHospitalModel());
            }
        }
        if (proto.hasHeroModel()) {
            this.getHeroModel().mergeFromSs(proto.getHeroModel());
        } else {
            if (this.heroModel != null) {
                this.heroModel.mergeFromSs(proto.getHeroModel());
            }
        }
        if (proto.hasTech()) {
            this.getTech().mergeFromSs(proto.getTech());
        } else {
            if (this.tech != null) {
                this.tech.mergeFromSs(proto.getTech());
            }
        }
        if (proto.hasInnerBuild()) {
            this.getInnerBuild().mergeFromSs(proto.getInnerBuild());
        } else {
            if (this.innerBuild != null) {
                this.innerBuild.mergeFromSs(proto.getInnerBuild());
            }
        }
        if (proto.hasTechModel()) {
            this.getTechModel().mergeFromSs(proto.getTechModel());
        } else {
            if (this.techModel != null) {
                this.techModel.mergeFromSs(proto.getTechModel());
            }
        }
        this.markAll();
        return DungeonPlayerProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(DungeonPlayer proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldier()) {
            this.getSoldier().mergeChangeFromSs(proto.getSoldier());
            fieldCnt++;
        }
        if (proto.hasSceneDevBuffSys()) {
            this.getSceneDevBuffSys().mergeChangeFromSs(proto.getSceneDevBuffSys());
            fieldCnt++;
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeChangeFromSs(proto.getAdditionSys());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasArmyModel()) {
            this.getArmyModel().mergeChangeFromSs(proto.getArmyModel());
            fieldCnt++;
        }
        if (proto.hasRallyModel()) {
            this.getRallyModel().mergeChangeFromSs(proto.getRallyModel());
            fieldCnt++;
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeChangeFromSs(proto.getDevBuffSys());
            fieldCnt++;
        }
        if (proto.hasCamp()) {
            this.setCamp(proto.getCamp());
            fieldCnt++;
        }
        if (proto.hasMainCityId()) {
            this.setMainCityId(proto.getMainCityId());
            fieldCnt++;
        }
        if (proto.hasDungeonSkill()) {
            this.getDungeonSkill().mergeChangeFromSs(proto.getDungeonSkill());
            fieldCnt++;
        }
        if (proto.hasWarning()) {
            this.getWarning().mergeChangeFromSs(proto.getWarning());
            fieldCnt++;
        }
        if (proto.hasWall()) {
            this.getWall().mergeChangeFromSs(proto.getWall());
            fieldCnt++;
        }
        if (proto.hasHospitalModel()) {
            this.getHospitalModel().mergeChangeFromSs(proto.getHospitalModel());
            fieldCnt++;
        }
        if (proto.hasHeroModel()) {
            this.getHeroModel().mergeChangeFromSs(proto.getHeroModel());
            fieldCnt++;
        }
        if (proto.hasTech()) {
            this.getTech().mergeChangeFromSs(proto.getTech());
            fieldCnt++;
        }
        if (proto.hasInnerBuild()) {
            this.getInnerBuild().mergeChangeFromSs(proto.getInnerBuild());
            fieldCnt++;
        }
        if (proto.hasTechModel()) {
            this.getTechModel().mergeChangeFromSs(proto.getTechModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        DungeonPlayer.Builder builder = DungeonPlayer.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIER) && this.soldier != null) {
            this.soldier.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SCENEDEVBUFFSYS) && this.sceneDevBuffSys != null) {
            this.sceneDevBuffSys.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            this.additionSys.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ARMYMODEL) && this.armyModel != null) {
            this.armyModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RALLYMODEL) && this.rallyModel != null) {
            this.rallyModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            this.devBuffSys.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONSKILL) && this.dungeonSkill != null) {
            this.dungeonSkill.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_WARNING) && this.warning != null) {
            this.warning.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_WALL) && this.wall != null) {
            this.wall.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_HOSPITALMODEL) && this.hospitalModel != null) {
            this.hospitalModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_HEROMODEL) && this.heroModel != null) {
            this.heroModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TECH) && this.tech != null) {
            this.tech.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_INNERBUILD) && this.innerBuild != null) {
            this.innerBuild.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TECHMODEL) && this.techModel != null) {
            this.techModel.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.soldier != null) {
            this.soldier.markAll();
        }
        if (this.sceneDevBuffSys != null) {
            this.sceneDevBuffSys.markAll();
        }
        if (this.additionSys != null) {
            this.additionSys.markAll();
        }
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        if (this.armyModel != null) {
            this.armyModel.markAll();
        }
        if (this.rallyModel != null) {
            this.rallyModel.markAll();
        }
        if (this.devBuffSys != null) {
            this.devBuffSys.markAll();
        }
        if (this.dungeonSkill != null) {
            this.dungeonSkill.markAll();
        }
        if (this.warning != null) {
            this.warning.markAll();
        }
        if (this.wall != null) {
            this.wall.markAll();
        }
        if (this.hospitalModel != null) {
            this.hospitalModel.markAll();
        }
        if (this.heroModel != null) {
            this.heroModel.markAll();
        }
        if (this.tech != null) {
            this.tech.markAll();
        }
        if (this.innerBuild != null) {
            this.innerBuild.markAll();
        }
        if (this.techModel != null) {
            this.techModel.markAll();
        }
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("DungeonPlayerProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("DungeonPlayerProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof DungeonPlayerProp)) {
            return false;
        }
        final DungeonPlayerProp otherNode = (DungeonPlayerProp) node;
        if (!this.getSoldier().compareDataTo(otherNode.getSoldier())) {
            return false;
        }
        if (!this.getSceneDevBuffSys().compareDataTo(otherNode.getSceneDevBuffSys())) {
            return false;
        }
        if (!this.getAdditionSys().compareDataTo(otherNode.getAdditionSys())) {
            return false;
        }
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        if (!this.getArmyModel().compareDataTo(otherNode.getArmyModel())) {
            return false;
        }
        if (!this.getRallyModel().compareDataTo(otherNode.getRallyModel())) {
            return false;
        }
        if (!this.getDevBuffSys().compareDataTo(otherNode.getDevBuffSys())) {
            return false;
        }
        if (this.camp != otherNode.camp) {
            return false;
        }
        if (this.mainCityId != otherNode.mainCityId) {
            return false;
        }
        if (!this.getDungeonSkill().compareDataTo(otherNode.getDungeonSkill())) {
            return false;
        }
        if (!this.getWarning().compareDataTo(otherNode.getWarning())) {
            return false;
        }
        if (!this.getWall().compareDataTo(otherNode.getWall())) {
            return false;
        }
        if (!this.getHospitalModel().compareDataTo(otherNode.getHospitalModel())) {
            return false;
        }
        if (!this.getHeroModel().compareDataTo(otherNode.getHeroModel())) {
            return false;
        }
        if (!this.getTech().compareDataTo(otherNode.getTech())) {
            return false;
        }
        if (!this.getInnerBuild().compareDataTo(otherNode.getInnerBuild())) {
            return false;
        }
        if (!this.getTechModel().compareDataTo(otherNode.getTechModel())) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 47;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
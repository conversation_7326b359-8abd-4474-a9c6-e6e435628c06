package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructMail.AddItemByMail;
import com.yorha.proto.StructMailPB.AddItemByMailPB;


/**
 * <AUTHOR> auto gen
 */
public class AddItemByMailProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ADDITEMWHENONLYFORSHOW = 0;
    public static final int FIELD_INDEX_ADDITEMREASON = 1;
    public static final int FIELD_INDEX_SUBREASON = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private boolean addItemWhenOnlyForShow = Constant.DEFAULT_BOOLEAN_VALUE;
    private Reason addItemReason = Reason.forNumber(0);
    private String subReason = Constant.DEFAULT_STR_VALUE;

    public AddItemByMailProp() {
        super(null, 0, FIELD_COUNT);
    }

    public AddItemByMailProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get addItemWhenOnlyForShow
     *
     * @return addItemWhenOnlyForShow value
     */
    public boolean getAddItemWhenOnlyForShow() {
        return this.addItemWhenOnlyForShow;
    }

    /**
     * set addItemWhenOnlyForShow && set marked
     *
     * @param addItemWhenOnlyForShow new value
     * @return current object
     */
    public AddItemByMailProp setAddItemWhenOnlyForShow(boolean addItemWhenOnlyForShow) {
        if (this.addItemWhenOnlyForShow != addItemWhenOnlyForShow) {
            this.mark(FIELD_INDEX_ADDITEMWHENONLYFORSHOW);
            this.addItemWhenOnlyForShow = addItemWhenOnlyForShow;
        }
        return this;
    }

    /**
     * inner set addItemWhenOnlyForShow
     *
     * @param addItemWhenOnlyForShow new value
     */
    private void innerSetAddItemWhenOnlyForShow(boolean addItemWhenOnlyForShow) {
        this.addItemWhenOnlyForShow = addItemWhenOnlyForShow;
    }

    /**
     * get addItemReason
     *
     * @return addItemReason value
     */
    public Reason getAddItemReason() {
        return this.addItemReason;
    }

    /**
     * set addItemReason && set marked
     *
     * @param addItemReason new value
     * @return current object
     */
    public AddItemByMailProp setAddItemReason(Reason addItemReason) {
        if (addItemReason == null) {
            throw new NullPointerException();
        }
        if (this.addItemReason != addItemReason) {
            this.mark(FIELD_INDEX_ADDITEMREASON);
            this.addItemReason = addItemReason;
        }
        return this;
    }

    /**
     * inner set addItemReason
     *
     * @param addItemReason new value
     */
    private void innerSetAddItemReason(Reason addItemReason) {
        this.addItemReason = addItemReason;
    }

    /**
     * get subReason
     *
     * @return subReason value
     */
    public String getSubReason() {
        return this.subReason;
    }

    /**
     * set subReason && set marked
     *
     * @param subReason new value
     * @return current object
     */
    public AddItemByMailProp setSubReason(String subReason) {
        if (subReason == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.subReason, subReason)) {
            this.mark(FIELD_INDEX_SUBREASON);
            this.subReason = subReason;
        }
        return this;
    }

    /**
     * inner set subReason
     *
     * @param subReason new value
     */
    private void innerSetSubReason(String subReason) {
        this.subReason = subReason;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public AddItemByMailPB.Builder getCopyCsBuilder() {
        final AddItemByMailPB.Builder builder = AddItemByMailPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(AddItemByMailPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getAddItemWhenOnlyForShow()) {
            builder.setAddItemWhenOnlyForShow(this.getAddItemWhenOnlyForShow());
            fieldCnt++;
        }  else if (builder.hasAddItemWhenOnlyForShow()) {
            // 清理AddItemWhenOnlyForShow
            builder.clearAddItemWhenOnlyForShow();
            fieldCnt++;
        }
        if (this.getAddItemReason() != Reason.forNumber(0)) {
            builder.setAddItemReason(this.getAddItemReason());
            fieldCnt++;
        }  else if (builder.hasAddItemReason()) {
            // 清理AddItemReason
            builder.clearAddItemReason();
            fieldCnt++;
        }
        if (!this.getSubReason().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSubReason(this.getSubReason());
            fieldCnt++;
        }  else if (builder.hasSubReason()) {
            // 清理SubReason
            builder.clearSubReason();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(AddItemByMailPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ADDITEMWHENONLYFORSHOW)) {
            builder.setAddItemWhenOnlyForShow(this.getAddItemWhenOnlyForShow());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ADDITEMREASON)) {
            builder.setAddItemReason(this.getAddItemReason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SUBREASON)) {
            builder.setSubReason(this.getSubReason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(AddItemByMailPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ADDITEMWHENONLYFORSHOW)) {
            builder.setAddItemWhenOnlyForShow(this.getAddItemWhenOnlyForShow());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ADDITEMREASON)) {
            builder.setAddItemReason(this.getAddItemReason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SUBREASON)) {
            builder.setSubReason(this.getSubReason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(AddItemByMailPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAddItemWhenOnlyForShow()) {
            this.innerSetAddItemWhenOnlyForShow(proto.getAddItemWhenOnlyForShow());
        } else {
            this.innerSetAddItemWhenOnlyForShow(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasAddItemReason()) {
            this.innerSetAddItemReason(proto.getAddItemReason());
        } else {
            this.innerSetAddItemReason(Reason.forNumber(0));
        }
        if (proto.hasSubReason()) {
            this.innerSetSubReason(proto.getSubReason());
        } else {
            this.innerSetSubReason(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return AddItemByMailProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(AddItemByMailPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAddItemWhenOnlyForShow()) {
            this.setAddItemWhenOnlyForShow(proto.getAddItemWhenOnlyForShow());
            fieldCnt++;
        }
        if (proto.hasAddItemReason()) {
            this.setAddItemReason(proto.getAddItemReason());
            fieldCnt++;
        }
        if (proto.hasSubReason()) {
            this.setSubReason(proto.getSubReason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public AddItemByMail.Builder getCopyDbBuilder() {
        final AddItemByMail.Builder builder = AddItemByMail.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(AddItemByMail.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getAddItemWhenOnlyForShow()) {
            builder.setAddItemWhenOnlyForShow(this.getAddItemWhenOnlyForShow());
            fieldCnt++;
        }  else if (builder.hasAddItemWhenOnlyForShow()) {
            // 清理AddItemWhenOnlyForShow
            builder.clearAddItemWhenOnlyForShow();
            fieldCnt++;
        }
        if (this.getAddItemReason() != Reason.forNumber(0)) {
            builder.setAddItemReason(this.getAddItemReason());
            fieldCnt++;
        }  else if (builder.hasAddItemReason()) {
            // 清理AddItemReason
            builder.clearAddItemReason();
            fieldCnt++;
        }
        if (!this.getSubReason().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSubReason(this.getSubReason());
            fieldCnt++;
        }  else if (builder.hasSubReason()) {
            // 清理SubReason
            builder.clearSubReason();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(AddItemByMail.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ADDITEMWHENONLYFORSHOW)) {
            builder.setAddItemWhenOnlyForShow(this.getAddItemWhenOnlyForShow());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ADDITEMREASON)) {
            builder.setAddItemReason(this.getAddItemReason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SUBREASON)) {
            builder.setSubReason(this.getSubReason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(AddItemByMail proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAddItemWhenOnlyForShow()) {
            this.innerSetAddItemWhenOnlyForShow(proto.getAddItemWhenOnlyForShow());
        } else {
            this.innerSetAddItemWhenOnlyForShow(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasAddItemReason()) {
            this.innerSetAddItemReason(proto.getAddItemReason());
        } else {
            this.innerSetAddItemReason(Reason.forNumber(0));
        }
        if (proto.hasSubReason()) {
            this.innerSetSubReason(proto.getSubReason());
        } else {
            this.innerSetSubReason(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return AddItemByMailProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(AddItemByMail proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAddItemWhenOnlyForShow()) {
            this.setAddItemWhenOnlyForShow(proto.getAddItemWhenOnlyForShow());
            fieldCnt++;
        }
        if (proto.hasAddItemReason()) {
            this.setAddItemReason(proto.getAddItemReason());
            fieldCnt++;
        }
        if (proto.hasSubReason()) {
            this.setSubReason(proto.getSubReason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public AddItemByMail.Builder getCopySsBuilder() {
        final AddItemByMail.Builder builder = AddItemByMail.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(AddItemByMail.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getAddItemWhenOnlyForShow()) {
            builder.setAddItemWhenOnlyForShow(this.getAddItemWhenOnlyForShow());
            fieldCnt++;
        }  else if (builder.hasAddItemWhenOnlyForShow()) {
            // 清理AddItemWhenOnlyForShow
            builder.clearAddItemWhenOnlyForShow();
            fieldCnt++;
        }
        if (this.getAddItemReason() != Reason.forNumber(0)) {
            builder.setAddItemReason(this.getAddItemReason());
            fieldCnt++;
        }  else if (builder.hasAddItemReason()) {
            // 清理AddItemReason
            builder.clearAddItemReason();
            fieldCnt++;
        }
        if (!this.getSubReason().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSubReason(this.getSubReason());
            fieldCnt++;
        }  else if (builder.hasSubReason()) {
            // 清理SubReason
            builder.clearSubReason();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(AddItemByMail.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ADDITEMWHENONLYFORSHOW)) {
            builder.setAddItemWhenOnlyForShow(this.getAddItemWhenOnlyForShow());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ADDITEMREASON)) {
            builder.setAddItemReason(this.getAddItemReason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SUBREASON)) {
            builder.setSubReason(this.getSubReason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(AddItemByMail proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAddItemWhenOnlyForShow()) {
            this.innerSetAddItemWhenOnlyForShow(proto.getAddItemWhenOnlyForShow());
        } else {
            this.innerSetAddItemWhenOnlyForShow(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasAddItemReason()) {
            this.innerSetAddItemReason(proto.getAddItemReason());
        } else {
            this.innerSetAddItemReason(Reason.forNumber(0));
        }
        if (proto.hasSubReason()) {
            this.innerSetSubReason(proto.getSubReason());
        } else {
            this.innerSetSubReason(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return AddItemByMailProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(AddItemByMail proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAddItemWhenOnlyForShow()) {
            this.setAddItemWhenOnlyForShow(proto.getAddItemWhenOnlyForShow());
            fieldCnt++;
        }
        if (proto.hasAddItemReason()) {
            this.setAddItemReason(proto.getAddItemReason());
            fieldCnt++;
        }
        if (proto.hasSubReason()) {
            this.setSubReason(proto.getSubReason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        AddItemByMail.Builder builder = AddItemByMail.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof AddItemByMailProp)) {
            return false;
        }
        final AddItemByMailProp otherNode = (AddItemByMailProp) node;
        if (this.addItemWhenOnlyForShow != otherNode.addItemWhenOnlyForShow) {
            return false;
        }
        if (this.addItemReason != otherNode.addItemReason) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.subReason, otherNode.subReason)) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
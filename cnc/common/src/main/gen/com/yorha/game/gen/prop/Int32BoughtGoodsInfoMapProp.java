 
package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.AbstractMapNode;
import com.yorha.proto.PlayerPB.Int32BoughtGoodsInfoMapPB;
import com.yorha.proto.PlayerPB.BoughtGoodsInfoPB;
import com.yorha.proto.Player.Int32BoughtGoodsInfoMap;
import com.yorha.proto.Player.BoughtGoodsInfo;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> auto gen
 */
public class Int32BoughtGoodsInfoMapProp extends AbstractMapNode<Integer, BoughtGoodsInfoProp> {
    /**
     * Creates a Int32BoughtGoodsInfoMapProp container
     *
     * @param parent     parent node
     * @param fieldIndex field index in parent node
     */
    public Int32BoughtGoodsInfoMapProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to Int32BoughtGoodsInfoMapProp
     *
     * @param k map key
     * @return new object
     */
    @Override
    public BoughtGoodsInfoProp addEmptyValue(Integer k) {
        BoughtGoodsInfoProp newProp = new BoughtGoodsInfoProp(null, DEFAULT_FIELD_INDEX);
        newProp.setConfigGoodsId(k);
        this.put(k, newProp);
        return newProp;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Int32BoughtGoodsInfoMapPB.Builder getCopyCsBuilder() {
        final Int32BoughtGoodsInfoMapPB.Builder builder = Int32BoughtGoodsInfoMapPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB.
     *
     * @param builder builder for protobuf PB
     * @return copied field count
     */
    public int copyToCs(Int32BoughtGoodsInfoMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0 || builder.hasClearFlag() || builder.getDeleteKeysCount() != 0) {
                builder.clear();
                return Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final Map.Entry<Integer, BoughtGoodsInfoProp> entry : this.entrySet()) {
            BoughtGoodsInfoPB.Builder itemBuilder = BoughtGoodsInfoPB.newBuilder();
            entry.getValue().copyToCs(itemBuilder);
            builder.putDatas(entry.getKey(), itemBuilder.build());
        }
        return Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
    }

    /**
     * copy data change to protobuf PB. clear first, then refresh, add at last.
     *
     * @param builder builder for protobuf PB
     * @return copied field count
     */
    public int copyChangeToCs(Int32BoughtGoodsInfoMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        if (this.isClearFlag()) {
            // clear builder, when clear flag true
            builder.clear();
            // set clear flag when not clear.
            builder.setClearFlag(this.isClearFlag());
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            final Set<Integer> deleteKeys = !builder.getClearFlag() ? new HashSet<>(builder.getDeleteKeysList()) : null;
            isChanged = true;
            boolean isDeleteKeysChanged = false;
            for(final Integer key : this.getRefreshKeys()) {
                builder.removeDatas(key);
                if (deleteKeys != null) {
                    isDeleteKeysChanged = deleteKeys.add(key) || isDeleteKeysChanged;
                }
            }
            // update delete key when builder not clear.
            if (isDeleteKeysChanged) {
                builder.clearDeleteKeys();
                if (!deleteKeys.isEmpty()) {
                    builder.addAllDeleteKeys(deleteKeys);
                }
            }
        }
        // put data when dirty
        for (final Integer key : this.getExistDirtyKeys()) {
            final BoughtGoodsInfoProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final BoughtGoodsInfoPB.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : BoughtGoodsInfoPB.newBuilder();
            final int changeCnt = oldValue.copyChangeToCs(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        return isChanged? Int32BoughtGoodsInfoMapProp.FIELD_COUNT: 0;
    }

    /**
     * copy data change to protobuf PB. clear first, then refresh, add at last. it wll clear clearFlag and deleteKeys.
     *
     * @param builder builder for protobuf PB
     * @return copied field count
     */
    public int copyChangeToAndClearDeleteKeysCs(Int32BoughtGoodsInfoMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        // clear builder, when clear flag true
        if (this.isClearFlag()) {
            builder.clear();
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            isChanged = true;
            for(final Integer key : this.getRefreshKeys()) {
                builder.removeDatas(key);
            }
        }
        // put data when dirty
        for (final Integer key : this.getExistDirtyKeys()) {
            final BoughtGoodsInfoProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final BoughtGoodsInfoPB.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : BoughtGoodsInfoPB.newBuilder();
            final int changeCnt = oldValue.copyChangeToAndClearDeleteKeysCs(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        if (isChanged) {
            builder.clearDeleteKeys();
            builder.clearClearFlag();
        }
        return isChanged? Int32BoughtGoodsInfoMapProp.FIELD_COUNT: 0;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(Int32BoughtGoodsInfoMapPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (final Map.Entry<Integer, BoughtGoodsInfoPB> entry : proto.getDatasMap().entrySet()) {
            this.addEmptyValue(entry.getKey()).mergeFromCs(entry.getValue());
        }
        this.markAll();
        return Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
    }

    /**
     * merge data change from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeChangeFromCs(Int32BoughtGoodsInfoMapPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int changeCnt = 0;
        if (proto.hasClearFlag() && proto.getClearFlag()) {
            this.clear();
            changeCnt = Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
        }
        if (proto.getDeleteKeysCount() > 0) {
            this.removeAll(proto.getDeleteKeysList());
            changeCnt = Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
        }
        for (final Map.Entry<Integer, BoughtGoodsInfoPB> entry : proto.getDatasMap().entrySet()) {
            if (this.containsKey(entry.getKey())) {
                this.get(entry.getKey()).mergeChangeFromCs(entry.getValue());
                changeCnt = Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
                continue;
            }
            this.addEmptyValue(entry.getKey()).mergeChangeFromCs(entry.getValue());
            changeCnt = Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
        }
        return changeCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Int32BoughtGoodsInfoMap.Builder getCopyDbBuilder() {
        final Int32BoughtGoodsInfoMap.Builder builder = Int32BoughtGoodsInfoMap.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf .
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyToDb(Int32BoughtGoodsInfoMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0 || builder.hasClearFlag() || builder.getDeleteKeysCount() != 0) {
                builder.clear();
                return Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final Map.Entry<Integer, BoughtGoodsInfoProp> entry : this.entrySet()) {
            BoughtGoodsInfo.Builder itemBuilder = BoughtGoodsInfo.newBuilder();
            entry.getValue().copyToDb(itemBuilder);
            builder.putDatas(entry.getKey(), itemBuilder.build());
        }
        return Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
    }

    /**
     * copy data change to protobuf . clear first, then refresh, add at last.
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyChangeToDb(Int32BoughtGoodsInfoMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        if (this.isClearFlag()) {
            // clear builder, when clear flag true
            builder.clear();
            // set clear flag when not clear.
            builder.setClearFlag(this.isClearFlag());
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            final Set<Integer> deleteKeys = !builder.getClearFlag() ? new HashSet<>(builder.getDeleteKeysList()) : null;
            isChanged = true;
            boolean isDeleteKeysChanged = false;
            for(final Integer key : this.getRefreshKeys()) {
                builder.removeDatas(key);
                if (deleteKeys != null) {
                    isDeleteKeysChanged = deleteKeys.add(key) || isDeleteKeysChanged;
                }
            }
            // update delete key when builder not clear.
            if (isDeleteKeysChanged) {
                builder.clearDeleteKeys();
                if (!deleteKeys.isEmpty()) {
                    builder.addAllDeleteKeys(deleteKeys);
                }
            }
        }
        // put data when dirty
        for (final Integer key : this.getExistDirtyKeys()) {
            final BoughtGoodsInfoProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final BoughtGoodsInfo.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : BoughtGoodsInfo.newBuilder();
            final int changeCnt = oldValue.copyChangeToDb(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        return isChanged? Int32BoughtGoodsInfoMapProp.FIELD_COUNT: 0;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(Int32BoughtGoodsInfoMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (final Map.Entry<Integer, BoughtGoodsInfo> entry : proto.getDatasMap().entrySet()) {
            this.addEmptyValue(entry.getKey()).mergeFromDb(entry.getValue());
        }
        this.markAll();
        return Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
    }

    /**
     * merge data change from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeChangeFromDb(Int32BoughtGoodsInfoMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int changeCnt = 0;
        if (proto.hasClearFlag() && proto.getClearFlag()) {
            this.clear();
            changeCnt = Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
        }
        if (proto.getDeleteKeysCount() > 0) {
            this.removeAll(proto.getDeleteKeysList());
            changeCnt = Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
        }
        for (final Map.Entry<Integer, BoughtGoodsInfo> entry : proto.getDatasMap().entrySet()) {
            if (this.containsKey(entry.getKey())) {
                this.get(entry.getKey()).mergeChangeFromDb(entry.getValue());
                changeCnt = Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
                continue;
            }
            this.addEmptyValue(entry.getKey()).mergeChangeFromDb(entry.getValue());
            changeCnt = Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
        }
        return changeCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Int32BoughtGoodsInfoMap.Builder getCopySsBuilder() {
        final Int32BoughtGoodsInfoMap.Builder builder = Int32BoughtGoodsInfoMap.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf .
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyToSs(Int32BoughtGoodsInfoMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0 || builder.hasClearFlag() || builder.getDeleteKeysCount() != 0) {
                builder.clear();
                return Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final Map.Entry<Integer, BoughtGoodsInfoProp> entry : this.entrySet()) {
            BoughtGoodsInfo.Builder itemBuilder = BoughtGoodsInfo.newBuilder();
            entry.getValue().copyToSs(itemBuilder);
            builder.putDatas(entry.getKey(), itemBuilder.build());
        }
        return Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
    }

    /**
     * copy data change to protobuf . clear first, then refresh, add at last.
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyChangeToSs(Int32BoughtGoodsInfoMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        if (this.isClearFlag()) {
            // clear builder, when clear flag true
            builder.clear();
            // set clear flag when not clear.
            builder.setClearFlag(this.isClearFlag());
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            final Set<Integer> deleteKeys = !builder.getClearFlag() ? new HashSet<>(builder.getDeleteKeysList()) : null;
            isChanged = true;
            boolean isDeleteKeysChanged = false;
            for(final Integer key : this.getRefreshKeys()) {
                builder.removeDatas(key);
                if (deleteKeys != null) {
                    isDeleteKeysChanged = deleteKeys.add(key) || isDeleteKeysChanged;
                }
            }
            // update delete key when builder not clear.
            if (isDeleteKeysChanged) {
                builder.clearDeleteKeys();
                if (!deleteKeys.isEmpty()) {
                    builder.addAllDeleteKeys(deleteKeys);
                }
            }
        }
        // put data when dirty
        for (final Integer key : this.getExistDirtyKeys()) {
            final BoughtGoodsInfoProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final BoughtGoodsInfo.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : BoughtGoodsInfo.newBuilder();
            final int changeCnt = oldValue.copyChangeToSs(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        return isChanged? Int32BoughtGoodsInfoMapProp.FIELD_COUNT: 0;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(Int32BoughtGoodsInfoMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (final Map.Entry<Integer, BoughtGoodsInfo> entry : proto.getDatasMap().entrySet()) {
            this.addEmptyValue(entry.getKey()).mergeFromSs(entry.getValue());
        }
        this.markAll();
        return Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
    }

    /**
     * merge data change from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeChangeFromSs(Int32BoughtGoodsInfoMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int changeCnt = 0;
        if (proto.hasClearFlag() && proto.getClearFlag()) {
            this.clear();
            changeCnt = Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
        }
        if (proto.getDeleteKeysCount() > 0) {
            this.removeAll(proto.getDeleteKeysList());
            changeCnt = Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
        }
        for (final Map.Entry<Integer, BoughtGoodsInfo> entry : proto.getDatasMap().entrySet()) {
            if (this.containsKey(entry.getKey())) {
                this.get(entry.getKey()).mergeChangeFromSs(entry.getValue());
                changeCnt = Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
                continue;
            }
            this.addEmptyValue(entry.getKey()).mergeChangeFromSs(entry.getValue());
            changeCnt = Int32BoughtGoodsInfoMapProp.FIELD_COUNT;
        }
        return changeCnt;
    }

    @Override
    public String toString() {
        Int32BoughtGoodsInfoMap.Builder builder = Int32BoughtGoodsInfoMap.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructMail.MailClanInviteData;
import com.yorha.proto.StructClan;
import com.yorha.proto.StructMailPB.MailClanInviteDataPB;
import com.yorha.proto.StructClanPB;


/**
 * <AUTHOR> auto gen
 */
public class MailClanInviteDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_INVITECLANID = 0;
    public static final int FIELD_INDEX_INVITEPLAYERNAME = 1;
    public static final int FIELD_INDEX_INVITECLANSIMPLENAME = 2;
    public static final int FIELD_INDEX_INVITECLANNAME = 3;
    public static final int FIELD_INDEX_INVITEFLAGINFO = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private long inviteClanId = Constant.DEFAULT_LONG_VALUE;
    private String invitePlayerName = Constant.DEFAULT_STR_VALUE;
    private String inviteClanSimpleName = Constant.DEFAULT_STR_VALUE;
    private String inviteClanName = Constant.DEFAULT_STR_VALUE;
    private ClanFlagInfoProp inviteFlagInfo = null;

    public MailClanInviteDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MailClanInviteDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get inviteClanId
     *
     * @return inviteClanId value
     */
    public long getInviteClanId() {
        return this.inviteClanId;
    }

    /**
     * set inviteClanId && set marked
     *
     * @param inviteClanId new value
     * @return current object
     */
    public MailClanInviteDataProp setInviteClanId(long inviteClanId) {
        if (this.inviteClanId != inviteClanId) {
            this.mark(FIELD_INDEX_INVITECLANID);
            this.inviteClanId = inviteClanId;
        }
        return this;
    }

    /**
     * inner set inviteClanId
     *
     * @param inviteClanId new value
     */
    private void innerSetInviteClanId(long inviteClanId) {
        this.inviteClanId = inviteClanId;
    }

    /**
     * get invitePlayerName
     *
     * @return invitePlayerName value
     */
    public String getInvitePlayerName() {
        return this.invitePlayerName;
    }

    /**
     * set invitePlayerName && set marked
     *
     * @param invitePlayerName new value
     * @return current object
     */
    public MailClanInviteDataProp setInvitePlayerName(String invitePlayerName) {
        if (invitePlayerName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.invitePlayerName, invitePlayerName)) {
            this.mark(FIELD_INDEX_INVITEPLAYERNAME);
            this.invitePlayerName = invitePlayerName;
        }
        return this;
    }

    /**
     * inner set invitePlayerName
     *
     * @param invitePlayerName new value
     */
    private void innerSetInvitePlayerName(String invitePlayerName) {
        this.invitePlayerName = invitePlayerName;
    }

    /**
     * get inviteClanSimpleName
     *
     * @return inviteClanSimpleName value
     */
    public String getInviteClanSimpleName() {
        return this.inviteClanSimpleName;
    }

    /**
     * set inviteClanSimpleName && set marked
     *
     * @param inviteClanSimpleName new value
     * @return current object
     */
    public MailClanInviteDataProp setInviteClanSimpleName(String inviteClanSimpleName) {
        if (inviteClanSimpleName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.inviteClanSimpleName, inviteClanSimpleName)) {
            this.mark(FIELD_INDEX_INVITECLANSIMPLENAME);
            this.inviteClanSimpleName = inviteClanSimpleName;
        }
        return this;
    }

    /**
     * inner set inviteClanSimpleName
     *
     * @param inviteClanSimpleName new value
     */
    private void innerSetInviteClanSimpleName(String inviteClanSimpleName) {
        this.inviteClanSimpleName = inviteClanSimpleName;
    }

    /**
     * get inviteClanName
     *
     * @return inviteClanName value
     */
    public String getInviteClanName() {
        return this.inviteClanName;
    }

    /**
     * set inviteClanName && set marked
     *
     * @param inviteClanName new value
     * @return current object
     */
    public MailClanInviteDataProp setInviteClanName(String inviteClanName) {
        if (inviteClanName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.inviteClanName, inviteClanName)) {
            this.mark(FIELD_INDEX_INVITECLANNAME);
            this.inviteClanName = inviteClanName;
        }
        return this;
    }

    /**
     * inner set inviteClanName
     *
     * @param inviteClanName new value
     */
    private void innerSetInviteClanName(String inviteClanName) {
        this.inviteClanName = inviteClanName;
    }

    /**
     * get inviteFlagInfo
     *
     * @return inviteFlagInfo value
     */
    public ClanFlagInfoProp getInviteFlagInfo() {
        if (this.inviteFlagInfo == null) {
            this.inviteFlagInfo = new ClanFlagInfoProp(this, FIELD_INDEX_INVITEFLAGINFO);
        }
        return this.inviteFlagInfo;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailClanInviteDataPB.Builder getCopyCsBuilder() {
        final MailClanInviteDataPB.Builder builder = MailClanInviteDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MailClanInviteDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getInviteClanId() != 0L) {
            builder.setInviteClanId(this.getInviteClanId());
            fieldCnt++;
        }  else if (builder.hasInviteClanId()) {
            // 清理InviteClanId
            builder.clearInviteClanId();
            fieldCnt++;
        }
        if (!this.getInvitePlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setInvitePlayerName(this.getInvitePlayerName());
            fieldCnt++;
        }  else if (builder.hasInvitePlayerName()) {
            // 清理InvitePlayerName
            builder.clearInvitePlayerName();
            fieldCnt++;
        }
        if (!this.getInviteClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setInviteClanSimpleName(this.getInviteClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasInviteClanSimpleName()) {
            // 清理InviteClanSimpleName
            builder.clearInviteClanSimpleName();
            fieldCnt++;
        }
        if (!this.getInviteClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setInviteClanName(this.getInviteClanName());
            fieldCnt++;
        }  else if (builder.hasInviteClanName()) {
            // 清理InviteClanName
            builder.clearInviteClanName();
            fieldCnt++;
        }
        if (this.inviteFlagInfo != null) {
            StructClanPB.ClanFlagInfoPB.Builder tmpBuilder = StructClanPB.ClanFlagInfoPB.newBuilder();
            final int tmpFieldCnt = this.inviteFlagInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInviteFlagInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInviteFlagInfo();
            }
        }  else if (builder.hasInviteFlagInfo()) {
            // 清理InviteFlagInfo
            builder.clearInviteFlagInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MailClanInviteDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INVITECLANID)) {
            builder.setInviteClanId(this.getInviteClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITEPLAYERNAME)) {
            builder.setInvitePlayerName(this.getInvitePlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITECLANSIMPLENAME)) {
            builder.setInviteClanSimpleName(this.getInviteClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITECLANNAME)) {
            builder.setInviteClanName(this.getInviteClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITEFLAGINFO) && this.inviteFlagInfo != null) {
            final boolean needClear = !builder.hasInviteFlagInfo();
            final int tmpFieldCnt = this.inviteFlagInfo.copyChangeToCs(builder.getInviteFlagInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInviteFlagInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MailClanInviteDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INVITECLANID)) {
            builder.setInviteClanId(this.getInviteClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITEPLAYERNAME)) {
            builder.setInvitePlayerName(this.getInvitePlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITECLANSIMPLENAME)) {
            builder.setInviteClanSimpleName(this.getInviteClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITECLANNAME)) {
            builder.setInviteClanName(this.getInviteClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITEFLAGINFO) && this.inviteFlagInfo != null) {
            final boolean needClear = !builder.hasInviteFlagInfo();
            final int tmpFieldCnt = this.inviteFlagInfo.copyChangeToAndClearDeleteKeysCs(builder.getInviteFlagInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInviteFlagInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MailClanInviteDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasInviteClanId()) {
            this.innerSetInviteClanId(proto.getInviteClanId());
        } else {
            this.innerSetInviteClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasInvitePlayerName()) {
            this.innerSetInvitePlayerName(proto.getInvitePlayerName());
        } else {
            this.innerSetInvitePlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasInviteClanSimpleName()) {
            this.innerSetInviteClanSimpleName(proto.getInviteClanSimpleName());
        } else {
            this.innerSetInviteClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasInviteClanName()) {
            this.innerSetInviteClanName(proto.getInviteClanName());
        } else {
            this.innerSetInviteClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasInviteFlagInfo()) {
            this.getInviteFlagInfo().mergeFromCs(proto.getInviteFlagInfo());
        } else {
            if (this.inviteFlagInfo != null) {
                this.inviteFlagInfo.mergeFromCs(proto.getInviteFlagInfo());
            }
        }
        this.markAll();
        return MailClanInviteDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MailClanInviteDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasInviteClanId()) {
            this.setInviteClanId(proto.getInviteClanId());
            fieldCnt++;
        }
        if (proto.hasInvitePlayerName()) {
            this.setInvitePlayerName(proto.getInvitePlayerName());
            fieldCnt++;
        }
        if (proto.hasInviteClanSimpleName()) {
            this.setInviteClanSimpleName(proto.getInviteClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasInviteClanName()) {
            this.setInviteClanName(proto.getInviteClanName());
            fieldCnt++;
        }
        if (proto.hasInviteFlagInfo()) {
            this.getInviteFlagInfo().mergeChangeFromCs(proto.getInviteFlagInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailClanInviteData.Builder getCopyDbBuilder() {
        final MailClanInviteData.Builder builder = MailClanInviteData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MailClanInviteData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getInviteClanId() != 0L) {
            builder.setInviteClanId(this.getInviteClanId());
            fieldCnt++;
        }  else if (builder.hasInviteClanId()) {
            // 清理InviteClanId
            builder.clearInviteClanId();
            fieldCnt++;
        }
        if (!this.getInvitePlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setInvitePlayerName(this.getInvitePlayerName());
            fieldCnt++;
        }  else if (builder.hasInvitePlayerName()) {
            // 清理InvitePlayerName
            builder.clearInvitePlayerName();
            fieldCnt++;
        }
        if (!this.getInviteClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setInviteClanSimpleName(this.getInviteClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasInviteClanSimpleName()) {
            // 清理InviteClanSimpleName
            builder.clearInviteClanSimpleName();
            fieldCnt++;
        }
        if (!this.getInviteClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setInviteClanName(this.getInviteClanName());
            fieldCnt++;
        }  else if (builder.hasInviteClanName()) {
            // 清理InviteClanName
            builder.clearInviteClanName();
            fieldCnt++;
        }
        if (this.inviteFlagInfo != null) {
            StructClan.ClanFlagInfo.Builder tmpBuilder = StructClan.ClanFlagInfo.newBuilder();
            final int tmpFieldCnt = this.inviteFlagInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInviteFlagInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInviteFlagInfo();
            }
        }  else if (builder.hasInviteFlagInfo()) {
            // 清理InviteFlagInfo
            builder.clearInviteFlagInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MailClanInviteData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INVITECLANID)) {
            builder.setInviteClanId(this.getInviteClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITEPLAYERNAME)) {
            builder.setInvitePlayerName(this.getInvitePlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITECLANSIMPLENAME)) {
            builder.setInviteClanSimpleName(this.getInviteClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITECLANNAME)) {
            builder.setInviteClanName(this.getInviteClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITEFLAGINFO) && this.inviteFlagInfo != null) {
            final boolean needClear = !builder.hasInviteFlagInfo();
            final int tmpFieldCnt = this.inviteFlagInfo.copyChangeToDb(builder.getInviteFlagInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInviteFlagInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MailClanInviteData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasInviteClanId()) {
            this.innerSetInviteClanId(proto.getInviteClanId());
        } else {
            this.innerSetInviteClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasInvitePlayerName()) {
            this.innerSetInvitePlayerName(proto.getInvitePlayerName());
        } else {
            this.innerSetInvitePlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasInviteClanSimpleName()) {
            this.innerSetInviteClanSimpleName(proto.getInviteClanSimpleName());
        } else {
            this.innerSetInviteClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasInviteClanName()) {
            this.innerSetInviteClanName(proto.getInviteClanName());
        } else {
            this.innerSetInviteClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasInviteFlagInfo()) {
            this.getInviteFlagInfo().mergeFromDb(proto.getInviteFlagInfo());
        } else {
            if (this.inviteFlagInfo != null) {
                this.inviteFlagInfo.mergeFromDb(proto.getInviteFlagInfo());
            }
        }
        this.markAll();
        return MailClanInviteDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MailClanInviteData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasInviteClanId()) {
            this.setInviteClanId(proto.getInviteClanId());
            fieldCnt++;
        }
        if (proto.hasInvitePlayerName()) {
            this.setInvitePlayerName(proto.getInvitePlayerName());
            fieldCnt++;
        }
        if (proto.hasInviteClanSimpleName()) {
            this.setInviteClanSimpleName(proto.getInviteClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasInviteClanName()) {
            this.setInviteClanName(proto.getInviteClanName());
            fieldCnt++;
        }
        if (proto.hasInviteFlagInfo()) {
            this.getInviteFlagInfo().mergeChangeFromDb(proto.getInviteFlagInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailClanInviteData.Builder getCopySsBuilder() {
        final MailClanInviteData.Builder builder = MailClanInviteData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MailClanInviteData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getInviteClanId() != 0L) {
            builder.setInviteClanId(this.getInviteClanId());
            fieldCnt++;
        }  else if (builder.hasInviteClanId()) {
            // 清理InviteClanId
            builder.clearInviteClanId();
            fieldCnt++;
        }
        if (!this.getInvitePlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setInvitePlayerName(this.getInvitePlayerName());
            fieldCnt++;
        }  else if (builder.hasInvitePlayerName()) {
            // 清理InvitePlayerName
            builder.clearInvitePlayerName();
            fieldCnt++;
        }
        if (!this.getInviteClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setInviteClanSimpleName(this.getInviteClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasInviteClanSimpleName()) {
            // 清理InviteClanSimpleName
            builder.clearInviteClanSimpleName();
            fieldCnt++;
        }
        if (!this.getInviteClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setInviteClanName(this.getInviteClanName());
            fieldCnt++;
        }  else if (builder.hasInviteClanName()) {
            // 清理InviteClanName
            builder.clearInviteClanName();
            fieldCnt++;
        }
        if (this.inviteFlagInfo != null) {
            StructClan.ClanFlagInfo.Builder tmpBuilder = StructClan.ClanFlagInfo.newBuilder();
            final int tmpFieldCnt = this.inviteFlagInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInviteFlagInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInviteFlagInfo();
            }
        }  else if (builder.hasInviteFlagInfo()) {
            // 清理InviteFlagInfo
            builder.clearInviteFlagInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MailClanInviteData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INVITECLANID)) {
            builder.setInviteClanId(this.getInviteClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITEPLAYERNAME)) {
            builder.setInvitePlayerName(this.getInvitePlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITECLANSIMPLENAME)) {
            builder.setInviteClanSimpleName(this.getInviteClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITECLANNAME)) {
            builder.setInviteClanName(this.getInviteClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITEFLAGINFO) && this.inviteFlagInfo != null) {
            final boolean needClear = !builder.hasInviteFlagInfo();
            final int tmpFieldCnt = this.inviteFlagInfo.copyChangeToSs(builder.getInviteFlagInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInviteFlagInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MailClanInviteData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasInviteClanId()) {
            this.innerSetInviteClanId(proto.getInviteClanId());
        } else {
            this.innerSetInviteClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasInvitePlayerName()) {
            this.innerSetInvitePlayerName(proto.getInvitePlayerName());
        } else {
            this.innerSetInvitePlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasInviteClanSimpleName()) {
            this.innerSetInviteClanSimpleName(proto.getInviteClanSimpleName());
        } else {
            this.innerSetInviteClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasInviteClanName()) {
            this.innerSetInviteClanName(proto.getInviteClanName());
        } else {
            this.innerSetInviteClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasInviteFlagInfo()) {
            this.getInviteFlagInfo().mergeFromSs(proto.getInviteFlagInfo());
        } else {
            if (this.inviteFlagInfo != null) {
                this.inviteFlagInfo.mergeFromSs(proto.getInviteFlagInfo());
            }
        }
        this.markAll();
        return MailClanInviteDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MailClanInviteData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasInviteClanId()) {
            this.setInviteClanId(proto.getInviteClanId());
            fieldCnt++;
        }
        if (proto.hasInvitePlayerName()) {
            this.setInvitePlayerName(proto.getInvitePlayerName());
            fieldCnt++;
        }
        if (proto.hasInviteClanSimpleName()) {
            this.setInviteClanSimpleName(proto.getInviteClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasInviteClanName()) {
            this.setInviteClanName(proto.getInviteClanName());
            fieldCnt++;
        }
        if (proto.hasInviteFlagInfo()) {
            this.getInviteFlagInfo().mergeChangeFromSs(proto.getInviteFlagInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MailClanInviteData.Builder builder = MailClanInviteData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_INVITEFLAGINFO) && this.inviteFlagInfo != null) {
            this.inviteFlagInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.inviteFlagInfo != null) {
            this.inviteFlagInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MailClanInviteDataProp)) {
            return false;
        }
        final MailClanInviteDataProp otherNode = (MailClanInviteDataProp) node;
        if (this.inviteClanId != otherNode.inviteClanId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.invitePlayerName, otherNode.invitePlayerName)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.inviteClanSimpleName, otherNode.inviteClanSimpleName)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.inviteClanName, otherNode.inviteClanName)) {
            return false;
        }
        if (!this.getInviteFlagInfo().compareDataTo(otherNode.getInviteFlagInfo())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
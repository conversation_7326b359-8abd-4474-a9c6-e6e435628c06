package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.ScenePowerModel;
import com.yorha.proto.PlayerPB.ScenePowerModelPB;


/**
 * <AUTHOR> auto gen
 */
public class ScenePowerModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TOTALSOLDIERPOWER = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private long totalSoldierPower = Constant.DEFAULT_LONG_VALUE;

    public ScenePowerModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ScenePowerModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get totalSoldierPower
     *
     * @return totalSoldierPower value
     */
    public long getTotalSoldierPower() {
        return this.totalSoldierPower;
    }

    /**
     * set totalSoldierPower && set marked
     *
     * @param totalSoldierPower new value
     * @return current object
     */
    public ScenePowerModelProp setTotalSoldierPower(long totalSoldierPower) {
        if (this.totalSoldierPower != totalSoldierPower) {
            this.mark(FIELD_INDEX_TOTALSOLDIERPOWER);
            this.totalSoldierPower = totalSoldierPower;
        }
        return this;
    }

    /**
     * inner set totalSoldierPower
     *
     * @param totalSoldierPower new value
     */
    private void innerSetTotalSoldierPower(long totalSoldierPower) {
        this.totalSoldierPower = totalSoldierPower;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePowerModelPB.Builder getCopyCsBuilder() {
        final ScenePowerModelPB.Builder builder = ScenePowerModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ScenePowerModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTotalSoldierPower() != 0L) {
            builder.setTotalSoldierPower(this.getTotalSoldierPower());
            fieldCnt++;
        }  else if (builder.hasTotalSoldierPower()) {
            // 清理TotalSoldierPower
            builder.clearTotalSoldierPower();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ScenePowerModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOTALSOLDIERPOWER)) {
            builder.setTotalSoldierPower(this.getTotalSoldierPower());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ScenePowerModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOTALSOLDIERPOWER)) {
            builder.setTotalSoldierPower(this.getTotalSoldierPower());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ScenePowerModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTotalSoldierPower()) {
            this.innerSetTotalSoldierPower(proto.getTotalSoldierPower());
        } else {
            this.innerSetTotalSoldierPower(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ScenePowerModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ScenePowerModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTotalSoldierPower()) {
            this.setTotalSoldierPower(proto.getTotalSoldierPower());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePowerModel.Builder getCopyDbBuilder() {
        final ScenePowerModel.Builder builder = ScenePowerModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ScenePowerModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTotalSoldierPower() != 0L) {
            builder.setTotalSoldierPower(this.getTotalSoldierPower());
            fieldCnt++;
        }  else if (builder.hasTotalSoldierPower()) {
            // 清理TotalSoldierPower
            builder.clearTotalSoldierPower();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ScenePowerModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOTALSOLDIERPOWER)) {
            builder.setTotalSoldierPower(this.getTotalSoldierPower());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ScenePowerModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTotalSoldierPower()) {
            this.innerSetTotalSoldierPower(proto.getTotalSoldierPower());
        } else {
            this.innerSetTotalSoldierPower(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ScenePowerModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ScenePowerModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTotalSoldierPower()) {
            this.setTotalSoldierPower(proto.getTotalSoldierPower());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePowerModel.Builder getCopySsBuilder() {
        final ScenePowerModel.Builder builder = ScenePowerModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ScenePowerModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTotalSoldierPower() != 0L) {
            builder.setTotalSoldierPower(this.getTotalSoldierPower());
            fieldCnt++;
        }  else if (builder.hasTotalSoldierPower()) {
            // 清理TotalSoldierPower
            builder.clearTotalSoldierPower();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ScenePowerModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOTALSOLDIERPOWER)) {
            builder.setTotalSoldierPower(this.getTotalSoldierPower());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ScenePowerModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTotalSoldierPower()) {
            this.innerSetTotalSoldierPower(proto.getTotalSoldierPower());
        } else {
            this.innerSetTotalSoldierPower(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ScenePowerModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ScenePowerModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTotalSoldierPower()) {
            this.setTotalSoldierPower(proto.getTotalSoldierPower());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ScenePowerModel.Builder builder = ScenePowerModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ScenePowerModelProp)) {
            return false;
        }
        final ScenePowerModelProp otherNode = (ScenePowerModelProp) node;
        if (this.totalSoldierPower != otherNode.totalSoldierPower) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
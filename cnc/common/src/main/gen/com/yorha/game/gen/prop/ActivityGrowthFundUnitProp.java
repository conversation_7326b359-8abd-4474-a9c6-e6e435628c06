package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityGrowthFundUnit;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ActivityGrowthFundUnitPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityGrowthFundUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TASKUNIT = 0;
    public static final int FIELD_INDEX_PAYTASKUNLOCKED = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private ActivityTaskUnitProp taskUnit = null;
    private boolean payTaskUnlocked = Constant.DEFAULT_BOOLEAN_VALUE;

    public ActivityGrowthFundUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityGrowthFundUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get taskUnit
     *
     * @return taskUnit value
     */
    public ActivityTaskUnitProp getTaskUnit() {
        if (this.taskUnit == null) {
            this.taskUnit = new ActivityTaskUnitProp(this, FIELD_INDEX_TASKUNIT);
        }
        return this.taskUnit;
    }

    /**
     * get payTaskUnlocked
     *
     * @return payTaskUnlocked value
     */
    public boolean getPayTaskUnlocked() {
        return this.payTaskUnlocked;
    }

    /**
     * set payTaskUnlocked && set marked
     *
     * @param payTaskUnlocked new value
     * @return current object
     */
    public ActivityGrowthFundUnitProp setPayTaskUnlocked(boolean payTaskUnlocked) {
        if (this.payTaskUnlocked != payTaskUnlocked) {
            this.mark(FIELD_INDEX_PAYTASKUNLOCKED);
            this.payTaskUnlocked = payTaskUnlocked;
        }
        return this;
    }

    /**
     * inner set payTaskUnlocked
     *
     * @param payTaskUnlocked new value
     */
    private void innerSetPayTaskUnlocked(boolean payTaskUnlocked) {
        this.payTaskUnlocked = payTaskUnlocked;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityGrowthFundUnitPB.Builder getCopyCsBuilder() {
        final ActivityGrowthFundUnitPB.Builder builder = ActivityGrowthFundUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityGrowthFundUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.taskUnit != null) {
            StructPB.ActivityTaskUnitPB.Builder tmpBuilder = StructPB.ActivityTaskUnitPB.newBuilder();
            final int tmpFieldCnt = this.taskUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskUnit();
            }
        }  else if (builder.hasTaskUnit()) {
            // 清理TaskUnit
            builder.clearTaskUnit();
            fieldCnt++;
        }
        if (this.getPayTaskUnlocked()) {
            builder.setPayTaskUnlocked(this.getPayTaskUnlocked());
            fieldCnt++;
        }  else if (builder.hasPayTaskUnlocked()) {
            // 清理PayTaskUnlocked
            builder.clearPayTaskUnlocked();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityGrowthFundUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TASKUNIT) && this.taskUnit != null) {
            final boolean needClear = !builder.hasTaskUnit();
            final int tmpFieldCnt = this.taskUnit.copyChangeToCs(builder.getTaskUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_PAYTASKUNLOCKED)) {
            builder.setPayTaskUnlocked(this.getPayTaskUnlocked());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityGrowthFundUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TASKUNIT) && this.taskUnit != null) {
            final boolean needClear = !builder.hasTaskUnit();
            final int tmpFieldCnt = this.taskUnit.copyChangeToAndClearDeleteKeysCs(builder.getTaskUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_PAYTASKUNLOCKED)) {
            builder.setPayTaskUnlocked(this.getPayTaskUnlocked());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityGrowthFundUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTaskUnit()) {
            this.getTaskUnit().mergeFromCs(proto.getTaskUnit());
        } else {
            if (this.taskUnit != null) {
                this.taskUnit.mergeFromCs(proto.getTaskUnit());
            }
        }
        if (proto.hasPayTaskUnlocked()) {
            this.innerSetPayTaskUnlocked(proto.getPayTaskUnlocked());
        } else {
            this.innerSetPayTaskUnlocked(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ActivityGrowthFundUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityGrowthFundUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTaskUnit()) {
            this.getTaskUnit().mergeChangeFromCs(proto.getTaskUnit());
            fieldCnt++;
        }
        if (proto.hasPayTaskUnlocked()) {
            this.setPayTaskUnlocked(proto.getPayTaskUnlocked());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityGrowthFundUnit.Builder getCopyDbBuilder() {
        final ActivityGrowthFundUnit.Builder builder = ActivityGrowthFundUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityGrowthFundUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.taskUnit != null) {
            Struct.ActivityTaskUnit.Builder tmpBuilder = Struct.ActivityTaskUnit.newBuilder();
            final int tmpFieldCnt = this.taskUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskUnit();
            }
        }  else if (builder.hasTaskUnit()) {
            // 清理TaskUnit
            builder.clearTaskUnit();
            fieldCnt++;
        }
        if (this.getPayTaskUnlocked()) {
            builder.setPayTaskUnlocked(this.getPayTaskUnlocked());
            fieldCnt++;
        }  else if (builder.hasPayTaskUnlocked()) {
            // 清理PayTaskUnlocked
            builder.clearPayTaskUnlocked();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityGrowthFundUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TASKUNIT) && this.taskUnit != null) {
            final boolean needClear = !builder.hasTaskUnit();
            final int tmpFieldCnt = this.taskUnit.copyChangeToDb(builder.getTaskUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_PAYTASKUNLOCKED)) {
            builder.setPayTaskUnlocked(this.getPayTaskUnlocked());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityGrowthFundUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTaskUnit()) {
            this.getTaskUnit().mergeFromDb(proto.getTaskUnit());
        } else {
            if (this.taskUnit != null) {
                this.taskUnit.mergeFromDb(proto.getTaskUnit());
            }
        }
        if (proto.hasPayTaskUnlocked()) {
            this.innerSetPayTaskUnlocked(proto.getPayTaskUnlocked());
        } else {
            this.innerSetPayTaskUnlocked(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ActivityGrowthFundUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityGrowthFundUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTaskUnit()) {
            this.getTaskUnit().mergeChangeFromDb(proto.getTaskUnit());
            fieldCnt++;
        }
        if (proto.hasPayTaskUnlocked()) {
            this.setPayTaskUnlocked(proto.getPayTaskUnlocked());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityGrowthFundUnit.Builder getCopySsBuilder() {
        final ActivityGrowthFundUnit.Builder builder = ActivityGrowthFundUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityGrowthFundUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.taskUnit != null) {
            Struct.ActivityTaskUnit.Builder tmpBuilder = Struct.ActivityTaskUnit.newBuilder();
            final int tmpFieldCnt = this.taskUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskUnit();
            }
        }  else if (builder.hasTaskUnit()) {
            // 清理TaskUnit
            builder.clearTaskUnit();
            fieldCnt++;
        }
        if (this.getPayTaskUnlocked()) {
            builder.setPayTaskUnlocked(this.getPayTaskUnlocked());
            fieldCnt++;
        }  else if (builder.hasPayTaskUnlocked()) {
            // 清理PayTaskUnlocked
            builder.clearPayTaskUnlocked();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityGrowthFundUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TASKUNIT) && this.taskUnit != null) {
            final boolean needClear = !builder.hasTaskUnit();
            final int tmpFieldCnt = this.taskUnit.copyChangeToSs(builder.getTaskUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_PAYTASKUNLOCKED)) {
            builder.setPayTaskUnlocked(this.getPayTaskUnlocked());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityGrowthFundUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTaskUnit()) {
            this.getTaskUnit().mergeFromSs(proto.getTaskUnit());
        } else {
            if (this.taskUnit != null) {
                this.taskUnit.mergeFromSs(proto.getTaskUnit());
            }
        }
        if (proto.hasPayTaskUnlocked()) {
            this.innerSetPayTaskUnlocked(proto.getPayTaskUnlocked());
        } else {
            this.innerSetPayTaskUnlocked(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ActivityGrowthFundUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityGrowthFundUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTaskUnit()) {
            this.getTaskUnit().mergeChangeFromSs(proto.getTaskUnit());
            fieldCnt++;
        }
        if (proto.hasPayTaskUnlocked()) {
            this.setPayTaskUnlocked(proto.getPayTaskUnlocked());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityGrowthFundUnit.Builder builder = ActivityGrowthFundUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TASKUNIT) && this.taskUnit != null) {
            this.taskUnit.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.taskUnit != null) {
            this.taskUnit.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityGrowthFundUnitProp)) {
            return false;
        }
        final ActivityGrowthFundUnitProp otherNode = (ActivityGrowthFundUnitProp) node;
        if (!this.getTaskUnit().compareDataTo(otherNode.getTaskUnit())) {
            return false;
        }
        if (this.payTaskUnlocked != otherNode.payTaskUnlocked) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructBattle.BattleRecordTroopEvent;
import com.yorha.proto.Struct;
import com.yorha.proto.StructBattlePB.BattleRecordTroopEventPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class BattleRecordTroopEventProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TYPE = 0;
    public static final int FIELD_INDEX_CLANNAME = 1;
    public static final int FIELD_INDEX_SOLDIERNUM = 2;
    public static final int FIELD_INDEX_CARDHEAD = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private BattleLogTroopChangeType type = BattleLogTroopChangeType.forNumber(0);
    private String clanName = Constant.DEFAULT_STR_VALUE;
    private int soldierNum = Constant.DEFAULT_INT_VALUE;
    private PlayerCardHeadProp cardHead = null;

    public BattleRecordTroopEventProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BattleRecordTroopEventProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get type
     *
     * @return type value
     */
    public BattleLogTroopChangeType getType() {
        return this.type;
    }

    /**
     * set type && set marked
     *
     * @param type new value
     * @return current object
     */
    public BattleRecordTroopEventProp setType(BattleLogTroopChangeType type) {
        if (type == null) {
            throw new NullPointerException();
        }
        if (this.type != type) {
            this.mark(FIELD_INDEX_TYPE);
            this.type = type;
        }
        return this;
    }

    /**
     * inner set type
     *
     * @param type new value
     */
    private void innerSetType(BattleLogTroopChangeType type) {
        this.type = type;
    }

    /**
     * get clanName
     *
     * @return clanName value
     */
    public String getClanName() {
        return this.clanName;
    }

    /**
     * set clanName && set marked
     *
     * @param clanName new value
     * @return current object
     */
    public BattleRecordTroopEventProp setClanName(String clanName) {
        if (clanName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanName, clanName)) {
            this.mark(FIELD_INDEX_CLANNAME);
            this.clanName = clanName;
        }
        return this;
    }

    /**
     * inner set clanName
     *
     * @param clanName new value
     */
    private void innerSetClanName(String clanName) {
        this.clanName = clanName;
    }

    /**
     * get soldierNum
     *
     * @return soldierNum value
     */
    public int getSoldierNum() {
        return this.soldierNum;
    }

    /**
     * set soldierNum && set marked
     *
     * @param soldierNum new value
     * @return current object
     */
    public BattleRecordTroopEventProp setSoldierNum(int soldierNum) {
        if (this.soldierNum != soldierNum) {
            this.mark(FIELD_INDEX_SOLDIERNUM);
            this.soldierNum = soldierNum;
        }
        return this;
    }

    /**
     * inner set soldierNum
     *
     * @param soldierNum new value
     */
    private void innerSetSoldierNum(int soldierNum) {
        this.soldierNum = soldierNum;
    }

    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordTroopEventPB.Builder getCopyCsBuilder() {
        final BattleRecordTroopEventPB.Builder builder = BattleRecordTroopEventPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BattleRecordTroopEventPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != BattleLogTroopChangeType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (!this.getClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }  else if (builder.hasClanName()) {
            // 清理ClanName
            builder.clearClanName();
            fieldCnt++;
        }
        if (this.getSoldierNum() != 0) {
            builder.setSoldierNum(this.getSoldierNum());
            fieldCnt++;
        }  else if (builder.hasSoldierNum()) {
            // 清理SoldierNum
            builder.clearSoldierNum();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BattleRecordTroopEventPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERNUM)) {
            builder.setSoldierNum(this.getSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BattleRecordTroopEventPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERNUM)) {
            builder.setSoldierNum(this.getSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToAndClearDeleteKeysCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BattleRecordTroopEventPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(BattleLogTroopChangeType.forNumber(0));
        }
        if (proto.hasClanName()) {
            this.innerSetClanName(proto.getClanName());
        } else {
            this.innerSetClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasSoldierNum()) {
            this.innerSetSoldierNum(proto.getSoldierNum());
        } else {
            this.innerSetSoldierNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromCs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromCs(proto.getCardHead());
            }
        }
        this.markAll();
        return BattleRecordTroopEventProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BattleRecordTroopEventPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasClanName()) {
            this.setClanName(proto.getClanName());
            fieldCnt++;
        }
        if (proto.hasSoldierNum()) {
            this.setSoldierNum(proto.getSoldierNum());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromCs(proto.getCardHead());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordTroopEvent.Builder getCopyDbBuilder() {
        final BattleRecordTroopEvent.Builder builder = BattleRecordTroopEvent.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(BattleRecordTroopEvent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != BattleLogTroopChangeType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (!this.getClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }  else if (builder.hasClanName()) {
            // 清理ClanName
            builder.clearClanName();
            fieldCnt++;
        }
        if (this.getSoldierNum() != 0) {
            builder.setSoldierNum(this.getSoldierNum());
            fieldCnt++;
        }  else if (builder.hasSoldierNum()) {
            // 清理SoldierNum
            builder.clearSoldierNum();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(BattleRecordTroopEvent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERNUM)) {
            builder.setSoldierNum(this.getSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToDb(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(BattleRecordTroopEvent proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(BattleLogTroopChangeType.forNumber(0));
        }
        if (proto.hasClanName()) {
            this.innerSetClanName(proto.getClanName());
        } else {
            this.innerSetClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasSoldierNum()) {
            this.innerSetSoldierNum(proto.getSoldierNum());
        } else {
            this.innerSetSoldierNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromDb(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromDb(proto.getCardHead());
            }
        }
        this.markAll();
        return BattleRecordTroopEventProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(BattleRecordTroopEvent proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasClanName()) {
            this.setClanName(proto.getClanName());
            fieldCnt++;
        }
        if (proto.hasSoldierNum()) {
            this.setSoldierNum(proto.getSoldierNum());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromDb(proto.getCardHead());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordTroopEvent.Builder getCopySsBuilder() {
        final BattleRecordTroopEvent.Builder builder = BattleRecordTroopEvent.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(BattleRecordTroopEvent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != BattleLogTroopChangeType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (!this.getClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }  else if (builder.hasClanName()) {
            // 清理ClanName
            builder.clearClanName();
            fieldCnt++;
        }
        if (this.getSoldierNum() != 0) {
            builder.setSoldierNum(this.getSoldierNum());
            fieldCnt++;
        }  else if (builder.hasSoldierNum()) {
            // 清理SoldierNum
            builder.clearSoldierNum();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(BattleRecordTroopEvent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERNUM)) {
            builder.setSoldierNum(this.getSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(BattleRecordTroopEvent proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(BattleLogTroopChangeType.forNumber(0));
        }
        if (proto.hasClanName()) {
            this.innerSetClanName(proto.getClanName());
        } else {
            this.innerSetClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasSoldierNum()) {
            this.innerSetSoldierNum(proto.getSoldierNum());
        } else {
            this.innerSetSoldierNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        this.markAll();
        return BattleRecordTroopEventProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(BattleRecordTroopEvent proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasClanName()) {
            this.setClanName(proto.getClanName());
            fieldCnt++;
        }
        if (proto.hasSoldierNum()) {
            this.setSoldierNum(proto.getSoldierNum());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        BattleRecordTroopEvent.Builder builder = BattleRecordTroopEvent.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BattleRecordTroopEventProp)) {
            return false;
        }
        final BattleRecordTroopEventProp otherNode = (BattleRecordTroopEventProp) node;
        if (this.type != otherNode.type) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanName, otherNode.clanName)) {
            return false;
        }
        if (this.soldierNum != otherNode.soldierNum) {
            return false;
        }
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.BasicPB.Int64ListPB;
import com.yorha.proto.Basic.Int64List;

/**
 * <AUTHOR> auto gen
 */
public class Int64ListProp extends AbstractListNode<Long> {
    /**
     * Create a Int64ListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public Int64ListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to Int64ListProp
     *
     * @return new object
     */
    @Override
    public Long addEmptyValue() {
        final Long newProp = 0L;
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Int64ListPB.Builder getCopyCsBuilder() {
        final Int64ListPB.Builder builder = Int64ListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(Int64ListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return Int64ListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        builder.addAllDatas(this);
        return Int64ListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(Int64ListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return Int64ListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(Int64ListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        this.addAll(proto.getDatasList());
        this.markAll();
        return Int64ListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(Int64ListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Int64List.Builder getCopyDbBuilder() {
        final Int64List.Builder builder = Int64List.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(Int64List.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return Int64ListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        builder.addAllDatas(this);
        return Int64ListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(Int64List.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return Int64ListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(Int64List proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        this.addAll(proto.getDatasList());
        this.markAll();
        return Int64ListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(Int64List proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Int64List.Builder getCopySsBuilder() {
        final Int64List.Builder builder = Int64List.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(Int64List.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return Int64ListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        builder.addAllDatas(this);
        return Int64ListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(Int64List.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return Int64ListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(Int64List proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        this.addAll(proto.getDatasList());
        this.markAll();
        return Int64ListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(Int64List proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        Int64List.Builder builder = Int64List.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
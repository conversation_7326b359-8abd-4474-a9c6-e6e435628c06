package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructPB.ArenaMailPlayerInfoListPB;
import com.yorha.proto.Struct.ArenaMailPlayerInfoList;
import com.yorha.proto.StructPB.ArenaMailPlayerInfoPB;
import com.yorha.proto.Struct.ArenaMailPlayerInfo;

/**
 * <AUTHOR> auto gen
 */
public class ArenaMailPlayerInfoListProp extends AbstractListNode<ArenaMailPlayerInfoProp> {
    /**
     * Create a ArenaMailPlayerInfoListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public ArenaMailPlayerInfoListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to ArenaMailPlayerInfoListProp
     *
     * @return new object
     */
    @Override
    public ArenaMailPlayerInfoProp addEmptyValue() {
        final ArenaMailPlayerInfoProp newProp = new ArenaMailPlayerInfoProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArenaMailPlayerInfoListPB.Builder getCopyCsBuilder() {
        final ArenaMailPlayerInfoListPB.Builder builder = ArenaMailPlayerInfoListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(ArenaMailPlayerInfoListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return ArenaMailPlayerInfoListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final ArenaMailPlayerInfoProp v : this) {
            final ArenaMailPlayerInfoPB.Builder itemBuilder = ArenaMailPlayerInfoPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return ArenaMailPlayerInfoListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(ArenaMailPlayerInfoListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return ArenaMailPlayerInfoListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(ArenaMailPlayerInfoListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (ArenaMailPlayerInfoPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return ArenaMailPlayerInfoListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(ArenaMailPlayerInfoListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArenaMailPlayerInfoList.Builder getCopyDbBuilder() {
        final ArenaMailPlayerInfoList.Builder builder = ArenaMailPlayerInfoList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(ArenaMailPlayerInfoList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return ArenaMailPlayerInfoListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final ArenaMailPlayerInfoProp v : this) {
            final ArenaMailPlayerInfo.Builder itemBuilder = ArenaMailPlayerInfo.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return ArenaMailPlayerInfoListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(ArenaMailPlayerInfoList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return ArenaMailPlayerInfoListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(ArenaMailPlayerInfoList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (ArenaMailPlayerInfo v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return ArenaMailPlayerInfoListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(ArenaMailPlayerInfoList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArenaMailPlayerInfoList.Builder getCopySsBuilder() {
        final ArenaMailPlayerInfoList.Builder builder = ArenaMailPlayerInfoList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(ArenaMailPlayerInfoList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return ArenaMailPlayerInfoListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final ArenaMailPlayerInfoProp v : this) {
            final ArenaMailPlayerInfo.Builder itemBuilder = ArenaMailPlayerInfo.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return ArenaMailPlayerInfoListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(ArenaMailPlayerInfoList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return ArenaMailPlayerInfoListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(ArenaMailPlayerInfoList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (ArenaMailPlayerInfo v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return ArenaMailPlayerInfoListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(ArenaMailPlayerInfoList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        ArenaMailPlayerInfoList.Builder builder = ArenaMailPlayerInfoList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
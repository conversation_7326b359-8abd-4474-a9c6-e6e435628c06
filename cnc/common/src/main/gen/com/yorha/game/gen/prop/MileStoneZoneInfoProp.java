package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.MileStoneZoneInfo;
import com.yorha.proto.StructPB.MileStoneZoneInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class MileStoneZoneInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ZONEID = 0;
    public static final int FIELD_INDEX_SCORE = 1;
    public static final int FIELD_INDEX_NAME = 2;
    public static final int FIELD_INDEX_LASTUPDATETSMS = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int zoneId = Constant.DEFAULT_INT_VALUE;
    private int score = Constant.DEFAULT_INT_VALUE;
    private String name = Constant.DEFAULT_STR_VALUE;
    private long lastUpdateTsMs = Constant.DEFAULT_LONG_VALUE;

    public MileStoneZoneInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MileStoneZoneInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get zoneId
     *
     * @return zoneId value
     */
    public int getZoneId() {
        return this.zoneId;
    }

    /**
     * set zoneId && set marked
     *
     * @param zoneId new value
     * @return current object
     */
    public MileStoneZoneInfoProp setZoneId(int zoneId) {
        if (this.zoneId != zoneId) {
            this.mark(FIELD_INDEX_ZONEID);
            this.zoneId = zoneId;
        }
        return this;
    }

    /**
     * inner set zoneId
     *
     * @param zoneId new value
     */
    private void innerSetZoneId(int zoneId) {
        this.zoneId = zoneId;
    }

    /**
     * get score
     *
     * @return score value
     */
    public int getScore() {
        return this.score;
    }

    /**
     * set score && set marked
     *
     * @param score new value
     * @return current object
     */
    public MileStoneZoneInfoProp setScore(int score) {
        if (this.score != score) {
            this.mark(FIELD_INDEX_SCORE);
            this.score = score;
        }
        return this;
    }

    /**
     * inner set score
     *
     * @param score new value
     */
    private void innerSetScore(int score) {
        this.score = score;
    }

    /**
     * get name
     *
     * @return name value
     */
    public String getName() {
        return this.name;
    }

    /**
     * set name && set marked
     *
     * @param name new value
     * @return current object
     */
    public MileStoneZoneInfoProp setName(String name) {
        if (name == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.name, name)) {
            this.mark(FIELD_INDEX_NAME);
            this.name = name;
        }
        return this;
    }

    /**
     * inner set name
     *
     * @param name new value
     */
    private void innerSetName(String name) {
        this.name = name;
    }

    /**
     * get lastUpdateTsMs
     *
     * @return lastUpdateTsMs value
     */
    public long getLastUpdateTsMs() {
        return this.lastUpdateTsMs;
    }

    /**
     * set lastUpdateTsMs && set marked
     *
     * @param lastUpdateTsMs new value
     * @return current object
     */
    public MileStoneZoneInfoProp setLastUpdateTsMs(long lastUpdateTsMs) {
        if (this.lastUpdateTsMs != lastUpdateTsMs) {
            this.mark(FIELD_INDEX_LASTUPDATETSMS);
            this.lastUpdateTsMs = lastUpdateTsMs;
        }
        return this;
    }

    /**
     * inner set lastUpdateTsMs
     *
     * @param lastUpdateTsMs new value
     */
    private void innerSetLastUpdateTsMs(long lastUpdateTsMs) {
        this.lastUpdateTsMs = lastUpdateTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneZoneInfoPB.Builder getCopyCsBuilder() {
        final MileStoneZoneInfoPB.Builder builder = MileStoneZoneInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MileStoneZoneInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        if (this.getScore() != 0) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        if (this.getLastUpdateTsMs() != 0L) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateTsMs()) {
            // 清理LastUpdateTsMs
            builder.clearLastUpdateTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MileStoneZoneInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATETSMS)) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MileStoneZoneInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATETSMS)) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MileStoneZoneInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasLastUpdateTsMs()) {
            this.innerSetLastUpdateTsMs(proto.getLastUpdateTsMs());
        } else {
            this.innerSetLastUpdateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MileStoneZoneInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MileStoneZoneInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        if (proto.hasLastUpdateTsMs()) {
            this.setLastUpdateTsMs(proto.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneZoneInfo.Builder getCopyDbBuilder() {
        final MileStoneZoneInfo.Builder builder = MileStoneZoneInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MileStoneZoneInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        if (this.getScore() != 0) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        if (this.getLastUpdateTsMs() != 0L) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateTsMs()) {
            // 清理LastUpdateTsMs
            builder.clearLastUpdateTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MileStoneZoneInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATETSMS)) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MileStoneZoneInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasLastUpdateTsMs()) {
            this.innerSetLastUpdateTsMs(proto.getLastUpdateTsMs());
        } else {
            this.innerSetLastUpdateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MileStoneZoneInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MileStoneZoneInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        if (proto.hasLastUpdateTsMs()) {
            this.setLastUpdateTsMs(proto.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneZoneInfo.Builder getCopySsBuilder() {
        final MileStoneZoneInfo.Builder builder = MileStoneZoneInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MileStoneZoneInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        if (this.getScore() != 0) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        if (this.getLastUpdateTsMs() != 0L) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateTsMs()) {
            // 清理LastUpdateTsMs
            builder.clearLastUpdateTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MileStoneZoneInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATETSMS)) {
            builder.setLastUpdateTsMs(this.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MileStoneZoneInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasLastUpdateTsMs()) {
            this.innerSetLastUpdateTsMs(proto.getLastUpdateTsMs());
        } else {
            this.innerSetLastUpdateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MileStoneZoneInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MileStoneZoneInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        if (proto.hasLastUpdateTsMs()) {
            this.setLastUpdateTsMs(proto.getLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MileStoneZoneInfo.Builder builder = MileStoneZoneInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.zoneId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MileStoneZoneInfoProp)) {
            return false;
        }
        final MileStoneZoneInfoProp otherNode = (MileStoneZoneInfoProp) node;
        if (this.zoneId != otherNode.zoneId) {
            return false;
        }
        if (this.score != otherNode.score) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.name, otherNode.name)) {
            return false;
        }
        if (this.lastUpdateTsMs != otherNode.lastUpdateTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
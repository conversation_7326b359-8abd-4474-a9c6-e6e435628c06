package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructCommon.CrossData;
import com.yorha.proto.StructCommonPB.CrossDataPB;


/**
 * <AUTHOR> auto gen
 */
public class CrossDataProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_PARTID = 0;
    public static final int FIELD_INDEX_OWNERCLANID = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int partId = Constant.DEFAULT_INT_VALUE;
    private long ownerClanId = Constant.DEFAULT_LONG_VALUE;

    public CrossDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public CrossDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get partId
     *
     * @return partId value
     */
    public int getPartId() {
        return this.partId;
    }

    /**
     * set partId && set marked
     *
     * @param partId new value
     * @return current object
     */
    public CrossDataProp setPartId(int partId) {
        if (this.partId != partId) {
            this.mark(FIELD_INDEX_PARTID);
            this.partId = partId;
        }
        return this;
    }

    /**
     * inner set partId
     *
     * @param partId new value
     */
    private void innerSetPartId(int partId) {
        this.partId = partId;
    }

    /**
     * get ownerClanId
     *
     * @return ownerClanId value
     */
    public long getOwnerClanId() {
        return this.ownerClanId;
    }

    /**
     * set ownerClanId && set marked
     *
     * @param ownerClanId new value
     * @return current object
     */
    public CrossDataProp setOwnerClanId(long ownerClanId) {
        if (this.ownerClanId != ownerClanId) {
            this.mark(FIELD_INDEX_OWNERCLANID);
            this.ownerClanId = ownerClanId;
        }
        return this;
    }

    /**
     * inner set ownerClanId
     *
     * @param ownerClanId new value
     */
    private void innerSetOwnerClanId(long ownerClanId) {
        this.ownerClanId = ownerClanId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CrossDataPB.Builder getCopyCsBuilder() {
        final CrossDataPB.Builder builder = CrossDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(CrossDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPartId() != 0) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }  else if (builder.hasPartId()) {
            // 清理PartId
            builder.clearPartId();
            fieldCnt++;
        }
        if (this.getOwnerClanId() != 0L) {
            builder.setOwnerClanId(this.getOwnerClanId());
            fieldCnt++;
        }  else if (builder.hasOwnerClanId()) {
            // 清理OwnerClanId
            builder.clearOwnerClanId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(CrossDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PARTID)) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERCLANID)) {
            builder.setOwnerClanId(this.getOwnerClanId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(CrossDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PARTID)) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERCLANID)) {
            builder.setOwnerClanId(this.getOwnerClanId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(CrossDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPartId()) {
            this.innerSetPartId(proto.getPartId());
        } else {
            this.innerSetPartId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOwnerClanId()) {
            this.innerSetOwnerClanId(proto.getOwnerClanId());
        } else {
            this.innerSetOwnerClanId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return CrossDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(CrossDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPartId()) {
            this.setPartId(proto.getPartId());
            fieldCnt++;
        }
        if (proto.hasOwnerClanId()) {
            this.setOwnerClanId(proto.getOwnerClanId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CrossData.Builder getCopyDbBuilder() {
        final CrossData.Builder builder = CrossData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(CrossData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPartId() != 0) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }  else if (builder.hasPartId()) {
            // 清理PartId
            builder.clearPartId();
            fieldCnt++;
        }
        if (this.getOwnerClanId() != 0L) {
            builder.setOwnerClanId(this.getOwnerClanId());
            fieldCnt++;
        }  else if (builder.hasOwnerClanId()) {
            // 清理OwnerClanId
            builder.clearOwnerClanId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(CrossData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PARTID)) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERCLANID)) {
            builder.setOwnerClanId(this.getOwnerClanId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(CrossData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPartId()) {
            this.innerSetPartId(proto.getPartId());
        } else {
            this.innerSetPartId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOwnerClanId()) {
            this.innerSetOwnerClanId(proto.getOwnerClanId());
        } else {
            this.innerSetOwnerClanId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return CrossDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(CrossData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPartId()) {
            this.setPartId(proto.getPartId());
            fieldCnt++;
        }
        if (proto.hasOwnerClanId()) {
            this.setOwnerClanId(proto.getOwnerClanId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CrossData.Builder getCopySsBuilder() {
        final CrossData.Builder builder = CrossData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(CrossData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPartId() != 0) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }  else if (builder.hasPartId()) {
            // 清理PartId
            builder.clearPartId();
            fieldCnt++;
        }
        if (this.getOwnerClanId() != 0L) {
            builder.setOwnerClanId(this.getOwnerClanId());
            fieldCnt++;
        }  else if (builder.hasOwnerClanId()) {
            // 清理OwnerClanId
            builder.clearOwnerClanId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(CrossData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PARTID)) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERCLANID)) {
            builder.setOwnerClanId(this.getOwnerClanId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(CrossData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPartId()) {
            this.innerSetPartId(proto.getPartId());
        } else {
            this.innerSetPartId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOwnerClanId()) {
            this.innerSetOwnerClanId(proto.getOwnerClanId());
        } else {
            this.innerSetOwnerClanId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return CrossDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(CrossData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPartId()) {
            this.setPartId(proto.getPartId());
            fieldCnt++;
        }
        if (proto.hasOwnerClanId()) {
            this.setOwnerClanId(proto.getOwnerClanId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        CrossData.Builder builder = CrossData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.partId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof CrossDataProp)) {
            return false;
        }
        final CrossDataProp otherNode = (CrossDataProp) node;
        if (this.partId != otherNode.partId) {
            return false;
        }
        if (this.ownerClanId != otherNode.ownerClanId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ClanTechResearchInfo;
import com.yorha.proto.StructPB.ClanTechResearchInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanTechResearchInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_TECHSUBID = 0;
    public static final int FIELD_INDEX_STARTTSSEC = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int techSubId = Constant.DEFAULT_INT_VALUE;
    private int startTsSec = Constant.DEFAULT_INT_VALUE;

    public ClanTechResearchInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanTechResearchInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get techSubId
     *
     * @return techSubId value
     */
    public int getTechSubId() {
        return this.techSubId;
    }

    /**
     * set techSubId && set marked
     *
     * @param techSubId new value
     * @return current object
     */
    public ClanTechResearchInfoProp setTechSubId(int techSubId) {
        if (this.techSubId != techSubId) {
            this.mark(FIELD_INDEX_TECHSUBID);
            this.techSubId = techSubId;
        }
        return this;
    }

    /**
     * inner set techSubId
     *
     * @param techSubId new value
     */
    private void innerSetTechSubId(int techSubId) {
        this.techSubId = techSubId;
    }

    /**
     * get startTsSec
     *
     * @return startTsSec value
     */
    public int getStartTsSec() {
        return this.startTsSec;
    }

    /**
     * set startTsSec && set marked
     *
     * @param startTsSec new value
     * @return current object
     */
    public ClanTechResearchInfoProp setStartTsSec(int startTsSec) {
        if (this.startTsSec != startTsSec) {
            this.mark(FIELD_INDEX_STARTTSSEC);
            this.startTsSec = startTsSec;
        }
        return this;
    }

    /**
     * inner set startTsSec
     *
     * @param startTsSec new value
     */
    private void innerSetStartTsSec(int startTsSec) {
        this.startTsSec = startTsSec;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanTechResearchInfoPB.Builder getCopyCsBuilder() {
        final ClanTechResearchInfoPB.Builder builder = ClanTechResearchInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanTechResearchInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTechSubId() != 0) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }  else if (builder.hasTechSubId()) {
            // 清理TechSubId
            builder.clearTechSubId();
            fieldCnt++;
        }
        if (this.getStartTsSec() != 0) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }  else if (builder.hasStartTsSec()) {
            // 清理StartTsSec
            builder.clearStartTsSec();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanTechResearchInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TECHSUBID)) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSSEC)) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanTechResearchInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TECHSUBID)) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSSEC)) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanTechResearchInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTechSubId()) {
            this.innerSetTechSubId(proto.getTechSubId());
        } else {
            this.innerSetTechSubId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartTsSec()) {
            this.innerSetStartTsSec(proto.getStartTsSec());
        } else {
            this.innerSetStartTsSec(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanTechResearchInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanTechResearchInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTechSubId()) {
            this.setTechSubId(proto.getTechSubId());
            fieldCnt++;
        }
        if (proto.hasStartTsSec()) {
            this.setStartTsSec(proto.getStartTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanTechResearchInfo.Builder getCopyDbBuilder() {
        final ClanTechResearchInfo.Builder builder = ClanTechResearchInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanTechResearchInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTechSubId() != 0) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }  else if (builder.hasTechSubId()) {
            // 清理TechSubId
            builder.clearTechSubId();
            fieldCnt++;
        }
        if (this.getStartTsSec() != 0) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }  else if (builder.hasStartTsSec()) {
            // 清理StartTsSec
            builder.clearStartTsSec();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanTechResearchInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TECHSUBID)) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSSEC)) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanTechResearchInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTechSubId()) {
            this.innerSetTechSubId(proto.getTechSubId());
        } else {
            this.innerSetTechSubId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartTsSec()) {
            this.innerSetStartTsSec(proto.getStartTsSec());
        } else {
            this.innerSetStartTsSec(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanTechResearchInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanTechResearchInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTechSubId()) {
            this.setTechSubId(proto.getTechSubId());
            fieldCnt++;
        }
        if (proto.hasStartTsSec()) {
            this.setStartTsSec(proto.getStartTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanTechResearchInfo.Builder getCopySsBuilder() {
        final ClanTechResearchInfo.Builder builder = ClanTechResearchInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanTechResearchInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTechSubId() != 0) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }  else if (builder.hasTechSubId()) {
            // 清理TechSubId
            builder.clearTechSubId();
            fieldCnt++;
        }
        if (this.getStartTsSec() != 0) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }  else if (builder.hasStartTsSec()) {
            // 清理StartTsSec
            builder.clearStartTsSec();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanTechResearchInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TECHSUBID)) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSSEC)) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanTechResearchInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTechSubId()) {
            this.innerSetTechSubId(proto.getTechSubId());
        } else {
            this.innerSetTechSubId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartTsSec()) {
            this.innerSetStartTsSec(proto.getStartTsSec());
        } else {
            this.innerSetStartTsSec(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanTechResearchInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanTechResearchInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTechSubId()) {
            this.setTechSubId(proto.getTechSubId());
            fieldCnt++;
        }
        if (proto.hasStartTsSec()) {
            this.setStartTsSec(proto.getStartTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanTechResearchInfo.Builder builder = ClanTechResearchInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.techSubId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanTechResearchInfoProp)) {
            return false;
        }
        final ClanTechResearchInfoProp otherNode = (ClanTechResearchInfoProp) node;
        if (this.techSubId != otherNode.techSubId) {
            return false;
        }
        if (this.startTsSec != otherNode.startTsSec) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
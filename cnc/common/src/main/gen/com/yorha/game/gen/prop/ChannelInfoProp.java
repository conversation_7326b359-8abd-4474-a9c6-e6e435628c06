package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.ChannelInfo;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.ChannelInfoPB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class ChannelInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_CHANNEL = 0;
    public static final int FIELD_INDEX_ITEM = 1;
    public static final int FIELD_INDEX_PREPAREITEM = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int channel = Constant.DEFAULT_INT_VALUE;
    private StringChatItemMapProp item = null;
    private StringPrepareChatItemMapProp prepareItem = null;

    public ChannelInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ChannelInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get channel
     *
     * @return channel value
     */
    public int getChannel() {
        return this.channel;
    }

    /**
     * set channel && set marked
     *
     * @param channel new value
     * @return current object
     */
    public ChannelInfoProp setChannel(int channel) {
        if (this.channel != channel) {
            this.mark(FIELD_INDEX_CHANNEL);
            this.channel = channel;
        }
        return this;
    }

    /**
     * inner set channel
     *
     * @param channel new value
     */
    private void innerSetChannel(int channel) {
        this.channel = channel;
    }

    /**
     * get item
     *
     * @return item value
     */
    public StringChatItemMapProp getItem() {
        if (this.item == null) {
            this.item = new StringChatItemMapProp(this, FIELD_INDEX_ITEM);
        }
        return this.item;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putItemV(ChatItemProp v) {
        this.getItem().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ChatItemProp addEmptyItem(String k) {
        return this.getItem().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getItemSize() {
        if (this.item == null) {
            return 0;
        }
        return this.item.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isItemEmpty() {
        if (this.item == null) {
            return true;
        }
        return this.item.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ChatItemProp getItemV(String k) {
        if (this.item == null || !this.item.containsKey(k)) {
            return null;
        }
        return this.item.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearItem() {
        if (this.item != null) {
            this.item.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeItemV(String k) {
        if (this.item != null) {
            this.item.remove(k);
        }
    }
    /**
     * get prepareItem
     *
     * @return prepareItem value
     */
    public StringPrepareChatItemMapProp getPrepareItem() {
        if (this.prepareItem == null) {
            this.prepareItem = new StringPrepareChatItemMapProp(this, FIELD_INDEX_PREPAREITEM);
        }
        return this.prepareItem;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putPrepareItemV(PrepareChatItemProp v) {
        this.getPrepareItem().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PrepareChatItemProp addEmptyPrepareItem(String k) {
        return this.getPrepareItem().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getPrepareItemSize() {
        if (this.prepareItem == null) {
            return 0;
        }
        return this.prepareItem.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isPrepareItemEmpty() {
        if (this.prepareItem == null) {
            return true;
        }
        return this.prepareItem.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PrepareChatItemProp getPrepareItemV(String k) {
        if (this.prepareItem == null || !this.prepareItem.containsKey(k)) {
            return null;
        }
        return this.prepareItem.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearPrepareItem() {
        if (this.prepareItem != null) {
            this.prepareItem.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removePrepareItemV(String k) {
        if (this.prepareItem != null) {
            this.prepareItem.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ChannelInfoPB.Builder getCopyCsBuilder() {
        final ChannelInfoPB.Builder builder = ChannelInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ChannelInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getChannel() != 0) {
            builder.setChannel(this.getChannel());
            fieldCnt++;
        }  else if (builder.hasChannel()) {
            // 清理Channel
            builder.clearChannel();
            fieldCnt++;
        }
        if (this.item != null) {
            PlayerPB.StringChatItemMapPB.Builder tmpBuilder = PlayerPB.StringChatItemMapPB.newBuilder();
            final int tmpFieldCnt = this.item.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItem(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItem();
            }
        }  else if (builder.hasItem()) {
            // 清理Item
            builder.clearItem();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ChannelInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHANNEL)) {
            builder.setChannel(this.getChannel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEM) && this.item != null) {
            final boolean needClear = !builder.hasItem();
            final int tmpFieldCnt = this.item.copyChangeToCs(builder.getItemBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItem();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ChannelInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHANNEL)) {
            builder.setChannel(this.getChannel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEM) && this.item != null) {
            final boolean needClear = !builder.hasItem();
            final int tmpFieldCnt = this.item.copyChangeToAndClearDeleteKeysCs(builder.getItemBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItem();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ChannelInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChannel()) {
            this.innerSetChannel(proto.getChannel());
        } else {
            this.innerSetChannel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasItem()) {
            this.getItem().mergeFromCs(proto.getItem());
        } else {
            if (this.item != null) {
                this.item.mergeFromCs(proto.getItem());
            }
        }
        this.markAll();
        return ChannelInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ChannelInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChannel()) {
            this.setChannel(proto.getChannel());
            fieldCnt++;
        }
        if (proto.hasItem()) {
            this.getItem().mergeChangeFromCs(proto.getItem());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ChannelInfo.Builder getCopyDbBuilder() {
        final ChannelInfo.Builder builder = ChannelInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ChannelInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getChannel() != 0) {
            builder.setChannel(this.getChannel());
            fieldCnt++;
        }  else if (builder.hasChannel()) {
            // 清理Channel
            builder.clearChannel();
            fieldCnt++;
        }
        if (this.item != null) {
            Player.StringChatItemMap.Builder tmpBuilder = Player.StringChatItemMap.newBuilder();
            final int tmpFieldCnt = this.item.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItem(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItem();
            }
        }  else if (builder.hasItem()) {
            // 清理Item
            builder.clearItem();
            fieldCnt++;
        }
        if (this.prepareItem != null) {
            Player.StringPrepareChatItemMap.Builder tmpBuilder = Player.StringPrepareChatItemMap.newBuilder();
            final int tmpFieldCnt = this.prepareItem.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPrepareItem(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPrepareItem();
            }
        }  else if (builder.hasPrepareItem()) {
            // 清理PrepareItem
            builder.clearPrepareItem();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ChannelInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHANNEL)) {
            builder.setChannel(this.getChannel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEM) && this.item != null) {
            final boolean needClear = !builder.hasItem();
            final int tmpFieldCnt = this.item.copyChangeToDb(builder.getItemBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItem();
            }
        }
        if (this.hasMark(FIELD_INDEX_PREPAREITEM) && this.prepareItem != null) {
            final boolean needClear = !builder.hasPrepareItem();
            final int tmpFieldCnt = this.prepareItem.copyChangeToDb(builder.getPrepareItemBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPrepareItem();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ChannelInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChannel()) {
            this.innerSetChannel(proto.getChannel());
        } else {
            this.innerSetChannel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasItem()) {
            this.getItem().mergeFromDb(proto.getItem());
        } else {
            if (this.item != null) {
                this.item.mergeFromDb(proto.getItem());
            }
        }
        if (proto.hasPrepareItem()) {
            this.getPrepareItem().mergeFromDb(proto.getPrepareItem());
        } else {
            if (this.prepareItem != null) {
                this.prepareItem.mergeFromDb(proto.getPrepareItem());
            }
        }
        this.markAll();
        return ChannelInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ChannelInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChannel()) {
            this.setChannel(proto.getChannel());
            fieldCnt++;
        }
        if (proto.hasItem()) {
            this.getItem().mergeChangeFromDb(proto.getItem());
            fieldCnt++;
        }
        if (proto.hasPrepareItem()) {
            this.getPrepareItem().mergeChangeFromDb(proto.getPrepareItem());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ChannelInfo.Builder getCopySsBuilder() {
        final ChannelInfo.Builder builder = ChannelInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ChannelInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getChannel() != 0) {
            builder.setChannel(this.getChannel());
            fieldCnt++;
        }  else if (builder.hasChannel()) {
            // 清理Channel
            builder.clearChannel();
            fieldCnt++;
        }
        if (this.item != null) {
            Player.StringChatItemMap.Builder tmpBuilder = Player.StringChatItemMap.newBuilder();
            final int tmpFieldCnt = this.item.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItem(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItem();
            }
        }  else if (builder.hasItem()) {
            // 清理Item
            builder.clearItem();
            fieldCnt++;
        }
        if (this.prepareItem != null) {
            Player.StringPrepareChatItemMap.Builder tmpBuilder = Player.StringPrepareChatItemMap.newBuilder();
            final int tmpFieldCnt = this.prepareItem.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPrepareItem(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPrepareItem();
            }
        }  else if (builder.hasPrepareItem()) {
            // 清理PrepareItem
            builder.clearPrepareItem();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ChannelInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHANNEL)) {
            builder.setChannel(this.getChannel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEM) && this.item != null) {
            final boolean needClear = !builder.hasItem();
            final int tmpFieldCnt = this.item.copyChangeToSs(builder.getItemBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItem();
            }
        }
        if (this.hasMark(FIELD_INDEX_PREPAREITEM) && this.prepareItem != null) {
            final boolean needClear = !builder.hasPrepareItem();
            final int tmpFieldCnt = this.prepareItem.copyChangeToSs(builder.getPrepareItemBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPrepareItem();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ChannelInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChannel()) {
            this.innerSetChannel(proto.getChannel());
        } else {
            this.innerSetChannel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasItem()) {
            this.getItem().mergeFromSs(proto.getItem());
        } else {
            if (this.item != null) {
                this.item.mergeFromSs(proto.getItem());
            }
        }
        if (proto.hasPrepareItem()) {
            this.getPrepareItem().mergeFromSs(proto.getPrepareItem());
        } else {
            if (this.prepareItem != null) {
                this.prepareItem.mergeFromSs(proto.getPrepareItem());
            }
        }
        this.markAll();
        return ChannelInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ChannelInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChannel()) {
            this.setChannel(proto.getChannel());
            fieldCnt++;
        }
        if (proto.hasItem()) {
            this.getItem().mergeChangeFromSs(proto.getItem());
            fieldCnt++;
        }
        if (proto.hasPrepareItem()) {
            this.getPrepareItem().mergeChangeFromSs(proto.getPrepareItem());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ChannelInfo.Builder builder = ChannelInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ITEM) && this.item != null) {
            this.item.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PREPAREITEM) && this.prepareItem != null) {
            this.prepareItem.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.item != null) {
            this.item.markAll();
        }
        if (this.prepareItem != null) {
            this.prepareItem.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.channel;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ChannelInfoProp)) {
            return false;
        }
        final ChannelInfoProp otherNode = (ChannelInfoProp) node;
        if (this.channel != otherNode.channel) {
            return false;
        }
        if (!this.getItem().compareDataTo(otherNode.getItem())) {
            return false;
        }
        if (!this.getPrepareItem().compareDataTo(otherNode.getPrepareItem())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
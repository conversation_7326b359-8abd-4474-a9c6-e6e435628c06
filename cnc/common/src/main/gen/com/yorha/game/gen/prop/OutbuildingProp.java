package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Outbuilding.OutbuildingEntity;
import com.yorha.proto.Struct;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.OutbuildingPB.OutbuildingEntityPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructBattlePB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class OutbuildingProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TEMPLATEID = 0;
    public static final int FIELD_INDEX_PARTID = 1;
    public static final int FIELD_INDEX_POINT = 2;
    public static final int FIELD_INDEX_TROOP = 3;
    public static final int FIELD_INDEX_BATTLE = 4;
    public static final int FIELD_INDEX_STATE = 5;
    public static final int FIELD_INDEX_BUFFSYS = 6;

    public static final int FIELD_COUNT = 7;

    private long markBits0 = 0L;
    private PropertyChangeListener listener;

    private int templateId = Constant.DEFAULT_INT_VALUE;
    private int partId = Constant.DEFAULT_INT_VALUE;
    private PointProp point = null;
    private TroopProp troop = null;
    private BattleProp battle = null;
    private OutbuildingState state = OutbuildingState.forNumber(0);
    private BuffSysProp buffSys = null;

    public OutbuildingProp() {
        super(null, 0, FIELD_COUNT);
    }

    public OutbuildingProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public OutbuildingProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }

    /**
     * get partId
     *
     * @return partId value
     */
    public int getPartId() {
        return this.partId;
    }

    /**
     * set partId && set marked
     *
     * @param partId new value
     * @return current object
     */
    public OutbuildingProp setPartId(int partId) {
        if (this.partId != partId) {
            this.mark(FIELD_INDEX_PARTID);
            this.partId = partId;
        }
        return this;
    }

    /**
     * inner set partId
     *
     * @param partId new value
     */
    private void innerSetPartId(int partId) {
        this.partId = partId;
    }

    /**
     * get point
     *
     * @return point value
     */
    public PointProp getPoint() {
        if (this.point == null) {
            this.point = new PointProp(this, FIELD_INDEX_POINT);
        }
        return this.point;
    }

    /**
     * get troop
     *
     * @return troop value
     */
    public TroopProp getTroop() {
        if (this.troop == null) {
            this.troop = new TroopProp(this, FIELD_INDEX_TROOP);
        }
        return this.troop;
    }

    /**
     * get battle
     *
     * @return battle value
     */
    public BattleProp getBattle() {
        if (this.battle == null) {
            this.battle = new BattleProp(this, FIELD_INDEX_BATTLE);
        }
        return this.battle;
    }

    /**
     * get state
     *
     * @return state value
     */
    public OutbuildingState getState() {
        return this.state;
    }

    /**
     * set state && set marked
     *
     * @param state new value
     * @return current object
     */
    public OutbuildingProp setState(OutbuildingState state) {
        if (state == null) {
            throw new NullPointerException();
        }
        if (this.state != state) {
            this.mark(FIELD_INDEX_STATE);
            this.state = state;
        }
        return this;
    }

    /**
     * inner set state
     *
     * @param state new value
     */
    private void innerSetState(OutbuildingState state) {
        this.state = state;
    }

    /**
     * get buffSys
     *
     * @return buffSys value
     */
    public BuffSysProp getBuffSys() {
        if (this.buffSys == null) {
            this.buffSys = new BuffSysProp(this, FIELD_INDEX_BUFFSYS);
        }
        return this.buffSys;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public OutbuildingEntityPB.Builder getCopyCsBuilder() {
        final OutbuildingEntityPB.Builder builder = OutbuildingEntityPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(OutbuildingEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getPartId() != 0) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }  else if (builder.hasPartId()) {
            // 清理PartId
            builder.clearPartId();
            fieldCnt++;
        }
        if (this.point != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.point.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayerPB.TroopPB.Builder tmpBuilder = StructPlayerPB.TroopPB.newBuilder();
            final int tmpFieldCnt = this.troop.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.battle != null) {
            StructBattlePB.BattlePB.Builder tmpBuilder = StructBattlePB.BattlePB.newBuilder();
            final int tmpFieldCnt = this.battle.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattle();
            }
        }  else if (builder.hasBattle()) {
            // 清理Battle
            builder.clearBattle();
            fieldCnt++;
        }
        if (this.getState() != OutbuildingState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.buffSys != null) {
            StructBattlePB.BuffSysPB.Builder tmpBuilder = StructBattlePB.BuffSysPB.newBuilder();
            final int tmpFieldCnt = this.buffSys.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuffSys();
            }
        }  else if (builder.hasBuffSys()) {
            // 清理BuffSys
            builder.clearBuffSys();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(OutbuildingEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PARTID)) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToCs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToCs(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUFFSYS) && this.buffSys != null) {
            final boolean needClear = !builder.hasBuffSys();
            final int tmpFieldCnt = this.buffSys.copyChangeToCs(builder.getBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuffSys();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(OutbuildingEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PARTID)) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToAndClearDeleteKeysCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToAndClearDeleteKeysCs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToAndClearDeleteKeysCs(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUFFSYS) && this.buffSys != null) {
            final boolean needClear = !builder.hasBuffSys();
            final int tmpFieldCnt = this.buffSys.copyChangeToAndClearDeleteKeysCs(builder.getBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuffSys();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(OutbuildingEntityPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPartId()) {
            this.innerSetPartId(proto.getPartId());
        } else {
            this.innerSetPartId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromCs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromCs(proto.getPoint());
            }
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromCs(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromCs(proto.getTroop());
            }
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeFromCs(proto.getBattle());
        } else {
            if (this.battle != null) {
                this.battle.mergeFromCs(proto.getBattle());
            }
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(OutbuildingState.forNumber(0));
        }
        if (proto.hasBuffSys()) {
            this.getBuffSys().mergeFromCs(proto.getBuffSys());
        } else {
            if (this.buffSys != null) {
                this.buffSys.mergeFromCs(proto.getBuffSys());
            }
        }
        this.markAll();
        return OutbuildingProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(OutbuildingEntityPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasPartId()) {
            this.setPartId(proto.getPartId());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromCs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromCs(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeChangeFromCs(proto.getBattle());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasBuffSys()) {
            this.getBuffSys().mergeChangeFromCs(proto.getBuffSys());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public OutbuildingEntity.Builder getCopyDbBuilder() {
        final OutbuildingEntity.Builder builder = OutbuildingEntity.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(OutbuildingEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getPartId() != 0) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }  else if (builder.hasPartId()) {
            // 清理PartId
            builder.clearPartId();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayer.Troop.Builder tmpBuilder = StructPlayer.Troop.newBuilder();
            final int tmpFieldCnt = this.troop.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.battle != null) {
            StructBattle.Battle.Builder tmpBuilder = StructBattle.Battle.newBuilder();
            final int tmpFieldCnt = this.battle.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattle();
            }
        }  else if (builder.hasBattle()) {
            // 清理Battle
            builder.clearBattle();
            fieldCnt++;
        }
        if (this.getState() != OutbuildingState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.buffSys != null) {
            StructBattle.BuffSys.Builder tmpBuilder = StructBattle.BuffSys.newBuilder();
            final int tmpFieldCnt = this.buffSys.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuffSys();
            }
        }  else if (builder.hasBuffSys()) {
            // 清理BuffSys
            builder.clearBuffSys();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(OutbuildingEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PARTID)) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToDb(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToDb(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToDb(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUFFSYS) && this.buffSys != null) {
            final boolean needClear = !builder.hasBuffSys();
            final int tmpFieldCnt = this.buffSys.copyChangeToDb(builder.getBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuffSys();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(OutbuildingEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPartId()) {
            this.innerSetPartId(proto.getPartId());
        } else {
            this.innerSetPartId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromDb(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromDb(proto.getPoint());
            }
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromDb(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromDb(proto.getTroop());
            }
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeFromDb(proto.getBattle());
        } else {
            if (this.battle != null) {
                this.battle.mergeFromDb(proto.getBattle());
            }
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(OutbuildingState.forNumber(0));
        }
        if (proto.hasBuffSys()) {
            this.getBuffSys().mergeFromDb(proto.getBuffSys());
        } else {
            if (this.buffSys != null) {
                this.buffSys.mergeFromDb(proto.getBuffSys());
            }
        }
        this.markAll();
        return OutbuildingProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(OutbuildingEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasPartId()) {
            this.setPartId(proto.getPartId());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromDb(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromDb(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeChangeFromDb(proto.getBattle());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasBuffSys()) {
            this.getBuffSys().mergeChangeFromDb(proto.getBuffSys());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public OutbuildingEntity.Builder getCopySsBuilder() {
        final OutbuildingEntity.Builder builder = OutbuildingEntity.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(OutbuildingEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getPartId() != 0) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }  else if (builder.hasPartId()) {
            // 清理PartId
            builder.clearPartId();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayer.Troop.Builder tmpBuilder = StructPlayer.Troop.newBuilder();
            final int tmpFieldCnt = this.troop.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.battle != null) {
            StructBattle.Battle.Builder tmpBuilder = StructBattle.Battle.newBuilder();
            final int tmpFieldCnt = this.battle.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattle();
            }
        }  else if (builder.hasBattle()) {
            // 清理Battle
            builder.clearBattle();
            fieldCnt++;
        }
        if (this.getState() != OutbuildingState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.buffSys != null) {
            StructBattle.BuffSys.Builder tmpBuilder = StructBattle.BuffSys.newBuilder();
            final int tmpFieldCnt = this.buffSys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuffSys();
            }
        }  else if (builder.hasBuffSys()) {
            // 清理BuffSys
            builder.clearBuffSys();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(OutbuildingEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PARTID)) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToSs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToSs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToSs(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUFFSYS) && this.buffSys != null) {
            final boolean needClear = !builder.hasBuffSys();
            final int tmpFieldCnt = this.buffSys.copyChangeToSs(builder.getBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuffSys();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(OutbuildingEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPartId()) {
            this.innerSetPartId(proto.getPartId());
        } else {
            this.innerSetPartId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromSs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromSs(proto.getPoint());
            }
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromSs(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromSs(proto.getTroop());
            }
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeFromSs(proto.getBattle());
        } else {
            if (this.battle != null) {
                this.battle.mergeFromSs(proto.getBattle());
            }
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(OutbuildingState.forNumber(0));
        }
        if (proto.hasBuffSys()) {
            this.getBuffSys().mergeFromSs(proto.getBuffSys());
        } else {
            if (this.buffSys != null) {
                this.buffSys.mergeFromSs(proto.getBuffSys());
            }
        }
        this.markAll();
        return OutbuildingProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(OutbuildingEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasPartId()) {
            this.setPartId(proto.getPartId());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromSs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromSs(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeChangeFromSs(proto.getBattle());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasBuffSys()) {
            this.getBuffSys().mergeChangeFromSs(proto.getBuffSys());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        OutbuildingEntity.Builder builder = OutbuildingEntity.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            this.point.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            this.troop.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            this.battle.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BUFFSYS) && this.buffSys != null) {
            this.buffSys.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.point != null) {
            this.point.markAll();
        }
        if (this.troop != null) {
            this.troop.markAll();
        }
        if (this.battle != null) {
            this.battle.markAll();
        }
        if (this.buffSys != null) {
            this.buffSys.markAll();
        }
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("OutbuildingProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("OutbuildingProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof OutbuildingProp)) {
            return false;
        }
        final OutbuildingProp otherNode = (OutbuildingProp) node;
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        if (this.partId != otherNode.partId) {
            return false;
        }
        if (!this.getPoint().compareDataTo(otherNode.getPoint())) {
            return false;
        }
        if (!this.getTroop().compareDataTo(otherNode.getTroop())) {
            return false;
        }
        if (!this.getBattle().compareDataTo(otherNode.getBattle())) {
            return false;
        }
        if (this.state != otherNode.state) {
            return false;
        }
        if (!this.getBuffSys().compareDataTo(otherNode.getBuffSys())) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    public static OutbuildingProp of(OutbuildingEntity fullAttrDb, OutbuildingEntity changeAttrDb) {
        OutbuildingProp prop = new OutbuildingProp();
        prop.mergeFromDb(fullAttrDb);
        prop.mergeChangeFromDb(changeAttrDb);
        prop.unMarkAll();
        return prop;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 57;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
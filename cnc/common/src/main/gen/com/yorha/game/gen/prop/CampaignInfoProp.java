package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.CampaignInfo;
import com.yorha.proto.Basic;
import com.yorha.proto.PlayerPB.CampaignInfoPB;
import com.yorha.proto.BasicPB;


/**
 * <AUTHOR> auto gen
 */
public class CampaignInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_LEVEL = 0;
    public static final int FIELD_INDEX_MAPS = 1;
    public static final int FIELD_INDEX_BLUEPRINTMAPINDEX = 2;
    public static final int FIELD_INDEX_BLUEPRINTUNLOCKID = 3;
    public static final int FIELD_INDEX_EVENTS = 4;
    public static final int FIELD_INDEX_FINISHGUIDE = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private int level = Constant.DEFAULT_INT_VALUE;
    private Int32ListProp maps = null;
    private int blueprintMapIndex = Constant.DEFAULT_INT_VALUE;
    private int blueprintUnlockId = Constant.DEFAULT_INT_VALUE;
    private Int32ListProp events = null;
    private boolean finishGuide = Constant.DEFAULT_BOOLEAN_VALUE;

    public CampaignInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public CampaignInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get level
     *
     * @return level value
     */
    public int getLevel() {
        return this.level;
    }

    /**
     * set level && set marked
     *
     * @param level new value
     * @return current object
     */
    public CampaignInfoProp setLevel(int level) {
        if (this.level != level) {
            this.mark(FIELD_INDEX_LEVEL);
            this.level = level;
        }
        return this;
    }

    /**
     * inner set level
     *
     * @param level new value
     */
    private void innerSetLevel(int level) {
        this.level = level;
    }

    /**
     * get maps
     *
     * @return maps value
     */
    public Int32ListProp getMaps() {
        if (this.maps == null) {
            this.maps = new Int32ListProp(this, FIELD_INDEX_MAPS);
        }
        return this.maps;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addMaps(Integer v) {
        this.getMaps().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getMapsIndex(int index) {
        return this.getMaps().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeMaps(Integer v) {
        if (this.maps == null) {
            return null;
        }
        if(this.maps.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getMapsSize() {
        if (this.maps == null) {
            return 0;
        }
        return this.maps.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isMapsEmpty() {
        if (this.maps == null) {
            return true;
        }
        return this.getMaps().isEmpty();
    }

    /**
     * clear list
     */
    public void clearMaps() {
        this.getMaps().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeMapsIndex(int index) {
        return this.getMaps().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setMapsIndex(int index, Integer v) {
        return this.getMaps().set(index, v);
    }
    /**
     * get blueprintMapIndex
     *
     * @return blueprintMapIndex value
     */
    public int getBlueprintMapIndex() {
        return this.blueprintMapIndex;
    }

    /**
     * set blueprintMapIndex && set marked
     *
     * @param blueprintMapIndex new value
     * @return current object
     */
    public CampaignInfoProp setBlueprintMapIndex(int blueprintMapIndex) {
        if (this.blueprintMapIndex != blueprintMapIndex) {
            this.mark(FIELD_INDEX_BLUEPRINTMAPINDEX);
            this.blueprintMapIndex = blueprintMapIndex;
        }
        return this;
    }

    /**
     * inner set blueprintMapIndex
     *
     * @param blueprintMapIndex new value
     */
    private void innerSetBlueprintMapIndex(int blueprintMapIndex) {
        this.blueprintMapIndex = blueprintMapIndex;
    }

    /**
     * get blueprintUnlockId
     *
     * @return blueprintUnlockId value
     */
    public int getBlueprintUnlockId() {
        return this.blueprintUnlockId;
    }

    /**
     * set blueprintUnlockId && set marked
     *
     * @param blueprintUnlockId new value
     * @return current object
     */
    public CampaignInfoProp setBlueprintUnlockId(int blueprintUnlockId) {
        if (this.blueprintUnlockId != blueprintUnlockId) {
            this.mark(FIELD_INDEX_BLUEPRINTUNLOCKID);
            this.blueprintUnlockId = blueprintUnlockId;
        }
        return this;
    }

    /**
     * inner set blueprintUnlockId
     *
     * @param blueprintUnlockId new value
     */
    private void innerSetBlueprintUnlockId(int blueprintUnlockId) {
        this.blueprintUnlockId = blueprintUnlockId;
    }

    /**
     * get events
     *
     * @return events value
     */
    public Int32ListProp getEvents() {
        if (this.events == null) {
            this.events = new Int32ListProp(this, FIELD_INDEX_EVENTS);
        }
        return this.events;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addEvents(Integer v) {
        this.getEvents().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getEventsIndex(int index) {
        return this.getEvents().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeEvents(Integer v) {
        if (this.events == null) {
            return null;
        }
        if(this.events.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getEventsSize() {
        if (this.events == null) {
            return 0;
        }
        return this.events.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isEventsEmpty() {
        if (this.events == null) {
            return true;
        }
        return this.getEvents().isEmpty();
    }

    /**
     * clear list
     */
    public void clearEvents() {
        this.getEvents().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeEventsIndex(int index) {
        return this.getEvents().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setEventsIndex(int index, Integer v) {
        return this.getEvents().set(index, v);
    }
    /**
     * get finishGuide
     *
     * @return finishGuide value
     */
    public boolean getFinishGuide() {
        return this.finishGuide;
    }

    /**
     * set finishGuide && set marked
     *
     * @param finishGuide new value
     * @return current object
     */
    public CampaignInfoProp setFinishGuide(boolean finishGuide) {
        if (this.finishGuide != finishGuide) {
            this.mark(FIELD_INDEX_FINISHGUIDE);
            this.finishGuide = finishGuide;
        }
        return this;
    }

    /**
     * inner set finishGuide
     *
     * @param finishGuide new value
     */
    private void innerSetFinishGuide(boolean finishGuide) {
        this.finishGuide = finishGuide;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignInfoPB.Builder getCopyCsBuilder() {
        final CampaignInfoPB.Builder builder = CampaignInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(CampaignInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.maps != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.maps.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMaps(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMaps();
            }
        }  else if (builder.hasMaps()) {
            // 清理Maps
            builder.clearMaps();
            fieldCnt++;
        }
        if (this.getBlueprintMapIndex() != 0) {
            builder.setBlueprintMapIndex(this.getBlueprintMapIndex());
            fieldCnt++;
        }  else if (builder.hasBlueprintMapIndex()) {
            // 清理BlueprintMapIndex
            builder.clearBlueprintMapIndex();
            fieldCnt++;
        }
        if (this.getBlueprintUnlockId() != 0) {
            builder.setBlueprintUnlockId(this.getBlueprintUnlockId());
            fieldCnt++;
        }  else if (builder.hasBlueprintUnlockId()) {
            // 清理BlueprintUnlockId
            builder.clearBlueprintUnlockId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(CampaignInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPS) && this.maps != null) {
            final boolean needClear = !builder.hasMaps();
            final int tmpFieldCnt = this.maps.copyChangeToCs(builder.getMapsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMaps();
            }
        }
        if (this.hasMark(FIELD_INDEX_BLUEPRINTMAPINDEX)) {
            builder.setBlueprintMapIndex(this.getBlueprintMapIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BLUEPRINTUNLOCKID)) {
            builder.setBlueprintUnlockId(this.getBlueprintUnlockId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(CampaignInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPS) && this.maps != null) {
            final boolean needClear = !builder.hasMaps();
            final int tmpFieldCnt = this.maps.copyChangeToCs(builder.getMapsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMaps();
            }
        }
        if (this.hasMark(FIELD_INDEX_BLUEPRINTMAPINDEX)) {
            builder.setBlueprintMapIndex(this.getBlueprintMapIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BLUEPRINTUNLOCKID)) {
            builder.setBlueprintUnlockId(this.getBlueprintUnlockId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(CampaignInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMaps()) {
            this.getMaps().mergeFromCs(proto.getMaps());
        } else {
            if (this.maps != null) {
                this.maps.mergeFromCs(proto.getMaps());
            }
        }
        if (proto.hasBlueprintMapIndex()) {
            this.innerSetBlueprintMapIndex(proto.getBlueprintMapIndex());
        } else {
            this.innerSetBlueprintMapIndex(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBlueprintUnlockId()) {
            this.innerSetBlueprintUnlockId(proto.getBlueprintUnlockId());
        } else {
            this.innerSetBlueprintUnlockId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return CampaignInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(CampaignInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasMaps()) {
            this.getMaps().mergeChangeFromCs(proto.getMaps());
            fieldCnt++;
        }
        if (proto.hasBlueprintMapIndex()) {
            this.setBlueprintMapIndex(proto.getBlueprintMapIndex());
            fieldCnt++;
        }
        if (proto.hasBlueprintUnlockId()) {
            this.setBlueprintUnlockId(proto.getBlueprintUnlockId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignInfo.Builder getCopyDbBuilder() {
        final CampaignInfo.Builder builder = CampaignInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(CampaignInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.maps != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.maps.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMaps(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMaps();
            }
        }  else if (builder.hasMaps()) {
            // 清理Maps
            builder.clearMaps();
            fieldCnt++;
        }
        if (this.getBlueprintMapIndex() != 0) {
            builder.setBlueprintMapIndex(this.getBlueprintMapIndex());
            fieldCnt++;
        }  else if (builder.hasBlueprintMapIndex()) {
            // 清理BlueprintMapIndex
            builder.clearBlueprintMapIndex();
            fieldCnt++;
        }
        if (this.getBlueprintUnlockId() != 0) {
            builder.setBlueprintUnlockId(this.getBlueprintUnlockId());
            fieldCnt++;
        }  else if (builder.hasBlueprintUnlockId()) {
            // 清理BlueprintUnlockId
            builder.clearBlueprintUnlockId();
            fieldCnt++;
        }
        if (this.events != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.events.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setEvents(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearEvents();
            }
        }  else if (builder.hasEvents()) {
            // 清理Events
            builder.clearEvents();
            fieldCnt++;
        }
        if (this.getFinishGuide()) {
            builder.setFinishGuide(this.getFinishGuide());
            fieldCnt++;
        }  else if (builder.hasFinishGuide()) {
            // 清理FinishGuide
            builder.clearFinishGuide();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(CampaignInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPS) && this.maps != null) {
            final boolean needClear = !builder.hasMaps();
            final int tmpFieldCnt = this.maps.copyChangeToDb(builder.getMapsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMaps();
            }
        }
        if (this.hasMark(FIELD_INDEX_BLUEPRINTMAPINDEX)) {
            builder.setBlueprintMapIndex(this.getBlueprintMapIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BLUEPRINTUNLOCKID)) {
            builder.setBlueprintUnlockId(this.getBlueprintUnlockId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EVENTS) && this.events != null) {
            final boolean needClear = !builder.hasEvents();
            final int tmpFieldCnt = this.events.copyChangeToDb(builder.getEventsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearEvents();
            }
        }
        if (this.hasMark(FIELD_INDEX_FINISHGUIDE)) {
            builder.setFinishGuide(this.getFinishGuide());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(CampaignInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMaps()) {
            this.getMaps().mergeFromDb(proto.getMaps());
        } else {
            if (this.maps != null) {
                this.maps.mergeFromDb(proto.getMaps());
            }
        }
        if (proto.hasBlueprintMapIndex()) {
            this.innerSetBlueprintMapIndex(proto.getBlueprintMapIndex());
        } else {
            this.innerSetBlueprintMapIndex(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBlueprintUnlockId()) {
            this.innerSetBlueprintUnlockId(proto.getBlueprintUnlockId());
        } else {
            this.innerSetBlueprintUnlockId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEvents()) {
            this.getEvents().mergeFromDb(proto.getEvents());
        } else {
            if (this.events != null) {
                this.events.mergeFromDb(proto.getEvents());
            }
        }
        if (proto.hasFinishGuide()) {
            this.innerSetFinishGuide(proto.getFinishGuide());
        } else {
            this.innerSetFinishGuide(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return CampaignInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(CampaignInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasMaps()) {
            this.getMaps().mergeChangeFromDb(proto.getMaps());
            fieldCnt++;
        }
        if (proto.hasBlueprintMapIndex()) {
            this.setBlueprintMapIndex(proto.getBlueprintMapIndex());
            fieldCnt++;
        }
        if (proto.hasBlueprintUnlockId()) {
            this.setBlueprintUnlockId(proto.getBlueprintUnlockId());
            fieldCnt++;
        }
        if (proto.hasEvents()) {
            this.getEvents().mergeChangeFromDb(proto.getEvents());
            fieldCnt++;
        }
        if (proto.hasFinishGuide()) {
            this.setFinishGuide(proto.getFinishGuide());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignInfo.Builder getCopySsBuilder() {
        final CampaignInfo.Builder builder = CampaignInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(CampaignInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.maps != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.maps.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMaps(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMaps();
            }
        }  else if (builder.hasMaps()) {
            // 清理Maps
            builder.clearMaps();
            fieldCnt++;
        }
        if (this.getBlueprintMapIndex() != 0) {
            builder.setBlueprintMapIndex(this.getBlueprintMapIndex());
            fieldCnt++;
        }  else if (builder.hasBlueprintMapIndex()) {
            // 清理BlueprintMapIndex
            builder.clearBlueprintMapIndex();
            fieldCnt++;
        }
        if (this.getBlueprintUnlockId() != 0) {
            builder.setBlueprintUnlockId(this.getBlueprintUnlockId());
            fieldCnt++;
        }  else if (builder.hasBlueprintUnlockId()) {
            // 清理BlueprintUnlockId
            builder.clearBlueprintUnlockId();
            fieldCnt++;
        }
        if (this.events != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.events.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setEvents(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearEvents();
            }
        }  else if (builder.hasEvents()) {
            // 清理Events
            builder.clearEvents();
            fieldCnt++;
        }
        if (this.getFinishGuide()) {
            builder.setFinishGuide(this.getFinishGuide());
            fieldCnt++;
        }  else if (builder.hasFinishGuide()) {
            // 清理FinishGuide
            builder.clearFinishGuide();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(CampaignInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPS) && this.maps != null) {
            final boolean needClear = !builder.hasMaps();
            final int tmpFieldCnt = this.maps.copyChangeToSs(builder.getMapsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMaps();
            }
        }
        if (this.hasMark(FIELD_INDEX_BLUEPRINTMAPINDEX)) {
            builder.setBlueprintMapIndex(this.getBlueprintMapIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BLUEPRINTUNLOCKID)) {
            builder.setBlueprintUnlockId(this.getBlueprintUnlockId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EVENTS) && this.events != null) {
            final boolean needClear = !builder.hasEvents();
            final int tmpFieldCnt = this.events.copyChangeToSs(builder.getEventsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearEvents();
            }
        }
        if (this.hasMark(FIELD_INDEX_FINISHGUIDE)) {
            builder.setFinishGuide(this.getFinishGuide());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(CampaignInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMaps()) {
            this.getMaps().mergeFromSs(proto.getMaps());
        } else {
            if (this.maps != null) {
                this.maps.mergeFromSs(proto.getMaps());
            }
        }
        if (proto.hasBlueprintMapIndex()) {
            this.innerSetBlueprintMapIndex(proto.getBlueprintMapIndex());
        } else {
            this.innerSetBlueprintMapIndex(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBlueprintUnlockId()) {
            this.innerSetBlueprintUnlockId(proto.getBlueprintUnlockId());
        } else {
            this.innerSetBlueprintUnlockId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEvents()) {
            this.getEvents().mergeFromSs(proto.getEvents());
        } else {
            if (this.events != null) {
                this.events.mergeFromSs(proto.getEvents());
            }
        }
        if (proto.hasFinishGuide()) {
            this.innerSetFinishGuide(proto.getFinishGuide());
        } else {
            this.innerSetFinishGuide(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return CampaignInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(CampaignInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasMaps()) {
            this.getMaps().mergeChangeFromSs(proto.getMaps());
            fieldCnt++;
        }
        if (proto.hasBlueprintMapIndex()) {
            this.setBlueprintMapIndex(proto.getBlueprintMapIndex());
            fieldCnt++;
        }
        if (proto.hasBlueprintUnlockId()) {
            this.setBlueprintUnlockId(proto.getBlueprintUnlockId());
            fieldCnt++;
        }
        if (proto.hasEvents()) {
            this.getEvents().mergeChangeFromSs(proto.getEvents());
            fieldCnt++;
        }
        if (proto.hasFinishGuide()) {
            this.setFinishGuide(proto.getFinishGuide());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        CampaignInfo.Builder builder = CampaignInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_MAPS) && this.maps != null) {
            this.maps.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_EVENTS) && this.events != null) {
            this.events.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.maps != null) {
            this.maps.markAll();
        }
        if (this.events != null) {
            this.events.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof CampaignInfoProp)) {
            return false;
        }
        final CampaignInfoProp otherNode = (CampaignInfoProp) node;
        if (this.level != otherNode.level) {
            return false;
        }
        if (!this.getMaps().compareDataTo(otherNode.getMaps())) {
            return false;
        }
        if (this.blueprintMapIndex != otherNode.blueprintMapIndex) {
            return false;
        }
        if (this.blueprintUnlockId != otherNode.blueprintUnlockId) {
            return false;
        }
        if (!this.getEvents().compareDataTo(otherNode.getEvents())) {
            return false;
        }
        if (this.finishGuide != otherNode.finishGuide) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.HistoryFragments;
import com.yorha.proto.Player;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.HistoryFragmentsPB;
import com.yorha.proto.PlayerPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class HistoryFragmentsProp extends AbstractPropNode {

    public static final int FIELD_INDEX_BIGSCENERECORDS = 0;
    public static final int FIELD_INDEX_SCHEDULEEXECUTERECORDS = 1;
    public static final int FIELD_INDEX_ASSISTHISTORY = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private BigSceneRecordsProp bigSceneRecords = null;
    private Int32ExecuteTimeMapProp scheduleExecuteRecords = null;
    private AssistRecordListProp assistHistory = null;

    public HistoryFragmentsProp() {
        super(null, 0, FIELD_COUNT);
    }

    public HistoryFragmentsProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get bigSceneRecords
     *
     * @return bigSceneRecords value
     */
    public BigSceneRecordsProp getBigSceneRecords() {
        if (this.bigSceneRecords == null) {
            this.bigSceneRecords = new BigSceneRecordsProp(this, FIELD_INDEX_BIGSCENERECORDS);
        }
        return this.bigSceneRecords;
    }

    /**
     * get scheduleExecuteRecords
     *
     * @return scheduleExecuteRecords value
     */
    public Int32ExecuteTimeMapProp getScheduleExecuteRecords() {
        if (this.scheduleExecuteRecords == null) {
            this.scheduleExecuteRecords = new Int32ExecuteTimeMapProp(this, FIELD_INDEX_SCHEDULEEXECUTERECORDS);
        }
        return this.scheduleExecuteRecords;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putScheduleExecuteRecordsV(ExecuteTimeProp v) {
        this.getScheduleExecuteRecords().put(v.getType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ExecuteTimeProp addEmptyScheduleExecuteRecords(Integer k) {
        return this.getScheduleExecuteRecords().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getScheduleExecuteRecordsSize() {
        if (this.scheduleExecuteRecords == null) {
            return 0;
        }
        return this.scheduleExecuteRecords.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isScheduleExecuteRecordsEmpty() {
        if (this.scheduleExecuteRecords == null) {
            return true;
        }
        return this.scheduleExecuteRecords.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ExecuteTimeProp getScheduleExecuteRecordsV(Integer k) {
        if (this.scheduleExecuteRecords == null || !this.scheduleExecuteRecords.containsKey(k)) {
            return null;
        }
        return this.scheduleExecuteRecords.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearScheduleExecuteRecords() {
        if (this.scheduleExecuteRecords != null) {
            this.scheduleExecuteRecords.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeScheduleExecuteRecordsV(Integer k) {
        if (this.scheduleExecuteRecords != null) {
            this.scheduleExecuteRecords.remove(k);
        }
    }
    /**
     * get assistHistory
     *
     * @return assistHistory value
     */
    public AssistRecordListProp getAssistHistory() {
        if (this.assistHistory == null) {
            this.assistHistory = new AssistRecordListProp(this, FIELD_INDEX_ASSISTHISTORY);
        }
        return this.assistHistory;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addAssistHistory(AssistRecordProp v) {
        this.getAssistHistory().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public AssistRecordProp getAssistHistoryIndex(int index) {
        return this.getAssistHistory().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public AssistRecordProp removeAssistHistory(AssistRecordProp v) {
        if (this.assistHistory == null) {
            return null;
        }
        if(this.assistHistory.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getAssistHistorySize() {
        if (this.assistHistory == null) {
            return 0;
        }
        return this.assistHistory.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isAssistHistoryEmpty() {
        if (this.assistHistory == null) {
            return true;
        }
        return this.getAssistHistory().isEmpty();
    }

    /**
     * clear list
     */
    public void clearAssistHistory() {
        this.getAssistHistory().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public AssistRecordProp removeAssistHistoryIndex(int index) {
        return this.getAssistHistory().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public AssistRecordProp setAssistHistoryIndex(int index, AssistRecordProp v) {
        return this.getAssistHistory().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public HistoryFragmentsPB.Builder getCopyCsBuilder() {
        final HistoryFragmentsPB.Builder builder = HistoryFragmentsPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(HistoryFragmentsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.bigSceneRecords != null) {
            PlayerPB.BigSceneRecordsPB.Builder tmpBuilder = PlayerPB.BigSceneRecordsPB.newBuilder();
            final int tmpFieldCnt = this.bigSceneRecords.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBigSceneRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBigSceneRecords();
            }
        }  else if (builder.hasBigSceneRecords()) {
            // 清理BigSceneRecords
            builder.clearBigSceneRecords();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(HistoryFragmentsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BIGSCENERECORDS) && this.bigSceneRecords != null) {
            final boolean needClear = !builder.hasBigSceneRecords();
            final int tmpFieldCnt = this.bigSceneRecords.copyChangeToCs(builder.getBigSceneRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBigSceneRecords();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(HistoryFragmentsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BIGSCENERECORDS) && this.bigSceneRecords != null) {
            final boolean needClear = !builder.hasBigSceneRecords();
            final int tmpFieldCnt = this.bigSceneRecords.copyChangeToAndClearDeleteKeysCs(builder.getBigSceneRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBigSceneRecords();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(HistoryFragmentsPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBigSceneRecords()) {
            this.getBigSceneRecords().mergeFromCs(proto.getBigSceneRecords());
        } else {
            if (this.bigSceneRecords != null) {
                this.bigSceneRecords.mergeFromCs(proto.getBigSceneRecords());
            }
        }
        this.markAll();
        return HistoryFragmentsProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(HistoryFragmentsPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBigSceneRecords()) {
            this.getBigSceneRecords().mergeChangeFromCs(proto.getBigSceneRecords());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public HistoryFragments.Builder getCopyDbBuilder() {
        final HistoryFragments.Builder builder = HistoryFragments.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(HistoryFragments.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.bigSceneRecords != null) {
            Player.BigSceneRecords.Builder tmpBuilder = Player.BigSceneRecords.newBuilder();
            final int tmpFieldCnt = this.bigSceneRecords.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBigSceneRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBigSceneRecords();
            }
        }  else if (builder.hasBigSceneRecords()) {
            // 清理BigSceneRecords
            builder.clearBigSceneRecords();
            fieldCnt++;
        }
        if (this.scheduleExecuteRecords != null) {
            Struct.Int32ExecuteTimeMap.Builder tmpBuilder = Struct.Int32ExecuteTimeMap.newBuilder();
            final int tmpFieldCnt = this.scheduleExecuteRecords.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScheduleExecuteRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScheduleExecuteRecords();
            }
        }  else if (builder.hasScheduleExecuteRecords()) {
            // 清理ScheduleExecuteRecords
            builder.clearScheduleExecuteRecords();
            fieldCnt++;
        }
        if (this.assistHistory != null) {
            Struct.AssistRecordList.Builder tmpBuilder = Struct.AssistRecordList.newBuilder();
            final int tmpFieldCnt = this.assistHistory.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAssistHistory(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAssistHistory();
            }
        }  else if (builder.hasAssistHistory()) {
            // 清理AssistHistory
            builder.clearAssistHistory();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(HistoryFragments.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BIGSCENERECORDS) && this.bigSceneRecords != null) {
            final boolean needClear = !builder.hasBigSceneRecords();
            final int tmpFieldCnt = this.bigSceneRecords.copyChangeToDb(builder.getBigSceneRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBigSceneRecords();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCHEDULEEXECUTERECORDS) && this.scheduleExecuteRecords != null) {
            final boolean needClear = !builder.hasScheduleExecuteRecords();
            final int tmpFieldCnt = this.scheduleExecuteRecords.copyChangeToDb(builder.getScheduleExecuteRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScheduleExecuteRecords();
            }
        }
        if (this.hasMark(FIELD_INDEX_ASSISTHISTORY) && this.assistHistory != null) {
            final boolean needClear = !builder.hasAssistHistory();
            final int tmpFieldCnt = this.assistHistory.copyChangeToDb(builder.getAssistHistoryBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAssistHistory();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(HistoryFragments proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBigSceneRecords()) {
            this.getBigSceneRecords().mergeFromDb(proto.getBigSceneRecords());
        } else {
            if (this.bigSceneRecords != null) {
                this.bigSceneRecords.mergeFromDb(proto.getBigSceneRecords());
            }
        }
        if (proto.hasScheduleExecuteRecords()) {
            this.getScheduleExecuteRecords().mergeFromDb(proto.getScheduleExecuteRecords());
        } else {
            if (this.scheduleExecuteRecords != null) {
                this.scheduleExecuteRecords.mergeFromDb(proto.getScheduleExecuteRecords());
            }
        }
        if (proto.hasAssistHistory()) {
            this.getAssistHistory().mergeFromDb(proto.getAssistHistory());
        } else {
            if (this.assistHistory != null) {
                this.assistHistory.mergeFromDb(proto.getAssistHistory());
            }
        }
        this.markAll();
        return HistoryFragmentsProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(HistoryFragments proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBigSceneRecords()) {
            this.getBigSceneRecords().mergeChangeFromDb(proto.getBigSceneRecords());
            fieldCnt++;
        }
        if (proto.hasScheduleExecuteRecords()) {
            this.getScheduleExecuteRecords().mergeChangeFromDb(proto.getScheduleExecuteRecords());
            fieldCnt++;
        }
        if (proto.hasAssistHistory()) {
            this.getAssistHistory().mergeChangeFromDb(proto.getAssistHistory());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public HistoryFragments.Builder getCopySsBuilder() {
        final HistoryFragments.Builder builder = HistoryFragments.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(HistoryFragments.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.bigSceneRecords != null) {
            Player.BigSceneRecords.Builder tmpBuilder = Player.BigSceneRecords.newBuilder();
            final int tmpFieldCnt = this.bigSceneRecords.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBigSceneRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBigSceneRecords();
            }
        }  else if (builder.hasBigSceneRecords()) {
            // 清理BigSceneRecords
            builder.clearBigSceneRecords();
            fieldCnt++;
        }
        if (this.scheduleExecuteRecords != null) {
            Struct.Int32ExecuteTimeMap.Builder tmpBuilder = Struct.Int32ExecuteTimeMap.newBuilder();
            final int tmpFieldCnt = this.scheduleExecuteRecords.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScheduleExecuteRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScheduleExecuteRecords();
            }
        }  else if (builder.hasScheduleExecuteRecords()) {
            // 清理ScheduleExecuteRecords
            builder.clearScheduleExecuteRecords();
            fieldCnt++;
        }
        if (this.assistHistory != null) {
            Struct.AssistRecordList.Builder tmpBuilder = Struct.AssistRecordList.newBuilder();
            final int tmpFieldCnt = this.assistHistory.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAssistHistory(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAssistHistory();
            }
        }  else if (builder.hasAssistHistory()) {
            // 清理AssistHistory
            builder.clearAssistHistory();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(HistoryFragments.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BIGSCENERECORDS) && this.bigSceneRecords != null) {
            final boolean needClear = !builder.hasBigSceneRecords();
            final int tmpFieldCnt = this.bigSceneRecords.copyChangeToSs(builder.getBigSceneRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBigSceneRecords();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCHEDULEEXECUTERECORDS) && this.scheduleExecuteRecords != null) {
            final boolean needClear = !builder.hasScheduleExecuteRecords();
            final int tmpFieldCnt = this.scheduleExecuteRecords.copyChangeToSs(builder.getScheduleExecuteRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScheduleExecuteRecords();
            }
        }
        if (this.hasMark(FIELD_INDEX_ASSISTHISTORY) && this.assistHistory != null) {
            final boolean needClear = !builder.hasAssistHistory();
            final int tmpFieldCnt = this.assistHistory.copyChangeToSs(builder.getAssistHistoryBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAssistHistory();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(HistoryFragments proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBigSceneRecords()) {
            this.getBigSceneRecords().mergeFromSs(proto.getBigSceneRecords());
        } else {
            if (this.bigSceneRecords != null) {
                this.bigSceneRecords.mergeFromSs(proto.getBigSceneRecords());
            }
        }
        if (proto.hasScheduleExecuteRecords()) {
            this.getScheduleExecuteRecords().mergeFromSs(proto.getScheduleExecuteRecords());
        } else {
            if (this.scheduleExecuteRecords != null) {
                this.scheduleExecuteRecords.mergeFromSs(proto.getScheduleExecuteRecords());
            }
        }
        if (proto.hasAssistHistory()) {
            this.getAssistHistory().mergeFromSs(proto.getAssistHistory());
        } else {
            if (this.assistHistory != null) {
                this.assistHistory.mergeFromSs(proto.getAssistHistory());
            }
        }
        this.markAll();
        return HistoryFragmentsProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(HistoryFragments proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBigSceneRecords()) {
            this.getBigSceneRecords().mergeChangeFromSs(proto.getBigSceneRecords());
            fieldCnt++;
        }
        if (proto.hasScheduleExecuteRecords()) {
            this.getScheduleExecuteRecords().mergeChangeFromSs(proto.getScheduleExecuteRecords());
            fieldCnt++;
        }
        if (proto.hasAssistHistory()) {
            this.getAssistHistory().mergeChangeFromSs(proto.getAssistHistory());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        HistoryFragments.Builder builder = HistoryFragments.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_BIGSCENERECORDS) && this.bigSceneRecords != null) {
            this.bigSceneRecords.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SCHEDULEEXECUTERECORDS) && this.scheduleExecuteRecords != null) {
            this.scheduleExecuteRecords.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ASSISTHISTORY) && this.assistHistory != null) {
            this.assistHistory.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.bigSceneRecords != null) {
            this.bigSceneRecords.markAll();
        }
        if (this.scheduleExecuteRecords != null) {
            this.scheduleExecuteRecords.markAll();
        }
        if (this.assistHistory != null) {
            this.assistHistory.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof HistoryFragmentsProp)) {
            return false;
        }
        final HistoryFragmentsProp otherNode = (HistoryFragmentsProp) node;
        if (!this.getBigSceneRecords().compareDataTo(otherNode.getBigSceneRecords())) {
            return false;
        }
        if (!this.getScheduleExecuteRecords().compareDataTo(otherNode.getScheduleExecuteRecords())) {
            return false;
        }
        if (!this.getAssistHistory().compareDataTo(otherNode.getAssistHistory())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
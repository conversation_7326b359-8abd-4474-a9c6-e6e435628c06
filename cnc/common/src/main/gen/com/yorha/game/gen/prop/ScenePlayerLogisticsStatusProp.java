package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.ScenePlayerLogisticsStatus;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.ScenePlayerLogisticsStatusPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ScenePlayerLogisticsStatusProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_LOGISTICSPLANEID = 0;
    public static final int FIELD_INDEX_STARTTSMS = 1;
    public static final int FIELD_INDEX_ENDTSMS = 2;
    public static final int FIELD_INDEX_RESOURCES = 3;
    public static final int FIELD_INDEX_TAX = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private long LogisticsPlaneId = Constant.DEFAULT_LONG_VALUE;
    private long startTsMs = Constant.DEFAULT_LONG_VALUE;
    private long endTsMs = Constant.DEFAULT_LONG_VALUE;
    private Int32CurrencyMapProp resources = null;
    private Int32CurrencyMapProp tax = null;

    public ScenePlayerLogisticsStatusProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ScenePlayerLogisticsStatusProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get LogisticsPlaneId
     *
     * @return LogisticsPlaneId value
     */
    public long getLogisticsPlaneId() {
        return this.LogisticsPlaneId;
    }

    /**
     * set LogisticsPlaneId && set marked
     *
     * @param LogisticsPlaneId new value
     * @return current object
     */
    public ScenePlayerLogisticsStatusProp setLogisticsPlaneId(long LogisticsPlaneId) {
        if (this.LogisticsPlaneId != LogisticsPlaneId) {
            this.mark(FIELD_INDEX_LOGISTICSPLANEID);
            this.LogisticsPlaneId = LogisticsPlaneId;
        }
        return this;
    }

    /**
     * inner set LogisticsPlaneId
     *
     * @param LogisticsPlaneId new value
     */
    private void innerSetLogisticsPlaneId(long LogisticsPlaneId) {
        this.LogisticsPlaneId = LogisticsPlaneId;
    }

    /**
     * get startTsMs
     *
     * @return startTsMs value
     */
    public long getStartTsMs() {
        return this.startTsMs;
    }

    /**
     * set startTsMs && set marked
     *
     * @param startTsMs new value
     * @return current object
     */
    public ScenePlayerLogisticsStatusProp setStartTsMs(long startTsMs) {
        if (this.startTsMs != startTsMs) {
            this.mark(FIELD_INDEX_STARTTSMS);
            this.startTsMs = startTsMs;
        }
        return this;
    }

    /**
     * inner set startTsMs
     *
     * @param startTsMs new value
     */
    private void innerSetStartTsMs(long startTsMs) {
        this.startTsMs = startTsMs;
    }

    /**
     * get endTsMs
     *
     * @return endTsMs value
     */
    public long getEndTsMs() {
        return this.endTsMs;
    }

    /**
     * set endTsMs && set marked
     *
     * @param endTsMs new value
     * @return current object
     */
    public ScenePlayerLogisticsStatusProp setEndTsMs(long endTsMs) {
        if (this.endTsMs != endTsMs) {
            this.mark(FIELD_INDEX_ENDTSMS);
            this.endTsMs = endTsMs;
        }
        return this;
    }

    /**
     * inner set endTsMs
     *
     * @param endTsMs new value
     */
    private void innerSetEndTsMs(long endTsMs) {
        this.endTsMs = endTsMs;
    }

    /**
     * get resources
     *
     * @return resources value
     */
    public Int32CurrencyMapProp getResources() {
        if (this.resources == null) {
            this.resources = new Int32CurrencyMapProp(this, FIELD_INDEX_RESOURCES);
        }
        return this.resources;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putResourcesV(CurrencyProp v) {
        this.getResources().put(v.getType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public CurrencyProp addEmptyResources(Integer k) {
        return this.getResources().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getResourcesSize() {
        if (this.resources == null) {
            return 0;
        }
        return this.resources.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isResourcesEmpty() {
        if (this.resources == null) {
            return true;
        }
        return this.resources.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public CurrencyProp getResourcesV(Integer k) {
        if (this.resources == null || !this.resources.containsKey(k)) {
            return null;
        }
        return this.resources.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearResources() {
        if (this.resources != null) {
            this.resources.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeResourcesV(Integer k) {
        if (this.resources != null) {
            this.resources.remove(k);
        }
    }
    /**
     * get tax
     *
     * @return tax value
     */
    public Int32CurrencyMapProp getTax() {
        if (this.tax == null) {
            this.tax = new Int32CurrencyMapProp(this, FIELD_INDEX_TAX);
        }
        return this.tax;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTaxV(CurrencyProp v) {
        this.getTax().put(v.getType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public CurrencyProp addEmptyTax(Integer k) {
        return this.getTax().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTaxSize() {
        if (this.tax == null) {
            return 0;
        }
        return this.tax.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTaxEmpty() {
        if (this.tax == null) {
            return true;
        }
        return this.tax.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public CurrencyProp getTaxV(Integer k) {
        if (this.tax == null || !this.tax.containsKey(k)) {
            return null;
        }
        return this.tax.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTax() {
        if (this.tax != null) {
            this.tax.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTaxV(Integer k) {
        if (this.tax != null) {
            this.tax.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerLogisticsStatusPB.Builder getCopyCsBuilder() {
        final ScenePlayerLogisticsStatusPB.Builder builder = ScenePlayerLogisticsStatusPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ScenePlayerLogisticsStatusPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLogisticsPlaneId() != 0L) {
            builder.setLogisticsPlaneId(this.getLogisticsPlaneId());
            fieldCnt++;
        }  else if (builder.hasLogisticsPlaneId()) {
            // 清理LogisticsPlaneId
            builder.clearLogisticsPlaneId();
            fieldCnt++;
        }
        if (this.getStartTsMs() != 0L) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStartTsMs()) {
            // 清理StartTsMs
            builder.clearStartTsMs();
            fieldCnt++;
        }
        if (this.getEndTsMs() != 0L) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }  else if (builder.hasEndTsMs()) {
            // 清理EndTsMs
            builder.clearEndTsMs();
            fieldCnt++;
        }
        if (this.resources != null) {
            StructPB.Int32CurrencyMapPB.Builder tmpBuilder = StructPB.Int32CurrencyMapPB.newBuilder();
            final int tmpFieldCnt = this.resources.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResources(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResources();
            }
        }  else if (builder.hasResources()) {
            // 清理Resources
            builder.clearResources();
            fieldCnt++;
        }
        if (this.tax != null) {
            StructPB.Int32CurrencyMapPB.Builder tmpBuilder = StructPB.Int32CurrencyMapPB.newBuilder();
            final int tmpFieldCnt = this.tax.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTax(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTax();
            }
        }  else if (builder.hasTax()) {
            // 清理Tax
            builder.clearTax();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ScenePlayerLogisticsStatusPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LOGISTICSPLANEID)) {
            builder.setLogisticsPlaneId(this.getLogisticsPlaneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            final boolean needClear = !builder.hasResources();
            final int tmpFieldCnt = this.resources.copyChangeToCs(builder.getResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResources();
            }
        }
        if (this.hasMark(FIELD_INDEX_TAX) && this.tax != null) {
            final boolean needClear = !builder.hasTax();
            final int tmpFieldCnt = this.tax.copyChangeToCs(builder.getTaxBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTax();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ScenePlayerLogisticsStatusPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LOGISTICSPLANEID)) {
            builder.setLogisticsPlaneId(this.getLogisticsPlaneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            final boolean needClear = !builder.hasResources();
            final int tmpFieldCnt = this.resources.copyChangeToAndClearDeleteKeysCs(builder.getResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResources();
            }
        }
        if (this.hasMark(FIELD_INDEX_TAX) && this.tax != null) {
            final boolean needClear = !builder.hasTax();
            final int tmpFieldCnt = this.tax.copyChangeToAndClearDeleteKeysCs(builder.getTaxBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTax();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ScenePlayerLogisticsStatusPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLogisticsPlaneId()) {
            this.innerSetLogisticsPlaneId(proto.getLogisticsPlaneId());
        } else {
            this.innerSetLogisticsPlaneId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStartTsMs()) {
            this.innerSetStartTsMs(proto.getStartTsMs());
        } else {
            this.innerSetStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTsMs()) {
            this.innerSetEndTsMs(proto.getEndTsMs());
        } else {
            this.innerSetEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasResources()) {
            this.getResources().mergeFromCs(proto.getResources());
        } else {
            if (this.resources != null) {
                this.resources.mergeFromCs(proto.getResources());
            }
        }
        if (proto.hasTax()) {
            this.getTax().mergeFromCs(proto.getTax());
        } else {
            if (this.tax != null) {
                this.tax.mergeFromCs(proto.getTax());
            }
        }
        this.markAll();
        return ScenePlayerLogisticsStatusProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ScenePlayerLogisticsStatusPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLogisticsPlaneId()) {
            this.setLogisticsPlaneId(proto.getLogisticsPlaneId());
            fieldCnt++;
        }
        if (proto.hasStartTsMs()) {
            this.setStartTsMs(proto.getStartTsMs());
            fieldCnt++;
        }
        if (proto.hasEndTsMs()) {
            this.setEndTsMs(proto.getEndTsMs());
            fieldCnt++;
        }
        if (proto.hasResources()) {
            this.getResources().mergeChangeFromCs(proto.getResources());
            fieldCnt++;
        }
        if (proto.hasTax()) {
            this.getTax().mergeChangeFromCs(proto.getTax());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerLogisticsStatus.Builder getCopyDbBuilder() {
        final ScenePlayerLogisticsStatus.Builder builder = ScenePlayerLogisticsStatus.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ScenePlayerLogisticsStatus.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLogisticsPlaneId() != 0L) {
            builder.setLogisticsPlaneId(this.getLogisticsPlaneId());
            fieldCnt++;
        }  else if (builder.hasLogisticsPlaneId()) {
            // 清理LogisticsPlaneId
            builder.clearLogisticsPlaneId();
            fieldCnt++;
        }
        if (this.getStartTsMs() != 0L) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStartTsMs()) {
            // 清理StartTsMs
            builder.clearStartTsMs();
            fieldCnt++;
        }
        if (this.getEndTsMs() != 0L) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }  else if (builder.hasEndTsMs()) {
            // 清理EndTsMs
            builder.clearEndTsMs();
            fieldCnt++;
        }
        if (this.resources != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.resources.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResources(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResources();
            }
        }  else if (builder.hasResources()) {
            // 清理Resources
            builder.clearResources();
            fieldCnt++;
        }
        if (this.tax != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.tax.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTax(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTax();
            }
        }  else if (builder.hasTax()) {
            // 清理Tax
            builder.clearTax();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ScenePlayerLogisticsStatus.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LOGISTICSPLANEID)) {
            builder.setLogisticsPlaneId(this.getLogisticsPlaneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            final boolean needClear = !builder.hasResources();
            final int tmpFieldCnt = this.resources.copyChangeToDb(builder.getResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResources();
            }
        }
        if (this.hasMark(FIELD_INDEX_TAX) && this.tax != null) {
            final boolean needClear = !builder.hasTax();
            final int tmpFieldCnt = this.tax.copyChangeToDb(builder.getTaxBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTax();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ScenePlayerLogisticsStatus proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLogisticsPlaneId()) {
            this.innerSetLogisticsPlaneId(proto.getLogisticsPlaneId());
        } else {
            this.innerSetLogisticsPlaneId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStartTsMs()) {
            this.innerSetStartTsMs(proto.getStartTsMs());
        } else {
            this.innerSetStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTsMs()) {
            this.innerSetEndTsMs(proto.getEndTsMs());
        } else {
            this.innerSetEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasResources()) {
            this.getResources().mergeFromDb(proto.getResources());
        } else {
            if (this.resources != null) {
                this.resources.mergeFromDb(proto.getResources());
            }
        }
        if (proto.hasTax()) {
            this.getTax().mergeFromDb(proto.getTax());
        } else {
            if (this.tax != null) {
                this.tax.mergeFromDb(proto.getTax());
            }
        }
        this.markAll();
        return ScenePlayerLogisticsStatusProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ScenePlayerLogisticsStatus proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLogisticsPlaneId()) {
            this.setLogisticsPlaneId(proto.getLogisticsPlaneId());
            fieldCnt++;
        }
        if (proto.hasStartTsMs()) {
            this.setStartTsMs(proto.getStartTsMs());
            fieldCnt++;
        }
        if (proto.hasEndTsMs()) {
            this.setEndTsMs(proto.getEndTsMs());
            fieldCnt++;
        }
        if (proto.hasResources()) {
            this.getResources().mergeChangeFromDb(proto.getResources());
            fieldCnt++;
        }
        if (proto.hasTax()) {
            this.getTax().mergeChangeFromDb(proto.getTax());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerLogisticsStatus.Builder getCopySsBuilder() {
        final ScenePlayerLogisticsStatus.Builder builder = ScenePlayerLogisticsStatus.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ScenePlayerLogisticsStatus.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLogisticsPlaneId() != 0L) {
            builder.setLogisticsPlaneId(this.getLogisticsPlaneId());
            fieldCnt++;
        }  else if (builder.hasLogisticsPlaneId()) {
            // 清理LogisticsPlaneId
            builder.clearLogisticsPlaneId();
            fieldCnt++;
        }
        if (this.getStartTsMs() != 0L) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStartTsMs()) {
            // 清理StartTsMs
            builder.clearStartTsMs();
            fieldCnt++;
        }
        if (this.getEndTsMs() != 0L) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }  else if (builder.hasEndTsMs()) {
            // 清理EndTsMs
            builder.clearEndTsMs();
            fieldCnt++;
        }
        if (this.resources != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.resources.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResources(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResources();
            }
        }  else if (builder.hasResources()) {
            // 清理Resources
            builder.clearResources();
            fieldCnt++;
        }
        if (this.tax != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.tax.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTax(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTax();
            }
        }  else if (builder.hasTax()) {
            // 清理Tax
            builder.clearTax();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ScenePlayerLogisticsStatus.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LOGISTICSPLANEID)) {
            builder.setLogisticsPlaneId(this.getLogisticsPlaneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            final boolean needClear = !builder.hasResources();
            final int tmpFieldCnt = this.resources.copyChangeToSs(builder.getResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResources();
            }
        }
        if (this.hasMark(FIELD_INDEX_TAX) && this.tax != null) {
            final boolean needClear = !builder.hasTax();
            final int tmpFieldCnt = this.tax.copyChangeToSs(builder.getTaxBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTax();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ScenePlayerLogisticsStatus proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLogisticsPlaneId()) {
            this.innerSetLogisticsPlaneId(proto.getLogisticsPlaneId());
        } else {
            this.innerSetLogisticsPlaneId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStartTsMs()) {
            this.innerSetStartTsMs(proto.getStartTsMs());
        } else {
            this.innerSetStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTsMs()) {
            this.innerSetEndTsMs(proto.getEndTsMs());
        } else {
            this.innerSetEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasResources()) {
            this.getResources().mergeFromSs(proto.getResources());
        } else {
            if (this.resources != null) {
                this.resources.mergeFromSs(proto.getResources());
            }
        }
        if (proto.hasTax()) {
            this.getTax().mergeFromSs(proto.getTax());
        } else {
            if (this.tax != null) {
                this.tax.mergeFromSs(proto.getTax());
            }
        }
        this.markAll();
        return ScenePlayerLogisticsStatusProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ScenePlayerLogisticsStatus proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLogisticsPlaneId()) {
            this.setLogisticsPlaneId(proto.getLogisticsPlaneId());
            fieldCnt++;
        }
        if (proto.hasStartTsMs()) {
            this.setStartTsMs(proto.getStartTsMs());
            fieldCnt++;
        }
        if (proto.hasEndTsMs()) {
            this.setEndTsMs(proto.getEndTsMs());
            fieldCnt++;
        }
        if (proto.hasResources()) {
            this.getResources().mergeChangeFromSs(proto.getResources());
            fieldCnt++;
        }
        if (proto.hasTax()) {
            this.getTax().mergeChangeFromSs(proto.getTax());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ScenePlayerLogisticsStatus.Builder builder = ScenePlayerLogisticsStatus.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            this.resources.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TAX) && this.tax != null) {
            this.tax.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.resources != null) {
            this.resources.markAll();
        }
        if (this.tax != null) {
            this.tax.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.LogisticsPlaneId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ScenePlayerLogisticsStatusProp)) {
            return false;
        }
        final ScenePlayerLogisticsStatusProp otherNode = (ScenePlayerLogisticsStatusProp) node;
        if (this.LogisticsPlaneId != otherNode.LogisticsPlaneId) {
            return false;
        }
        if (this.startTsMs != otherNode.startTsMs) {
            return false;
        }
        if (this.endTsMs != otherNode.endTsMs) {
            return false;
        }
        if (!this.getResources().compareDataTo(otherNode.getResources())) {
            return false;
        }
        if (!this.getTax().compareDataTo(otherNode.getTax())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
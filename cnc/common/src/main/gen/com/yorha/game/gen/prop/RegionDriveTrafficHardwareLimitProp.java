package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zone.RegionDriveTrafficHardwareLimit;
import com.yorha.proto.Zone;
import com.yorha.proto.ZonePB.RegionDriveTrafficHardwareLimitPB;
import com.yorha.proto.ZonePB;


/**
 * <AUTHOR> auto gen
 */
public class RegionDriveTrafficHardwareLimitProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_REGIONID = 0;
    public static final int FIELD_INDEX_DRIVETRAFFICHARDWARELIMIT = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int regionId = Constant.DEFAULT_INT_VALUE;
    private Int32DriveTrafficHardwareLimitItemMapProp driveTrafficHardwareLimit = null;

    public RegionDriveTrafficHardwareLimitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public RegionDriveTrafficHardwareLimitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get regionId
     *
     * @return regionId value
     */
    public int getRegionId() {
        return this.regionId;
    }

    /**
     * set regionId && set marked
     *
     * @param regionId new value
     * @return current object
     */
    public RegionDriveTrafficHardwareLimitProp setRegionId(int regionId) {
        if (this.regionId != regionId) {
            this.mark(FIELD_INDEX_REGIONID);
            this.regionId = regionId;
        }
        return this;
    }

    /**
     * inner set regionId
     *
     * @param regionId new value
     */
    private void innerSetRegionId(int regionId) {
        this.regionId = regionId;
    }

    /**
     * get driveTrafficHardwareLimit
     *
     * @return driveTrafficHardwareLimit value
     */
    public Int32DriveTrafficHardwareLimitItemMapProp getDriveTrafficHardwareLimit() {
        if (this.driveTrafficHardwareLimit == null) {
            this.driveTrafficHardwareLimit = new Int32DriveTrafficHardwareLimitItemMapProp(this, FIELD_INDEX_DRIVETRAFFICHARDWARELIMIT);
        }
        return this.driveTrafficHardwareLimit;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putDriveTrafficHardwareLimitV(DriveTrafficHardwareLimitItemProp v) {
        this.getDriveTrafficHardwareLimit().put(v.getDriveTrafficHardwareLevel(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public DriveTrafficHardwareLimitItemProp addEmptyDriveTrafficHardwareLimit(Integer k) {
        return this.getDriveTrafficHardwareLimit().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getDriveTrafficHardwareLimitSize() {
        if (this.driveTrafficHardwareLimit == null) {
            return 0;
        }
        return this.driveTrafficHardwareLimit.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isDriveTrafficHardwareLimitEmpty() {
        if (this.driveTrafficHardwareLimit == null) {
            return true;
        }
        return this.driveTrafficHardwareLimit.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public DriveTrafficHardwareLimitItemProp getDriveTrafficHardwareLimitV(Integer k) {
        if (this.driveTrafficHardwareLimit == null || !this.driveTrafficHardwareLimit.containsKey(k)) {
            return null;
        }
        return this.driveTrafficHardwareLimit.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearDriveTrafficHardwareLimit() {
        if (this.driveTrafficHardwareLimit != null) {
            this.driveTrafficHardwareLimit.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeDriveTrafficHardwareLimitV(Integer k) {
        if (this.driveTrafficHardwareLimit != null) {
            this.driveTrafficHardwareLimit.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RegionDriveTrafficHardwareLimitPB.Builder getCopyCsBuilder() {
        final RegionDriveTrafficHardwareLimitPB.Builder builder = RegionDriveTrafficHardwareLimitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(RegionDriveTrafficHardwareLimitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRegionId() != 0) {
            builder.setRegionId(this.getRegionId());
            fieldCnt++;
        }  else if (builder.hasRegionId()) {
            // 清理RegionId
            builder.clearRegionId();
            fieldCnt++;
        }
        if (this.driveTrafficHardwareLimit != null) {
            ZonePB.Int32DriveTrafficHardwareLimitItemMapPB.Builder tmpBuilder = ZonePB.Int32DriveTrafficHardwareLimitItemMapPB.newBuilder();
            final int tmpFieldCnt = this.driveTrafficHardwareLimit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDriveTrafficHardwareLimit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDriveTrafficHardwareLimit();
            }
        }  else if (builder.hasDriveTrafficHardwareLimit()) {
            // 清理DriveTrafficHardwareLimit
            builder.clearDriveTrafficHardwareLimit();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(RegionDriveTrafficHardwareLimitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REGIONID)) {
            builder.setRegionId(this.getRegionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DRIVETRAFFICHARDWARELIMIT) && this.driveTrafficHardwareLimit != null) {
            final boolean needClear = !builder.hasDriveTrafficHardwareLimit();
            final int tmpFieldCnt = this.driveTrafficHardwareLimit.copyChangeToCs(builder.getDriveTrafficHardwareLimitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDriveTrafficHardwareLimit();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(RegionDriveTrafficHardwareLimitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REGIONID)) {
            builder.setRegionId(this.getRegionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DRIVETRAFFICHARDWARELIMIT) && this.driveTrafficHardwareLimit != null) {
            final boolean needClear = !builder.hasDriveTrafficHardwareLimit();
            final int tmpFieldCnt = this.driveTrafficHardwareLimit.copyChangeToAndClearDeleteKeysCs(builder.getDriveTrafficHardwareLimitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDriveTrafficHardwareLimit();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(RegionDriveTrafficHardwareLimitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRegionId()) {
            this.innerSetRegionId(proto.getRegionId());
        } else {
            this.innerSetRegionId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDriveTrafficHardwareLimit()) {
            this.getDriveTrafficHardwareLimit().mergeFromCs(proto.getDriveTrafficHardwareLimit());
        } else {
            if (this.driveTrafficHardwareLimit != null) {
                this.driveTrafficHardwareLimit.mergeFromCs(proto.getDriveTrafficHardwareLimit());
            }
        }
        this.markAll();
        return RegionDriveTrafficHardwareLimitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(RegionDriveTrafficHardwareLimitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRegionId()) {
            this.setRegionId(proto.getRegionId());
            fieldCnt++;
        }
        if (proto.hasDriveTrafficHardwareLimit()) {
            this.getDriveTrafficHardwareLimit().mergeChangeFromCs(proto.getDriveTrafficHardwareLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RegionDriveTrafficHardwareLimit.Builder getCopyDbBuilder() {
        final RegionDriveTrafficHardwareLimit.Builder builder = RegionDriveTrafficHardwareLimit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(RegionDriveTrafficHardwareLimit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRegionId() != 0) {
            builder.setRegionId(this.getRegionId());
            fieldCnt++;
        }  else if (builder.hasRegionId()) {
            // 清理RegionId
            builder.clearRegionId();
            fieldCnt++;
        }
        if (this.driveTrafficHardwareLimit != null) {
            Zone.Int32DriveTrafficHardwareLimitItemMap.Builder tmpBuilder = Zone.Int32DriveTrafficHardwareLimitItemMap.newBuilder();
            final int tmpFieldCnt = this.driveTrafficHardwareLimit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDriveTrafficHardwareLimit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDriveTrafficHardwareLimit();
            }
        }  else if (builder.hasDriveTrafficHardwareLimit()) {
            // 清理DriveTrafficHardwareLimit
            builder.clearDriveTrafficHardwareLimit();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(RegionDriveTrafficHardwareLimit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REGIONID)) {
            builder.setRegionId(this.getRegionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DRIVETRAFFICHARDWARELIMIT) && this.driveTrafficHardwareLimit != null) {
            final boolean needClear = !builder.hasDriveTrafficHardwareLimit();
            final int tmpFieldCnt = this.driveTrafficHardwareLimit.copyChangeToDb(builder.getDriveTrafficHardwareLimitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDriveTrafficHardwareLimit();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(RegionDriveTrafficHardwareLimit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRegionId()) {
            this.innerSetRegionId(proto.getRegionId());
        } else {
            this.innerSetRegionId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDriveTrafficHardwareLimit()) {
            this.getDriveTrafficHardwareLimit().mergeFromDb(proto.getDriveTrafficHardwareLimit());
        } else {
            if (this.driveTrafficHardwareLimit != null) {
                this.driveTrafficHardwareLimit.mergeFromDb(proto.getDriveTrafficHardwareLimit());
            }
        }
        this.markAll();
        return RegionDriveTrafficHardwareLimitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(RegionDriveTrafficHardwareLimit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRegionId()) {
            this.setRegionId(proto.getRegionId());
            fieldCnt++;
        }
        if (proto.hasDriveTrafficHardwareLimit()) {
            this.getDriveTrafficHardwareLimit().mergeChangeFromDb(proto.getDriveTrafficHardwareLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RegionDriveTrafficHardwareLimit.Builder getCopySsBuilder() {
        final RegionDriveTrafficHardwareLimit.Builder builder = RegionDriveTrafficHardwareLimit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(RegionDriveTrafficHardwareLimit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRegionId() != 0) {
            builder.setRegionId(this.getRegionId());
            fieldCnt++;
        }  else if (builder.hasRegionId()) {
            // 清理RegionId
            builder.clearRegionId();
            fieldCnt++;
        }
        if (this.driveTrafficHardwareLimit != null) {
            Zone.Int32DriveTrafficHardwareLimitItemMap.Builder tmpBuilder = Zone.Int32DriveTrafficHardwareLimitItemMap.newBuilder();
            final int tmpFieldCnt = this.driveTrafficHardwareLimit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDriveTrafficHardwareLimit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDriveTrafficHardwareLimit();
            }
        }  else if (builder.hasDriveTrafficHardwareLimit()) {
            // 清理DriveTrafficHardwareLimit
            builder.clearDriveTrafficHardwareLimit();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(RegionDriveTrafficHardwareLimit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REGIONID)) {
            builder.setRegionId(this.getRegionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DRIVETRAFFICHARDWARELIMIT) && this.driveTrafficHardwareLimit != null) {
            final boolean needClear = !builder.hasDriveTrafficHardwareLimit();
            final int tmpFieldCnt = this.driveTrafficHardwareLimit.copyChangeToSs(builder.getDriveTrafficHardwareLimitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDriveTrafficHardwareLimit();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(RegionDriveTrafficHardwareLimit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRegionId()) {
            this.innerSetRegionId(proto.getRegionId());
        } else {
            this.innerSetRegionId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDriveTrafficHardwareLimit()) {
            this.getDriveTrafficHardwareLimit().mergeFromSs(proto.getDriveTrafficHardwareLimit());
        } else {
            if (this.driveTrafficHardwareLimit != null) {
                this.driveTrafficHardwareLimit.mergeFromSs(proto.getDriveTrafficHardwareLimit());
            }
        }
        this.markAll();
        return RegionDriveTrafficHardwareLimitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(RegionDriveTrafficHardwareLimit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRegionId()) {
            this.setRegionId(proto.getRegionId());
            fieldCnt++;
        }
        if (proto.hasDriveTrafficHardwareLimit()) {
            this.getDriveTrafficHardwareLimit().mergeChangeFromSs(proto.getDriveTrafficHardwareLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        RegionDriveTrafficHardwareLimit.Builder builder = RegionDriveTrafficHardwareLimit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_DRIVETRAFFICHARDWARELIMIT) && this.driveTrafficHardwareLimit != null) {
            this.driveTrafficHardwareLimit.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.driveTrafficHardwareLimit != null) {
            this.driveTrafficHardwareLimit.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.regionId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof RegionDriveTrafficHardwareLimitProp)) {
            return false;
        }
        final RegionDriveTrafficHardwareLimitProp otherNode = (RegionDriveTrafficHardwareLimitProp) node;
        if (this.regionId != otherNode.regionId) {
            return false;
        }
        if (!this.getDriveTrafficHardwareLimit().compareDataTo(otherNode.getDriveTrafficHardwareLimit())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
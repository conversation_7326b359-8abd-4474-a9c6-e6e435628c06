package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Zoneside.ZoneActivity;
import com.yorha.proto.Basic;
import com.yorha.proto.Zoneside;
import com.yorha.proto.ZonesidePB.ZoneActivityPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.ZonesidePB;


/**
 * <AUTHOR> auto gen
 */
public class ZoneActivityProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ACTIVITYID = 0;
    public static final int FIELD_INDEX_ACTSCHEDULEID = 1;
    public static final int FIELD_INDEX_STARTTSSEC = 2;
    public static final int FIELD_INDEX_ENDTSSEC = 3;
    public static final int FIELD_INDEX_CHILDACTIVITIES = 4;
    public static final int FIELD_INDEX_STATUS = 5;
    public static final int FIELD_INDEX_STATUSENTERTSSEC = 6;
    public static final int FIELD_INDEX_CLOSEDCHILDACTIVITIES = 7;
    public static final int FIELD_INDEX_BESTCOMMANDERUNIT = 8;
    public static final int FIELD_INDEX_BCTOTALRANKUNIT = 9;

    public static final int FIELD_COUNT = 10;

    private long markBits0 = 0L;

    private int activityId = Constant.DEFAULT_INT_VALUE;
    private int actScheduleId = Constant.DEFAULT_INT_VALUE;
    private int startTsSec = Constant.DEFAULT_INT_VALUE;
    private int endTsSec = Constant.DEFAULT_INT_VALUE;
    private Int32ZoneActivityMapProp childActivities = null;
    private ActivityStatus status = ActivityStatus.forNumber(0);
    private int statusEnterTsSec = Constant.DEFAULT_INT_VALUE;
    private Int32SetProp closedChildActivities = null;
    private ZoneSideBestCommanderUnitProp bestCommanderUnit = null;
    private ZoneSideBestCommanderTotalRankUnitProp bcTotalRankUnit = null;

    public ZoneActivityProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ZoneActivityProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get activityId
     *
     * @return activityId value
     */
    public int getActivityId() {
        return this.activityId;
    }

    /**
     * set activityId && set marked
     *
     * @param activityId new value
     * @return current object
     */
    public ZoneActivityProp setActivityId(int activityId) {
        if (this.activityId != activityId) {
            this.mark(FIELD_INDEX_ACTIVITYID);
            this.activityId = activityId;
        }
        return this;
    }

    /**
     * inner set activityId
     *
     * @param activityId new value
     */
    private void innerSetActivityId(int activityId) {
        this.activityId = activityId;
    }

    /**
     * get actScheduleId
     *
     * @return actScheduleId value
     */
    public int getActScheduleId() {
        return this.actScheduleId;
    }

    /**
     * set actScheduleId && set marked
     *
     * @param actScheduleId new value
     * @return current object
     */
    public ZoneActivityProp setActScheduleId(int actScheduleId) {
        if (this.actScheduleId != actScheduleId) {
            this.mark(FIELD_INDEX_ACTSCHEDULEID);
            this.actScheduleId = actScheduleId;
        }
        return this;
    }

    /**
     * inner set actScheduleId
     *
     * @param actScheduleId new value
     */
    private void innerSetActScheduleId(int actScheduleId) {
        this.actScheduleId = actScheduleId;
    }

    /**
     * get startTsSec
     *
     * @return startTsSec value
     */
    public int getStartTsSec() {
        return this.startTsSec;
    }

    /**
     * set startTsSec && set marked
     *
     * @param startTsSec new value
     * @return current object
     */
    public ZoneActivityProp setStartTsSec(int startTsSec) {
        if (this.startTsSec != startTsSec) {
            this.mark(FIELD_INDEX_STARTTSSEC);
            this.startTsSec = startTsSec;
        }
        return this;
    }

    /**
     * inner set startTsSec
     *
     * @param startTsSec new value
     */
    private void innerSetStartTsSec(int startTsSec) {
        this.startTsSec = startTsSec;
    }

    /**
     * get endTsSec
     *
     * @return endTsSec value
     */
    public int getEndTsSec() {
        return this.endTsSec;
    }

    /**
     * set endTsSec && set marked
     *
     * @param endTsSec new value
     * @return current object
     */
    public ZoneActivityProp setEndTsSec(int endTsSec) {
        if (this.endTsSec != endTsSec) {
            this.mark(FIELD_INDEX_ENDTSSEC);
            this.endTsSec = endTsSec;
        }
        return this;
    }

    /**
     * inner set endTsSec
     *
     * @param endTsSec new value
     */
    private void innerSetEndTsSec(int endTsSec) {
        this.endTsSec = endTsSec;
    }

    /**
     * get childActivities
     *
     * @return childActivities value
     */
    public Int32ZoneActivityMapProp getChildActivities() {
        if (this.childActivities == null) {
            this.childActivities = new Int32ZoneActivityMapProp(this, FIELD_INDEX_CHILDACTIVITIES);
        }
        return this.childActivities;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putChildActivitiesV(ZoneActivityProp v) {
        this.getChildActivities().put(v.getActivityId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ZoneActivityProp addEmptyChildActivities(Integer k) {
        return this.getChildActivities().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getChildActivitiesSize() {
        if (this.childActivities == null) {
            return 0;
        }
        return this.childActivities.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isChildActivitiesEmpty() {
        if (this.childActivities == null) {
            return true;
        }
        return this.childActivities.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ZoneActivityProp getChildActivitiesV(Integer k) {
        if (this.childActivities == null || !this.childActivities.containsKey(k)) {
            return null;
        }
        return this.childActivities.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearChildActivities() {
        if (this.childActivities != null) {
            this.childActivities.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeChildActivitiesV(Integer k) {
        if (this.childActivities != null) {
            this.childActivities.remove(k);
        }
    }
    /**
     * get status
     *
     * @return status value
     */
    public ActivityStatus getStatus() {
        return this.status;
    }

    /**
     * set status && set marked
     *
     * @param status new value
     * @return current object
     */
    public ZoneActivityProp setStatus(ActivityStatus status) {
        if (status == null) {
            throw new NullPointerException();
        }
        if (this.status != status) {
            this.mark(FIELD_INDEX_STATUS);
            this.status = status;
        }
        return this;
    }

    /**
     * inner set status
     *
     * @param status new value
     */
    private void innerSetStatus(ActivityStatus status) {
        this.status = status;
    }

    /**
     * get statusEnterTsSec
     *
     * @return statusEnterTsSec value
     */
    public int getStatusEnterTsSec() {
        return this.statusEnterTsSec;
    }

    /**
     * set statusEnterTsSec && set marked
     *
     * @param statusEnterTsSec new value
     * @return current object
     */
    public ZoneActivityProp setStatusEnterTsSec(int statusEnterTsSec) {
        if (this.statusEnterTsSec != statusEnterTsSec) {
            this.mark(FIELD_INDEX_STATUSENTERTSSEC);
            this.statusEnterTsSec = statusEnterTsSec;
        }
        return this;
    }

    /**
     * inner set statusEnterTsSec
     *
     * @param statusEnterTsSec new value
     */
    private void innerSetStatusEnterTsSec(int statusEnterTsSec) {
        this.statusEnterTsSec = statusEnterTsSec;
    }

    /**
     * get closedChildActivities
     *
     * @return closedChildActivities value
     */
    public Int32SetProp getClosedChildActivities() {
        if (this.closedChildActivities == null) {
            this.closedChildActivities = new Int32SetProp(this, FIELD_INDEX_CLOSEDCHILDACTIVITIES);
        }
        return this.closedChildActivities;
    }


    /**
     * add value to set
     *
     * @param e value
     */
    public void addClosedChildActivities(Integer e) {
        this.getClosedChildActivities().add(e);
    }

    /**
     * remove the specified element in set
     *
     * @param e element
     * @return e if success or null by fail
     */
    public Integer removeClosedChildActivities(Integer e) {
        if (this.closedChildActivities == null) {
            return null;
        }
        if(this.closedChildActivities.remove(e)) {
            return e;
        }
        return null;
    }

    /**
     * get set size
     *
     * @return set size
     */
    public int getClosedChildActivitiesSize() {
        if (this.closedChildActivities == null) {
            return 0;
        }
        return this.closedChildActivities.size();
    }

    /**
     * get is a empty set
     *
     * @return true if empty
     */
    public boolean isClosedChildActivitiesEmpty() {
        if (this.closedChildActivities == null) {
            return true;
        }
        return this.getClosedChildActivities().isEmpty();
    }

    /**
     * clear set
     */
    public void clearClosedChildActivities() {
        this.getClosedChildActivities().clear();
    }

    /**
     * Returns true if this set contains the specified element.
     *
     * @param e elem
     * @return true if this set contains the specified element, false otherwise
     */
    public boolean isClosedChildActivitiesContains(Integer e) {
        return this.closedChildActivities != null && this.closedChildActivities.contains(e);
    }

    /**
     * get bestCommanderUnit
     *
     * @return bestCommanderUnit value
     */
    public ZoneSideBestCommanderUnitProp getBestCommanderUnit() {
        if (this.bestCommanderUnit == null) {
            this.bestCommanderUnit = new ZoneSideBestCommanderUnitProp(this, FIELD_INDEX_BESTCOMMANDERUNIT);
        }
        return this.bestCommanderUnit;
    }

    /**
     * get bcTotalRankUnit
     *
     * @return bcTotalRankUnit value
     */
    public ZoneSideBestCommanderTotalRankUnitProp getBcTotalRankUnit() {
        if (this.bcTotalRankUnit == null) {
            this.bcTotalRankUnit = new ZoneSideBestCommanderTotalRankUnitProp(this, FIELD_INDEX_BCTOTALRANKUNIT);
        }
        return this.bcTotalRankUnit;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneActivityPB.Builder getCopyCsBuilder() {
        final ZoneActivityPB.Builder builder = ZoneActivityPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ZoneActivityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActivityId() != 0) {
            builder.setActivityId(this.getActivityId());
            fieldCnt++;
        }  else if (builder.hasActivityId()) {
            // 清理ActivityId
            builder.clearActivityId();
            fieldCnt++;
        }
        if (this.getActScheduleId() != 0) {
            builder.setActScheduleId(this.getActScheduleId());
            fieldCnt++;
        }  else if (builder.hasActScheduleId()) {
            // 清理ActScheduleId
            builder.clearActScheduleId();
            fieldCnt++;
        }
        if (this.getStartTsSec() != 0) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }  else if (builder.hasStartTsSec()) {
            // 清理StartTsSec
            builder.clearStartTsSec();
            fieldCnt++;
        }
        if (this.getEndTsSec() != 0) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }  else if (builder.hasEndTsSec()) {
            // 清理EndTsSec
            builder.clearEndTsSec();
            fieldCnt++;
        }
        if (this.childActivities != null) {
            ZonesidePB.Int32ZoneActivityMapPB.Builder tmpBuilder = ZonesidePB.Int32ZoneActivityMapPB.newBuilder();
            final int tmpFieldCnt = this.childActivities.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChildActivities(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChildActivities();
            }
        }  else if (builder.hasChildActivities()) {
            // 清理ChildActivities
            builder.clearChildActivities();
            fieldCnt++;
        }
        if (this.getStatus() != ActivityStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.getStatusEnterTsSec() != 0) {
            builder.setStatusEnterTsSec(this.getStatusEnterTsSec());
            fieldCnt++;
        }  else if (builder.hasStatusEnterTsSec()) {
            // 清理StatusEnterTsSec
            builder.clearStatusEnterTsSec();
            fieldCnt++;
        }
        if (this.bestCommanderUnit != null) {
            ZonesidePB.ZoneSideBestCommanderUnitPB.Builder tmpBuilder = ZonesidePB.ZoneSideBestCommanderUnitPB.newBuilder();
            final int tmpFieldCnt = this.bestCommanderUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBestCommanderUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBestCommanderUnit();
            }
        }  else if (builder.hasBestCommanderUnit()) {
            // 清理BestCommanderUnit
            builder.clearBestCommanderUnit();
            fieldCnt++;
        }
        if (this.bcTotalRankUnit != null) {
            ZonesidePB.ZoneSideBestCommanderTotalRankUnitPB.Builder tmpBuilder = ZonesidePB.ZoneSideBestCommanderTotalRankUnitPB.newBuilder();
            final int tmpFieldCnt = this.bcTotalRankUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBcTotalRankUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBcTotalRankUnit();
            }
        }  else if (builder.hasBcTotalRankUnit()) {
            // 清理BcTotalRankUnit
            builder.clearBcTotalRankUnit();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ZoneActivityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITYID)) {
            builder.setActivityId(this.getActivityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTSCHEDULEID)) {
            builder.setActScheduleId(this.getActScheduleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSSEC)) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSSEC)) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CHILDACTIVITIES) && this.childActivities != null) {
            final boolean needClear = !builder.hasChildActivities();
            final int tmpFieldCnt = this.childActivities.copyChangeToCs(builder.getChildActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChildActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUSENTERTSSEC)) {
            builder.setStatusEnterTsSec(this.getStatusEnterTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERUNIT) && this.bestCommanderUnit != null) {
            final boolean needClear = !builder.hasBestCommanderUnit();
            final int tmpFieldCnt = this.bestCommanderUnit.copyChangeToCs(builder.getBestCommanderUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BCTOTALRANKUNIT) && this.bcTotalRankUnit != null) {
            final boolean needClear = !builder.hasBcTotalRankUnit();
            final int tmpFieldCnt = this.bcTotalRankUnit.copyChangeToCs(builder.getBcTotalRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBcTotalRankUnit();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ZoneActivityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITYID)) {
            builder.setActivityId(this.getActivityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTSCHEDULEID)) {
            builder.setActScheduleId(this.getActScheduleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSSEC)) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSSEC)) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CHILDACTIVITIES) && this.childActivities != null) {
            final boolean needClear = !builder.hasChildActivities();
            final int tmpFieldCnt = this.childActivities.copyChangeToAndClearDeleteKeysCs(builder.getChildActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChildActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUSENTERTSSEC)) {
            builder.setStatusEnterTsSec(this.getStatusEnterTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERUNIT) && this.bestCommanderUnit != null) {
            final boolean needClear = !builder.hasBestCommanderUnit();
            final int tmpFieldCnt = this.bestCommanderUnit.copyChangeToAndClearDeleteKeysCs(builder.getBestCommanderUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BCTOTALRANKUNIT) && this.bcTotalRankUnit != null) {
            final boolean needClear = !builder.hasBcTotalRankUnit();
            final int tmpFieldCnt = this.bcTotalRankUnit.copyChangeToAndClearDeleteKeysCs(builder.getBcTotalRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBcTotalRankUnit();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ZoneActivityPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActivityId()) {
            this.innerSetActivityId(proto.getActivityId());
        } else {
            this.innerSetActivityId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasActScheduleId()) {
            this.innerSetActScheduleId(proto.getActScheduleId());
        } else {
            this.innerSetActScheduleId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartTsSec()) {
            this.innerSetStartTsSec(proto.getStartTsSec());
        } else {
            this.innerSetStartTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEndTsSec()) {
            this.innerSetEndTsSec(proto.getEndTsSec());
        } else {
            this.innerSetEndTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasChildActivities()) {
            this.getChildActivities().mergeFromCs(proto.getChildActivities());
        } else {
            if (this.childActivities != null) {
                this.childActivities.mergeFromCs(proto.getChildActivities());
            }
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(ActivityStatus.forNumber(0));
        }
        if (proto.hasStatusEnterTsSec()) {
            this.innerSetStatusEnterTsSec(proto.getStatusEnterTsSec());
        } else {
            this.innerSetStatusEnterTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBestCommanderUnit()) {
            this.getBestCommanderUnit().mergeFromCs(proto.getBestCommanderUnit());
        } else {
            if (this.bestCommanderUnit != null) {
                this.bestCommanderUnit.mergeFromCs(proto.getBestCommanderUnit());
            }
        }
        if (proto.hasBcTotalRankUnit()) {
            this.getBcTotalRankUnit().mergeFromCs(proto.getBcTotalRankUnit());
        } else {
            if (this.bcTotalRankUnit != null) {
                this.bcTotalRankUnit.mergeFromCs(proto.getBcTotalRankUnit());
            }
        }
        this.markAll();
        return ZoneActivityProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ZoneActivityPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActivityId()) {
            this.setActivityId(proto.getActivityId());
            fieldCnt++;
        }
        if (proto.hasActScheduleId()) {
            this.setActScheduleId(proto.getActScheduleId());
            fieldCnt++;
        }
        if (proto.hasStartTsSec()) {
            this.setStartTsSec(proto.getStartTsSec());
            fieldCnt++;
        }
        if (proto.hasEndTsSec()) {
            this.setEndTsSec(proto.getEndTsSec());
            fieldCnt++;
        }
        if (proto.hasChildActivities()) {
            this.getChildActivities().mergeChangeFromCs(proto.getChildActivities());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasStatusEnterTsSec()) {
            this.setStatusEnterTsSec(proto.getStatusEnterTsSec());
            fieldCnt++;
        }
        if (proto.hasBestCommanderUnit()) {
            this.getBestCommanderUnit().mergeChangeFromCs(proto.getBestCommanderUnit());
            fieldCnt++;
        }
        if (proto.hasBcTotalRankUnit()) {
            this.getBcTotalRankUnit().mergeChangeFromCs(proto.getBcTotalRankUnit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneActivity.Builder getCopyDbBuilder() {
        final ZoneActivity.Builder builder = ZoneActivity.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ZoneActivity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActivityId() != 0) {
            builder.setActivityId(this.getActivityId());
            fieldCnt++;
        }  else if (builder.hasActivityId()) {
            // 清理ActivityId
            builder.clearActivityId();
            fieldCnt++;
        }
        if (this.getActScheduleId() != 0) {
            builder.setActScheduleId(this.getActScheduleId());
            fieldCnt++;
        }  else if (builder.hasActScheduleId()) {
            // 清理ActScheduleId
            builder.clearActScheduleId();
            fieldCnt++;
        }
        if (this.getStartTsSec() != 0) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }  else if (builder.hasStartTsSec()) {
            // 清理StartTsSec
            builder.clearStartTsSec();
            fieldCnt++;
        }
        if (this.getEndTsSec() != 0) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }  else if (builder.hasEndTsSec()) {
            // 清理EndTsSec
            builder.clearEndTsSec();
            fieldCnt++;
        }
        if (this.childActivities != null) {
            Zoneside.Int32ZoneActivityMap.Builder tmpBuilder = Zoneside.Int32ZoneActivityMap.newBuilder();
            final int tmpFieldCnt = this.childActivities.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChildActivities(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChildActivities();
            }
        }  else if (builder.hasChildActivities()) {
            // 清理ChildActivities
            builder.clearChildActivities();
            fieldCnt++;
        }
        if (this.getStatus() != ActivityStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.getStatusEnterTsSec() != 0) {
            builder.setStatusEnterTsSec(this.getStatusEnterTsSec());
            fieldCnt++;
        }  else if (builder.hasStatusEnterTsSec()) {
            // 清理StatusEnterTsSec
            builder.clearStatusEnterTsSec();
            fieldCnt++;
        }
        if (this.closedChildActivities != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.closedChildActivities.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClosedChildActivities(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClosedChildActivities();
            }
        }  else if (builder.hasClosedChildActivities()) {
            // 清理ClosedChildActivities
            builder.clearClosedChildActivities();
            fieldCnt++;
        }
        if (this.bestCommanderUnit != null) {
            Zoneside.ZoneSideBestCommanderUnit.Builder tmpBuilder = Zoneside.ZoneSideBestCommanderUnit.newBuilder();
            final int tmpFieldCnt = this.bestCommanderUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBestCommanderUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBestCommanderUnit();
            }
        }  else if (builder.hasBestCommanderUnit()) {
            // 清理BestCommanderUnit
            builder.clearBestCommanderUnit();
            fieldCnt++;
        }
        if (this.bcTotalRankUnit != null) {
            Zoneside.ZoneSideBestCommanderTotalRankUnit.Builder tmpBuilder = Zoneside.ZoneSideBestCommanderTotalRankUnit.newBuilder();
            final int tmpFieldCnt = this.bcTotalRankUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBcTotalRankUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBcTotalRankUnit();
            }
        }  else if (builder.hasBcTotalRankUnit()) {
            // 清理BcTotalRankUnit
            builder.clearBcTotalRankUnit();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ZoneActivity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITYID)) {
            builder.setActivityId(this.getActivityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTSCHEDULEID)) {
            builder.setActScheduleId(this.getActScheduleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSSEC)) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSSEC)) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CHILDACTIVITIES) && this.childActivities != null) {
            final boolean needClear = !builder.hasChildActivities();
            final int tmpFieldCnt = this.childActivities.copyChangeToDb(builder.getChildActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChildActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUSENTERTSSEC)) {
            builder.setStatusEnterTsSec(this.getStatusEnterTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLOSEDCHILDACTIVITIES) && this.closedChildActivities != null) {
            final boolean needClear = !builder.hasClosedChildActivities();
            final int tmpFieldCnt = this.closedChildActivities.copyChangeToDb(builder.getClosedChildActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClosedChildActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERUNIT) && this.bestCommanderUnit != null) {
            final boolean needClear = !builder.hasBestCommanderUnit();
            final int tmpFieldCnt = this.bestCommanderUnit.copyChangeToDb(builder.getBestCommanderUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BCTOTALRANKUNIT) && this.bcTotalRankUnit != null) {
            final boolean needClear = !builder.hasBcTotalRankUnit();
            final int tmpFieldCnt = this.bcTotalRankUnit.copyChangeToDb(builder.getBcTotalRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBcTotalRankUnit();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ZoneActivity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActivityId()) {
            this.innerSetActivityId(proto.getActivityId());
        } else {
            this.innerSetActivityId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasActScheduleId()) {
            this.innerSetActScheduleId(proto.getActScheduleId());
        } else {
            this.innerSetActScheduleId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartTsSec()) {
            this.innerSetStartTsSec(proto.getStartTsSec());
        } else {
            this.innerSetStartTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEndTsSec()) {
            this.innerSetEndTsSec(proto.getEndTsSec());
        } else {
            this.innerSetEndTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasChildActivities()) {
            this.getChildActivities().mergeFromDb(proto.getChildActivities());
        } else {
            if (this.childActivities != null) {
                this.childActivities.mergeFromDb(proto.getChildActivities());
            }
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(ActivityStatus.forNumber(0));
        }
        if (proto.hasStatusEnterTsSec()) {
            this.innerSetStatusEnterTsSec(proto.getStatusEnterTsSec());
        } else {
            this.innerSetStatusEnterTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasClosedChildActivities()) {
            this.getClosedChildActivities().mergeFromDb(proto.getClosedChildActivities());
        } else {
            if (this.closedChildActivities != null) {
                this.closedChildActivities.mergeFromDb(proto.getClosedChildActivities());
            }
        }
        if (proto.hasBestCommanderUnit()) {
            this.getBestCommanderUnit().mergeFromDb(proto.getBestCommanderUnit());
        } else {
            if (this.bestCommanderUnit != null) {
                this.bestCommanderUnit.mergeFromDb(proto.getBestCommanderUnit());
            }
        }
        if (proto.hasBcTotalRankUnit()) {
            this.getBcTotalRankUnit().mergeFromDb(proto.getBcTotalRankUnit());
        } else {
            if (this.bcTotalRankUnit != null) {
                this.bcTotalRankUnit.mergeFromDb(proto.getBcTotalRankUnit());
            }
        }
        this.markAll();
        return ZoneActivityProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ZoneActivity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActivityId()) {
            this.setActivityId(proto.getActivityId());
            fieldCnt++;
        }
        if (proto.hasActScheduleId()) {
            this.setActScheduleId(proto.getActScheduleId());
            fieldCnt++;
        }
        if (proto.hasStartTsSec()) {
            this.setStartTsSec(proto.getStartTsSec());
            fieldCnt++;
        }
        if (proto.hasEndTsSec()) {
            this.setEndTsSec(proto.getEndTsSec());
            fieldCnt++;
        }
        if (proto.hasChildActivities()) {
            this.getChildActivities().mergeChangeFromDb(proto.getChildActivities());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasStatusEnterTsSec()) {
            this.setStatusEnterTsSec(proto.getStatusEnterTsSec());
            fieldCnt++;
        }
        if (proto.hasClosedChildActivities()) {
            this.getClosedChildActivities().mergeChangeFromDb(proto.getClosedChildActivities());
            fieldCnt++;
        }
        if (proto.hasBestCommanderUnit()) {
            this.getBestCommanderUnit().mergeChangeFromDb(proto.getBestCommanderUnit());
            fieldCnt++;
        }
        if (proto.hasBcTotalRankUnit()) {
            this.getBcTotalRankUnit().mergeChangeFromDb(proto.getBcTotalRankUnit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneActivity.Builder getCopySsBuilder() {
        final ZoneActivity.Builder builder = ZoneActivity.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ZoneActivity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActivityId() != 0) {
            builder.setActivityId(this.getActivityId());
            fieldCnt++;
        }  else if (builder.hasActivityId()) {
            // 清理ActivityId
            builder.clearActivityId();
            fieldCnt++;
        }
        if (this.getActScheduleId() != 0) {
            builder.setActScheduleId(this.getActScheduleId());
            fieldCnt++;
        }  else if (builder.hasActScheduleId()) {
            // 清理ActScheduleId
            builder.clearActScheduleId();
            fieldCnt++;
        }
        if (this.getStartTsSec() != 0) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }  else if (builder.hasStartTsSec()) {
            // 清理StartTsSec
            builder.clearStartTsSec();
            fieldCnt++;
        }
        if (this.getEndTsSec() != 0) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }  else if (builder.hasEndTsSec()) {
            // 清理EndTsSec
            builder.clearEndTsSec();
            fieldCnt++;
        }
        if (this.childActivities != null) {
            Zoneside.Int32ZoneActivityMap.Builder tmpBuilder = Zoneside.Int32ZoneActivityMap.newBuilder();
            final int tmpFieldCnt = this.childActivities.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChildActivities(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChildActivities();
            }
        }  else if (builder.hasChildActivities()) {
            // 清理ChildActivities
            builder.clearChildActivities();
            fieldCnt++;
        }
        if (this.getStatus() != ActivityStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.getStatusEnterTsSec() != 0) {
            builder.setStatusEnterTsSec(this.getStatusEnterTsSec());
            fieldCnt++;
        }  else if (builder.hasStatusEnterTsSec()) {
            // 清理StatusEnterTsSec
            builder.clearStatusEnterTsSec();
            fieldCnt++;
        }
        if (this.closedChildActivities != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.closedChildActivities.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClosedChildActivities(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClosedChildActivities();
            }
        }  else if (builder.hasClosedChildActivities()) {
            // 清理ClosedChildActivities
            builder.clearClosedChildActivities();
            fieldCnt++;
        }
        if (this.bestCommanderUnit != null) {
            Zoneside.ZoneSideBestCommanderUnit.Builder tmpBuilder = Zoneside.ZoneSideBestCommanderUnit.newBuilder();
            final int tmpFieldCnt = this.bestCommanderUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBestCommanderUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBestCommanderUnit();
            }
        }  else if (builder.hasBestCommanderUnit()) {
            // 清理BestCommanderUnit
            builder.clearBestCommanderUnit();
            fieldCnt++;
        }
        if (this.bcTotalRankUnit != null) {
            Zoneside.ZoneSideBestCommanderTotalRankUnit.Builder tmpBuilder = Zoneside.ZoneSideBestCommanderTotalRankUnit.newBuilder();
            final int tmpFieldCnt = this.bcTotalRankUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBcTotalRankUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBcTotalRankUnit();
            }
        }  else if (builder.hasBcTotalRankUnit()) {
            // 清理BcTotalRankUnit
            builder.clearBcTotalRankUnit();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ZoneActivity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITYID)) {
            builder.setActivityId(this.getActivityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTSCHEDULEID)) {
            builder.setActScheduleId(this.getActScheduleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSSEC)) {
            builder.setStartTsSec(this.getStartTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSSEC)) {
            builder.setEndTsSec(this.getEndTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CHILDACTIVITIES) && this.childActivities != null) {
            final boolean needClear = !builder.hasChildActivities();
            final int tmpFieldCnt = this.childActivities.copyChangeToSs(builder.getChildActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChildActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUSENTERTSSEC)) {
            builder.setStatusEnterTsSec(this.getStatusEnterTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLOSEDCHILDACTIVITIES) && this.closedChildActivities != null) {
            final boolean needClear = !builder.hasClosedChildActivities();
            final int tmpFieldCnt = this.closedChildActivities.copyChangeToSs(builder.getClosedChildActivitiesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClosedChildActivities();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERUNIT) && this.bestCommanderUnit != null) {
            final boolean needClear = !builder.hasBestCommanderUnit();
            final int tmpFieldCnt = this.bestCommanderUnit.copyChangeToSs(builder.getBestCommanderUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BCTOTALRANKUNIT) && this.bcTotalRankUnit != null) {
            final boolean needClear = !builder.hasBcTotalRankUnit();
            final int tmpFieldCnt = this.bcTotalRankUnit.copyChangeToSs(builder.getBcTotalRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBcTotalRankUnit();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ZoneActivity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActivityId()) {
            this.innerSetActivityId(proto.getActivityId());
        } else {
            this.innerSetActivityId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasActScheduleId()) {
            this.innerSetActScheduleId(proto.getActScheduleId());
        } else {
            this.innerSetActScheduleId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartTsSec()) {
            this.innerSetStartTsSec(proto.getStartTsSec());
        } else {
            this.innerSetStartTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEndTsSec()) {
            this.innerSetEndTsSec(proto.getEndTsSec());
        } else {
            this.innerSetEndTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasChildActivities()) {
            this.getChildActivities().mergeFromSs(proto.getChildActivities());
        } else {
            if (this.childActivities != null) {
                this.childActivities.mergeFromSs(proto.getChildActivities());
            }
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(ActivityStatus.forNumber(0));
        }
        if (proto.hasStatusEnterTsSec()) {
            this.innerSetStatusEnterTsSec(proto.getStatusEnterTsSec());
        } else {
            this.innerSetStatusEnterTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasClosedChildActivities()) {
            this.getClosedChildActivities().mergeFromSs(proto.getClosedChildActivities());
        } else {
            if (this.closedChildActivities != null) {
                this.closedChildActivities.mergeFromSs(proto.getClosedChildActivities());
            }
        }
        if (proto.hasBestCommanderUnit()) {
            this.getBestCommanderUnit().mergeFromSs(proto.getBestCommanderUnit());
        } else {
            if (this.bestCommanderUnit != null) {
                this.bestCommanderUnit.mergeFromSs(proto.getBestCommanderUnit());
            }
        }
        if (proto.hasBcTotalRankUnit()) {
            this.getBcTotalRankUnit().mergeFromSs(proto.getBcTotalRankUnit());
        } else {
            if (this.bcTotalRankUnit != null) {
                this.bcTotalRankUnit.mergeFromSs(proto.getBcTotalRankUnit());
            }
        }
        this.markAll();
        return ZoneActivityProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ZoneActivity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActivityId()) {
            this.setActivityId(proto.getActivityId());
            fieldCnt++;
        }
        if (proto.hasActScheduleId()) {
            this.setActScheduleId(proto.getActScheduleId());
            fieldCnt++;
        }
        if (proto.hasStartTsSec()) {
            this.setStartTsSec(proto.getStartTsSec());
            fieldCnt++;
        }
        if (proto.hasEndTsSec()) {
            this.setEndTsSec(proto.getEndTsSec());
            fieldCnt++;
        }
        if (proto.hasChildActivities()) {
            this.getChildActivities().mergeChangeFromSs(proto.getChildActivities());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasStatusEnterTsSec()) {
            this.setStatusEnterTsSec(proto.getStatusEnterTsSec());
            fieldCnt++;
        }
        if (proto.hasClosedChildActivities()) {
            this.getClosedChildActivities().mergeChangeFromSs(proto.getClosedChildActivities());
            fieldCnt++;
        }
        if (proto.hasBestCommanderUnit()) {
            this.getBestCommanderUnit().mergeChangeFromSs(proto.getBestCommanderUnit());
            fieldCnt++;
        }
        if (proto.hasBcTotalRankUnit()) {
            this.getBcTotalRankUnit().mergeChangeFromSs(proto.getBcTotalRankUnit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ZoneActivity.Builder builder = ZoneActivity.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CHILDACTIVITIES) && this.childActivities != null) {
            this.childActivities.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CLOSEDCHILDACTIVITIES) && this.closedChildActivities != null) {
            this.closedChildActivities.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERUNIT) && this.bestCommanderUnit != null) {
            this.bestCommanderUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BCTOTALRANKUNIT) && this.bcTotalRankUnit != null) {
            this.bcTotalRankUnit.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.childActivities != null) {
            this.childActivities.markAll();
        }
        if (this.closedChildActivities != null) {
            this.closedChildActivities.markAll();
        }
        if (this.bestCommanderUnit != null) {
            this.bestCommanderUnit.markAll();
        }
        if (this.bcTotalRankUnit != null) {
            this.bcTotalRankUnit.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.activityId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ZoneActivityProp)) {
            return false;
        }
        final ZoneActivityProp otherNode = (ZoneActivityProp) node;
        if (this.activityId != otherNode.activityId) {
            return false;
        }
        if (this.actScheduleId != otherNode.actScheduleId) {
            return false;
        }
        if (this.startTsSec != otherNode.startTsSec) {
            return false;
        }
        if (this.endTsSec != otherNode.endTsSec) {
            return false;
        }
        if (!this.getChildActivities().compareDataTo(otherNode.getChildActivities())) {
            return false;
        }
        if (this.status != otherNode.status) {
            return false;
        }
        if (this.statusEnterTsSec != otherNode.statusEnterTsSec) {
            return false;
        }
        if (!this.getClosedChildActivities().compareDataTo(otherNode.getClosedChildActivities())) {
            return false;
        }
        if (!this.getBestCommanderUnit().compareDataTo(otherNode.getBestCommanderUnit())) {
            return false;
        }
        if (!this.getBcTotalRankUnit().compareDataTo(otherNode.getBcTotalRankUnit())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 54;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
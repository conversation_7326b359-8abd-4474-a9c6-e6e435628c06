package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.CityDress;
import com.yorha.proto.StructPB.CityDressPB;


/**
 * <AUTHOR> auto gen
 */
public class CityDressProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_DRESSTEMPLATEID = 0;
    public static final int FIELD_INDEX_TIMEOUTTSMS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int dressTemplateId = Constant.DEFAULT_INT_VALUE;
    private long timeoutTsMs = Constant.DEFAULT_LONG_VALUE;

    public CityDressProp() {
        super(null, 0, FIELD_COUNT);
    }

    public CityDressProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get dressTemplateId
     *
     * @return dressTemplateId value
     */
    public int getDressTemplateId() {
        return this.dressTemplateId;
    }

    /**
     * set dressTemplateId && set marked
     *
     * @param dressTemplateId new value
     * @return current object
     */
    public CityDressProp setDressTemplateId(int dressTemplateId) {
        if (this.dressTemplateId != dressTemplateId) {
            this.mark(FIELD_INDEX_DRESSTEMPLATEID);
            this.dressTemplateId = dressTemplateId;
        }
        return this;
    }

    /**
     * inner set dressTemplateId
     *
     * @param dressTemplateId new value
     */
    private void innerSetDressTemplateId(int dressTemplateId) {
        this.dressTemplateId = dressTemplateId;
    }

    /**
     * get timeoutTsMs
     *
     * @return timeoutTsMs value
     */
    public long getTimeoutTsMs() {
        return this.timeoutTsMs;
    }

    /**
     * set timeoutTsMs && set marked
     *
     * @param timeoutTsMs new value
     * @return current object
     */
    public CityDressProp setTimeoutTsMs(long timeoutTsMs) {
        if (this.timeoutTsMs != timeoutTsMs) {
            this.mark(FIELD_INDEX_TIMEOUTTSMS);
            this.timeoutTsMs = timeoutTsMs;
        }
        return this;
    }

    /**
     * inner set timeoutTsMs
     *
     * @param timeoutTsMs new value
     */
    private void innerSetTimeoutTsMs(long timeoutTsMs) {
        this.timeoutTsMs = timeoutTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CityDressPB.Builder getCopyCsBuilder() {
        final CityDressPB.Builder builder = CityDressPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(CityDressPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDressTemplateId() != 0) {
            builder.setDressTemplateId(this.getDressTemplateId());
            fieldCnt++;
        }  else if (builder.hasDressTemplateId()) {
            // 清理DressTemplateId
            builder.clearDressTemplateId();
            fieldCnt++;
        }
        if (this.getTimeoutTsMs() != 0L) {
            builder.setTimeoutTsMs(this.getTimeoutTsMs());
            fieldCnt++;
        }  else if (builder.hasTimeoutTsMs()) {
            // 清理TimeoutTsMs
            builder.clearTimeoutTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(CityDressPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DRESSTEMPLATEID)) {
            builder.setDressTemplateId(this.getDressTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TIMEOUTTSMS)) {
            builder.setTimeoutTsMs(this.getTimeoutTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(CityDressPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DRESSTEMPLATEID)) {
            builder.setDressTemplateId(this.getDressTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TIMEOUTTSMS)) {
            builder.setTimeoutTsMs(this.getTimeoutTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(CityDressPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDressTemplateId()) {
            this.innerSetDressTemplateId(proto.getDressTemplateId());
        } else {
            this.innerSetDressTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTimeoutTsMs()) {
            this.innerSetTimeoutTsMs(proto.getTimeoutTsMs());
        } else {
            this.innerSetTimeoutTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return CityDressProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(CityDressPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDressTemplateId()) {
            this.setDressTemplateId(proto.getDressTemplateId());
            fieldCnt++;
        }
        if (proto.hasTimeoutTsMs()) {
            this.setTimeoutTsMs(proto.getTimeoutTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CityDress.Builder getCopyDbBuilder() {
        final CityDress.Builder builder = CityDress.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(CityDress.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDressTemplateId() != 0) {
            builder.setDressTemplateId(this.getDressTemplateId());
            fieldCnt++;
        }  else if (builder.hasDressTemplateId()) {
            // 清理DressTemplateId
            builder.clearDressTemplateId();
            fieldCnt++;
        }
        if (this.getTimeoutTsMs() != 0L) {
            builder.setTimeoutTsMs(this.getTimeoutTsMs());
            fieldCnt++;
        }  else if (builder.hasTimeoutTsMs()) {
            // 清理TimeoutTsMs
            builder.clearTimeoutTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(CityDress.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DRESSTEMPLATEID)) {
            builder.setDressTemplateId(this.getDressTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TIMEOUTTSMS)) {
            builder.setTimeoutTsMs(this.getTimeoutTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(CityDress proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDressTemplateId()) {
            this.innerSetDressTemplateId(proto.getDressTemplateId());
        } else {
            this.innerSetDressTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTimeoutTsMs()) {
            this.innerSetTimeoutTsMs(proto.getTimeoutTsMs());
        } else {
            this.innerSetTimeoutTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return CityDressProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(CityDress proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDressTemplateId()) {
            this.setDressTemplateId(proto.getDressTemplateId());
            fieldCnt++;
        }
        if (proto.hasTimeoutTsMs()) {
            this.setTimeoutTsMs(proto.getTimeoutTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CityDress.Builder getCopySsBuilder() {
        final CityDress.Builder builder = CityDress.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(CityDress.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDressTemplateId() != 0) {
            builder.setDressTemplateId(this.getDressTemplateId());
            fieldCnt++;
        }  else if (builder.hasDressTemplateId()) {
            // 清理DressTemplateId
            builder.clearDressTemplateId();
            fieldCnt++;
        }
        if (this.getTimeoutTsMs() != 0L) {
            builder.setTimeoutTsMs(this.getTimeoutTsMs());
            fieldCnt++;
        }  else if (builder.hasTimeoutTsMs()) {
            // 清理TimeoutTsMs
            builder.clearTimeoutTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(CityDress.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DRESSTEMPLATEID)) {
            builder.setDressTemplateId(this.getDressTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TIMEOUTTSMS)) {
            builder.setTimeoutTsMs(this.getTimeoutTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(CityDress proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDressTemplateId()) {
            this.innerSetDressTemplateId(proto.getDressTemplateId());
        } else {
            this.innerSetDressTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTimeoutTsMs()) {
            this.innerSetTimeoutTsMs(proto.getTimeoutTsMs());
        } else {
            this.innerSetTimeoutTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return CityDressProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(CityDress proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDressTemplateId()) {
            this.setDressTemplateId(proto.getDressTemplateId());
            fieldCnt++;
        }
        if (proto.hasTimeoutTsMs()) {
            this.setTimeoutTsMs(proto.getTimeoutTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        CityDress.Builder builder = CityDress.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.dressTemplateId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof CityDressProp)) {
            return false;
        }
        final CityDressProp otherNode = (CityDressProp) node;
        if (this.dressTemplateId != otherNode.dressTemplateId) {
            return false;
        }
        if (this.timeoutTsMs != otherNode.timeoutTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
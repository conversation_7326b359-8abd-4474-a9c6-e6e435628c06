package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructMail.MailContent;
import com.yorha.proto.Struct;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructClan;
import com.yorha.proto.StructMail;
import com.yorha.proto.StructMailPB.MailContentPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructBattlePB;
import com.yorha.proto.StructClanPB;
import com.yorha.proto.StructMailPB;


/**
 * <AUTHOR> auto gen
 */
public class MailContentProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CONTENTTYPE = 0;
    public static final int FIELD_INDEX_BATTLERECORD = 1;
    public static final int FIELD_INDEX_DISPLAYDATA = 2;
    public static final int FIELD_INDEX_COLLECTDATA = 3;
    public static final int FIELD_INDEX_EXPLOREDATA = 4;
    public static final int FIELD_INDEX_SPYDATA = 5;
    public static final int FIELD_INDEX_TEXT = 6;
    public static final int FIELD_INDEX_CLANTECHRESEARCHFINISHDATA = 7;
    public static final int FIELD_INDEX_CLANCONTRIBUTIONRANKINGDATA = 8;
    public static final int FIELD_INDEX_ACTIVITYREWARDDATA = 9;
    public static final int FIELD_INDEX_CLANAPPLYDATA = 10;
    public static final int FIELD_INDEX_ARENAMAILDATA = 11;
    public static final int FIELD_INDEX_REWARDRECORDS = 12;
    public static final int FIELD_INDEX_ASSISTDATA = 13;
    public static final int FIELD_INDEX_BUYGOODSDATA = 14;
    public static final int FIELD_INDEX_CLANBUILDINGDATA = 15;
    public static final int FIELD_INDEX_KINGBUFFDATA = 16;
    public static final int FIELD_INDEX_KINGGAINTAXDATA = 17;
    public static final int FIELD_INDEX_CHARGEBASEDATA = 18;
    public static final int FIELD_INDEX_CLANINVITEDATA = 19;

    public static final int FIELD_COUNT = 20;

    private long markBits0 = 0L;

    private MailContentType contentType = MailContentType.forNumber(0);
    private BattleRecordAllProp battleRecord = null;
    private DisplayDataProp displayData = null;
    private CollectDataProp collectData = null;
    private ExploreDataProp exploreData = null;
    private SpyDataProp spyData = null;
    private String text = Constant.DEFAULT_STR_VALUE;
    private MailClanTechResearchFinishDataProp clanTechResearchFinishData = null;
    private MailClanContributionRankingDataProp clanContributionRankingData = null;
    private ActivityRewardDataProp ActivityRewardData = null;
    private ClanApplyDataProp clanApplyData = null;
    private ArenaMailDataProp arenaMailData = null;
    private RewardRecordListProp rewardRecords = null;
    private ResourceAssistDataProp assistData = null;
    private MailBuyGoodsDataProp buyGoodsData = null;
    private MailClanBuildingDataProp clanBuildingData = null;
    private MailKingBuffOrSkillDataProp kingBuffData = null;
    private MailKingGainTaxDataProp kingGainTaxData = null;
    private MailChargeBaseDataProp chargeBaseData = null;
    private MailClanInviteDataProp clanInviteData = null;

    public MailContentProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MailContentProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get contentType
     *
     * @return contentType value
     */
    public MailContentType getContentType() {
        return this.contentType;
    }

    /**
     * set contentType && set marked
     *
     * @param contentType new value
     * @return current object
     */
    public MailContentProp setContentType(MailContentType contentType) {
        if (contentType == null) {
            throw new NullPointerException();
        }
        if (this.contentType != contentType) {
            this.mark(FIELD_INDEX_CONTENTTYPE);
            this.contentType = contentType;
        }
        return this;
    }

    /**
     * inner set contentType
     *
     * @param contentType new value
     */
    private void innerSetContentType(MailContentType contentType) {
        this.contentType = contentType;
    }

    /**
     * get battleRecord
     *
     * @return battleRecord value
     */
    public BattleRecordAllProp getBattleRecord() {
        if (this.battleRecord == null) {
            this.battleRecord = new BattleRecordAllProp(this, FIELD_INDEX_BATTLERECORD);
        }
        return this.battleRecord;
    }

    /**
     * get displayData
     *
     * @return displayData value
     */
    public DisplayDataProp getDisplayData() {
        if (this.displayData == null) {
            this.displayData = new DisplayDataProp(this, FIELD_INDEX_DISPLAYDATA);
        }
        return this.displayData;
    }

    /**
     * get collectData
     *
     * @return collectData value
     */
    public CollectDataProp getCollectData() {
        if (this.collectData == null) {
            this.collectData = new CollectDataProp(this, FIELD_INDEX_COLLECTDATA);
        }
        return this.collectData;
    }

    /**
     * get exploreData
     *
     * @return exploreData value
     */
    public ExploreDataProp getExploreData() {
        if (this.exploreData == null) {
            this.exploreData = new ExploreDataProp(this, FIELD_INDEX_EXPLOREDATA);
        }
        return this.exploreData;
    }

    /**
     * get spyData
     *
     * @return spyData value
     */
    public SpyDataProp getSpyData() {
        if (this.spyData == null) {
            this.spyData = new SpyDataProp(this, FIELD_INDEX_SPYDATA);
        }
        return this.spyData;
    }

    /**
     * get text
     *
     * @return text value
     */
    public String getText() {
        return this.text;
    }

    /**
     * set text && set marked
     *
     * @param text new value
     * @return current object
     */
    public MailContentProp setText(String text) {
        if (text == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.text, text)) {
            this.mark(FIELD_INDEX_TEXT);
            this.text = text;
        }
        return this;
    }

    /**
     * inner set text
     *
     * @param text new value
     */
    private void innerSetText(String text) {
        this.text = text;
    }

    /**
     * get clanTechResearchFinishData
     *
     * @return clanTechResearchFinishData value
     */
    public MailClanTechResearchFinishDataProp getClanTechResearchFinishData() {
        if (this.clanTechResearchFinishData == null) {
            this.clanTechResearchFinishData = new MailClanTechResearchFinishDataProp(this, FIELD_INDEX_CLANTECHRESEARCHFINISHDATA);
        }
        return this.clanTechResearchFinishData;
    }

    /**
     * get clanContributionRankingData
     *
     * @return clanContributionRankingData value
     */
    public MailClanContributionRankingDataProp getClanContributionRankingData() {
        if (this.clanContributionRankingData == null) {
            this.clanContributionRankingData = new MailClanContributionRankingDataProp(this, FIELD_INDEX_CLANCONTRIBUTIONRANKINGDATA);
        }
        return this.clanContributionRankingData;
    }

    /**
     * get ActivityRewardData
     *
     * @return ActivityRewardData value
     */
    public ActivityRewardDataProp getActivityRewardData() {
        if (this.ActivityRewardData == null) {
            this.ActivityRewardData = new ActivityRewardDataProp(this, FIELD_INDEX_ACTIVITYREWARDDATA);
        }
        return this.ActivityRewardData;
    }

    /**
     * get clanApplyData
     *
     * @return clanApplyData value
     */
    public ClanApplyDataProp getClanApplyData() {
        if (this.clanApplyData == null) {
            this.clanApplyData = new ClanApplyDataProp(this, FIELD_INDEX_CLANAPPLYDATA);
        }
        return this.clanApplyData;
    }

    /**
     * get arenaMailData
     *
     * @return arenaMailData value
     */
    public ArenaMailDataProp getArenaMailData() {
        if (this.arenaMailData == null) {
            this.arenaMailData = new ArenaMailDataProp(this, FIELD_INDEX_ARENAMAILDATA);
        }
        return this.arenaMailData;
    }

    /**
     * get rewardRecords
     *
     * @return rewardRecords value
     */
    public RewardRecordListProp getRewardRecords() {
        if (this.rewardRecords == null) {
            this.rewardRecords = new RewardRecordListProp(this, FIELD_INDEX_REWARDRECORDS);
        }
        return this.rewardRecords;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addRewardRecords(RewardRecordProp v) {
        this.getRewardRecords().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public RewardRecordProp getRewardRecordsIndex(int index) {
        return this.getRewardRecords().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public RewardRecordProp removeRewardRecords(RewardRecordProp v) {
        if (this.rewardRecords == null) {
            return null;
        }
        if(this.rewardRecords.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getRewardRecordsSize() {
        if (this.rewardRecords == null) {
            return 0;
        }
        return this.rewardRecords.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isRewardRecordsEmpty() {
        if (this.rewardRecords == null) {
            return true;
        }
        return this.getRewardRecords().isEmpty();
    }

    /**
     * clear list
     */
    public void clearRewardRecords() {
        this.getRewardRecords().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public RewardRecordProp removeRewardRecordsIndex(int index) {
        return this.getRewardRecords().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public RewardRecordProp setRewardRecordsIndex(int index, RewardRecordProp v) {
        return this.getRewardRecords().set(index, v);
    }
    /**
     * get assistData
     *
     * @return assistData value
     */
    public ResourceAssistDataProp getAssistData() {
        if (this.assistData == null) {
            this.assistData = new ResourceAssistDataProp(this, FIELD_INDEX_ASSISTDATA);
        }
        return this.assistData;
    }

    /**
     * get buyGoodsData
     *
     * @return buyGoodsData value
     */
    public MailBuyGoodsDataProp getBuyGoodsData() {
        if (this.buyGoodsData == null) {
            this.buyGoodsData = new MailBuyGoodsDataProp(this, FIELD_INDEX_BUYGOODSDATA);
        }
        return this.buyGoodsData;
    }

    /**
     * get clanBuildingData
     *
     * @return clanBuildingData value
     */
    public MailClanBuildingDataProp getClanBuildingData() {
        if (this.clanBuildingData == null) {
            this.clanBuildingData = new MailClanBuildingDataProp(this, FIELD_INDEX_CLANBUILDINGDATA);
        }
        return this.clanBuildingData;
    }

    /**
     * get kingBuffData
     *
     * @return kingBuffData value
     */
    public MailKingBuffOrSkillDataProp getKingBuffData() {
        if (this.kingBuffData == null) {
            this.kingBuffData = new MailKingBuffOrSkillDataProp(this, FIELD_INDEX_KINGBUFFDATA);
        }
        return this.kingBuffData;
    }

    /**
     * get kingGainTaxData
     *
     * @return kingGainTaxData value
     */
    public MailKingGainTaxDataProp getKingGainTaxData() {
        if (this.kingGainTaxData == null) {
            this.kingGainTaxData = new MailKingGainTaxDataProp(this, FIELD_INDEX_KINGGAINTAXDATA);
        }
        return this.kingGainTaxData;
    }

    /**
     * get chargeBaseData
     *
     * @return chargeBaseData value
     */
    public MailChargeBaseDataProp getChargeBaseData() {
        if (this.chargeBaseData == null) {
            this.chargeBaseData = new MailChargeBaseDataProp(this, FIELD_INDEX_CHARGEBASEDATA);
        }
        return this.chargeBaseData;
    }

    /**
     * get clanInviteData
     *
     * @return clanInviteData value
     */
    public MailClanInviteDataProp getClanInviteData() {
        if (this.clanInviteData == null) {
            this.clanInviteData = new MailClanInviteDataProp(this, FIELD_INDEX_CLANINVITEDATA);
        }
        return this.clanInviteData;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailContentPB.Builder getCopyCsBuilder() {
        final MailContentPB.Builder builder = MailContentPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MailContentPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getContentType() != MailContentType.forNumber(0)) {
            builder.setContentType(this.getContentType());
            fieldCnt++;
        }  else if (builder.hasContentType()) {
            // 清理ContentType
            builder.clearContentType();
            fieldCnt++;
        }
        if (this.battleRecord != null) {
            StructBattlePB.BattleRecordAllPB.Builder tmpBuilder = StructBattlePB.BattleRecordAllPB.newBuilder();
            final int tmpFieldCnt = this.battleRecord.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattleRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattleRecord();
            }
        }  else if (builder.hasBattleRecord()) {
            // 清理BattleRecord
            builder.clearBattleRecord();
            fieldCnt++;
        }
        if (this.displayData != null) {
            StructPB.DisplayDataPB.Builder tmpBuilder = StructPB.DisplayDataPB.newBuilder();
            final int tmpFieldCnt = this.displayData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDisplayData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDisplayData();
            }
        }  else if (builder.hasDisplayData()) {
            // 清理DisplayData
            builder.clearDisplayData();
            fieldCnt++;
        }
        if (this.collectData != null) {
            StructPB.CollectDataPB.Builder tmpBuilder = StructPB.CollectDataPB.newBuilder();
            final int tmpFieldCnt = this.collectData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCollectData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCollectData();
            }
        }  else if (builder.hasCollectData()) {
            // 清理CollectData
            builder.clearCollectData();
            fieldCnt++;
        }
        if (this.exploreData != null) {
            StructPB.ExploreDataPB.Builder tmpBuilder = StructPB.ExploreDataPB.newBuilder();
            final int tmpFieldCnt = this.exploreData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExploreData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExploreData();
            }
        }  else if (builder.hasExploreData()) {
            // 清理ExploreData
            builder.clearExploreData();
            fieldCnt++;
        }
        if (this.spyData != null) {
            StructPB.SpyDataPB.Builder tmpBuilder = StructPB.SpyDataPB.newBuilder();
            final int tmpFieldCnt = this.spyData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyData();
            }
        }  else if (builder.hasSpyData()) {
            // 清理SpyData
            builder.clearSpyData();
            fieldCnt++;
        }
        if (!this.getText().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setText(this.getText());
            fieldCnt++;
        }  else if (builder.hasText()) {
            // 清理Text
            builder.clearText();
            fieldCnt++;
        }
        if (this.clanTechResearchFinishData != null) {
            StructMailPB.MailClanTechResearchFinishDataPB.Builder tmpBuilder = StructMailPB.MailClanTechResearchFinishDataPB.newBuilder();
            final int tmpFieldCnt = this.clanTechResearchFinishData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanTechResearchFinishData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanTechResearchFinishData();
            }
        }  else if (builder.hasClanTechResearchFinishData()) {
            // 清理ClanTechResearchFinishData
            builder.clearClanTechResearchFinishData();
            fieldCnt++;
        }
        if (this.clanContributionRankingData != null) {
            StructMailPB.MailClanContributionRankingDataPB.Builder tmpBuilder = StructMailPB.MailClanContributionRankingDataPB.newBuilder();
            final int tmpFieldCnt = this.clanContributionRankingData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanContributionRankingData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanContributionRankingData();
            }
        }  else if (builder.hasClanContributionRankingData()) {
            // 清理ClanContributionRankingData
            builder.clearClanContributionRankingData();
            fieldCnt++;
        }
        if (this.ActivityRewardData != null) {
            StructPB.ActivityRewardDataPB.Builder tmpBuilder = StructPB.ActivityRewardDataPB.newBuilder();
            final int tmpFieldCnt = this.ActivityRewardData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActivityRewardData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActivityRewardData();
            }
        }  else if (builder.hasActivityRewardData()) {
            // 清理ActivityRewardData
            builder.clearActivityRewardData();
            fieldCnt++;
        }
        if (this.clanApplyData != null) {
            StructClanPB.ClanApplyDataPB.Builder tmpBuilder = StructClanPB.ClanApplyDataPB.newBuilder();
            final int tmpFieldCnt = this.clanApplyData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanApplyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanApplyData();
            }
        }  else if (builder.hasClanApplyData()) {
            // 清理ClanApplyData
            builder.clearClanApplyData();
            fieldCnt++;
        }
        if (this.arenaMailData != null) {
            StructPB.ArenaMailDataPB.Builder tmpBuilder = StructPB.ArenaMailDataPB.newBuilder();
            final int tmpFieldCnt = this.arenaMailData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArenaMailData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArenaMailData();
            }
        }  else if (builder.hasArenaMailData()) {
            // 清理ArenaMailData
            builder.clearArenaMailData();
            fieldCnt++;
        }
        if (this.rewardRecords != null) {
            StructPB.RewardRecordListPB.Builder tmpBuilder = StructPB.RewardRecordListPB.newBuilder();
            final int tmpFieldCnt = this.rewardRecords.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardRecords();
            }
        }  else if (builder.hasRewardRecords()) {
            // 清理RewardRecords
            builder.clearRewardRecords();
            fieldCnt++;
        }
        if (this.assistData != null) {
            StructMailPB.ResourceAssistDataPB.Builder tmpBuilder = StructMailPB.ResourceAssistDataPB.newBuilder();
            final int tmpFieldCnt = this.assistData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAssistData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAssistData();
            }
        }  else if (builder.hasAssistData()) {
            // 清理AssistData
            builder.clearAssistData();
            fieldCnt++;
        }
        if (this.buyGoodsData != null) {
            StructMailPB.MailBuyGoodsDataPB.Builder tmpBuilder = StructMailPB.MailBuyGoodsDataPB.newBuilder();
            final int tmpFieldCnt = this.buyGoodsData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuyGoodsData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuyGoodsData();
            }
        }  else if (builder.hasBuyGoodsData()) {
            // 清理BuyGoodsData
            builder.clearBuyGoodsData();
            fieldCnt++;
        }
        if (this.clanBuildingData != null) {
            StructMailPB.MailClanBuildingDataPB.Builder tmpBuilder = StructMailPB.MailClanBuildingDataPB.newBuilder();
            final int tmpFieldCnt = this.clanBuildingData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanBuildingData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanBuildingData();
            }
        }  else if (builder.hasClanBuildingData()) {
            // 清理ClanBuildingData
            builder.clearClanBuildingData();
            fieldCnt++;
        }
        if (this.kingBuffData != null) {
            StructMailPB.MailKingBuffOrSkillDataPB.Builder tmpBuilder = StructMailPB.MailKingBuffOrSkillDataPB.newBuilder();
            final int tmpFieldCnt = this.kingBuffData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKingBuffData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKingBuffData();
            }
        }  else if (builder.hasKingBuffData()) {
            // 清理KingBuffData
            builder.clearKingBuffData();
            fieldCnt++;
        }
        if (this.kingGainTaxData != null) {
            StructMailPB.MailKingGainTaxDataPB.Builder tmpBuilder = StructMailPB.MailKingGainTaxDataPB.newBuilder();
            final int tmpFieldCnt = this.kingGainTaxData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKingGainTaxData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKingGainTaxData();
            }
        }  else if (builder.hasKingGainTaxData()) {
            // 清理KingGainTaxData
            builder.clearKingGainTaxData();
            fieldCnt++;
        }
        if (this.chargeBaseData != null) {
            StructMailPB.MailChargeBaseDataPB.Builder tmpBuilder = StructMailPB.MailChargeBaseDataPB.newBuilder();
            final int tmpFieldCnt = this.chargeBaseData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChargeBaseData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChargeBaseData();
            }
        }  else if (builder.hasChargeBaseData()) {
            // 清理ChargeBaseData
            builder.clearChargeBaseData();
            fieldCnt++;
        }
        if (this.clanInviteData != null) {
            StructMailPB.MailClanInviteDataPB.Builder tmpBuilder = StructMailPB.MailClanInviteDataPB.newBuilder();
            final int tmpFieldCnt = this.clanInviteData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanInviteData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanInviteData();
            }
        }  else if (builder.hasClanInviteData()) {
            // 清理ClanInviteData
            builder.clearClanInviteData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MailContentPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONTENTTYPE)) {
            builder.setContentType(this.getContentType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLERECORD) && this.battleRecord != null) {
            final boolean needClear = !builder.hasBattleRecord();
            final int tmpFieldCnt = this.battleRecord.copyChangeToCs(builder.getBattleRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattleRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_DISPLAYDATA) && this.displayData != null) {
            final boolean needClear = !builder.hasDisplayData();
            final int tmpFieldCnt = this.displayData.copyChangeToCs(builder.getDisplayDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDisplayData();
            }
        }
        if (this.hasMark(FIELD_INDEX_COLLECTDATA) && this.collectData != null) {
            final boolean needClear = !builder.hasCollectData();
            final int tmpFieldCnt = this.collectData.copyChangeToCs(builder.getCollectDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCollectData();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXPLOREDATA) && this.exploreData != null) {
            final boolean needClear = !builder.hasExploreData();
            final int tmpFieldCnt = this.exploreData.copyChangeToCs(builder.getExploreDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExploreData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYDATA) && this.spyData != null) {
            final boolean needClear = !builder.hasSpyData();
            final int tmpFieldCnt = this.spyData.copyChangeToCs(builder.getSpyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TEXT)) {
            builder.setText(this.getText());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANTECHRESEARCHFINISHDATA) && this.clanTechResearchFinishData != null) {
            final boolean needClear = !builder.hasClanTechResearchFinishData();
            final int tmpFieldCnt = this.clanTechResearchFinishData.copyChangeToCs(builder.getClanTechResearchFinishDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanTechResearchFinishData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANCONTRIBUTIONRANKINGDATA) && this.clanContributionRankingData != null) {
            final boolean needClear = !builder.hasClanContributionRankingData();
            final int tmpFieldCnt = this.clanContributionRankingData.copyChangeToCs(builder.getClanContributionRankingDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanContributionRankingData();
            }
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITYREWARDDATA) && this.ActivityRewardData != null) {
            final boolean needClear = !builder.hasActivityRewardData();
            final int tmpFieldCnt = this.ActivityRewardData.copyChangeToCs(builder.getActivityRewardDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivityRewardData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANAPPLYDATA) && this.clanApplyData != null) {
            final boolean needClear = !builder.hasClanApplyData();
            final int tmpFieldCnt = this.clanApplyData.copyChangeToCs(builder.getClanApplyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanApplyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARENAMAILDATA) && this.arenaMailData != null) {
            final boolean needClear = !builder.hasArenaMailData();
            final int tmpFieldCnt = this.arenaMailData.copyChangeToCs(builder.getArenaMailDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArenaMailData();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDRECORDS) && this.rewardRecords != null) {
            final boolean needClear = !builder.hasRewardRecords();
            final int tmpFieldCnt = this.rewardRecords.copyChangeToCs(builder.getRewardRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardRecords();
            }
        }
        if (this.hasMark(FIELD_INDEX_ASSISTDATA) && this.assistData != null) {
            final boolean needClear = !builder.hasAssistData();
            final int tmpFieldCnt = this.assistData.copyChangeToCs(builder.getAssistDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAssistData();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUYGOODSDATA) && this.buyGoodsData != null) {
            final boolean needClear = !builder.hasBuyGoodsData();
            final int tmpFieldCnt = this.buyGoodsData.copyChangeToCs(builder.getBuyGoodsDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuyGoodsData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANBUILDINGDATA) && this.clanBuildingData != null) {
            final boolean needClear = !builder.hasClanBuildingData();
            final int tmpFieldCnt = this.clanBuildingData.copyChangeToCs(builder.getClanBuildingDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanBuildingData();
            }
        }
        if (this.hasMark(FIELD_INDEX_KINGBUFFDATA) && this.kingBuffData != null) {
            final boolean needClear = !builder.hasKingBuffData();
            final int tmpFieldCnt = this.kingBuffData.copyChangeToCs(builder.getKingBuffDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingBuffData();
            }
        }
        if (this.hasMark(FIELD_INDEX_KINGGAINTAXDATA) && this.kingGainTaxData != null) {
            final boolean needClear = !builder.hasKingGainTaxData();
            final int tmpFieldCnt = this.kingGainTaxData.copyChangeToCs(builder.getKingGainTaxDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingGainTaxData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CHARGEBASEDATA) && this.chargeBaseData != null) {
            final boolean needClear = !builder.hasChargeBaseData();
            final int tmpFieldCnt = this.chargeBaseData.copyChangeToCs(builder.getChargeBaseDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChargeBaseData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANINVITEDATA) && this.clanInviteData != null) {
            final boolean needClear = !builder.hasClanInviteData();
            final int tmpFieldCnt = this.clanInviteData.copyChangeToCs(builder.getClanInviteDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanInviteData();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MailContentPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONTENTTYPE)) {
            builder.setContentType(this.getContentType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLERECORD) && this.battleRecord != null) {
            final boolean needClear = !builder.hasBattleRecord();
            final int tmpFieldCnt = this.battleRecord.copyChangeToAndClearDeleteKeysCs(builder.getBattleRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattleRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_DISPLAYDATA) && this.displayData != null) {
            final boolean needClear = !builder.hasDisplayData();
            final int tmpFieldCnt = this.displayData.copyChangeToAndClearDeleteKeysCs(builder.getDisplayDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDisplayData();
            }
        }
        if (this.hasMark(FIELD_INDEX_COLLECTDATA) && this.collectData != null) {
            final boolean needClear = !builder.hasCollectData();
            final int tmpFieldCnt = this.collectData.copyChangeToAndClearDeleteKeysCs(builder.getCollectDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCollectData();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXPLOREDATA) && this.exploreData != null) {
            final boolean needClear = !builder.hasExploreData();
            final int tmpFieldCnt = this.exploreData.copyChangeToAndClearDeleteKeysCs(builder.getExploreDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExploreData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYDATA) && this.spyData != null) {
            final boolean needClear = !builder.hasSpyData();
            final int tmpFieldCnt = this.spyData.copyChangeToAndClearDeleteKeysCs(builder.getSpyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TEXT)) {
            builder.setText(this.getText());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANTECHRESEARCHFINISHDATA) && this.clanTechResearchFinishData != null) {
            final boolean needClear = !builder.hasClanTechResearchFinishData();
            final int tmpFieldCnt = this.clanTechResearchFinishData.copyChangeToAndClearDeleteKeysCs(builder.getClanTechResearchFinishDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanTechResearchFinishData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANCONTRIBUTIONRANKINGDATA) && this.clanContributionRankingData != null) {
            final boolean needClear = !builder.hasClanContributionRankingData();
            final int tmpFieldCnt = this.clanContributionRankingData.copyChangeToAndClearDeleteKeysCs(builder.getClanContributionRankingDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanContributionRankingData();
            }
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITYREWARDDATA) && this.ActivityRewardData != null) {
            final boolean needClear = !builder.hasActivityRewardData();
            final int tmpFieldCnt = this.ActivityRewardData.copyChangeToAndClearDeleteKeysCs(builder.getActivityRewardDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivityRewardData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANAPPLYDATA) && this.clanApplyData != null) {
            final boolean needClear = !builder.hasClanApplyData();
            final int tmpFieldCnt = this.clanApplyData.copyChangeToAndClearDeleteKeysCs(builder.getClanApplyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanApplyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARENAMAILDATA) && this.arenaMailData != null) {
            final boolean needClear = !builder.hasArenaMailData();
            final int tmpFieldCnt = this.arenaMailData.copyChangeToAndClearDeleteKeysCs(builder.getArenaMailDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArenaMailData();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDRECORDS) && this.rewardRecords != null) {
            final boolean needClear = !builder.hasRewardRecords();
            final int tmpFieldCnt = this.rewardRecords.copyChangeToCs(builder.getRewardRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardRecords();
            }
        }
        if (this.hasMark(FIELD_INDEX_ASSISTDATA) && this.assistData != null) {
            final boolean needClear = !builder.hasAssistData();
            final int tmpFieldCnt = this.assistData.copyChangeToAndClearDeleteKeysCs(builder.getAssistDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAssistData();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUYGOODSDATA) && this.buyGoodsData != null) {
            final boolean needClear = !builder.hasBuyGoodsData();
            final int tmpFieldCnt = this.buyGoodsData.copyChangeToAndClearDeleteKeysCs(builder.getBuyGoodsDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuyGoodsData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANBUILDINGDATA) && this.clanBuildingData != null) {
            final boolean needClear = !builder.hasClanBuildingData();
            final int tmpFieldCnt = this.clanBuildingData.copyChangeToAndClearDeleteKeysCs(builder.getClanBuildingDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanBuildingData();
            }
        }
        if (this.hasMark(FIELD_INDEX_KINGBUFFDATA) && this.kingBuffData != null) {
            final boolean needClear = !builder.hasKingBuffData();
            final int tmpFieldCnt = this.kingBuffData.copyChangeToAndClearDeleteKeysCs(builder.getKingBuffDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingBuffData();
            }
        }
        if (this.hasMark(FIELD_INDEX_KINGGAINTAXDATA) && this.kingGainTaxData != null) {
            final boolean needClear = !builder.hasKingGainTaxData();
            final int tmpFieldCnt = this.kingGainTaxData.copyChangeToAndClearDeleteKeysCs(builder.getKingGainTaxDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingGainTaxData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CHARGEBASEDATA) && this.chargeBaseData != null) {
            final boolean needClear = !builder.hasChargeBaseData();
            final int tmpFieldCnt = this.chargeBaseData.copyChangeToAndClearDeleteKeysCs(builder.getChargeBaseDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChargeBaseData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANINVITEDATA) && this.clanInviteData != null) {
            final boolean needClear = !builder.hasClanInviteData();
            final int tmpFieldCnt = this.clanInviteData.copyChangeToAndClearDeleteKeysCs(builder.getClanInviteDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanInviteData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MailContentPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasContentType()) {
            this.innerSetContentType(proto.getContentType());
        } else {
            this.innerSetContentType(MailContentType.forNumber(0));
        }
        if (proto.hasBattleRecord()) {
            this.getBattleRecord().mergeFromCs(proto.getBattleRecord());
        } else {
            if (this.battleRecord != null) {
                this.battleRecord.mergeFromCs(proto.getBattleRecord());
            }
        }
        if (proto.hasDisplayData()) {
            this.getDisplayData().mergeFromCs(proto.getDisplayData());
        } else {
            if (this.displayData != null) {
                this.displayData.mergeFromCs(proto.getDisplayData());
            }
        }
        if (proto.hasCollectData()) {
            this.getCollectData().mergeFromCs(proto.getCollectData());
        } else {
            if (this.collectData != null) {
                this.collectData.mergeFromCs(proto.getCollectData());
            }
        }
        if (proto.hasExploreData()) {
            this.getExploreData().mergeFromCs(proto.getExploreData());
        } else {
            if (this.exploreData != null) {
                this.exploreData.mergeFromCs(proto.getExploreData());
            }
        }
        if (proto.hasSpyData()) {
            this.getSpyData().mergeFromCs(proto.getSpyData());
        } else {
            if (this.spyData != null) {
                this.spyData.mergeFromCs(proto.getSpyData());
            }
        }
        if (proto.hasText()) {
            this.innerSetText(proto.getText());
        } else {
            this.innerSetText(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanTechResearchFinishData()) {
            this.getClanTechResearchFinishData().mergeFromCs(proto.getClanTechResearchFinishData());
        } else {
            if (this.clanTechResearchFinishData != null) {
                this.clanTechResearchFinishData.mergeFromCs(proto.getClanTechResearchFinishData());
            }
        }
        if (proto.hasClanContributionRankingData()) {
            this.getClanContributionRankingData().mergeFromCs(proto.getClanContributionRankingData());
        } else {
            if (this.clanContributionRankingData != null) {
                this.clanContributionRankingData.mergeFromCs(proto.getClanContributionRankingData());
            }
        }
        if (proto.hasActivityRewardData()) {
            this.getActivityRewardData().mergeFromCs(proto.getActivityRewardData());
        } else {
            if (this.ActivityRewardData != null) {
                this.ActivityRewardData.mergeFromCs(proto.getActivityRewardData());
            }
        }
        if (proto.hasClanApplyData()) {
            this.getClanApplyData().mergeFromCs(proto.getClanApplyData());
        } else {
            if (this.clanApplyData != null) {
                this.clanApplyData.mergeFromCs(proto.getClanApplyData());
            }
        }
        if (proto.hasArenaMailData()) {
            this.getArenaMailData().mergeFromCs(proto.getArenaMailData());
        } else {
            if (this.arenaMailData != null) {
                this.arenaMailData.mergeFromCs(proto.getArenaMailData());
            }
        }
        if (proto.hasRewardRecords()) {
            this.getRewardRecords().mergeFromCs(proto.getRewardRecords());
        } else {
            if (this.rewardRecords != null) {
                this.rewardRecords.mergeFromCs(proto.getRewardRecords());
            }
        }
        if (proto.hasAssistData()) {
            this.getAssistData().mergeFromCs(proto.getAssistData());
        } else {
            if (this.assistData != null) {
                this.assistData.mergeFromCs(proto.getAssistData());
            }
        }
        if (proto.hasBuyGoodsData()) {
            this.getBuyGoodsData().mergeFromCs(proto.getBuyGoodsData());
        } else {
            if (this.buyGoodsData != null) {
                this.buyGoodsData.mergeFromCs(proto.getBuyGoodsData());
            }
        }
        if (proto.hasClanBuildingData()) {
            this.getClanBuildingData().mergeFromCs(proto.getClanBuildingData());
        } else {
            if (this.clanBuildingData != null) {
                this.clanBuildingData.mergeFromCs(proto.getClanBuildingData());
            }
        }
        if (proto.hasKingBuffData()) {
            this.getKingBuffData().mergeFromCs(proto.getKingBuffData());
        } else {
            if (this.kingBuffData != null) {
                this.kingBuffData.mergeFromCs(proto.getKingBuffData());
            }
        }
        if (proto.hasKingGainTaxData()) {
            this.getKingGainTaxData().mergeFromCs(proto.getKingGainTaxData());
        } else {
            if (this.kingGainTaxData != null) {
                this.kingGainTaxData.mergeFromCs(proto.getKingGainTaxData());
            }
        }
        if (proto.hasChargeBaseData()) {
            this.getChargeBaseData().mergeFromCs(proto.getChargeBaseData());
        } else {
            if (this.chargeBaseData != null) {
                this.chargeBaseData.mergeFromCs(proto.getChargeBaseData());
            }
        }
        if (proto.hasClanInviteData()) {
            this.getClanInviteData().mergeFromCs(proto.getClanInviteData());
        } else {
            if (this.clanInviteData != null) {
                this.clanInviteData.mergeFromCs(proto.getClanInviteData());
            }
        }
        this.markAll();
        return MailContentProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MailContentPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasContentType()) {
            this.setContentType(proto.getContentType());
            fieldCnt++;
        }
        if (proto.hasBattleRecord()) {
            this.getBattleRecord().mergeChangeFromCs(proto.getBattleRecord());
            fieldCnt++;
        }
        if (proto.hasDisplayData()) {
            this.getDisplayData().mergeChangeFromCs(proto.getDisplayData());
            fieldCnt++;
        }
        if (proto.hasCollectData()) {
            this.getCollectData().mergeChangeFromCs(proto.getCollectData());
            fieldCnt++;
        }
        if (proto.hasExploreData()) {
            this.getExploreData().mergeChangeFromCs(proto.getExploreData());
            fieldCnt++;
        }
        if (proto.hasSpyData()) {
            this.getSpyData().mergeChangeFromCs(proto.getSpyData());
            fieldCnt++;
        }
        if (proto.hasText()) {
            this.setText(proto.getText());
            fieldCnt++;
        }
        if (proto.hasClanTechResearchFinishData()) {
            this.getClanTechResearchFinishData().mergeChangeFromCs(proto.getClanTechResearchFinishData());
            fieldCnt++;
        }
        if (proto.hasClanContributionRankingData()) {
            this.getClanContributionRankingData().mergeChangeFromCs(proto.getClanContributionRankingData());
            fieldCnt++;
        }
        if (proto.hasActivityRewardData()) {
            this.getActivityRewardData().mergeChangeFromCs(proto.getActivityRewardData());
            fieldCnt++;
        }
        if (proto.hasClanApplyData()) {
            this.getClanApplyData().mergeChangeFromCs(proto.getClanApplyData());
            fieldCnt++;
        }
        if (proto.hasArenaMailData()) {
            this.getArenaMailData().mergeChangeFromCs(proto.getArenaMailData());
            fieldCnt++;
        }
        if (proto.hasRewardRecords()) {
            this.getRewardRecords().mergeChangeFromCs(proto.getRewardRecords());
            fieldCnt++;
        }
        if (proto.hasAssistData()) {
            this.getAssistData().mergeChangeFromCs(proto.getAssistData());
            fieldCnt++;
        }
        if (proto.hasBuyGoodsData()) {
            this.getBuyGoodsData().mergeChangeFromCs(proto.getBuyGoodsData());
            fieldCnt++;
        }
        if (proto.hasClanBuildingData()) {
            this.getClanBuildingData().mergeChangeFromCs(proto.getClanBuildingData());
            fieldCnt++;
        }
        if (proto.hasKingBuffData()) {
            this.getKingBuffData().mergeChangeFromCs(proto.getKingBuffData());
            fieldCnt++;
        }
        if (proto.hasKingGainTaxData()) {
            this.getKingGainTaxData().mergeChangeFromCs(proto.getKingGainTaxData());
            fieldCnt++;
        }
        if (proto.hasChargeBaseData()) {
            this.getChargeBaseData().mergeChangeFromCs(proto.getChargeBaseData());
            fieldCnt++;
        }
        if (proto.hasClanInviteData()) {
            this.getClanInviteData().mergeChangeFromCs(proto.getClanInviteData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailContent.Builder getCopyDbBuilder() {
        final MailContent.Builder builder = MailContent.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MailContent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getContentType() != MailContentType.forNumber(0)) {
            builder.setContentType(this.getContentType());
            fieldCnt++;
        }  else if (builder.hasContentType()) {
            // 清理ContentType
            builder.clearContentType();
            fieldCnt++;
        }
        if (this.battleRecord != null) {
            StructBattle.BattleRecordAll.Builder tmpBuilder = StructBattle.BattleRecordAll.newBuilder();
            final int tmpFieldCnt = this.battleRecord.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattleRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattleRecord();
            }
        }  else if (builder.hasBattleRecord()) {
            // 清理BattleRecord
            builder.clearBattleRecord();
            fieldCnt++;
        }
        if (this.displayData != null) {
            Struct.DisplayData.Builder tmpBuilder = Struct.DisplayData.newBuilder();
            final int tmpFieldCnt = this.displayData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDisplayData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDisplayData();
            }
        }  else if (builder.hasDisplayData()) {
            // 清理DisplayData
            builder.clearDisplayData();
            fieldCnt++;
        }
        if (this.collectData != null) {
            Struct.CollectData.Builder tmpBuilder = Struct.CollectData.newBuilder();
            final int tmpFieldCnt = this.collectData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCollectData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCollectData();
            }
        }  else if (builder.hasCollectData()) {
            // 清理CollectData
            builder.clearCollectData();
            fieldCnt++;
        }
        if (this.exploreData != null) {
            Struct.ExploreData.Builder tmpBuilder = Struct.ExploreData.newBuilder();
            final int tmpFieldCnt = this.exploreData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExploreData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExploreData();
            }
        }  else if (builder.hasExploreData()) {
            // 清理ExploreData
            builder.clearExploreData();
            fieldCnt++;
        }
        if (this.spyData != null) {
            Struct.SpyData.Builder tmpBuilder = Struct.SpyData.newBuilder();
            final int tmpFieldCnt = this.spyData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyData();
            }
        }  else if (builder.hasSpyData()) {
            // 清理SpyData
            builder.clearSpyData();
            fieldCnt++;
        }
        if (!this.getText().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setText(this.getText());
            fieldCnt++;
        }  else if (builder.hasText()) {
            // 清理Text
            builder.clearText();
            fieldCnt++;
        }
        if (this.clanTechResearchFinishData != null) {
            StructMail.MailClanTechResearchFinishData.Builder tmpBuilder = StructMail.MailClanTechResearchFinishData.newBuilder();
            final int tmpFieldCnt = this.clanTechResearchFinishData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanTechResearchFinishData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanTechResearchFinishData();
            }
        }  else if (builder.hasClanTechResearchFinishData()) {
            // 清理ClanTechResearchFinishData
            builder.clearClanTechResearchFinishData();
            fieldCnt++;
        }
        if (this.clanContributionRankingData != null) {
            StructMail.MailClanContributionRankingData.Builder tmpBuilder = StructMail.MailClanContributionRankingData.newBuilder();
            final int tmpFieldCnt = this.clanContributionRankingData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanContributionRankingData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanContributionRankingData();
            }
        }  else if (builder.hasClanContributionRankingData()) {
            // 清理ClanContributionRankingData
            builder.clearClanContributionRankingData();
            fieldCnt++;
        }
        if (this.ActivityRewardData != null) {
            Struct.ActivityRewardData.Builder tmpBuilder = Struct.ActivityRewardData.newBuilder();
            final int tmpFieldCnt = this.ActivityRewardData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActivityRewardData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActivityRewardData();
            }
        }  else if (builder.hasActivityRewardData()) {
            // 清理ActivityRewardData
            builder.clearActivityRewardData();
            fieldCnt++;
        }
        if (this.clanApplyData != null) {
            StructClan.ClanApplyData.Builder tmpBuilder = StructClan.ClanApplyData.newBuilder();
            final int tmpFieldCnt = this.clanApplyData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanApplyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanApplyData();
            }
        }  else if (builder.hasClanApplyData()) {
            // 清理ClanApplyData
            builder.clearClanApplyData();
            fieldCnt++;
        }
        if (this.arenaMailData != null) {
            Struct.ArenaMailData.Builder tmpBuilder = Struct.ArenaMailData.newBuilder();
            final int tmpFieldCnt = this.arenaMailData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArenaMailData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArenaMailData();
            }
        }  else if (builder.hasArenaMailData()) {
            // 清理ArenaMailData
            builder.clearArenaMailData();
            fieldCnt++;
        }
        if (this.rewardRecords != null) {
            Struct.RewardRecordList.Builder tmpBuilder = Struct.RewardRecordList.newBuilder();
            final int tmpFieldCnt = this.rewardRecords.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardRecords();
            }
        }  else if (builder.hasRewardRecords()) {
            // 清理RewardRecords
            builder.clearRewardRecords();
            fieldCnt++;
        }
        if (this.assistData != null) {
            StructMail.ResourceAssistData.Builder tmpBuilder = StructMail.ResourceAssistData.newBuilder();
            final int tmpFieldCnt = this.assistData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAssistData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAssistData();
            }
        }  else if (builder.hasAssistData()) {
            // 清理AssistData
            builder.clearAssistData();
            fieldCnt++;
        }
        if (this.buyGoodsData != null) {
            StructMail.MailBuyGoodsData.Builder tmpBuilder = StructMail.MailBuyGoodsData.newBuilder();
            final int tmpFieldCnt = this.buyGoodsData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuyGoodsData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuyGoodsData();
            }
        }  else if (builder.hasBuyGoodsData()) {
            // 清理BuyGoodsData
            builder.clearBuyGoodsData();
            fieldCnt++;
        }
        if (this.clanBuildingData != null) {
            StructMail.MailClanBuildingData.Builder tmpBuilder = StructMail.MailClanBuildingData.newBuilder();
            final int tmpFieldCnt = this.clanBuildingData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanBuildingData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanBuildingData();
            }
        }  else if (builder.hasClanBuildingData()) {
            // 清理ClanBuildingData
            builder.clearClanBuildingData();
            fieldCnt++;
        }
        if (this.kingBuffData != null) {
            StructMail.MailKingBuffOrSkillData.Builder tmpBuilder = StructMail.MailKingBuffOrSkillData.newBuilder();
            final int tmpFieldCnt = this.kingBuffData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKingBuffData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKingBuffData();
            }
        }  else if (builder.hasKingBuffData()) {
            // 清理KingBuffData
            builder.clearKingBuffData();
            fieldCnt++;
        }
        if (this.kingGainTaxData != null) {
            StructMail.MailKingGainTaxData.Builder tmpBuilder = StructMail.MailKingGainTaxData.newBuilder();
            final int tmpFieldCnt = this.kingGainTaxData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKingGainTaxData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKingGainTaxData();
            }
        }  else if (builder.hasKingGainTaxData()) {
            // 清理KingGainTaxData
            builder.clearKingGainTaxData();
            fieldCnt++;
        }
        if (this.chargeBaseData != null) {
            StructMail.MailChargeBaseData.Builder tmpBuilder = StructMail.MailChargeBaseData.newBuilder();
            final int tmpFieldCnt = this.chargeBaseData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChargeBaseData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChargeBaseData();
            }
        }  else if (builder.hasChargeBaseData()) {
            // 清理ChargeBaseData
            builder.clearChargeBaseData();
            fieldCnt++;
        }
        if (this.clanInviteData != null) {
            StructMail.MailClanInviteData.Builder tmpBuilder = StructMail.MailClanInviteData.newBuilder();
            final int tmpFieldCnt = this.clanInviteData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanInviteData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanInviteData();
            }
        }  else if (builder.hasClanInviteData()) {
            // 清理ClanInviteData
            builder.clearClanInviteData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MailContent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONTENTTYPE)) {
            builder.setContentType(this.getContentType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLERECORD) && this.battleRecord != null) {
            final boolean needClear = !builder.hasBattleRecord();
            final int tmpFieldCnt = this.battleRecord.copyChangeToDb(builder.getBattleRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattleRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_DISPLAYDATA) && this.displayData != null) {
            final boolean needClear = !builder.hasDisplayData();
            final int tmpFieldCnt = this.displayData.copyChangeToDb(builder.getDisplayDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDisplayData();
            }
        }
        if (this.hasMark(FIELD_INDEX_COLLECTDATA) && this.collectData != null) {
            final boolean needClear = !builder.hasCollectData();
            final int tmpFieldCnt = this.collectData.copyChangeToDb(builder.getCollectDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCollectData();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXPLOREDATA) && this.exploreData != null) {
            final boolean needClear = !builder.hasExploreData();
            final int tmpFieldCnt = this.exploreData.copyChangeToDb(builder.getExploreDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExploreData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYDATA) && this.spyData != null) {
            final boolean needClear = !builder.hasSpyData();
            final int tmpFieldCnt = this.spyData.copyChangeToDb(builder.getSpyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TEXT)) {
            builder.setText(this.getText());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANTECHRESEARCHFINISHDATA) && this.clanTechResearchFinishData != null) {
            final boolean needClear = !builder.hasClanTechResearchFinishData();
            final int tmpFieldCnt = this.clanTechResearchFinishData.copyChangeToDb(builder.getClanTechResearchFinishDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanTechResearchFinishData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANCONTRIBUTIONRANKINGDATA) && this.clanContributionRankingData != null) {
            final boolean needClear = !builder.hasClanContributionRankingData();
            final int tmpFieldCnt = this.clanContributionRankingData.copyChangeToDb(builder.getClanContributionRankingDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanContributionRankingData();
            }
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITYREWARDDATA) && this.ActivityRewardData != null) {
            final boolean needClear = !builder.hasActivityRewardData();
            final int tmpFieldCnt = this.ActivityRewardData.copyChangeToDb(builder.getActivityRewardDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivityRewardData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANAPPLYDATA) && this.clanApplyData != null) {
            final boolean needClear = !builder.hasClanApplyData();
            final int tmpFieldCnt = this.clanApplyData.copyChangeToDb(builder.getClanApplyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanApplyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARENAMAILDATA) && this.arenaMailData != null) {
            final boolean needClear = !builder.hasArenaMailData();
            final int tmpFieldCnt = this.arenaMailData.copyChangeToDb(builder.getArenaMailDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArenaMailData();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDRECORDS) && this.rewardRecords != null) {
            final boolean needClear = !builder.hasRewardRecords();
            final int tmpFieldCnt = this.rewardRecords.copyChangeToDb(builder.getRewardRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardRecords();
            }
        }
        if (this.hasMark(FIELD_INDEX_ASSISTDATA) && this.assistData != null) {
            final boolean needClear = !builder.hasAssistData();
            final int tmpFieldCnt = this.assistData.copyChangeToDb(builder.getAssistDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAssistData();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUYGOODSDATA) && this.buyGoodsData != null) {
            final boolean needClear = !builder.hasBuyGoodsData();
            final int tmpFieldCnt = this.buyGoodsData.copyChangeToDb(builder.getBuyGoodsDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuyGoodsData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANBUILDINGDATA) && this.clanBuildingData != null) {
            final boolean needClear = !builder.hasClanBuildingData();
            final int tmpFieldCnt = this.clanBuildingData.copyChangeToDb(builder.getClanBuildingDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanBuildingData();
            }
        }
        if (this.hasMark(FIELD_INDEX_KINGBUFFDATA) && this.kingBuffData != null) {
            final boolean needClear = !builder.hasKingBuffData();
            final int tmpFieldCnt = this.kingBuffData.copyChangeToDb(builder.getKingBuffDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingBuffData();
            }
        }
        if (this.hasMark(FIELD_INDEX_KINGGAINTAXDATA) && this.kingGainTaxData != null) {
            final boolean needClear = !builder.hasKingGainTaxData();
            final int tmpFieldCnt = this.kingGainTaxData.copyChangeToDb(builder.getKingGainTaxDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingGainTaxData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CHARGEBASEDATA) && this.chargeBaseData != null) {
            final boolean needClear = !builder.hasChargeBaseData();
            final int tmpFieldCnt = this.chargeBaseData.copyChangeToDb(builder.getChargeBaseDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChargeBaseData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANINVITEDATA) && this.clanInviteData != null) {
            final boolean needClear = !builder.hasClanInviteData();
            final int tmpFieldCnt = this.clanInviteData.copyChangeToDb(builder.getClanInviteDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanInviteData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MailContent proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasContentType()) {
            this.innerSetContentType(proto.getContentType());
        } else {
            this.innerSetContentType(MailContentType.forNumber(0));
        }
        if (proto.hasBattleRecord()) {
            this.getBattleRecord().mergeFromDb(proto.getBattleRecord());
        } else {
            if (this.battleRecord != null) {
                this.battleRecord.mergeFromDb(proto.getBattleRecord());
            }
        }
        if (proto.hasDisplayData()) {
            this.getDisplayData().mergeFromDb(proto.getDisplayData());
        } else {
            if (this.displayData != null) {
                this.displayData.mergeFromDb(proto.getDisplayData());
            }
        }
        if (proto.hasCollectData()) {
            this.getCollectData().mergeFromDb(proto.getCollectData());
        } else {
            if (this.collectData != null) {
                this.collectData.mergeFromDb(proto.getCollectData());
            }
        }
        if (proto.hasExploreData()) {
            this.getExploreData().mergeFromDb(proto.getExploreData());
        } else {
            if (this.exploreData != null) {
                this.exploreData.mergeFromDb(proto.getExploreData());
            }
        }
        if (proto.hasSpyData()) {
            this.getSpyData().mergeFromDb(proto.getSpyData());
        } else {
            if (this.spyData != null) {
                this.spyData.mergeFromDb(proto.getSpyData());
            }
        }
        if (proto.hasText()) {
            this.innerSetText(proto.getText());
        } else {
            this.innerSetText(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanTechResearchFinishData()) {
            this.getClanTechResearchFinishData().mergeFromDb(proto.getClanTechResearchFinishData());
        } else {
            if (this.clanTechResearchFinishData != null) {
                this.clanTechResearchFinishData.mergeFromDb(proto.getClanTechResearchFinishData());
            }
        }
        if (proto.hasClanContributionRankingData()) {
            this.getClanContributionRankingData().mergeFromDb(proto.getClanContributionRankingData());
        } else {
            if (this.clanContributionRankingData != null) {
                this.clanContributionRankingData.mergeFromDb(proto.getClanContributionRankingData());
            }
        }
        if (proto.hasActivityRewardData()) {
            this.getActivityRewardData().mergeFromDb(proto.getActivityRewardData());
        } else {
            if (this.ActivityRewardData != null) {
                this.ActivityRewardData.mergeFromDb(proto.getActivityRewardData());
            }
        }
        if (proto.hasClanApplyData()) {
            this.getClanApplyData().mergeFromDb(proto.getClanApplyData());
        } else {
            if (this.clanApplyData != null) {
                this.clanApplyData.mergeFromDb(proto.getClanApplyData());
            }
        }
        if (proto.hasArenaMailData()) {
            this.getArenaMailData().mergeFromDb(proto.getArenaMailData());
        } else {
            if (this.arenaMailData != null) {
                this.arenaMailData.mergeFromDb(proto.getArenaMailData());
            }
        }
        if (proto.hasRewardRecords()) {
            this.getRewardRecords().mergeFromDb(proto.getRewardRecords());
        } else {
            if (this.rewardRecords != null) {
                this.rewardRecords.mergeFromDb(proto.getRewardRecords());
            }
        }
        if (proto.hasAssistData()) {
            this.getAssistData().mergeFromDb(proto.getAssistData());
        } else {
            if (this.assistData != null) {
                this.assistData.mergeFromDb(proto.getAssistData());
            }
        }
        if (proto.hasBuyGoodsData()) {
            this.getBuyGoodsData().mergeFromDb(proto.getBuyGoodsData());
        } else {
            if (this.buyGoodsData != null) {
                this.buyGoodsData.mergeFromDb(proto.getBuyGoodsData());
            }
        }
        if (proto.hasClanBuildingData()) {
            this.getClanBuildingData().mergeFromDb(proto.getClanBuildingData());
        } else {
            if (this.clanBuildingData != null) {
                this.clanBuildingData.mergeFromDb(proto.getClanBuildingData());
            }
        }
        if (proto.hasKingBuffData()) {
            this.getKingBuffData().mergeFromDb(proto.getKingBuffData());
        } else {
            if (this.kingBuffData != null) {
                this.kingBuffData.mergeFromDb(proto.getKingBuffData());
            }
        }
        if (proto.hasKingGainTaxData()) {
            this.getKingGainTaxData().mergeFromDb(proto.getKingGainTaxData());
        } else {
            if (this.kingGainTaxData != null) {
                this.kingGainTaxData.mergeFromDb(proto.getKingGainTaxData());
            }
        }
        if (proto.hasChargeBaseData()) {
            this.getChargeBaseData().mergeFromDb(proto.getChargeBaseData());
        } else {
            if (this.chargeBaseData != null) {
                this.chargeBaseData.mergeFromDb(proto.getChargeBaseData());
            }
        }
        if (proto.hasClanInviteData()) {
            this.getClanInviteData().mergeFromDb(proto.getClanInviteData());
        } else {
            if (this.clanInviteData != null) {
                this.clanInviteData.mergeFromDb(proto.getClanInviteData());
            }
        }
        this.markAll();
        return MailContentProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MailContent proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasContentType()) {
            this.setContentType(proto.getContentType());
            fieldCnt++;
        }
        if (proto.hasBattleRecord()) {
            this.getBattleRecord().mergeChangeFromDb(proto.getBattleRecord());
            fieldCnt++;
        }
        if (proto.hasDisplayData()) {
            this.getDisplayData().mergeChangeFromDb(proto.getDisplayData());
            fieldCnt++;
        }
        if (proto.hasCollectData()) {
            this.getCollectData().mergeChangeFromDb(proto.getCollectData());
            fieldCnt++;
        }
        if (proto.hasExploreData()) {
            this.getExploreData().mergeChangeFromDb(proto.getExploreData());
            fieldCnt++;
        }
        if (proto.hasSpyData()) {
            this.getSpyData().mergeChangeFromDb(proto.getSpyData());
            fieldCnt++;
        }
        if (proto.hasText()) {
            this.setText(proto.getText());
            fieldCnt++;
        }
        if (proto.hasClanTechResearchFinishData()) {
            this.getClanTechResearchFinishData().mergeChangeFromDb(proto.getClanTechResearchFinishData());
            fieldCnt++;
        }
        if (proto.hasClanContributionRankingData()) {
            this.getClanContributionRankingData().mergeChangeFromDb(proto.getClanContributionRankingData());
            fieldCnt++;
        }
        if (proto.hasActivityRewardData()) {
            this.getActivityRewardData().mergeChangeFromDb(proto.getActivityRewardData());
            fieldCnt++;
        }
        if (proto.hasClanApplyData()) {
            this.getClanApplyData().mergeChangeFromDb(proto.getClanApplyData());
            fieldCnt++;
        }
        if (proto.hasArenaMailData()) {
            this.getArenaMailData().mergeChangeFromDb(proto.getArenaMailData());
            fieldCnt++;
        }
        if (proto.hasRewardRecords()) {
            this.getRewardRecords().mergeChangeFromDb(proto.getRewardRecords());
            fieldCnt++;
        }
        if (proto.hasAssistData()) {
            this.getAssistData().mergeChangeFromDb(proto.getAssistData());
            fieldCnt++;
        }
        if (proto.hasBuyGoodsData()) {
            this.getBuyGoodsData().mergeChangeFromDb(proto.getBuyGoodsData());
            fieldCnt++;
        }
        if (proto.hasClanBuildingData()) {
            this.getClanBuildingData().mergeChangeFromDb(proto.getClanBuildingData());
            fieldCnt++;
        }
        if (proto.hasKingBuffData()) {
            this.getKingBuffData().mergeChangeFromDb(proto.getKingBuffData());
            fieldCnt++;
        }
        if (proto.hasKingGainTaxData()) {
            this.getKingGainTaxData().mergeChangeFromDb(proto.getKingGainTaxData());
            fieldCnt++;
        }
        if (proto.hasChargeBaseData()) {
            this.getChargeBaseData().mergeChangeFromDb(proto.getChargeBaseData());
            fieldCnt++;
        }
        if (proto.hasClanInviteData()) {
            this.getClanInviteData().mergeChangeFromDb(proto.getClanInviteData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailContent.Builder getCopySsBuilder() {
        final MailContent.Builder builder = MailContent.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MailContent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getContentType() != MailContentType.forNumber(0)) {
            builder.setContentType(this.getContentType());
            fieldCnt++;
        }  else if (builder.hasContentType()) {
            // 清理ContentType
            builder.clearContentType();
            fieldCnt++;
        }
        if (this.battleRecord != null) {
            StructBattle.BattleRecordAll.Builder tmpBuilder = StructBattle.BattleRecordAll.newBuilder();
            final int tmpFieldCnt = this.battleRecord.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattleRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattleRecord();
            }
        }  else if (builder.hasBattleRecord()) {
            // 清理BattleRecord
            builder.clearBattleRecord();
            fieldCnt++;
        }
        if (this.displayData != null) {
            Struct.DisplayData.Builder tmpBuilder = Struct.DisplayData.newBuilder();
            final int tmpFieldCnt = this.displayData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDisplayData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDisplayData();
            }
        }  else if (builder.hasDisplayData()) {
            // 清理DisplayData
            builder.clearDisplayData();
            fieldCnt++;
        }
        if (this.collectData != null) {
            Struct.CollectData.Builder tmpBuilder = Struct.CollectData.newBuilder();
            final int tmpFieldCnt = this.collectData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCollectData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCollectData();
            }
        }  else if (builder.hasCollectData()) {
            // 清理CollectData
            builder.clearCollectData();
            fieldCnt++;
        }
        if (this.exploreData != null) {
            Struct.ExploreData.Builder tmpBuilder = Struct.ExploreData.newBuilder();
            final int tmpFieldCnt = this.exploreData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExploreData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExploreData();
            }
        }  else if (builder.hasExploreData()) {
            // 清理ExploreData
            builder.clearExploreData();
            fieldCnt++;
        }
        if (this.spyData != null) {
            Struct.SpyData.Builder tmpBuilder = Struct.SpyData.newBuilder();
            final int tmpFieldCnt = this.spyData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyData();
            }
        }  else if (builder.hasSpyData()) {
            // 清理SpyData
            builder.clearSpyData();
            fieldCnt++;
        }
        if (!this.getText().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setText(this.getText());
            fieldCnt++;
        }  else if (builder.hasText()) {
            // 清理Text
            builder.clearText();
            fieldCnt++;
        }
        if (this.clanTechResearchFinishData != null) {
            StructMail.MailClanTechResearchFinishData.Builder tmpBuilder = StructMail.MailClanTechResearchFinishData.newBuilder();
            final int tmpFieldCnt = this.clanTechResearchFinishData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanTechResearchFinishData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanTechResearchFinishData();
            }
        }  else if (builder.hasClanTechResearchFinishData()) {
            // 清理ClanTechResearchFinishData
            builder.clearClanTechResearchFinishData();
            fieldCnt++;
        }
        if (this.clanContributionRankingData != null) {
            StructMail.MailClanContributionRankingData.Builder tmpBuilder = StructMail.MailClanContributionRankingData.newBuilder();
            final int tmpFieldCnt = this.clanContributionRankingData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanContributionRankingData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanContributionRankingData();
            }
        }  else if (builder.hasClanContributionRankingData()) {
            // 清理ClanContributionRankingData
            builder.clearClanContributionRankingData();
            fieldCnt++;
        }
        if (this.ActivityRewardData != null) {
            Struct.ActivityRewardData.Builder tmpBuilder = Struct.ActivityRewardData.newBuilder();
            final int tmpFieldCnt = this.ActivityRewardData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActivityRewardData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActivityRewardData();
            }
        }  else if (builder.hasActivityRewardData()) {
            // 清理ActivityRewardData
            builder.clearActivityRewardData();
            fieldCnt++;
        }
        if (this.clanApplyData != null) {
            StructClan.ClanApplyData.Builder tmpBuilder = StructClan.ClanApplyData.newBuilder();
            final int tmpFieldCnt = this.clanApplyData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanApplyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanApplyData();
            }
        }  else if (builder.hasClanApplyData()) {
            // 清理ClanApplyData
            builder.clearClanApplyData();
            fieldCnt++;
        }
        if (this.arenaMailData != null) {
            Struct.ArenaMailData.Builder tmpBuilder = Struct.ArenaMailData.newBuilder();
            final int tmpFieldCnt = this.arenaMailData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArenaMailData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArenaMailData();
            }
        }  else if (builder.hasArenaMailData()) {
            // 清理ArenaMailData
            builder.clearArenaMailData();
            fieldCnt++;
        }
        if (this.rewardRecords != null) {
            Struct.RewardRecordList.Builder tmpBuilder = Struct.RewardRecordList.newBuilder();
            final int tmpFieldCnt = this.rewardRecords.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardRecords();
            }
        }  else if (builder.hasRewardRecords()) {
            // 清理RewardRecords
            builder.clearRewardRecords();
            fieldCnt++;
        }
        if (this.assistData != null) {
            StructMail.ResourceAssistData.Builder tmpBuilder = StructMail.ResourceAssistData.newBuilder();
            final int tmpFieldCnt = this.assistData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAssistData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAssistData();
            }
        }  else if (builder.hasAssistData()) {
            // 清理AssistData
            builder.clearAssistData();
            fieldCnt++;
        }
        if (this.buyGoodsData != null) {
            StructMail.MailBuyGoodsData.Builder tmpBuilder = StructMail.MailBuyGoodsData.newBuilder();
            final int tmpFieldCnt = this.buyGoodsData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuyGoodsData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuyGoodsData();
            }
        }  else if (builder.hasBuyGoodsData()) {
            // 清理BuyGoodsData
            builder.clearBuyGoodsData();
            fieldCnt++;
        }
        if (this.clanBuildingData != null) {
            StructMail.MailClanBuildingData.Builder tmpBuilder = StructMail.MailClanBuildingData.newBuilder();
            final int tmpFieldCnt = this.clanBuildingData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanBuildingData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanBuildingData();
            }
        }  else if (builder.hasClanBuildingData()) {
            // 清理ClanBuildingData
            builder.clearClanBuildingData();
            fieldCnt++;
        }
        if (this.kingBuffData != null) {
            StructMail.MailKingBuffOrSkillData.Builder tmpBuilder = StructMail.MailKingBuffOrSkillData.newBuilder();
            final int tmpFieldCnt = this.kingBuffData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKingBuffData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKingBuffData();
            }
        }  else if (builder.hasKingBuffData()) {
            // 清理KingBuffData
            builder.clearKingBuffData();
            fieldCnt++;
        }
        if (this.kingGainTaxData != null) {
            StructMail.MailKingGainTaxData.Builder tmpBuilder = StructMail.MailKingGainTaxData.newBuilder();
            final int tmpFieldCnt = this.kingGainTaxData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKingGainTaxData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKingGainTaxData();
            }
        }  else if (builder.hasKingGainTaxData()) {
            // 清理KingGainTaxData
            builder.clearKingGainTaxData();
            fieldCnt++;
        }
        if (this.chargeBaseData != null) {
            StructMail.MailChargeBaseData.Builder tmpBuilder = StructMail.MailChargeBaseData.newBuilder();
            final int tmpFieldCnt = this.chargeBaseData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChargeBaseData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChargeBaseData();
            }
        }  else if (builder.hasChargeBaseData()) {
            // 清理ChargeBaseData
            builder.clearChargeBaseData();
            fieldCnt++;
        }
        if (this.clanInviteData != null) {
            StructMail.MailClanInviteData.Builder tmpBuilder = StructMail.MailClanInviteData.newBuilder();
            final int tmpFieldCnt = this.clanInviteData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanInviteData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanInviteData();
            }
        }  else if (builder.hasClanInviteData()) {
            // 清理ClanInviteData
            builder.clearClanInviteData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MailContent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONTENTTYPE)) {
            builder.setContentType(this.getContentType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLERECORD) && this.battleRecord != null) {
            final boolean needClear = !builder.hasBattleRecord();
            final int tmpFieldCnt = this.battleRecord.copyChangeToSs(builder.getBattleRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattleRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_DISPLAYDATA) && this.displayData != null) {
            final boolean needClear = !builder.hasDisplayData();
            final int tmpFieldCnt = this.displayData.copyChangeToSs(builder.getDisplayDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDisplayData();
            }
        }
        if (this.hasMark(FIELD_INDEX_COLLECTDATA) && this.collectData != null) {
            final boolean needClear = !builder.hasCollectData();
            final int tmpFieldCnt = this.collectData.copyChangeToSs(builder.getCollectDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCollectData();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXPLOREDATA) && this.exploreData != null) {
            final boolean needClear = !builder.hasExploreData();
            final int tmpFieldCnt = this.exploreData.copyChangeToSs(builder.getExploreDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExploreData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYDATA) && this.spyData != null) {
            final boolean needClear = !builder.hasSpyData();
            final int tmpFieldCnt = this.spyData.copyChangeToSs(builder.getSpyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TEXT)) {
            builder.setText(this.getText());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANTECHRESEARCHFINISHDATA) && this.clanTechResearchFinishData != null) {
            final boolean needClear = !builder.hasClanTechResearchFinishData();
            final int tmpFieldCnt = this.clanTechResearchFinishData.copyChangeToSs(builder.getClanTechResearchFinishDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanTechResearchFinishData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANCONTRIBUTIONRANKINGDATA) && this.clanContributionRankingData != null) {
            final boolean needClear = !builder.hasClanContributionRankingData();
            final int tmpFieldCnt = this.clanContributionRankingData.copyChangeToSs(builder.getClanContributionRankingDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanContributionRankingData();
            }
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITYREWARDDATA) && this.ActivityRewardData != null) {
            final boolean needClear = !builder.hasActivityRewardData();
            final int tmpFieldCnt = this.ActivityRewardData.copyChangeToSs(builder.getActivityRewardDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivityRewardData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANAPPLYDATA) && this.clanApplyData != null) {
            final boolean needClear = !builder.hasClanApplyData();
            final int tmpFieldCnt = this.clanApplyData.copyChangeToSs(builder.getClanApplyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanApplyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARENAMAILDATA) && this.arenaMailData != null) {
            final boolean needClear = !builder.hasArenaMailData();
            final int tmpFieldCnt = this.arenaMailData.copyChangeToSs(builder.getArenaMailDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArenaMailData();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDRECORDS) && this.rewardRecords != null) {
            final boolean needClear = !builder.hasRewardRecords();
            final int tmpFieldCnt = this.rewardRecords.copyChangeToSs(builder.getRewardRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardRecords();
            }
        }
        if (this.hasMark(FIELD_INDEX_ASSISTDATA) && this.assistData != null) {
            final boolean needClear = !builder.hasAssistData();
            final int tmpFieldCnt = this.assistData.copyChangeToSs(builder.getAssistDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAssistData();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUYGOODSDATA) && this.buyGoodsData != null) {
            final boolean needClear = !builder.hasBuyGoodsData();
            final int tmpFieldCnt = this.buyGoodsData.copyChangeToSs(builder.getBuyGoodsDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuyGoodsData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANBUILDINGDATA) && this.clanBuildingData != null) {
            final boolean needClear = !builder.hasClanBuildingData();
            final int tmpFieldCnt = this.clanBuildingData.copyChangeToSs(builder.getClanBuildingDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanBuildingData();
            }
        }
        if (this.hasMark(FIELD_INDEX_KINGBUFFDATA) && this.kingBuffData != null) {
            final boolean needClear = !builder.hasKingBuffData();
            final int tmpFieldCnt = this.kingBuffData.copyChangeToSs(builder.getKingBuffDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingBuffData();
            }
        }
        if (this.hasMark(FIELD_INDEX_KINGGAINTAXDATA) && this.kingGainTaxData != null) {
            final boolean needClear = !builder.hasKingGainTaxData();
            final int tmpFieldCnt = this.kingGainTaxData.copyChangeToSs(builder.getKingGainTaxDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingGainTaxData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CHARGEBASEDATA) && this.chargeBaseData != null) {
            final boolean needClear = !builder.hasChargeBaseData();
            final int tmpFieldCnt = this.chargeBaseData.copyChangeToSs(builder.getChargeBaseDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChargeBaseData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANINVITEDATA) && this.clanInviteData != null) {
            final boolean needClear = !builder.hasClanInviteData();
            final int tmpFieldCnt = this.clanInviteData.copyChangeToSs(builder.getClanInviteDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanInviteData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MailContent proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasContentType()) {
            this.innerSetContentType(proto.getContentType());
        } else {
            this.innerSetContentType(MailContentType.forNumber(0));
        }
        if (proto.hasBattleRecord()) {
            this.getBattleRecord().mergeFromSs(proto.getBattleRecord());
        } else {
            if (this.battleRecord != null) {
                this.battleRecord.mergeFromSs(proto.getBattleRecord());
            }
        }
        if (proto.hasDisplayData()) {
            this.getDisplayData().mergeFromSs(proto.getDisplayData());
        } else {
            if (this.displayData != null) {
                this.displayData.mergeFromSs(proto.getDisplayData());
            }
        }
        if (proto.hasCollectData()) {
            this.getCollectData().mergeFromSs(proto.getCollectData());
        } else {
            if (this.collectData != null) {
                this.collectData.mergeFromSs(proto.getCollectData());
            }
        }
        if (proto.hasExploreData()) {
            this.getExploreData().mergeFromSs(proto.getExploreData());
        } else {
            if (this.exploreData != null) {
                this.exploreData.mergeFromSs(proto.getExploreData());
            }
        }
        if (proto.hasSpyData()) {
            this.getSpyData().mergeFromSs(proto.getSpyData());
        } else {
            if (this.spyData != null) {
                this.spyData.mergeFromSs(proto.getSpyData());
            }
        }
        if (proto.hasText()) {
            this.innerSetText(proto.getText());
        } else {
            this.innerSetText(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanTechResearchFinishData()) {
            this.getClanTechResearchFinishData().mergeFromSs(proto.getClanTechResearchFinishData());
        } else {
            if (this.clanTechResearchFinishData != null) {
                this.clanTechResearchFinishData.mergeFromSs(proto.getClanTechResearchFinishData());
            }
        }
        if (proto.hasClanContributionRankingData()) {
            this.getClanContributionRankingData().mergeFromSs(proto.getClanContributionRankingData());
        } else {
            if (this.clanContributionRankingData != null) {
                this.clanContributionRankingData.mergeFromSs(proto.getClanContributionRankingData());
            }
        }
        if (proto.hasActivityRewardData()) {
            this.getActivityRewardData().mergeFromSs(proto.getActivityRewardData());
        } else {
            if (this.ActivityRewardData != null) {
                this.ActivityRewardData.mergeFromSs(proto.getActivityRewardData());
            }
        }
        if (proto.hasClanApplyData()) {
            this.getClanApplyData().mergeFromSs(proto.getClanApplyData());
        } else {
            if (this.clanApplyData != null) {
                this.clanApplyData.mergeFromSs(proto.getClanApplyData());
            }
        }
        if (proto.hasArenaMailData()) {
            this.getArenaMailData().mergeFromSs(proto.getArenaMailData());
        } else {
            if (this.arenaMailData != null) {
                this.arenaMailData.mergeFromSs(proto.getArenaMailData());
            }
        }
        if (proto.hasRewardRecords()) {
            this.getRewardRecords().mergeFromSs(proto.getRewardRecords());
        } else {
            if (this.rewardRecords != null) {
                this.rewardRecords.mergeFromSs(proto.getRewardRecords());
            }
        }
        if (proto.hasAssistData()) {
            this.getAssistData().mergeFromSs(proto.getAssistData());
        } else {
            if (this.assistData != null) {
                this.assistData.mergeFromSs(proto.getAssistData());
            }
        }
        if (proto.hasBuyGoodsData()) {
            this.getBuyGoodsData().mergeFromSs(proto.getBuyGoodsData());
        } else {
            if (this.buyGoodsData != null) {
                this.buyGoodsData.mergeFromSs(proto.getBuyGoodsData());
            }
        }
        if (proto.hasClanBuildingData()) {
            this.getClanBuildingData().mergeFromSs(proto.getClanBuildingData());
        } else {
            if (this.clanBuildingData != null) {
                this.clanBuildingData.mergeFromSs(proto.getClanBuildingData());
            }
        }
        if (proto.hasKingBuffData()) {
            this.getKingBuffData().mergeFromSs(proto.getKingBuffData());
        } else {
            if (this.kingBuffData != null) {
                this.kingBuffData.mergeFromSs(proto.getKingBuffData());
            }
        }
        if (proto.hasKingGainTaxData()) {
            this.getKingGainTaxData().mergeFromSs(proto.getKingGainTaxData());
        } else {
            if (this.kingGainTaxData != null) {
                this.kingGainTaxData.mergeFromSs(proto.getKingGainTaxData());
            }
        }
        if (proto.hasChargeBaseData()) {
            this.getChargeBaseData().mergeFromSs(proto.getChargeBaseData());
        } else {
            if (this.chargeBaseData != null) {
                this.chargeBaseData.mergeFromSs(proto.getChargeBaseData());
            }
        }
        if (proto.hasClanInviteData()) {
            this.getClanInviteData().mergeFromSs(proto.getClanInviteData());
        } else {
            if (this.clanInviteData != null) {
                this.clanInviteData.mergeFromSs(proto.getClanInviteData());
            }
        }
        this.markAll();
        return MailContentProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MailContent proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasContentType()) {
            this.setContentType(proto.getContentType());
            fieldCnt++;
        }
        if (proto.hasBattleRecord()) {
            this.getBattleRecord().mergeChangeFromSs(proto.getBattleRecord());
            fieldCnt++;
        }
        if (proto.hasDisplayData()) {
            this.getDisplayData().mergeChangeFromSs(proto.getDisplayData());
            fieldCnt++;
        }
        if (proto.hasCollectData()) {
            this.getCollectData().mergeChangeFromSs(proto.getCollectData());
            fieldCnt++;
        }
        if (proto.hasExploreData()) {
            this.getExploreData().mergeChangeFromSs(proto.getExploreData());
            fieldCnt++;
        }
        if (proto.hasSpyData()) {
            this.getSpyData().mergeChangeFromSs(proto.getSpyData());
            fieldCnt++;
        }
        if (proto.hasText()) {
            this.setText(proto.getText());
            fieldCnt++;
        }
        if (proto.hasClanTechResearchFinishData()) {
            this.getClanTechResearchFinishData().mergeChangeFromSs(proto.getClanTechResearchFinishData());
            fieldCnt++;
        }
        if (proto.hasClanContributionRankingData()) {
            this.getClanContributionRankingData().mergeChangeFromSs(proto.getClanContributionRankingData());
            fieldCnt++;
        }
        if (proto.hasActivityRewardData()) {
            this.getActivityRewardData().mergeChangeFromSs(proto.getActivityRewardData());
            fieldCnt++;
        }
        if (proto.hasClanApplyData()) {
            this.getClanApplyData().mergeChangeFromSs(proto.getClanApplyData());
            fieldCnt++;
        }
        if (proto.hasArenaMailData()) {
            this.getArenaMailData().mergeChangeFromSs(proto.getArenaMailData());
            fieldCnt++;
        }
        if (proto.hasRewardRecords()) {
            this.getRewardRecords().mergeChangeFromSs(proto.getRewardRecords());
            fieldCnt++;
        }
        if (proto.hasAssistData()) {
            this.getAssistData().mergeChangeFromSs(proto.getAssistData());
            fieldCnt++;
        }
        if (proto.hasBuyGoodsData()) {
            this.getBuyGoodsData().mergeChangeFromSs(proto.getBuyGoodsData());
            fieldCnt++;
        }
        if (proto.hasClanBuildingData()) {
            this.getClanBuildingData().mergeChangeFromSs(proto.getClanBuildingData());
            fieldCnt++;
        }
        if (proto.hasKingBuffData()) {
            this.getKingBuffData().mergeChangeFromSs(proto.getKingBuffData());
            fieldCnt++;
        }
        if (proto.hasKingGainTaxData()) {
            this.getKingGainTaxData().mergeChangeFromSs(proto.getKingGainTaxData());
            fieldCnt++;
        }
        if (proto.hasChargeBaseData()) {
            this.getChargeBaseData().mergeChangeFromSs(proto.getChargeBaseData());
            fieldCnt++;
        }
        if (proto.hasClanInviteData()) {
            this.getClanInviteData().mergeChangeFromSs(proto.getClanInviteData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MailContent.Builder builder = MailContent.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_BATTLERECORD) && this.battleRecord != null) {
            this.battleRecord.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DISPLAYDATA) && this.displayData != null) {
            this.displayData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_COLLECTDATA) && this.collectData != null) {
            this.collectData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_EXPLOREDATA) && this.exploreData != null) {
            this.exploreData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SPYDATA) && this.spyData != null) {
            this.spyData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CLANTECHRESEARCHFINISHDATA) && this.clanTechResearchFinishData != null) {
            this.clanTechResearchFinishData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CLANCONTRIBUTIONRANKINGDATA) && this.clanContributionRankingData != null) {
            this.clanContributionRankingData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITYREWARDDATA) && this.ActivityRewardData != null) {
            this.ActivityRewardData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CLANAPPLYDATA) && this.clanApplyData != null) {
            this.clanApplyData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ARENAMAILDATA) && this.arenaMailData != null) {
            this.arenaMailData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_REWARDRECORDS) && this.rewardRecords != null) {
            this.rewardRecords.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ASSISTDATA) && this.assistData != null) {
            this.assistData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BUYGOODSDATA) && this.buyGoodsData != null) {
            this.buyGoodsData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CLANBUILDINGDATA) && this.clanBuildingData != null) {
            this.clanBuildingData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_KINGBUFFDATA) && this.kingBuffData != null) {
            this.kingBuffData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_KINGGAINTAXDATA) && this.kingGainTaxData != null) {
            this.kingGainTaxData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CHARGEBASEDATA) && this.chargeBaseData != null) {
            this.chargeBaseData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CLANINVITEDATA) && this.clanInviteData != null) {
            this.clanInviteData.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.battleRecord != null) {
            this.battleRecord.markAll();
        }
        if (this.displayData != null) {
            this.displayData.markAll();
        }
        if (this.collectData != null) {
            this.collectData.markAll();
        }
        if (this.exploreData != null) {
            this.exploreData.markAll();
        }
        if (this.spyData != null) {
            this.spyData.markAll();
        }
        if (this.clanTechResearchFinishData != null) {
            this.clanTechResearchFinishData.markAll();
        }
        if (this.clanContributionRankingData != null) {
            this.clanContributionRankingData.markAll();
        }
        if (this.ActivityRewardData != null) {
            this.ActivityRewardData.markAll();
        }
        if (this.clanApplyData != null) {
            this.clanApplyData.markAll();
        }
        if (this.arenaMailData != null) {
            this.arenaMailData.markAll();
        }
        if (this.rewardRecords != null) {
            this.rewardRecords.markAll();
        }
        if (this.assistData != null) {
            this.assistData.markAll();
        }
        if (this.buyGoodsData != null) {
            this.buyGoodsData.markAll();
        }
        if (this.clanBuildingData != null) {
            this.clanBuildingData.markAll();
        }
        if (this.kingBuffData != null) {
            this.kingBuffData.markAll();
        }
        if (this.kingGainTaxData != null) {
            this.kingGainTaxData.markAll();
        }
        if (this.chargeBaseData != null) {
            this.chargeBaseData.markAll();
        }
        if (this.clanInviteData != null) {
            this.clanInviteData.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MailContentProp)) {
            return false;
        }
        final MailContentProp otherNode = (MailContentProp) node;
        if (this.contentType != otherNode.contentType) {
            return false;
        }
        if (!this.getBattleRecord().compareDataTo(otherNode.getBattleRecord())) {
            return false;
        }
        if (!this.getDisplayData().compareDataTo(otherNode.getDisplayData())) {
            return false;
        }
        if (!this.getCollectData().compareDataTo(otherNode.getCollectData())) {
            return false;
        }
        if (!this.getExploreData().compareDataTo(otherNode.getExploreData())) {
            return false;
        }
        if (!this.getSpyData().compareDataTo(otherNode.getSpyData())) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.text, otherNode.text)) {
            return false;
        }
        if (!this.getClanTechResearchFinishData().compareDataTo(otherNode.getClanTechResearchFinishData())) {
            return false;
        }
        if (!this.getClanContributionRankingData().compareDataTo(otherNode.getClanContributionRankingData())) {
            return false;
        }
        if (!this.getActivityRewardData().compareDataTo(otherNode.getActivityRewardData())) {
            return false;
        }
        if (!this.getClanApplyData().compareDataTo(otherNode.getClanApplyData())) {
            return false;
        }
        if (!this.getArenaMailData().compareDataTo(otherNode.getArenaMailData())) {
            return false;
        }
        if (!this.getRewardRecords().compareDataTo(otherNode.getRewardRecords())) {
            return false;
        }
        if (!this.getAssistData().compareDataTo(otherNode.getAssistData())) {
            return false;
        }
        if (!this.getBuyGoodsData().compareDataTo(otherNode.getBuyGoodsData())) {
            return false;
        }
        if (!this.getClanBuildingData().compareDataTo(otherNode.getClanBuildingData())) {
            return false;
        }
        if (!this.getKingBuffData().compareDataTo(otherNode.getKingBuffData())) {
            return false;
        }
        if (!this.getKingGainTaxData().compareDataTo(otherNode.getKingGainTaxData())) {
            return false;
        }
        if (!this.getChargeBaseData().compareDataTo(otherNode.getChargeBaseData())) {
            return false;
        }
        if (!this.getClanInviteData().compareDataTo(otherNode.getClanInviteData())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 44;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
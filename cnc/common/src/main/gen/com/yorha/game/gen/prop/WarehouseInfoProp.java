package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Clan.WarehouseInfo;
import com.yorha.proto.ClanPB.WarehouseInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class WarehouseInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_TYPE = 0;
    public static final int FIELD_INDEX_MAXCOUNT = 1;
    public static final int FIELD_INDEX_BASEPRODUCEPERHOUR = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int type = Constant.DEFAULT_INT_VALUE;
    private long maxCount = Constant.DEFAULT_LONG_VALUE;
    private long baseProducePerHour = Constant.DEFAULT_LONG_VALUE;

    public WarehouseInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public WarehouseInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get type
     *
     * @return type value
     */
    public int getType() {
        return this.type;
    }

    /**
     * set type && set marked
     *
     * @param type new value
     * @return current object
     */
    public WarehouseInfoProp setType(int type) {
        if (this.type != type) {
            this.mark(FIELD_INDEX_TYPE);
            this.type = type;
        }
        return this;
    }

    /**
     * inner set type
     *
     * @param type new value
     */
    private void innerSetType(int type) {
        this.type = type;
    }

    /**
     * get maxCount
     *
     * @return maxCount value
     */
    public long getMaxCount() {
        return this.maxCount;
    }

    /**
     * set maxCount && set marked
     *
     * @param maxCount new value
     * @return current object
     */
    public WarehouseInfoProp setMaxCount(long maxCount) {
        if (this.maxCount != maxCount) {
            this.mark(FIELD_INDEX_MAXCOUNT);
            this.maxCount = maxCount;
        }
        return this;
    }

    /**
     * inner set maxCount
     *
     * @param maxCount new value
     */
    private void innerSetMaxCount(long maxCount) {
        this.maxCount = maxCount;
    }

    /**
     * get baseProducePerHour
     *
     * @return baseProducePerHour value
     */
    public long getBaseProducePerHour() {
        return this.baseProducePerHour;
    }

    /**
     * set baseProducePerHour && set marked
     *
     * @param baseProducePerHour new value
     * @return current object
     */
    public WarehouseInfoProp setBaseProducePerHour(long baseProducePerHour) {
        if (this.baseProducePerHour != baseProducePerHour) {
            this.mark(FIELD_INDEX_BASEPRODUCEPERHOUR);
            this.baseProducePerHour = baseProducePerHour;
        }
        return this;
    }

    /**
     * inner set baseProducePerHour
     *
     * @param baseProducePerHour new value
     */
    private void innerSetBaseProducePerHour(long baseProducePerHour) {
        this.baseProducePerHour = baseProducePerHour;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public WarehouseInfoPB.Builder getCopyCsBuilder() {
        final WarehouseInfoPB.Builder builder = WarehouseInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(WarehouseInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != 0) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getMaxCount() != 0L) {
            builder.setMaxCount(this.getMaxCount());
            fieldCnt++;
        }  else if (builder.hasMaxCount()) {
            // 清理MaxCount
            builder.clearMaxCount();
            fieldCnt++;
        }
        if (this.getBaseProducePerHour() != 0L) {
            builder.setBaseProducePerHour(this.getBaseProducePerHour());
            fieldCnt++;
        }  else if (builder.hasBaseProducePerHour()) {
            // 清理BaseProducePerHour
            builder.clearBaseProducePerHour();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(WarehouseInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXCOUNT)) {
            builder.setMaxCount(this.getMaxCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASEPRODUCEPERHOUR)) {
            builder.setBaseProducePerHour(this.getBaseProducePerHour());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(WarehouseInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXCOUNT)) {
            builder.setMaxCount(this.getMaxCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASEPRODUCEPERHOUR)) {
            builder.setBaseProducePerHour(this.getBaseProducePerHour());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(WarehouseInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMaxCount()) {
            this.innerSetMaxCount(proto.getMaxCount());
        } else {
            this.innerSetMaxCount(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBaseProducePerHour()) {
            this.innerSetBaseProducePerHour(proto.getBaseProducePerHour());
        } else {
            this.innerSetBaseProducePerHour(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return WarehouseInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(WarehouseInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasMaxCount()) {
            this.setMaxCount(proto.getMaxCount());
            fieldCnt++;
        }
        if (proto.hasBaseProducePerHour()) {
            this.setBaseProducePerHour(proto.getBaseProducePerHour());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public WarehouseInfo.Builder getCopyDbBuilder() {
        final WarehouseInfo.Builder builder = WarehouseInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(WarehouseInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != 0) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getMaxCount() != 0L) {
            builder.setMaxCount(this.getMaxCount());
            fieldCnt++;
        }  else if (builder.hasMaxCount()) {
            // 清理MaxCount
            builder.clearMaxCount();
            fieldCnt++;
        }
        if (this.getBaseProducePerHour() != 0L) {
            builder.setBaseProducePerHour(this.getBaseProducePerHour());
            fieldCnt++;
        }  else if (builder.hasBaseProducePerHour()) {
            // 清理BaseProducePerHour
            builder.clearBaseProducePerHour();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(WarehouseInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXCOUNT)) {
            builder.setMaxCount(this.getMaxCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASEPRODUCEPERHOUR)) {
            builder.setBaseProducePerHour(this.getBaseProducePerHour());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(WarehouseInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMaxCount()) {
            this.innerSetMaxCount(proto.getMaxCount());
        } else {
            this.innerSetMaxCount(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBaseProducePerHour()) {
            this.innerSetBaseProducePerHour(proto.getBaseProducePerHour());
        } else {
            this.innerSetBaseProducePerHour(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return WarehouseInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(WarehouseInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasMaxCount()) {
            this.setMaxCount(proto.getMaxCount());
            fieldCnt++;
        }
        if (proto.hasBaseProducePerHour()) {
            this.setBaseProducePerHour(proto.getBaseProducePerHour());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public WarehouseInfo.Builder getCopySsBuilder() {
        final WarehouseInfo.Builder builder = WarehouseInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(WarehouseInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != 0) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getMaxCount() != 0L) {
            builder.setMaxCount(this.getMaxCount());
            fieldCnt++;
        }  else if (builder.hasMaxCount()) {
            // 清理MaxCount
            builder.clearMaxCount();
            fieldCnt++;
        }
        if (this.getBaseProducePerHour() != 0L) {
            builder.setBaseProducePerHour(this.getBaseProducePerHour());
            fieldCnt++;
        }  else if (builder.hasBaseProducePerHour()) {
            // 清理BaseProducePerHour
            builder.clearBaseProducePerHour();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(WarehouseInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXCOUNT)) {
            builder.setMaxCount(this.getMaxCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASEPRODUCEPERHOUR)) {
            builder.setBaseProducePerHour(this.getBaseProducePerHour());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(WarehouseInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMaxCount()) {
            this.innerSetMaxCount(proto.getMaxCount());
        } else {
            this.innerSetMaxCount(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBaseProducePerHour()) {
            this.innerSetBaseProducePerHour(proto.getBaseProducePerHour());
        } else {
            this.innerSetBaseProducePerHour(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return WarehouseInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(WarehouseInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasMaxCount()) {
            this.setMaxCount(proto.getMaxCount());
            fieldCnt++;
        }
        if (proto.hasBaseProducePerHour()) {
            this.setBaseProducePerHour(proto.getBaseProducePerHour());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        WarehouseInfo.Builder builder = WarehouseInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.type;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof WarehouseInfoProp)) {
            return false;
        }
        final WarehouseInfoProp otherNode = (WarehouseInfoProp) node;
        if (this.type != otherNode.type) {
            return false;
        }
        if (this.maxCount != otherNode.maxCount) {
            return false;
        }
        if (this.baseProducePerHour != otherNode.baseProducePerHour) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
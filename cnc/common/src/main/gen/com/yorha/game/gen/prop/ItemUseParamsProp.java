package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Player.ItemUseParams;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.ItemUseParamsPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ItemUseParamsProp extends AbstractPropNode {

    public static final int FIELD_INDEX_POINT = 0;
    public static final int FIELD_INDEX_INDEX = 1;
    public static final int FIELD_INDEX_NEWPLAYERNAME = 2;
    public static final int FIELD_INDEX_HEROID = 3;
    public static final int FIELD_INDEX_TALENTPAGENUM = 4;
    public static final int FIELD_INDEX_TALENTPAGEID = 5;
    public static final int FIELD_INDEX_QUEUETYPE = 6;
    public static final int FIELD_INDEX_TASKID = 7;
    public static final int FIELD_INDEX_TRANSPORTID = 8;
    public static final int FIELD_INDEX_POINTLIST = 9;
    public static final int FIELD_INDEX_USEDIRECTLY = 10;
    public static final int FIELD_INDEX_SPASSWORD = 11;
    public static final int FIELD_INDEX_ISREPLACETERRITORYTYPE = 12;

    public static final int FIELD_COUNT = 13;

    private long markBits0 = 0L;

    private PointProp point = null;
    private int index = Constant.DEFAULT_INT_VALUE;
    private String newPlayerName = Constant.DEFAULT_STR_VALUE;
    private int heroId = Constant.DEFAULT_INT_VALUE;
    private int talentPageNum = Constant.DEFAULT_INT_VALUE;
    private long talentPageId = Constant.DEFAULT_LONG_VALUE;
    private QueueTaskType queueType = QueueTaskType.forNumber(0);
    private long taskId = Constant.DEFAULT_LONG_VALUE;
    private long transportId = Constant.DEFAULT_LONG_VALUE;
    private PointListProp pointList = null;
    private boolean useDirectly = Constant.DEFAULT_BOOLEAN_VALUE;
    private String sPassWord = Constant.DEFAULT_STR_VALUE;
    private boolean isReplaceTerritoryType = Constant.DEFAULT_BOOLEAN_VALUE;

    public ItemUseParamsProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ItemUseParamsProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get point
     *
     * @return point value
     */
    public PointProp getPoint() {
        if (this.point == null) {
            this.point = new PointProp(this, FIELD_INDEX_POINT);
        }
        return this.point;
    }

    /**
     * get index
     *
     * @return index value
     */
    public int getIndex() {
        return this.index;
    }

    /**
     * set index && set marked
     *
     * @param index new value
     * @return current object
     */
    public ItemUseParamsProp setIndex(int index) {
        if (this.index != index) {
            this.mark(FIELD_INDEX_INDEX);
            this.index = index;
        }
        return this;
    }

    /**
     * inner set index
     *
     * @param index new value
     */
    private void innerSetIndex(int index) {
        this.index = index;
    }

    /**
     * get newPlayerName
     *
     * @return newPlayerName value
     */
    public String getNewPlayerName() {
        return this.newPlayerName;
    }

    /**
     * set newPlayerName && set marked
     *
     * @param newPlayerName new value
     * @return current object
     */
    public ItemUseParamsProp setNewPlayerName(String newPlayerName) {
        if (newPlayerName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.newPlayerName, newPlayerName)) {
            this.mark(FIELD_INDEX_NEWPLAYERNAME);
            this.newPlayerName = newPlayerName;
        }
        return this;
    }

    /**
     * inner set newPlayerName
     *
     * @param newPlayerName new value
     */
    private void innerSetNewPlayerName(String newPlayerName) {
        this.newPlayerName = newPlayerName;
    }

    /**
     * get heroId
     *
     * @return heroId value
     */
    public int getHeroId() {
        return this.heroId;
    }

    /**
     * set heroId && set marked
     *
     * @param heroId new value
     * @return current object
     */
    public ItemUseParamsProp setHeroId(int heroId) {
        if (this.heroId != heroId) {
            this.mark(FIELD_INDEX_HEROID);
            this.heroId = heroId;
        }
        return this;
    }

    /**
     * inner set heroId
     *
     * @param heroId new value
     */
    private void innerSetHeroId(int heroId) {
        this.heroId = heroId;
    }

    /**
     * get talentPageNum
     *
     * @return talentPageNum value
     */
    public int getTalentPageNum() {
        return this.talentPageNum;
    }

    /**
     * set talentPageNum && set marked
     *
     * @param talentPageNum new value
     * @return current object
     */
    public ItemUseParamsProp setTalentPageNum(int talentPageNum) {
        if (this.talentPageNum != talentPageNum) {
            this.mark(FIELD_INDEX_TALENTPAGENUM);
            this.talentPageNum = talentPageNum;
        }
        return this;
    }

    /**
     * inner set talentPageNum
     *
     * @param talentPageNum new value
     */
    private void innerSetTalentPageNum(int talentPageNum) {
        this.talentPageNum = talentPageNum;
    }

    /**
     * get talentPageId
     *
     * @return talentPageId value
     */
    public long getTalentPageId() {
        return this.talentPageId;
    }

    /**
     * set talentPageId && set marked
     *
     * @param talentPageId new value
     * @return current object
     */
    public ItemUseParamsProp setTalentPageId(long talentPageId) {
        if (this.talentPageId != talentPageId) {
            this.mark(FIELD_INDEX_TALENTPAGEID);
            this.talentPageId = talentPageId;
        }
        return this;
    }

    /**
     * inner set talentPageId
     *
     * @param talentPageId new value
     */
    private void innerSetTalentPageId(long talentPageId) {
        this.talentPageId = talentPageId;
    }

    /**
     * get queueType
     *
     * @return queueType value
     */
    public QueueTaskType getQueueType() {
        return this.queueType;
    }

    /**
     * set queueType && set marked
     *
     * @param queueType new value
     * @return current object
     */
    public ItemUseParamsProp setQueueType(QueueTaskType queueType) {
        if (queueType == null) {
            throw new NullPointerException();
        }
        if (this.queueType != queueType) {
            this.mark(FIELD_INDEX_QUEUETYPE);
            this.queueType = queueType;
        }
        return this;
    }

    /**
     * inner set queueType
     *
     * @param queueType new value
     */
    private void innerSetQueueType(QueueTaskType queueType) {
        this.queueType = queueType;
    }

    /**
     * get taskId
     *
     * @return taskId value
     */
    public long getTaskId() {
        return this.taskId;
    }

    /**
     * set taskId && set marked
     *
     * @param taskId new value
     * @return current object
     */
    public ItemUseParamsProp setTaskId(long taskId) {
        if (this.taskId != taskId) {
            this.mark(FIELD_INDEX_TASKID);
            this.taskId = taskId;
        }
        return this;
    }

    /**
     * inner set taskId
     *
     * @param taskId new value
     */
    private void innerSetTaskId(long taskId) {
        this.taskId = taskId;
    }

    /**
     * get transportId
     *
     * @return transportId value
     */
    public long getTransportId() {
        return this.transportId;
    }

    /**
     * set transportId && set marked
     *
     * @param transportId new value
     * @return current object
     */
    public ItemUseParamsProp setTransportId(long transportId) {
        if (this.transportId != transportId) {
            this.mark(FIELD_INDEX_TRANSPORTID);
            this.transportId = transportId;
        }
        return this;
    }

    /**
     * inner set transportId
     *
     * @param transportId new value
     */
    private void innerSetTransportId(long transportId) {
        this.transportId = transportId;
    }

    /**
     * get pointList
     *
     * @return pointList value
     */
    public PointListProp getPointList() {
        if (this.pointList == null) {
            this.pointList = new PointListProp(this, FIELD_INDEX_POINTLIST);
        }
        return this.pointList;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addPointList(PointProp v) {
        this.getPointList().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public PointProp getPointListIndex(int index) {
        return this.getPointList().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public PointProp removePointList(PointProp v) {
        if (this.pointList == null) {
            return null;
        }
        if(this.pointList.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getPointListSize() {
        if (this.pointList == null) {
            return 0;
        }
        return this.pointList.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isPointListEmpty() {
        if (this.pointList == null) {
            return true;
        }
        return this.getPointList().isEmpty();
    }

    /**
     * clear list
     */
    public void clearPointList() {
        this.getPointList().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public PointProp removePointListIndex(int index) {
        return this.getPointList().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public PointProp setPointListIndex(int index, PointProp v) {
        return this.getPointList().set(index, v);
    }
    /**
     * get useDirectly
     *
     * @return useDirectly value
     */
    public boolean getUseDirectly() {
        return this.useDirectly;
    }

    /**
     * set useDirectly && set marked
     *
     * @param useDirectly new value
     * @return current object
     */
    public ItemUseParamsProp setUseDirectly(boolean useDirectly) {
        if (this.useDirectly != useDirectly) {
            this.mark(FIELD_INDEX_USEDIRECTLY);
            this.useDirectly = useDirectly;
        }
        return this;
    }

    /**
     * inner set useDirectly
     *
     * @param useDirectly new value
     */
    private void innerSetUseDirectly(boolean useDirectly) {
        this.useDirectly = useDirectly;
    }

    /**
     * get sPassWord
     *
     * @return sPassWord value
     */
    public String getSPassWord() {
        return this.sPassWord;
    }

    /**
     * set sPassWord && set marked
     *
     * @param sPassWord new value
     * @return current object
     */
    public ItemUseParamsProp setSPassWord(String sPassWord) {
        if (sPassWord == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.sPassWord, sPassWord)) {
            this.mark(FIELD_INDEX_SPASSWORD);
            this.sPassWord = sPassWord;
        }
        return this;
    }

    /**
     * inner set sPassWord
     *
     * @param sPassWord new value
     */
    private void innerSetSPassWord(String sPassWord) {
        this.sPassWord = sPassWord;
    }

    /**
     * get isReplaceTerritoryType
     *
     * @return isReplaceTerritoryType value
     */
    public boolean getIsReplaceTerritoryType() {
        return this.isReplaceTerritoryType;
    }

    /**
     * set isReplaceTerritoryType && set marked
     *
     * @param isReplaceTerritoryType new value
     * @return current object
     */
    public ItemUseParamsProp setIsReplaceTerritoryType(boolean isReplaceTerritoryType) {
        if (this.isReplaceTerritoryType != isReplaceTerritoryType) {
            this.mark(FIELD_INDEX_ISREPLACETERRITORYTYPE);
            this.isReplaceTerritoryType = isReplaceTerritoryType;
        }
        return this;
    }

    /**
     * inner set isReplaceTerritoryType
     *
     * @param isReplaceTerritoryType new value
     */
    private void innerSetIsReplaceTerritoryType(boolean isReplaceTerritoryType) {
        this.isReplaceTerritoryType = isReplaceTerritoryType;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ItemUseParamsPB.Builder getCopyCsBuilder() {
        final ItemUseParamsPB.Builder builder = ItemUseParamsPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ItemUseParamsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.point != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.point.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getIndex() != 0) {
            builder.setIndex(this.getIndex());
            fieldCnt++;
        }  else if (builder.hasIndex()) {
            // 清理Index
            builder.clearIndex();
            fieldCnt++;
        }
        if (!this.getNewPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setNewPlayerName(this.getNewPlayerName());
            fieldCnt++;
        }  else if (builder.hasNewPlayerName()) {
            // 清理NewPlayerName
            builder.clearNewPlayerName();
            fieldCnt++;
        }
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.getTalentPageNum() != 0) {
            builder.setTalentPageNum(this.getTalentPageNum());
            fieldCnt++;
        }  else if (builder.hasTalentPageNum()) {
            // 清理TalentPageNum
            builder.clearTalentPageNum();
            fieldCnt++;
        }
        if (this.getTalentPageId() != 0L) {
            builder.setTalentPageId(this.getTalentPageId());
            fieldCnt++;
        }  else if (builder.hasTalentPageId()) {
            // 清理TalentPageId
            builder.clearTalentPageId();
            fieldCnt++;
        }
        if (this.getQueueType() != QueueTaskType.forNumber(0)) {
            builder.setQueueType(this.getQueueType());
            fieldCnt++;
        }  else if (builder.hasQueueType()) {
            // 清理QueueType
            builder.clearQueueType();
            fieldCnt++;
        }
        if (this.getTaskId() != 0L) {
            builder.setTaskId(this.getTaskId());
            fieldCnt++;
        }  else if (builder.hasTaskId()) {
            // 清理TaskId
            builder.clearTaskId();
            fieldCnt++;
        }
        if (this.getTransportId() != 0L) {
            builder.setTransportId(this.getTransportId());
            fieldCnt++;
        }  else if (builder.hasTransportId()) {
            // 清理TransportId
            builder.clearTransportId();
            fieldCnt++;
        }
        if (this.pointList != null) {
            StructPB.PointListPB.Builder tmpBuilder = StructPB.PointListPB.newBuilder();
            final int tmpFieldCnt = this.pointList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPointList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPointList();
            }
        }  else if (builder.hasPointList()) {
            // 清理PointList
            builder.clearPointList();
            fieldCnt++;
        }
        if (this.getUseDirectly()) {
            builder.setUseDirectly(this.getUseDirectly());
            fieldCnt++;
        }  else if (builder.hasUseDirectly()) {
            // 清理UseDirectly
            builder.clearUseDirectly();
            fieldCnt++;
        }
        if (!this.getSPassWord().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSPassWord(this.getSPassWord());
            fieldCnt++;
        }  else if (builder.hasSPassWord()) {
            // 清理SPassWord
            builder.clearSPassWord();
            fieldCnt++;
        }
        if (this.getIsReplaceTerritoryType()) {
            builder.setIsReplaceTerritoryType(this.getIsReplaceTerritoryType());
            fieldCnt++;
        }  else if (builder.hasIsReplaceTerritoryType()) {
            // 清理IsReplaceTerritoryType
            builder.clearIsReplaceTerritoryType();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ItemUseParamsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_INDEX)) {
            builder.setIndex(this.getIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEWPLAYERNAME)) {
            builder.setNewPlayerName(this.getNewPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTPAGENUM)) {
            builder.setTalentPageNum(this.getTalentPageNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTPAGEID)) {
            builder.setTalentPageId(this.getTalentPageId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_QUEUETYPE)) {
            builder.setQueueType(this.getQueueType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKID)) {
            builder.setTaskId(this.getTaskId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRANSPORTID)) {
            builder.setTransportId(this.getTransportId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINTLIST) && this.pointList != null) {
            final boolean needClear = !builder.hasPointList();
            final int tmpFieldCnt = this.pointList.copyChangeToCs(builder.getPointListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPointList();
            }
        }
        if (this.hasMark(FIELD_INDEX_USEDIRECTLY)) {
            builder.setUseDirectly(this.getUseDirectly());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SPASSWORD)) {
            builder.setSPassWord(this.getSPassWord());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISREPLACETERRITORYTYPE)) {
            builder.setIsReplaceTerritoryType(this.getIsReplaceTerritoryType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ItemUseParamsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToAndClearDeleteKeysCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_INDEX)) {
            builder.setIndex(this.getIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEWPLAYERNAME)) {
            builder.setNewPlayerName(this.getNewPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTPAGENUM)) {
            builder.setTalentPageNum(this.getTalentPageNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTPAGEID)) {
            builder.setTalentPageId(this.getTalentPageId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_QUEUETYPE)) {
            builder.setQueueType(this.getQueueType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKID)) {
            builder.setTaskId(this.getTaskId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRANSPORTID)) {
            builder.setTransportId(this.getTransportId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINTLIST) && this.pointList != null) {
            final boolean needClear = !builder.hasPointList();
            final int tmpFieldCnt = this.pointList.copyChangeToCs(builder.getPointListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPointList();
            }
        }
        if (this.hasMark(FIELD_INDEX_USEDIRECTLY)) {
            builder.setUseDirectly(this.getUseDirectly());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SPASSWORD)) {
            builder.setSPassWord(this.getSPassWord());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISREPLACETERRITORYTYPE)) {
            builder.setIsReplaceTerritoryType(this.getIsReplaceTerritoryType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ItemUseParamsPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromCs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromCs(proto.getPoint());
            }
        }
        if (proto.hasIndex()) {
            this.innerSetIndex(proto.getIndex());
        } else {
            this.innerSetIndex(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNewPlayerName()) {
            this.innerSetNewPlayerName(proto.getNewPlayerName());
        } else {
            this.innerSetNewPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTalentPageNum()) {
            this.innerSetTalentPageNum(proto.getTalentPageNum());
        } else {
            this.innerSetTalentPageNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTalentPageId()) {
            this.innerSetTalentPageId(proto.getTalentPageId());
        } else {
            this.innerSetTalentPageId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasQueueType()) {
            this.innerSetQueueType(proto.getQueueType());
        } else {
            this.innerSetQueueType(QueueTaskType.forNumber(0));
        }
        if (proto.hasTaskId()) {
            this.innerSetTaskId(proto.getTaskId());
        } else {
            this.innerSetTaskId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTransportId()) {
            this.innerSetTransportId(proto.getTransportId());
        } else {
            this.innerSetTransportId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPointList()) {
            this.getPointList().mergeFromCs(proto.getPointList());
        } else {
            if (this.pointList != null) {
                this.pointList.mergeFromCs(proto.getPointList());
            }
        }
        if (proto.hasUseDirectly()) {
            this.innerSetUseDirectly(proto.getUseDirectly());
        } else {
            this.innerSetUseDirectly(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasSPassWord()) {
            this.innerSetSPassWord(proto.getSPassWord());
        } else {
            this.innerSetSPassWord(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasIsReplaceTerritoryType()) {
            this.innerSetIsReplaceTerritoryType(proto.getIsReplaceTerritoryType());
        } else {
            this.innerSetIsReplaceTerritoryType(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ItemUseParamsProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ItemUseParamsPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromCs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasIndex()) {
            this.setIndex(proto.getIndex());
            fieldCnt++;
        }
        if (proto.hasNewPlayerName()) {
            this.setNewPlayerName(proto.getNewPlayerName());
            fieldCnt++;
        }
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasTalentPageNum()) {
            this.setTalentPageNum(proto.getTalentPageNum());
            fieldCnt++;
        }
        if (proto.hasTalentPageId()) {
            this.setTalentPageId(proto.getTalentPageId());
            fieldCnt++;
        }
        if (proto.hasQueueType()) {
            this.setQueueType(proto.getQueueType());
            fieldCnt++;
        }
        if (proto.hasTaskId()) {
            this.setTaskId(proto.getTaskId());
            fieldCnt++;
        }
        if (proto.hasTransportId()) {
            this.setTransportId(proto.getTransportId());
            fieldCnt++;
        }
        if (proto.hasPointList()) {
            this.getPointList().mergeChangeFromCs(proto.getPointList());
            fieldCnt++;
        }
        if (proto.hasUseDirectly()) {
            this.setUseDirectly(proto.getUseDirectly());
            fieldCnt++;
        }
        if (proto.hasSPassWord()) {
            this.setSPassWord(proto.getSPassWord());
            fieldCnt++;
        }
        if (proto.hasIsReplaceTerritoryType()) {
            this.setIsReplaceTerritoryType(proto.getIsReplaceTerritoryType());
            fieldCnt++;
        }
        return fieldCnt;
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ItemUseParams.Builder getCopySsBuilder() {
        final ItemUseParams.Builder builder = ItemUseParams.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ItemUseParams.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getIndex() != 0) {
            builder.setIndex(this.getIndex());
            fieldCnt++;
        }  else if (builder.hasIndex()) {
            // 清理Index
            builder.clearIndex();
            fieldCnt++;
        }
        if (!this.getNewPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setNewPlayerName(this.getNewPlayerName());
            fieldCnt++;
        }  else if (builder.hasNewPlayerName()) {
            // 清理NewPlayerName
            builder.clearNewPlayerName();
            fieldCnt++;
        }
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.getTalentPageNum() != 0) {
            builder.setTalentPageNum(this.getTalentPageNum());
            fieldCnt++;
        }  else if (builder.hasTalentPageNum()) {
            // 清理TalentPageNum
            builder.clearTalentPageNum();
            fieldCnt++;
        }
        if (this.getTalentPageId() != 0L) {
            builder.setTalentPageId(this.getTalentPageId());
            fieldCnt++;
        }  else if (builder.hasTalentPageId()) {
            // 清理TalentPageId
            builder.clearTalentPageId();
            fieldCnt++;
        }
        if (this.getQueueType() != QueueTaskType.forNumber(0)) {
            builder.setQueueType(this.getQueueType());
            fieldCnt++;
        }  else if (builder.hasQueueType()) {
            // 清理QueueType
            builder.clearQueueType();
            fieldCnt++;
        }
        if (this.getTaskId() != 0L) {
            builder.setTaskId(this.getTaskId());
            fieldCnt++;
        }  else if (builder.hasTaskId()) {
            // 清理TaskId
            builder.clearTaskId();
            fieldCnt++;
        }
        if (this.getTransportId() != 0L) {
            builder.setTransportId(this.getTransportId());
            fieldCnt++;
        }  else if (builder.hasTransportId()) {
            // 清理TransportId
            builder.clearTransportId();
            fieldCnt++;
        }
        if (this.pointList != null) {
            Struct.PointList.Builder tmpBuilder = Struct.PointList.newBuilder();
            final int tmpFieldCnt = this.pointList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPointList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPointList();
            }
        }  else if (builder.hasPointList()) {
            // 清理PointList
            builder.clearPointList();
            fieldCnt++;
        }
        if (this.getUseDirectly()) {
            builder.setUseDirectly(this.getUseDirectly());
            fieldCnt++;
        }  else if (builder.hasUseDirectly()) {
            // 清理UseDirectly
            builder.clearUseDirectly();
            fieldCnt++;
        }
        if (!this.getSPassWord().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSPassWord(this.getSPassWord());
            fieldCnt++;
        }  else if (builder.hasSPassWord()) {
            // 清理SPassWord
            builder.clearSPassWord();
            fieldCnt++;
        }
        if (this.getIsReplaceTerritoryType()) {
            builder.setIsReplaceTerritoryType(this.getIsReplaceTerritoryType());
            fieldCnt++;
        }  else if (builder.hasIsReplaceTerritoryType()) {
            // 清理IsReplaceTerritoryType
            builder.clearIsReplaceTerritoryType();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ItemUseParams.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToSs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_INDEX)) {
            builder.setIndex(this.getIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEWPLAYERNAME)) {
            builder.setNewPlayerName(this.getNewPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTPAGENUM)) {
            builder.setTalentPageNum(this.getTalentPageNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTPAGEID)) {
            builder.setTalentPageId(this.getTalentPageId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_QUEUETYPE)) {
            builder.setQueueType(this.getQueueType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKID)) {
            builder.setTaskId(this.getTaskId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRANSPORTID)) {
            builder.setTransportId(this.getTransportId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINTLIST) && this.pointList != null) {
            final boolean needClear = !builder.hasPointList();
            final int tmpFieldCnt = this.pointList.copyChangeToSs(builder.getPointListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPointList();
            }
        }
        if (this.hasMark(FIELD_INDEX_USEDIRECTLY)) {
            builder.setUseDirectly(this.getUseDirectly());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SPASSWORD)) {
            builder.setSPassWord(this.getSPassWord());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISREPLACETERRITORYTYPE)) {
            builder.setIsReplaceTerritoryType(this.getIsReplaceTerritoryType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ItemUseParams proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromSs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromSs(proto.getPoint());
            }
        }
        if (proto.hasIndex()) {
            this.innerSetIndex(proto.getIndex());
        } else {
            this.innerSetIndex(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNewPlayerName()) {
            this.innerSetNewPlayerName(proto.getNewPlayerName());
        } else {
            this.innerSetNewPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTalentPageNum()) {
            this.innerSetTalentPageNum(proto.getTalentPageNum());
        } else {
            this.innerSetTalentPageNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTalentPageId()) {
            this.innerSetTalentPageId(proto.getTalentPageId());
        } else {
            this.innerSetTalentPageId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasQueueType()) {
            this.innerSetQueueType(proto.getQueueType());
        } else {
            this.innerSetQueueType(QueueTaskType.forNumber(0));
        }
        if (proto.hasTaskId()) {
            this.innerSetTaskId(proto.getTaskId());
        } else {
            this.innerSetTaskId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTransportId()) {
            this.innerSetTransportId(proto.getTransportId());
        } else {
            this.innerSetTransportId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPointList()) {
            this.getPointList().mergeFromSs(proto.getPointList());
        } else {
            if (this.pointList != null) {
                this.pointList.mergeFromSs(proto.getPointList());
            }
        }
        if (proto.hasUseDirectly()) {
            this.innerSetUseDirectly(proto.getUseDirectly());
        } else {
            this.innerSetUseDirectly(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasSPassWord()) {
            this.innerSetSPassWord(proto.getSPassWord());
        } else {
            this.innerSetSPassWord(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasIsReplaceTerritoryType()) {
            this.innerSetIsReplaceTerritoryType(proto.getIsReplaceTerritoryType());
        } else {
            this.innerSetIsReplaceTerritoryType(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ItemUseParamsProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ItemUseParams proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromSs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasIndex()) {
            this.setIndex(proto.getIndex());
            fieldCnt++;
        }
        if (proto.hasNewPlayerName()) {
            this.setNewPlayerName(proto.getNewPlayerName());
            fieldCnt++;
        }
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasTalentPageNum()) {
            this.setTalentPageNum(proto.getTalentPageNum());
            fieldCnt++;
        }
        if (proto.hasTalentPageId()) {
            this.setTalentPageId(proto.getTalentPageId());
            fieldCnt++;
        }
        if (proto.hasQueueType()) {
            this.setQueueType(proto.getQueueType());
            fieldCnt++;
        }
        if (proto.hasTaskId()) {
            this.setTaskId(proto.getTaskId());
            fieldCnt++;
        }
        if (proto.hasTransportId()) {
            this.setTransportId(proto.getTransportId());
            fieldCnt++;
        }
        if (proto.hasPointList()) {
            this.getPointList().mergeChangeFromSs(proto.getPointList());
            fieldCnt++;
        }
        if (proto.hasUseDirectly()) {
            this.setUseDirectly(proto.getUseDirectly());
            fieldCnt++;
        }
        if (proto.hasSPassWord()) {
            this.setSPassWord(proto.getSPassWord());
            fieldCnt++;
        }
        if (proto.hasIsReplaceTerritoryType()) {
            this.setIsReplaceTerritoryType(proto.getIsReplaceTerritoryType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ItemUseParams.Builder builder = ItemUseParams.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            this.point.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_POINTLIST) && this.pointList != null) {
            this.pointList.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.point != null) {
            this.point.markAll();
        }
        if (this.pointList != null) {
            this.pointList.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ItemUseParamsProp)) {
            return false;
        }
        final ItemUseParamsProp otherNode = (ItemUseParamsProp) node;
        if (!this.getPoint().compareDataTo(otherNode.getPoint())) {
            return false;
        }
        if (this.index != otherNode.index) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.newPlayerName, otherNode.newPlayerName)) {
            return false;
        }
        if (this.heroId != otherNode.heroId) {
            return false;
        }
        if (this.talentPageNum != otherNode.talentPageNum) {
            return false;
        }
        if (this.talentPageId != otherNode.talentPageId) {
            return false;
        }
        if (this.queueType != otherNode.queueType) {
            return false;
        }
        if (this.taskId != otherNode.taskId) {
            return false;
        }
        if (this.transportId != otherNode.transportId) {
            return false;
        }
        if (!this.getPointList().compareDataTo(otherNode.getPointList())) {
            return false;
        }
        if (this.useDirectly != otherNode.useDirectly) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.sPassWord, otherNode.sPassWord)) {
            return false;
        }
        if (this.isReplaceTerritoryType != otherNode.isReplaceTerritoryType) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 51;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerWallModel;
import com.yorha.proto.PlayerPB.PlayerWallModelPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerWallModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_USEMAINHEROID = 0;
    public static final int FIELD_INDEX_USEDEPUTYHEROID = 1;
    public static final int FIELD_INDEX_SETUPMAINHEROID = 2;
    public static final int FIELD_INDEX_SETUPDEPUTYHEROID = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int useMainHeroId = Constant.DEFAULT_INT_VALUE;
    private int useDeputyHeroId = Constant.DEFAULT_INT_VALUE;
    private int setUpMainHeroId = Constant.DEFAULT_INT_VALUE;
    private int setUpDeputyHeroId = Constant.DEFAULT_INT_VALUE;

    public PlayerWallModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerWallModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get useMainHeroId
     *
     * @return useMainHeroId value
     */
    public int getUseMainHeroId() {
        return this.useMainHeroId;
    }

    /**
     * set useMainHeroId && set marked
     *
     * @param useMainHeroId new value
     * @return current object
     */
    public PlayerWallModelProp setUseMainHeroId(int useMainHeroId) {
        if (this.useMainHeroId != useMainHeroId) {
            this.mark(FIELD_INDEX_USEMAINHEROID);
            this.useMainHeroId = useMainHeroId;
        }
        return this;
    }

    /**
     * inner set useMainHeroId
     *
     * @param useMainHeroId new value
     */
    private void innerSetUseMainHeroId(int useMainHeroId) {
        this.useMainHeroId = useMainHeroId;
    }

    /**
     * get useDeputyHeroId
     *
     * @return useDeputyHeroId value
     */
    public int getUseDeputyHeroId() {
        return this.useDeputyHeroId;
    }

    /**
     * set useDeputyHeroId && set marked
     *
     * @param useDeputyHeroId new value
     * @return current object
     */
    public PlayerWallModelProp setUseDeputyHeroId(int useDeputyHeroId) {
        if (this.useDeputyHeroId != useDeputyHeroId) {
            this.mark(FIELD_INDEX_USEDEPUTYHEROID);
            this.useDeputyHeroId = useDeputyHeroId;
        }
        return this;
    }

    /**
     * inner set useDeputyHeroId
     *
     * @param useDeputyHeroId new value
     */
    private void innerSetUseDeputyHeroId(int useDeputyHeroId) {
        this.useDeputyHeroId = useDeputyHeroId;
    }

    /**
     * get setUpMainHeroId
     *
     * @return setUpMainHeroId value
     */
    public int getSetUpMainHeroId() {
        return this.setUpMainHeroId;
    }

    /**
     * set setUpMainHeroId && set marked
     *
     * @param setUpMainHeroId new value
     * @return current object
     */
    public PlayerWallModelProp setSetUpMainHeroId(int setUpMainHeroId) {
        if (this.setUpMainHeroId != setUpMainHeroId) {
            this.mark(FIELD_INDEX_SETUPMAINHEROID);
            this.setUpMainHeroId = setUpMainHeroId;
        }
        return this;
    }

    /**
     * inner set setUpMainHeroId
     *
     * @param setUpMainHeroId new value
     */
    private void innerSetSetUpMainHeroId(int setUpMainHeroId) {
        this.setUpMainHeroId = setUpMainHeroId;
    }

    /**
     * get setUpDeputyHeroId
     *
     * @return setUpDeputyHeroId value
     */
    public int getSetUpDeputyHeroId() {
        return this.setUpDeputyHeroId;
    }

    /**
     * set setUpDeputyHeroId && set marked
     *
     * @param setUpDeputyHeroId new value
     * @return current object
     */
    public PlayerWallModelProp setSetUpDeputyHeroId(int setUpDeputyHeroId) {
        if (this.setUpDeputyHeroId != setUpDeputyHeroId) {
            this.mark(FIELD_INDEX_SETUPDEPUTYHEROID);
            this.setUpDeputyHeroId = setUpDeputyHeroId;
        }
        return this;
    }

    /**
     * inner set setUpDeputyHeroId
     *
     * @param setUpDeputyHeroId new value
     */
    private void innerSetSetUpDeputyHeroId(int setUpDeputyHeroId) {
        this.setUpDeputyHeroId = setUpDeputyHeroId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerWallModelPB.Builder getCopyCsBuilder() {
        final PlayerWallModelPB.Builder builder = PlayerWallModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerWallModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUseMainHeroId() != 0) {
            builder.setUseMainHeroId(this.getUseMainHeroId());
            fieldCnt++;
        }  else if (builder.hasUseMainHeroId()) {
            // 清理UseMainHeroId
            builder.clearUseMainHeroId();
            fieldCnt++;
        }
        if (this.getUseDeputyHeroId() != 0) {
            builder.setUseDeputyHeroId(this.getUseDeputyHeroId());
            fieldCnt++;
        }  else if (builder.hasUseDeputyHeroId()) {
            // 清理UseDeputyHeroId
            builder.clearUseDeputyHeroId();
            fieldCnt++;
        }
        if (this.getSetUpMainHeroId() != 0) {
            builder.setSetUpMainHeroId(this.getSetUpMainHeroId());
            fieldCnt++;
        }  else if (builder.hasSetUpMainHeroId()) {
            // 清理SetUpMainHeroId
            builder.clearSetUpMainHeroId();
            fieldCnt++;
        }
        if (this.getSetUpDeputyHeroId() != 0) {
            builder.setSetUpDeputyHeroId(this.getSetUpDeputyHeroId());
            fieldCnt++;
        }  else if (builder.hasSetUpDeputyHeroId()) {
            // 清理SetUpDeputyHeroId
            builder.clearSetUpDeputyHeroId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerWallModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_USEMAINHEROID)) {
            builder.setUseMainHeroId(this.getUseMainHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USEDEPUTYHEROID)) {
            builder.setUseDeputyHeroId(this.getUseDeputyHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SETUPMAINHEROID)) {
            builder.setSetUpMainHeroId(this.getSetUpMainHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SETUPDEPUTYHEROID)) {
            builder.setSetUpDeputyHeroId(this.getSetUpDeputyHeroId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerWallModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_USEMAINHEROID)) {
            builder.setUseMainHeroId(this.getUseMainHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USEDEPUTYHEROID)) {
            builder.setUseDeputyHeroId(this.getUseDeputyHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SETUPMAINHEROID)) {
            builder.setSetUpMainHeroId(this.getSetUpMainHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SETUPDEPUTYHEROID)) {
            builder.setSetUpDeputyHeroId(this.getSetUpDeputyHeroId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerWallModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUseMainHeroId()) {
            this.innerSetUseMainHeroId(proto.getUseMainHeroId());
        } else {
            this.innerSetUseMainHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUseDeputyHeroId()) {
            this.innerSetUseDeputyHeroId(proto.getUseDeputyHeroId());
        } else {
            this.innerSetUseDeputyHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSetUpMainHeroId()) {
            this.innerSetSetUpMainHeroId(proto.getSetUpMainHeroId());
        } else {
            this.innerSetSetUpMainHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSetUpDeputyHeroId()) {
            this.innerSetSetUpDeputyHeroId(proto.getSetUpDeputyHeroId());
        } else {
            this.innerSetSetUpDeputyHeroId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerWallModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerWallModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUseMainHeroId()) {
            this.setUseMainHeroId(proto.getUseMainHeroId());
            fieldCnt++;
        }
        if (proto.hasUseDeputyHeroId()) {
            this.setUseDeputyHeroId(proto.getUseDeputyHeroId());
            fieldCnt++;
        }
        if (proto.hasSetUpMainHeroId()) {
            this.setSetUpMainHeroId(proto.getSetUpMainHeroId());
            fieldCnt++;
        }
        if (proto.hasSetUpDeputyHeroId()) {
            this.setSetUpDeputyHeroId(proto.getSetUpDeputyHeroId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerWallModel.Builder getCopyDbBuilder() {
        final PlayerWallModel.Builder builder = PlayerWallModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerWallModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUseMainHeroId() != 0) {
            builder.setUseMainHeroId(this.getUseMainHeroId());
            fieldCnt++;
        }  else if (builder.hasUseMainHeroId()) {
            // 清理UseMainHeroId
            builder.clearUseMainHeroId();
            fieldCnt++;
        }
        if (this.getUseDeputyHeroId() != 0) {
            builder.setUseDeputyHeroId(this.getUseDeputyHeroId());
            fieldCnt++;
        }  else if (builder.hasUseDeputyHeroId()) {
            // 清理UseDeputyHeroId
            builder.clearUseDeputyHeroId();
            fieldCnt++;
        }
        if (this.getSetUpMainHeroId() != 0) {
            builder.setSetUpMainHeroId(this.getSetUpMainHeroId());
            fieldCnt++;
        }  else if (builder.hasSetUpMainHeroId()) {
            // 清理SetUpMainHeroId
            builder.clearSetUpMainHeroId();
            fieldCnt++;
        }
        if (this.getSetUpDeputyHeroId() != 0) {
            builder.setSetUpDeputyHeroId(this.getSetUpDeputyHeroId());
            fieldCnt++;
        }  else if (builder.hasSetUpDeputyHeroId()) {
            // 清理SetUpDeputyHeroId
            builder.clearSetUpDeputyHeroId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerWallModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_USEMAINHEROID)) {
            builder.setUseMainHeroId(this.getUseMainHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USEDEPUTYHEROID)) {
            builder.setUseDeputyHeroId(this.getUseDeputyHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SETUPMAINHEROID)) {
            builder.setSetUpMainHeroId(this.getSetUpMainHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SETUPDEPUTYHEROID)) {
            builder.setSetUpDeputyHeroId(this.getSetUpDeputyHeroId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerWallModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUseMainHeroId()) {
            this.innerSetUseMainHeroId(proto.getUseMainHeroId());
        } else {
            this.innerSetUseMainHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUseDeputyHeroId()) {
            this.innerSetUseDeputyHeroId(proto.getUseDeputyHeroId());
        } else {
            this.innerSetUseDeputyHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSetUpMainHeroId()) {
            this.innerSetSetUpMainHeroId(proto.getSetUpMainHeroId());
        } else {
            this.innerSetSetUpMainHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSetUpDeputyHeroId()) {
            this.innerSetSetUpDeputyHeroId(proto.getSetUpDeputyHeroId());
        } else {
            this.innerSetSetUpDeputyHeroId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerWallModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerWallModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUseMainHeroId()) {
            this.setUseMainHeroId(proto.getUseMainHeroId());
            fieldCnt++;
        }
        if (proto.hasUseDeputyHeroId()) {
            this.setUseDeputyHeroId(proto.getUseDeputyHeroId());
            fieldCnt++;
        }
        if (proto.hasSetUpMainHeroId()) {
            this.setSetUpMainHeroId(proto.getSetUpMainHeroId());
            fieldCnt++;
        }
        if (proto.hasSetUpDeputyHeroId()) {
            this.setSetUpDeputyHeroId(proto.getSetUpDeputyHeroId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerWallModel.Builder getCopySsBuilder() {
        final PlayerWallModel.Builder builder = PlayerWallModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerWallModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUseMainHeroId() != 0) {
            builder.setUseMainHeroId(this.getUseMainHeroId());
            fieldCnt++;
        }  else if (builder.hasUseMainHeroId()) {
            // 清理UseMainHeroId
            builder.clearUseMainHeroId();
            fieldCnt++;
        }
        if (this.getUseDeputyHeroId() != 0) {
            builder.setUseDeputyHeroId(this.getUseDeputyHeroId());
            fieldCnt++;
        }  else if (builder.hasUseDeputyHeroId()) {
            // 清理UseDeputyHeroId
            builder.clearUseDeputyHeroId();
            fieldCnt++;
        }
        if (this.getSetUpMainHeroId() != 0) {
            builder.setSetUpMainHeroId(this.getSetUpMainHeroId());
            fieldCnt++;
        }  else if (builder.hasSetUpMainHeroId()) {
            // 清理SetUpMainHeroId
            builder.clearSetUpMainHeroId();
            fieldCnt++;
        }
        if (this.getSetUpDeputyHeroId() != 0) {
            builder.setSetUpDeputyHeroId(this.getSetUpDeputyHeroId());
            fieldCnt++;
        }  else if (builder.hasSetUpDeputyHeroId()) {
            // 清理SetUpDeputyHeroId
            builder.clearSetUpDeputyHeroId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerWallModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_USEMAINHEROID)) {
            builder.setUseMainHeroId(this.getUseMainHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USEDEPUTYHEROID)) {
            builder.setUseDeputyHeroId(this.getUseDeputyHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SETUPMAINHEROID)) {
            builder.setSetUpMainHeroId(this.getSetUpMainHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SETUPDEPUTYHEROID)) {
            builder.setSetUpDeputyHeroId(this.getSetUpDeputyHeroId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerWallModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUseMainHeroId()) {
            this.innerSetUseMainHeroId(proto.getUseMainHeroId());
        } else {
            this.innerSetUseMainHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUseDeputyHeroId()) {
            this.innerSetUseDeputyHeroId(proto.getUseDeputyHeroId());
        } else {
            this.innerSetUseDeputyHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSetUpMainHeroId()) {
            this.innerSetSetUpMainHeroId(proto.getSetUpMainHeroId());
        } else {
            this.innerSetSetUpMainHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSetUpDeputyHeroId()) {
            this.innerSetSetUpDeputyHeroId(proto.getSetUpDeputyHeroId());
        } else {
            this.innerSetSetUpDeputyHeroId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerWallModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerWallModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUseMainHeroId()) {
            this.setUseMainHeroId(proto.getUseMainHeroId());
            fieldCnt++;
        }
        if (proto.hasUseDeputyHeroId()) {
            this.setUseDeputyHeroId(proto.getUseDeputyHeroId());
            fieldCnt++;
        }
        if (proto.hasSetUpMainHeroId()) {
            this.setSetUpMainHeroId(proto.getSetUpMainHeroId());
            fieldCnt++;
        }
        if (proto.hasSetUpDeputyHeroId()) {
            this.setSetUpDeputyHeroId(proto.getSetUpDeputyHeroId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerWallModel.Builder builder = PlayerWallModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerWallModelProp)) {
            return false;
        }
        final PlayerWallModelProp otherNode = (PlayerWallModelProp) node;
        if (this.useMainHeroId != otherNode.useMainHeroId) {
            return false;
        }
        if (this.useDeputyHeroId != otherNode.useDeputyHeroId) {
            return false;
        }
        if (this.setUpMainHeroId != otherNode.setUpMainHeroId) {
            return false;
        }
        if (this.setUpDeputyHeroId != otherNode.setUpDeputyHeroId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
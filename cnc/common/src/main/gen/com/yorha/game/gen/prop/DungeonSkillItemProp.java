package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructCommon.DungeonSkillItem;
import com.yorha.proto.StructCommonPB.DungeonSkillItemPB;


/**
 * <AUTHOR> auto gen
 */
public class DungeonSkillItemProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_SKILLID = 0;
    public static final int FIELD_INDEX_CANUSENUM = 1;
    public static final int FIELD_INDEX_CANUSETSMS = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int skillId = Constant.DEFAULT_INT_VALUE;
    private int canUseNum = Constant.DEFAULT_INT_VALUE;
    private long canUseTsMs = Constant.DEFAULT_LONG_VALUE;

    public DungeonSkillItemProp() {
        super(null, 0, FIELD_COUNT);
    }

    public DungeonSkillItemProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get skillId
     *
     * @return skillId value
     */
    public int getSkillId() {
        return this.skillId;
    }

    /**
     * set skillId && set marked
     *
     * @param skillId new value
     * @return current object
     */
    public DungeonSkillItemProp setSkillId(int skillId) {
        if (this.skillId != skillId) {
            this.mark(FIELD_INDEX_SKILLID);
            this.skillId = skillId;
        }
        return this;
    }

    /**
     * inner set skillId
     *
     * @param skillId new value
     */
    private void innerSetSkillId(int skillId) {
        this.skillId = skillId;
    }

    /**
     * get canUseNum
     *
     * @return canUseNum value
     */
    public int getCanUseNum() {
        return this.canUseNum;
    }

    /**
     * set canUseNum && set marked
     *
     * @param canUseNum new value
     * @return current object
     */
    public DungeonSkillItemProp setCanUseNum(int canUseNum) {
        if (this.canUseNum != canUseNum) {
            this.mark(FIELD_INDEX_CANUSENUM);
            this.canUseNum = canUseNum;
        }
        return this;
    }

    /**
     * inner set canUseNum
     *
     * @param canUseNum new value
     */
    private void innerSetCanUseNum(int canUseNum) {
        this.canUseNum = canUseNum;
    }

    /**
     * get canUseTsMs
     *
     * @return canUseTsMs value
     */
    public long getCanUseTsMs() {
        return this.canUseTsMs;
    }

    /**
     * set canUseTsMs && set marked
     *
     * @param canUseTsMs new value
     * @return current object
     */
    public DungeonSkillItemProp setCanUseTsMs(long canUseTsMs) {
        if (this.canUseTsMs != canUseTsMs) {
            this.mark(FIELD_INDEX_CANUSETSMS);
            this.canUseTsMs = canUseTsMs;
        }
        return this;
    }

    /**
     * inner set canUseTsMs
     *
     * @param canUseTsMs new value
     */
    private void innerSetCanUseTsMs(long canUseTsMs) {
        this.canUseTsMs = canUseTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DungeonSkillItemPB.Builder getCopyCsBuilder() {
        final DungeonSkillItemPB.Builder builder = DungeonSkillItemPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(DungeonSkillItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSkillId() != 0) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }  else if (builder.hasSkillId()) {
            // 清理SkillId
            builder.clearSkillId();
            fieldCnt++;
        }
        if (this.getCanUseNum() != 0) {
            builder.setCanUseNum(this.getCanUseNum());
            fieldCnt++;
        }  else if (builder.hasCanUseNum()) {
            // 清理CanUseNum
            builder.clearCanUseNum();
            fieldCnt++;
        }
        if (this.getCanUseTsMs() != 0L) {
            builder.setCanUseTsMs(this.getCanUseTsMs());
            fieldCnt++;
        }  else if (builder.hasCanUseTsMs()) {
            // 清理CanUseTsMs
            builder.clearCanUseTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(DungeonSkillItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLID)) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANUSENUM)) {
            builder.setCanUseNum(this.getCanUseNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANUSETSMS)) {
            builder.setCanUseTsMs(this.getCanUseTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(DungeonSkillItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLID)) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANUSENUM)) {
            builder.setCanUseNum(this.getCanUseNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANUSETSMS)) {
            builder.setCanUseTsMs(this.getCanUseTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(DungeonSkillItemPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkillId()) {
            this.innerSetSkillId(proto.getSkillId());
        } else {
            this.innerSetSkillId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCanUseNum()) {
            this.innerSetCanUseNum(proto.getCanUseNum());
        } else {
            this.innerSetCanUseNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCanUseTsMs()) {
            this.innerSetCanUseTsMs(proto.getCanUseTsMs());
        } else {
            this.innerSetCanUseTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return DungeonSkillItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(DungeonSkillItemPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkillId()) {
            this.setSkillId(proto.getSkillId());
            fieldCnt++;
        }
        if (proto.hasCanUseNum()) {
            this.setCanUseNum(proto.getCanUseNum());
            fieldCnt++;
        }
        if (proto.hasCanUseTsMs()) {
            this.setCanUseTsMs(proto.getCanUseTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DungeonSkillItem.Builder getCopyDbBuilder() {
        final DungeonSkillItem.Builder builder = DungeonSkillItem.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(DungeonSkillItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSkillId() != 0) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }  else if (builder.hasSkillId()) {
            // 清理SkillId
            builder.clearSkillId();
            fieldCnt++;
        }
        if (this.getCanUseNum() != 0) {
            builder.setCanUseNum(this.getCanUseNum());
            fieldCnt++;
        }  else if (builder.hasCanUseNum()) {
            // 清理CanUseNum
            builder.clearCanUseNum();
            fieldCnt++;
        }
        if (this.getCanUseTsMs() != 0L) {
            builder.setCanUseTsMs(this.getCanUseTsMs());
            fieldCnt++;
        }  else if (builder.hasCanUseTsMs()) {
            // 清理CanUseTsMs
            builder.clearCanUseTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(DungeonSkillItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLID)) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANUSENUM)) {
            builder.setCanUseNum(this.getCanUseNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANUSETSMS)) {
            builder.setCanUseTsMs(this.getCanUseTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(DungeonSkillItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkillId()) {
            this.innerSetSkillId(proto.getSkillId());
        } else {
            this.innerSetSkillId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCanUseNum()) {
            this.innerSetCanUseNum(proto.getCanUseNum());
        } else {
            this.innerSetCanUseNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCanUseTsMs()) {
            this.innerSetCanUseTsMs(proto.getCanUseTsMs());
        } else {
            this.innerSetCanUseTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return DungeonSkillItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(DungeonSkillItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkillId()) {
            this.setSkillId(proto.getSkillId());
            fieldCnt++;
        }
        if (proto.hasCanUseNum()) {
            this.setCanUseNum(proto.getCanUseNum());
            fieldCnt++;
        }
        if (proto.hasCanUseTsMs()) {
            this.setCanUseTsMs(proto.getCanUseTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DungeonSkillItem.Builder getCopySsBuilder() {
        final DungeonSkillItem.Builder builder = DungeonSkillItem.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(DungeonSkillItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSkillId() != 0) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }  else if (builder.hasSkillId()) {
            // 清理SkillId
            builder.clearSkillId();
            fieldCnt++;
        }
        if (this.getCanUseNum() != 0) {
            builder.setCanUseNum(this.getCanUseNum());
            fieldCnt++;
        }  else if (builder.hasCanUseNum()) {
            // 清理CanUseNum
            builder.clearCanUseNum();
            fieldCnt++;
        }
        if (this.getCanUseTsMs() != 0L) {
            builder.setCanUseTsMs(this.getCanUseTsMs());
            fieldCnt++;
        }  else if (builder.hasCanUseTsMs()) {
            // 清理CanUseTsMs
            builder.clearCanUseTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(DungeonSkillItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLID)) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANUSENUM)) {
            builder.setCanUseNum(this.getCanUseNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANUSETSMS)) {
            builder.setCanUseTsMs(this.getCanUseTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(DungeonSkillItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkillId()) {
            this.innerSetSkillId(proto.getSkillId());
        } else {
            this.innerSetSkillId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCanUseNum()) {
            this.innerSetCanUseNum(proto.getCanUseNum());
        } else {
            this.innerSetCanUseNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCanUseTsMs()) {
            this.innerSetCanUseTsMs(proto.getCanUseTsMs());
        } else {
            this.innerSetCanUseTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return DungeonSkillItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(DungeonSkillItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkillId()) {
            this.setSkillId(proto.getSkillId());
            fieldCnt++;
        }
        if (proto.hasCanUseNum()) {
            this.setCanUseNum(proto.getCanUseNum());
            fieldCnt++;
        }
        if (proto.hasCanUseTsMs()) {
            this.setCanUseTsMs(proto.getCanUseTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        DungeonSkillItem.Builder builder = DungeonSkillItem.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.skillId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof DungeonSkillItemProp)) {
            return false;
        }
        final DungeonSkillItemProp otherNode = (DungeonSkillItemProp) node;
        if (this.skillId != otherNode.skillId) {
            return false;
        }
        if (this.canUseNum != otherNode.canUseNum) {
            return false;
        }
        if (this.canUseTsMs != otherNode.canUseTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.PlayerSoldierUnit;
import com.yorha.proto.StructPB.PlayerSoldierUnitPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerSoldierUnitProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_SOLDIERID = 0;
    public static final int FIELD_INDEX_NUM = 1;
    public static final int FIELD_INDEX_ISLEVELUP = 2;
    public static final int FIELD_INDEX_PRESOLDIERID = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int soldierId = Constant.DEFAULT_INT_VALUE;
    private int num = Constant.DEFAULT_INT_VALUE;
    private boolean isLevelUp = Constant.DEFAULT_BOOLEAN_VALUE;
    private int preSoldierId = Constant.DEFAULT_INT_VALUE;

    public PlayerSoldierUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerSoldierUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get soldierId
     *
     * @return soldierId value
     */
    public int getSoldierId() {
        return this.soldierId;
    }

    /**
     * set soldierId && set marked
     *
     * @param soldierId new value
     * @return current object
     */
    public PlayerSoldierUnitProp setSoldierId(int soldierId) {
        if (this.soldierId != soldierId) {
            this.mark(FIELD_INDEX_SOLDIERID);
            this.soldierId = soldierId;
        }
        return this;
    }

    /**
     * inner set soldierId
     *
     * @param soldierId new value
     */
    private void innerSetSoldierId(int soldierId) {
        this.soldierId = soldierId;
    }

    /**
     * get num
     *
     * @return num value
     */
    public int getNum() {
        return this.num;
    }

    /**
     * set num && set marked
     *
     * @param num new value
     * @return current object
     */
    public PlayerSoldierUnitProp setNum(int num) {
        if (this.num != num) {
            this.mark(FIELD_INDEX_NUM);
            this.num = num;
        }
        return this;
    }

    /**
     * inner set num
     *
     * @param num new value
     */
    private void innerSetNum(int num) {
        this.num = num;
    }

    /**
     * get isLevelUp
     *
     * @return isLevelUp value
     */
    public boolean getIsLevelUp() {
        return this.isLevelUp;
    }

    /**
     * set isLevelUp && set marked
     *
     * @param isLevelUp new value
     * @return current object
     */
    public PlayerSoldierUnitProp setIsLevelUp(boolean isLevelUp) {
        if (this.isLevelUp != isLevelUp) {
            this.mark(FIELD_INDEX_ISLEVELUP);
            this.isLevelUp = isLevelUp;
        }
        return this;
    }

    /**
     * inner set isLevelUp
     *
     * @param isLevelUp new value
     */
    private void innerSetIsLevelUp(boolean isLevelUp) {
        this.isLevelUp = isLevelUp;
    }

    /**
     * get preSoldierId
     *
     * @return preSoldierId value
     */
    public int getPreSoldierId() {
        return this.preSoldierId;
    }

    /**
     * set preSoldierId && set marked
     *
     * @param preSoldierId new value
     * @return current object
     */
    public PlayerSoldierUnitProp setPreSoldierId(int preSoldierId) {
        if (this.preSoldierId != preSoldierId) {
            this.mark(FIELD_INDEX_PRESOLDIERID);
            this.preSoldierId = preSoldierId;
        }
        return this;
    }

    /**
     * inner set preSoldierId
     *
     * @param preSoldierId new value
     */
    private void innerSetPreSoldierId(int preSoldierId) {
        this.preSoldierId = preSoldierId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerSoldierUnitPB.Builder getCopyCsBuilder() {
        final PlayerSoldierUnitPB.Builder builder = PlayerSoldierUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerSoldierUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSoldierId() != 0) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }  else if (builder.hasSoldierId()) {
            // 清理SoldierId
            builder.clearSoldierId();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerSoldierUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERID)) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerSoldierUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERID)) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerSoldierUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierId()) {
            this.innerSetSoldierId(proto.getSoldierId());
        } else {
            this.innerSetSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerSoldierUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerSoldierUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierId()) {
            this.setSoldierId(proto.getSoldierId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerSoldierUnit.Builder getCopyDbBuilder() {
        final PlayerSoldierUnit.Builder builder = PlayerSoldierUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerSoldierUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSoldierId() != 0) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }  else if (builder.hasSoldierId()) {
            // 清理SoldierId
            builder.clearSoldierId();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        if (this.getIsLevelUp()) {
            builder.setIsLevelUp(this.getIsLevelUp());
            fieldCnt++;
        }  else if (builder.hasIsLevelUp()) {
            // 清理IsLevelUp
            builder.clearIsLevelUp();
            fieldCnt++;
        }
        if (this.getPreSoldierId() != 0) {
            builder.setPreSoldierId(this.getPreSoldierId());
            fieldCnt++;
        }  else if (builder.hasPreSoldierId()) {
            // 清理PreSoldierId
            builder.clearPreSoldierId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerSoldierUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERID)) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISLEVELUP)) {
            builder.setIsLevelUp(this.getIsLevelUp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PRESOLDIERID)) {
            builder.setPreSoldierId(this.getPreSoldierId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerSoldierUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierId()) {
            this.innerSetSoldierId(proto.getSoldierId());
        } else {
            this.innerSetSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIsLevelUp()) {
            this.innerSetIsLevelUp(proto.getIsLevelUp());
        } else {
            this.innerSetIsLevelUp(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasPreSoldierId()) {
            this.innerSetPreSoldierId(proto.getPreSoldierId());
        } else {
            this.innerSetPreSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerSoldierUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerSoldierUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierId()) {
            this.setSoldierId(proto.getSoldierId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        if (proto.hasIsLevelUp()) {
            this.setIsLevelUp(proto.getIsLevelUp());
            fieldCnt++;
        }
        if (proto.hasPreSoldierId()) {
            this.setPreSoldierId(proto.getPreSoldierId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerSoldierUnit.Builder getCopySsBuilder() {
        final PlayerSoldierUnit.Builder builder = PlayerSoldierUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerSoldierUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSoldierId() != 0) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }  else if (builder.hasSoldierId()) {
            // 清理SoldierId
            builder.clearSoldierId();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        if (this.getIsLevelUp()) {
            builder.setIsLevelUp(this.getIsLevelUp());
            fieldCnt++;
        }  else if (builder.hasIsLevelUp()) {
            // 清理IsLevelUp
            builder.clearIsLevelUp();
            fieldCnt++;
        }
        if (this.getPreSoldierId() != 0) {
            builder.setPreSoldierId(this.getPreSoldierId());
            fieldCnt++;
        }  else if (builder.hasPreSoldierId()) {
            // 清理PreSoldierId
            builder.clearPreSoldierId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerSoldierUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERID)) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISLEVELUP)) {
            builder.setIsLevelUp(this.getIsLevelUp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PRESOLDIERID)) {
            builder.setPreSoldierId(this.getPreSoldierId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerSoldierUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierId()) {
            this.innerSetSoldierId(proto.getSoldierId());
        } else {
            this.innerSetSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIsLevelUp()) {
            this.innerSetIsLevelUp(proto.getIsLevelUp());
        } else {
            this.innerSetIsLevelUp(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasPreSoldierId()) {
            this.innerSetPreSoldierId(proto.getPreSoldierId());
        } else {
            this.innerSetPreSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerSoldierUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerSoldierUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierId()) {
            this.setSoldierId(proto.getSoldierId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        if (proto.hasIsLevelUp()) {
            this.setIsLevelUp(proto.getIsLevelUp());
            fieldCnt++;
        }
        if (proto.hasPreSoldierId()) {
            this.setPreSoldierId(proto.getPreSoldierId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerSoldierUnit.Builder builder = PlayerSoldierUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.soldierId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerSoldierUnitProp)) {
            return false;
        }
        final PlayerSoldierUnitProp otherNode = (PlayerSoldierUnitProp) node;
        if (this.soldierId != otherNode.soldierId) {
            return false;
        }
        if (this.num != otherNode.num) {
            return false;
        }
        if (this.isLevelUp != otherNode.isLevelUp) {
            return false;
        }
        if (this.preSoldierId != otherNode.preSoldierId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerResourceProduceMap;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.PlayerPB.PlayerResourceProduceMapPB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerResourceProduceMapProp extends AbstractPropNode {

    public static final int FIELD_INDEX_PRODUCEMAP = 0;
    public static final int FIELD_INDEX_NEWBIEPRODUCETAKEN = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private Int32ResourceProduceMapProp produceMap = null;
    private boolean newbieProduceTaken = Constant.DEFAULT_BOOLEAN_VALUE;

    public PlayerResourceProduceMapProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerResourceProduceMapProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get produceMap
     *
     * @return produceMap value
     */
    public Int32ResourceProduceMapProp getProduceMap() {
        if (this.produceMap == null) {
            this.produceMap = new Int32ResourceProduceMapProp(this, FIELD_INDEX_PRODUCEMAP);
        }
        return this.produceMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putProduceMapV(ResourceProduceProp v) {
        this.getProduceMap().put(v.getResourceId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ResourceProduceProp addEmptyProduceMap(Integer k) {
        return this.getProduceMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getProduceMapSize() {
        if (this.produceMap == null) {
            return 0;
        }
        return this.produceMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isProduceMapEmpty() {
        if (this.produceMap == null) {
            return true;
        }
        return this.produceMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ResourceProduceProp getProduceMapV(Integer k) {
        if (this.produceMap == null || !this.produceMap.containsKey(k)) {
            return null;
        }
        return this.produceMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearProduceMap() {
        if (this.produceMap != null) {
            this.produceMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeProduceMapV(Integer k) {
        if (this.produceMap != null) {
            this.produceMap.remove(k);
        }
    }
    /**
     * get newbieProduceTaken
     *
     * @return newbieProduceTaken value
     */
    public boolean getNewbieProduceTaken() {
        return this.newbieProduceTaken;
    }

    /**
     * set newbieProduceTaken && set marked
     *
     * @param newbieProduceTaken new value
     * @return current object
     */
    public PlayerResourceProduceMapProp setNewbieProduceTaken(boolean newbieProduceTaken) {
        if (this.newbieProduceTaken != newbieProduceTaken) {
            this.mark(FIELD_INDEX_NEWBIEPRODUCETAKEN);
            this.newbieProduceTaken = newbieProduceTaken;
        }
        return this;
    }

    /**
     * inner set newbieProduceTaken
     *
     * @param newbieProduceTaken new value
     */
    private void innerSetNewbieProduceTaken(boolean newbieProduceTaken) {
        this.newbieProduceTaken = newbieProduceTaken;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerResourceProduceMapPB.Builder getCopyCsBuilder() {
        final PlayerResourceProduceMapPB.Builder builder = PlayerResourceProduceMapPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerResourceProduceMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.produceMap != null) {
            StructPlayerPB.Int32ResourceProduceMapPB.Builder tmpBuilder = StructPlayerPB.Int32ResourceProduceMapPB.newBuilder();
            final int tmpFieldCnt = this.produceMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setProduceMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearProduceMap();
            }
        }  else if (builder.hasProduceMap()) {
            // 清理ProduceMap
            builder.clearProduceMap();
            fieldCnt++;
        }
        if (this.getNewbieProduceTaken()) {
            builder.setNewbieProduceTaken(this.getNewbieProduceTaken());
            fieldCnt++;
        }  else if (builder.hasNewbieProduceTaken()) {
            // 清理NewbieProduceTaken
            builder.clearNewbieProduceTaken();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerResourceProduceMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PRODUCEMAP) && this.produceMap != null) {
            final boolean needClear = !builder.hasProduceMap();
            final int tmpFieldCnt = this.produceMap.copyChangeToCs(builder.getProduceMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearProduceMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_NEWBIEPRODUCETAKEN)) {
            builder.setNewbieProduceTaken(this.getNewbieProduceTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerResourceProduceMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PRODUCEMAP) && this.produceMap != null) {
            final boolean needClear = !builder.hasProduceMap();
            final int tmpFieldCnt = this.produceMap.copyChangeToAndClearDeleteKeysCs(builder.getProduceMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearProduceMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_NEWBIEPRODUCETAKEN)) {
            builder.setNewbieProduceTaken(this.getNewbieProduceTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerResourceProduceMapPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasProduceMap()) {
            this.getProduceMap().mergeFromCs(proto.getProduceMap());
        } else {
            if (this.produceMap != null) {
                this.produceMap.mergeFromCs(proto.getProduceMap());
            }
        }
        if (proto.hasNewbieProduceTaken()) {
            this.innerSetNewbieProduceTaken(proto.getNewbieProduceTaken());
        } else {
            this.innerSetNewbieProduceTaken(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return PlayerResourceProduceMapProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerResourceProduceMapPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasProduceMap()) {
            this.getProduceMap().mergeChangeFromCs(proto.getProduceMap());
            fieldCnt++;
        }
        if (proto.hasNewbieProduceTaken()) {
            this.setNewbieProduceTaken(proto.getNewbieProduceTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerResourceProduceMap.Builder getCopyDbBuilder() {
        final PlayerResourceProduceMap.Builder builder = PlayerResourceProduceMap.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerResourceProduceMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.produceMap != null) {
            StructPlayer.Int32ResourceProduceMap.Builder tmpBuilder = StructPlayer.Int32ResourceProduceMap.newBuilder();
            final int tmpFieldCnt = this.produceMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setProduceMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearProduceMap();
            }
        }  else if (builder.hasProduceMap()) {
            // 清理ProduceMap
            builder.clearProduceMap();
            fieldCnt++;
        }
        if (this.getNewbieProduceTaken()) {
            builder.setNewbieProduceTaken(this.getNewbieProduceTaken());
            fieldCnt++;
        }  else if (builder.hasNewbieProduceTaken()) {
            // 清理NewbieProduceTaken
            builder.clearNewbieProduceTaken();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerResourceProduceMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PRODUCEMAP) && this.produceMap != null) {
            final boolean needClear = !builder.hasProduceMap();
            final int tmpFieldCnt = this.produceMap.copyChangeToDb(builder.getProduceMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearProduceMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_NEWBIEPRODUCETAKEN)) {
            builder.setNewbieProduceTaken(this.getNewbieProduceTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerResourceProduceMap proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasProduceMap()) {
            this.getProduceMap().mergeFromDb(proto.getProduceMap());
        } else {
            if (this.produceMap != null) {
                this.produceMap.mergeFromDb(proto.getProduceMap());
            }
        }
        if (proto.hasNewbieProduceTaken()) {
            this.innerSetNewbieProduceTaken(proto.getNewbieProduceTaken());
        } else {
            this.innerSetNewbieProduceTaken(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return PlayerResourceProduceMapProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerResourceProduceMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasProduceMap()) {
            this.getProduceMap().mergeChangeFromDb(proto.getProduceMap());
            fieldCnt++;
        }
        if (proto.hasNewbieProduceTaken()) {
            this.setNewbieProduceTaken(proto.getNewbieProduceTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerResourceProduceMap.Builder getCopySsBuilder() {
        final PlayerResourceProduceMap.Builder builder = PlayerResourceProduceMap.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerResourceProduceMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.produceMap != null) {
            StructPlayer.Int32ResourceProduceMap.Builder tmpBuilder = StructPlayer.Int32ResourceProduceMap.newBuilder();
            final int tmpFieldCnt = this.produceMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setProduceMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearProduceMap();
            }
        }  else if (builder.hasProduceMap()) {
            // 清理ProduceMap
            builder.clearProduceMap();
            fieldCnt++;
        }
        if (this.getNewbieProduceTaken()) {
            builder.setNewbieProduceTaken(this.getNewbieProduceTaken());
            fieldCnt++;
        }  else if (builder.hasNewbieProduceTaken()) {
            // 清理NewbieProduceTaken
            builder.clearNewbieProduceTaken();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerResourceProduceMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PRODUCEMAP) && this.produceMap != null) {
            final boolean needClear = !builder.hasProduceMap();
            final int tmpFieldCnt = this.produceMap.copyChangeToSs(builder.getProduceMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearProduceMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_NEWBIEPRODUCETAKEN)) {
            builder.setNewbieProduceTaken(this.getNewbieProduceTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerResourceProduceMap proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasProduceMap()) {
            this.getProduceMap().mergeFromSs(proto.getProduceMap());
        } else {
            if (this.produceMap != null) {
                this.produceMap.mergeFromSs(proto.getProduceMap());
            }
        }
        if (proto.hasNewbieProduceTaken()) {
            this.innerSetNewbieProduceTaken(proto.getNewbieProduceTaken());
        } else {
            this.innerSetNewbieProduceTaken(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return PlayerResourceProduceMapProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerResourceProduceMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasProduceMap()) {
            this.getProduceMap().mergeChangeFromSs(proto.getProduceMap());
            fieldCnt++;
        }
        if (proto.hasNewbieProduceTaken()) {
            this.setNewbieProduceTaken(proto.getNewbieProduceTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerResourceProduceMap.Builder builder = PlayerResourceProduceMap.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_PRODUCEMAP) && this.produceMap != null) {
            this.produceMap.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.produceMap != null) {
            this.produceMap.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerResourceProduceMapProp)) {
            return false;
        }
        final PlayerResourceProduceMapProp otherNode = (PlayerResourceProduceMapProp) node;
        if (!this.getProduceMap().compareDataTo(otherNode.getProduceMap())) {
            return false;
        }
        if (this.newbieProduceTaken != otherNode.newbieProduceTaken) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructCommon.DungeonSkillSys;
import com.yorha.proto.StructCommon;
import com.yorha.proto.StructCommonPB.DungeonSkillSysPB;
import com.yorha.proto.StructCommonPB;


/**
 * <AUTHOR> auto gen
 */
public class DungeonSkillSysProp extends AbstractPropNode {

    public static final int FIELD_INDEX_SKILL = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32DungeonSkillItemMapProp skill = null;

    public DungeonSkillSysProp() {
        super(null, 0, FIELD_COUNT);
    }

    public DungeonSkillSysProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get skill
     *
     * @return skill value
     */
    public Int32DungeonSkillItemMapProp getSkill() {
        if (this.skill == null) {
            this.skill = new Int32DungeonSkillItemMapProp(this, FIELD_INDEX_SKILL);
        }
        return this.skill;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putSkillV(DungeonSkillItemProp v) {
        this.getSkill().put(v.getSkillId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public DungeonSkillItemProp addEmptySkill(Integer k) {
        return this.getSkill().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getSkillSize() {
        if (this.skill == null) {
            return 0;
        }
        return this.skill.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isSkillEmpty() {
        if (this.skill == null) {
            return true;
        }
        return this.skill.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public DungeonSkillItemProp getSkillV(Integer k) {
        if (this.skill == null || !this.skill.containsKey(k)) {
            return null;
        }
        return this.skill.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearSkill() {
        if (this.skill != null) {
            this.skill.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeSkillV(Integer k) {
        if (this.skill != null) {
            this.skill.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DungeonSkillSysPB.Builder getCopyCsBuilder() {
        final DungeonSkillSysPB.Builder builder = DungeonSkillSysPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(DungeonSkillSysPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.skill != null) {
            StructCommonPB.Int32DungeonSkillItemMapPB.Builder tmpBuilder = StructCommonPB.Int32DungeonSkillItemMapPB.newBuilder();
            final int tmpFieldCnt = this.skill.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkill(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkill();
            }
        }  else if (builder.hasSkill()) {
            // 清理Skill
            builder.clearSkill();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(DungeonSkillSysPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILL) && this.skill != null) {
            final boolean needClear = !builder.hasSkill();
            final int tmpFieldCnt = this.skill.copyChangeToCs(builder.getSkillBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkill();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(DungeonSkillSysPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILL) && this.skill != null) {
            final boolean needClear = !builder.hasSkill();
            final int tmpFieldCnt = this.skill.copyChangeToAndClearDeleteKeysCs(builder.getSkillBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkill();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(DungeonSkillSysPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkill()) {
            this.getSkill().mergeFromCs(proto.getSkill());
        } else {
            if (this.skill != null) {
                this.skill.mergeFromCs(proto.getSkill());
            }
        }
        this.markAll();
        return DungeonSkillSysProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(DungeonSkillSysPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkill()) {
            this.getSkill().mergeChangeFromCs(proto.getSkill());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DungeonSkillSys.Builder getCopyDbBuilder() {
        final DungeonSkillSys.Builder builder = DungeonSkillSys.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(DungeonSkillSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.skill != null) {
            StructCommon.Int32DungeonSkillItemMap.Builder tmpBuilder = StructCommon.Int32DungeonSkillItemMap.newBuilder();
            final int tmpFieldCnt = this.skill.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkill(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkill();
            }
        }  else if (builder.hasSkill()) {
            // 清理Skill
            builder.clearSkill();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(DungeonSkillSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILL) && this.skill != null) {
            final boolean needClear = !builder.hasSkill();
            final int tmpFieldCnt = this.skill.copyChangeToDb(builder.getSkillBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkill();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(DungeonSkillSys proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkill()) {
            this.getSkill().mergeFromDb(proto.getSkill());
        } else {
            if (this.skill != null) {
                this.skill.mergeFromDb(proto.getSkill());
            }
        }
        this.markAll();
        return DungeonSkillSysProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(DungeonSkillSys proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkill()) {
            this.getSkill().mergeChangeFromDb(proto.getSkill());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DungeonSkillSys.Builder getCopySsBuilder() {
        final DungeonSkillSys.Builder builder = DungeonSkillSys.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(DungeonSkillSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.skill != null) {
            StructCommon.Int32DungeonSkillItemMap.Builder tmpBuilder = StructCommon.Int32DungeonSkillItemMap.newBuilder();
            final int tmpFieldCnt = this.skill.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkill(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkill();
            }
        }  else if (builder.hasSkill()) {
            // 清理Skill
            builder.clearSkill();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(DungeonSkillSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILL) && this.skill != null) {
            final boolean needClear = !builder.hasSkill();
            final int tmpFieldCnt = this.skill.copyChangeToSs(builder.getSkillBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkill();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(DungeonSkillSys proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkill()) {
            this.getSkill().mergeFromSs(proto.getSkill());
        } else {
            if (this.skill != null) {
                this.skill.mergeFromSs(proto.getSkill());
            }
        }
        this.markAll();
        return DungeonSkillSysProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(DungeonSkillSys proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkill()) {
            this.getSkill().mergeChangeFromSs(proto.getSkill());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        DungeonSkillSys.Builder builder = DungeonSkillSys.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SKILL) && this.skill != null) {
            this.skill.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.skill != null) {
            this.skill.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof DungeonSkillSysProp)) {
            return false;
        }
        final DungeonSkillSysProp otherNode = (DungeonSkillSysProp) node;
        if (!this.getSkill().compareDataTo(otherNode.getSkill())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
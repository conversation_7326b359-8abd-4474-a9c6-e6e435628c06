package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Player.ScenePlayerPushNtfModel;
import com.yorha.proto.PlayerPB.ScenePlayerPushNtfModelPB;


/**
 * <AUTHOR> auto gen
 */
public class ScenePlayerPushNtfModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_INTLTOKENREFRESHTSMS = 0;
    public static final int FIELD_INDEX_INTLNTFTOKEN = 1;
    public static final int FIELD_INDEX_NOTIFICATIONMASK = 2;
    public static final int FIELD_INDEX_LANGUAGE = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private long intlTokenRefreshTsMs = Constant.DEFAULT_LONG_VALUE;
    private String intlNtfToken = Constant.DEFAULT_STR_VALUE;
    private long notificationMask = Constant.DEFAULT_LONG_VALUE;
    private Language language = Language.forNumber(0);

    public ScenePlayerPushNtfModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ScenePlayerPushNtfModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get intlTokenRefreshTsMs
     *
     * @return intlTokenRefreshTsMs value
     */
    public long getIntlTokenRefreshTsMs() {
        return this.intlTokenRefreshTsMs;
    }

    /**
     * set intlTokenRefreshTsMs && set marked
     *
     * @param intlTokenRefreshTsMs new value
     * @return current object
     */
    public ScenePlayerPushNtfModelProp setIntlTokenRefreshTsMs(long intlTokenRefreshTsMs) {
        if (this.intlTokenRefreshTsMs != intlTokenRefreshTsMs) {
            this.mark(FIELD_INDEX_INTLTOKENREFRESHTSMS);
            this.intlTokenRefreshTsMs = intlTokenRefreshTsMs;
        }
        return this;
    }

    /**
     * inner set intlTokenRefreshTsMs
     *
     * @param intlTokenRefreshTsMs new value
     */
    private void innerSetIntlTokenRefreshTsMs(long intlTokenRefreshTsMs) {
        this.intlTokenRefreshTsMs = intlTokenRefreshTsMs;
    }

    /**
     * get intlNtfToken
     *
     * @return intlNtfToken value
     */
    public String getIntlNtfToken() {
        return this.intlNtfToken;
    }

    /**
     * set intlNtfToken && set marked
     *
     * @param intlNtfToken new value
     * @return current object
     */
    public ScenePlayerPushNtfModelProp setIntlNtfToken(String intlNtfToken) {
        if (intlNtfToken == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.intlNtfToken, intlNtfToken)) {
            this.mark(FIELD_INDEX_INTLNTFTOKEN);
            this.intlNtfToken = intlNtfToken;
        }
        return this;
    }

    /**
     * inner set intlNtfToken
     *
     * @param intlNtfToken new value
     */
    private void innerSetIntlNtfToken(String intlNtfToken) {
        this.intlNtfToken = intlNtfToken;
    }

    /**
     * get notificationMask
     *
     * @return notificationMask value
     */
    public long getNotificationMask() {
        return this.notificationMask;
    }

    /**
     * set notificationMask && set marked
     *
     * @param notificationMask new value
     * @return current object
     */
    public ScenePlayerPushNtfModelProp setNotificationMask(long notificationMask) {
        if (this.notificationMask != notificationMask) {
            this.mark(FIELD_INDEX_NOTIFICATIONMASK);
            this.notificationMask = notificationMask;
        }
        return this;
    }

    /**
     * inner set notificationMask
     *
     * @param notificationMask new value
     */
    private void innerSetNotificationMask(long notificationMask) {
        this.notificationMask = notificationMask;
    }

    /**
     * get language
     *
     * @return language value
     */
    public Language getLanguage() {
        return this.language;
    }

    /**
     * set language && set marked
     *
     * @param language new value
     * @return current object
     */
    public ScenePlayerPushNtfModelProp setLanguage(Language language) {
        if (language == null) {
            throw new NullPointerException();
        }
        if (this.language != language) {
            this.mark(FIELD_INDEX_LANGUAGE);
            this.language = language;
        }
        return this;
    }

    /**
     * inner set language
     *
     * @param language new value
     */
    private void innerSetLanguage(Language language) {
        this.language = language;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerPushNtfModelPB.Builder getCopyCsBuilder() {
        final ScenePlayerPushNtfModelPB.Builder builder = ScenePlayerPushNtfModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ScenePlayerPushNtfModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ScenePlayerPushNtfModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ScenePlayerPushNtfModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ScenePlayerPushNtfModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        this.markAll();
        return ScenePlayerPushNtfModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ScenePlayerPushNtfModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerPushNtfModel.Builder getCopyDbBuilder() {
        final ScenePlayerPushNtfModel.Builder builder = ScenePlayerPushNtfModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ScenePlayerPushNtfModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIntlTokenRefreshTsMs() != 0L) {
            builder.setIntlTokenRefreshTsMs(this.getIntlTokenRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasIntlTokenRefreshTsMs()) {
            // 清理IntlTokenRefreshTsMs
            builder.clearIntlTokenRefreshTsMs();
            fieldCnt++;
        }
        if (!this.getIntlNtfToken().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setIntlNtfToken(this.getIntlNtfToken());
            fieldCnt++;
        }  else if (builder.hasIntlNtfToken()) {
            // 清理IntlNtfToken
            builder.clearIntlNtfToken();
            fieldCnt++;
        }
        if (this.getNotificationMask() != 0L) {
            builder.setNotificationMask(this.getNotificationMask());
            fieldCnt++;
        }  else if (builder.hasNotificationMask()) {
            // 清理NotificationMask
            builder.clearNotificationMask();
            fieldCnt++;
        }
        if (this.getLanguage() != Language.forNumber(0)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }  else if (builder.hasLanguage()) {
            // 清理Language
            builder.clearLanguage();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ScenePlayerPushNtfModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INTLTOKENREFRESHTSMS)) {
            builder.setIntlTokenRefreshTsMs(this.getIntlTokenRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INTLNTFTOKEN)) {
            builder.setIntlNtfToken(this.getIntlNtfToken());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NOTIFICATIONMASK)) {
            builder.setNotificationMask(this.getNotificationMask());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LANGUAGE)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ScenePlayerPushNtfModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIntlTokenRefreshTsMs()) {
            this.innerSetIntlTokenRefreshTsMs(proto.getIntlTokenRefreshTsMs());
        } else {
            this.innerSetIntlTokenRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIntlNtfToken()) {
            this.innerSetIntlNtfToken(proto.getIntlNtfToken());
        } else {
            this.innerSetIntlNtfToken(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasNotificationMask()) {
            this.innerSetNotificationMask(proto.getNotificationMask());
        } else {
            this.innerSetNotificationMask(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLanguage()) {
            this.innerSetLanguage(proto.getLanguage());
        } else {
            this.innerSetLanguage(Language.forNumber(0));
        }
        this.markAll();
        return ScenePlayerPushNtfModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ScenePlayerPushNtfModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIntlTokenRefreshTsMs()) {
            this.setIntlTokenRefreshTsMs(proto.getIntlTokenRefreshTsMs());
            fieldCnt++;
        }
        if (proto.hasIntlNtfToken()) {
            this.setIntlNtfToken(proto.getIntlNtfToken());
            fieldCnt++;
        }
        if (proto.hasNotificationMask()) {
            this.setNotificationMask(proto.getNotificationMask());
            fieldCnt++;
        }
        if (proto.hasLanguage()) {
            this.setLanguage(proto.getLanguage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerPushNtfModel.Builder getCopySsBuilder() {
        final ScenePlayerPushNtfModel.Builder builder = ScenePlayerPushNtfModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ScenePlayerPushNtfModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIntlTokenRefreshTsMs() != 0L) {
            builder.setIntlTokenRefreshTsMs(this.getIntlTokenRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasIntlTokenRefreshTsMs()) {
            // 清理IntlTokenRefreshTsMs
            builder.clearIntlTokenRefreshTsMs();
            fieldCnt++;
        }
        if (!this.getIntlNtfToken().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setIntlNtfToken(this.getIntlNtfToken());
            fieldCnt++;
        }  else if (builder.hasIntlNtfToken()) {
            // 清理IntlNtfToken
            builder.clearIntlNtfToken();
            fieldCnt++;
        }
        if (this.getNotificationMask() != 0L) {
            builder.setNotificationMask(this.getNotificationMask());
            fieldCnt++;
        }  else if (builder.hasNotificationMask()) {
            // 清理NotificationMask
            builder.clearNotificationMask();
            fieldCnt++;
        }
        if (this.getLanguage() != Language.forNumber(0)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }  else if (builder.hasLanguage()) {
            // 清理Language
            builder.clearLanguage();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ScenePlayerPushNtfModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INTLTOKENREFRESHTSMS)) {
            builder.setIntlTokenRefreshTsMs(this.getIntlTokenRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INTLNTFTOKEN)) {
            builder.setIntlNtfToken(this.getIntlNtfToken());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NOTIFICATIONMASK)) {
            builder.setNotificationMask(this.getNotificationMask());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LANGUAGE)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ScenePlayerPushNtfModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIntlTokenRefreshTsMs()) {
            this.innerSetIntlTokenRefreshTsMs(proto.getIntlTokenRefreshTsMs());
        } else {
            this.innerSetIntlTokenRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIntlNtfToken()) {
            this.innerSetIntlNtfToken(proto.getIntlNtfToken());
        } else {
            this.innerSetIntlNtfToken(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasNotificationMask()) {
            this.innerSetNotificationMask(proto.getNotificationMask());
        } else {
            this.innerSetNotificationMask(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLanguage()) {
            this.innerSetLanguage(proto.getLanguage());
        } else {
            this.innerSetLanguage(Language.forNumber(0));
        }
        this.markAll();
        return ScenePlayerPushNtfModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ScenePlayerPushNtfModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIntlTokenRefreshTsMs()) {
            this.setIntlTokenRefreshTsMs(proto.getIntlTokenRefreshTsMs());
            fieldCnt++;
        }
        if (proto.hasIntlNtfToken()) {
            this.setIntlNtfToken(proto.getIntlNtfToken());
            fieldCnt++;
        }
        if (proto.hasNotificationMask()) {
            this.setNotificationMask(proto.getNotificationMask());
            fieldCnt++;
        }
        if (proto.hasLanguage()) {
            this.setLanguage(proto.getLanguage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ScenePlayerPushNtfModel.Builder builder = ScenePlayerPushNtfModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ScenePlayerPushNtfModelProp)) {
            return false;
        }
        final ScenePlayerPushNtfModelProp otherNode = (ScenePlayerPushNtfModelProp) node;
        if (this.intlTokenRefreshTsMs != otherNode.intlTokenRefreshTsMs) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.intlNtfToken, otherNode.intlNtfToken)) {
            return false;
        }
        if (this.notificationMask != otherNode.notificationMask) {
            return false;
        }
        if (this.language != otherNode.language) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
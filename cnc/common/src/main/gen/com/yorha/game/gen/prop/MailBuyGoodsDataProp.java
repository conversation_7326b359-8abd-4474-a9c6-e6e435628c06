package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructMail.MailBuyGoodsData;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMailPB.MailBuyGoodsDataPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class MailBuyGoodsDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CHARGEGOODSID = 0;
    public static final int FIELD_INDEX_SELECTITEMS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int chargeGoodsId = Constant.DEFAULT_INT_VALUE;
    private ItemPairListProp selectItems = null;

    public MailBuyGoodsDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MailBuyGoodsDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get chargeGoodsId
     *
     * @return chargeGoodsId value
     */
    public int getChargeGoodsId() {
        return this.chargeGoodsId;
    }

    /**
     * set chargeGoodsId && set marked
     *
     * @param chargeGoodsId new value
     * @return current object
     */
    public MailBuyGoodsDataProp setChargeGoodsId(int chargeGoodsId) {
        if (this.chargeGoodsId != chargeGoodsId) {
            this.mark(FIELD_INDEX_CHARGEGOODSID);
            this.chargeGoodsId = chargeGoodsId;
        }
        return this;
    }

    /**
     * inner set chargeGoodsId
     *
     * @param chargeGoodsId new value
     */
    private void innerSetChargeGoodsId(int chargeGoodsId) {
        this.chargeGoodsId = chargeGoodsId;
    }

    /**
     * get selectItems
     *
     * @return selectItems value
     */
    public ItemPairListProp getSelectItems() {
        if (this.selectItems == null) {
            this.selectItems = new ItemPairListProp(this, FIELD_INDEX_SELECTITEMS);
        }
        return this.selectItems;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addSelectItems(ItemPairProp v) {
        this.getSelectItems().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ItemPairProp getSelectItemsIndex(int index) {
        return this.getSelectItems().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public ItemPairProp removeSelectItems(ItemPairProp v) {
        if (this.selectItems == null) {
            return null;
        }
        if(this.selectItems.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getSelectItemsSize() {
        if (this.selectItems == null) {
            return 0;
        }
        return this.selectItems.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isSelectItemsEmpty() {
        if (this.selectItems == null) {
            return true;
        }
        return this.getSelectItems().isEmpty();
    }

    /**
     * clear list
     */
    public void clearSelectItems() {
        this.getSelectItems().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ItemPairProp removeSelectItemsIndex(int index) {
        return this.getSelectItems().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public ItemPairProp setSelectItemsIndex(int index, ItemPairProp v) {
        return this.getSelectItems().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailBuyGoodsDataPB.Builder getCopyCsBuilder() {
        final MailBuyGoodsDataPB.Builder builder = MailBuyGoodsDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MailBuyGoodsDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getChargeGoodsId() != 0) {
            builder.setChargeGoodsId(this.getChargeGoodsId());
            fieldCnt++;
        }  else if (builder.hasChargeGoodsId()) {
            // 清理ChargeGoodsId
            builder.clearChargeGoodsId();
            fieldCnt++;
        }
        if (this.selectItems != null) {
            StructPB.ItemPairListPB.Builder tmpBuilder = StructPB.ItemPairListPB.newBuilder();
            final int tmpFieldCnt = this.selectItems.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSelectItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSelectItems();
            }
        }  else if (builder.hasSelectItems()) {
            // 清理SelectItems
            builder.clearSelectItems();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MailBuyGoodsDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHARGEGOODSID)) {
            builder.setChargeGoodsId(this.getChargeGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SELECTITEMS) && this.selectItems != null) {
            final boolean needClear = !builder.hasSelectItems();
            final int tmpFieldCnt = this.selectItems.copyChangeToCs(builder.getSelectItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSelectItems();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MailBuyGoodsDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHARGEGOODSID)) {
            builder.setChargeGoodsId(this.getChargeGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SELECTITEMS) && this.selectItems != null) {
            final boolean needClear = !builder.hasSelectItems();
            final int tmpFieldCnt = this.selectItems.copyChangeToCs(builder.getSelectItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSelectItems();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MailBuyGoodsDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChargeGoodsId()) {
            this.innerSetChargeGoodsId(proto.getChargeGoodsId());
        } else {
            this.innerSetChargeGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSelectItems()) {
            this.getSelectItems().mergeFromCs(proto.getSelectItems());
        } else {
            if (this.selectItems != null) {
                this.selectItems.mergeFromCs(proto.getSelectItems());
            }
        }
        this.markAll();
        return MailBuyGoodsDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MailBuyGoodsDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChargeGoodsId()) {
            this.setChargeGoodsId(proto.getChargeGoodsId());
            fieldCnt++;
        }
        if (proto.hasSelectItems()) {
            this.getSelectItems().mergeChangeFromCs(proto.getSelectItems());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailBuyGoodsData.Builder getCopyDbBuilder() {
        final MailBuyGoodsData.Builder builder = MailBuyGoodsData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MailBuyGoodsData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getChargeGoodsId() != 0) {
            builder.setChargeGoodsId(this.getChargeGoodsId());
            fieldCnt++;
        }  else if (builder.hasChargeGoodsId()) {
            // 清理ChargeGoodsId
            builder.clearChargeGoodsId();
            fieldCnt++;
        }
        if (this.selectItems != null) {
            Struct.ItemPairList.Builder tmpBuilder = Struct.ItemPairList.newBuilder();
            final int tmpFieldCnt = this.selectItems.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSelectItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSelectItems();
            }
        }  else if (builder.hasSelectItems()) {
            // 清理SelectItems
            builder.clearSelectItems();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MailBuyGoodsData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHARGEGOODSID)) {
            builder.setChargeGoodsId(this.getChargeGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SELECTITEMS) && this.selectItems != null) {
            final boolean needClear = !builder.hasSelectItems();
            final int tmpFieldCnt = this.selectItems.copyChangeToDb(builder.getSelectItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSelectItems();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MailBuyGoodsData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChargeGoodsId()) {
            this.innerSetChargeGoodsId(proto.getChargeGoodsId());
        } else {
            this.innerSetChargeGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSelectItems()) {
            this.getSelectItems().mergeFromDb(proto.getSelectItems());
        } else {
            if (this.selectItems != null) {
                this.selectItems.mergeFromDb(proto.getSelectItems());
            }
        }
        this.markAll();
        return MailBuyGoodsDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MailBuyGoodsData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChargeGoodsId()) {
            this.setChargeGoodsId(proto.getChargeGoodsId());
            fieldCnt++;
        }
        if (proto.hasSelectItems()) {
            this.getSelectItems().mergeChangeFromDb(proto.getSelectItems());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailBuyGoodsData.Builder getCopySsBuilder() {
        final MailBuyGoodsData.Builder builder = MailBuyGoodsData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MailBuyGoodsData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getChargeGoodsId() != 0) {
            builder.setChargeGoodsId(this.getChargeGoodsId());
            fieldCnt++;
        }  else if (builder.hasChargeGoodsId()) {
            // 清理ChargeGoodsId
            builder.clearChargeGoodsId();
            fieldCnt++;
        }
        if (this.selectItems != null) {
            Struct.ItemPairList.Builder tmpBuilder = Struct.ItemPairList.newBuilder();
            final int tmpFieldCnt = this.selectItems.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSelectItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSelectItems();
            }
        }  else if (builder.hasSelectItems()) {
            // 清理SelectItems
            builder.clearSelectItems();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MailBuyGoodsData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHARGEGOODSID)) {
            builder.setChargeGoodsId(this.getChargeGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SELECTITEMS) && this.selectItems != null) {
            final boolean needClear = !builder.hasSelectItems();
            final int tmpFieldCnt = this.selectItems.copyChangeToSs(builder.getSelectItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSelectItems();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MailBuyGoodsData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChargeGoodsId()) {
            this.innerSetChargeGoodsId(proto.getChargeGoodsId());
        } else {
            this.innerSetChargeGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSelectItems()) {
            this.getSelectItems().mergeFromSs(proto.getSelectItems());
        } else {
            if (this.selectItems != null) {
                this.selectItems.mergeFromSs(proto.getSelectItems());
            }
        }
        this.markAll();
        return MailBuyGoodsDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MailBuyGoodsData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChargeGoodsId()) {
            this.setChargeGoodsId(proto.getChargeGoodsId());
            fieldCnt++;
        }
        if (proto.hasSelectItems()) {
            this.getSelectItems().mergeChangeFromSs(proto.getSelectItems());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MailBuyGoodsData.Builder builder = MailBuyGoodsData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SELECTITEMS) && this.selectItems != null) {
            this.selectItems.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.selectItems != null) {
            this.selectItems.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MailBuyGoodsDataProp)) {
            return false;
        }
        final MailBuyGoodsDataProp otherNode = (MailBuyGoodsDataProp) node;
        if (this.chargeGoodsId != otherNode.chargeGoodsId) {
            return false;
        }
        if (!this.getSelectItems().compareDataTo(otherNode.getSelectItems())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructPB.DisplayParamListPB;
import com.yorha.proto.Struct.DisplayParamList;
import com.yorha.proto.StructPB.DisplayParamPB;
import com.yorha.proto.Struct.DisplayParam;

/**
 * <AUTHOR> auto gen
 */
public class DisplayParamListProp extends AbstractListNode<DisplayParamProp> {
    /**
     * Create a DisplayParamListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public DisplayParamListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to DisplayParamListProp
     *
     * @return new object
     */
    @Override
    public DisplayParamProp addEmptyValue() {
        final DisplayParamProp newProp = new DisplayParamProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DisplayParamListPB.Builder getCopyCsBuilder() {
        final DisplayParamListPB.Builder builder = DisplayParamListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(DisplayParamListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return DisplayParamListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final DisplayParamProp v : this) {
            final DisplayParamPB.Builder itemBuilder = DisplayParamPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return DisplayParamListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(DisplayParamListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return DisplayParamListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(DisplayParamListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (DisplayParamPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return DisplayParamListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(DisplayParamListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DisplayParamList.Builder getCopyDbBuilder() {
        final DisplayParamList.Builder builder = DisplayParamList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(DisplayParamList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return DisplayParamListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final DisplayParamProp v : this) {
            final DisplayParam.Builder itemBuilder = DisplayParam.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return DisplayParamListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(DisplayParamList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return DisplayParamListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(DisplayParamList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (DisplayParam v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return DisplayParamListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(DisplayParamList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DisplayParamList.Builder getCopySsBuilder() {
        final DisplayParamList.Builder builder = DisplayParamList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(DisplayParamList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return DisplayParamListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final DisplayParamProp v : this) {
            final DisplayParam.Builder itemBuilder = DisplayParam.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return DisplayParamListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(DisplayParamList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return DisplayParamListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(DisplayParamList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (DisplayParam v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return DisplayParamListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(DisplayParamList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        DisplayParamList.Builder builder = DisplayParamList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
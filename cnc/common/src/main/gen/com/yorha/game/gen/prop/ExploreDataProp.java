package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ExploreData;
import com.yorha.proto.StructPB.ExploreDataPB;


/**
 * <AUTHOR> auto gen
 */
public class ExploreDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CONFIGID = 0;
    public static final int FIELD_INDEX_PARTID = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int configId = Constant.DEFAULT_INT_VALUE;
    private int partId = Constant.DEFAULT_INT_VALUE;

    public ExploreDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ExploreDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get configId
     *
     * @return configId value
     */
    public int getConfigId() {
        return this.configId;
    }

    /**
     * set configId && set marked
     *
     * @param configId new value
     * @return current object
     */
    public ExploreDataProp setConfigId(int configId) {
        if (this.configId != configId) {
            this.mark(FIELD_INDEX_CONFIGID);
            this.configId = configId;
        }
        return this;
    }

    /**
     * inner set configId
     *
     * @param configId new value
     */
    private void innerSetConfigId(int configId) {
        this.configId = configId;
    }

    /**
     * get partId
     *
     * @return partId value
     */
    public int getPartId() {
        return this.partId;
    }

    /**
     * set partId && set marked
     *
     * @param partId new value
     * @return current object
     */
    public ExploreDataProp setPartId(int partId) {
        if (this.partId != partId) {
            this.mark(FIELD_INDEX_PARTID);
            this.partId = partId;
        }
        return this;
    }

    /**
     * inner set partId
     *
     * @param partId new value
     */
    private void innerSetPartId(int partId) {
        this.partId = partId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ExploreDataPB.Builder getCopyCsBuilder() {
        final ExploreDataPB.Builder builder = ExploreDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ExploreDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getConfigId() != 0) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }  else if (builder.hasConfigId()) {
            // 清理ConfigId
            builder.clearConfigId();
            fieldCnt++;
        }
        if (this.getPartId() != 0) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }  else if (builder.hasPartId()) {
            // 清理PartId
            builder.clearPartId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ExploreDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONFIGID)) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PARTID)) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ExploreDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONFIGID)) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PARTID)) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ExploreDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasConfigId()) {
            this.innerSetConfigId(proto.getConfigId());
        } else {
            this.innerSetConfigId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPartId()) {
            this.innerSetPartId(proto.getPartId());
        } else {
            this.innerSetPartId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ExploreDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ExploreDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasConfigId()) {
            this.setConfigId(proto.getConfigId());
            fieldCnt++;
        }
        if (proto.hasPartId()) {
            this.setPartId(proto.getPartId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ExploreData.Builder getCopyDbBuilder() {
        final ExploreData.Builder builder = ExploreData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ExploreData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getConfigId() != 0) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }  else if (builder.hasConfigId()) {
            // 清理ConfigId
            builder.clearConfigId();
            fieldCnt++;
        }
        if (this.getPartId() != 0) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }  else if (builder.hasPartId()) {
            // 清理PartId
            builder.clearPartId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ExploreData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONFIGID)) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PARTID)) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ExploreData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasConfigId()) {
            this.innerSetConfigId(proto.getConfigId());
        } else {
            this.innerSetConfigId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPartId()) {
            this.innerSetPartId(proto.getPartId());
        } else {
            this.innerSetPartId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ExploreDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ExploreData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasConfigId()) {
            this.setConfigId(proto.getConfigId());
            fieldCnt++;
        }
        if (proto.hasPartId()) {
            this.setPartId(proto.getPartId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ExploreData.Builder getCopySsBuilder() {
        final ExploreData.Builder builder = ExploreData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ExploreData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getConfigId() != 0) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }  else if (builder.hasConfigId()) {
            // 清理ConfigId
            builder.clearConfigId();
            fieldCnt++;
        }
        if (this.getPartId() != 0) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }  else if (builder.hasPartId()) {
            // 清理PartId
            builder.clearPartId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ExploreData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONFIGID)) {
            builder.setConfigId(this.getConfigId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PARTID)) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ExploreData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasConfigId()) {
            this.innerSetConfigId(proto.getConfigId());
        } else {
            this.innerSetConfigId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPartId()) {
            this.innerSetPartId(proto.getPartId());
        } else {
            this.innerSetPartId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ExploreDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ExploreData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasConfigId()) {
            this.setConfigId(proto.getConfigId());
            fieldCnt++;
        }
        if (proto.hasPartId()) {
            this.setPartId(proto.getPartId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ExploreData.Builder builder = ExploreData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ExploreDataProp)) {
            return false;
        }
        final ExploreDataProp otherNode = (ExploreDataProp) node;
        if (this.configId != otherNode.configId) {
            return false;
        }
        if (this.partId != otherNode.partId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
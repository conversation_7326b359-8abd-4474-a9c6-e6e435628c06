package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerEnergyModel;
import com.yorha.proto.PlayerPB.PlayerEnergyModelPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerEnergyModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ENERGY = 0;
    public static final int FIELD_INDEX_LASTRECOVERSEC = 1;
    public static final int FIELD_INDEX_DAILYFREEENERGYTAKEN = 2;
    public static final int FIELD_INDEX_DAILYBUYTIMES = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int energy = Constant.DEFAULT_INT_VALUE;
    private int lastRecoverSec = Constant.DEFAULT_INT_VALUE;
    private boolean dailyFreeEnergyTaken = Constant.DEFAULT_BOOLEAN_VALUE;
    private int dailyBuyTimes = Constant.DEFAULT_INT_VALUE;

    public PlayerEnergyModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerEnergyModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get energy
     *
     * @return energy value
     */
    public int getEnergy() {
        return this.energy;
    }

    /**
     * set energy && set marked
     *
     * @param energy new value
     * @return current object
     */
    public PlayerEnergyModelProp setEnergy(int energy) {
        if (this.energy != energy) {
            this.mark(FIELD_INDEX_ENERGY);
            this.energy = energy;
        }
        return this;
    }

    /**
     * inner set energy
     *
     * @param energy new value
     */
    private void innerSetEnergy(int energy) {
        this.energy = energy;
    }

    /**
     * get lastRecoverSec
     *
     * @return lastRecoverSec value
     */
    public int getLastRecoverSec() {
        return this.lastRecoverSec;
    }

    /**
     * set lastRecoverSec && set marked
     *
     * @param lastRecoverSec new value
     * @return current object
     */
    public PlayerEnergyModelProp setLastRecoverSec(int lastRecoverSec) {
        if (this.lastRecoverSec != lastRecoverSec) {
            this.mark(FIELD_INDEX_LASTRECOVERSEC);
            this.lastRecoverSec = lastRecoverSec;
        }
        return this;
    }

    /**
     * inner set lastRecoverSec
     *
     * @param lastRecoverSec new value
     */
    private void innerSetLastRecoverSec(int lastRecoverSec) {
        this.lastRecoverSec = lastRecoverSec;
    }

    /**
     * get dailyFreeEnergyTaken
     *
     * @return dailyFreeEnergyTaken value
     */
    public boolean getDailyFreeEnergyTaken() {
        return this.dailyFreeEnergyTaken;
    }

    /**
     * set dailyFreeEnergyTaken && set marked
     *
     * @param dailyFreeEnergyTaken new value
     * @return current object
     */
    public PlayerEnergyModelProp setDailyFreeEnergyTaken(boolean dailyFreeEnergyTaken) {
        if (this.dailyFreeEnergyTaken != dailyFreeEnergyTaken) {
            this.mark(FIELD_INDEX_DAILYFREEENERGYTAKEN);
            this.dailyFreeEnergyTaken = dailyFreeEnergyTaken;
        }
        return this;
    }

    /**
     * inner set dailyFreeEnergyTaken
     *
     * @param dailyFreeEnergyTaken new value
     */
    private void innerSetDailyFreeEnergyTaken(boolean dailyFreeEnergyTaken) {
        this.dailyFreeEnergyTaken = dailyFreeEnergyTaken;
    }

    /**
     * get dailyBuyTimes
     *
     * @return dailyBuyTimes value
     */
    public int getDailyBuyTimes() {
        return this.dailyBuyTimes;
    }

    /**
     * set dailyBuyTimes && set marked
     *
     * @param dailyBuyTimes new value
     * @return current object
     */
    public PlayerEnergyModelProp setDailyBuyTimes(int dailyBuyTimes) {
        if (this.dailyBuyTimes != dailyBuyTimes) {
            this.mark(FIELD_INDEX_DAILYBUYTIMES);
            this.dailyBuyTimes = dailyBuyTimes;
        }
        return this;
    }

    /**
     * inner set dailyBuyTimes
     *
     * @param dailyBuyTimes new value
     */
    private void innerSetDailyBuyTimes(int dailyBuyTimes) {
        this.dailyBuyTimes = dailyBuyTimes;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerEnergyModelPB.Builder getCopyCsBuilder() {
        final PlayerEnergyModelPB.Builder builder = PlayerEnergyModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerEnergyModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getEnergy() != 0) {
            builder.setEnergy(this.getEnergy());
            fieldCnt++;
        }  else if (builder.hasEnergy()) {
            // 清理Energy
            builder.clearEnergy();
            fieldCnt++;
        }
        if (this.getLastRecoverSec() != 0) {
            builder.setLastRecoverSec(this.getLastRecoverSec());
            fieldCnt++;
        }  else if (builder.hasLastRecoverSec()) {
            // 清理LastRecoverSec
            builder.clearLastRecoverSec();
            fieldCnt++;
        }
        if (this.getDailyFreeEnergyTaken()) {
            builder.setDailyFreeEnergyTaken(this.getDailyFreeEnergyTaken());
            fieldCnt++;
        }  else if (builder.hasDailyFreeEnergyTaken()) {
            // 清理DailyFreeEnergyTaken
            builder.clearDailyFreeEnergyTaken();
            fieldCnt++;
        }
        if (this.getDailyBuyTimes() != 0) {
            builder.setDailyBuyTimes(this.getDailyBuyTimes());
            fieldCnt++;
        }  else if (builder.hasDailyBuyTimes()) {
            // 清理DailyBuyTimes
            builder.clearDailyBuyTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerEnergyModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ENERGY)) {
            builder.setEnergy(this.getEnergy());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTRECOVERSEC)) {
            builder.setLastRecoverSec(this.getLastRecoverSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYFREEENERGYTAKEN)) {
            builder.setDailyFreeEnergyTaken(this.getDailyFreeEnergyTaken());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYBUYTIMES)) {
            builder.setDailyBuyTimes(this.getDailyBuyTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerEnergyModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ENERGY)) {
            builder.setEnergy(this.getEnergy());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTRECOVERSEC)) {
            builder.setLastRecoverSec(this.getLastRecoverSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYFREEENERGYTAKEN)) {
            builder.setDailyFreeEnergyTaken(this.getDailyFreeEnergyTaken());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYBUYTIMES)) {
            builder.setDailyBuyTimes(this.getDailyBuyTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerEnergyModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasEnergy()) {
            this.innerSetEnergy(proto.getEnergy());
        } else {
            this.innerSetEnergy(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastRecoverSec()) {
            this.innerSetLastRecoverSec(proto.getLastRecoverSec());
        } else {
            this.innerSetLastRecoverSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDailyFreeEnergyTaken()) {
            this.innerSetDailyFreeEnergyTaken(proto.getDailyFreeEnergyTaken());
        } else {
            this.innerSetDailyFreeEnergyTaken(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasDailyBuyTimes()) {
            this.innerSetDailyBuyTimes(proto.getDailyBuyTimes());
        } else {
            this.innerSetDailyBuyTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerEnergyModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerEnergyModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasEnergy()) {
            this.setEnergy(proto.getEnergy());
            fieldCnt++;
        }
        if (proto.hasLastRecoverSec()) {
            this.setLastRecoverSec(proto.getLastRecoverSec());
            fieldCnt++;
        }
        if (proto.hasDailyFreeEnergyTaken()) {
            this.setDailyFreeEnergyTaken(proto.getDailyFreeEnergyTaken());
            fieldCnt++;
        }
        if (proto.hasDailyBuyTimes()) {
            this.setDailyBuyTimes(proto.getDailyBuyTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerEnergyModel.Builder getCopyDbBuilder() {
        final PlayerEnergyModel.Builder builder = PlayerEnergyModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerEnergyModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getEnergy() != 0) {
            builder.setEnergy(this.getEnergy());
            fieldCnt++;
        }  else if (builder.hasEnergy()) {
            // 清理Energy
            builder.clearEnergy();
            fieldCnt++;
        }
        if (this.getLastRecoverSec() != 0) {
            builder.setLastRecoverSec(this.getLastRecoverSec());
            fieldCnt++;
        }  else if (builder.hasLastRecoverSec()) {
            // 清理LastRecoverSec
            builder.clearLastRecoverSec();
            fieldCnt++;
        }
        if (this.getDailyFreeEnergyTaken()) {
            builder.setDailyFreeEnergyTaken(this.getDailyFreeEnergyTaken());
            fieldCnt++;
        }  else if (builder.hasDailyFreeEnergyTaken()) {
            // 清理DailyFreeEnergyTaken
            builder.clearDailyFreeEnergyTaken();
            fieldCnt++;
        }
        if (this.getDailyBuyTimes() != 0) {
            builder.setDailyBuyTimes(this.getDailyBuyTimes());
            fieldCnt++;
        }  else if (builder.hasDailyBuyTimes()) {
            // 清理DailyBuyTimes
            builder.clearDailyBuyTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerEnergyModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ENERGY)) {
            builder.setEnergy(this.getEnergy());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTRECOVERSEC)) {
            builder.setLastRecoverSec(this.getLastRecoverSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYFREEENERGYTAKEN)) {
            builder.setDailyFreeEnergyTaken(this.getDailyFreeEnergyTaken());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYBUYTIMES)) {
            builder.setDailyBuyTimes(this.getDailyBuyTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerEnergyModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasEnergy()) {
            this.innerSetEnergy(proto.getEnergy());
        } else {
            this.innerSetEnergy(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastRecoverSec()) {
            this.innerSetLastRecoverSec(proto.getLastRecoverSec());
        } else {
            this.innerSetLastRecoverSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDailyFreeEnergyTaken()) {
            this.innerSetDailyFreeEnergyTaken(proto.getDailyFreeEnergyTaken());
        } else {
            this.innerSetDailyFreeEnergyTaken(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasDailyBuyTimes()) {
            this.innerSetDailyBuyTimes(proto.getDailyBuyTimes());
        } else {
            this.innerSetDailyBuyTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerEnergyModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerEnergyModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasEnergy()) {
            this.setEnergy(proto.getEnergy());
            fieldCnt++;
        }
        if (proto.hasLastRecoverSec()) {
            this.setLastRecoverSec(proto.getLastRecoverSec());
            fieldCnt++;
        }
        if (proto.hasDailyFreeEnergyTaken()) {
            this.setDailyFreeEnergyTaken(proto.getDailyFreeEnergyTaken());
            fieldCnt++;
        }
        if (proto.hasDailyBuyTimes()) {
            this.setDailyBuyTimes(proto.getDailyBuyTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerEnergyModel.Builder getCopySsBuilder() {
        final PlayerEnergyModel.Builder builder = PlayerEnergyModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerEnergyModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getEnergy() != 0) {
            builder.setEnergy(this.getEnergy());
            fieldCnt++;
        }  else if (builder.hasEnergy()) {
            // 清理Energy
            builder.clearEnergy();
            fieldCnt++;
        }
        if (this.getLastRecoverSec() != 0) {
            builder.setLastRecoverSec(this.getLastRecoverSec());
            fieldCnt++;
        }  else if (builder.hasLastRecoverSec()) {
            // 清理LastRecoverSec
            builder.clearLastRecoverSec();
            fieldCnt++;
        }
        if (this.getDailyFreeEnergyTaken()) {
            builder.setDailyFreeEnergyTaken(this.getDailyFreeEnergyTaken());
            fieldCnt++;
        }  else if (builder.hasDailyFreeEnergyTaken()) {
            // 清理DailyFreeEnergyTaken
            builder.clearDailyFreeEnergyTaken();
            fieldCnt++;
        }
        if (this.getDailyBuyTimes() != 0) {
            builder.setDailyBuyTimes(this.getDailyBuyTimes());
            fieldCnt++;
        }  else if (builder.hasDailyBuyTimes()) {
            // 清理DailyBuyTimes
            builder.clearDailyBuyTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerEnergyModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ENERGY)) {
            builder.setEnergy(this.getEnergy());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTRECOVERSEC)) {
            builder.setLastRecoverSec(this.getLastRecoverSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYFREEENERGYTAKEN)) {
            builder.setDailyFreeEnergyTaken(this.getDailyFreeEnergyTaken());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYBUYTIMES)) {
            builder.setDailyBuyTimes(this.getDailyBuyTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerEnergyModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasEnergy()) {
            this.innerSetEnergy(proto.getEnergy());
        } else {
            this.innerSetEnergy(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastRecoverSec()) {
            this.innerSetLastRecoverSec(proto.getLastRecoverSec());
        } else {
            this.innerSetLastRecoverSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDailyFreeEnergyTaken()) {
            this.innerSetDailyFreeEnergyTaken(proto.getDailyFreeEnergyTaken());
        } else {
            this.innerSetDailyFreeEnergyTaken(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasDailyBuyTimes()) {
            this.innerSetDailyBuyTimes(proto.getDailyBuyTimes());
        } else {
            this.innerSetDailyBuyTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerEnergyModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerEnergyModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasEnergy()) {
            this.setEnergy(proto.getEnergy());
            fieldCnt++;
        }
        if (proto.hasLastRecoverSec()) {
            this.setLastRecoverSec(proto.getLastRecoverSec());
            fieldCnt++;
        }
        if (proto.hasDailyFreeEnergyTaken()) {
            this.setDailyFreeEnergyTaken(proto.getDailyFreeEnergyTaken());
            fieldCnt++;
        }
        if (proto.hasDailyBuyTimes()) {
            this.setDailyBuyTimes(proto.getDailyBuyTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerEnergyModel.Builder builder = PlayerEnergyModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerEnergyModelProp)) {
            return false;
        }
        final PlayerEnergyModelProp otherNode = (PlayerEnergyModelProp) node;
        if (this.energy != otherNode.energy) {
            return false;
        }
        if (this.lastRecoverSec != otherNode.lastRecoverSec) {
            return false;
        }
        if (this.dailyFreeEnergyTaken != otherNode.dailyFreeEnergyTaken) {
            return false;
        }
        if (this.dailyBuyTimes != otherNode.dailyBuyTimes) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
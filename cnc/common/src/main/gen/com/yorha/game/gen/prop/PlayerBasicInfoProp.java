package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerBasicInfo;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.PlayerBasicInfoPB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerBasicInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_LASTLOGINTSMS = 0;
    public static final int FIELD_INDEX_LASTLOGOUTTSMS = 1;
    public static final int FIELD_INDEX_ROLESEQOFACCOUNT = 2;
    public static final int FIELD_INDEX_LASTUPDATELOGINDAYSTSMS = 3;
    public static final int FIELD_INDEX_ONLINETIMEMS = 4;
    public static final int FIELD_INDEX_BANINFO = 5;
    public static final int FIELD_INDEX_INTERNALPLAYERTAG = 6;
    public static final int FIELD_INDEX_MIDASPF = 7;
    public static final int FIELD_INDEX_BORNPLATID = 8;
    public static final int FIELD_INDEX_LOGOFF = 9;
    public static final int FIELD_INDEX_REGCHANNEL = 10;

    public static final int FIELD_COUNT = 11;

    private long markBits0 = 0L;

    private long lastLoginTsMs = Constant.DEFAULT_LONG_VALUE;
    private long lastLogoutTsMs = Constant.DEFAULT_LONG_VALUE;
    private int roleSeqOfAccount = Constant.DEFAULT_INT_VALUE;
    private long lastUpdateLoginDaysTsMs = Constant.DEFAULT_LONG_VALUE;
    private long onlineTimeMs = Constant.DEFAULT_LONG_VALUE;
    private PlayerBanInfoProp banInfo = null;
    private boolean internalPlayerTag = Constant.DEFAULT_BOOLEAN_VALUE;
    private String midasPf = Constant.DEFAULT_STR_VALUE;
    private int bornPlatId = Constant.DEFAULT_INT_VALUE;
    private boolean logOff = Constant.DEFAULT_BOOLEAN_VALUE;
    private int regChannel = Constant.DEFAULT_INT_VALUE;

    public PlayerBasicInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerBasicInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get lastLoginTsMs
     *
     * @return lastLoginTsMs value
     */
    public long getLastLoginTsMs() {
        return this.lastLoginTsMs;
    }

    /**
     * set lastLoginTsMs && set marked
     *
     * @param lastLoginTsMs new value
     * @return current object
     */
    public PlayerBasicInfoProp setLastLoginTsMs(long lastLoginTsMs) {
        if (this.lastLoginTsMs != lastLoginTsMs) {
            this.mark(FIELD_INDEX_LASTLOGINTSMS);
            this.lastLoginTsMs = lastLoginTsMs;
        }
        return this;
    }

    /**
     * inner set lastLoginTsMs
     *
     * @param lastLoginTsMs new value
     */
    private void innerSetLastLoginTsMs(long lastLoginTsMs) {
        this.lastLoginTsMs = lastLoginTsMs;
    }

    /**
     * get lastLogoutTsMs
     *
     * @return lastLogoutTsMs value
     */
    public long getLastLogoutTsMs() {
        return this.lastLogoutTsMs;
    }

    /**
     * set lastLogoutTsMs && set marked
     *
     * @param lastLogoutTsMs new value
     * @return current object
     */
    public PlayerBasicInfoProp setLastLogoutTsMs(long lastLogoutTsMs) {
        if (this.lastLogoutTsMs != lastLogoutTsMs) {
            this.mark(FIELD_INDEX_LASTLOGOUTTSMS);
            this.lastLogoutTsMs = lastLogoutTsMs;
        }
        return this;
    }

    /**
     * inner set lastLogoutTsMs
     *
     * @param lastLogoutTsMs new value
     */
    private void innerSetLastLogoutTsMs(long lastLogoutTsMs) {
        this.lastLogoutTsMs = lastLogoutTsMs;
    }

    /**
     * get roleSeqOfAccount
     *
     * @return roleSeqOfAccount value
     */
    public int getRoleSeqOfAccount() {
        return this.roleSeqOfAccount;
    }

    /**
     * set roleSeqOfAccount && set marked
     *
     * @param roleSeqOfAccount new value
     * @return current object
     */
    public PlayerBasicInfoProp setRoleSeqOfAccount(int roleSeqOfAccount) {
        if (this.roleSeqOfAccount != roleSeqOfAccount) {
            this.mark(FIELD_INDEX_ROLESEQOFACCOUNT);
            this.roleSeqOfAccount = roleSeqOfAccount;
        }
        return this;
    }

    /**
     * inner set roleSeqOfAccount
     *
     * @param roleSeqOfAccount new value
     */
    private void innerSetRoleSeqOfAccount(int roleSeqOfAccount) {
        this.roleSeqOfAccount = roleSeqOfAccount;
    }

    /**
     * get lastUpdateLoginDaysTsMs
     *
     * @return lastUpdateLoginDaysTsMs value
     */
    public long getLastUpdateLoginDaysTsMs() {
        return this.lastUpdateLoginDaysTsMs;
    }

    /**
     * set lastUpdateLoginDaysTsMs && set marked
     *
     * @param lastUpdateLoginDaysTsMs new value
     * @return current object
     */
    public PlayerBasicInfoProp setLastUpdateLoginDaysTsMs(long lastUpdateLoginDaysTsMs) {
        if (this.lastUpdateLoginDaysTsMs != lastUpdateLoginDaysTsMs) {
            this.mark(FIELD_INDEX_LASTUPDATELOGINDAYSTSMS);
            this.lastUpdateLoginDaysTsMs = lastUpdateLoginDaysTsMs;
        }
        return this;
    }

    /**
     * inner set lastUpdateLoginDaysTsMs
     *
     * @param lastUpdateLoginDaysTsMs new value
     */
    private void innerSetLastUpdateLoginDaysTsMs(long lastUpdateLoginDaysTsMs) {
        this.lastUpdateLoginDaysTsMs = lastUpdateLoginDaysTsMs;
    }

    /**
     * get onlineTimeMs
     *
     * @return onlineTimeMs value
     */
    public long getOnlineTimeMs() {
        return this.onlineTimeMs;
    }

    /**
     * set onlineTimeMs && set marked
     *
     * @param onlineTimeMs new value
     * @return current object
     */
    public PlayerBasicInfoProp setOnlineTimeMs(long onlineTimeMs) {
        if (this.onlineTimeMs != onlineTimeMs) {
            this.mark(FIELD_INDEX_ONLINETIMEMS);
            this.onlineTimeMs = onlineTimeMs;
        }
        return this;
    }

    /**
     * inner set onlineTimeMs
     *
     * @param onlineTimeMs new value
     */
    private void innerSetOnlineTimeMs(long onlineTimeMs) {
        this.onlineTimeMs = onlineTimeMs;
    }

    /**
     * get banInfo
     *
     * @return banInfo value
     */
    public PlayerBanInfoProp getBanInfo() {
        if (this.banInfo == null) {
            this.banInfo = new PlayerBanInfoProp(this, FIELD_INDEX_BANINFO);
        }
        return this.banInfo;
    }

    /**
     * get internalPlayerTag
     *
     * @return internalPlayerTag value
     */
    public boolean getInternalPlayerTag() {
        return this.internalPlayerTag;
    }

    /**
     * set internalPlayerTag && set marked
     *
     * @param internalPlayerTag new value
     * @return current object
     */
    public PlayerBasicInfoProp setInternalPlayerTag(boolean internalPlayerTag) {
        if (this.internalPlayerTag != internalPlayerTag) {
            this.mark(FIELD_INDEX_INTERNALPLAYERTAG);
            this.internalPlayerTag = internalPlayerTag;
        }
        return this;
    }

    /**
     * inner set internalPlayerTag
     *
     * @param internalPlayerTag new value
     */
    private void innerSetInternalPlayerTag(boolean internalPlayerTag) {
        this.internalPlayerTag = internalPlayerTag;
    }

    /**
     * get midasPf
     *
     * @return midasPf value
     */
    public String getMidasPf() {
        return this.midasPf;
    }

    /**
     * set midasPf && set marked
     *
     * @param midasPf new value
     * @return current object
     */
    public PlayerBasicInfoProp setMidasPf(String midasPf) {
        if (midasPf == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.midasPf, midasPf)) {
            this.mark(FIELD_INDEX_MIDASPF);
            this.midasPf = midasPf;
        }
        return this;
    }

    /**
     * inner set midasPf
     *
     * @param midasPf new value
     */
    private void innerSetMidasPf(String midasPf) {
        this.midasPf = midasPf;
    }

    /**
     * get bornPlatId
     *
     * @return bornPlatId value
     */
    public int getBornPlatId() {
        return this.bornPlatId;
    }

    /**
     * set bornPlatId && set marked
     *
     * @param bornPlatId new value
     * @return current object
     */
    public PlayerBasicInfoProp setBornPlatId(int bornPlatId) {
        if (this.bornPlatId != bornPlatId) {
            this.mark(FIELD_INDEX_BORNPLATID);
            this.bornPlatId = bornPlatId;
        }
        return this;
    }

    /**
     * inner set bornPlatId
     *
     * @param bornPlatId new value
     */
    private void innerSetBornPlatId(int bornPlatId) {
        this.bornPlatId = bornPlatId;
    }

    /**
     * get logOff
     *
     * @return logOff value
     */
    public boolean getLogOff() {
        return this.logOff;
    }

    /**
     * set logOff && set marked
     *
     * @param logOff new value
     * @return current object
     */
    public PlayerBasicInfoProp setLogOff(boolean logOff) {
        if (this.logOff != logOff) {
            this.mark(FIELD_INDEX_LOGOFF);
            this.logOff = logOff;
        }
        return this;
    }

    /**
     * inner set logOff
     *
     * @param logOff new value
     */
    private void innerSetLogOff(boolean logOff) {
        this.logOff = logOff;
    }

    /**
     * get regChannel
     *
     * @return regChannel value
     */
    public int getRegChannel() {
        return this.regChannel;
    }

    /**
     * set regChannel && set marked
     *
     * @param regChannel new value
     * @return current object
     */
    public PlayerBasicInfoProp setRegChannel(int regChannel) {
        if (this.regChannel != regChannel) {
            this.mark(FIELD_INDEX_REGCHANNEL);
            this.regChannel = regChannel;
        }
        return this;
    }

    /**
     * inner set regChannel
     *
     * @param regChannel new value
     */
    private void innerSetRegChannel(int regChannel) {
        this.regChannel = regChannel;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerBasicInfoPB.Builder getCopyCsBuilder() {
        final PlayerBasicInfoPB.Builder builder = PlayerBasicInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerBasicInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLastLoginTsMs() != 0L) {
            builder.setLastLoginTsMs(this.getLastLoginTsMs());
            fieldCnt++;
        }  else if (builder.hasLastLoginTsMs()) {
            // 清理LastLoginTsMs
            builder.clearLastLoginTsMs();
            fieldCnt++;
        }
        if (this.getLastLogoutTsMs() != 0L) {
            builder.setLastLogoutTsMs(this.getLastLogoutTsMs());
            fieldCnt++;
        }  else if (builder.hasLastLogoutTsMs()) {
            // 清理LastLogoutTsMs
            builder.clearLastLogoutTsMs();
            fieldCnt++;
        }
        if (this.getRoleSeqOfAccount() != 0) {
            builder.setRoleSeqOfAccount(this.getRoleSeqOfAccount());
            fieldCnt++;
        }  else if (builder.hasRoleSeqOfAccount()) {
            // 清理RoleSeqOfAccount
            builder.clearRoleSeqOfAccount();
            fieldCnt++;
        }
        if (this.getLastUpdateLoginDaysTsMs() != 0L) {
            builder.setLastUpdateLoginDaysTsMs(this.getLastUpdateLoginDaysTsMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateLoginDaysTsMs()) {
            // 清理LastUpdateLoginDaysTsMs
            builder.clearLastUpdateLoginDaysTsMs();
            fieldCnt++;
        }
        if (this.getOnlineTimeMs() != 0L) {
            builder.setOnlineTimeMs(this.getOnlineTimeMs());
            fieldCnt++;
        }  else if (builder.hasOnlineTimeMs()) {
            // 清理OnlineTimeMs
            builder.clearOnlineTimeMs();
            fieldCnt++;
        }
        if (this.getLogOff()) {
            builder.setLogOff(this.getLogOff());
            fieldCnt++;
        }  else if (builder.hasLogOff()) {
            // 清理LogOff
            builder.clearLogOff();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerBasicInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LASTLOGINTSMS)) {
            builder.setLastLoginTsMs(this.getLastLoginTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTLOGOUTTSMS)) {
            builder.setLastLogoutTsMs(this.getLastLogoutTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ROLESEQOFACCOUNT)) {
            builder.setRoleSeqOfAccount(this.getRoleSeqOfAccount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATELOGINDAYSTSMS)) {
            builder.setLastUpdateLoginDaysTsMs(this.getLastUpdateLoginDaysTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ONLINETIMEMS)) {
            builder.setOnlineTimeMs(this.getOnlineTimeMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOGOFF)) {
            builder.setLogOff(this.getLogOff());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerBasicInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LASTLOGINTSMS)) {
            builder.setLastLoginTsMs(this.getLastLoginTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTLOGOUTTSMS)) {
            builder.setLastLogoutTsMs(this.getLastLogoutTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ROLESEQOFACCOUNT)) {
            builder.setRoleSeqOfAccount(this.getRoleSeqOfAccount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATELOGINDAYSTSMS)) {
            builder.setLastUpdateLoginDaysTsMs(this.getLastUpdateLoginDaysTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ONLINETIMEMS)) {
            builder.setOnlineTimeMs(this.getOnlineTimeMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOGOFF)) {
            builder.setLogOff(this.getLogOff());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerBasicInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLastLoginTsMs()) {
            this.innerSetLastLoginTsMs(proto.getLastLoginTsMs());
        } else {
            this.innerSetLastLoginTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastLogoutTsMs()) {
            this.innerSetLastLogoutTsMs(proto.getLastLogoutTsMs());
        } else {
            this.innerSetLastLogoutTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRoleSeqOfAccount()) {
            this.innerSetRoleSeqOfAccount(proto.getRoleSeqOfAccount());
        } else {
            this.innerSetRoleSeqOfAccount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastUpdateLoginDaysTsMs()) {
            this.innerSetLastUpdateLoginDaysTsMs(proto.getLastUpdateLoginDaysTsMs());
        } else {
            this.innerSetLastUpdateLoginDaysTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOnlineTimeMs()) {
            this.innerSetOnlineTimeMs(proto.getOnlineTimeMs());
        } else {
            this.innerSetOnlineTimeMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLogOff()) {
            this.innerSetLogOff(proto.getLogOff());
        } else {
            this.innerSetLogOff(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return PlayerBasicInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerBasicInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLastLoginTsMs()) {
            this.setLastLoginTsMs(proto.getLastLoginTsMs());
            fieldCnt++;
        }
        if (proto.hasLastLogoutTsMs()) {
            this.setLastLogoutTsMs(proto.getLastLogoutTsMs());
            fieldCnt++;
        }
        if (proto.hasRoleSeqOfAccount()) {
            this.setRoleSeqOfAccount(proto.getRoleSeqOfAccount());
            fieldCnt++;
        }
        if (proto.hasLastUpdateLoginDaysTsMs()) {
            this.setLastUpdateLoginDaysTsMs(proto.getLastUpdateLoginDaysTsMs());
            fieldCnt++;
        }
        if (proto.hasOnlineTimeMs()) {
            this.setOnlineTimeMs(proto.getOnlineTimeMs());
            fieldCnt++;
        }
        if (proto.hasLogOff()) {
            this.setLogOff(proto.getLogOff());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerBasicInfo.Builder getCopyDbBuilder() {
        final PlayerBasicInfo.Builder builder = PlayerBasicInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerBasicInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLastLoginTsMs() != 0L) {
            builder.setLastLoginTsMs(this.getLastLoginTsMs());
            fieldCnt++;
        }  else if (builder.hasLastLoginTsMs()) {
            // 清理LastLoginTsMs
            builder.clearLastLoginTsMs();
            fieldCnt++;
        }
        if (this.getLastLogoutTsMs() != 0L) {
            builder.setLastLogoutTsMs(this.getLastLogoutTsMs());
            fieldCnt++;
        }  else if (builder.hasLastLogoutTsMs()) {
            // 清理LastLogoutTsMs
            builder.clearLastLogoutTsMs();
            fieldCnt++;
        }
        if (this.getRoleSeqOfAccount() != 0) {
            builder.setRoleSeqOfAccount(this.getRoleSeqOfAccount());
            fieldCnt++;
        }  else if (builder.hasRoleSeqOfAccount()) {
            // 清理RoleSeqOfAccount
            builder.clearRoleSeqOfAccount();
            fieldCnt++;
        }
        if (this.getLastUpdateLoginDaysTsMs() != 0L) {
            builder.setLastUpdateLoginDaysTsMs(this.getLastUpdateLoginDaysTsMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateLoginDaysTsMs()) {
            // 清理LastUpdateLoginDaysTsMs
            builder.clearLastUpdateLoginDaysTsMs();
            fieldCnt++;
        }
        if (this.getOnlineTimeMs() != 0L) {
            builder.setOnlineTimeMs(this.getOnlineTimeMs());
            fieldCnt++;
        }  else if (builder.hasOnlineTimeMs()) {
            // 清理OnlineTimeMs
            builder.clearOnlineTimeMs();
            fieldCnt++;
        }
        if (this.banInfo != null) {
            Player.PlayerBanInfo.Builder tmpBuilder = Player.PlayerBanInfo.newBuilder();
            final int tmpFieldCnt = this.banInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBanInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBanInfo();
            }
        }  else if (builder.hasBanInfo()) {
            // 清理BanInfo
            builder.clearBanInfo();
            fieldCnt++;
        }
        if (this.getInternalPlayerTag()) {
            builder.setInternalPlayerTag(this.getInternalPlayerTag());
            fieldCnt++;
        }  else if (builder.hasInternalPlayerTag()) {
            // 清理InternalPlayerTag
            builder.clearInternalPlayerTag();
            fieldCnt++;
        }
        if (!this.getMidasPf().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setMidasPf(this.getMidasPf());
            fieldCnt++;
        }  else if (builder.hasMidasPf()) {
            // 清理MidasPf
            builder.clearMidasPf();
            fieldCnt++;
        }
        if (this.getBornPlatId() != 0) {
            builder.setBornPlatId(this.getBornPlatId());
            fieldCnt++;
        }  else if (builder.hasBornPlatId()) {
            // 清理BornPlatId
            builder.clearBornPlatId();
            fieldCnt++;
        }
        if (this.getLogOff()) {
            builder.setLogOff(this.getLogOff());
            fieldCnt++;
        }  else if (builder.hasLogOff()) {
            // 清理LogOff
            builder.clearLogOff();
            fieldCnt++;
        }
        if (this.getRegChannel() != 0) {
            builder.setRegChannel(this.getRegChannel());
            fieldCnt++;
        }  else if (builder.hasRegChannel()) {
            // 清理RegChannel
            builder.clearRegChannel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerBasicInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LASTLOGINTSMS)) {
            builder.setLastLoginTsMs(this.getLastLoginTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTLOGOUTTSMS)) {
            builder.setLastLogoutTsMs(this.getLastLogoutTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ROLESEQOFACCOUNT)) {
            builder.setRoleSeqOfAccount(this.getRoleSeqOfAccount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATELOGINDAYSTSMS)) {
            builder.setLastUpdateLoginDaysTsMs(this.getLastUpdateLoginDaysTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ONLINETIMEMS)) {
            builder.setOnlineTimeMs(this.getOnlineTimeMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BANINFO) && this.banInfo != null) {
            final boolean needClear = !builder.hasBanInfo();
            final int tmpFieldCnt = this.banInfo.copyChangeToDb(builder.getBanInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBanInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_INTERNALPLAYERTAG)) {
            builder.setInternalPlayerTag(this.getInternalPlayerTag());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MIDASPF)) {
            builder.setMidasPf(this.getMidasPf());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BORNPLATID)) {
            builder.setBornPlatId(this.getBornPlatId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOGOFF)) {
            builder.setLogOff(this.getLogOff());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REGCHANNEL)) {
            builder.setRegChannel(this.getRegChannel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerBasicInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLastLoginTsMs()) {
            this.innerSetLastLoginTsMs(proto.getLastLoginTsMs());
        } else {
            this.innerSetLastLoginTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastLogoutTsMs()) {
            this.innerSetLastLogoutTsMs(proto.getLastLogoutTsMs());
        } else {
            this.innerSetLastLogoutTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRoleSeqOfAccount()) {
            this.innerSetRoleSeqOfAccount(proto.getRoleSeqOfAccount());
        } else {
            this.innerSetRoleSeqOfAccount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastUpdateLoginDaysTsMs()) {
            this.innerSetLastUpdateLoginDaysTsMs(proto.getLastUpdateLoginDaysTsMs());
        } else {
            this.innerSetLastUpdateLoginDaysTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOnlineTimeMs()) {
            this.innerSetOnlineTimeMs(proto.getOnlineTimeMs());
        } else {
            this.innerSetOnlineTimeMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBanInfo()) {
            this.getBanInfo().mergeFromDb(proto.getBanInfo());
        } else {
            if (this.banInfo != null) {
                this.banInfo.mergeFromDb(proto.getBanInfo());
            }
        }
        if (proto.hasInternalPlayerTag()) {
            this.innerSetInternalPlayerTag(proto.getInternalPlayerTag());
        } else {
            this.innerSetInternalPlayerTag(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasMidasPf()) {
            this.innerSetMidasPf(proto.getMidasPf());
        } else {
            this.innerSetMidasPf(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasBornPlatId()) {
            this.innerSetBornPlatId(proto.getBornPlatId());
        } else {
            this.innerSetBornPlatId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLogOff()) {
            this.innerSetLogOff(proto.getLogOff());
        } else {
            this.innerSetLogOff(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasRegChannel()) {
            this.innerSetRegChannel(proto.getRegChannel());
        } else {
            this.innerSetRegChannel(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerBasicInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerBasicInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLastLoginTsMs()) {
            this.setLastLoginTsMs(proto.getLastLoginTsMs());
            fieldCnt++;
        }
        if (proto.hasLastLogoutTsMs()) {
            this.setLastLogoutTsMs(proto.getLastLogoutTsMs());
            fieldCnt++;
        }
        if (proto.hasRoleSeqOfAccount()) {
            this.setRoleSeqOfAccount(proto.getRoleSeqOfAccount());
            fieldCnt++;
        }
        if (proto.hasLastUpdateLoginDaysTsMs()) {
            this.setLastUpdateLoginDaysTsMs(proto.getLastUpdateLoginDaysTsMs());
            fieldCnt++;
        }
        if (proto.hasOnlineTimeMs()) {
            this.setOnlineTimeMs(proto.getOnlineTimeMs());
            fieldCnt++;
        }
        if (proto.hasBanInfo()) {
            this.getBanInfo().mergeChangeFromDb(proto.getBanInfo());
            fieldCnt++;
        }
        if (proto.hasInternalPlayerTag()) {
            this.setInternalPlayerTag(proto.getInternalPlayerTag());
            fieldCnt++;
        }
        if (proto.hasMidasPf()) {
            this.setMidasPf(proto.getMidasPf());
            fieldCnt++;
        }
        if (proto.hasBornPlatId()) {
            this.setBornPlatId(proto.getBornPlatId());
            fieldCnt++;
        }
        if (proto.hasLogOff()) {
            this.setLogOff(proto.getLogOff());
            fieldCnt++;
        }
        if (proto.hasRegChannel()) {
            this.setRegChannel(proto.getRegChannel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerBasicInfo.Builder getCopySsBuilder() {
        final PlayerBasicInfo.Builder builder = PlayerBasicInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerBasicInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLastLoginTsMs() != 0L) {
            builder.setLastLoginTsMs(this.getLastLoginTsMs());
            fieldCnt++;
        }  else if (builder.hasLastLoginTsMs()) {
            // 清理LastLoginTsMs
            builder.clearLastLoginTsMs();
            fieldCnt++;
        }
        if (this.getLastLogoutTsMs() != 0L) {
            builder.setLastLogoutTsMs(this.getLastLogoutTsMs());
            fieldCnt++;
        }  else if (builder.hasLastLogoutTsMs()) {
            // 清理LastLogoutTsMs
            builder.clearLastLogoutTsMs();
            fieldCnt++;
        }
        if (this.getRoleSeqOfAccount() != 0) {
            builder.setRoleSeqOfAccount(this.getRoleSeqOfAccount());
            fieldCnt++;
        }  else if (builder.hasRoleSeqOfAccount()) {
            // 清理RoleSeqOfAccount
            builder.clearRoleSeqOfAccount();
            fieldCnt++;
        }
        if (this.getLastUpdateLoginDaysTsMs() != 0L) {
            builder.setLastUpdateLoginDaysTsMs(this.getLastUpdateLoginDaysTsMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateLoginDaysTsMs()) {
            // 清理LastUpdateLoginDaysTsMs
            builder.clearLastUpdateLoginDaysTsMs();
            fieldCnt++;
        }
        if (this.getOnlineTimeMs() != 0L) {
            builder.setOnlineTimeMs(this.getOnlineTimeMs());
            fieldCnt++;
        }  else if (builder.hasOnlineTimeMs()) {
            // 清理OnlineTimeMs
            builder.clearOnlineTimeMs();
            fieldCnt++;
        }
        if (this.banInfo != null) {
            Player.PlayerBanInfo.Builder tmpBuilder = Player.PlayerBanInfo.newBuilder();
            final int tmpFieldCnt = this.banInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBanInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBanInfo();
            }
        }  else if (builder.hasBanInfo()) {
            // 清理BanInfo
            builder.clearBanInfo();
            fieldCnt++;
        }
        if (this.getInternalPlayerTag()) {
            builder.setInternalPlayerTag(this.getInternalPlayerTag());
            fieldCnt++;
        }  else if (builder.hasInternalPlayerTag()) {
            // 清理InternalPlayerTag
            builder.clearInternalPlayerTag();
            fieldCnt++;
        }
        if (!this.getMidasPf().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setMidasPf(this.getMidasPf());
            fieldCnt++;
        }  else if (builder.hasMidasPf()) {
            // 清理MidasPf
            builder.clearMidasPf();
            fieldCnt++;
        }
        if (this.getBornPlatId() != 0) {
            builder.setBornPlatId(this.getBornPlatId());
            fieldCnt++;
        }  else if (builder.hasBornPlatId()) {
            // 清理BornPlatId
            builder.clearBornPlatId();
            fieldCnt++;
        }
        if (this.getLogOff()) {
            builder.setLogOff(this.getLogOff());
            fieldCnt++;
        }  else if (builder.hasLogOff()) {
            // 清理LogOff
            builder.clearLogOff();
            fieldCnt++;
        }
        if (this.getRegChannel() != 0) {
            builder.setRegChannel(this.getRegChannel());
            fieldCnt++;
        }  else if (builder.hasRegChannel()) {
            // 清理RegChannel
            builder.clearRegChannel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerBasicInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LASTLOGINTSMS)) {
            builder.setLastLoginTsMs(this.getLastLoginTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTLOGOUTTSMS)) {
            builder.setLastLogoutTsMs(this.getLastLogoutTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ROLESEQOFACCOUNT)) {
            builder.setRoleSeqOfAccount(this.getRoleSeqOfAccount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATELOGINDAYSTSMS)) {
            builder.setLastUpdateLoginDaysTsMs(this.getLastUpdateLoginDaysTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ONLINETIMEMS)) {
            builder.setOnlineTimeMs(this.getOnlineTimeMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BANINFO) && this.banInfo != null) {
            final boolean needClear = !builder.hasBanInfo();
            final int tmpFieldCnt = this.banInfo.copyChangeToSs(builder.getBanInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBanInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_INTERNALPLAYERTAG)) {
            builder.setInternalPlayerTag(this.getInternalPlayerTag());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MIDASPF)) {
            builder.setMidasPf(this.getMidasPf());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BORNPLATID)) {
            builder.setBornPlatId(this.getBornPlatId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOGOFF)) {
            builder.setLogOff(this.getLogOff());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REGCHANNEL)) {
            builder.setRegChannel(this.getRegChannel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerBasicInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLastLoginTsMs()) {
            this.innerSetLastLoginTsMs(proto.getLastLoginTsMs());
        } else {
            this.innerSetLastLoginTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastLogoutTsMs()) {
            this.innerSetLastLogoutTsMs(proto.getLastLogoutTsMs());
        } else {
            this.innerSetLastLogoutTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRoleSeqOfAccount()) {
            this.innerSetRoleSeqOfAccount(proto.getRoleSeqOfAccount());
        } else {
            this.innerSetRoleSeqOfAccount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastUpdateLoginDaysTsMs()) {
            this.innerSetLastUpdateLoginDaysTsMs(proto.getLastUpdateLoginDaysTsMs());
        } else {
            this.innerSetLastUpdateLoginDaysTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOnlineTimeMs()) {
            this.innerSetOnlineTimeMs(proto.getOnlineTimeMs());
        } else {
            this.innerSetOnlineTimeMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBanInfo()) {
            this.getBanInfo().mergeFromSs(proto.getBanInfo());
        } else {
            if (this.banInfo != null) {
                this.banInfo.mergeFromSs(proto.getBanInfo());
            }
        }
        if (proto.hasInternalPlayerTag()) {
            this.innerSetInternalPlayerTag(proto.getInternalPlayerTag());
        } else {
            this.innerSetInternalPlayerTag(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasMidasPf()) {
            this.innerSetMidasPf(proto.getMidasPf());
        } else {
            this.innerSetMidasPf(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasBornPlatId()) {
            this.innerSetBornPlatId(proto.getBornPlatId());
        } else {
            this.innerSetBornPlatId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLogOff()) {
            this.innerSetLogOff(proto.getLogOff());
        } else {
            this.innerSetLogOff(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasRegChannel()) {
            this.innerSetRegChannel(proto.getRegChannel());
        } else {
            this.innerSetRegChannel(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerBasicInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerBasicInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLastLoginTsMs()) {
            this.setLastLoginTsMs(proto.getLastLoginTsMs());
            fieldCnt++;
        }
        if (proto.hasLastLogoutTsMs()) {
            this.setLastLogoutTsMs(proto.getLastLogoutTsMs());
            fieldCnt++;
        }
        if (proto.hasRoleSeqOfAccount()) {
            this.setRoleSeqOfAccount(proto.getRoleSeqOfAccount());
            fieldCnt++;
        }
        if (proto.hasLastUpdateLoginDaysTsMs()) {
            this.setLastUpdateLoginDaysTsMs(proto.getLastUpdateLoginDaysTsMs());
            fieldCnt++;
        }
        if (proto.hasOnlineTimeMs()) {
            this.setOnlineTimeMs(proto.getOnlineTimeMs());
            fieldCnt++;
        }
        if (proto.hasBanInfo()) {
            this.getBanInfo().mergeChangeFromSs(proto.getBanInfo());
            fieldCnt++;
        }
        if (proto.hasInternalPlayerTag()) {
            this.setInternalPlayerTag(proto.getInternalPlayerTag());
            fieldCnt++;
        }
        if (proto.hasMidasPf()) {
            this.setMidasPf(proto.getMidasPf());
            fieldCnt++;
        }
        if (proto.hasBornPlatId()) {
            this.setBornPlatId(proto.getBornPlatId());
            fieldCnt++;
        }
        if (proto.hasLogOff()) {
            this.setLogOff(proto.getLogOff());
            fieldCnt++;
        }
        if (proto.hasRegChannel()) {
            this.setRegChannel(proto.getRegChannel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerBasicInfo.Builder builder = PlayerBasicInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_BANINFO) && this.banInfo != null) {
            this.banInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.banInfo != null) {
            this.banInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerBasicInfoProp)) {
            return false;
        }
        final PlayerBasicInfoProp otherNode = (PlayerBasicInfoProp) node;
        if (this.lastLoginTsMs != otherNode.lastLoginTsMs) {
            return false;
        }
        if (this.lastLogoutTsMs != otherNode.lastLogoutTsMs) {
            return false;
        }
        if (this.roleSeqOfAccount != otherNode.roleSeqOfAccount) {
            return false;
        }
        if (this.lastUpdateLoginDaysTsMs != otherNode.lastUpdateLoginDaysTsMs) {
            return false;
        }
        if (this.onlineTimeMs != otherNode.onlineTimeMs) {
            return false;
        }
        if (!this.getBanInfo().compareDataTo(otherNode.getBanInfo())) {
            return false;
        }
        if (this.internalPlayerTag != otherNode.internalPlayerTag) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.midasPf, otherNode.midasPf)) {
            return false;
        }
        if (this.bornPlatId != otherNode.bornPlatId) {
            return false;
        }
        if (this.logOff != otherNode.logOff) {
            return false;
        }
        if (this.regChannel != otherNode.regChannel) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 53;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
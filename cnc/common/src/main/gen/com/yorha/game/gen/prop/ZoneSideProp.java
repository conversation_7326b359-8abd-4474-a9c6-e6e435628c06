package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.Zoneside.ZoneSideEntity;
import com.yorha.proto.Zoneside;
import com.yorha.proto.ZonesidePB.ZoneSideEntityPB;
import com.yorha.proto.ZonesidePB;


/**
 * <AUTHOR> auto gen
 */
public class ZoneSideProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ACTIVITYMODEL = 0;
    public static final int FIELD_INDEX_REFRESHMODEL = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;
    private PropertyChangeListener listener;

    private ZoneActivityModelProp activityModel = null;
    private ZoneSideRefreshModelProp refreshModel = null;

    public ZoneSideProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ZoneSideProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get activityModel
     *
     * @return activityModel value
     */
    public ZoneActivityModelProp getActivityModel() {
        if (this.activityModel == null) {
            this.activityModel = new ZoneActivityModelProp(this, FIELD_INDEX_ACTIVITYMODEL);
        }
        return this.activityModel;
    }

    /**
     * get refreshModel
     *
     * @return refreshModel value
     */
    public ZoneSideRefreshModelProp getRefreshModel() {
        if (this.refreshModel == null) {
            this.refreshModel = new ZoneSideRefreshModelProp(this, FIELD_INDEX_REFRESHMODEL);
        }
        return this.refreshModel;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSideEntityPB.Builder getCopyCsBuilder() {
        final ZoneSideEntityPB.Builder builder = ZoneSideEntityPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ZoneSideEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.activityModel != null) {
            ZonesidePB.ZoneActivityModelPB.Builder tmpBuilder = ZonesidePB.ZoneActivityModelPB.newBuilder();
            final int tmpFieldCnt = this.activityModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActivityModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActivityModel();
            }
        }  else if (builder.hasActivityModel()) {
            // 清理ActivityModel
            builder.clearActivityModel();
            fieldCnt++;
        }
        if (this.refreshModel != null) {
            ZonesidePB.ZoneSideRefreshModelPB.Builder tmpBuilder = ZonesidePB.ZoneSideRefreshModelPB.newBuilder();
            final int tmpFieldCnt = this.refreshModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRefreshModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRefreshModel();
            }
        }  else if (builder.hasRefreshModel()) {
            // 清理RefreshModel
            builder.clearRefreshModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ZoneSideEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITYMODEL) && this.activityModel != null) {
            final boolean needClear = !builder.hasActivityModel();
            final int tmpFieldCnt = this.activityModel.copyChangeToCs(builder.getActivityModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivityModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REFRESHMODEL) && this.refreshModel != null) {
            final boolean needClear = !builder.hasRefreshModel();
            final int tmpFieldCnt = this.refreshModel.copyChangeToCs(builder.getRefreshModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRefreshModel();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ZoneSideEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITYMODEL) && this.activityModel != null) {
            final boolean needClear = !builder.hasActivityModel();
            final int tmpFieldCnt = this.activityModel.copyChangeToAndClearDeleteKeysCs(builder.getActivityModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivityModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REFRESHMODEL) && this.refreshModel != null) {
            final boolean needClear = !builder.hasRefreshModel();
            final int tmpFieldCnt = this.refreshModel.copyChangeToAndClearDeleteKeysCs(builder.getRefreshModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRefreshModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ZoneSideEntityPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActivityModel()) {
            this.getActivityModel().mergeFromCs(proto.getActivityModel());
        } else {
            if (this.activityModel != null) {
                this.activityModel.mergeFromCs(proto.getActivityModel());
            }
        }
        if (proto.hasRefreshModel()) {
            this.getRefreshModel().mergeFromCs(proto.getRefreshModel());
        } else {
            if (this.refreshModel != null) {
                this.refreshModel.mergeFromCs(proto.getRefreshModel());
            }
        }
        this.markAll();
        return ZoneSideProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ZoneSideEntityPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActivityModel()) {
            this.getActivityModel().mergeChangeFromCs(proto.getActivityModel());
            fieldCnt++;
        }
        if (proto.hasRefreshModel()) {
            this.getRefreshModel().mergeChangeFromCs(proto.getRefreshModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSideEntity.Builder getCopyDbBuilder() {
        final ZoneSideEntity.Builder builder = ZoneSideEntity.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ZoneSideEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.activityModel != null) {
            Zoneside.ZoneActivityModel.Builder tmpBuilder = Zoneside.ZoneActivityModel.newBuilder();
            final int tmpFieldCnt = this.activityModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActivityModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActivityModel();
            }
        }  else if (builder.hasActivityModel()) {
            // 清理ActivityModel
            builder.clearActivityModel();
            fieldCnt++;
        }
        if (this.refreshModel != null) {
            Zoneside.ZoneSideRefreshModel.Builder tmpBuilder = Zoneside.ZoneSideRefreshModel.newBuilder();
            final int tmpFieldCnt = this.refreshModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRefreshModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRefreshModel();
            }
        }  else if (builder.hasRefreshModel()) {
            // 清理RefreshModel
            builder.clearRefreshModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ZoneSideEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITYMODEL) && this.activityModel != null) {
            final boolean needClear = !builder.hasActivityModel();
            final int tmpFieldCnt = this.activityModel.copyChangeToDb(builder.getActivityModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivityModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REFRESHMODEL) && this.refreshModel != null) {
            final boolean needClear = !builder.hasRefreshModel();
            final int tmpFieldCnt = this.refreshModel.copyChangeToDb(builder.getRefreshModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRefreshModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ZoneSideEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActivityModel()) {
            this.getActivityModel().mergeFromDb(proto.getActivityModel());
        } else {
            if (this.activityModel != null) {
                this.activityModel.mergeFromDb(proto.getActivityModel());
            }
        }
        if (proto.hasRefreshModel()) {
            this.getRefreshModel().mergeFromDb(proto.getRefreshModel());
        } else {
            if (this.refreshModel != null) {
                this.refreshModel.mergeFromDb(proto.getRefreshModel());
            }
        }
        this.markAll();
        return ZoneSideProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ZoneSideEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActivityModel()) {
            this.getActivityModel().mergeChangeFromDb(proto.getActivityModel());
            fieldCnt++;
        }
        if (proto.hasRefreshModel()) {
            this.getRefreshModel().mergeChangeFromDb(proto.getRefreshModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSideEntity.Builder getCopySsBuilder() {
        final ZoneSideEntity.Builder builder = ZoneSideEntity.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ZoneSideEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.activityModel != null) {
            Zoneside.ZoneActivityModel.Builder tmpBuilder = Zoneside.ZoneActivityModel.newBuilder();
            final int tmpFieldCnt = this.activityModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActivityModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActivityModel();
            }
        }  else if (builder.hasActivityModel()) {
            // 清理ActivityModel
            builder.clearActivityModel();
            fieldCnt++;
        }
        if (this.refreshModel != null) {
            Zoneside.ZoneSideRefreshModel.Builder tmpBuilder = Zoneside.ZoneSideRefreshModel.newBuilder();
            final int tmpFieldCnt = this.refreshModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRefreshModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRefreshModel();
            }
        }  else if (builder.hasRefreshModel()) {
            // 清理RefreshModel
            builder.clearRefreshModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ZoneSideEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITYMODEL) && this.activityModel != null) {
            final boolean needClear = !builder.hasActivityModel();
            final int tmpFieldCnt = this.activityModel.copyChangeToSs(builder.getActivityModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivityModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REFRESHMODEL) && this.refreshModel != null) {
            final boolean needClear = !builder.hasRefreshModel();
            final int tmpFieldCnt = this.refreshModel.copyChangeToSs(builder.getRefreshModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRefreshModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ZoneSideEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActivityModel()) {
            this.getActivityModel().mergeFromSs(proto.getActivityModel());
        } else {
            if (this.activityModel != null) {
                this.activityModel.mergeFromSs(proto.getActivityModel());
            }
        }
        if (proto.hasRefreshModel()) {
            this.getRefreshModel().mergeFromSs(proto.getRefreshModel());
        } else {
            if (this.refreshModel != null) {
                this.refreshModel.mergeFromSs(proto.getRefreshModel());
            }
        }
        this.markAll();
        return ZoneSideProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ZoneSideEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActivityModel()) {
            this.getActivityModel().mergeChangeFromSs(proto.getActivityModel());
            fieldCnt++;
        }
        if (proto.hasRefreshModel()) {
            this.getRefreshModel().mergeChangeFromSs(proto.getRefreshModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ZoneSideEntity.Builder builder = ZoneSideEntity.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITYMODEL) && this.activityModel != null) {
            this.activityModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_REFRESHMODEL) && this.refreshModel != null) {
            this.refreshModel.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.activityModel != null) {
            this.activityModel.markAll();
        }
        if (this.refreshModel != null) {
            this.refreshModel.markAll();
        }
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("ZoneSideProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("ZoneSideProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ZoneSideProp)) {
            return false;
        }
        final ZoneSideProp otherNode = (ZoneSideProp) node;
        if (!this.getActivityModel().compareDataTo(otherNode.getActivityModel())) {
            return false;
        }
        if (!this.getRefreshModel().compareDataTo(otherNode.getRefreshModel())) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    public static ZoneSideProp of(ZoneSideEntity fullAttrDb, ZoneSideEntity changeAttrDb) {
        ZoneSideProp prop = new ZoneSideProp();
        prop.mergeFromDb(fullAttrDb);
        prop.mergeChangeFromDb(changeAttrDb);
        prop.unMarkAll();
        return prop;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
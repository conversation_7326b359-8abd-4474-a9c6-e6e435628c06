package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivitySelectGoodsRecordInfo;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ActivitySelectGoodsRecordInfoPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivitySelectGoodsRecordInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_GOODSID = 0;
    public static final int FIELD_INDEX_REWARDINFOS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int goodsId = Constant.DEFAULT_INT_VALUE;
    private Int32ActivitySelectGoodsRewardInfoMapProp rewardInfos = null;

    public ActivitySelectGoodsRecordInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivitySelectGoodsRecordInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get goodsId
     *
     * @return goodsId value
     */
    public int getGoodsId() {
        return this.goodsId;
    }

    /**
     * set goodsId && set marked
     *
     * @param goodsId new value
     * @return current object
     */
    public ActivitySelectGoodsRecordInfoProp setGoodsId(int goodsId) {
        if (this.goodsId != goodsId) {
            this.mark(FIELD_INDEX_GOODSID);
            this.goodsId = goodsId;
        }
        return this;
    }

    /**
     * inner set goodsId
     *
     * @param goodsId new value
     */
    private void innerSetGoodsId(int goodsId) {
        this.goodsId = goodsId;
    }

    /**
     * get rewardInfos
     *
     * @return rewardInfos value
     */
    public Int32ActivitySelectGoodsRewardInfoMapProp getRewardInfos() {
        if (this.rewardInfos == null) {
            this.rewardInfos = new Int32ActivitySelectGoodsRewardInfoMapProp(this, FIELD_INDEX_REWARDINFOS);
        }
        return this.rewardInfos;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putRewardInfosV(ActivitySelectGoodsRewardInfoProp v) {
        this.getRewardInfos().put(v.getSelectRewardId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ActivitySelectGoodsRewardInfoProp addEmptyRewardInfos(Integer k) {
        return this.getRewardInfos().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getRewardInfosSize() {
        if (this.rewardInfos == null) {
            return 0;
        }
        return this.rewardInfos.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isRewardInfosEmpty() {
        if (this.rewardInfos == null) {
            return true;
        }
        return this.rewardInfos.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ActivitySelectGoodsRewardInfoProp getRewardInfosV(Integer k) {
        if (this.rewardInfos == null || !this.rewardInfos.containsKey(k)) {
            return null;
        }
        return this.rewardInfos.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearRewardInfos() {
        if (this.rewardInfos != null) {
            this.rewardInfos.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeRewardInfosV(Integer k) {
        if (this.rewardInfos != null) {
            this.rewardInfos.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivitySelectGoodsRecordInfoPB.Builder getCopyCsBuilder() {
        final ActivitySelectGoodsRecordInfoPB.Builder builder = ActivitySelectGoodsRecordInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivitySelectGoodsRecordInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGoodsId() != 0) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }  else if (builder.hasGoodsId()) {
            // 清理GoodsId
            builder.clearGoodsId();
            fieldCnt++;
        }
        if (this.rewardInfos != null) {
            StructPB.Int32ActivitySelectGoodsRewardInfoMapPB.Builder tmpBuilder = StructPB.Int32ActivitySelectGoodsRewardInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.rewardInfos.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardInfos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardInfos();
            }
        }  else if (builder.hasRewardInfos()) {
            // 清理RewardInfos
            builder.clearRewardInfos();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivitySelectGoodsRecordInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODSID)) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDINFOS) && this.rewardInfos != null) {
            final boolean needClear = !builder.hasRewardInfos();
            final int tmpFieldCnt = this.rewardInfos.copyChangeToCs(builder.getRewardInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivitySelectGoodsRecordInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODSID)) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDINFOS) && this.rewardInfos != null) {
            final boolean needClear = !builder.hasRewardInfos();
            final int tmpFieldCnt = this.rewardInfos.copyChangeToAndClearDeleteKeysCs(builder.getRewardInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivitySelectGoodsRecordInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGoodsId()) {
            this.innerSetGoodsId(proto.getGoodsId());
        } else {
            this.innerSetGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardInfos()) {
            this.getRewardInfos().mergeFromCs(proto.getRewardInfos());
        } else {
            if (this.rewardInfos != null) {
                this.rewardInfos.mergeFromCs(proto.getRewardInfos());
            }
        }
        this.markAll();
        return ActivitySelectGoodsRecordInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivitySelectGoodsRecordInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGoodsId()) {
            this.setGoodsId(proto.getGoodsId());
            fieldCnt++;
        }
        if (proto.hasRewardInfos()) {
            this.getRewardInfos().mergeChangeFromCs(proto.getRewardInfos());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivitySelectGoodsRecordInfo.Builder getCopyDbBuilder() {
        final ActivitySelectGoodsRecordInfo.Builder builder = ActivitySelectGoodsRecordInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivitySelectGoodsRecordInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGoodsId() != 0) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }  else if (builder.hasGoodsId()) {
            // 清理GoodsId
            builder.clearGoodsId();
            fieldCnt++;
        }
        if (this.rewardInfos != null) {
            Struct.Int32ActivitySelectGoodsRewardInfoMap.Builder tmpBuilder = Struct.Int32ActivitySelectGoodsRewardInfoMap.newBuilder();
            final int tmpFieldCnt = this.rewardInfos.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardInfos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardInfos();
            }
        }  else if (builder.hasRewardInfos()) {
            // 清理RewardInfos
            builder.clearRewardInfos();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivitySelectGoodsRecordInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODSID)) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDINFOS) && this.rewardInfos != null) {
            final boolean needClear = !builder.hasRewardInfos();
            final int tmpFieldCnt = this.rewardInfos.copyChangeToDb(builder.getRewardInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivitySelectGoodsRecordInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGoodsId()) {
            this.innerSetGoodsId(proto.getGoodsId());
        } else {
            this.innerSetGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardInfos()) {
            this.getRewardInfos().mergeFromDb(proto.getRewardInfos());
        } else {
            if (this.rewardInfos != null) {
                this.rewardInfos.mergeFromDb(proto.getRewardInfos());
            }
        }
        this.markAll();
        return ActivitySelectGoodsRecordInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivitySelectGoodsRecordInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGoodsId()) {
            this.setGoodsId(proto.getGoodsId());
            fieldCnt++;
        }
        if (proto.hasRewardInfos()) {
            this.getRewardInfos().mergeChangeFromDb(proto.getRewardInfos());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivitySelectGoodsRecordInfo.Builder getCopySsBuilder() {
        final ActivitySelectGoodsRecordInfo.Builder builder = ActivitySelectGoodsRecordInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivitySelectGoodsRecordInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGoodsId() != 0) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }  else if (builder.hasGoodsId()) {
            // 清理GoodsId
            builder.clearGoodsId();
            fieldCnt++;
        }
        if (this.rewardInfos != null) {
            Struct.Int32ActivitySelectGoodsRewardInfoMap.Builder tmpBuilder = Struct.Int32ActivitySelectGoodsRewardInfoMap.newBuilder();
            final int tmpFieldCnt = this.rewardInfos.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardInfos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardInfos();
            }
        }  else if (builder.hasRewardInfos()) {
            // 清理RewardInfos
            builder.clearRewardInfos();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivitySelectGoodsRecordInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODSID)) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDINFOS) && this.rewardInfos != null) {
            final boolean needClear = !builder.hasRewardInfos();
            final int tmpFieldCnt = this.rewardInfos.copyChangeToSs(builder.getRewardInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivitySelectGoodsRecordInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGoodsId()) {
            this.innerSetGoodsId(proto.getGoodsId());
        } else {
            this.innerSetGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardInfos()) {
            this.getRewardInfos().mergeFromSs(proto.getRewardInfos());
        } else {
            if (this.rewardInfos != null) {
                this.rewardInfos.mergeFromSs(proto.getRewardInfos());
            }
        }
        this.markAll();
        return ActivitySelectGoodsRecordInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivitySelectGoodsRecordInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGoodsId()) {
            this.setGoodsId(proto.getGoodsId());
            fieldCnt++;
        }
        if (proto.hasRewardInfos()) {
            this.getRewardInfos().mergeChangeFromSs(proto.getRewardInfos());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivitySelectGoodsRecordInfo.Builder builder = ActivitySelectGoodsRecordInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_REWARDINFOS) && this.rewardInfos != null) {
            this.rewardInfos.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.rewardInfos != null) {
            this.rewardInfos.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.goodsId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivitySelectGoodsRecordInfoProp)) {
            return false;
        }
        final ActivitySelectGoodsRecordInfoProp otherNode = (ActivitySelectGoodsRecordInfoProp) node;
        if (this.goodsId != otherNode.goodsId) {
            return false;
        }
        if (!this.getRewardInfos().compareDataTo(otherNode.getRewardInfos())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
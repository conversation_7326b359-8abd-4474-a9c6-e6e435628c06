package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.TriggerBundleOrderParam;
import com.yorha.proto.StructPB.TriggerBundleOrderParamPB;


/**
 * <AUTHOR> auto gen
 */
public class TriggerBundleOrderParamProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TRIGGERBUNDLEID = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private long triggerBundleId = Constant.DEFAULT_LONG_VALUE;

    public TriggerBundleOrderParamProp() {
        super(null, 0, FIELD_COUNT);
    }

    public TriggerBundleOrderParamProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get triggerBundleId
     *
     * @return triggerBundleId value
     */
    public long getTriggerBundleId() {
        return this.triggerBundleId;
    }

    /**
     * set triggerBundleId && set marked
     *
     * @param triggerBundleId new value
     * @return current object
     */
    public TriggerBundleOrderParamProp setTriggerBundleId(long triggerBundleId) {
        if (this.triggerBundleId != triggerBundleId) {
            this.mark(FIELD_INDEX_TRIGGERBUNDLEID);
            this.triggerBundleId = triggerBundleId;
        }
        return this;
    }

    /**
     * inner set triggerBundleId
     *
     * @param triggerBundleId new value
     */
    private void innerSetTriggerBundleId(long triggerBundleId) {
        this.triggerBundleId = triggerBundleId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TriggerBundleOrderParamPB.Builder getCopyCsBuilder() {
        final TriggerBundleOrderParamPB.Builder builder = TriggerBundleOrderParamPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(TriggerBundleOrderParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTriggerBundleId() != 0L) {
            builder.setTriggerBundleId(this.getTriggerBundleId());
            fieldCnt++;
        }  else if (builder.hasTriggerBundleId()) {
            // 清理TriggerBundleId
            builder.clearTriggerBundleId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(TriggerBundleOrderParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TRIGGERBUNDLEID)) {
            builder.setTriggerBundleId(this.getTriggerBundleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(TriggerBundleOrderParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TRIGGERBUNDLEID)) {
            builder.setTriggerBundleId(this.getTriggerBundleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(TriggerBundleOrderParamPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTriggerBundleId()) {
            this.innerSetTriggerBundleId(proto.getTriggerBundleId());
        } else {
            this.innerSetTriggerBundleId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return TriggerBundleOrderParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(TriggerBundleOrderParamPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTriggerBundleId()) {
            this.setTriggerBundleId(proto.getTriggerBundleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TriggerBundleOrderParam.Builder getCopyDbBuilder() {
        final TriggerBundleOrderParam.Builder builder = TriggerBundleOrderParam.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(TriggerBundleOrderParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTriggerBundleId() != 0L) {
            builder.setTriggerBundleId(this.getTriggerBundleId());
            fieldCnt++;
        }  else if (builder.hasTriggerBundleId()) {
            // 清理TriggerBundleId
            builder.clearTriggerBundleId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(TriggerBundleOrderParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TRIGGERBUNDLEID)) {
            builder.setTriggerBundleId(this.getTriggerBundleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(TriggerBundleOrderParam proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTriggerBundleId()) {
            this.innerSetTriggerBundleId(proto.getTriggerBundleId());
        } else {
            this.innerSetTriggerBundleId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return TriggerBundleOrderParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(TriggerBundleOrderParam proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTriggerBundleId()) {
            this.setTriggerBundleId(proto.getTriggerBundleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TriggerBundleOrderParam.Builder getCopySsBuilder() {
        final TriggerBundleOrderParam.Builder builder = TriggerBundleOrderParam.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(TriggerBundleOrderParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTriggerBundleId() != 0L) {
            builder.setTriggerBundleId(this.getTriggerBundleId());
            fieldCnt++;
        }  else if (builder.hasTriggerBundleId()) {
            // 清理TriggerBundleId
            builder.clearTriggerBundleId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(TriggerBundleOrderParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TRIGGERBUNDLEID)) {
            builder.setTriggerBundleId(this.getTriggerBundleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(TriggerBundleOrderParam proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTriggerBundleId()) {
            this.innerSetTriggerBundleId(proto.getTriggerBundleId());
        } else {
            this.innerSetTriggerBundleId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return TriggerBundleOrderParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(TriggerBundleOrderParam proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTriggerBundleId()) {
            this.setTriggerBundleId(proto.getTriggerBundleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        TriggerBundleOrderParam.Builder builder = TriggerBundleOrderParam.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof TriggerBundleOrderParamProp)) {
            return false;
        }
        final TriggerBundleOrderParamProp otherNode = (TriggerBundleOrderParamProp) node;
        if (this.triggerBundleId != otherNode.triggerBundleId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
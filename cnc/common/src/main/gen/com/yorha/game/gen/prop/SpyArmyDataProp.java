package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.SpyArmyData;
import com.yorha.proto.Basic;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.SpyArmyDataPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class SpyArmyDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_HEROLIST = 0;
    public static final int FIELD_INDEX_SOLDIERLIST = 1;
    public static final int FIELD_INDEX_MAINHEROIDLIST = 2;
    public static final int FIELD_INDEX_PLANELIST = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private HeroListProp heroList = null;
    private SoldierListProp soldierList = null;
    private Int64ListProp mainHeroIdList = null;
    private SceneBattlePlaneListProp planeList = null;

    public SpyArmyDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SpyArmyDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get heroList
     *
     * @return heroList value
     */
    public HeroListProp getHeroList() {
        if (this.heroList == null) {
            this.heroList = new HeroListProp(this, FIELD_INDEX_HEROLIST);
        }
        return this.heroList;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addHeroList(HeroProp v) {
        this.getHeroList().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public HeroProp getHeroListIndex(int index) {
        return this.getHeroList().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public HeroProp removeHeroList(HeroProp v) {
        if (this.heroList == null) {
            return null;
        }
        if(this.heroList.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getHeroListSize() {
        if (this.heroList == null) {
            return 0;
        }
        return this.heroList.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isHeroListEmpty() {
        if (this.heroList == null) {
            return true;
        }
        return this.getHeroList().isEmpty();
    }

    /**
     * clear list
     */
    public void clearHeroList() {
        this.getHeroList().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public HeroProp removeHeroListIndex(int index) {
        return this.getHeroList().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public HeroProp setHeroListIndex(int index, HeroProp v) {
        return this.getHeroList().set(index, v);
    }
    /**
     * get soldierList
     *
     * @return soldierList value
     */
    public SoldierListProp getSoldierList() {
        if (this.soldierList == null) {
            this.soldierList = new SoldierListProp(this, FIELD_INDEX_SOLDIERLIST);
        }
        return this.soldierList;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addSoldierList(SoldierProp v) {
        this.getSoldierList().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public SoldierProp getSoldierListIndex(int index) {
        return this.getSoldierList().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public SoldierProp removeSoldierList(SoldierProp v) {
        if (this.soldierList == null) {
            return null;
        }
        if(this.soldierList.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getSoldierListSize() {
        if (this.soldierList == null) {
            return 0;
        }
        return this.soldierList.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isSoldierListEmpty() {
        if (this.soldierList == null) {
            return true;
        }
        return this.getSoldierList().isEmpty();
    }

    /**
     * clear list
     */
    public void clearSoldierList() {
        this.getSoldierList().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public SoldierProp removeSoldierListIndex(int index) {
        return this.getSoldierList().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public SoldierProp setSoldierListIndex(int index, SoldierProp v) {
        return this.getSoldierList().set(index, v);
    }
    /**
     * get mainHeroIdList
     *
     * @return mainHeroIdList value
     */
    public Int64ListProp getMainHeroIdList() {
        if (this.mainHeroIdList == null) {
            this.mainHeroIdList = new Int64ListProp(this, FIELD_INDEX_MAINHEROIDLIST);
        }
        return this.mainHeroIdList;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addMainHeroIdList(Long v) {
        this.getMainHeroIdList().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long getMainHeroIdListIndex(int index) {
        return this.getMainHeroIdList().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Long removeMainHeroIdList(Long v) {
        if (this.mainHeroIdList == null) {
            return null;
        }
        if(this.mainHeroIdList.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getMainHeroIdListSize() {
        if (this.mainHeroIdList == null) {
            return 0;
        }
        return this.mainHeroIdList.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isMainHeroIdListEmpty() {
        if (this.mainHeroIdList == null) {
            return true;
        }
        return this.getMainHeroIdList().isEmpty();
    }

    /**
     * clear list
     */
    public void clearMainHeroIdList() {
        this.getMainHeroIdList().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long removeMainHeroIdListIndex(int index) {
        return this.getMainHeroIdList().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Long setMainHeroIdListIndex(int index, Long v) {
        return this.getMainHeroIdList().set(index, v);
    }
    /**
     * get planeList
     *
     * @return planeList value
     */
    public SceneBattlePlaneListProp getPlaneList() {
        if (this.planeList == null) {
            this.planeList = new SceneBattlePlaneListProp(this, FIELD_INDEX_PLANELIST);
        }
        return this.planeList;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addPlaneList(SceneBattlePlaneProp v) {
        this.getPlaneList().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public SceneBattlePlaneProp getPlaneListIndex(int index) {
        return this.getPlaneList().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public SceneBattlePlaneProp removePlaneList(SceneBattlePlaneProp v) {
        if (this.planeList == null) {
            return null;
        }
        if(this.planeList.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getPlaneListSize() {
        if (this.planeList == null) {
            return 0;
        }
        return this.planeList.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isPlaneListEmpty() {
        if (this.planeList == null) {
            return true;
        }
        return this.getPlaneList().isEmpty();
    }

    /**
     * clear list
     */
    public void clearPlaneList() {
        this.getPlaneList().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public SceneBattlePlaneProp removePlaneListIndex(int index) {
        return this.getPlaneList().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public SceneBattlePlaneProp setPlaneListIndex(int index, SceneBattlePlaneProp v) {
        return this.getPlaneList().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SpyArmyDataPB.Builder getCopyCsBuilder() {
        final SpyArmyDataPB.Builder builder = SpyArmyDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SpyArmyDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.heroList != null) {
            StructPB.HeroListPB.Builder tmpBuilder = StructPB.HeroListPB.newBuilder();
            final int tmpFieldCnt = this.heroList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHeroList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHeroList();
            }
        }  else if (builder.hasHeroList()) {
            // 清理HeroList
            builder.clearHeroList();
            fieldCnt++;
        }
        if (this.soldierList != null) {
            StructPB.SoldierListPB.Builder tmpBuilder = StructPB.SoldierListPB.newBuilder();
            final int tmpFieldCnt = this.soldierList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldierList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldierList();
            }
        }  else if (builder.hasSoldierList()) {
            // 清理SoldierList
            builder.clearSoldierList();
            fieldCnt++;
        }
        if (this.mainHeroIdList != null) {
            BasicPB.Int64ListPB.Builder tmpBuilder = BasicPB.Int64ListPB.newBuilder();
            final int tmpFieldCnt = this.mainHeroIdList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMainHeroIdList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMainHeroIdList();
            }
        }  else if (builder.hasMainHeroIdList()) {
            // 清理MainHeroIdList
            builder.clearMainHeroIdList();
            fieldCnt++;
        }
        if (this.planeList != null) {
            StructPB.SceneBattlePlaneListPB.Builder tmpBuilder = StructPB.SceneBattlePlaneListPB.newBuilder();
            final int tmpFieldCnt = this.planeList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlaneList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlaneList();
            }
        }  else if (builder.hasPlaneList()) {
            // 清理PlaneList
            builder.clearPlaneList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SpyArmyDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HEROLIST) && this.heroList != null) {
            final boolean needClear = !builder.hasHeroList();
            final int tmpFieldCnt = this.heroList.copyChangeToCs(builder.getHeroListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHeroList();
            }
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERLIST) && this.soldierList != null) {
            final boolean needClear = !builder.hasSoldierList();
            final int tmpFieldCnt = this.soldierList.copyChangeToCs(builder.getSoldierListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldierList();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAINHEROIDLIST) && this.mainHeroIdList != null) {
            final boolean needClear = !builder.hasMainHeroIdList();
            final int tmpFieldCnt = this.mainHeroIdList.copyChangeToCs(builder.getMainHeroIdListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMainHeroIdList();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLANELIST) && this.planeList != null) {
            final boolean needClear = !builder.hasPlaneList();
            final int tmpFieldCnt = this.planeList.copyChangeToCs(builder.getPlaneListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlaneList();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SpyArmyDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HEROLIST) && this.heroList != null) {
            final boolean needClear = !builder.hasHeroList();
            final int tmpFieldCnt = this.heroList.copyChangeToCs(builder.getHeroListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHeroList();
            }
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERLIST) && this.soldierList != null) {
            final boolean needClear = !builder.hasSoldierList();
            final int tmpFieldCnt = this.soldierList.copyChangeToCs(builder.getSoldierListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldierList();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAINHEROIDLIST) && this.mainHeroIdList != null) {
            final boolean needClear = !builder.hasMainHeroIdList();
            final int tmpFieldCnt = this.mainHeroIdList.copyChangeToCs(builder.getMainHeroIdListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMainHeroIdList();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLANELIST) && this.planeList != null) {
            final boolean needClear = !builder.hasPlaneList();
            final int tmpFieldCnt = this.planeList.copyChangeToCs(builder.getPlaneListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlaneList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SpyArmyDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasHeroList()) {
            this.getHeroList().mergeFromCs(proto.getHeroList());
        } else {
            if (this.heroList != null) {
                this.heroList.mergeFromCs(proto.getHeroList());
            }
        }
        if (proto.hasSoldierList()) {
            this.getSoldierList().mergeFromCs(proto.getSoldierList());
        } else {
            if (this.soldierList != null) {
                this.soldierList.mergeFromCs(proto.getSoldierList());
            }
        }
        if (proto.hasMainHeroIdList()) {
            this.getMainHeroIdList().mergeFromCs(proto.getMainHeroIdList());
        } else {
            if (this.mainHeroIdList != null) {
                this.mainHeroIdList.mergeFromCs(proto.getMainHeroIdList());
            }
        }
        if (proto.hasPlaneList()) {
            this.getPlaneList().mergeFromCs(proto.getPlaneList());
        } else {
            if (this.planeList != null) {
                this.planeList.mergeFromCs(proto.getPlaneList());
            }
        }
        this.markAll();
        return SpyArmyDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SpyArmyDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasHeroList()) {
            this.getHeroList().mergeChangeFromCs(proto.getHeroList());
            fieldCnt++;
        }
        if (proto.hasSoldierList()) {
            this.getSoldierList().mergeChangeFromCs(proto.getSoldierList());
            fieldCnt++;
        }
        if (proto.hasMainHeroIdList()) {
            this.getMainHeroIdList().mergeChangeFromCs(proto.getMainHeroIdList());
            fieldCnt++;
        }
        if (proto.hasPlaneList()) {
            this.getPlaneList().mergeChangeFromCs(proto.getPlaneList());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SpyArmyData.Builder getCopyDbBuilder() {
        final SpyArmyData.Builder builder = SpyArmyData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SpyArmyData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.heroList != null) {
            Struct.HeroList.Builder tmpBuilder = Struct.HeroList.newBuilder();
            final int tmpFieldCnt = this.heroList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHeroList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHeroList();
            }
        }  else if (builder.hasHeroList()) {
            // 清理HeroList
            builder.clearHeroList();
            fieldCnt++;
        }
        if (this.soldierList != null) {
            Struct.SoldierList.Builder tmpBuilder = Struct.SoldierList.newBuilder();
            final int tmpFieldCnt = this.soldierList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldierList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldierList();
            }
        }  else if (builder.hasSoldierList()) {
            // 清理SoldierList
            builder.clearSoldierList();
            fieldCnt++;
        }
        if (this.mainHeroIdList != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.mainHeroIdList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMainHeroIdList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMainHeroIdList();
            }
        }  else if (builder.hasMainHeroIdList()) {
            // 清理MainHeroIdList
            builder.clearMainHeroIdList();
            fieldCnt++;
        }
        if (this.planeList != null) {
            Struct.SceneBattlePlaneList.Builder tmpBuilder = Struct.SceneBattlePlaneList.newBuilder();
            final int tmpFieldCnt = this.planeList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlaneList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlaneList();
            }
        }  else if (builder.hasPlaneList()) {
            // 清理PlaneList
            builder.clearPlaneList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SpyArmyData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HEROLIST) && this.heroList != null) {
            final boolean needClear = !builder.hasHeroList();
            final int tmpFieldCnt = this.heroList.copyChangeToDb(builder.getHeroListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHeroList();
            }
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERLIST) && this.soldierList != null) {
            final boolean needClear = !builder.hasSoldierList();
            final int tmpFieldCnt = this.soldierList.copyChangeToDb(builder.getSoldierListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldierList();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAINHEROIDLIST) && this.mainHeroIdList != null) {
            final boolean needClear = !builder.hasMainHeroIdList();
            final int tmpFieldCnt = this.mainHeroIdList.copyChangeToDb(builder.getMainHeroIdListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMainHeroIdList();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLANELIST) && this.planeList != null) {
            final boolean needClear = !builder.hasPlaneList();
            final int tmpFieldCnt = this.planeList.copyChangeToDb(builder.getPlaneListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlaneList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SpyArmyData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasHeroList()) {
            this.getHeroList().mergeFromDb(proto.getHeroList());
        } else {
            if (this.heroList != null) {
                this.heroList.mergeFromDb(proto.getHeroList());
            }
        }
        if (proto.hasSoldierList()) {
            this.getSoldierList().mergeFromDb(proto.getSoldierList());
        } else {
            if (this.soldierList != null) {
                this.soldierList.mergeFromDb(proto.getSoldierList());
            }
        }
        if (proto.hasMainHeroIdList()) {
            this.getMainHeroIdList().mergeFromDb(proto.getMainHeroIdList());
        } else {
            if (this.mainHeroIdList != null) {
                this.mainHeroIdList.mergeFromDb(proto.getMainHeroIdList());
            }
        }
        if (proto.hasPlaneList()) {
            this.getPlaneList().mergeFromDb(proto.getPlaneList());
        } else {
            if (this.planeList != null) {
                this.planeList.mergeFromDb(proto.getPlaneList());
            }
        }
        this.markAll();
        return SpyArmyDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SpyArmyData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasHeroList()) {
            this.getHeroList().mergeChangeFromDb(proto.getHeroList());
            fieldCnt++;
        }
        if (proto.hasSoldierList()) {
            this.getSoldierList().mergeChangeFromDb(proto.getSoldierList());
            fieldCnt++;
        }
        if (proto.hasMainHeroIdList()) {
            this.getMainHeroIdList().mergeChangeFromDb(proto.getMainHeroIdList());
            fieldCnt++;
        }
        if (proto.hasPlaneList()) {
            this.getPlaneList().mergeChangeFromDb(proto.getPlaneList());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SpyArmyData.Builder getCopySsBuilder() {
        final SpyArmyData.Builder builder = SpyArmyData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SpyArmyData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.heroList != null) {
            Struct.HeroList.Builder tmpBuilder = Struct.HeroList.newBuilder();
            final int tmpFieldCnt = this.heroList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHeroList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHeroList();
            }
        }  else if (builder.hasHeroList()) {
            // 清理HeroList
            builder.clearHeroList();
            fieldCnt++;
        }
        if (this.soldierList != null) {
            Struct.SoldierList.Builder tmpBuilder = Struct.SoldierList.newBuilder();
            final int tmpFieldCnt = this.soldierList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldierList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldierList();
            }
        }  else if (builder.hasSoldierList()) {
            // 清理SoldierList
            builder.clearSoldierList();
            fieldCnt++;
        }
        if (this.mainHeroIdList != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.mainHeroIdList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMainHeroIdList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMainHeroIdList();
            }
        }  else if (builder.hasMainHeroIdList()) {
            // 清理MainHeroIdList
            builder.clearMainHeroIdList();
            fieldCnt++;
        }
        if (this.planeList != null) {
            Struct.SceneBattlePlaneList.Builder tmpBuilder = Struct.SceneBattlePlaneList.newBuilder();
            final int tmpFieldCnt = this.planeList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlaneList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlaneList();
            }
        }  else if (builder.hasPlaneList()) {
            // 清理PlaneList
            builder.clearPlaneList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SpyArmyData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HEROLIST) && this.heroList != null) {
            final boolean needClear = !builder.hasHeroList();
            final int tmpFieldCnt = this.heroList.copyChangeToSs(builder.getHeroListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHeroList();
            }
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERLIST) && this.soldierList != null) {
            final boolean needClear = !builder.hasSoldierList();
            final int tmpFieldCnt = this.soldierList.copyChangeToSs(builder.getSoldierListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldierList();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAINHEROIDLIST) && this.mainHeroIdList != null) {
            final boolean needClear = !builder.hasMainHeroIdList();
            final int tmpFieldCnt = this.mainHeroIdList.copyChangeToSs(builder.getMainHeroIdListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMainHeroIdList();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLANELIST) && this.planeList != null) {
            final boolean needClear = !builder.hasPlaneList();
            final int tmpFieldCnt = this.planeList.copyChangeToSs(builder.getPlaneListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlaneList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SpyArmyData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasHeroList()) {
            this.getHeroList().mergeFromSs(proto.getHeroList());
        } else {
            if (this.heroList != null) {
                this.heroList.mergeFromSs(proto.getHeroList());
            }
        }
        if (proto.hasSoldierList()) {
            this.getSoldierList().mergeFromSs(proto.getSoldierList());
        } else {
            if (this.soldierList != null) {
                this.soldierList.mergeFromSs(proto.getSoldierList());
            }
        }
        if (proto.hasMainHeroIdList()) {
            this.getMainHeroIdList().mergeFromSs(proto.getMainHeroIdList());
        } else {
            if (this.mainHeroIdList != null) {
                this.mainHeroIdList.mergeFromSs(proto.getMainHeroIdList());
            }
        }
        if (proto.hasPlaneList()) {
            this.getPlaneList().mergeFromSs(proto.getPlaneList());
        } else {
            if (this.planeList != null) {
                this.planeList.mergeFromSs(proto.getPlaneList());
            }
        }
        this.markAll();
        return SpyArmyDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SpyArmyData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasHeroList()) {
            this.getHeroList().mergeChangeFromSs(proto.getHeroList());
            fieldCnt++;
        }
        if (proto.hasSoldierList()) {
            this.getSoldierList().mergeChangeFromSs(proto.getSoldierList());
            fieldCnt++;
        }
        if (proto.hasMainHeroIdList()) {
            this.getMainHeroIdList().mergeChangeFromSs(proto.getMainHeroIdList());
            fieldCnt++;
        }
        if (proto.hasPlaneList()) {
            this.getPlaneList().mergeChangeFromSs(proto.getPlaneList());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SpyArmyData.Builder builder = SpyArmyData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_HEROLIST) && this.heroList != null) {
            this.heroList.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERLIST) && this.soldierList != null) {
            this.soldierList.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_MAINHEROIDLIST) && this.mainHeroIdList != null) {
            this.mainHeroIdList.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLANELIST) && this.planeList != null) {
            this.planeList.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.heroList != null) {
            this.heroList.markAll();
        }
        if (this.soldierList != null) {
            this.soldierList.markAll();
        }
        if (this.mainHeroIdList != null) {
            this.mainHeroIdList.markAll();
        }
        if (this.planeList != null) {
            this.planeList.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SpyArmyDataProp)) {
            return false;
        }
        final SpyArmyDataProp otherNode = (SpyArmyDataProp) node;
        if (!this.getHeroList().compareDataTo(otherNode.getHeroList())) {
            return false;
        }
        if (!this.getSoldierList().compareDataTo(otherNode.getSoldierList())) {
            return false;
        }
        if (!this.getMainHeroIdList().compareDataTo(otherNode.getMainHeroIdList())) {
            return false;
        }
        if (!this.getPlaneList().compareDataTo(otherNode.getPlaneList())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.CampaignEvent;
import com.yorha.proto.PlayerPB.CampaignEventPB;


/**
 * <AUTHOR> auto gen
 */
public class CampaignEventProp extends AbstractPropNode {

    public static final int FIELD_INDEX_EVENTID = 0;
    public static final int FIELD_INDEX_EVENTTRAINUNITID = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int eventId = Constant.DEFAULT_INT_VALUE;
    private int eventTrainUnitId = Constant.DEFAULT_INT_VALUE;

    public CampaignEventProp() {
        super(null, 0, FIELD_COUNT);
    }

    public CampaignEventProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get eventId
     *
     * @return eventId value
     */
    public int getEventId() {
        return this.eventId;
    }

    /**
     * set eventId && set marked
     *
     * @param eventId new value
     * @return current object
     */
    public CampaignEventProp setEventId(int eventId) {
        if (this.eventId != eventId) {
            this.mark(FIELD_INDEX_EVENTID);
            this.eventId = eventId;
        }
        return this;
    }

    /**
     * inner set eventId
     *
     * @param eventId new value
     */
    private void innerSetEventId(int eventId) {
        this.eventId = eventId;
    }

    /**
     * get eventTrainUnitId
     *
     * @return eventTrainUnitId value
     */
    public int getEventTrainUnitId() {
        return this.eventTrainUnitId;
    }

    /**
     * set eventTrainUnitId && set marked
     *
     * @param eventTrainUnitId new value
     * @return current object
     */
    public CampaignEventProp setEventTrainUnitId(int eventTrainUnitId) {
        if (this.eventTrainUnitId != eventTrainUnitId) {
            this.mark(FIELD_INDEX_EVENTTRAINUNITID);
            this.eventTrainUnitId = eventTrainUnitId;
        }
        return this;
    }

    /**
     * inner set eventTrainUnitId
     *
     * @param eventTrainUnitId new value
     */
    private void innerSetEventTrainUnitId(int eventTrainUnitId) {
        this.eventTrainUnitId = eventTrainUnitId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignEventPB.Builder getCopyCsBuilder() {
        final CampaignEventPB.Builder builder = CampaignEventPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(CampaignEventPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getEventId() != 0) {
            builder.setEventId(this.getEventId());
            fieldCnt++;
        }  else if (builder.hasEventId()) {
            // 清理EventId
            builder.clearEventId();
            fieldCnt++;
        }
        if (this.getEventTrainUnitId() != 0) {
            builder.setEventTrainUnitId(this.getEventTrainUnitId());
            fieldCnt++;
        }  else if (builder.hasEventTrainUnitId()) {
            // 清理EventTrainUnitId
            builder.clearEventTrainUnitId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(CampaignEventPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_EVENTID)) {
            builder.setEventId(this.getEventId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EVENTTRAINUNITID)) {
            builder.setEventTrainUnitId(this.getEventTrainUnitId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(CampaignEventPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_EVENTID)) {
            builder.setEventId(this.getEventId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EVENTTRAINUNITID)) {
            builder.setEventTrainUnitId(this.getEventTrainUnitId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(CampaignEventPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasEventId()) {
            this.innerSetEventId(proto.getEventId());
        } else {
            this.innerSetEventId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEventTrainUnitId()) {
            this.innerSetEventTrainUnitId(proto.getEventTrainUnitId());
        } else {
            this.innerSetEventTrainUnitId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return CampaignEventProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(CampaignEventPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasEventId()) {
            this.setEventId(proto.getEventId());
            fieldCnt++;
        }
        if (proto.hasEventTrainUnitId()) {
            this.setEventTrainUnitId(proto.getEventTrainUnitId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignEvent.Builder getCopyDbBuilder() {
        final CampaignEvent.Builder builder = CampaignEvent.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(CampaignEvent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getEventId() != 0) {
            builder.setEventId(this.getEventId());
            fieldCnt++;
        }  else if (builder.hasEventId()) {
            // 清理EventId
            builder.clearEventId();
            fieldCnt++;
        }
        if (this.getEventTrainUnitId() != 0) {
            builder.setEventTrainUnitId(this.getEventTrainUnitId());
            fieldCnt++;
        }  else if (builder.hasEventTrainUnitId()) {
            // 清理EventTrainUnitId
            builder.clearEventTrainUnitId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(CampaignEvent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_EVENTID)) {
            builder.setEventId(this.getEventId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EVENTTRAINUNITID)) {
            builder.setEventTrainUnitId(this.getEventTrainUnitId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(CampaignEvent proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasEventId()) {
            this.innerSetEventId(proto.getEventId());
        } else {
            this.innerSetEventId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEventTrainUnitId()) {
            this.innerSetEventTrainUnitId(proto.getEventTrainUnitId());
        } else {
            this.innerSetEventTrainUnitId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return CampaignEventProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(CampaignEvent proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasEventId()) {
            this.setEventId(proto.getEventId());
            fieldCnt++;
        }
        if (proto.hasEventTrainUnitId()) {
            this.setEventTrainUnitId(proto.getEventTrainUnitId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignEvent.Builder getCopySsBuilder() {
        final CampaignEvent.Builder builder = CampaignEvent.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(CampaignEvent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getEventId() != 0) {
            builder.setEventId(this.getEventId());
            fieldCnt++;
        }  else if (builder.hasEventId()) {
            // 清理EventId
            builder.clearEventId();
            fieldCnt++;
        }
        if (this.getEventTrainUnitId() != 0) {
            builder.setEventTrainUnitId(this.getEventTrainUnitId());
            fieldCnt++;
        }  else if (builder.hasEventTrainUnitId()) {
            // 清理EventTrainUnitId
            builder.clearEventTrainUnitId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(CampaignEvent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_EVENTID)) {
            builder.setEventId(this.getEventId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EVENTTRAINUNITID)) {
            builder.setEventTrainUnitId(this.getEventTrainUnitId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(CampaignEvent proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasEventId()) {
            this.innerSetEventId(proto.getEventId());
        } else {
            this.innerSetEventId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEventTrainUnitId()) {
            this.innerSetEventTrainUnitId(proto.getEventTrainUnitId());
        } else {
            this.innerSetEventTrainUnitId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return CampaignEventProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(CampaignEvent proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasEventId()) {
            this.setEventId(proto.getEventId());
            fieldCnt++;
        }
        if (proto.hasEventTrainUnitId()) {
            this.setEventTrainUnitId(proto.getEventTrainUnitId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        CampaignEvent.Builder builder = CampaignEvent.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof CampaignEventProp)) {
            return false;
        }
        final CampaignEventProp otherNode = (CampaignEventProp) node;
        if (this.eventId != otherNode.eventId) {
            return false;
        }
        if (this.eventTrainUnitId != otherNode.eventTrainUnitId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
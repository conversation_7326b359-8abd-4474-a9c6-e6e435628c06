package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivitySelectGoodsRewardInfo;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ActivitySelectGoodsRewardInfoPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivitySelectGoodsRewardInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_SELECTREWARDID = 0;
    public static final int FIELD_INDEX_ITEMPAIR = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int selectRewardId = Constant.DEFAULT_INT_VALUE;
    private ItemPairProp itemPair = null;

    public ActivitySelectGoodsRewardInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivitySelectGoodsRewardInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get selectRewardId
     *
     * @return selectRewardId value
     */
    public int getSelectRewardId() {
        return this.selectRewardId;
    }

    /**
     * set selectRewardId && set marked
     *
     * @param selectRewardId new value
     * @return current object
     */
    public ActivitySelectGoodsRewardInfoProp setSelectRewardId(int selectRewardId) {
        if (this.selectRewardId != selectRewardId) {
            this.mark(FIELD_INDEX_SELECTREWARDID);
            this.selectRewardId = selectRewardId;
        }
        return this;
    }

    /**
     * inner set selectRewardId
     *
     * @param selectRewardId new value
     */
    private void innerSetSelectRewardId(int selectRewardId) {
        this.selectRewardId = selectRewardId;
    }

    /**
     * get itemPair
     *
     * @return itemPair value
     */
    public ItemPairProp getItemPair() {
        if (this.itemPair == null) {
            this.itemPair = new ItemPairProp(this, FIELD_INDEX_ITEMPAIR);
        }
        return this.itemPair;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivitySelectGoodsRewardInfoPB.Builder getCopyCsBuilder() {
        final ActivitySelectGoodsRewardInfoPB.Builder builder = ActivitySelectGoodsRewardInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivitySelectGoodsRewardInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSelectRewardId() != 0) {
            builder.setSelectRewardId(this.getSelectRewardId());
            fieldCnt++;
        }  else if (builder.hasSelectRewardId()) {
            // 清理SelectRewardId
            builder.clearSelectRewardId();
            fieldCnt++;
        }
        if (this.itemPair != null) {
            StructPB.ItemPairPB.Builder tmpBuilder = StructPB.ItemPairPB.newBuilder();
            final int tmpFieldCnt = this.itemPair.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItemPair(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItemPair();
            }
        }  else if (builder.hasItemPair()) {
            // 清理ItemPair
            builder.clearItemPair();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivitySelectGoodsRewardInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SELECTREWARDID)) {
            builder.setSelectRewardId(this.getSelectRewardId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMPAIR) && this.itemPair != null) {
            final boolean needClear = !builder.hasItemPair();
            final int tmpFieldCnt = this.itemPair.copyChangeToCs(builder.getItemPairBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemPair();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivitySelectGoodsRewardInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SELECTREWARDID)) {
            builder.setSelectRewardId(this.getSelectRewardId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMPAIR) && this.itemPair != null) {
            final boolean needClear = !builder.hasItemPair();
            final int tmpFieldCnt = this.itemPair.copyChangeToAndClearDeleteKeysCs(builder.getItemPairBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemPair();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivitySelectGoodsRewardInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSelectRewardId()) {
            this.innerSetSelectRewardId(proto.getSelectRewardId());
        } else {
            this.innerSetSelectRewardId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasItemPair()) {
            this.getItemPair().mergeFromCs(proto.getItemPair());
        } else {
            if (this.itemPair != null) {
                this.itemPair.mergeFromCs(proto.getItemPair());
            }
        }
        this.markAll();
        return ActivitySelectGoodsRewardInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivitySelectGoodsRewardInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSelectRewardId()) {
            this.setSelectRewardId(proto.getSelectRewardId());
            fieldCnt++;
        }
        if (proto.hasItemPair()) {
            this.getItemPair().mergeChangeFromCs(proto.getItemPair());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivitySelectGoodsRewardInfo.Builder getCopyDbBuilder() {
        final ActivitySelectGoodsRewardInfo.Builder builder = ActivitySelectGoodsRewardInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivitySelectGoodsRewardInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSelectRewardId() != 0) {
            builder.setSelectRewardId(this.getSelectRewardId());
            fieldCnt++;
        }  else if (builder.hasSelectRewardId()) {
            // 清理SelectRewardId
            builder.clearSelectRewardId();
            fieldCnt++;
        }
        if (this.itemPair != null) {
            Struct.ItemPair.Builder tmpBuilder = Struct.ItemPair.newBuilder();
            final int tmpFieldCnt = this.itemPair.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItemPair(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItemPair();
            }
        }  else if (builder.hasItemPair()) {
            // 清理ItemPair
            builder.clearItemPair();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivitySelectGoodsRewardInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SELECTREWARDID)) {
            builder.setSelectRewardId(this.getSelectRewardId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMPAIR) && this.itemPair != null) {
            final boolean needClear = !builder.hasItemPair();
            final int tmpFieldCnt = this.itemPair.copyChangeToDb(builder.getItemPairBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemPair();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivitySelectGoodsRewardInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSelectRewardId()) {
            this.innerSetSelectRewardId(proto.getSelectRewardId());
        } else {
            this.innerSetSelectRewardId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasItemPair()) {
            this.getItemPair().mergeFromDb(proto.getItemPair());
        } else {
            if (this.itemPair != null) {
                this.itemPair.mergeFromDb(proto.getItemPair());
            }
        }
        this.markAll();
        return ActivitySelectGoodsRewardInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivitySelectGoodsRewardInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSelectRewardId()) {
            this.setSelectRewardId(proto.getSelectRewardId());
            fieldCnt++;
        }
        if (proto.hasItemPair()) {
            this.getItemPair().mergeChangeFromDb(proto.getItemPair());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivitySelectGoodsRewardInfo.Builder getCopySsBuilder() {
        final ActivitySelectGoodsRewardInfo.Builder builder = ActivitySelectGoodsRewardInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivitySelectGoodsRewardInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSelectRewardId() != 0) {
            builder.setSelectRewardId(this.getSelectRewardId());
            fieldCnt++;
        }  else if (builder.hasSelectRewardId()) {
            // 清理SelectRewardId
            builder.clearSelectRewardId();
            fieldCnt++;
        }
        if (this.itemPair != null) {
            Struct.ItemPair.Builder tmpBuilder = Struct.ItemPair.newBuilder();
            final int tmpFieldCnt = this.itemPair.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItemPair(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItemPair();
            }
        }  else if (builder.hasItemPair()) {
            // 清理ItemPair
            builder.clearItemPair();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivitySelectGoodsRewardInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SELECTREWARDID)) {
            builder.setSelectRewardId(this.getSelectRewardId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMPAIR) && this.itemPair != null) {
            final boolean needClear = !builder.hasItemPair();
            final int tmpFieldCnt = this.itemPair.copyChangeToSs(builder.getItemPairBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemPair();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivitySelectGoodsRewardInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSelectRewardId()) {
            this.innerSetSelectRewardId(proto.getSelectRewardId());
        } else {
            this.innerSetSelectRewardId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasItemPair()) {
            this.getItemPair().mergeFromSs(proto.getItemPair());
        } else {
            if (this.itemPair != null) {
                this.itemPair.mergeFromSs(proto.getItemPair());
            }
        }
        this.markAll();
        return ActivitySelectGoodsRewardInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivitySelectGoodsRewardInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSelectRewardId()) {
            this.setSelectRewardId(proto.getSelectRewardId());
            fieldCnt++;
        }
        if (proto.hasItemPair()) {
            this.getItemPair().mergeChangeFromSs(proto.getItemPair());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivitySelectGoodsRewardInfo.Builder builder = ActivitySelectGoodsRewardInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ITEMPAIR) && this.itemPair != null) {
            this.itemPair.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.itemPair != null) {
            this.itemPair.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.selectRewardId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivitySelectGoodsRewardInfoProp)) {
            return false;
        }
        final ActivitySelectGoodsRewardInfoProp otherNode = (ActivitySelectGoodsRewardInfoProp) node;
        if (this.selectRewardId != otherNode.selectRewardId) {
            return false;
        }
        if (!this.getItemPair().compareDataTo(otherNode.getItemPair())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructBattlePB.DisplayRewardListPB;
import com.yorha.proto.StructBattle.DisplayRewardList;
import com.yorha.proto.StructBattlePB.DisplayRewardPB;
import com.yorha.proto.StructBattle.DisplayReward;

/**
 * <AUTHOR> auto gen
 */
public class DisplayRewardListProp extends AbstractListNode<DisplayRewardProp> {
    /**
     * Create a DisplayRewardListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public DisplayRewardListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to DisplayRewardListProp
     *
     * @return new object
     */
    @Override
    public DisplayRewardProp addEmptyValue() {
        final DisplayRewardProp newProp = new DisplayRewardProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DisplayRewardListPB.Builder getCopyCsBuilder() {
        final DisplayRewardListPB.Builder builder = DisplayRewardListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(DisplayRewardListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return DisplayRewardListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final DisplayRewardProp v : this) {
            final DisplayRewardPB.Builder itemBuilder = DisplayRewardPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return DisplayRewardListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(DisplayRewardListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return DisplayRewardListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(DisplayRewardListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (DisplayRewardPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return DisplayRewardListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(DisplayRewardListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DisplayRewardList.Builder getCopyDbBuilder() {
        final DisplayRewardList.Builder builder = DisplayRewardList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(DisplayRewardList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return DisplayRewardListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final DisplayRewardProp v : this) {
            final DisplayReward.Builder itemBuilder = DisplayReward.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return DisplayRewardListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(DisplayRewardList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return DisplayRewardListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(DisplayRewardList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (DisplayReward v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return DisplayRewardListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(DisplayRewardList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DisplayRewardList.Builder getCopySsBuilder() {
        final DisplayRewardList.Builder builder = DisplayRewardList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(DisplayRewardList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return DisplayRewardListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final DisplayRewardProp v : this) {
            final DisplayReward.Builder itemBuilder = DisplayReward.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return DisplayRewardListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(DisplayRewardList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return DisplayRewardListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(DisplayRewardList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (DisplayReward v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return DisplayRewardListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(DisplayRewardList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        DisplayRewardList.Builder builder = DisplayRewardList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
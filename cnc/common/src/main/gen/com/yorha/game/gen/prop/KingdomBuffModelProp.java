package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zone.KingdomBuffModel;
import com.yorha.proto.ZonePB.KingdomBuffModelPB;


/**
 * <AUTHOR> auto gen
 */
public class KingdomBuffModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ACTIVEBUFFID = 0;
    public static final int FIELD_INDEX_BUFFENDTSMS = 1;
    public static final int FIELD_INDEX_CDENDTSMS = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int activeBuffId = Constant.DEFAULT_INT_VALUE;
    private long buffEndTsMs = Constant.DEFAULT_LONG_VALUE;
    private long cdEndTsMs = Constant.DEFAULT_LONG_VALUE;

    public KingdomBuffModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public KingdomBuffModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get activeBuffId
     *
     * @return activeBuffId value
     */
    public int getActiveBuffId() {
        return this.activeBuffId;
    }

    /**
     * set activeBuffId && set marked
     *
     * @param activeBuffId new value
     * @return current object
     */
    public KingdomBuffModelProp setActiveBuffId(int activeBuffId) {
        if (this.activeBuffId != activeBuffId) {
            this.mark(FIELD_INDEX_ACTIVEBUFFID);
            this.activeBuffId = activeBuffId;
        }
        return this;
    }

    /**
     * inner set activeBuffId
     *
     * @param activeBuffId new value
     */
    private void innerSetActiveBuffId(int activeBuffId) {
        this.activeBuffId = activeBuffId;
    }

    /**
     * get buffEndTsMs
     *
     * @return buffEndTsMs value
     */
    public long getBuffEndTsMs() {
        return this.buffEndTsMs;
    }

    /**
     * set buffEndTsMs && set marked
     *
     * @param buffEndTsMs new value
     * @return current object
     */
    public KingdomBuffModelProp setBuffEndTsMs(long buffEndTsMs) {
        if (this.buffEndTsMs != buffEndTsMs) {
            this.mark(FIELD_INDEX_BUFFENDTSMS);
            this.buffEndTsMs = buffEndTsMs;
        }
        return this;
    }

    /**
     * inner set buffEndTsMs
     *
     * @param buffEndTsMs new value
     */
    private void innerSetBuffEndTsMs(long buffEndTsMs) {
        this.buffEndTsMs = buffEndTsMs;
    }

    /**
     * get cdEndTsMs
     *
     * @return cdEndTsMs value
     */
    public long getCdEndTsMs() {
        return this.cdEndTsMs;
    }

    /**
     * set cdEndTsMs && set marked
     *
     * @param cdEndTsMs new value
     * @return current object
     */
    public KingdomBuffModelProp setCdEndTsMs(long cdEndTsMs) {
        if (this.cdEndTsMs != cdEndTsMs) {
            this.mark(FIELD_INDEX_CDENDTSMS);
            this.cdEndTsMs = cdEndTsMs;
        }
        return this;
    }

    /**
     * inner set cdEndTsMs
     *
     * @param cdEndTsMs new value
     */
    private void innerSetCdEndTsMs(long cdEndTsMs) {
        this.cdEndTsMs = cdEndTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomBuffModelPB.Builder getCopyCsBuilder() {
        final KingdomBuffModelPB.Builder builder = KingdomBuffModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(KingdomBuffModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActiveBuffId() != 0) {
            builder.setActiveBuffId(this.getActiveBuffId());
            fieldCnt++;
        }  else if (builder.hasActiveBuffId()) {
            // 清理ActiveBuffId
            builder.clearActiveBuffId();
            fieldCnt++;
        }
        if (this.getBuffEndTsMs() != 0L) {
            builder.setBuffEndTsMs(this.getBuffEndTsMs());
            fieldCnt++;
        }  else if (builder.hasBuffEndTsMs()) {
            // 清理BuffEndTsMs
            builder.clearBuffEndTsMs();
            fieldCnt++;
        }
        if (this.getCdEndTsMs() != 0L) {
            builder.setCdEndTsMs(this.getCdEndTsMs());
            fieldCnt++;
        }  else if (builder.hasCdEndTsMs()) {
            // 清理CdEndTsMs
            builder.clearCdEndTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(KingdomBuffModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVEBUFFID)) {
            builder.setActiveBuffId(this.getActiveBuffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUFFENDTSMS)) {
            builder.setBuffEndTsMs(this.getBuffEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CDENDTSMS)) {
            builder.setCdEndTsMs(this.getCdEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(KingdomBuffModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVEBUFFID)) {
            builder.setActiveBuffId(this.getActiveBuffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUFFENDTSMS)) {
            builder.setBuffEndTsMs(this.getBuffEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CDENDTSMS)) {
            builder.setCdEndTsMs(this.getCdEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(KingdomBuffModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActiveBuffId()) {
            this.innerSetActiveBuffId(proto.getActiveBuffId());
        } else {
            this.innerSetActiveBuffId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuffEndTsMs()) {
            this.innerSetBuffEndTsMs(proto.getBuffEndTsMs());
        } else {
            this.innerSetBuffEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCdEndTsMs()) {
            this.innerSetCdEndTsMs(proto.getCdEndTsMs());
        } else {
            this.innerSetCdEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return KingdomBuffModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(KingdomBuffModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActiveBuffId()) {
            this.setActiveBuffId(proto.getActiveBuffId());
            fieldCnt++;
        }
        if (proto.hasBuffEndTsMs()) {
            this.setBuffEndTsMs(proto.getBuffEndTsMs());
            fieldCnt++;
        }
        if (proto.hasCdEndTsMs()) {
            this.setCdEndTsMs(proto.getCdEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomBuffModel.Builder getCopyDbBuilder() {
        final KingdomBuffModel.Builder builder = KingdomBuffModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(KingdomBuffModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActiveBuffId() != 0) {
            builder.setActiveBuffId(this.getActiveBuffId());
            fieldCnt++;
        }  else if (builder.hasActiveBuffId()) {
            // 清理ActiveBuffId
            builder.clearActiveBuffId();
            fieldCnt++;
        }
        if (this.getBuffEndTsMs() != 0L) {
            builder.setBuffEndTsMs(this.getBuffEndTsMs());
            fieldCnt++;
        }  else if (builder.hasBuffEndTsMs()) {
            // 清理BuffEndTsMs
            builder.clearBuffEndTsMs();
            fieldCnt++;
        }
        if (this.getCdEndTsMs() != 0L) {
            builder.setCdEndTsMs(this.getCdEndTsMs());
            fieldCnt++;
        }  else if (builder.hasCdEndTsMs()) {
            // 清理CdEndTsMs
            builder.clearCdEndTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(KingdomBuffModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVEBUFFID)) {
            builder.setActiveBuffId(this.getActiveBuffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUFFENDTSMS)) {
            builder.setBuffEndTsMs(this.getBuffEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CDENDTSMS)) {
            builder.setCdEndTsMs(this.getCdEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(KingdomBuffModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActiveBuffId()) {
            this.innerSetActiveBuffId(proto.getActiveBuffId());
        } else {
            this.innerSetActiveBuffId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuffEndTsMs()) {
            this.innerSetBuffEndTsMs(proto.getBuffEndTsMs());
        } else {
            this.innerSetBuffEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCdEndTsMs()) {
            this.innerSetCdEndTsMs(proto.getCdEndTsMs());
        } else {
            this.innerSetCdEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return KingdomBuffModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(KingdomBuffModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActiveBuffId()) {
            this.setActiveBuffId(proto.getActiveBuffId());
            fieldCnt++;
        }
        if (proto.hasBuffEndTsMs()) {
            this.setBuffEndTsMs(proto.getBuffEndTsMs());
            fieldCnt++;
        }
        if (proto.hasCdEndTsMs()) {
            this.setCdEndTsMs(proto.getCdEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomBuffModel.Builder getCopySsBuilder() {
        final KingdomBuffModel.Builder builder = KingdomBuffModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(KingdomBuffModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActiveBuffId() != 0) {
            builder.setActiveBuffId(this.getActiveBuffId());
            fieldCnt++;
        }  else if (builder.hasActiveBuffId()) {
            // 清理ActiveBuffId
            builder.clearActiveBuffId();
            fieldCnt++;
        }
        if (this.getBuffEndTsMs() != 0L) {
            builder.setBuffEndTsMs(this.getBuffEndTsMs());
            fieldCnt++;
        }  else if (builder.hasBuffEndTsMs()) {
            // 清理BuffEndTsMs
            builder.clearBuffEndTsMs();
            fieldCnt++;
        }
        if (this.getCdEndTsMs() != 0L) {
            builder.setCdEndTsMs(this.getCdEndTsMs());
            fieldCnt++;
        }  else if (builder.hasCdEndTsMs()) {
            // 清理CdEndTsMs
            builder.clearCdEndTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(KingdomBuffModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVEBUFFID)) {
            builder.setActiveBuffId(this.getActiveBuffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUFFENDTSMS)) {
            builder.setBuffEndTsMs(this.getBuffEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CDENDTSMS)) {
            builder.setCdEndTsMs(this.getCdEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(KingdomBuffModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActiveBuffId()) {
            this.innerSetActiveBuffId(proto.getActiveBuffId());
        } else {
            this.innerSetActiveBuffId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuffEndTsMs()) {
            this.innerSetBuffEndTsMs(proto.getBuffEndTsMs());
        } else {
            this.innerSetBuffEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCdEndTsMs()) {
            this.innerSetCdEndTsMs(proto.getCdEndTsMs());
        } else {
            this.innerSetCdEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return KingdomBuffModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(KingdomBuffModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActiveBuffId()) {
            this.setActiveBuffId(proto.getActiveBuffId());
            fieldCnt++;
        }
        if (proto.hasBuffEndTsMs()) {
            this.setBuffEndTsMs(proto.getBuffEndTsMs());
            fieldCnt++;
        }
        if (proto.hasCdEndTsMs()) {
            this.setCdEndTsMs(proto.getCdEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        KingdomBuffModel.Builder builder = KingdomBuffModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof KingdomBuffModelProp)) {
            return false;
        }
        final KingdomBuffModelProp otherNode = (KingdomBuffModelProp) node;
        if (this.activeBuffId != otherNode.activeBuffId) {
            return false;
        }
        if (this.buffEndTsMs != otherNode.buffEndTsMs) {
            return false;
        }
        if (this.cdEndTsMs != otherNode.cdEndTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
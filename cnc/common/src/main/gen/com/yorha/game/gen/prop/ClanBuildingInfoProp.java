package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructClan.ClanBuildingInfo;
import com.yorha.proto.StructClanPB.ClanBuildingInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanBuildingInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_NUM = 1;
    public static final int FIELD_INDEX_BUILTNUM = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int id = Constant.DEFAULT_INT_VALUE;
    private int num = Constant.DEFAULT_INT_VALUE;
    private int builtNum = Constant.DEFAULT_INT_VALUE;

    public ClanBuildingInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanBuildingInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public int getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public ClanBuildingInfoProp setId(int id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(int id) {
        this.id = id;
    }

    /**
     * get num
     *
     * @return num value
     */
    public int getNum() {
        return this.num;
    }

    /**
     * set num && set marked
     *
     * @param num new value
     * @return current object
     */
    public ClanBuildingInfoProp setNum(int num) {
        if (this.num != num) {
            this.mark(FIELD_INDEX_NUM);
            this.num = num;
        }
        return this;
    }

    /**
     * inner set num
     *
     * @param num new value
     */
    private void innerSetNum(int num) {
        this.num = num;
    }

    /**
     * get builtNum
     *
     * @return builtNum value
     */
    public int getBuiltNum() {
        return this.builtNum;
    }

    /**
     * set builtNum && set marked
     *
     * @param builtNum new value
     * @return current object
     */
    public ClanBuildingInfoProp setBuiltNum(int builtNum) {
        if (this.builtNum != builtNum) {
            this.mark(FIELD_INDEX_BUILTNUM);
            this.builtNum = builtNum;
        }
        return this;
    }

    /**
     * inner set builtNum
     *
     * @param builtNum new value
     */
    private void innerSetBuiltNum(int builtNum) {
        this.builtNum = builtNum;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanBuildingInfoPB.Builder getCopyCsBuilder() {
        final ClanBuildingInfoPB.Builder builder = ClanBuildingInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanBuildingInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        if (this.getBuiltNum() != 0) {
            builder.setBuiltNum(this.getBuiltNum());
            fieldCnt++;
        }  else if (builder.hasBuiltNum()) {
            // 清理BuiltNum
            builder.clearBuiltNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanBuildingInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILTNUM)) {
            builder.setBuiltNum(this.getBuiltNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanBuildingInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILTNUM)) {
            builder.setBuiltNum(this.getBuiltNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanBuildingInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuiltNum()) {
            this.innerSetBuiltNum(proto.getBuiltNum());
        } else {
            this.innerSetBuiltNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanBuildingInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanBuildingInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        if (proto.hasBuiltNum()) {
            this.setBuiltNum(proto.getBuiltNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanBuildingInfo.Builder getCopyDbBuilder() {
        final ClanBuildingInfo.Builder builder = ClanBuildingInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanBuildingInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        if (this.getBuiltNum() != 0) {
            builder.setBuiltNum(this.getBuiltNum());
            fieldCnt++;
        }  else if (builder.hasBuiltNum()) {
            // 清理BuiltNum
            builder.clearBuiltNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanBuildingInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILTNUM)) {
            builder.setBuiltNum(this.getBuiltNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanBuildingInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuiltNum()) {
            this.innerSetBuiltNum(proto.getBuiltNum());
        } else {
            this.innerSetBuiltNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanBuildingInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanBuildingInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        if (proto.hasBuiltNum()) {
            this.setBuiltNum(proto.getBuiltNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanBuildingInfo.Builder getCopySsBuilder() {
        final ClanBuildingInfo.Builder builder = ClanBuildingInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanBuildingInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        if (this.getBuiltNum() != 0) {
            builder.setBuiltNum(this.getBuiltNum());
            fieldCnt++;
        }  else if (builder.hasBuiltNum()) {
            // 清理BuiltNum
            builder.clearBuiltNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanBuildingInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILTNUM)) {
            builder.setBuiltNum(this.getBuiltNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanBuildingInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuiltNum()) {
            this.innerSetBuiltNum(proto.getBuiltNum());
        } else {
            this.innerSetBuiltNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanBuildingInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanBuildingInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        if (proto.hasBuiltNum()) {
            this.setBuiltNum(proto.getBuiltNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanBuildingInfo.Builder builder = ClanBuildingInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanBuildingInfoProp)) {
            return false;
        }
        final ClanBuildingInfoProp otherNode = (ClanBuildingInfoProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.num != otherNode.num) {
            return false;
        }
        if (this.builtNum != otherNode.builtNum) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.SysAssistInfo;
import com.yorha.proto.Basic;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.SysAssistInfoPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class SysAssistInfoProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_EXPIRATION = 0;
    public static final int FIELD_INDEX_MAILID = 1;
    public static final int FIELD_INDEX_TARGETCLANSHORTNAME = 2;
    public static final int FIELD_INDEX_TARGETPLAYERNAME = 3;
    public static final int FIELD_INDEX_ASSISTPLAYERNAME = 4;
    public static final int FIELD_INDEX_ITEMREWARD = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private long expiration = Constant.DEFAULT_LONG_VALUE;
    private int mailId = Constant.DEFAULT_INT_VALUE;
    private String targetClanShortName = Constant.DEFAULT_STR_VALUE;
    private String targetPlayerName = Constant.DEFAULT_STR_VALUE;
    private StringListProp assistPlayerName = null;
    private ItemPairListProp itemReward = null;

    public SysAssistInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SysAssistInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get expiration
     *
     * @return expiration value
     */
    public long getExpiration() {
        return this.expiration;
    }

    /**
     * set expiration && set marked
     *
     * @param expiration new value
     * @return current object
     */
    public SysAssistInfoProp setExpiration(long expiration) {
        if (this.expiration != expiration) {
            this.mark(FIELD_INDEX_EXPIRATION);
            this.expiration = expiration;
        }
        return this;
    }

    /**
     * inner set expiration
     *
     * @param expiration new value
     */
    private void innerSetExpiration(long expiration) {
        this.expiration = expiration;
    }

    /**
     * get mailId
     *
     * @return mailId value
     */
    public int getMailId() {
        return this.mailId;
    }

    /**
     * set mailId && set marked
     *
     * @param mailId new value
     * @return current object
     */
    public SysAssistInfoProp setMailId(int mailId) {
        if (this.mailId != mailId) {
            this.mark(FIELD_INDEX_MAILID);
            this.mailId = mailId;
        }
        return this;
    }

    /**
     * inner set mailId
     *
     * @param mailId new value
     */
    private void innerSetMailId(int mailId) {
        this.mailId = mailId;
    }

    /**
     * get targetClanShortName
     *
     * @return targetClanShortName value
     */
    public String getTargetClanShortName() {
        return this.targetClanShortName;
    }

    /**
     * set targetClanShortName && set marked
     *
     * @param targetClanShortName new value
     * @return current object
     */
    public SysAssistInfoProp setTargetClanShortName(String targetClanShortName) {
        if (targetClanShortName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.targetClanShortName, targetClanShortName)) {
            this.mark(FIELD_INDEX_TARGETCLANSHORTNAME);
            this.targetClanShortName = targetClanShortName;
        }
        return this;
    }

    /**
     * inner set targetClanShortName
     *
     * @param targetClanShortName new value
     */
    private void innerSetTargetClanShortName(String targetClanShortName) {
        this.targetClanShortName = targetClanShortName;
    }

    /**
     * get targetPlayerName
     *
     * @return targetPlayerName value
     */
    public String getTargetPlayerName() {
        return this.targetPlayerName;
    }

    /**
     * set targetPlayerName && set marked
     *
     * @param targetPlayerName new value
     * @return current object
     */
    public SysAssistInfoProp setTargetPlayerName(String targetPlayerName) {
        if (targetPlayerName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.targetPlayerName, targetPlayerName)) {
            this.mark(FIELD_INDEX_TARGETPLAYERNAME);
            this.targetPlayerName = targetPlayerName;
        }
        return this;
    }

    /**
     * inner set targetPlayerName
     *
     * @param targetPlayerName new value
     */
    private void innerSetTargetPlayerName(String targetPlayerName) {
        this.targetPlayerName = targetPlayerName;
    }

    /**
     * get assistPlayerName
     *
     * @return assistPlayerName value
     */
    public StringListProp getAssistPlayerName() {
        if (this.assistPlayerName == null) {
            this.assistPlayerName = new StringListProp(this, FIELD_INDEX_ASSISTPLAYERNAME);
        }
        return this.assistPlayerName;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addAssistPlayerName(String v) {
        this.getAssistPlayerName().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public String getAssistPlayerNameIndex(int index) {
        return this.getAssistPlayerName().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public String removeAssistPlayerName(String v) {
        if (this.assistPlayerName == null) {
            return null;
        }
        if(this.assistPlayerName.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getAssistPlayerNameSize() {
        if (this.assistPlayerName == null) {
            return 0;
        }
        return this.assistPlayerName.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isAssistPlayerNameEmpty() {
        if (this.assistPlayerName == null) {
            return true;
        }
        return this.getAssistPlayerName().isEmpty();
    }

    /**
     * clear list
     */
    public void clearAssistPlayerName() {
        this.getAssistPlayerName().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public String removeAssistPlayerNameIndex(int index) {
        return this.getAssistPlayerName().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public String setAssistPlayerNameIndex(int index, String v) {
        return this.getAssistPlayerName().set(index, v);
    }
    /**
     * get itemReward
     *
     * @return itemReward value
     */
    public ItemPairListProp getItemReward() {
        if (this.itemReward == null) {
            this.itemReward = new ItemPairListProp(this, FIELD_INDEX_ITEMREWARD);
        }
        return this.itemReward;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addItemReward(ItemPairProp v) {
        this.getItemReward().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ItemPairProp getItemRewardIndex(int index) {
        return this.getItemReward().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public ItemPairProp removeItemReward(ItemPairProp v) {
        if (this.itemReward == null) {
            return null;
        }
        if(this.itemReward.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getItemRewardSize() {
        if (this.itemReward == null) {
            return 0;
        }
        return this.itemReward.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isItemRewardEmpty() {
        if (this.itemReward == null) {
            return true;
        }
        return this.getItemReward().isEmpty();
    }

    /**
     * clear list
     */
    public void clearItemReward() {
        this.getItemReward().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ItemPairProp removeItemRewardIndex(int index) {
        return this.getItemReward().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public ItemPairProp setItemRewardIndex(int index, ItemPairProp v) {
        return this.getItemReward().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SysAssistInfoPB.Builder getCopyCsBuilder() {
        final SysAssistInfoPB.Builder builder = SysAssistInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SysAssistInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getExpiration() != 0L) {
            builder.setExpiration(this.getExpiration());
            fieldCnt++;
        }  else if (builder.hasExpiration()) {
            // 清理Expiration
            builder.clearExpiration();
            fieldCnt++;
        }
        if (!this.getTargetClanShortName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTargetClanShortName(this.getTargetClanShortName());
            fieldCnt++;
        }  else if (builder.hasTargetClanShortName()) {
            // 清理TargetClanShortName
            builder.clearTargetClanShortName();
            fieldCnt++;
        }
        if (!this.getTargetPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTargetPlayerName(this.getTargetPlayerName());
            fieldCnt++;
        }  else if (builder.hasTargetPlayerName()) {
            // 清理TargetPlayerName
            builder.clearTargetPlayerName();
            fieldCnt++;
        }
        if (this.assistPlayerName != null) {
            BasicPB.StringListPB.Builder tmpBuilder = BasicPB.StringListPB.newBuilder();
            final int tmpFieldCnt = this.assistPlayerName.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAssistPlayerName(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAssistPlayerName();
            }
        }  else if (builder.hasAssistPlayerName()) {
            // 清理AssistPlayerName
            builder.clearAssistPlayerName();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SysAssistInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_EXPIRATION)) {
            builder.setExpiration(this.getExpiration());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETCLANSHORTNAME)) {
            builder.setTargetClanShortName(this.getTargetClanShortName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETPLAYERNAME)) {
            builder.setTargetPlayerName(this.getTargetPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ASSISTPLAYERNAME) && this.assistPlayerName != null) {
            final boolean needClear = !builder.hasAssistPlayerName();
            final int tmpFieldCnt = this.assistPlayerName.copyChangeToCs(builder.getAssistPlayerNameBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAssistPlayerName();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SysAssistInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_EXPIRATION)) {
            builder.setExpiration(this.getExpiration());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETCLANSHORTNAME)) {
            builder.setTargetClanShortName(this.getTargetClanShortName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETPLAYERNAME)) {
            builder.setTargetPlayerName(this.getTargetPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ASSISTPLAYERNAME) && this.assistPlayerName != null) {
            final boolean needClear = !builder.hasAssistPlayerName();
            final int tmpFieldCnt = this.assistPlayerName.copyChangeToCs(builder.getAssistPlayerNameBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAssistPlayerName();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SysAssistInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasExpiration()) {
            this.innerSetExpiration(proto.getExpiration());
        } else {
            this.innerSetExpiration(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTargetClanShortName()) {
            this.innerSetTargetClanShortName(proto.getTargetClanShortName());
        } else {
            this.innerSetTargetClanShortName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTargetPlayerName()) {
            this.innerSetTargetPlayerName(proto.getTargetPlayerName());
        } else {
            this.innerSetTargetPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasAssistPlayerName()) {
            this.getAssistPlayerName().mergeFromCs(proto.getAssistPlayerName());
        } else {
            if (this.assistPlayerName != null) {
                this.assistPlayerName.mergeFromCs(proto.getAssistPlayerName());
            }
        }
        this.markAll();
        return SysAssistInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SysAssistInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasExpiration()) {
            this.setExpiration(proto.getExpiration());
            fieldCnt++;
        }
        if (proto.hasTargetClanShortName()) {
            this.setTargetClanShortName(proto.getTargetClanShortName());
            fieldCnt++;
        }
        if (proto.hasTargetPlayerName()) {
            this.setTargetPlayerName(proto.getTargetPlayerName());
            fieldCnt++;
        }
        if (proto.hasAssistPlayerName()) {
            this.getAssistPlayerName().mergeChangeFromCs(proto.getAssistPlayerName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SysAssistInfo.Builder getCopyDbBuilder() {
        final SysAssistInfo.Builder builder = SysAssistInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SysAssistInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getExpiration() != 0L) {
            builder.setExpiration(this.getExpiration());
            fieldCnt++;
        }  else if (builder.hasExpiration()) {
            // 清理Expiration
            builder.clearExpiration();
            fieldCnt++;
        }
        if (this.getMailId() != 0) {
            builder.setMailId(this.getMailId());
            fieldCnt++;
        }  else if (builder.hasMailId()) {
            // 清理MailId
            builder.clearMailId();
            fieldCnt++;
        }
        if (!this.getTargetClanShortName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTargetClanShortName(this.getTargetClanShortName());
            fieldCnt++;
        }  else if (builder.hasTargetClanShortName()) {
            // 清理TargetClanShortName
            builder.clearTargetClanShortName();
            fieldCnt++;
        }
        if (!this.getTargetPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTargetPlayerName(this.getTargetPlayerName());
            fieldCnt++;
        }  else if (builder.hasTargetPlayerName()) {
            // 清理TargetPlayerName
            builder.clearTargetPlayerName();
            fieldCnt++;
        }
        if (this.assistPlayerName != null) {
            Basic.StringList.Builder tmpBuilder = Basic.StringList.newBuilder();
            final int tmpFieldCnt = this.assistPlayerName.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAssistPlayerName(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAssistPlayerName();
            }
        }  else if (builder.hasAssistPlayerName()) {
            // 清理AssistPlayerName
            builder.clearAssistPlayerName();
            fieldCnt++;
        }
        if (this.itemReward != null) {
            Struct.ItemPairList.Builder tmpBuilder = Struct.ItemPairList.newBuilder();
            final int tmpFieldCnt = this.itemReward.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItemReward(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItemReward();
            }
        }  else if (builder.hasItemReward()) {
            // 清理ItemReward
            builder.clearItemReward();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SysAssistInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_EXPIRATION)) {
            builder.setExpiration(this.getExpiration());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAILID)) {
            builder.setMailId(this.getMailId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETCLANSHORTNAME)) {
            builder.setTargetClanShortName(this.getTargetClanShortName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETPLAYERNAME)) {
            builder.setTargetPlayerName(this.getTargetPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ASSISTPLAYERNAME) && this.assistPlayerName != null) {
            final boolean needClear = !builder.hasAssistPlayerName();
            final int tmpFieldCnt = this.assistPlayerName.copyChangeToDb(builder.getAssistPlayerNameBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAssistPlayerName();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            final boolean needClear = !builder.hasItemReward();
            final int tmpFieldCnt = this.itemReward.copyChangeToDb(builder.getItemRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemReward();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SysAssistInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasExpiration()) {
            this.innerSetExpiration(proto.getExpiration());
        } else {
            this.innerSetExpiration(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMailId()) {
            this.innerSetMailId(proto.getMailId());
        } else {
            this.innerSetMailId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTargetClanShortName()) {
            this.innerSetTargetClanShortName(proto.getTargetClanShortName());
        } else {
            this.innerSetTargetClanShortName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTargetPlayerName()) {
            this.innerSetTargetPlayerName(proto.getTargetPlayerName());
        } else {
            this.innerSetTargetPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasAssistPlayerName()) {
            this.getAssistPlayerName().mergeFromDb(proto.getAssistPlayerName());
        } else {
            if (this.assistPlayerName != null) {
                this.assistPlayerName.mergeFromDb(proto.getAssistPlayerName());
            }
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeFromDb(proto.getItemReward());
        } else {
            if (this.itemReward != null) {
                this.itemReward.mergeFromDb(proto.getItemReward());
            }
        }
        this.markAll();
        return SysAssistInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SysAssistInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasExpiration()) {
            this.setExpiration(proto.getExpiration());
            fieldCnt++;
        }
        if (proto.hasMailId()) {
            this.setMailId(proto.getMailId());
            fieldCnt++;
        }
        if (proto.hasTargetClanShortName()) {
            this.setTargetClanShortName(proto.getTargetClanShortName());
            fieldCnt++;
        }
        if (proto.hasTargetPlayerName()) {
            this.setTargetPlayerName(proto.getTargetPlayerName());
            fieldCnt++;
        }
        if (proto.hasAssistPlayerName()) {
            this.getAssistPlayerName().mergeChangeFromDb(proto.getAssistPlayerName());
            fieldCnt++;
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeChangeFromDb(proto.getItemReward());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SysAssistInfo.Builder getCopySsBuilder() {
        final SysAssistInfo.Builder builder = SysAssistInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SysAssistInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getExpiration() != 0L) {
            builder.setExpiration(this.getExpiration());
            fieldCnt++;
        }  else if (builder.hasExpiration()) {
            // 清理Expiration
            builder.clearExpiration();
            fieldCnt++;
        }
        if (this.getMailId() != 0) {
            builder.setMailId(this.getMailId());
            fieldCnt++;
        }  else if (builder.hasMailId()) {
            // 清理MailId
            builder.clearMailId();
            fieldCnt++;
        }
        if (!this.getTargetClanShortName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTargetClanShortName(this.getTargetClanShortName());
            fieldCnt++;
        }  else if (builder.hasTargetClanShortName()) {
            // 清理TargetClanShortName
            builder.clearTargetClanShortName();
            fieldCnt++;
        }
        if (!this.getTargetPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTargetPlayerName(this.getTargetPlayerName());
            fieldCnt++;
        }  else if (builder.hasTargetPlayerName()) {
            // 清理TargetPlayerName
            builder.clearTargetPlayerName();
            fieldCnt++;
        }
        if (this.assistPlayerName != null) {
            Basic.StringList.Builder tmpBuilder = Basic.StringList.newBuilder();
            final int tmpFieldCnt = this.assistPlayerName.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAssistPlayerName(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAssistPlayerName();
            }
        }  else if (builder.hasAssistPlayerName()) {
            // 清理AssistPlayerName
            builder.clearAssistPlayerName();
            fieldCnt++;
        }
        if (this.itemReward != null) {
            Struct.ItemPairList.Builder tmpBuilder = Struct.ItemPairList.newBuilder();
            final int tmpFieldCnt = this.itemReward.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItemReward(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItemReward();
            }
        }  else if (builder.hasItemReward()) {
            // 清理ItemReward
            builder.clearItemReward();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SysAssistInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_EXPIRATION)) {
            builder.setExpiration(this.getExpiration());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAILID)) {
            builder.setMailId(this.getMailId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETCLANSHORTNAME)) {
            builder.setTargetClanShortName(this.getTargetClanShortName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETPLAYERNAME)) {
            builder.setTargetPlayerName(this.getTargetPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ASSISTPLAYERNAME) && this.assistPlayerName != null) {
            final boolean needClear = !builder.hasAssistPlayerName();
            final int tmpFieldCnt = this.assistPlayerName.copyChangeToSs(builder.getAssistPlayerNameBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAssistPlayerName();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            final boolean needClear = !builder.hasItemReward();
            final int tmpFieldCnt = this.itemReward.copyChangeToSs(builder.getItemRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemReward();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SysAssistInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasExpiration()) {
            this.innerSetExpiration(proto.getExpiration());
        } else {
            this.innerSetExpiration(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMailId()) {
            this.innerSetMailId(proto.getMailId());
        } else {
            this.innerSetMailId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTargetClanShortName()) {
            this.innerSetTargetClanShortName(proto.getTargetClanShortName());
        } else {
            this.innerSetTargetClanShortName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTargetPlayerName()) {
            this.innerSetTargetPlayerName(proto.getTargetPlayerName());
        } else {
            this.innerSetTargetPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasAssistPlayerName()) {
            this.getAssistPlayerName().mergeFromSs(proto.getAssistPlayerName());
        } else {
            if (this.assistPlayerName != null) {
                this.assistPlayerName.mergeFromSs(proto.getAssistPlayerName());
            }
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeFromSs(proto.getItemReward());
        } else {
            if (this.itemReward != null) {
                this.itemReward.mergeFromSs(proto.getItemReward());
            }
        }
        this.markAll();
        return SysAssistInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SysAssistInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasExpiration()) {
            this.setExpiration(proto.getExpiration());
            fieldCnt++;
        }
        if (proto.hasMailId()) {
            this.setMailId(proto.getMailId());
            fieldCnt++;
        }
        if (proto.hasTargetClanShortName()) {
            this.setTargetClanShortName(proto.getTargetClanShortName());
            fieldCnt++;
        }
        if (proto.hasTargetPlayerName()) {
            this.setTargetPlayerName(proto.getTargetPlayerName());
            fieldCnt++;
        }
        if (proto.hasAssistPlayerName()) {
            this.getAssistPlayerName().mergeChangeFromSs(proto.getAssistPlayerName());
            fieldCnt++;
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeChangeFromSs(proto.getItemReward());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SysAssistInfo.Builder builder = SysAssistInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ASSISTPLAYERNAME) && this.assistPlayerName != null) {
            this.assistPlayerName.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            this.itemReward.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.assistPlayerName != null) {
            this.assistPlayerName.markAll();
        }
        if (this.itemReward != null) {
            this.itemReward.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.expiration;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SysAssistInfoProp)) {
            return false;
        }
        final SysAssistInfoProp otherNode = (SysAssistInfoProp) node;
        if (this.expiration != otherNode.expiration) {
            return false;
        }
        if (this.mailId != otherNode.mailId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.targetClanShortName, otherNode.targetClanShortName)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.targetPlayerName, otherNode.targetPlayerName)) {
            return false;
        }
        if (!this.getAssistPlayerName().compareDataTo(otherNode.getAssistPlayerName())) {
            return false;
        }
        if (!this.getItemReward().compareDataTo(otherNode.getItemReward())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructPB.SoldierListPB;
import com.yorha.proto.Struct.SoldierList;
import com.yorha.proto.StructPB.SoldierPB;
import com.yorha.proto.Struct.Soldier;

/**
 * <AUTHOR> auto gen
 */
public class SoldierListProp extends AbstractListNode<SoldierProp> {
    /**
     * Create a SoldierListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public SoldierListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to SoldierListProp
     *
     * @return new object
     */
    @Override
    public SoldierProp addEmptyValue() {
        final SoldierProp newProp = new SoldierProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SoldierListPB.Builder getCopyCsBuilder() {
        final SoldierListPB.Builder builder = SoldierListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(SoldierListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return SoldierListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final SoldierProp v : this) {
            final SoldierPB.Builder itemBuilder = SoldierPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return SoldierListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(SoldierListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return SoldierListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(SoldierListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (SoldierPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return SoldierListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(SoldierListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SoldierList.Builder getCopyDbBuilder() {
        final SoldierList.Builder builder = SoldierList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(SoldierList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return SoldierListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final SoldierProp v : this) {
            final Soldier.Builder itemBuilder = Soldier.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return SoldierListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(SoldierList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return SoldierListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(SoldierList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (Soldier v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return SoldierListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(SoldierList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SoldierList.Builder getCopySsBuilder() {
        final SoldierList.Builder builder = SoldierList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(SoldierList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return SoldierListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final SoldierProp v : this) {
            final Soldier.Builder itemBuilder = Soldier.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return SoldierListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(SoldierList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return SoldierListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(SoldierList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (Soldier v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return SoldierListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(SoldierList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        SoldierList.Builder builder = SoldierList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
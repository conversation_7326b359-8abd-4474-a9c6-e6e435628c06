package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.CampaignBuilding;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.CampaignBuildingPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class CampaignBuildingProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_UNITID = 1;
    public static final int FIELD_INDEX_HP = 2;
    public static final int FIELD_INDEX_HPMAX = 3;
    public static final int FIELD_INDEX_POINT = 4;
    public static final int FIELD_INDEX_DISABLE = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private int id = Constant.DEFAULT_INT_VALUE;
    private int unitId = Constant.DEFAULT_INT_VALUE;
    private int hp = Constant.DEFAULT_INT_VALUE;
    private int hpMax = Constant.DEFAULT_INT_VALUE;
    private PointProp point = null;
    private boolean disable = Constant.DEFAULT_BOOLEAN_VALUE;

    public CampaignBuildingProp() {
        super(null, 0, FIELD_COUNT);
    }

    public CampaignBuildingProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public int getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public CampaignBuildingProp setId(int id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(int id) {
        this.id = id;
    }

    /**
     * get unitId
     *
     * @return unitId value
     */
    public int getUnitId() {
        return this.unitId;
    }

    /**
     * set unitId && set marked
     *
     * @param unitId new value
     * @return current object
     */
    public CampaignBuildingProp setUnitId(int unitId) {
        if (this.unitId != unitId) {
            this.mark(FIELD_INDEX_UNITID);
            this.unitId = unitId;
        }
        return this;
    }

    /**
     * inner set unitId
     *
     * @param unitId new value
     */
    private void innerSetUnitId(int unitId) {
        this.unitId = unitId;
    }

    /**
     * get hp
     *
     * @return hp value
     */
    public int getHp() {
        return this.hp;
    }

    /**
     * set hp && set marked
     *
     * @param hp new value
     * @return current object
     */
    public CampaignBuildingProp setHp(int hp) {
        if (this.hp != hp) {
            this.mark(FIELD_INDEX_HP);
            this.hp = hp;
        }
        return this;
    }

    /**
     * inner set hp
     *
     * @param hp new value
     */
    private void innerSetHp(int hp) {
        this.hp = hp;
    }

    /**
     * get hpMax
     *
     * @return hpMax value
     */
    public int getHpMax() {
        return this.hpMax;
    }

    /**
     * set hpMax && set marked
     *
     * @param hpMax new value
     * @return current object
     */
    public CampaignBuildingProp setHpMax(int hpMax) {
        if (this.hpMax != hpMax) {
            this.mark(FIELD_INDEX_HPMAX);
            this.hpMax = hpMax;
        }
        return this;
    }

    /**
     * inner set hpMax
     *
     * @param hpMax new value
     */
    private void innerSetHpMax(int hpMax) {
        this.hpMax = hpMax;
    }

    /**
     * get point
     *
     * @return point value
     */
    public PointProp getPoint() {
        if (this.point == null) {
            this.point = new PointProp(this, FIELD_INDEX_POINT);
        }
        return this.point;
    }

    /**
     * get disable
     *
     * @return disable value
     */
    public boolean getDisable() {
        return this.disable;
    }

    /**
     * set disable && set marked
     *
     * @param disable new value
     * @return current object
     */
    public CampaignBuildingProp setDisable(boolean disable) {
        if (this.disable != disable) {
            this.mark(FIELD_INDEX_DISABLE);
            this.disable = disable;
        }
        return this;
    }

    /**
     * inner set disable
     *
     * @param disable new value
     */
    private void innerSetDisable(boolean disable) {
        this.disable = disable;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignBuildingPB.Builder getCopyCsBuilder() {
        final CampaignBuildingPB.Builder builder = CampaignBuildingPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(CampaignBuildingPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        if (this.getHp() != 0) {
            builder.setHp(this.getHp());
            fieldCnt++;
        }  else if (builder.hasHp()) {
            // 清理Hp
            builder.clearHp();
            fieldCnt++;
        }
        if (this.getHpMax() != 0) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }  else if (builder.hasHpMax()) {
            // 清理HpMax
            builder.clearHpMax();
            fieldCnt++;
        }
        if (this.point != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.point.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getDisable()) {
            builder.setDisable(this.getDisable());
            fieldCnt++;
        }  else if (builder.hasDisable()) {
            // 清理Disable
            builder.clearDisable();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(CampaignBuildingPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HP)) {
            builder.setHp(this.getHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HPMAX)) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_DISABLE)) {
            builder.setDisable(this.getDisable());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(CampaignBuildingPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HP)) {
            builder.setHp(this.getHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HPMAX)) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToAndClearDeleteKeysCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_DISABLE)) {
            builder.setDisable(this.getDisable());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(CampaignBuildingPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHp()) {
            this.innerSetHp(proto.getHp());
        } else {
            this.innerSetHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHpMax()) {
            this.innerSetHpMax(proto.getHpMax());
        } else {
            this.innerSetHpMax(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromCs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromCs(proto.getPoint());
            }
        }
        if (proto.hasDisable()) {
            this.innerSetDisable(proto.getDisable());
        } else {
            this.innerSetDisable(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return CampaignBuildingProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(CampaignBuildingPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        if (proto.hasHp()) {
            this.setHp(proto.getHp());
            fieldCnt++;
        }
        if (proto.hasHpMax()) {
            this.setHpMax(proto.getHpMax());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromCs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasDisable()) {
            this.setDisable(proto.getDisable());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignBuilding.Builder getCopyDbBuilder() {
        final CampaignBuilding.Builder builder = CampaignBuilding.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(CampaignBuilding.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        if (this.getHp() != 0) {
            builder.setHp(this.getHp());
            fieldCnt++;
        }  else if (builder.hasHp()) {
            // 清理Hp
            builder.clearHp();
            fieldCnt++;
        }
        if (this.getHpMax() != 0) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }  else if (builder.hasHpMax()) {
            // 清理HpMax
            builder.clearHpMax();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getDisable()) {
            builder.setDisable(this.getDisable());
            fieldCnt++;
        }  else if (builder.hasDisable()) {
            // 清理Disable
            builder.clearDisable();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(CampaignBuilding.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HP)) {
            builder.setHp(this.getHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HPMAX)) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToDb(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_DISABLE)) {
            builder.setDisable(this.getDisable());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(CampaignBuilding proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHp()) {
            this.innerSetHp(proto.getHp());
        } else {
            this.innerSetHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHpMax()) {
            this.innerSetHpMax(proto.getHpMax());
        } else {
            this.innerSetHpMax(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromDb(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromDb(proto.getPoint());
            }
        }
        if (proto.hasDisable()) {
            this.innerSetDisable(proto.getDisable());
        } else {
            this.innerSetDisable(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return CampaignBuildingProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(CampaignBuilding proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        if (proto.hasHp()) {
            this.setHp(proto.getHp());
            fieldCnt++;
        }
        if (proto.hasHpMax()) {
            this.setHpMax(proto.getHpMax());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromDb(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasDisable()) {
            this.setDisable(proto.getDisable());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignBuilding.Builder getCopySsBuilder() {
        final CampaignBuilding.Builder builder = CampaignBuilding.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(CampaignBuilding.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        if (this.getHp() != 0) {
            builder.setHp(this.getHp());
            fieldCnt++;
        }  else if (builder.hasHp()) {
            // 清理Hp
            builder.clearHp();
            fieldCnt++;
        }
        if (this.getHpMax() != 0) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }  else if (builder.hasHpMax()) {
            // 清理HpMax
            builder.clearHpMax();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getDisable()) {
            builder.setDisable(this.getDisable());
            fieldCnt++;
        }  else if (builder.hasDisable()) {
            // 清理Disable
            builder.clearDisable();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(CampaignBuilding.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HP)) {
            builder.setHp(this.getHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HPMAX)) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToSs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_DISABLE)) {
            builder.setDisable(this.getDisable());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(CampaignBuilding proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHp()) {
            this.innerSetHp(proto.getHp());
        } else {
            this.innerSetHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHpMax()) {
            this.innerSetHpMax(proto.getHpMax());
        } else {
            this.innerSetHpMax(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromSs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromSs(proto.getPoint());
            }
        }
        if (proto.hasDisable()) {
            this.innerSetDisable(proto.getDisable());
        } else {
            this.innerSetDisable(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return CampaignBuildingProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(CampaignBuilding proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        if (proto.hasHp()) {
            this.setHp(proto.getHp());
            fieldCnt++;
        }
        if (proto.hasHpMax()) {
            this.setHpMax(proto.getHpMax());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromSs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasDisable()) {
            this.setDisable(proto.getDisable());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        CampaignBuilding.Builder builder = CampaignBuilding.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            this.point.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.point != null) {
            this.point.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof CampaignBuildingProp)) {
            return false;
        }
        final CampaignBuildingProp otherNode = (CampaignBuildingProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.unitId != otherNode.unitId) {
            return false;
        }
        if (this.hp != otherNode.hp) {
            return false;
        }
        if (this.hpMax != otherNode.hpMax) {
            return false;
        }
        if (!this.getPoint().compareDataTo(otherNode.getPoint())) {
            return false;
        }
        if (this.disable != otherNode.disable) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.YoTest.YoTestMap;
import com.yorha.proto.YoTest;
import com.yorha.proto.YoTestPB.YoTestMapPB;
import com.yorha.proto.YoTestPB;


/**
 * <AUTHOR> auto gen
 */
public class YoTestMapProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_TESTMAPINTFIELD = 1;
    public static final int FIELD_INDEX_INNERMAPFIELD = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private long id = Constant.DEFAULT_LONG_VALUE;
    private int testMapIntField = Constant.DEFAULT_INT_VALUE;
    private Int32YoTestInnerMapMapProp innerMapField = null;

    public YoTestMapProp() {
        super(null, 0, FIELD_COUNT);
    }

    public YoTestMapProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public long getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public YoTestMapProp setId(long id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(long id) {
        this.id = id;
    }

    /**
     * get testMapIntField
     *
     * @return testMapIntField value
     */
    public int getTestMapIntField() {
        return this.testMapIntField;
    }

    /**
     * set testMapIntField && set marked
     *
     * @param testMapIntField new value
     * @return current object
     */
    public YoTestMapProp setTestMapIntField(int testMapIntField) {
        if (this.testMapIntField != testMapIntField) {
            this.mark(FIELD_INDEX_TESTMAPINTFIELD);
            this.testMapIntField = testMapIntField;
        }
        return this;
    }

    /**
     * inner set testMapIntField
     *
     * @param testMapIntField new value
     */
    private void innerSetTestMapIntField(int testMapIntField) {
        this.testMapIntField = testMapIntField;
    }

    /**
     * get innerMapField
     *
     * @return innerMapField value
     */
    public Int32YoTestInnerMapMapProp getInnerMapField() {
        if (this.innerMapField == null) {
            this.innerMapField = new Int32YoTestInnerMapMapProp(this, FIELD_INDEX_INNERMAPFIELD);
        }
        return this.innerMapField;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putInnerMapFieldV(YoTestInnerMapProp v) {
        this.getInnerMapField().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public YoTestInnerMapProp addEmptyInnerMapField(Integer k) {
        return this.getInnerMapField().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getInnerMapFieldSize() {
        if (this.innerMapField == null) {
            return 0;
        }
        return this.innerMapField.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isInnerMapFieldEmpty() {
        if (this.innerMapField == null) {
            return true;
        }
        return this.innerMapField.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public YoTestInnerMapProp getInnerMapFieldV(Integer k) {
        if (this.innerMapField == null || !this.innerMapField.containsKey(k)) {
            return null;
        }
        return this.innerMapField.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearInnerMapField() {
        if (this.innerMapField != null) {
            this.innerMapField.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeInnerMapFieldV(Integer k) {
        if (this.innerMapField != null) {
            this.innerMapField.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestMapPB.Builder getCopyCsBuilder() {
        final YoTestMapPB.Builder builder = YoTestMapPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(YoTestMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getTestMapIntField() != 0) {
            builder.setTestMapIntField(this.getTestMapIntField());
            fieldCnt++;
        }  else if (builder.hasTestMapIntField()) {
            // 清理TestMapIntField
            builder.clearTestMapIntField();
            fieldCnt++;
        }
        if (this.innerMapField != null) {
            YoTestPB.Int32YoTestInnerMapMapPB.Builder tmpBuilder = YoTestPB.Int32YoTestInnerMapMapPB.newBuilder();
            final int tmpFieldCnt = this.innerMapField.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInnerMapField(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInnerMapField();
            }
        }  else if (builder.hasInnerMapField()) {
            // 清理InnerMapField
            builder.clearInnerMapField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(YoTestMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTMAPINTFIELD)) {
            builder.setTestMapIntField(this.getTestMapIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INNERMAPFIELD) && this.innerMapField != null) {
            final boolean needClear = !builder.hasInnerMapField();
            final int tmpFieldCnt = this.innerMapField.copyChangeToCs(builder.getInnerMapFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerMapField();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(YoTestMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTMAPINTFIELD)) {
            builder.setTestMapIntField(this.getTestMapIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INNERMAPFIELD) && this.innerMapField != null) {
            final boolean needClear = !builder.hasInnerMapField();
            final int tmpFieldCnt = this.innerMapField.copyChangeToAndClearDeleteKeysCs(builder.getInnerMapFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerMapField();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(YoTestMapPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTestMapIntField()) {
            this.innerSetTestMapIntField(proto.getTestMapIntField());
        } else {
            this.innerSetTestMapIntField(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasInnerMapField()) {
            this.getInnerMapField().mergeFromCs(proto.getInnerMapField());
        } else {
            if (this.innerMapField != null) {
                this.innerMapField.mergeFromCs(proto.getInnerMapField());
            }
        }
        this.markAll();
        return YoTestMapProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(YoTestMapPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasTestMapIntField()) {
            this.setTestMapIntField(proto.getTestMapIntField());
            fieldCnt++;
        }
        if (proto.hasInnerMapField()) {
            this.getInnerMapField().mergeChangeFromCs(proto.getInnerMapField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestMap.Builder getCopyDbBuilder() {
        final YoTestMap.Builder builder = YoTestMap.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(YoTestMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getTestMapIntField() != 0) {
            builder.setTestMapIntField(this.getTestMapIntField());
            fieldCnt++;
        }  else if (builder.hasTestMapIntField()) {
            // 清理TestMapIntField
            builder.clearTestMapIntField();
            fieldCnt++;
        }
        if (this.innerMapField != null) {
            YoTest.Int32YoTestInnerMapMap.Builder tmpBuilder = YoTest.Int32YoTestInnerMapMap.newBuilder();
            final int tmpFieldCnt = this.innerMapField.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInnerMapField(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInnerMapField();
            }
        }  else if (builder.hasInnerMapField()) {
            // 清理InnerMapField
            builder.clearInnerMapField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(YoTestMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTMAPINTFIELD)) {
            builder.setTestMapIntField(this.getTestMapIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INNERMAPFIELD) && this.innerMapField != null) {
            final boolean needClear = !builder.hasInnerMapField();
            final int tmpFieldCnt = this.innerMapField.copyChangeToDb(builder.getInnerMapFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerMapField();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(YoTestMap proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTestMapIntField()) {
            this.innerSetTestMapIntField(proto.getTestMapIntField());
        } else {
            this.innerSetTestMapIntField(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasInnerMapField()) {
            this.getInnerMapField().mergeFromDb(proto.getInnerMapField());
        } else {
            if (this.innerMapField != null) {
                this.innerMapField.mergeFromDb(proto.getInnerMapField());
            }
        }
        this.markAll();
        return YoTestMapProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(YoTestMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasTestMapIntField()) {
            this.setTestMapIntField(proto.getTestMapIntField());
            fieldCnt++;
        }
        if (proto.hasInnerMapField()) {
            this.getInnerMapField().mergeChangeFromDb(proto.getInnerMapField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestMap.Builder getCopySsBuilder() {
        final YoTestMap.Builder builder = YoTestMap.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(YoTestMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getTestMapIntField() != 0) {
            builder.setTestMapIntField(this.getTestMapIntField());
            fieldCnt++;
        }  else if (builder.hasTestMapIntField()) {
            // 清理TestMapIntField
            builder.clearTestMapIntField();
            fieldCnt++;
        }
        if (this.innerMapField != null) {
            YoTest.Int32YoTestInnerMapMap.Builder tmpBuilder = YoTest.Int32YoTestInnerMapMap.newBuilder();
            final int tmpFieldCnt = this.innerMapField.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInnerMapField(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInnerMapField();
            }
        }  else if (builder.hasInnerMapField()) {
            // 清理InnerMapField
            builder.clearInnerMapField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(YoTestMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TESTMAPINTFIELD)) {
            builder.setTestMapIntField(this.getTestMapIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INNERMAPFIELD) && this.innerMapField != null) {
            final boolean needClear = !builder.hasInnerMapField();
            final int tmpFieldCnt = this.innerMapField.copyChangeToSs(builder.getInnerMapFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerMapField();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(YoTestMap proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTestMapIntField()) {
            this.innerSetTestMapIntField(proto.getTestMapIntField());
        } else {
            this.innerSetTestMapIntField(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasInnerMapField()) {
            this.getInnerMapField().mergeFromSs(proto.getInnerMapField());
        } else {
            if (this.innerMapField != null) {
                this.innerMapField.mergeFromSs(proto.getInnerMapField());
            }
        }
        this.markAll();
        return YoTestMapProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(YoTestMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasTestMapIntField()) {
            this.setTestMapIntField(proto.getTestMapIntField());
            fieldCnt++;
        }
        if (proto.hasInnerMapField()) {
            this.getInnerMapField().mergeChangeFromSs(proto.getInnerMapField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        YoTestMap.Builder builder = YoTestMap.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_INNERMAPFIELD) && this.innerMapField != null) {
            this.innerMapField.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.innerMapField != null) {
            this.innerMapField.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof YoTestMapProp)) {
            return false;
        }
        final YoTestMapProp otherNode = (YoTestMapProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.testMapIntField != otherNode.testMapIntField) {
            return false;
        }
        if (!this.getInnerMapField().compareDataTo(otherNode.getInnerMapField())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.GoodsChainInfo;
import com.yorha.proto.StructPB.GoodsChainInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class GoodsChainInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_CHAINID = 0;
    public static final int FIELD_INDEX_LASTBOUGHTGOODSID = 1;
    public static final int FIELD_INDEX_LASTGOODSBOUGHTTIMES = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int chainId = Constant.DEFAULT_INT_VALUE;
    private int lastBoughtGoodsId = Constant.DEFAULT_INT_VALUE;
    private int lastGoodsBoughtTimes = Constant.DEFAULT_INT_VALUE;

    public GoodsChainInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public GoodsChainInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get chainId
     *
     * @return chainId value
     */
    public int getChainId() {
        return this.chainId;
    }

    /**
     * set chainId && set marked
     *
     * @param chainId new value
     * @return current object
     */
    public GoodsChainInfoProp setChainId(int chainId) {
        if (this.chainId != chainId) {
            this.mark(FIELD_INDEX_CHAINID);
            this.chainId = chainId;
        }
        return this;
    }

    /**
     * inner set chainId
     *
     * @param chainId new value
     */
    private void innerSetChainId(int chainId) {
        this.chainId = chainId;
    }

    /**
     * get lastBoughtGoodsId
     *
     * @return lastBoughtGoodsId value
     */
    public int getLastBoughtGoodsId() {
        return this.lastBoughtGoodsId;
    }

    /**
     * set lastBoughtGoodsId && set marked
     *
     * @param lastBoughtGoodsId new value
     * @return current object
     */
    public GoodsChainInfoProp setLastBoughtGoodsId(int lastBoughtGoodsId) {
        if (this.lastBoughtGoodsId != lastBoughtGoodsId) {
            this.mark(FIELD_INDEX_LASTBOUGHTGOODSID);
            this.lastBoughtGoodsId = lastBoughtGoodsId;
        }
        return this;
    }

    /**
     * inner set lastBoughtGoodsId
     *
     * @param lastBoughtGoodsId new value
     */
    private void innerSetLastBoughtGoodsId(int lastBoughtGoodsId) {
        this.lastBoughtGoodsId = lastBoughtGoodsId;
    }

    /**
     * get lastGoodsBoughtTimes
     *
     * @return lastGoodsBoughtTimes value
     */
    public int getLastGoodsBoughtTimes() {
        return this.lastGoodsBoughtTimes;
    }

    /**
     * set lastGoodsBoughtTimes && set marked
     *
     * @param lastGoodsBoughtTimes new value
     * @return current object
     */
    public GoodsChainInfoProp setLastGoodsBoughtTimes(int lastGoodsBoughtTimes) {
        if (this.lastGoodsBoughtTimes != lastGoodsBoughtTimes) {
            this.mark(FIELD_INDEX_LASTGOODSBOUGHTTIMES);
            this.lastGoodsBoughtTimes = lastGoodsBoughtTimes;
        }
        return this;
    }

    /**
     * inner set lastGoodsBoughtTimes
     *
     * @param lastGoodsBoughtTimes new value
     */
    private void innerSetLastGoodsBoughtTimes(int lastGoodsBoughtTimes) {
        this.lastGoodsBoughtTimes = lastGoodsBoughtTimes;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public GoodsChainInfoPB.Builder getCopyCsBuilder() {
        final GoodsChainInfoPB.Builder builder = GoodsChainInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(GoodsChainInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getChainId() != 0) {
            builder.setChainId(this.getChainId());
            fieldCnt++;
        }  else if (builder.hasChainId()) {
            // 清理ChainId
            builder.clearChainId();
            fieldCnt++;
        }
        if (this.getLastBoughtGoodsId() != 0) {
            builder.setLastBoughtGoodsId(this.getLastBoughtGoodsId());
            fieldCnt++;
        }  else if (builder.hasLastBoughtGoodsId()) {
            // 清理LastBoughtGoodsId
            builder.clearLastBoughtGoodsId();
            fieldCnt++;
        }
        if (this.getLastGoodsBoughtTimes() != 0) {
            builder.setLastGoodsBoughtTimes(this.getLastGoodsBoughtTimes());
            fieldCnt++;
        }  else if (builder.hasLastGoodsBoughtTimes()) {
            // 清理LastGoodsBoughtTimes
            builder.clearLastGoodsBoughtTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(GoodsChainInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHAINID)) {
            builder.setChainId(this.getChainId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTBOUGHTGOODSID)) {
            builder.setLastBoughtGoodsId(this.getLastBoughtGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTGOODSBOUGHTTIMES)) {
            builder.setLastGoodsBoughtTimes(this.getLastGoodsBoughtTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(GoodsChainInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHAINID)) {
            builder.setChainId(this.getChainId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTBOUGHTGOODSID)) {
            builder.setLastBoughtGoodsId(this.getLastBoughtGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTGOODSBOUGHTTIMES)) {
            builder.setLastGoodsBoughtTimes(this.getLastGoodsBoughtTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(GoodsChainInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChainId()) {
            this.innerSetChainId(proto.getChainId());
        } else {
            this.innerSetChainId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastBoughtGoodsId()) {
            this.innerSetLastBoughtGoodsId(proto.getLastBoughtGoodsId());
        } else {
            this.innerSetLastBoughtGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastGoodsBoughtTimes()) {
            this.innerSetLastGoodsBoughtTimes(proto.getLastGoodsBoughtTimes());
        } else {
            this.innerSetLastGoodsBoughtTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return GoodsChainInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(GoodsChainInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChainId()) {
            this.setChainId(proto.getChainId());
            fieldCnt++;
        }
        if (proto.hasLastBoughtGoodsId()) {
            this.setLastBoughtGoodsId(proto.getLastBoughtGoodsId());
            fieldCnt++;
        }
        if (proto.hasLastGoodsBoughtTimes()) {
            this.setLastGoodsBoughtTimes(proto.getLastGoodsBoughtTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public GoodsChainInfo.Builder getCopyDbBuilder() {
        final GoodsChainInfo.Builder builder = GoodsChainInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(GoodsChainInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getChainId() != 0) {
            builder.setChainId(this.getChainId());
            fieldCnt++;
        }  else if (builder.hasChainId()) {
            // 清理ChainId
            builder.clearChainId();
            fieldCnt++;
        }
        if (this.getLastBoughtGoodsId() != 0) {
            builder.setLastBoughtGoodsId(this.getLastBoughtGoodsId());
            fieldCnt++;
        }  else if (builder.hasLastBoughtGoodsId()) {
            // 清理LastBoughtGoodsId
            builder.clearLastBoughtGoodsId();
            fieldCnt++;
        }
        if (this.getLastGoodsBoughtTimes() != 0) {
            builder.setLastGoodsBoughtTimes(this.getLastGoodsBoughtTimes());
            fieldCnt++;
        }  else if (builder.hasLastGoodsBoughtTimes()) {
            // 清理LastGoodsBoughtTimes
            builder.clearLastGoodsBoughtTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(GoodsChainInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHAINID)) {
            builder.setChainId(this.getChainId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTBOUGHTGOODSID)) {
            builder.setLastBoughtGoodsId(this.getLastBoughtGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTGOODSBOUGHTTIMES)) {
            builder.setLastGoodsBoughtTimes(this.getLastGoodsBoughtTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(GoodsChainInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChainId()) {
            this.innerSetChainId(proto.getChainId());
        } else {
            this.innerSetChainId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastBoughtGoodsId()) {
            this.innerSetLastBoughtGoodsId(proto.getLastBoughtGoodsId());
        } else {
            this.innerSetLastBoughtGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastGoodsBoughtTimes()) {
            this.innerSetLastGoodsBoughtTimes(proto.getLastGoodsBoughtTimes());
        } else {
            this.innerSetLastGoodsBoughtTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return GoodsChainInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(GoodsChainInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChainId()) {
            this.setChainId(proto.getChainId());
            fieldCnt++;
        }
        if (proto.hasLastBoughtGoodsId()) {
            this.setLastBoughtGoodsId(proto.getLastBoughtGoodsId());
            fieldCnt++;
        }
        if (proto.hasLastGoodsBoughtTimes()) {
            this.setLastGoodsBoughtTimes(proto.getLastGoodsBoughtTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public GoodsChainInfo.Builder getCopySsBuilder() {
        final GoodsChainInfo.Builder builder = GoodsChainInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(GoodsChainInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getChainId() != 0) {
            builder.setChainId(this.getChainId());
            fieldCnt++;
        }  else if (builder.hasChainId()) {
            // 清理ChainId
            builder.clearChainId();
            fieldCnt++;
        }
        if (this.getLastBoughtGoodsId() != 0) {
            builder.setLastBoughtGoodsId(this.getLastBoughtGoodsId());
            fieldCnt++;
        }  else if (builder.hasLastBoughtGoodsId()) {
            // 清理LastBoughtGoodsId
            builder.clearLastBoughtGoodsId();
            fieldCnt++;
        }
        if (this.getLastGoodsBoughtTimes() != 0) {
            builder.setLastGoodsBoughtTimes(this.getLastGoodsBoughtTimes());
            fieldCnt++;
        }  else if (builder.hasLastGoodsBoughtTimes()) {
            // 清理LastGoodsBoughtTimes
            builder.clearLastGoodsBoughtTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(GoodsChainInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHAINID)) {
            builder.setChainId(this.getChainId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTBOUGHTGOODSID)) {
            builder.setLastBoughtGoodsId(this.getLastBoughtGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTGOODSBOUGHTTIMES)) {
            builder.setLastGoodsBoughtTimes(this.getLastGoodsBoughtTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(GoodsChainInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChainId()) {
            this.innerSetChainId(proto.getChainId());
        } else {
            this.innerSetChainId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastBoughtGoodsId()) {
            this.innerSetLastBoughtGoodsId(proto.getLastBoughtGoodsId());
        } else {
            this.innerSetLastBoughtGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastGoodsBoughtTimes()) {
            this.innerSetLastGoodsBoughtTimes(proto.getLastGoodsBoughtTimes());
        } else {
            this.innerSetLastGoodsBoughtTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return GoodsChainInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(GoodsChainInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChainId()) {
            this.setChainId(proto.getChainId());
            fieldCnt++;
        }
        if (proto.hasLastBoughtGoodsId()) {
            this.setLastBoughtGoodsId(proto.getLastBoughtGoodsId());
            fieldCnt++;
        }
        if (proto.hasLastGoodsBoughtTimes()) {
            this.setLastGoodsBoughtTimes(proto.getLastGoodsBoughtTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        GoodsChainInfo.Builder builder = GoodsChainInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.chainId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof GoodsChainInfoProp)) {
            return false;
        }
        final GoodsChainInfoProp otherNode = (GoodsChainInfoProp) node;
        if (this.chainId != otherNode.chainId) {
            return false;
        }
        if (this.lastBoughtGoodsId != otherNode.lastBoughtGoodsId) {
            return false;
        }
        if (this.lastGoodsBoughtTimes != otherNode.lastGoodsBoughtTimes) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
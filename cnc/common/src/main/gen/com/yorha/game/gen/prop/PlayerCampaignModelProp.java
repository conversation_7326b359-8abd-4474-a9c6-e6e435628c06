package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Player.PlayerCampaignModel;
import com.yorha.proto.Basic;
import com.yorha.proto.Player;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.PlayerCampaignModelPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.PlayerPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerCampaignModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_STATE = 0;
    public static final int FIELD_INDEX_INFO = 1;
    public static final int FIELD_INDEX_CAMPAIGNLEVEL = 2;
    public static final int FIELD_INDEX_MISSIONLEVEL = 3;
    public static final int FIELD_INDEX_MISSIONS = 4;
    public static final int FIELD_INDEX_BUILDINGS = 5;
    public static final int FIELD_INDEX_UNITS = 6;
    public static final int FIELD_INDEX_SKILLS = 7;
    public static final int FIELD_INDEX_ITEMSIZE = 8;
    public static final int FIELD_INDEX_ITEMS = 9;
    public static final int FIELD_INDEX_REWARDITEMS = 10;
    public static final int FIELD_INDEX_ADDITIONS = 11;
    public static final int FIELD_INDEX_EVENT = 12;
    public static final int FIELD_INDEX_BATTLE = 13;
    public static final int FIELD_INDEX_RESOURCES = 14;
    public static final int FIELD_INDEX_BLUEPRINTID = 15;
    public static final int FIELD_INDEX_BUFFS = 16;
    public static final int FIELD_INDEX_BATTLEINFO = 17;

    public static final int FIELD_COUNT = 18;

    private long markBits0 = 0L;

    private CampaignState state = CampaignState.forNumber(0);
    private CampaignInfoProp info = null;
    private int campaignLevel = Constant.DEFAULT_INT_VALUE;
    private int missionLevel = Constant.DEFAULT_INT_VALUE;
    private CampaignMissionListProp missions = null;
    private Int32CampaignBuildingMapProp buildings = null;
    private Int32CampaignUnitMapProp units = null;
    private Int32ListProp skills = null;
    private int itemSize = Constant.DEFAULT_INT_VALUE;
    private Int32ItemPairMapProp items = null;
    private Int32ItemPairMapProp rewardItems = null;
    private Int32CampaignAdditionMapProp additions = null;
    private CampaignEventProp event = null;
    private CampaignBattleProp battle = null;
    private CurrencyListProp resources = null;
    private int blueprintId = Constant.DEFAULT_INT_VALUE;
    private Int32ListProp buffs = null;
    private com.google.protobuf.ByteString battleInfo = Constant.DEFAULT_BYTE_STRING_VALUE;

    public PlayerCampaignModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerCampaignModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get state
     *
     * @return state value
     */
    public CampaignState getState() {
        return this.state;
    }

    /**
     * set state && set marked
     *
     * @param state new value
     * @return current object
     */
    public PlayerCampaignModelProp setState(CampaignState state) {
        if (state == null) {
            throw new NullPointerException();
        }
        if (this.state != state) {
            this.mark(FIELD_INDEX_STATE);
            this.state = state;
        }
        return this;
    }

    /**
     * inner set state
     *
     * @param state new value
     */
    private void innerSetState(CampaignState state) {
        this.state = state;
    }

    /**
     * get info
     *
     * @return info value
     */
    public CampaignInfoProp getInfo() {
        if (this.info == null) {
            this.info = new CampaignInfoProp(this, FIELD_INDEX_INFO);
        }
        return this.info;
    }

    /**
     * get campaignLevel
     *
     * @return campaignLevel value
     */
    public int getCampaignLevel() {
        return this.campaignLevel;
    }

    /**
     * set campaignLevel && set marked
     *
     * @param campaignLevel new value
     * @return current object
     */
    public PlayerCampaignModelProp setCampaignLevel(int campaignLevel) {
        if (this.campaignLevel != campaignLevel) {
            this.mark(FIELD_INDEX_CAMPAIGNLEVEL);
            this.campaignLevel = campaignLevel;
        }
        return this;
    }

    /**
     * inner set campaignLevel
     *
     * @param campaignLevel new value
     */
    private void innerSetCampaignLevel(int campaignLevel) {
        this.campaignLevel = campaignLevel;
    }

    /**
     * get missionLevel
     *
     * @return missionLevel value
     */
    public int getMissionLevel() {
        return this.missionLevel;
    }

    /**
     * set missionLevel && set marked
     *
     * @param missionLevel new value
     * @return current object
     */
    public PlayerCampaignModelProp setMissionLevel(int missionLevel) {
        if (this.missionLevel != missionLevel) {
            this.mark(FIELD_INDEX_MISSIONLEVEL);
            this.missionLevel = missionLevel;
        }
        return this;
    }

    /**
     * inner set missionLevel
     *
     * @param missionLevel new value
     */
    private void innerSetMissionLevel(int missionLevel) {
        this.missionLevel = missionLevel;
    }

    /**
     * get missions
     *
     * @return missions value
     */
    public CampaignMissionListProp getMissions() {
        if (this.missions == null) {
            this.missions = new CampaignMissionListProp(this, FIELD_INDEX_MISSIONS);
        }
        return this.missions;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addMissions(CampaignMissionProp v) {
        this.getMissions().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public CampaignMissionProp getMissionsIndex(int index) {
        return this.getMissions().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public CampaignMissionProp removeMissions(CampaignMissionProp v) {
        if (this.missions == null) {
            return null;
        }
        if(this.missions.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getMissionsSize() {
        if (this.missions == null) {
            return 0;
        }
        return this.missions.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isMissionsEmpty() {
        if (this.missions == null) {
            return true;
        }
        return this.getMissions().isEmpty();
    }

    /**
     * clear list
     */
    public void clearMissions() {
        this.getMissions().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public CampaignMissionProp removeMissionsIndex(int index) {
        return this.getMissions().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public CampaignMissionProp setMissionsIndex(int index, CampaignMissionProp v) {
        return this.getMissions().set(index, v);
    }
    /**
     * get buildings
     *
     * @return buildings value
     */
    public Int32CampaignBuildingMapProp getBuildings() {
        if (this.buildings == null) {
            this.buildings = new Int32CampaignBuildingMapProp(this, FIELD_INDEX_BUILDINGS);
        }
        return this.buildings;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putBuildingsV(CampaignBuildingProp v) {
        this.getBuildings().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public CampaignBuildingProp addEmptyBuildings(Integer k) {
        return this.getBuildings().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getBuildingsSize() {
        if (this.buildings == null) {
            return 0;
        }
        return this.buildings.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isBuildingsEmpty() {
        if (this.buildings == null) {
            return true;
        }
        return this.buildings.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public CampaignBuildingProp getBuildingsV(Integer k) {
        if (this.buildings == null || !this.buildings.containsKey(k)) {
            return null;
        }
        return this.buildings.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearBuildings() {
        if (this.buildings != null) {
            this.buildings.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeBuildingsV(Integer k) {
        if (this.buildings != null) {
            this.buildings.remove(k);
        }
    }
    /**
     * get units
     *
     * @return units value
     */
    public Int32CampaignUnitMapProp getUnits() {
        if (this.units == null) {
            this.units = new Int32CampaignUnitMapProp(this, FIELD_INDEX_UNITS);
        }
        return this.units;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putUnitsV(CampaignUnitProp v) {
        this.getUnits().put(v.getUnitId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public CampaignUnitProp addEmptyUnits(Integer k) {
        return this.getUnits().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getUnitsSize() {
        if (this.units == null) {
            return 0;
        }
        return this.units.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isUnitsEmpty() {
        if (this.units == null) {
            return true;
        }
        return this.units.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public CampaignUnitProp getUnitsV(Integer k) {
        if (this.units == null || !this.units.containsKey(k)) {
            return null;
        }
        return this.units.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearUnits() {
        if (this.units != null) {
            this.units.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeUnitsV(Integer k) {
        if (this.units != null) {
            this.units.remove(k);
        }
    }
    /**
     * get skills
     *
     * @return skills value
     */
    public Int32ListProp getSkills() {
        if (this.skills == null) {
            this.skills = new Int32ListProp(this, FIELD_INDEX_SKILLS);
        }
        return this.skills;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addSkills(Integer v) {
        this.getSkills().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getSkillsIndex(int index) {
        return this.getSkills().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeSkills(Integer v) {
        if (this.skills == null) {
            return null;
        }
        if(this.skills.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getSkillsSize() {
        if (this.skills == null) {
            return 0;
        }
        return this.skills.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isSkillsEmpty() {
        if (this.skills == null) {
            return true;
        }
        return this.getSkills().isEmpty();
    }

    /**
     * clear list
     */
    public void clearSkills() {
        this.getSkills().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeSkillsIndex(int index) {
        return this.getSkills().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setSkillsIndex(int index, Integer v) {
        return this.getSkills().set(index, v);
    }
    /**
     * get itemSize
     *
     * @return itemSize value
     */
    public int getItemSize() {
        return this.itemSize;
    }

    /**
     * set itemSize && set marked
     *
     * @param itemSize new value
     * @return current object
     */
    public PlayerCampaignModelProp setItemSize(int itemSize) {
        if (this.itemSize != itemSize) {
            this.mark(FIELD_INDEX_ITEMSIZE);
            this.itemSize = itemSize;
        }
        return this;
    }

    /**
     * inner set itemSize
     *
     * @param itemSize new value
     */
    private void innerSetItemSize(int itemSize) {
        this.itemSize = itemSize;
    }

    /**
     * get items
     *
     * @return items value
     */
    public Int32ItemPairMapProp getItems() {
        if (this.items == null) {
            this.items = new Int32ItemPairMapProp(this, FIELD_INDEX_ITEMS);
        }
        return this.items;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putItemsV(ItemPairProp v) {
        this.getItems().put(v.getItemTemplateId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ItemPairProp addEmptyItems(Integer k) {
        return this.getItems().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getItemsSize() {
        if (this.items == null) {
            return 0;
        }
        return this.items.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isItemsEmpty() {
        if (this.items == null) {
            return true;
        }
        return this.items.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ItemPairProp getItemsV(Integer k) {
        if (this.items == null || !this.items.containsKey(k)) {
            return null;
        }
        return this.items.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearItems() {
        if (this.items != null) {
            this.items.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeItemsV(Integer k) {
        if (this.items != null) {
            this.items.remove(k);
        }
    }
    /**
     * get rewardItems
     *
     * @return rewardItems value
     */
    public Int32ItemPairMapProp getRewardItems() {
        if (this.rewardItems == null) {
            this.rewardItems = new Int32ItemPairMapProp(this, FIELD_INDEX_REWARDITEMS);
        }
        return this.rewardItems;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putRewardItemsV(ItemPairProp v) {
        this.getRewardItems().put(v.getItemTemplateId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ItemPairProp addEmptyRewardItems(Integer k) {
        return this.getRewardItems().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getRewardItemsSize() {
        if (this.rewardItems == null) {
            return 0;
        }
        return this.rewardItems.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isRewardItemsEmpty() {
        if (this.rewardItems == null) {
            return true;
        }
        return this.rewardItems.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ItemPairProp getRewardItemsV(Integer k) {
        if (this.rewardItems == null || !this.rewardItems.containsKey(k)) {
            return null;
        }
        return this.rewardItems.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearRewardItems() {
        if (this.rewardItems != null) {
            this.rewardItems.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeRewardItemsV(Integer k) {
        if (this.rewardItems != null) {
            this.rewardItems.remove(k);
        }
    }
    /**
     * get additions
     *
     * @return additions value
     */
    public Int32CampaignAdditionMapProp getAdditions() {
        if (this.additions == null) {
            this.additions = new Int32CampaignAdditionMapProp(this, FIELD_INDEX_ADDITIONS);
        }
        return this.additions;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putAdditionsV(CampaignAdditionProp v) {
        this.getAdditions().put(v.getAdditionId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public CampaignAdditionProp addEmptyAdditions(Integer k) {
        return this.getAdditions().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getAdditionsSize() {
        if (this.additions == null) {
            return 0;
        }
        return this.additions.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isAdditionsEmpty() {
        if (this.additions == null) {
            return true;
        }
        return this.additions.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public CampaignAdditionProp getAdditionsV(Integer k) {
        if (this.additions == null || !this.additions.containsKey(k)) {
            return null;
        }
        return this.additions.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearAdditions() {
        if (this.additions != null) {
            this.additions.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeAdditionsV(Integer k) {
        if (this.additions != null) {
            this.additions.remove(k);
        }
    }
    /**
     * get event
     *
     * @return event value
     */
    public CampaignEventProp getEvent() {
        if (this.event == null) {
            this.event = new CampaignEventProp(this, FIELD_INDEX_EVENT);
        }
        return this.event;
    }

    /**
     * get battle
     *
     * @return battle value
     */
    public CampaignBattleProp getBattle() {
        if (this.battle == null) {
            this.battle = new CampaignBattleProp(this, FIELD_INDEX_BATTLE);
        }
        return this.battle;
    }

    /**
     * get resources
     *
     * @return resources value
     */
    public CurrencyListProp getResources() {
        if (this.resources == null) {
            this.resources = new CurrencyListProp(this, FIELD_INDEX_RESOURCES);
        }
        return this.resources;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addResources(CurrencyProp v) {
        this.getResources().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public CurrencyProp getResourcesIndex(int index) {
        return this.getResources().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public CurrencyProp removeResources(CurrencyProp v) {
        if (this.resources == null) {
            return null;
        }
        if(this.resources.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getResourcesSize() {
        if (this.resources == null) {
            return 0;
        }
        return this.resources.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isResourcesEmpty() {
        if (this.resources == null) {
            return true;
        }
        return this.getResources().isEmpty();
    }

    /**
     * clear list
     */
    public void clearResources() {
        this.getResources().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public CurrencyProp removeResourcesIndex(int index) {
        return this.getResources().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public CurrencyProp setResourcesIndex(int index, CurrencyProp v) {
        return this.getResources().set(index, v);
    }
    /**
     * get blueprintId
     *
     * @return blueprintId value
     */
    public int getBlueprintId() {
        return this.blueprintId;
    }

    /**
     * set blueprintId && set marked
     *
     * @param blueprintId new value
     * @return current object
     */
    public PlayerCampaignModelProp setBlueprintId(int blueprintId) {
        if (this.blueprintId != blueprintId) {
            this.mark(FIELD_INDEX_BLUEPRINTID);
            this.blueprintId = blueprintId;
        }
        return this;
    }

    /**
     * inner set blueprintId
     *
     * @param blueprintId new value
     */
    private void innerSetBlueprintId(int blueprintId) {
        this.blueprintId = blueprintId;
    }

    /**
     * get buffs
     *
     * @return buffs value
     */
    public Int32ListProp getBuffs() {
        if (this.buffs == null) {
            this.buffs = new Int32ListProp(this, FIELD_INDEX_BUFFS);
        }
        return this.buffs;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addBuffs(Integer v) {
        this.getBuffs().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getBuffsIndex(int index) {
        return this.getBuffs().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeBuffs(Integer v) {
        if (this.buffs == null) {
            return null;
        }
        if(this.buffs.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getBuffsSize() {
        if (this.buffs == null) {
            return 0;
        }
        return this.buffs.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isBuffsEmpty() {
        if (this.buffs == null) {
            return true;
        }
        return this.getBuffs().isEmpty();
    }

    /**
     * clear list
     */
    public void clearBuffs() {
        this.getBuffs().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeBuffsIndex(int index) {
        return this.getBuffs().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setBuffsIndex(int index, Integer v) {
        return this.getBuffs().set(index, v);
    }
    /**
     * get battleInfo
     *
     * @return battleInfo value
     */
    public com.google.protobuf.ByteString getBattleInfo() {
        return this.battleInfo;
    }

    /**
     * set battleInfo && set marked
     *
     * @param battleInfo new value
     * @return current object
     */
    public PlayerCampaignModelProp setBattleInfo(com.google.protobuf.ByteString battleInfo) {
        if (this.battleInfo != battleInfo) {
            this.mark(FIELD_INDEX_BATTLEINFO);
            this.battleInfo = battleInfo;
        }
        return this;
    }

    /**
     * inner set battleInfo
     *
     * @param battleInfo new value
     */
    private void innerSetBattleInfo(com.google.protobuf.ByteString battleInfo) {
        this.battleInfo = battleInfo;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerCampaignModelPB.Builder getCopyCsBuilder() {
        final PlayerCampaignModelPB.Builder builder = PlayerCampaignModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerCampaignModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getState() != CampaignState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.info != null) {
            PlayerPB.CampaignInfoPB.Builder tmpBuilder = PlayerPB.CampaignInfoPB.newBuilder();
            final int tmpFieldCnt = this.info.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInfo();
            }
        }  else if (builder.hasInfo()) {
            // 清理Info
            builder.clearInfo();
            fieldCnt++;
        }
        if (this.getCampaignLevel() != 0) {
            builder.setCampaignLevel(this.getCampaignLevel());
            fieldCnt++;
        }  else if (builder.hasCampaignLevel()) {
            // 清理CampaignLevel
            builder.clearCampaignLevel();
            fieldCnt++;
        }
        if (this.getMissionLevel() != 0) {
            builder.setMissionLevel(this.getMissionLevel());
            fieldCnt++;
        }  else if (builder.hasMissionLevel()) {
            // 清理MissionLevel
            builder.clearMissionLevel();
            fieldCnt++;
        }
        if (this.missions != null) {
            PlayerPB.CampaignMissionListPB.Builder tmpBuilder = PlayerPB.CampaignMissionListPB.newBuilder();
            final int tmpFieldCnt = this.missions.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMissions(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMissions();
            }
        }  else if (builder.hasMissions()) {
            // 清理Missions
            builder.clearMissions();
            fieldCnt++;
        }
        if (this.buildings != null) {
            StructPB.Int32CampaignBuildingMapPB.Builder tmpBuilder = StructPB.Int32CampaignBuildingMapPB.newBuilder();
            final int tmpFieldCnt = this.buildings.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuildings(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuildings();
            }
        }  else if (builder.hasBuildings()) {
            // 清理Buildings
            builder.clearBuildings();
            fieldCnt++;
        }
        if (this.units != null) {
            StructPB.Int32CampaignUnitMapPB.Builder tmpBuilder = StructPB.Int32CampaignUnitMapPB.newBuilder();
            final int tmpFieldCnt = this.units.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnits(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnits();
            }
        }  else if (builder.hasUnits()) {
            // 清理Units
            builder.clearUnits();
            fieldCnt++;
        }
        if (this.skills != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.skills.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkills(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkills();
            }
        }  else if (builder.hasSkills()) {
            // 清理Skills
            builder.clearSkills();
            fieldCnt++;
        }
        if (this.getItemSize() != 0) {
            builder.setItemSize(this.getItemSize());
            fieldCnt++;
        }  else if (builder.hasItemSize()) {
            // 清理ItemSize
            builder.clearItemSize();
            fieldCnt++;
        }
        if (this.items != null) {
            StructPB.Int32ItemPairMapPB.Builder tmpBuilder = StructPB.Int32ItemPairMapPB.newBuilder();
            final int tmpFieldCnt = this.items.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItems();
            }
        }  else if (builder.hasItems()) {
            // 清理Items
            builder.clearItems();
            fieldCnt++;
        }
        if (this.rewardItems != null) {
            StructPB.Int32ItemPairMapPB.Builder tmpBuilder = StructPB.Int32ItemPairMapPB.newBuilder();
            final int tmpFieldCnt = this.rewardItems.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardItems();
            }
        }  else if (builder.hasRewardItems()) {
            // 清理RewardItems
            builder.clearRewardItems();
            fieldCnt++;
        }
        if (this.additions != null) {
            StructPB.Int32CampaignAdditionMapPB.Builder tmpBuilder = StructPB.Int32CampaignAdditionMapPB.newBuilder();
            final int tmpFieldCnt = this.additions.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditions(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditions();
            }
        }  else if (builder.hasAdditions()) {
            // 清理Additions
            builder.clearAdditions();
            fieldCnt++;
        }
        if (this.event != null) {
            PlayerPB.CampaignEventPB.Builder tmpBuilder = PlayerPB.CampaignEventPB.newBuilder();
            final int tmpFieldCnt = this.event.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setEvent(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearEvent();
            }
        }  else if (builder.hasEvent()) {
            // 清理Event
            builder.clearEvent();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerCampaignModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INFO) && this.info != null) {
            final boolean needClear = !builder.hasInfo();
            final int tmpFieldCnt = this.info.copyChangeToCs(builder.getInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMPAIGNLEVEL)) {
            builder.setCampaignLevel(this.getCampaignLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MISSIONLEVEL)) {
            builder.setMissionLevel(this.getMissionLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MISSIONS) && this.missions != null) {
            final boolean needClear = !builder.hasMissions();
            final int tmpFieldCnt = this.missions.copyChangeToCs(builder.getMissionsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMissions();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUILDINGS) && this.buildings != null) {
            final boolean needClear = !builder.hasBuildings();
            final int tmpFieldCnt = this.buildings.copyChangeToCs(builder.getBuildingsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuildings();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNITS) && this.units != null) {
            final boolean needClear = !builder.hasUnits();
            final int tmpFieldCnt = this.units.copyChangeToCs(builder.getUnitsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnits();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKILLS) && this.skills != null) {
            final boolean needClear = !builder.hasSkills();
            final int tmpFieldCnt = this.skills.copyChangeToCs(builder.getSkillsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkills();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMSIZE)) {
            builder.setItemSize(this.getItemSize());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMS) && this.items != null) {
            final boolean needClear = !builder.hasItems();
            final int tmpFieldCnt = this.items.copyChangeToCs(builder.getItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDITEMS) && this.rewardItems != null) {
            final boolean needClear = !builder.hasRewardItems();
            final int tmpFieldCnt = this.rewardItems.copyChangeToCs(builder.getRewardItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONS) && this.additions != null) {
            final boolean needClear = !builder.hasAdditions();
            final int tmpFieldCnt = this.additions.copyChangeToCs(builder.getAdditionsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditions();
            }
        }
        if (this.hasMark(FIELD_INDEX_EVENT) && this.event != null) {
            final boolean needClear = !builder.hasEvent();
            final int tmpFieldCnt = this.event.copyChangeToCs(builder.getEventBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearEvent();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerCampaignModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INFO) && this.info != null) {
            final boolean needClear = !builder.hasInfo();
            final int tmpFieldCnt = this.info.copyChangeToAndClearDeleteKeysCs(builder.getInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMPAIGNLEVEL)) {
            builder.setCampaignLevel(this.getCampaignLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MISSIONLEVEL)) {
            builder.setMissionLevel(this.getMissionLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MISSIONS) && this.missions != null) {
            final boolean needClear = !builder.hasMissions();
            final int tmpFieldCnt = this.missions.copyChangeToCs(builder.getMissionsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMissions();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUILDINGS) && this.buildings != null) {
            final boolean needClear = !builder.hasBuildings();
            final int tmpFieldCnt = this.buildings.copyChangeToAndClearDeleteKeysCs(builder.getBuildingsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuildings();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNITS) && this.units != null) {
            final boolean needClear = !builder.hasUnits();
            final int tmpFieldCnt = this.units.copyChangeToAndClearDeleteKeysCs(builder.getUnitsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnits();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKILLS) && this.skills != null) {
            final boolean needClear = !builder.hasSkills();
            final int tmpFieldCnt = this.skills.copyChangeToCs(builder.getSkillsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkills();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMSIZE)) {
            builder.setItemSize(this.getItemSize());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMS) && this.items != null) {
            final boolean needClear = !builder.hasItems();
            final int tmpFieldCnt = this.items.copyChangeToAndClearDeleteKeysCs(builder.getItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDITEMS) && this.rewardItems != null) {
            final boolean needClear = !builder.hasRewardItems();
            final int tmpFieldCnt = this.rewardItems.copyChangeToAndClearDeleteKeysCs(builder.getRewardItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONS) && this.additions != null) {
            final boolean needClear = !builder.hasAdditions();
            final int tmpFieldCnt = this.additions.copyChangeToAndClearDeleteKeysCs(builder.getAdditionsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditions();
            }
        }
        if (this.hasMark(FIELD_INDEX_EVENT) && this.event != null) {
            final boolean needClear = !builder.hasEvent();
            final int tmpFieldCnt = this.event.copyChangeToAndClearDeleteKeysCs(builder.getEventBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearEvent();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerCampaignModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(CampaignState.forNumber(0));
        }
        if (proto.hasInfo()) {
            this.getInfo().mergeFromCs(proto.getInfo());
        } else {
            if (this.info != null) {
                this.info.mergeFromCs(proto.getInfo());
            }
        }
        if (proto.hasCampaignLevel()) {
            this.innerSetCampaignLevel(proto.getCampaignLevel());
        } else {
            this.innerSetCampaignLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMissionLevel()) {
            this.innerSetMissionLevel(proto.getMissionLevel());
        } else {
            this.innerSetMissionLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMissions()) {
            this.getMissions().mergeFromCs(proto.getMissions());
        } else {
            if (this.missions != null) {
                this.missions.mergeFromCs(proto.getMissions());
            }
        }
        if (proto.hasBuildings()) {
            this.getBuildings().mergeFromCs(proto.getBuildings());
        } else {
            if (this.buildings != null) {
                this.buildings.mergeFromCs(proto.getBuildings());
            }
        }
        if (proto.hasUnits()) {
            this.getUnits().mergeFromCs(proto.getUnits());
        } else {
            if (this.units != null) {
                this.units.mergeFromCs(proto.getUnits());
            }
        }
        if (proto.hasSkills()) {
            this.getSkills().mergeFromCs(proto.getSkills());
        } else {
            if (this.skills != null) {
                this.skills.mergeFromCs(proto.getSkills());
            }
        }
        if (proto.hasItemSize()) {
            this.innerSetItemSize(proto.getItemSize());
        } else {
            this.innerSetItemSize(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasItems()) {
            this.getItems().mergeFromCs(proto.getItems());
        } else {
            if (this.items != null) {
                this.items.mergeFromCs(proto.getItems());
            }
        }
        if (proto.hasRewardItems()) {
            this.getRewardItems().mergeFromCs(proto.getRewardItems());
        } else {
            if (this.rewardItems != null) {
                this.rewardItems.mergeFromCs(proto.getRewardItems());
            }
        }
        if (proto.hasAdditions()) {
            this.getAdditions().mergeFromCs(proto.getAdditions());
        } else {
            if (this.additions != null) {
                this.additions.mergeFromCs(proto.getAdditions());
            }
        }
        if (proto.hasEvent()) {
            this.getEvent().mergeFromCs(proto.getEvent());
        } else {
            if (this.event != null) {
                this.event.mergeFromCs(proto.getEvent());
            }
        }
        this.markAll();
        return PlayerCampaignModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerCampaignModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasInfo()) {
            this.getInfo().mergeChangeFromCs(proto.getInfo());
            fieldCnt++;
        }
        if (proto.hasCampaignLevel()) {
            this.setCampaignLevel(proto.getCampaignLevel());
            fieldCnt++;
        }
        if (proto.hasMissionLevel()) {
            this.setMissionLevel(proto.getMissionLevel());
            fieldCnt++;
        }
        if (proto.hasMissions()) {
            this.getMissions().mergeChangeFromCs(proto.getMissions());
            fieldCnt++;
        }
        if (proto.hasBuildings()) {
            this.getBuildings().mergeChangeFromCs(proto.getBuildings());
            fieldCnt++;
        }
        if (proto.hasUnits()) {
            this.getUnits().mergeChangeFromCs(proto.getUnits());
            fieldCnt++;
        }
        if (proto.hasSkills()) {
            this.getSkills().mergeChangeFromCs(proto.getSkills());
            fieldCnt++;
        }
        if (proto.hasItemSize()) {
            this.setItemSize(proto.getItemSize());
            fieldCnt++;
        }
        if (proto.hasItems()) {
            this.getItems().mergeChangeFromCs(proto.getItems());
            fieldCnt++;
        }
        if (proto.hasRewardItems()) {
            this.getRewardItems().mergeChangeFromCs(proto.getRewardItems());
            fieldCnt++;
        }
        if (proto.hasAdditions()) {
            this.getAdditions().mergeChangeFromCs(proto.getAdditions());
            fieldCnt++;
        }
        if (proto.hasEvent()) {
            this.getEvent().mergeChangeFromCs(proto.getEvent());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerCampaignModel.Builder getCopyDbBuilder() {
        final PlayerCampaignModel.Builder builder = PlayerCampaignModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerCampaignModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getState() != CampaignState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.info != null) {
            Player.CampaignInfo.Builder tmpBuilder = Player.CampaignInfo.newBuilder();
            final int tmpFieldCnt = this.info.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInfo();
            }
        }  else if (builder.hasInfo()) {
            // 清理Info
            builder.clearInfo();
            fieldCnt++;
        }
        if (this.getCampaignLevel() != 0) {
            builder.setCampaignLevel(this.getCampaignLevel());
            fieldCnt++;
        }  else if (builder.hasCampaignLevel()) {
            // 清理CampaignLevel
            builder.clearCampaignLevel();
            fieldCnt++;
        }
        if (this.getMissionLevel() != 0) {
            builder.setMissionLevel(this.getMissionLevel());
            fieldCnt++;
        }  else if (builder.hasMissionLevel()) {
            // 清理MissionLevel
            builder.clearMissionLevel();
            fieldCnt++;
        }
        if (this.missions != null) {
            Player.CampaignMissionList.Builder tmpBuilder = Player.CampaignMissionList.newBuilder();
            final int tmpFieldCnt = this.missions.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMissions(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMissions();
            }
        }  else if (builder.hasMissions()) {
            // 清理Missions
            builder.clearMissions();
            fieldCnt++;
        }
        if (this.buildings != null) {
            Struct.Int32CampaignBuildingMap.Builder tmpBuilder = Struct.Int32CampaignBuildingMap.newBuilder();
            final int tmpFieldCnt = this.buildings.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuildings(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuildings();
            }
        }  else if (builder.hasBuildings()) {
            // 清理Buildings
            builder.clearBuildings();
            fieldCnt++;
        }
        if (this.units != null) {
            Struct.Int32CampaignUnitMap.Builder tmpBuilder = Struct.Int32CampaignUnitMap.newBuilder();
            final int tmpFieldCnt = this.units.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnits(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnits();
            }
        }  else if (builder.hasUnits()) {
            // 清理Units
            builder.clearUnits();
            fieldCnt++;
        }
        if (this.skills != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.skills.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkills(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkills();
            }
        }  else if (builder.hasSkills()) {
            // 清理Skills
            builder.clearSkills();
            fieldCnt++;
        }
        if (this.getItemSize() != 0) {
            builder.setItemSize(this.getItemSize());
            fieldCnt++;
        }  else if (builder.hasItemSize()) {
            // 清理ItemSize
            builder.clearItemSize();
            fieldCnt++;
        }
        if (this.items != null) {
            Struct.Int32ItemPairMap.Builder tmpBuilder = Struct.Int32ItemPairMap.newBuilder();
            final int tmpFieldCnt = this.items.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItems();
            }
        }  else if (builder.hasItems()) {
            // 清理Items
            builder.clearItems();
            fieldCnt++;
        }
        if (this.rewardItems != null) {
            Struct.Int32ItemPairMap.Builder tmpBuilder = Struct.Int32ItemPairMap.newBuilder();
            final int tmpFieldCnt = this.rewardItems.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardItems();
            }
        }  else if (builder.hasRewardItems()) {
            // 清理RewardItems
            builder.clearRewardItems();
            fieldCnt++;
        }
        if (this.additions != null) {
            Struct.Int32CampaignAdditionMap.Builder tmpBuilder = Struct.Int32CampaignAdditionMap.newBuilder();
            final int tmpFieldCnt = this.additions.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditions(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditions();
            }
        }  else if (builder.hasAdditions()) {
            // 清理Additions
            builder.clearAdditions();
            fieldCnt++;
        }
        if (this.event != null) {
            Player.CampaignEvent.Builder tmpBuilder = Player.CampaignEvent.newBuilder();
            final int tmpFieldCnt = this.event.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setEvent(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearEvent();
            }
        }  else if (builder.hasEvent()) {
            // 清理Event
            builder.clearEvent();
            fieldCnt++;
        }
        if (this.battle != null) {
            Player.CampaignBattle.Builder tmpBuilder = Player.CampaignBattle.newBuilder();
            final int tmpFieldCnt = this.battle.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattle();
            }
        }  else if (builder.hasBattle()) {
            // 清理Battle
            builder.clearBattle();
            fieldCnt++;
        }
        if (this.resources != null) {
            Struct.CurrencyList.Builder tmpBuilder = Struct.CurrencyList.newBuilder();
            final int tmpFieldCnt = this.resources.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResources(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResources();
            }
        }  else if (builder.hasResources()) {
            // 清理Resources
            builder.clearResources();
            fieldCnt++;
        }
        if (this.getBlueprintId() != 0) {
            builder.setBlueprintId(this.getBlueprintId());
            fieldCnt++;
        }  else if (builder.hasBlueprintId()) {
            // 清理BlueprintId
            builder.clearBlueprintId();
            fieldCnt++;
        }
        if (this.buffs != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.buffs.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuffs(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuffs();
            }
        }  else if (builder.hasBuffs()) {
            // 清理Buffs
            builder.clearBuffs();
            fieldCnt++;
        }
        if (this.getBattleInfo() != com.google.protobuf.ByteString.EMPTY) {
            builder.setBattleInfo(this.getBattleInfo());
            fieldCnt++;
        }  else if (builder.hasBattleInfo()) {
            // 清理BattleInfo
            builder.clearBattleInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerCampaignModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INFO) && this.info != null) {
            final boolean needClear = !builder.hasInfo();
            final int tmpFieldCnt = this.info.copyChangeToDb(builder.getInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMPAIGNLEVEL)) {
            builder.setCampaignLevel(this.getCampaignLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MISSIONLEVEL)) {
            builder.setMissionLevel(this.getMissionLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MISSIONS) && this.missions != null) {
            final boolean needClear = !builder.hasMissions();
            final int tmpFieldCnt = this.missions.copyChangeToDb(builder.getMissionsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMissions();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUILDINGS) && this.buildings != null) {
            final boolean needClear = !builder.hasBuildings();
            final int tmpFieldCnt = this.buildings.copyChangeToDb(builder.getBuildingsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuildings();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNITS) && this.units != null) {
            final boolean needClear = !builder.hasUnits();
            final int tmpFieldCnt = this.units.copyChangeToDb(builder.getUnitsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnits();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKILLS) && this.skills != null) {
            final boolean needClear = !builder.hasSkills();
            final int tmpFieldCnt = this.skills.copyChangeToDb(builder.getSkillsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkills();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMSIZE)) {
            builder.setItemSize(this.getItemSize());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMS) && this.items != null) {
            final boolean needClear = !builder.hasItems();
            final int tmpFieldCnt = this.items.copyChangeToDb(builder.getItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDITEMS) && this.rewardItems != null) {
            final boolean needClear = !builder.hasRewardItems();
            final int tmpFieldCnt = this.rewardItems.copyChangeToDb(builder.getRewardItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONS) && this.additions != null) {
            final boolean needClear = !builder.hasAdditions();
            final int tmpFieldCnt = this.additions.copyChangeToDb(builder.getAdditionsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditions();
            }
        }
        if (this.hasMark(FIELD_INDEX_EVENT) && this.event != null) {
            final boolean needClear = !builder.hasEvent();
            final int tmpFieldCnt = this.event.copyChangeToDb(builder.getEventBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearEvent();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToDb(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            final boolean needClear = !builder.hasResources();
            final int tmpFieldCnt = this.resources.copyChangeToDb(builder.getResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResources();
            }
        }
        if (this.hasMark(FIELD_INDEX_BLUEPRINTID)) {
            builder.setBlueprintId(this.getBlueprintId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUFFS) && this.buffs != null) {
            final boolean needClear = !builder.hasBuffs();
            final int tmpFieldCnt = this.buffs.copyChangeToDb(builder.getBuffsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuffs();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLEINFO)) {
            builder.setBattleInfo(this.getBattleInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerCampaignModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(CampaignState.forNumber(0));
        }
        if (proto.hasInfo()) {
            this.getInfo().mergeFromDb(proto.getInfo());
        } else {
            if (this.info != null) {
                this.info.mergeFromDb(proto.getInfo());
            }
        }
        if (proto.hasCampaignLevel()) {
            this.innerSetCampaignLevel(proto.getCampaignLevel());
        } else {
            this.innerSetCampaignLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMissionLevel()) {
            this.innerSetMissionLevel(proto.getMissionLevel());
        } else {
            this.innerSetMissionLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMissions()) {
            this.getMissions().mergeFromDb(proto.getMissions());
        } else {
            if (this.missions != null) {
                this.missions.mergeFromDb(proto.getMissions());
            }
        }
        if (proto.hasBuildings()) {
            this.getBuildings().mergeFromDb(proto.getBuildings());
        } else {
            if (this.buildings != null) {
                this.buildings.mergeFromDb(proto.getBuildings());
            }
        }
        if (proto.hasUnits()) {
            this.getUnits().mergeFromDb(proto.getUnits());
        } else {
            if (this.units != null) {
                this.units.mergeFromDb(proto.getUnits());
            }
        }
        if (proto.hasSkills()) {
            this.getSkills().mergeFromDb(proto.getSkills());
        } else {
            if (this.skills != null) {
                this.skills.mergeFromDb(proto.getSkills());
            }
        }
        if (proto.hasItemSize()) {
            this.innerSetItemSize(proto.getItemSize());
        } else {
            this.innerSetItemSize(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasItems()) {
            this.getItems().mergeFromDb(proto.getItems());
        } else {
            if (this.items != null) {
                this.items.mergeFromDb(proto.getItems());
            }
        }
        if (proto.hasRewardItems()) {
            this.getRewardItems().mergeFromDb(proto.getRewardItems());
        } else {
            if (this.rewardItems != null) {
                this.rewardItems.mergeFromDb(proto.getRewardItems());
            }
        }
        if (proto.hasAdditions()) {
            this.getAdditions().mergeFromDb(proto.getAdditions());
        } else {
            if (this.additions != null) {
                this.additions.mergeFromDb(proto.getAdditions());
            }
        }
        if (proto.hasEvent()) {
            this.getEvent().mergeFromDb(proto.getEvent());
        } else {
            if (this.event != null) {
                this.event.mergeFromDb(proto.getEvent());
            }
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeFromDb(proto.getBattle());
        } else {
            if (this.battle != null) {
                this.battle.mergeFromDb(proto.getBattle());
            }
        }
        if (proto.hasResources()) {
            this.getResources().mergeFromDb(proto.getResources());
        } else {
            if (this.resources != null) {
                this.resources.mergeFromDb(proto.getResources());
            }
        }
        if (proto.hasBlueprintId()) {
            this.innerSetBlueprintId(proto.getBlueprintId());
        } else {
            this.innerSetBlueprintId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuffs()) {
            this.getBuffs().mergeFromDb(proto.getBuffs());
        } else {
            if (this.buffs != null) {
                this.buffs.mergeFromDb(proto.getBuffs());
            }
        }
        if (proto.hasBattleInfo()) {
            this.innerSetBattleInfo(proto.getBattleInfo());
        } else {
            this.innerSetBattleInfo(Constant.DEFAULT_BYTE_STRING_VALUE);
        }
        this.markAll();
        return PlayerCampaignModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerCampaignModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasInfo()) {
            this.getInfo().mergeChangeFromDb(proto.getInfo());
            fieldCnt++;
        }
        if (proto.hasCampaignLevel()) {
            this.setCampaignLevel(proto.getCampaignLevel());
            fieldCnt++;
        }
        if (proto.hasMissionLevel()) {
            this.setMissionLevel(proto.getMissionLevel());
            fieldCnt++;
        }
        if (proto.hasMissions()) {
            this.getMissions().mergeChangeFromDb(proto.getMissions());
            fieldCnt++;
        }
        if (proto.hasBuildings()) {
            this.getBuildings().mergeChangeFromDb(proto.getBuildings());
            fieldCnt++;
        }
        if (proto.hasUnits()) {
            this.getUnits().mergeChangeFromDb(proto.getUnits());
            fieldCnt++;
        }
        if (proto.hasSkills()) {
            this.getSkills().mergeChangeFromDb(proto.getSkills());
            fieldCnt++;
        }
        if (proto.hasItemSize()) {
            this.setItemSize(proto.getItemSize());
            fieldCnt++;
        }
        if (proto.hasItems()) {
            this.getItems().mergeChangeFromDb(proto.getItems());
            fieldCnt++;
        }
        if (proto.hasRewardItems()) {
            this.getRewardItems().mergeChangeFromDb(proto.getRewardItems());
            fieldCnt++;
        }
        if (proto.hasAdditions()) {
            this.getAdditions().mergeChangeFromDb(proto.getAdditions());
            fieldCnt++;
        }
        if (proto.hasEvent()) {
            this.getEvent().mergeChangeFromDb(proto.getEvent());
            fieldCnt++;
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeChangeFromDb(proto.getBattle());
            fieldCnt++;
        }
        if (proto.hasResources()) {
            this.getResources().mergeChangeFromDb(proto.getResources());
            fieldCnt++;
        }
        if (proto.hasBlueprintId()) {
            this.setBlueprintId(proto.getBlueprintId());
            fieldCnt++;
        }
        if (proto.hasBuffs()) {
            this.getBuffs().mergeChangeFromDb(proto.getBuffs());
            fieldCnt++;
        }
        if (proto.hasBattleInfo()) {
            this.setBattleInfo(proto.getBattleInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerCampaignModel.Builder getCopySsBuilder() {
        final PlayerCampaignModel.Builder builder = PlayerCampaignModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerCampaignModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getState() != CampaignState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.info != null) {
            Player.CampaignInfo.Builder tmpBuilder = Player.CampaignInfo.newBuilder();
            final int tmpFieldCnt = this.info.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInfo();
            }
        }  else if (builder.hasInfo()) {
            // 清理Info
            builder.clearInfo();
            fieldCnt++;
        }
        if (this.getCampaignLevel() != 0) {
            builder.setCampaignLevel(this.getCampaignLevel());
            fieldCnt++;
        }  else if (builder.hasCampaignLevel()) {
            // 清理CampaignLevel
            builder.clearCampaignLevel();
            fieldCnt++;
        }
        if (this.getMissionLevel() != 0) {
            builder.setMissionLevel(this.getMissionLevel());
            fieldCnt++;
        }  else if (builder.hasMissionLevel()) {
            // 清理MissionLevel
            builder.clearMissionLevel();
            fieldCnt++;
        }
        if (this.missions != null) {
            Player.CampaignMissionList.Builder tmpBuilder = Player.CampaignMissionList.newBuilder();
            final int tmpFieldCnt = this.missions.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMissions(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMissions();
            }
        }  else if (builder.hasMissions()) {
            // 清理Missions
            builder.clearMissions();
            fieldCnt++;
        }
        if (this.buildings != null) {
            Struct.Int32CampaignBuildingMap.Builder tmpBuilder = Struct.Int32CampaignBuildingMap.newBuilder();
            final int tmpFieldCnt = this.buildings.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuildings(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuildings();
            }
        }  else if (builder.hasBuildings()) {
            // 清理Buildings
            builder.clearBuildings();
            fieldCnt++;
        }
        if (this.units != null) {
            Struct.Int32CampaignUnitMap.Builder tmpBuilder = Struct.Int32CampaignUnitMap.newBuilder();
            final int tmpFieldCnt = this.units.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnits(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnits();
            }
        }  else if (builder.hasUnits()) {
            // 清理Units
            builder.clearUnits();
            fieldCnt++;
        }
        if (this.skills != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.skills.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkills(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkills();
            }
        }  else if (builder.hasSkills()) {
            // 清理Skills
            builder.clearSkills();
            fieldCnt++;
        }
        if (this.getItemSize() != 0) {
            builder.setItemSize(this.getItemSize());
            fieldCnt++;
        }  else if (builder.hasItemSize()) {
            // 清理ItemSize
            builder.clearItemSize();
            fieldCnt++;
        }
        if (this.items != null) {
            Struct.Int32ItemPairMap.Builder tmpBuilder = Struct.Int32ItemPairMap.newBuilder();
            final int tmpFieldCnt = this.items.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItems();
            }
        }  else if (builder.hasItems()) {
            // 清理Items
            builder.clearItems();
            fieldCnt++;
        }
        if (this.rewardItems != null) {
            Struct.Int32ItemPairMap.Builder tmpBuilder = Struct.Int32ItemPairMap.newBuilder();
            final int tmpFieldCnt = this.rewardItems.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardItems();
            }
        }  else if (builder.hasRewardItems()) {
            // 清理RewardItems
            builder.clearRewardItems();
            fieldCnt++;
        }
        if (this.additions != null) {
            Struct.Int32CampaignAdditionMap.Builder tmpBuilder = Struct.Int32CampaignAdditionMap.newBuilder();
            final int tmpFieldCnt = this.additions.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditions(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditions();
            }
        }  else if (builder.hasAdditions()) {
            // 清理Additions
            builder.clearAdditions();
            fieldCnt++;
        }
        if (this.event != null) {
            Player.CampaignEvent.Builder tmpBuilder = Player.CampaignEvent.newBuilder();
            final int tmpFieldCnt = this.event.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setEvent(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearEvent();
            }
        }  else if (builder.hasEvent()) {
            // 清理Event
            builder.clearEvent();
            fieldCnt++;
        }
        if (this.battle != null) {
            Player.CampaignBattle.Builder tmpBuilder = Player.CampaignBattle.newBuilder();
            final int tmpFieldCnt = this.battle.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattle();
            }
        }  else if (builder.hasBattle()) {
            // 清理Battle
            builder.clearBattle();
            fieldCnt++;
        }
        if (this.resources != null) {
            Struct.CurrencyList.Builder tmpBuilder = Struct.CurrencyList.newBuilder();
            final int tmpFieldCnt = this.resources.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResources(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResources();
            }
        }  else if (builder.hasResources()) {
            // 清理Resources
            builder.clearResources();
            fieldCnt++;
        }
        if (this.getBlueprintId() != 0) {
            builder.setBlueprintId(this.getBlueprintId());
            fieldCnt++;
        }  else if (builder.hasBlueprintId()) {
            // 清理BlueprintId
            builder.clearBlueprintId();
            fieldCnt++;
        }
        if (this.buffs != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.buffs.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuffs(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuffs();
            }
        }  else if (builder.hasBuffs()) {
            // 清理Buffs
            builder.clearBuffs();
            fieldCnt++;
        }
        if (this.getBattleInfo() != com.google.protobuf.ByteString.EMPTY) {
            builder.setBattleInfo(this.getBattleInfo());
            fieldCnt++;
        }  else if (builder.hasBattleInfo()) {
            // 清理BattleInfo
            builder.clearBattleInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerCampaignModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INFO) && this.info != null) {
            final boolean needClear = !builder.hasInfo();
            final int tmpFieldCnt = this.info.copyChangeToSs(builder.getInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMPAIGNLEVEL)) {
            builder.setCampaignLevel(this.getCampaignLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MISSIONLEVEL)) {
            builder.setMissionLevel(this.getMissionLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MISSIONS) && this.missions != null) {
            final boolean needClear = !builder.hasMissions();
            final int tmpFieldCnt = this.missions.copyChangeToSs(builder.getMissionsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMissions();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUILDINGS) && this.buildings != null) {
            final boolean needClear = !builder.hasBuildings();
            final int tmpFieldCnt = this.buildings.copyChangeToSs(builder.getBuildingsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuildings();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNITS) && this.units != null) {
            final boolean needClear = !builder.hasUnits();
            final int tmpFieldCnt = this.units.copyChangeToSs(builder.getUnitsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnits();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKILLS) && this.skills != null) {
            final boolean needClear = !builder.hasSkills();
            final int tmpFieldCnt = this.skills.copyChangeToSs(builder.getSkillsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkills();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMSIZE)) {
            builder.setItemSize(this.getItemSize());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMS) && this.items != null) {
            final boolean needClear = !builder.hasItems();
            final int tmpFieldCnt = this.items.copyChangeToSs(builder.getItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDITEMS) && this.rewardItems != null) {
            final boolean needClear = !builder.hasRewardItems();
            final int tmpFieldCnt = this.rewardItems.copyChangeToSs(builder.getRewardItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONS) && this.additions != null) {
            final boolean needClear = !builder.hasAdditions();
            final int tmpFieldCnt = this.additions.copyChangeToSs(builder.getAdditionsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditions();
            }
        }
        if (this.hasMark(FIELD_INDEX_EVENT) && this.event != null) {
            final boolean needClear = !builder.hasEvent();
            final int tmpFieldCnt = this.event.copyChangeToSs(builder.getEventBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearEvent();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToSs(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            final boolean needClear = !builder.hasResources();
            final int tmpFieldCnt = this.resources.copyChangeToSs(builder.getResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResources();
            }
        }
        if (this.hasMark(FIELD_INDEX_BLUEPRINTID)) {
            builder.setBlueprintId(this.getBlueprintId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUFFS) && this.buffs != null) {
            final boolean needClear = !builder.hasBuffs();
            final int tmpFieldCnt = this.buffs.copyChangeToSs(builder.getBuffsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuffs();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLEINFO)) {
            builder.setBattleInfo(this.getBattleInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerCampaignModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(CampaignState.forNumber(0));
        }
        if (proto.hasInfo()) {
            this.getInfo().mergeFromSs(proto.getInfo());
        } else {
            if (this.info != null) {
                this.info.mergeFromSs(proto.getInfo());
            }
        }
        if (proto.hasCampaignLevel()) {
            this.innerSetCampaignLevel(proto.getCampaignLevel());
        } else {
            this.innerSetCampaignLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMissionLevel()) {
            this.innerSetMissionLevel(proto.getMissionLevel());
        } else {
            this.innerSetMissionLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMissions()) {
            this.getMissions().mergeFromSs(proto.getMissions());
        } else {
            if (this.missions != null) {
                this.missions.mergeFromSs(proto.getMissions());
            }
        }
        if (proto.hasBuildings()) {
            this.getBuildings().mergeFromSs(proto.getBuildings());
        } else {
            if (this.buildings != null) {
                this.buildings.mergeFromSs(proto.getBuildings());
            }
        }
        if (proto.hasUnits()) {
            this.getUnits().mergeFromSs(proto.getUnits());
        } else {
            if (this.units != null) {
                this.units.mergeFromSs(proto.getUnits());
            }
        }
        if (proto.hasSkills()) {
            this.getSkills().mergeFromSs(proto.getSkills());
        } else {
            if (this.skills != null) {
                this.skills.mergeFromSs(proto.getSkills());
            }
        }
        if (proto.hasItemSize()) {
            this.innerSetItemSize(proto.getItemSize());
        } else {
            this.innerSetItemSize(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasItems()) {
            this.getItems().mergeFromSs(proto.getItems());
        } else {
            if (this.items != null) {
                this.items.mergeFromSs(proto.getItems());
            }
        }
        if (proto.hasRewardItems()) {
            this.getRewardItems().mergeFromSs(proto.getRewardItems());
        } else {
            if (this.rewardItems != null) {
                this.rewardItems.mergeFromSs(proto.getRewardItems());
            }
        }
        if (proto.hasAdditions()) {
            this.getAdditions().mergeFromSs(proto.getAdditions());
        } else {
            if (this.additions != null) {
                this.additions.mergeFromSs(proto.getAdditions());
            }
        }
        if (proto.hasEvent()) {
            this.getEvent().mergeFromSs(proto.getEvent());
        } else {
            if (this.event != null) {
                this.event.mergeFromSs(proto.getEvent());
            }
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeFromSs(proto.getBattle());
        } else {
            if (this.battle != null) {
                this.battle.mergeFromSs(proto.getBattle());
            }
        }
        if (proto.hasResources()) {
            this.getResources().mergeFromSs(proto.getResources());
        } else {
            if (this.resources != null) {
                this.resources.mergeFromSs(proto.getResources());
            }
        }
        if (proto.hasBlueprintId()) {
            this.innerSetBlueprintId(proto.getBlueprintId());
        } else {
            this.innerSetBlueprintId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuffs()) {
            this.getBuffs().mergeFromSs(proto.getBuffs());
        } else {
            if (this.buffs != null) {
                this.buffs.mergeFromSs(proto.getBuffs());
            }
        }
        if (proto.hasBattleInfo()) {
            this.innerSetBattleInfo(proto.getBattleInfo());
        } else {
            this.innerSetBattleInfo(Constant.DEFAULT_BYTE_STRING_VALUE);
        }
        this.markAll();
        return PlayerCampaignModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerCampaignModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasInfo()) {
            this.getInfo().mergeChangeFromSs(proto.getInfo());
            fieldCnt++;
        }
        if (proto.hasCampaignLevel()) {
            this.setCampaignLevel(proto.getCampaignLevel());
            fieldCnt++;
        }
        if (proto.hasMissionLevel()) {
            this.setMissionLevel(proto.getMissionLevel());
            fieldCnt++;
        }
        if (proto.hasMissions()) {
            this.getMissions().mergeChangeFromSs(proto.getMissions());
            fieldCnt++;
        }
        if (proto.hasBuildings()) {
            this.getBuildings().mergeChangeFromSs(proto.getBuildings());
            fieldCnt++;
        }
        if (proto.hasUnits()) {
            this.getUnits().mergeChangeFromSs(proto.getUnits());
            fieldCnt++;
        }
        if (proto.hasSkills()) {
            this.getSkills().mergeChangeFromSs(proto.getSkills());
            fieldCnt++;
        }
        if (proto.hasItemSize()) {
            this.setItemSize(proto.getItemSize());
            fieldCnt++;
        }
        if (proto.hasItems()) {
            this.getItems().mergeChangeFromSs(proto.getItems());
            fieldCnt++;
        }
        if (proto.hasRewardItems()) {
            this.getRewardItems().mergeChangeFromSs(proto.getRewardItems());
            fieldCnt++;
        }
        if (proto.hasAdditions()) {
            this.getAdditions().mergeChangeFromSs(proto.getAdditions());
            fieldCnt++;
        }
        if (proto.hasEvent()) {
            this.getEvent().mergeChangeFromSs(proto.getEvent());
            fieldCnt++;
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeChangeFromSs(proto.getBattle());
            fieldCnt++;
        }
        if (proto.hasResources()) {
            this.getResources().mergeChangeFromSs(proto.getResources());
            fieldCnt++;
        }
        if (proto.hasBlueprintId()) {
            this.setBlueprintId(proto.getBlueprintId());
            fieldCnt++;
        }
        if (proto.hasBuffs()) {
            this.getBuffs().mergeChangeFromSs(proto.getBuffs());
            fieldCnt++;
        }
        if (proto.hasBattleInfo()) {
            this.setBattleInfo(proto.getBattleInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerCampaignModel.Builder builder = PlayerCampaignModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_INFO) && this.info != null) {
            this.info.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_MISSIONS) && this.missions != null) {
            this.missions.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BUILDINGS) && this.buildings != null) {
            this.buildings.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_UNITS) && this.units != null) {
            this.units.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SKILLS) && this.skills != null) {
            this.skills.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ITEMS) && this.items != null) {
            this.items.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_REWARDITEMS) && this.rewardItems != null) {
            this.rewardItems.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONS) && this.additions != null) {
            this.additions.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_EVENT) && this.event != null) {
            this.event.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            this.battle.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            this.resources.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BUFFS) && this.buffs != null) {
            this.buffs.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.info != null) {
            this.info.markAll();
        }
        if (this.missions != null) {
            this.missions.markAll();
        }
        if (this.buildings != null) {
            this.buildings.markAll();
        }
        if (this.units != null) {
            this.units.markAll();
        }
        if (this.skills != null) {
            this.skills.markAll();
        }
        if (this.items != null) {
            this.items.markAll();
        }
        if (this.rewardItems != null) {
            this.rewardItems.markAll();
        }
        if (this.additions != null) {
            this.additions.markAll();
        }
        if (this.event != null) {
            this.event.markAll();
        }
        if (this.battle != null) {
            this.battle.markAll();
        }
        if (this.resources != null) {
            this.resources.markAll();
        }
        if (this.buffs != null) {
            this.buffs.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerCampaignModelProp)) {
            return false;
        }
        final PlayerCampaignModelProp otherNode = (PlayerCampaignModelProp) node;
        if (this.state != otherNode.state) {
            return false;
        }
        if (!this.getInfo().compareDataTo(otherNode.getInfo())) {
            return false;
        }
        if (this.campaignLevel != otherNode.campaignLevel) {
            return false;
        }
        if (this.missionLevel != otherNode.missionLevel) {
            return false;
        }
        if (!this.getMissions().compareDataTo(otherNode.getMissions())) {
            return false;
        }
        if (!this.getBuildings().compareDataTo(otherNode.getBuildings())) {
            return false;
        }
        if (!this.getUnits().compareDataTo(otherNode.getUnits())) {
            return false;
        }
        if (!this.getSkills().compareDataTo(otherNode.getSkills())) {
            return false;
        }
        if (this.itemSize != otherNode.itemSize) {
            return false;
        }
        if (!this.getItems().compareDataTo(otherNode.getItems())) {
            return false;
        }
        if (!this.getRewardItems().compareDataTo(otherNode.getRewardItems())) {
            return false;
        }
        if (!this.getAdditions().compareDataTo(otherNode.getAdditions())) {
            return false;
        }
        if (!this.getEvent().compareDataTo(otherNode.getEvent())) {
            return false;
        }
        if (!this.getBattle().compareDataTo(otherNode.getBattle())) {
            return false;
        }
        if (!this.getResources().compareDataTo(otherNode.getResources())) {
            return false;
        }
        if (this.blueprintId != otherNode.blueprintId) {
            return false;
        }
        if (!this.getBuffs().compareDataTo(otherNode.getBuffs())) {
            return false;
        }
        if (this.battleInfo != otherNode.battleInfo) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 46;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
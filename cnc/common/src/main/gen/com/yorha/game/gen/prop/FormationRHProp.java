package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructPlayer.FormationRH;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPlayerPB.FormationRHPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class FormationRHProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_SLOTID = 0;
    public static final int FIELD_INDEX_HEROID = 1;
    public static final int FIELD_INDEX_SOLDIER = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int slotId = Constant.DEFAULT_INT_VALUE;
    private int heroId = Constant.DEFAULT_INT_VALUE;
    private SoldierProp soldier = null;

    public FormationRHProp() {
        super(null, 0, FIELD_COUNT);
    }

    public FormationRHProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get slotId
     *
     * @return slotId value
     */
    public int getSlotId() {
        return this.slotId;
    }

    /**
     * set slotId && set marked
     *
     * @param slotId new value
     * @return current object
     */
    public FormationRHProp setSlotId(int slotId) {
        if (this.slotId != slotId) {
            this.mark(FIELD_INDEX_SLOTID);
            this.slotId = slotId;
        }
        return this;
    }

    /**
     * inner set slotId
     *
     * @param slotId new value
     */
    private void innerSetSlotId(int slotId) {
        this.slotId = slotId;
    }

    /**
     * get heroId
     *
     * @return heroId value
     */
    public int getHeroId() {
        return this.heroId;
    }

    /**
     * set heroId && set marked
     *
     * @param heroId new value
     * @return current object
     */
    public FormationRHProp setHeroId(int heroId) {
        if (this.heroId != heroId) {
            this.mark(FIELD_INDEX_HEROID);
            this.heroId = heroId;
        }
        return this;
    }

    /**
     * inner set heroId
     *
     * @param heroId new value
     */
    private void innerSetHeroId(int heroId) {
        this.heroId = heroId;
    }

    /**
     * get soldier
     *
     * @return soldier value
     */
    public SoldierProp getSoldier() {
        if (this.soldier == null) {
            this.soldier = new SoldierProp(this, FIELD_INDEX_SOLDIER);
        }
        return this.soldier;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public FormationRHPB.Builder getCopyCsBuilder() {
        final FormationRHPB.Builder builder = FormationRHPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(FormationRHPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSlotId() != 0) {
            builder.setSlotId(this.getSlotId());
            fieldCnt++;
        }  else if (builder.hasSlotId()) {
            // 清理SlotId
            builder.clearSlotId();
            fieldCnt++;
        }
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.soldier != null) {
            StructPB.SoldierPB.Builder tmpBuilder = StructPB.SoldierPB.newBuilder();
            final int tmpFieldCnt = this.soldier.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldier(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldier();
            }
        }  else if (builder.hasSoldier()) {
            // 清理Soldier
            builder.clearSoldier();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(FormationRHPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SLOTID)) {
            builder.setSlotId(this.getSlotId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIER) && this.soldier != null) {
            final boolean needClear = !builder.hasSoldier();
            final int tmpFieldCnt = this.soldier.copyChangeToCs(builder.getSoldierBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldier();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(FormationRHPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SLOTID)) {
            builder.setSlotId(this.getSlotId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIER) && this.soldier != null) {
            final boolean needClear = !builder.hasSoldier();
            final int tmpFieldCnt = this.soldier.copyChangeToAndClearDeleteKeysCs(builder.getSoldierBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldier();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(FormationRHPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSlotId()) {
            this.innerSetSlotId(proto.getSlotId());
        } else {
            this.innerSetSlotId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSoldier()) {
            this.getSoldier().mergeFromCs(proto.getSoldier());
        } else {
            if (this.soldier != null) {
                this.soldier.mergeFromCs(proto.getSoldier());
            }
        }
        this.markAll();
        return FormationRHProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(FormationRHPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSlotId()) {
            this.setSlotId(proto.getSlotId());
            fieldCnt++;
        }
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasSoldier()) {
            this.getSoldier().mergeChangeFromCs(proto.getSoldier());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public FormationRH.Builder getCopyDbBuilder() {
        final FormationRH.Builder builder = FormationRH.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(FormationRH.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSlotId() != 0) {
            builder.setSlotId(this.getSlotId());
            fieldCnt++;
        }  else if (builder.hasSlotId()) {
            // 清理SlotId
            builder.clearSlotId();
            fieldCnt++;
        }
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.soldier != null) {
            Struct.Soldier.Builder tmpBuilder = Struct.Soldier.newBuilder();
            final int tmpFieldCnt = this.soldier.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldier(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldier();
            }
        }  else if (builder.hasSoldier()) {
            // 清理Soldier
            builder.clearSoldier();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(FormationRH.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SLOTID)) {
            builder.setSlotId(this.getSlotId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIER) && this.soldier != null) {
            final boolean needClear = !builder.hasSoldier();
            final int tmpFieldCnt = this.soldier.copyChangeToDb(builder.getSoldierBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldier();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(FormationRH proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSlotId()) {
            this.innerSetSlotId(proto.getSlotId());
        } else {
            this.innerSetSlotId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSoldier()) {
            this.getSoldier().mergeFromDb(proto.getSoldier());
        } else {
            if (this.soldier != null) {
                this.soldier.mergeFromDb(proto.getSoldier());
            }
        }
        this.markAll();
        return FormationRHProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(FormationRH proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSlotId()) {
            this.setSlotId(proto.getSlotId());
            fieldCnt++;
        }
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasSoldier()) {
            this.getSoldier().mergeChangeFromDb(proto.getSoldier());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public FormationRH.Builder getCopySsBuilder() {
        final FormationRH.Builder builder = FormationRH.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(FormationRH.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSlotId() != 0) {
            builder.setSlotId(this.getSlotId());
            fieldCnt++;
        }  else if (builder.hasSlotId()) {
            // 清理SlotId
            builder.clearSlotId();
            fieldCnt++;
        }
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.soldier != null) {
            Struct.Soldier.Builder tmpBuilder = Struct.Soldier.newBuilder();
            final int tmpFieldCnt = this.soldier.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldier(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldier();
            }
        }  else if (builder.hasSoldier()) {
            // 清理Soldier
            builder.clearSoldier();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(FormationRH.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SLOTID)) {
            builder.setSlotId(this.getSlotId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIER) && this.soldier != null) {
            final boolean needClear = !builder.hasSoldier();
            final int tmpFieldCnt = this.soldier.copyChangeToSs(builder.getSoldierBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldier();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(FormationRH proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSlotId()) {
            this.innerSetSlotId(proto.getSlotId());
        } else {
            this.innerSetSlotId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSoldier()) {
            this.getSoldier().mergeFromSs(proto.getSoldier());
        } else {
            if (this.soldier != null) {
                this.soldier.mergeFromSs(proto.getSoldier());
            }
        }
        this.markAll();
        return FormationRHProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(FormationRH proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSlotId()) {
            this.setSlotId(proto.getSlotId());
            fieldCnt++;
        }
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasSoldier()) {
            this.getSoldier().mergeChangeFromSs(proto.getSoldier());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        FormationRH.Builder builder = FormationRH.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIER) && this.soldier != null) {
            this.soldier.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.soldier != null) {
            this.soldier.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.slotId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof FormationRHProp)) {
            return false;
        }
        final FormationRHProp otherNode = (FormationRHProp) node;
        if (this.slotId != otherNode.slotId) {
            return false;
        }
        if (this.heroId != otherNode.heroId) {
            return false;
        }
        if (!this.getSoldier().compareDataTo(otherNode.getSoldier())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
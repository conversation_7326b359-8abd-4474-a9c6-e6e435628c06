package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Struct.ClanRecord;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ClanRecordPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanRecordProp extends AbstractPropNode {

    public static final int FIELD_INDEX_RECORDTYPE = 0;
    public static final int FIELD_INDEX_PARAMS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private ClanRecordType recordType = ClanRecordType.forNumber(0);
    private DisplayDataProp params = null;

    public ClanRecordProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanRecordProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get recordType
     *
     * @return recordType value
     */
    public ClanRecordType getRecordType() {
        return this.recordType;
    }

    /**
     * set recordType && set marked
     *
     * @param recordType new value
     * @return current object
     */
    public ClanRecordProp setRecordType(ClanRecordType recordType) {
        if (recordType == null) {
            throw new NullPointerException();
        }
        if (this.recordType != recordType) {
            this.mark(FIELD_INDEX_RECORDTYPE);
            this.recordType = recordType;
        }
        return this;
    }

    /**
     * inner set recordType
     *
     * @param recordType new value
     */
    private void innerSetRecordType(ClanRecordType recordType) {
        this.recordType = recordType;
    }

    /**
     * get params
     *
     * @return params value
     */
    public DisplayDataProp getParams() {
        if (this.params == null) {
            this.params = new DisplayDataProp(this, FIELD_INDEX_PARAMS);
        }
        return this.params;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanRecordPB.Builder getCopyCsBuilder() {
        final ClanRecordPB.Builder builder = ClanRecordPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRecordType() != ClanRecordType.forNumber(0)) {
            builder.setRecordType(this.getRecordType());
            fieldCnt++;
        }  else if (builder.hasRecordType()) {
            // 清理RecordType
            builder.clearRecordType();
            fieldCnt++;
        }
        if (this.params != null) {
            StructPB.DisplayDataPB.Builder tmpBuilder = StructPB.DisplayDataPB.newBuilder();
            final int tmpFieldCnt = this.params.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setParams(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearParams();
            }
        }  else if (builder.hasParams()) {
            // 清理Params
            builder.clearParams();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECORDTYPE)) {
            builder.setRecordType(this.getRecordType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PARAMS) && this.params != null) {
            final boolean needClear = !builder.hasParams();
            final int tmpFieldCnt = this.params.copyChangeToCs(builder.getParamsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearParams();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECORDTYPE)) {
            builder.setRecordType(this.getRecordType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PARAMS) && this.params != null) {
            final boolean needClear = !builder.hasParams();
            final int tmpFieldCnt = this.params.copyChangeToAndClearDeleteKeysCs(builder.getParamsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearParams();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanRecordPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRecordType()) {
            this.innerSetRecordType(proto.getRecordType());
        } else {
            this.innerSetRecordType(ClanRecordType.forNumber(0));
        }
        if (proto.hasParams()) {
            this.getParams().mergeFromCs(proto.getParams());
        } else {
            if (this.params != null) {
                this.params.mergeFromCs(proto.getParams());
            }
        }
        this.markAll();
        return ClanRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanRecordPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRecordType()) {
            this.setRecordType(proto.getRecordType());
            fieldCnt++;
        }
        if (proto.hasParams()) {
            this.getParams().mergeChangeFromCs(proto.getParams());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanRecord.Builder getCopyDbBuilder() {
        final ClanRecord.Builder builder = ClanRecord.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRecordType() != ClanRecordType.forNumber(0)) {
            builder.setRecordType(this.getRecordType());
            fieldCnt++;
        }  else if (builder.hasRecordType()) {
            // 清理RecordType
            builder.clearRecordType();
            fieldCnt++;
        }
        if (this.params != null) {
            Struct.DisplayData.Builder tmpBuilder = Struct.DisplayData.newBuilder();
            final int tmpFieldCnt = this.params.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setParams(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearParams();
            }
        }  else if (builder.hasParams()) {
            // 清理Params
            builder.clearParams();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECORDTYPE)) {
            builder.setRecordType(this.getRecordType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PARAMS) && this.params != null) {
            final boolean needClear = !builder.hasParams();
            final int tmpFieldCnt = this.params.copyChangeToDb(builder.getParamsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearParams();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanRecord proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRecordType()) {
            this.innerSetRecordType(proto.getRecordType());
        } else {
            this.innerSetRecordType(ClanRecordType.forNumber(0));
        }
        if (proto.hasParams()) {
            this.getParams().mergeFromDb(proto.getParams());
        } else {
            if (this.params != null) {
                this.params.mergeFromDb(proto.getParams());
            }
        }
        this.markAll();
        return ClanRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanRecord proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRecordType()) {
            this.setRecordType(proto.getRecordType());
            fieldCnt++;
        }
        if (proto.hasParams()) {
            this.getParams().mergeChangeFromDb(proto.getParams());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanRecord.Builder getCopySsBuilder() {
        final ClanRecord.Builder builder = ClanRecord.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRecordType() != ClanRecordType.forNumber(0)) {
            builder.setRecordType(this.getRecordType());
            fieldCnt++;
        }  else if (builder.hasRecordType()) {
            // 清理RecordType
            builder.clearRecordType();
            fieldCnt++;
        }
        if (this.params != null) {
            Struct.DisplayData.Builder tmpBuilder = Struct.DisplayData.newBuilder();
            final int tmpFieldCnt = this.params.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setParams(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearParams();
            }
        }  else if (builder.hasParams()) {
            // 清理Params
            builder.clearParams();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECORDTYPE)) {
            builder.setRecordType(this.getRecordType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PARAMS) && this.params != null) {
            final boolean needClear = !builder.hasParams();
            final int tmpFieldCnt = this.params.copyChangeToSs(builder.getParamsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearParams();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanRecord proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRecordType()) {
            this.innerSetRecordType(proto.getRecordType());
        } else {
            this.innerSetRecordType(ClanRecordType.forNumber(0));
        }
        if (proto.hasParams()) {
            this.getParams().mergeFromSs(proto.getParams());
        } else {
            if (this.params != null) {
                this.params.mergeFromSs(proto.getParams());
            }
        }
        this.markAll();
        return ClanRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanRecord proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRecordType()) {
            this.setRecordType(proto.getRecordType());
            fieldCnt++;
        }
        if (proto.hasParams()) {
            this.getParams().mergeChangeFromSs(proto.getParams());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanRecord.Builder builder = ClanRecord.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_PARAMS) && this.params != null) {
            this.params.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.params != null) {
            this.params.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanRecordProp)) {
            return false;
        }
        final ClanRecordProp otherNode = (ClanRecordProp) node;
        if (this.recordType != otherNode.recordType) {
            return false;
        }
        if (!this.getParams().compareDataTo(otherNode.getParams())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerClanStoreModel;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.PlayerClanStoreModelPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerClanStoreModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_REFRESHTIME = 0;
    public static final int FIELD_INDEX_CLANSHOPBUYINFO = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private long refreshTime = Constant.DEFAULT_LONG_VALUE;
    private Int32ShopBuyInfoMapProp clanShopBuyInfo = null;

    public PlayerClanStoreModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerClanStoreModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get refreshTime
     *
     * @return refreshTime value
     */
    public long getRefreshTime() {
        return this.refreshTime;
    }

    /**
     * set refreshTime && set marked
     *
     * @param refreshTime new value
     * @return current object
     */
    public PlayerClanStoreModelProp setRefreshTime(long refreshTime) {
        if (this.refreshTime != refreshTime) {
            this.mark(FIELD_INDEX_REFRESHTIME);
            this.refreshTime = refreshTime;
        }
        return this;
    }

    /**
     * inner set refreshTime
     *
     * @param refreshTime new value
     */
    private void innerSetRefreshTime(long refreshTime) {
        this.refreshTime = refreshTime;
    }

    /**
     * get clanShopBuyInfo
     *
     * @return clanShopBuyInfo value
     */
    public Int32ShopBuyInfoMapProp getClanShopBuyInfo() {
        if (this.clanShopBuyInfo == null) {
            this.clanShopBuyInfo = new Int32ShopBuyInfoMapProp(this, FIELD_INDEX_CLANSHOPBUYINFO);
        }
        return this.clanShopBuyInfo;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putClanShopBuyInfoV(ShopBuyInfoProp v) {
        this.getClanShopBuyInfo().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ShopBuyInfoProp addEmptyClanShopBuyInfo(Integer k) {
        return this.getClanShopBuyInfo().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getClanShopBuyInfoSize() {
        if (this.clanShopBuyInfo == null) {
            return 0;
        }
        return this.clanShopBuyInfo.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isClanShopBuyInfoEmpty() {
        if (this.clanShopBuyInfo == null) {
            return true;
        }
        return this.clanShopBuyInfo.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ShopBuyInfoProp getClanShopBuyInfoV(Integer k) {
        if (this.clanShopBuyInfo == null || !this.clanShopBuyInfo.containsKey(k)) {
            return null;
        }
        return this.clanShopBuyInfo.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearClanShopBuyInfo() {
        if (this.clanShopBuyInfo != null) {
            this.clanShopBuyInfo.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeClanShopBuyInfoV(Integer k) {
        if (this.clanShopBuyInfo != null) {
            this.clanShopBuyInfo.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerClanStoreModelPB.Builder getCopyCsBuilder() {
        final PlayerClanStoreModelPB.Builder builder = PlayerClanStoreModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerClanStoreModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRefreshTime() != 0L) {
            builder.setRefreshTime(this.getRefreshTime());
            fieldCnt++;
        }  else if (builder.hasRefreshTime()) {
            // 清理RefreshTime
            builder.clearRefreshTime();
            fieldCnt++;
        }
        if (this.clanShopBuyInfo != null) {
            StructPB.Int32ShopBuyInfoMapPB.Builder tmpBuilder = StructPB.Int32ShopBuyInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.clanShopBuyInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanShopBuyInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanShopBuyInfo();
            }
        }  else if (builder.hasClanShopBuyInfo()) {
            // 清理ClanShopBuyInfo
            builder.clearClanShopBuyInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerClanStoreModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REFRESHTIME)) {
            builder.setRefreshTime(this.getRefreshTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSHOPBUYINFO) && this.clanShopBuyInfo != null) {
            final boolean needClear = !builder.hasClanShopBuyInfo();
            final int tmpFieldCnt = this.clanShopBuyInfo.copyChangeToCs(builder.getClanShopBuyInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanShopBuyInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerClanStoreModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REFRESHTIME)) {
            builder.setRefreshTime(this.getRefreshTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSHOPBUYINFO) && this.clanShopBuyInfo != null) {
            final boolean needClear = !builder.hasClanShopBuyInfo();
            final int tmpFieldCnt = this.clanShopBuyInfo.copyChangeToAndClearDeleteKeysCs(builder.getClanShopBuyInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanShopBuyInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerClanStoreModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRefreshTime()) {
            this.innerSetRefreshTime(proto.getRefreshTime());
        } else {
            this.innerSetRefreshTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanShopBuyInfo()) {
            this.getClanShopBuyInfo().mergeFromCs(proto.getClanShopBuyInfo());
        } else {
            if (this.clanShopBuyInfo != null) {
                this.clanShopBuyInfo.mergeFromCs(proto.getClanShopBuyInfo());
            }
        }
        this.markAll();
        return PlayerClanStoreModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerClanStoreModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRefreshTime()) {
            this.setRefreshTime(proto.getRefreshTime());
            fieldCnt++;
        }
        if (proto.hasClanShopBuyInfo()) {
            this.getClanShopBuyInfo().mergeChangeFromCs(proto.getClanShopBuyInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerClanStoreModel.Builder getCopyDbBuilder() {
        final PlayerClanStoreModel.Builder builder = PlayerClanStoreModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerClanStoreModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRefreshTime() != 0L) {
            builder.setRefreshTime(this.getRefreshTime());
            fieldCnt++;
        }  else if (builder.hasRefreshTime()) {
            // 清理RefreshTime
            builder.clearRefreshTime();
            fieldCnt++;
        }
        if (this.clanShopBuyInfo != null) {
            Struct.Int32ShopBuyInfoMap.Builder tmpBuilder = Struct.Int32ShopBuyInfoMap.newBuilder();
            final int tmpFieldCnt = this.clanShopBuyInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanShopBuyInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanShopBuyInfo();
            }
        }  else if (builder.hasClanShopBuyInfo()) {
            // 清理ClanShopBuyInfo
            builder.clearClanShopBuyInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerClanStoreModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REFRESHTIME)) {
            builder.setRefreshTime(this.getRefreshTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSHOPBUYINFO) && this.clanShopBuyInfo != null) {
            final boolean needClear = !builder.hasClanShopBuyInfo();
            final int tmpFieldCnt = this.clanShopBuyInfo.copyChangeToDb(builder.getClanShopBuyInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanShopBuyInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerClanStoreModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRefreshTime()) {
            this.innerSetRefreshTime(proto.getRefreshTime());
        } else {
            this.innerSetRefreshTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanShopBuyInfo()) {
            this.getClanShopBuyInfo().mergeFromDb(proto.getClanShopBuyInfo());
        } else {
            if (this.clanShopBuyInfo != null) {
                this.clanShopBuyInfo.mergeFromDb(proto.getClanShopBuyInfo());
            }
        }
        this.markAll();
        return PlayerClanStoreModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerClanStoreModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRefreshTime()) {
            this.setRefreshTime(proto.getRefreshTime());
            fieldCnt++;
        }
        if (proto.hasClanShopBuyInfo()) {
            this.getClanShopBuyInfo().mergeChangeFromDb(proto.getClanShopBuyInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerClanStoreModel.Builder getCopySsBuilder() {
        final PlayerClanStoreModel.Builder builder = PlayerClanStoreModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerClanStoreModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRefreshTime() != 0L) {
            builder.setRefreshTime(this.getRefreshTime());
            fieldCnt++;
        }  else if (builder.hasRefreshTime()) {
            // 清理RefreshTime
            builder.clearRefreshTime();
            fieldCnt++;
        }
        if (this.clanShopBuyInfo != null) {
            Struct.Int32ShopBuyInfoMap.Builder tmpBuilder = Struct.Int32ShopBuyInfoMap.newBuilder();
            final int tmpFieldCnt = this.clanShopBuyInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanShopBuyInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanShopBuyInfo();
            }
        }  else if (builder.hasClanShopBuyInfo()) {
            // 清理ClanShopBuyInfo
            builder.clearClanShopBuyInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerClanStoreModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REFRESHTIME)) {
            builder.setRefreshTime(this.getRefreshTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSHOPBUYINFO) && this.clanShopBuyInfo != null) {
            final boolean needClear = !builder.hasClanShopBuyInfo();
            final int tmpFieldCnt = this.clanShopBuyInfo.copyChangeToSs(builder.getClanShopBuyInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanShopBuyInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerClanStoreModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRefreshTime()) {
            this.innerSetRefreshTime(proto.getRefreshTime());
        } else {
            this.innerSetRefreshTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanShopBuyInfo()) {
            this.getClanShopBuyInfo().mergeFromSs(proto.getClanShopBuyInfo());
        } else {
            if (this.clanShopBuyInfo != null) {
                this.clanShopBuyInfo.mergeFromSs(proto.getClanShopBuyInfo());
            }
        }
        this.markAll();
        return PlayerClanStoreModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerClanStoreModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRefreshTime()) {
            this.setRefreshTime(proto.getRefreshTime());
            fieldCnt++;
        }
        if (proto.hasClanShopBuyInfo()) {
            this.getClanShopBuyInfo().mergeChangeFromSs(proto.getClanShopBuyInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerClanStoreModel.Builder builder = PlayerClanStoreModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CLANSHOPBUYINFO) && this.clanShopBuyInfo != null) {
            this.clanShopBuyInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.clanShopBuyInfo != null) {
            this.clanShopBuyInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerClanStoreModelProp)) {
            return false;
        }
        final PlayerClanStoreModelProp otherNode = (PlayerClanStoreModelProp) node;
        if (this.refreshTime != otherNode.refreshTime) {
            return false;
        }
        if (!this.getClanShopBuyInfo().compareDataTo(otherNode.getClanShopBuyInfo())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
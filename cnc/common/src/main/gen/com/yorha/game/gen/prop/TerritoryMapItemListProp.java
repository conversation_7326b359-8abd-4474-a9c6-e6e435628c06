package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructPB.TerritoryMapItemListPB;
import com.yorha.proto.Struct.TerritoryMapItemList;
import com.yorha.proto.StructPB.TerritoryMapItemPB;
import com.yorha.proto.Struct.TerritoryMapItem;

/**
 * <AUTHOR> auto gen
 */
public class TerritoryMapItemListProp extends AbstractListNode<TerritoryMapItemProp> {
    /**
     * Create a TerritoryMapItemListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public TerritoryMapItemListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to TerritoryMapItemListProp
     *
     * @return new object
     */
    @Override
    public TerritoryMapItemProp addEmptyValue() {
        final TerritoryMapItemProp newProp = new TerritoryMapItemProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TerritoryMapItemListPB.Builder getCopyCsBuilder() {
        final TerritoryMapItemListPB.Builder builder = TerritoryMapItemListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(TerritoryMapItemListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return TerritoryMapItemListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final TerritoryMapItemProp v : this) {
            final TerritoryMapItemPB.Builder itemBuilder = TerritoryMapItemPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return TerritoryMapItemListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(TerritoryMapItemListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return TerritoryMapItemListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(TerritoryMapItemListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (TerritoryMapItemPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return TerritoryMapItemListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(TerritoryMapItemListPB proto) {
        return mergeFromCs(proto);
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TerritoryMapItemList.Builder getCopySsBuilder() {
        final TerritoryMapItemList.Builder builder = TerritoryMapItemList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(TerritoryMapItemList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return TerritoryMapItemListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final TerritoryMapItemProp v : this) {
            final TerritoryMapItem.Builder itemBuilder = TerritoryMapItem.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return TerritoryMapItemListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(TerritoryMapItemList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return TerritoryMapItemListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(TerritoryMapItemList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (TerritoryMapItem v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return TerritoryMapItemListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(TerritoryMapItemList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        TerritoryMapItemList.Builder builder = TerritoryMapItemList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.DialogParams;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.DialogParamsPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class DialogParamsProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ERRORCODE = 0;
    public static final int FIELD_INDEX_POS = 1;
    public static final int FIELD_INDEX_ENTITYID = 2;
    public static final int FIELD_INDEX_TEMPLATEID = 3;
    public static final int FIELD_INDEX_DATA = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private int errorCode = Constant.DEFAULT_INT_VALUE;
    private PointProp pos = null;
    private long entityId = Constant.DEFAULT_LONG_VALUE;
    private int templateId = Constant.DEFAULT_INT_VALUE;
    private DisplayDataProp data = null;

    public DialogParamsProp() {
        super(null, 0, FIELD_COUNT);
    }

    public DialogParamsProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get errorCode
     *
     * @return errorCode value
     */
    public int getErrorCode() {
        return this.errorCode;
    }

    /**
     * set errorCode && set marked
     *
     * @param errorCode new value
     * @return current object
     */
    public DialogParamsProp setErrorCode(int errorCode) {
        if (this.errorCode != errorCode) {
            this.mark(FIELD_INDEX_ERRORCODE);
            this.errorCode = errorCode;
        }
        return this;
    }

    /**
     * inner set errorCode
     *
     * @param errorCode new value
     */
    private void innerSetErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * get pos
     *
     * @return pos value
     */
    public PointProp getPos() {
        if (this.pos == null) {
            this.pos = new PointProp(this, FIELD_INDEX_POS);
        }
        return this.pos;
    }

    /**
     * get entityId
     *
     * @return entityId value
     */
    public long getEntityId() {
        return this.entityId;
    }

    /**
     * set entityId && set marked
     *
     * @param entityId new value
     * @return current object
     */
    public DialogParamsProp setEntityId(long entityId) {
        if (this.entityId != entityId) {
            this.mark(FIELD_INDEX_ENTITYID);
            this.entityId = entityId;
        }
        return this;
    }

    /**
     * inner set entityId
     *
     * @param entityId new value
     */
    private void innerSetEntityId(long entityId) {
        this.entityId = entityId;
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public DialogParamsProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }

    /**
     * get data
     *
     * @return data value
     */
    public DisplayDataProp getData() {
        if (this.data == null) {
            this.data = new DisplayDataProp(this, FIELD_INDEX_DATA);
        }
        return this.data;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DialogParamsPB.Builder getCopyCsBuilder() {
        final DialogParamsPB.Builder builder = DialogParamsPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(DialogParamsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getErrorCode() != 0) {
            builder.setErrorCode(this.getErrorCode());
            fieldCnt++;
        }  else if (builder.hasErrorCode()) {
            // 清理ErrorCode
            builder.clearErrorCode();
            fieldCnt++;
        }
        if (this.pos != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.pos.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPos();
            }
        }  else if (builder.hasPos()) {
            // 清理Pos
            builder.clearPos();
            fieldCnt++;
        }
        if (this.getEntityId() != 0L) {
            builder.setEntityId(this.getEntityId());
            fieldCnt++;
        }  else if (builder.hasEntityId()) {
            // 清理EntityId
            builder.clearEntityId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.data != null) {
            StructPB.DisplayDataPB.Builder tmpBuilder = StructPB.DisplayDataPB.newBuilder();
            final int tmpFieldCnt = this.data.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearData();
            }
        }  else if (builder.hasData()) {
            // 清理Data
            builder.clearData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(DialogParamsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ERRORCODE)) {
            builder.setErrorCode(this.getErrorCode());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POS) && this.pos != null) {
            final boolean needClear = !builder.hasPos();
            final int tmpFieldCnt = this.pos.copyChangeToCs(builder.getPosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPos();
            }
        }
        if (this.hasMark(FIELD_INDEX_ENTITYID)) {
            builder.setEntityId(this.getEntityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DATA) && this.data != null) {
            final boolean needClear = !builder.hasData();
            final int tmpFieldCnt = this.data.copyChangeToCs(builder.getDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearData();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(DialogParamsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ERRORCODE)) {
            builder.setErrorCode(this.getErrorCode());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POS) && this.pos != null) {
            final boolean needClear = !builder.hasPos();
            final int tmpFieldCnt = this.pos.copyChangeToAndClearDeleteKeysCs(builder.getPosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPos();
            }
        }
        if (this.hasMark(FIELD_INDEX_ENTITYID)) {
            builder.setEntityId(this.getEntityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DATA) && this.data != null) {
            final boolean needClear = !builder.hasData();
            final int tmpFieldCnt = this.data.copyChangeToAndClearDeleteKeysCs(builder.getDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(DialogParamsPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasErrorCode()) {
            this.innerSetErrorCode(proto.getErrorCode());
        } else {
            this.innerSetErrorCode(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPos()) {
            this.getPos().mergeFromCs(proto.getPos());
        } else {
            if (this.pos != null) {
                this.pos.mergeFromCs(proto.getPos());
            }
        }
        if (proto.hasEntityId()) {
            this.innerSetEntityId(proto.getEntityId());
        } else {
            this.innerSetEntityId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasData()) {
            this.getData().mergeFromCs(proto.getData());
        } else {
            if (this.data != null) {
                this.data.mergeFromCs(proto.getData());
            }
        }
        this.markAll();
        return DialogParamsProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(DialogParamsPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasErrorCode()) {
            this.setErrorCode(proto.getErrorCode());
            fieldCnt++;
        }
        if (proto.hasPos()) {
            this.getPos().mergeChangeFromCs(proto.getPos());
            fieldCnt++;
        }
        if (proto.hasEntityId()) {
            this.setEntityId(proto.getEntityId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasData()) {
            this.getData().mergeChangeFromCs(proto.getData());
            fieldCnt++;
        }
        return fieldCnt;
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DialogParams.Builder getCopySsBuilder() {
        final DialogParams.Builder builder = DialogParams.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(DialogParams.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getErrorCode() != 0) {
            builder.setErrorCode(this.getErrorCode());
            fieldCnt++;
        }  else if (builder.hasErrorCode()) {
            // 清理ErrorCode
            builder.clearErrorCode();
            fieldCnt++;
        }
        if (this.pos != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.pos.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPos();
            }
        }  else if (builder.hasPos()) {
            // 清理Pos
            builder.clearPos();
            fieldCnt++;
        }
        if (this.getEntityId() != 0L) {
            builder.setEntityId(this.getEntityId());
            fieldCnt++;
        }  else if (builder.hasEntityId()) {
            // 清理EntityId
            builder.clearEntityId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.data != null) {
            Struct.DisplayData.Builder tmpBuilder = Struct.DisplayData.newBuilder();
            final int tmpFieldCnt = this.data.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearData();
            }
        }  else if (builder.hasData()) {
            // 清理Data
            builder.clearData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(DialogParams.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ERRORCODE)) {
            builder.setErrorCode(this.getErrorCode());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POS) && this.pos != null) {
            final boolean needClear = !builder.hasPos();
            final int tmpFieldCnt = this.pos.copyChangeToSs(builder.getPosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPos();
            }
        }
        if (this.hasMark(FIELD_INDEX_ENTITYID)) {
            builder.setEntityId(this.getEntityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DATA) && this.data != null) {
            final boolean needClear = !builder.hasData();
            final int tmpFieldCnt = this.data.copyChangeToSs(builder.getDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(DialogParams proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasErrorCode()) {
            this.innerSetErrorCode(proto.getErrorCode());
        } else {
            this.innerSetErrorCode(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPos()) {
            this.getPos().mergeFromSs(proto.getPos());
        } else {
            if (this.pos != null) {
                this.pos.mergeFromSs(proto.getPos());
            }
        }
        if (proto.hasEntityId()) {
            this.innerSetEntityId(proto.getEntityId());
        } else {
            this.innerSetEntityId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasData()) {
            this.getData().mergeFromSs(proto.getData());
        } else {
            if (this.data != null) {
                this.data.mergeFromSs(proto.getData());
            }
        }
        this.markAll();
        return DialogParamsProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(DialogParams proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasErrorCode()) {
            this.setErrorCode(proto.getErrorCode());
            fieldCnt++;
        }
        if (proto.hasPos()) {
            this.getPos().mergeChangeFromSs(proto.getPos());
            fieldCnt++;
        }
        if (proto.hasEntityId()) {
            this.setEntityId(proto.getEntityId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasData()) {
            this.getData().mergeChangeFromSs(proto.getData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        DialogParams.Builder builder = DialogParams.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_POS) && this.pos != null) {
            this.pos.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DATA) && this.data != null) {
            this.data.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.pos != null) {
            this.pos.markAll();
        }
        if (this.data != null) {
            this.data.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof DialogParamsProp)) {
            return false;
        }
        final DialogParamsProp otherNode = (DialogParamsProp) node;
        if (this.errorCode != otherNode.errorCode) {
            return false;
        }
        if (!this.getPos().compareDataTo(otherNode.getPos())) {
            return false;
        }
        if (this.entityId != otherNode.entityId) {
            return false;
        }
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        if (!this.getData().compareDataTo(otherNode.getData())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
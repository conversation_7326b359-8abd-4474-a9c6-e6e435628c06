package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityScoreRewardUnit;
import com.yorha.proto.Basic;
import com.yorha.proto.StructPB.ActivityScoreRewardUnitPB;
import com.yorha.proto.BasicPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityScoreRewardUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_SCORE = 0;
    public static final int FIELD_INDEX_TAKENBOXIDS = 1;
    public static final int FIELD_INDEX_CITYLEVEL = 2;
    public static final int FIELD_INDEX_SERVEROPENDAY = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int score = Constant.DEFAULT_INT_VALUE;
    private Int32ListProp takenBoxIds = null;
    private int cityLevel = Constant.DEFAULT_INT_VALUE;
    private int serverOpenDay = Constant.DEFAULT_INT_VALUE;

    public ActivityScoreRewardUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityScoreRewardUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get score
     *
     * @return score value
     */
    public int getScore() {
        return this.score;
    }

    /**
     * set score && set marked
     *
     * @param score new value
     * @return current object
     */
    public ActivityScoreRewardUnitProp setScore(int score) {
        if (this.score != score) {
            this.mark(FIELD_INDEX_SCORE);
            this.score = score;
        }
        return this;
    }

    /**
     * inner set score
     *
     * @param score new value
     */
    private void innerSetScore(int score) {
        this.score = score;
    }

    /**
     * get takenBoxIds
     *
     * @return takenBoxIds value
     */
    public Int32ListProp getTakenBoxIds() {
        if (this.takenBoxIds == null) {
            this.takenBoxIds = new Int32ListProp(this, FIELD_INDEX_TAKENBOXIDS);
        }
        return this.takenBoxIds;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addTakenBoxIds(Integer v) {
        this.getTakenBoxIds().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getTakenBoxIdsIndex(int index) {
        return this.getTakenBoxIds().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeTakenBoxIds(Integer v) {
        if (this.takenBoxIds == null) {
            return null;
        }
        if(this.takenBoxIds.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getTakenBoxIdsSize() {
        if (this.takenBoxIds == null) {
            return 0;
        }
        return this.takenBoxIds.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isTakenBoxIdsEmpty() {
        if (this.takenBoxIds == null) {
            return true;
        }
        return this.getTakenBoxIds().isEmpty();
    }

    /**
     * clear list
     */
    public void clearTakenBoxIds() {
        this.getTakenBoxIds().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeTakenBoxIdsIndex(int index) {
        return this.getTakenBoxIds().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setTakenBoxIdsIndex(int index, Integer v) {
        return this.getTakenBoxIds().set(index, v);
    }
    /**
     * get cityLevel
     *
     * @return cityLevel value
     */
    public int getCityLevel() {
        return this.cityLevel;
    }

    /**
     * set cityLevel && set marked
     *
     * @param cityLevel new value
     * @return current object
     */
    public ActivityScoreRewardUnitProp setCityLevel(int cityLevel) {
        if (this.cityLevel != cityLevel) {
            this.mark(FIELD_INDEX_CITYLEVEL);
            this.cityLevel = cityLevel;
        }
        return this;
    }

    /**
     * inner set cityLevel
     *
     * @param cityLevel new value
     */
    private void innerSetCityLevel(int cityLevel) {
        this.cityLevel = cityLevel;
    }

    /**
     * get serverOpenDay
     *
     * @return serverOpenDay value
     */
    public int getServerOpenDay() {
        return this.serverOpenDay;
    }

    /**
     * set serverOpenDay && set marked
     *
     * @param serverOpenDay new value
     * @return current object
     */
    public ActivityScoreRewardUnitProp setServerOpenDay(int serverOpenDay) {
        if (this.serverOpenDay != serverOpenDay) {
            this.mark(FIELD_INDEX_SERVEROPENDAY);
            this.serverOpenDay = serverOpenDay;
        }
        return this;
    }

    /**
     * inner set serverOpenDay
     *
     * @param serverOpenDay new value
     */
    private void innerSetServerOpenDay(int serverOpenDay) {
        this.serverOpenDay = serverOpenDay;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityScoreRewardUnitPB.Builder getCopyCsBuilder() {
        final ActivityScoreRewardUnitPB.Builder builder = ActivityScoreRewardUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityScoreRewardUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getScore() != 0) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (this.takenBoxIds != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.takenBoxIds.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTakenBoxIds(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTakenBoxIds();
            }
        }  else if (builder.hasTakenBoxIds()) {
            // 清理TakenBoxIds
            builder.clearTakenBoxIds();
            fieldCnt++;
        }
        if (this.getCityLevel() != 0) {
            builder.setCityLevel(this.getCityLevel());
            fieldCnt++;
        }  else if (builder.hasCityLevel()) {
            // 清理CityLevel
            builder.clearCityLevel();
            fieldCnt++;
        }
        if (this.getServerOpenDay() != 0) {
            builder.setServerOpenDay(this.getServerOpenDay());
            fieldCnt++;
        }  else if (builder.hasServerOpenDay()) {
            // 清理ServerOpenDay
            builder.clearServerOpenDay();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityScoreRewardUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TAKENBOXIDS) && this.takenBoxIds != null) {
            final boolean needClear = !builder.hasTakenBoxIds();
            final int tmpFieldCnt = this.takenBoxIds.copyChangeToCs(builder.getTakenBoxIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTakenBoxIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_CITYLEVEL)) {
            builder.setCityLevel(this.getCityLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SERVEROPENDAY)) {
            builder.setServerOpenDay(this.getServerOpenDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityScoreRewardUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TAKENBOXIDS) && this.takenBoxIds != null) {
            final boolean needClear = !builder.hasTakenBoxIds();
            final int tmpFieldCnt = this.takenBoxIds.copyChangeToCs(builder.getTakenBoxIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTakenBoxIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_CITYLEVEL)) {
            builder.setCityLevel(this.getCityLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SERVEROPENDAY)) {
            builder.setServerOpenDay(this.getServerOpenDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityScoreRewardUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTakenBoxIds()) {
            this.getTakenBoxIds().mergeFromCs(proto.getTakenBoxIds());
        } else {
            if (this.takenBoxIds != null) {
                this.takenBoxIds.mergeFromCs(proto.getTakenBoxIds());
            }
        }
        if (proto.hasCityLevel()) {
            this.innerSetCityLevel(proto.getCityLevel());
        } else {
            this.innerSetCityLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasServerOpenDay()) {
            this.innerSetServerOpenDay(proto.getServerOpenDay());
        } else {
            this.innerSetServerOpenDay(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivityScoreRewardUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityScoreRewardUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasTakenBoxIds()) {
            this.getTakenBoxIds().mergeChangeFromCs(proto.getTakenBoxIds());
            fieldCnt++;
        }
        if (proto.hasCityLevel()) {
            this.setCityLevel(proto.getCityLevel());
            fieldCnt++;
        }
        if (proto.hasServerOpenDay()) {
            this.setServerOpenDay(proto.getServerOpenDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityScoreRewardUnit.Builder getCopyDbBuilder() {
        final ActivityScoreRewardUnit.Builder builder = ActivityScoreRewardUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityScoreRewardUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getScore() != 0) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (this.takenBoxIds != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.takenBoxIds.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTakenBoxIds(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTakenBoxIds();
            }
        }  else if (builder.hasTakenBoxIds()) {
            // 清理TakenBoxIds
            builder.clearTakenBoxIds();
            fieldCnt++;
        }
        if (this.getCityLevel() != 0) {
            builder.setCityLevel(this.getCityLevel());
            fieldCnt++;
        }  else if (builder.hasCityLevel()) {
            // 清理CityLevel
            builder.clearCityLevel();
            fieldCnt++;
        }
        if (this.getServerOpenDay() != 0) {
            builder.setServerOpenDay(this.getServerOpenDay());
            fieldCnt++;
        }  else if (builder.hasServerOpenDay()) {
            // 清理ServerOpenDay
            builder.clearServerOpenDay();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityScoreRewardUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TAKENBOXIDS) && this.takenBoxIds != null) {
            final boolean needClear = !builder.hasTakenBoxIds();
            final int tmpFieldCnt = this.takenBoxIds.copyChangeToDb(builder.getTakenBoxIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTakenBoxIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_CITYLEVEL)) {
            builder.setCityLevel(this.getCityLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SERVEROPENDAY)) {
            builder.setServerOpenDay(this.getServerOpenDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityScoreRewardUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTakenBoxIds()) {
            this.getTakenBoxIds().mergeFromDb(proto.getTakenBoxIds());
        } else {
            if (this.takenBoxIds != null) {
                this.takenBoxIds.mergeFromDb(proto.getTakenBoxIds());
            }
        }
        if (proto.hasCityLevel()) {
            this.innerSetCityLevel(proto.getCityLevel());
        } else {
            this.innerSetCityLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasServerOpenDay()) {
            this.innerSetServerOpenDay(proto.getServerOpenDay());
        } else {
            this.innerSetServerOpenDay(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivityScoreRewardUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityScoreRewardUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasTakenBoxIds()) {
            this.getTakenBoxIds().mergeChangeFromDb(proto.getTakenBoxIds());
            fieldCnt++;
        }
        if (proto.hasCityLevel()) {
            this.setCityLevel(proto.getCityLevel());
            fieldCnt++;
        }
        if (proto.hasServerOpenDay()) {
            this.setServerOpenDay(proto.getServerOpenDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityScoreRewardUnit.Builder getCopySsBuilder() {
        final ActivityScoreRewardUnit.Builder builder = ActivityScoreRewardUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityScoreRewardUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getScore() != 0) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (this.takenBoxIds != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.takenBoxIds.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTakenBoxIds(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTakenBoxIds();
            }
        }  else if (builder.hasTakenBoxIds()) {
            // 清理TakenBoxIds
            builder.clearTakenBoxIds();
            fieldCnt++;
        }
        if (this.getCityLevel() != 0) {
            builder.setCityLevel(this.getCityLevel());
            fieldCnt++;
        }  else if (builder.hasCityLevel()) {
            // 清理CityLevel
            builder.clearCityLevel();
            fieldCnt++;
        }
        if (this.getServerOpenDay() != 0) {
            builder.setServerOpenDay(this.getServerOpenDay());
            fieldCnt++;
        }  else if (builder.hasServerOpenDay()) {
            // 清理ServerOpenDay
            builder.clearServerOpenDay();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityScoreRewardUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TAKENBOXIDS) && this.takenBoxIds != null) {
            final boolean needClear = !builder.hasTakenBoxIds();
            final int tmpFieldCnt = this.takenBoxIds.copyChangeToSs(builder.getTakenBoxIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTakenBoxIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_CITYLEVEL)) {
            builder.setCityLevel(this.getCityLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SERVEROPENDAY)) {
            builder.setServerOpenDay(this.getServerOpenDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityScoreRewardUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTakenBoxIds()) {
            this.getTakenBoxIds().mergeFromSs(proto.getTakenBoxIds());
        } else {
            if (this.takenBoxIds != null) {
                this.takenBoxIds.mergeFromSs(proto.getTakenBoxIds());
            }
        }
        if (proto.hasCityLevel()) {
            this.innerSetCityLevel(proto.getCityLevel());
        } else {
            this.innerSetCityLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasServerOpenDay()) {
            this.innerSetServerOpenDay(proto.getServerOpenDay());
        } else {
            this.innerSetServerOpenDay(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivityScoreRewardUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityScoreRewardUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasTakenBoxIds()) {
            this.getTakenBoxIds().mergeChangeFromSs(proto.getTakenBoxIds());
            fieldCnt++;
        }
        if (proto.hasCityLevel()) {
            this.setCityLevel(proto.getCityLevel());
            fieldCnt++;
        }
        if (proto.hasServerOpenDay()) {
            this.setServerOpenDay(proto.getServerOpenDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityScoreRewardUnit.Builder builder = ActivityScoreRewardUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TAKENBOXIDS) && this.takenBoxIds != null) {
            this.takenBoxIds.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.takenBoxIds != null) {
            this.takenBoxIds.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityScoreRewardUnitProp)) {
            return false;
        }
        final ActivityScoreRewardUnitProp otherNode = (ActivityScoreRewardUnitProp) node;
        if (this.score != otherNode.score) {
            return false;
        }
        if (!this.getTakenBoxIds().compareDataTo(otherNode.getTakenBoxIds())) {
            return false;
        }
        if (this.cityLevel != otherNode.cityLevel) {
            return false;
        }
        if (this.serverOpenDay != otherNode.serverOpenDay) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
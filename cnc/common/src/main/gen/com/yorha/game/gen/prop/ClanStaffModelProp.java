package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Clan.ClanStaffModel;
import com.yorha.proto.Clan;
import com.yorha.proto.ClanPB.ClanStaffModelPB;
import com.yorha.proto.ClanPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanStaffModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_STAFFINFOMAP = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32ClanStaffInfoMapProp staffInfoMap = null;

    public ClanStaffModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanStaffModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get staffInfoMap
     *
     * @return staffInfoMap value
     */
    public Int32ClanStaffInfoMapProp getStaffInfoMap() {
        if (this.staffInfoMap == null) {
            this.staffInfoMap = new Int32ClanStaffInfoMapProp(this, FIELD_INDEX_STAFFINFOMAP);
        }
        return this.staffInfoMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putStaffInfoMapV(ClanStaffInfoProp v) {
        this.getStaffInfoMap().put(v.getStaffId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ClanStaffInfoProp addEmptyStaffInfoMap(Integer k) {
        return this.getStaffInfoMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getStaffInfoMapSize() {
        if (this.staffInfoMap == null) {
            return 0;
        }
        return this.staffInfoMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isStaffInfoMapEmpty() {
        if (this.staffInfoMap == null) {
            return true;
        }
        return this.staffInfoMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ClanStaffInfoProp getStaffInfoMapV(Integer k) {
        if (this.staffInfoMap == null || !this.staffInfoMap.containsKey(k)) {
            return null;
        }
        return this.staffInfoMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearStaffInfoMap() {
        if (this.staffInfoMap != null) {
            this.staffInfoMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeStaffInfoMapV(Integer k) {
        if (this.staffInfoMap != null) {
            this.staffInfoMap.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanStaffModelPB.Builder getCopyCsBuilder() {
        final ClanStaffModelPB.Builder builder = ClanStaffModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanStaffModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.staffInfoMap != null) {
            ClanPB.Int32ClanStaffInfoMapPB.Builder tmpBuilder = ClanPB.Int32ClanStaffInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.staffInfoMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStaffInfoMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStaffInfoMap();
            }
        }  else if (builder.hasStaffInfoMap()) {
            // 清理StaffInfoMap
            builder.clearStaffInfoMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanStaffModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STAFFINFOMAP) && this.staffInfoMap != null) {
            final boolean needClear = !builder.hasStaffInfoMap();
            final int tmpFieldCnt = this.staffInfoMap.copyChangeToCs(builder.getStaffInfoMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStaffInfoMap();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanStaffModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STAFFINFOMAP) && this.staffInfoMap != null) {
            final boolean needClear = !builder.hasStaffInfoMap();
            final int tmpFieldCnt = this.staffInfoMap.copyChangeToAndClearDeleteKeysCs(builder.getStaffInfoMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStaffInfoMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanStaffModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasStaffInfoMap()) {
            this.getStaffInfoMap().mergeFromCs(proto.getStaffInfoMap());
        } else {
            if (this.staffInfoMap != null) {
                this.staffInfoMap.mergeFromCs(proto.getStaffInfoMap());
            }
        }
        this.markAll();
        return ClanStaffModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanStaffModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasStaffInfoMap()) {
            this.getStaffInfoMap().mergeChangeFromCs(proto.getStaffInfoMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanStaffModel.Builder getCopyDbBuilder() {
        final ClanStaffModel.Builder builder = ClanStaffModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanStaffModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.staffInfoMap != null) {
            Clan.Int32ClanStaffInfoMap.Builder tmpBuilder = Clan.Int32ClanStaffInfoMap.newBuilder();
            final int tmpFieldCnt = this.staffInfoMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStaffInfoMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStaffInfoMap();
            }
        }  else if (builder.hasStaffInfoMap()) {
            // 清理StaffInfoMap
            builder.clearStaffInfoMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanStaffModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STAFFINFOMAP) && this.staffInfoMap != null) {
            final boolean needClear = !builder.hasStaffInfoMap();
            final int tmpFieldCnt = this.staffInfoMap.copyChangeToDb(builder.getStaffInfoMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStaffInfoMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanStaffModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasStaffInfoMap()) {
            this.getStaffInfoMap().mergeFromDb(proto.getStaffInfoMap());
        } else {
            if (this.staffInfoMap != null) {
                this.staffInfoMap.mergeFromDb(proto.getStaffInfoMap());
            }
        }
        this.markAll();
        return ClanStaffModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanStaffModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasStaffInfoMap()) {
            this.getStaffInfoMap().mergeChangeFromDb(proto.getStaffInfoMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanStaffModel.Builder getCopySsBuilder() {
        final ClanStaffModel.Builder builder = ClanStaffModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanStaffModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.staffInfoMap != null) {
            Clan.Int32ClanStaffInfoMap.Builder tmpBuilder = Clan.Int32ClanStaffInfoMap.newBuilder();
            final int tmpFieldCnt = this.staffInfoMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStaffInfoMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStaffInfoMap();
            }
        }  else if (builder.hasStaffInfoMap()) {
            // 清理StaffInfoMap
            builder.clearStaffInfoMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanStaffModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STAFFINFOMAP) && this.staffInfoMap != null) {
            final boolean needClear = !builder.hasStaffInfoMap();
            final int tmpFieldCnt = this.staffInfoMap.copyChangeToSs(builder.getStaffInfoMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStaffInfoMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanStaffModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasStaffInfoMap()) {
            this.getStaffInfoMap().mergeFromSs(proto.getStaffInfoMap());
        } else {
            if (this.staffInfoMap != null) {
                this.staffInfoMap.mergeFromSs(proto.getStaffInfoMap());
            }
        }
        this.markAll();
        return ClanStaffModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanStaffModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasStaffInfoMap()) {
            this.getStaffInfoMap().mergeChangeFromSs(proto.getStaffInfoMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanStaffModel.Builder builder = ClanStaffModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_STAFFINFOMAP) && this.staffInfoMap != null) {
            this.staffInfoMap.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.staffInfoMap != null) {
            this.staffInfoMap.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanStaffModelProp)) {
            return false;
        }
        final ClanStaffModelProp otherNode = (ClanStaffModelProp) node;
        if (!this.getStaffInfoMap().compareDataTo(otherNode.getStaffInfoMap())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
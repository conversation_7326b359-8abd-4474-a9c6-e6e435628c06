package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Clan.ClanLogRecordModel;
import com.yorha.proto.Struct;
import com.yorha.proto.ClanPB.ClanLogRecordModelPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanLogRecordModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_LOGRECORD = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int64ClanLogItemMapProp logRecord = null;

    public ClanLogRecordModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanLogRecordModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get logRecord
     *
     * @return logRecord value
     */
    public Int64ClanLogItemMapProp getLogRecord() {
        if (this.logRecord == null) {
            this.logRecord = new Int64ClanLogItemMapProp(this, FIELD_INDEX_LOGRECORD);
        }
        return this.logRecord;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putLogRecordV(ClanLogItemProp v) {
        this.getLogRecord().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ClanLogItemProp addEmptyLogRecord(Long k) {
        return this.getLogRecord().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getLogRecordSize() {
        if (this.logRecord == null) {
            return 0;
        }
        return this.logRecord.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isLogRecordEmpty() {
        if (this.logRecord == null) {
            return true;
        }
        return this.logRecord.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ClanLogItemProp getLogRecordV(Long k) {
        if (this.logRecord == null || !this.logRecord.containsKey(k)) {
            return null;
        }
        return this.logRecord.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearLogRecord() {
        if (this.logRecord != null) {
            this.logRecord.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeLogRecordV(Long k) {
        if (this.logRecord != null) {
            this.logRecord.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanLogRecordModelPB.Builder getCopyCsBuilder() {
        final ClanLogRecordModelPB.Builder builder = ClanLogRecordModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanLogRecordModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.logRecord != null) {
            StructPB.Int64ClanLogItemMapPB.Builder tmpBuilder = StructPB.Int64ClanLogItemMapPB.newBuilder();
            final int tmpFieldCnt = this.logRecord.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLogRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLogRecord();
            }
        }  else if (builder.hasLogRecord()) {
            // 清理LogRecord
            builder.clearLogRecord();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanLogRecordModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LOGRECORD) && this.logRecord != null) {
            final boolean needClear = !builder.hasLogRecord();
            final int tmpFieldCnt = this.logRecord.copyChangeToCs(builder.getLogRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLogRecord();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanLogRecordModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LOGRECORD) && this.logRecord != null) {
            final boolean needClear = !builder.hasLogRecord();
            final int tmpFieldCnt = this.logRecord.copyChangeToAndClearDeleteKeysCs(builder.getLogRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLogRecord();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanLogRecordModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLogRecord()) {
            this.getLogRecord().mergeFromCs(proto.getLogRecord());
        } else {
            if (this.logRecord != null) {
                this.logRecord.mergeFromCs(proto.getLogRecord());
            }
        }
        this.markAll();
        return ClanLogRecordModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanLogRecordModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLogRecord()) {
            this.getLogRecord().mergeChangeFromCs(proto.getLogRecord());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanLogRecordModel.Builder getCopyDbBuilder() {
        final ClanLogRecordModel.Builder builder = ClanLogRecordModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanLogRecordModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.logRecord != null) {
            Struct.Int64ClanLogItemMap.Builder tmpBuilder = Struct.Int64ClanLogItemMap.newBuilder();
            final int tmpFieldCnt = this.logRecord.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLogRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLogRecord();
            }
        }  else if (builder.hasLogRecord()) {
            // 清理LogRecord
            builder.clearLogRecord();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanLogRecordModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LOGRECORD) && this.logRecord != null) {
            final boolean needClear = !builder.hasLogRecord();
            final int tmpFieldCnt = this.logRecord.copyChangeToDb(builder.getLogRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLogRecord();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanLogRecordModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLogRecord()) {
            this.getLogRecord().mergeFromDb(proto.getLogRecord());
        } else {
            if (this.logRecord != null) {
                this.logRecord.mergeFromDb(proto.getLogRecord());
            }
        }
        this.markAll();
        return ClanLogRecordModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanLogRecordModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLogRecord()) {
            this.getLogRecord().mergeChangeFromDb(proto.getLogRecord());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanLogRecordModel.Builder getCopySsBuilder() {
        final ClanLogRecordModel.Builder builder = ClanLogRecordModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanLogRecordModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.logRecord != null) {
            Struct.Int64ClanLogItemMap.Builder tmpBuilder = Struct.Int64ClanLogItemMap.newBuilder();
            final int tmpFieldCnt = this.logRecord.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLogRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLogRecord();
            }
        }  else if (builder.hasLogRecord()) {
            // 清理LogRecord
            builder.clearLogRecord();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanLogRecordModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LOGRECORD) && this.logRecord != null) {
            final boolean needClear = !builder.hasLogRecord();
            final int tmpFieldCnt = this.logRecord.copyChangeToSs(builder.getLogRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLogRecord();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanLogRecordModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLogRecord()) {
            this.getLogRecord().mergeFromSs(proto.getLogRecord());
        } else {
            if (this.logRecord != null) {
                this.logRecord.mergeFromSs(proto.getLogRecord());
            }
        }
        this.markAll();
        return ClanLogRecordModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanLogRecordModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLogRecord()) {
            this.getLogRecord().mergeChangeFromSs(proto.getLogRecord());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanLogRecordModel.Builder builder = ClanLogRecordModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_LOGRECORD) && this.logRecord != null) {
            this.logRecord.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.logRecord != null) {
            this.logRecord.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanLogRecordModelProp)) {
            return false;
        }
        final ClanLogRecordModelProp otherNode = (ClanLogRecordModelProp) node;
        if (!this.getLogRecord().compareDataTo(otherNode.getLogRecord())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
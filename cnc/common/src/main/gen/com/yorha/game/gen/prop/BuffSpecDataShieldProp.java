package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructBattle.BuffSpecDataShield;
import com.yorha.proto.StructBattlePB.BuffSpecDataShieldPB;


/**
 * <AUTHOR> auto gen
 */
public class BuffSpecDataShieldProp extends AbstractPropNode {

    public static final int FIELD_INDEX_SHIELDVALUE = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private long shieldValue = Constant.DEFAULT_LONG_VALUE;

    public BuffSpecDataShieldProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BuffSpecDataShieldProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get shieldValue
     *
     * @return shieldValue value
     */
    public long getShieldValue() {
        return this.shieldValue;
    }

    /**
     * set shieldValue && set marked
     *
     * @param shieldValue new value
     * @return current object
     */
    public BuffSpecDataShieldProp setShieldValue(long shieldValue) {
        if (this.shieldValue != shieldValue) {
            this.mark(FIELD_INDEX_SHIELDVALUE);
            this.shieldValue = shieldValue;
        }
        return this;
    }

    /**
     * inner set shieldValue
     *
     * @param shieldValue new value
     */
    private void innerSetShieldValue(long shieldValue) {
        this.shieldValue = shieldValue;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BuffSpecDataShieldPB.Builder getCopyCsBuilder() {
        final BuffSpecDataShieldPB.Builder builder = BuffSpecDataShieldPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BuffSpecDataShieldPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getShieldValue() != 0L) {
            builder.setShieldValue(this.getShieldValue());
            fieldCnt++;
        }  else if (builder.hasShieldValue()) {
            // 清理ShieldValue
            builder.clearShieldValue();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BuffSpecDataShieldPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SHIELDVALUE)) {
            builder.setShieldValue(this.getShieldValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BuffSpecDataShieldPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SHIELDVALUE)) {
            builder.setShieldValue(this.getShieldValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BuffSpecDataShieldPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasShieldValue()) {
            this.innerSetShieldValue(proto.getShieldValue());
        } else {
            this.innerSetShieldValue(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return BuffSpecDataShieldProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BuffSpecDataShieldPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasShieldValue()) {
            this.setShieldValue(proto.getShieldValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BuffSpecDataShield.Builder getCopyDbBuilder() {
        final BuffSpecDataShield.Builder builder = BuffSpecDataShield.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(BuffSpecDataShield.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getShieldValue() != 0L) {
            builder.setShieldValue(this.getShieldValue());
            fieldCnt++;
        }  else if (builder.hasShieldValue()) {
            // 清理ShieldValue
            builder.clearShieldValue();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(BuffSpecDataShield.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SHIELDVALUE)) {
            builder.setShieldValue(this.getShieldValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(BuffSpecDataShield proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasShieldValue()) {
            this.innerSetShieldValue(proto.getShieldValue());
        } else {
            this.innerSetShieldValue(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return BuffSpecDataShieldProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(BuffSpecDataShield proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasShieldValue()) {
            this.setShieldValue(proto.getShieldValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BuffSpecDataShield.Builder getCopySsBuilder() {
        final BuffSpecDataShield.Builder builder = BuffSpecDataShield.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(BuffSpecDataShield.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getShieldValue() != 0L) {
            builder.setShieldValue(this.getShieldValue());
            fieldCnt++;
        }  else if (builder.hasShieldValue()) {
            // 清理ShieldValue
            builder.clearShieldValue();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(BuffSpecDataShield.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SHIELDVALUE)) {
            builder.setShieldValue(this.getShieldValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(BuffSpecDataShield proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasShieldValue()) {
            this.innerSetShieldValue(proto.getShieldValue());
        } else {
            this.innerSetShieldValue(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return BuffSpecDataShieldProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(BuffSpecDataShield proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasShieldValue()) {
            this.setShieldValue(proto.getShieldValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        BuffSpecDataShield.Builder builder = BuffSpecDataShield.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BuffSpecDataShieldProp)) {
            return false;
        }
        final BuffSpecDataShieldProp otherNode = (BuffSpecDataShieldProp) node;
        if (this.shieldValue != otherNode.shieldValue) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
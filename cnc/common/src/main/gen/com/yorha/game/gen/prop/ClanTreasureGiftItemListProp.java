package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructClanPB.ClanTreasureGiftItemListPB;
import com.yorha.proto.StructClan.ClanTreasureGiftItemList;
import com.yorha.proto.StructClanPB.ClanTreasureGiftItemPB;
import com.yorha.proto.StructClan.ClanTreasureGiftItem;

/**
 * <AUTHOR> auto gen
 */
public class ClanTreasureGiftItemListProp extends AbstractListNode<ClanTreasureGiftItemProp> {
    /**
     * Create a ClanTreasureGiftItemListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public ClanTreasureGiftItemListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to ClanTreasureGiftItemListProp
     *
     * @return new object
     */
    @Override
    public ClanTreasureGiftItemProp addEmptyValue() {
        final ClanTreasureGiftItemProp newProp = new ClanTreasureGiftItemProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanTreasureGiftItemListPB.Builder getCopyCsBuilder() {
        final ClanTreasureGiftItemListPB.Builder builder = ClanTreasureGiftItemListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(ClanTreasureGiftItemListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return ClanTreasureGiftItemListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final ClanTreasureGiftItemProp v : this) {
            final ClanTreasureGiftItemPB.Builder itemBuilder = ClanTreasureGiftItemPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return ClanTreasureGiftItemListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(ClanTreasureGiftItemListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return ClanTreasureGiftItemListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(ClanTreasureGiftItemListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (ClanTreasureGiftItemPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return ClanTreasureGiftItemListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(ClanTreasureGiftItemListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanTreasureGiftItemList.Builder getCopyDbBuilder() {
        final ClanTreasureGiftItemList.Builder builder = ClanTreasureGiftItemList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(ClanTreasureGiftItemList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return ClanTreasureGiftItemListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final ClanTreasureGiftItemProp v : this) {
            final ClanTreasureGiftItem.Builder itemBuilder = ClanTreasureGiftItem.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return ClanTreasureGiftItemListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(ClanTreasureGiftItemList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return ClanTreasureGiftItemListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(ClanTreasureGiftItemList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (ClanTreasureGiftItem v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return ClanTreasureGiftItemListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(ClanTreasureGiftItemList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanTreasureGiftItemList.Builder getCopySsBuilder() {
        final ClanTreasureGiftItemList.Builder builder = ClanTreasureGiftItemList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(ClanTreasureGiftItemList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return ClanTreasureGiftItemListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final ClanTreasureGiftItemProp v : this) {
            final ClanTreasureGiftItem.Builder itemBuilder = ClanTreasureGiftItem.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return ClanTreasureGiftItemListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(ClanTreasureGiftItemList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return ClanTreasureGiftItemListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(ClanTreasureGiftItemList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (ClanTreasureGiftItem v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return ClanTreasureGiftItemListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(ClanTreasureGiftItemList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        ClanTreasureGiftItemList.Builder builder = ClanTreasureGiftItemList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
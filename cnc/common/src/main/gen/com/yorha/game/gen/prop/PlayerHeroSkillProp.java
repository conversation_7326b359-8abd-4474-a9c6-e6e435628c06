package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerHeroSkill;
import com.yorha.proto.PlayerPB.PlayerHeroSkillPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerHeroSkillProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_SKILLGROUPID = 0;
    public static final int FIELD_INDEX_SKILLLEVEL = 1;
    public static final int FIELD_INDEX_SKILLSTAR = 2;
    public static final int FIELD_INDEX_SKILLSLOT = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int skillGroupId = Constant.DEFAULT_INT_VALUE;
    private int skillLevel = Constant.DEFAULT_INT_VALUE;
    private int skillStar = Constant.DEFAULT_INT_VALUE;
    private int skillSlot = Constant.DEFAULT_INT_VALUE;

    public PlayerHeroSkillProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerHeroSkillProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get skillGroupId
     *
     * @return skillGroupId value
     */
    public int getSkillGroupId() {
        return this.skillGroupId;
    }

    /**
     * set skillGroupId && set marked
     *
     * @param skillGroupId new value
     * @return current object
     */
    public PlayerHeroSkillProp setSkillGroupId(int skillGroupId) {
        if (this.skillGroupId != skillGroupId) {
            this.mark(FIELD_INDEX_SKILLGROUPID);
            this.skillGroupId = skillGroupId;
        }
        return this;
    }

    /**
     * inner set skillGroupId
     *
     * @param skillGroupId new value
     */
    private void innerSetSkillGroupId(int skillGroupId) {
        this.skillGroupId = skillGroupId;
    }

    /**
     * get skillLevel
     *
     * @return skillLevel value
     */
    public int getSkillLevel() {
        return this.skillLevel;
    }

    /**
     * set skillLevel && set marked
     *
     * @param skillLevel new value
     * @return current object
     */
    public PlayerHeroSkillProp setSkillLevel(int skillLevel) {
        if (this.skillLevel != skillLevel) {
            this.mark(FIELD_INDEX_SKILLLEVEL);
            this.skillLevel = skillLevel;
        }
        return this;
    }

    /**
     * inner set skillLevel
     *
     * @param skillLevel new value
     */
    private void innerSetSkillLevel(int skillLevel) {
        this.skillLevel = skillLevel;
    }

    /**
     * get skillStar
     *
     * @return skillStar value
     */
    public int getSkillStar() {
        return this.skillStar;
    }

    /**
     * set skillStar && set marked
     *
     * @param skillStar new value
     * @return current object
     */
    public PlayerHeroSkillProp setSkillStar(int skillStar) {
        if (this.skillStar != skillStar) {
            this.mark(FIELD_INDEX_SKILLSTAR);
            this.skillStar = skillStar;
        }
        return this;
    }

    /**
     * inner set skillStar
     *
     * @param skillStar new value
     */
    private void innerSetSkillStar(int skillStar) {
        this.skillStar = skillStar;
    }

    /**
     * get skillSlot
     *
     * @return skillSlot value
     */
    public int getSkillSlot() {
        return this.skillSlot;
    }

    /**
     * set skillSlot && set marked
     *
     * @param skillSlot new value
     * @return current object
     */
    public PlayerHeroSkillProp setSkillSlot(int skillSlot) {
        if (this.skillSlot != skillSlot) {
            this.mark(FIELD_INDEX_SKILLSLOT);
            this.skillSlot = skillSlot;
        }
        return this;
    }

    /**
     * inner set skillSlot
     *
     * @param skillSlot new value
     */
    private void innerSetSkillSlot(int skillSlot) {
        this.skillSlot = skillSlot;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerHeroSkillPB.Builder getCopyCsBuilder() {
        final PlayerHeroSkillPB.Builder builder = PlayerHeroSkillPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerHeroSkillPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSkillGroupId() != 0) {
            builder.setSkillGroupId(this.getSkillGroupId());
            fieldCnt++;
        }  else if (builder.hasSkillGroupId()) {
            // 清理SkillGroupId
            builder.clearSkillGroupId();
            fieldCnt++;
        }
        if (this.getSkillLevel() != 0) {
            builder.setSkillLevel(this.getSkillLevel());
            fieldCnt++;
        }  else if (builder.hasSkillLevel()) {
            // 清理SkillLevel
            builder.clearSkillLevel();
            fieldCnt++;
        }
        if (this.getSkillStar() != 0) {
            builder.setSkillStar(this.getSkillStar());
            fieldCnt++;
        }  else if (builder.hasSkillStar()) {
            // 清理SkillStar
            builder.clearSkillStar();
            fieldCnt++;
        }
        if (this.getSkillSlot() != 0) {
            builder.setSkillSlot(this.getSkillSlot());
            fieldCnt++;
        }  else if (builder.hasSkillSlot()) {
            // 清理SkillSlot
            builder.clearSkillSlot();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerHeroSkillPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLGROUPID)) {
            builder.setSkillGroupId(this.getSkillGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLLEVEL)) {
            builder.setSkillLevel(this.getSkillLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLSTAR)) {
            builder.setSkillStar(this.getSkillStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLSLOT)) {
            builder.setSkillSlot(this.getSkillSlot());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerHeroSkillPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLGROUPID)) {
            builder.setSkillGroupId(this.getSkillGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLLEVEL)) {
            builder.setSkillLevel(this.getSkillLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLSTAR)) {
            builder.setSkillStar(this.getSkillStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLSLOT)) {
            builder.setSkillSlot(this.getSkillSlot());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerHeroSkillPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkillGroupId()) {
            this.innerSetSkillGroupId(proto.getSkillGroupId());
        } else {
            this.innerSetSkillGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkillLevel()) {
            this.innerSetSkillLevel(proto.getSkillLevel());
        } else {
            this.innerSetSkillLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkillStar()) {
            this.innerSetSkillStar(proto.getSkillStar());
        } else {
            this.innerSetSkillStar(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkillSlot()) {
            this.innerSetSkillSlot(proto.getSkillSlot());
        } else {
            this.innerSetSkillSlot(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerHeroSkillProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerHeroSkillPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkillGroupId()) {
            this.setSkillGroupId(proto.getSkillGroupId());
            fieldCnt++;
        }
        if (proto.hasSkillLevel()) {
            this.setSkillLevel(proto.getSkillLevel());
            fieldCnt++;
        }
        if (proto.hasSkillStar()) {
            this.setSkillStar(proto.getSkillStar());
            fieldCnt++;
        }
        if (proto.hasSkillSlot()) {
            this.setSkillSlot(proto.getSkillSlot());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerHeroSkill.Builder getCopyDbBuilder() {
        final PlayerHeroSkill.Builder builder = PlayerHeroSkill.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerHeroSkill.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSkillGroupId() != 0) {
            builder.setSkillGroupId(this.getSkillGroupId());
            fieldCnt++;
        }  else if (builder.hasSkillGroupId()) {
            // 清理SkillGroupId
            builder.clearSkillGroupId();
            fieldCnt++;
        }
        if (this.getSkillLevel() != 0) {
            builder.setSkillLevel(this.getSkillLevel());
            fieldCnt++;
        }  else if (builder.hasSkillLevel()) {
            // 清理SkillLevel
            builder.clearSkillLevel();
            fieldCnt++;
        }
        if (this.getSkillStar() != 0) {
            builder.setSkillStar(this.getSkillStar());
            fieldCnt++;
        }  else if (builder.hasSkillStar()) {
            // 清理SkillStar
            builder.clearSkillStar();
            fieldCnt++;
        }
        if (this.getSkillSlot() != 0) {
            builder.setSkillSlot(this.getSkillSlot());
            fieldCnt++;
        }  else if (builder.hasSkillSlot()) {
            // 清理SkillSlot
            builder.clearSkillSlot();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerHeroSkill.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLGROUPID)) {
            builder.setSkillGroupId(this.getSkillGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLLEVEL)) {
            builder.setSkillLevel(this.getSkillLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLSTAR)) {
            builder.setSkillStar(this.getSkillStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLSLOT)) {
            builder.setSkillSlot(this.getSkillSlot());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerHeroSkill proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkillGroupId()) {
            this.innerSetSkillGroupId(proto.getSkillGroupId());
        } else {
            this.innerSetSkillGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkillLevel()) {
            this.innerSetSkillLevel(proto.getSkillLevel());
        } else {
            this.innerSetSkillLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkillStar()) {
            this.innerSetSkillStar(proto.getSkillStar());
        } else {
            this.innerSetSkillStar(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkillSlot()) {
            this.innerSetSkillSlot(proto.getSkillSlot());
        } else {
            this.innerSetSkillSlot(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerHeroSkillProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerHeroSkill proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkillGroupId()) {
            this.setSkillGroupId(proto.getSkillGroupId());
            fieldCnt++;
        }
        if (proto.hasSkillLevel()) {
            this.setSkillLevel(proto.getSkillLevel());
            fieldCnt++;
        }
        if (proto.hasSkillStar()) {
            this.setSkillStar(proto.getSkillStar());
            fieldCnt++;
        }
        if (proto.hasSkillSlot()) {
            this.setSkillSlot(proto.getSkillSlot());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerHeroSkill.Builder getCopySsBuilder() {
        final PlayerHeroSkill.Builder builder = PlayerHeroSkill.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerHeroSkill.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSkillGroupId() != 0) {
            builder.setSkillGroupId(this.getSkillGroupId());
            fieldCnt++;
        }  else if (builder.hasSkillGroupId()) {
            // 清理SkillGroupId
            builder.clearSkillGroupId();
            fieldCnt++;
        }
        if (this.getSkillLevel() != 0) {
            builder.setSkillLevel(this.getSkillLevel());
            fieldCnt++;
        }  else if (builder.hasSkillLevel()) {
            // 清理SkillLevel
            builder.clearSkillLevel();
            fieldCnt++;
        }
        if (this.getSkillStar() != 0) {
            builder.setSkillStar(this.getSkillStar());
            fieldCnt++;
        }  else if (builder.hasSkillStar()) {
            // 清理SkillStar
            builder.clearSkillStar();
            fieldCnt++;
        }
        if (this.getSkillSlot() != 0) {
            builder.setSkillSlot(this.getSkillSlot());
            fieldCnt++;
        }  else if (builder.hasSkillSlot()) {
            // 清理SkillSlot
            builder.clearSkillSlot();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerHeroSkill.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLGROUPID)) {
            builder.setSkillGroupId(this.getSkillGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLLEVEL)) {
            builder.setSkillLevel(this.getSkillLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLSTAR)) {
            builder.setSkillStar(this.getSkillStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLSLOT)) {
            builder.setSkillSlot(this.getSkillSlot());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerHeroSkill proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkillGroupId()) {
            this.innerSetSkillGroupId(proto.getSkillGroupId());
        } else {
            this.innerSetSkillGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkillLevel()) {
            this.innerSetSkillLevel(proto.getSkillLevel());
        } else {
            this.innerSetSkillLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkillStar()) {
            this.innerSetSkillStar(proto.getSkillStar());
        } else {
            this.innerSetSkillStar(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkillSlot()) {
            this.innerSetSkillSlot(proto.getSkillSlot());
        } else {
            this.innerSetSkillSlot(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerHeroSkillProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerHeroSkill proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkillGroupId()) {
            this.setSkillGroupId(proto.getSkillGroupId());
            fieldCnt++;
        }
        if (proto.hasSkillLevel()) {
            this.setSkillLevel(proto.getSkillLevel());
            fieldCnt++;
        }
        if (proto.hasSkillStar()) {
            this.setSkillStar(proto.getSkillStar());
            fieldCnt++;
        }
        if (proto.hasSkillSlot()) {
            this.setSkillSlot(proto.getSkillSlot());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerHeroSkill.Builder builder = PlayerHeroSkill.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.skillGroupId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerHeroSkillProp)) {
            return false;
        }
        final PlayerHeroSkillProp otherNode = (PlayerHeroSkillProp) node;
        if (this.skillGroupId != otherNode.skillGroupId) {
            return false;
        }
        if (this.skillLevel != otherNode.skillLevel) {
            return false;
        }
        if (this.skillStar != otherNode.skillStar) {
            return false;
        }
        if (this.skillSlot != otherNode.skillSlot) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
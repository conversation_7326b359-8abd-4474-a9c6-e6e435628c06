package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityFestivalBpStatus;
import com.yorha.proto.StructPB.ActivityFestivalBpStatusPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityFestivalBpStatusProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_BPID = 0;
    public static final int FIELD_INDEX_CANTAKELEVEL = 1;
    public static final int FIELD_INDEX_HASTAKELEVEL = 2;
    public static final int FIELD_INDEX_OPEN = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int bpId = Constant.DEFAULT_INT_VALUE;
    private int canTakeLevel = Constant.DEFAULT_INT_VALUE;
    private int hasTakeLevel = Constant.DEFAULT_INT_VALUE;
    private boolean open = Constant.DEFAULT_BOOLEAN_VALUE;

    public ActivityFestivalBpStatusProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityFestivalBpStatusProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get bpId
     *
     * @return bpId value
     */
    public int getBpId() {
        return this.bpId;
    }

    /**
     * set bpId && set marked
     *
     * @param bpId new value
     * @return current object
     */
    public ActivityFestivalBpStatusProp setBpId(int bpId) {
        if (this.bpId != bpId) {
            this.mark(FIELD_INDEX_BPID);
            this.bpId = bpId;
        }
        return this;
    }

    /**
     * inner set bpId
     *
     * @param bpId new value
     */
    private void innerSetBpId(int bpId) {
        this.bpId = bpId;
    }

    /**
     * get canTakeLevel
     *
     * @return canTakeLevel value
     */
    public int getCanTakeLevel() {
        return this.canTakeLevel;
    }

    /**
     * set canTakeLevel && set marked
     *
     * @param canTakeLevel new value
     * @return current object
     */
    public ActivityFestivalBpStatusProp setCanTakeLevel(int canTakeLevel) {
        if (this.canTakeLevel != canTakeLevel) {
            this.mark(FIELD_INDEX_CANTAKELEVEL);
            this.canTakeLevel = canTakeLevel;
        }
        return this;
    }

    /**
     * inner set canTakeLevel
     *
     * @param canTakeLevel new value
     */
    private void innerSetCanTakeLevel(int canTakeLevel) {
        this.canTakeLevel = canTakeLevel;
    }

    /**
     * get hasTakeLevel
     *
     * @return hasTakeLevel value
     */
    public int getHasTakeLevel() {
        return this.hasTakeLevel;
    }

    /**
     * set hasTakeLevel && set marked
     *
     * @param hasTakeLevel new value
     * @return current object
     */
    public ActivityFestivalBpStatusProp setHasTakeLevel(int hasTakeLevel) {
        if (this.hasTakeLevel != hasTakeLevel) {
            this.mark(FIELD_INDEX_HASTAKELEVEL);
            this.hasTakeLevel = hasTakeLevel;
        }
        return this;
    }

    /**
     * inner set hasTakeLevel
     *
     * @param hasTakeLevel new value
     */
    private void innerSetHasTakeLevel(int hasTakeLevel) {
        this.hasTakeLevel = hasTakeLevel;
    }

    /**
     * get open
     *
     * @return open value
     */
    public boolean getOpen() {
        return this.open;
    }

    /**
     * set open && set marked
     *
     * @param open new value
     * @return current object
     */
    public ActivityFestivalBpStatusProp setOpen(boolean open) {
        if (this.open != open) {
            this.mark(FIELD_INDEX_OPEN);
            this.open = open;
        }
        return this;
    }

    /**
     * inner set open
     *
     * @param open new value
     */
    private void innerSetOpen(boolean open) {
        this.open = open;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityFestivalBpStatusPB.Builder getCopyCsBuilder() {
        final ActivityFestivalBpStatusPB.Builder builder = ActivityFestivalBpStatusPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityFestivalBpStatusPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBpId() != 0) {
            builder.setBpId(this.getBpId());
            fieldCnt++;
        }  else if (builder.hasBpId()) {
            // 清理BpId
            builder.clearBpId();
            fieldCnt++;
        }
        if (this.getCanTakeLevel() != 0) {
            builder.setCanTakeLevel(this.getCanTakeLevel());
            fieldCnt++;
        }  else if (builder.hasCanTakeLevel()) {
            // 清理CanTakeLevel
            builder.clearCanTakeLevel();
            fieldCnt++;
        }
        if (this.getHasTakeLevel() != 0) {
            builder.setHasTakeLevel(this.getHasTakeLevel());
            fieldCnt++;
        }  else if (builder.hasHasTakeLevel()) {
            // 清理HasTakeLevel
            builder.clearHasTakeLevel();
            fieldCnt++;
        }
        if (this.getOpen()) {
            builder.setOpen(this.getOpen());
            fieldCnt++;
        }  else if (builder.hasOpen()) {
            // 清理Open
            builder.clearOpen();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityFestivalBpStatusPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BPID)) {
            builder.setBpId(this.getBpId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANTAKELEVEL)) {
            builder.setCanTakeLevel(this.getCanTakeLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HASTAKELEVEL)) {
            builder.setHasTakeLevel(this.getHasTakeLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPEN)) {
            builder.setOpen(this.getOpen());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityFestivalBpStatusPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BPID)) {
            builder.setBpId(this.getBpId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANTAKELEVEL)) {
            builder.setCanTakeLevel(this.getCanTakeLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HASTAKELEVEL)) {
            builder.setHasTakeLevel(this.getHasTakeLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPEN)) {
            builder.setOpen(this.getOpen());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityFestivalBpStatusPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBpId()) {
            this.innerSetBpId(proto.getBpId());
        } else {
            this.innerSetBpId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCanTakeLevel()) {
            this.innerSetCanTakeLevel(proto.getCanTakeLevel());
        } else {
            this.innerSetCanTakeLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHasTakeLevel()) {
            this.innerSetHasTakeLevel(proto.getHasTakeLevel());
        } else {
            this.innerSetHasTakeLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOpen()) {
            this.innerSetOpen(proto.getOpen());
        } else {
            this.innerSetOpen(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ActivityFestivalBpStatusProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityFestivalBpStatusPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBpId()) {
            this.setBpId(proto.getBpId());
            fieldCnt++;
        }
        if (proto.hasCanTakeLevel()) {
            this.setCanTakeLevel(proto.getCanTakeLevel());
            fieldCnt++;
        }
        if (proto.hasHasTakeLevel()) {
            this.setHasTakeLevel(proto.getHasTakeLevel());
            fieldCnt++;
        }
        if (proto.hasOpen()) {
            this.setOpen(proto.getOpen());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityFestivalBpStatus.Builder getCopyDbBuilder() {
        final ActivityFestivalBpStatus.Builder builder = ActivityFestivalBpStatus.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityFestivalBpStatus.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBpId() != 0) {
            builder.setBpId(this.getBpId());
            fieldCnt++;
        }  else if (builder.hasBpId()) {
            // 清理BpId
            builder.clearBpId();
            fieldCnt++;
        }
        if (this.getCanTakeLevel() != 0) {
            builder.setCanTakeLevel(this.getCanTakeLevel());
            fieldCnt++;
        }  else if (builder.hasCanTakeLevel()) {
            // 清理CanTakeLevel
            builder.clearCanTakeLevel();
            fieldCnt++;
        }
        if (this.getHasTakeLevel() != 0) {
            builder.setHasTakeLevel(this.getHasTakeLevel());
            fieldCnt++;
        }  else if (builder.hasHasTakeLevel()) {
            // 清理HasTakeLevel
            builder.clearHasTakeLevel();
            fieldCnt++;
        }
        if (this.getOpen()) {
            builder.setOpen(this.getOpen());
            fieldCnt++;
        }  else if (builder.hasOpen()) {
            // 清理Open
            builder.clearOpen();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityFestivalBpStatus.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BPID)) {
            builder.setBpId(this.getBpId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANTAKELEVEL)) {
            builder.setCanTakeLevel(this.getCanTakeLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HASTAKELEVEL)) {
            builder.setHasTakeLevel(this.getHasTakeLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPEN)) {
            builder.setOpen(this.getOpen());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityFestivalBpStatus proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBpId()) {
            this.innerSetBpId(proto.getBpId());
        } else {
            this.innerSetBpId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCanTakeLevel()) {
            this.innerSetCanTakeLevel(proto.getCanTakeLevel());
        } else {
            this.innerSetCanTakeLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHasTakeLevel()) {
            this.innerSetHasTakeLevel(proto.getHasTakeLevel());
        } else {
            this.innerSetHasTakeLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOpen()) {
            this.innerSetOpen(proto.getOpen());
        } else {
            this.innerSetOpen(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ActivityFestivalBpStatusProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityFestivalBpStatus proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBpId()) {
            this.setBpId(proto.getBpId());
            fieldCnt++;
        }
        if (proto.hasCanTakeLevel()) {
            this.setCanTakeLevel(proto.getCanTakeLevel());
            fieldCnt++;
        }
        if (proto.hasHasTakeLevel()) {
            this.setHasTakeLevel(proto.getHasTakeLevel());
            fieldCnt++;
        }
        if (proto.hasOpen()) {
            this.setOpen(proto.getOpen());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityFestivalBpStatus.Builder getCopySsBuilder() {
        final ActivityFestivalBpStatus.Builder builder = ActivityFestivalBpStatus.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityFestivalBpStatus.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBpId() != 0) {
            builder.setBpId(this.getBpId());
            fieldCnt++;
        }  else if (builder.hasBpId()) {
            // 清理BpId
            builder.clearBpId();
            fieldCnt++;
        }
        if (this.getCanTakeLevel() != 0) {
            builder.setCanTakeLevel(this.getCanTakeLevel());
            fieldCnt++;
        }  else if (builder.hasCanTakeLevel()) {
            // 清理CanTakeLevel
            builder.clearCanTakeLevel();
            fieldCnt++;
        }
        if (this.getHasTakeLevel() != 0) {
            builder.setHasTakeLevel(this.getHasTakeLevel());
            fieldCnt++;
        }  else if (builder.hasHasTakeLevel()) {
            // 清理HasTakeLevel
            builder.clearHasTakeLevel();
            fieldCnt++;
        }
        if (this.getOpen()) {
            builder.setOpen(this.getOpen());
            fieldCnt++;
        }  else if (builder.hasOpen()) {
            // 清理Open
            builder.clearOpen();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityFestivalBpStatus.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BPID)) {
            builder.setBpId(this.getBpId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANTAKELEVEL)) {
            builder.setCanTakeLevel(this.getCanTakeLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HASTAKELEVEL)) {
            builder.setHasTakeLevel(this.getHasTakeLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPEN)) {
            builder.setOpen(this.getOpen());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityFestivalBpStatus proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBpId()) {
            this.innerSetBpId(proto.getBpId());
        } else {
            this.innerSetBpId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCanTakeLevel()) {
            this.innerSetCanTakeLevel(proto.getCanTakeLevel());
        } else {
            this.innerSetCanTakeLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHasTakeLevel()) {
            this.innerSetHasTakeLevel(proto.getHasTakeLevel());
        } else {
            this.innerSetHasTakeLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOpen()) {
            this.innerSetOpen(proto.getOpen());
        } else {
            this.innerSetOpen(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ActivityFestivalBpStatusProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityFestivalBpStatus proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBpId()) {
            this.setBpId(proto.getBpId());
            fieldCnt++;
        }
        if (proto.hasCanTakeLevel()) {
            this.setCanTakeLevel(proto.getCanTakeLevel());
            fieldCnt++;
        }
        if (proto.hasHasTakeLevel()) {
            this.setHasTakeLevel(proto.getHasTakeLevel());
            fieldCnt++;
        }
        if (proto.hasOpen()) {
            this.setOpen(proto.getOpen());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityFestivalBpStatus.Builder builder = ActivityFestivalBpStatus.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.bpId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityFestivalBpStatusProp)) {
            return false;
        }
        final ActivityFestivalBpStatusProp otherNode = (ActivityFestivalBpStatusProp) node;
        if (this.bpId != otherNode.bpId) {
            return false;
        }
        if (this.canTakeLevel != otherNode.canTakeLevel) {
            return false;
        }
        if (this.hasTakeLevel != otherNode.hasTakeLevel) {
            return false;
        }
        if (this.open != otherNode.open) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
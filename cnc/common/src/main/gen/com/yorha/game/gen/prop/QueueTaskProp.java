package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructPlayer.QueueTask;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.StructPlayerPB.QueueTaskPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class QueueTaskProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_TYPE = 1;
    public static final int FIELD_INDEX_QUEUEEXPIRTIME = 2;
    public static final int FIELD_INDEX_TASKID = 3;
    public static final int FIELD_INDEX_STARTIME = 4;
    public static final int FIELD_INDEX_ENDTIME = 5;
    public static final int FIELD_INDEX_COSTRESOURCE = 6;
    public static final int FIELD_INDEX_EXTRAPARAM = 7;
    public static final int FIELD_INDEX_SPEEDTIMESEC = 8;
    public static final int FIELD_INDEX_FREESPEEDCOUNT = 9;
    public static final int FIELD_INDEX_ISOPENCLANHELP = 10;

    public static final int FIELD_COUNT = 11;

    private long markBits0 = 0L;

    private long id = Constant.DEFAULT_LONG_VALUE;
    private QueueTaskType type = QueueTaskType.forNumber(0);
    private long queueExpirTime = Constant.DEFAULT_LONG_VALUE;
    private long taskId = Constant.DEFAULT_LONG_VALUE;
    private long starTime = Constant.DEFAULT_LONG_VALUE;
    private long endTime = Constant.DEFAULT_LONG_VALUE;
    private CurrencyListProp costResource = null;
    private QueueExtraParamProp extraParam = null;
    private int speedTimeSec = Constant.DEFAULT_INT_VALUE;
    private int freeSpeedCount = Constant.DEFAULT_INT_VALUE;
    private boolean isOpenClanHelp = Constant.DEFAULT_BOOLEAN_VALUE;

    public QueueTaskProp() {
        super(null, 0, FIELD_COUNT);
    }

    public QueueTaskProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public long getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public QueueTaskProp setId(long id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(long id) {
        this.id = id;
    }

    /**
     * get type
     *
     * @return type value
     */
    public QueueTaskType getType() {
        return this.type;
    }

    /**
     * set type && set marked
     *
     * @param type new value
     * @return current object
     */
    public QueueTaskProp setType(QueueTaskType type) {
        if (type == null) {
            throw new NullPointerException();
        }
        if (this.type != type) {
            this.mark(FIELD_INDEX_TYPE);
            this.type = type;
        }
        return this;
    }

    /**
     * inner set type
     *
     * @param type new value
     */
    private void innerSetType(QueueTaskType type) {
        this.type = type;
    }

    /**
     * get queueExpirTime
     *
     * @return queueExpirTime value
     */
    public long getQueueExpirTime() {
        return this.queueExpirTime;
    }

    /**
     * set queueExpirTime && set marked
     *
     * @param queueExpirTime new value
     * @return current object
     */
    public QueueTaskProp setQueueExpirTime(long queueExpirTime) {
        if (this.queueExpirTime != queueExpirTime) {
            this.mark(FIELD_INDEX_QUEUEEXPIRTIME);
            this.queueExpirTime = queueExpirTime;
        }
        return this;
    }

    /**
     * inner set queueExpirTime
     *
     * @param queueExpirTime new value
     */
    private void innerSetQueueExpirTime(long queueExpirTime) {
        this.queueExpirTime = queueExpirTime;
    }

    /**
     * get taskId
     *
     * @return taskId value
     */
    public long getTaskId() {
        return this.taskId;
    }

    /**
     * set taskId && set marked
     *
     * @param taskId new value
     * @return current object
     */
    public QueueTaskProp setTaskId(long taskId) {
        if (this.taskId != taskId) {
            this.mark(FIELD_INDEX_TASKID);
            this.taskId = taskId;
        }
        return this;
    }

    /**
     * inner set taskId
     *
     * @param taskId new value
     */
    private void innerSetTaskId(long taskId) {
        this.taskId = taskId;
    }

    /**
     * get starTime
     *
     * @return starTime value
     */
    public long getStarTime() {
        return this.starTime;
    }

    /**
     * set starTime && set marked
     *
     * @param starTime new value
     * @return current object
     */
    public QueueTaskProp setStarTime(long starTime) {
        if (this.starTime != starTime) {
            this.mark(FIELD_INDEX_STARTIME);
            this.starTime = starTime;
        }
        return this;
    }

    /**
     * inner set starTime
     *
     * @param starTime new value
     */
    private void innerSetStarTime(long starTime) {
        this.starTime = starTime;
    }

    /**
     * get endTime
     *
     * @return endTime value
     */
    public long getEndTime() {
        return this.endTime;
    }

    /**
     * set endTime && set marked
     *
     * @param endTime new value
     * @return current object
     */
    public QueueTaskProp setEndTime(long endTime) {
        if (this.endTime != endTime) {
            this.mark(FIELD_INDEX_ENDTIME);
            this.endTime = endTime;
        }
        return this;
    }

    /**
     * inner set endTime
     *
     * @param endTime new value
     */
    private void innerSetEndTime(long endTime) {
        this.endTime = endTime;
    }

    /**
     * get costResource
     *
     * @return costResource value
     */
    public CurrencyListProp getCostResource() {
        if (this.costResource == null) {
            this.costResource = new CurrencyListProp(this, FIELD_INDEX_COSTRESOURCE);
        }
        return this.costResource;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addCostResource(CurrencyProp v) {
        this.getCostResource().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public CurrencyProp getCostResourceIndex(int index) {
        return this.getCostResource().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public CurrencyProp removeCostResource(CurrencyProp v) {
        if (this.costResource == null) {
            return null;
        }
        if(this.costResource.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getCostResourceSize() {
        if (this.costResource == null) {
            return 0;
        }
        return this.costResource.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isCostResourceEmpty() {
        if (this.costResource == null) {
            return true;
        }
        return this.getCostResource().isEmpty();
    }

    /**
     * clear list
     */
    public void clearCostResource() {
        this.getCostResource().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public CurrencyProp removeCostResourceIndex(int index) {
        return this.getCostResource().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public CurrencyProp setCostResourceIndex(int index, CurrencyProp v) {
        return this.getCostResource().set(index, v);
    }
    /**
     * get extraParam
     *
     * @return extraParam value
     */
    public QueueExtraParamProp getExtraParam() {
        if (this.extraParam == null) {
            this.extraParam = new QueueExtraParamProp(this, FIELD_INDEX_EXTRAPARAM);
        }
        return this.extraParam;
    }

    /**
     * get speedTimeSec
     *
     * @return speedTimeSec value
     */
    public int getSpeedTimeSec() {
        return this.speedTimeSec;
    }

    /**
     * set speedTimeSec && set marked
     *
     * @param speedTimeSec new value
     * @return current object
     */
    public QueueTaskProp setSpeedTimeSec(int speedTimeSec) {
        if (this.speedTimeSec != speedTimeSec) {
            this.mark(FIELD_INDEX_SPEEDTIMESEC);
            this.speedTimeSec = speedTimeSec;
        }
        return this;
    }

    /**
     * inner set speedTimeSec
     *
     * @param speedTimeSec new value
     */
    private void innerSetSpeedTimeSec(int speedTimeSec) {
        this.speedTimeSec = speedTimeSec;
    }

    /**
     * get freeSpeedCount
     *
     * @return freeSpeedCount value
     */
    public int getFreeSpeedCount() {
        return this.freeSpeedCount;
    }

    /**
     * set freeSpeedCount && set marked
     *
     * @param freeSpeedCount new value
     * @return current object
     */
    public QueueTaskProp setFreeSpeedCount(int freeSpeedCount) {
        if (this.freeSpeedCount != freeSpeedCount) {
            this.mark(FIELD_INDEX_FREESPEEDCOUNT);
            this.freeSpeedCount = freeSpeedCount;
        }
        return this;
    }

    /**
     * inner set freeSpeedCount
     *
     * @param freeSpeedCount new value
     */
    private void innerSetFreeSpeedCount(int freeSpeedCount) {
        this.freeSpeedCount = freeSpeedCount;
    }

    /**
     * get isOpenClanHelp
     *
     * @return isOpenClanHelp value
     */
    public boolean getIsOpenClanHelp() {
        return this.isOpenClanHelp;
    }

    /**
     * set isOpenClanHelp && set marked
     *
     * @param isOpenClanHelp new value
     * @return current object
     */
    public QueueTaskProp setIsOpenClanHelp(boolean isOpenClanHelp) {
        if (this.isOpenClanHelp != isOpenClanHelp) {
            this.mark(FIELD_INDEX_ISOPENCLANHELP);
            this.isOpenClanHelp = isOpenClanHelp;
        }
        return this;
    }

    /**
     * inner set isOpenClanHelp
     *
     * @param isOpenClanHelp new value
     */
    private void innerSetIsOpenClanHelp(boolean isOpenClanHelp) {
        this.isOpenClanHelp = isOpenClanHelp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public QueueTaskPB.Builder getCopyCsBuilder() {
        final QueueTaskPB.Builder builder = QueueTaskPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(QueueTaskPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getType() != QueueTaskType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getQueueExpirTime() != 0L) {
            builder.setQueueExpirTime(this.getQueueExpirTime());
            fieldCnt++;
        }  else if (builder.hasQueueExpirTime()) {
            // 清理QueueExpirTime
            builder.clearQueueExpirTime();
            fieldCnt++;
        }
        if (this.getTaskId() != 0L) {
            builder.setTaskId(this.getTaskId());
            fieldCnt++;
        }  else if (builder.hasTaskId()) {
            // 清理TaskId
            builder.clearTaskId();
            fieldCnt++;
        }
        if (this.getStarTime() != 0L) {
            builder.setStarTime(this.getStarTime());
            fieldCnt++;
        }  else if (builder.hasStarTime()) {
            // 清理StarTime
            builder.clearStarTime();
            fieldCnt++;
        }
        if (this.getEndTime() != 0L) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }  else if (builder.hasEndTime()) {
            // 清理EndTime
            builder.clearEndTime();
            fieldCnt++;
        }
        if (this.costResource != null) {
            StructPB.CurrencyListPB.Builder tmpBuilder = StructPB.CurrencyListPB.newBuilder();
            final int tmpFieldCnt = this.costResource.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCostResource(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCostResource();
            }
        }  else if (builder.hasCostResource()) {
            // 清理CostResource
            builder.clearCostResource();
            fieldCnt++;
        }
        if (this.extraParam != null) {
            StructPlayerPB.QueueExtraParamPB.Builder tmpBuilder = StructPlayerPB.QueueExtraParamPB.newBuilder();
            final int tmpFieldCnt = this.extraParam.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExtraParam(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExtraParam();
            }
        }  else if (builder.hasExtraParam()) {
            // 清理ExtraParam
            builder.clearExtraParam();
            fieldCnt++;
        }
        if (this.getSpeedTimeSec() != 0) {
            builder.setSpeedTimeSec(this.getSpeedTimeSec());
            fieldCnt++;
        }  else if (builder.hasSpeedTimeSec()) {
            // 清理SpeedTimeSec
            builder.clearSpeedTimeSec();
            fieldCnt++;
        }
        if (this.getFreeSpeedCount() != 0) {
            builder.setFreeSpeedCount(this.getFreeSpeedCount());
            fieldCnt++;
        }  else if (builder.hasFreeSpeedCount()) {
            // 清理FreeSpeedCount
            builder.clearFreeSpeedCount();
            fieldCnt++;
        }
        if (this.getIsOpenClanHelp()) {
            builder.setIsOpenClanHelp(this.getIsOpenClanHelp());
            fieldCnt++;
        }  else if (builder.hasIsOpenClanHelp()) {
            // 清理IsOpenClanHelp
            builder.clearIsOpenClanHelp();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(QueueTaskPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_QUEUEEXPIRTIME)) {
            builder.setQueueExpirTime(this.getQueueExpirTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKID)) {
            builder.setTaskId(this.getTaskId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTIME)) {
            builder.setStarTime(this.getStarTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTIME)) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COSTRESOURCE) && this.costResource != null) {
            final boolean needClear = !builder.hasCostResource();
            final int tmpFieldCnt = this.costResource.copyChangeToCs(builder.getCostResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCostResource();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXTRAPARAM) && this.extraParam != null) {
            final boolean needClear = !builder.hasExtraParam();
            final int tmpFieldCnt = this.extraParam.copyChangeToCs(builder.getExtraParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExtraParam();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPEEDTIMESEC)) {
            builder.setSpeedTimeSec(this.getSpeedTimeSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREESPEEDCOUNT)) {
            builder.setFreeSpeedCount(this.getFreeSpeedCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISOPENCLANHELP)) {
            builder.setIsOpenClanHelp(this.getIsOpenClanHelp());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(QueueTaskPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_QUEUEEXPIRTIME)) {
            builder.setQueueExpirTime(this.getQueueExpirTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKID)) {
            builder.setTaskId(this.getTaskId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTIME)) {
            builder.setStarTime(this.getStarTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTIME)) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COSTRESOURCE) && this.costResource != null) {
            final boolean needClear = !builder.hasCostResource();
            final int tmpFieldCnt = this.costResource.copyChangeToCs(builder.getCostResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCostResource();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXTRAPARAM) && this.extraParam != null) {
            final boolean needClear = !builder.hasExtraParam();
            final int tmpFieldCnt = this.extraParam.copyChangeToAndClearDeleteKeysCs(builder.getExtraParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExtraParam();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPEEDTIMESEC)) {
            builder.setSpeedTimeSec(this.getSpeedTimeSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREESPEEDCOUNT)) {
            builder.setFreeSpeedCount(this.getFreeSpeedCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISOPENCLANHELP)) {
            builder.setIsOpenClanHelp(this.getIsOpenClanHelp());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(QueueTaskPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(QueueTaskType.forNumber(0));
        }
        if (proto.hasQueueExpirTime()) {
            this.innerSetQueueExpirTime(proto.getQueueExpirTime());
        } else {
            this.innerSetQueueExpirTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTaskId()) {
            this.innerSetTaskId(proto.getTaskId());
        } else {
            this.innerSetTaskId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStarTime()) {
            this.innerSetStarTime(proto.getStarTime());
        } else {
            this.innerSetStarTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTime()) {
            this.innerSetEndTime(proto.getEndTime());
        } else {
            this.innerSetEndTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCostResource()) {
            this.getCostResource().mergeFromCs(proto.getCostResource());
        } else {
            if (this.costResource != null) {
                this.costResource.mergeFromCs(proto.getCostResource());
            }
        }
        if (proto.hasExtraParam()) {
            this.getExtraParam().mergeFromCs(proto.getExtraParam());
        } else {
            if (this.extraParam != null) {
                this.extraParam.mergeFromCs(proto.getExtraParam());
            }
        }
        if (proto.hasSpeedTimeSec()) {
            this.innerSetSpeedTimeSec(proto.getSpeedTimeSec());
        } else {
            this.innerSetSpeedTimeSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFreeSpeedCount()) {
            this.innerSetFreeSpeedCount(proto.getFreeSpeedCount());
        } else {
            this.innerSetFreeSpeedCount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIsOpenClanHelp()) {
            this.innerSetIsOpenClanHelp(proto.getIsOpenClanHelp());
        } else {
            this.innerSetIsOpenClanHelp(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return QueueTaskProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(QueueTaskPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasQueueExpirTime()) {
            this.setQueueExpirTime(proto.getQueueExpirTime());
            fieldCnt++;
        }
        if (proto.hasTaskId()) {
            this.setTaskId(proto.getTaskId());
            fieldCnt++;
        }
        if (proto.hasStarTime()) {
            this.setStarTime(proto.getStarTime());
            fieldCnt++;
        }
        if (proto.hasEndTime()) {
            this.setEndTime(proto.getEndTime());
            fieldCnt++;
        }
        if (proto.hasCostResource()) {
            this.getCostResource().mergeChangeFromCs(proto.getCostResource());
            fieldCnt++;
        }
        if (proto.hasExtraParam()) {
            this.getExtraParam().mergeChangeFromCs(proto.getExtraParam());
            fieldCnt++;
        }
        if (proto.hasSpeedTimeSec()) {
            this.setSpeedTimeSec(proto.getSpeedTimeSec());
            fieldCnt++;
        }
        if (proto.hasFreeSpeedCount()) {
            this.setFreeSpeedCount(proto.getFreeSpeedCount());
            fieldCnt++;
        }
        if (proto.hasIsOpenClanHelp()) {
            this.setIsOpenClanHelp(proto.getIsOpenClanHelp());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public QueueTask.Builder getCopyDbBuilder() {
        final QueueTask.Builder builder = QueueTask.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(QueueTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getType() != QueueTaskType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getQueueExpirTime() != 0L) {
            builder.setQueueExpirTime(this.getQueueExpirTime());
            fieldCnt++;
        }  else if (builder.hasQueueExpirTime()) {
            // 清理QueueExpirTime
            builder.clearQueueExpirTime();
            fieldCnt++;
        }
        if (this.getTaskId() != 0L) {
            builder.setTaskId(this.getTaskId());
            fieldCnt++;
        }  else if (builder.hasTaskId()) {
            // 清理TaskId
            builder.clearTaskId();
            fieldCnt++;
        }
        if (this.getStarTime() != 0L) {
            builder.setStarTime(this.getStarTime());
            fieldCnt++;
        }  else if (builder.hasStarTime()) {
            // 清理StarTime
            builder.clearStarTime();
            fieldCnt++;
        }
        if (this.getEndTime() != 0L) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }  else if (builder.hasEndTime()) {
            // 清理EndTime
            builder.clearEndTime();
            fieldCnt++;
        }
        if (this.costResource != null) {
            Struct.CurrencyList.Builder tmpBuilder = Struct.CurrencyList.newBuilder();
            final int tmpFieldCnt = this.costResource.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCostResource(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCostResource();
            }
        }  else if (builder.hasCostResource()) {
            // 清理CostResource
            builder.clearCostResource();
            fieldCnt++;
        }
        if (this.extraParam != null) {
            StructPlayer.QueueExtraParam.Builder tmpBuilder = StructPlayer.QueueExtraParam.newBuilder();
            final int tmpFieldCnt = this.extraParam.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExtraParam(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExtraParam();
            }
        }  else if (builder.hasExtraParam()) {
            // 清理ExtraParam
            builder.clearExtraParam();
            fieldCnt++;
        }
        if (this.getSpeedTimeSec() != 0) {
            builder.setSpeedTimeSec(this.getSpeedTimeSec());
            fieldCnt++;
        }  else if (builder.hasSpeedTimeSec()) {
            // 清理SpeedTimeSec
            builder.clearSpeedTimeSec();
            fieldCnt++;
        }
        if (this.getFreeSpeedCount() != 0) {
            builder.setFreeSpeedCount(this.getFreeSpeedCount());
            fieldCnt++;
        }  else if (builder.hasFreeSpeedCount()) {
            // 清理FreeSpeedCount
            builder.clearFreeSpeedCount();
            fieldCnt++;
        }
        if (this.getIsOpenClanHelp()) {
            builder.setIsOpenClanHelp(this.getIsOpenClanHelp());
            fieldCnt++;
        }  else if (builder.hasIsOpenClanHelp()) {
            // 清理IsOpenClanHelp
            builder.clearIsOpenClanHelp();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(QueueTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_QUEUEEXPIRTIME)) {
            builder.setQueueExpirTime(this.getQueueExpirTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKID)) {
            builder.setTaskId(this.getTaskId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTIME)) {
            builder.setStarTime(this.getStarTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTIME)) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COSTRESOURCE) && this.costResource != null) {
            final boolean needClear = !builder.hasCostResource();
            final int tmpFieldCnt = this.costResource.copyChangeToDb(builder.getCostResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCostResource();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXTRAPARAM) && this.extraParam != null) {
            final boolean needClear = !builder.hasExtraParam();
            final int tmpFieldCnt = this.extraParam.copyChangeToDb(builder.getExtraParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExtraParam();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPEEDTIMESEC)) {
            builder.setSpeedTimeSec(this.getSpeedTimeSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREESPEEDCOUNT)) {
            builder.setFreeSpeedCount(this.getFreeSpeedCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISOPENCLANHELP)) {
            builder.setIsOpenClanHelp(this.getIsOpenClanHelp());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(QueueTask proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(QueueTaskType.forNumber(0));
        }
        if (proto.hasQueueExpirTime()) {
            this.innerSetQueueExpirTime(proto.getQueueExpirTime());
        } else {
            this.innerSetQueueExpirTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTaskId()) {
            this.innerSetTaskId(proto.getTaskId());
        } else {
            this.innerSetTaskId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStarTime()) {
            this.innerSetStarTime(proto.getStarTime());
        } else {
            this.innerSetStarTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTime()) {
            this.innerSetEndTime(proto.getEndTime());
        } else {
            this.innerSetEndTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCostResource()) {
            this.getCostResource().mergeFromDb(proto.getCostResource());
        } else {
            if (this.costResource != null) {
                this.costResource.mergeFromDb(proto.getCostResource());
            }
        }
        if (proto.hasExtraParam()) {
            this.getExtraParam().mergeFromDb(proto.getExtraParam());
        } else {
            if (this.extraParam != null) {
                this.extraParam.mergeFromDb(proto.getExtraParam());
            }
        }
        if (proto.hasSpeedTimeSec()) {
            this.innerSetSpeedTimeSec(proto.getSpeedTimeSec());
        } else {
            this.innerSetSpeedTimeSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFreeSpeedCount()) {
            this.innerSetFreeSpeedCount(proto.getFreeSpeedCount());
        } else {
            this.innerSetFreeSpeedCount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIsOpenClanHelp()) {
            this.innerSetIsOpenClanHelp(proto.getIsOpenClanHelp());
        } else {
            this.innerSetIsOpenClanHelp(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return QueueTaskProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(QueueTask proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasQueueExpirTime()) {
            this.setQueueExpirTime(proto.getQueueExpirTime());
            fieldCnt++;
        }
        if (proto.hasTaskId()) {
            this.setTaskId(proto.getTaskId());
            fieldCnt++;
        }
        if (proto.hasStarTime()) {
            this.setStarTime(proto.getStarTime());
            fieldCnt++;
        }
        if (proto.hasEndTime()) {
            this.setEndTime(proto.getEndTime());
            fieldCnt++;
        }
        if (proto.hasCostResource()) {
            this.getCostResource().mergeChangeFromDb(proto.getCostResource());
            fieldCnt++;
        }
        if (proto.hasExtraParam()) {
            this.getExtraParam().mergeChangeFromDb(proto.getExtraParam());
            fieldCnt++;
        }
        if (proto.hasSpeedTimeSec()) {
            this.setSpeedTimeSec(proto.getSpeedTimeSec());
            fieldCnt++;
        }
        if (proto.hasFreeSpeedCount()) {
            this.setFreeSpeedCount(proto.getFreeSpeedCount());
            fieldCnt++;
        }
        if (proto.hasIsOpenClanHelp()) {
            this.setIsOpenClanHelp(proto.getIsOpenClanHelp());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public QueueTask.Builder getCopySsBuilder() {
        final QueueTask.Builder builder = QueueTask.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(QueueTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getType() != QueueTaskType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getQueueExpirTime() != 0L) {
            builder.setQueueExpirTime(this.getQueueExpirTime());
            fieldCnt++;
        }  else if (builder.hasQueueExpirTime()) {
            // 清理QueueExpirTime
            builder.clearQueueExpirTime();
            fieldCnt++;
        }
        if (this.getTaskId() != 0L) {
            builder.setTaskId(this.getTaskId());
            fieldCnt++;
        }  else if (builder.hasTaskId()) {
            // 清理TaskId
            builder.clearTaskId();
            fieldCnt++;
        }
        if (this.getStarTime() != 0L) {
            builder.setStarTime(this.getStarTime());
            fieldCnt++;
        }  else if (builder.hasStarTime()) {
            // 清理StarTime
            builder.clearStarTime();
            fieldCnt++;
        }
        if (this.getEndTime() != 0L) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }  else if (builder.hasEndTime()) {
            // 清理EndTime
            builder.clearEndTime();
            fieldCnt++;
        }
        if (this.costResource != null) {
            Struct.CurrencyList.Builder tmpBuilder = Struct.CurrencyList.newBuilder();
            final int tmpFieldCnt = this.costResource.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCostResource(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCostResource();
            }
        }  else if (builder.hasCostResource()) {
            // 清理CostResource
            builder.clearCostResource();
            fieldCnt++;
        }
        if (this.extraParam != null) {
            StructPlayer.QueueExtraParam.Builder tmpBuilder = StructPlayer.QueueExtraParam.newBuilder();
            final int tmpFieldCnt = this.extraParam.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExtraParam(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExtraParam();
            }
        }  else if (builder.hasExtraParam()) {
            // 清理ExtraParam
            builder.clearExtraParam();
            fieldCnt++;
        }
        if (this.getSpeedTimeSec() != 0) {
            builder.setSpeedTimeSec(this.getSpeedTimeSec());
            fieldCnt++;
        }  else if (builder.hasSpeedTimeSec()) {
            // 清理SpeedTimeSec
            builder.clearSpeedTimeSec();
            fieldCnt++;
        }
        if (this.getFreeSpeedCount() != 0) {
            builder.setFreeSpeedCount(this.getFreeSpeedCount());
            fieldCnt++;
        }  else if (builder.hasFreeSpeedCount()) {
            // 清理FreeSpeedCount
            builder.clearFreeSpeedCount();
            fieldCnt++;
        }
        if (this.getIsOpenClanHelp()) {
            builder.setIsOpenClanHelp(this.getIsOpenClanHelp());
            fieldCnt++;
        }  else if (builder.hasIsOpenClanHelp()) {
            // 清理IsOpenClanHelp
            builder.clearIsOpenClanHelp();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(QueueTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_QUEUEEXPIRTIME)) {
            builder.setQueueExpirTime(this.getQueueExpirTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKID)) {
            builder.setTaskId(this.getTaskId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTIME)) {
            builder.setStarTime(this.getStarTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTIME)) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COSTRESOURCE) && this.costResource != null) {
            final boolean needClear = !builder.hasCostResource();
            final int tmpFieldCnt = this.costResource.copyChangeToSs(builder.getCostResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCostResource();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXTRAPARAM) && this.extraParam != null) {
            final boolean needClear = !builder.hasExtraParam();
            final int tmpFieldCnt = this.extraParam.copyChangeToSs(builder.getExtraParamBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExtraParam();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPEEDTIMESEC)) {
            builder.setSpeedTimeSec(this.getSpeedTimeSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREESPEEDCOUNT)) {
            builder.setFreeSpeedCount(this.getFreeSpeedCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISOPENCLANHELP)) {
            builder.setIsOpenClanHelp(this.getIsOpenClanHelp());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(QueueTask proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(QueueTaskType.forNumber(0));
        }
        if (proto.hasQueueExpirTime()) {
            this.innerSetQueueExpirTime(proto.getQueueExpirTime());
        } else {
            this.innerSetQueueExpirTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTaskId()) {
            this.innerSetTaskId(proto.getTaskId());
        } else {
            this.innerSetTaskId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStarTime()) {
            this.innerSetStarTime(proto.getStarTime());
        } else {
            this.innerSetStarTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTime()) {
            this.innerSetEndTime(proto.getEndTime());
        } else {
            this.innerSetEndTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCostResource()) {
            this.getCostResource().mergeFromSs(proto.getCostResource());
        } else {
            if (this.costResource != null) {
                this.costResource.mergeFromSs(proto.getCostResource());
            }
        }
        if (proto.hasExtraParam()) {
            this.getExtraParam().mergeFromSs(proto.getExtraParam());
        } else {
            if (this.extraParam != null) {
                this.extraParam.mergeFromSs(proto.getExtraParam());
            }
        }
        if (proto.hasSpeedTimeSec()) {
            this.innerSetSpeedTimeSec(proto.getSpeedTimeSec());
        } else {
            this.innerSetSpeedTimeSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFreeSpeedCount()) {
            this.innerSetFreeSpeedCount(proto.getFreeSpeedCount());
        } else {
            this.innerSetFreeSpeedCount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIsOpenClanHelp()) {
            this.innerSetIsOpenClanHelp(proto.getIsOpenClanHelp());
        } else {
            this.innerSetIsOpenClanHelp(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return QueueTaskProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(QueueTask proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasQueueExpirTime()) {
            this.setQueueExpirTime(proto.getQueueExpirTime());
            fieldCnt++;
        }
        if (proto.hasTaskId()) {
            this.setTaskId(proto.getTaskId());
            fieldCnt++;
        }
        if (proto.hasStarTime()) {
            this.setStarTime(proto.getStarTime());
            fieldCnt++;
        }
        if (proto.hasEndTime()) {
            this.setEndTime(proto.getEndTime());
            fieldCnt++;
        }
        if (proto.hasCostResource()) {
            this.getCostResource().mergeChangeFromSs(proto.getCostResource());
            fieldCnt++;
        }
        if (proto.hasExtraParam()) {
            this.getExtraParam().mergeChangeFromSs(proto.getExtraParam());
            fieldCnt++;
        }
        if (proto.hasSpeedTimeSec()) {
            this.setSpeedTimeSec(proto.getSpeedTimeSec());
            fieldCnt++;
        }
        if (proto.hasFreeSpeedCount()) {
            this.setFreeSpeedCount(proto.getFreeSpeedCount());
            fieldCnt++;
        }
        if (proto.hasIsOpenClanHelp()) {
            this.setIsOpenClanHelp(proto.getIsOpenClanHelp());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        QueueTask.Builder builder = QueueTask.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_COSTRESOURCE) && this.costResource != null) {
            this.costResource.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_EXTRAPARAM) && this.extraParam != null) {
            this.extraParam.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.costResource != null) {
            this.costResource.markAll();
        }
        if (this.extraParam != null) {
            this.extraParam.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof QueueTaskProp)) {
            return false;
        }
        final QueueTaskProp otherNode = (QueueTaskProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.type != otherNode.type) {
            return false;
        }
        if (this.queueExpirTime != otherNode.queueExpirTime) {
            return false;
        }
        if (this.taskId != otherNode.taskId) {
            return false;
        }
        if (this.starTime != otherNode.starTime) {
            return false;
        }
        if (this.endTime != otherNode.endTime) {
            return false;
        }
        if (!this.getCostResource().compareDataTo(otherNode.getCostResource())) {
            return false;
        }
        if (!this.getExtraParam().compareDataTo(otherNode.getExtraParam())) {
            return false;
        }
        if (this.speedTimeSec != otherNode.speedTimeSec) {
            return false;
        }
        if (this.freeSpeedCount != otherNode.freeSpeedCount) {
            return false;
        }
        if (this.isOpenClanHelp != otherNode.isOpenClanHelp) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 53;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
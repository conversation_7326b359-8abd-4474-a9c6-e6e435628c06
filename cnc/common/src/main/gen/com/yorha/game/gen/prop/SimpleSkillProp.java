package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.SimpleSkill;
import com.yorha.proto.StructPB.SimpleSkillPB;


/**
 * <AUTHOR> auto gen
 */
public class SimpleSkillProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_SKILLGROUPID = 0;
    public static final int FIELD_INDEX_LEVEL = 1;
    public static final int FIELD_INDEX_STAR = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int skillGroupId = Constant.DEFAULT_INT_VALUE;
    private int level = Constant.DEFAULT_INT_VALUE;
    private int star = Constant.DEFAULT_INT_VALUE;

    public SimpleSkillProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SimpleSkillProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get skillGroupId
     *
     * @return skillGroupId value
     */
    public int getSkillGroupId() {
        return this.skillGroupId;
    }

    /**
     * set skillGroupId && set marked
     *
     * @param skillGroupId new value
     * @return current object
     */
    public SimpleSkillProp setSkillGroupId(int skillGroupId) {
        if (this.skillGroupId != skillGroupId) {
            this.mark(FIELD_INDEX_SKILLGROUPID);
            this.skillGroupId = skillGroupId;
        }
        return this;
    }

    /**
     * inner set skillGroupId
     *
     * @param skillGroupId new value
     */
    private void innerSetSkillGroupId(int skillGroupId) {
        this.skillGroupId = skillGroupId;
    }

    /**
     * get level
     *
     * @return level value
     */
    public int getLevel() {
        return this.level;
    }

    /**
     * set level && set marked
     *
     * @param level new value
     * @return current object
     */
    public SimpleSkillProp setLevel(int level) {
        if (this.level != level) {
            this.mark(FIELD_INDEX_LEVEL);
            this.level = level;
        }
        return this;
    }

    /**
     * inner set level
     *
     * @param level new value
     */
    private void innerSetLevel(int level) {
        this.level = level;
    }

    /**
     * get star
     *
     * @return star value
     */
    public int getStar() {
        return this.star;
    }

    /**
     * set star && set marked
     *
     * @param star new value
     * @return current object
     */
    public SimpleSkillProp setStar(int star) {
        if (this.star != star) {
            this.mark(FIELD_INDEX_STAR);
            this.star = star;
        }
        return this;
    }

    /**
     * inner set star
     *
     * @param star new value
     */
    private void innerSetStar(int star) {
        this.star = star;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SimpleSkillPB.Builder getCopyCsBuilder() {
        final SimpleSkillPB.Builder builder = SimpleSkillPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SimpleSkillPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSkillGroupId() != 0) {
            builder.setSkillGroupId(this.getSkillGroupId());
            fieldCnt++;
        }  else if (builder.hasSkillGroupId()) {
            // 清理SkillGroupId
            builder.clearSkillGroupId();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getStar() != 0) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }  else if (builder.hasStar()) {
            // 清理Star
            builder.clearStar();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SimpleSkillPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLGROUPID)) {
            builder.setSkillGroupId(this.getSkillGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SimpleSkillPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLGROUPID)) {
            builder.setSkillGroupId(this.getSkillGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SimpleSkillPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkillGroupId()) {
            this.innerSetSkillGroupId(proto.getSkillGroupId());
        } else {
            this.innerSetSkillGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStar()) {
            this.innerSetStar(proto.getStar());
        } else {
            this.innerSetStar(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SimpleSkillProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SimpleSkillPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkillGroupId()) {
            this.setSkillGroupId(proto.getSkillGroupId());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasStar()) {
            this.setStar(proto.getStar());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SimpleSkill.Builder getCopyDbBuilder() {
        final SimpleSkill.Builder builder = SimpleSkill.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SimpleSkill.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSkillGroupId() != 0) {
            builder.setSkillGroupId(this.getSkillGroupId());
            fieldCnt++;
        }  else if (builder.hasSkillGroupId()) {
            // 清理SkillGroupId
            builder.clearSkillGroupId();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getStar() != 0) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }  else if (builder.hasStar()) {
            // 清理Star
            builder.clearStar();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SimpleSkill.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLGROUPID)) {
            builder.setSkillGroupId(this.getSkillGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SimpleSkill proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkillGroupId()) {
            this.innerSetSkillGroupId(proto.getSkillGroupId());
        } else {
            this.innerSetSkillGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStar()) {
            this.innerSetStar(proto.getStar());
        } else {
            this.innerSetStar(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SimpleSkillProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SimpleSkill proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkillGroupId()) {
            this.setSkillGroupId(proto.getSkillGroupId());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasStar()) {
            this.setStar(proto.getStar());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SimpleSkill.Builder getCopySsBuilder() {
        final SimpleSkill.Builder builder = SimpleSkill.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SimpleSkill.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSkillGroupId() != 0) {
            builder.setSkillGroupId(this.getSkillGroupId());
            fieldCnt++;
        }  else if (builder.hasSkillGroupId()) {
            // 清理SkillGroupId
            builder.clearSkillGroupId();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getStar() != 0) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }  else if (builder.hasStar()) {
            // 清理Star
            builder.clearStar();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SimpleSkill.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLGROUPID)) {
            builder.setSkillGroupId(this.getSkillGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SimpleSkill proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkillGroupId()) {
            this.innerSetSkillGroupId(proto.getSkillGroupId());
        } else {
            this.innerSetSkillGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStar()) {
            this.innerSetStar(proto.getStar());
        } else {
            this.innerSetStar(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SimpleSkillProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SimpleSkill proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkillGroupId()) {
            this.setSkillGroupId(proto.getSkillGroupId());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasStar()) {
            this.setStar(proto.getStar());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SimpleSkill.Builder builder = SimpleSkill.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.skillGroupId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SimpleSkillProp)) {
            return false;
        }
        final SimpleSkillProp otherNode = (SimpleSkillProp) node;
        if (this.skillGroupId != otherNode.skillGroupId) {
            return false;
        }
        if (this.level != otherNode.level) {
            return false;
        }
        if (this.star != otherNode.star) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
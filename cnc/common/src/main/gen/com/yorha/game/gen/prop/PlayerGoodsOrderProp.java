package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerGoodsOrder;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.PlayerGoodsOrderPB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerGoodsOrderProp extends AbstractContainerElementNode<String> {

    public static final int FIELD_INDEX_TOKEN = 0;
    public static final int FIELD_INDEX_GOODSID = 1;
    public static final int FIELD_INDEX_TSSEC = 2;
    public static final int FIELD_INDEX_EXTINFO = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private String token = Constant.DEFAULT_STR_VALUE;
    private int goodsId = Constant.DEFAULT_INT_VALUE;
    private int tsSec = Constant.DEFAULT_INT_VALUE;
    private GoodsOrderExtInfoProp extInfo = null;

    public PlayerGoodsOrderProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerGoodsOrderProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get token
     *
     * @return token value
     */
    public String getToken() {
        return this.token;
    }

    /**
     * set token && set marked
     *
     * @param token new value
     * @return current object
     */
    public PlayerGoodsOrderProp setToken(String token) {
        if (token == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.token, token)) {
            this.mark(FIELD_INDEX_TOKEN);
            this.token = token;
        }
        return this;
    }

    /**
     * inner set token
     *
     * @param token new value
     */
    private void innerSetToken(String token) {
        this.token = token;
    }

    /**
     * get goodsId
     *
     * @return goodsId value
     */
    public int getGoodsId() {
        return this.goodsId;
    }

    /**
     * set goodsId && set marked
     *
     * @param goodsId new value
     * @return current object
     */
    public PlayerGoodsOrderProp setGoodsId(int goodsId) {
        if (this.goodsId != goodsId) {
            this.mark(FIELD_INDEX_GOODSID);
            this.goodsId = goodsId;
        }
        return this;
    }

    /**
     * inner set goodsId
     *
     * @param goodsId new value
     */
    private void innerSetGoodsId(int goodsId) {
        this.goodsId = goodsId;
    }

    /**
     * get tsSec
     *
     * @return tsSec value
     */
    public int getTsSec() {
        return this.tsSec;
    }

    /**
     * set tsSec && set marked
     *
     * @param tsSec new value
     * @return current object
     */
    public PlayerGoodsOrderProp setTsSec(int tsSec) {
        if (this.tsSec != tsSec) {
            this.mark(FIELD_INDEX_TSSEC);
            this.tsSec = tsSec;
        }
        return this;
    }

    /**
     * inner set tsSec
     *
     * @param tsSec new value
     */
    private void innerSetTsSec(int tsSec) {
        this.tsSec = tsSec;
    }

    /**
     * get extInfo
     *
     * @return extInfo value
     */
    public GoodsOrderExtInfoProp getExtInfo() {
        if (this.extInfo == null) {
            this.extInfo = new GoodsOrderExtInfoProp(this, FIELD_INDEX_EXTINFO);
        }
        return this.extInfo;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerGoodsOrderPB.Builder getCopyCsBuilder() {
        final PlayerGoodsOrderPB.Builder builder = PlayerGoodsOrderPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerGoodsOrderPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getToken().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setToken(this.getToken());
            fieldCnt++;
        }  else if (builder.hasToken()) {
            // 清理Token
            builder.clearToken();
            fieldCnt++;
        }
        if (this.getGoodsId() != 0) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }  else if (builder.hasGoodsId()) {
            // 清理GoodsId
            builder.clearGoodsId();
            fieldCnt++;
        }
        if (this.getTsSec() != 0) {
            builder.setTsSec(this.getTsSec());
            fieldCnt++;
        }  else if (builder.hasTsSec()) {
            // 清理TsSec
            builder.clearTsSec();
            fieldCnt++;
        }
        if (this.extInfo != null) {
            PlayerPB.GoodsOrderExtInfoPB.Builder tmpBuilder = PlayerPB.GoodsOrderExtInfoPB.newBuilder();
            final int tmpFieldCnt = this.extInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExtInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExtInfo();
            }
        }  else if (builder.hasExtInfo()) {
            // 清理ExtInfo
            builder.clearExtInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerGoodsOrderPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOKEN)) {
            builder.setToken(this.getToken());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GOODSID)) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TSSEC)) {
            builder.setTsSec(this.getTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXTINFO) && this.extInfo != null) {
            final boolean needClear = !builder.hasExtInfo();
            final int tmpFieldCnt = this.extInfo.copyChangeToCs(builder.getExtInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExtInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerGoodsOrderPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOKEN)) {
            builder.setToken(this.getToken());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GOODSID)) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TSSEC)) {
            builder.setTsSec(this.getTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXTINFO) && this.extInfo != null) {
            final boolean needClear = !builder.hasExtInfo();
            final int tmpFieldCnt = this.extInfo.copyChangeToAndClearDeleteKeysCs(builder.getExtInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExtInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerGoodsOrderPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasToken()) {
            this.innerSetToken(proto.getToken());
        } else {
            this.innerSetToken(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasGoodsId()) {
            this.innerSetGoodsId(proto.getGoodsId());
        } else {
            this.innerSetGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTsSec()) {
            this.innerSetTsSec(proto.getTsSec());
        } else {
            this.innerSetTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExtInfo()) {
            this.getExtInfo().mergeFromCs(proto.getExtInfo());
        } else {
            if (this.extInfo != null) {
                this.extInfo.mergeFromCs(proto.getExtInfo());
            }
        }
        this.markAll();
        return PlayerGoodsOrderProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerGoodsOrderPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasToken()) {
            this.setToken(proto.getToken());
            fieldCnt++;
        }
        if (proto.hasGoodsId()) {
            this.setGoodsId(proto.getGoodsId());
            fieldCnt++;
        }
        if (proto.hasTsSec()) {
            this.setTsSec(proto.getTsSec());
            fieldCnt++;
        }
        if (proto.hasExtInfo()) {
            this.getExtInfo().mergeChangeFromCs(proto.getExtInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerGoodsOrder.Builder getCopyDbBuilder() {
        final PlayerGoodsOrder.Builder builder = PlayerGoodsOrder.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerGoodsOrder.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getToken().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setToken(this.getToken());
            fieldCnt++;
        }  else if (builder.hasToken()) {
            // 清理Token
            builder.clearToken();
            fieldCnt++;
        }
        if (this.getGoodsId() != 0) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }  else if (builder.hasGoodsId()) {
            // 清理GoodsId
            builder.clearGoodsId();
            fieldCnt++;
        }
        if (this.getTsSec() != 0) {
            builder.setTsSec(this.getTsSec());
            fieldCnt++;
        }  else if (builder.hasTsSec()) {
            // 清理TsSec
            builder.clearTsSec();
            fieldCnt++;
        }
        if (this.extInfo != null) {
            Player.GoodsOrderExtInfo.Builder tmpBuilder = Player.GoodsOrderExtInfo.newBuilder();
            final int tmpFieldCnt = this.extInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExtInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExtInfo();
            }
        }  else if (builder.hasExtInfo()) {
            // 清理ExtInfo
            builder.clearExtInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerGoodsOrder.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOKEN)) {
            builder.setToken(this.getToken());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GOODSID)) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TSSEC)) {
            builder.setTsSec(this.getTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXTINFO) && this.extInfo != null) {
            final boolean needClear = !builder.hasExtInfo();
            final int tmpFieldCnt = this.extInfo.copyChangeToDb(builder.getExtInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExtInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerGoodsOrder proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasToken()) {
            this.innerSetToken(proto.getToken());
        } else {
            this.innerSetToken(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasGoodsId()) {
            this.innerSetGoodsId(proto.getGoodsId());
        } else {
            this.innerSetGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTsSec()) {
            this.innerSetTsSec(proto.getTsSec());
        } else {
            this.innerSetTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExtInfo()) {
            this.getExtInfo().mergeFromDb(proto.getExtInfo());
        } else {
            if (this.extInfo != null) {
                this.extInfo.mergeFromDb(proto.getExtInfo());
            }
        }
        this.markAll();
        return PlayerGoodsOrderProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerGoodsOrder proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasToken()) {
            this.setToken(proto.getToken());
            fieldCnt++;
        }
        if (proto.hasGoodsId()) {
            this.setGoodsId(proto.getGoodsId());
            fieldCnt++;
        }
        if (proto.hasTsSec()) {
            this.setTsSec(proto.getTsSec());
            fieldCnt++;
        }
        if (proto.hasExtInfo()) {
            this.getExtInfo().mergeChangeFromDb(proto.getExtInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerGoodsOrder.Builder getCopySsBuilder() {
        final PlayerGoodsOrder.Builder builder = PlayerGoodsOrder.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerGoodsOrder.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getToken().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setToken(this.getToken());
            fieldCnt++;
        }  else if (builder.hasToken()) {
            // 清理Token
            builder.clearToken();
            fieldCnt++;
        }
        if (this.getGoodsId() != 0) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }  else if (builder.hasGoodsId()) {
            // 清理GoodsId
            builder.clearGoodsId();
            fieldCnt++;
        }
        if (this.getTsSec() != 0) {
            builder.setTsSec(this.getTsSec());
            fieldCnt++;
        }  else if (builder.hasTsSec()) {
            // 清理TsSec
            builder.clearTsSec();
            fieldCnt++;
        }
        if (this.extInfo != null) {
            Player.GoodsOrderExtInfo.Builder tmpBuilder = Player.GoodsOrderExtInfo.newBuilder();
            final int tmpFieldCnt = this.extInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExtInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExtInfo();
            }
        }  else if (builder.hasExtInfo()) {
            // 清理ExtInfo
            builder.clearExtInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerGoodsOrder.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOKEN)) {
            builder.setToken(this.getToken());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GOODSID)) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TSSEC)) {
            builder.setTsSec(this.getTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXTINFO) && this.extInfo != null) {
            final boolean needClear = !builder.hasExtInfo();
            final int tmpFieldCnt = this.extInfo.copyChangeToSs(builder.getExtInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExtInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerGoodsOrder proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasToken()) {
            this.innerSetToken(proto.getToken());
        } else {
            this.innerSetToken(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasGoodsId()) {
            this.innerSetGoodsId(proto.getGoodsId());
        } else {
            this.innerSetGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTsSec()) {
            this.innerSetTsSec(proto.getTsSec());
        } else {
            this.innerSetTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExtInfo()) {
            this.getExtInfo().mergeFromSs(proto.getExtInfo());
        } else {
            if (this.extInfo != null) {
                this.extInfo.mergeFromSs(proto.getExtInfo());
            }
        }
        this.markAll();
        return PlayerGoodsOrderProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerGoodsOrder proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasToken()) {
            this.setToken(proto.getToken());
            fieldCnt++;
        }
        if (proto.hasGoodsId()) {
            this.setGoodsId(proto.getGoodsId());
            fieldCnt++;
        }
        if (proto.hasTsSec()) {
            this.setTsSec(proto.getTsSec());
            fieldCnt++;
        }
        if (proto.hasExtInfo()) {
            this.getExtInfo().mergeChangeFromSs(proto.getExtInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerGoodsOrder.Builder builder = PlayerGoodsOrder.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_EXTINFO) && this.extInfo != null) {
            this.extInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.extInfo != null) {
            this.extInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public String getPrivateKey() {
        return this.token;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerGoodsOrderProp)) {
            return false;
        }
        final PlayerGoodsOrderProp otherNode = (PlayerGoodsOrderProp) node;
        if (!com.yorha.gemini.utils.StringUtils.equals(this.token, otherNode.token)) {
            return false;
        }
        if (this.goodsId != otherNode.goodsId) {
            return false;
        }
        if (this.tsSec != otherNode.tsSec) {
            return false;
        }
        if (!this.getExtInfo().compareDataTo(otherNode.getExtInfo())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
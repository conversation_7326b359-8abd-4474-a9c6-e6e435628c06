package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.Player.FriendPlayer;
import com.yorha.proto.Player;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.FriendPlayerPB;
import com.yorha.proto.PlayerPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class FriendPlayerProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CARDHEAD = 0;
    public static final int FIELD_INDEX_FRIENDLIST = 1;
    public static final int FIELD_INDEX_APPLYLIST = 2;
    public static final int FIELD_INDEX_SHILEDLIST = 3;
    public static final int FIELD_INDEX_WAITLIST = 4;
    public static final int FIELD_INDEX_BESHIELDLIST = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;
    private PropertyChangeListener listener;

    private PlayerCardHeadProp cardHead = null;
    private Int64SimpleFriendInfoMapProp friendList = null;
    private Int64FriendApplyInfoMapProp applyList = null;
    private Int64ShieldPlayerInfoMapProp shiledList = null;
    private Int64WaitResponseInfoMapProp waitList = null;
    private Int64PlayerBeShieldInfoMapProp beShieldList = null;

    public FriendPlayerProp() {
        super(null, 0, FIELD_COUNT);
    }

    public FriendPlayerProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }

    /**
     * get friendList
     *
     * @return friendList value
     */
    public Int64SimpleFriendInfoMapProp getFriendList() {
        if (this.friendList == null) {
            this.friendList = new Int64SimpleFriendInfoMapProp(this, FIELD_INDEX_FRIENDLIST);
        }
        return this.friendList;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putFriendListV(SimpleFriendInfoProp v) {
        this.getFriendList().put(v.getPlayerId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public SimpleFriendInfoProp addEmptyFriendList(Long k) {
        return this.getFriendList().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getFriendListSize() {
        if (this.friendList == null) {
            return 0;
        }
        return this.friendList.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isFriendListEmpty() {
        if (this.friendList == null) {
            return true;
        }
        return this.friendList.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public SimpleFriendInfoProp getFriendListV(Long k) {
        if (this.friendList == null || !this.friendList.containsKey(k)) {
            return null;
        }
        return this.friendList.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearFriendList() {
        if (this.friendList != null) {
            this.friendList.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeFriendListV(Long k) {
        if (this.friendList != null) {
            this.friendList.remove(k);
        }
    }
    /**
     * get applyList
     *
     * @return applyList value
     */
    public Int64FriendApplyInfoMapProp getApplyList() {
        if (this.applyList == null) {
            this.applyList = new Int64FriendApplyInfoMapProp(this, FIELD_INDEX_APPLYLIST);
        }
        return this.applyList;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putApplyListV(FriendApplyInfoProp v) {
        this.getApplyList().put(v.getPlayerId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public FriendApplyInfoProp addEmptyApplyList(Long k) {
        return this.getApplyList().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getApplyListSize() {
        if (this.applyList == null) {
            return 0;
        }
        return this.applyList.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isApplyListEmpty() {
        if (this.applyList == null) {
            return true;
        }
        return this.applyList.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public FriendApplyInfoProp getApplyListV(Long k) {
        if (this.applyList == null || !this.applyList.containsKey(k)) {
            return null;
        }
        return this.applyList.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearApplyList() {
        if (this.applyList != null) {
            this.applyList.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeApplyListV(Long k) {
        if (this.applyList != null) {
            this.applyList.remove(k);
        }
    }
    /**
     * get shiledList
     *
     * @return shiledList value
     */
    public Int64ShieldPlayerInfoMapProp getShiledList() {
        if (this.shiledList == null) {
            this.shiledList = new Int64ShieldPlayerInfoMapProp(this, FIELD_INDEX_SHILEDLIST);
        }
        return this.shiledList;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putShiledListV(ShieldPlayerInfoProp v) {
        this.getShiledList().put(v.getPlayerId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ShieldPlayerInfoProp addEmptyShiledList(Long k) {
        return this.getShiledList().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getShiledListSize() {
        if (this.shiledList == null) {
            return 0;
        }
        return this.shiledList.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isShiledListEmpty() {
        if (this.shiledList == null) {
            return true;
        }
        return this.shiledList.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ShieldPlayerInfoProp getShiledListV(Long k) {
        if (this.shiledList == null || !this.shiledList.containsKey(k)) {
            return null;
        }
        return this.shiledList.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearShiledList() {
        if (this.shiledList != null) {
            this.shiledList.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeShiledListV(Long k) {
        if (this.shiledList != null) {
            this.shiledList.remove(k);
        }
    }
    /**
     * get waitList
     *
     * @return waitList value
     */
    public Int64WaitResponseInfoMapProp getWaitList() {
        if (this.waitList == null) {
            this.waitList = new Int64WaitResponseInfoMapProp(this, FIELD_INDEX_WAITLIST);
        }
        return this.waitList;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putWaitListV(WaitResponseInfoProp v) {
        this.getWaitList().put(v.getPlayerId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public WaitResponseInfoProp addEmptyWaitList(Long k) {
        return this.getWaitList().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getWaitListSize() {
        if (this.waitList == null) {
            return 0;
        }
        return this.waitList.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isWaitListEmpty() {
        if (this.waitList == null) {
            return true;
        }
        return this.waitList.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public WaitResponseInfoProp getWaitListV(Long k) {
        if (this.waitList == null || !this.waitList.containsKey(k)) {
            return null;
        }
        return this.waitList.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearWaitList() {
        if (this.waitList != null) {
            this.waitList.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeWaitListV(Long k) {
        if (this.waitList != null) {
            this.waitList.remove(k);
        }
    }
    /**
     * get beShieldList
     *
     * @return beShieldList value
     */
    public Int64PlayerBeShieldInfoMapProp getBeShieldList() {
        if (this.beShieldList == null) {
            this.beShieldList = new Int64PlayerBeShieldInfoMapProp(this, FIELD_INDEX_BESHIELDLIST);
        }
        return this.beShieldList;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putBeShieldListV(PlayerBeShieldInfoProp v) {
        this.getBeShieldList().put(v.getPlayerId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerBeShieldInfoProp addEmptyBeShieldList(Long k) {
        return this.getBeShieldList().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getBeShieldListSize() {
        if (this.beShieldList == null) {
            return 0;
        }
        return this.beShieldList.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isBeShieldListEmpty() {
        if (this.beShieldList == null) {
            return true;
        }
        return this.beShieldList.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerBeShieldInfoProp getBeShieldListV(Long k) {
        if (this.beShieldList == null || !this.beShieldList.containsKey(k)) {
            return null;
        }
        return this.beShieldList.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearBeShieldList() {
        if (this.beShieldList != null) {
            this.beShieldList.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeBeShieldListV(Long k) {
        if (this.beShieldList != null) {
            this.beShieldList.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public FriendPlayerPB.Builder getCopyCsBuilder() {
        final FriendPlayerPB.Builder builder = FriendPlayerPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(FriendPlayerPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.friendList != null) {
            PlayerPB.Int64SimpleFriendInfoMapPB.Builder tmpBuilder = PlayerPB.Int64SimpleFriendInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.friendList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFriendList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFriendList();
            }
        }  else if (builder.hasFriendList()) {
            // 清理FriendList
            builder.clearFriendList();
            fieldCnt++;
        }
        if (this.applyList != null) {
            PlayerPB.Int64FriendApplyInfoMapPB.Builder tmpBuilder = PlayerPB.Int64FriendApplyInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.applyList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setApplyList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearApplyList();
            }
        }  else if (builder.hasApplyList()) {
            // 清理ApplyList
            builder.clearApplyList();
            fieldCnt++;
        }
        if (this.shiledList != null) {
            PlayerPB.Int64ShieldPlayerInfoMapPB.Builder tmpBuilder = PlayerPB.Int64ShieldPlayerInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.shiledList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setShiledList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearShiledList();
            }
        }  else if (builder.hasShiledList()) {
            // 清理ShiledList
            builder.clearShiledList();
            fieldCnt++;
        }
        if (this.waitList != null) {
            PlayerPB.Int64WaitResponseInfoMapPB.Builder tmpBuilder = PlayerPB.Int64WaitResponseInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.waitList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWaitList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWaitList();
            }
        }  else if (builder.hasWaitList()) {
            // 清理WaitList
            builder.clearWaitList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(FriendPlayerPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FRIENDLIST) && this.friendList != null) {
            final boolean needClear = !builder.hasFriendList();
            final int tmpFieldCnt = this.friendList.copyChangeToCs(builder.getFriendListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFriendList();
            }
        }
        if (this.hasMark(FIELD_INDEX_APPLYLIST) && this.applyList != null) {
            final boolean needClear = !builder.hasApplyList();
            final int tmpFieldCnt = this.applyList.copyChangeToCs(builder.getApplyListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearApplyList();
            }
        }
        if (this.hasMark(FIELD_INDEX_SHILEDLIST) && this.shiledList != null) {
            final boolean needClear = !builder.hasShiledList();
            final int tmpFieldCnt = this.shiledList.copyChangeToCs(builder.getShiledListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearShiledList();
            }
        }
        if (this.hasMark(FIELD_INDEX_WAITLIST) && this.waitList != null) {
            final boolean needClear = !builder.hasWaitList();
            final int tmpFieldCnt = this.waitList.copyChangeToCs(builder.getWaitListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWaitList();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(FriendPlayerPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FRIENDLIST) && this.friendList != null) {
            final boolean needClear = !builder.hasFriendList();
            final int tmpFieldCnt = this.friendList.copyChangeToAndClearDeleteKeysCs(builder.getFriendListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFriendList();
            }
        }
        if (this.hasMark(FIELD_INDEX_APPLYLIST) && this.applyList != null) {
            final boolean needClear = !builder.hasApplyList();
            final int tmpFieldCnt = this.applyList.copyChangeToAndClearDeleteKeysCs(builder.getApplyListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearApplyList();
            }
        }
        if (this.hasMark(FIELD_INDEX_SHILEDLIST) && this.shiledList != null) {
            final boolean needClear = !builder.hasShiledList();
            final int tmpFieldCnt = this.shiledList.copyChangeToAndClearDeleteKeysCs(builder.getShiledListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearShiledList();
            }
        }
        if (this.hasMark(FIELD_INDEX_WAITLIST) && this.waitList != null) {
            final boolean needClear = !builder.hasWaitList();
            final int tmpFieldCnt = this.waitList.copyChangeToAndClearDeleteKeysCs(builder.getWaitListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWaitList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(FriendPlayerPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasFriendList()) {
            this.getFriendList().mergeFromCs(proto.getFriendList());
        } else {
            if (this.friendList != null) {
                this.friendList.mergeFromCs(proto.getFriendList());
            }
        }
        if (proto.hasApplyList()) {
            this.getApplyList().mergeFromCs(proto.getApplyList());
        } else {
            if (this.applyList != null) {
                this.applyList.mergeFromCs(proto.getApplyList());
            }
        }
        if (proto.hasShiledList()) {
            this.getShiledList().mergeFromCs(proto.getShiledList());
        } else {
            if (this.shiledList != null) {
                this.shiledList.mergeFromCs(proto.getShiledList());
            }
        }
        if (proto.hasWaitList()) {
            this.getWaitList().mergeFromCs(proto.getWaitList());
        } else {
            if (this.waitList != null) {
                this.waitList.mergeFromCs(proto.getWaitList());
            }
        }
        this.markAll();
        return FriendPlayerProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(FriendPlayerPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasFriendList()) {
            this.getFriendList().mergeChangeFromCs(proto.getFriendList());
            fieldCnt++;
        }
        if (proto.hasApplyList()) {
            this.getApplyList().mergeChangeFromCs(proto.getApplyList());
            fieldCnt++;
        }
        if (proto.hasShiledList()) {
            this.getShiledList().mergeChangeFromCs(proto.getShiledList());
            fieldCnt++;
        }
        if (proto.hasWaitList()) {
            this.getWaitList().mergeChangeFromCs(proto.getWaitList());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public FriendPlayer.Builder getCopyDbBuilder() {
        final FriendPlayer.Builder builder = FriendPlayer.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(FriendPlayer.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.friendList != null) {
            Player.Int64SimpleFriendInfoMap.Builder tmpBuilder = Player.Int64SimpleFriendInfoMap.newBuilder();
            final int tmpFieldCnt = this.friendList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFriendList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFriendList();
            }
        }  else if (builder.hasFriendList()) {
            // 清理FriendList
            builder.clearFriendList();
            fieldCnt++;
        }
        if (this.applyList != null) {
            Player.Int64FriendApplyInfoMap.Builder tmpBuilder = Player.Int64FriendApplyInfoMap.newBuilder();
            final int tmpFieldCnt = this.applyList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setApplyList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearApplyList();
            }
        }  else if (builder.hasApplyList()) {
            // 清理ApplyList
            builder.clearApplyList();
            fieldCnt++;
        }
        if (this.shiledList != null) {
            Player.Int64ShieldPlayerInfoMap.Builder tmpBuilder = Player.Int64ShieldPlayerInfoMap.newBuilder();
            final int tmpFieldCnt = this.shiledList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setShiledList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearShiledList();
            }
        }  else if (builder.hasShiledList()) {
            // 清理ShiledList
            builder.clearShiledList();
            fieldCnt++;
        }
        if (this.waitList != null) {
            Player.Int64WaitResponseInfoMap.Builder tmpBuilder = Player.Int64WaitResponseInfoMap.newBuilder();
            final int tmpFieldCnt = this.waitList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWaitList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWaitList();
            }
        }  else if (builder.hasWaitList()) {
            // 清理WaitList
            builder.clearWaitList();
            fieldCnt++;
        }
        if (this.beShieldList != null) {
            Player.Int64PlayerBeShieldInfoMap.Builder tmpBuilder = Player.Int64PlayerBeShieldInfoMap.newBuilder();
            final int tmpFieldCnt = this.beShieldList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBeShieldList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBeShieldList();
            }
        }  else if (builder.hasBeShieldList()) {
            // 清理BeShieldList
            builder.clearBeShieldList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(FriendPlayer.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToDb(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_FRIENDLIST) && this.friendList != null) {
            final boolean needClear = !builder.hasFriendList();
            final int tmpFieldCnt = this.friendList.copyChangeToDb(builder.getFriendListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFriendList();
            }
        }
        if (this.hasMark(FIELD_INDEX_APPLYLIST) && this.applyList != null) {
            final boolean needClear = !builder.hasApplyList();
            final int tmpFieldCnt = this.applyList.copyChangeToDb(builder.getApplyListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearApplyList();
            }
        }
        if (this.hasMark(FIELD_INDEX_SHILEDLIST) && this.shiledList != null) {
            final boolean needClear = !builder.hasShiledList();
            final int tmpFieldCnt = this.shiledList.copyChangeToDb(builder.getShiledListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearShiledList();
            }
        }
        if (this.hasMark(FIELD_INDEX_WAITLIST) && this.waitList != null) {
            final boolean needClear = !builder.hasWaitList();
            final int tmpFieldCnt = this.waitList.copyChangeToDb(builder.getWaitListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWaitList();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESHIELDLIST) && this.beShieldList != null) {
            final boolean needClear = !builder.hasBeShieldList();
            final int tmpFieldCnt = this.beShieldList.copyChangeToDb(builder.getBeShieldListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBeShieldList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(FriendPlayer proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromDb(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromDb(proto.getCardHead());
            }
        }
        if (proto.hasFriendList()) {
            this.getFriendList().mergeFromDb(proto.getFriendList());
        } else {
            if (this.friendList != null) {
                this.friendList.mergeFromDb(proto.getFriendList());
            }
        }
        if (proto.hasApplyList()) {
            this.getApplyList().mergeFromDb(proto.getApplyList());
        } else {
            if (this.applyList != null) {
                this.applyList.mergeFromDb(proto.getApplyList());
            }
        }
        if (proto.hasShiledList()) {
            this.getShiledList().mergeFromDb(proto.getShiledList());
        } else {
            if (this.shiledList != null) {
                this.shiledList.mergeFromDb(proto.getShiledList());
            }
        }
        if (proto.hasWaitList()) {
            this.getWaitList().mergeFromDb(proto.getWaitList());
        } else {
            if (this.waitList != null) {
                this.waitList.mergeFromDb(proto.getWaitList());
            }
        }
        if (proto.hasBeShieldList()) {
            this.getBeShieldList().mergeFromDb(proto.getBeShieldList());
        } else {
            if (this.beShieldList != null) {
                this.beShieldList.mergeFromDb(proto.getBeShieldList());
            }
        }
        this.markAll();
        return FriendPlayerProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(FriendPlayer proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromDb(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasFriendList()) {
            this.getFriendList().mergeChangeFromDb(proto.getFriendList());
            fieldCnt++;
        }
        if (proto.hasApplyList()) {
            this.getApplyList().mergeChangeFromDb(proto.getApplyList());
            fieldCnt++;
        }
        if (proto.hasShiledList()) {
            this.getShiledList().mergeChangeFromDb(proto.getShiledList());
            fieldCnt++;
        }
        if (proto.hasWaitList()) {
            this.getWaitList().mergeChangeFromDb(proto.getWaitList());
            fieldCnt++;
        }
        if (proto.hasBeShieldList()) {
            this.getBeShieldList().mergeChangeFromDb(proto.getBeShieldList());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public FriendPlayer.Builder getCopySsBuilder() {
        final FriendPlayer.Builder builder = FriendPlayer.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(FriendPlayer.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.friendList != null) {
            Player.Int64SimpleFriendInfoMap.Builder tmpBuilder = Player.Int64SimpleFriendInfoMap.newBuilder();
            final int tmpFieldCnt = this.friendList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFriendList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFriendList();
            }
        }  else if (builder.hasFriendList()) {
            // 清理FriendList
            builder.clearFriendList();
            fieldCnt++;
        }
        if (this.applyList != null) {
            Player.Int64FriendApplyInfoMap.Builder tmpBuilder = Player.Int64FriendApplyInfoMap.newBuilder();
            final int tmpFieldCnt = this.applyList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setApplyList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearApplyList();
            }
        }  else if (builder.hasApplyList()) {
            // 清理ApplyList
            builder.clearApplyList();
            fieldCnt++;
        }
        if (this.shiledList != null) {
            Player.Int64ShieldPlayerInfoMap.Builder tmpBuilder = Player.Int64ShieldPlayerInfoMap.newBuilder();
            final int tmpFieldCnt = this.shiledList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setShiledList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearShiledList();
            }
        }  else if (builder.hasShiledList()) {
            // 清理ShiledList
            builder.clearShiledList();
            fieldCnt++;
        }
        if (this.waitList != null) {
            Player.Int64WaitResponseInfoMap.Builder tmpBuilder = Player.Int64WaitResponseInfoMap.newBuilder();
            final int tmpFieldCnt = this.waitList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWaitList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWaitList();
            }
        }  else if (builder.hasWaitList()) {
            // 清理WaitList
            builder.clearWaitList();
            fieldCnt++;
        }
        if (this.beShieldList != null) {
            Player.Int64PlayerBeShieldInfoMap.Builder tmpBuilder = Player.Int64PlayerBeShieldInfoMap.newBuilder();
            final int tmpFieldCnt = this.beShieldList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBeShieldList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBeShieldList();
            }
        }  else if (builder.hasBeShieldList()) {
            // 清理BeShieldList
            builder.clearBeShieldList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(FriendPlayer.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_FRIENDLIST) && this.friendList != null) {
            final boolean needClear = !builder.hasFriendList();
            final int tmpFieldCnt = this.friendList.copyChangeToSs(builder.getFriendListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFriendList();
            }
        }
        if (this.hasMark(FIELD_INDEX_APPLYLIST) && this.applyList != null) {
            final boolean needClear = !builder.hasApplyList();
            final int tmpFieldCnt = this.applyList.copyChangeToSs(builder.getApplyListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearApplyList();
            }
        }
        if (this.hasMark(FIELD_INDEX_SHILEDLIST) && this.shiledList != null) {
            final boolean needClear = !builder.hasShiledList();
            final int tmpFieldCnt = this.shiledList.copyChangeToSs(builder.getShiledListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearShiledList();
            }
        }
        if (this.hasMark(FIELD_INDEX_WAITLIST) && this.waitList != null) {
            final boolean needClear = !builder.hasWaitList();
            final int tmpFieldCnt = this.waitList.copyChangeToSs(builder.getWaitListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWaitList();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESHIELDLIST) && this.beShieldList != null) {
            final boolean needClear = !builder.hasBeShieldList();
            final int tmpFieldCnt = this.beShieldList.copyChangeToSs(builder.getBeShieldListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBeShieldList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(FriendPlayer proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        if (proto.hasFriendList()) {
            this.getFriendList().mergeFromSs(proto.getFriendList());
        } else {
            if (this.friendList != null) {
                this.friendList.mergeFromSs(proto.getFriendList());
            }
        }
        if (proto.hasApplyList()) {
            this.getApplyList().mergeFromSs(proto.getApplyList());
        } else {
            if (this.applyList != null) {
                this.applyList.mergeFromSs(proto.getApplyList());
            }
        }
        if (proto.hasShiledList()) {
            this.getShiledList().mergeFromSs(proto.getShiledList());
        } else {
            if (this.shiledList != null) {
                this.shiledList.mergeFromSs(proto.getShiledList());
            }
        }
        if (proto.hasWaitList()) {
            this.getWaitList().mergeFromSs(proto.getWaitList());
        } else {
            if (this.waitList != null) {
                this.waitList.mergeFromSs(proto.getWaitList());
            }
        }
        if (proto.hasBeShieldList()) {
            this.getBeShieldList().mergeFromSs(proto.getBeShieldList());
        } else {
            if (this.beShieldList != null) {
                this.beShieldList.mergeFromSs(proto.getBeShieldList());
            }
        }
        this.markAll();
        return FriendPlayerProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(FriendPlayer proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasFriendList()) {
            this.getFriendList().mergeChangeFromSs(proto.getFriendList());
            fieldCnt++;
        }
        if (proto.hasApplyList()) {
            this.getApplyList().mergeChangeFromSs(proto.getApplyList());
            fieldCnt++;
        }
        if (proto.hasShiledList()) {
            this.getShiledList().mergeChangeFromSs(proto.getShiledList());
            fieldCnt++;
        }
        if (proto.hasWaitList()) {
            this.getWaitList().mergeChangeFromSs(proto.getWaitList());
            fieldCnt++;
        }
        if (proto.hasBeShieldList()) {
            this.getBeShieldList().mergeChangeFromSs(proto.getBeShieldList());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        FriendPlayer.Builder builder = FriendPlayer.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_FRIENDLIST) && this.friendList != null) {
            this.friendList.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_APPLYLIST) && this.applyList != null) {
            this.applyList.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SHILEDLIST) && this.shiledList != null) {
            this.shiledList.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_WAITLIST) && this.waitList != null) {
            this.waitList.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BESHIELDLIST) && this.beShieldList != null) {
            this.beShieldList.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        if (this.friendList != null) {
            this.friendList.markAll();
        }
        if (this.applyList != null) {
            this.applyList.markAll();
        }
        if (this.shiledList != null) {
            this.shiledList.markAll();
        }
        if (this.waitList != null) {
            this.waitList.markAll();
        }
        if (this.beShieldList != null) {
            this.beShieldList.markAll();
        }
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("FriendPlayerProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("FriendPlayerProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof FriendPlayerProp)) {
            return false;
        }
        final FriendPlayerProp otherNode = (FriendPlayerProp) node;
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        if (!this.getFriendList().compareDataTo(otherNode.getFriendList())) {
            return false;
        }
        if (!this.getApplyList().compareDataTo(otherNode.getApplyList())) {
            return false;
        }
        if (!this.getShiledList().compareDataTo(otherNode.getShiledList())) {
            return false;
        }
        if (!this.getWaitList().compareDataTo(otherNode.getWaitList())) {
            return false;
        }
        if (!this.getBeShieldList().compareDataTo(otherNode.getBeShieldList())) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    public static FriendPlayerProp of(FriendPlayer fullAttrDb, FriendPlayer changeAttrDb) {
        FriendPlayerProp prop = new FriendPlayerProp();
        prop.mergeFromDb(fullAttrDb);
        prop.mergeChangeFromDb(changeAttrDb);
        prop.unMarkAll();
        return prop;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
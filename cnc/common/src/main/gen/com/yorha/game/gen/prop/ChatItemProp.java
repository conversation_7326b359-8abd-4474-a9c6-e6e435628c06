package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.ChatItem;
import com.yorha.proto.PlayerPB.ChatItemPB;


/**
 * <AUTHOR> auto gen
 */
public class ChatItem<PERSON>rop extends AbstractContainerElementNode<String> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_READINDEX = 1;
    public static final int FIELD_INDEX_STARTINDEX = 2;
    public static final int FIELD_INDEX_MAXINDEX = 3;
    public static final int FIELD_INDEX_OWNER = 4;
    public static final int FIELD_INDEX_VERSION = 5;
    public static final int FIELD_INDEX_GROUPNAME = 6;
    public static final int FIELD_INDEX_NOTDISTURB = 7;
    public static final int FIELD_INDEX_FIRSTATINDEX = 8;
    public static final int FIELD_INDEX_LASTCHATTSMS = 9;
    public static final int FIELD_INDEX_IGNORECOUNT = 10;
    public static final int FIELD_INDEX_HIDE = 11;
    public static final int FIELD_INDEX_CREATETSMS = 12;

    public static final int FIELD_COUNT = 13;

    private long markBits0 = 0L;

    private String id = Constant.DEFAULT_STR_VALUE;
    private long readIndex = Constant.DEFAULT_LONG_VALUE;
    private long startIndex = Constant.DEFAULT_LONG_VALUE;
    private long maxIndex = Constant.DEFAULT_LONG_VALUE;
    private long owner = Constant.DEFAULT_LONG_VALUE;
    private int version = Constant.DEFAULT_INT_VALUE;
    private String groupName = Constant.DEFAULT_STR_VALUE;
    private boolean notDisturb = Constant.DEFAULT_BOOLEAN_VALUE;
    private long firstAtIndex = Constant.DEFAULT_LONG_VALUE;
    private long lastChatTsMs = Constant.DEFAULT_LONG_VALUE;
    private int ignoreCount = Constant.DEFAULT_INT_VALUE;
    private boolean hide = Constant.DEFAULT_BOOLEAN_VALUE;
    private long createTsMs = Constant.DEFAULT_LONG_VALUE;

    public ChatItemProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ChatItemProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public String getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public ChatItemProp setId(String id) {
        if (id == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.id, id)) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(String id) {
        this.id = id;
    }

    /**
     * get readIndex
     *
     * @return readIndex value
     */
    public long getReadIndex() {
        return this.readIndex;
    }

    /**
     * set readIndex && set marked
     *
     * @param readIndex new value
     * @return current object
     */
    public ChatItemProp setReadIndex(long readIndex) {
        if (this.readIndex != readIndex) {
            this.mark(FIELD_INDEX_READINDEX);
            this.readIndex = readIndex;
        }
        return this;
    }

    /**
     * inner set readIndex
     *
     * @param readIndex new value
     */
    private void innerSetReadIndex(long readIndex) {
        this.readIndex = readIndex;
    }

    /**
     * get startIndex
     *
     * @return startIndex value
     */
    public long getStartIndex() {
        return this.startIndex;
    }

    /**
     * set startIndex && set marked
     *
     * @param startIndex new value
     * @return current object
     */
    public ChatItemProp setStartIndex(long startIndex) {
        if (this.startIndex != startIndex) {
            this.mark(FIELD_INDEX_STARTINDEX);
            this.startIndex = startIndex;
        }
        return this;
    }

    /**
     * inner set startIndex
     *
     * @param startIndex new value
     */
    private void innerSetStartIndex(long startIndex) {
        this.startIndex = startIndex;
    }

    /**
     * get maxIndex
     *
     * @return maxIndex value
     */
    public long getMaxIndex() {
        return this.maxIndex;
    }

    /**
     * set maxIndex && set marked
     *
     * @param maxIndex new value
     * @return current object
     */
    public ChatItemProp setMaxIndex(long maxIndex) {
        if (this.maxIndex != maxIndex) {
            this.mark(FIELD_INDEX_MAXINDEX);
            this.maxIndex = maxIndex;
        }
        return this;
    }

    /**
     * inner set maxIndex
     *
     * @param maxIndex new value
     */
    private void innerSetMaxIndex(long maxIndex) {
        this.maxIndex = maxIndex;
    }

    /**
     * get owner
     *
     * @return owner value
     */
    public long getOwner() {
        return this.owner;
    }

    /**
     * set owner && set marked
     *
     * @param owner new value
     * @return current object
     */
    public ChatItemProp setOwner(long owner) {
        if (this.owner != owner) {
            this.mark(FIELD_INDEX_OWNER);
            this.owner = owner;
        }
        return this;
    }

    /**
     * inner set owner
     *
     * @param owner new value
     */
    private void innerSetOwner(long owner) {
        this.owner = owner;
    }

    /**
     * get version
     *
     * @return version value
     */
    public int getVersion() {
        return this.version;
    }

    /**
     * set version && set marked
     *
     * @param version new value
     * @return current object
     */
    public ChatItemProp setVersion(int version) {
        if (this.version != version) {
            this.mark(FIELD_INDEX_VERSION);
            this.version = version;
        }
        return this;
    }

    /**
     * inner set version
     *
     * @param version new value
     */
    private void innerSetVersion(int version) {
        this.version = version;
    }

    /**
     * get groupName
     *
     * @return groupName value
     */
    public String getGroupName() {
        return this.groupName;
    }

    /**
     * set groupName && set marked
     *
     * @param groupName new value
     * @return current object
     */
    public ChatItemProp setGroupName(String groupName) {
        if (groupName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.groupName, groupName)) {
            this.mark(FIELD_INDEX_GROUPNAME);
            this.groupName = groupName;
        }
        return this;
    }

    /**
     * inner set groupName
     *
     * @param groupName new value
     */
    private void innerSetGroupName(String groupName) {
        this.groupName = groupName;
    }

    /**
     * get notDisturb
     *
     * @return notDisturb value
     */
    public boolean getNotDisturb() {
        return this.notDisturb;
    }

    /**
     * set notDisturb && set marked
     *
     * @param notDisturb new value
     * @return current object
     */
    public ChatItemProp setNotDisturb(boolean notDisturb) {
        if (this.notDisturb != notDisturb) {
            this.mark(FIELD_INDEX_NOTDISTURB);
            this.notDisturb = notDisturb;
        }
        return this;
    }

    /**
     * inner set notDisturb
     *
     * @param notDisturb new value
     */
    private void innerSetNotDisturb(boolean notDisturb) {
        this.notDisturb = notDisturb;
    }

    /**
     * get firstAtIndex
     *
     * @return firstAtIndex value
     */
    public long getFirstAtIndex() {
        return this.firstAtIndex;
    }

    /**
     * set firstAtIndex && set marked
     *
     * @param firstAtIndex new value
     * @return current object
     */
    public ChatItemProp setFirstAtIndex(long firstAtIndex) {
        if (this.firstAtIndex != firstAtIndex) {
            this.mark(FIELD_INDEX_FIRSTATINDEX);
            this.firstAtIndex = firstAtIndex;
        }
        return this;
    }

    /**
     * inner set firstAtIndex
     *
     * @param firstAtIndex new value
     */
    private void innerSetFirstAtIndex(long firstAtIndex) {
        this.firstAtIndex = firstAtIndex;
    }

    /**
     * get lastChatTsMs
     *
     * @return lastChatTsMs value
     */
    public long getLastChatTsMs() {
        return this.lastChatTsMs;
    }

    /**
     * set lastChatTsMs && set marked
     *
     * @param lastChatTsMs new value
     * @return current object
     */
    public ChatItemProp setLastChatTsMs(long lastChatTsMs) {
        if (this.lastChatTsMs != lastChatTsMs) {
            this.mark(FIELD_INDEX_LASTCHATTSMS);
            this.lastChatTsMs = lastChatTsMs;
        }
        return this;
    }

    /**
     * inner set lastChatTsMs
     *
     * @param lastChatTsMs new value
     */
    private void innerSetLastChatTsMs(long lastChatTsMs) {
        this.lastChatTsMs = lastChatTsMs;
    }

    /**
     * get ignoreCount
     *
     * @return ignoreCount value
     */
    public int getIgnoreCount() {
        return this.ignoreCount;
    }

    /**
     * set ignoreCount && set marked
     *
     * @param ignoreCount new value
     * @return current object
     */
    public ChatItemProp setIgnoreCount(int ignoreCount) {
        if (this.ignoreCount != ignoreCount) {
            this.mark(FIELD_INDEX_IGNORECOUNT);
            this.ignoreCount = ignoreCount;
        }
        return this;
    }

    /**
     * inner set ignoreCount
     *
     * @param ignoreCount new value
     */
    private void innerSetIgnoreCount(int ignoreCount) {
        this.ignoreCount = ignoreCount;
    }

    /**
     * get hide
     *
     * @return hide value
     */
    public boolean getHide() {
        return this.hide;
    }

    /**
     * set hide && set marked
     *
     * @param hide new value
     * @return current object
     */
    public ChatItemProp setHide(boolean hide) {
        if (this.hide != hide) {
            this.mark(FIELD_INDEX_HIDE);
            this.hide = hide;
        }
        return this;
    }

    /**
     * inner set hide
     *
     * @param hide new value
     */
    private void innerSetHide(boolean hide) {
        this.hide = hide;
    }

    /**
     * get createTsMs
     *
     * @return createTsMs value
     */
    public long getCreateTsMs() {
        return this.createTsMs;
    }

    /**
     * set createTsMs && set marked
     *
     * @param createTsMs new value
     * @return current object
     */
    public ChatItemProp setCreateTsMs(long createTsMs) {
        if (this.createTsMs != createTsMs) {
            this.mark(FIELD_INDEX_CREATETSMS);
            this.createTsMs = createTsMs;
        }
        return this;
    }

    /**
     * inner set createTsMs
     *
     * @param createTsMs new value
     */
    private void innerSetCreateTsMs(long createTsMs) {
        this.createTsMs = createTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ChatItemPB.Builder getCopyCsBuilder() {
        final ChatItemPB.Builder builder = ChatItemPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ChatItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getId().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getReadIndex() != 0L) {
            builder.setReadIndex(this.getReadIndex());
            fieldCnt++;
        }  else if (builder.hasReadIndex()) {
            // 清理ReadIndex
            builder.clearReadIndex();
            fieldCnt++;
        }
        if (this.getMaxIndex() != 0L) {
            builder.setMaxIndex(this.getMaxIndex());
            fieldCnt++;
        }  else if (builder.hasMaxIndex()) {
            // 清理MaxIndex
            builder.clearMaxIndex();
            fieldCnt++;
        }
        if (this.getOwner() != 0L) {
            builder.setOwner(this.getOwner());
            fieldCnt++;
        }  else if (builder.hasOwner()) {
            // 清理Owner
            builder.clearOwner();
            fieldCnt++;
        }
        if (this.getVersion() != 0) {
            builder.setVersion(this.getVersion());
            fieldCnt++;
        }  else if (builder.hasVersion()) {
            // 清理Version
            builder.clearVersion();
            fieldCnt++;
        }
        if (!this.getGroupName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setGroupName(this.getGroupName());
            fieldCnt++;
        }  else if (builder.hasGroupName()) {
            // 清理GroupName
            builder.clearGroupName();
            fieldCnt++;
        }
        if (this.getNotDisturb()) {
            builder.setNotDisturb(this.getNotDisturb());
            fieldCnt++;
        }  else if (builder.hasNotDisturb()) {
            // 清理NotDisturb
            builder.clearNotDisturb();
            fieldCnt++;
        }
        if (this.getFirstAtIndex() != 0L) {
            builder.setFirstAtIndex(this.getFirstAtIndex());
            fieldCnt++;
        }  else if (builder.hasFirstAtIndex()) {
            // 清理FirstAtIndex
            builder.clearFirstAtIndex();
            fieldCnt++;
        }
        if (this.getLastChatTsMs() != 0L) {
            builder.setLastChatTsMs(this.getLastChatTsMs());
            fieldCnt++;
        }  else if (builder.hasLastChatTsMs()) {
            // 清理LastChatTsMs
            builder.clearLastChatTsMs();
            fieldCnt++;
        }
        if (this.getIgnoreCount() != 0) {
            builder.setIgnoreCount(this.getIgnoreCount());
            fieldCnt++;
        }  else if (builder.hasIgnoreCount()) {
            // 清理IgnoreCount
            builder.clearIgnoreCount();
            fieldCnt++;
        }
        if (this.getHide()) {
            builder.setHide(this.getHide());
            fieldCnt++;
        }  else if (builder.hasHide()) {
            // 清理Hide
            builder.clearHide();
            fieldCnt++;
        }
        if (this.getCreateTsMs() != 0L) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }  else if (builder.hasCreateTsMs()) {
            // 清理CreateTsMs
            builder.clearCreateTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ChatItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_READINDEX)) {
            builder.setReadIndex(this.getReadIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXINDEX)) {
            builder.setMaxIndex(this.getMaxIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNER)) {
            builder.setOwner(this.getOwner());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VERSION)) {
            builder.setVersion(this.getVersion());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GROUPNAME)) {
            builder.setGroupName(this.getGroupName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NOTDISTURB)) {
            builder.setNotDisturb(this.getNotDisturb());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FIRSTATINDEX)) {
            builder.setFirstAtIndex(this.getFirstAtIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTCHATTSMS)) {
            builder.setLastChatTsMs(this.getLastChatTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_IGNORECOUNT)) {
            builder.setIgnoreCount(this.getIgnoreCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HIDE)) {
            builder.setHide(this.getHide());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ChatItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_READINDEX)) {
            builder.setReadIndex(this.getReadIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXINDEX)) {
            builder.setMaxIndex(this.getMaxIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNER)) {
            builder.setOwner(this.getOwner());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VERSION)) {
            builder.setVersion(this.getVersion());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GROUPNAME)) {
            builder.setGroupName(this.getGroupName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NOTDISTURB)) {
            builder.setNotDisturb(this.getNotDisturb());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FIRSTATINDEX)) {
            builder.setFirstAtIndex(this.getFirstAtIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTCHATTSMS)) {
            builder.setLastChatTsMs(this.getLastChatTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_IGNORECOUNT)) {
            builder.setIgnoreCount(this.getIgnoreCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HIDE)) {
            builder.setHide(this.getHide());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ChatItemPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasReadIndex()) {
            this.innerSetReadIndex(proto.getReadIndex());
        } else {
            this.innerSetReadIndex(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMaxIndex()) {
            this.innerSetMaxIndex(proto.getMaxIndex());
        } else {
            this.innerSetMaxIndex(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOwner()) {
            this.innerSetOwner(proto.getOwner());
        } else {
            this.innerSetOwner(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasVersion()) {
            this.innerSetVersion(proto.getVersion());
        } else {
            this.innerSetVersion(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasGroupName()) {
            this.innerSetGroupName(proto.getGroupName());
        } else {
            this.innerSetGroupName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasNotDisturb()) {
            this.innerSetNotDisturb(proto.getNotDisturb());
        } else {
            this.innerSetNotDisturb(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasFirstAtIndex()) {
            this.innerSetFirstAtIndex(proto.getFirstAtIndex());
        } else {
            this.innerSetFirstAtIndex(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastChatTsMs()) {
            this.innerSetLastChatTsMs(proto.getLastChatTsMs());
        } else {
            this.innerSetLastChatTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIgnoreCount()) {
            this.innerSetIgnoreCount(proto.getIgnoreCount());
        } else {
            this.innerSetIgnoreCount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHide()) {
            this.innerSetHide(proto.getHide());
        } else {
            this.innerSetHide(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasCreateTsMs()) {
            this.innerSetCreateTsMs(proto.getCreateTsMs());
        } else {
            this.innerSetCreateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ChatItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ChatItemPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasReadIndex()) {
            this.setReadIndex(proto.getReadIndex());
            fieldCnt++;
        }
        if (proto.hasMaxIndex()) {
            this.setMaxIndex(proto.getMaxIndex());
            fieldCnt++;
        }
        if (proto.hasOwner()) {
            this.setOwner(proto.getOwner());
            fieldCnt++;
        }
        if (proto.hasVersion()) {
            this.setVersion(proto.getVersion());
            fieldCnt++;
        }
        if (proto.hasGroupName()) {
            this.setGroupName(proto.getGroupName());
            fieldCnt++;
        }
        if (proto.hasNotDisturb()) {
            this.setNotDisturb(proto.getNotDisturb());
            fieldCnt++;
        }
        if (proto.hasFirstAtIndex()) {
            this.setFirstAtIndex(proto.getFirstAtIndex());
            fieldCnt++;
        }
        if (proto.hasLastChatTsMs()) {
            this.setLastChatTsMs(proto.getLastChatTsMs());
            fieldCnt++;
        }
        if (proto.hasIgnoreCount()) {
            this.setIgnoreCount(proto.getIgnoreCount());
            fieldCnt++;
        }
        if (proto.hasHide()) {
            this.setHide(proto.getHide());
            fieldCnt++;
        }
        if (proto.hasCreateTsMs()) {
            this.setCreateTsMs(proto.getCreateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ChatItem.Builder getCopyDbBuilder() {
        final ChatItem.Builder builder = ChatItem.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ChatItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getId().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getReadIndex() != 0L) {
            builder.setReadIndex(this.getReadIndex());
            fieldCnt++;
        }  else if (builder.hasReadIndex()) {
            // 清理ReadIndex
            builder.clearReadIndex();
            fieldCnt++;
        }
        if (this.getStartIndex() != 0L) {
            builder.setStartIndex(this.getStartIndex());
            fieldCnt++;
        }  else if (builder.hasStartIndex()) {
            // 清理StartIndex
            builder.clearStartIndex();
            fieldCnt++;
        }
        if (this.getMaxIndex() != 0L) {
            builder.setMaxIndex(this.getMaxIndex());
            fieldCnt++;
        }  else if (builder.hasMaxIndex()) {
            // 清理MaxIndex
            builder.clearMaxIndex();
            fieldCnt++;
        }
        if (this.getOwner() != 0L) {
            builder.setOwner(this.getOwner());
            fieldCnt++;
        }  else if (builder.hasOwner()) {
            // 清理Owner
            builder.clearOwner();
            fieldCnt++;
        }
        if (this.getVersion() != 0) {
            builder.setVersion(this.getVersion());
            fieldCnt++;
        }  else if (builder.hasVersion()) {
            // 清理Version
            builder.clearVersion();
            fieldCnt++;
        }
        if (!this.getGroupName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setGroupName(this.getGroupName());
            fieldCnt++;
        }  else if (builder.hasGroupName()) {
            // 清理GroupName
            builder.clearGroupName();
            fieldCnt++;
        }
        if (this.getNotDisturb()) {
            builder.setNotDisturb(this.getNotDisturb());
            fieldCnt++;
        }  else if (builder.hasNotDisturb()) {
            // 清理NotDisturb
            builder.clearNotDisturb();
            fieldCnt++;
        }
        if (this.getFirstAtIndex() != 0L) {
            builder.setFirstAtIndex(this.getFirstAtIndex());
            fieldCnt++;
        }  else if (builder.hasFirstAtIndex()) {
            // 清理FirstAtIndex
            builder.clearFirstAtIndex();
            fieldCnt++;
        }
        if (this.getLastChatTsMs() != 0L) {
            builder.setLastChatTsMs(this.getLastChatTsMs());
            fieldCnt++;
        }  else if (builder.hasLastChatTsMs()) {
            // 清理LastChatTsMs
            builder.clearLastChatTsMs();
            fieldCnt++;
        }
        if (this.getIgnoreCount() != 0) {
            builder.setIgnoreCount(this.getIgnoreCount());
            fieldCnt++;
        }  else if (builder.hasIgnoreCount()) {
            // 清理IgnoreCount
            builder.clearIgnoreCount();
            fieldCnt++;
        }
        if (this.getHide()) {
            builder.setHide(this.getHide());
            fieldCnt++;
        }  else if (builder.hasHide()) {
            // 清理Hide
            builder.clearHide();
            fieldCnt++;
        }
        if (this.getCreateTsMs() != 0L) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }  else if (builder.hasCreateTsMs()) {
            // 清理CreateTsMs
            builder.clearCreateTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ChatItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_READINDEX)) {
            builder.setReadIndex(this.getReadIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTINDEX)) {
            builder.setStartIndex(this.getStartIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXINDEX)) {
            builder.setMaxIndex(this.getMaxIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNER)) {
            builder.setOwner(this.getOwner());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VERSION)) {
            builder.setVersion(this.getVersion());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GROUPNAME)) {
            builder.setGroupName(this.getGroupName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NOTDISTURB)) {
            builder.setNotDisturb(this.getNotDisturb());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FIRSTATINDEX)) {
            builder.setFirstAtIndex(this.getFirstAtIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTCHATTSMS)) {
            builder.setLastChatTsMs(this.getLastChatTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_IGNORECOUNT)) {
            builder.setIgnoreCount(this.getIgnoreCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HIDE)) {
            builder.setHide(this.getHide());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ChatItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasReadIndex()) {
            this.innerSetReadIndex(proto.getReadIndex());
        } else {
            this.innerSetReadIndex(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStartIndex()) {
            this.innerSetStartIndex(proto.getStartIndex());
        } else {
            this.innerSetStartIndex(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMaxIndex()) {
            this.innerSetMaxIndex(proto.getMaxIndex());
        } else {
            this.innerSetMaxIndex(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOwner()) {
            this.innerSetOwner(proto.getOwner());
        } else {
            this.innerSetOwner(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasVersion()) {
            this.innerSetVersion(proto.getVersion());
        } else {
            this.innerSetVersion(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasGroupName()) {
            this.innerSetGroupName(proto.getGroupName());
        } else {
            this.innerSetGroupName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasNotDisturb()) {
            this.innerSetNotDisturb(proto.getNotDisturb());
        } else {
            this.innerSetNotDisturb(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasFirstAtIndex()) {
            this.innerSetFirstAtIndex(proto.getFirstAtIndex());
        } else {
            this.innerSetFirstAtIndex(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastChatTsMs()) {
            this.innerSetLastChatTsMs(proto.getLastChatTsMs());
        } else {
            this.innerSetLastChatTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIgnoreCount()) {
            this.innerSetIgnoreCount(proto.getIgnoreCount());
        } else {
            this.innerSetIgnoreCount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHide()) {
            this.innerSetHide(proto.getHide());
        } else {
            this.innerSetHide(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasCreateTsMs()) {
            this.innerSetCreateTsMs(proto.getCreateTsMs());
        } else {
            this.innerSetCreateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ChatItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ChatItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasReadIndex()) {
            this.setReadIndex(proto.getReadIndex());
            fieldCnt++;
        }
        if (proto.hasStartIndex()) {
            this.setStartIndex(proto.getStartIndex());
            fieldCnt++;
        }
        if (proto.hasMaxIndex()) {
            this.setMaxIndex(proto.getMaxIndex());
            fieldCnt++;
        }
        if (proto.hasOwner()) {
            this.setOwner(proto.getOwner());
            fieldCnt++;
        }
        if (proto.hasVersion()) {
            this.setVersion(proto.getVersion());
            fieldCnt++;
        }
        if (proto.hasGroupName()) {
            this.setGroupName(proto.getGroupName());
            fieldCnt++;
        }
        if (proto.hasNotDisturb()) {
            this.setNotDisturb(proto.getNotDisturb());
            fieldCnt++;
        }
        if (proto.hasFirstAtIndex()) {
            this.setFirstAtIndex(proto.getFirstAtIndex());
            fieldCnt++;
        }
        if (proto.hasLastChatTsMs()) {
            this.setLastChatTsMs(proto.getLastChatTsMs());
            fieldCnt++;
        }
        if (proto.hasIgnoreCount()) {
            this.setIgnoreCount(proto.getIgnoreCount());
            fieldCnt++;
        }
        if (proto.hasHide()) {
            this.setHide(proto.getHide());
            fieldCnt++;
        }
        if (proto.hasCreateTsMs()) {
            this.setCreateTsMs(proto.getCreateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ChatItem.Builder getCopySsBuilder() {
        final ChatItem.Builder builder = ChatItem.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ChatItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getId().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getReadIndex() != 0L) {
            builder.setReadIndex(this.getReadIndex());
            fieldCnt++;
        }  else if (builder.hasReadIndex()) {
            // 清理ReadIndex
            builder.clearReadIndex();
            fieldCnt++;
        }
        if (this.getStartIndex() != 0L) {
            builder.setStartIndex(this.getStartIndex());
            fieldCnt++;
        }  else if (builder.hasStartIndex()) {
            // 清理StartIndex
            builder.clearStartIndex();
            fieldCnt++;
        }
        if (this.getMaxIndex() != 0L) {
            builder.setMaxIndex(this.getMaxIndex());
            fieldCnt++;
        }  else if (builder.hasMaxIndex()) {
            // 清理MaxIndex
            builder.clearMaxIndex();
            fieldCnt++;
        }
        if (this.getOwner() != 0L) {
            builder.setOwner(this.getOwner());
            fieldCnt++;
        }  else if (builder.hasOwner()) {
            // 清理Owner
            builder.clearOwner();
            fieldCnt++;
        }
        if (this.getVersion() != 0) {
            builder.setVersion(this.getVersion());
            fieldCnt++;
        }  else if (builder.hasVersion()) {
            // 清理Version
            builder.clearVersion();
            fieldCnt++;
        }
        if (!this.getGroupName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setGroupName(this.getGroupName());
            fieldCnt++;
        }  else if (builder.hasGroupName()) {
            // 清理GroupName
            builder.clearGroupName();
            fieldCnt++;
        }
        if (this.getNotDisturb()) {
            builder.setNotDisturb(this.getNotDisturb());
            fieldCnt++;
        }  else if (builder.hasNotDisturb()) {
            // 清理NotDisturb
            builder.clearNotDisturb();
            fieldCnt++;
        }
        if (this.getFirstAtIndex() != 0L) {
            builder.setFirstAtIndex(this.getFirstAtIndex());
            fieldCnt++;
        }  else if (builder.hasFirstAtIndex()) {
            // 清理FirstAtIndex
            builder.clearFirstAtIndex();
            fieldCnt++;
        }
        if (this.getLastChatTsMs() != 0L) {
            builder.setLastChatTsMs(this.getLastChatTsMs());
            fieldCnt++;
        }  else if (builder.hasLastChatTsMs()) {
            // 清理LastChatTsMs
            builder.clearLastChatTsMs();
            fieldCnt++;
        }
        if (this.getIgnoreCount() != 0) {
            builder.setIgnoreCount(this.getIgnoreCount());
            fieldCnt++;
        }  else if (builder.hasIgnoreCount()) {
            // 清理IgnoreCount
            builder.clearIgnoreCount();
            fieldCnt++;
        }
        if (this.getHide()) {
            builder.setHide(this.getHide());
            fieldCnt++;
        }  else if (builder.hasHide()) {
            // 清理Hide
            builder.clearHide();
            fieldCnt++;
        }
        if (this.getCreateTsMs() != 0L) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }  else if (builder.hasCreateTsMs()) {
            // 清理CreateTsMs
            builder.clearCreateTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ChatItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_READINDEX)) {
            builder.setReadIndex(this.getReadIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTINDEX)) {
            builder.setStartIndex(this.getStartIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXINDEX)) {
            builder.setMaxIndex(this.getMaxIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNER)) {
            builder.setOwner(this.getOwner());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VERSION)) {
            builder.setVersion(this.getVersion());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GROUPNAME)) {
            builder.setGroupName(this.getGroupName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NOTDISTURB)) {
            builder.setNotDisturb(this.getNotDisturb());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FIRSTATINDEX)) {
            builder.setFirstAtIndex(this.getFirstAtIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTCHATTSMS)) {
            builder.setLastChatTsMs(this.getLastChatTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_IGNORECOUNT)) {
            builder.setIgnoreCount(this.getIgnoreCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HIDE)) {
            builder.setHide(this.getHide());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ChatItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasReadIndex()) {
            this.innerSetReadIndex(proto.getReadIndex());
        } else {
            this.innerSetReadIndex(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStartIndex()) {
            this.innerSetStartIndex(proto.getStartIndex());
        } else {
            this.innerSetStartIndex(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMaxIndex()) {
            this.innerSetMaxIndex(proto.getMaxIndex());
        } else {
            this.innerSetMaxIndex(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOwner()) {
            this.innerSetOwner(proto.getOwner());
        } else {
            this.innerSetOwner(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasVersion()) {
            this.innerSetVersion(proto.getVersion());
        } else {
            this.innerSetVersion(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasGroupName()) {
            this.innerSetGroupName(proto.getGroupName());
        } else {
            this.innerSetGroupName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasNotDisturb()) {
            this.innerSetNotDisturb(proto.getNotDisturb());
        } else {
            this.innerSetNotDisturb(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasFirstAtIndex()) {
            this.innerSetFirstAtIndex(proto.getFirstAtIndex());
        } else {
            this.innerSetFirstAtIndex(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastChatTsMs()) {
            this.innerSetLastChatTsMs(proto.getLastChatTsMs());
        } else {
            this.innerSetLastChatTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIgnoreCount()) {
            this.innerSetIgnoreCount(proto.getIgnoreCount());
        } else {
            this.innerSetIgnoreCount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHide()) {
            this.innerSetHide(proto.getHide());
        } else {
            this.innerSetHide(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasCreateTsMs()) {
            this.innerSetCreateTsMs(proto.getCreateTsMs());
        } else {
            this.innerSetCreateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ChatItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ChatItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasReadIndex()) {
            this.setReadIndex(proto.getReadIndex());
            fieldCnt++;
        }
        if (proto.hasStartIndex()) {
            this.setStartIndex(proto.getStartIndex());
            fieldCnt++;
        }
        if (proto.hasMaxIndex()) {
            this.setMaxIndex(proto.getMaxIndex());
            fieldCnt++;
        }
        if (proto.hasOwner()) {
            this.setOwner(proto.getOwner());
            fieldCnt++;
        }
        if (proto.hasVersion()) {
            this.setVersion(proto.getVersion());
            fieldCnt++;
        }
        if (proto.hasGroupName()) {
            this.setGroupName(proto.getGroupName());
            fieldCnt++;
        }
        if (proto.hasNotDisturb()) {
            this.setNotDisturb(proto.getNotDisturb());
            fieldCnt++;
        }
        if (proto.hasFirstAtIndex()) {
            this.setFirstAtIndex(proto.getFirstAtIndex());
            fieldCnt++;
        }
        if (proto.hasLastChatTsMs()) {
            this.setLastChatTsMs(proto.getLastChatTsMs());
            fieldCnt++;
        }
        if (proto.hasIgnoreCount()) {
            this.setIgnoreCount(proto.getIgnoreCount());
            fieldCnt++;
        }
        if (proto.hasHide()) {
            this.setHide(proto.getHide());
            fieldCnt++;
        }
        if (proto.hasCreateTsMs()) {
            this.setCreateTsMs(proto.getCreateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ChatItem.Builder builder = ChatItem.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public String getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ChatItemProp)) {
            return false;
        }
        final ChatItemProp otherNode = (ChatItemProp) node;
        if (!com.yorha.gemini.utils.StringUtils.equals(this.id, otherNode.id)) {
            return false;
        }
        if (this.readIndex != otherNode.readIndex) {
            return false;
        }
        if (this.startIndex != otherNode.startIndex) {
            return false;
        }
        if (this.maxIndex != otherNode.maxIndex) {
            return false;
        }
        if (this.owner != otherNode.owner) {
            return false;
        }
        if (this.version != otherNode.version) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.groupName, otherNode.groupName)) {
            return false;
        }
        if (this.notDisturb != otherNode.notDisturb) {
            return false;
        }
        if (this.firstAtIndex != otherNode.firstAtIndex) {
            return false;
        }
        if (this.lastChatTsMs != otherNode.lastChatTsMs) {
            return false;
        }
        if (this.ignoreCount != otherNode.ignoreCount) {
            return false;
        }
        if (this.hide != otherNode.hide) {
            return false;
        }
        if (this.createTsMs != otherNode.createTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 51;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
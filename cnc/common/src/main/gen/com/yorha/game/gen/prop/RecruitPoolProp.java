package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Player.RecruitPool;
import com.yorha.proto.PlayerPB.RecruitPoolPB;


/**
 * <AUTHOR> auto gen
 */
public class RecruitPool<PERSON>rop extends AbstractPropNode {

    public static final int FIELD_INDEX_RECRUITPOOLTYPE = 0;
    public static final int FIELD_INDEX_DAILYRECRUITCNT = 1;
    public static final int FIELD_INDEX_TOTALRECRUITCNT = 2;
    public static final int FIELD_INDEX_FREERECRUITCNT = 3;
    public static final int FIELD_INDEX_NEXTFREERECRUITTIME = 4;
    public static final int FIELD_INDEX_FULLRECRUITED = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private RecruitPoolType recruitPoolType = RecruitPoolType.forNumber(0);
    private int dailyRecruitCnt = Constant.DEFAULT_INT_VALUE;
    private int totalRecruitCnt = Constant.DEFAULT_INT_VALUE;
    private int freeRecruitCnt = Constant.DEFAULT_INT_VALUE;
    private long nextFreeRecruitTime = Constant.DEFAULT_LONG_VALUE;
    private boolean fullRecruited = Constant.DEFAULT_BOOLEAN_VALUE;

    public RecruitPoolProp() {
        super(null, 0, FIELD_COUNT);
    }

    public RecruitPoolProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get recruitPoolType
     *
     * @return recruitPoolType value
     */
    public RecruitPoolType getRecruitPoolType() {
        return this.recruitPoolType;
    }

    /**
     * set recruitPoolType && set marked
     *
     * @param recruitPoolType new value
     * @return current object
     */
    public RecruitPoolProp setRecruitPoolType(RecruitPoolType recruitPoolType) {
        if (recruitPoolType == null) {
            throw new NullPointerException();
        }
        if (this.recruitPoolType != recruitPoolType) {
            this.mark(FIELD_INDEX_RECRUITPOOLTYPE);
            this.recruitPoolType = recruitPoolType;
        }
        return this;
    }

    /**
     * inner set recruitPoolType
     *
     * @param recruitPoolType new value
     */
    private void innerSetRecruitPoolType(RecruitPoolType recruitPoolType) {
        this.recruitPoolType = recruitPoolType;
    }

    /**
     * get dailyRecruitCnt
     *
     * @return dailyRecruitCnt value
     */
    public int getDailyRecruitCnt() {
        return this.dailyRecruitCnt;
    }

    /**
     * set dailyRecruitCnt && set marked
     *
     * @param dailyRecruitCnt new value
     * @return current object
     */
    public RecruitPoolProp setDailyRecruitCnt(int dailyRecruitCnt) {
        if (this.dailyRecruitCnt != dailyRecruitCnt) {
            this.mark(FIELD_INDEX_DAILYRECRUITCNT);
            this.dailyRecruitCnt = dailyRecruitCnt;
        }
        return this;
    }

    /**
     * inner set dailyRecruitCnt
     *
     * @param dailyRecruitCnt new value
     */
    private void innerSetDailyRecruitCnt(int dailyRecruitCnt) {
        this.dailyRecruitCnt = dailyRecruitCnt;
    }

    /**
     * get totalRecruitCnt
     *
     * @return totalRecruitCnt value
     */
    public int getTotalRecruitCnt() {
        return this.totalRecruitCnt;
    }

    /**
     * set totalRecruitCnt && set marked
     *
     * @param totalRecruitCnt new value
     * @return current object
     */
    public RecruitPoolProp setTotalRecruitCnt(int totalRecruitCnt) {
        if (this.totalRecruitCnt != totalRecruitCnt) {
            this.mark(FIELD_INDEX_TOTALRECRUITCNT);
            this.totalRecruitCnt = totalRecruitCnt;
        }
        return this;
    }

    /**
     * inner set totalRecruitCnt
     *
     * @param totalRecruitCnt new value
     */
    private void innerSetTotalRecruitCnt(int totalRecruitCnt) {
        this.totalRecruitCnt = totalRecruitCnt;
    }

    /**
     * get freeRecruitCnt
     *
     * @return freeRecruitCnt value
     */
    public int getFreeRecruitCnt() {
        return this.freeRecruitCnt;
    }

    /**
     * set freeRecruitCnt && set marked
     *
     * @param freeRecruitCnt new value
     * @return current object
     */
    public RecruitPoolProp setFreeRecruitCnt(int freeRecruitCnt) {
        if (this.freeRecruitCnt != freeRecruitCnt) {
            this.mark(FIELD_INDEX_FREERECRUITCNT);
            this.freeRecruitCnt = freeRecruitCnt;
        }
        return this;
    }

    /**
     * inner set freeRecruitCnt
     *
     * @param freeRecruitCnt new value
     */
    private void innerSetFreeRecruitCnt(int freeRecruitCnt) {
        this.freeRecruitCnt = freeRecruitCnt;
    }

    /**
     * get nextFreeRecruitTime
     *
     * @return nextFreeRecruitTime value
     */
    public long getNextFreeRecruitTime() {
        return this.nextFreeRecruitTime;
    }

    /**
     * set nextFreeRecruitTime && set marked
     *
     * @param nextFreeRecruitTime new value
     * @return current object
     */
    public RecruitPoolProp setNextFreeRecruitTime(long nextFreeRecruitTime) {
        if (this.nextFreeRecruitTime != nextFreeRecruitTime) {
            this.mark(FIELD_INDEX_NEXTFREERECRUITTIME);
            this.nextFreeRecruitTime = nextFreeRecruitTime;
        }
        return this;
    }

    /**
     * inner set nextFreeRecruitTime
     *
     * @param nextFreeRecruitTime new value
     */
    private void innerSetNextFreeRecruitTime(long nextFreeRecruitTime) {
        this.nextFreeRecruitTime = nextFreeRecruitTime;
    }

    /**
     * get fullRecruited
     *
     * @return fullRecruited value
     */
    public boolean getFullRecruited() {
        return this.fullRecruited;
    }

    /**
     * set fullRecruited && set marked
     *
     * @param fullRecruited new value
     * @return current object
     */
    public RecruitPoolProp setFullRecruited(boolean fullRecruited) {
        if (this.fullRecruited != fullRecruited) {
            this.mark(FIELD_INDEX_FULLRECRUITED);
            this.fullRecruited = fullRecruited;
        }
        return this;
    }

    /**
     * inner set fullRecruited
     *
     * @param fullRecruited new value
     */
    private void innerSetFullRecruited(boolean fullRecruited) {
        this.fullRecruited = fullRecruited;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RecruitPoolPB.Builder getCopyCsBuilder() {
        final RecruitPoolPB.Builder builder = RecruitPoolPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(RecruitPoolPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRecruitPoolType() != RecruitPoolType.forNumber(0)) {
            builder.setRecruitPoolType(this.getRecruitPoolType());
            fieldCnt++;
        }  else if (builder.hasRecruitPoolType()) {
            // 清理RecruitPoolType
            builder.clearRecruitPoolType();
            fieldCnt++;
        }
        if (this.getDailyRecruitCnt() != 0) {
            builder.setDailyRecruitCnt(this.getDailyRecruitCnt());
            fieldCnt++;
        }  else if (builder.hasDailyRecruitCnt()) {
            // 清理DailyRecruitCnt
            builder.clearDailyRecruitCnt();
            fieldCnt++;
        }
        if (this.getTotalRecruitCnt() != 0) {
            builder.setTotalRecruitCnt(this.getTotalRecruitCnt());
            fieldCnt++;
        }  else if (builder.hasTotalRecruitCnt()) {
            // 清理TotalRecruitCnt
            builder.clearTotalRecruitCnt();
            fieldCnt++;
        }
        if (this.getFreeRecruitCnt() != 0) {
            builder.setFreeRecruitCnt(this.getFreeRecruitCnt());
            fieldCnt++;
        }  else if (builder.hasFreeRecruitCnt()) {
            // 清理FreeRecruitCnt
            builder.clearFreeRecruitCnt();
            fieldCnt++;
        }
        if (this.getNextFreeRecruitTime() != 0L) {
            builder.setNextFreeRecruitTime(this.getNextFreeRecruitTime());
            fieldCnt++;
        }  else if (builder.hasNextFreeRecruitTime()) {
            // 清理NextFreeRecruitTime
            builder.clearNextFreeRecruitTime();
            fieldCnt++;
        }
        if (this.getFullRecruited()) {
            builder.setFullRecruited(this.getFullRecruited());
            fieldCnt++;
        }  else if (builder.hasFullRecruited()) {
            // 清理FullRecruited
            builder.clearFullRecruited();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(RecruitPoolPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECRUITPOOLTYPE)) {
            builder.setRecruitPoolType(this.getRecruitPoolType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYRECRUITCNT)) {
            builder.setDailyRecruitCnt(this.getDailyRecruitCnt());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALRECRUITCNT)) {
            builder.setTotalRecruitCnt(this.getTotalRecruitCnt());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREERECRUITCNT)) {
            builder.setFreeRecruitCnt(this.getFreeRecruitCnt());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTFREERECRUITTIME)) {
            builder.setNextFreeRecruitTime(this.getNextFreeRecruitTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FULLRECRUITED)) {
            builder.setFullRecruited(this.getFullRecruited());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(RecruitPoolPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECRUITPOOLTYPE)) {
            builder.setRecruitPoolType(this.getRecruitPoolType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYRECRUITCNT)) {
            builder.setDailyRecruitCnt(this.getDailyRecruitCnt());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALRECRUITCNT)) {
            builder.setTotalRecruitCnt(this.getTotalRecruitCnt());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREERECRUITCNT)) {
            builder.setFreeRecruitCnt(this.getFreeRecruitCnt());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTFREERECRUITTIME)) {
            builder.setNextFreeRecruitTime(this.getNextFreeRecruitTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FULLRECRUITED)) {
            builder.setFullRecruited(this.getFullRecruited());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(RecruitPoolPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRecruitPoolType()) {
            this.innerSetRecruitPoolType(proto.getRecruitPoolType());
        } else {
            this.innerSetRecruitPoolType(RecruitPoolType.forNumber(0));
        }
        if (proto.hasDailyRecruitCnt()) {
            this.innerSetDailyRecruitCnt(proto.getDailyRecruitCnt());
        } else {
            this.innerSetDailyRecruitCnt(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTotalRecruitCnt()) {
            this.innerSetTotalRecruitCnt(proto.getTotalRecruitCnt());
        } else {
            this.innerSetTotalRecruitCnt(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFreeRecruitCnt()) {
            this.innerSetFreeRecruitCnt(proto.getFreeRecruitCnt());
        } else {
            this.innerSetFreeRecruitCnt(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNextFreeRecruitTime()) {
            this.innerSetNextFreeRecruitTime(proto.getNextFreeRecruitTime());
        } else {
            this.innerSetNextFreeRecruitTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasFullRecruited()) {
            this.innerSetFullRecruited(proto.getFullRecruited());
        } else {
            this.innerSetFullRecruited(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return RecruitPoolProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(RecruitPoolPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRecruitPoolType()) {
            this.setRecruitPoolType(proto.getRecruitPoolType());
            fieldCnt++;
        }
        if (proto.hasDailyRecruitCnt()) {
            this.setDailyRecruitCnt(proto.getDailyRecruitCnt());
            fieldCnt++;
        }
        if (proto.hasTotalRecruitCnt()) {
            this.setTotalRecruitCnt(proto.getTotalRecruitCnt());
            fieldCnt++;
        }
        if (proto.hasFreeRecruitCnt()) {
            this.setFreeRecruitCnt(proto.getFreeRecruitCnt());
            fieldCnt++;
        }
        if (proto.hasNextFreeRecruitTime()) {
            this.setNextFreeRecruitTime(proto.getNextFreeRecruitTime());
            fieldCnt++;
        }
        if (proto.hasFullRecruited()) {
            this.setFullRecruited(proto.getFullRecruited());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RecruitPool.Builder getCopyDbBuilder() {
        final RecruitPool.Builder builder = RecruitPool.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(RecruitPool.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRecruitPoolType() != RecruitPoolType.forNumber(0)) {
            builder.setRecruitPoolType(this.getRecruitPoolType());
            fieldCnt++;
        }  else if (builder.hasRecruitPoolType()) {
            // 清理RecruitPoolType
            builder.clearRecruitPoolType();
            fieldCnt++;
        }
        if (this.getDailyRecruitCnt() != 0) {
            builder.setDailyRecruitCnt(this.getDailyRecruitCnt());
            fieldCnt++;
        }  else if (builder.hasDailyRecruitCnt()) {
            // 清理DailyRecruitCnt
            builder.clearDailyRecruitCnt();
            fieldCnt++;
        }
        if (this.getTotalRecruitCnt() != 0) {
            builder.setTotalRecruitCnt(this.getTotalRecruitCnt());
            fieldCnt++;
        }  else if (builder.hasTotalRecruitCnt()) {
            // 清理TotalRecruitCnt
            builder.clearTotalRecruitCnt();
            fieldCnt++;
        }
        if (this.getFreeRecruitCnt() != 0) {
            builder.setFreeRecruitCnt(this.getFreeRecruitCnt());
            fieldCnt++;
        }  else if (builder.hasFreeRecruitCnt()) {
            // 清理FreeRecruitCnt
            builder.clearFreeRecruitCnt();
            fieldCnt++;
        }
        if (this.getNextFreeRecruitTime() != 0L) {
            builder.setNextFreeRecruitTime(this.getNextFreeRecruitTime());
            fieldCnt++;
        }  else if (builder.hasNextFreeRecruitTime()) {
            // 清理NextFreeRecruitTime
            builder.clearNextFreeRecruitTime();
            fieldCnt++;
        }
        if (this.getFullRecruited()) {
            builder.setFullRecruited(this.getFullRecruited());
            fieldCnt++;
        }  else if (builder.hasFullRecruited()) {
            // 清理FullRecruited
            builder.clearFullRecruited();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(RecruitPool.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECRUITPOOLTYPE)) {
            builder.setRecruitPoolType(this.getRecruitPoolType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYRECRUITCNT)) {
            builder.setDailyRecruitCnt(this.getDailyRecruitCnt());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALRECRUITCNT)) {
            builder.setTotalRecruitCnt(this.getTotalRecruitCnt());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREERECRUITCNT)) {
            builder.setFreeRecruitCnt(this.getFreeRecruitCnt());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTFREERECRUITTIME)) {
            builder.setNextFreeRecruitTime(this.getNextFreeRecruitTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FULLRECRUITED)) {
            builder.setFullRecruited(this.getFullRecruited());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(RecruitPool proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRecruitPoolType()) {
            this.innerSetRecruitPoolType(proto.getRecruitPoolType());
        } else {
            this.innerSetRecruitPoolType(RecruitPoolType.forNumber(0));
        }
        if (proto.hasDailyRecruitCnt()) {
            this.innerSetDailyRecruitCnt(proto.getDailyRecruitCnt());
        } else {
            this.innerSetDailyRecruitCnt(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTotalRecruitCnt()) {
            this.innerSetTotalRecruitCnt(proto.getTotalRecruitCnt());
        } else {
            this.innerSetTotalRecruitCnt(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFreeRecruitCnt()) {
            this.innerSetFreeRecruitCnt(proto.getFreeRecruitCnt());
        } else {
            this.innerSetFreeRecruitCnt(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNextFreeRecruitTime()) {
            this.innerSetNextFreeRecruitTime(proto.getNextFreeRecruitTime());
        } else {
            this.innerSetNextFreeRecruitTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasFullRecruited()) {
            this.innerSetFullRecruited(proto.getFullRecruited());
        } else {
            this.innerSetFullRecruited(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return RecruitPoolProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(RecruitPool proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRecruitPoolType()) {
            this.setRecruitPoolType(proto.getRecruitPoolType());
            fieldCnt++;
        }
        if (proto.hasDailyRecruitCnt()) {
            this.setDailyRecruitCnt(proto.getDailyRecruitCnt());
            fieldCnt++;
        }
        if (proto.hasTotalRecruitCnt()) {
            this.setTotalRecruitCnt(proto.getTotalRecruitCnt());
            fieldCnt++;
        }
        if (proto.hasFreeRecruitCnt()) {
            this.setFreeRecruitCnt(proto.getFreeRecruitCnt());
            fieldCnt++;
        }
        if (proto.hasNextFreeRecruitTime()) {
            this.setNextFreeRecruitTime(proto.getNextFreeRecruitTime());
            fieldCnt++;
        }
        if (proto.hasFullRecruited()) {
            this.setFullRecruited(proto.getFullRecruited());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RecruitPool.Builder getCopySsBuilder() {
        final RecruitPool.Builder builder = RecruitPool.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(RecruitPool.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRecruitPoolType() != RecruitPoolType.forNumber(0)) {
            builder.setRecruitPoolType(this.getRecruitPoolType());
            fieldCnt++;
        }  else if (builder.hasRecruitPoolType()) {
            // 清理RecruitPoolType
            builder.clearRecruitPoolType();
            fieldCnt++;
        }
        if (this.getDailyRecruitCnt() != 0) {
            builder.setDailyRecruitCnt(this.getDailyRecruitCnt());
            fieldCnt++;
        }  else if (builder.hasDailyRecruitCnt()) {
            // 清理DailyRecruitCnt
            builder.clearDailyRecruitCnt();
            fieldCnt++;
        }
        if (this.getTotalRecruitCnt() != 0) {
            builder.setTotalRecruitCnt(this.getTotalRecruitCnt());
            fieldCnt++;
        }  else if (builder.hasTotalRecruitCnt()) {
            // 清理TotalRecruitCnt
            builder.clearTotalRecruitCnt();
            fieldCnt++;
        }
        if (this.getFreeRecruitCnt() != 0) {
            builder.setFreeRecruitCnt(this.getFreeRecruitCnt());
            fieldCnt++;
        }  else if (builder.hasFreeRecruitCnt()) {
            // 清理FreeRecruitCnt
            builder.clearFreeRecruitCnt();
            fieldCnt++;
        }
        if (this.getNextFreeRecruitTime() != 0L) {
            builder.setNextFreeRecruitTime(this.getNextFreeRecruitTime());
            fieldCnt++;
        }  else if (builder.hasNextFreeRecruitTime()) {
            // 清理NextFreeRecruitTime
            builder.clearNextFreeRecruitTime();
            fieldCnt++;
        }
        if (this.getFullRecruited()) {
            builder.setFullRecruited(this.getFullRecruited());
            fieldCnt++;
        }  else if (builder.hasFullRecruited()) {
            // 清理FullRecruited
            builder.clearFullRecruited();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(RecruitPool.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECRUITPOOLTYPE)) {
            builder.setRecruitPoolType(this.getRecruitPoolType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYRECRUITCNT)) {
            builder.setDailyRecruitCnt(this.getDailyRecruitCnt());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALRECRUITCNT)) {
            builder.setTotalRecruitCnt(this.getTotalRecruitCnt());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREERECRUITCNT)) {
            builder.setFreeRecruitCnt(this.getFreeRecruitCnt());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTFREERECRUITTIME)) {
            builder.setNextFreeRecruitTime(this.getNextFreeRecruitTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FULLRECRUITED)) {
            builder.setFullRecruited(this.getFullRecruited());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(RecruitPool proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRecruitPoolType()) {
            this.innerSetRecruitPoolType(proto.getRecruitPoolType());
        } else {
            this.innerSetRecruitPoolType(RecruitPoolType.forNumber(0));
        }
        if (proto.hasDailyRecruitCnt()) {
            this.innerSetDailyRecruitCnt(proto.getDailyRecruitCnt());
        } else {
            this.innerSetDailyRecruitCnt(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTotalRecruitCnt()) {
            this.innerSetTotalRecruitCnt(proto.getTotalRecruitCnt());
        } else {
            this.innerSetTotalRecruitCnt(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFreeRecruitCnt()) {
            this.innerSetFreeRecruitCnt(proto.getFreeRecruitCnt());
        } else {
            this.innerSetFreeRecruitCnt(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNextFreeRecruitTime()) {
            this.innerSetNextFreeRecruitTime(proto.getNextFreeRecruitTime());
        } else {
            this.innerSetNextFreeRecruitTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasFullRecruited()) {
            this.innerSetFullRecruited(proto.getFullRecruited());
        } else {
            this.innerSetFullRecruited(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return RecruitPoolProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(RecruitPool proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRecruitPoolType()) {
            this.setRecruitPoolType(proto.getRecruitPoolType());
            fieldCnt++;
        }
        if (proto.hasDailyRecruitCnt()) {
            this.setDailyRecruitCnt(proto.getDailyRecruitCnt());
            fieldCnt++;
        }
        if (proto.hasTotalRecruitCnt()) {
            this.setTotalRecruitCnt(proto.getTotalRecruitCnt());
            fieldCnt++;
        }
        if (proto.hasFreeRecruitCnt()) {
            this.setFreeRecruitCnt(proto.getFreeRecruitCnt());
            fieldCnt++;
        }
        if (proto.hasNextFreeRecruitTime()) {
            this.setNextFreeRecruitTime(proto.getNextFreeRecruitTime());
            fieldCnt++;
        }
        if (proto.hasFullRecruited()) {
            this.setFullRecruited(proto.getFullRecruited());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        RecruitPool.Builder builder = RecruitPool.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof RecruitPoolProp)) {
            return false;
        }
        final RecruitPoolProp otherNode = (RecruitPoolProp) node;
        if (this.recruitPoolType != otherNode.recruitPoolType) {
            return false;
        }
        if (this.dailyRecruitCnt != otherNode.dailyRecruitCnt) {
            return false;
        }
        if (this.totalRecruitCnt != otherNode.totalRecruitCnt) {
            return false;
        }
        if (this.freeRecruitCnt != otherNode.freeRecruitCnt) {
            return false;
        }
        if (this.nextFreeRecruitTime != otherNode.nextFreeRecruitTime) {
            return false;
        }
        if (this.fullRecruited != otherNode.fullRecruited) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.PlayerPickLimit;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.PlayerPickLimitPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerPickLimitProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_GROUPID = 0;
    public static final int FIELD_INDEX_PICKINFO = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int groupId = Constant.DEFAULT_INT_VALUE;
    private PickLimitInfoListProp pickInfo = null;

    public PlayerPickLimitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerPickLimitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get groupId
     *
     * @return groupId value
     */
    public int getGroupId() {
        return this.groupId;
    }

    /**
     * set groupId && set marked
     *
     * @param groupId new value
     * @return current object
     */
    public PlayerPickLimitProp setGroupId(int groupId) {
        if (this.groupId != groupId) {
            this.mark(FIELD_INDEX_GROUPID);
            this.groupId = groupId;
        }
        return this;
    }

    /**
     * inner set groupId
     *
     * @param groupId new value
     */
    private void innerSetGroupId(int groupId) {
        this.groupId = groupId;
    }

    /**
     * get pickInfo
     *
     * @return pickInfo value
     */
    public PickLimitInfoListProp getPickInfo() {
        if (this.pickInfo == null) {
            this.pickInfo = new PickLimitInfoListProp(this, FIELD_INDEX_PICKINFO);
        }
        return this.pickInfo;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addPickInfo(PickLimitInfoProp v) {
        this.getPickInfo().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public PickLimitInfoProp getPickInfoIndex(int index) {
        return this.getPickInfo().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public PickLimitInfoProp removePickInfo(PickLimitInfoProp v) {
        if (this.pickInfo == null) {
            return null;
        }
        if(this.pickInfo.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getPickInfoSize() {
        if (this.pickInfo == null) {
            return 0;
        }
        return this.pickInfo.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isPickInfoEmpty() {
        if (this.pickInfo == null) {
            return true;
        }
        return this.getPickInfo().isEmpty();
    }

    /**
     * clear list
     */
    public void clearPickInfo() {
        this.getPickInfo().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public PickLimitInfoProp removePickInfoIndex(int index) {
        return this.getPickInfo().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public PickLimitInfoProp setPickInfoIndex(int index, PickLimitInfoProp v) {
        return this.getPickInfo().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerPickLimitPB.Builder getCopyCsBuilder() {
        final PlayerPickLimitPB.Builder builder = PlayerPickLimitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerPickLimitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGroupId() != 0) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }  else if (builder.hasGroupId()) {
            // 清理GroupId
            builder.clearGroupId();
            fieldCnt++;
        }
        if (this.pickInfo != null) {
            StructPB.PickLimitInfoListPB.Builder tmpBuilder = StructPB.PickLimitInfoListPB.newBuilder();
            final int tmpFieldCnt = this.pickInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPickInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPickInfo();
            }
        }  else if (builder.hasPickInfo()) {
            // 清理PickInfo
            builder.clearPickInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerPickLimitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICKINFO) && this.pickInfo != null) {
            final boolean needClear = !builder.hasPickInfo();
            final int tmpFieldCnt = this.pickInfo.copyChangeToCs(builder.getPickInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPickInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerPickLimitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICKINFO) && this.pickInfo != null) {
            final boolean needClear = !builder.hasPickInfo();
            final int tmpFieldCnt = this.pickInfo.copyChangeToCs(builder.getPickInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPickInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerPickLimitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGroupId()) {
            this.innerSetGroupId(proto.getGroupId());
        } else {
            this.innerSetGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPickInfo()) {
            this.getPickInfo().mergeFromCs(proto.getPickInfo());
        } else {
            if (this.pickInfo != null) {
                this.pickInfo.mergeFromCs(proto.getPickInfo());
            }
        }
        this.markAll();
        return PlayerPickLimitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerPickLimitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGroupId()) {
            this.setGroupId(proto.getGroupId());
            fieldCnt++;
        }
        if (proto.hasPickInfo()) {
            this.getPickInfo().mergeChangeFromCs(proto.getPickInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerPickLimit.Builder getCopyDbBuilder() {
        final PlayerPickLimit.Builder builder = PlayerPickLimit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerPickLimit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGroupId() != 0) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }  else if (builder.hasGroupId()) {
            // 清理GroupId
            builder.clearGroupId();
            fieldCnt++;
        }
        if (this.pickInfo != null) {
            Struct.PickLimitInfoList.Builder tmpBuilder = Struct.PickLimitInfoList.newBuilder();
            final int tmpFieldCnt = this.pickInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPickInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPickInfo();
            }
        }  else if (builder.hasPickInfo()) {
            // 清理PickInfo
            builder.clearPickInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerPickLimit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICKINFO) && this.pickInfo != null) {
            final boolean needClear = !builder.hasPickInfo();
            final int tmpFieldCnt = this.pickInfo.copyChangeToDb(builder.getPickInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPickInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerPickLimit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGroupId()) {
            this.innerSetGroupId(proto.getGroupId());
        } else {
            this.innerSetGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPickInfo()) {
            this.getPickInfo().mergeFromDb(proto.getPickInfo());
        } else {
            if (this.pickInfo != null) {
                this.pickInfo.mergeFromDb(proto.getPickInfo());
            }
        }
        this.markAll();
        return PlayerPickLimitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerPickLimit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGroupId()) {
            this.setGroupId(proto.getGroupId());
            fieldCnt++;
        }
        if (proto.hasPickInfo()) {
            this.getPickInfo().mergeChangeFromDb(proto.getPickInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerPickLimit.Builder getCopySsBuilder() {
        final PlayerPickLimit.Builder builder = PlayerPickLimit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerPickLimit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGroupId() != 0) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }  else if (builder.hasGroupId()) {
            // 清理GroupId
            builder.clearGroupId();
            fieldCnt++;
        }
        if (this.pickInfo != null) {
            Struct.PickLimitInfoList.Builder tmpBuilder = Struct.PickLimitInfoList.newBuilder();
            final int tmpFieldCnt = this.pickInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPickInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPickInfo();
            }
        }  else if (builder.hasPickInfo()) {
            // 清理PickInfo
            builder.clearPickInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerPickLimit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICKINFO) && this.pickInfo != null) {
            final boolean needClear = !builder.hasPickInfo();
            final int tmpFieldCnt = this.pickInfo.copyChangeToSs(builder.getPickInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPickInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerPickLimit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGroupId()) {
            this.innerSetGroupId(proto.getGroupId());
        } else {
            this.innerSetGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPickInfo()) {
            this.getPickInfo().mergeFromSs(proto.getPickInfo());
        } else {
            if (this.pickInfo != null) {
                this.pickInfo.mergeFromSs(proto.getPickInfo());
            }
        }
        this.markAll();
        return PlayerPickLimitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerPickLimit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGroupId()) {
            this.setGroupId(proto.getGroupId());
            fieldCnt++;
        }
        if (proto.hasPickInfo()) {
            this.getPickInfo().mergeChangeFromSs(proto.getPickInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerPickLimit.Builder builder = PlayerPickLimit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_PICKINFO) && this.pickInfo != null) {
            this.pickInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.pickInfo != null) {
            this.pickInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.groupId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerPickLimitProp)) {
            return false;
        }
        final PlayerPickLimitProp otherNode = (PlayerPickLimitProp) node;
        if (this.groupId != otherNode.groupId) {
            return false;
        }
        if (!this.getPickInfo().compareDataTo(otherNode.getPickInfo())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
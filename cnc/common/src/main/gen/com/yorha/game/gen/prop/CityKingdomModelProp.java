package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.City.CityKingdomModel;
import com.yorha.proto.City;
import com.yorha.proto.CityPB.CityKingdomModelPB;
import com.yorha.proto.CityPB;


/**
 * <AUTHOR> auto gen
 */
public class CityKingdomModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_OFFICEID = 0;
    public static final int FIELD_INDEX_SKILLMAP = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int officeId = Constant.DEFAULT_INT_VALUE;
    private Int32CityKingdomSkillInfoMapProp skillMap = null;

    public CityKingdomModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public CityKingdomModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get officeId
     *
     * @return officeId value
     */
    public int getOfficeId() {
        return this.officeId;
    }

    /**
     * set officeId && set marked
     *
     * @param officeId new value
     * @return current object
     */
    public CityKingdomModelProp setOfficeId(int officeId) {
        if (this.officeId != officeId) {
            this.mark(FIELD_INDEX_OFFICEID);
            this.officeId = officeId;
        }
        return this;
    }

    /**
     * inner set officeId
     *
     * @param officeId new value
     */
    private void innerSetOfficeId(int officeId) {
        this.officeId = officeId;
    }

    /**
     * get skillMap
     *
     * @return skillMap value
     */
    public Int32CityKingdomSkillInfoMapProp getSkillMap() {
        if (this.skillMap == null) {
            this.skillMap = new Int32CityKingdomSkillInfoMapProp(this, FIELD_INDEX_SKILLMAP);
        }
        return this.skillMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putSkillMapV(CityKingdomSkillInfoProp v) {
        this.getSkillMap().put(v.getSkillId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public CityKingdomSkillInfoProp addEmptySkillMap(Integer k) {
        return this.getSkillMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getSkillMapSize() {
        if (this.skillMap == null) {
            return 0;
        }
        return this.skillMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isSkillMapEmpty() {
        if (this.skillMap == null) {
            return true;
        }
        return this.skillMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public CityKingdomSkillInfoProp getSkillMapV(Integer k) {
        if (this.skillMap == null || !this.skillMap.containsKey(k)) {
            return null;
        }
        return this.skillMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearSkillMap() {
        if (this.skillMap != null) {
            this.skillMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeSkillMapV(Integer k) {
        if (this.skillMap != null) {
            this.skillMap.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CityKingdomModelPB.Builder getCopyCsBuilder() {
        final CityKingdomModelPB.Builder builder = CityKingdomModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(CityKingdomModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getOfficeId() != 0) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }  else if (builder.hasOfficeId()) {
            // 清理OfficeId
            builder.clearOfficeId();
            fieldCnt++;
        }
        if (this.skillMap != null) {
            CityPB.Int32CityKingdomSkillInfoMapPB.Builder tmpBuilder = CityPB.Int32CityKingdomSkillInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.skillMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkillMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkillMap();
            }
        }  else if (builder.hasSkillMap()) {
            // 清理SkillMap
            builder.clearSkillMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(CityKingdomModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OFFICEID)) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLMAP) && this.skillMap != null) {
            final boolean needClear = !builder.hasSkillMap();
            final int tmpFieldCnt = this.skillMap.copyChangeToCs(builder.getSkillMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillMap();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(CityKingdomModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OFFICEID)) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLMAP) && this.skillMap != null) {
            final boolean needClear = !builder.hasSkillMap();
            final int tmpFieldCnt = this.skillMap.copyChangeToAndClearDeleteKeysCs(builder.getSkillMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(CityKingdomModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOfficeId()) {
            this.innerSetOfficeId(proto.getOfficeId());
        } else {
            this.innerSetOfficeId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkillMap()) {
            this.getSkillMap().mergeFromCs(proto.getSkillMap());
        } else {
            if (this.skillMap != null) {
                this.skillMap.mergeFromCs(proto.getSkillMap());
            }
        }
        this.markAll();
        return CityKingdomModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(CityKingdomModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOfficeId()) {
            this.setOfficeId(proto.getOfficeId());
            fieldCnt++;
        }
        if (proto.hasSkillMap()) {
            this.getSkillMap().mergeChangeFromCs(proto.getSkillMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CityKingdomModel.Builder getCopyDbBuilder() {
        final CityKingdomModel.Builder builder = CityKingdomModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(CityKingdomModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getOfficeId() != 0) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }  else if (builder.hasOfficeId()) {
            // 清理OfficeId
            builder.clearOfficeId();
            fieldCnt++;
        }
        if (this.skillMap != null) {
            City.Int32CityKingdomSkillInfoMap.Builder tmpBuilder = City.Int32CityKingdomSkillInfoMap.newBuilder();
            final int tmpFieldCnt = this.skillMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkillMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkillMap();
            }
        }  else if (builder.hasSkillMap()) {
            // 清理SkillMap
            builder.clearSkillMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(CityKingdomModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OFFICEID)) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLMAP) && this.skillMap != null) {
            final boolean needClear = !builder.hasSkillMap();
            final int tmpFieldCnt = this.skillMap.copyChangeToDb(builder.getSkillMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(CityKingdomModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOfficeId()) {
            this.innerSetOfficeId(proto.getOfficeId());
        } else {
            this.innerSetOfficeId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkillMap()) {
            this.getSkillMap().mergeFromDb(proto.getSkillMap());
        } else {
            if (this.skillMap != null) {
                this.skillMap.mergeFromDb(proto.getSkillMap());
            }
        }
        this.markAll();
        return CityKingdomModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(CityKingdomModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOfficeId()) {
            this.setOfficeId(proto.getOfficeId());
            fieldCnt++;
        }
        if (proto.hasSkillMap()) {
            this.getSkillMap().mergeChangeFromDb(proto.getSkillMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CityKingdomModel.Builder getCopySsBuilder() {
        final CityKingdomModel.Builder builder = CityKingdomModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(CityKingdomModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getOfficeId() != 0) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }  else if (builder.hasOfficeId()) {
            // 清理OfficeId
            builder.clearOfficeId();
            fieldCnt++;
        }
        if (this.skillMap != null) {
            City.Int32CityKingdomSkillInfoMap.Builder tmpBuilder = City.Int32CityKingdomSkillInfoMap.newBuilder();
            final int tmpFieldCnt = this.skillMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkillMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkillMap();
            }
        }  else if (builder.hasSkillMap()) {
            // 清理SkillMap
            builder.clearSkillMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(CityKingdomModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OFFICEID)) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLMAP) && this.skillMap != null) {
            final boolean needClear = !builder.hasSkillMap();
            final int tmpFieldCnt = this.skillMap.copyChangeToSs(builder.getSkillMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(CityKingdomModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOfficeId()) {
            this.innerSetOfficeId(proto.getOfficeId());
        } else {
            this.innerSetOfficeId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkillMap()) {
            this.getSkillMap().mergeFromSs(proto.getSkillMap());
        } else {
            if (this.skillMap != null) {
                this.skillMap.mergeFromSs(proto.getSkillMap());
            }
        }
        this.markAll();
        return CityKingdomModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(CityKingdomModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOfficeId()) {
            this.setOfficeId(proto.getOfficeId());
            fieldCnt++;
        }
        if (proto.hasSkillMap()) {
            this.getSkillMap().mergeChangeFromSs(proto.getSkillMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        CityKingdomModel.Builder builder = CityKingdomModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SKILLMAP) && this.skillMap != null) {
            this.skillMap.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.skillMap != null) {
            this.skillMap.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof CityKingdomModelProp)) {
            return false;
        }
        final CityKingdomModelProp otherNode = (CityKingdomModelProp) node;
        if (this.officeId != otherNode.officeId) {
            return false;
        }
        if (!this.getSkillMap().compareDataTo(otherNode.getSkillMap())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
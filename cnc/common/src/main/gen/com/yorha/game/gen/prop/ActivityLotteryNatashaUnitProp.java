package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityLotteryNatashaUnit;
import com.yorha.proto.Basic;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ActivityLotteryNatashaUnitPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityLotteryNatashaUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_DAILYTASKS = 0;
    public static final int FIELD_INDEX_RECHARGETASKS = 1;
    public static final int FIELD_INDEX_ISRECHARGED = 2;
    public static final int FIELD_INDEX_RECEIVEDBOXID = 3;
    public static final int FIELD_INDEX_DRAWNUMSMALL = 4;
    public static final int FIELD_INDEX_DRAWNUMLARGE = 5;
    public static final int FIELD_INDEX_TOTALDRAWTIMES = 6;

    public static final int FIELD_COUNT = 7;

    private long markBits0 = 0L;

    private Int32TaskInfoMapProp dailyTasks = null;
    private Int32TaskInfoMapProp rechargeTasks = null;
    private boolean isRecharged = Constant.DEFAULT_BOOLEAN_VALUE;
    private Int32SetProp receivedBoxId = null;
    private int drawNumSmall = Constant.DEFAULT_INT_VALUE;
    private int drawNumLarge = Constant.DEFAULT_INT_VALUE;
    private int totalDrawTimes = Constant.DEFAULT_INT_VALUE;

    public ActivityLotteryNatashaUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityLotteryNatashaUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get dailyTasks
     *
     * @return dailyTasks value
     */
    public Int32TaskInfoMapProp getDailyTasks() {
        if (this.dailyTasks == null) {
            this.dailyTasks = new Int32TaskInfoMapProp(this, FIELD_INDEX_DAILYTASKS);
        }
        return this.dailyTasks;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putDailyTasksV(TaskInfoProp v) {
        this.getDailyTasks().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public TaskInfoProp addEmptyDailyTasks(Integer k) {
        return this.getDailyTasks().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getDailyTasksSize() {
        if (this.dailyTasks == null) {
            return 0;
        }
        return this.dailyTasks.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isDailyTasksEmpty() {
        if (this.dailyTasks == null) {
            return true;
        }
        return this.dailyTasks.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public TaskInfoProp getDailyTasksV(Integer k) {
        if (this.dailyTasks == null || !this.dailyTasks.containsKey(k)) {
            return null;
        }
        return this.dailyTasks.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearDailyTasks() {
        if (this.dailyTasks != null) {
            this.dailyTasks.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeDailyTasksV(Integer k) {
        if (this.dailyTasks != null) {
            this.dailyTasks.remove(k);
        }
    }
    /**
     * get rechargeTasks
     *
     * @return rechargeTasks value
     */
    public Int32TaskInfoMapProp getRechargeTasks() {
        if (this.rechargeTasks == null) {
            this.rechargeTasks = new Int32TaskInfoMapProp(this, FIELD_INDEX_RECHARGETASKS);
        }
        return this.rechargeTasks;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putRechargeTasksV(TaskInfoProp v) {
        this.getRechargeTasks().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public TaskInfoProp addEmptyRechargeTasks(Integer k) {
        return this.getRechargeTasks().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getRechargeTasksSize() {
        if (this.rechargeTasks == null) {
            return 0;
        }
        return this.rechargeTasks.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isRechargeTasksEmpty() {
        if (this.rechargeTasks == null) {
            return true;
        }
        return this.rechargeTasks.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public TaskInfoProp getRechargeTasksV(Integer k) {
        if (this.rechargeTasks == null || !this.rechargeTasks.containsKey(k)) {
            return null;
        }
        return this.rechargeTasks.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearRechargeTasks() {
        if (this.rechargeTasks != null) {
            this.rechargeTasks.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeRechargeTasksV(Integer k) {
        if (this.rechargeTasks != null) {
            this.rechargeTasks.remove(k);
        }
    }
    /**
     * get isRecharged
     *
     * @return isRecharged value
     */
    public boolean getIsRecharged() {
        return this.isRecharged;
    }

    /**
     * set isRecharged && set marked
     *
     * @param isRecharged new value
     * @return current object
     */
    public ActivityLotteryNatashaUnitProp setIsRecharged(boolean isRecharged) {
        if (this.isRecharged != isRecharged) {
            this.mark(FIELD_INDEX_ISRECHARGED);
            this.isRecharged = isRecharged;
        }
        return this;
    }

    /**
     * inner set isRecharged
     *
     * @param isRecharged new value
     */
    private void innerSetIsRecharged(boolean isRecharged) {
        this.isRecharged = isRecharged;
    }

    /**
     * get receivedBoxId
     *
     * @return receivedBoxId value
     */
    public Int32SetProp getReceivedBoxId() {
        if (this.receivedBoxId == null) {
            this.receivedBoxId = new Int32SetProp(this, FIELD_INDEX_RECEIVEDBOXID);
        }
        return this.receivedBoxId;
    }


    /**
     * add value to set
     *
     * @param e value
     */
    public void addReceivedBoxId(Integer e) {
        this.getReceivedBoxId().add(e);
    }

    /**
     * remove the specified element in set
     *
     * @param e element
     * @return e if success or null by fail
     */
    public Integer removeReceivedBoxId(Integer e) {
        if (this.receivedBoxId == null) {
            return null;
        }
        if(this.receivedBoxId.remove(e)) {
            return e;
        }
        return null;
    }

    /**
     * get set size
     *
     * @return set size
     */
    public int getReceivedBoxIdSize() {
        if (this.receivedBoxId == null) {
            return 0;
        }
        return this.receivedBoxId.size();
    }

    /**
     * get is a empty set
     *
     * @return true if empty
     */
    public boolean isReceivedBoxIdEmpty() {
        if (this.receivedBoxId == null) {
            return true;
        }
        return this.getReceivedBoxId().isEmpty();
    }

    /**
     * clear set
     */
    public void clearReceivedBoxId() {
        this.getReceivedBoxId().clear();
    }

    /**
     * Returns true if this set contains the specified element.
     *
     * @param e elem
     * @return true if this set contains the specified element, false otherwise
     */
    public boolean isReceivedBoxIdContains(Integer e) {
        return this.receivedBoxId != null && this.receivedBoxId.contains(e);
    }

    /**
     * get drawNumSmall
     *
     * @return drawNumSmall value
     */
    public int getDrawNumSmall() {
        return this.drawNumSmall;
    }

    /**
     * set drawNumSmall && set marked
     *
     * @param drawNumSmall new value
     * @return current object
     */
    public ActivityLotteryNatashaUnitProp setDrawNumSmall(int drawNumSmall) {
        if (this.drawNumSmall != drawNumSmall) {
            this.mark(FIELD_INDEX_DRAWNUMSMALL);
            this.drawNumSmall = drawNumSmall;
        }
        return this;
    }

    /**
     * inner set drawNumSmall
     *
     * @param drawNumSmall new value
     */
    private void innerSetDrawNumSmall(int drawNumSmall) {
        this.drawNumSmall = drawNumSmall;
    }

    /**
     * get drawNumLarge
     *
     * @return drawNumLarge value
     */
    public int getDrawNumLarge() {
        return this.drawNumLarge;
    }

    /**
     * set drawNumLarge && set marked
     *
     * @param drawNumLarge new value
     * @return current object
     */
    public ActivityLotteryNatashaUnitProp setDrawNumLarge(int drawNumLarge) {
        if (this.drawNumLarge != drawNumLarge) {
            this.mark(FIELD_INDEX_DRAWNUMLARGE);
            this.drawNumLarge = drawNumLarge;
        }
        return this;
    }

    /**
     * inner set drawNumLarge
     *
     * @param drawNumLarge new value
     */
    private void innerSetDrawNumLarge(int drawNumLarge) {
        this.drawNumLarge = drawNumLarge;
    }

    /**
     * get totalDrawTimes
     *
     * @return totalDrawTimes value
     */
    public int getTotalDrawTimes() {
        return this.totalDrawTimes;
    }

    /**
     * set totalDrawTimes && set marked
     *
     * @param totalDrawTimes new value
     * @return current object
     */
    public ActivityLotteryNatashaUnitProp setTotalDrawTimes(int totalDrawTimes) {
        if (this.totalDrawTimes != totalDrawTimes) {
            this.mark(FIELD_INDEX_TOTALDRAWTIMES);
            this.totalDrawTimes = totalDrawTimes;
        }
        return this;
    }

    /**
     * inner set totalDrawTimes
     *
     * @param totalDrawTimes new value
     */
    private void innerSetTotalDrawTimes(int totalDrawTimes) {
        this.totalDrawTimes = totalDrawTimes;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityLotteryNatashaUnitPB.Builder getCopyCsBuilder() {
        final ActivityLotteryNatashaUnitPB.Builder builder = ActivityLotteryNatashaUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityLotteryNatashaUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.dailyTasks != null) {
            StructPB.Int32TaskInfoMapPB.Builder tmpBuilder = StructPB.Int32TaskInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.dailyTasks.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDailyTasks(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDailyTasks();
            }
        }  else if (builder.hasDailyTasks()) {
            // 清理DailyTasks
            builder.clearDailyTasks();
            fieldCnt++;
        }
        if (this.rechargeTasks != null) {
            StructPB.Int32TaskInfoMapPB.Builder tmpBuilder = StructPB.Int32TaskInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.rechargeTasks.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRechargeTasks(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRechargeTasks();
            }
        }  else if (builder.hasRechargeTasks()) {
            // 清理RechargeTasks
            builder.clearRechargeTasks();
            fieldCnt++;
        }
        if (this.getIsRecharged()) {
            builder.setIsRecharged(this.getIsRecharged());
            fieldCnt++;
        }  else if (builder.hasIsRecharged()) {
            // 清理IsRecharged
            builder.clearIsRecharged();
            fieldCnt++;
        }
        if (this.receivedBoxId != null) {
            BasicPB.Int32SetPB.Builder tmpBuilder = BasicPB.Int32SetPB.newBuilder();
            final int tmpFieldCnt = this.receivedBoxId.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setReceivedBoxId(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearReceivedBoxId();
            }
        }  else if (builder.hasReceivedBoxId()) {
            // 清理ReceivedBoxId
            builder.clearReceivedBoxId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityLotteryNatashaUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DAILYTASKS) && this.dailyTasks != null) {
            final boolean needClear = !builder.hasDailyTasks();
            final int tmpFieldCnt = this.dailyTasks.copyChangeToCs(builder.getDailyTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDailyTasks();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECHARGETASKS) && this.rechargeTasks != null) {
            final boolean needClear = !builder.hasRechargeTasks();
            final int tmpFieldCnt = this.rechargeTasks.copyChangeToCs(builder.getRechargeTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRechargeTasks();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISRECHARGED)) {
            builder.setIsRecharged(this.getIsRecharged());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RECEIVEDBOXID) && this.receivedBoxId != null) {
            final boolean needClear = !builder.hasReceivedBoxId();
            final int tmpFieldCnt = this.receivedBoxId.copyChangeToCs(builder.getReceivedBoxIdBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearReceivedBoxId();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityLotteryNatashaUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DAILYTASKS) && this.dailyTasks != null) {
            final boolean needClear = !builder.hasDailyTasks();
            final int tmpFieldCnt = this.dailyTasks.copyChangeToAndClearDeleteKeysCs(builder.getDailyTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDailyTasks();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECHARGETASKS) && this.rechargeTasks != null) {
            final boolean needClear = !builder.hasRechargeTasks();
            final int tmpFieldCnt = this.rechargeTasks.copyChangeToAndClearDeleteKeysCs(builder.getRechargeTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRechargeTasks();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISRECHARGED)) {
            builder.setIsRecharged(this.getIsRecharged());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RECEIVEDBOXID) && this.receivedBoxId != null) {
            final boolean needClear = !builder.hasReceivedBoxId();
            final int tmpFieldCnt = this.receivedBoxId.copyChangeToAndClearDeleteKeysCs(builder.getReceivedBoxIdBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearReceivedBoxId();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityLotteryNatashaUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDailyTasks()) {
            this.getDailyTasks().mergeFromCs(proto.getDailyTasks());
        } else {
            if (this.dailyTasks != null) {
                this.dailyTasks.mergeFromCs(proto.getDailyTasks());
            }
        }
        if (proto.hasRechargeTasks()) {
            this.getRechargeTasks().mergeFromCs(proto.getRechargeTasks());
        } else {
            if (this.rechargeTasks != null) {
                this.rechargeTasks.mergeFromCs(proto.getRechargeTasks());
            }
        }
        if (proto.hasIsRecharged()) {
            this.innerSetIsRecharged(proto.getIsRecharged());
        } else {
            this.innerSetIsRecharged(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasReceivedBoxId()) {
            this.getReceivedBoxId().mergeFromCs(proto.getReceivedBoxId());
        } else {
            if (this.receivedBoxId != null) {
                this.receivedBoxId.mergeFromCs(proto.getReceivedBoxId());
            }
        }
        this.markAll();
        return ActivityLotteryNatashaUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityLotteryNatashaUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDailyTasks()) {
            this.getDailyTasks().mergeChangeFromCs(proto.getDailyTasks());
            fieldCnt++;
        }
        if (proto.hasRechargeTasks()) {
            this.getRechargeTasks().mergeChangeFromCs(proto.getRechargeTasks());
            fieldCnt++;
        }
        if (proto.hasIsRecharged()) {
            this.setIsRecharged(proto.getIsRecharged());
            fieldCnt++;
        }
        if (proto.hasReceivedBoxId()) {
            this.getReceivedBoxId().mergeChangeFromCs(proto.getReceivedBoxId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityLotteryNatashaUnit.Builder getCopyDbBuilder() {
        final ActivityLotteryNatashaUnit.Builder builder = ActivityLotteryNatashaUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityLotteryNatashaUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.dailyTasks != null) {
            Struct.Int32TaskInfoMap.Builder tmpBuilder = Struct.Int32TaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.dailyTasks.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDailyTasks(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDailyTasks();
            }
        }  else if (builder.hasDailyTasks()) {
            // 清理DailyTasks
            builder.clearDailyTasks();
            fieldCnt++;
        }
        if (this.rechargeTasks != null) {
            Struct.Int32TaskInfoMap.Builder tmpBuilder = Struct.Int32TaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.rechargeTasks.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRechargeTasks(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRechargeTasks();
            }
        }  else if (builder.hasRechargeTasks()) {
            // 清理RechargeTasks
            builder.clearRechargeTasks();
            fieldCnt++;
        }
        if (this.getIsRecharged()) {
            builder.setIsRecharged(this.getIsRecharged());
            fieldCnt++;
        }  else if (builder.hasIsRecharged()) {
            // 清理IsRecharged
            builder.clearIsRecharged();
            fieldCnt++;
        }
        if (this.receivedBoxId != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.receivedBoxId.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setReceivedBoxId(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearReceivedBoxId();
            }
        }  else if (builder.hasReceivedBoxId()) {
            // 清理ReceivedBoxId
            builder.clearReceivedBoxId();
            fieldCnt++;
        }
        if (this.getDrawNumSmall() != 0) {
            builder.setDrawNumSmall(this.getDrawNumSmall());
            fieldCnt++;
        }  else if (builder.hasDrawNumSmall()) {
            // 清理DrawNumSmall
            builder.clearDrawNumSmall();
            fieldCnt++;
        }
        if (this.getDrawNumLarge() != 0) {
            builder.setDrawNumLarge(this.getDrawNumLarge());
            fieldCnt++;
        }  else if (builder.hasDrawNumLarge()) {
            // 清理DrawNumLarge
            builder.clearDrawNumLarge();
            fieldCnt++;
        }
        if (this.getTotalDrawTimes() != 0) {
            builder.setTotalDrawTimes(this.getTotalDrawTimes());
            fieldCnt++;
        }  else if (builder.hasTotalDrawTimes()) {
            // 清理TotalDrawTimes
            builder.clearTotalDrawTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityLotteryNatashaUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DAILYTASKS) && this.dailyTasks != null) {
            final boolean needClear = !builder.hasDailyTasks();
            final int tmpFieldCnt = this.dailyTasks.copyChangeToDb(builder.getDailyTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDailyTasks();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECHARGETASKS) && this.rechargeTasks != null) {
            final boolean needClear = !builder.hasRechargeTasks();
            final int tmpFieldCnt = this.rechargeTasks.copyChangeToDb(builder.getRechargeTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRechargeTasks();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISRECHARGED)) {
            builder.setIsRecharged(this.getIsRecharged());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RECEIVEDBOXID) && this.receivedBoxId != null) {
            final boolean needClear = !builder.hasReceivedBoxId();
            final int tmpFieldCnt = this.receivedBoxId.copyChangeToDb(builder.getReceivedBoxIdBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearReceivedBoxId();
            }
        }
        if (this.hasMark(FIELD_INDEX_DRAWNUMSMALL)) {
            builder.setDrawNumSmall(this.getDrawNumSmall());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DRAWNUMLARGE)) {
            builder.setDrawNumLarge(this.getDrawNumLarge());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALDRAWTIMES)) {
            builder.setTotalDrawTimes(this.getTotalDrawTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityLotteryNatashaUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDailyTasks()) {
            this.getDailyTasks().mergeFromDb(proto.getDailyTasks());
        } else {
            if (this.dailyTasks != null) {
                this.dailyTasks.mergeFromDb(proto.getDailyTasks());
            }
        }
        if (proto.hasRechargeTasks()) {
            this.getRechargeTasks().mergeFromDb(proto.getRechargeTasks());
        } else {
            if (this.rechargeTasks != null) {
                this.rechargeTasks.mergeFromDb(proto.getRechargeTasks());
            }
        }
        if (proto.hasIsRecharged()) {
            this.innerSetIsRecharged(proto.getIsRecharged());
        } else {
            this.innerSetIsRecharged(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasReceivedBoxId()) {
            this.getReceivedBoxId().mergeFromDb(proto.getReceivedBoxId());
        } else {
            if (this.receivedBoxId != null) {
                this.receivedBoxId.mergeFromDb(proto.getReceivedBoxId());
            }
        }
        if (proto.hasDrawNumSmall()) {
            this.innerSetDrawNumSmall(proto.getDrawNumSmall());
        } else {
            this.innerSetDrawNumSmall(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDrawNumLarge()) {
            this.innerSetDrawNumLarge(proto.getDrawNumLarge());
        } else {
            this.innerSetDrawNumLarge(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTotalDrawTimes()) {
            this.innerSetTotalDrawTimes(proto.getTotalDrawTimes());
        } else {
            this.innerSetTotalDrawTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivityLotteryNatashaUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityLotteryNatashaUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDailyTasks()) {
            this.getDailyTasks().mergeChangeFromDb(proto.getDailyTasks());
            fieldCnt++;
        }
        if (proto.hasRechargeTasks()) {
            this.getRechargeTasks().mergeChangeFromDb(proto.getRechargeTasks());
            fieldCnt++;
        }
        if (proto.hasIsRecharged()) {
            this.setIsRecharged(proto.getIsRecharged());
            fieldCnt++;
        }
        if (proto.hasReceivedBoxId()) {
            this.getReceivedBoxId().mergeChangeFromDb(proto.getReceivedBoxId());
            fieldCnt++;
        }
        if (proto.hasDrawNumSmall()) {
            this.setDrawNumSmall(proto.getDrawNumSmall());
            fieldCnt++;
        }
        if (proto.hasDrawNumLarge()) {
            this.setDrawNumLarge(proto.getDrawNumLarge());
            fieldCnt++;
        }
        if (proto.hasTotalDrawTimes()) {
            this.setTotalDrawTimes(proto.getTotalDrawTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityLotteryNatashaUnit.Builder getCopySsBuilder() {
        final ActivityLotteryNatashaUnit.Builder builder = ActivityLotteryNatashaUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityLotteryNatashaUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.dailyTasks != null) {
            Struct.Int32TaskInfoMap.Builder tmpBuilder = Struct.Int32TaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.dailyTasks.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDailyTasks(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDailyTasks();
            }
        }  else if (builder.hasDailyTasks()) {
            // 清理DailyTasks
            builder.clearDailyTasks();
            fieldCnt++;
        }
        if (this.rechargeTasks != null) {
            Struct.Int32TaskInfoMap.Builder tmpBuilder = Struct.Int32TaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.rechargeTasks.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRechargeTasks(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRechargeTasks();
            }
        }  else if (builder.hasRechargeTasks()) {
            // 清理RechargeTasks
            builder.clearRechargeTasks();
            fieldCnt++;
        }
        if (this.getIsRecharged()) {
            builder.setIsRecharged(this.getIsRecharged());
            fieldCnt++;
        }  else if (builder.hasIsRecharged()) {
            // 清理IsRecharged
            builder.clearIsRecharged();
            fieldCnt++;
        }
        if (this.receivedBoxId != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.receivedBoxId.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setReceivedBoxId(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearReceivedBoxId();
            }
        }  else if (builder.hasReceivedBoxId()) {
            // 清理ReceivedBoxId
            builder.clearReceivedBoxId();
            fieldCnt++;
        }
        if (this.getDrawNumSmall() != 0) {
            builder.setDrawNumSmall(this.getDrawNumSmall());
            fieldCnt++;
        }  else if (builder.hasDrawNumSmall()) {
            // 清理DrawNumSmall
            builder.clearDrawNumSmall();
            fieldCnt++;
        }
        if (this.getDrawNumLarge() != 0) {
            builder.setDrawNumLarge(this.getDrawNumLarge());
            fieldCnt++;
        }  else if (builder.hasDrawNumLarge()) {
            // 清理DrawNumLarge
            builder.clearDrawNumLarge();
            fieldCnt++;
        }
        if (this.getTotalDrawTimes() != 0) {
            builder.setTotalDrawTimes(this.getTotalDrawTimes());
            fieldCnt++;
        }  else if (builder.hasTotalDrawTimes()) {
            // 清理TotalDrawTimes
            builder.clearTotalDrawTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityLotteryNatashaUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DAILYTASKS) && this.dailyTasks != null) {
            final boolean needClear = !builder.hasDailyTasks();
            final int tmpFieldCnt = this.dailyTasks.copyChangeToSs(builder.getDailyTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDailyTasks();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECHARGETASKS) && this.rechargeTasks != null) {
            final boolean needClear = !builder.hasRechargeTasks();
            final int tmpFieldCnt = this.rechargeTasks.copyChangeToSs(builder.getRechargeTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRechargeTasks();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISRECHARGED)) {
            builder.setIsRecharged(this.getIsRecharged());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RECEIVEDBOXID) && this.receivedBoxId != null) {
            final boolean needClear = !builder.hasReceivedBoxId();
            final int tmpFieldCnt = this.receivedBoxId.copyChangeToSs(builder.getReceivedBoxIdBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearReceivedBoxId();
            }
        }
        if (this.hasMark(FIELD_INDEX_DRAWNUMSMALL)) {
            builder.setDrawNumSmall(this.getDrawNumSmall());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DRAWNUMLARGE)) {
            builder.setDrawNumLarge(this.getDrawNumLarge());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALDRAWTIMES)) {
            builder.setTotalDrawTimes(this.getTotalDrawTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityLotteryNatashaUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDailyTasks()) {
            this.getDailyTasks().mergeFromSs(proto.getDailyTasks());
        } else {
            if (this.dailyTasks != null) {
                this.dailyTasks.mergeFromSs(proto.getDailyTasks());
            }
        }
        if (proto.hasRechargeTasks()) {
            this.getRechargeTasks().mergeFromSs(proto.getRechargeTasks());
        } else {
            if (this.rechargeTasks != null) {
                this.rechargeTasks.mergeFromSs(proto.getRechargeTasks());
            }
        }
        if (proto.hasIsRecharged()) {
            this.innerSetIsRecharged(proto.getIsRecharged());
        } else {
            this.innerSetIsRecharged(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasReceivedBoxId()) {
            this.getReceivedBoxId().mergeFromSs(proto.getReceivedBoxId());
        } else {
            if (this.receivedBoxId != null) {
                this.receivedBoxId.mergeFromSs(proto.getReceivedBoxId());
            }
        }
        if (proto.hasDrawNumSmall()) {
            this.innerSetDrawNumSmall(proto.getDrawNumSmall());
        } else {
            this.innerSetDrawNumSmall(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDrawNumLarge()) {
            this.innerSetDrawNumLarge(proto.getDrawNumLarge());
        } else {
            this.innerSetDrawNumLarge(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTotalDrawTimes()) {
            this.innerSetTotalDrawTimes(proto.getTotalDrawTimes());
        } else {
            this.innerSetTotalDrawTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivityLotteryNatashaUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityLotteryNatashaUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDailyTasks()) {
            this.getDailyTasks().mergeChangeFromSs(proto.getDailyTasks());
            fieldCnt++;
        }
        if (proto.hasRechargeTasks()) {
            this.getRechargeTasks().mergeChangeFromSs(proto.getRechargeTasks());
            fieldCnt++;
        }
        if (proto.hasIsRecharged()) {
            this.setIsRecharged(proto.getIsRecharged());
            fieldCnt++;
        }
        if (proto.hasReceivedBoxId()) {
            this.getReceivedBoxId().mergeChangeFromSs(proto.getReceivedBoxId());
            fieldCnt++;
        }
        if (proto.hasDrawNumSmall()) {
            this.setDrawNumSmall(proto.getDrawNumSmall());
            fieldCnt++;
        }
        if (proto.hasDrawNumLarge()) {
            this.setDrawNumLarge(proto.getDrawNumLarge());
            fieldCnt++;
        }
        if (proto.hasTotalDrawTimes()) {
            this.setTotalDrawTimes(proto.getTotalDrawTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityLotteryNatashaUnit.Builder builder = ActivityLotteryNatashaUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_DAILYTASKS) && this.dailyTasks != null) {
            this.dailyTasks.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RECHARGETASKS) && this.rechargeTasks != null) {
            this.rechargeTasks.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RECEIVEDBOXID) && this.receivedBoxId != null) {
            this.receivedBoxId.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.dailyTasks != null) {
            this.dailyTasks.markAll();
        }
        if (this.rechargeTasks != null) {
            this.rechargeTasks.markAll();
        }
        if (this.receivedBoxId != null) {
            this.receivedBoxId.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityLotteryNatashaUnitProp)) {
            return false;
        }
        final ActivityLotteryNatashaUnitProp otherNode = (ActivityLotteryNatashaUnitProp) node;
        if (!this.getDailyTasks().compareDataTo(otherNode.getDailyTasks())) {
            return false;
        }
        if (!this.getRechargeTasks().compareDataTo(otherNode.getRechargeTasks())) {
            return false;
        }
        if (this.isRecharged != otherNode.isRecharged) {
            return false;
        }
        if (!this.getReceivedBoxId().compareDataTo(otherNode.getReceivedBoxId())) {
            return false;
        }
        if (this.drawNumSmall != otherNode.drawNumSmall) {
            return false;
        }
        if (this.drawNumLarge != otherNode.drawNumLarge) {
            return false;
        }
        if (this.totalDrawTimes != otherNode.totalDrawTimes) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 57;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
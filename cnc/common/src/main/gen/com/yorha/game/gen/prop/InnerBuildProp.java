package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructPlayer.InnerBuild;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPlayerPB.InnerBuildPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class InnerBuildProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_BUILDID = 1;
    public static final int FIELD_INDEX_LEVEL = 2;
    public static final int FIELD_INDEX_BUILDTYPE = 3;
    public static final int FIELD_INDEX_BUILDSTATE = 4;
    public static final int FIELD_INDEX_POINT = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private long id = Constant.DEFAULT_LONG_VALUE;
    private int buildId = Constant.DEFAULT_INT_VALUE;
    private int level = Constant.DEFAULT_INT_VALUE;
    private int buildType = Constant.DEFAULT_INT_VALUE;
    private BuildState buildState = BuildState.forNumber(0);
    private PointProp point = null;

    public InnerBuildProp() {
        super(null, 0, FIELD_COUNT);
    }

    public InnerBuildProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public long getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public InnerBuildProp setId(long id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(long id) {
        this.id = id;
    }

    /**
     * get buildId
     *
     * @return buildId value
     */
    public int getBuildId() {
        return this.buildId;
    }

    /**
     * set buildId && set marked
     *
     * @param buildId new value
     * @return current object
     */
    public InnerBuildProp setBuildId(int buildId) {
        if (this.buildId != buildId) {
            this.mark(FIELD_INDEX_BUILDID);
            this.buildId = buildId;
        }
        return this;
    }

    /**
     * inner set buildId
     *
     * @param buildId new value
     */
    private void innerSetBuildId(int buildId) {
        this.buildId = buildId;
    }

    /**
     * get level
     *
     * @return level value
     */
    public int getLevel() {
        return this.level;
    }

    /**
     * set level && set marked
     *
     * @param level new value
     * @return current object
     */
    public InnerBuildProp setLevel(int level) {
        if (this.level != level) {
            this.mark(FIELD_INDEX_LEVEL);
            this.level = level;
        }
        return this;
    }

    /**
     * inner set level
     *
     * @param level new value
     */
    private void innerSetLevel(int level) {
        this.level = level;
    }

    /**
     * get buildType
     *
     * @return buildType value
     */
    public int getBuildType() {
        return this.buildType;
    }

    /**
     * set buildType && set marked
     *
     * @param buildType new value
     * @return current object
     */
    public InnerBuildProp setBuildType(int buildType) {
        if (this.buildType != buildType) {
            this.mark(FIELD_INDEX_BUILDTYPE);
            this.buildType = buildType;
        }
        return this;
    }

    /**
     * inner set buildType
     *
     * @param buildType new value
     */
    private void innerSetBuildType(int buildType) {
        this.buildType = buildType;
    }

    /**
     * get buildState
     *
     * @return buildState value
     */
    public BuildState getBuildState() {
        return this.buildState;
    }

    /**
     * set buildState && set marked
     *
     * @param buildState new value
     * @return current object
     */
    public InnerBuildProp setBuildState(BuildState buildState) {
        if (buildState == null) {
            throw new NullPointerException();
        }
        if (this.buildState != buildState) {
            this.mark(FIELD_INDEX_BUILDSTATE);
            this.buildState = buildState;
        }
        return this;
    }

    /**
     * inner set buildState
     *
     * @param buildState new value
     */
    private void innerSetBuildState(BuildState buildState) {
        this.buildState = buildState;
    }

    /**
     * get point
     *
     * @return point value
     */
    public PointProp getPoint() {
        if (this.point == null) {
            this.point = new PointProp(this, FIELD_INDEX_POINT);
        }
        return this.point;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public InnerBuildPB.Builder getCopyCsBuilder() {
        final InnerBuildPB.Builder builder = InnerBuildPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(InnerBuildPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getBuildId() != 0) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }  else if (builder.hasBuildId()) {
            // 清理BuildId
            builder.clearBuildId();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getBuildType() != 0) {
            builder.setBuildType(this.getBuildType());
            fieldCnt++;
        }  else if (builder.hasBuildType()) {
            // 清理BuildType
            builder.clearBuildType();
            fieldCnt++;
        }
        if (this.getBuildState() != BuildState.forNumber(0)) {
            builder.setBuildState(this.getBuildState());
            fieldCnt++;
        }  else if (builder.hasBuildState()) {
            // 清理BuildState
            builder.clearBuildState();
            fieldCnt++;
        }
        if (this.point != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.point.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(InnerBuildPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDID)) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDTYPE)) {
            builder.setBuildType(this.getBuildType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDSTATE)) {
            builder.setBuildState(this.getBuildState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(InnerBuildPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDID)) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDTYPE)) {
            builder.setBuildType(this.getBuildType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDSTATE)) {
            builder.setBuildState(this.getBuildState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToAndClearDeleteKeysCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(InnerBuildPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBuildId()) {
            this.innerSetBuildId(proto.getBuildId());
        } else {
            this.innerSetBuildId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuildType()) {
            this.innerSetBuildType(proto.getBuildType());
        } else {
            this.innerSetBuildType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuildState()) {
            this.innerSetBuildState(proto.getBuildState());
        } else {
            this.innerSetBuildState(BuildState.forNumber(0));
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromCs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromCs(proto.getPoint());
            }
        }
        this.markAll();
        return InnerBuildProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(InnerBuildPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasBuildId()) {
            this.setBuildId(proto.getBuildId());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasBuildType()) {
            this.setBuildType(proto.getBuildType());
            fieldCnt++;
        }
        if (proto.hasBuildState()) {
            this.setBuildState(proto.getBuildState());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromCs(proto.getPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public InnerBuild.Builder getCopyDbBuilder() {
        final InnerBuild.Builder builder = InnerBuild.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(InnerBuild.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getBuildId() != 0) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }  else if (builder.hasBuildId()) {
            // 清理BuildId
            builder.clearBuildId();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getBuildType() != 0) {
            builder.setBuildType(this.getBuildType());
            fieldCnt++;
        }  else if (builder.hasBuildType()) {
            // 清理BuildType
            builder.clearBuildType();
            fieldCnt++;
        }
        if (this.getBuildState() != BuildState.forNumber(0)) {
            builder.setBuildState(this.getBuildState());
            fieldCnt++;
        }  else if (builder.hasBuildState()) {
            // 清理BuildState
            builder.clearBuildState();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(InnerBuild.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDID)) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDTYPE)) {
            builder.setBuildType(this.getBuildType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDSTATE)) {
            builder.setBuildState(this.getBuildState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToDb(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(InnerBuild proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBuildId()) {
            this.innerSetBuildId(proto.getBuildId());
        } else {
            this.innerSetBuildId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuildType()) {
            this.innerSetBuildType(proto.getBuildType());
        } else {
            this.innerSetBuildType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuildState()) {
            this.innerSetBuildState(proto.getBuildState());
        } else {
            this.innerSetBuildState(BuildState.forNumber(0));
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromDb(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromDb(proto.getPoint());
            }
        }
        this.markAll();
        return InnerBuildProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(InnerBuild proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasBuildId()) {
            this.setBuildId(proto.getBuildId());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasBuildType()) {
            this.setBuildType(proto.getBuildType());
            fieldCnt++;
        }
        if (proto.hasBuildState()) {
            this.setBuildState(proto.getBuildState());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromDb(proto.getPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public InnerBuild.Builder getCopySsBuilder() {
        final InnerBuild.Builder builder = InnerBuild.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(InnerBuild.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getBuildId() != 0) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }  else if (builder.hasBuildId()) {
            // 清理BuildId
            builder.clearBuildId();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getBuildType() != 0) {
            builder.setBuildType(this.getBuildType());
            fieldCnt++;
        }  else if (builder.hasBuildType()) {
            // 清理BuildType
            builder.clearBuildType();
            fieldCnt++;
        }
        if (this.getBuildState() != BuildState.forNumber(0)) {
            builder.setBuildState(this.getBuildState());
            fieldCnt++;
        }  else if (builder.hasBuildState()) {
            // 清理BuildState
            builder.clearBuildState();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(InnerBuild.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDID)) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDTYPE)) {
            builder.setBuildType(this.getBuildType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDSTATE)) {
            builder.setBuildState(this.getBuildState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToSs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(InnerBuild proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBuildId()) {
            this.innerSetBuildId(proto.getBuildId());
        } else {
            this.innerSetBuildId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuildType()) {
            this.innerSetBuildType(proto.getBuildType());
        } else {
            this.innerSetBuildType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuildState()) {
            this.innerSetBuildState(proto.getBuildState());
        } else {
            this.innerSetBuildState(BuildState.forNumber(0));
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromSs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromSs(proto.getPoint());
            }
        }
        this.markAll();
        return InnerBuildProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(InnerBuild proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasBuildId()) {
            this.setBuildId(proto.getBuildId());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasBuildType()) {
            this.setBuildType(proto.getBuildType());
            fieldCnt++;
        }
        if (proto.hasBuildState()) {
            this.setBuildState(proto.getBuildState());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromSs(proto.getPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        InnerBuild.Builder builder = InnerBuild.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            this.point.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.point != null) {
            this.point.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof InnerBuildProp)) {
            return false;
        }
        final InnerBuildProp otherNode = (InnerBuildProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.buildId != otherNode.buildId) {
            return false;
        }
        if (this.level != otherNode.level) {
            return false;
        }
        if (this.buildType != otherNode.buildType) {
            return false;
        }
        if (this.buildState != otherNode.buildState) {
            return false;
        }
        if (!this.getPoint().compareDataTo(otherNode.getPoint())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
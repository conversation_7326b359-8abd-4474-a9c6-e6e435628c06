package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.MileStoneRewardPlayerInfo;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.MileStoneRewardPlayerInfoPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class MileStoneRewardPlayerInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_REWARDLEVEL = 0;
    public static final int FIELD_INDEX_REWARDPLAYERS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int rewardLevel = Constant.DEFAULT_INT_VALUE;
    private Int64PlayerIdDataMapProp rewardPlayers = null;

    public MileStoneRewardPlayerInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MileStoneRewardPlayerInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get rewardLevel
     *
     * @return rewardLevel value
     */
    public int getRewardLevel() {
        return this.rewardLevel;
    }

    /**
     * set rewardLevel && set marked
     *
     * @param rewardLevel new value
     * @return current object
     */
    public MileStoneRewardPlayerInfoProp setRewardLevel(int rewardLevel) {
        if (this.rewardLevel != rewardLevel) {
            this.mark(FIELD_INDEX_REWARDLEVEL);
            this.rewardLevel = rewardLevel;
        }
        return this;
    }

    /**
     * inner set rewardLevel
     *
     * @param rewardLevel new value
     */
    private void innerSetRewardLevel(int rewardLevel) {
        this.rewardLevel = rewardLevel;
    }

    /**
     * get rewardPlayers
     *
     * @return rewardPlayers value
     */
    public Int64PlayerIdDataMapProp getRewardPlayers() {
        if (this.rewardPlayers == null) {
            this.rewardPlayers = new Int64PlayerIdDataMapProp(this, FIELD_INDEX_REWARDPLAYERS);
        }
        return this.rewardPlayers;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putRewardPlayersV(PlayerIdDataProp v) {
        this.getRewardPlayers().put(v.getPlayerId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerIdDataProp addEmptyRewardPlayers(Long k) {
        return this.getRewardPlayers().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getRewardPlayersSize() {
        if (this.rewardPlayers == null) {
            return 0;
        }
        return this.rewardPlayers.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isRewardPlayersEmpty() {
        if (this.rewardPlayers == null) {
            return true;
        }
        return this.rewardPlayers.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerIdDataProp getRewardPlayersV(Long k) {
        if (this.rewardPlayers == null || !this.rewardPlayers.containsKey(k)) {
            return null;
        }
        return this.rewardPlayers.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearRewardPlayers() {
        if (this.rewardPlayers != null) {
            this.rewardPlayers.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeRewardPlayersV(Long k) {
        if (this.rewardPlayers != null) {
            this.rewardPlayers.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneRewardPlayerInfoPB.Builder getCopyCsBuilder() {
        final MileStoneRewardPlayerInfoPB.Builder builder = MileStoneRewardPlayerInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MileStoneRewardPlayerInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRewardLevel() != 0) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }  else if (builder.hasRewardLevel()) {
            // 清理RewardLevel
            builder.clearRewardLevel();
            fieldCnt++;
        }
        if (this.rewardPlayers != null) {
            StructPB.Int64PlayerIdDataMapPB.Builder tmpBuilder = StructPB.Int64PlayerIdDataMapPB.newBuilder();
            final int tmpFieldCnt = this.rewardPlayers.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardPlayers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardPlayers();
            }
        }  else if (builder.hasRewardPlayers()) {
            // 清理RewardPlayers
            builder.clearRewardPlayers();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MileStoneRewardPlayerInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDLEVEL)) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDPLAYERS) && this.rewardPlayers != null) {
            final boolean needClear = !builder.hasRewardPlayers();
            final int tmpFieldCnt = this.rewardPlayers.copyChangeToCs(builder.getRewardPlayersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardPlayers();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MileStoneRewardPlayerInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDLEVEL)) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDPLAYERS) && this.rewardPlayers != null) {
            final boolean needClear = !builder.hasRewardPlayers();
            final int tmpFieldCnt = this.rewardPlayers.copyChangeToAndClearDeleteKeysCs(builder.getRewardPlayersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardPlayers();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MileStoneRewardPlayerInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardLevel()) {
            this.innerSetRewardLevel(proto.getRewardLevel());
        } else {
            this.innerSetRewardLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardPlayers()) {
            this.getRewardPlayers().mergeFromCs(proto.getRewardPlayers());
        } else {
            if (this.rewardPlayers != null) {
                this.rewardPlayers.mergeFromCs(proto.getRewardPlayers());
            }
        }
        this.markAll();
        return MileStoneRewardPlayerInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MileStoneRewardPlayerInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardLevel()) {
            this.setRewardLevel(proto.getRewardLevel());
            fieldCnt++;
        }
        if (proto.hasRewardPlayers()) {
            this.getRewardPlayers().mergeChangeFromCs(proto.getRewardPlayers());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneRewardPlayerInfo.Builder getCopyDbBuilder() {
        final MileStoneRewardPlayerInfo.Builder builder = MileStoneRewardPlayerInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MileStoneRewardPlayerInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRewardLevel() != 0) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }  else if (builder.hasRewardLevel()) {
            // 清理RewardLevel
            builder.clearRewardLevel();
            fieldCnt++;
        }
        if (this.rewardPlayers != null) {
            Struct.Int64PlayerIdDataMap.Builder tmpBuilder = Struct.Int64PlayerIdDataMap.newBuilder();
            final int tmpFieldCnt = this.rewardPlayers.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardPlayers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardPlayers();
            }
        }  else if (builder.hasRewardPlayers()) {
            // 清理RewardPlayers
            builder.clearRewardPlayers();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MileStoneRewardPlayerInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDLEVEL)) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDPLAYERS) && this.rewardPlayers != null) {
            final boolean needClear = !builder.hasRewardPlayers();
            final int tmpFieldCnt = this.rewardPlayers.copyChangeToDb(builder.getRewardPlayersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardPlayers();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MileStoneRewardPlayerInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardLevel()) {
            this.innerSetRewardLevel(proto.getRewardLevel());
        } else {
            this.innerSetRewardLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardPlayers()) {
            this.getRewardPlayers().mergeFromDb(proto.getRewardPlayers());
        } else {
            if (this.rewardPlayers != null) {
                this.rewardPlayers.mergeFromDb(proto.getRewardPlayers());
            }
        }
        this.markAll();
        return MileStoneRewardPlayerInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MileStoneRewardPlayerInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardLevel()) {
            this.setRewardLevel(proto.getRewardLevel());
            fieldCnt++;
        }
        if (proto.hasRewardPlayers()) {
            this.getRewardPlayers().mergeChangeFromDb(proto.getRewardPlayers());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneRewardPlayerInfo.Builder getCopySsBuilder() {
        final MileStoneRewardPlayerInfo.Builder builder = MileStoneRewardPlayerInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MileStoneRewardPlayerInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRewardLevel() != 0) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }  else if (builder.hasRewardLevel()) {
            // 清理RewardLevel
            builder.clearRewardLevel();
            fieldCnt++;
        }
        if (this.rewardPlayers != null) {
            Struct.Int64PlayerIdDataMap.Builder tmpBuilder = Struct.Int64PlayerIdDataMap.newBuilder();
            final int tmpFieldCnt = this.rewardPlayers.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardPlayers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardPlayers();
            }
        }  else if (builder.hasRewardPlayers()) {
            // 清理RewardPlayers
            builder.clearRewardPlayers();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MileStoneRewardPlayerInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDLEVEL)) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDPLAYERS) && this.rewardPlayers != null) {
            final boolean needClear = !builder.hasRewardPlayers();
            final int tmpFieldCnt = this.rewardPlayers.copyChangeToSs(builder.getRewardPlayersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardPlayers();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MileStoneRewardPlayerInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardLevel()) {
            this.innerSetRewardLevel(proto.getRewardLevel());
        } else {
            this.innerSetRewardLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardPlayers()) {
            this.getRewardPlayers().mergeFromSs(proto.getRewardPlayers());
        } else {
            if (this.rewardPlayers != null) {
                this.rewardPlayers.mergeFromSs(proto.getRewardPlayers());
            }
        }
        this.markAll();
        return MileStoneRewardPlayerInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MileStoneRewardPlayerInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardLevel()) {
            this.setRewardLevel(proto.getRewardLevel());
            fieldCnt++;
        }
        if (proto.hasRewardPlayers()) {
            this.getRewardPlayers().mergeChangeFromSs(proto.getRewardPlayers());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MileStoneRewardPlayerInfo.Builder builder = MileStoneRewardPlayerInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_REWARDPLAYERS) && this.rewardPlayers != null) {
            this.rewardPlayers.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.rewardPlayers != null) {
            this.rewardPlayers.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.rewardLevel;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MileStoneRewardPlayerInfoProp)) {
            return false;
        }
        final MileStoneRewardPlayerInfoProp otherNode = (MileStoneRewardPlayerInfoProp) node;
        if (this.rewardLevel != otherNode.rewardLevel) {
            return false;
        }
        if (!this.getRewardPlayers().compareDataTo(otherNode.getRewardPlayers())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
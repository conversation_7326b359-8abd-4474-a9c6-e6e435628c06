package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Player.PlayerHeroTalentPage;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.PlayerHeroTalentPagePB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerHeroTalentPageProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_SLOTNUM = 0;
    public static final int FIELD_INDEX_TALENTPAGENAME = 1;
    public static final int FIELD_INDEX_TALENTPAGESTATE = 2;
    public static final int FIELD_INDEX_LEFTTALENTPOINT = 3;
    public static final int FIELD_INDEX_TALENTMAP = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private int slotNum = Constant.DEFAULT_INT_VALUE;
    private String talentPageName = Constant.DEFAULT_STR_VALUE;
    private talentPateState talentPageState = talentPateState.forNumber(0);
    private int leftTalentPoint = Constant.DEFAULT_INT_VALUE;
    private Int32PlayerHeroTalentMapProp talentMap = null;

    public PlayerHeroTalentPageProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerHeroTalentPageProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get slotNum
     *
     * @return slotNum value
     */
    public int getSlotNum() {
        return this.slotNum;
    }

    /**
     * set slotNum && set marked
     *
     * @param slotNum new value
     * @return current object
     */
    public PlayerHeroTalentPageProp setSlotNum(int slotNum) {
        if (this.slotNum != slotNum) {
            this.mark(FIELD_INDEX_SLOTNUM);
            this.slotNum = slotNum;
        }
        return this;
    }

    /**
     * inner set slotNum
     *
     * @param slotNum new value
     */
    private void innerSetSlotNum(int slotNum) {
        this.slotNum = slotNum;
    }

    /**
     * get talentPageName
     *
     * @return talentPageName value
     */
    public String getTalentPageName() {
        return this.talentPageName;
    }

    /**
     * set talentPageName && set marked
     *
     * @param talentPageName new value
     * @return current object
     */
    public PlayerHeroTalentPageProp setTalentPageName(String talentPageName) {
        if (talentPageName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.talentPageName, talentPageName)) {
            this.mark(FIELD_INDEX_TALENTPAGENAME);
            this.talentPageName = talentPageName;
        }
        return this;
    }

    /**
     * inner set talentPageName
     *
     * @param talentPageName new value
     */
    private void innerSetTalentPageName(String talentPageName) {
        this.talentPageName = talentPageName;
    }

    /**
     * get talentPageState
     *
     * @return talentPageState value
     */
    public talentPateState getTalentPageState() {
        return this.talentPageState;
    }

    /**
     * set talentPageState && set marked
     *
     * @param talentPageState new value
     * @return current object
     */
    public PlayerHeroTalentPageProp setTalentPageState(talentPateState talentPageState) {
        if (talentPageState == null) {
            throw new NullPointerException();
        }
        if (this.talentPageState != talentPageState) {
            this.mark(FIELD_INDEX_TALENTPAGESTATE);
            this.talentPageState = talentPageState;
        }
        return this;
    }

    /**
     * inner set talentPageState
     *
     * @param talentPageState new value
     */
    private void innerSetTalentPageState(talentPateState talentPageState) {
        this.talentPageState = talentPageState;
    }

    /**
     * get leftTalentPoint
     *
     * @return leftTalentPoint value
     */
    public int getLeftTalentPoint() {
        return this.leftTalentPoint;
    }

    /**
     * set leftTalentPoint && set marked
     *
     * @param leftTalentPoint new value
     * @return current object
     */
    public PlayerHeroTalentPageProp setLeftTalentPoint(int leftTalentPoint) {
        if (this.leftTalentPoint != leftTalentPoint) {
            this.mark(FIELD_INDEX_LEFTTALENTPOINT);
            this.leftTalentPoint = leftTalentPoint;
        }
        return this;
    }

    /**
     * inner set leftTalentPoint
     *
     * @param leftTalentPoint new value
     */
    private void innerSetLeftTalentPoint(int leftTalentPoint) {
        this.leftTalentPoint = leftTalentPoint;
    }

    /**
     * get talentMap
     *
     * @return talentMap value
     */
    public Int32PlayerHeroTalentMapProp getTalentMap() {
        if (this.talentMap == null) {
            this.talentMap = new Int32PlayerHeroTalentMapProp(this, FIELD_INDEX_TALENTMAP);
        }
        return this.talentMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTalentMapV(PlayerHeroTalentProp v) {
        this.getTalentMap().put(v.getTalentGroupId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerHeroTalentProp addEmptyTalentMap(Integer k) {
        return this.getTalentMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTalentMapSize() {
        if (this.talentMap == null) {
            return 0;
        }
        return this.talentMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTalentMapEmpty() {
        if (this.talentMap == null) {
            return true;
        }
        return this.talentMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerHeroTalentProp getTalentMapV(Integer k) {
        if (this.talentMap == null || !this.talentMap.containsKey(k)) {
            return null;
        }
        return this.talentMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTalentMap() {
        if (this.talentMap != null) {
            this.talentMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTalentMapV(Integer k) {
        if (this.talentMap != null) {
            this.talentMap.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerHeroTalentPagePB.Builder getCopyCsBuilder() {
        final PlayerHeroTalentPagePB.Builder builder = PlayerHeroTalentPagePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerHeroTalentPagePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSlotNum() != 0) {
            builder.setSlotNum(this.getSlotNum());
            fieldCnt++;
        }  else if (builder.hasSlotNum()) {
            // 清理SlotNum
            builder.clearSlotNum();
            fieldCnt++;
        }
        if (!this.getTalentPageName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTalentPageName(this.getTalentPageName());
            fieldCnt++;
        }  else if (builder.hasTalentPageName()) {
            // 清理TalentPageName
            builder.clearTalentPageName();
            fieldCnt++;
        }
        if (this.getTalentPageState() != talentPateState.forNumber(0)) {
            builder.setTalentPageState(this.getTalentPageState());
            fieldCnt++;
        }  else if (builder.hasTalentPageState()) {
            // 清理TalentPageState
            builder.clearTalentPageState();
            fieldCnt++;
        }
        if (this.getLeftTalentPoint() != 0) {
            builder.setLeftTalentPoint(this.getLeftTalentPoint());
            fieldCnt++;
        }  else if (builder.hasLeftTalentPoint()) {
            // 清理LeftTalentPoint
            builder.clearLeftTalentPoint();
            fieldCnt++;
        }
        if (this.talentMap != null) {
            PlayerPB.Int32PlayerHeroTalentMapPB.Builder tmpBuilder = PlayerPB.Int32PlayerHeroTalentMapPB.newBuilder();
            final int tmpFieldCnt = this.talentMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTalentMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTalentMap();
            }
        }  else if (builder.hasTalentMap()) {
            // 清理TalentMap
            builder.clearTalentMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerHeroTalentPagePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SLOTNUM)) {
            builder.setSlotNum(this.getSlotNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTPAGENAME)) {
            builder.setTalentPageName(this.getTalentPageName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTPAGESTATE)) {
            builder.setTalentPageState(this.getTalentPageState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEFTTALENTPOINT)) {
            builder.setLeftTalentPoint(this.getLeftTalentPoint());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTMAP) && this.talentMap != null) {
            final boolean needClear = !builder.hasTalentMap();
            final int tmpFieldCnt = this.talentMap.copyChangeToCs(builder.getTalentMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTalentMap();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerHeroTalentPagePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SLOTNUM)) {
            builder.setSlotNum(this.getSlotNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTPAGENAME)) {
            builder.setTalentPageName(this.getTalentPageName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTPAGESTATE)) {
            builder.setTalentPageState(this.getTalentPageState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEFTTALENTPOINT)) {
            builder.setLeftTalentPoint(this.getLeftTalentPoint());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTMAP) && this.talentMap != null) {
            final boolean needClear = !builder.hasTalentMap();
            final int tmpFieldCnt = this.talentMap.copyChangeToAndClearDeleteKeysCs(builder.getTalentMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTalentMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerHeroTalentPagePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSlotNum()) {
            this.innerSetSlotNum(proto.getSlotNum());
        } else {
            this.innerSetSlotNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTalentPageName()) {
            this.innerSetTalentPageName(proto.getTalentPageName());
        } else {
            this.innerSetTalentPageName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTalentPageState()) {
            this.innerSetTalentPageState(proto.getTalentPageState());
        } else {
            this.innerSetTalentPageState(talentPateState.forNumber(0));
        }
        if (proto.hasLeftTalentPoint()) {
            this.innerSetLeftTalentPoint(proto.getLeftTalentPoint());
        } else {
            this.innerSetLeftTalentPoint(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTalentMap()) {
            this.getTalentMap().mergeFromCs(proto.getTalentMap());
        } else {
            if (this.talentMap != null) {
                this.talentMap.mergeFromCs(proto.getTalentMap());
            }
        }
        this.markAll();
        return PlayerHeroTalentPageProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerHeroTalentPagePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSlotNum()) {
            this.setSlotNum(proto.getSlotNum());
            fieldCnt++;
        }
        if (proto.hasTalentPageName()) {
            this.setTalentPageName(proto.getTalentPageName());
            fieldCnt++;
        }
        if (proto.hasTalentPageState()) {
            this.setTalentPageState(proto.getTalentPageState());
            fieldCnt++;
        }
        if (proto.hasLeftTalentPoint()) {
            this.setLeftTalentPoint(proto.getLeftTalentPoint());
            fieldCnt++;
        }
        if (proto.hasTalentMap()) {
            this.getTalentMap().mergeChangeFromCs(proto.getTalentMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerHeroTalentPage.Builder getCopyDbBuilder() {
        final PlayerHeroTalentPage.Builder builder = PlayerHeroTalentPage.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerHeroTalentPage.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSlotNum() != 0) {
            builder.setSlotNum(this.getSlotNum());
            fieldCnt++;
        }  else if (builder.hasSlotNum()) {
            // 清理SlotNum
            builder.clearSlotNum();
            fieldCnt++;
        }
        if (!this.getTalentPageName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTalentPageName(this.getTalentPageName());
            fieldCnt++;
        }  else if (builder.hasTalentPageName()) {
            // 清理TalentPageName
            builder.clearTalentPageName();
            fieldCnt++;
        }
        if (this.getTalentPageState() != talentPateState.forNumber(0)) {
            builder.setTalentPageState(this.getTalentPageState());
            fieldCnt++;
        }  else if (builder.hasTalentPageState()) {
            // 清理TalentPageState
            builder.clearTalentPageState();
            fieldCnt++;
        }
        if (this.getLeftTalentPoint() != 0) {
            builder.setLeftTalentPoint(this.getLeftTalentPoint());
            fieldCnt++;
        }  else if (builder.hasLeftTalentPoint()) {
            // 清理LeftTalentPoint
            builder.clearLeftTalentPoint();
            fieldCnt++;
        }
        if (this.talentMap != null) {
            Player.Int32PlayerHeroTalentMap.Builder tmpBuilder = Player.Int32PlayerHeroTalentMap.newBuilder();
            final int tmpFieldCnt = this.talentMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTalentMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTalentMap();
            }
        }  else if (builder.hasTalentMap()) {
            // 清理TalentMap
            builder.clearTalentMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerHeroTalentPage.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SLOTNUM)) {
            builder.setSlotNum(this.getSlotNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTPAGENAME)) {
            builder.setTalentPageName(this.getTalentPageName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTPAGESTATE)) {
            builder.setTalentPageState(this.getTalentPageState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEFTTALENTPOINT)) {
            builder.setLeftTalentPoint(this.getLeftTalentPoint());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTMAP) && this.talentMap != null) {
            final boolean needClear = !builder.hasTalentMap();
            final int tmpFieldCnt = this.talentMap.copyChangeToDb(builder.getTalentMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTalentMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerHeroTalentPage proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSlotNum()) {
            this.innerSetSlotNum(proto.getSlotNum());
        } else {
            this.innerSetSlotNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTalentPageName()) {
            this.innerSetTalentPageName(proto.getTalentPageName());
        } else {
            this.innerSetTalentPageName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTalentPageState()) {
            this.innerSetTalentPageState(proto.getTalentPageState());
        } else {
            this.innerSetTalentPageState(talentPateState.forNumber(0));
        }
        if (proto.hasLeftTalentPoint()) {
            this.innerSetLeftTalentPoint(proto.getLeftTalentPoint());
        } else {
            this.innerSetLeftTalentPoint(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTalentMap()) {
            this.getTalentMap().mergeFromDb(proto.getTalentMap());
        } else {
            if (this.talentMap != null) {
                this.talentMap.mergeFromDb(proto.getTalentMap());
            }
        }
        this.markAll();
        return PlayerHeroTalentPageProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerHeroTalentPage proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSlotNum()) {
            this.setSlotNum(proto.getSlotNum());
            fieldCnt++;
        }
        if (proto.hasTalentPageName()) {
            this.setTalentPageName(proto.getTalentPageName());
            fieldCnt++;
        }
        if (proto.hasTalentPageState()) {
            this.setTalentPageState(proto.getTalentPageState());
            fieldCnt++;
        }
        if (proto.hasLeftTalentPoint()) {
            this.setLeftTalentPoint(proto.getLeftTalentPoint());
            fieldCnt++;
        }
        if (proto.hasTalentMap()) {
            this.getTalentMap().mergeChangeFromDb(proto.getTalentMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerHeroTalentPage.Builder getCopySsBuilder() {
        final PlayerHeroTalentPage.Builder builder = PlayerHeroTalentPage.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerHeroTalentPage.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSlotNum() != 0) {
            builder.setSlotNum(this.getSlotNum());
            fieldCnt++;
        }  else if (builder.hasSlotNum()) {
            // 清理SlotNum
            builder.clearSlotNum();
            fieldCnt++;
        }
        if (!this.getTalentPageName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTalentPageName(this.getTalentPageName());
            fieldCnt++;
        }  else if (builder.hasTalentPageName()) {
            // 清理TalentPageName
            builder.clearTalentPageName();
            fieldCnt++;
        }
        if (this.getTalentPageState() != talentPateState.forNumber(0)) {
            builder.setTalentPageState(this.getTalentPageState());
            fieldCnt++;
        }  else if (builder.hasTalentPageState()) {
            // 清理TalentPageState
            builder.clearTalentPageState();
            fieldCnt++;
        }
        if (this.getLeftTalentPoint() != 0) {
            builder.setLeftTalentPoint(this.getLeftTalentPoint());
            fieldCnt++;
        }  else if (builder.hasLeftTalentPoint()) {
            // 清理LeftTalentPoint
            builder.clearLeftTalentPoint();
            fieldCnt++;
        }
        if (this.talentMap != null) {
            Player.Int32PlayerHeroTalentMap.Builder tmpBuilder = Player.Int32PlayerHeroTalentMap.newBuilder();
            final int tmpFieldCnt = this.talentMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTalentMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTalentMap();
            }
        }  else if (builder.hasTalentMap()) {
            // 清理TalentMap
            builder.clearTalentMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerHeroTalentPage.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SLOTNUM)) {
            builder.setSlotNum(this.getSlotNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTPAGENAME)) {
            builder.setTalentPageName(this.getTalentPageName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTPAGESTATE)) {
            builder.setTalentPageState(this.getTalentPageState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEFTTALENTPOINT)) {
            builder.setLeftTalentPoint(this.getLeftTalentPoint());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTMAP) && this.talentMap != null) {
            final boolean needClear = !builder.hasTalentMap();
            final int tmpFieldCnt = this.talentMap.copyChangeToSs(builder.getTalentMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTalentMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerHeroTalentPage proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSlotNum()) {
            this.innerSetSlotNum(proto.getSlotNum());
        } else {
            this.innerSetSlotNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTalentPageName()) {
            this.innerSetTalentPageName(proto.getTalentPageName());
        } else {
            this.innerSetTalentPageName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTalentPageState()) {
            this.innerSetTalentPageState(proto.getTalentPageState());
        } else {
            this.innerSetTalentPageState(talentPateState.forNumber(0));
        }
        if (proto.hasLeftTalentPoint()) {
            this.innerSetLeftTalentPoint(proto.getLeftTalentPoint());
        } else {
            this.innerSetLeftTalentPoint(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTalentMap()) {
            this.getTalentMap().mergeFromSs(proto.getTalentMap());
        } else {
            if (this.talentMap != null) {
                this.talentMap.mergeFromSs(proto.getTalentMap());
            }
        }
        this.markAll();
        return PlayerHeroTalentPageProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerHeroTalentPage proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSlotNum()) {
            this.setSlotNum(proto.getSlotNum());
            fieldCnt++;
        }
        if (proto.hasTalentPageName()) {
            this.setTalentPageName(proto.getTalentPageName());
            fieldCnt++;
        }
        if (proto.hasTalentPageState()) {
            this.setTalentPageState(proto.getTalentPageState());
            fieldCnt++;
        }
        if (proto.hasLeftTalentPoint()) {
            this.setLeftTalentPoint(proto.getLeftTalentPoint());
            fieldCnt++;
        }
        if (proto.hasTalentMap()) {
            this.getTalentMap().mergeChangeFromSs(proto.getTalentMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerHeroTalentPage.Builder builder = PlayerHeroTalentPage.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TALENTMAP) && this.talentMap != null) {
            this.talentMap.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.talentMap != null) {
            this.talentMap.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.slotNum;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerHeroTalentPageProp)) {
            return false;
        }
        final PlayerHeroTalentPageProp otherNode = (PlayerHeroTalentPageProp) node;
        if (this.slotNum != otherNode.slotNum) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.talentPageName, otherNode.talentPageName)) {
            return false;
        }
        if (this.talentPageState != otherNode.talentPageState) {
            return false;
        }
        if (this.leftTalentPoint != otherNode.leftTalentPoint) {
            return false;
        }
        if (!this.getTalentMap().compareDataTo(otherNode.getTalentMap())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
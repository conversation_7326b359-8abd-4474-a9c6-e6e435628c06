package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zone.KingdomSkillInfo;
import com.yorha.proto.ZonePB.KingdomSkillInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class KingdomSkillInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_SKILLID = 0;
    public static final int FIELD_INDEX_SKILLENDTSMS = 1;
    public static final int FIELD_INDEX_CDENDTSMS = 2;
    public static final int FIELD_INDEX_USESKILLPLAYERID = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int skillId = Constant.DEFAULT_INT_VALUE;
    private long skillEndTsMs = Constant.DEFAULT_LONG_VALUE;
    private long cdEndTsMs = Constant.DEFAULT_LONG_VALUE;
    private long useSkillPlayerId = Constant.DEFAULT_LONG_VALUE;

    public KingdomSkillInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public KingdomSkillInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get skillId
     *
     * @return skillId value
     */
    public int getSkillId() {
        return this.skillId;
    }

    /**
     * set skillId && set marked
     *
     * @param skillId new value
     * @return current object
     */
    public KingdomSkillInfoProp setSkillId(int skillId) {
        if (this.skillId != skillId) {
            this.mark(FIELD_INDEX_SKILLID);
            this.skillId = skillId;
        }
        return this;
    }

    /**
     * inner set skillId
     *
     * @param skillId new value
     */
    private void innerSetSkillId(int skillId) {
        this.skillId = skillId;
    }

    /**
     * get skillEndTsMs
     *
     * @return skillEndTsMs value
     */
    public long getSkillEndTsMs() {
        return this.skillEndTsMs;
    }

    /**
     * set skillEndTsMs && set marked
     *
     * @param skillEndTsMs new value
     * @return current object
     */
    public KingdomSkillInfoProp setSkillEndTsMs(long skillEndTsMs) {
        if (this.skillEndTsMs != skillEndTsMs) {
            this.mark(FIELD_INDEX_SKILLENDTSMS);
            this.skillEndTsMs = skillEndTsMs;
        }
        return this;
    }

    /**
     * inner set skillEndTsMs
     *
     * @param skillEndTsMs new value
     */
    private void innerSetSkillEndTsMs(long skillEndTsMs) {
        this.skillEndTsMs = skillEndTsMs;
    }

    /**
     * get cdEndTsMs
     *
     * @return cdEndTsMs value
     */
    public long getCdEndTsMs() {
        return this.cdEndTsMs;
    }

    /**
     * set cdEndTsMs && set marked
     *
     * @param cdEndTsMs new value
     * @return current object
     */
    public KingdomSkillInfoProp setCdEndTsMs(long cdEndTsMs) {
        if (this.cdEndTsMs != cdEndTsMs) {
            this.mark(FIELD_INDEX_CDENDTSMS);
            this.cdEndTsMs = cdEndTsMs;
        }
        return this;
    }

    /**
     * inner set cdEndTsMs
     *
     * @param cdEndTsMs new value
     */
    private void innerSetCdEndTsMs(long cdEndTsMs) {
        this.cdEndTsMs = cdEndTsMs;
    }

    /**
     * get useSkillPlayerId
     *
     * @return useSkillPlayerId value
     */
    public long getUseSkillPlayerId() {
        return this.useSkillPlayerId;
    }

    /**
     * set useSkillPlayerId && set marked
     *
     * @param useSkillPlayerId new value
     * @return current object
     */
    public KingdomSkillInfoProp setUseSkillPlayerId(long useSkillPlayerId) {
        if (this.useSkillPlayerId != useSkillPlayerId) {
            this.mark(FIELD_INDEX_USESKILLPLAYERID);
            this.useSkillPlayerId = useSkillPlayerId;
        }
        return this;
    }

    /**
     * inner set useSkillPlayerId
     *
     * @param useSkillPlayerId new value
     */
    private void innerSetUseSkillPlayerId(long useSkillPlayerId) {
        this.useSkillPlayerId = useSkillPlayerId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomSkillInfoPB.Builder getCopyCsBuilder() {
        final KingdomSkillInfoPB.Builder builder = KingdomSkillInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(KingdomSkillInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSkillId() != 0) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }  else if (builder.hasSkillId()) {
            // 清理SkillId
            builder.clearSkillId();
            fieldCnt++;
        }
        if (this.getSkillEndTsMs() != 0L) {
            builder.setSkillEndTsMs(this.getSkillEndTsMs());
            fieldCnt++;
        }  else if (builder.hasSkillEndTsMs()) {
            // 清理SkillEndTsMs
            builder.clearSkillEndTsMs();
            fieldCnt++;
        }
        if (this.getCdEndTsMs() != 0L) {
            builder.setCdEndTsMs(this.getCdEndTsMs());
            fieldCnt++;
        }  else if (builder.hasCdEndTsMs()) {
            // 清理CdEndTsMs
            builder.clearCdEndTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(KingdomSkillInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLID)) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLENDTSMS)) {
            builder.setSkillEndTsMs(this.getSkillEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CDENDTSMS)) {
            builder.setCdEndTsMs(this.getCdEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(KingdomSkillInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLID)) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLENDTSMS)) {
            builder.setSkillEndTsMs(this.getSkillEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CDENDTSMS)) {
            builder.setCdEndTsMs(this.getCdEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(KingdomSkillInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkillId()) {
            this.innerSetSkillId(proto.getSkillId());
        } else {
            this.innerSetSkillId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkillEndTsMs()) {
            this.innerSetSkillEndTsMs(proto.getSkillEndTsMs());
        } else {
            this.innerSetSkillEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCdEndTsMs()) {
            this.innerSetCdEndTsMs(proto.getCdEndTsMs());
        } else {
            this.innerSetCdEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return KingdomSkillInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(KingdomSkillInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkillId()) {
            this.setSkillId(proto.getSkillId());
            fieldCnt++;
        }
        if (proto.hasSkillEndTsMs()) {
            this.setSkillEndTsMs(proto.getSkillEndTsMs());
            fieldCnt++;
        }
        if (proto.hasCdEndTsMs()) {
            this.setCdEndTsMs(proto.getCdEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomSkillInfo.Builder getCopyDbBuilder() {
        final KingdomSkillInfo.Builder builder = KingdomSkillInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(KingdomSkillInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSkillId() != 0) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }  else if (builder.hasSkillId()) {
            // 清理SkillId
            builder.clearSkillId();
            fieldCnt++;
        }
        if (this.getSkillEndTsMs() != 0L) {
            builder.setSkillEndTsMs(this.getSkillEndTsMs());
            fieldCnt++;
        }  else if (builder.hasSkillEndTsMs()) {
            // 清理SkillEndTsMs
            builder.clearSkillEndTsMs();
            fieldCnt++;
        }
        if (this.getCdEndTsMs() != 0L) {
            builder.setCdEndTsMs(this.getCdEndTsMs());
            fieldCnt++;
        }  else if (builder.hasCdEndTsMs()) {
            // 清理CdEndTsMs
            builder.clearCdEndTsMs();
            fieldCnt++;
        }
        if (this.getUseSkillPlayerId() != 0L) {
            builder.setUseSkillPlayerId(this.getUseSkillPlayerId());
            fieldCnt++;
        }  else if (builder.hasUseSkillPlayerId()) {
            // 清理UseSkillPlayerId
            builder.clearUseSkillPlayerId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(KingdomSkillInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLID)) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLENDTSMS)) {
            builder.setSkillEndTsMs(this.getSkillEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CDENDTSMS)) {
            builder.setCdEndTsMs(this.getCdEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USESKILLPLAYERID)) {
            builder.setUseSkillPlayerId(this.getUseSkillPlayerId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(KingdomSkillInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkillId()) {
            this.innerSetSkillId(proto.getSkillId());
        } else {
            this.innerSetSkillId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkillEndTsMs()) {
            this.innerSetSkillEndTsMs(proto.getSkillEndTsMs());
        } else {
            this.innerSetSkillEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCdEndTsMs()) {
            this.innerSetCdEndTsMs(proto.getCdEndTsMs());
        } else {
            this.innerSetCdEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasUseSkillPlayerId()) {
            this.innerSetUseSkillPlayerId(proto.getUseSkillPlayerId());
        } else {
            this.innerSetUseSkillPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return KingdomSkillInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(KingdomSkillInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkillId()) {
            this.setSkillId(proto.getSkillId());
            fieldCnt++;
        }
        if (proto.hasSkillEndTsMs()) {
            this.setSkillEndTsMs(proto.getSkillEndTsMs());
            fieldCnt++;
        }
        if (proto.hasCdEndTsMs()) {
            this.setCdEndTsMs(proto.getCdEndTsMs());
            fieldCnt++;
        }
        if (proto.hasUseSkillPlayerId()) {
            this.setUseSkillPlayerId(proto.getUseSkillPlayerId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomSkillInfo.Builder getCopySsBuilder() {
        final KingdomSkillInfo.Builder builder = KingdomSkillInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(KingdomSkillInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSkillId() != 0) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }  else if (builder.hasSkillId()) {
            // 清理SkillId
            builder.clearSkillId();
            fieldCnt++;
        }
        if (this.getSkillEndTsMs() != 0L) {
            builder.setSkillEndTsMs(this.getSkillEndTsMs());
            fieldCnt++;
        }  else if (builder.hasSkillEndTsMs()) {
            // 清理SkillEndTsMs
            builder.clearSkillEndTsMs();
            fieldCnt++;
        }
        if (this.getCdEndTsMs() != 0L) {
            builder.setCdEndTsMs(this.getCdEndTsMs());
            fieldCnt++;
        }  else if (builder.hasCdEndTsMs()) {
            // 清理CdEndTsMs
            builder.clearCdEndTsMs();
            fieldCnt++;
        }
        if (this.getUseSkillPlayerId() != 0L) {
            builder.setUseSkillPlayerId(this.getUseSkillPlayerId());
            fieldCnt++;
        }  else if (builder.hasUseSkillPlayerId()) {
            // 清理UseSkillPlayerId
            builder.clearUseSkillPlayerId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(KingdomSkillInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLID)) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLENDTSMS)) {
            builder.setSkillEndTsMs(this.getSkillEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CDENDTSMS)) {
            builder.setCdEndTsMs(this.getCdEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USESKILLPLAYERID)) {
            builder.setUseSkillPlayerId(this.getUseSkillPlayerId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(KingdomSkillInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkillId()) {
            this.innerSetSkillId(proto.getSkillId());
        } else {
            this.innerSetSkillId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkillEndTsMs()) {
            this.innerSetSkillEndTsMs(proto.getSkillEndTsMs());
        } else {
            this.innerSetSkillEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCdEndTsMs()) {
            this.innerSetCdEndTsMs(proto.getCdEndTsMs());
        } else {
            this.innerSetCdEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasUseSkillPlayerId()) {
            this.innerSetUseSkillPlayerId(proto.getUseSkillPlayerId());
        } else {
            this.innerSetUseSkillPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return KingdomSkillInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(KingdomSkillInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkillId()) {
            this.setSkillId(proto.getSkillId());
            fieldCnt++;
        }
        if (proto.hasSkillEndTsMs()) {
            this.setSkillEndTsMs(proto.getSkillEndTsMs());
            fieldCnt++;
        }
        if (proto.hasCdEndTsMs()) {
            this.setCdEndTsMs(proto.getCdEndTsMs());
            fieldCnt++;
        }
        if (proto.hasUseSkillPlayerId()) {
            this.setUseSkillPlayerId(proto.getUseSkillPlayerId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        KingdomSkillInfo.Builder builder = KingdomSkillInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.skillId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof KingdomSkillInfoProp)) {
            return false;
        }
        final KingdomSkillInfoProp otherNode = (KingdomSkillInfoProp) node;
        if (this.skillId != otherNode.skillId) {
            return false;
        }
        if (this.skillEndTsMs != otherNode.skillEndTsMs) {
            return false;
        }
        if (this.cdEndTsMs != otherNode.cdEndTsMs) {
            return false;
        }
        if (this.useSkillPlayerId != otherNode.useSkillPlayerId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
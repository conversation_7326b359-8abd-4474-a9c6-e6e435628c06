package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructSkynet.SkynetNormalTask;
import com.yorha.proto.StructSkynet;
import com.yorha.proto.StructSkynetPB.SkynetNormalTaskPB;
import com.yorha.proto.StructSkynetPB;


/**
 * <AUTHOR> auto gen
 */
public class SkynetNormalTaskProp extends AbstractPropNode {

    public static final int FIELD_INDEX_NORMALTASK = 0;
    public static final int FIELD_INDEX_LASTREFRESHTSMS = 1;
    public static final int FIELD_INDEX_TASKPOOLNUM = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private Int32SkynetTaskMapProp normalTask = null;
    private long lastRefreshTsMs = Constant.DEFAULT_LONG_VALUE;
    private int taskPoolNum = Constant.DEFAULT_INT_VALUE;

    public SkynetNormalTaskProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SkynetNormalTaskProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get normalTask
     *
     * @return normalTask value
     */
    public Int32SkynetTaskMapProp getNormalTask() {
        if (this.normalTask == null) {
            this.normalTask = new Int32SkynetTaskMapProp(this, FIELD_INDEX_NORMALTASK);
        }
        return this.normalTask;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putNormalTaskV(SkynetTaskProp v) {
        this.getNormalTask().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public SkynetTaskProp addEmptyNormalTask(Integer k) {
        return this.getNormalTask().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getNormalTaskSize() {
        if (this.normalTask == null) {
            return 0;
        }
        return this.normalTask.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isNormalTaskEmpty() {
        if (this.normalTask == null) {
            return true;
        }
        return this.normalTask.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public SkynetTaskProp getNormalTaskV(Integer k) {
        if (this.normalTask == null || !this.normalTask.containsKey(k)) {
            return null;
        }
        return this.normalTask.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearNormalTask() {
        if (this.normalTask != null) {
            this.normalTask.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeNormalTaskV(Integer k) {
        if (this.normalTask != null) {
            this.normalTask.remove(k);
        }
    }
    /**
     * get lastRefreshTsMs
     *
     * @return lastRefreshTsMs value
     */
    public long getLastRefreshTsMs() {
        return this.lastRefreshTsMs;
    }

    /**
     * set lastRefreshTsMs && set marked
     *
     * @param lastRefreshTsMs new value
     * @return current object
     */
    public SkynetNormalTaskProp setLastRefreshTsMs(long lastRefreshTsMs) {
        if (this.lastRefreshTsMs != lastRefreshTsMs) {
            this.mark(FIELD_INDEX_LASTREFRESHTSMS);
            this.lastRefreshTsMs = lastRefreshTsMs;
        }
        return this;
    }

    /**
     * inner set lastRefreshTsMs
     *
     * @param lastRefreshTsMs new value
     */
    private void innerSetLastRefreshTsMs(long lastRefreshTsMs) {
        this.lastRefreshTsMs = lastRefreshTsMs;
    }

    /**
     * get taskPoolNum
     *
     * @return taskPoolNum value
     */
    public int getTaskPoolNum() {
        return this.taskPoolNum;
    }

    /**
     * set taskPoolNum && set marked
     *
     * @param taskPoolNum new value
     * @return current object
     */
    public SkynetNormalTaskProp setTaskPoolNum(int taskPoolNum) {
        if (this.taskPoolNum != taskPoolNum) {
            this.mark(FIELD_INDEX_TASKPOOLNUM);
            this.taskPoolNum = taskPoolNum;
        }
        return this;
    }

    /**
     * inner set taskPoolNum
     *
     * @param taskPoolNum new value
     */
    private void innerSetTaskPoolNum(int taskPoolNum) {
        this.taskPoolNum = taskPoolNum;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SkynetNormalTaskPB.Builder getCopyCsBuilder() {
        final SkynetNormalTaskPB.Builder builder = SkynetNormalTaskPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SkynetNormalTaskPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.normalTask != null) {
            StructSkynetPB.Int32SkynetTaskMapPB.Builder tmpBuilder = StructSkynetPB.Int32SkynetTaskMapPB.newBuilder();
            final int tmpFieldCnt = this.normalTask.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setNormalTask(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearNormalTask();
            }
        }  else if (builder.hasNormalTask()) {
            // 清理NormalTask
            builder.clearNormalTask();
            fieldCnt++;
        }
        if (this.getLastRefreshTsMs() != 0L) {
            builder.setLastRefreshTsMs(this.getLastRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasLastRefreshTsMs()) {
            // 清理LastRefreshTsMs
            builder.clearLastRefreshTsMs();
            fieldCnt++;
        }
        if (this.getTaskPoolNum() != 0) {
            builder.setTaskPoolNum(this.getTaskPoolNum());
            fieldCnt++;
        }  else if (builder.hasTaskPoolNum()) {
            // 清理TaskPoolNum
            builder.clearTaskPoolNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SkynetNormalTaskPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_NORMALTASK) && this.normalTask != null) {
            final boolean needClear = !builder.hasNormalTask();
            final int tmpFieldCnt = this.normalTask.copyChangeToCs(builder.getNormalTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearNormalTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTSMS)) {
            builder.setLastRefreshTsMs(this.getLastRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKPOOLNUM)) {
            builder.setTaskPoolNum(this.getTaskPoolNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SkynetNormalTaskPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_NORMALTASK) && this.normalTask != null) {
            final boolean needClear = !builder.hasNormalTask();
            final int tmpFieldCnt = this.normalTask.copyChangeToAndClearDeleteKeysCs(builder.getNormalTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearNormalTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTSMS)) {
            builder.setLastRefreshTsMs(this.getLastRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKPOOLNUM)) {
            builder.setTaskPoolNum(this.getTaskPoolNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SkynetNormalTaskPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasNormalTask()) {
            this.getNormalTask().mergeFromCs(proto.getNormalTask());
        } else {
            if (this.normalTask != null) {
                this.normalTask.mergeFromCs(proto.getNormalTask());
            }
        }
        if (proto.hasLastRefreshTsMs()) {
            this.innerSetLastRefreshTsMs(proto.getLastRefreshTsMs());
        } else {
            this.innerSetLastRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTaskPoolNum()) {
            this.innerSetTaskPoolNum(proto.getTaskPoolNum());
        } else {
            this.innerSetTaskPoolNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SkynetNormalTaskProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SkynetNormalTaskPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasNormalTask()) {
            this.getNormalTask().mergeChangeFromCs(proto.getNormalTask());
            fieldCnt++;
        }
        if (proto.hasLastRefreshTsMs()) {
            this.setLastRefreshTsMs(proto.getLastRefreshTsMs());
            fieldCnt++;
        }
        if (proto.hasTaskPoolNum()) {
            this.setTaskPoolNum(proto.getTaskPoolNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SkynetNormalTask.Builder getCopyDbBuilder() {
        final SkynetNormalTask.Builder builder = SkynetNormalTask.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SkynetNormalTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.normalTask != null) {
            StructSkynet.Int32SkynetTaskMap.Builder tmpBuilder = StructSkynet.Int32SkynetTaskMap.newBuilder();
            final int tmpFieldCnt = this.normalTask.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setNormalTask(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearNormalTask();
            }
        }  else if (builder.hasNormalTask()) {
            // 清理NormalTask
            builder.clearNormalTask();
            fieldCnt++;
        }
        if (this.getLastRefreshTsMs() != 0L) {
            builder.setLastRefreshTsMs(this.getLastRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasLastRefreshTsMs()) {
            // 清理LastRefreshTsMs
            builder.clearLastRefreshTsMs();
            fieldCnt++;
        }
        if (this.getTaskPoolNum() != 0) {
            builder.setTaskPoolNum(this.getTaskPoolNum());
            fieldCnt++;
        }  else if (builder.hasTaskPoolNum()) {
            // 清理TaskPoolNum
            builder.clearTaskPoolNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SkynetNormalTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_NORMALTASK) && this.normalTask != null) {
            final boolean needClear = !builder.hasNormalTask();
            final int tmpFieldCnt = this.normalTask.copyChangeToDb(builder.getNormalTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearNormalTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTSMS)) {
            builder.setLastRefreshTsMs(this.getLastRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKPOOLNUM)) {
            builder.setTaskPoolNum(this.getTaskPoolNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SkynetNormalTask proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasNormalTask()) {
            this.getNormalTask().mergeFromDb(proto.getNormalTask());
        } else {
            if (this.normalTask != null) {
                this.normalTask.mergeFromDb(proto.getNormalTask());
            }
        }
        if (proto.hasLastRefreshTsMs()) {
            this.innerSetLastRefreshTsMs(proto.getLastRefreshTsMs());
        } else {
            this.innerSetLastRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTaskPoolNum()) {
            this.innerSetTaskPoolNum(proto.getTaskPoolNum());
        } else {
            this.innerSetTaskPoolNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SkynetNormalTaskProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SkynetNormalTask proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasNormalTask()) {
            this.getNormalTask().mergeChangeFromDb(proto.getNormalTask());
            fieldCnt++;
        }
        if (proto.hasLastRefreshTsMs()) {
            this.setLastRefreshTsMs(proto.getLastRefreshTsMs());
            fieldCnt++;
        }
        if (proto.hasTaskPoolNum()) {
            this.setTaskPoolNum(proto.getTaskPoolNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SkynetNormalTask.Builder getCopySsBuilder() {
        final SkynetNormalTask.Builder builder = SkynetNormalTask.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SkynetNormalTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.normalTask != null) {
            StructSkynet.Int32SkynetTaskMap.Builder tmpBuilder = StructSkynet.Int32SkynetTaskMap.newBuilder();
            final int tmpFieldCnt = this.normalTask.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setNormalTask(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearNormalTask();
            }
        }  else if (builder.hasNormalTask()) {
            // 清理NormalTask
            builder.clearNormalTask();
            fieldCnt++;
        }
        if (this.getLastRefreshTsMs() != 0L) {
            builder.setLastRefreshTsMs(this.getLastRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasLastRefreshTsMs()) {
            // 清理LastRefreshTsMs
            builder.clearLastRefreshTsMs();
            fieldCnt++;
        }
        if (this.getTaskPoolNum() != 0) {
            builder.setTaskPoolNum(this.getTaskPoolNum());
            fieldCnt++;
        }  else if (builder.hasTaskPoolNum()) {
            // 清理TaskPoolNum
            builder.clearTaskPoolNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SkynetNormalTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_NORMALTASK) && this.normalTask != null) {
            final boolean needClear = !builder.hasNormalTask();
            final int tmpFieldCnt = this.normalTask.copyChangeToSs(builder.getNormalTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearNormalTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTSMS)) {
            builder.setLastRefreshTsMs(this.getLastRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKPOOLNUM)) {
            builder.setTaskPoolNum(this.getTaskPoolNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SkynetNormalTask proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasNormalTask()) {
            this.getNormalTask().mergeFromSs(proto.getNormalTask());
        } else {
            if (this.normalTask != null) {
                this.normalTask.mergeFromSs(proto.getNormalTask());
            }
        }
        if (proto.hasLastRefreshTsMs()) {
            this.innerSetLastRefreshTsMs(proto.getLastRefreshTsMs());
        } else {
            this.innerSetLastRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTaskPoolNum()) {
            this.innerSetTaskPoolNum(proto.getTaskPoolNum());
        } else {
            this.innerSetTaskPoolNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SkynetNormalTaskProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SkynetNormalTask proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasNormalTask()) {
            this.getNormalTask().mergeChangeFromSs(proto.getNormalTask());
            fieldCnt++;
        }
        if (proto.hasLastRefreshTsMs()) {
            this.setLastRefreshTsMs(proto.getLastRefreshTsMs());
            fieldCnt++;
        }
        if (proto.hasTaskPoolNum()) {
            this.setTaskPoolNum(proto.getTaskPoolNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SkynetNormalTask.Builder builder = SkynetNormalTask.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_NORMALTASK) && this.normalTask != null) {
            this.normalTask.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.normalTask != null) {
            this.normalTask.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SkynetNormalTaskProp)) {
            return false;
        }
        final SkynetNormalTaskProp otherNode = (SkynetNormalTaskProp) node;
        if (!this.getNormalTask().compareDataTo(otherNode.getNormalTask())) {
            return false;
        }
        if (this.lastRefreshTsMs != otherNode.lastRefreshTsMs) {
            return false;
        }
        if (this.taskPoolNum != otherNode.taskPoolNum) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
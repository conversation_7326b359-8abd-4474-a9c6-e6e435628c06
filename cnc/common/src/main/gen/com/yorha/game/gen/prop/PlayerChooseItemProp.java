package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zoneside.PlayerChooseItem;
import com.yorha.proto.ZonesidePB.PlayerChooseItemPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerChooseItemProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_PLAYERID = 0;
    public static final int FIELD_INDEX_ITEMID = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private long playerId = Constant.DEFAULT_LONG_VALUE;
    private int itemId = Constant.DEFAULT_INT_VALUE;

    public PlayerChooseItemProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerChooseItemProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get playerId
     *
     * @return playerId value
     */
    public long getPlayerId() {
        return this.playerId;
    }

    /**
     * set playerId && set marked
     *
     * @param playerId new value
     * @return current object
     */
    public PlayerChooseItemProp setPlayerId(long playerId) {
        if (this.playerId != playerId) {
            this.mark(FIELD_INDEX_PLAYERID);
            this.playerId = playerId;
        }
        return this;
    }

    /**
     * inner set playerId
     *
     * @param playerId new value
     */
    private void innerSetPlayerId(long playerId) {
        this.playerId = playerId;
    }

    /**
     * get itemId
     *
     * @return itemId value
     */
    public int getItemId() {
        return this.itemId;
    }

    /**
     * set itemId && set marked
     *
     * @param itemId new value
     * @return current object
     */
    public PlayerChooseItemProp setItemId(int itemId) {
        if (this.itemId != itemId) {
            this.mark(FIELD_INDEX_ITEMID);
            this.itemId = itemId;
        }
        return this;
    }

    /**
     * inner set itemId
     *
     * @param itemId new value
     */
    private void innerSetItemId(int itemId) {
        this.itemId = itemId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerChooseItemPB.Builder getCopyCsBuilder() {
        final PlayerChooseItemPB.Builder builder = PlayerChooseItemPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerChooseItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getItemId() != 0) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }  else if (builder.hasItemId()) {
            // 清理ItemId
            builder.clearItemId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerChooseItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMID)) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerChooseItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMID)) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerChooseItemPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasItemId()) {
            this.innerSetItemId(proto.getItemId());
        } else {
            this.innerSetItemId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerChooseItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerChooseItemPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasItemId()) {
            this.setItemId(proto.getItemId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerChooseItem.Builder getCopyDbBuilder() {
        final PlayerChooseItem.Builder builder = PlayerChooseItem.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerChooseItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getItemId() != 0) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }  else if (builder.hasItemId()) {
            // 清理ItemId
            builder.clearItemId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerChooseItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMID)) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerChooseItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasItemId()) {
            this.innerSetItemId(proto.getItemId());
        } else {
            this.innerSetItemId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerChooseItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerChooseItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasItemId()) {
            this.setItemId(proto.getItemId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerChooseItem.Builder getCopySsBuilder() {
        final PlayerChooseItem.Builder builder = PlayerChooseItem.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerChooseItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getItemId() != 0) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }  else if (builder.hasItemId()) {
            // 清理ItemId
            builder.clearItemId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerChooseItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMID)) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerChooseItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasItemId()) {
            this.innerSetItemId(proto.getItemId());
        } else {
            this.innerSetItemId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerChooseItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerChooseItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasItemId()) {
            this.setItemId(proto.getItemId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerChooseItem.Builder builder = PlayerChooseItem.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.playerId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerChooseItemProp)) {
            return false;
        }
        final PlayerChooseItemProp otherNode = (PlayerChooseItemProp) node;
        if (this.playerId != otherNode.playerId) {
            return false;
        }
        if (this.itemId != otherNode.itemId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
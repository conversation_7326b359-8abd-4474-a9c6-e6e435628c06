package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerSkynetModel;
import com.yorha.proto.StructSkynet;
import com.yorha.proto.PlayerPB.PlayerSkynetModelPB;
import com.yorha.proto.StructSkynetPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerSkynetModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_NORMALTASK = 0;
    public static final int FIELD_INDEX_BOSSTASK = 1;
    public static final int FIELD_INDEX_GUARDTASK = 2;
    public static final int FIELD_INDEX_BASEDATA = 3;
    public static final int FIELD_INDEX_SHOPDATA = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private SkynetNormalTaskProp normalTask = null;
    private SkynetBossTaskProp bossTask = null;
    private SkynetGuardTaskProp guardTask = null;
    private SkynetBaseDataProp baseData = null;
    private SkynetShopProp shopData = null;

    public PlayerSkynetModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerSkynetModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get normalTask
     *
     * @return normalTask value
     */
    public SkynetNormalTaskProp getNormalTask() {
        if (this.normalTask == null) {
            this.normalTask = new SkynetNormalTaskProp(this, FIELD_INDEX_NORMALTASK);
        }
        return this.normalTask;
    }

    /**
     * get bossTask
     *
     * @return bossTask value
     */
    public SkynetBossTaskProp getBossTask() {
        if (this.bossTask == null) {
            this.bossTask = new SkynetBossTaskProp(this, FIELD_INDEX_BOSSTASK);
        }
        return this.bossTask;
    }

    /**
     * get guardTask
     *
     * @return guardTask value
     */
    public SkynetGuardTaskProp getGuardTask() {
        if (this.guardTask == null) {
            this.guardTask = new SkynetGuardTaskProp(this, FIELD_INDEX_GUARDTASK);
        }
        return this.guardTask;
    }

    /**
     * get baseData
     *
     * @return baseData value
     */
    public SkynetBaseDataProp getBaseData() {
        if (this.baseData == null) {
            this.baseData = new SkynetBaseDataProp(this, FIELD_INDEX_BASEDATA);
        }
        return this.baseData;
    }

    /**
     * get shopData
     *
     * @return shopData value
     */
    public SkynetShopProp getShopData() {
        if (this.shopData == null) {
            this.shopData = new SkynetShopProp(this, FIELD_INDEX_SHOPDATA);
        }
        return this.shopData;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerSkynetModelPB.Builder getCopyCsBuilder() {
        final PlayerSkynetModelPB.Builder builder = PlayerSkynetModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerSkynetModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.normalTask != null) {
            StructSkynetPB.SkynetNormalTaskPB.Builder tmpBuilder = StructSkynetPB.SkynetNormalTaskPB.newBuilder();
            final int tmpFieldCnt = this.normalTask.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setNormalTask(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearNormalTask();
            }
        }  else if (builder.hasNormalTask()) {
            // 清理NormalTask
            builder.clearNormalTask();
            fieldCnt++;
        }
        if (this.bossTask != null) {
            StructSkynetPB.SkynetBossTaskPB.Builder tmpBuilder = StructSkynetPB.SkynetBossTaskPB.newBuilder();
            final int tmpFieldCnt = this.bossTask.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBossTask(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBossTask();
            }
        }  else if (builder.hasBossTask()) {
            // 清理BossTask
            builder.clearBossTask();
            fieldCnt++;
        }
        if (this.guardTask != null) {
            StructSkynetPB.SkynetGuardTaskPB.Builder tmpBuilder = StructSkynetPB.SkynetGuardTaskPB.newBuilder();
            final int tmpFieldCnt = this.guardTask.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGuardTask(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGuardTask();
            }
        }  else if (builder.hasGuardTask()) {
            // 清理GuardTask
            builder.clearGuardTask();
            fieldCnt++;
        }
        if (this.baseData != null) {
            StructSkynetPB.SkynetBaseDataPB.Builder tmpBuilder = StructSkynetPB.SkynetBaseDataPB.newBuilder();
            final int tmpFieldCnt = this.baseData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBaseData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBaseData();
            }
        }  else if (builder.hasBaseData()) {
            // 清理BaseData
            builder.clearBaseData();
            fieldCnt++;
        }
        if (this.shopData != null) {
            StructSkynetPB.SkynetShopPB.Builder tmpBuilder = StructSkynetPB.SkynetShopPB.newBuilder();
            final int tmpFieldCnt = this.shopData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setShopData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearShopData();
            }
        }  else if (builder.hasShopData()) {
            // 清理ShopData
            builder.clearShopData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerSkynetModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_NORMALTASK) && this.normalTask != null) {
            final boolean needClear = !builder.hasNormalTask();
            final int tmpFieldCnt = this.normalTask.copyChangeToCs(builder.getNormalTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearNormalTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_BOSSTASK) && this.bossTask != null) {
            final boolean needClear = !builder.hasBossTask();
            final int tmpFieldCnt = this.bossTask.copyChangeToCs(builder.getBossTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBossTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_GUARDTASK) && this.guardTask != null) {
            final boolean needClear = !builder.hasGuardTask();
            final int tmpFieldCnt = this.guardTask.copyChangeToCs(builder.getGuardTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGuardTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_BASEDATA) && this.baseData != null) {
            final boolean needClear = !builder.hasBaseData();
            final int tmpFieldCnt = this.baseData.copyChangeToCs(builder.getBaseDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBaseData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SHOPDATA) && this.shopData != null) {
            final boolean needClear = !builder.hasShopData();
            final int tmpFieldCnt = this.shopData.copyChangeToCs(builder.getShopDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearShopData();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerSkynetModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_NORMALTASK) && this.normalTask != null) {
            final boolean needClear = !builder.hasNormalTask();
            final int tmpFieldCnt = this.normalTask.copyChangeToAndClearDeleteKeysCs(builder.getNormalTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearNormalTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_BOSSTASK) && this.bossTask != null) {
            final boolean needClear = !builder.hasBossTask();
            final int tmpFieldCnt = this.bossTask.copyChangeToAndClearDeleteKeysCs(builder.getBossTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBossTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_GUARDTASK) && this.guardTask != null) {
            final boolean needClear = !builder.hasGuardTask();
            final int tmpFieldCnt = this.guardTask.copyChangeToAndClearDeleteKeysCs(builder.getGuardTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGuardTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_BASEDATA) && this.baseData != null) {
            final boolean needClear = !builder.hasBaseData();
            final int tmpFieldCnt = this.baseData.copyChangeToAndClearDeleteKeysCs(builder.getBaseDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBaseData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SHOPDATA) && this.shopData != null) {
            final boolean needClear = !builder.hasShopData();
            final int tmpFieldCnt = this.shopData.copyChangeToAndClearDeleteKeysCs(builder.getShopDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearShopData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerSkynetModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasNormalTask()) {
            this.getNormalTask().mergeFromCs(proto.getNormalTask());
        } else {
            if (this.normalTask != null) {
                this.normalTask.mergeFromCs(proto.getNormalTask());
            }
        }
        if (proto.hasBossTask()) {
            this.getBossTask().mergeFromCs(proto.getBossTask());
        } else {
            if (this.bossTask != null) {
                this.bossTask.mergeFromCs(proto.getBossTask());
            }
        }
        if (proto.hasGuardTask()) {
            this.getGuardTask().mergeFromCs(proto.getGuardTask());
        } else {
            if (this.guardTask != null) {
                this.guardTask.mergeFromCs(proto.getGuardTask());
            }
        }
        if (proto.hasBaseData()) {
            this.getBaseData().mergeFromCs(proto.getBaseData());
        } else {
            if (this.baseData != null) {
                this.baseData.mergeFromCs(proto.getBaseData());
            }
        }
        if (proto.hasShopData()) {
            this.getShopData().mergeFromCs(proto.getShopData());
        } else {
            if (this.shopData != null) {
                this.shopData.mergeFromCs(proto.getShopData());
            }
        }
        this.markAll();
        return PlayerSkynetModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerSkynetModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasNormalTask()) {
            this.getNormalTask().mergeChangeFromCs(proto.getNormalTask());
            fieldCnt++;
        }
        if (proto.hasBossTask()) {
            this.getBossTask().mergeChangeFromCs(proto.getBossTask());
            fieldCnt++;
        }
        if (proto.hasGuardTask()) {
            this.getGuardTask().mergeChangeFromCs(proto.getGuardTask());
            fieldCnt++;
        }
        if (proto.hasBaseData()) {
            this.getBaseData().mergeChangeFromCs(proto.getBaseData());
            fieldCnt++;
        }
        if (proto.hasShopData()) {
            this.getShopData().mergeChangeFromCs(proto.getShopData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerSkynetModel.Builder getCopyDbBuilder() {
        final PlayerSkynetModel.Builder builder = PlayerSkynetModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerSkynetModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.normalTask != null) {
            StructSkynet.SkynetNormalTask.Builder tmpBuilder = StructSkynet.SkynetNormalTask.newBuilder();
            final int tmpFieldCnt = this.normalTask.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setNormalTask(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearNormalTask();
            }
        }  else if (builder.hasNormalTask()) {
            // 清理NormalTask
            builder.clearNormalTask();
            fieldCnt++;
        }
        if (this.bossTask != null) {
            StructSkynet.SkynetBossTask.Builder tmpBuilder = StructSkynet.SkynetBossTask.newBuilder();
            final int tmpFieldCnt = this.bossTask.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBossTask(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBossTask();
            }
        }  else if (builder.hasBossTask()) {
            // 清理BossTask
            builder.clearBossTask();
            fieldCnt++;
        }
        if (this.guardTask != null) {
            StructSkynet.SkynetGuardTask.Builder tmpBuilder = StructSkynet.SkynetGuardTask.newBuilder();
            final int tmpFieldCnt = this.guardTask.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGuardTask(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGuardTask();
            }
        }  else if (builder.hasGuardTask()) {
            // 清理GuardTask
            builder.clearGuardTask();
            fieldCnt++;
        }
        if (this.baseData != null) {
            StructSkynet.SkynetBaseData.Builder tmpBuilder = StructSkynet.SkynetBaseData.newBuilder();
            final int tmpFieldCnt = this.baseData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBaseData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBaseData();
            }
        }  else if (builder.hasBaseData()) {
            // 清理BaseData
            builder.clearBaseData();
            fieldCnt++;
        }
        if (this.shopData != null) {
            StructSkynet.SkynetShop.Builder tmpBuilder = StructSkynet.SkynetShop.newBuilder();
            final int tmpFieldCnt = this.shopData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setShopData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearShopData();
            }
        }  else if (builder.hasShopData()) {
            // 清理ShopData
            builder.clearShopData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerSkynetModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_NORMALTASK) && this.normalTask != null) {
            final boolean needClear = !builder.hasNormalTask();
            final int tmpFieldCnt = this.normalTask.copyChangeToDb(builder.getNormalTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearNormalTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_BOSSTASK) && this.bossTask != null) {
            final boolean needClear = !builder.hasBossTask();
            final int tmpFieldCnt = this.bossTask.copyChangeToDb(builder.getBossTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBossTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_GUARDTASK) && this.guardTask != null) {
            final boolean needClear = !builder.hasGuardTask();
            final int tmpFieldCnt = this.guardTask.copyChangeToDb(builder.getGuardTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGuardTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_BASEDATA) && this.baseData != null) {
            final boolean needClear = !builder.hasBaseData();
            final int tmpFieldCnt = this.baseData.copyChangeToDb(builder.getBaseDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBaseData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SHOPDATA) && this.shopData != null) {
            final boolean needClear = !builder.hasShopData();
            final int tmpFieldCnt = this.shopData.copyChangeToDb(builder.getShopDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearShopData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerSkynetModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasNormalTask()) {
            this.getNormalTask().mergeFromDb(proto.getNormalTask());
        } else {
            if (this.normalTask != null) {
                this.normalTask.mergeFromDb(proto.getNormalTask());
            }
        }
        if (proto.hasBossTask()) {
            this.getBossTask().mergeFromDb(proto.getBossTask());
        } else {
            if (this.bossTask != null) {
                this.bossTask.mergeFromDb(proto.getBossTask());
            }
        }
        if (proto.hasGuardTask()) {
            this.getGuardTask().mergeFromDb(proto.getGuardTask());
        } else {
            if (this.guardTask != null) {
                this.guardTask.mergeFromDb(proto.getGuardTask());
            }
        }
        if (proto.hasBaseData()) {
            this.getBaseData().mergeFromDb(proto.getBaseData());
        } else {
            if (this.baseData != null) {
                this.baseData.mergeFromDb(proto.getBaseData());
            }
        }
        if (proto.hasShopData()) {
            this.getShopData().mergeFromDb(proto.getShopData());
        } else {
            if (this.shopData != null) {
                this.shopData.mergeFromDb(proto.getShopData());
            }
        }
        this.markAll();
        return PlayerSkynetModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerSkynetModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasNormalTask()) {
            this.getNormalTask().mergeChangeFromDb(proto.getNormalTask());
            fieldCnt++;
        }
        if (proto.hasBossTask()) {
            this.getBossTask().mergeChangeFromDb(proto.getBossTask());
            fieldCnt++;
        }
        if (proto.hasGuardTask()) {
            this.getGuardTask().mergeChangeFromDb(proto.getGuardTask());
            fieldCnt++;
        }
        if (proto.hasBaseData()) {
            this.getBaseData().mergeChangeFromDb(proto.getBaseData());
            fieldCnt++;
        }
        if (proto.hasShopData()) {
            this.getShopData().mergeChangeFromDb(proto.getShopData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerSkynetModel.Builder getCopySsBuilder() {
        final PlayerSkynetModel.Builder builder = PlayerSkynetModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerSkynetModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.normalTask != null) {
            StructSkynet.SkynetNormalTask.Builder tmpBuilder = StructSkynet.SkynetNormalTask.newBuilder();
            final int tmpFieldCnt = this.normalTask.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setNormalTask(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearNormalTask();
            }
        }  else if (builder.hasNormalTask()) {
            // 清理NormalTask
            builder.clearNormalTask();
            fieldCnt++;
        }
        if (this.bossTask != null) {
            StructSkynet.SkynetBossTask.Builder tmpBuilder = StructSkynet.SkynetBossTask.newBuilder();
            final int tmpFieldCnt = this.bossTask.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBossTask(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBossTask();
            }
        }  else if (builder.hasBossTask()) {
            // 清理BossTask
            builder.clearBossTask();
            fieldCnt++;
        }
        if (this.guardTask != null) {
            StructSkynet.SkynetGuardTask.Builder tmpBuilder = StructSkynet.SkynetGuardTask.newBuilder();
            final int tmpFieldCnt = this.guardTask.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGuardTask(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGuardTask();
            }
        }  else if (builder.hasGuardTask()) {
            // 清理GuardTask
            builder.clearGuardTask();
            fieldCnt++;
        }
        if (this.baseData != null) {
            StructSkynet.SkynetBaseData.Builder tmpBuilder = StructSkynet.SkynetBaseData.newBuilder();
            final int tmpFieldCnt = this.baseData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBaseData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBaseData();
            }
        }  else if (builder.hasBaseData()) {
            // 清理BaseData
            builder.clearBaseData();
            fieldCnt++;
        }
        if (this.shopData != null) {
            StructSkynet.SkynetShop.Builder tmpBuilder = StructSkynet.SkynetShop.newBuilder();
            final int tmpFieldCnt = this.shopData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setShopData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearShopData();
            }
        }  else if (builder.hasShopData()) {
            // 清理ShopData
            builder.clearShopData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerSkynetModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_NORMALTASK) && this.normalTask != null) {
            final boolean needClear = !builder.hasNormalTask();
            final int tmpFieldCnt = this.normalTask.copyChangeToSs(builder.getNormalTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearNormalTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_BOSSTASK) && this.bossTask != null) {
            final boolean needClear = !builder.hasBossTask();
            final int tmpFieldCnt = this.bossTask.copyChangeToSs(builder.getBossTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBossTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_GUARDTASK) && this.guardTask != null) {
            final boolean needClear = !builder.hasGuardTask();
            final int tmpFieldCnt = this.guardTask.copyChangeToSs(builder.getGuardTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGuardTask();
            }
        }
        if (this.hasMark(FIELD_INDEX_BASEDATA) && this.baseData != null) {
            final boolean needClear = !builder.hasBaseData();
            final int tmpFieldCnt = this.baseData.copyChangeToSs(builder.getBaseDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBaseData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SHOPDATA) && this.shopData != null) {
            final boolean needClear = !builder.hasShopData();
            final int tmpFieldCnt = this.shopData.copyChangeToSs(builder.getShopDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearShopData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerSkynetModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasNormalTask()) {
            this.getNormalTask().mergeFromSs(proto.getNormalTask());
        } else {
            if (this.normalTask != null) {
                this.normalTask.mergeFromSs(proto.getNormalTask());
            }
        }
        if (proto.hasBossTask()) {
            this.getBossTask().mergeFromSs(proto.getBossTask());
        } else {
            if (this.bossTask != null) {
                this.bossTask.mergeFromSs(proto.getBossTask());
            }
        }
        if (proto.hasGuardTask()) {
            this.getGuardTask().mergeFromSs(proto.getGuardTask());
        } else {
            if (this.guardTask != null) {
                this.guardTask.mergeFromSs(proto.getGuardTask());
            }
        }
        if (proto.hasBaseData()) {
            this.getBaseData().mergeFromSs(proto.getBaseData());
        } else {
            if (this.baseData != null) {
                this.baseData.mergeFromSs(proto.getBaseData());
            }
        }
        if (proto.hasShopData()) {
            this.getShopData().mergeFromSs(proto.getShopData());
        } else {
            if (this.shopData != null) {
                this.shopData.mergeFromSs(proto.getShopData());
            }
        }
        this.markAll();
        return PlayerSkynetModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerSkynetModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasNormalTask()) {
            this.getNormalTask().mergeChangeFromSs(proto.getNormalTask());
            fieldCnt++;
        }
        if (proto.hasBossTask()) {
            this.getBossTask().mergeChangeFromSs(proto.getBossTask());
            fieldCnt++;
        }
        if (proto.hasGuardTask()) {
            this.getGuardTask().mergeChangeFromSs(proto.getGuardTask());
            fieldCnt++;
        }
        if (proto.hasBaseData()) {
            this.getBaseData().mergeChangeFromSs(proto.getBaseData());
            fieldCnt++;
        }
        if (proto.hasShopData()) {
            this.getShopData().mergeChangeFromSs(proto.getShopData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerSkynetModel.Builder builder = PlayerSkynetModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_NORMALTASK) && this.normalTask != null) {
            this.normalTask.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BOSSTASK) && this.bossTask != null) {
            this.bossTask.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_GUARDTASK) && this.guardTask != null) {
            this.guardTask.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BASEDATA) && this.baseData != null) {
            this.baseData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SHOPDATA) && this.shopData != null) {
            this.shopData.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.normalTask != null) {
            this.normalTask.markAll();
        }
        if (this.bossTask != null) {
            this.bossTask.markAll();
        }
        if (this.guardTask != null) {
            this.guardTask.markAll();
        }
        if (this.baseData != null) {
            this.baseData.markAll();
        }
        if (this.shopData != null) {
            this.shopData.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerSkynetModelProp)) {
            return false;
        }
        final PlayerSkynetModelProp otherNode = (PlayerSkynetModelProp) node;
        if (!this.getNormalTask().compareDataTo(otherNode.getNormalTask())) {
            return false;
        }
        if (!this.getBossTask().compareDataTo(otherNode.getBossTask())) {
            return false;
        }
        if (!this.getGuardTask().compareDataTo(otherNode.getGuardTask())) {
            return false;
        }
        if (!this.getBaseData().compareDataTo(otherNode.getBaseData())) {
            return false;
        }
        if (!this.getShopData().compareDataTo(otherNode.getShopData())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerStoreRatingModel;
import com.yorha.proto.PlayerPB.PlayerStoreRatingModelPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerStoreRatingModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CITYFALLTSMS = 0;
    public static final int FIELD_INDEX_BASEBEATTACKTSMS = 1;
    public static final int FIELD_INDEX_PVPFAILTSMS = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private long cityFallTsMs = Constant.DEFAULT_LONG_VALUE;
    private long baseBeAttackTsMs = Constant.DEFAULT_LONG_VALUE;
    private long pvpFailTsMs = Constant.DEFAULT_LONG_VALUE;

    public PlayerStoreRatingModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerStoreRatingModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get cityFallTsMs
     *
     * @return cityFallTsMs value
     */
    public long getCityFallTsMs() {
        return this.cityFallTsMs;
    }

    /**
     * set cityFallTsMs && set marked
     *
     * @param cityFallTsMs new value
     * @return current object
     */
    public PlayerStoreRatingModelProp setCityFallTsMs(long cityFallTsMs) {
        if (this.cityFallTsMs != cityFallTsMs) {
            this.mark(FIELD_INDEX_CITYFALLTSMS);
            this.cityFallTsMs = cityFallTsMs;
        }
        return this;
    }

    /**
     * inner set cityFallTsMs
     *
     * @param cityFallTsMs new value
     */
    private void innerSetCityFallTsMs(long cityFallTsMs) {
        this.cityFallTsMs = cityFallTsMs;
    }

    /**
     * get baseBeAttackTsMs
     *
     * @return baseBeAttackTsMs value
     */
    public long getBaseBeAttackTsMs() {
        return this.baseBeAttackTsMs;
    }

    /**
     * set baseBeAttackTsMs && set marked
     *
     * @param baseBeAttackTsMs new value
     * @return current object
     */
    public PlayerStoreRatingModelProp setBaseBeAttackTsMs(long baseBeAttackTsMs) {
        if (this.baseBeAttackTsMs != baseBeAttackTsMs) {
            this.mark(FIELD_INDEX_BASEBEATTACKTSMS);
            this.baseBeAttackTsMs = baseBeAttackTsMs;
        }
        return this;
    }

    /**
     * inner set baseBeAttackTsMs
     *
     * @param baseBeAttackTsMs new value
     */
    private void innerSetBaseBeAttackTsMs(long baseBeAttackTsMs) {
        this.baseBeAttackTsMs = baseBeAttackTsMs;
    }

    /**
     * get pvpFailTsMs
     *
     * @return pvpFailTsMs value
     */
    public long getPvpFailTsMs() {
        return this.pvpFailTsMs;
    }

    /**
     * set pvpFailTsMs && set marked
     *
     * @param pvpFailTsMs new value
     * @return current object
     */
    public PlayerStoreRatingModelProp setPvpFailTsMs(long pvpFailTsMs) {
        if (this.pvpFailTsMs != pvpFailTsMs) {
            this.mark(FIELD_INDEX_PVPFAILTSMS);
            this.pvpFailTsMs = pvpFailTsMs;
        }
        return this;
    }

    /**
     * inner set pvpFailTsMs
     *
     * @param pvpFailTsMs new value
     */
    private void innerSetPvpFailTsMs(long pvpFailTsMs) {
        this.pvpFailTsMs = pvpFailTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerStoreRatingModelPB.Builder getCopyCsBuilder() {
        final PlayerStoreRatingModelPB.Builder builder = PlayerStoreRatingModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerStoreRatingModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCityFallTsMs() != 0L) {
            builder.setCityFallTsMs(this.getCityFallTsMs());
            fieldCnt++;
        }  else if (builder.hasCityFallTsMs()) {
            // 清理CityFallTsMs
            builder.clearCityFallTsMs();
            fieldCnt++;
        }
        if (this.getBaseBeAttackTsMs() != 0L) {
            builder.setBaseBeAttackTsMs(this.getBaseBeAttackTsMs());
            fieldCnt++;
        }  else if (builder.hasBaseBeAttackTsMs()) {
            // 清理BaseBeAttackTsMs
            builder.clearBaseBeAttackTsMs();
            fieldCnt++;
        }
        if (this.getPvpFailTsMs() != 0L) {
            builder.setPvpFailTsMs(this.getPvpFailTsMs());
            fieldCnt++;
        }  else if (builder.hasPvpFailTsMs()) {
            // 清理PvpFailTsMs
            builder.clearPvpFailTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerStoreRatingModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CITYFALLTSMS)) {
            builder.setCityFallTsMs(this.getCityFallTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASEBEATTACKTSMS)) {
            builder.setBaseBeAttackTsMs(this.getBaseBeAttackTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PVPFAILTSMS)) {
            builder.setPvpFailTsMs(this.getPvpFailTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerStoreRatingModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CITYFALLTSMS)) {
            builder.setCityFallTsMs(this.getCityFallTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASEBEATTACKTSMS)) {
            builder.setBaseBeAttackTsMs(this.getBaseBeAttackTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PVPFAILTSMS)) {
            builder.setPvpFailTsMs(this.getPvpFailTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerStoreRatingModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCityFallTsMs()) {
            this.innerSetCityFallTsMs(proto.getCityFallTsMs());
        } else {
            this.innerSetCityFallTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBaseBeAttackTsMs()) {
            this.innerSetBaseBeAttackTsMs(proto.getBaseBeAttackTsMs());
        } else {
            this.innerSetBaseBeAttackTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPvpFailTsMs()) {
            this.innerSetPvpFailTsMs(proto.getPvpFailTsMs());
        } else {
            this.innerSetPvpFailTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerStoreRatingModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerStoreRatingModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCityFallTsMs()) {
            this.setCityFallTsMs(proto.getCityFallTsMs());
            fieldCnt++;
        }
        if (proto.hasBaseBeAttackTsMs()) {
            this.setBaseBeAttackTsMs(proto.getBaseBeAttackTsMs());
            fieldCnt++;
        }
        if (proto.hasPvpFailTsMs()) {
            this.setPvpFailTsMs(proto.getPvpFailTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerStoreRatingModel.Builder getCopyDbBuilder() {
        final PlayerStoreRatingModel.Builder builder = PlayerStoreRatingModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerStoreRatingModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCityFallTsMs() != 0L) {
            builder.setCityFallTsMs(this.getCityFallTsMs());
            fieldCnt++;
        }  else if (builder.hasCityFallTsMs()) {
            // 清理CityFallTsMs
            builder.clearCityFallTsMs();
            fieldCnt++;
        }
        if (this.getBaseBeAttackTsMs() != 0L) {
            builder.setBaseBeAttackTsMs(this.getBaseBeAttackTsMs());
            fieldCnt++;
        }  else if (builder.hasBaseBeAttackTsMs()) {
            // 清理BaseBeAttackTsMs
            builder.clearBaseBeAttackTsMs();
            fieldCnt++;
        }
        if (this.getPvpFailTsMs() != 0L) {
            builder.setPvpFailTsMs(this.getPvpFailTsMs());
            fieldCnt++;
        }  else if (builder.hasPvpFailTsMs()) {
            // 清理PvpFailTsMs
            builder.clearPvpFailTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerStoreRatingModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CITYFALLTSMS)) {
            builder.setCityFallTsMs(this.getCityFallTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASEBEATTACKTSMS)) {
            builder.setBaseBeAttackTsMs(this.getBaseBeAttackTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PVPFAILTSMS)) {
            builder.setPvpFailTsMs(this.getPvpFailTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerStoreRatingModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCityFallTsMs()) {
            this.innerSetCityFallTsMs(proto.getCityFallTsMs());
        } else {
            this.innerSetCityFallTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBaseBeAttackTsMs()) {
            this.innerSetBaseBeAttackTsMs(proto.getBaseBeAttackTsMs());
        } else {
            this.innerSetBaseBeAttackTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPvpFailTsMs()) {
            this.innerSetPvpFailTsMs(proto.getPvpFailTsMs());
        } else {
            this.innerSetPvpFailTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerStoreRatingModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerStoreRatingModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCityFallTsMs()) {
            this.setCityFallTsMs(proto.getCityFallTsMs());
            fieldCnt++;
        }
        if (proto.hasBaseBeAttackTsMs()) {
            this.setBaseBeAttackTsMs(proto.getBaseBeAttackTsMs());
            fieldCnt++;
        }
        if (proto.hasPvpFailTsMs()) {
            this.setPvpFailTsMs(proto.getPvpFailTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerStoreRatingModel.Builder getCopySsBuilder() {
        final PlayerStoreRatingModel.Builder builder = PlayerStoreRatingModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerStoreRatingModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCityFallTsMs() != 0L) {
            builder.setCityFallTsMs(this.getCityFallTsMs());
            fieldCnt++;
        }  else if (builder.hasCityFallTsMs()) {
            // 清理CityFallTsMs
            builder.clearCityFallTsMs();
            fieldCnt++;
        }
        if (this.getBaseBeAttackTsMs() != 0L) {
            builder.setBaseBeAttackTsMs(this.getBaseBeAttackTsMs());
            fieldCnt++;
        }  else if (builder.hasBaseBeAttackTsMs()) {
            // 清理BaseBeAttackTsMs
            builder.clearBaseBeAttackTsMs();
            fieldCnt++;
        }
        if (this.getPvpFailTsMs() != 0L) {
            builder.setPvpFailTsMs(this.getPvpFailTsMs());
            fieldCnt++;
        }  else if (builder.hasPvpFailTsMs()) {
            // 清理PvpFailTsMs
            builder.clearPvpFailTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerStoreRatingModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CITYFALLTSMS)) {
            builder.setCityFallTsMs(this.getCityFallTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASEBEATTACKTSMS)) {
            builder.setBaseBeAttackTsMs(this.getBaseBeAttackTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PVPFAILTSMS)) {
            builder.setPvpFailTsMs(this.getPvpFailTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerStoreRatingModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCityFallTsMs()) {
            this.innerSetCityFallTsMs(proto.getCityFallTsMs());
        } else {
            this.innerSetCityFallTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBaseBeAttackTsMs()) {
            this.innerSetBaseBeAttackTsMs(proto.getBaseBeAttackTsMs());
        } else {
            this.innerSetBaseBeAttackTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPvpFailTsMs()) {
            this.innerSetPvpFailTsMs(proto.getPvpFailTsMs());
        } else {
            this.innerSetPvpFailTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerStoreRatingModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerStoreRatingModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCityFallTsMs()) {
            this.setCityFallTsMs(proto.getCityFallTsMs());
            fieldCnt++;
        }
        if (proto.hasBaseBeAttackTsMs()) {
            this.setBaseBeAttackTsMs(proto.getBaseBeAttackTsMs());
            fieldCnt++;
        }
        if (proto.hasPvpFailTsMs()) {
            this.setPvpFailTsMs(proto.getPvpFailTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerStoreRatingModel.Builder builder = PlayerStoreRatingModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerStoreRatingModelProp)) {
            return false;
        }
        final PlayerStoreRatingModelProp otherNode = (PlayerStoreRatingModelProp) node;
        if (this.cityFallTsMs != otherNode.cityFallTsMs) {
            return false;
        }
        if (this.baseBeAttackTsMs != otherNode.baseBeAttackTsMs) {
            return false;
        }
        if (this.pvpFailTsMs != otherNode.pvpFailTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
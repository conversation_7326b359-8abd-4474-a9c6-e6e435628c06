package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerResetInfo;
import com.yorha.proto.PlayerPB.PlayerResetInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerResetInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_BANRESETNAMETSMS = 0;
    public static final int FIELD_INDEX_BANRESETPICTSMS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private long banResetNameTsMs = Constant.DEFAULT_LONG_VALUE;
    private long banResetPicTsMs = Constant.DEFAULT_LONG_VALUE;

    public PlayerResetInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerResetInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get banResetNameTsMs
     *
     * @return banResetNameTsMs value
     */
    public long getBanResetNameTsMs() {
        return this.banResetNameTsMs;
    }

    /**
     * set banResetNameTsMs && set marked
     *
     * @param banResetNameTsMs new value
     * @return current object
     */
    public PlayerResetInfoProp setBanResetNameTsMs(long banResetNameTsMs) {
        if (this.banResetNameTsMs != banResetNameTsMs) {
            this.mark(FIELD_INDEX_BANRESETNAMETSMS);
            this.banResetNameTsMs = banResetNameTsMs;
        }
        return this;
    }

    /**
     * inner set banResetNameTsMs
     *
     * @param banResetNameTsMs new value
     */
    private void innerSetBanResetNameTsMs(long banResetNameTsMs) {
        this.banResetNameTsMs = banResetNameTsMs;
    }

    /**
     * get banResetPicTsMs
     *
     * @return banResetPicTsMs value
     */
    public long getBanResetPicTsMs() {
        return this.banResetPicTsMs;
    }

    /**
     * set banResetPicTsMs && set marked
     *
     * @param banResetPicTsMs new value
     * @return current object
     */
    public PlayerResetInfoProp setBanResetPicTsMs(long banResetPicTsMs) {
        if (this.banResetPicTsMs != banResetPicTsMs) {
            this.mark(FIELD_INDEX_BANRESETPICTSMS);
            this.banResetPicTsMs = banResetPicTsMs;
        }
        return this;
    }

    /**
     * inner set banResetPicTsMs
     *
     * @param banResetPicTsMs new value
     */
    private void innerSetBanResetPicTsMs(long banResetPicTsMs) {
        this.banResetPicTsMs = banResetPicTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerResetInfoPB.Builder getCopyCsBuilder() {
        final PlayerResetInfoPB.Builder builder = PlayerResetInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerResetInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBanResetNameTsMs() != 0L) {
            builder.setBanResetNameTsMs(this.getBanResetNameTsMs());
            fieldCnt++;
        }  else if (builder.hasBanResetNameTsMs()) {
            // 清理BanResetNameTsMs
            builder.clearBanResetNameTsMs();
            fieldCnt++;
        }
        if (this.getBanResetPicTsMs() != 0L) {
            builder.setBanResetPicTsMs(this.getBanResetPicTsMs());
            fieldCnt++;
        }  else if (builder.hasBanResetPicTsMs()) {
            // 清理BanResetPicTsMs
            builder.clearBanResetPicTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerResetInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BANRESETNAMETSMS)) {
            builder.setBanResetNameTsMs(this.getBanResetNameTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BANRESETPICTSMS)) {
            builder.setBanResetPicTsMs(this.getBanResetPicTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerResetInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BANRESETNAMETSMS)) {
            builder.setBanResetNameTsMs(this.getBanResetNameTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BANRESETPICTSMS)) {
            builder.setBanResetPicTsMs(this.getBanResetPicTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerResetInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBanResetNameTsMs()) {
            this.innerSetBanResetNameTsMs(proto.getBanResetNameTsMs());
        } else {
            this.innerSetBanResetNameTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBanResetPicTsMs()) {
            this.innerSetBanResetPicTsMs(proto.getBanResetPicTsMs());
        } else {
            this.innerSetBanResetPicTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerResetInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerResetInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBanResetNameTsMs()) {
            this.setBanResetNameTsMs(proto.getBanResetNameTsMs());
            fieldCnt++;
        }
        if (proto.hasBanResetPicTsMs()) {
            this.setBanResetPicTsMs(proto.getBanResetPicTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerResetInfo.Builder getCopyDbBuilder() {
        final PlayerResetInfo.Builder builder = PlayerResetInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerResetInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBanResetNameTsMs() != 0L) {
            builder.setBanResetNameTsMs(this.getBanResetNameTsMs());
            fieldCnt++;
        }  else if (builder.hasBanResetNameTsMs()) {
            // 清理BanResetNameTsMs
            builder.clearBanResetNameTsMs();
            fieldCnt++;
        }
        if (this.getBanResetPicTsMs() != 0L) {
            builder.setBanResetPicTsMs(this.getBanResetPicTsMs());
            fieldCnt++;
        }  else if (builder.hasBanResetPicTsMs()) {
            // 清理BanResetPicTsMs
            builder.clearBanResetPicTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerResetInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BANRESETNAMETSMS)) {
            builder.setBanResetNameTsMs(this.getBanResetNameTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BANRESETPICTSMS)) {
            builder.setBanResetPicTsMs(this.getBanResetPicTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerResetInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBanResetNameTsMs()) {
            this.innerSetBanResetNameTsMs(proto.getBanResetNameTsMs());
        } else {
            this.innerSetBanResetNameTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBanResetPicTsMs()) {
            this.innerSetBanResetPicTsMs(proto.getBanResetPicTsMs());
        } else {
            this.innerSetBanResetPicTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerResetInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerResetInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBanResetNameTsMs()) {
            this.setBanResetNameTsMs(proto.getBanResetNameTsMs());
            fieldCnt++;
        }
        if (proto.hasBanResetPicTsMs()) {
            this.setBanResetPicTsMs(proto.getBanResetPicTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerResetInfo.Builder getCopySsBuilder() {
        final PlayerResetInfo.Builder builder = PlayerResetInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerResetInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBanResetNameTsMs() != 0L) {
            builder.setBanResetNameTsMs(this.getBanResetNameTsMs());
            fieldCnt++;
        }  else if (builder.hasBanResetNameTsMs()) {
            // 清理BanResetNameTsMs
            builder.clearBanResetNameTsMs();
            fieldCnt++;
        }
        if (this.getBanResetPicTsMs() != 0L) {
            builder.setBanResetPicTsMs(this.getBanResetPicTsMs());
            fieldCnt++;
        }  else if (builder.hasBanResetPicTsMs()) {
            // 清理BanResetPicTsMs
            builder.clearBanResetPicTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerResetInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BANRESETNAMETSMS)) {
            builder.setBanResetNameTsMs(this.getBanResetNameTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BANRESETPICTSMS)) {
            builder.setBanResetPicTsMs(this.getBanResetPicTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerResetInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBanResetNameTsMs()) {
            this.innerSetBanResetNameTsMs(proto.getBanResetNameTsMs());
        } else {
            this.innerSetBanResetNameTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBanResetPicTsMs()) {
            this.innerSetBanResetPicTsMs(proto.getBanResetPicTsMs());
        } else {
            this.innerSetBanResetPicTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerResetInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerResetInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBanResetNameTsMs()) {
            this.setBanResetNameTsMs(proto.getBanResetNameTsMs());
            fieldCnt++;
        }
        if (proto.hasBanResetPicTsMs()) {
            this.setBanResetPicTsMs(proto.getBanResetPicTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerResetInfo.Builder builder = PlayerResetInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerResetInfoProp)) {
            return false;
        }
        final PlayerResetInfoProp otherNode = (PlayerResetInfoProp) node;
        if (this.banResetNameTsMs != otherNode.banResetNameTsMs) {
            return false;
        }
        if (this.banResetPicTsMs != otherNode.banResetPicTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ResellTriggerBundleOrderParam;
import com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB;


/**
 * <AUTHOR> auto gen
 */
public class ResellTriggerBundleOrderParamProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ACTID = 0;
    public static final int FIELD_INDEX_UNITID = 1;
    public static final int FIELD_INDEX_TRIGGERBUNDLEID = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int actId = Constant.DEFAULT_INT_VALUE;
    private int unitId = Constant.DEFAULT_INT_VALUE;
    private long triggerBundleId = Constant.DEFAULT_LONG_VALUE;

    public ResellTriggerBundleOrderParamProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ResellTriggerBundleOrderParamProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get actId
     *
     * @return actId value
     */
    public int getActId() {
        return this.actId;
    }

    /**
     * set actId && set marked
     *
     * @param actId new value
     * @return current object
     */
    public ResellTriggerBundleOrderParamProp setActId(int actId) {
        if (this.actId != actId) {
            this.mark(FIELD_INDEX_ACTID);
            this.actId = actId;
        }
        return this;
    }

    /**
     * inner set actId
     *
     * @param actId new value
     */
    private void innerSetActId(int actId) {
        this.actId = actId;
    }

    /**
     * get unitId
     *
     * @return unitId value
     */
    public int getUnitId() {
        return this.unitId;
    }

    /**
     * set unitId && set marked
     *
     * @param unitId new value
     * @return current object
     */
    public ResellTriggerBundleOrderParamProp setUnitId(int unitId) {
        if (this.unitId != unitId) {
            this.mark(FIELD_INDEX_UNITID);
            this.unitId = unitId;
        }
        return this;
    }

    /**
     * inner set unitId
     *
     * @param unitId new value
     */
    private void innerSetUnitId(int unitId) {
        this.unitId = unitId;
    }

    /**
     * get triggerBundleId
     *
     * @return triggerBundleId value
     */
    public long getTriggerBundleId() {
        return this.triggerBundleId;
    }

    /**
     * set triggerBundleId && set marked
     *
     * @param triggerBundleId new value
     * @return current object
     */
    public ResellTriggerBundleOrderParamProp setTriggerBundleId(long triggerBundleId) {
        if (this.triggerBundleId != triggerBundleId) {
            this.mark(FIELD_INDEX_TRIGGERBUNDLEID);
            this.triggerBundleId = triggerBundleId;
        }
        return this;
    }

    /**
     * inner set triggerBundleId
     *
     * @param triggerBundleId new value
     */
    private void innerSetTriggerBundleId(long triggerBundleId) {
        this.triggerBundleId = triggerBundleId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ResellTriggerBundleOrderParamPB.Builder getCopyCsBuilder() {
        final ResellTriggerBundleOrderParamPB.Builder builder = ResellTriggerBundleOrderParamPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ResellTriggerBundleOrderParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActId() != 0) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }  else if (builder.hasActId()) {
            // 清理ActId
            builder.clearActId();
            fieldCnt++;
        }
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        if (this.getTriggerBundleId() != 0L) {
            builder.setTriggerBundleId(this.getTriggerBundleId());
            fieldCnt++;
        }  else if (builder.hasTriggerBundleId()) {
            // 清理TriggerBundleId
            builder.clearTriggerBundleId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ResellTriggerBundleOrderParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERBUNDLEID)) {
            builder.setTriggerBundleId(this.getTriggerBundleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ResellTriggerBundleOrderParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERBUNDLEID)) {
            builder.setTriggerBundleId(this.getTriggerBundleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ResellTriggerBundleOrderParamPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActId()) {
            this.innerSetActId(proto.getActId());
        } else {
            this.innerSetActId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTriggerBundleId()) {
            this.innerSetTriggerBundleId(proto.getTriggerBundleId());
        } else {
            this.innerSetTriggerBundleId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ResellTriggerBundleOrderParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ResellTriggerBundleOrderParamPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActId()) {
            this.setActId(proto.getActId());
            fieldCnt++;
        }
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        if (proto.hasTriggerBundleId()) {
            this.setTriggerBundleId(proto.getTriggerBundleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ResellTriggerBundleOrderParam.Builder getCopyDbBuilder() {
        final ResellTriggerBundleOrderParam.Builder builder = ResellTriggerBundleOrderParam.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ResellTriggerBundleOrderParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActId() != 0) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }  else if (builder.hasActId()) {
            // 清理ActId
            builder.clearActId();
            fieldCnt++;
        }
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        if (this.getTriggerBundleId() != 0L) {
            builder.setTriggerBundleId(this.getTriggerBundleId());
            fieldCnt++;
        }  else if (builder.hasTriggerBundleId()) {
            // 清理TriggerBundleId
            builder.clearTriggerBundleId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ResellTriggerBundleOrderParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERBUNDLEID)) {
            builder.setTriggerBundleId(this.getTriggerBundleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ResellTriggerBundleOrderParam proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActId()) {
            this.innerSetActId(proto.getActId());
        } else {
            this.innerSetActId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTriggerBundleId()) {
            this.innerSetTriggerBundleId(proto.getTriggerBundleId());
        } else {
            this.innerSetTriggerBundleId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ResellTriggerBundleOrderParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ResellTriggerBundleOrderParam proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActId()) {
            this.setActId(proto.getActId());
            fieldCnt++;
        }
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        if (proto.hasTriggerBundleId()) {
            this.setTriggerBundleId(proto.getTriggerBundleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ResellTriggerBundleOrderParam.Builder getCopySsBuilder() {
        final ResellTriggerBundleOrderParam.Builder builder = ResellTriggerBundleOrderParam.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ResellTriggerBundleOrderParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActId() != 0) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }  else if (builder.hasActId()) {
            // 清理ActId
            builder.clearActId();
            fieldCnt++;
        }
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        if (this.getTriggerBundleId() != 0L) {
            builder.setTriggerBundleId(this.getTriggerBundleId());
            fieldCnt++;
        }  else if (builder.hasTriggerBundleId()) {
            // 清理TriggerBundleId
            builder.clearTriggerBundleId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ResellTriggerBundleOrderParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERBUNDLEID)) {
            builder.setTriggerBundleId(this.getTriggerBundleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ResellTriggerBundleOrderParam proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActId()) {
            this.innerSetActId(proto.getActId());
        } else {
            this.innerSetActId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTriggerBundleId()) {
            this.innerSetTriggerBundleId(proto.getTriggerBundleId());
        } else {
            this.innerSetTriggerBundleId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ResellTriggerBundleOrderParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ResellTriggerBundleOrderParam proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActId()) {
            this.setActId(proto.getActId());
            fieldCnt++;
        }
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        if (proto.hasTriggerBundleId()) {
            this.setTriggerBundleId(proto.getTriggerBundleId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ResellTriggerBundleOrderParam.Builder builder = ResellTriggerBundleOrderParam.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ResellTriggerBundleOrderParamProp)) {
            return false;
        }
        final ResellTriggerBundleOrderParamProp otherNode = (ResellTriggerBundleOrderParamProp) node;
        if (this.actId != otherNode.actId) {
            return false;
        }
        if (this.unitId != otherNode.unitId) {
            return false;
        }
        if (this.triggerBundleId != otherNode.triggerBundleId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructPlayer.PlayerFormation;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.StructPlayerPB.PlayerFormationPB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerFormationProp extends AbstractPropNode {

    public static final int FIELD_INDEX_FORMATIONMAP = 0;
    public static final int FIELD_INDEX_FORCEDDEFEATARMYTSMS = 1;
    public static final int FIELD_INDEX_TROOPRHMAP = 2;
    public static final int FIELD_INDEX_POSITIONMAP = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private Int32FormationMapProp formationMap = null;
    private long forcedDefeatArmyTsMs = Constant.DEFAULT_LONG_VALUE;
    private Int32TroopRHMapProp troopRHMap = null;
    private Int32VirtualFormationMapProp positionMap = null;

    public PlayerFormationProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerFormationProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get formationMap
     *
     * @return formationMap value
     */
    public Int32FormationMapProp getFormationMap() {
        if (this.formationMap == null) {
            this.formationMap = new Int32FormationMapProp(this, FIELD_INDEX_FORMATIONMAP);
        }
        return this.formationMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putFormationMapV(FormationProp v) {
        this.getFormationMap().put(v.getFormationId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public FormationProp addEmptyFormationMap(Integer k) {
        return this.getFormationMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getFormationMapSize() {
        if (this.formationMap == null) {
            return 0;
        }
        return this.formationMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isFormationMapEmpty() {
        if (this.formationMap == null) {
            return true;
        }
        return this.formationMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public FormationProp getFormationMapV(Integer k) {
        if (this.formationMap == null || !this.formationMap.containsKey(k)) {
            return null;
        }
        return this.formationMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearFormationMap() {
        if (this.formationMap != null) {
            this.formationMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeFormationMapV(Integer k) {
        if (this.formationMap != null) {
            this.formationMap.remove(k);
        }
    }
    /**
     * get forcedDefeatArmyTsMs
     *
     * @return forcedDefeatArmyTsMs value
     */
    public long getForcedDefeatArmyTsMs() {
        return this.forcedDefeatArmyTsMs;
    }

    /**
     * set forcedDefeatArmyTsMs && set marked
     *
     * @param forcedDefeatArmyTsMs new value
     * @return current object
     */
    public PlayerFormationProp setForcedDefeatArmyTsMs(long forcedDefeatArmyTsMs) {
        if (this.forcedDefeatArmyTsMs != forcedDefeatArmyTsMs) {
            this.mark(FIELD_INDEX_FORCEDDEFEATARMYTSMS);
            this.forcedDefeatArmyTsMs = forcedDefeatArmyTsMs;
        }
        return this;
    }

    /**
     * inner set forcedDefeatArmyTsMs
     *
     * @param forcedDefeatArmyTsMs new value
     */
    private void innerSetForcedDefeatArmyTsMs(long forcedDefeatArmyTsMs) {
        this.forcedDefeatArmyTsMs = forcedDefeatArmyTsMs;
    }

    /**
     * get troopRHMap
     *
     * @return troopRHMap value
     */
    public Int32TroopRHMapProp getTroopRHMap() {
        if (this.troopRHMap == null) {
            this.troopRHMap = new Int32TroopRHMapProp(this, FIELD_INDEX_TROOPRHMAP);
        }
        return this.troopRHMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTroopRHMapV(TroopRHProp v) {
        this.getTroopRHMap().put(v.getTroopId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public TroopRHProp addEmptyTroopRHMap(Integer k) {
        return this.getTroopRHMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTroopRHMapSize() {
        if (this.troopRHMap == null) {
            return 0;
        }
        return this.troopRHMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTroopRHMapEmpty() {
        if (this.troopRHMap == null) {
            return true;
        }
        return this.troopRHMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public TroopRHProp getTroopRHMapV(Integer k) {
        if (this.troopRHMap == null || !this.troopRHMap.containsKey(k)) {
            return null;
        }
        return this.troopRHMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTroopRHMap() {
        if (this.troopRHMap != null) {
            this.troopRHMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTroopRHMapV(Integer k) {
        if (this.troopRHMap != null) {
            this.troopRHMap.remove(k);
        }
    }
    /**
     * get positionMap
     *
     * @return positionMap value
     */
    public Int32VirtualFormationMapProp getPositionMap() {
        if (this.positionMap == null) {
            this.positionMap = new Int32VirtualFormationMapProp(this, FIELD_INDEX_POSITIONMAP);
        }
        return this.positionMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putPositionMapV(VirtualFormationProp v) {
        this.getPositionMap().put(v.getPosition(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public VirtualFormationProp addEmptyPositionMap(Integer k) {
        return this.getPositionMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getPositionMapSize() {
        if (this.positionMap == null) {
            return 0;
        }
        return this.positionMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isPositionMapEmpty() {
        if (this.positionMap == null) {
            return true;
        }
        return this.positionMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public VirtualFormationProp getPositionMapV(Integer k) {
        if (this.positionMap == null || !this.positionMap.containsKey(k)) {
            return null;
        }
        return this.positionMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearPositionMap() {
        if (this.positionMap != null) {
            this.positionMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removePositionMapV(Integer k) {
        if (this.positionMap != null) {
            this.positionMap.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerFormationPB.Builder getCopyCsBuilder() {
        final PlayerFormationPB.Builder builder = PlayerFormationPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerFormationPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.formationMap != null) {
            StructPlayerPB.Int32FormationMapPB.Builder tmpBuilder = StructPlayerPB.Int32FormationMapPB.newBuilder();
            final int tmpFieldCnt = this.formationMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFormationMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFormationMap();
            }
        }  else if (builder.hasFormationMap()) {
            // 清理FormationMap
            builder.clearFormationMap();
            fieldCnt++;
        }
        if (this.getForcedDefeatArmyTsMs() != 0L) {
            builder.setForcedDefeatArmyTsMs(this.getForcedDefeatArmyTsMs());
            fieldCnt++;
        }  else if (builder.hasForcedDefeatArmyTsMs()) {
            // 清理ForcedDefeatArmyTsMs
            builder.clearForcedDefeatArmyTsMs();
            fieldCnt++;
        }
        if (this.troopRHMap != null) {
            StructPlayerPB.Int32TroopRHMapPB.Builder tmpBuilder = StructPlayerPB.Int32TroopRHMapPB.newBuilder();
            final int tmpFieldCnt = this.troopRHMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroopRHMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroopRHMap();
            }
        }  else if (builder.hasTroopRHMap()) {
            // 清理TroopRHMap
            builder.clearTroopRHMap();
            fieldCnt++;
        }
        if (this.positionMap != null) {
            StructPlayerPB.Int32VirtualFormationMapPB.Builder tmpBuilder = StructPlayerPB.Int32VirtualFormationMapPB.newBuilder();
            final int tmpFieldCnt = this.positionMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPositionMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPositionMap();
            }
        }  else if (builder.hasPositionMap()) {
            // 清理PositionMap
            builder.clearPositionMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerFormationPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FORMATIONMAP) && this.formationMap != null) {
            final boolean needClear = !builder.hasFormationMap();
            final int tmpFieldCnt = this.formationMap.copyChangeToCs(builder.getFormationMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFormationMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_FORCEDDEFEATARMYTSMS)) {
            builder.setForcedDefeatArmyTsMs(this.getForcedDefeatArmyTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOPRHMAP) && this.troopRHMap != null) {
            final boolean needClear = !builder.hasTroopRHMap();
            final int tmpFieldCnt = this.troopRHMap.copyChangeToCs(builder.getTroopRHMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroopRHMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_POSITIONMAP) && this.positionMap != null) {
            final boolean needClear = !builder.hasPositionMap();
            final int tmpFieldCnt = this.positionMap.copyChangeToCs(builder.getPositionMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPositionMap();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerFormationPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FORMATIONMAP) && this.formationMap != null) {
            final boolean needClear = !builder.hasFormationMap();
            final int tmpFieldCnt = this.formationMap.copyChangeToAndClearDeleteKeysCs(builder.getFormationMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFormationMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_FORCEDDEFEATARMYTSMS)) {
            builder.setForcedDefeatArmyTsMs(this.getForcedDefeatArmyTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOPRHMAP) && this.troopRHMap != null) {
            final boolean needClear = !builder.hasTroopRHMap();
            final int tmpFieldCnt = this.troopRHMap.copyChangeToAndClearDeleteKeysCs(builder.getTroopRHMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroopRHMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_POSITIONMAP) && this.positionMap != null) {
            final boolean needClear = !builder.hasPositionMap();
            final int tmpFieldCnt = this.positionMap.copyChangeToAndClearDeleteKeysCs(builder.getPositionMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPositionMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerFormationPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasFormationMap()) {
            this.getFormationMap().mergeFromCs(proto.getFormationMap());
        } else {
            if (this.formationMap != null) {
                this.formationMap.mergeFromCs(proto.getFormationMap());
            }
        }
        if (proto.hasForcedDefeatArmyTsMs()) {
            this.innerSetForcedDefeatArmyTsMs(proto.getForcedDefeatArmyTsMs());
        } else {
            this.innerSetForcedDefeatArmyTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTroopRHMap()) {
            this.getTroopRHMap().mergeFromCs(proto.getTroopRHMap());
        } else {
            if (this.troopRHMap != null) {
                this.troopRHMap.mergeFromCs(proto.getTroopRHMap());
            }
        }
        if (proto.hasPositionMap()) {
            this.getPositionMap().mergeFromCs(proto.getPositionMap());
        } else {
            if (this.positionMap != null) {
                this.positionMap.mergeFromCs(proto.getPositionMap());
            }
        }
        this.markAll();
        return PlayerFormationProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerFormationPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasFormationMap()) {
            this.getFormationMap().mergeChangeFromCs(proto.getFormationMap());
            fieldCnt++;
        }
        if (proto.hasForcedDefeatArmyTsMs()) {
            this.setForcedDefeatArmyTsMs(proto.getForcedDefeatArmyTsMs());
            fieldCnt++;
        }
        if (proto.hasTroopRHMap()) {
            this.getTroopRHMap().mergeChangeFromCs(proto.getTroopRHMap());
            fieldCnt++;
        }
        if (proto.hasPositionMap()) {
            this.getPositionMap().mergeChangeFromCs(proto.getPositionMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerFormation.Builder getCopyDbBuilder() {
        final PlayerFormation.Builder builder = PlayerFormation.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerFormation.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.formationMap != null) {
            StructPlayer.Int32FormationMap.Builder tmpBuilder = StructPlayer.Int32FormationMap.newBuilder();
            final int tmpFieldCnt = this.formationMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFormationMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFormationMap();
            }
        }  else if (builder.hasFormationMap()) {
            // 清理FormationMap
            builder.clearFormationMap();
            fieldCnt++;
        }
        if (this.getForcedDefeatArmyTsMs() != 0L) {
            builder.setForcedDefeatArmyTsMs(this.getForcedDefeatArmyTsMs());
            fieldCnt++;
        }  else if (builder.hasForcedDefeatArmyTsMs()) {
            // 清理ForcedDefeatArmyTsMs
            builder.clearForcedDefeatArmyTsMs();
            fieldCnt++;
        }
        if (this.troopRHMap != null) {
            StructPlayer.Int32TroopRHMap.Builder tmpBuilder = StructPlayer.Int32TroopRHMap.newBuilder();
            final int tmpFieldCnt = this.troopRHMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroopRHMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroopRHMap();
            }
        }  else if (builder.hasTroopRHMap()) {
            // 清理TroopRHMap
            builder.clearTroopRHMap();
            fieldCnt++;
        }
        if (this.positionMap != null) {
            StructPlayer.Int32VirtualFormationMap.Builder tmpBuilder = StructPlayer.Int32VirtualFormationMap.newBuilder();
            final int tmpFieldCnt = this.positionMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPositionMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPositionMap();
            }
        }  else if (builder.hasPositionMap()) {
            // 清理PositionMap
            builder.clearPositionMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerFormation.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FORMATIONMAP) && this.formationMap != null) {
            final boolean needClear = !builder.hasFormationMap();
            final int tmpFieldCnt = this.formationMap.copyChangeToDb(builder.getFormationMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFormationMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_FORCEDDEFEATARMYTSMS)) {
            builder.setForcedDefeatArmyTsMs(this.getForcedDefeatArmyTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOPRHMAP) && this.troopRHMap != null) {
            final boolean needClear = !builder.hasTroopRHMap();
            final int tmpFieldCnt = this.troopRHMap.copyChangeToDb(builder.getTroopRHMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroopRHMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_POSITIONMAP) && this.positionMap != null) {
            final boolean needClear = !builder.hasPositionMap();
            final int tmpFieldCnt = this.positionMap.copyChangeToDb(builder.getPositionMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPositionMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerFormation proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasFormationMap()) {
            this.getFormationMap().mergeFromDb(proto.getFormationMap());
        } else {
            if (this.formationMap != null) {
                this.formationMap.mergeFromDb(proto.getFormationMap());
            }
        }
        if (proto.hasForcedDefeatArmyTsMs()) {
            this.innerSetForcedDefeatArmyTsMs(proto.getForcedDefeatArmyTsMs());
        } else {
            this.innerSetForcedDefeatArmyTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTroopRHMap()) {
            this.getTroopRHMap().mergeFromDb(proto.getTroopRHMap());
        } else {
            if (this.troopRHMap != null) {
                this.troopRHMap.mergeFromDb(proto.getTroopRHMap());
            }
        }
        if (proto.hasPositionMap()) {
            this.getPositionMap().mergeFromDb(proto.getPositionMap());
        } else {
            if (this.positionMap != null) {
                this.positionMap.mergeFromDb(proto.getPositionMap());
            }
        }
        this.markAll();
        return PlayerFormationProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerFormation proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasFormationMap()) {
            this.getFormationMap().mergeChangeFromDb(proto.getFormationMap());
            fieldCnt++;
        }
        if (proto.hasForcedDefeatArmyTsMs()) {
            this.setForcedDefeatArmyTsMs(proto.getForcedDefeatArmyTsMs());
            fieldCnt++;
        }
        if (proto.hasTroopRHMap()) {
            this.getTroopRHMap().mergeChangeFromDb(proto.getTroopRHMap());
            fieldCnt++;
        }
        if (proto.hasPositionMap()) {
            this.getPositionMap().mergeChangeFromDb(proto.getPositionMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerFormation.Builder getCopySsBuilder() {
        final PlayerFormation.Builder builder = PlayerFormation.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerFormation.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.formationMap != null) {
            StructPlayer.Int32FormationMap.Builder tmpBuilder = StructPlayer.Int32FormationMap.newBuilder();
            final int tmpFieldCnt = this.formationMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFormationMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFormationMap();
            }
        }  else if (builder.hasFormationMap()) {
            // 清理FormationMap
            builder.clearFormationMap();
            fieldCnt++;
        }
        if (this.getForcedDefeatArmyTsMs() != 0L) {
            builder.setForcedDefeatArmyTsMs(this.getForcedDefeatArmyTsMs());
            fieldCnt++;
        }  else if (builder.hasForcedDefeatArmyTsMs()) {
            // 清理ForcedDefeatArmyTsMs
            builder.clearForcedDefeatArmyTsMs();
            fieldCnt++;
        }
        if (this.troopRHMap != null) {
            StructPlayer.Int32TroopRHMap.Builder tmpBuilder = StructPlayer.Int32TroopRHMap.newBuilder();
            final int tmpFieldCnt = this.troopRHMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroopRHMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroopRHMap();
            }
        }  else if (builder.hasTroopRHMap()) {
            // 清理TroopRHMap
            builder.clearTroopRHMap();
            fieldCnt++;
        }
        if (this.positionMap != null) {
            StructPlayer.Int32VirtualFormationMap.Builder tmpBuilder = StructPlayer.Int32VirtualFormationMap.newBuilder();
            final int tmpFieldCnt = this.positionMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPositionMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPositionMap();
            }
        }  else if (builder.hasPositionMap()) {
            // 清理PositionMap
            builder.clearPositionMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerFormation.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FORMATIONMAP) && this.formationMap != null) {
            final boolean needClear = !builder.hasFormationMap();
            final int tmpFieldCnt = this.formationMap.copyChangeToSs(builder.getFormationMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFormationMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_FORCEDDEFEATARMYTSMS)) {
            builder.setForcedDefeatArmyTsMs(this.getForcedDefeatArmyTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOPRHMAP) && this.troopRHMap != null) {
            final boolean needClear = !builder.hasTroopRHMap();
            final int tmpFieldCnt = this.troopRHMap.copyChangeToSs(builder.getTroopRHMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroopRHMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_POSITIONMAP) && this.positionMap != null) {
            final boolean needClear = !builder.hasPositionMap();
            final int tmpFieldCnt = this.positionMap.copyChangeToSs(builder.getPositionMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPositionMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerFormation proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasFormationMap()) {
            this.getFormationMap().mergeFromSs(proto.getFormationMap());
        } else {
            if (this.formationMap != null) {
                this.formationMap.mergeFromSs(proto.getFormationMap());
            }
        }
        if (proto.hasForcedDefeatArmyTsMs()) {
            this.innerSetForcedDefeatArmyTsMs(proto.getForcedDefeatArmyTsMs());
        } else {
            this.innerSetForcedDefeatArmyTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTroopRHMap()) {
            this.getTroopRHMap().mergeFromSs(proto.getTroopRHMap());
        } else {
            if (this.troopRHMap != null) {
                this.troopRHMap.mergeFromSs(proto.getTroopRHMap());
            }
        }
        if (proto.hasPositionMap()) {
            this.getPositionMap().mergeFromSs(proto.getPositionMap());
        } else {
            if (this.positionMap != null) {
                this.positionMap.mergeFromSs(proto.getPositionMap());
            }
        }
        this.markAll();
        return PlayerFormationProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerFormation proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasFormationMap()) {
            this.getFormationMap().mergeChangeFromSs(proto.getFormationMap());
            fieldCnt++;
        }
        if (proto.hasForcedDefeatArmyTsMs()) {
            this.setForcedDefeatArmyTsMs(proto.getForcedDefeatArmyTsMs());
            fieldCnt++;
        }
        if (proto.hasTroopRHMap()) {
            this.getTroopRHMap().mergeChangeFromSs(proto.getTroopRHMap());
            fieldCnt++;
        }
        if (proto.hasPositionMap()) {
            this.getPositionMap().mergeChangeFromSs(proto.getPositionMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerFormation.Builder builder = PlayerFormation.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_FORMATIONMAP) && this.formationMap != null) {
            this.formationMap.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TROOPRHMAP) && this.troopRHMap != null) {
            this.troopRHMap.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_POSITIONMAP) && this.positionMap != null) {
            this.positionMap.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.formationMap != null) {
            this.formationMap.markAll();
        }
        if (this.troopRHMap != null) {
            this.troopRHMap.markAll();
        }
        if (this.positionMap != null) {
            this.positionMap.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerFormationProp)) {
            return false;
        }
        final PlayerFormationProp otherNode = (PlayerFormationProp) node;
        if (!this.getFormationMap().compareDataTo(otherNode.getFormationMap())) {
            return false;
        }
        if (this.forcedDefeatArmyTsMs != otherNode.forcedDefeatArmyTsMs) {
            return false;
        }
        if (!this.getTroopRHMap().compareDataTo(otherNode.getTroopRHMap())) {
            return false;
        }
        if (!this.getPositionMap().compareDataTo(otherNode.getPositionMap())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
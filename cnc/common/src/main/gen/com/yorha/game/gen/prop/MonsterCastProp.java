package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.MonsterCast;
import com.yorha.proto.StructPB.MonsterCastPB;


/**
 * <AUTHOR> auto gen
 */
public class MonsterCastProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CASTRATE = 0;
    public static final int FIELD_INDEX_CASTVALUE = 1;
    public static final int FIELD_INDEX_FINISH = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int castRate = Constant.DEFAULT_INT_VALUE;
    private int castValue = Constant.DEFAULT_INT_VALUE;
    private boolean finish = Constant.DEFAULT_BOOLEAN_VALUE;

    public MonsterCastProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MonsterCastProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get castRate
     *
     * @return castRate value
     */
    public int getCastRate() {
        return this.castRate;
    }

    /**
     * set castRate && set marked
     *
     * @param castRate new value
     * @return current object
     */
    public MonsterCastProp setCastRate(int castRate) {
        if (this.castRate != castRate) {
            this.mark(FIELD_INDEX_CASTRATE);
            this.castRate = castRate;
        }
        return this;
    }

    /**
     * inner set castRate
     *
     * @param castRate new value
     */
    private void innerSetCastRate(int castRate) {
        this.castRate = castRate;
    }

    /**
     * get castValue
     *
     * @return castValue value
     */
    public int getCastValue() {
        return this.castValue;
    }

    /**
     * set castValue && set marked
     *
     * @param castValue new value
     * @return current object
     */
    public MonsterCastProp setCastValue(int castValue) {
        if (this.castValue != castValue) {
            this.mark(FIELD_INDEX_CASTVALUE);
            this.castValue = castValue;
        }
        return this;
    }

    /**
     * inner set castValue
     *
     * @param castValue new value
     */
    private void innerSetCastValue(int castValue) {
        this.castValue = castValue;
    }

    /**
     * get finish
     *
     * @return finish value
     */
    public boolean getFinish() {
        return this.finish;
    }

    /**
     * set finish && set marked
     *
     * @param finish new value
     * @return current object
     */
    public MonsterCastProp setFinish(boolean finish) {
        if (this.finish != finish) {
            this.mark(FIELD_INDEX_FINISH);
            this.finish = finish;
        }
        return this;
    }

    /**
     * inner set finish
     *
     * @param finish new value
     */
    private void innerSetFinish(boolean finish) {
        this.finish = finish;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MonsterCastPB.Builder getCopyCsBuilder() {
        final MonsterCastPB.Builder builder = MonsterCastPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MonsterCastPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCastValue() != 0) {
            builder.setCastValue(this.getCastValue());
            fieldCnt++;
        }  else if (builder.hasCastValue()) {
            // 清理CastValue
            builder.clearCastValue();
            fieldCnt++;
        }
        if (this.getFinish()) {
            builder.setFinish(this.getFinish());
            fieldCnt++;
        }  else if (builder.hasFinish()) {
            // 清理Finish
            builder.clearFinish();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MonsterCastPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CASTVALUE)) {
            builder.setCastValue(this.getCastValue());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FINISH)) {
            builder.setFinish(this.getFinish());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MonsterCastPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CASTVALUE)) {
            builder.setCastValue(this.getCastValue());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FINISH)) {
            builder.setFinish(this.getFinish());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MonsterCastPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCastValue()) {
            this.innerSetCastValue(proto.getCastValue());
        } else {
            this.innerSetCastValue(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFinish()) {
            this.innerSetFinish(proto.getFinish());
        } else {
            this.innerSetFinish(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return MonsterCastProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MonsterCastPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCastValue()) {
            this.setCastValue(proto.getCastValue());
            fieldCnt++;
        }
        if (proto.hasFinish()) {
            this.setFinish(proto.getFinish());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MonsterCast.Builder getCopyDbBuilder() {
        final MonsterCast.Builder builder = MonsterCast.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MonsterCast.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCastRate() != 0) {
            builder.setCastRate(this.getCastRate());
            fieldCnt++;
        }  else if (builder.hasCastRate()) {
            // 清理CastRate
            builder.clearCastRate();
            fieldCnt++;
        }
        if (this.getCastValue() != 0) {
            builder.setCastValue(this.getCastValue());
            fieldCnt++;
        }  else if (builder.hasCastValue()) {
            // 清理CastValue
            builder.clearCastValue();
            fieldCnt++;
        }
        if (this.getFinish()) {
            builder.setFinish(this.getFinish());
            fieldCnt++;
        }  else if (builder.hasFinish()) {
            // 清理Finish
            builder.clearFinish();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MonsterCast.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CASTRATE)) {
            builder.setCastRate(this.getCastRate());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CASTVALUE)) {
            builder.setCastValue(this.getCastValue());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FINISH)) {
            builder.setFinish(this.getFinish());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MonsterCast proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCastRate()) {
            this.innerSetCastRate(proto.getCastRate());
        } else {
            this.innerSetCastRate(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCastValue()) {
            this.innerSetCastValue(proto.getCastValue());
        } else {
            this.innerSetCastValue(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFinish()) {
            this.innerSetFinish(proto.getFinish());
        } else {
            this.innerSetFinish(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return MonsterCastProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MonsterCast proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCastRate()) {
            this.setCastRate(proto.getCastRate());
            fieldCnt++;
        }
        if (proto.hasCastValue()) {
            this.setCastValue(proto.getCastValue());
            fieldCnt++;
        }
        if (proto.hasFinish()) {
            this.setFinish(proto.getFinish());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MonsterCast.Builder getCopySsBuilder() {
        final MonsterCast.Builder builder = MonsterCast.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MonsterCast.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCastRate() != 0) {
            builder.setCastRate(this.getCastRate());
            fieldCnt++;
        }  else if (builder.hasCastRate()) {
            // 清理CastRate
            builder.clearCastRate();
            fieldCnt++;
        }
        if (this.getCastValue() != 0) {
            builder.setCastValue(this.getCastValue());
            fieldCnt++;
        }  else if (builder.hasCastValue()) {
            // 清理CastValue
            builder.clearCastValue();
            fieldCnt++;
        }
        if (this.getFinish()) {
            builder.setFinish(this.getFinish());
            fieldCnt++;
        }  else if (builder.hasFinish()) {
            // 清理Finish
            builder.clearFinish();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MonsterCast.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CASTRATE)) {
            builder.setCastRate(this.getCastRate());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CASTVALUE)) {
            builder.setCastValue(this.getCastValue());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FINISH)) {
            builder.setFinish(this.getFinish());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MonsterCast proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCastRate()) {
            this.innerSetCastRate(proto.getCastRate());
        } else {
            this.innerSetCastRate(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCastValue()) {
            this.innerSetCastValue(proto.getCastValue());
        } else {
            this.innerSetCastValue(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFinish()) {
            this.innerSetFinish(proto.getFinish());
        } else {
            this.innerSetFinish(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return MonsterCastProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MonsterCast proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCastRate()) {
            this.setCastRate(proto.getCastRate());
            fieldCnt++;
        }
        if (proto.hasCastValue()) {
            this.setCastValue(proto.getCastValue());
            fieldCnt++;
        }
        if (proto.hasFinish()) {
            this.setFinish(proto.getFinish());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MonsterCast.Builder builder = MonsterCast.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MonsterCastProp)) {
            return false;
        }
        final MonsterCastProp otherNode = (MonsterCastProp) node;
        if (this.castRate != otherNode.castRate) {
            return false;
        }
        if (this.castValue != otherNode.castValue) {
            return false;
        }
        if (this.finish != otherNode.finish) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
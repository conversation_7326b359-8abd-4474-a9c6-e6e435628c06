package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructMail.MailReadIndex;
import com.yorha.proto.StructMail;
import com.yorha.proto.StructMailPB.MailReadIndexPB;
import com.yorha.proto.StructMailPB;


/**
 * <AUTHOR> auto gen
 */
public class MailReadIndexProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ZONEMAILINDEX = 0;
    public static final int FIELD_INDEX_CLANMAILINDEX = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int zoneMailIndex = Constant.DEFAULT_INT_VALUE;
    private ClanMailIndexProp clanMailIndex = null;

    public MailReadIndexProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MailReadIndexProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get zoneMailIndex
     *
     * @return zoneMailIndex value
     */
    public int getZoneMailIndex() {
        return this.zoneMailIndex;
    }

    /**
     * set zoneMailIndex && set marked
     *
     * @param zoneMailIndex new value
     * @return current object
     */
    public MailReadIndexProp setZoneMailIndex(int zoneMailIndex) {
        if (this.zoneMailIndex != zoneMailIndex) {
            this.mark(FIELD_INDEX_ZONEMAILINDEX);
            this.zoneMailIndex = zoneMailIndex;
        }
        return this;
    }

    /**
     * inner set zoneMailIndex
     *
     * @param zoneMailIndex new value
     */
    private void innerSetZoneMailIndex(int zoneMailIndex) {
        this.zoneMailIndex = zoneMailIndex;
    }

    /**
     * get clanMailIndex
     *
     * @return clanMailIndex value
     */
    public ClanMailIndexProp getClanMailIndex() {
        if (this.clanMailIndex == null) {
            this.clanMailIndex = new ClanMailIndexProp(this, FIELD_INDEX_CLANMAILINDEX);
        }
        return this.clanMailIndex;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailReadIndexPB.Builder getCopyCsBuilder() {
        final MailReadIndexPB.Builder builder = MailReadIndexPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MailReadIndexPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getZoneMailIndex() != 0) {
            builder.setZoneMailIndex(this.getZoneMailIndex());
            fieldCnt++;
        }  else if (builder.hasZoneMailIndex()) {
            // 清理ZoneMailIndex
            builder.clearZoneMailIndex();
            fieldCnt++;
        }
        if (this.clanMailIndex != null) {
            StructMailPB.ClanMailIndexPB.Builder tmpBuilder = StructMailPB.ClanMailIndexPB.newBuilder();
            final int tmpFieldCnt = this.clanMailIndex.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanMailIndex(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanMailIndex();
            }
        }  else if (builder.hasClanMailIndex()) {
            // 清理ClanMailIndex
            builder.clearClanMailIndex();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MailReadIndexPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ZONEMAILINDEX)) {
            builder.setZoneMailIndex(this.getZoneMailIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANMAILINDEX) && this.clanMailIndex != null) {
            final boolean needClear = !builder.hasClanMailIndex();
            final int tmpFieldCnt = this.clanMailIndex.copyChangeToCs(builder.getClanMailIndexBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanMailIndex();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MailReadIndexPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ZONEMAILINDEX)) {
            builder.setZoneMailIndex(this.getZoneMailIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANMAILINDEX) && this.clanMailIndex != null) {
            final boolean needClear = !builder.hasClanMailIndex();
            final int tmpFieldCnt = this.clanMailIndex.copyChangeToAndClearDeleteKeysCs(builder.getClanMailIndexBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanMailIndex();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MailReadIndexPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasZoneMailIndex()) {
            this.innerSetZoneMailIndex(proto.getZoneMailIndex());
        } else {
            this.innerSetZoneMailIndex(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasClanMailIndex()) {
            this.getClanMailIndex().mergeFromCs(proto.getClanMailIndex());
        } else {
            if (this.clanMailIndex != null) {
                this.clanMailIndex.mergeFromCs(proto.getClanMailIndex());
            }
        }
        this.markAll();
        return MailReadIndexProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MailReadIndexPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasZoneMailIndex()) {
            this.setZoneMailIndex(proto.getZoneMailIndex());
            fieldCnt++;
        }
        if (proto.hasClanMailIndex()) {
            this.getClanMailIndex().mergeChangeFromCs(proto.getClanMailIndex());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailReadIndex.Builder getCopyDbBuilder() {
        final MailReadIndex.Builder builder = MailReadIndex.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MailReadIndex.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getZoneMailIndex() != 0) {
            builder.setZoneMailIndex(this.getZoneMailIndex());
            fieldCnt++;
        }  else if (builder.hasZoneMailIndex()) {
            // 清理ZoneMailIndex
            builder.clearZoneMailIndex();
            fieldCnt++;
        }
        if (this.clanMailIndex != null) {
            StructMail.ClanMailIndex.Builder tmpBuilder = StructMail.ClanMailIndex.newBuilder();
            final int tmpFieldCnt = this.clanMailIndex.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanMailIndex(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanMailIndex();
            }
        }  else if (builder.hasClanMailIndex()) {
            // 清理ClanMailIndex
            builder.clearClanMailIndex();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MailReadIndex.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ZONEMAILINDEX)) {
            builder.setZoneMailIndex(this.getZoneMailIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANMAILINDEX) && this.clanMailIndex != null) {
            final boolean needClear = !builder.hasClanMailIndex();
            final int tmpFieldCnt = this.clanMailIndex.copyChangeToDb(builder.getClanMailIndexBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanMailIndex();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MailReadIndex proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasZoneMailIndex()) {
            this.innerSetZoneMailIndex(proto.getZoneMailIndex());
        } else {
            this.innerSetZoneMailIndex(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasClanMailIndex()) {
            this.getClanMailIndex().mergeFromDb(proto.getClanMailIndex());
        } else {
            if (this.clanMailIndex != null) {
                this.clanMailIndex.mergeFromDb(proto.getClanMailIndex());
            }
        }
        this.markAll();
        return MailReadIndexProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MailReadIndex proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasZoneMailIndex()) {
            this.setZoneMailIndex(proto.getZoneMailIndex());
            fieldCnt++;
        }
        if (proto.hasClanMailIndex()) {
            this.getClanMailIndex().mergeChangeFromDb(proto.getClanMailIndex());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailReadIndex.Builder getCopySsBuilder() {
        final MailReadIndex.Builder builder = MailReadIndex.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MailReadIndex.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getZoneMailIndex() != 0) {
            builder.setZoneMailIndex(this.getZoneMailIndex());
            fieldCnt++;
        }  else if (builder.hasZoneMailIndex()) {
            // 清理ZoneMailIndex
            builder.clearZoneMailIndex();
            fieldCnt++;
        }
        if (this.clanMailIndex != null) {
            StructMail.ClanMailIndex.Builder tmpBuilder = StructMail.ClanMailIndex.newBuilder();
            final int tmpFieldCnt = this.clanMailIndex.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanMailIndex(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanMailIndex();
            }
        }  else if (builder.hasClanMailIndex()) {
            // 清理ClanMailIndex
            builder.clearClanMailIndex();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MailReadIndex.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ZONEMAILINDEX)) {
            builder.setZoneMailIndex(this.getZoneMailIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANMAILINDEX) && this.clanMailIndex != null) {
            final boolean needClear = !builder.hasClanMailIndex();
            final int tmpFieldCnt = this.clanMailIndex.copyChangeToSs(builder.getClanMailIndexBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanMailIndex();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MailReadIndex proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasZoneMailIndex()) {
            this.innerSetZoneMailIndex(proto.getZoneMailIndex());
        } else {
            this.innerSetZoneMailIndex(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasClanMailIndex()) {
            this.getClanMailIndex().mergeFromSs(proto.getClanMailIndex());
        } else {
            if (this.clanMailIndex != null) {
                this.clanMailIndex.mergeFromSs(proto.getClanMailIndex());
            }
        }
        this.markAll();
        return MailReadIndexProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MailReadIndex proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasZoneMailIndex()) {
            this.setZoneMailIndex(proto.getZoneMailIndex());
            fieldCnt++;
        }
        if (proto.hasClanMailIndex()) {
            this.getClanMailIndex().mergeChangeFromSs(proto.getClanMailIndex());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MailReadIndex.Builder builder = MailReadIndex.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CLANMAILINDEX) && this.clanMailIndex != null) {
            this.clanMailIndex.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.clanMailIndex != null) {
            this.clanMailIndex.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MailReadIndexProp)) {
            return false;
        }
        final MailReadIndexProp otherNode = (MailReadIndexProp) node;
        if (this.zoneMailIndex != otherNode.zoneMailIndex) {
            return false;
        }
        if (!this.getClanMailIndex().compareDataTo(otherNode.getClanMailIndex())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
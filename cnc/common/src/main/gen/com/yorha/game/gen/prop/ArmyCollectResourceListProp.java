package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.ArmyPB.ArmyCollectResourceListPB;
import com.yorha.proto.Army.ArmyCollectResourceList;
import com.yorha.proto.ArmyPB.ArmyCollectResourcePB;
import com.yorha.proto.Army.ArmyCollectResource;

/**
 * <AUTHOR> auto gen
 */
public class ArmyCollectResourceListProp extends AbstractListNode<ArmyCollectResourceProp> {
    /**
     * Create a ArmyCollectResourceListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public ArmyCollectResourceListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to ArmyCollectResourceListProp
     *
     * @return new object
     */
    @Override
    public ArmyCollectResourceProp addEmptyValue() {
        final ArmyCollectResourceProp newProp = new ArmyCollectResourceProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArmyCollectResourceListPB.Builder getCopyCsBuilder() {
        final ArmyCollectResourceListPB.Builder builder = ArmyCollectResourceListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(ArmyCollectResourceListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return ArmyCollectResourceListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final ArmyCollectResourceProp v : this) {
            final ArmyCollectResourcePB.Builder itemBuilder = ArmyCollectResourcePB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return ArmyCollectResourceListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(ArmyCollectResourceListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return ArmyCollectResourceListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(ArmyCollectResourceListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (ArmyCollectResourcePB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return ArmyCollectResourceListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(ArmyCollectResourceListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArmyCollectResourceList.Builder getCopyDbBuilder() {
        final ArmyCollectResourceList.Builder builder = ArmyCollectResourceList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(ArmyCollectResourceList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return ArmyCollectResourceListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final ArmyCollectResourceProp v : this) {
            final ArmyCollectResource.Builder itemBuilder = ArmyCollectResource.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return ArmyCollectResourceListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(ArmyCollectResourceList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return ArmyCollectResourceListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(ArmyCollectResourceList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (ArmyCollectResource v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return ArmyCollectResourceListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(ArmyCollectResourceList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArmyCollectResourceList.Builder getCopySsBuilder() {
        final ArmyCollectResourceList.Builder builder = ArmyCollectResourceList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(ArmyCollectResourceList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return ArmyCollectResourceListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final ArmyCollectResourceProp v : this) {
            final ArmyCollectResource.Builder itemBuilder = ArmyCollectResource.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return ArmyCollectResourceListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(ArmyCollectResourceList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return ArmyCollectResourceListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(ArmyCollectResourceList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (ArmyCollectResource v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return ArmyCollectResourceListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(ArmyCollectResourceList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        ArmyCollectResourceList.Builder builder = ArmyCollectResourceList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
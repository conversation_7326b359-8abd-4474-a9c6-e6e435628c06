package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.YoTestPB.YoTestListListPB;
import com.yorha.proto.YoTest.YoTestListList;
import com.yorha.proto.YoTestPB.YoTestListPB;
import com.yorha.proto.YoTest.YoTestList;

/**
 * <AUTHOR> auto gen
 */
public class YoTestListListProp extends AbstractListNode<YoTestListProp> {
    /**
     * Create a YoTestListListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public YoTestListListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to YoTestListListProp
     *
     * @return new object
     */
    @Override
    public YoTestListProp addEmptyValue() {
        final YoTestListProp newProp = new YoTestListProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestListListPB.Builder getCopyCsBuilder() {
        final YoTestListListPB.Builder builder = YoTestListListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(YoTestListListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return YoTestListListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final YoTestListProp v : this) {
            final YoTestListPB.Builder itemBuilder = YoTestListPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return YoTestListListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(YoTestListListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return YoTestListListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(YoTestListListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (YoTestListPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return YoTestListListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(YoTestListListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestListList.Builder getCopyDbBuilder() {
        final YoTestListList.Builder builder = YoTestListList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(YoTestListList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return YoTestListListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final YoTestListProp v : this) {
            final YoTestList.Builder itemBuilder = YoTestList.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return YoTestListListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(YoTestListList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return YoTestListListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(YoTestListList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (YoTestList v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return YoTestListListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(YoTestListList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestListList.Builder getCopySsBuilder() {
        final YoTestListList.Builder builder = YoTestListList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(YoTestListList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return YoTestListListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final YoTestListProp v : this) {
            final YoTestList.Builder itemBuilder = YoTestList.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return YoTestListListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(YoTestListList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return YoTestListListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(YoTestListList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (YoTestList v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return YoTestListListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(YoTestListList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        YoTestListList.Builder builder = YoTestListList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
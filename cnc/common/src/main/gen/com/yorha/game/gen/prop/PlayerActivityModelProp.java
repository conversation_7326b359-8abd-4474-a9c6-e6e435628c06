package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerActivityModel;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.PlayerActivityModelPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerActivityModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ACTSCHEDULES = 0;
    public static final int FIELD_INDEX_EXPIREDACTSCHEDULES = 1;
    public static final int FIELD_INDEX_FORCEOFFSCHEDULEDATA = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private Int32ActivityScheduleMapProp actSchedules = null;
    private Int32ActivityScheduleMapProp expiredActSchedules = null;
    private Int32ActivityScheduleMapProp forceOffScheduleData = null;

    public PlayerActivityModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerActivityModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get actSchedules
     *
     * @return actSchedules value
     */
    public Int32ActivityScheduleMapProp getActSchedules() {
        if (this.actSchedules == null) {
            this.actSchedules = new Int32ActivityScheduleMapProp(this, FIELD_INDEX_ACTSCHEDULES);
        }
        return this.actSchedules;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putActSchedulesV(ActivityScheduleProp v) {
        this.getActSchedules().put(v.getScheduleId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ActivityScheduleProp addEmptyActSchedules(Integer k) {
        return this.getActSchedules().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getActSchedulesSize() {
        if (this.actSchedules == null) {
            return 0;
        }
        return this.actSchedules.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isActSchedulesEmpty() {
        if (this.actSchedules == null) {
            return true;
        }
        return this.actSchedules.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ActivityScheduleProp getActSchedulesV(Integer k) {
        if (this.actSchedules == null || !this.actSchedules.containsKey(k)) {
            return null;
        }
        return this.actSchedules.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearActSchedules() {
        if (this.actSchedules != null) {
            this.actSchedules.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeActSchedulesV(Integer k) {
        if (this.actSchedules != null) {
            this.actSchedules.remove(k);
        }
    }
    /**
     * get expiredActSchedules
     *
     * @return expiredActSchedules value
     */
    public Int32ActivityScheduleMapProp getExpiredActSchedules() {
        if (this.expiredActSchedules == null) {
            this.expiredActSchedules = new Int32ActivityScheduleMapProp(this, FIELD_INDEX_EXPIREDACTSCHEDULES);
        }
        return this.expiredActSchedules;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putExpiredActSchedulesV(ActivityScheduleProp v) {
        this.getExpiredActSchedules().put(v.getScheduleId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ActivityScheduleProp addEmptyExpiredActSchedules(Integer k) {
        return this.getExpiredActSchedules().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getExpiredActSchedulesSize() {
        if (this.expiredActSchedules == null) {
            return 0;
        }
        return this.expiredActSchedules.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isExpiredActSchedulesEmpty() {
        if (this.expiredActSchedules == null) {
            return true;
        }
        return this.expiredActSchedules.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ActivityScheduleProp getExpiredActSchedulesV(Integer k) {
        if (this.expiredActSchedules == null || !this.expiredActSchedules.containsKey(k)) {
            return null;
        }
        return this.expiredActSchedules.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearExpiredActSchedules() {
        if (this.expiredActSchedules != null) {
            this.expiredActSchedules.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeExpiredActSchedulesV(Integer k) {
        if (this.expiredActSchedules != null) {
            this.expiredActSchedules.remove(k);
        }
    }
    /**
     * get forceOffScheduleData
     *
     * @return forceOffScheduleData value
     */
    public Int32ActivityScheduleMapProp getForceOffScheduleData() {
        if (this.forceOffScheduleData == null) {
            this.forceOffScheduleData = new Int32ActivityScheduleMapProp(this, FIELD_INDEX_FORCEOFFSCHEDULEDATA);
        }
        return this.forceOffScheduleData;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putForceOffScheduleDataV(ActivityScheduleProp v) {
        this.getForceOffScheduleData().put(v.getScheduleId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ActivityScheduleProp addEmptyForceOffScheduleData(Integer k) {
        return this.getForceOffScheduleData().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getForceOffScheduleDataSize() {
        if (this.forceOffScheduleData == null) {
            return 0;
        }
        return this.forceOffScheduleData.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isForceOffScheduleDataEmpty() {
        if (this.forceOffScheduleData == null) {
            return true;
        }
        return this.forceOffScheduleData.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ActivityScheduleProp getForceOffScheduleDataV(Integer k) {
        if (this.forceOffScheduleData == null || !this.forceOffScheduleData.containsKey(k)) {
            return null;
        }
        return this.forceOffScheduleData.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearForceOffScheduleData() {
        if (this.forceOffScheduleData != null) {
            this.forceOffScheduleData.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeForceOffScheduleDataV(Integer k) {
        if (this.forceOffScheduleData != null) {
            this.forceOffScheduleData.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerActivityModelPB.Builder getCopyCsBuilder() {
        final PlayerActivityModelPB.Builder builder = PlayerActivityModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerActivityModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.actSchedules != null) {
            StructPB.Int32ActivityScheduleMapPB.Builder tmpBuilder = StructPB.Int32ActivityScheduleMapPB.newBuilder();
            final int tmpFieldCnt = this.actSchedules.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActSchedules(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActSchedules();
            }
        }  else if (builder.hasActSchedules()) {
            // 清理ActSchedules
            builder.clearActSchedules();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerActivityModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTSCHEDULES) && this.actSchedules != null) {
            final boolean needClear = !builder.hasActSchedules();
            final int tmpFieldCnt = this.actSchedules.copyChangeToCs(builder.getActSchedulesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActSchedules();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerActivityModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTSCHEDULES) && this.actSchedules != null) {
            final boolean needClear = !builder.hasActSchedules();
            final int tmpFieldCnt = this.actSchedules.copyChangeToAndClearDeleteKeysCs(builder.getActSchedulesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActSchedules();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerActivityModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActSchedules()) {
            this.getActSchedules().mergeFromCs(proto.getActSchedules());
        } else {
            if (this.actSchedules != null) {
                this.actSchedules.mergeFromCs(proto.getActSchedules());
            }
        }
        this.markAll();
        return PlayerActivityModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerActivityModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActSchedules()) {
            this.getActSchedules().mergeChangeFromCs(proto.getActSchedules());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerActivityModel.Builder getCopyDbBuilder() {
        final PlayerActivityModel.Builder builder = PlayerActivityModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerActivityModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.actSchedules != null) {
            Struct.Int32ActivityScheduleMap.Builder tmpBuilder = Struct.Int32ActivityScheduleMap.newBuilder();
            final int tmpFieldCnt = this.actSchedules.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActSchedules(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActSchedules();
            }
        }  else if (builder.hasActSchedules()) {
            // 清理ActSchedules
            builder.clearActSchedules();
            fieldCnt++;
        }
        if (this.expiredActSchedules != null) {
            Struct.Int32ActivityScheduleMap.Builder tmpBuilder = Struct.Int32ActivityScheduleMap.newBuilder();
            final int tmpFieldCnt = this.expiredActSchedules.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExpiredActSchedules(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExpiredActSchedules();
            }
        }  else if (builder.hasExpiredActSchedules()) {
            // 清理ExpiredActSchedules
            builder.clearExpiredActSchedules();
            fieldCnt++;
        }
        if (this.forceOffScheduleData != null) {
            Struct.Int32ActivityScheduleMap.Builder tmpBuilder = Struct.Int32ActivityScheduleMap.newBuilder();
            final int tmpFieldCnt = this.forceOffScheduleData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setForceOffScheduleData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearForceOffScheduleData();
            }
        }  else if (builder.hasForceOffScheduleData()) {
            // 清理ForceOffScheduleData
            builder.clearForceOffScheduleData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerActivityModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTSCHEDULES) && this.actSchedules != null) {
            final boolean needClear = !builder.hasActSchedules();
            final int tmpFieldCnt = this.actSchedules.copyChangeToDb(builder.getActSchedulesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActSchedules();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXPIREDACTSCHEDULES) && this.expiredActSchedules != null) {
            final boolean needClear = !builder.hasExpiredActSchedules();
            final int tmpFieldCnt = this.expiredActSchedules.copyChangeToDb(builder.getExpiredActSchedulesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpiredActSchedules();
            }
        }
        if (this.hasMark(FIELD_INDEX_FORCEOFFSCHEDULEDATA) && this.forceOffScheduleData != null) {
            final boolean needClear = !builder.hasForceOffScheduleData();
            final int tmpFieldCnt = this.forceOffScheduleData.copyChangeToDb(builder.getForceOffScheduleDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearForceOffScheduleData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerActivityModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActSchedules()) {
            this.getActSchedules().mergeFromDb(proto.getActSchedules());
        } else {
            if (this.actSchedules != null) {
                this.actSchedules.mergeFromDb(proto.getActSchedules());
            }
        }
        if (proto.hasExpiredActSchedules()) {
            this.getExpiredActSchedules().mergeFromDb(proto.getExpiredActSchedules());
        } else {
            if (this.expiredActSchedules != null) {
                this.expiredActSchedules.mergeFromDb(proto.getExpiredActSchedules());
            }
        }
        if (proto.hasForceOffScheduleData()) {
            this.getForceOffScheduleData().mergeFromDb(proto.getForceOffScheduleData());
        } else {
            if (this.forceOffScheduleData != null) {
                this.forceOffScheduleData.mergeFromDb(proto.getForceOffScheduleData());
            }
        }
        this.markAll();
        return PlayerActivityModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerActivityModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActSchedules()) {
            this.getActSchedules().mergeChangeFromDb(proto.getActSchedules());
            fieldCnt++;
        }
        if (proto.hasExpiredActSchedules()) {
            this.getExpiredActSchedules().mergeChangeFromDb(proto.getExpiredActSchedules());
            fieldCnt++;
        }
        if (proto.hasForceOffScheduleData()) {
            this.getForceOffScheduleData().mergeChangeFromDb(proto.getForceOffScheduleData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerActivityModel.Builder getCopySsBuilder() {
        final PlayerActivityModel.Builder builder = PlayerActivityModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerActivityModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.actSchedules != null) {
            Struct.Int32ActivityScheduleMap.Builder tmpBuilder = Struct.Int32ActivityScheduleMap.newBuilder();
            final int tmpFieldCnt = this.actSchedules.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActSchedules(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActSchedules();
            }
        }  else if (builder.hasActSchedules()) {
            // 清理ActSchedules
            builder.clearActSchedules();
            fieldCnt++;
        }
        if (this.expiredActSchedules != null) {
            Struct.Int32ActivityScheduleMap.Builder tmpBuilder = Struct.Int32ActivityScheduleMap.newBuilder();
            final int tmpFieldCnt = this.expiredActSchedules.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExpiredActSchedules(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExpiredActSchedules();
            }
        }  else if (builder.hasExpiredActSchedules()) {
            // 清理ExpiredActSchedules
            builder.clearExpiredActSchedules();
            fieldCnt++;
        }
        if (this.forceOffScheduleData != null) {
            Struct.Int32ActivityScheduleMap.Builder tmpBuilder = Struct.Int32ActivityScheduleMap.newBuilder();
            final int tmpFieldCnt = this.forceOffScheduleData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setForceOffScheduleData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearForceOffScheduleData();
            }
        }  else if (builder.hasForceOffScheduleData()) {
            // 清理ForceOffScheduleData
            builder.clearForceOffScheduleData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerActivityModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTSCHEDULES) && this.actSchedules != null) {
            final boolean needClear = !builder.hasActSchedules();
            final int tmpFieldCnt = this.actSchedules.copyChangeToSs(builder.getActSchedulesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActSchedules();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXPIREDACTSCHEDULES) && this.expiredActSchedules != null) {
            final boolean needClear = !builder.hasExpiredActSchedules();
            final int tmpFieldCnt = this.expiredActSchedules.copyChangeToSs(builder.getExpiredActSchedulesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpiredActSchedules();
            }
        }
        if (this.hasMark(FIELD_INDEX_FORCEOFFSCHEDULEDATA) && this.forceOffScheduleData != null) {
            final boolean needClear = !builder.hasForceOffScheduleData();
            final int tmpFieldCnt = this.forceOffScheduleData.copyChangeToSs(builder.getForceOffScheduleDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearForceOffScheduleData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerActivityModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActSchedules()) {
            this.getActSchedules().mergeFromSs(proto.getActSchedules());
        } else {
            if (this.actSchedules != null) {
                this.actSchedules.mergeFromSs(proto.getActSchedules());
            }
        }
        if (proto.hasExpiredActSchedules()) {
            this.getExpiredActSchedules().mergeFromSs(proto.getExpiredActSchedules());
        } else {
            if (this.expiredActSchedules != null) {
                this.expiredActSchedules.mergeFromSs(proto.getExpiredActSchedules());
            }
        }
        if (proto.hasForceOffScheduleData()) {
            this.getForceOffScheduleData().mergeFromSs(proto.getForceOffScheduleData());
        } else {
            if (this.forceOffScheduleData != null) {
                this.forceOffScheduleData.mergeFromSs(proto.getForceOffScheduleData());
            }
        }
        this.markAll();
        return PlayerActivityModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerActivityModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActSchedules()) {
            this.getActSchedules().mergeChangeFromSs(proto.getActSchedules());
            fieldCnt++;
        }
        if (proto.hasExpiredActSchedules()) {
            this.getExpiredActSchedules().mergeChangeFromSs(proto.getExpiredActSchedules());
            fieldCnt++;
        }
        if (proto.hasForceOffScheduleData()) {
            this.getForceOffScheduleData().mergeChangeFromSs(proto.getForceOffScheduleData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerActivityModel.Builder builder = PlayerActivityModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ACTSCHEDULES) && this.actSchedules != null) {
            this.actSchedules.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_EXPIREDACTSCHEDULES) && this.expiredActSchedules != null) {
            this.expiredActSchedules.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_FORCEOFFSCHEDULEDATA) && this.forceOffScheduleData != null) {
            this.forceOffScheduleData.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.actSchedules != null) {
            this.actSchedules.markAll();
        }
        if (this.expiredActSchedules != null) {
            this.expiredActSchedules.markAll();
        }
        if (this.forceOffScheduleData != null) {
            this.forceOffScheduleData.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerActivityModelProp)) {
            return false;
        }
        final PlayerActivityModelProp otherNode = (PlayerActivityModelProp) node;
        if (!this.getActSchedules().compareDataTo(otherNode.getActSchedules())) {
            return false;
        }
        if (!this.getExpiredActSchedules().compareDataTo(otherNode.getExpiredActSchedules())) {
            return false;
        }
        if (!this.getForceOffScheduleData().compareDataTo(otherNode.getForceOffScheduleData())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
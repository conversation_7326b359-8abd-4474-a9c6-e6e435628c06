package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructCommon.ProgressInfo;
import com.yorha.proto.StructCommonPB.ProgressInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class ProgressInfoProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_UID = 0;
    public static final int FIELD_INDEX_LASTCALNUM = 1;
    public static final int FIELD_INDEX_MAXNUM = 2;
    public static final int FIELD_INDEX_LASTCALTSMS = 3;
    public static final int FIELD_INDEX_SPEED = 4;
    public static final int FIELD_INDEX_STATEENDTSMS = 5;
    public static final int FIELD_INDEX_STATESTARTTSMS = 6;
    public static final int FIELD_INDEX_ENTERADDITIONVALUE = 7;

    public static final int FIELD_COUNT = 8;

    private long markBits0 = 0L;

    private long uid = Constant.DEFAULT_LONG_VALUE;
    private long lastCalNum = Constant.DEFAULT_LONG_VALUE;
    private long maxNum = Constant.DEFAULT_LONG_VALUE;
    private long lastCalTsMs = Constant.DEFAULT_LONG_VALUE;
    private long speed = Constant.DEFAULT_LONG_VALUE;
    private long stateEndTsMs = Constant.DEFAULT_LONG_VALUE;
    private long stateStartTsMs = Constant.DEFAULT_LONG_VALUE;
    private long enterAdditionValue = Constant.DEFAULT_LONG_VALUE;

    public ProgressInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ProgressInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get uid
     *
     * @return uid value
     */
    public long getUid() {
        return this.uid;
    }

    /**
     * set uid && set marked
     *
     * @param uid new value
     * @return current object
     */
    public ProgressInfoProp setUid(long uid) {
        if (this.uid != uid) {
            this.mark(FIELD_INDEX_UID);
            this.uid = uid;
        }
        return this;
    }

    /**
     * inner set uid
     *
     * @param uid new value
     */
    private void innerSetUid(long uid) {
        this.uid = uid;
    }

    /**
     * get lastCalNum
     *
     * @return lastCalNum value
     */
    public long getLastCalNum() {
        return this.lastCalNum;
    }

    /**
     * set lastCalNum && set marked
     *
     * @param lastCalNum new value
     * @return current object
     */
    public ProgressInfoProp setLastCalNum(long lastCalNum) {
        if (this.lastCalNum != lastCalNum) {
            this.mark(FIELD_INDEX_LASTCALNUM);
            this.lastCalNum = lastCalNum;
        }
        return this;
    }

    /**
     * inner set lastCalNum
     *
     * @param lastCalNum new value
     */
    private void innerSetLastCalNum(long lastCalNum) {
        this.lastCalNum = lastCalNum;
    }

    /**
     * get maxNum
     *
     * @return maxNum value
     */
    public long getMaxNum() {
        return this.maxNum;
    }

    /**
     * set maxNum && set marked
     *
     * @param maxNum new value
     * @return current object
     */
    public ProgressInfoProp setMaxNum(long maxNum) {
        if (this.maxNum != maxNum) {
            this.mark(FIELD_INDEX_MAXNUM);
            this.maxNum = maxNum;
        }
        return this;
    }

    /**
     * inner set maxNum
     *
     * @param maxNum new value
     */
    private void innerSetMaxNum(long maxNum) {
        this.maxNum = maxNum;
    }

    /**
     * get lastCalTsMs
     *
     * @return lastCalTsMs value
     */
    public long getLastCalTsMs() {
        return this.lastCalTsMs;
    }

    /**
     * set lastCalTsMs && set marked
     *
     * @param lastCalTsMs new value
     * @return current object
     */
    public ProgressInfoProp setLastCalTsMs(long lastCalTsMs) {
        if (this.lastCalTsMs != lastCalTsMs) {
            this.mark(FIELD_INDEX_LASTCALTSMS);
            this.lastCalTsMs = lastCalTsMs;
        }
        return this;
    }

    /**
     * inner set lastCalTsMs
     *
     * @param lastCalTsMs new value
     */
    private void innerSetLastCalTsMs(long lastCalTsMs) {
        this.lastCalTsMs = lastCalTsMs;
    }

    /**
     * get speed
     *
     * @return speed value
     */
    public long getSpeed() {
        return this.speed;
    }

    /**
     * set speed && set marked
     *
     * @param speed new value
     * @return current object
     */
    public ProgressInfoProp setSpeed(long speed) {
        if (this.speed != speed) {
            this.mark(FIELD_INDEX_SPEED);
            this.speed = speed;
        }
        return this;
    }

    /**
     * inner set speed
     *
     * @param speed new value
     */
    private void innerSetSpeed(long speed) {
        this.speed = speed;
    }

    /**
     * get stateEndTsMs
     *
     * @return stateEndTsMs value
     */
    public long getStateEndTsMs() {
        return this.stateEndTsMs;
    }

    /**
     * set stateEndTsMs && set marked
     *
     * @param stateEndTsMs new value
     * @return current object
     */
    public ProgressInfoProp setStateEndTsMs(long stateEndTsMs) {
        if (this.stateEndTsMs != stateEndTsMs) {
            this.mark(FIELD_INDEX_STATEENDTSMS);
            this.stateEndTsMs = stateEndTsMs;
        }
        return this;
    }

    /**
     * inner set stateEndTsMs
     *
     * @param stateEndTsMs new value
     */
    private void innerSetStateEndTsMs(long stateEndTsMs) {
        this.stateEndTsMs = stateEndTsMs;
    }

    /**
     * get stateStartTsMs
     *
     * @return stateStartTsMs value
     */
    public long getStateStartTsMs() {
        return this.stateStartTsMs;
    }

    /**
     * set stateStartTsMs && set marked
     *
     * @param stateStartTsMs new value
     * @return current object
     */
    public ProgressInfoProp setStateStartTsMs(long stateStartTsMs) {
        if (this.stateStartTsMs != stateStartTsMs) {
            this.mark(FIELD_INDEX_STATESTARTTSMS);
            this.stateStartTsMs = stateStartTsMs;
        }
        return this;
    }

    /**
     * inner set stateStartTsMs
     *
     * @param stateStartTsMs new value
     */
    private void innerSetStateStartTsMs(long stateStartTsMs) {
        this.stateStartTsMs = stateStartTsMs;
    }

    /**
     * get enterAdditionValue
     *
     * @return enterAdditionValue value
     */
    public long getEnterAdditionValue() {
        return this.enterAdditionValue;
    }

    /**
     * set enterAdditionValue && set marked
     *
     * @param enterAdditionValue new value
     * @return current object
     */
    public ProgressInfoProp setEnterAdditionValue(long enterAdditionValue) {
        if (this.enterAdditionValue != enterAdditionValue) {
            this.mark(FIELD_INDEX_ENTERADDITIONVALUE);
            this.enterAdditionValue = enterAdditionValue;
        }
        return this;
    }

    /**
     * inner set enterAdditionValue
     *
     * @param enterAdditionValue new value
     */
    private void innerSetEnterAdditionValue(long enterAdditionValue) {
        this.enterAdditionValue = enterAdditionValue;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ProgressInfoPB.Builder getCopyCsBuilder() {
        final ProgressInfoPB.Builder builder = ProgressInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ProgressInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUid() != 0L) {
            builder.setUid(this.getUid());
            fieldCnt++;
        }  else if (builder.hasUid()) {
            // 清理Uid
            builder.clearUid();
            fieldCnt++;
        }
        if (this.getLastCalNum() != 0L) {
            builder.setLastCalNum(this.getLastCalNum());
            fieldCnt++;
        }  else if (builder.hasLastCalNum()) {
            // 清理LastCalNum
            builder.clearLastCalNum();
            fieldCnt++;
        }
        if (this.getMaxNum() != 0L) {
            builder.setMaxNum(this.getMaxNum());
            fieldCnt++;
        }  else if (builder.hasMaxNum()) {
            // 清理MaxNum
            builder.clearMaxNum();
            fieldCnt++;
        }
        if (this.getLastCalTsMs() != 0L) {
            builder.setLastCalTsMs(this.getLastCalTsMs());
            fieldCnt++;
        }  else if (builder.hasLastCalTsMs()) {
            // 清理LastCalTsMs
            builder.clearLastCalTsMs();
            fieldCnt++;
        }
        if (this.getSpeed() != 0L) {
            builder.setSpeed(this.getSpeed());
            fieldCnt++;
        }  else if (builder.hasSpeed()) {
            // 清理Speed
            builder.clearSpeed();
            fieldCnt++;
        }
        if (this.getStateEndTsMs() != 0L) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }  else if (builder.hasStateEndTsMs()) {
            // 清理StateEndTsMs
            builder.clearStateEndTsMs();
            fieldCnt++;
        }
        if (this.getStateStartTsMs() != 0L) {
            builder.setStateStartTsMs(this.getStateStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStateStartTsMs()) {
            // 清理StateStartTsMs
            builder.clearStateStartTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ProgressInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UID)) {
            builder.setUid(this.getUid());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTCALNUM)) {
            builder.setLastCalNum(this.getLastCalNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXNUM)) {
            builder.setMaxNum(this.getMaxNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTCALTSMS)) {
            builder.setLastCalTsMs(this.getLastCalTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SPEED)) {
            builder.setSpeed(this.getSpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATEENDTSMS)) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATESTARTTSMS)) {
            builder.setStateStartTsMs(this.getStateStartTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ProgressInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UID)) {
            builder.setUid(this.getUid());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTCALNUM)) {
            builder.setLastCalNum(this.getLastCalNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXNUM)) {
            builder.setMaxNum(this.getMaxNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTCALTSMS)) {
            builder.setLastCalTsMs(this.getLastCalTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SPEED)) {
            builder.setSpeed(this.getSpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATEENDTSMS)) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATESTARTTSMS)) {
            builder.setStateStartTsMs(this.getStateStartTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ProgressInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUid()) {
            this.innerSetUid(proto.getUid());
        } else {
            this.innerSetUid(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastCalNum()) {
            this.innerSetLastCalNum(proto.getLastCalNum());
        } else {
            this.innerSetLastCalNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMaxNum()) {
            this.innerSetMaxNum(proto.getMaxNum());
        } else {
            this.innerSetMaxNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastCalTsMs()) {
            this.innerSetLastCalTsMs(proto.getLastCalTsMs());
        } else {
            this.innerSetLastCalTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSpeed()) {
            this.innerSetSpeed(proto.getSpeed());
        } else {
            this.innerSetSpeed(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStateEndTsMs()) {
            this.innerSetStateEndTsMs(proto.getStateEndTsMs());
        } else {
            this.innerSetStateEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStateStartTsMs()) {
            this.innerSetStateStartTsMs(proto.getStateStartTsMs());
        } else {
            this.innerSetStateStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ProgressInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ProgressInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUid()) {
            this.setUid(proto.getUid());
            fieldCnt++;
        }
        if (proto.hasLastCalNum()) {
            this.setLastCalNum(proto.getLastCalNum());
            fieldCnt++;
        }
        if (proto.hasMaxNum()) {
            this.setMaxNum(proto.getMaxNum());
            fieldCnt++;
        }
        if (proto.hasLastCalTsMs()) {
            this.setLastCalTsMs(proto.getLastCalTsMs());
            fieldCnt++;
        }
        if (proto.hasSpeed()) {
            this.setSpeed(proto.getSpeed());
            fieldCnt++;
        }
        if (proto.hasStateEndTsMs()) {
            this.setStateEndTsMs(proto.getStateEndTsMs());
            fieldCnt++;
        }
        if (proto.hasStateStartTsMs()) {
            this.setStateStartTsMs(proto.getStateStartTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ProgressInfo.Builder getCopyDbBuilder() {
        final ProgressInfo.Builder builder = ProgressInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ProgressInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUid() != 0L) {
            builder.setUid(this.getUid());
            fieldCnt++;
        }  else if (builder.hasUid()) {
            // 清理Uid
            builder.clearUid();
            fieldCnt++;
        }
        if (this.getLastCalNum() != 0L) {
            builder.setLastCalNum(this.getLastCalNum());
            fieldCnt++;
        }  else if (builder.hasLastCalNum()) {
            // 清理LastCalNum
            builder.clearLastCalNum();
            fieldCnt++;
        }
        if (this.getMaxNum() != 0L) {
            builder.setMaxNum(this.getMaxNum());
            fieldCnt++;
        }  else if (builder.hasMaxNum()) {
            // 清理MaxNum
            builder.clearMaxNum();
            fieldCnt++;
        }
        if (this.getLastCalTsMs() != 0L) {
            builder.setLastCalTsMs(this.getLastCalTsMs());
            fieldCnt++;
        }  else if (builder.hasLastCalTsMs()) {
            // 清理LastCalTsMs
            builder.clearLastCalTsMs();
            fieldCnt++;
        }
        if (this.getSpeed() != 0L) {
            builder.setSpeed(this.getSpeed());
            fieldCnt++;
        }  else if (builder.hasSpeed()) {
            // 清理Speed
            builder.clearSpeed();
            fieldCnt++;
        }
        if (this.getStateEndTsMs() != 0L) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }  else if (builder.hasStateEndTsMs()) {
            // 清理StateEndTsMs
            builder.clearStateEndTsMs();
            fieldCnt++;
        }
        if (this.getStateStartTsMs() != 0L) {
            builder.setStateStartTsMs(this.getStateStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStateStartTsMs()) {
            // 清理StateStartTsMs
            builder.clearStateStartTsMs();
            fieldCnt++;
        }
        if (this.getEnterAdditionValue() != 0L) {
            builder.setEnterAdditionValue(this.getEnterAdditionValue());
            fieldCnt++;
        }  else if (builder.hasEnterAdditionValue()) {
            // 清理EnterAdditionValue
            builder.clearEnterAdditionValue();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ProgressInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UID)) {
            builder.setUid(this.getUid());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTCALNUM)) {
            builder.setLastCalNum(this.getLastCalNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXNUM)) {
            builder.setMaxNum(this.getMaxNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTCALTSMS)) {
            builder.setLastCalTsMs(this.getLastCalTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SPEED)) {
            builder.setSpeed(this.getSpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATEENDTSMS)) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATESTARTTSMS)) {
            builder.setStateStartTsMs(this.getStateStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERADDITIONVALUE)) {
            builder.setEnterAdditionValue(this.getEnterAdditionValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ProgressInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUid()) {
            this.innerSetUid(proto.getUid());
        } else {
            this.innerSetUid(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastCalNum()) {
            this.innerSetLastCalNum(proto.getLastCalNum());
        } else {
            this.innerSetLastCalNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMaxNum()) {
            this.innerSetMaxNum(proto.getMaxNum());
        } else {
            this.innerSetMaxNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastCalTsMs()) {
            this.innerSetLastCalTsMs(proto.getLastCalTsMs());
        } else {
            this.innerSetLastCalTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSpeed()) {
            this.innerSetSpeed(proto.getSpeed());
        } else {
            this.innerSetSpeed(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStateEndTsMs()) {
            this.innerSetStateEndTsMs(proto.getStateEndTsMs());
        } else {
            this.innerSetStateEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStateStartTsMs()) {
            this.innerSetStateStartTsMs(proto.getStateStartTsMs());
        } else {
            this.innerSetStateStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEnterAdditionValue()) {
            this.innerSetEnterAdditionValue(proto.getEnterAdditionValue());
        } else {
            this.innerSetEnterAdditionValue(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ProgressInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ProgressInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUid()) {
            this.setUid(proto.getUid());
            fieldCnt++;
        }
        if (proto.hasLastCalNum()) {
            this.setLastCalNum(proto.getLastCalNum());
            fieldCnt++;
        }
        if (proto.hasMaxNum()) {
            this.setMaxNum(proto.getMaxNum());
            fieldCnt++;
        }
        if (proto.hasLastCalTsMs()) {
            this.setLastCalTsMs(proto.getLastCalTsMs());
            fieldCnt++;
        }
        if (proto.hasSpeed()) {
            this.setSpeed(proto.getSpeed());
            fieldCnt++;
        }
        if (proto.hasStateEndTsMs()) {
            this.setStateEndTsMs(proto.getStateEndTsMs());
            fieldCnt++;
        }
        if (proto.hasStateStartTsMs()) {
            this.setStateStartTsMs(proto.getStateStartTsMs());
            fieldCnt++;
        }
        if (proto.hasEnterAdditionValue()) {
            this.setEnterAdditionValue(proto.getEnterAdditionValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ProgressInfo.Builder getCopySsBuilder() {
        final ProgressInfo.Builder builder = ProgressInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ProgressInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUid() != 0L) {
            builder.setUid(this.getUid());
            fieldCnt++;
        }  else if (builder.hasUid()) {
            // 清理Uid
            builder.clearUid();
            fieldCnt++;
        }
        if (this.getLastCalNum() != 0L) {
            builder.setLastCalNum(this.getLastCalNum());
            fieldCnt++;
        }  else if (builder.hasLastCalNum()) {
            // 清理LastCalNum
            builder.clearLastCalNum();
            fieldCnt++;
        }
        if (this.getMaxNum() != 0L) {
            builder.setMaxNum(this.getMaxNum());
            fieldCnt++;
        }  else if (builder.hasMaxNum()) {
            // 清理MaxNum
            builder.clearMaxNum();
            fieldCnt++;
        }
        if (this.getLastCalTsMs() != 0L) {
            builder.setLastCalTsMs(this.getLastCalTsMs());
            fieldCnt++;
        }  else if (builder.hasLastCalTsMs()) {
            // 清理LastCalTsMs
            builder.clearLastCalTsMs();
            fieldCnt++;
        }
        if (this.getSpeed() != 0L) {
            builder.setSpeed(this.getSpeed());
            fieldCnt++;
        }  else if (builder.hasSpeed()) {
            // 清理Speed
            builder.clearSpeed();
            fieldCnt++;
        }
        if (this.getStateEndTsMs() != 0L) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }  else if (builder.hasStateEndTsMs()) {
            // 清理StateEndTsMs
            builder.clearStateEndTsMs();
            fieldCnt++;
        }
        if (this.getStateStartTsMs() != 0L) {
            builder.setStateStartTsMs(this.getStateStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStateStartTsMs()) {
            // 清理StateStartTsMs
            builder.clearStateStartTsMs();
            fieldCnt++;
        }
        if (this.getEnterAdditionValue() != 0L) {
            builder.setEnterAdditionValue(this.getEnterAdditionValue());
            fieldCnt++;
        }  else if (builder.hasEnterAdditionValue()) {
            // 清理EnterAdditionValue
            builder.clearEnterAdditionValue();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ProgressInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UID)) {
            builder.setUid(this.getUid());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTCALNUM)) {
            builder.setLastCalNum(this.getLastCalNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXNUM)) {
            builder.setMaxNum(this.getMaxNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTCALTSMS)) {
            builder.setLastCalTsMs(this.getLastCalTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SPEED)) {
            builder.setSpeed(this.getSpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATEENDTSMS)) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATESTARTTSMS)) {
            builder.setStateStartTsMs(this.getStateStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERADDITIONVALUE)) {
            builder.setEnterAdditionValue(this.getEnterAdditionValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ProgressInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUid()) {
            this.innerSetUid(proto.getUid());
        } else {
            this.innerSetUid(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastCalNum()) {
            this.innerSetLastCalNum(proto.getLastCalNum());
        } else {
            this.innerSetLastCalNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMaxNum()) {
            this.innerSetMaxNum(proto.getMaxNum());
        } else {
            this.innerSetMaxNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastCalTsMs()) {
            this.innerSetLastCalTsMs(proto.getLastCalTsMs());
        } else {
            this.innerSetLastCalTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSpeed()) {
            this.innerSetSpeed(proto.getSpeed());
        } else {
            this.innerSetSpeed(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStateEndTsMs()) {
            this.innerSetStateEndTsMs(proto.getStateEndTsMs());
        } else {
            this.innerSetStateEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStateStartTsMs()) {
            this.innerSetStateStartTsMs(proto.getStateStartTsMs());
        } else {
            this.innerSetStateStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEnterAdditionValue()) {
            this.innerSetEnterAdditionValue(proto.getEnterAdditionValue());
        } else {
            this.innerSetEnterAdditionValue(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ProgressInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ProgressInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUid()) {
            this.setUid(proto.getUid());
            fieldCnt++;
        }
        if (proto.hasLastCalNum()) {
            this.setLastCalNum(proto.getLastCalNum());
            fieldCnt++;
        }
        if (proto.hasMaxNum()) {
            this.setMaxNum(proto.getMaxNum());
            fieldCnt++;
        }
        if (proto.hasLastCalTsMs()) {
            this.setLastCalTsMs(proto.getLastCalTsMs());
            fieldCnt++;
        }
        if (proto.hasSpeed()) {
            this.setSpeed(proto.getSpeed());
            fieldCnt++;
        }
        if (proto.hasStateEndTsMs()) {
            this.setStateEndTsMs(proto.getStateEndTsMs());
            fieldCnt++;
        }
        if (proto.hasStateStartTsMs()) {
            this.setStateStartTsMs(proto.getStateStartTsMs());
            fieldCnt++;
        }
        if (proto.hasEnterAdditionValue()) {
            this.setEnterAdditionValue(proto.getEnterAdditionValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ProgressInfo.Builder builder = ProgressInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.uid;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ProgressInfoProp)) {
            return false;
        }
        final ProgressInfoProp otherNode = (ProgressInfoProp) node;
        if (this.uid != otherNode.uid) {
            return false;
        }
        if (this.lastCalNum != otherNode.lastCalNum) {
            return false;
        }
        if (this.maxNum != otherNode.maxNum) {
            return false;
        }
        if (this.lastCalTsMs != otherNode.lastCalTsMs) {
            return false;
        }
        if (this.speed != otherNode.speed) {
            return false;
        }
        if (this.stateEndTsMs != otherNode.stateEndTsMs) {
            return false;
        }
        if (this.stateStartTsMs != otherNode.stateStartTsMs) {
            return false;
        }
        if (this.enterAdditionValue != otherNode.enterAdditionValue) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 56;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
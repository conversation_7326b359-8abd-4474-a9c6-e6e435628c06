package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;

/**
 * <AUTHOR> auto gen
 */
public class ItemRewardListProp extends AbstractListNode<ItemRewardProp> {
    /**
     * Create a ItemRewardListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public ItemRewardListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to ItemRewardListProp
     *
     * @return new object
     */
    @Override
    public ItemRewardProp addEmptyValue() {
        final ItemRewardProp newProp = new ItemRewardProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }

                        
}
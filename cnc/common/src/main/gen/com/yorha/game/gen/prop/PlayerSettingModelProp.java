package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerSettingModel;
import com.yorha.proto.Basic;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.PlayerSettingModelPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerSettingModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_REGISTERIP = 0;
    public static final int FIELD_INDEX_CURPFLAG = 1;
    public static final int FIELD_INDEX_SHOWPFLAG = 2;
    public static final int FIELD_INDEX_SPASSWORD = 3;
    public static final int FIELD_INDEX_CLOSESPWTIME = 4;
    public static final int FIELD_INDEX_EXPRESSIONS = 5;
    public static final int FIELD_INDEX_OPENSPW = 6;
    public static final int FIELD_INDEX_FAILEDTIMESBYDAY = 7;
    public static final int FIELD_INDEX_SWITCHSETTING = 8;

    public static final int FIELD_COUNT = 9;

    private long markBits0 = 0L;

    private String registerIp = Constant.DEFAULT_STR_VALUE;
    private int curPFlag = Constant.DEFAULT_INT_VALUE;
    private boolean showPFlag = Constant.DEFAULT_BOOLEAN_VALUE;
    private String sPassWord = Constant.DEFAULT_STR_VALUE;
    private long closeSpwTime = Constant.DEFAULT_LONG_VALUE;
    private Int32ExpressionMapProp expressions = null;
    private boolean openSpw = Constant.DEFAULT_BOOLEAN_VALUE;
    private long failedTimesByDay = Constant.DEFAULT_LONG_VALUE;
    private Int32SetProp switchSetting = null;

    public PlayerSettingModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerSettingModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get registerIp
     *
     * @return registerIp value
     */
    public String getRegisterIp() {
        return this.registerIp;
    }

    /**
     * set registerIp && set marked
     *
     * @param registerIp new value
     * @return current object
     */
    public PlayerSettingModelProp setRegisterIp(String registerIp) {
        if (registerIp == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.registerIp, registerIp)) {
            this.mark(FIELD_INDEX_REGISTERIP);
            this.registerIp = registerIp;
        }
        return this;
    }

    /**
     * inner set registerIp
     *
     * @param registerIp new value
     */
    private void innerSetRegisterIp(String registerIp) {
        this.registerIp = registerIp;
    }

    /**
     * get curPFlag
     *
     * @return curPFlag value
     */
    public int getCurPFlag() {
        return this.curPFlag;
    }

    /**
     * set curPFlag && set marked
     *
     * @param curPFlag new value
     * @return current object
     */
    public PlayerSettingModelProp setCurPFlag(int curPFlag) {
        if (this.curPFlag != curPFlag) {
            this.mark(FIELD_INDEX_CURPFLAG);
            this.curPFlag = curPFlag;
        }
        return this;
    }

    /**
     * inner set curPFlag
     *
     * @param curPFlag new value
     */
    private void innerSetCurPFlag(int curPFlag) {
        this.curPFlag = curPFlag;
    }

    /**
     * get showPFlag
     *
     * @return showPFlag value
     */
    public boolean getShowPFlag() {
        return this.showPFlag;
    }

    /**
     * set showPFlag && set marked
     *
     * @param showPFlag new value
     * @return current object
     */
    public PlayerSettingModelProp setShowPFlag(boolean showPFlag) {
        if (this.showPFlag != showPFlag) {
            this.mark(FIELD_INDEX_SHOWPFLAG);
            this.showPFlag = showPFlag;
        }
        return this;
    }

    /**
     * inner set showPFlag
     *
     * @param showPFlag new value
     */
    private void innerSetShowPFlag(boolean showPFlag) {
        this.showPFlag = showPFlag;
    }

    /**
     * get sPassWord
     *
     * @return sPassWord value
     */
    public String getSPassWord() {
        return this.sPassWord;
    }

    /**
     * set sPassWord && set marked
     *
     * @param sPassWord new value
     * @return current object
     */
    public PlayerSettingModelProp setSPassWord(String sPassWord) {
        if (sPassWord == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.sPassWord, sPassWord)) {
            this.mark(FIELD_INDEX_SPASSWORD);
            this.sPassWord = sPassWord;
        }
        return this;
    }

    /**
     * inner set sPassWord
     *
     * @param sPassWord new value
     */
    private void innerSetSPassWord(String sPassWord) {
        this.sPassWord = sPassWord;
    }

    /**
     * get closeSpwTime
     *
     * @return closeSpwTime value
     */
    public long getCloseSpwTime() {
        return this.closeSpwTime;
    }

    /**
     * set closeSpwTime && set marked
     *
     * @param closeSpwTime new value
     * @return current object
     */
    public PlayerSettingModelProp setCloseSpwTime(long closeSpwTime) {
        if (this.closeSpwTime != closeSpwTime) {
            this.mark(FIELD_INDEX_CLOSESPWTIME);
            this.closeSpwTime = closeSpwTime;
        }
        return this;
    }

    /**
     * inner set closeSpwTime
     *
     * @param closeSpwTime new value
     */
    private void innerSetCloseSpwTime(long closeSpwTime) {
        this.closeSpwTime = closeSpwTime;
    }

    /**
     * get expressions
     *
     * @return expressions value
     */
    public Int32ExpressionMapProp getExpressions() {
        if (this.expressions == null) {
            this.expressions = new Int32ExpressionMapProp(this, FIELD_INDEX_EXPRESSIONS);
        }
        return this.expressions;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putExpressionsV(ExpressionProp v) {
        this.getExpressions().put(v.getExpressionId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ExpressionProp addEmptyExpressions(Integer k) {
        return this.getExpressions().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getExpressionsSize() {
        if (this.expressions == null) {
            return 0;
        }
        return this.expressions.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isExpressionsEmpty() {
        if (this.expressions == null) {
            return true;
        }
        return this.expressions.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ExpressionProp getExpressionsV(Integer k) {
        if (this.expressions == null || !this.expressions.containsKey(k)) {
            return null;
        }
        return this.expressions.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearExpressions() {
        if (this.expressions != null) {
            this.expressions.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeExpressionsV(Integer k) {
        if (this.expressions != null) {
            this.expressions.remove(k);
        }
    }
    /**
     * get openSpw
     *
     * @return openSpw value
     */
    public boolean getOpenSpw() {
        return this.openSpw;
    }

    /**
     * set openSpw && set marked
     *
     * @param openSpw new value
     * @return current object
     */
    public PlayerSettingModelProp setOpenSpw(boolean openSpw) {
        if (this.openSpw != openSpw) {
            this.mark(FIELD_INDEX_OPENSPW);
            this.openSpw = openSpw;
        }
        return this;
    }

    /**
     * inner set openSpw
     *
     * @param openSpw new value
     */
    private void innerSetOpenSpw(boolean openSpw) {
        this.openSpw = openSpw;
    }

    /**
     * get failedTimesByDay
     *
     * @return failedTimesByDay value
     */
    public long getFailedTimesByDay() {
        return this.failedTimesByDay;
    }

    /**
     * set failedTimesByDay && set marked
     *
     * @param failedTimesByDay new value
     * @return current object
     */
    public PlayerSettingModelProp setFailedTimesByDay(long failedTimesByDay) {
        if (this.failedTimesByDay != failedTimesByDay) {
            this.mark(FIELD_INDEX_FAILEDTIMESBYDAY);
            this.failedTimesByDay = failedTimesByDay;
        }
        return this;
    }

    /**
     * inner set failedTimesByDay
     *
     * @param failedTimesByDay new value
     */
    private void innerSetFailedTimesByDay(long failedTimesByDay) {
        this.failedTimesByDay = failedTimesByDay;
    }

    /**
     * get switchSetting
     *
     * @return switchSetting value
     */
    public Int32SetProp getSwitchSetting() {
        if (this.switchSetting == null) {
            this.switchSetting = new Int32SetProp(this, FIELD_INDEX_SWITCHSETTING);
        }
        return this.switchSetting;
    }


    /**
     * add value to set
     *
     * @param e value
     */
    public void addSwitchSetting(Integer e) {
        this.getSwitchSetting().add(e);
    }

    /**
     * remove the specified element in set
     *
     * @param e element
     * @return e if success or null by fail
     */
    public Integer removeSwitchSetting(Integer e) {
        if (this.switchSetting == null) {
            return null;
        }
        if(this.switchSetting.remove(e)) {
            return e;
        }
        return null;
    }

    /**
     * get set size
     *
     * @return set size
     */
    public int getSwitchSettingSize() {
        if (this.switchSetting == null) {
            return 0;
        }
        return this.switchSetting.size();
    }

    /**
     * get is a empty set
     *
     * @return true if empty
     */
    public boolean isSwitchSettingEmpty() {
        if (this.switchSetting == null) {
            return true;
        }
        return this.getSwitchSetting().isEmpty();
    }

    /**
     * clear set
     */
    public void clearSwitchSetting() {
        this.getSwitchSetting().clear();
    }

    /**
     * Returns true if this set contains the specified element.
     *
     * @param e elem
     * @return true if this set contains the specified element, false otherwise
     */
    public boolean isSwitchSettingContains(Integer e) {
        return this.switchSetting != null && this.switchSetting.contains(e);
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerSettingModelPB.Builder getCopyCsBuilder() {
        final PlayerSettingModelPB.Builder builder = PlayerSettingModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerSettingModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getRegisterIp().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setRegisterIp(this.getRegisterIp());
            fieldCnt++;
        }  else if (builder.hasRegisterIp()) {
            // 清理RegisterIp
            builder.clearRegisterIp();
            fieldCnt++;
        }
        if (this.getCurPFlag() != 0) {
            builder.setCurPFlag(this.getCurPFlag());
            fieldCnt++;
        }  else if (builder.hasCurPFlag()) {
            // 清理CurPFlag
            builder.clearCurPFlag();
            fieldCnt++;
        }
        if (this.getShowPFlag()) {
            builder.setShowPFlag(this.getShowPFlag());
            fieldCnt++;
        }  else if (builder.hasShowPFlag()) {
            // 清理ShowPFlag
            builder.clearShowPFlag();
            fieldCnt++;
        }
        if (this.getCloseSpwTime() != 0L) {
            builder.setCloseSpwTime(this.getCloseSpwTime());
            fieldCnt++;
        }  else if (builder.hasCloseSpwTime()) {
            // 清理CloseSpwTime
            builder.clearCloseSpwTime();
            fieldCnt++;
        }
        if (this.expressions != null) {
            StructPB.Int32ExpressionMapPB.Builder tmpBuilder = StructPB.Int32ExpressionMapPB.newBuilder();
            final int tmpFieldCnt = this.expressions.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExpressions(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExpressions();
            }
        }  else if (builder.hasExpressions()) {
            // 清理Expressions
            builder.clearExpressions();
            fieldCnt++;
        }
        if (this.getOpenSpw()) {
            builder.setOpenSpw(this.getOpenSpw());
            fieldCnt++;
        }  else if (builder.hasOpenSpw()) {
            // 清理OpenSpw
            builder.clearOpenSpw();
            fieldCnt++;
        }
        if (this.getFailedTimesByDay() != 0L) {
            builder.setFailedTimesByDay(this.getFailedTimesByDay());
            fieldCnt++;
        }  else if (builder.hasFailedTimesByDay()) {
            // 清理FailedTimesByDay
            builder.clearFailedTimesByDay();
            fieldCnt++;
        }
        if (this.switchSetting != null) {
            BasicPB.Int32SetPB.Builder tmpBuilder = BasicPB.Int32SetPB.newBuilder();
            final int tmpFieldCnt = this.switchSetting.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSwitchSetting(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSwitchSetting();
            }
        }  else if (builder.hasSwitchSetting()) {
            // 清理SwitchSetting
            builder.clearSwitchSetting();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerSettingModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REGISTERIP)) {
            builder.setRegisterIp(this.getRegisterIp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURPFLAG)) {
            builder.setCurPFlag(this.getCurPFlag());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWPFLAG)) {
            builder.setShowPFlag(this.getShowPFlag());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLOSESPWTIME)) {
            builder.setCloseSpwTime(this.getCloseSpwTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSIONS) && this.expressions != null) {
            final boolean needClear = !builder.hasExpressions();
            final int tmpFieldCnt = this.expressions.copyChangeToCs(builder.getExpressionsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpressions();
            }
        }
        if (this.hasMark(FIELD_INDEX_OPENSPW)) {
            builder.setOpenSpw(this.getOpenSpw());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FAILEDTIMESBYDAY)) {
            builder.setFailedTimesByDay(this.getFailedTimesByDay());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SWITCHSETTING) && this.switchSetting != null) {
            final boolean needClear = !builder.hasSwitchSetting();
            final int tmpFieldCnt = this.switchSetting.copyChangeToCs(builder.getSwitchSettingBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSwitchSetting();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerSettingModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REGISTERIP)) {
            builder.setRegisterIp(this.getRegisterIp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURPFLAG)) {
            builder.setCurPFlag(this.getCurPFlag());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWPFLAG)) {
            builder.setShowPFlag(this.getShowPFlag());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLOSESPWTIME)) {
            builder.setCloseSpwTime(this.getCloseSpwTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSIONS) && this.expressions != null) {
            final boolean needClear = !builder.hasExpressions();
            final int tmpFieldCnt = this.expressions.copyChangeToAndClearDeleteKeysCs(builder.getExpressionsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpressions();
            }
        }
        if (this.hasMark(FIELD_INDEX_OPENSPW)) {
            builder.setOpenSpw(this.getOpenSpw());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FAILEDTIMESBYDAY)) {
            builder.setFailedTimesByDay(this.getFailedTimesByDay());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SWITCHSETTING) && this.switchSetting != null) {
            final boolean needClear = !builder.hasSwitchSetting();
            final int tmpFieldCnt = this.switchSetting.copyChangeToAndClearDeleteKeysCs(builder.getSwitchSettingBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSwitchSetting();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerSettingModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRegisterIp()) {
            this.innerSetRegisterIp(proto.getRegisterIp());
        } else {
            this.innerSetRegisterIp(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasCurPFlag()) {
            this.innerSetCurPFlag(proto.getCurPFlag());
        } else {
            this.innerSetCurPFlag(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasShowPFlag()) {
            this.innerSetShowPFlag(proto.getShowPFlag());
        } else {
            this.innerSetShowPFlag(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasCloseSpwTime()) {
            this.innerSetCloseSpwTime(proto.getCloseSpwTime());
        } else {
            this.innerSetCloseSpwTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasExpressions()) {
            this.getExpressions().mergeFromCs(proto.getExpressions());
        } else {
            if (this.expressions != null) {
                this.expressions.mergeFromCs(proto.getExpressions());
            }
        }
        if (proto.hasOpenSpw()) {
            this.innerSetOpenSpw(proto.getOpenSpw());
        } else {
            this.innerSetOpenSpw(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasFailedTimesByDay()) {
            this.innerSetFailedTimesByDay(proto.getFailedTimesByDay());
        } else {
            this.innerSetFailedTimesByDay(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSwitchSetting()) {
            this.getSwitchSetting().mergeFromCs(proto.getSwitchSetting());
        } else {
            if (this.switchSetting != null) {
                this.switchSetting.mergeFromCs(proto.getSwitchSetting());
            }
        }
        this.markAll();
        return PlayerSettingModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerSettingModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRegisterIp()) {
            this.setRegisterIp(proto.getRegisterIp());
            fieldCnt++;
        }
        if (proto.hasCurPFlag()) {
            this.setCurPFlag(proto.getCurPFlag());
            fieldCnt++;
        }
        if (proto.hasShowPFlag()) {
            this.setShowPFlag(proto.getShowPFlag());
            fieldCnt++;
        }
        if (proto.hasCloseSpwTime()) {
            this.setCloseSpwTime(proto.getCloseSpwTime());
            fieldCnt++;
        }
        if (proto.hasExpressions()) {
            this.getExpressions().mergeChangeFromCs(proto.getExpressions());
            fieldCnt++;
        }
        if (proto.hasOpenSpw()) {
            this.setOpenSpw(proto.getOpenSpw());
            fieldCnt++;
        }
        if (proto.hasFailedTimesByDay()) {
            this.setFailedTimesByDay(proto.getFailedTimesByDay());
            fieldCnt++;
        }
        if (proto.hasSwitchSetting()) {
            this.getSwitchSetting().mergeChangeFromCs(proto.getSwitchSetting());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerSettingModel.Builder getCopyDbBuilder() {
        final PlayerSettingModel.Builder builder = PlayerSettingModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerSettingModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getRegisterIp().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setRegisterIp(this.getRegisterIp());
            fieldCnt++;
        }  else if (builder.hasRegisterIp()) {
            // 清理RegisterIp
            builder.clearRegisterIp();
            fieldCnt++;
        }
        if (this.getCurPFlag() != 0) {
            builder.setCurPFlag(this.getCurPFlag());
            fieldCnt++;
        }  else if (builder.hasCurPFlag()) {
            // 清理CurPFlag
            builder.clearCurPFlag();
            fieldCnt++;
        }
        if (this.getShowPFlag()) {
            builder.setShowPFlag(this.getShowPFlag());
            fieldCnt++;
        }  else if (builder.hasShowPFlag()) {
            // 清理ShowPFlag
            builder.clearShowPFlag();
            fieldCnt++;
        }
        if (!this.getSPassWord().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSPassWord(this.getSPassWord());
            fieldCnt++;
        }  else if (builder.hasSPassWord()) {
            // 清理SPassWord
            builder.clearSPassWord();
            fieldCnt++;
        }
        if (this.getCloseSpwTime() != 0L) {
            builder.setCloseSpwTime(this.getCloseSpwTime());
            fieldCnt++;
        }  else if (builder.hasCloseSpwTime()) {
            // 清理CloseSpwTime
            builder.clearCloseSpwTime();
            fieldCnt++;
        }
        if (this.expressions != null) {
            Struct.Int32ExpressionMap.Builder tmpBuilder = Struct.Int32ExpressionMap.newBuilder();
            final int tmpFieldCnt = this.expressions.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExpressions(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExpressions();
            }
        }  else if (builder.hasExpressions()) {
            // 清理Expressions
            builder.clearExpressions();
            fieldCnt++;
        }
        if (this.getOpenSpw()) {
            builder.setOpenSpw(this.getOpenSpw());
            fieldCnt++;
        }  else if (builder.hasOpenSpw()) {
            // 清理OpenSpw
            builder.clearOpenSpw();
            fieldCnt++;
        }
        if (this.getFailedTimesByDay() != 0L) {
            builder.setFailedTimesByDay(this.getFailedTimesByDay());
            fieldCnt++;
        }  else if (builder.hasFailedTimesByDay()) {
            // 清理FailedTimesByDay
            builder.clearFailedTimesByDay();
            fieldCnt++;
        }
        if (this.switchSetting != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.switchSetting.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSwitchSetting(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSwitchSetting();
            }
        }  else if (builder.hasSwitchSetting()) {
            // 清理SwitchSetting
            builder.clearSwitchSetting();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerSettingModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REGISTERIP)) {
            builder.setRegisterIp(this.getRegisterIp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURPFLAG)) {
            builder.setCurPFlag(this.getCurPFlag());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWPFLAG)) {
            builder.setShowPFlag(this.getShowPFlag());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SPASSWORD)) {
            builder.setSPassWord(this.getSPassWord());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLOSESPWTIME)) {
            builder.setCloseSpwTime(this.getCloseSpwTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSIONS) && this.expressions != null) {
            final boolean needClear = !builder.hasExpressions();
            final int tmpFieldCnt = this.expressions.copyChangeToDb(builder.getExpressionsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpressions();
            }
        }
        if (this.hasMark(FIELD_INDEX_OPENSPW)) {
            builder.setOpenSpw(this.getOpenSpw());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FAILEDTIMESBYDAY)) {
            builder.setFailedTimesByDay(this.getFailedTimesByDay());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SWITCHSETTING) && this.switchSetting != null) {
            final boolean needClear = !builder.hasSwitchSetting();
            final int tmpFieldCnt = this.switchSetting.copyChangeToDb(builder.getSwitchSettingBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSwitchSetting();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerSettingModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRegisterIp()) {
            this.innerSetRegisterIp(proto.getRegisterIp());
        } else {
            this.innerSetRegisterIp(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasCurPFlag()) {
            this.innerSetCurPFlag(proto.getCurPFlag());
        } else {
            this.innerSetCurPFlag(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasShowPFlag()) {
            this.innerSetShowPFlag(proto.getShowPFlag());
        } else {
            this.innerSetShowPFlag(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasSPassWord()) {
            this.innerSetSPassWord(proto.getSPassWord());
        } else {
            this.innerSetSPassWord(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasCloseSpwTime()) {
            this.innerSetCloseSpwTime(proto.getCloseSpwTime());
        } else {
            this.innerSetCloseSpwTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasExpressions()) {
            this.getExpressions().mergeFromDb(proto.getExpressions());
        } else {
            if (this.expressions != null) {
                this.expressions.mergeFromDb(proto.getExpressions());
            }
        }
        if (proto.hasOpenSpw()) {
            this.innerSetOpenSpw(proto.getOpenSpw());
        } else {
            this.innerSetOpenSpw(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasFailedTimesByDay()) {
            this.innerSetFailedTimesByDay(proto.getFailedTimesByDay());
        } else {
            this.innerSetFailedTimesByDay(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSwitchSetting()) {
            this.getSwitchSetting().mergeFromDb(proto.getSwitchSetting());
        } else {
            if (this.switchSetting != null) {
                this.switchSetting.mergeFromDb(proto.getSwitchSetting());
            }
        }
        this.markAll();
        return PlayerSettingModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerSettingModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRegisterIp()) {
            this.setRegisterIp(proto.getRegisterIp());
            fieldCnt++;
        }
        if (proto.hasCurPFlag()) {
            this.setCurPFlag(proto.getCurPFlag());
            fieldCnt++;
        }
        if (proto.hasShowPFlag()) {
            this.setShowPFlag(proto.getShowPFlag());
            fieldCnt++;
        }
        if (proto.hasSPassWord()) {
            this.setSPassWord(proto.getSPassWord());
            fieldCnt++;
        }
        if (proto.hasCloseSpwTime()) {
            this.setCloseSpwTime(proto.getCloseSpwTime());
            fieldCnt++;
        }
        if (proto.hasExpressions()) {
            this.getExpressions().mergeChangeFromDb(proto.getExpressions());
            fieldCnt++;
        }
        if (proto.hasOpenSpw()) {
            this.setOpenSpw(proto.getOpenSpw());
            fieldCnt++;
        }
        if (proto.hasFailedTimesByDay()) {
            this.setFailedTimesByDay(proto.getFailedTimesByDay());
            fieldCnt++;
        }
        if (proto.hasSwitchSetting()) {
            this.getSwitchSetting().mergeChangeFromDb(proto.getSwitchSetting());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerSettingModel.Builder getCopySsBuilder() {
        final PlayerSettingModel.Builder builder = PlayerSettingModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerSettingModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getRegisterIp().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setRegisterIp(this.getRegisterIp());
            fieldCnt++;
        }  else if (builder.hasRegisterIp()) {
            // 清理RegisterIp
            builder.clearRegisterIp();
            fieldCnt++;
        }
        if (this.getCurPFlag() != 0) {
            builder.setCurPFlag(this.getCurPFlag());
            fieldCnt++;
        }  else if (builder.hasCurPFlag()) {
            // 清理CurPFlag
            builder.clearCurPFlag();
            fieldCnt++;
        }
        if (this.getShowPFlag()) {
            builder.setShowPFlag(this.getShowPFlag());
            fieldCnt++;
        }  else if (builder.hasShowPFlag()) {
            // 清理ShowPFlag
            builder.clearShowPFlag();
            fieldCnt++;
        }
        if (!this.getSPassWord().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSPassWord(this.getSPassWord());
            fieldCnt++;
        }  else if (builder.hasSPassWord()) {
            // 清理SPassWord
            builder.clearSPassWord();
            fieldCnt++;
        }
        if (this.getCloseSpwTime() != 0L) {
            builder.setCloseSpwTime(this.getCloseSpwTime());
            fieldCnt++;
        }  else if (builder.hasCloseSpwTime()) {
            // 清理CloseSpwTime
            builder.clearCloseSpwTime();
            fieldCnt++;
        }
        if (this.expressions != null) {
            Struct.Int32ExpressionMap.Builder tmpBuilder = Struct.Int32ExpressionMap.newBuilder();
            final int tmpFieldCnt = this.expressions.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExpressions(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExpressions();
            }
        }  else if (builder.hasExpressions()) {
            // 清理Expressions
            builder.clearExpressions();
            fieldCnt++;
        }
        if (this.getOpenSpw()) {
            builder.setOpenSpw(this.getOpenSpw());
            fieldCnt++;
        }  else if (builder.hasOpenSpw()) {
            // 清理OpenSpw
            builder.clearOpenSpw();
            fieldCnt++;
        }
        if (this.getFailedTimesByDay() != 0L) {
            builder.setFailedTimesByDay(this.getFailedTimesByDay());
            fieldCnt++;
        }  else if (builder.hasFailedTimesByDay()) {
            // 清理FailedTimesByDay
            builder.clearFailedTimesByDay();
            fieldCnt++;
        }
        if (this.switchSetting != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.switchSetting.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSwitchSetting(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSwitchSetting();
            }
        }  else if (builder.hasSwitchSetting()) {
            // 清理SwitchSetting
            builder.clearSwitchSetting();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerSettingModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REGISTERIP)) {
            builder.setRegisterIp(this.getRegisterIp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURPFLAG)) {
            builder.setCurPFlag(this.getCurPFlag());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWPFLAG)) {
            builder.setShowPFlag(this.getShowPFlag());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SPASSWORD)) {
            builder.setSPassWord(this.getSPassWord());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLOSESPWTIME)) {
            builder.setCloseSpwTime(this.getCloseSpwTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSIONS) && this.expressions != null) {
            final boolean needClear = !builder.hasExpressions();
            final int tmpFieldCnt = this.expressions.copyChangeToSs(builder.getExpressionsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpressions();
            }
        }
        if (this.hasMark(FIELD_INDEX_OPENSPW)) {
            builder.setOpenSpw(this.getOpenSpw());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FAILEDTIMESBYDAY)) {
            builder.setFailedTimesByDay(this.getFailedTimesByDay());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SWITCHSETTING) && this.switchSetting != null) {
            final boolean needClear = !builder.hasSwitchSetting();
            final int tmpFieldCnt = this.switchSetting.copyChangeToSs(builder.getSwitchSettingBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSwitchSetting();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerSettingModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRegisterIp()) {
            this.innerSetRegisterIp(proto.getRegisterIp());
        } else {
            this.innerSetRegisterIp(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasCurPFlag()) {
            this.innerSetCurPFlag(proto.getCurPFlag());
        } else {
            this.innerSetCurPFlag(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasShowPFlag()) {
            this.innerSetShowPFlag(proto.getShowPFlag());
        } else {
            this.innerSetShowPFlag(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasSPassWord()) {
            this.innerSetSPassWord(proto.getSPassWord());
        } else {
            this.innerSetSPassWord(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasCloseSpwTime()) {
            this.innerSetCloseSpwTime(proto.getCloseSpwTime());
        } else {
            this.innerSetCloseSpwTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasExpressions()) {
            this.getExpressions().mergeFromSs(proto.getExpressions());
        } else {
            if (this.expressions != null) {
                this.expressions.mergeFromSs(proto.getExpressions());
            }
        }
        if (proto.hasOpenSpw()) {
            this.innerSetOpenSpw(proto.getOpenSpw());
        } else {
            this.innerSetOpenSpw(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasFailedTimesByDay()) {
            this.innerSetFailedTimesByDay(proto.getFailedTimesByDay());
        } else {
            this.innerSetFailedTimesByDay(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSwitchSetting()) {
            this.getSwitchSetting().mergeFromSs(proto.getSwitchSetting());
        } else {
            if (this.switchSetting != null) {
                this.switchSetting.mergeFromSs(proto.getSwitchSetting());
            }
        }
        this.markAll();
        return PlayerSettingModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerSettingModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRegisterIp()) {
            this.setRegisterIp(proto.getRegisterIp());
            fieldCnt++;
        }
        if (proto.hasCurPFlag()) {
            this.setCurPFlag(proto.getCurPFlag());
            fieldCnt++;
        }
        if (proto.hasShowPFlag()) {
            this.setShowPFlag(proto.getShowPFlag());
            fieldCnt++;
        }
        if (proto.hasSPassWord()) {
            this.setSPassWord(proto.getSPassWord());
            fieldCnt++;
        }
        if (proto.hasCloseSpwTime()) {
            this.setCloseSpwTime(proto.getCloseSpwTime());
            fieldCnt++;
        }
        if (proto.hasExpressions()) {
            this.getExpressions().mergeChangeFromSs(proto.getExpressions());
            fieldCnt++;
        }
        if (proto.hasOpenSpw()) {
            this.setOpenSpw(proto.getOpenSpw());
            fieldCnt++;
        }
        if (proto.hasFailedTimesByDay()) {
            this.setFailedTimesByDay(proto.getFailedTimesByDay());
            fieldCnt++;
        }
        if (proto.hasSwitchSetting()) {
            this.getSwitchSetting().mergeChangeFromSs(proto.getSwitchSetting());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerSettingModel.Builder builder = PlayerSettingModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSIONS) && this.expressions != null) {
            this.expressions.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SWITCHSETTING) && this.switchSetting != null) {
            this.switchSetting.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.expressions != null) {
            this.expressions.markAll();
        }
        if (this.switchSetting != null) {
            this.switchSetting.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerSettingModelProp)) {
            return false;
        }
        final PlayerSettingModelProp otherNode = (PlayerSettingModelProp) node;
        if (!com.yorha.gemini.utils.StringUtils.equals(this.registerIp, otherNode.registerIp)) {
            return false;
        }
        if (this.curPFlag != otherNode.curPFlag) {
            return false;
        }
        if (this.showPFlag != otherNode.showPFlag) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.sPassWord, otherNode.sPassWord)) {
            return false;
        }
        if (this.closeSpwTime != otherNode.closeSpwTime) {
            return false;
        }
        if (!this.getExpressions().compareDataTo(otherNode.getExpressions())) {
            return false;
        }
        if (this.openSpw != otherNode.openSpw) {
            return false;
        }
        if (this.failedTimesByDay != otherNode.failedTimesByDay) {
            return false;
        }
        if (!this.getSwitchSetting().compareDataTo(otherNode.getSwitchSetting())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 55;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
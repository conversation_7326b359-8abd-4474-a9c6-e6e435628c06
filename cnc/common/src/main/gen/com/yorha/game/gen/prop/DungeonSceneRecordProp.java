package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.DungeonSceneRecord;
import com.yorha.proto.PlayerPB.DungeonSceneRecordPB;


/**
 * <AUTHOR> auto gen
 */
public class DungeonSceneRecordProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_DUNGEONTYPE = 0;
    public static final int FIELD_INDEX_DUNGEONSCENEID = 1;
    public static final int FIELD_INDEX_LOGOUTTSMS = 2;
    public static final int FIELD_INDEX_DUNGEONREFBUS = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int dungeonType = Constant.DEFAULT_INT_VALUE;
    private long dungeonSceneId = Constant.DEFAULT_LONG_VALUE;
    private long logoutTsMs = Constant.DEFAULT_LONG_VALUE;
    private String dungeonRefBus = Constant.DEFAULT_STR_VALUE;

    public DungeonSceneRecordProp() {
        super(null, 0, FIELD_COUNT);
    }

    public DungeonSceneRecordProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get dungeonType
     *
     * @return dungeonType value
     */
    public int getDungeonType() {
        return this.dungeonType;
    }

    /**
     * set dungeonType && set marked
     *
     * @param dungeonType new value
     * @return current object
     */
    public DungeonSceneRecordProp setDungeonType(int dungeonType) {
        if (this.dungeonType != dungeonType) {
            this.mark(FIELD_INDEX_DUNGEONTYPE);
            this.dungeonType = dungeonType;
        }
        return this;
    }

    /**
     * inner set dungeonType
     *
     * @param dungeonType new value
     */
    private void innerSetDungeonType(int dungeonType) {
        this.dungeonType = dungeonType;
    }

    /**
     * get dungeonSceneId
     *
     * @return dungeonSceneId value
     */
    public long getDungeonSceneId() {
        return this.dungeonSceneId;
    }

    /**
     * set dungeonSceneId && set marked
     *
     * @param dungeonSceneId new value
     * @return current object
     */
    public DungeonSceneRecordProp setDungeonSceneId(long dungeonSceneId) {
        if (this.dungeonSceneId != dungeonSceneId) {
            this.mark(FIELD_INDEX_DUNGEONSCENEID);
            this.dungeonSceneId = dungeonSceneId;
        }
        return this;
    }

    /**
     * inner set dungeonSceneId
     *
     * @param dungeonSceneId new value
     */
    private void innerSetDungeonSceneId(long dungeonSceneId) {
        this.dungeonSceneId = dungeonSceneId;
    }

    /**
     * get logoutTsMs
     *
     * @return logoutTsMs value
     */
    public long getLogoutTsMs() {
        return this.logoutTsMs;
    }

    /**
     * set logoutTsMs && set marked
     *
     * @param logoutTsMs new value
     * @return current object
     */
    public DungeonSceneRecordProp setLogoutTsMs(long logoutTsMs) {
        if (this.logoutTsMs != logoutTsMs) {
            this.mark(FIELD_INDEX_LOGOUTTSMS);
            this.logoutTsMs = logoutTsMs;
        }
        return this;
    }

    /**
     * inner set logoutTsMs
     *
     * @param logoutTsMs new value
     */
    private void innerSetLogoutTsMs(long logoutTsMs) {
        this.logoutTsMs = logoutTsMs;
    }

    /**
     * get dungeonRefBus
     *
     * @return dungeonRefBus value
     */
    public String getDungeonRefBus() {
        return this.dungeonRefBus;
    }

    /**
     * set dungeonRefBus && set marked
     *
     * @param dungeonRefBus new value
     * @return current object
     */
    public DungeonSceneRecordProp setDungeonRefBus(String dungeonRefBus) {
        if (dungeonRefBus == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.dungeonRefBus, dungeonRefBus)) {
            this.mark(FIELD_INDEX_DUNGEONREFBUS);
            this.dungeonRefBus = dungeonRefBus;
        }
        return this;
    }

    /**
     * inner set dungeonRefBus
     *
     * @param dungeonRefBus new value
     */
    private void innerSetDungeonRefBus(String dungeonRefBus) {
        this.dungeonRefBus = dungeonRefBus;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DungeonSceneRecordPB.Builder getCopyCsBuilder() {
        final DungeonSceneRecordPB.Builder builder = DungeonSceneRecordPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(DungeonSceneRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDungeonType() != 0) {
            builder.setDungeonType(this.getDungeonType());
            fieldCnt++;
        }  else if (builder.hasDungeonType()) {
            // 清理DungeonType
            builder.clearDungeonType();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(DungeonSceneRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DUNGEONTYPE)) {
            builder.setDungeonType(this.getDungeonType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(DungeonSceneRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DUNGEONTYPE)) {
            builder.setDungeonType(this.getDungeonType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(DungeonSceneRecordPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDungeonType()) {
            this.innerSetDungeonType(proto.getDungeonType());
        } else {
            this.innerSetDungeonType(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return DungeonSceneRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(DungeonSceneRecordPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDungeonType()) {
            this.setDungeonType(proto.getDungeonType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DungeonSceneRecord.Builder getCopyDbBuilder() {
        final DungeonSceneRecord.Builder builder = DungeonSceneRecord.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(DungeonSceneRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDungeonType() != 0) {
            builder.setDungeonType(this.getDungeonType());
            fieldCnt++;
        }  else if (builder.hasDungeonType()) {
            // 清理DungeonType
            builder.clearDungeonType();
            fieldCnt++;
        }
        if (this.getDungeonSceneId() != 0L) {
            builder.setDungeonSceneId(this.getDungeonSceneId());
            fieldCnt++;
        }  else if (builder.hasDungeonSceneId()) {
            // 清理DungeonSceneId
            builder.clearDungeonSceneId();
            fieldCnt++;
        }
        if (this.getLogoutTsMs() != 0L) {
            builder.setLogoutTsMs(this.getLogoutTsMs());
            fieldCnt++;
        }  else if (builder.hasLogoutTsMs()) {
            // 清理LogoutTsMs
            builder.clearLogoutTsMs();
            fieldCnt++;
        }
        if (!this.getDungeonRefBus().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setDungeonRefBus(this.getDungeonRefBus());
            fieldCnt++;
        }  else if (builder.hasDungeonRefBus()) {
            // 清理DungeonRefBus
            builder.clearDungeonRefBus();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(DungeonSceneRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DUNGEONTYPE)) {
            builder.setDungeonType(this.getDungeonType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONSCENEID)) {
            builder.setDungeonSceneId(this.getDungeonSceneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOGOUTTSMS)) {
            builder.setLogoutTsMs(this.getLogoutTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONREFBUS)) {
            builder.setDungeonRefBus(this.getDungeonRefBus());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(DungeonSceneRecord proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDungeonType()) {
            this.innerSetDungeonType(proto.getDungeonType());
        } else {
            this.innerSetDungeonType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDungeonSceneId()) {
            this.innerSetDungeonSceneId(proto.getDungeonSceneId());
        } else {
            this.innerSetDungeonSceneId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLogoutTsMs()) {
            this.innerSetLogoutTsMs(proto.getLogoutTsMs());
        } else {
            this.innerSetLogoutTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasDungeonRefBus()) {
            this.innerSetDungeonRefBus(proto.getDungeonRefBus());
        } else {
            this.innerSetDungeonRefBus(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return DungeonSceneRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(DungeonSceneRecord proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDungeonType()) {
            this.setDungeonType(proto.getDungeonType());
            fieldCnt++;
        }
        if (proto.hasDungeonSceneId()) {
            this.setDungeonSceneId(proto.getDungeonSceneId());
            fieldCnt++;
        }
        if (proto.hasLogoutTsMs()) {
            this.setLogoutTsMs(proto.getLogoutTsMs());
            fieldCnt++;
        }
        if (proto.hasDungeonRefBus()) {
            this.setDungeonRefBus(proto.getDungeonRefBus());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DungeonSceneRecord.Builder getCopySsBuilder() {
        final DungeonSceneRecord.Builder builder = DungeonSceneRecord.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(DungeonSceneRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDungeonType() != 0) {
            builder.setDungeonType(this.getDungeonType());
            fieldCnt++;
        }  else if (builder.hasDungeonType()) {
            // 清理DungeonType
            builder.clearDungeonType();
            fieldCnt++;
        }
        if (this.getDungeonSceneId() != 0L) {
            builder.setDungeonSceneId(this.getDungeonSceneId());
            fieldCnt++;
        }  else if (builder.hasDungeonSceneId()) {
            // 清理DungeonSceneId
            builder.clearDungeonSceneId();
            fieldCnt++;
        }
        if (this.getLogoutTsMs() != 0L) {
            builder.setLogoutTsMs(this.getLogoutTsMs());
            fieldCnt++;
        }  else if (builder.hasLogoutTsMs()) {
            // 清理LogoutTsMs
            builder.clearLogoutTsMs();
            fieldCnt++;
        }
        if (!this.getDungeonRefBus().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setDungeonRefBus(this.getDungeonRefBus());
            fieldCnt++;
        }  else if (builder.hasDungeonRefBus()) {
            // 清理DungeonRefBus
            builder.clearDungeonRefBus();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(DungeonSceneRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DUNGEONTYPE)) {
            builder.setDungeonType(this.getDungeonType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONSCENEID)) {
            builder.setDungeonSceneId(this.getDungeonSceneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOGOUTTSMS)) {
            builder.setLogoutTsMs(this.getLogoutTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONREFBUS)) {
            builder.setDungeonRefBus(this.getDungeonRefBus());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(DungeonSceneRecord proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDungeonType()) {
            this.innerSetDungeonType(proto.getDungeonType());
        } else {
            this.innerSetDungeonType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDungeonSceneId()) {
            this.innerSetDungeonSceneId(proto.getDungeonSceneId());
        } else {
            this.innerSetDungeonSceneId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLogoutTsMs()) {
            this.innerSetLogoutTsMs(proto.getLogoutTsMs());
        } else {
            this.innerSetLogoutTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasDungeonRefBus()) {
            this.innerSetDungeonRefBus(proto.getDungeonRefBus());
        } else {
            this.innerSetDungeonRefBus(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return DungeonSceneRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(DungeonSceneRecord proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDungeonType()) {
            this.setDungeonType(proto.getDungeonType());
            fieldCnt++;
        }
        if (proto.hasDungeonSceneId()) {
            this.setDungeonSceneId(proto.getDungeonSceneId());
            fieldCnt++;
        }
        if (proto.hasLogoutTsMs()) {
            this.setLogoutTsMs(proto.getLogoutTsMs());
            fieldCnt++;
        }
        if (proto.hasDungeonRefBus()) {
            this.setDungeonRefBus(proto.getDungeonRefBus());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        DungeonSceneRecord.Builder builder = DungeonSceneRecord.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.dungeonType;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof DungeonSceneRecordProp)) {
            return false;
        }
        final DungeonSceneRecordProp otherNode = (DungeonSceneRecordProp) node;
        if (this.dungeonType != otherNode.dungeonType) {
            return false;
        }
        if (this.dungeonSceneId != otherNode.dungeonSceneId) {
            return false;
        }
        if (this.logoutTsMs != otherNode.logoutTsMs) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.dungeonRefBus, otherNode.dungeonRefBus)) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
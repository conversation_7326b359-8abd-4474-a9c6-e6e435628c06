package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.CampaignMission;
import com.yorha.proto.PlayerPB.CampaignMissionPB;


/**
 * <AUTHOR> auto gen
 */
public class CampaignMissionProp extends AbstractPropNode {

    public static final int FIELD_INDEX_MAPID = 0;
    public static final int FIELD_INDEX_REWARDTYPE = 1;
    public static final int FIELD_INDEX_BLUEPRINTID = 2;
    public static final int FIELD_INDEX_EVENTID = 3;
    public static final int FIELD_INDEX_REWARDLEVEL = 4;
    public static final int FIELD_INDEX_REWARDID = 5;
    public static final int FIELD_INDEX_FINISHED = 6;

    public static final int FIELD_COUNT = 7;

    private long markBits0 = 0L;

    private int mapId = Constant.DEFAULT_INT_VALUE;
    private int rewardType = Constant.DEFAULT_INT_VALUE;
    private int blueprintId = Constant.DEFAULT_INT_VALUE;
    private int eventId = Constant.DEFAULT_INT_VALUE;
    private int rewardLevel = Constant.DEFAULT_INT_VALUE;
    private int rewardId = Constant.DEFAULT_INT_VALUE;
    private boolean finished = Constant.DEFAULT_BOOLEAN_VALUE;

    public CampaignMissionProp() {
        super(null, 0, FIELD_COUNT);
    }

    public CampaignMissionProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get mapId
     *
     * @return mapId value
     */
    public int getMapId() {
        return this.mapId;
    }

    /**
     * set mapId && set marked
     *
     * @param mapId new value
     * @return current object
     */
    public CampaignMissionProp setMapId(int mapId) {
        if (this.mapId != mapId) {
            this.mark(FIELD_INDEX_MAPID);
            this.mapId = mapId;
        }
        return this;
    }

    /**
     * inner set mapId
     *
     * @param mapId new value
     */
    private void innerSetMapId(int mapId) {
        this.mapId = mapId;
    }

    /**
     * get rewardType
     *
     * @return rewardType value
     */
    public int getRewardType() {
        return this.rewardType;
    }

    /**
     * set rewardType && set marked
     *
     * @param rewardType new value
     * @return current object
     */
    public CampaignMissionProp setRewardType(int rewardType) {
        if (this.rewardType != rewardType) {
            this.mark(FIELD_INDEX_REWARDTYPE);
            this.rewardType = rewardType;
        }
        return this;
    }

    /**
     * inner set rewardType
     *
     * @param rewardType new value
     */
    private void innerSetRewardType(int rewardType) {
        this.rewardType = rewardType;
    }

    /**
     * get blueprintId
     *
     * @return blueprintId value
     */
    public int getBlueprintId() {
        return this.blueprintId;
    }

    /**
     * set blueprintId && set marked
     *
     * @param blueprintId new value
     * @return current object
     */
    public CampaignMissionProp setBlueprintId(int blueprintId) {
        if (this.blueprintId != blueprintId) {
            this.mark(FIELD_INDEX_BLUEPRINTID);
            this.blueprintId = blueprintId;
        }
        return this;
    }

    /**
     * inner set blueprintId
     *
     * @param blueprintId new value
     */
    private void innerSetBlueprintId(int blueprintId) {
        this.blueprintId = blueprintId;
    }

    /**
     * get eventId
     *
     * @return eventId value
     */
    public int getEventId() {
        return this.eventId;
    }

    /**
     * set eventId && set marked
     *
     * @param eventId new value
     * @return current object
     */
    public CampaignMissionProp setEventId(int eventId) {
        if (this.eventId != eventId) {
            this.mark(FIELD_INDEX_EVENTID);
            this.eventId = eventId;
        }
        return this;
    }

    /**
     * inner set eventId
     *
     * @param eventId new value
     */
    private void innerSetEventId(int eventId) {
        this.eventId = eventId;
    }

    /**
     * get rewardLevel
     *
     * @return rewardLevel value
     */
    public int getRewardLevel() {
        return this.rewardLevel;
    }

    /**
     * set rewardLevel && set marked
     *
     * @param rewardLevel new value
     * @return current object
     */
    public CampaignMissionProp setRewardLevel(int rewardLevel) {
        if (this.rewardLevel != rewardLevel) {
            this.mark(FIELD_INDEX_REWARDLEVEL);
            this.rewardLevel = rewardLevel;
        }
        return this;
    }

    /**
     * inner set rewardLevel
     *
     * @param rewardLevel new value
     */
    private void innerSetRewardLevel(int rewardLevel) {
        this.rewardLevel = rewardLevel;
    }

    /**
     * get rewardId
     *
     * @return rewardId value
     */
    public int getRewardId() {
        return this.rewardId;
    }

    /**
     * set rewardId && set marked
     *
     * @param rewardId new value
     * @return current object
     */
    public CampaignMissionProp setRewardId(int rewardId) {
        if (this.rewardId != rewardId) {
            this.mark(FIELD_INDEX_REWARDID);
            this.rewardId = rewardId;
        }
        return this;
    }

    /**
     * inner set rewardId
     *
     * @param rewardId new value
     */
    private void innerSetRewardId(int rewardId) {
        this.rewardId = rewardId;
    }

    /**
     * get finished
     *
     * @return finished value
     */
    public boolean getFinished() {
        return this.finished;
    }

    /**
     * set finished && set marked
     *
     * @param finished new value
     * @return current object
     */
    public CampaignMissionProp setFinished(boolean finished) {
        if (this.finished != finished) {
            this.mark(FIELD_INDEX_FINISHED);
            this.finished = finished;
        }
        return this;
    }

    /**
     * inner set finished
     *
     * @param finished new value
     */
    private void innerSetFinished(boolean finished) {
        this.finished = finished;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignMissionPB.Builder getCopyCsBuilder() {
        final CampaignMissionPB.Builder builder = CampaignMissionPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(CampaignMissionPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMapId() != 0) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }  else if (builder.hasMapId()) {
            // 清理MapId
            builder.clearMapId();
            fieldCnt++;
        }
        if (this.getRewardType() != 0) {
            builder.setRewardType(this.getRewardType());
            fieldCnt++;
        }  else if (builder.hasRewardType()) {
            // 清理RewardType
            builder.clearRewardType();
            fieldCnt++;
        }
        if (this.getBlueprintId() != 0) {
            builder.setBlueprintId(this.getBlueprintId());
            fieldCnt++;
        }  else if (builder.hasBlueprintId()) {
            // 清理BlueprintId
            builder.clearBlueprintId();
            fieldCnt++;
        }
        if (this.getEventId() != 0) {
            builder.setEventId(this.getEventId());
            fieldCnt++;
        }  else if (builder.hasEventId()) {
            // 清理EventId
            builder.clearEventId();
            fieldCnt++;
        }
        if (this.getRewardLevel() != 0) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }  else if (builder.hasRewardLevel()) {
            // 清理RewardLevel
            builder.clearRewardLevel();
            fieldCnt++;
        }
        if (this.getRewardId() != 0) {
            builder.setRewardId(this.getRewardId());
            fieldCnt++;
        }  else if (builder.hasRewardId()) {
            // 清理RewardId
            builder.clearRewardId();
            fieldCnt++;
        }
        if (this.getFinished()) {
            builder.setFinished(this.getFinished());
            fieldCnt++;
        }  else if (builder.hasFinished()) {
            // 清理Finished
            builder.clearFinished();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(CampaignMissionPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAPID)) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDTYPE)) {
            builder.setRewardType(this.getRewardType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BLUEPRINTID)) {
            builder.setBlueprintId(this.getBlueprintId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EVENTID)) {
            builder.setEventId(this.getEventId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDLEVEL)) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDID)) {
            builder.setRewardId(this.getRewardId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FINISHED)) {
            builder.setFinished(this.getFinished());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(CampaignMissionPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAPID)) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDTYPE)) {
            builder.setRewardType(this.getRewardType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BLUEPRINTID)) {
            builder.setBlueprintId(this.getBlueprintId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EVENTID)) {
            builder.setEventId(this.getEventId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDLEVEL)) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDID)) {
            builder.setRewardId(this.getRewardId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FINISHED)) {
            builder.setFinished(this.getFinished());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(CampaignMissionPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMapId()) {
            this.innerSetMapId(proto.getMapId());
        } else {
            this.innerSetMapId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardType()) {
            this.innerSetRewardType(proto.getRewardType());
        } else {
            this.innerSetRewardType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBlueprintId()) {
            this.innerSetBlueprintId(proto.getBlueprintId());
        } else {
            this.innerSetBlueprintId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEventId()) {
            this.innerSetEventId(proto.getEventId());
        } else {
            this.innerSetEventId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardLevel()) {
            this.innerSetRewardLevel(proto.getRewardLevel());
        } else {
            this.innerSetRewardLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardId()) {
            this.innerSetRewardId(proto.getRewardId());
        } else {
            this.innerSetRewardId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFinished()) {
            this.innerSetFinished(proto.getFinished());
        } else {
            this.innerSetFinished(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return CampaignMissionProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(CampaignMissionPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMapId()) {
            this.setMapId(proto.getMapId());
            fieldCnt++;
        }
        if (proto.hasRewardType()) {
            this.setRewardType(proto.getRewardType());
            fieldCnt++;
        }
        if (proto.hasBlueprintId()) {
            this.setBlueprintId(proto.getBlueprintId());
            fieldCnt++;
        }
        if (proto.hasEventId()) {
            this.setEventId(proto.getEventId());
            fieldCnt++;
        }
        if (proto.hasRewardLevel()) {
            this.setRewardLevel(proto.getRewardLevel());
            fieldCnt++;
        }
        if (proto.hasRewardId()) {
            this.setRewardId(proto.getRewardId());
            fieldCnt++;
        }
        if (proto.hasFinished()) {
            this.setFinished(proto.getFinished());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignMission.Builder getCopyDbBuilder() {
        final CampaignMission.Builder builder = CampaignMission.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(CampaignMission.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMapId() != 0) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }  else if (builder.hasMapId()) {
            // 清理MapId
            builder.clearMapId();
            fieldCnt++;
        }
        if (this.getRewardType() != 0) {
            builder.setRewardType(this.getRewardType());
            fieldCnt++;
        }  else if (builder.hasRewardType()) {
            // 清理RewardType
            builder.clearRewardType();
            fieldCnt++;
        }
        if (this.getBlueprintId() != 0) {
            builder.setBlueprintId(this.getBlueprintId());
            fieldCnt++;
        }  else if (builder.hasBlueprintId()) {
            // 清理BlueprintId
            builder.clearBlueprintId();
            fieldCnt++;
        }
        if (this.getEventId() != 0) {
            builder.setEventId(this.getEventId());
            fieldCnt++;
        }  else if (builder.hasEventId()) {
            // 清理EventId
            builder.clearEventId();
            fieldCnt++;
        }
        if (this.getRewardLevel() != 0) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }  else if (builder.hasRewardLevel()) {
            // 清理RewardLevel
            builder.clearRewardLevel();
            fieldCnt++;
        }
        if (this.getRewardId() != 0) {
            builder.setRewardId(this.getRewardId());
            fieldCnt++;
        }  else if (builder.hasRewardId()) {
            // 清理RewardId
            builder.clearRewardId();
            fieldCnt++;
        }
        if (this.getFinished()) {
            builder.setFinished(this.getFinished());
            fieldCnt++;
        }  else if (builder.hasFinished()) {
            // 清理Finished
            builder.clearFinished();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(CampaignMission.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAPID)) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDTYPE)) {
            builder.setRewardType(this.getRewardType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BLUEPRINTID)) {
            builder.setBlueprintId(this.getBlueprintId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EVENTID)) {
            builder.setEventId(this.getEventId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDLEVEL)) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDID)) {
            builder.setRewardId(this.getRewardId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FINISHED)) {
            builder.setFinished(this.getFinished());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(CampaignMission proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMapId()) {
            this.innerSetMapId(proto.getMapId());
        } else {
            this.innerSetMapId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardType()) {
            this.innerSetRewardType(proto.getRewardType());
        } else {
            this.innerSetRewardType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBlueprintId()) {
            this.innerSetBlueprintId(proto.getBlueprintId());
        } else {
            this.innerSetBlueprintId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEventId()) {
            this.innerSetEventId(proto.getEventId());
        } else {
            this.innerSetEventId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardLevel()) {
            this.innerSetRewardLevel(proto.getRewardLevel());
        } else {
            this.innerSetRewardLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardId()) {
            this.innerSetRewardId(proto.getRewardId());
        } else {
            this.innerSetRewardId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFinished()) {
            this.innerSetFinished(proto.getFinished());
        } else {
            this.innerSetFinished(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return CampaignMissionProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(CampaignMission proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMapId()) {
            this.setMapId(proto.getMapId());
            fieldCnt++;
        }
        if (proto.hasRewardType()) {
            this.setRewardType(proto.getRewardType());
            fieldCnt++;
        }
        if (proto.hasBlueprintId()) {
            this.setBlueprintId(proto.getBlueprintId());
            fieldCnt++;
        }
        if (proto.hasEventId()) {
            this.setEventId(proto.getEventId());
            fieldCnt++;
        }
        if (proto.hasRewardLevel()) {
            this.setRewardLevel(proto.getRewardLevel());
            fieldCnt++;
        }
        if (proto.hasRewardId()) {
            this.setRewardId(proto.getRewardId());
            fieldCnt++;
        }
        if (proto.hasFinished()) {
            this.setFinished(proto.getFinished());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignMission.Builder getCopySsBuilder() {
        final CampaignMission.Builder builder = CampaignMission.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(CampaignMission.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMapId() != 0) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }  else if (builder.hasMapId()) {
            // 清理MapId
            builder.clearMapId();
            fieldCnt++;
        }
        if (this.getRewardType() != 0) {
            builder.setRewardType(this.getRewardType());
            fieldCnt++;
        }  else if (builder.hasRewardType()) {
            // 清理RewardType
            builder.clearRewardType();
            fieldCnt++;
        }
        if (this.getBlueprintId() != 0) {
            builder.setBlueprintId(this.getBlueprintId());
            fieldCnt++;
        }  else if (builder.hasBlueprintId()) {
            // 清理BlueprintId
            builder.clearBlueprintId();
            fieldCnt++;
        }
        if (this.getEventId() != 0) {
            builder.setEventId(this.getEventId());
            fieldCnt++;
        }  else if (builder.hasEventId()) {
            // 清理EventId
            builder.clearEventId();
            fieldCnt++;
        }
        if (this.getRewardLevel() != 0) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }  else if (builder.hasRewardLevel()) {
            // 清理RewardLevel
            builder.clearRewardLevel();
            fieldCnt++;
        }
        if (this.getRewardId() != 0) {
            builder.setRewardId(this.getRewardId());
            fieldCnt++;
        }  else if (builder.hasRewardId()) {
            // 清理RewardId
            builder.clearRewardId();
            fieldCnt++;
        }
        if (this.getFinished()) {
            builder.setFinished(this.getFinished());
            fieldCnt++;
        }  else if (builder.hasFinished()) {
            // 清理Finished
            builder.clearFinished();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(CampaignMission.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAPID)) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDTYPE)) {
            builder.setRewardType(this.getRewardType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BLUEPRINTID)) {
            builder.setBlueprintId(this.getBlueprintId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EVENTID)) {
            builder.setEventId(this.getEventId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDLEVEL)) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDID)) {
            builder.setRewardId(this.getRewardId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FINISHED)) {
            builder.setFinished(this.getFinished());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(CampaignMission proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMapId()) {
            this.innerSetMapId(proto.getMapId());
        } else {
            this.innerSetMapId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardType()) {
            this.innerSetRewardType(proto.getRewardType());
        } else {
            this.innerSetRewardType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBlueprintId()) {
            this.innerSetBlueprintId(proto.getBlueprintId());
        } else {
            this.innerSetBlueprintId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEventId()) {
            this.innerSetEventId(proto.getEventId());
        } else {
            this.innerSetEventId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardLevel()) {
            this.innerSetRewardLevel(proto.getRewardLevel());
        } else {
            this.innerSetRewardLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardId()) {
            this.innerSetRewardId(proto.getRewardId());
        } else {
            this.innerSetRewardId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFinished()) {
            this.innerSetFinished(proto.getFinished());
        } else {
            this.innerSetFinished(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return CampaignMissionProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(CampaignMission proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMapId()) {
            this.setMapId(proto.getMapId());
            fieldCnt++;
        }
        if (proto.hasRewardType()) {
            this.setRewardType(proto.getRewardType());
            fieldCnt++;
        }
        if (proto.hasBlueprintId()) {
            this.setBlueprintId(proto.getBlueprintId());
            fieldCnt++;
        }
        if (proto.hasEventId()) {
            this.setEventId(proto.getEventId());
            fieldCnt++;
        }
        if (proto.hasRewardLevel()) {
            this.setRewardLevel(proto.getRewardLevel());
            fieldCnt++;
        }
        if (proto.hasRewardId()) {
            this.setRewardId(proto.getRewardId());
            fieldCnt++;
        }
        if (proto.hasFinished()) {
            this.setFinished(proto.getFinished());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        CampaignMission.Builder builder = CampaignMission.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof CampaignMissionProp)) {
            return false;
        }
        final CampaignMissionProp otherNode = (CampaignMissionProp) node;
        if (this.mapId != otherNode.mapId) {
            return false;
        }
        if (this.rewardType != otherNode.rewardType) {
            return false;
        }
        if (this.blueprintId != otherNode.blueprintId) {
            return false;
        }
        if (this.eventId != otherNode.eventId) {
            return false;
        }
        if (this.rewardLevel != otherNode.rewardLevel) {
            return false;
        }
        if (this.rewardId != otherNode.rewardId) {
            return false;
        }
        if (this.finished != otherNode.finished) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 57;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
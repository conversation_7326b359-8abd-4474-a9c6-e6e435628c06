package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.DisplayData;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.DisplayDataPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class DisplayDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_PARAMS = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private DisplayParamListProp params = null;

    public DisplayDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public DisplayDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get params
     *
     * @return params value
     */
    public DisplayParamListProp getParams() {
        if (this.params == null) {
            this.params = new DisplayParamListProp(this, FIELD_INDEX_PARAMS);
        }
        return this.params;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addParams(DisplayParamProp v) {
        this.getParams().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public DisplayParamProp getParamsIndex(int index) {
        return this.getParams().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public DisplayParamProp removeParams(DisplayParamProp v) {
        if (this.params == null) {
            return null;
        }
        if(this.params.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getParamsSize() {
        if (this.params == null) {
            return 0;
        }
        return this.params.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isParamsEmpty() {
        if (this.params == null) {
            return true;
        }
        return this.getParams().isEmpty();
    }

    /**
     * clear list
     */
    public void clearParams() {
        this.getParams().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public DisplayParamProp removeParamsIndex(int index) {
        return this.getParams().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public DisplayParamProp setParamsIndex(int index, DisplayParamProp v) {
        return this.getParams().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DisplayDataPB.Builder getCopyCsBuilder() {
        final DisplayDataPB.Builder builder = DisplayDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(DisplayDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.params != null) {
            StructPB.DisplayParamListPB.Builder tmpBuilder = StructPB.DisplayParamListPB.newBuilder();
            final int tmpFieldCnt = this.params.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setParams(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearParams();
            }
        }  else if (builder.hasParams()) {
            // 清理Params
            builder.clearParams();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(DisplayDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PARAMS) && this.params != null) {
            final boolean needClear = !builder.hasParams();
            final int tmpFieldCnt = this.params.copyChangeToCs(builder.getParamsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearParams();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(DisplayDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PARAMS) && this.params != null) {
            final boolean needClear = !builder.hasParams();
            final int tmpFieldCnt = this.params.copyChangeToCs(builder.getParamsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearParams();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(DisplayDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasParams()) {
            this.getParams().mergeFromCs(proto.getParams());
        } else {
            if (this.params != null) {
                this.params.mergeFromCs(proto.getParams());
            }
        }
        this.markAll();
        return DisplayDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(DisplayDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasParams()) {
            this.getParams().mergeChangeFromCs(proto.getParams());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DisplayData.Builder getCopyDbBuilder() {
        final DisplayData.Builder builder = DisplayData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(DisplayData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.params != null) {
            Struct.DisplayParamList.Builder tmpBuilder = Struct.DisplayParamList.newBuilder();
            final int tmpFieldCnt = this.params.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setParams(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearParams();
            }
        }  else if (builder.hasParams()) {
            // 清理Params
            builder.clearParams();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(DisplayData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PARAMS) && this.params != null) {
            final boolean needClear = !builder.hasParams();
            final int tmpFieldCnt = this.params.copyChangeToDb(builder.getParamsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearParams();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(DisplayData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasParams()) {
            this.getParams().mergeFromDb(proto.getParams());
        } else {
            if (this.params != null) {
                this.params.mergeFromDb(proto.getParams());
            }
        }
        this.markAll();
        return DisplayDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(DisplayData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasParams()) {
            this.getParams().mergeChangeFromDb(proto.getParams());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DisplayData.Builder getCopySsBuilder() {
        final DisplayData.Builder builder = DisplayData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(DisplayData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.params != null) {
            Struct.DisplayParamList.Builder tmpBuilder = Struct.DisplayParamList.newBuilder();
            final int tmpFieldCnt = this.params.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setParams(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearParams();
            }
        }  else if (builder.hasParams()) {
            // 清理Params
            builder.clearParams();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(DisplayData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PARAMS) && this.params != null) {
            final boolean needClear = !builder.hasParams();
            final int tmpFieldCnt = this.params.copyChangeToSs(builder.getParamsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearParams();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(DisplayData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasParams()) {
            this.getParams().mergeFromSs(proto.getParams());
        } else {
            if (this.params != null) {
                this.params.mergeFromSs(proto.getParams());
            }
        }
        this.markAll();
        return DisplayDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(DisplayData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasParams()) {
            this.getParams().mergeChangeFromSs(proto.getParams());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        DisplayData.Builder builder = DisplayData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_PARAMS) && this.params != null) {
            this.params.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.params != null) {
            this.params.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof DisplayDataProp)) {
            return false;
        }
        final DisplayDataProp otherNode = (DisplayDataProp) node;
        if (!this.getParams().compareDataTo(otherNode.getParams())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
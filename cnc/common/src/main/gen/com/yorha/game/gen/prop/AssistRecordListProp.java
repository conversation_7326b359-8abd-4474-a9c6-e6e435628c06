package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructPB.AssistRecordListPB;
import com.yorha.proto.Struct.AssistRecordList;
import com.yorha.proto.StructPB.AssistRecordPB;
import com.yorha.proto.Struct.AssistRecord;

/**
 * <AUTHOR> auto gen
 */
public class AssistRecordListProp extends AbstractListNode<AssistRecordProp> {
    /**
     * Create a AssistRecordListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public AssistRecordListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to AssistRecordListProp
     *
     * @return new object
     */
    @Override
    public AssistRecordProp addEmptyValue() {
        final AssistRecordProp newProp = new AssistRecordProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public AssistRecordListPB.Builder getCopyCsBuilder() {
        final AssistRecordListPB.Builder builder = AssistRecordListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(AssistRecordListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return AssistRecordListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final AssistRecordProp v : this) {
            final AssistRecordPB.Builder itemBuilder = AssistRecordPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return AssistRecordListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(AssistRecordListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return AssistRecordListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(AssistRecordListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (AssistRecordPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return AssistRecordListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(AssistRecordListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public AssistRecordList.Builder getCopyDbBuilder() {
        final AssistRecordList.Builder builder = AssistRecordList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(AssistRecordList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return AssistRecordListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final AssistRecordProp v : this) {
            final AssistRecord.Builder itemBuilder = AssistRecord.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return AssistRecordListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(AssistRecordList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return AssistRecordListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(AssistRecordList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (AssistRecord v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return AssistRecordListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(AssistRecordList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public AssistRecordList.Builder getCopySsBuilder() {
        final AssistRecordList.Builder builder = AssistRecordList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(AssistRecordList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return AssistRecordListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final AssistRecordProp v : this) {
            final AssistRecord.Builder itemBuilder = AssistRecord.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return AssistRecordListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(AssistRecordList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return AssistRecordListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(AssistRecordList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (AssistRecord v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return AssistRecordListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(AssistRecordList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        AssistRecordList.Builder builder = AssistRecordList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
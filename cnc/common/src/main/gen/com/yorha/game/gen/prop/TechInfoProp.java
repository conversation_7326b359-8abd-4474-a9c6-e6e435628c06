package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.TechInfo;
import com.yorha.proto.StructPB.TechInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class TechInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_TECHID = 0;
    public static final int FIELD_INDEX_TECHSUBID = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int techId = Constant.DEFAULT_INT_VALUE;
    private int techSubId = Constant.DEFAULT_INT_VALUE;

    public TechInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public TechInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get techId
     *
     * @return techId value
     */
    public int getTechId() {
        return this.techId;
    }

    /**
     * set techId && set marked
     *
     * @param techId new value
     * @return current object
     */
    public TechInfoProp setTechId(int techId) {
        if (this.techId != techId) {
            this.mark(FIELD_INDEX_TECHID);
            this.techId = techId;
        }
        return this;
    }

    /**
     * inner set techId
     *
     * @param techId new value
     */
    private void innerSetTechId(int techId) {
        this.techId = techId;
    }

    /**
     * get techSubId
     *
     * @return techSubId value
     */
    public int getTechSubId() {
        return this.techSubId;
    }

    /**
     * set techSubId && set marked
     *
     * @param techSubId new value
     * @return current object
     */
    public TechInfoProp setTechSubId(int techSubId) {
        if (this.techSubId != techSubId) {
            this.mark(FIELD_INDEX_TECHSUBID);
            this.techSubId = techSubId;
        }
        return this;
    }

    /**
     * inner set techSubId
     *
     * @param techSubId new value
     */
    private void innerSetTechSubId(int techSubId) {
        this.techSubId = techSubId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TechInfoPB.Builder getCopyCsBuilder() {
        final TechInfoPB.Builder builder = TechInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(TechInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTechId() != 0) {
            builder.setTechId(this.getTechId());
            fieldCnt++;
        }  else if (builder.hasTechId()) {
            // 清理TechId
            builder.clearTechId();
            fieldCnt++;
        }
        if (this.getTechSubId() != 0) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }  else if (builder.hasTechSubId()) {
            // 清理TechSubId
            builder.clearTechSubId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(TechInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TECHID)) {
            builder.setTechId(this.getTechId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TECHSUBID)) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(TechInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TECHID)) {
            builder.setTechId(this.getTechId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TECHSUBID)) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(TechInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTechId()) {
            this.innerSetTechId(proto.getTechId());
        } else {
            this.innerSetTechId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTechSubId()) {
            this.innerSetTechSubId(proto.getTechSubId());
        } else {
            this.innerSetTechSubId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return TechInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(TechInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTechId()) {
            this.setTechId(proto.getTechId());
            fieldCnt++;
        }
        if (proto.hasTechSubId()) {
            this.setTechSubId(proto.getTechSubId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TechInfo.Builder getCopyDbBuilder() {
        final TechInfo.Builder builder = TechInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(TechInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTechId() != 0) {
            builder.setTechId(this.getTechId());
            fieldCnt++;
        }  else if (builder.hasTechId()) {
            // 清理TechId
            builder.clearTechId();
            fieldCnt++;
        }
        if (this.getTechSubId() != 0) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }  else if (builder.hasTechSubId()) {
            // 清理TechSubId
            builder.clearTechSubId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(TechInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TECHID)) {
            builder.setTechId(this.getTechId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TECHSUBID)) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(TechInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTechId()) {
            this.innerSetTechId(proto.getTechId());
        } else {
            this.innerSetTechId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTechSubId()) {
            this.innerSetTechSubId(proto.getTechSubId());
        } else {
            this.innerSetTechSubId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return TechInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(TechInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTechId()) {
            this.setTechId(proto.getTechId());
            fieldCnt++;
        }
        if (proto.hasTechSubId()) {
            this.setTechSubId(proto.getTechSubId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TechInfo.Builder getCopySsBuilder() {
        final TechInfo.Builder builder = TechInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(TechInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTechId() != 0) {
            builder.setTechId(this.getTechId());
            fieldCnt++;
        }  else if (builder.hasTechId()) {
            // 清理TechId
            builder.clearTechId();
            fieldCnt++;
        }
        if (this.getTechSubId() != 0) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }  else if (builder.hasTechSubId()) {
            // 清理TechSubId
            builder.clearTechSubId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(TechInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TECHID)) {
            builder.setTechId(this.getTechId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TECHSUBID)) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(TechInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTechId()) {
            this.innerSetTechId(proto.getTechId());
        } else {
            this.innerSetTechId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTechSubId()) {
            this.innerSetTechSubId(proto.getTechSubId());
        } else {
            this.innerSetTechSubId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return TechInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(TechInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTechId()) {
            this.setTechId(proto.getTechId());
            fieldCnt++;
        }
        if (proto.hasTechSubId()) {
            this.setTechSubId(proto.getTechSubId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        TechInfo.Builder builder = TechInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.techId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof TechInfoProp)) {
            return false;
        }
        final TechInfoProp otherNode = (TechInfoProp) node;
        if (this.techId != otherNode.techId) {
            return false;
        }
        if (this.techSubId != otherNode.techSubId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.YoTest.YoTestUnit;
import com.yorha.proto.YoTestPB.YoTestUnitPB;


/**
 * <AUTHOR> auto gen
 */
public class YoTestUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_INTFIELD = 0;
    public static final int FIELD_INDEX_LONGFIELD = 1;
    public static final int FIELD_INDEX_STRINGFIELD = 2;
    public static final int FIELD_INDEX_ENUMFIELD = 3;
    public static final int FIELD_INDEX_BOOLFIELD = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private int intField = Constant.DEFAULT_INT_VALUE;
    private long longField = Constant.DEFAULT_LONG_VALUE;
    private String stringField = Constant.DEFAULT_STR_VALUE;
    private YoTestEnumType enumField = YoTestEnumType.forNumber(0);
    private boolean boolField = Constant.DEFAULT_BOOLEAN_VALUE;

    public YoTestUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public YoTestUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get intField
     *
     * @return intField value
     */
    public int getIntField() {
        return this.intField;
    }

    /**
     * set intField && set marked
     *
     * @param intField new value
     * @return current object
     */
    public YoTestUnitProp setIntField(int intField) {
        if (this.intField != intField) {
            this.mark(FIELD_INDEX_INTFIELD);
            this.intField = intField;
        }
        return this;
    }

    /**
     * inner set intField
     *
     * @param intField new value
     */
    private void innerSetIntField(int intField) {
        this.intField = intField;
    }

    /**
     * get longField
     *
     * @return longField value
     */
    public long getLongField() {
        return this.longField;
    }

    /**
     * set longField && set marked
     *
     * @param longField new value
     * @return current object
     */
    public YoTestUnitProp setLongField(long longField) {
        if (this.longField != longField) {
            this.mark(FIELD_INDEX_LONGFIELD);
            this.longField = longField;
        }
        return this;
    }

    /**
     * inner set longField
     *
     * @param longField new value
     */
    private void innerSetLongField(long longField) {
        this.longField = longField;
    }

    /**
     * get stringField
     *
     * @return stringField value
     */
    public String getStringField() {
        return this.stringField;
    }

    /**
     * set stringField && set marked
     *
     * @param stringField new value
     * @return current object
     */
    public YoTestUnitProp setStringField(String stringField) {
        if (stringField == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.stringField, stringField)) {
            this.mark(FIELD_INDEX_STRINGFIELD);
            this.stringField = stringField;
        }
        return this;
    }

    /**
     * inner set stringField
     *
     * @param stringField new value
     */
    private void innerSetStringField(String stringField) {
        this.stringField = stringField;
    }

    /**
     * get enumField
     *
     * @return enumField value
     */
    public YoTestEnumType getEnumField() {
        return this.enumField;
    }

    /**
     * set enumField && set marked
     *
     * @param enumField new value
     * @return current object
     */
    public YoTestUnitProp setEnumField(YoTestEnumType enumField) {
        if (enumField == null) {
            throw new NullPointerException();
        }
        if (this.enumField != enumField) {
            this.mark(FIELD_INDEX_ENUMFIELD);
            this.enumField = enumField;
        }
        return this;
    }

    /**
     * inner set enumField
     *
     * @param enumField new value
     */
    private void innerSetEnumField(YoTestEnumType enumField) {
        this.enumField = enumField;
    }

    /**
     * get boolField
     *
     * @return boolField value
     */
    public boolean getBoolField() {
        return this.boolField;
    }

    /**
     * set boolField && set marked
     *
     * @param boolField new value
     * @return current object
     */
    public YoTestUnitProp setBoolField(boolean boolField) {
        if (this.boolField != boolField) {
            this.mark(FIELD_INDEX_BOOLFIELD);
            this.boolField = boolField;
        }
        return this;
    }

    /**
     * inner set boolField
     *
     * @param boolField new value
     */
    private void innerSetBoolField(boolean boolField) {
        this.boolField = boolField;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestUnitPB.Builder getCopyCsBuilder() {
        final YoTestUnitPB.Builder builder = YoTestUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(YoTestUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIntField() != 0) {
            builder.setIntField(this.getIntField());
            fieldCnt++;
        }  else if (builder.hasIntField()) {
            // 清理IntField
            builder.clearIntField();
            fieldCnt++;
        }
        if (this.getLongField() != 0L) {
            builder.setLongField(this.getLongField());
            fieldCnt++;
        }  else if (builder.hasLongField()) {
            // 清理LongField
            builder.clearLongField();
            fieldCnt++;
        }
        if (!this.getStringField().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setStringField(this.getStringField());
            fieldCnt++;
        }  else if (builder.hasStringField()) {
            // 清理StringField
            builder.clearStringField();
            fieldCnt++;
        }
        if (this.getEnumField() != YoTestEnumType.forNumber(0)) {
            builder.setEnumField(this.getEnumField());
            fieldCnt++;
        }  else if (builder.hasEnumField()) {
            // 清理EnumField
            builder.clearEnumField();
            fieldCnt++;
        }
        if (this.getBoolField()) {
            builder.setBoolField(this.getBoolField());
            fieldCnt++;
        }  else if (builder.hasBoolField()) {
            // 清理BoolField
            builder.clearBoolField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(YoTestUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INTFIELD)) {
            builder.setIntField(this.getIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LONGFIELD)) {
            builder.setLongField(this.getLongField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STRINGFIELD)) {
            builder.setStringField(this.getStringField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENUMFIELD)) {
            builder.setEnumField(this.getEnumField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOOLFIELD)) {
            builder.setBoolField(this.getBoolField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(YoTestUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INTFIELD)) {
            builder.setIntField(this.getIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LONGFIELD)) {
            builder.setLongField(this.getLongField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STRINGFIELD)) {
            builder.setStringField(this.getStringField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENUMFIELD)) {
            builder.setEnumField(this.getEnumField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOOLFIELD)) {
            builder.setBoolField(this.getBoolField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(YoTestUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIntField()) {
            this.innerSetIntField(proto.getIntField());
        } else {
            this.innerSetIntField(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLongField()) {
            this.innerSetLongField(proto.getLongField());
        } else {
            this.innerSetLongField(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStringField()) {
            this.innerSetStringField(proto.getStringField());
        } else {
            this.innerSetStringField(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasEnumField()) {
            this.innerSetEnumField(proto.getEnumField());
        } else {
            this.innerSetEnumField(YoTestEnumType.forNumber(0));
        }
        if (proto.hasBoolField()) {
            this.innerSetBoolField(proto.getBoolField());
        } else {
            this.innerSetBoolField(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return YoTestUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(YoTestUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIntField()) {
            this.setIntField(proto.getIntField());
            fieldCnt++;
        }
        if (proto.hasLongField()) {
            this.setLongField(proto.getLongField());
            fieldCnt++;
        }
        if (proto.hasStringField()) {
            this.setStringField(proto.getStringField());
            fieldCnt++;
        }
        if (proto.hasEnumField()) {
            this.setEnumField(proto.getEnumField());
            fieldCnt++;
        }
        if (proto.hasBoolField()) {
            this.setBoolField(proto.getBoolField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestUnit.Builder getCopyDbBuilder() {
        final YoTestUnit.Builder builder = YoTestUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(YoTestUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIntField() != 0) {
            builder.setIntField(this.getIntField());
            fieldCnt++;
        }  else if (builder.hasIntField()) {
            // 清理IntField
            builder.clearIntField();
            fieldCnt++;
        }
        if (this.getLongField() != 0L) {
            builder.setLongField(this.getLongField());
            fieldCnt++;
        }  else if (builder.hasLongField()) {
            // 清理LongField
            builder.clearLongField();
            fieldCnt++;
        }
        if (!this.getStringField().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setStringField(this.getStringField());
            fieldCnt++;
        }  else if (builder.hasStringField()) {
            // 清理StringField
            builder.clearStringField();
            fieldCnt++;
        }
        if (this.getEnumField() != YoTestEnumType.forNumber(0)) {
            builder.setEnumField(this.getEnumField());
            fieldCnt++;
        }  else if (builder.hasEnumField()) {
            // 清理EnumField
            builder.clearEnumField();
            fieldCnt++;
        }
        if (this.getBoolField()) {
            builder.setBoolField(this.getBoolField());
            fieldCnt++;
        }  else if (builder.hasBoolField()) {
            // 清理BoolField
            builder.clearBoolField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(YoTestUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INTFIELD)) {
            builder.setIntField(this.getIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LONGFIELD)) {
            builder.setLongField(this.getLongField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STRINGFIELD)) {
            builder.setStringField(this.getStringField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENUMFIELD)) {
            builder.setEnumField(this.getEnumField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOOLFIELD)) {
            builder.setBoolField(this.getBoolField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(YoTestUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIntField()) {
            this.innerSetIntField(proto.getIntField());
        } else {
            this.innerSetIntField(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLongField()) {
            this.innerSetLongField(proto.getLongField());
        } else {
            this.innerSetLongField(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStringField()) {
            this.innerSetStringField(proto.getStringField());
        } else {
            this.innerSetStringField(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasEnumField()) {
            this.innerSetEnumField(proto.getEnumField());
        } else {
            this.innerSetEnumField(YoTestEnumType.forNumber(0));
        }
        if (proto.hasBoolField()) {
            this.innerSetBoolField(proto.getBoolField());
        } else {
            this.innerSetBoolField(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return YoTestUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(YoTestUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIntField()) {
            this.setIntField(proto.getIntField());
            fieldCnt++;
        }
        if (proto.hasLongField()) {
            this.setLongField(proto.getLongField());
            fieldCnt++;
        }
        if (proto.hasStringField()) {
            this.setStringField(proto.getStringField());
            fieldCnt++;
        }
        if (proto.hasEnumField()) {
            this.setEnumField(proto.getEnumField());
            fieldCnt++;
        }
        if (proto.hasBoolField()) {
            this.setBoolField(proto.getBoolField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestUnit.Builder getCopySsBuilder() {
        final YoTestUnit.Builder builder = YoTestUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(YoTestUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIntField() != 0) {
            builder.setIntField(this.getIntField());
            fieldCnt++;
        }  else if (builder.hasIntField()) {
            // 清理IntField
            builder.clearIntField();
            fieldCnt++;
        }
        if (this.getLongField() != 0L) {
            builder.setLongField(this.getLongField());
            fieldCnt++;
        }  else if (builder.hasLongField()) {
            // 清理LongField
            builder.clearLongField();
            fieldCnt++;
        }
        if (!this.getStringField().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setStringField(this.getStringField());
            fieldCnt++;
        }  else if (builder.hasStringField()) {
            // 清理StringField
            builder.clearStringField();
            fieldCnt++;
        }
        if (this.getEnumField() != YoTestEnumType.forNumber(0)) {
            builder.setEnumField(this.getEnumField());
            fieldCnt++;
        }  else if (builder.hasEnumField()) {
            // 清理EnumField
            builder.clearEnumField();
            fieldCnt++;
        }
        if (this.getBoolField()) {
            builder.setBoolField(this.getBoolField());
            fieldCnt++;
        }  else if (builder.hasBoolField()) {
            // 清理BoolField
            builder.clearBoolField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(YoTestUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INTFIELD)) {
            builder.setIntField(this.getIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LONGFIELD)) {
            builder.setLongField(this.getLongField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STRINGFIELD)) {
            builder.setStringField(this.getStringField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENUMFIELD)) {
            builder.setEnumField(this.getEnumField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOOLFIELD)) {
            builder.setBoolField(this.getBoolField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(YoTestUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIntField()) {
            this.innerSetIntField(proto.getIntField());
        } else {
            this.innerSetIntField(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLongField()) {
            this.innerSetLongField(proto.getLongField());
        } else {
            this.innerSetLongField(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStringField()) {
            this.innerSetStringField(proto.getStringField());
        } else {
            this.innerSetStringField(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasEnumField()) {
            this.innerSetEnumField(proto.getEnumField());
        } else {
            this.innerSetEnumField(YoTestEnumType.forNumber(0));
        }
        if (proto.hasBoolField()) {
            this.innerSetBoolField(proto.getBoolField());
        } else {
            this.innerSetBoolField(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return YoTestUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(YoTestUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIntField()) {
            this.setIntField(proto.getIntField());
            fieldCnt++;
        }
        if (proto.hasLongField()) {
            this.setLongField(proto.getLongField());
            fieldCnt++;
        }
        if (proto.hasStringField()) {
            this.setStringField(proto.getStringField());
            fieldCnt++;
        }
        if (proto.hasEnumField()) {
            this.setEnumField(proto.getEnumField());
            fieldCnt++;
        }
        if (proto.hasBoolField()) {
            this.setBoolField(proto.getBoolField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        YoTestUnit.Builder builder = YoTestUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof YoTestUnitProp)) {
            return false;
        }
        final YoTestUnitProp otherNode = (YoTestUnitProp) node;
        if (this.intField != otherNode.intField) {
            return false;
        }
        if (this.longField != otherNode.longField) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.stringField, otherNode.stringField)) {
            return false;
        }
        if (this.enumField != otherNode.enumField) {
            return false;
        }
        if (this.boolField != otherNode.boolField) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.RefreshInfo;
import com.yorha.proto.PlayerPB.RefreshInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class RefreshInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_DAYREFRESHTSMS = 0;
    public static final int FIELD_INDEX_WEEKREFRESHTSMS = 1;
    public static final int FIELD_INDEX_NEXTDAYREFRESHTSMS = 2;
    public static final int FIELD_INDEX_NEXTWEEKREFRESHTSMS = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private long dayRefreshTsMs = Constant.DEFAULT_LONG_VALUE;
    private long weekRefreshTsMs = Constant.DEFAULT_LONG_VALUE;
    private long nextDayRefreshTsMs = Constant.DEFAULT_LONG_VALUE;
    private long nextWeekRefreshTsMs = Constant.DEFAULT_LONG_VALUE;

    public RefreshInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public RefreshInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get dayRefreshTsMs
     *
     * @return dayRefreshTsMs value
     */
    public long getDayRefreshTsMs() {
        return this.dayRefreshTsMs;
    }

    /**
     * set dayRefreshTsMs && set marked
     *
     * @param dayRefreshTsMs new value
     * @return current object
     */
    public RefreshInfoProp setDayRefreshTsMs(long dayRefreshTsMs) {
        if (this.dayRefreshTsMs != dayRefreshTsMs) {
            this.mark(FIELD_INDEX_DAYREFRESHTSMS);
            this.dayRefreshTsMs = dayRefreshTsMs;
        }
        return this;
    }

    /**
     * inner set dayRefreshTsMs
     *
     * @param dayRefreshTsMs new value
     */
    private void innerSetDayRefreshTsMs(long dayRefreshTsMs) {
        this.dayRefreshTsMs = dayRefreshTsMs;
    }

    /**
     * get weekRefreshTsMs
     *
     * @return weekRefreshTsMs value
     */
    public long getWeekRefreshTsMs() {
        return this.weekRefreshTsMs;
    }

    /**
     * set weekRefreshTsMs && set marked
     *
     * @param weekRefreshTsMs new value
     * @return current object
     */
    public RefreshInfoProp setWeekRefreshTsMs(long weekRefreshTsMs) {
        if (this.weekRefreshTsMs != weekRefreshTsMs) {
            this.mark(FIELD_INDEX_WEEKREFRESHTSMS);
            this.weekRefreshTsMs = weekRefreshTsMs;
        }
        return this;
    }

    /**
     * inner set weekRefreshTsMs
     *
     * @param weekRefreshTsMs new value
     */
    private void innerSetWeekRefreshTsMs(long weekRefreshTsMs) {
        this.weekRefreshTsMs = weekRefreshTsMs;
    }

    /**
     * get nextDayRefreshTsMs
     *
     * @return nextDayRefreshTsMs value
     */
    public long getNextDayRefreshTsMs() {
        return this.nextDayRefreshTsMs;
    }

    /**
     * set nextDayRefreshTsMs && set marked
     *
     * @param nextDayRefreshTsMs new value
     * @return current object
     */
    public RefreshInfoProp setNextDayRefreshTsMs(long nextDayRefreshTsMs) {
        if (this.nextDayRefreshTsMs != nextDayRefreshTsMs) {
            this.mark(FIELD_INDEX_NEXTDAYREFRESHTSMS);
            this.nextDayRefreshTsMs = nextDayRefreshTsMs;
        }
        return this;
    }

    /**
     * inner set nextDayRefreshTsMs
     *
     * @param nextDayRefreshTsMs new value
     */
    private void innerSetNextDayRefreshTsMs(long nextDayRefreshTsMs) {
        this.nextDayRefreshTsMs = nextDayRefreshTsMs;
    }

    /**
     * get nextWeekRefreshTsMs
     *
     * @return nextWeekRefreshTsMs value
     */
    public long getNextWeekRefreshTsMs() {
        return this.nextWeekRefreshTsMs;
    }

    /**
     * set nextWeekRefreshTsMs && set marked
     *
     * @param nextWeekRefreshTsMs new value
     * @return current object
     */
    public RefreshInfoProp setNextWeekRefreshTsMs(long nextWeekRefreshTsMs) {
        if (this.nextWeekRefreshTsMs != nextWeekRefreshTsMs) {
            this.mark(FIELD_INDEX_NEXTWEEKREFRESHTSMS);
            this.nextWeekRefreshTsMs = nextWeekRefreshTsMs;
        }
        return this;
    }

    /**
     * inner set nextWeekRefreshTsMs
     *
     * @param nextWeekRefreshTsMs new value
     */
    private void innerSetNextWeekRefreshTsMs(long nextWeekRefreshTsMs) {
        this.nextWeekRefreshTsMs = nextWeekRefreshTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RefreshInfoPB.Builder getCopyCsBuilder() {
        final RefreshInfoPB.Builder builder = RefreshInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(RefreshInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getNextDayRefreshTsMs() != 0L) {
            builder.setNextDayRefreshTsMs(this.getNextDayRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasNextDayRefreshTsMs()) {
            // 清理NextDayRefreshTsMs
            builder.clearNextDayRefreshTsMs();
            fieldCnt++;
        }
        if (this.getNextWeekRefreshTsMs() != 0L) {
            builder.setNextWeekRefreshTsMs(this.getNextWeekRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasNextWeekRefreshTsMs()) {
            // 清理NextWeekRefreshTsMs
            builder.clearNextWeekRefreshTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(RefreshInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_NEXTDAYREFRESHTSMS)) {
            builder.setNextDayRefreshTsMs(this.getNextDayRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTWEEKREFRESHTSMS)) {
            builder.setNextWeekRefreshTsMs(this.getNextWeekRefreshTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(RefreshInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_NEXTDAYREFRESHTSMS)) {
            builder.setNextDayRefreshTsMs(this.getNextDayRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTWEEKREFRESHTSMS)) {
            builder.setNextWeekRefreshTsMs(this.getNextWeekRefreshTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(RefreshInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasNextDayRefreshTsMs()) {
            this.innerSetNextDayRefreshTsMs(proto.getNextDayRefreshTsMs());
        } else {
            this.innerSetNextDayRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasNextWeekRefreshTsMs()) {
            this.innerSetNextWeekRefreshTsMs(proto.getNextWeekRefreshTsMs());
        } else {
            this.innerSetNextWeekRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return RefreshInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(RefreshInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasNextDayRefreshTsMs()) {
            this.setNextDayRefreshTsMs(proto.getNextDayRefreshTsMs());
            fieldCnt++;
        }
        if (proto.hasNextWeekRefreshTsMs()) {
            this.setNextWeekRefreshTsMs(proto.getNextWeekRefreshTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RefreshInfo.Builder getCopyDbBuilder() {
        final RefreshInfo.Builder builder = RefreshInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(RefreshInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDayRefreshTsMs() != 0L) {
            builder.setDayRefreshTsMs(this.getDayRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasDayRefreshTsMs()) {
            // 清理DayRefreshTsMs
            builder.clearDayRefreshTsMs();
            fieldCnt++;
        }
        if (this.getWeekRefreshTsMs() != 0L) {
            builder.setWeekRefreshTsMs(this.getWeekRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasWeekRefreshTsMs()) {
            // 清理WeekRefreshTsMs
            builder.clearWeekRefreshTsMs();
            fieldCnt++;
        }
        if (this.getNextDayRefreshTsMs() != 0L) {
            builder.setNextDayRefreshTsMs(this.getNextDayRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasNextDayRefreshTsMs()) {
            // 清理NextDayRefreshTsMs
            builder.clearNextDayRefreshTsMs();
            fieldCnt++;
        }
        if (this.getNextWeekRefreshTsMs() != 0L) {
            builder.setNextWeekRefreshTsMs(this.getNextWeekRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasNextWeekRefreshTsMs()) {
            // 清理NextWeekRefreshTsMs
            builder.clearNextWeekRefreshTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(RefreshInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DAYREFRESHTSMS)) {
            builder.setDayRefreshTsMs(this.getDayRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WEEKREFRESHTSMS)) {
            builder.setWeekRefreshTsMs(this.getWeekRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTDAYREFRESHTSMS)) {
            builder.setNextDayRefreshTsMs(this.getNextDayRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTWEEKREFRESHTSMS)) {
            builder.setNextWeekRefreshTsMs(this.getNextWeekRefreshTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(RefreshInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDayRefreshTsMs()) {
            this.innerSetDayRefreshTsMs(proto.getDayRefreshTsMs());
        } else {
            this.innerSetDayRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasWeekRefreshTsMs()) {
            this.innerSetWeekRefreshTsMs(proto.getWeekRefreshTsMs());
        } else {
            this.innerSetWeekRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasNextDayRefreshTsMs()) {
            this.innerSetNextDayRefreshTsMs(proto.getNextDayRefreshTsMs());
        } else {
            this.innerSetNextDayRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasNextWeekRefreshTsMs()) {
            this.innerSetNextWeekRefreshTsMs(proto.getNextWeekRefreshTsMs());
        } else {
            this.innerSetNextWeekRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return RefreshInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(RefreshInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDayRefreshTsMs()) {
            this.setDayRefreshTsMs(proto.getDayRefreshTsMs());
            fieldCnt++;
        }
        if (proto.hasWeekRefreshTsMs()) {
            this.setWeekRefreshTsMs(proto.getWeekRefreshTsMs());
            fieldCnt++;
        }
        if (proto.hasNextDayRefreshTsMs()) {
            this.setNextDayRefreshTsMs(proto.getNextDayRefreshTsMs());
            fieldCnt++;
        }
        if (proto.hasNextWeekRefreshTsMs()) {
            this.setNextWeekRefreshTsMs(proto.getNextWeekRefreshTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RefreshInfo.Builder getCopySsBuilder() {
        final RefreshInfo.Builder builder = RefreshInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(RefreshInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDayRefreshTsMs() != 0L) {
            builder.setDayRefreshTsMs(this.getDayRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasDayRefreshTsMs()) {
            // 清理DayRefreshTsMs
            builder.clearDayRefreshTsMs();
            fieldCnt++;
        }
        if (this.getWeekRefreshTsMs() != 0L) {
            builder.setWeekRefreshTsMs(this.getWeekRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasWeekRefreshTsMs()) {
            // 清理WeekRefreshTsMs
            builder.clearWeekRefreshTsMs();
            fieldCnt++;
        }
        if (this.getNextDayRefreshTsMs() != 0L) {
            builder.setNextDayRefreshTsMs(this.getNextDayRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasNextDayRefreshTsMs()) {
            // 清理NextDayRefreshTsMs
            builder.clearNextDayRefreshTsMs();
            fieldCnt++;
        }
        if (this.getNextWeekRefreshTsMs() != 0L) {
            builder.setNextWeekRefreshTsMs(this.getNextWeekRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasNextWeekRefreshTsMs()) {
            // 清理NextWeekRefreshTsMs
            builder.clearNextWeekRefreshTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(RefreshInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DAYREFRESHTSMS)) {
            builder.setDayRefreshTsMs(this.getDayRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WEEKREFRESHTSMS)) {
            builder.setWeekRefreshTsMs(this.getWeekRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTDAYREFRESHTSMS)) {
            builder.setNextDayRefreshTsMs(this.getNextDayRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTWEEKREFRESHTSMS)) {
            builder.setNextWeekRefreshTsMs(this.getNextWeekRefreshTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(RefreshInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDayRefreshTsMs()) {
            this.innerSetDayRefreshTsMs(proto.getDayRefreshTsMs());
        } else {
            this.innerSetDayRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasWeekRefreshTsMs()) {
            this.innerSetWeekRefreshTsMs(proto.getWeekRefreshTsMs());
        } else {
            this.innerSetWeekRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasNextDayRefreshTsMs()) {
            this.innerSetNextDayRefreshTsMs(proto.getNextDayRefreshTsMs());
        } else {
            this.innerSetNextDayRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasNextWeekRefreshTsMs()) {
            this.innerSetNextWeekRefreshTsMs(proto.getNextWeekRefreshTsMs());
        } else {
            this.innerSetNextWeekRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return RefreshInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(RefreshInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDayRefreshTsMs()) {
            this.setDayRefreshTsMs(proto.getDayRefreshTsMs());
            fieldCnt++;
        }
        if (proto.hasWeekRefreshTsMs()) {
            this.setWeekRefreshTsMs(proto.getWeekRefreshTsMs());
            fieldCnt++;
        }
        if (proto.hasNextDayRefreshTsMs()) {
            this.setNextDayRefreshTsMs(proto.getNextDayRefreshTsMs());
            fieldCnt++;
        }
        if (proto.hasNextWeekRefreshTsMs()) {
            this.setNextWeekRefreshTsMs(proto.getNextWeekRefreshTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        RefreshInfo.Builder builder = RefreshInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof RefreshInfoProp)) {
            return false;
        }
        final RefreshInfoProp otherNode = (RefreshInfoProp) node;
        if (this.dayRefreshTsMs != otherNode.dayRefreshTsMs) {
            return false;
        }
        if (this.weekRefreshTsMs != otherNode.weekRefreshTsMs) {
            return false;
        }
        if (this.nextDayRefreshTsMs != otherNode.nextDayRefreshTsMs) {
            return false;
        }
        if (this.nextWeekRefreshTsMs != otherNode.nextWeekRefreshTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
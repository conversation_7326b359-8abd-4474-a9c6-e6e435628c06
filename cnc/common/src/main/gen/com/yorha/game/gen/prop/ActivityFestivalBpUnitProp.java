package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityFestivalBpUnit;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ActivityFestivalBpUnitPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityFestivalBpUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CURLEVEL = 0;
    public static final int FIELD_INDEX_SCORE = 1;
    public static final int FIELD_INDEX_BPSTATUS = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int curLevel = Constant.DEFAULT_INT_VALUE;
    private int score = Constant.DEFAULT_INT_VALUE;
    private Int32ActivityFestivalBpStatusMapProp bpStatus = null;

    public ActivityFestivalBpUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityFestivalBpUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get curLevel
     *
     * @return curLevel value
     */
    public int getCurLevel() {
        return this.curLevel;
    }

    /**
     * set curLevel && set marked
     *
     * @param curLevel new value
     * @return current object
     */
    public ActivityFestivalBpUnitProp setCurLevel(int curLevel) {
        if (this.curLevel != curLevel) {
            this.mark(FIELD_INDEX_CURLEVEL);
            this.curLevel = curLevel;
        }
        return this;
    }

    /**
     * inner set curLevel
     *
     * @param curLevel new value
     */
    private void innerSetCurLevel(int curLevel) {
        this.curLevel = curLevel;
    }

    /**
     * get score
     *
     * @return score value
     */
    public int getScore() {
        return this.score;
    }

    /**
     * set score && set marked
     *
     * @param score new value
     * @return current object
     */
    public ActivityFestivalBpUnitProp setScore(int score) {
        if (this.score != score) {
            this.mark(FIELD_INDEX_SCORE);
            this.score = score;
        }
        return this;
    }

    /**
     * inner set score
     *
     * @param score new value
     */
    private void innerSetScore(int score) {
        this.score = score;
    }

    /**
     * get bpStatus
     *
     * @return bpStatus value
     */
    public Int32ActivityFestivalBpStatusMapProp getBpStatus() {
        if (this.bpStatus == null) {
            this.bpStatus = new Int32ActivityFestivalBpStatusMapProp(this, FIELD_INDEX_BPSTATUS);
        }
        return this.bpStatus;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putBpStatusV(ActivityFestivalBpStatusProp v) {
        this.getBpStatus().put(v.getBpId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ActivityFestivalBpStatusProp addEmptyBpStatus(Integer k) {
        return this.getBpStatus().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getBpStatusSize() {
        if (this.bpStatus == null) {
            return 0;
        }
        return this.bpStatus.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isBpStatusEmpty() {
        if (this.bpStatus == null) {
            return true;
        }
        return this.bpStatus.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ActivityFestivalBpStatusProp getBpStatusV(Integer k) {
        if (this.bpStatus == null || !this.bpStatus.containsKey(k)) {
            return null;
        }
        return this.bpStatus.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearBpStatus() {
        if (this.bpStatus != null) {
            this.bpStatus.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeBpStatusV(Integer k) {
        if (this.bpStatus != null) {
            this.bpStatus.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityFestivalBpUnitPB.Builder getCopyCsBuilder() {
        final ActivityFestivalBpUnitPB.Builder builder = ActivityFestivalBpUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityFestivalBpUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurLevel() != 0) {
            builder.setCurLevel(this.getCurLevel());
            fieldCnt++;
        }  else if (builder.hasCurLevel()) {
            // 清理CurLevel
            builder.clearCurLevel();
            fieldCnt++;
        }
        if (this.getScore() != 0) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (this.bpStatus != null) {
            StructPB.Int32ActivityFestivalBpStatusMapPB.Builder tmpBuilder = StructPB.Int32ActivityFestivalBpStatusMapPB.newBuilder();
            final int tmpFieldCnt = this.bpStatus.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBpStatus(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBpStatus();
            }
        }  else if (builder.hasBpStatus()) {
            // 清理BpStatus
            builder.clearBpStatus();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityFestivalBpUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURLEVEL)) {
            builder.setCurLevel(this.getCurLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BPSTATUS) && this.bpStatus != null) {
            final boolean needClear = !builder.hasBpStatus();
            final int tmpFieldCnt = this.bpStatus.copyChangeToCs(builder.getBpStatusBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBpStatus();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityFestivalBpUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURLEVEL)) {
            builder.setCurLevel(this.getCurLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BPSTATUS) && this.bpStatus != null) {
            final boolean needClear = !builder.hasBpStatus();
            final int tmpFieldCnt = this.bpStatus.copyChangeToAndClearDeleteKeysCs(builder.getBpStatusBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBpStatus();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityFestivalBpUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurLevel()) {
            this.innerSetCurLevel(proto.getCurLevel());
        } else {
            this.innerSetCurLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBpStatus()) {
            this.getBpStatus().mergeFromCs(proto.getBpStatus());
        } else {
            if (this.bpStatus != null) {
                this.bpStatus.mergeFromCs(proto.getBpStatus());
            }
        }
        this.markAll();
        return ActivityFestivalBpUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityFestivalBpUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurLevel()) {
            this.setCurLevel(proto.getCurLevel());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasBpStatus()) {
            this.getBpStatus().mergeChangeFromCs(proto.getBpStatus());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityFestivalBpUnit.Builder getCopyDbBuilder() {
        final ActivityFestivalBpUnit.Builder builder = ActivityFestivalBpUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityFestivalBpUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurLevel() != 0) {
            builder.setCurLevel(this.getCurLevel());
            fieldCnt++;
        }  else if (builder.hasCurLevel()) {
            // 清理CurLevel
            builder.clearCurLevel();
            fieldCnt++;
        }
        if (this.getScore() != 0) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (this.bpStatus != null) {
            Struct.Int32ActivityFestivalBpStatusMap.Builder tmpBuilder = Struct.Int32ActivityFestivalBpStatusMap.newBuilder();
            final int tmpFieldCnt = this.bpStatus.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBpStatus(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBpStatus();
            }
        }  else if (builder.hasBpStatus()) {
            // 清理BpStatus
            builder.clearBpStatus();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityFestivalBpUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURLEVEL)) {
            builder.setCurLevel(this.getCurLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BPSTATUS) && this.bpStatus != null) {
            final boolean needClear = !builder.hasBpStatus();
            final int tmpFieldCnt = this.bpStatus.copyChangeToDb(builder.getBpStatusBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBpStatus();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityFestivalBpUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurLevel()) {
            this.innerSetCurLevel(proto.getCurLevel());
        } else {
            this.innerSetCurLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBpStatus()) {
            this.getBpStatus().mergeFromDb(proto.getBpStatus());
        } else {
            if (this.bpStatus != null) {
                this.bpStatus.mergeFromDb(proto.getBpStatus());
            }
        }
        this.markAll();
        return ActivityFestivalBpUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityFestivalBpUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurLevel()) {
            this.setCurLevel(proto.getCurLevel());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasBpStatus()) {
            this.getBpStatus().mergeChangeFromDb(proto.getBpStatus());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityFestivalBpUnit.Builder getCopySsBuilder() {
        final ActivityFestivalBpUnit.Builder builder = ActivityFestivalBpUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityFestivalBpUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurLevel() != 0) {
            builder.setCurLevel(this.getCurLevel());
            fieldCnt++;
        }  else if (builder.hasCurLevel()) {
            // 清理CurLevel
            builder.clearCurLevel();
            fieldCnt++;
        }
        if (this.getScore() != 0) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (this.bpStatus != null) {
            Struct.Int32ActivityFestivalBpStatusMap.Builder tmpBuilder = Struct.Int32ActivityFestivalBpStatusMap.newBuilder();
            final int tmpFieldCnt = this.bpStatus.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBpStatus(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBpStatus();
            }
        }  else if (builder.hasBpStatus()) {
            // 清理BpStatus
            builder.clearBpStatus();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityFestivalBpUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURLEVEL)) {
            builder.setCurLevel(this.getCurLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BPSTATUS) && this.bpStatus != null) {
            final boolean needClear = !builder.hasBpStatus();
            final int tmpFieldCnt = this.bpStatus.copyChangeToSs(builder.getBpStatusBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBpStatus();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityFestivalBpUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurLevel()) {
            this.innerSetCurLevel(proto.getCurLevel());
        } else {
            this.innerSetCurLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBpStatus()) {
            this.getBpStatus().mergeFromSs(proto.getBpStatus());
        } else {
            if (this.bpStatus != null) {
                this.bpStatus.mergeFromSs(proto.getBpStatus());
            }
        }
        this.markAll();
        return ActivityFestivalBpUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityFestivalBpUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurLevel()) {
            this.setCurLevel(proto.getCurLevel());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasBpStatus()) {
            this.getBpStatus().mergeChangeFromSs(proto.getBpStatus());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityFestivalBpUnit.Builder builder = ActivityFestivalBpUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_BPSTATUS) && this.bpStatus != null) {
            this.bpStatus.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.bpStatus != null) {
            this.bpStatus.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityFestivalBpUnitProp)) {
            return false;
        }
        final ActivityFestivalBpUnitProp otherNode = (ActivityFestivalBpUnitProp) node;
        if (this.curLevel != otherNode.curLevel) {
            return false;
        }
        if (this.score != otherNode.score) {
            return false;
        }
        if (!this.getBpStatus().compareDataTo(otherNode.getBpStatus())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
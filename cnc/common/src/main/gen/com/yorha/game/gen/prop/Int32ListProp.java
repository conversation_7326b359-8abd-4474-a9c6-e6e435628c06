package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.BasicPB.Int32ListPB;
import com.yorha.proto.Basic.Int32List;

/**
 * <AUTHOR> auto gen
 */
public class Int32ListProp extends AbstractListNode<Integer> {
    /**
     * Create a Int32ListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public Int32ListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to Int32ListProp
     *
     * @return new object
     */
    @Override
    public Integer addEmptyValue() {
        final Integer newProp = 0;
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Int32ListPB.Builder getCopyCsBuilder() {
        final Int32ListPB.Builder builder = Int32ListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(Int32ListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return Int32ListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        builder.addAllDatas(this);
        return Int32ListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(Int32ListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return Int32ListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(Int32ListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        this.addAll(proto.getDatasList());
        this.markAll();
        return Int32ListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(Int32ListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Int32List.Builder getCopyDbBuilder() {
        final Int32List.Builder builder = Int32List.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(Int32List.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return Int32ListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        builder.addAllDatas(this);
        return Int32ListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(Int32List.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return Int32ListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(Int32List proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        this.addAll(proto.getDatasList());
        this.markAll();
        return Int32ListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(Int32List proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Int32List.Builder getCopySsBuilder() {
        final Int32List.Builder builder = Int32List.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(Int32List.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return Int32ListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        builder.addAllDatas(this);
        return Int32ListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(Int32List.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return Int32ListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(Int32List proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        this.addAll(proto.getDatasList());
        this.markAll();
        return Int32ListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(Int32List proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        Int32List.Builder builder = Int32List.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.ClanInfo;
import com.yorha.proto.Basic;
import com.yorha.proto.Player;
import com.yorha.proto.Struct;
import com.yorha.proto.StructClan;
import com.yorha.proto.PlayerPB.ClanInfoPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.PlayerPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructClanPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CLANID = 0;
    public static final int FIELD_INDEX_ENTERTIME = 1;
    public static final int FIELD_INDEX_QUITTIME = 2;
    public static final int FIELD_INDEX_APPLYCLANLIST = 3;
    public static final int FIELD_INDEX_OBTAINPOWERREWARDTSMS = 4;
    public static final int FIELD_INDEX_SCOREINFO = 5;
    public static final int FIELD_INDEX_RESOURCES = 6;
    public static final int FIELD_INDEX_GIFTITEMS = 7;
    public static final int FIELD_INDEX_TREASUREGIFTITEMS = 8;
    public static final int FIELD_INDEX_TOTALCLANSCORE = 9;
    public static final int FIELD_INDEX_PLAYERCLANHELPMODEL = 10;
    public static final int FIELD_INDEX_STAFFID = 11;
    public static final int FIELD_INDEX_CANBECLANOWNERTSMS = 12;
    public static final int FIELD_INDEX_HASGOTFIRSTENTERCLANREWARD = 13;
    public static final int FIELD_INDEX_CLANTECHMODEL = 14;
    public static final int FIELD_INDEX_ALREADYRECOMMENDCLANIDS = 15;
    public static final int FIELD_INDEX_CURCLANGIFTINDEX = 16;

    public static final int FIELD_COUNT = 17;

    private long markBits0 = 0L;

    private long clanId = Constant.DEFAULT_LONG_VALUE;
    private long enterTime = Constant.DEFAULT_LONG_VALUE;
    private long quitTime = Constant.DEFAULT_LONG_VALUE;
    private Int64ListProp applyClanList = null;
    private long obtainPowerRewardTsMs = Constant.DEFAULT_LONG_VALUE;
    private Int32ClanScoreItemMapProp scoreInfo = null;
    private Int32CurrencyMapProp resources = null;
    private ClanGiftItemListProp giftItems = null;
    private ClanTreasureGiftItemListProp treasureGiftItems = null;
    private long totalClanScore = Constant.DEFAULT_LONG_VALUE;
    private PlayerClanHelpModelProp playerClanHelpModel = null;
    private int staffId = Constant.DEFAULT_INT_VALUE;
    private long canBeClanOwnerTsMs = Constant.DEFAULT_LONG_VALUE;
    private boolean hasGotFirstEnterClanReward = Constant.DEFAULT_BOOLEAN_VALUE;
    private PlayerClanTechModelProp clanTechModel = null;
    private Int64ListProp alreadyRecommendClanIds = null;
    private long curClanGiftIndex = Constant.DEFAULT_LONG_VALUE;

    public ClanInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get clanId
     *
     * @return clanId value
     */
    public long getClanId() {
        return this.clanId;
    }

    /**
     * set clanId && set marked
     *
     * @param clanId new value
     * @return current object
     */
    public ClanInfoProp setClanId(long clanId) {
        if (this.clanId != clanId) {
            this.mark(FIELD_INDEX_CLANID);
            this.clanId = clanId;
        }
        return this;
    }

    /**
     * inner set clanId
     *
     * @param clanId new value
     */
    private void innerSetClanId(long clanId) {
        this.clanId = clanId;
    }

    /**
     * get enterTime
     *
     * @return enterTime value
     */
    public long getEnterTime() {
        return this.enterTime;
    }

    /**
     * set enterTime && set marked
     *
     * @param enterTime new value
     * @return current object
     */
    public ClanInfoProp setEnterTime(long enterTime) {
        if (this.enterTime != enterTime) {
            this.mark(FIELD_INDEX_ENTERTIME);
            this.enterTime = enterTime;
        }
        return this;
    }

    /**
     * inner set enterTime
     *
     * @param enterTime new value
     */
    private void innerSetEnterTime(long enterTime) {
        this.enterTime = enterTime;
    }

    /**
     * get quitTime
     *
     * @return quitTime value
     */
    public long getQuitTime() {
        return this.quitTime;
    }

    /**
     * set quitTime && set marked
     *
     * @param quitTime new value
     * @return current object
     */
    public ClanInfoProp setQuitTime(long quitTime) {
        if (this.quitTime != quitTime) {
            this.mark(FIELD_INDEX_QUITTIME);
            this.quitTime = quitTime;
        }
        return this;
    }

    /**
     * inner set quitTime
     *
     * @param quitTime new value
     */
    private void innerSetQuitTime(long quitTime) {
        this.quitTime = quitTime;
    }

    /**
     * get applyClanList
     *
     * @return applyClanList value
     */
    public Int64ListProp getApplyClanList() {
        if (this.applyClanList == null) {
            this.applyClanList = new Int64ListProp(this, FIELD_INDEX_APPLYCLANLIST);
        }
        return this.applyClanList;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addApplyClanList(Long v) {
        this.getApplyClanList().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long getApplyClanListIndex(int index) {
        return this.getApplyClanList().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Long removeApplyClanList(Long v) {
        if (this.applyClanList == null) {
            return null;
        }
        if(this.applyClanList.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getApplyClanListSize() {
        if (this.applyClanList == null) {
            return 0;
        }
        return this.applyClanList.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isApplyClanListEmpty() {
        if (this.applyClanList == null) {
            return true;
        }
        return this.getApplyClanList().isEmpty();
    }

    /**
     * clear list
     */
    public void clearApplyClanList() {
        this.getApplyClanList().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long removeApplyClanListIndex(int index) {
        return this.getApplyClanList().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Long setApplyClanListIndex(int index, Long v) {
        return this.getApplyClanList().set(index, v);
    }
    /**
     * get obtainPowerRewardTsMs
     *
     * @return obtainPowerRewardTsMs value
     */
    public long getObtainPowerRewardTsMs() {
        return this.obtainPowerRewardTsMs;
    }

    /**
     * set obtainPowerRewardTsMs && set marked
     *
     * @param obtainPowerRewardTsMs new value
     * @return current object
     */
    public ClanInfoProp setObtainPowerRewardTsMs(long obtainPowerRewardTsMs) {
        if (this.obtainPowerRewardTsMs != obtainPowerRewardTsMs) {
            this.mark(FIELD_INDEX_OBTAINPOWERREWARDTSMS);
            this.obtainPowerRewardTsMs = obtainPowerRewardTsMs;
        }
        return this;
    }

    /**
     * inner set obtainPowerRewardTsMs
     *
     * @param obtainPowerRewardTsMs new value
     */
    private void innerSetObtainPowerRewardTsMs(long obtainPowerRewardTsMs) {
        this.obtainPowerRewardTsMs = obtainPowerRewardTsMs;
    }

    /**
     * get scoreInfo
     *
     * @return scoreInfo value
     */
    public Int32ClanScoreItemMapProp getScoreInfo() {
        if (this.scoreInfo == null) {
            this.scoreInfo = new Int32ClanScoreItemMapProp(this, FIELD_INDEX_SCOREINFO);
        }
        return this.scoreInfo;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putScoreInfoV(ClanScoreItemProp v) {
        this.getScoreInfo().put(v.getScoreType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ClanScoreItemProp addEmptyScoreInfo(Integer k) {
        return this.getScoreInfo().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getScoreInfoSize() {
        if (this.scoreInfo == null) {
            return 0;
        }
        return this.scoreInfo.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isScoreInfoEmpty() {
        if (this.scoreInfo == null) {
            return true;
        }
        return this.scoreInfo.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ClanScoreItemProp getScoreInfoV(Integer k) {
        if (this.scoreInfo == null || !this.scoreInfo.containsKey(k)) {
            return null;
        }
        return this.scoreInfo.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearScoreInfo() {
        if (this.scoreInfo != null) {
            this.scoreInfo.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeScoreInfoV(Integer k) {
        if (this.scoreInfo != null) {
            this.scoreInfo.remove(k);
        }
    }
    /**
     * get resources
     *
     * @return resources value
     */
    public Int32CurrencyMapProp getResources() {
        if (this.resources == null) {
            this.resources = new Int32CurrencyMapProp(this, FIELD_INDEX_RESOURCES);
        }
        return this.resources;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putResourcesV(CurrencyProp v) {
        this.getResources().put(v.getType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public CurrencyProp addEmptyResources(Integer k) {
        return this.getResources().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getResourcesSize() {
        if (this.resources == null) {
            return 0;
        }
        return this.resources.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isResourcesEmpty() {
        if (this.resources == null) {
            return true;
        }
        return this.resources.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public CurrencyProp getResourcesV(Integer k) {
        if (this.resources == null || !this.resources.containsKey(k)) {
            return null;
        }
        return this.resources.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearResources() {
        if (this.resources != null) {
            this.resources.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeResourcesV(Integer k) {
        if (this.resources != null) {
            this.resources.remove(k);
        }
    }
    /**
     * get giftItems
     *
     * @return giftItems value
     */
    public ClanGiftItemListProp getGiftItems() {
        if (this.giftItems == null) {
            this.giftItems = new ClanGiftItemListProp(this, FIELD_INDEX_GIFTITEMS);
        }
        return this.giftItems;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addGiftItems(ClanGiftItemProp v) {
        this.getGiftItems().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ClanGiftItemProp getGiftItemsIndex(int index) {
        return this.getGiftItems().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public ClanGiftItemProp removeGiftItems(ClanGiftItemProp v) {
        if (this.giftItems == null) {
            return null;
        }
        if(this.giftItems.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getGiftItemsSize() {
        if (this.giftItems == null) {
            return 0;
        }
        return this.giftItems.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isGiftItemsEmpty() {
        if (this.giftItems == null) {
            return true;
        }
        return this.getGiftItems().isEmpty();
    }

    /**
     * clear list
     */
    public void clearGiftItems() {
        this.getGiftItems().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ClanGiftItemProp removeGiftItemsIndex(int index) {
        return this.getGiftItems().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public ClanGiftItemProp setGiftItemsIndex(int index, ClanGiftItemProp v) {
        return this.getGiftItems().set(index, v);
    }
    /**
     * get treasureGiftItems
     *
     * @return treasureGiftItems value
     */
    public ClanTreasureGiftItemListProp getTreasureGiftItems() {
        if (this.treasureGiftItems == null) {
            this.treasureGiftItems = new ClanTreasureGiftItemListProp(this, FIELD_INDEX_TREASUREGIFTITEMS);
        }
        return this.treasureGiftItems;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addTreasureGiftItems(ClanTreasureGiftItemProp v) {
        this.getTreasureGiftItems().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ClanTreasureGiftItemProp getTreasureGiftItemsIndex(int index) {
        return this.getTreasureGiftItems().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public ClanTreasureGiftItemProp removeTreasureGiftItems(ClanTreasureGiftItemProp v) {
        if (this.treasureGiftItems == null) {
            return null;
        }
        if(this.treasureGiftItems.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getTreasureGiftItemsSize() {
        if (this.treasureGiftItems == null) {
            return 0;
        }
        return this.treasureGiftItems.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isTreasureGiftItemsEmpty() {
        if (this.treasureGiftItems == null) {
            return true;
        }
        return this.getTreasureGiftItems().isEmpty();
    }

    /**
     * clear list
     */
    public void clearTreasureGiftItems() {
        this.getTreasureGiftItems().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ClanTreasureGiftItemProp removeTreasureGiftItemsIndex(int index) {
        return this.getTreasureGiftItems().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public ClanTreasureGiftItemProp setTreasureGiftItemsIndex(int index, ClanTreasureGiftItemProp v) {
        return this.getTreasureGiftItems().set(index, v);
    }
    /**
     * get totalClanScore
     *
     * @return totalClanScore value
     */
    public long getTotalClanScore() {
        return this.totalClanScore;
    }

    /**
     * set totalClanScore && set marked
     *
     * @param totalClanScore new value
     * @return current object
     */
    public ClanInfoProp setTotalClanScore(long totalClanScore) {
        if (this.totalClanScore != totalClanScore) {
            this.mark(FIELD_INDEX_TOTALCLANSCORE);
            this.totalClanScore = totalClanScore;
        }
        return this;
    }

    /**
     * inner set totalClanScore
     *
     * @param totalClanScore new value
     */
    private void innerSetTotalClanScore(long totalClanScore) {
        this.totalClanScore = totalClanScore;
    }

    /**
     * get playerClanHelpModel
     *
     * @return playerClanHelpModel value
     */
    public PlayerClanHelpModelProp getPlayerClanHelpModel() {
        if (this.playerClanHelpModel == null) {
            this.playerClanHelpModel = new PlayerClanHelpModelProp(this, FIELD_INDEX_PLAYERCLANHELPMODEL);
        }
        return this.playerClanHelpModel;
    }

    /**
     * get staffId
     *
     * @return staffId value
     */
    public int getStaffId() {
        return this.staffId;
    }

    /**
     * set staffId && set marked
     *
     * @param staffId new value
     * @return current object
     */
    public ClanInfoProp setStaffId(int staffId) {
        if (this.staffId != staffId) {
            this.mark(FIELD_INDEX_STAFFID);
            this.staffId = staffId;
        }
        return this;
    }

    /**
     * inner set staffId
     *
     * @param staffId new value
     */
    private void innerSetStaffId(int staffId) {
        this.staffId = staffId;
    }

    /**
     * get canBeClanOwnerTsMs
     *
     * @return canBeClanOwnerTsMs value
     */
    public long getCanBeClanOwnerTsMs() {
        return this.canBeClanOwnerTsMs;
    }

    /**
     * set canBeClanOwnerTsMs && set marked
     *
     * @param canBeClanOwnerTsMs new value
     * @return current object
     */
    public ClanInfoProp setCanBeClanOwnerTsMs(long canBeClanOwnerTsMs) {
        if (this.canBeClanOwnerTsMs != canBeClanOwnerTsMs) {
            this.mark(FIELD_INDEX_CANBECLANOWNERTSMS);
            this.canBeClanOwnerTsMs = canBeClanOwnerTsMs;
        }
        return this;
    }

    /**
     * inner set canBeClanOwnerTsMs
     *
     * @param canBeClanOwnerTsMs new value
     */
    private void innerSetCanBeClanOwnerTsMs(long canBeClanOwnerTsMs) {
        this.canBeClanOwnerTsMs = canBeClanOwnerTsMs;
    }

    /**
     * get hasGotFirstEnterClanReward
     *
     * @return hasGotFirstEnterClanReward value
     */
    public boolean getHasGotFirstEnterClanReward() {
        return this.hasGotFirstEnterClanReward;
    }

    /**
     * set hasGotFirstEnterClanReward && set marked
     *
     * @param hasGotFirstEnterClanReward new value
     * @return current object
     */
    public ClanInfoProp setHasGotFirstEnterClanReward(boolean hasGotFirstEnterClanReward) {
        if (this.hasGotFirstEnterClanReward != hasGotFirstEnterClanReward) {
            this.mark(FIELD_INDEX_HASGOTFIRSTENTERCLANREWARD);
            this.hasGotFirstEnterClanReward = hasGotFirstEnterClanReward;
        }
        return this;
    }

    /**
     * inner set hasGotFirstEnterClanReward
     *
     * @param hasGotFirstEnterClanReward new value
     */
    private void innerSetHasGotFirstEnterClanReward(boolean hasGotFirstEnterClanReward) {
        this.hasGotFirstEnterClanReward = hasGotFirstEnterClanReward;
    }

    /**
     * get clanTechModel
     *
     * @return clanTechModel value
     */
    public PlayerClanTechModelProp getClanTechModel() {
        if (this.clanTechModel == null) {
            this.clanTechModel = new PlayerClanTechModelProp(this, FIELD_INDEX_CLANTECHMODEL);
        }
        return this.clanTechModel;
    }

    /**
     * get alreadyRecommendClanIds
     *
     * @return alreadyRecommendClanIds value
     */
    public Int64ListProp getAlreadyRecommendClanIds() {
        if (this.alreadyRecommendClanIds == null) {
            this.alreadyRecommendClanIds = new Int64ListProp(this, FIELD_INDEX_ALREADYRECOMMENDCLANIDS);
        }
        return this.alreadyRecommendClanIds;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addAlreadyRecommendClanIds(Long v) {
        this.getAlreadyRecommendClanIds().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long getAlreadyRecommendClanIdsIndex(int index) {
        return this.getAlreadyRecommendClanIds().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Long removeAlreadyRecommendClanIds(Long v) {
        if (this.alreadyRecommendClanIds == null) {
            return null;
        }
        if(this.alreadyRecommendClanIds.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getAlreadyRecommendClanIdsSize() {
        if (this.alreadyRecommendClanIds == null) {
            return 0;
        }
        return this.alreadyRecommendClanIds.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isAlreadyRecommendClanIdsEmpty() {
        if (this.alreadyRecommendClanIds == null) {
            return true;
        }
        return this.getAlreadyRecommendClanIds().isEmpty();
    }

    /**
     * clear list
     */
    public void clearAlreadyRecommendClanIds() {
        this.getAlreadyRecommendClanIds().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long removeAlreadyRecommendClanIdsIndex(int index) {
        return this.getAlreadyRecommendClanIds().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Long setAlreadyRecommendClanIdsIndex(int index, Long v) {
        return this.getAlreadyRecommendClanIds().set(index, v);
    }
    /**
     * get curClanGiftIndex
     *
     * @return curClanGiftIndex value
     */
    public long getCurClanGiftIndex() {
        return this.curClanGiftIndex;
    }

    /**
     * set curClanGiftIndex && set marked
     *
     * @param curClanGiftIndex new value
     * @return current object
     */
    public ClanInfoProp setCurClanGiftIndex(long curClanGiftIndex) {
        if (this.curClanGiftIndex != curClanGiftIndex) {
            this.mark(FIELD_INDEX_CURCLANGIFTINDEX);
            this.curClanGiftIndex = curClanGiftIndex;
        }
        return this;
    }

    /**
     * inner set curClanGiftIndex
     *
     * @param curClanGiftIndex new value
     */
    private void innerSetCurClanGiftIndex(long curClanGiftIndex) {
        this.curClanGiftIndex = curClanGiftIndex;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanInfoPB.Builder getCopyCsBuilder() {
        final ClanInfoPB.Builder builder = ClanInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (this.getEnterTime() != 0L) {
            builder.setEnterTime(this.getEnterTime());
            fieldCnt++;
        }  else if (builder.hasEnterTime()) {
            // 清理EnterTime
            builder.clearEnterTime();
            fieldCnt++;
        }
        if (this.getQuitTime() != 0L) {
            builder.setQuitTime(this.getQuitTime());
            fieldCnt++;
        }  else if (builder.hasQuitTime()) {
            // 清理QuitTime
            builder.clearQuitTime();
            fieldCnt++;
        }
        if (this.applyClanList != null) {
            BasicPB.Int64ListPB.Builder tmpBuilder = BasicPB.Int64ListPB.newBuilder();
            final int tmpFieldCnt = this.applyClanList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setApplyClanList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearApplyClanList();
            }
        }  else if (builder.hasApplyClanList()) {
            // 清理ApplyClanList
            builder.clearApplyClanList();
            fieldCnt++;
        }
        if (this.getObtainPowerRewardTsMs() != 0L) {
            builder.setObtainPowerRewardTsMs(this.getObtainPowerRewardTsMs());
            fieldCnt++;
        }  else if (builder.hasObtainPowerRewardTsMs()) {
            // 清理ObtainPowerRewardTsMs
            builder.clearObtainPowerRewardTsMs();
            fieldCnt++;
        }
        if (this.scoreInfo != null) {
            PlayerPB.Int32ClanScoreItemMapPB.Builder tmpBuilder = PlayerPB.Int32ClanScoreItemMapPB.newBuilder();
            final int tmpFieldCnt = this.scoreInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScoreInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScoreInfo();
            }
        }  else if (builder.hasScoreInfo()) {
            // 清理ScoreInfo
            builder.clearScoreInfo();
            fieldCnt++;
        }
        if (this.giftItems != null) {
            StructClanPB.ClanGiftItemListPB.Builder tmpBuilder = StructClanPB.ClanGiftItemListPB.newBuilder();
            final int tmpFieldCnt = this.giftItems.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGiftItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGiftItems();
            }
        }  else if (builder.hasGiftItems()) {
            // 清理GiftItems
            builder.clearGiftItems();
            fieldCnt++;
        }
        if (this.treasureGiftItems != null) {
            StructClanPB.ClanTreasureGiftItemListPB.Builder tmpBuilder = StructClanPB.ClanTreasureGiftItemListPB.newBuilder();
            final int tmpFieldCnt = this.treasureGiftItems.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTreasureGiftItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTreasureGiftItems();
            }
        }  else if (builder.hasTreasureGiftItems()) {
            // 清理TreasureGiftItems
            builder.clearTreasureGiftItems();
            fieldCnt++;
        }
        if (this.getTotalClanScore() != 0L) {
            builder.setTotalClanScore(this.getTotalClanScore());
            fieldCnt++;
        }  else if (builder.hasTotalClanScore()) {
            // 清理TotalClanScore
            builder.clearTotalClanScore();
            fieldCnt++;
        }
        if (this.playerClanHelpModel != null) {
            PlayerPB.PlayerClanHelpModelPB.Builder tmpBuilder = PlayerPB.PlayerClanHelpModelPB.newBuilder();
            final int tmpFieldCnt = this.playerClanHelpModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerClanHelpModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerClanHelpModel();
            }
        }  else if (builder.hasPlayerClanHelpModel()) {
            // 清理PlayerClanHelpModel
            builder.clearPlayerClanHelpModel();
            fieldCnt++;
        }
        if (this.getStaffId() != 0) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }  else if (builder.hasStaffId()) {
            // 清理StaffId
            builder.clearStaffId();
            fieldCnt++;
        }
        if (this.getCanBeClanOwnerTsMs() != 0L) {
            builder.setCanBeClanOwnerTsMs(this.getCanBeClanOwnerTsMs());
            fieldCnt++;
        }  else if (builder.hasCanBeClanOwnerTsMs()) {
            // 清理CanBeClanOwnerTsMs
            builder.clearCanBeClanOwnerTsMs();
            fieldCnt++;
        }
        if (this.getHasGotFirstEnterClanReward()) {
            builder.setHasGotFirstEnterClanReward(this.getHasGotFirstEnterClanReward());
            fieldCnt++;
        }  else if (builder.hasHasGotFirstEnterClanReward()) {
            // 清理HasGotFirstEnterClanReward
            builder.clearHasGotFirstEnterClanReward();
            fieldCnt++;
        }
        if (this.clanTechModel != null) {
            PlayerPB.PlayerClanTechModelPB.Builder tmpBuilder = PlayerPB.PlayerClanTechModelPB.newBuilder();
            final int tmpFieldCnt = this.clanTechModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanTechModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanTechModel();
            }
        }  else if (builder.hasClanTechModel()) {
            // 清理ClanTechModel
            builder.clearClanTechModel();
            fieldCnt++;
        }
        if (this.alreadyRecommendClanIds != null) {
            BasicPB.Int64ListPB.Builder tmpBuilder = BasicPB.Int64ListPB.newBuilder();
            final int tmpFieldCnt = this.alreadyRecommendClanIds.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAlreadyRecommendClanIds(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAlreadyRecommendClanIds();
            }
        }  else if (builder.hasAlreadyRecommendClanIds()) {
            // 清理AlreadyRecommendClanIds
            builder.clearAlreadyRecommendClanIds();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERTIME)) {
            builder.setEnterTime(this.getEnterTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_QUITTIME)) {
            builder.setQuitTime(this.getQuitTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_APPLYCLANLIST) && this.applyClanList != null) {
            final boolean needClear = !builder.hasApplyClanList();
            final int tmpFieldCnt = this.applyClanList.copyChangeToCs(builder.getApplyClanListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearApplyClanList();
            }
        }
        if (this.hasMark(FIELD_INDEX_OBTAINPOWERREWARDTSMS)) {
            builder.setObtainPowerRewardTsMs(this.getObtainPowerRewardTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCOREINFO) && this.scoreInfo != null) {
            final boolean needClear = !builder.hasScoreInfo();
            final int tmpFieldCnt = this.scoreInfo.copyChangeToCs(builder.getScoreInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_GIFTITEMS) && this.giftItems != null) {
            final boolean needClear = !builder.hasGiftItems();
            final int tmpFieldCnt = this.giftItems.copyChangeToCs(builder.getGiftItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_TREASUREGIFTITEMS) && this.treasureGiftItems != null) {
            final boolean needClear = !builder.hasTreasureGiftItems();
            final int tmpFieldCnt = this.treasureGiftItems.copyChangeToCs(builder.getTreasureGiftItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTreasureGiftItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_TOTALCLANSCORE)) {
            builder.setTotalClanScore(this.getTotalClanScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCLANHELPMODEL) && this.playerClanHelpModel != null) {
            final boolean needClear = !builder.hasPlayerClanHelpModel();
            final int tmpFieldCnt = this.playerClanHelpModel.copyChangeToCs(builder.getPlayerClanHelpModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerClanHelpModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_STAFFID)) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANBECLANOWNERTSMS)) {
            builder.setCanBeClanOwnerTsMs(this.getCanBeClanOwnerTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HASGOTFIRSTENTERCLANREWARD)) {
            builder.setHasGotFirstEnterClanReward(this.getHasGotFirstEnterClanReward());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANTECHMODEL) && this.clanTechModel != null) {
            final boolean needClear = !builder.hasClanTechModel();
            final int tmpFieldCnt = this.clanTechModel.copyChangeToCs(builder.getClanTechModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanTechModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ALREADYRECOMMENDCLANIDS) && this.alreadyRecommendClanIds != null) {
            final boolean needClear = !builder.hasAlreadyRecommendClanIds();
            final int tmpFieldCnt = this.alreadyRecommendClanIds.copyChangeToCs(builder.getAlreadyRecommendClanIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAlreadyRecommendClanIds();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERTIME)) {
            builder.setEnterTime(this.getEnterTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_QUITTIME)) {
            builder.setQuitTime(this.getQuitTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_APPLYCLANLIST) && this.applyClanList != null) {
            final boolean needClear = !builder.hasApplyClanList();
            final int tmpFieldCnt = this.applyClanList.copyChangeToCs(builder.getApplyClanListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearApplyClanList();
            }
        }
        if (this.hasMark(FIELD_INDEX_OBTAINPOWERREWARDTSMS)) {
            builder.setObtainPowerRewardTsMs(this.getObtainPowerRewardTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCOREINFO) && this.scoreInfo != null) {
            final boolean needClear = !builder.hasScoreInfo();
            final int tmpFieldCnt = this.scoreInfo.copyChangeToAndClearDeleteKeysCs(builder.getScoreInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_GIFTITEMS) && this.giftItems != null) {
            final boolean needClear = !builder.hasGiftItems();
            final int tmpFieldCnt = this.giftItems.copyChangeToCs(builder.getGiftItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_TREASUREGIFTITEMS) && this.treasureGiftItems != null) {
            final boolean needClear = !builder.hasTreasureGiftItems();
            final int tmpFieldCnt = this.treasureGiftItems.copyChangeToCs(builder.getTreasureGiftItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTreasureGiftItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_TOTALCLANSCORE)) {
            builder.setTotalClanScore(this.getTotalClanScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCLANHELPMODEL) && this.playerClanHelpModel != null) {
            final boolean needClear = !builder.hasPlayerClanHelpModel();
            final int tmpFieldCnt = this.playerClanHelpModel.copyChangeToAndClearDeleteKeysCs(builder.getPlayerClanHelpModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerClanHelpModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_STAFFID)) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANBECLANOWNERTSMS)) {
            builder.setCanBeClanOwnerTsMs(this.getCanBeClanOwnerTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HASGOTFIRSTENTERCLANREWARD)) {
            builder.setHasGotFirstEnterClanReward(this.getHasGotFirstEnterClanReward());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANTECHMODEL) && this.clanTechModel != null) {
            final boolean needClear = !builder.hasClanTechModel();
            final int tmpFieldCnt = this.clanTechModel.copyChangeToAndClearDeleteKeysCs(builder.getClanTechModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanTechModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ALREADYRECOMMENDCLANIDS) && this.alreadyRecommendClanIds != null) {
            final boolean needClear = !builder.hasAlreadyRecommendClanIds();
            final int tmpFieldCnt = this.alreadyRecommendClanIds.copyChangeToCs(builder.getAlreadyRecommendClanIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAlreadyRecommendClanIds();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEnterTime()) {
            this.innerSetEnterTime(proto.getEnterTime());
        } else {
            this.innerSetEnterTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasQuitTime()) {
            this.innerSetQuitTime(proto.getQuitTime());
        } else {
            this.innerSetQuitTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasApplyClanList()) {
            this.getApplyClanList().mergeFromCs(proto.getApplyClanList());
        } else {
            if (this.applyClanList != null) {
                this.applyClanList.mergeFromCs(proto.getApplyClanList());
            }
        }
        if (proto.hasObtainPowerRewardTsMs()) {
            this.innerSetObtainPowerRewardTsMs(proto.getObtainPowerRewardTsMs());
        } else {
            this.innerSetObtainPowerRewardTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasScoreInfo()) {
            this.getScoreInfo().mergeFromCs(proto.getScoreInfo());
        } else {
            if (this.scoreInfo != null) {
                this.scoreInfo.mergeFromCs(proto.getScoreInfo());
            }
        }
        if (proto.hasGiftItems()) {
            this.getGiftItems().mergeFromCs(proto.getGiftItems());
        } else {
            if (this.giftItems != null) {
                this.giftItems.mergeFromCs(proto.getGiftItems());
            }
        }
        if (proto.hasTreasureGiftItems()) {
            this.getTreasureGiftItems().mergeFromCs(proto.getTreasureGiftItems());
        } else {
            if (this.treasureGiftItems != null) {
                this.treasureGiftItems.mergeFromCs(proto.getTreasureGiftItems());
            }
        }
        if (proto.hasTotalClanScore()) {
            this.innerSetTotalClanScore(proto.getTotalClanScore());
        } else {
            this.innerSetTotalClanScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerClanHelpModel()) {
            this.getPlayerClanHelpModel().mergeFromCs(proto.getPlayerClanHelpModel());
        } else {
            if (this.playerClanHelpModel != null) {
                this.playerClanHelpModel.mergeFromCs(proto.getPlayerClanHelpModel());
            }
        }
        if (proto.hasStaffId()) {
            this.innerSetStaffId(proto.getStaffId());
        } else {
            this.innerSetStaffId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCanBeClanOwnerTsMs()) {
            this.innerSetCanBeClanOwnerTsMs(proto.getCanBeClanOwnerTsMs());
        } else {
            this.innerSetCanBeClanOwnerTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasHasGotFirstEnterClanReward()) {
            this.innerSetHasGotFirstEnterClanReward(proto.getHasGotFirstEnterClanReward());
        } else {
            this.innerSetHasGotFirstEnterClanReward(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasClanTechModel()) {
            this.getClanTechModel().mergeFromCs(proto.getClanTechModel());
        } else {
            if (this.clanTechModel != null) {
                this.clanTechModel.mergeFromCs(proto.getClanTechModel());
            }
        }
        if (proto.hasAlreadyRecommendClanIds()) {
            this.getAlreadyRecommendClanIds().mergeFromCs(proto.getAlreadyRecommendClanIds());
        } else {
            if (this.alreadyRecommendClanIds != null) {
                this.alreadyRecommendClanIds.mergeFromCs(proto.getAlreadyRecommendClanIds());
            }
        }
        this.markAll();
        return ClanInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasEnterTime()) {
            this.setEnterTime(proto.getEnterTime());
            fieldCnt++;
        }
        if (proto.hasQuitTime()) {
            this.setQuitTime(proto.getQuitTime());
            fieldCnt++;
        }
        if (proto.hasApplyClanList()) {
            this.getApplyClanList().mergeChangeFromCs(proto.getApplyClanList());
            fieldCnt++;
        }
        if (proto.hasObtainPowerRewardTsMs()) {
            this.setObtainPowerRewardTsMs(proto.getObtainPowerRewardTsMs());
            fieldCnt++;
        }
        if (proto.hasScoreInfo()) {
            this.getScoreInfo().mergeChangeFromCs(proto.getScoreInfo());
            fieldCnt++;
        }
        if (proto.hasGiftItems()) {
            this.getGiftItems().mergeChangeFromCs(proto.getGiftItems());
            fieldCnt++;
        }
        if (proto.hasTreasureGiftItems()) {
            this.getTreasureGiftItems().mergeChangeFromCs(proto.getTreasureGiftItems());
            fieldCnt++;
        }
        if (proto.hasTotalClanScore()) {
            this.setTotalClanScore(proto.getTotalClanScore());
            fieldCnt++;
        }
        if (proto.hasPlayerClanHelpModel()) {
            this.getPlayerClanHelpModel().mergeChangeFromCs(proto.getPlayerClanHelpModel());
            fieldCnt++;
        }
        if (proto.hasStaffId()) {
            this.setStaffId(proto.getStaffId());
            fieldCnt++;
        }
        if (proto.hasCanBeClanOwnerTsMs()) {
            this.setCanBeClanOwnerTsMs(proto.getCanBeClanOwnerTsMs());
            fieldCnt++;
        }
        if (proto.hasHasGotFirstEnterClanReward()) {
            this.setHasGotFirstEnterClanReward(proto.getHasGotFirstEnterClanReward());
            fieldCnt++;
        }
        if (proto.hasClanTechModel()) {
            this.getClanTechModel().mergeChangeFromCs(proto.getClanTechModel());
            fieldCnt++;
        }
        if (proto.hasAlreadyRecommendClanIds()) {
            this.getAlreadyRecommendClanIds().mergeChangeFromCs(proto.getAlreadyRecommendClanIds());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanInfo.Builder getCopyDbBuilder() {
        final ClanInfo.Builder builder = ClanInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (this.getEnterTime() != 0L) {
            builder.setEnterTime(this.getEnterTime());
            fieldCnt++;
        }  else if (builder.hasEnterTime()) {
            // 清理EnterTime
            builder.clearEnterTime();
            fieldCnt++;
        }
        if (this.getQuitTime() != 0L) {
            builder.setQuitTime(this.getQuitTime());
            fieldCnt++;
        }  else if (builder.hasQuitTime()) {
            // 清理QuitTime
            builder.clearQuitTime();
            fieldCnt++;
        }
        if (this.applyClanList != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.applyClanList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setApplyClanList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearApplyClanList();
            }
        }  else if (builder.hasApplyClanList()) {
            // 清理ApplyClanList
            builder.clearApplyClanList();
            fieldCnt++;
        }
        if (this.getObtainPowerRewardTsMs() != 0L) {
            builder.setObtainPowerRewardTsMs(this.getObtainPowerRewardTsMs());
            fieldCnt++;
        }  else if (builder.hasObtainPowerRewardTsMs()) {
            // 清理ObtainPowerRewardTsMs
            builder.clearObtainPowerRewardTsMs();
            fieldCnt++;
        }
        if (this.scoreInfo != null) {
            Player.Int32ClanScoreItemMap.Builder tmpBuilder = Player.Int32ClanScoreItemMap.newBuilder();
            final int tmpFieldCnt = this.scoreInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScoreInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScoreInfo();
            }
        }  else if (builder.hasScoreInfo()) {
            // 清理ScoreInfo
            builder.clearScoreInfo();
            fieldCnt++;
        }
        if (this.resources != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.resources.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResources(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResources();
            }
        }  else if (builder.hasResources()) {
            // 清理Resources
            builder.clearResources();
            fieldCnt++;
        }
        if (this.giftItems != null) {
            StructClan.ClanGiftItemList.Builder tmpBuilder = StructClan.ClanGiftItemList.newBuilder();
            final int tmpFieldCnt = this.giftItems.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGiftItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGiftItems();
            }
        }  else if (builder.hasGiftItems()) {
            // 清理GiftItems
            builder.clearGiftItems();
            fieldCnt++;
        }
        if (this.treasureGiftItems != null) {
            StructClan.ClanTreasureGiftItemList.Builder tmpBuilder = StructClan.ClanTreasureGiftItemList.newBuilder();
            final int tmpFieldCnt = this.treasureGiftItems.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTreasureGiftItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTreasureGiftItems();
            }
        }  else if (builder.hasTreasureGiftItems()) {
            // 清理TreasureGiftItems
            builder.clearTreasureGiftItems();
            fieldCnt++;
        }
        if (this.getTotalClanScore() != 0L) {
            builder.setTotalClanScore(this.getTotalClanScore());
            fieldCnt++;
        }  else if (builder.hasTotalClanScore()) {
            // 清理TotalClanScore
            builder.clearTotalClanScore();
            fieldCnt++;
        }
        if (this.playerClanHelpModel != null) {
            Player.PlayerClanHelpModel.Builder tmpBuilder = Player.PlayerClanHelpModel.newBuilder();
            final int tmpFieldCnt = this.playerClanHelpModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerClanHelpModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerClanHelpModel();
            }
        }  else if (builder.hasPlayerClanHelpModel()) {
            // 清理PlayerClanHelpModel
            builder.clearPlayerClanHelpModel();
            fieldCnt++;
        }
        if (this.getStaffId() != 0) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }  else if (builder.hasStaffId()) {
            // 清理StaffId
            builder.clearStaffId();
            fieldCnt++;
        }
        if (this.getCanBeClanOwnerTsMs() != 0L) {
            builder.setCanBeClanOwnerTsMs(this.getCanBeClanOwnerTsMs());
            fieldCnt++;
        }  else if (builder.hasCanBeClanOwnerTsMs()) {
            // 清理CanBeClanOwnerTsMs
            builder.clearCanBeClanOwnerTsMs();
            fieldCnt++;
        }
        if (this.getHasGotFirstEnterClanReward()) {
            builder.setHasGotFirstEnterClanReward(this.getHasGotFirstEnterClanReward());
            fieldCnt++;
        }  else if (builder.hasHasGotFirstEnterClanReward()) {
            // 清理HasGotFirstEnterClanReward
            builder.clearHasGotFirstEnterClanReward();
            fieldCnt++;
        }
        if (this.clanTechModel != null) {
            Player.PlayerClanTechModel.Builder tmpBuilder = Player.PlayerClanTechModel.newBuilder();
            final int tmpFieldCnt = this.clanTechModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanTechModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanTechModel();
            }
        }  else if (builder.hasClanTechModel()) {
            // 清理ClanTechModel
            builder.clearClanTechModel();
            fieldCnt++;
        }
        if (this.alreadyRecommendClanIds != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.alreadyRecommendClanIds.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAlreadyRecommendClanIds(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAlreadyRecommendClanIds();
            }
        }  else if (builder.hasAlreadyRecommendClanIds()) {
            // 清理AlreadyRecommendClanIds
            builder.clearAlreadyRecommendClanIds();
            fieldCnt++;
        }
        if (this.getCurClanGiftIndex() != 0L) {
            builder.setCurClanGiftIndex(this.getCurClanGiftIndex());
            fieldCnt++;
        }  else if (builder.hasCurClanGiftIndex()) {
            // 清理CurClanGiftIndex
            builder.clearCurClanGiftIndex();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERTIME)) {
            builder.setEnterTime(this.getEnterTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_QUITTIME)) {
            builder.setQuitTime(this.getQuitTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_APPLYCLANLIST) && this.applyClanList != null) {
            final boolean needClear = !builder.hasApplyClanList();
            final int tmpFieldCnt = this.applyClanList.copyChangeToDb(builder.getApplyClanListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearApplyClanList();
            }
        }
        if (this.hasMark(FIELD_INDEX_OBTAINPOWERREWARDTSMS)) {
            builder.setObtainPowerRewardTsMs(this.getObtainPowerRewardTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCOREINFO) && this.scoreInfo != null) {
            final boolean needClear = !builder.hasScoreInfo();
            final int tmpFieldCnt = this.scoreInfo.copyChangeToDb(builder.getScoreInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            final boolean needClear = !builder.hasResources();
            final int tmpFieldCnt = this.resources.copyChangeToDb(builder.getResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResources();
            }
        }
        if (this.hasMark(FIELD_INDEX_GIFTITEMS) && this.giftItems != null) {
            final boolean needClear = !builder.hasGiftItems();
            final int tmpFieldCnt = this.giftItems.copyChangeToDb(builder.getGiftItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_TREASUREGIFTITEMS) && this.treasureGiftItems != null) {
            final boolean needClear = !builder.hasTreasureGiftItems();
            final int tmpFieldCnt = this.treasureGiftItems.copyChangeToDb(builder.getTreasureGiftItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTreasureGiftItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_TOTALCLANSCORE)) {
            builder.setTotalClanScore(this.getTotalClanScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCLANHELPMODEL) && this.playerClanHelpModel != null) {
            final boolean needClear = !builder.hasPlayerClanHelpModel();
            final int tmpFieldCnt = this.playerClanHelpModel.copyChangeToDb(builder.getPlayerClanHelpModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerClanHelpModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_STAFFID)) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANBECLANOWNERTSMS)) {
            builder.setCanBeClanOwnerTsMs(this.getCanBeClanOwnerTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HASGOTFIRSTENTERCLANREWARD)) {
            builder.setHasGotFirstEnterClanReward(this.getHasGotFirstEnterClanReward());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANTECHMODEL) && this.clanTechModel != null) {
            final boolean needClear = !builder.hasClanTechModel();
            final int tmpFieldCnt = this.clanTechModel.copyChangeToDb(builder.getClanTechModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanTechModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ALREADYRECOMMENDCLANIDS) && this.alreadyRecommendClanIds != null) {
            final boolean needClear = !builder.hasAlreadyRecommendClanIds();
            final int tmpFieldCnt = this.alreadyRecommendClanIds.copyChangeToDb(builder.getAlreadyRecommendClanIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAlreadyRecommendClanIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_CURCLANGIFTINDEX)) {
            builder.setCurClanGiftIndex(this.getCurClanGiftIndex());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEnterTime()) {
            this.innerSetEnterTime(proto.getEnterTime());
        } else {
            this.innerSetEnterTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasQuitTime()) {
            this.innerSetQuitTime(proto.getQuitTime());
        } else {
            this.innerSetQuitTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasApplyClanList()) {
            this.getApplyClanList().mergeFromDb(proto.getApplyClanList());
        } else {
            if (this.applyClanList != null) {
                this.applyClanList.mergeFromDb(proto.getApplyClanList());
            }
        }
        if (proto.hasObtainPowerRewardTsMs()) {
            this.innerSetObtainPowerRewardTsMs(proto.getObtainPowerRewardTsMs());
        } else {
            this.innerSetObtainPowerRewardTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasScoreInfo()) {
            this.getScoreInfo().mergeFromDb(proto.getScoreInfo());
        } else {
            if (this.scoreInfo != null) {
                this.scoreInfo.mergeFromDb(proto.getScoreInfo());
            }
        }
        if (proto.hasResources()) {
            this.getResources().mergeFromDb(proto.getResources());
        } else {
            if (this.resources != null) {
                this.resources.mergeFromDb(proto.getResources());
            }
        }
        if (proto.hasGiftItems()) {
            this.getGiftItems().mergeFromDb(proto.getGiftItems());
        } else {
            if (this.giftItems != null) {
                this.giftItems.mergeFromDb(proto.getGiftItems());
            }
        }
        if (proto.hasTreasureGiftItems()) {
            this.getTreasureGiftItems().mergeFromDb(proto.getTreasureGiftItems());
        } else {
            if (this.treasureGiftItems != null) {
                this.treasureGiftItems.mergeFromDb(proto.getTreasureGiftItems());
            }
        }
        if (proto.hasTotalClanScore()) {
            this.innerSetTotalClanScore(proto.getTotalClanScore());
        } else {
            this.innerSetTotalClanScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerClanHelpModel()) {
            this.getPlayerClanHelpModel().mergeFromDb(proto.getPlayerClanHelpModel());
        } else {
            if (this.playerClanHelpModel != null) {
                this.playerClanHelpModel.mergeFromDb(proto.getPlayerClanHelpModel());
            }
        }
        if (proto.hasStaffId()) {
            this.innerSetStaffId(proto.getStaffId());
        } else {
            this.innerSetStaffId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCanBeClanOwnerTsMs()) {
            this.innerSetCanBeClanOwnerTsMs(proto.getCanBeClanOwnerTsMs());
        } else {
            this.innerSetCanBeClanOwnerTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasHasGotFirstEnterClanReward()) {
            this.innerSetHasGotFirstEnterClanReward(proto.getHasGotFirstEnterClanReward());
        } else {
            this.innerSetHasGotFirstEnterClanReward(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasClanTechModel()) {
            this.getClanTechModel().mergeFromDb(proto.getClanTechModel());
        } else {
            if (this.clanTechModel != null) {
                this.clanTechModel.mergeFromDb(proto.getClanTechModel());
            }
        }
        if (proto.hasAlreadyRecommendClanIds()) {
            this.getAlreadyRecommendClanIds().mergeFromDb(proto.getAlreadyRecommendClanIds());
        } else {
            if (this.alreadyRecommendClanIds != null) {
                this.alreadyRecommendClanIds.mergeFromDb(proto.getAlreadyRecommendClanIds());
            }
        }
        if (proto.hasCurClanGiftIndex()) {
            this.innerSetCurClanGiftIndex(proto.getCurClanGiftIndex());
        } else {
            this.innerSetCurClanGiftIndex(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasEnterTime()) {
            this.setEnterTime(proto.getEnterTime());
            fieldCnt++;
        }
        if (proto.hasQuitTime()) {
            this.setQuitTime(proto.getQuitTime());
            fieldCnt++;
        }
        if (proto.hasApplyClanList()) {
            this.getApplyClanList().mergeChangeFromDb(proto.getApplyClanList());
            fieldCnt++;
        }
        if (proto.hasObtainPowerRewardTsMs()) {
            this.setObtainPowerRewardTsMs(proto.getObtainPowerRewardTsMs());
            fieldCnt++;
        }
        if (proto.hasScoreInfo()) {
            this.getScoreInfo().mergeChangeFromDb(proto.getScoreInfo());
            fieldCnt++;
        }
        if (proto.hasResources()) {
            this.getResources().mergeChangeFromDb(proto.getResources());
            fieldCnt++;
        }
        if (proto.hasGiftItems()) {
            this.getGiftItems().mergeChangeFromDb(proto.getGiftItems());
            fieldCnt++;
        }
        if (proto.hasTreasureGiftItems()) {
            this.getTreasureGiftItems().mergeChangeFromDb(proto.getTreasureGiftItems());
            fieldCnt++;
        }
        if (proto.hasTotalClanScore()) {
            this.setTotalClanScore(proto.getTotalClanScore());
            fieldCnt++;
        }
        if (proto.hasPlayerClanHelpModel()) {
            this.getPlayerClanHelpModel().mergeChangeFromDb(proto.getPlayerClanHelpModel());
            fieldCnt++;
        }
        if (proto.hasStaffId()) {
            this.setStaffId(proto.getStaffId());
            fieldCnt++;
        }
        if (proto.hasCanBeClanOwnerTsMs()) {
            this.setCanBeClanOwnerTsMs(proto.getCanBeClanOwnerTsMs());
            fieldCnt++;
        }
        if (proto.hasHasGotFirstEnterClanReward()) {
            this.setHasGotFirstEnterClanReward(proto.getHasGotFirstEnterClanReward());
            fieldCnt++;
        }
        if (proto.hasClanTechModel()) {
            this.getClanTechModel().mergeChangeFromDb(proto.getClanTechModel());
            fieldCnt++;
        }
        if (proto.hasAlreadyRecommendClanIds()) {
            this.getAlreadyRecommendClanIds().mergeChangeFromDb(proto.getAlreadyRecommendClanIds());
            fieldCnt++;
        }
        if (proto.hasCurClanGiftIndex()) {
            this.setCurClanGiftIndex(proto.getCurClanGiftIndex());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanInfo.Builder getCopySsBuilder() {
        final ClanInfo.Builder builder = ClanInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (this.getEnterTime() != 0L) {
            builder.setEnterTime(this.getEnterTime());
            fieldCnt++;
        }  else if (builder.hasEnterTime()) {
            // 清理EnterTime
            builder.clearEnterTime();
            fieldCnt++;
        }
        if (this.getQuitTime() != 0L) {
            builder.setQuitTime(this.getQuitTime());
            fieldCnt++;
        }  else if (builder.hasQuitTime()) {
            // 清理QuitTime
            builder.clearQuitTime();
            fieldCnt++;
        }
        if (this.applyClanList != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.applyClanList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setApplyClanList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearApplyClanList();
            }
        }  else if (builder.hasApplyClanList()) {
            // 清理ApplyClanList
            builder.clearApplyClanList();
            fieldCnt++;
        }
        if (this.getObtainPowerRewardTsMs() != 0L) {
            builder.setObtainPowerRewardTsMs(this.getObtainPowerRewardTsMs());
            fieldCnt++;
        }  else if (builder.hasObtainPowerRewardTsMs()) {
            // 清理ObtainPowerRewardTsMs
            builder.clearObtainPowerRewardTsMs();
            fieldCnt++;
        }
        if (this.scoreInfo != null) {
            Player.Int32ClanScoreItemMap.Builder tmpBuilder = Player.Int32ClanScoreItemMap.newBuilder();
            final int tmpFieldCnt = this.scoreInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScoreInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScoreInfo();
            }
        }  else if (builder.hasScoreInfo()) {
            // 清理ScoreInfo
            builder.clearScoreInfo();
            fieldCnt++;
        }
        if (this.resources != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.resources.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResources(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResources();
            }
        }  else if (builder.hasResources()) {
            // 清理Resources
            builder.clearResources();
            fieldCnt++;
        }
        if (this.giftItems != null) {
            StructClan.ClanGiftItemList.Builder tmpBuilder = StructClan.ClanGiftItemList.newBuilder();
            final int tmpFieldCnt = this.giftItems.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGiftItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGiftItems();
            }
        }  else if (builder.hasGiftItems()) {
            // 清理GiftItems
            builder.clearGiftItems();
            fieldCnt++;
        }
        if (this.treasureGiftItems != null) {
            StructClan.ClanTreasureGiftItemList.Builder tmpBuilder = StructClan.ClanTreasureGiftItemList.newBuilder();
            final int tmpFieldCnt = this.treasureGiftItems.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTreasureGiftItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTreasureGiftItems();
            }
        }  else if (builder.hasTreasureGiftItems()) {
            // 清理TreasureGiftItems
            builder.clearTreasureGiftItems();
            fieldCnt++;
        }
        if (this.getTotalClanScore() != 0L) {
            builder.setTotalClanScore(this.getTotalClanScore());
            fieldCnt++;
        }  else if (builder.hasTotalClanScore()) {
            // 清理TotalClanScore
            builder.clearTotalClanScore();
            fieldCnt++;
        }
        if (this.playerClanHelpModel != null) {
            Player.PlayerClanHelpModel.Builder tmpBuilder = Player.PlayerClanHelpModel.newBuilder();
            final int tmpFieldCnt = this.playerClanHelpModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerClanHelpModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerClanHelpModel();
            }
        }  else if (builder.hasPlayerClanHelpModel()) {
            // 清理PlayerClanHelpModel
            builder.clearPlayerClanHelpModel();
            fieldCnt++;
        }
        if (this.getStaffId() != 0) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }  else if (builder.hasStaffId()) {
            // 清理StaffId
            builder.clearStaffId();
            fieldCnt++;
        }
        if (this.getCanBeClanOwnerTsMs() != 0L) {
            builder.setCanBeClanOwnerTsMs(this.getCanBeClanOwnerTsMs());
            fieldCnt++;
        }  else if (builder.hasCanBeClanOwnerTsMs()) {
            // 清理CanBeClanOwnerTsMs
            builder.clearCanBeClanOwnerTsMs();
            fieldCnt++;
        }
        if (this.getHasGotFirstEnterClanReward()) {
            builder.setHasGotFirstEnterClanReward(this.getHasGotFirstEnterClanReward());
            fieldCnt++;
        }  else if (builder.hasHasGotFirstEnterClanReward()) {
            // 清理HasGotFirstEnterClanReward
            builder.clearHasGotFirstEnterClanReward();
            fieldCnt++;
        }
        if (this.clanTechModel != null) {
            Player.PlayerClanTechModel.Builder tmpBuilder = Player.PlayerClanTechModel.newBuilder();
            final int tmpFieldCnt = this.clanTechModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanTechModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanTechModel();
            }
        }  else if (builder.hasClanTechModel()) {
            // 清理ClanTechModel
            builder.clearClanTechModel();
            fieldCnt++;
        }
        if (this.alreadyRecommendClanIds != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.alreadyRecommendClanIds.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAlreadyRecommendClanIds(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAlreadyRecommendClanIds();
            }
        }  else if (builder.hasAlreadyRecommendClanIds()) {
            // 清理AlreadyRecommendClanIds
            builder.clearAlreadyRecommendClanIds();
            fieldCnt++;
        }
        if (this.getCurClanGiftIndex() != 0L) {
            builder.setCurClanGiftIndex(this.getCurClanGiftIndex());
            fieldCnt++;
        }  else if (builder.hasCurClanGiftIndex()) {
            // 清理CurClanGiftIndex
            builder.clearCurClanGiftIndex();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERTIME)) {
            builder.setEnterTime(this.getEnterTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_QUITTIME)) {
            builder.setQuitTime(this.getQuitTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_APPLYCLANLIST) && this.applyClanList != null) {
            final boolean needClear = !builder.hasApplyClanList();
            final int tmpFieldCnt = this.applyClanList.copyChangeToSs(builder.getApplyClanListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearApplyClanList();
            }
        }
        if (this.hasMark(FIELD_INDEX_OBTAINPOWERREWARDTSMS)) {
            builder.setObtainPowerRewardTsMs(this.getObtainPowerRewardTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCOREINFO) && this.scoreInfo != null) {
            final boolean needClear = !builder.hasScoreInfo();
            final int tmpFieldCnt = this.scoreInfo.copyChangeToSs(builder.getScoreInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            final boolean needClear = !builder.hasResources();
            final int tmpFieldCnt = this.resources.copyChangeToSs(builder.getResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResources();
            }
        }
        if (this.hasMark(FIELD_INDEX_GIFTITEMS) && this.giftItems != null) {
            final boolean needClear = !builder.hasGiftItems();
            final int tmpFieldCnt = this.giftItems.copyChangeToSs(builder.getGiftItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_TREASUREGIFTITEMS) && this.treasureGiftItems != null) {
            final boolean needClear = !builder.hasTreasureGiftItems();
            final int tmpFieldCnt = this.treasureGiftItems.copyChangeToSs(builder.getTreasureGiftItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTreasureGiftItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_TOTALCLANSCORE)) {
            builder.setTotalClanScore(this.getTotalClanScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCLANHELPMODEL) && this.playerClanHelpModel != null) {
            final boolean needClear = !builder.hasPlayerClanHelpModel();
            final int tmpFieldCnt = this.playerClanHelpModel.copyChangeToSs(builder.getPlayerClanHelpModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerClanHelpModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_STAFFID)) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANBECLANOWNERTSMS)) {
            builder.setCanBeClanOwnerTsMs(this.getCanBeClanOwnerTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HASGOTFIRSTENTERCLANREWARD)) {
            builder.setHasGotFirstEnterClanReward(this.getHasGotFirstEnterClanReward());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANTECHMODEL) && this.clanTechModel != null) {
            final boolean needClear = !builder.hasClanTechModel();
            final int tmpFieldCnt = this.clanTechModel.copyChangeToSs(builder.getClanTechModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanTechModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ALREADYRECOMMENDCLANIDS) && this.alreadyRecommendClanIds != null) {
            final boolean needClear = !builder.hasAlreadyRecommendClanIds();
            final int tmpFieldCnt = this.alreadyRecommendClanIds.copyChangeToSs(builder.getAlreadyRecommendClanIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAlreadyRecommendClanIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_CURCLANGIFTINDEX)) {
            builder.setCurClanGiftIndex(this.getCurClanGiftIndex());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEnterTime()) {
            this.innerSetEnterTime(proto.getEnterTime());
        } else {
            this.innerSetEnterTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasQuitTime()) {
            this.innerSetQuitTime(proto.getQuitTime());
        } else {
            this.innerSetQuitTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasApplyClanList()) {
            this.getApplyClanList().mergeFromSs(proto.getApplyClanList());
        } else {
            if (this.applyClanList != null) {
                this.applyClanList.mergeFromSs(proto.getApplyClanList());
            }
        }
        if (proto.hasObtainPowerRewardTsMs()) {
            this.innerSetObtainPowerRewardTsMs(proto.getObtainPowerRewardTsMs());
        } else {
            this.innerSetObtainPowerRewardTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasScoreInfo()) {
            this.getScoreInfo().mergeFromSs(proto.getScoreInfo());
        } else {
            if (this.scoreInfo != null) {
                this.scoreInfo.mergeFromSs(proto.getScoreInfo());
            }
        }
        if (proto.hasResources()) {
            this.getResources().mergeFromSs(proto.getResources());
        } else {
            if (this.resources != null) {
                this.resources.mergeFromSs(proto.getResources());
            }
        }
        if (proto.hasGiftItems()) {
            this.getGiftItems().mergeFromSs(proto.getGiftItems());
        } else {
            if (this.giftItems != null) {
                this.giftItems.mergeFromSs(proto.getGiftItems());
            }
        }
        if (proto.hasTreasureGiftItems()) {
            this.getTreasureGiftItems().mergeFromSs(proto.getTreasureGiftItems());
        } else {
            if (this.treasureGiftItems != null) {
                this.treasureGiftItems.mergeFromSs(proto.getTreasureGiftItems());
            }
        }
        if (proto.hasTotalClanScore()) {
            this.innerSetTotalClanScore(proto.getTotalClanScore());
        } else {
            this.innerSetTotalClanScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerClanHelpModel()) {
            this.getPlayerClanHelpModel().mergeFromSs(proto.getPlayerClanHelpModel());
        } else {
            if (this.playerClanHelpModel != null) {
                this.playerClanHelpModel.mergeFromSs(proto.getPlayerClanHelpModel());
            }
        }
        if (proto.hasStaffId()) {
            this.innerSetStaffId(proto.getStaffId());
        } else {
            this.innerSetStaffId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCanBeClanOwnerTsMs()) {
            this.innerSetCanBeClanOwnerTsMs(proto.getCanBeClanOwnerTsMs());
        } else {
            this.innerSetCanBeClanOwnerTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasHasGotFirstEnterClanReward()) {
            this.innerSetHasGotFirstEnterClanReward(proto.getHasGotFirstEnterClanReward());
        } else {
            this.innerSetHasGotFirstEnterClanReward(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasClanTechModel()) {
            this.getClanTechModel().mergeFromSs(proto.getClanTechModel());
        } else {
            if (this.clanTechModel != null) {
                this.clanTechModel.mergeFromSs(proto.getClanTechModel());
            }
        }
        if (proto.hasAlreadyRecommendClanIds()) {
            this.getAlreadyRecommendClanIds().mergeFromSs(proto.getAlreadyRecommendClanIds());
        } else {
            if (this.alreadyRecommendClanIds != null) {
                this.alreadyRecommendClanIds.mergeFromSs(proto.getAlreadyRecommendClanIds());
            }
        }
        if (proto.hasCurClanGiftIndex()) {
            this.innerSetCurClanGiftIndex(proto.getCurClanGiftIndex());
        } else {
            this.innerSetCurClanGiftIndex(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasEnterTime()) {
            this.setEnterTime(proto.getEnterTime());
            fieldCnt++;
        }
        if (proto.hasQuitTime()) {
            this.setQuitTime(proto.getQuitTime());
            fieldCnt++;
        }
        if (proto.hasApplyClanList()) {
            this.getApplyClanList().mergeChangeFromSs(proto.getApplyClanList());
            fieldCnt++;
        }
        if (proto.hasObtainPowerRewardTsMs()) {
            this.setObtainPowerRewardTsMs(proto.getObtainPowerRewardTsMs());
            fieldCnt++;
        }
        if (proto.hasScoreInfo()) {
            this.getScoreInfo().mergeChangeFromSs(proto.getScoreInfo());
            fieldCnt++;
        }
        if (proto.hasResources()) {
            this.getResources().mergeChangeFromSs(proto.getResources());
            fieldCnt++;
        }
        if (proto.hasGiftItems()) {
            this.getGiftItems().mergeChangeFromSs(proto.getGiftItems());
            fieldCnt++;
        }
        if (proto.hasTreasureGiftItems()) {
            this.getTreasureGiftItems().mergeChangeFromSs(proto.getTreasureGiftItems());
            fieldCnt++;
        }
        if (proto.hasTotalClanScore()) {
            this.setTotalClanScore(proto.getTotalClanScore());
            fieldCnt++;
        }
        if (proto.hasPlayerClanHelpModel()) {
            this.getPlayerClanHelpModel().mergeChangeFromSs(proto.getPlayerClanHelpModel());
            fieldCnt++;
        }
        if (proto.hasStaffId()) {
            this.setStaffId(proto.getStaffId());
            fieldCnt++;
        }
        if (proto.hasCanBeClanOwnerTsMs()) {
            this.setCanBeClanOwnerTsMs(proto.getCanBeClanOwnerTsMs());
            fieldCnt++;
        }
        if (proto.hasHasGotFirstEnterClanReward()) {
            this.setHasGotFirstEnterClanReward(proto.getHasGotFirstEnterClanReward());
            fieldCnt++;
        }
        if (proto.hasClanTechModel()) {
            this.getClanTechModel().mergeChangeFromSs(proto.getClanTechModel());
            fieldCnt++;
        }
        if (proto.hasAlreadyRecommendClanIds()) {
            this.getAlreadyRecommendClanIds().mergeChangeFromSs(proto.getAlreadyRecommendClanIds());
            fieldCnt++;
        }
        if (proto.hasCurClanGiftIndex()) {
            this.setCurClanGiftIndex(proto.getCurClanGiftIndex());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanInfo.Builder builder = ClanInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_APPLYCLANLIST) && this.applyClanList != null) {
            this.applyClanList.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SCOREINFO) && this.scoreInfo != null) {
            this.scoreInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            this.resources.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_GIFTITEMS) && this.giftItems != null) {
            this.giftItems.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TREASUREGIFTITEMS) && this.treasureGiftItems != null) {
            this.treasureGiftItems.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCLANHELPMODEL) && this.playerClanHelpModel != null) {
            this.playerClanHelpModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CLANTECHMODEL) && this.clanTechModel != null) {
            this.clanTechModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ALREADYRECOMMENDCLANIDS) && this.alreadyRecommendClanIds != null) {
            this.alreadyRecommendClanIds.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.applyClanList != null) {
            this.applyClanList.markAll();
        }
        if (this.scoreInfo != null) {
            this.scoreInfo.markAll();
        }
        if (this.resources != null) {
            this.resources.markAll();
        }
        if (this.giftItems != null) {
            this.giftItems.markAll();
        }
        if (this.treasureGiftItems != null) {
            this.treasureGiftItems.markAll();
        }
        if (this.playerClanHelpModel != null) {
            this.playerClanHelpModel.markAll();
        }
        if (this.clanTechModel != null) {
            this.clanTechModel.markAll();
        }
        if (this.alreadyRecommendClanIds != null) {
            this.alreadyRecommendClanIds.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanInfoProp)) {
            return false;
        }
        final ClanInfoProp otherNode = (ClanInfoProp) node;
        if (this.clanId != otherNode.clanId) {
            return false;
        }
        if (this.enterTime != otherNode.enterTime) {
            return false;
        }
        if (this.quitTime != otherNode.quitTime) {
            return false;
        }
        if (!this.getApplyClanList().compareDataTo(otherNode.getApplyClanList())) {
            return false;
        }
        if (this.obtainPowerRewardTsMs != otherNode.obtainPowerRewardTsMs) {
            return false;
        }
        if (!this.getScoreInfo().compareDataTo(otherNode.getScoreInfo())) {
            return false;
        }
        if (!this.getResources().compareDataTo(otherNode.getResources())) {
            return false;
        }
        if (!this.getGiftItems().compareDataTo(otherNode.getGiftItems())) {
            return false;
        }
        if (!this.getTreasureGiftItems().compareDataTo(otherNode.getTreasureGiftItems())) {
            return false;
        }
        if (this.totalClanScore != otherNode.totalClanScore) {
            return false;
        }
        if (!this.getPlayerClanHelpModel().compareDataTo(otherNode.getPlayerClanHelpModel())) {
            return false;
        }
        if (this.staffId != otherNode.staffId) {
            return false;
        }
        if (this.canBeClanOwnerTsMs != otherNode.canBeClanOwnerTsMs) {
            return false;
        }
        if (this.hasGotFirstEnterClanReward != otherNode.hasGotFirstEnterClanReward) {
            return false;
        }
        if (!this.getClanTechModel().compareDataTo(otherNode.getClanTechModel())) {
            return false;
        }
        if (!this.getAlreadyRecommendClanIds().compareDataTo(otherNode.getAlreadyRecommendClanIds())) {
            return false;
        }
        if (this.curClanGiftIndex != otherNode.curClanGiftIndex) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 47;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
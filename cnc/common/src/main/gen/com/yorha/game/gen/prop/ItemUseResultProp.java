package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.ItemUseResult;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.ItemUseResultPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ItemUseResultProp extends AbstractPropNode {

    public static final int FIELD_INDEX_POINT = 0;
    public static final int FIELD_INDEX_ITEMS = 1;
    public static final int FIELD_INDEX_CURRENCY = 2;
    public static final int FIELD_INDEX_NEWPLAYERNAME = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private PointProp point = null;
    private ItemListProp items = null;
    private CurrencyListProp currency = null;
    private String newPlayerName = Constant.DEFAULT_STR_VALUE;

    public ItemUseResultProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ItemUseResultProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get point
     *
     * @return point value
     */
    public PointProp getPoint() {
        if (this.point == null) {
            this.point = new PointProp(this, FIELD_INDEX_POINT);
        }
        return this.point;
    }

    /**
     * get items
     *
     * @return items value
     */
    public ItemListProp getItems() {
        if (this.items == null) {
            this.items = new ItemListProp(this, FIELD_INDEX_ITEMS);
        }
        return this.items;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addItems(ItemProp v) {
        this.getItems().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ItemProp getItemsIndex(int index) {
        return this.getItems().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public ItemProp removeItems(ItemProp v) {
        if (this.items == null) {
            return null;
        }
        if(this.items.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getItemsSize() {
        if (this.items == null) {
            return 0;
        }
        return this.items.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isItemsEmpty() {
        if (this.items == null) {
            return true;
        }
        return this.getItems().isEmpty();
    }

    /**
     * clear list
     */
    public void clearItems() {
        this.getItems().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ItemProp removeItemsIndex(int index) {
        return this.getItems().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public ItemProp setItemsIndex(int index, ItemProp v) {
        return this.getItems().set(index, v);
    }
    /**
     * get currency
     *
     * @return currency value
     */
    public CurrencyListProp getCurrency() {
        if (this.currency == null) {
            this.currency = new CurrencyListProp(this, FIELD_INDEX_CURRENCY);
        }
        return this.currency;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addCurrency(CurrencyProp v) {
        this.getCurrency().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public CurrencyProp getCurrencyIndex(int index) {
        return this.getCurrency().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public CurrencyProp removeCurrency(CurrencyProp v) {
        if (this.currency == null) {
            return null;
        }
        if(this.currency.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getCurrencySize() {
        if (this.currency == null) {
            return 0;
        }
        return this.currency.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isCurrencyEmpty() {
        if (this.currency == null) {
            return true;
        }
        return this.getCurrency().isEmpty();
    }

    /**
     * clear list
     */
    public void clearCurrency() {
        this.getCurrency().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public CurrencyProp removeCurrencyIndex(int index) {
        return this.getCurrency().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public CurrencyProp setCurrencyIndex(int index, CurrencyProp v) {
        return this.getCurrency().set(index, v);
    }
    /**
     * get newPlayerName
     *
     * @return newPlayerName value
     */
    public String getNewPlayerName() {
        return this.newPlayerName;
    }

    /**
     * set newPlayerName && set marked
     *
     * @param newPlayerName new value
     * @return current object
     */
    public ItemUseResultProp setNewPlayerName(String newPlayerName) {
        if (newPlayerName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.newPlayerName, newPlayerName)) {
            this.mark(FIELD_INDEX_NEWPLAYERNAME);
            this.newPlayerName = newPlayerName;
        }
        return this;
    }

    /**
     * inner set newPlayerName
     *
     * @param newPlayerName new value
     */
    private void innerSetNewPlayerName(String newPlayerName) {
        this.newPlayerName = newPlayerName;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ItemUseResultPB.Builder getCopyCsBuilder() {
        final ItemUseResultPB.Builder builder = ItemUseResultPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ItemUseResultPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.point != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.point.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.items != null) {
            StructPB.ItemListPB.Builder tmpBuilder = StructPB.ItemListPB.newBuilder();
            final int tmpFieldCnt = this.items.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItems();
            }
        }  else if (builder.hasItems()) {
            // 清理Items
            builder.clearItems();
            fieldCnt++;
        }
        if (this.currency != null) {
            StructPB.CurrencyListPB.Builder tmpBuilder = StructPB.CurrencyListPB.newBuilder();
            final int tmpFieldCnt = this.currency.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCurrency(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCurrency();
            }
        }  else if (builder.hasCurrency()) {
            // 清理Currency
            builder.clearCurrency();
            fieldCnt++;
        }
        if (!this.getNewPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setNewPlayerName(this.getNewPlayerName());
            fieldCnt++;
        }  else if (builder.hasNewPlayerName()) {
            // 清理NewPlayerName
            builder.clearNewPlayerName();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ItemUseResultPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMS) && this.items != null) {
            final boolean needClear = !builder.hasItems();
            final int tmpFieldCnt = this.items.copyChangeToCs(builder.getItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_CURRENCY) && this.currency != null) {
            final boolean needClear = !builder.hasCurrency();
            final int tmpFieldCnt = this.currency.copyChangeToCs(builder.getCurrencyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCurrency();
            }
        }
        if (this.hasMark(FIELD_INDEX_NEWPLAYERNAME)) {
            builder.setNewPlayerName(this.getNewPlayerName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ItemUseResultPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToAndClearDeleteKeysCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMS) && this.items != null) {
            final boolean needClear = !builder.hasItems();
            final int tmpFieldCnt = this.items.copyChangeToCs(builder.getItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_CURRENCY) && this.currency != null) {
            final boolean needClear = !builder.hasCurrency();
            final int tmpFieldCnt = this.currency.copyChangeToCs(builder.getCurrencyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCurrency();
            }
        }
        if (this.hasMark(FIELD_INDEX_NEWPLAYERNAME)) {
            builder.setNewPlayerName(this.getNewPlayerName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ItemUseResultPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromCs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromCs(proto.getPoint());
            }
        }
        if (proto.hasItems()) {
            this.getItems().mergeFromCs(proto.getItems());
        } else {
            if (this.items != null) {
                this.items.mergeFromCs(proto.getItems());
            }
        }
        if (proto.hasCurrency()) {
            this.getCurrency().mergeFromCs(proto.getCurrency());
        } else {
            if (this.currency != null) {
                this.currency.mergeFromCs(proto.getCurrency());
            }
        }
        if (proto.hasNewPlayerName()) {
            this.innerSetNewPlayerName(proto.getNewPlayerName());
        } else {
            this.innerSetNewPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return ItemUseResultProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ItemUseResultPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromCs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasItems()) {
            this.getItems().mergeChangeFromCs(proto.getItems());
            fieldCnt++;
        }
        if (proto.hasCurrency()) {
            this.getCurrency().mergeChangeFromCs(proto.getCurrency());
            fieldCnt++;
        }
        if (proto.hasNewPlayerName()) {
            this.setNewPlayerName(proto.getNewPlayerName());
            fieldCnt++;
        }
        return fieldCnt;
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ItemUseResult.Builder getCopySsBuilder() {
        final ItemUseResult.Builder builder = ItemUseResult.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ItemUseResult.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.items != null) {
            Struct.ItemList.Builder tmpBuilder = Struct.ItemList.newBuilder();
            final int tmpFieldCnt = this.items.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItems();
            }
        }  else if (builder.hasItems()) {
            // 清理Items
            builder.clearItems();
            fieldCnt++;
        }
        if (this.currency != null) {
            Struct.CurrencyList.Builder tmpBuilder = Struct.CurrencyList.newBuilder();
            final int tmpFieldCnt = this.currency.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCurrency(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCurrency();
            }
        }  else if (builder.hasCurrency()) {
            // 清理Currency
            builder.clearCurrency();
            fieldCnt++;
        }
        if (!this.getNewPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setNewPlayerName(this.getNewPlayerName());
            fieldCnt++;
        }  else if (builder.hasNewPlayerName()) {
            // 清理NewPlayerName
            builder.clearNewPlayerName();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ItemUseResult.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToSs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMS) && this.items != null) {
            final boolean needClear = !builder.hasItems();
            final int tmpFieldCnt = this.items.copyChangeToSs(builder.getItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_CURRENCY) && this.currency != null) {
            final boolean needClear = !builder.hasCurrency();
            final int tmpFieldCnt = this.currency.copyChangeToSs(builder.getCurrencyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCurrency();
            }
        }
        if (this.hasMark(FIELD_INDEX_NEWPLAYERNAME)) {
            builder.setNewPlayerName(this.getNewPlayerName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ItemUseResult proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromSs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromSs(proto.getPoint());
            }
        }
        if (proto.hasItems()) {
            this.getItems().mergeFromSs(proto.getItems());
        } else {
            if (this.items != null) {
                this.items.mergeFromSs(proto.getItems());
            }
        }
        if (proto.hasCurrency()) {
            this.getCurrency().mergeFromSs(proto.getCurrency());
        } else {
            if (this.currency != null) {
                this.currency.mergeFromSs(proto.getCurrency());
            }
        }
        if (proto.hasNewPlayerName()) {
            this.innerSetNewPlayerName(proto.getNewPlayerName());
        } else {
            this.innerSetNewPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return ItemUseResultProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ItemUseResult proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromSs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasItems()) {
            this.getItems().mergeChangeFromSs(proto.getItems());
            fieldCnt++;
        }
        if (proto.hasCurrency()) {
            this.getCurrency().mergeChangeFromSs(proto.getCurrency());
            fieldCnt++;
        }
        if (proto.hasNewPlayerName()) {
            this.setNewPlayerName(proto.getNewPlayerName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ItemUseResult.Builder builder = ItemUseResult.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            this.point.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ITEMS) && this.items != null) {
            this.items.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CURRENCY) && this.currency != null) {
            this.currency.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.point != null) {
            this.point.markAll();
        }
        if (this.items != null) {
            this.items.markAll();
        }
        if (this.currency != null) {
            this.currency.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ItemUseResultProp)) {
            return false;
        }
        final ItemUseResultProp otherNode = (ItemUseResultProp) node;
        if (!this.getPoint().compareDataTo(otherNode.getPoint())) {
            return false;
        }
        if (!this.getItems().compareDataTo(otherNode.getItems())) {
            return false;
        }
        if (!this.getCurrency().compareDataTo(otherNode.getCurrency())) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.newPlayerName, otherNode.newPlayerName)) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
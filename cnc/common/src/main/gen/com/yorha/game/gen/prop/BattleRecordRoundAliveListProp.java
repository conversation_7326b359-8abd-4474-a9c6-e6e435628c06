package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructBattlePB.BattleRecordRoundAliveListPB;
import com.yorha.proto.StructBattle.BattleRecordRoundAliveList;
import com.yorha.proto.StructBattlePB.BattleRecordRoundAlivePB;
import com.yorha.proto.StructBattle.BattleRecordRoundAlive;

/**
 * <AUTHOR> auto gen
 */
public class BattleRecordRoundAliveListProp extends AbstractListNode<BattleRecordRoundAliveProp> {
    /**
     * Create a BattleRecordRoundAliveListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public BattleRecordRoundAliveListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to BattleRecordRoundAliveListProp
     *
     * @return new object
     */
    @Override
    public BattleRecordRoundAliveProp addEmptyValue() {
        final BattleRecordRoundAliveProp newProp = new BattleRecordRoundAliveProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRoundAliveListPB.Builder getCopyCsBuilder() {
        final BattleRecordRoundAliveListPB.Builder builder = BattleRecordRoundAliveListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(BattleRecordRoundAliveListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return BattleRecordRoundAliveListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final BattleRecordRoundAliveProp v : this) {
            final BattleRecordRoundAlivePB.Builder itemBuilder = BattleRecordRoundAlivePB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return BattleRecordRoundAliveListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(BattleRecordRoundAliveListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return BattleRecordRoundAliveListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(BattleRecordRoundAliveListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (BattleRecordRoundAlivePB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return BattleRecordRoundAliveListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(BattleRecordRoundAliveListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRoundAliveList.Builder getCopyDbBuilder() {
        final BattleRecordRoundAliveList.Builder builder = BattleRecordRoundAliveList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(BattleRecordRoundAliveList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return BattleRecordRoundAliveListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final BattleRecordRoundAliveProp v : this) {
            final BattleRecordRoundAlive.Builder itemBuilder = BattleRecordRoundAlive.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return BattleRecordRoundAliveListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(BattleRecordRoundAliveList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return BattleRecordRoundAliveListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(BattleRecordRoundAliveList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (BattleRecordRoundAlive v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return BattleRecordRoundAliveListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(BattleRecordRoundAliveList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRoundAliveList.Builder getCopySsBuilder() {
        final BattleRecordRoundAliveList.Builder builder = BattleRecordRoundAliveList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(BattleRecordRoundAliveList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return BattleRecordRoundAliveListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final BattleRecordRoundAliveProp v : this) {
            final BattleRecordRoundAlive.Builder itemBuilder = BattleRecordRoundAlive.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return BattleRecordRoundAliveListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(BattleRecordRoundAliveList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return BattleRecordRoundAliveListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(BattleRecordRoundAliveList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (BattleRecordRoundAlive v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return BattleRecordRoundAliveListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(BattleRecordRoundAliveList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        BattleRecordRoundAliveList.Builder builder = BattleRecordRoundAliveList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
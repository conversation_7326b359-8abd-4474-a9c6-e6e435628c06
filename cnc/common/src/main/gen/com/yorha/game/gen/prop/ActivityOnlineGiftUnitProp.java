package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityOnlineGiftUnit;
import com.yorha.proto.Basic;
import com.yorha.proto.StructPB.ActivityOnlineGiftUnitPB;
import com.yorha.proto.BasicPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityOnlineGiftUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_STARTTSMS = 0;
    public static final int FIELD_INDEX_ENDTSMS = 1;
    public static final int FIELD_INDEX_GIFTHISTORY = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private long startTsMs = Constant.DEFAULT_LONG_VALUE;
    private long endTsMs = Constant.DEFAULT_LONG_VALUE;
    private Int32ListProp giftHistory = null;

    public ActivityOnlineGiftUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityOnlineGiftUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get startTsMs
     *
     * @return startTsMs value
     */
    public long getStartTsMs() {
        return this.startTsMs;
    }

    /**
     * set startTsMs && set marked
     *
     * @param startTsMs new value
     * @return current object
     */
    public ActivityOnlineGiftUnitProp setStartTsMs(long startTsMs) {
        if (this.startTsMs != startTsMs) {
            this.mark(FIELD_INDEX_STARTTSMS);
            this.startTsMs = startTsMs;
        }
        return this;
    }

    /**
     * inner set startTsMs
     *
     * @param startTsMs new value
     */
    private void innerSetStartTsMs(long startTsMs) {
        this.startTsMs = startTsMs;
    }

    /**
     * get endTsMs
     *
     * @return endTsMs value
     */
    public long getEndTsMs() {
        return this.endTsMs;
    }

    /**
     * set endTsMs && set marked
     *
     * @param endTsMs new value
     * @return current object
     */
    public ActivityOnlineGiftUnitProp setEndTsMs(long endTsMs) {
        if (this.endTsMs != endTsMs) {
            this.mark(FIELD_INDEX_ENDTSMS);
            this.endTsMs = endTsMs;
        }
        return this;
    }

    /**
     * inner set endTsMs
     *
     * @param endTsMs new value
     */
    private void innerSetEndTsMs(long endTsMs) {
        this.endTsMs = endTsMs;
    }

    /**
     * get giftHistory
     *
     * @return giftHistory value
     */
    public Int32ListProp getGiftHistory() {
        if (this.giftHistory == null) {
            this.giftHistory = new Int32ListProp(this, FIELD_INDEX_GIFTHISTORY);
        }
        return this.giftHistory;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addGiftHistory(Integer v) {
        this.getGiftHistory().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getGiftHistoryIndex(int index) {
        return this.getGiftHistory().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeGiftHistory(Integer v) {
        if (this.giftHistory == null) {
            return null;
        }
        if(this.giftHistory.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getGiftHistorySize() {
        if (this.giftHistory == null) {
            return 0;
        }
        return this.giftHistory.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isGiftHistoryEmpty() {
        if (this.giftHistory == null) {
            return true;
        }
        return this.getGiftHistory().isEmpty();
    }

    /**
     * clear list
     */
    public void clearGiftHistory() {
        this.getGiftHistory().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeGiftHistoryIndex(int index) {
        return this.getGiftHistory().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setGiftHistoryIndex(int index, Integer v) {
        return this.getGiftHistory().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityOnlineGiftUnitPB.Builder getCopyCsBuilder() {
        final ActivityOnlineGiftUnitPB.Builder builder = ActivityOnlineGiftUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityOnlineGiftUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getStartTsMs() != 0L) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStartTsMs()) {
            // 清理StartTsMs
            builder.clearStartTsMs();
            fieldCnt++;
        }
        if (this.getEndTsMs() != 0L) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }  else if (builder.hasEndTsMs()) {
            // 清理EndTsMs
            builder.clearEndTsMs();
            fieldCnt++;
        }
        if (this.giftHistory != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.giftHistory.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGiftHistory(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGiftHistory();
            }
        }  else if (builder.hasGiftHistory()) {
            // 清理GiftHistory
            builder.clearGiftHistory();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityOnlineGiftUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTHISTORY) && this.giftHistory != null) {
            final boolean needClear = !builder.hasGiftHistory();
            final int tmpFieldCnt = this.giftHistory.copyChangeToCs(builder.getGiftHistoryBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftHistory();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityOnlineGiftUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTHISTORY) && this.giftHistory != null) {
            final boolean needClear = !builder.hasGiftHistory();
            final int tmpFieldCnt = this.giftHistory.copyChangeToCs(builder.getGiftHistoryBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftHistory();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityOnlineGiftUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasStartTsMs()) {
            this.innerSetStartTsMs(proto.getStartTsMs());
        } else {
            this.innerSetStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTsMs()) {
            this.innerSetEndTsMs(proto.getEndTsMs());
        } else {
            this.innerSetEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasGiftHistory()) {
            this.getGiftHistory().mergeFromCs(proto.getGiftHistory());
        } else {
            if (this.giftHistory != null) {
                this.giftHistory.mergeFromCs(proto.getGiftHistory());
            }
        }
        this.markAll();
        return ActivityOnlineGiftUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityOnlineGiftUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasStartTsMs()) {
            this.setStartTsMs(proto.getStartTsMs());
            fieldCnt++;
        }
        if (proto.hasEndTsMs()) {
            this.setEndTsMs(proto.getEndTsMs());
            fieldCnt++;
        }
        if (proto.hasGiftHistory()) {
            this.getGiftHistory().mergeChangeFromCs(proto.getGiftHistory());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityOnlineGiftUnit.Builder getCopyDbBuilder() {
        final ActivityOnlineGiftUnit.Builder builder = ActivityOnlineGiftUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityOnlineGiftUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getStartTsMs() != 0L) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStartTsMs()) {
            // 清理StartTsMs
            builder.clearStartTsMs();
            fieldCnt++;
        }
        if (this.getEndTsMs() != 0L) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }  else if (builder.hasEndTsMs()) {
            // 清理EndTsMs
            builder.clearEndTsMs();
            fieldCnt++;
        }
        if (this.giftHistory != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.giftHistory.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGiftHistory(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGiftHistory();
            }
        }  else if (builder.hasGiftHistory()) {
            // 清理GiftHistory
            builder.clearGiftHistory();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityOnlineGiftUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTHISTORY) && this.giftHistory != null) {
            final boolean needClear = !builder.hasGiftHistory();
            final int tmpFieldCnt = this.giftHistory.copyChangeToDb(builder.getGiftHistoryBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftHistory();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityOnlineGiftUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasStartTsMs()) {
            this.innerSetStartTsMs(proto.getStartTsMs());
        } else {
            this.innerSetStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTsMs()) {
            this.innerSetEndTsMs(proto.getEndTsMs());
        } else {
            this.innerSetEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasGiftHistory()) {
            this.getGiftHistory().mergeFromDb(proto.getGiftHistory());
        } else {
            if (this.giftHistory != null) {
                this.giftHistory.mergeFromDb(proto.getGiftHistory());
            }
        }
        this.markAll();
        return ActivityOnlineGiftUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityOnlineGiftUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasStartTsMs()) {
            this.setStartTsMs(proto.getStartTsMs());
            fieldCnt++;
        }
        if (proto.hasEndTsMs()) {
            this.setEndTsMs(proto.getEndTsMs());
            fieldCnt++;
        }
        if (proto.hasGiftHistory()) {
            this.getGiftHistory().mergeChangeFromDb(proto.getGiftHistory());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityOnlineGiftUnit.Builder getCopySsBuilder() {
        final ActivityOnlineGiftUnit.Builder builder = ActivityOnlineGiftUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityOnlineGiftUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getStartTsMs() != 0L) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStartTsMs()) {
            // 清理StartTsMs
            builder.clearStartTsMs();
            fieldCnt++;
        }
        if (this.getEndTsMs() != 0L) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }  else if (builder.hasEndTsMs()) {
            // 清理EndTsMs
            builder.clearEndTsMs();
            fieldCnt++;
        }
        if (this.giftHistory != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.giftHistory.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGiftHistory(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGiftHistory();
            }
        }  else if (builder.hasGiftHistory()) {
            // 清理GiftHistory
            builder.clearGiftHistory();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityOnlineGiftUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTHISTORY) && this.giftHistory != null) {
            final boolean needClear = !builder.hasGiftHistory();
            final int tmpFieldCnt = this.giftHistory.copyChangeToSs(builder.getGiftHistoryBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftHistory();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityOnlineGiftUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasStartTsMs()) {
            this.innerSetStartTsMs(proto.getStartTsMs());
        } else {
            this.innerSetStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTsMs()) {
            this.innerSetEndTsMs(proto.getEndTsMs());
        } else {
            this.innerSetEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasGiftHistory()) {
            this.getGiftHistory().mergeFromSs(proto.getGiftHistory());
        } else {
            if (this.giftHistory != null) {
                this.giftHistory.mergeFromSs(proto.getGiftHistory());
            }
        }
        this.markAll();
        return ActivityOnlineGiftUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityOnlineGiftUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasStartTsMs()) {
            this.setStartTsMs(proto.getStartTsMs());
            fieldCnt++;
        }
        if (proto.hasEndTsMs()) {
            this.setEndTsMs(proto.getEndTsMs());
            fieldCnt++;
        }
        if (proto.hasGiftHistory()) {
            this.getGiftHistory().mergeChangeFromSs(proto.getGiftHistory());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityOnlineGiftUnit.Builder builder = ActivityOnlineGiftUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_GIFTHISTORY) && this.giftHistory != null) {
            this.giftHistory.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.giftHistory != null) {
            this.giftHistory.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityOnlineGiftUnitProp)) {
            return false;
        }
        final ActivityOnlineGiftUnitProp otherNode = (ActivityOnlineGiftUnitProp) node;
        if (this.startTsMs != otherNode.startTsMs) {
            return false;
        }
        if (this.endTsMs != otherNode.endTsMs) {
            return false;
        }
        if (!this.getGiftHistory().compareDataTo(otherNode.getGiftHistory())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
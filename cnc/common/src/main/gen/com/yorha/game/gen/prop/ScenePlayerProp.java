package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Player.ScenePlayer;
import com.yorha.proto.Player;
import com.yorha.proto.Struct;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.PlayerPB.ScenePlayerPB;
import com.yorha.proto.PlayerPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructBattlePB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class ScenePlayerProp extends AbstractPropNode {

    public static final int FIELD_INDEX_MAINCITYID = 0;
    public static final int FIELD_INDEX_CLANID = 1;
    public static final int FIELD_INDEX_LOGOUTTIME = 2;
    public static final int FIELD_INDEX_SOLDIER = 3;
    public static final int FIELD_INDEX_CAMP = 4;
    public static final int FIELD_INDEX_RALLY = 5;
    public static final int FIELD_INDEX_WARNING = 6;
    public static final int FIELD_INDEX_PICKLIMIT = 7;
    public static final int FIELD_INDEX_ADDITIONSYS = 8;
    public static final int FIELD_INDEX_CREATETIME = 9;
    public static final int FIELD_INDEX_WALL = 10;
    public static final int FIELD_INDEX_KILLMONSTERMAXLEVEL = 11;
    public static final int FIELD_INDEX_PLUNDERMODEL = 12;
    public static final int FIELD_INDEX_CITYBUILDLEVELMAP = 13;
    public static final int FIELD_INDEX_POSMARKMODEL = 14;
    public static final int FIELD_INDEX_HOSPITALMODEL = 15;
    public static final int FIELD_INDEX_ARMYMODEL = 16;
    public static final int FIELD_INDEX_TECHMODEL = 17;
    public static final int FIELD_INDEX_CARDHEAD = 18;
    public static final int FIELD_INDEX_LOGISTICSMODEL = 19;
    public static final int FIELD_INDEX_DEVBUFFSYSNEW = 20;
    public static final int FIELD_INDEX_POWERMODEL = 21;
    public static final int FIELD_INDEX_ZONEMODEL = 22;
    public static final int FIELD_INDEX_PUSHNTFMODEL = 23;
    public static final int FIELD_INDEX_MONSTERMODEL = 24;

    public static final int FIELD_COUNT = 25;

    private long markBits0 = 0L;
    private PropertyChangeListener listener;

    private long mainCityId = Constant.DEFAULT_LONG_VALUE;
    private long clanId = Constant.DEFAULT_LONG_VALUE;
    private long logoutTime = Constant.DEFAULT_LONG_VALUE;
    private ScenePlayerSoldierProp soldier = null;
    private Camp camp = Camp.forNumber(0);
    private ScenePlayerRallyBaseProp rally = null;
    private WarningItemListProp warning = null;
    private Int32PlayerPickLimitMapProp pickLimit = null;
    private AdditionSysProp additionSys = null;
    private long createTime = Constant.DEFAULT_LONG_VALUE;
    private WallInfoProp wall = null;
    private int killMonsterMaxLevel = Constant.DEFAULT_INT_VALUE;
    private ScenePlayerPlunderModelProp plunderModel = null;
    private Int32CityBuildInfoMapProp cityBuildLevelMap = null;
    private ScenePlayerPosMarkModelProp posMarkModel = null;
    private ScenePlayerHospitalModelProp hospitalModel = null;
    private ScenePlayerArmyModelProp armyModel = null;
    private ScenePlayerTechModelProp techModel = null;
    private PlayerCardHeadProp cardHead = null;
    private ScenePlayerLogisticsModelProp logisticsModel = null;
    private DevBuffSysProp devBuffSysNew = null;
    private ScenePowerModelProp powerModel = null;
    private ScenePlayerZoneModelProp zoneModel = null;
    private ScenePlayerPushNtfModelProp pushNtfModel = null;
    private ScenePlayerMonsterModelProp monsterModel = null;

    public ScenePlayerProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ScenePlayerProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get mainCityId
     *
     * @return mainCityId value
     */
    public long getMainCityId() {
        return this.mainCityId;
    }

    /**
     * set mainCityId && set marked
     *
     * @param mainCityId new value
     * @return current object
     */
    public ScenePlayerProp setMainCityId(long mainCityId) {
        if (this.mainCityId != mainCityId) {
            this.mark(FIELD_INDEX_MAINCITYID);
            this.mainCityId = mainCityId;
        }
        return this;
    }

    /**
     * inner set mainCityId
     *
     * @param mainCityId new value
     */
    private void innerSetMainCityId(long mainCityId) {
        this.mainCityId = mainCityId;
    }

    /**
     * get clanId
     *
     * @return clanId value
     */
    public long getClanId() {
        return this.clanId;
    }

    /**
     * set clanId && set marked
     *
     * @param clanId new value
     * @return current object
     */
    public ScenePlayerProp setClanId(long clanId) {
        if (this.clanId != clanId) {
            this.mark(FIELD_INDEX_CLANID);
            this.clanId = clanId;
        }
        return this;
    }

    /**
     * inner set clanId
     *
     * @param clanId new value
     */
    private void innerSetClanId(long clanId) {
        this.clanId = clanId;
    }

    /**
     * get logoutTime
     *
     * @return logoutTime value
     */
    public long getLogoutTime() {
        return this.logoutTime;
    }

    /**
     * set logoutTime && set marked
     *
     * @param logoutTime new value
     * @return current object
     */
    public ScenePlayerProp setLogoutTime(long logoutTime) {
        if (this.logoutTime != logoutTime) {
            this.mark(FIELD_INDEX_LOGOUTTIME);
            this.logoutTime = logoutTime;
        }
        return this;
    }

    /**
     * inner set logoutTime
     *
     * @param logoutTime new value
     */
    private void innerSetLogoutTime(long logoutTime) {
        this.logoutTime = logoutTime;
    }

    /**
     * get soldier
     *
     * @return soldier value
     */
    public ScenePlayerSoldierProp getSoldier() {
        if (this.soldier == null) {
            this.soldier = new ScenePlayerSoldierProp(this, FIELD_INDEX_SOLDIER);
        }
        return this.soldier;
    }

    /**
     * get camp
     *
     * @return camp value
     */
    public Camp getCamp() {
        return this.camp;
    }

    /**
     * set camp && set marked
     *
     * @param camp new value
     * @return current object
     */
    public ScenePlayerProp setCamp(Camp camp) {
        if (camp == null) {
            throw new NullPointerException();
        }
        if (this.camp != camp) {
            this.mark(FIELD_INDEX_CAMP);
            this.camp = camp;
        }
        return this;
    }

    /**
     * inner set camp
     *
     * @param camp new value
     */
    private void innerSetCamp(Camp camp) {
        this.camp = camp;
    }

    /**
     * get rally
     *
     * @return rally value
     */
    public ScenePlayerRallyBaseProp getRally() {
        if (this.rally == null) {
            this.rally = new ScenePlayerRallyBaseProp(this, FIELD_INDEX_RALLY);
        }
        return this.rally;
    }

    /**
     * get warning
     *
     * @return warning value
     */
    public WarningItemListProp getWarning() {
        if (this.warning == null) {
            this.warning = new WarningItemListProp(this, FIELD_INDEX_WARNING);
        }
        return this.warning;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addWarning(WarningItemProp v) {
        this.getWarning().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public WarningItemProp getWarningIndex(int index) {
        return this.getWarning().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public WarningItemProp removeWarning(WarningItemProp v) {
        if (this.warning == null) {
            return null;
        }
        if(this.warning.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getWarningSize() {
        if (this.warning == null) {
            return 0;
        }
        return this.warning.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isWarningEmpty() {
        if (this.warning == null) {
            return true;
        }
        return this.getWarning().isEmpty();
    }

    /**
     * clear list
     */
    public void clearWarning() {
        this.getWarning().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public WarningItemProp removeWarningIndex(int index) {
        return this.getWarning().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public WarningItemProp setWarningIndex(int index, WarningItemProp v) {
        return this.getWarning().set(index, v);
    }
    /**
     * get pickLimit
     *
     * @return pickLimit value
     */
    public Int32PlayerPickLimitMapProp getPickLimit() {
        if (this.pickLimit == null) {
            this.pickLimit = new Int32PlayerPickLimitMapProp(this, FIELD_INDEX_PICKLIMIT);
        }
        return this.pickLimit;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putPickLimitV(PlayerPickLimitProp v) {
        this.getPickLimit().put(v.getGroupId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerPickLimitProp addEmptyPickLimit(Integer k) {
        return this.getPickLimit().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getPickLimitSize() {
        if (this.pickLimit == null) {
            return 0;
        }
        return this.pickLimit.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isPickLimitEmpty() {
        if (this.pickLimit == null) {
            return true;
        }
        return this.pickLimit.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerPickLimitProp getPickLimitV(Integer k) {
        if (this.pickLimit == null || !this.pickLimit.containsKey(k)) {
            return null;
        }
        return this.pickLimit.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearPickLimit() {
        if (this.pickLimit != null) {
            this.pickLimit.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removePickLimitV(Integer k) {
        if (this.pickLimit != null) {
            this.pickLimit.remove(k);
        }
    }
    /**
     * get additionSys
     *
     * @return additionSys value
     */
    public AdditionSysProp getAdditionSys() {
        if (this.additionSys == null) {
            this.additionSys = new AdditionSysProp(this, FIELD_INDEX_ADDITIONSYS);
        }
        return this.additionSys;
    }

    /**
     * get createTime
     *
     * @return createTime value
     */
    public long getCreateTime() {
        return this.createTime;
    }

    /**
     * set createTime && set marked
     *
     * @param createTime new value
     * @return current object
     */
    public ScenePlayerProp setCreateTime(long createTime) {
        if (this.createTime != createTime) {
            this.mark(FIELD_INDEX_CREATETIME);
            this.createTime = createTime;
        }
        return this;
    }

    /**
     * inner set createTime
     *
     * @param createTime new value
     */
    private void innerSetCreateTime(long createTime) {
        this.createTime = createTime;
    }

    /**
     * get wall
     *
     * @return wall value
     */
    public WallInfoProp getWall() {
        if (this.wall == null) {
            this.wall = new WallInfoProp(this, FIELD_INDEX_WALL);
        }
        return this.wall;
    }

    /**
     * get killMonsterMaxLevel
     *
     * @return killMonsterMaxLevel value
     */
    public int getKillMonsterMaxLevel() {
        return this.killMonsterMaxLevel;
    }

    /**
     * set killMonsterMaxLevel && set marked
     *
     * @param killMonsterMaxLevel new value
     * @return current object
     */
    public ScenePlayerProp setKillMonsterMaxLevel(int killMonsterMaxLevel) {
        if (this.killMonsterMaxLevel != killMonsterMaxLevel) {
            this.mark(FIELD_INDEX_KILLMONSTERMAXLEVEL);
            this.killMonsterMaxLevel = killMonsterMaxLevel;
        }
        return this;
    }

    /**
     * inner set killMonsterMaxLevel
     *
     * @param killMonsterMaxLevel new value
     */
    private void innerSetKillMonsterMaxLevel(int killMonsterMaxLevel) {
        this.killMonsterMaxLevel = killMonsterMaxLevel;
    }

    /**
     * get plunderModel
     *
     * @return plunderModel value
     */
    public ScenePlayerPlunderModelProp getPlunderModel() {
        if (this.plunderModel == null) {
            this.plunderModel = new ScenePlayerPlunderModelProp(this, FIELD_INDEX_PLUNDERMODEL);
        }
        return this.plunderModel;
    }

    /**
     * get cityBuildLevelMap
     *
     * @return cityBuildLevelMap value
     */
    public Int32CityBuildInfoMapProp getCityBuildLevelMap() {
        if (this.cityBuildLevelMap == null) {
            this.cityBuildLevelMap = new Int32CityBuildInfoMapProp(this, FIELD_INDEX_CITYBUILDLEVELMAP);
        }
        return this.cityBuildLevelMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putCityBuildLevelMapV(CityBuildInfoProp v) {
        this.getCityBuildLevelMap().put(v.getCityBuildType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public CityBuildInfoProp addEmptyCityBuildLevelMap(Integer k) {
        return this.getCityBuildLevelMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getCityBuildLevelMapSize() {
        if (this.cityBuildLevelMap == null) {
            return 0;
        }
        return this.cityBuildLevelMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isCityBuildLevelMapEmpty() {
        if (this.cityBuildLevelMap == null) {
            return true;
        }
        return this.cityBuildLevelMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public CityBuildInfoProp getCityBuildLevelMapV(Integer k) {
        if (this.cityBuildLevelMap == null || !this.cityBuildLevelMap.containsKey(k)) {
            return null;
        }
        return this.cityBuildLevelMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearCityBuildLevelMap() {
        if (this.cityBuildLevelMap != null) {
            this.cityBuildLevelMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeCityBuildLevelMapV(Integer k) {
        if (this.cityBuildLevelMap != null) {
            this.cityBuildLevelMap.remove(k);
        }
    }
    /**
     * get posMarkModel
     *
     * @return posMarkModel value
     */
    public ScenePlayerPosMarkModelProp getPosMarkModel() {
        if (this.posMarkModel == null) {
            this.posMarkModel = new ScenePlayerPosMarkModelProp(this, FIELD_INDEX_POSMARKMODEL);
        }
        return this.posMarkModel;
    }

    /**
     * get hospitalModel
     *
     * @return hospitalModel value
     */
    public ScenePlayerHospitalModelProp getHospitalModel() {
        if (this.hospitalModel == null) {
            this.hospitalModel = new ScenePlayerHospitalModelProp(this, FIELD_INDEX_HOSPITALMODEL);
        }
        return this.hospitalModel;
    }

    /**
     * get armyModel
     *
     * @return armyModel value
     */
    public ScenePlayerArmyModelProp getArmyModel() {
        if (this.armyModel == null) {
            this.armyModel = new ScenePlayerArmyModelProp(this, FIELD_INDEX_ARMYMODEL);
        }
        return this.armyModel;
    }

    /**
     * get techModel
     *
     * @return techModel value
     */
    public ScenePlayerTechModelProp getTechModel() {
        if (this.techModel == null) {
            this.techModel = new ScenePlayerTechModelProp(this, FIELD_INDEX_TECHMODEL);
        }
        return this.techModel;
    }

    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }

    /**
     * get logisticsModel
     *
     * @return logisticsModel value
     */
    public ScenePlayerLogisticsModelProp getLogisticsModel() {
        if (this.logisticsModel == null) {
            this.logisticsModel = new ScenePlayerLogisticsModelProp(this, FIELD_INDEX_LOGISTICSMODEL);
        }
        return this.logisticsModel;
    }

    /**
     * get devBuffSysNew
     *
     * @return devBuffSysNew value
     */
    public DevBuffSysProp getDevBuffSysNew() {
        if (this.devBuffSysNew == null) {
            this.devBuffSysNew = new DevBuffSysProp(this, FIELD_INDEX_DEVBUFFSYSNEW);
        }
        return this.devBuffSysNew;
    }

    /**
     * get powerModel
     *
     * @return powerModel value
     */
    public ScenePowerModelProp getPowerModel() {
        if (this.powerModel == null) {
            this.powerModel = new ScenePowerModelProp(this, FIELD_INDEX_POWERMODEL);
        }
        return this.powerModel;
    }

    /**
     * get zoneModel
     *
     * @return zoneModel value
     */
    public ScenePlayerZoneModelProp getZoneModel() {
        if (this.zoneModel == null) {
            this.zoneModel = new ScenePlayerZoneModelProp(this, FIELD_INDEX_ZONEMODEL);
        }
        return this.zoneModel;
    }

    /**
     * get pushNtfModel
     *
     * @return pushNtfModel value
     */
    public ScenePlayerPushNtfModelProp getPushNtfModel() {
        if (this.pushNtfModel == null) {
            this.pushNtfModel = new ScenePlayerPushNtfModelProp(this, FIELD_INDEX_PUSHNTFMODEL);
        }
        return this.pushNtfModel;
    }

    /**
     * get monsterModel
     *
     * @return monsterModel value
     */
    public ScenePlayerMonsterModelProp getMonsterModel() {
        if (this.monsterModel == null) {
            this.monsterModel = new ScenePlayerMonsterModelProp(this, FIELD_INDEX_MONSTERMODEL);
        }
        return this.monsterModel;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerPB.Builder getCopyCsBuilder() {
        final ScenePlayerPB.Builder builder = ScenePlayerPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ScenePlayerPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMainCityId() != 0L) {
            builder.setMainCityId(this.getMainCityId());
            fieldCnt++;
        }  else if (builder.hasMainCityId()) {
            // 清理MainCityId
            builder.clearMainCityId();
            fieldCnt++;
        }
        if (this.soldier != null) {
            StructPlayerPB.ScenePlayerSoldierPB.Builder tmpBuilder = StructPlayerPB.ScenePlayerSoldierPB.newBuilder();
            final int tmpFieldCnt = this.soldier.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldier(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldier();
            }
        }  else if (builder.hasSoldier()) {
            // 清理Soldier
            builder.clearSoldier();
            fieldCnt++;
        }
        if (this.getCamp() != Camp.forNumber(0)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }  else if (builder.hasCamp()) {
            // 清理Camp
            builder.clearCamp();
            fieldCnt++;
        }
        if (this.rally != null) {
            StructPB.ScenePlayerRallyBasePB.Builder tmpBuilder = StructPB.ScenePlayerRallyBasePB.newBuilder();
            final int tmpFieldCnt = this.rally.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRally(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRally();
            }
        }  else if (builder.hasRally()) {
            // 清理Rally
            builder.clearRally();
            fieldCnt++;
        }
        if (this.warning != null) {
            StructPB.WarningItemListPB.Builder tmpBuilder = StructPB.WarningItemListPB.newBuilder();
            final int tmpFieldCnt = this.warning.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWarning(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWarning();
            }
        }  else if (builder.hasWarning()) {
            // 清理Warning
            builder.clearWarning();
            fieldCnt++;
        }
        if (this.pickLimit != null) {
            StructPB.Int32PlayerPickLimitMapPB.Builder tmpBuilder = StructPB.Int32PlayerPickLimitMapPB.newBuilder();
            final int tmpFieldCnt = this.pickLimit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPickLimit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPickLimit();
            }
        }  else if (builder.hasPickLimit()) {
            // 清理PickLimit
            builder.clearPickLimit();
            fieldCnt++;
        }
        if (this.additionSys != null) {
            StructPB.AdditionSysPB.Builder tmpBuilder = StructPB.AdditionSysPB.newBuilder();
            final int tmpFieldCnt = this.additionSys.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionSys();
            }
        }  else if (builder.hasAdditionSys()) {
            // 清理AdditionSys
            builder.clearAdditionSys();
            fieldCnt++;
        }
        if (this.getCreateTime() != 0L) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }  else if (builder.hasCreateTime()) {
            // 清理CreateTime
            builder.clearCreateTime();
            fieldCnt++;
        }
        if (this.wall != null) {
            StructPlayerPB.WallInfoPB.Builder tmpBuilder = StructPlayerPB.WallInfoPB.newBuilder();
            final int tmpFieldCnt = this.wall.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWall(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWall();
            }
        }  else if (builder.hasWall()) {
            // 清理Wall
            builder.clearWall();
            fieldCnt++;
        }
        if (this.getKillMonsterMaxLevel() != 0) {
            builder.setKillMonsterMaxLevel(this.getKillMonsterMaxLevel());
            fieldCnt++;
        }  else if (builder.hasKillMonsterMaxLevel()) {
            // 清理KillMonsterMaxLevel
            builder.clearKillMonsterMaxLevel();
            fieldCnt++;
        }
        if (this.posMarkModel != null) {
            PlayerPB.ScenePlayerPosMarkModelPB.Builder tmpBuilder = PlayerPB.ScenePlayerPosMarkModelPB.newBuilder();
            final int tmpFieldCnt = this.posMarkModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPosMarkModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPosMarkModel();
            }
        }  else if (builder.hasPosMarkModel()) {
            // 清理PosMarkModel
            builder.clearPosMarkModel();
            fieldCnt++;
        }
        if (this.hospitalModel != null) {
            StructPB.ScenePlayerHospitalModelPB.Builder tmpBuilder = StructPB.ScenePlayerHospitalModelPB.newBuilder();
            final int tmpFieldCnt = this.hospitalModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHospitalModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHospitalModel();
            }
        }  else if (builder.hasHospitalModel()) {
            // 清理HospitalModel
            builder.clearHospitalModel();
            fieldCnt++;
        }
        if (this.armyModel != null) {
            PlayerPB.ScenePlayerArmyModelPB.Builder tmpBuilder = PlayerPB.ScenePlayerArmyModelPB.newBuilder();
            final int tmpFieldCnt = this.armyModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArmyModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArmyModel();
            }
        }  else if (builder.hasArmyModel()) {
            // 清理ArmyModel
            builder.clearArmyModel();
            fieldCnt++;
        }
        if (this.logisticsModel != null) {
            PlayerPB.ScenePlayerLogisticsModelPB.Builder tmpBuilder = PlayerPB.ScenePlayerLogisticsModelPB.newBuilder();
            final int tmpFieldCnt = this.logisticsModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLogisticsModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLogisticsModel();
            }
        }  else if (builder.hasLogisticsModel()) {
            // 清理LogisticsModel
            builder.clearLogisticsModel();
            fieldCnt++;
        }
        if (this.devBuffSysNew != null) {
            StructBattlePB.DevBuffSysPB.Builder tmpBuilder = StructBattlePB.DevBuffSysPB.newBuilder();
            final int tmpFieldCnt = this.devBuffSysNew.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSysNew(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSysNew();
            }
        }  else if (builder.hasDevBuffSysNew()) {
            // 清理DevBuffSysNew
            builder.clearDevBuffSysNew();
            fieldCnt++;
        }
        if (this.pushNtfModel != null) {
            PlayerPB.ScenePlayerPushNtfModelPB.Builder tmpBuilder = PlayerPB.ScenePlayerPushNtfModelPB.newBuilder();
            final int tmpFieldCnt = this.pushNtfModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPushNtfModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPushNtfModel();
            }
        }  else if (builder.hasPushNtfModel()) {
            // 清理PushNtfModel
            builder.clearPushNtfModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ScenePlayerPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAINCITYID)) {
            builder.setMainCityId(this.getMainCityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIER) && this.soldier != null) {
            final boolean needClear = !builder.hasSoldier();
            final int tmpFieldCnt = this.soldier.copyChangeToCs(builder.getSoldierBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldier();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLY) && this.rally != null) {
            final boolean needClear = !builder.hasRally();
            final int tmpFieldCnt = this.rally.copyChangeToCs(builder.getRallyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRally();
            }
        }
        if (this.hasMark(FIELD_INDEX_WARNING) && this.warning != null) {
            final boolean needClear = !builder.hasWarning();
            final int tmpFieldCnt = this.warning.copyChangeToCs(builder.getWarningBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWarning();
            }
        }
        if (this.hasMark(FIELD_INDEX_PICKLIMIT) && this.pickLimit != null) {
            final boolean needClear = !builder.hasPickLimit();
            final int tmpFieldCnt = this.pickLimit.copyChangeToCs(builder.getPickLimitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPickLimit();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            final boolean needClear = !builder.hasAdditionSys();
            final int tmpFieldCnt = this.additionSys.copyChangeToCs(builder.getAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETIME)) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WALL) && this.wall != null) {
            final boolean needClear = !builder.hasWall();
            final int tmpFieldCnt = this.wall.copyChangeToCs(builder.getWallBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWall();
            }
        }
        if (this.hasMark(FIELD_INDEX_KILLMONSTERMAXLEVEL)) {
            builder.setKillMonsterMaxLevel(this.getKillMonsterMaxLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POSMARKMODEL) && this.posMarkModel != null) {
            final boolean needClear = !builder.hasPosMarkModel();
            final int tmpFieldCnt = this.posMarkModel.copyChangeToCs(builder.getPosMarkModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPosMarkModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_HOSPITALMODEL) && this.hospitalModel != null) {
            final boolean needClear = !builder.hasHospitalModel();
            final int tmpFieldCnt = this.hospitalModel.copyChangeToCs(builder.getHospitalModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHospitalModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARMYMODEL) && this.armyModel != null) {
            final boolean needClear = !builder.hasArmyModel();
            final int tmpFieldCnt = this.armyModel.copyChangeToCs(builder.getArmyModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmyModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOGISTICSMODEL) && this.logisticsModel != null) {
            final boolean needClear = !builder.hasLogisticsModel();
            final int tmpFieldCnt = this.logisticsModel.copyChangeToCs(builder.getLogisticsModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLogisticsModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYSNEW) && this.devBuffSysNew != null) {
            final boolean needClear = !builder.hasDevBuffSysNew();
            final int tmpFieldCnt = this.devBuffSysNew.copyChangeToCs(builder.getDevBuffSysNewBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSysNew();
            }
        }
        if (this.hasMark(FIELD_INDEX_PUSHNTFMODEL) && this.pushNtfModel != null) {
            final boolean needClear = !builder.hasPushNtfModel();
            final int tmpFieldCnt = this.pushNtfModel.copyChangeToCs(builder.getPushNtfModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPushNtfModel();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ScenePlayerPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAINCITYID)) {
            builder.setMainCityId(this.getMainCityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIER) && this.soldier != null) {
            final boolean needClear = !builder.hasSoldier();
            final int tmpFieldCnt = this.soldier.copyChangeToAndClearDeleteKeysCs(builder.getSoldierBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldier();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLY) && this.rally != null) {
            final boolean needClear = !builder.hasRally();
            final int tmpFieldCnt = this.rally.copyChangeToAndClearDeleteKeysCs(builder.getRallyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRally();
            }
        }
        if (this.hasMark(FIELD_INDEX_WARNING) && this.warning != null) {
            final boolean needClear = !builder.hasWarning();
            final int tmpFieldCnt = this.warning.copyChangeToCs(builder.getWarningBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWarning();
            }
        }
        if (this.hasMark(FIELD_INDEX_PICKLIMIT) && this.pickLimit != null) {
            final boolean needClear = !builder.hasPickLimit();
            final int tmpFieldCnt = this.pickLimit.copyChangeToAndClearDeleteKeysCs(builder.getPickLimitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPickLimit();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            final boolean needClear = !builder.hasAdditionSys();
            final int tmpFieldCnt = this.additionSys.copyChangeToAndClearDeleteKeysCs(builder.getAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETIME)) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WALL) && this.wall != null) {
            final boolean needClear = !builder.hasWall();
            final int tmpFieldCnt = this.wall.copyChangeToAndClearDeleteKeysCs(builder.getWallBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWall();
            }
        }
        if (this.hasMark(FIELD_INDEX_KILLMONSTERMAXLEVEL)) {
            builder.setKillMonsterMaxLevel(this.getKillMonsterMaxLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POSMARKMODEL) && this.posMarkModel != null) {
            final boolean needClear = !builder.hasPosMarkModel();
            final int tmpFieldCnt = this.posMarkModel.copyChangeToAndClearDeleteKeysCs(builder.getPosMarkModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPosMarkModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_HOSPITALMODEL) && this.hospitalModel != null) {
            final boolean needClear = !builder.hasHospitalModel();
            final int tmpFieldCnt = this.hospitalModel.copyChangeToAndClearDeleteKeysCs(builder.getHospitalModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHospitalModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARMYMODEL) && this.armyModel != null) {
            final boolean needClear = !builder.hasArmyModel();
            final int tmpFieldCnt = this.armyModel.copyChangeToAndClearDeleteKeysCs(builder.getArmyModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmyModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOGISTICSMODEL) && this.logisticsModel != null) {
            final boolean needClear = !builder.hasLogisticsModel();
            final int tmpFieldCnt = this.logisticsModel.copyChangeToAndClearDeleteKeysCs(builder.getLogisticsModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLogisticsModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYSNEW) && this.devBuffSysNew != null) {
            final boolean needClear = !builder.hasDevBuffSysNew();
            final int tmpFieldCnt = this.devBuffSysNew.copyChangeToAndClearDeleteKeysCs(builder.getDevBuffSysNewBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSysNew();
            }
        }
        if (this.hasMark(FIELD_INDEX_PUSHNTFMODEL) && this.pushNtfModel != null) {
            final boolean needClear = !builder.hasPushNtfModel();
            final int tmpFieldCnt = this.pushNtfModel.copyChangeToAndClearDeleteKeysCs(builder.getPushNtfModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPushNtfModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ScenePlayerPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMainCityId()) {
            this.innerSetMainCityId(proto.getMainCityId());
        } else {
            this.innerSetMainCityId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSoldier()) {
            this.getSoldier().mergeFromCs(proto.getSoldier());
        } else {
            if (this.soldier != null) {
                this.soldier.mergeFromCs(proto.getSoldier());
            }
        }
        if (proto.hasCamp()) {
            this.innerSetCamp(proto.getCamp());
        } else {
            this.innerSetCamp(Camp.forNumber(0));
        }
        if (proto.hasRally()) {
            this.getRally().mergeFromCs(proto.getRally());
        } else {
            if (this.rally != null) {
                this.rally.mergeFromCs(proto.getRally());
            }
        }
        if (proto.hasWarning()) {
            this.getWarning().mergeFromCs(proto.getWarning());
        } else {
            if (this.warning != null) {
                this.warning.mergeFromCs(proto.getWarning());
            }
        }
        if (proto.hasPickLimit()) {
            this.getPickLimit().mergeFromCs(proto.getPickLimit());
        } else {
            if (this.pickLimit != null) {
                this.pickLimit.mergeFromCs(proto.getPickLimit());
            }
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeFromCs(proto.getAdditionSys());
        } else {
            if (this.additionSys != null) {
                this.additionSys.mergeFromCs(proto.getAdditionSys());
            }
        }
        if (proto.hasCreateTime()) {
            this.innerSetCreateTime(proto.getCreateTime());
        } else {
            this.innerSetCreateTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasWall()) {
            this.getWall().mergeFromCs(proto.getWall());
        } else {
            if (this.wall != null) {
                this.wall.mergeFromCs(proto.getWall());
            }
        }
        if (proto.hasKillMonsterMaxLevel()) {
            this.innerSetKillMonsterMaxLevel(proto.getKillMonsterMaxLevel());
        } else {
            this.innerSetKillMonsterMaxLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPosMarkModel()) {
            this.getPosMarkModel().mergeFromCs(proto.getPosMarkModel());
        } else {
            if (this.posMarkModel != null) {
                this.posMarkModel.mergeFromCs(proto.getPosMarkModel());
            }
        }
        if (proto.hasHospitalModel()) {
            this.getHospitalModel().mergeFromCs(proto.getHospitalModel());
        } else {
            if (this.hospitalModel != null) {
                this.hospitalModel.mergeFromCs(proto.getHospitalModel());
            }
        }
        if (proto.hasArmyModel()) {
            this.getArmyModel().mergeFromCs(proto.getArmyModel());
        } else {
            if (this.armyModel != null) {
                this.armyModel.mergeFromCs(proto.getArmyModel());
            }
        }
        if (proto.hasLogisticsModel()) {
            this.getLogisticsModel().mergeFromCs(proto.getLogisticsModel());
        } else {
            if (this.logisticsModel != null) {
                this.logisticsModel.mergeFromCs(proto.getLogisticsModel());
            }
        }
        if (proto.hasDevBuffSysNew()) {
            this.getDevBuffSysNew().mergeFromCs(proto.getDevBuffSysNew());
        } else {
            if (this.devBuffSysNew != null) {
                this.devBuffSysNew.mergeFromCs(proto.getDevBuffSysNew());
            }
        }
        if (proto.hasPushNtfModel()) {
            this.getPushNtfModel().mergeFromCs(proto.getPushNtfModel());
        } else {
            if (this.pushNtfModel != null) {
                this.pushNtfModel.mergeFromCs(proto.getPushNtfModel());
            }
        }
        this.markAll();
        return ScenePlayerProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ScenePlayerPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMainCityId()) {
            this.setMainCityId(proto.getMainCityId());
            fieldCnt++;
        }
        if (proto.hasSoldier()) {
            this.getSoldier().mergeChangeFromCs(proto.getSoldier());
            fieldCnt++;
        }
        if (proto.hasCamp()) {
            this.setCamp(proto.getCamp());
            fieldCnt++;
        }
        if (proto.hasRally()) {
            this.getRally().mergeChangeFromCs(proto.getRally());
            fieldCnt++;
        }
        if (proto.hasWarning()) {
            this.getWarning().mergeChangeFromCs(proto.getWarning());
            fieldCnt++;
        }
        if (proto.hasPickLimit()) {
            this.getPickLimit().mergeChangeFromCs(proto.getPickLimit());
            fieldCnt++;
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeChangeFromCs(proto.getAdditionSys());
            fieldCnt++;
        }
        if (proto.hasCreateTime()) {
            this.setCreateTime(proto.getCreateTime());
            fieldCnt++;
        }
        if (proto.hasWall()) {
            this.getWall().mergeChangeFromCs(proto.getWall());
            fieldCnt++;
        }
        if (proto.hasKillMonsterMaxLevel()) {
            this.setKillMonsterMaxLevel(proto.getKillMonsterMaxLevel());
            fieldCnt++;
        }
        if (proto.hasPosMarkModel()) {
            this.getPosMarkModel().mergeChangeFromCs(proto.getPosMarkModel());
            fieldCnt++;
        }
        if (proto.hasHospitalModel()) {
            this.getHospitalModel().mergeChangeFromCs(proto.getHospitalModel());
            fieldCnt++;
        }
        if (proto.hasArmyModel()) {
            this.getArmyModel().mergeChangeFromCs(proto.getArmyModel());
            fieldCnt++;
        }
        if (proto.hasLogisticsModel()) {
            this.getLogisticsModel().mergeChangeFromCs(proto.getLogisticsModel());
            fieldCnt++;
        }
        if (proto.hasDevBuffSysNew()) {
            this.getDevBuffSysNew().mergeChangeFromCs(proto.getDevBuffSysNew());
            fieldCnt++;
        }
        if (proto.hasPushNtfModel()) {
            this.getPushNtfModel().mergeChangeFromCs(proto.getPushNtfModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayer.Builder getCopyDbBuilder() {
        final ScenePlayer.Builder builder = ScenePlayer.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ScenePlayer.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMainCityId() != 0L) {
            builder.setMainCityId(this.getMainCityId());
            fieldCnt++;
        }  else if (builder.hasMainCityId()) {
            // 清理MainCityId
            builder.clearMainCityId();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (this.getLogoutTime() != 0L) {
            builder.setLogoutTime(this.getLogoutTime());
            fieldCnt++;
        }  else if (builder.hasLogoutTime()) {
            // 清理LogoutTime
            builder.clearLogoutTime();
            fieldCnt++;
        }
        if (this.soldier != null) {
            StructPlayer.ScenePlayerSoldier.Builder tmpBuilder = StructPlayer.ScenePlayerSoldier.newBuilder();
            final int tmpFieldCnt = this.soldier.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldier(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldier();
            }
        }  else if (builder.hasSoldier()) {
            // 清理Soldier
            builder.clearSoldier();
            fieldCnt++;
        }
        if (this.getCamp() != Camp.forNumber(0)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }  else if (builder.hasCamp()) {
            // 清理Camp
            builder.clearCamp();
            fieldCnt++;
        }
        if (this.rally != null) {
            Struct.ScenePlayerRallyBase.Builder tmpBuilder = Struct.ScenePlayerRallyBase.newBuilder();
            final int tmpFieldCnt = this.rally.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRally(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRally();
            }
        }  else if (builder.hasRally()) {
            // 清理Rally
            builder.clearRally();
            fieldCnt++;
        }
        if (this.pickLimit != null) {
            Struct.Int32PlayerPickLimitMap.Builder tmpBuilder = Struct.Int32PlayerPickLimitMap.newBuilder();
            final int tmpFieldCnt = this.pickLimit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPickLimit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPickLimit();
            }
        }  else if (builder.hasPickLimit()) {
            // 清理PickLimit
            builder.clearPickLimit();
            fieldCnt++;
        }
        if (this.additionSys != null) {
            Struct.AdditionSys.Builder tmpBuilder = Struct.AdditionSys.newBuilder();
            final int tmpFieldCnt = this.additionSys.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionSys();
            }
        }  else if (builder.hasAdditionSys()) {
            // 清理AdditionSys
            builder.clearAdditionSys();
            fieldCnt++;
        }
        if (this.getCreateTime() != 0L) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }  else if (builder.hasCreateTime()) {
            // 清理CreateTime
            builder.clearCreateTime();
            fieldCnt++;
        }
        if (this.wall != null) {
            StructPlayer.WallInfo.Builder tmpBuilder = StructPlayer.WallInfo.newBuilder();
            final int tmpFieldCnt = this.wall.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWall(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWall();
            }
        }  else if (builder.hasWall()) {
            // 清理Wall
            builder.clearWall();
            fieldCnt++;
        }
        if (this.getKillMonsterMaxLevel() != 0) {
            builder.setKillMonsterMaxLevel(this.getKillMonsterMaxLevel());
            fieldCnt++;
        }  else if (builder.hasKillMonsterMaxLevel()) {
            // 清理KillMonsterMaxLevel
            builder.clearKillMonsterMaxLevel();
            fieldCnt++;
        }
        if (this.plunderModel != null) {
            Player.ScenePlayerPlunderModel.Builder tmpBuilder = Player.ScenePlayerPlunderModel.newBuilder();
            final int tmpFieldCnt = this.plunderModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlunderModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlunderModel();
            }
        }  else if (builder.hasPlunderModel()) {
            // 清理PlunderModel
            builder.clearPlunderModel();
            fieldCnt++;
        }
        if (this.cityBuildLevelMap != null) {
            Struct.Int32CityBuildInfoMap.Builder tmpBuilder = Struct.Int32CityBuildInfoMap.newBuilder();
            final int tmpFieldCnt = this.cityBuildLevelMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCityBuildLevelMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCityBuildLevelMap();
            }
        }  else if (builder.hasCityBuildLevelMap()) {
            // 清理CityBuildLevelMap
            builder.clearCityBuildLevelMap();
            fieldCnt++;
        }
        if (this.posMarkModel != null) {
            Player.ScenePlayerPosMarkModel.Builder tmpBuilder = Player.ScenePlayerPosMarkModel.newBuilder();
            final int tmpFieldCnt = this.posMarkModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPosMarkModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPosMarkModel();
            }
        }  else if (builder.hasPosMarkModel()) {
            // 清理PosMarkModel
            builder.clearPosMarkModel();
            fieldCnt++;
        }
        if (this.hospitalModel != null) {
            Struct.ScenePlayerHospitalModel.Builder tmpBuilder = Struct.ScenePlayerHospitalModel.newBuilder();
            final int tmpFieldCnt = this.hospitalModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHospitalModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHospitalModel();
            }
        }  else if (builder.hasHospitalModel()) {
            // 清理HospitalModel
            builder.clearHospitalModel();
            fieldCnt++;
        }
        if (this.armyModel != null) {
            Player.ScenePlayerArmyModel.Builder tmpBuilder = Player.ScenePlayerArmyModel.newBuilder();
            final int tmpFieldCnt = this.armyModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArmyModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArmyModel();
            }
        }  else if (builder.hasArmyModel()) {
            // 清理ArmyModel
            builder.clearArmyModel();
            fieldCnt++;
        }
        if (this.techModel != null) {
            StructPlayer.ScenePlayerTechModel.Builder tmpBuilder = StructPlayer.ScenePlayerTechModel.newBuilder();
            final int tmpFieldCnt = this.techModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTechModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTechModel();
            }
        }  else if (builder.hasTechModel()) {
            // 清理TechModel
            builder.clearTechModel();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.logisticsModel != null) {
            Player.ScenePlayerLogisticsModel.Builder tmpBuilder = Player.ScenePlayerLogisticsModel.newBuilder();
            final int tmpFieldCnt = this.logisticsModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLogisticsModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLogisticsModel();
            }
        }  else if (builder.hasLogisticsModel()) {
            // 清理LogisticsModel
            builder.clearLogisticsModel();
            fieldCnt++;
        }
        if (this.devBuffSysNew != null) {
            StructBattle.DevBuffSys.Builder tmpBuilder = StructBattle.DevBuffSys.newBuilder();
            final int tmpFieldCnt = this.devBuffSysNew.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSysNew(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSysNew();
            }
        }  else if (builder.hasDevBuffSysNew()) {
            // 清理DevBuffSysNew
            builder.clearDevBuffSysNew();
            fieldCnt++;
        }
        if (this.powerModel != null) {
            Player.ScenePowerModel.Builder tmpBuilder = Player.ScenePowerModel.newBuilder();
            final int tmpFieldCnt = this.powerModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPowerModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPowerModel();
            }
        }  else if (builder.hasPowerModel()) {
            // 清理PowerModel
            builder.clearPowerModel();
            fieldCnt++;
        }
        if (this.zoneModel != null) {
            Player.ScenePlayerZoneModel.Builder tmpBuilder = Player.ScenePlayerZoneModel.newBuilder();
            final int tmpFieldCnt = this.zoneModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setZoneModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearZoneModel();
            }
        }  else if (builder.hasZoneModel()) {
            // 清理ZoneModel
            builder.clearZoneModel();
            fieldCnt++;
        }
        if (this.pushNtfModel != null) {
            Player.ScenePlayerPushNtfModel.Builder tmpBuilder = Player.ScenePlayerPushNtfModel.newBuilder();
            final int tmpFieldCnt = this.pushNtfModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPushNtfModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPushNtfModel();
            }
        }  else if (builder.hasPushNtfModel()) {
            // 清理PushNtfModel
            builder.clearPushNtfModel();
            fieldCnt++;
        }
        if (this.monsterModel != null) {
            Player.ScenePlayerMonsterModel.Builder tmpBuilder = Player.ScenePlayerMonsterModel.newBuilder();
            final int tmpFieldCnt = this.monsterModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMonsterModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMonsterModel();
            }
        }  else if (builder.hasMonsterModel()) {
            // 清理MonsterModel
            builder.clearMonsterModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ScenePlayer.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAINCITYID)) {
            builder.setMainCityId(this.getMainCityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOGOUTTIME)) {
            builder.setLogoutTime(this.getLogoutTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIER) && this.soldier != null) {
            final boolean needClear = !builder.hasSoldier();
            final int tmpFieldCnt = this.soldier.copyChangeToDb(builder.getSoldierBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldier();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLY) && this.rally != null) {
            final boolean needClear = !builder.hasRally();
            final int tmpFieldCnt = this.rally.copyChangeToDb(builder.getRallyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRally();
            }
        }
        if (this.hasMark(FIELD_INDEX_PICKLIMIT) && this.pickLimit != null) {
            final boolean needClear = !builder.hasPickLimit();
            final int tmpFieldCnt = this.pickLimit.copyChangeToDb(builder.getPickLimitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPickLimit();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            final boolean needClear = !builder.hasAdditionSys();
            final int tmpFieldCnt = this.additionSys.copyChangeToDb(builder.getAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETIME)) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WALL) && this.wall != null) {
            final boolean needClear = !builder.hasWall();
            final int tmpFieldCnt = this.wall.copyChangeToDb(builder.getWallBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWall();
            }
        }
        if (this.hasMark(FIELD_INDEX_KILLMONSTERMAXLEVEL)) {
            builder.setKillMonsterMaxLevel(this.getKillMonsterMaxLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLUNDERMODEL) && this.plunderModel != null) {
            final boolean needClear = !builder.hasPlunderModel();
            final int tmpFieldCnt = this.plunderModel.copyChangeToDb(builder.getPlunderModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlunderModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_CITYBUILDLEVELMAP) && this.cityBuildLevelMap != null) {
            final boolean needClear = !builder.hasCityBuildLevelMap();
            final int tmpFieldCnt = this.cityBuildLevelMap.copyChangeToDb(builder.getCityBuildLevelMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCityBuildLevelMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_POSMARKMODEL) && this.posMarkModel != null) {
            final boolean needClear = !builder.hasPosMarkModel();
            final int tmpFieldCnt = this.posMarkModel.copyChangeToDb(builder.getPosMarkModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPosMarkModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_HOSPITALMODEL) && this.hospitalModel != null) {
            final boolean needClear = !builder.hasHospitalModel();
            final int tmpFieldCnt = this.hospitalModel.copyChangeToDb(builder.getHospitalModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHospitalModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARMYMODEL) && this.armyModel != null) {
            final boolean needClear = !builder.hasArmyModel();
            final int tmpFieldCnt = this.armyModel.copyChangeToDb(builder.getArmyModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmyModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_TECHMODEL) && this.techModel != null) {
            final boolean needClear = !builder.hasTechModel();
            final int tmpFieldCnt = this.techModel.copyChangeToDb(builder.getTechModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTechModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToDb(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOGISTICSMODEL) && this.logisticsModel != null) {
            final boolean needClear = !builder.hasLogisticsModel();
            final int tmpFieldCnt = this.logisticsModel.copyChangeToDb(builder.getLogisticsModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLogisticsModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYSNEW) && this.devBuffSysNew != null) {
            final boolean needClear = !builder.hasDevBuffSysNew();
            final int tmpFieldCnt = this.devBuffSysNew.copyChangeToDb(builder.getDevBuffSysNewBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSysNew();
            }
        }
        if (this.hasMark(FIELD_INDEX_POWERMODEL) && this.powerModel != null) {
            final boolean needClear = !builder.hasPowerModel();
            final int tmpFieldCnt = this.powerModel.copyChangeToDb(builder.getPowerModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPowerModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ZONEMODEL) && this.zoneModel != null) {
            final boolean needClear = !builder.hasZoneModel();
            final int tmpFieldCnt = this.zoneModel.copyChangeToDb(builder.getZoneModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearZoneModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PUSHNTFMODEL) && this.pushNtfModel != null) {
            final boolean needClear = !builder.hasPushNtfModel();
            final int tmpFieldCnt = this.pushNtfModel.copyChangeToDb(builder.getPushNtfModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPushNtfModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_MONSTERMODEL) && this.monsterModel != null) {
            final boolean needClear = !builder.hasMonsterModel();
            final int tmpFieldCnt = this.monsterModel.copyChangeToDb(builder.getMonsterModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMonsterModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ScenePlayer proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMainCityId()) {
            this.innerSetMainCityId(proto.getMainCityId());
        } else {
            this.innerSetMainCityId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLogoutTime()) {
            this.innerSetLogoutTime(proto.getLogoutTime());
        } else {
            this.innerSetLogoutTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSoldier()) {
            this.getSoldier().mergeFromDb(proto.getSoldier());
        } else {
            if (this.soldier != null) {
                this.soldier.mergeFromDb(proto.getSoldier());
            }
        }
        if (proto.hasCamp()) {
            this.innerSetCamp(proto.getCamp());
        } else {
            this.innerSetCamp(Camp.forNumber(0));
        }
        if (proto.hasRally()) {
            this.getRally().mergeFromDb(proto.getRally());
        } else {
            if (this.rally != null) {
                this.rally.mergeFromDb(proto.getRally());
            }
        }
        if (proto.hasPickLimit()) {
            this.getPickLimit().mergeFromDb(proto.getPickLimit());
        } else {
            if (this.pickLimit != null) {
                this.pickLimit.mergeFromDb(proto.getPickLimit());
            }
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeFromDb(proto.getAdditionSys());
        } else {
            if (this.additionSys != null) {
                this.additionSys.mergeFromDb(proto.getAdditionSys());
            }
        }
        if (proto.hasCreateTime()) {
            this.innerSetCreateTime(proto.getCreateTime());
        } else {
            this.innerSetCreateTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasWall()) {
            this.getWall().mergeFromDb(proto.getWall());
        } else {
            if (this.wall != null) {
                this.wall.mergeFromDb(proto.getWall());
            }
        }
        if (proto.hasKillMonsterMaxLevel()) {
            this.innerSetKillMonsterMaxLevel(proto.getKillMonsterMaxLevel());
        } else {
            this.innerSetKillMonsterMaxLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPlunderModel()) {
            this.getPlunderModel().mergeFromDb(proto.getPlunderModel());
        } else {
            if (this.plunderModel != null) {
                this.plunderModel.mergeFromDb(proto.getPlunderModel());
            }
        }
        if (proto.hasCityBuildLevelMap()) {
            this.getCityBuildLevelMap().mergeFromDb(proto.getCityBuildLevelMap());
        } else {
            if (this.cityBuildLevelMap != null) {
                this.cityBuildLevelMap.mergeFromDb(proto.getCityBuildLevelMap());
            }
        }
        if (proto.hasPosMarkModel()) {
            this.getPosMarkModel().mergeFromDb(proto.getPosMarkModel());
        } else {
            if (this.posMarkModel != null) {
                this.posMarkModel.mergeFromDb(proto.getPosMarkModel());
            }
        }
        if (proto.hasHospitalModel()) {
            this.getHospitalModel().mergeFromDb(proto.getHospitalModel());
        } else {
            if (this.hospitalModel != null) {
                this.hospitalModel.mergeFromDb(proto.getHospitalModel());
            }
        }
        if (proto.hasArmyModel()) {
            this.getArmyModel().mergeFromDb(proto.getArmyModel());
        } else {
            if (this.armyModel != null) {
                this.armyModel.mergeFromDb(proto.getArmyModel());
            }
        }
        if (proto.hasTechModel()) {
            this.getTechModel().mergeFromDb(proto.getTechModel());
        } else {
            if (this.techModel != null) {
                this.techModel.mergeFromDb(proto.getTechModel());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromDb(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromDb(proto.getCardHead());
            }
        }
        if (proto.hasLogisticsModel()) {
            this.getLogisticsModel().mergeFromDb(proto.getLogisticsModel());
        } else {
            if (this.logisticsModel != null) {
                this.logisticsModel.mergeFromDb(proto.getLogisticsModel());
            }
        }
        if (proto.hasDevBuffSysNew()) {
            this.getDevBuffSysNew().mergeFromDb(proto.getDevBuffSysNew());
        } else {
            if (this.devBuffSysNew != null) {
                this.devBuffSysNew.mergeFromDb(proto.getDevBuffSysNew());
            }
        }
        if (proto.hasPowerModel()) {
            this.getPowerModel().mergeFromDb(proto.getPowerModel());
        } else {
            if (this.powerModel != null) {
                this.powerModel.mergeFromDb(proto.getPowerModel());
            }
        }
        if (proto.hasZoneModel()) {
            this.getZoneModel().mergeFromDb(proto.getZoneModel());
        } else {
            if (this.zoneModel != null) {
                this.zoneModel.mergeFromDb(proto.getZoneModel());
            }
        }
        if (proto.hasPushNtfModel()) {
            this.getPushNtfModel().mergeFromDb(proto.getPushNtfModel());
        } else {
            if (this.pushNtfModel != null) {
                this.pushNtfModel.mergeFromDb(proto.getPushNtfModel());
            }
        }
        if (proto.hasMonsterModel()) {
            this.getMonsterModel().mergeFromDb(proto.getMonsterModel());
        } else {
            if (this.monsterModel != null) {
                this.monsterModel.mergeFromDb(proto.getMonsterModel());
            }
        }
        this.markAll();
        return ScenePlayerProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ScenePlayer proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMainCityId()) {
            this.setMainCityId(proto.getMainCityId());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasLogoutTime()) {
            this.setLogoutTime(proto.getLogoutTime());
            fieldCnt++;
        }
        if (proto.hasSoldier()) {
            this.getSoldier().mergeChangeFromDb(proto.getSoldier());
            fieldCnt++;
        }
        if (proto.hasCamp()) {
            this.setCamp(proto.getCamp());
            fieldCnt++;
        }
        if (proto.hasRally()) {
            this.getRally().mergeChangeFromDb(proto.getRally());
            fieldCnt++;
        }
        if (proto.hasPickLimit()) {
            this.getPickLimit().mergeChangeFromDb(proto.getPickLimit());
            fieldCnt++;
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeChangeFromDb(proto.getAdditionSys());
            fieldCnt++;
        }
        if (proto.hasCreateTime()) {
            this.setCreateTime(proto.getCreateTime());
            fieldCnt++;
        }
        if (proto.hasWall()) {
            this.getWall().mergeChangeFromDb(proto.getWall());
            fieldCnt++;
        }
        if (proto.hasKillMonsterMaxLevel()) {
            this.setKillMonsterMaxLevel(proto.getKillMonsterMaxLevel());
            fieldCnt++;
        }
        if (proto.hasPlunderModel()) {
            this.getPlunderModel().mergeChangeFromDb(proto.getPlunderModel());
            fieldCnt++;
        }
        if (proto.hasCityBuildLevelMap()) {
            this.getCityBuildLevelMap().mergeChangeFromDb(proto.getCityBuildLevelMap());
            fieldCnt++;
        }
        if (proto.hasPosMarkModel()) {
            this.getPosMarkModel().mergeChangeFromDb(proto.getPosMarkModel());
            fieldCnt++;
        }
        if (proto.hasHospitalModel()) {
            this.getHospitalModel().mergeChangeFromDb(proto.getHospitalModel());
            fieldCnt++;
        }
        if (proto.hasArmyModel()) {
            this.getArmyModel().mergeChangeFromDb(proto.getArmyModel());
            fieldCnt++;
        }
        if (proto.hasTechModel()) {
            this.getTechModel().mergeChangeFromDb(proto.getTechModel());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromDb(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasLogisticsModel()) {
            this.getLogisticsModel().mergeChangeFromDb(proto.getLogisticsModel());
            fieldCnt++;
        }
        if (proto.hasDevBuffSysNew()) {
            this.getDevBuffSysNew().mergeChangeFromDb(proto.getDevBuffSysNew());
            fieldCnt++;
        }
        if (proto.hasPowerModel()) {
            this.getPowerModel().mergeChangeFromDb(proto.getPowerModel());
            fieldCnt++;
        }
        if (proto.hasZoneModel()) {
            this.getZoneModel().mergeChangeFromDb(proto.getZoneModel());
            fieldCnt++;
        }
        if (proto.hasPushNtfModel()) {
            this.getPushNtfModel().mergeChangeFromDb(proto.getPushNtfModel());
            fieldCnt++;
        }
        if (proto.hasMonsterModel()) {
            this.getMonsterModel().mergeChangeFromDb(proto.getMonsterModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayer.Builder getCopySsBuilder() {
        final ScenePlayer.Builder builder = ScenePlayer.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ScenePlayer.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMainCityId() != 0L) {
            builder.setMainCityId(this.getMainCityId());
            fieldCnt++;
        }  else if (builder.hasMainCityId()) {
            // 清理MainCityId
            builder.clearMainCityId();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (this.getLogoutTime() != 0L) {
            builder.setLogoutTime(this.getLogoutTime());
            fieldCnt++;
        }  else if (builder.hasLogoutTime()) {
            // 清理LogoutTime
            builder.clearLogoutTime();
            fieldCnt++;
        }
        if (this.soldier != null) {
            StructPlayer.ScenePlayerSoldier.Builder tmpBuilder = StructPlayer.ScenePlayerSoldier.newBuilder();
            final int tmpFieldCnt = this.soldier.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldier(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldier();
            }
        }  else if (builder.hasSoldier()) {
            // 清理Soldier
            builder.clearSoldier();
            fieldCnt++;
        }
        if (this.getCamp() != Camp.forNumber(0)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }  else if (builder.hasCamp()) {
            // 清理Camp
            builder.clearCamp();
            fieldCnt++;
        }
        if (this.rally != null) {
            Struct.ScenePlayerRallyBase.Builder tmpBuilder = Struct.ScenePlayerRallyBase.newBuilder();
            final int tmpFieldCnt = this.rally.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRally(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRally();
            }
        }  else if (builder.hasRally()) {
            // 清理Rally
            builder.clearRally();
            fieldCnt++;
        }
        if (this.warning != null) {
            Struct.WarningItemList.Builder tmpBuilder = Struct.WarningItemList.newBuilder();
            final int tmpFieldCnt = this.warning.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWarning(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWarning();
            }
        }  else if (builder.hasWarning()) {
            // 清理Warning
            builder.clearWarning();
            fieldCnt++;
        }
        if (this.pickLimit != null) {
            Struct.Int32PlayerPickLimitMap.Builder tmpBuilder = Struct.Int32PlayerPickLimitMap.newBuilder();
            final int tmpFieldCnt = this.pickLimit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPickLimit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPickLimit();
            }
        }  else if (builder.hasPickLimit()) {
            // 清理PickLimit
            builder.clearPickLimit();
            fieldCnt++;
        }
        if (this.additionSys != null) {
            Struct.AdditionSys.Builder tmpBuilder = Struct.AdditionSys.newBuilder();
            final int tmpFieldCnt = this.additionSys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionSys();
            }
        }  else if (builder.hasAdditionSys()) {
            // 清理AdditionSys
            builder.clearAdditionSys();
            fieldCnt++;
        }
        if (this.getCreateTime() != 0L) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }  else if (builder.hasCreateTime()) {
            // 清理CreateTime
            builder.clearCreateTime();
            fieldCnt++;
        }
        if (this.wall != null) {
            StructPlayer.WallInfo.Builder tmpBuilder = StructPlayer.WallInfo.newBuilder();
            final int tmpFieldCnt = this.wall.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWall(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWall();
            }
        }  else if (builder.hasWall()) {
            // 清理Wall
            builder.clearWall();
            fieldCnt++;
        }
        if (this.getKillMonsterMaxLevel() != 0) {
            builder.setKillMonsterMaxLevel(this.getKillMonsterMaxLevel());
            fieldCnt++;
        }  else if (builder.hasKillMonsterMaxLevel()) {
            // 清理KillMonsterMaxLevel
            builder.clearKillMonsterMaxLevel();
            fieldCnt++;
        }
        if (this.plunderModel != null) {
            Player.ScenePlayerPlunderModel.Builder tmpBuilder = Player.ScenePlayerPlunderModel.newBuilder();
            final int tmpFieldCnt = this.plunderModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlunderModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlunderModel();
            }
        }  else if (builder.hasPlunderModel()) {
            // 清理PlunderModel
            builder.clearPlunderModel();
            fieldCnt++;
        }
        if (this.cityBuildLevelMap != null) {
            Struct.Int32CityBuildInfoMap.Builder tmpBuilder = Struct.Int32CityBuildInfoMap.newBuilder();
            final int tmpFieldCnt = this.cityBuildLevelMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCityBuildLevelMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCityBuildLevelMap();
            }
        }  else if (builder.hasCityBuildLevelMap()) {
            // 清理CityBuildLevelMap
            builder.clearCityBuildLevelMap();
            fieldCnt++;
        }
        if (this.posMarkModel != null) {
            Player.ScenePlayerPosMarkModel.Builder tmpBuilder = Player.ScenePlayerPosMarkModel.newBuilder();
            final int tmpFieldCnt = this.posMarkModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPosMarkModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPosMarkModel();
            }
        }  else if (builder.hasPosMarkModel()) {
            // 清理PosMarkModel
            builder.clearPosMarkModel();
            fieldCnt++;
        }
        if (this.hospitalModel != null) {
            Struct.ScenePlayerHospitalModel.Builder tmpBuilder = Struct.ScenePlayerHospitalModel.newBuilder();
            final int tmpFieldCnt = this.hospitalModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHospitalModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHospitalModel();
            }
        }  else if (builder.hasHospitalModel()) {
            // 清理HospitalModel
            builder.clearHospitalModel();
            fieldCnt++;
        }
        if (this.armyModel != null) {
            Player.ScenePlayerArmyModel.Builder tmpBuilder = Player.ScenePlayerArmyModel.newBuilder();
            final int tmpFieldCnt = this.armyModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArmyModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArmyModel();
            }
        }  else if (builder.hasArmyModel()) {
            // 清理ArmyModel
            builder.clearArmyModel();
            fieldCnt++;
        }
        if (this.techModel != null) {
            StructPlayer.ScenePlayerTechModel.Builder tmpBuilder = StructPlayer.ScenePlayerTechModel.newBuilder();
            final int tmpFieldCnt = this.techModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTechModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTechModel();
            }
        }  else if (builder.hasTechModel()) {
            // 清理TechModel
            builder.clearTechModel();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.logisticsModel != null) {
            Player.ScenePlayerLogisticsModel.Builder tmpBuilder = Player.ScenePlayerLogisticsModel.newBuilder();
            final int tmpFieldCnt = this.logisticsModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLogisticsModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLogisticsModel();
            }
        }  else if (builder.hasLogisticsModel()) {
            // 清理LogisticsModel
            builder.clearLogisticsModel();
            fieldCnt++;
        }
        if (this.devBuffSysNew != null) {
            StructBattle.DevBuffSys.Builder tmpBuilder = StructBattle.DevBuffSys.newBuilder();
            final int tmpFieldCnt = this.devBuffSysNew.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSysNew(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSysNew();
            }
        }  else if (builder.hasDevBuffSysNew()) {
            // 清理DevBuffSysNew
            builder.clearDevBuffSysNew();
            fieldCnt++;
        }
        if (this.powerModel != null) {
            Player.ScenePowerModel.Builder tmpBuilder = Player.ScenePowerModel.newBuilder();
            final int tmpFieldCnt = this.powerModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPowerModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPowerModel();
            }
        }  else if (builder.hasPowerModel()) {
            // 清理PowerModel
            builder.clearPowerModel();
            fieldCnt++;
        }
        if (this.zoneModel != null) {
            Player.ScenePlayerZoneModel.Builder tmpBuilder = Player.ScenePlayerZoneModel.newBuilder();
            final int tmpFieldCnt = this.zoneModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setZoneModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearZoneModel();
            }
        }  else if (builder.hasZoneModel()) {
            // 清理ZoneModel
            builder.clearZoneModel();
            fieldCnt++;
        }
        if (this.pushNtfModel != null) {
            Player.ScenePlayerPushNtfModel.Builder tmpBuilder = Player.ScenePlayerPushNtfModel.newBuilder();
            final int tmpFieldCnt = this.pushNtfModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPushNtfModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPushNtfModel();
            }
        }  else if (builder.hasPushNtfModel()) {
            // 清理PushNtfModel
            builder.clearPushNtfModel();
            fieldCnt++;
        }
        if (this.monsterModel != null) {
            Player.ScenePlayerMonsterModel.Builder tmpBuilder = Player.ScenePlayerMonsterModel.newBuilder();
            final int tmpFieldCnt = this.monsterModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMonsterModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMonsterModel();
            }
        }  else if (builder.hasMonsterModel()) {
            // 清理MonsterModel
            builder.clearMonsterModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ScenePlayer.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAINCITYID)) {
            builder.setMainCityId(this.getMainCityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOGOUTTIME)) {
            builder.setLogoutTime(this.getLogoutTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIER) && this.soldier != null) {
            final boolean needClear = !builder.hasSoldier();
            final int tmpFieldCnt = this.soldier.copyChangeToSs(builder.getSoldierBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldier();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLY) && this.rally != null) {
            final boolean needClear = !builder.hasRally();
            final int tmpFieldCnt = this.rally.copyChangeToSs(builder.getRallyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRally();
            }
        }
        if (this.hasMark(FIELD_INDEX_WARNING) && this.warning != null) {
            final boolean needClear = !builder.hasWarning();
            final int tmpFieldCnt = this.warning.copyChangeToSs(builder.getWarningBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWarning();
            }
        }
        if (this.hasMark(FIELD_INDEX_PICKLIMIT) && this.pickLimit != null) {
            final boolean needClear = !builder.hasPickLimit();
            final int tmpFieldCnt = this.pickLimit.copyChangeToSs(builder.getPickLimitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPickLimit();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            final boolean needClear = !builder.hasAdditionSys();
            final int tmpFieldCnt = this.additionSys.copyChangeToSs(builder.getAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETIME)) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WALL) && this.wall != null) {
            final boolean needClear = !builder.hasWall();
            final int tmpFieldCnt = this.wall.copyChangeToSs(builder.getWallBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWall();
            }
        }
        if (this.hasMark(FIELD_INDEX_KILLMONSTERMAXLEVEL)) {
            builder.setKillMonsterMaxLevel(this.getKillMonsterMaxLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLUNDERMODEL) && this.plunderModel != null) {
            final boolean needClear = !builder.hasPlunderModel();
            final int tmpFieldCnt = this.plunderModel.copyChangeToSs(builder.getPlunderModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlunderModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_CITYBUILDLEVELMAP) && this.cityBuildLevelMap != null) {
            final boolean needClear = !builder.hasCityBuildLevelMap();
            final int tmpFieldCnt = this.cityBuildLevelMap.copyChangeToSs(builder.getCityBuildLevelMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCityBuildLevelMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_POSMARKMODEL) && this.posMarkModel != null) {
            final boolean needClear = !builder.hasPosMarkModel();
            final int tmpFieldCnt = this.posMarkModel.copyChangeToSs(builder.getPosMarkModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPosMarkModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_HOSPITALMODEL) && this.hospitalModel != null) {
            final boolean needClear = !builder.hasHospitalModel();
            final int tmpFieldCnt = this.hospitalModel.copyChangeToSs(builder.getHospitalModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHospitalModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARMYMODEL) && this.armyModel != null) {
            final boolean needClear = !builder.hasArmyModel();
            final int tmpFieldCnt = this.armyModel.copyChangeToSs(builder.getArmyModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmyModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_TECHMODEL) && this.techModel != null) {
            final boolean needClear = !builder.hasTechModel();
            final int tmpFieldCnt = this.techModel.copyChangeToSs(builder.getTechModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTechModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOGISTICSMODEL) && this.logisticsModel != null) {
            final boolean needClear = !builder.hasLogisticsModel();
            final int tmpFieldCnt = this.logisticsModel.copyChangeToSs(builder.getLogisticsModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLogisticsModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYSNEW) && this.devBuffSysNew != null) {
            final boolean needClear = !builder.hasDevBuffSysNew();
            final int tmpFieldCnt = this.devBuffSysNew.copyChangeToSs(builder.getDevBuffSysNewBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSysNew();
            }
        }
        if (this.hasMark(FIELD_INDEX_POWERMODEL) && this.powerModel != null) {
            final boolean needClear = !builder.hasPowerModel();
            final int tmpFieldCnt = this.powerModel.copyChangeToSs(builder.getPowerModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPowerModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ZONEMODEL) && this.zoneModel != null) {
            final boolean needClear = !builder.hasZoneModel();
            final int tmpFieldCnt = this.zoneModel.copyChangeToSs(builder.getZoneModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearZoneModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PUSHNTFMODEL) && this.pushNtfModel != null) {
            final boolean needClear = !builder.hasPushNtfModel();
            final int tmpFieldCnt = this.pushNtfModel.copyChangeToSs(builder.getPushNtfModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPushNtfModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_MONSTERMODEL) && this.monsterModel != null) {
            final boolean needClear = !builder.hasMonsterModel();
            final int tmpFieldCnt = this.monsterModel.copyChangeToSs(builder.getMonsterModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMonsterModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ScenePlayer proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMainCityId()) {
            this.innerSetMainCityId(proto.getMainCityId());
        } else {
            this.innerSetMainCityId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLogoutTime()) {
            this.innerSetLogoutTime(proto.getLogoutTime());
        } else {
            this.innerSetLogoutTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSoldier()) {
            this.getSoldier().mergeFromSs(proto.getSoldier());
        } else {
            if (this.soldier != null) {
                this.soldier.mergeFromSs(proto.getSoldier());
            }
        }
        if (proto.hasCamp()) {
            this.innerSetCamp(proto.getCamp());
        } else {
            this.innerSetCamp(Camp.forNumber(0));
        }
        if (proto.hasRally()) {
            this.getRally().mergeFromSs(proto.getRally());
        } else {
            if (this.rally != null) {
                this.rally.mergeFromSs(proto.getRally());
            }
        }
        if (proto.hasWarning()) {
            this.getWarning().mergeFromSs(proto.getWarning());
        } else {
            if (this.warning != null) {
                this.warning.mergeFromSs(proto.getWarning());
            }
        }
        if (proto.hasPickLimit()) {
            this.getPickLimit().mergeFromSs(proto.getPickLimit());
        } else {
            if (this.pickLimit != null) {
                this.pickLimit.mergeFromSs(proto.getPickLimit());
            }
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeFromSs(proto.getAdditionSys());
        } else {
            if (this.additionSys != null) {
                this.additionSys.mergeFromSs(proto.getAdditionSys());
            }
        }
        if (proto.hasCreateTime()) {
            this.innerSetCreateTime(proto.getCreateTime());
        } else {
            this.innerSetCreateTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasWall()) {
            this.getWall().mergeFromSs(proto.getWall());
        } else {
            if (this.wall != null) {
                this.wall.mergeFromSs(proto.getWall());
            }
        }
        if (proto.hasKillMonsterMaxLevel()) {
            this.innerSetKillMonsterMaxLevel(proto.getKillMonsterMaxLevel());
        } else {
            this.innerSetKillMonsterMaxLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPlunderModel()) {
            this.getPlunderModel().mergeFromSs(proto.getPlunderModel());
        } else {
            if (this.plunderModel != null) {
                this.plunderModel.mergeFromSs(proto.getPlunderModel());
            }
        }
        if (proto.hasCityBuildLevelMap()) {
            this.getCityBuildLevelMap().mergeFromSs(proto.getCityBuildLevelMap());
        } else {
            if (this.cityBuildLevelMap != null) {
                this.cityBuildLevelMap.mergeFromSs(proto.getCityBuildLevelMap());
            }
        }
        if (proto.hasPosMarkModel()) {
            this.getPosMarkModel().mergeFromSs(proto.getPosMarkModel());
        } else {
            if (this.posMarkModel != null) {
                this.posMarkModel.mergeFromSs(proto.getPosMarkModel());
            }
        }
        if (proto.hasHospitalModel()) {
            this.getHospitalModel().mergeFromSs(proto.getHospitalModel());
        } else {
            if (this.hospitalModel != null) {
                this.hospitalModel.mergeFromSs(proto.getHospitalModel());
            }
        }
        if (proto.hasArmyModel()) {
            this.getArmyModel().mergeFromSs(proto.getArmyModel());
        } else {
            if (this.armyModel != null) {
                this.armyModel.mergeFromSs(proto.getArmyModel());
            }
        }
        if (proto.hasTechModel()) {
            this.getTechModel().mergeFromSs(proto.getTechModel());
        } else {
            if (this.techModel != null) {
                this.techModel.mergeFromSs(proto.getTechModel());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        if (proto.hasLogisticsModel()) {
            this.getLogisticsModel().mergeFromSs(proto.getLogisticsModel());
        } else {
            if (this.logisticsModel != null) {
                this.logisticsModel.mergeFromSs(proto.getLogisticsModel());
            }
        }
        if (proto.hasDevBuffSysNew()) {
            this.getDevBuffSysNew().mergeFromSs(proto.getDevBuffSysNew());
        } else {
            if (this.devBuffSysNew != null) {
                this.devBuffSysNew.mergeFromSs(proto.getDevBuffSysNew());
            }
        }
        if (proto.hasPowerModel()) {
            this.getPowerModel().mergeFromSs(proto.getPowerModel());
        } else {
            if (this.powerModel != null) {
                this.powerModel.mergeFromSs(proto.getPowerModel());
            }
        }
        if (proto.hasZoneModel()) {
            this.getZoneModel().mergeFromSs(proto.getZoneModel());
        } else {
            if (this.zoneModel != null) {
                this.zoneModel.mergeFromSs(proto.getZoneModel());
            }
        }
        if (proto.hasPushNtfModel()) {
            this.getPushNtfModel().mergeFromSs(proto.getPushNtfModel());
        } else {
            if (this.pushNtfModel != null) {
                this.pushNtfModel.mergeFromSs(proto.getPushNtfModel());
            }
        }
        if (proto.hasMonsterModel()) {
            this.getMonsterModel().mergeFromSs(proto.getMonsterModel());
        } else {
            if (this.monsterModel != null) {
                this.monsterModel.mergeFromSs(proto.getMonsterModel());
            }
        }
        this.markAll();
        return ScenePlayerProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ScenePlayer proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMainCityId()) {
            this.setMainCityId(proto.getMainCityId());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasLogoutTime()) {
            this.setLogoutTime(proto.getLogoutTime());
            fieldCnt++;
        }
        if (proto.hasSoldier()) {
            this.getSoldier().mergeChangeFromSs(proto.getSoldier());
            fieldCnt++;
        }
        if (proto.hasCamp()) {
            this.setCamp(proto.getCamp());
            fieldCnt++;
        }
        if (proto.hasRally()) {
            this.getRally().mergeChangeFromSs(proto.getRally());
            fieldCnt++;
        }
        if (proto.hasWarning()) {
            this.getWarning().mergeChangeFromSs(proto.getWarning());
            fieldCnt++;
        }
        if (proto.hasPickLimit()) {
            this.getPickLimit().mergeChangeFromSs(proto.getPickLimit());
            fieldCnt++;
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeChangeFromSs(proto.getAdditionSys());
            fieldCnt++;
        }
        if (proto.hasCreateTime()) {
            this.setCreateTime(proto.getCreateTime());
            fieldCnt++;
        }
        if (proto.hasWall()) {
            this.getWall().mergeChangeFromSs(proto.getWall());
            fieldCnt++;
        }
        if (proto.hasKillMonsterMaxLevel()) {
            this.setKillMonsterMaxLevel(proto.getKillMonsterMaxLevel());
            fieldCnt++;
        }
        if (proto.hasPlunderModel()) {
            this.getPlunderModel().mergeChangeFromSs(proto.getPlunderModel());
            fieldCnt++;
        }
        if (proto.hasCityBuildLevelMap()) {
            this.getCityBuildLevelMap().mergeChangeFromSs(proto.getCityBuildLevelMap());
            fieldCnt++;
        }
        if (proto.hasPosMarkModel()) {
            this.getPosMarkModel().mergeChangeFromSs(proto.getPosMarkModel());
            fieldCnt++;
        }
        if (proto.hasHospitalModel()) {
            this.getHospitalModel().mergeChangeFromSs(proto.getHospitalModel());
            fieldCnt++;
        }
        if (proto.hasArmyModel()) {
            this.getArmyModel().mergeChangeFromSs(proto.getArmyModel());
            fieldCnt++;
        }
        if (proto.hasTechModel()) {
            this.getTechModel().mergeChangeFromSs(proto.getTechModel());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasLogisticsModel()) {
            this.getLogisticsModel().mergeChangeFromSs(proto.getLogisticsModel());
            fieldCnt++;
        }
        if (proto.hasDevBuffSysNew()) {
            this.getDevBuffSysNew().mergeChangeFromSs(proto.getDevBuffSysNew());
            fieldCnt++;
        }
        if (proto.hasPowerModel()) {
            this.getPowerModel().mergeChangeFromSs(proto.getPowerModel());
            fieldCnt++;
        }
        if (proto.hasZoneModel()) {
            this.getZoneModel().mergeChangeFromSs(proto.getZoneModel());
            fieldCnt++;
        }
        if (proto.hasPushNtfModel()) {
            this.getPushNtfModel().mergeChangeFromSs(proto.getPushNtfModel());
            fieldCnt++;
        }
        if (proto.hasMonsterModel()) {
            this.getMonsterModel().mergeChangeFromSs(proto.getMonsterModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ScenePlayer.Builder builder = ScenePlayer.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIER) && this.soldier != null) {
            this.soldier.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RALLY) && this.rally != null) {
            this.rally.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_WARNING) && this.warning != null) {
            this.warning.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PICKLIMIT) && this.pickLimit != null) {
            this.pickLimit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            this.additionSys.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_WALL) && this.wall != null) {
            this.wall.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLUNDERMODEL) && this.plunderModel != null) {
            this.plunderModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CITYBUILDLEVELMAP) && this.cityBuildLevelMap != null) {
            this.cityBuildLevelMap.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_POSMARKMODEL) && this.posMarkModel != null) {
            this.posMarkModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_HOSPITALMODEL) && this.hospitalModel != null) {
            this.hospitalModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ARMYMODEL) && this.armyModel != null) {
            this.armyModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TECHMODEL) && this.techModel != null) {
            this.techModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_LOGISTICSMODEL) && this.logisticsModel != null) {
            this.logisticsModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYSNEW) && this.devBuffSysNew != null) {
            this.devBuffSysNew.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_POWERMODEL) && this.powerModel != null) {
            this.powerModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ZONEMODEL) && this.zoneModel != null) {
            this.zoneModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PUSHNTFMODEL) && this.pushNtfModel != null) {
            this.pushNtfModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_MONSTERMODEL) && this.monsterModel != null) {
            this.monsterModel.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.soldier != null) {
            this.soldier.markAll();
        }
        if (this.rally != null) {
            this.rally.markAll();
        }
        if (this.warning != null) {
            this.warning.markAll();
        }
        if (this.pickLimit != null) {
            this.pickLimit.markAll();
        }
        if (this.additionSys != null) {
            this.additionSys.markAll();
        }
        if (this.wall != null) {
            this.wall.markAll();
        }
        if (this.plunderModel != null) {
            this.plunderModel.markAll();
        }
        if (this.cityBuildLevelMap != null) {
            this.cityBuildLevelMap.markAll();
        }
        if (this.posMarkModel != null) {
            this.posMarkModel.markAll();
        }
        if (this.hospitalModel != null) {
            this.hospitalModel.markAll();
        }
        if (this.armyModel != null) {
            this.armyModel.markAll();
        }
        if (this.techModel != null) {
            this.techModel.markAll();
        }
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        if (this.logisticsModel != null) {
            this.logisticsModel.markAll();
        }
        if (this.devBuffSysNew != null) {
            this.devBuffSysNew.markAll();
        }
        if (this.powerModel != null) {
            this.powerModel.markAll();
        }
        if (this.zoneModel != null) {
            this.zoneModel.markAll();
        }
        if (this.pushNtfModel != null) {
            this.pushNtfModel.markAll();
        }
        if (this.monsterModel != null) {
            this.monsterModel.markAll();
        }
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("ScenePlayerProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("ScenePlayerProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ScenePlayerProp)) {
            return false;
        }
        final ScenePlayerProp otherNode = (ScenePlayerProp) node;
        if (this.mainCityId != otherNode.mainCityId) {
            return false;
        }
        if (this.clanId != otherNode.clanId) {
            return false;
        }
        if (this.logoutTime != otherNode.logoutTime) {
            return false;
        }
        if (!this.getSoldier().compareDataTo(otherNode.getSoldier())) {
            return false;
        }
        if (this.camp != otherNode.camp) {
            return false;
        }
        if (!this.getRally().compareDataTo(otherNode.getRally())) {
            return false;
        }
        if (!this.getWarning().compareDataTo(otherNode.getWarning())) {
            return false;
        }
        if (!this.getPickLimit().compareDataTo(otherNode.getPickLimit())) {
            return false;
        }
        if (!this.getAdditionSys().compareDataTo(otherNode.getAdditionSys())) {
            return false;
        }
        if (this.createTime != otherNode.createTime) {
            return false;
        }
        if (!this.getWall().compareDataTo(otherNode.getWall())) {
            return false;
        }
        if (this.killMonsterMaxLevel != otherNode.killMonsterMaxLevel) {
            return false;
        }
        if (!this.getPlunderModel().compareDataTo(otherNode.getPlunderModel())) {
            return false;
        }
        if (!this.getCityBuildLevelMap().compareDataTo(otherNode.getCityBuildLevelMap())) {
            return false;
        }
        if (!this.getPosMarkModel().compareDataTo(otherNode.getPosMarkModel())) {
            return false;
        }
        if (!this.getHospitalModel().compareDataTo(otherNode.getHospitalModel())) {
            return false;
        }
        if (!this.getArmyModel().compareDataTo(otherNode.getArmyModel())) {
            return false;
        }
        if (!this.getTechModel().compareDataTo(otherNode.getTechModel())) {
            return false;
        }
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        if (!this.getLogisticsModel().compareDataTo(otherNode.getLogisticsModel())) {
            return false;
        }
        if (!this.getDevBuffSysNew().compareDataTo(otherNode.getDevBuffSysNew())) {
            return false;
        }
        if (!this.getPowerModel().compareDataTo(otherNode.getPowerModel())) {
            return false;
        }
        if (!this.getZoneModel().compareDataTo(otherNode.getZoneModel())) {
            return false;
        }
        if (!this.getPushNtfModel().compareDataTo(otherNode.getPushNtfModel())) {
            return false;
        }
        if (!this.getMonsterModel().compareDataTo(otherNode.getMonsterModel())) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    public static ScenePlayerProp of(ScenePlayer fullAttrDb, ScenePlayer changeAttrDb) {
        ScenePlayerProp prop = new ScenePlayerProp();
        prop.mergeFromDb(fullAttrDb);
        prop.mergeChangeFromDb(changeAttrDb);
        prop.unMarkAll();
        return prop;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 39;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.BattlePassTaskInfo;
import com.yorha.proto.Basic;
import com.yorha.proto.PlayerPB.BattlePassTaskInfoPB;
import com.yorha.proto.BasicPB;


/**
 * <AUTHOR> auto gen
 */
public class BattlePassTaskInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_GROUPID = 0;
    public static final int FIELD_INDEX_GROUPTASKS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int groupId = Constant.DEFAULT_INT_VALUE;
    private Int32ListProp groupTasks = null;

    public BattlePassTaskInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BattlePassTaskInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get groupId
     *
     * @return groupId value
     */
    public int getGroupId() {
        return this.groupId;
    }

    /**
     * set groupId && set marked
     *
     * @param groupId new value
     * @return current object
     */
    public BattlePassTaskInfoProp setGroupId(int groupId) {
        if (this.groupId != groupId) {
            this.mark(FIELD_INDEX_GROUPID);
            this.groupId = groupId;
        }
        return this;
    }

    /**
     * inner set groupId
     *
     * @param groupId new value
     */
    private void innerSetGroupId(int groupId) {
        this.groupId = groupId;
    }

    /**
     * get groupTasks
     *
     * @return groupTasks value
     */
    public Int32ListProp getGroupTasks() {
        if (this.groupTasks == null) {
            this.groupTasks = new Int32ListProp(this, FIELD_INDEX_GROUPTASKS);
        }
        return this.groupTasks;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addGroupTasks(Integer v) {
        this.getGroupTasks().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getGroupTasksIndex(int index) {
        return this.getGroupTasks().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeGroupTasks(Integer v) {
        if (this.groupTasks == null) {
            return null;
        }
        if(this.groupTasks.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getGroupTasksSize() {
        if (this.groupTasks == null) {
            return 0;
        }
        return this.groupTasks.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isGroupTasksEmpty() {
        if (this.groupTasks == null) {
            return true;
        }
        return this.getGroupTasks().isEmpty();
    }

    /**
     * clear list
     */
    public void clearGroupTasks() {
        this.getGroupTasks().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeGroupTasksIndex(int index) {
        return this.getGroupTasks().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setGroupTasksIndex(int index, Integer v) {
        return this.getGroupTasks().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattlePassTaskInfoPB.Builder getCopyCsBuilder() {
        final BattlePassTaskInfoPB.Builder builder = BattlePassTaskInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BattlePassTaskInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGroupId() != 0) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }  else if (builder.hasGroupId()) {
            // 清理GroupId
            builder.clearGroupId();
            fieldCnt++;
        }
        if (this.groupTasks != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.groupTasks.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGroupTasks(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGroupTasks();
            }
        }  else if (builder.hasGroupTasks()) {
            // 清理GroupTasks
            builder.clearGroupTasks();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BattlePassTaskInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GROUPTASKS) && this.groupTasks != null) {
            final boolean needClear = !builder.hasGroupTasks();
            final int tmpFieldCnt = this.groupTasks.copyChangeToCs(builder.getGroupTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGroupTasks();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BattlePassTaskInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GROUPTASKS) && this.groupTasks != null) {
            final boolean needClear = !builder.hasGroupTasks();
            final int tmpFieldCnt = this.groupTasks.copyChangeToCs(builder.getGroupTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGroupTasks();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BattlePassTaskInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGroupId()) {
            this.innerSetGroupId(proto.getGroupId());
        } else {
            this.innerSetGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasGroupTasks()) {
            this.getGroupTasks().mergeFromCs(proto.getGroupTasks());
        } else {
            if (this.groupTasks != null) {
                this.groupTasks.mergeFromCs(proto.getGroupTasks());
            }
        }
        this.markAll();
        return BattlePassTaskInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BattlePassTaskInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGroupId()) {
            this.setGroupId(proto.getGroupId());
            fieldCnt++;
        }
        if (proto.hasGroupTasks()) {
            this.getGroupTasks().mergeChangeFromCs(proto.getGroupTasks());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattlePassTaskInfo.Builder getCopyDbBuilder() {
        final BattlePassTaskInfo.Builder builder = BattlePassTaskInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(BattlePassTaskInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGroupId() != 0) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }  else if (builder.hasGroupId()) {
            // 清理GroupId
            builder.clearGroupId();
            fieldCnt++;
        }
        if (this.groupTasks != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.groupTasks.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGroupTasks(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGroupTasks();
            }
        }  else if (builder.hasGroupTasks()) {
            // 清理GroupTasks
            builder.clearGroupTasks();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(BattlePassTaskInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GROUPTASKS) && this.groupTasks != null) {
            final boolean needClear = !builder.hasGroupTasks();
            final int tmpFieldCnt = this.groupTasks.copyChangeToDb(builder.getGroupTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGroupTasks();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(BattlePassTaskInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGroupId()) {
            this.innerSetGroupId(proto.getGroupId());
        } else {
            this.innerSetGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasGroupTasks()) {
            this.getGroupTasks().mergeFromDb(proto.getGroupTasks());
        } else {
            if (this.groupTasks != null) {
                this.groupTasks.mergeFromDb(proto.getGroupTasks());
            }
        }
        this.markAll();
        return BattlePassTaskInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(BattlePassTaskInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGroupId()) {
            this.setGroupId(proto.getGroupId());
            fieldCnt++;
        }
        if (proto.hasGroupTasks()) {
            this.getGroupTasks().mergeChangeFromDb(proto.getGroupTasks());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattlePassTaskInfo.Builder getCopySsBuilder() {
        final BattlePassTaskInfo.Builder builder = BattlePassTaskInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(BattlePassTaskInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGroupId() != 0) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }  else if (builder.hasGroupId()) {
            // 清理GroupId
            builder.clearGroupId();
            fieldCnt++;
        }
        if (this.groupTasks != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.groupTasks.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGroupTasks(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGroupTasks();
            }
        }  else if (builder.hasGroupTasks()) {
            // 清理GroupTasks
            builder.clearGroupTasks();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(BattlePassTaskInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GROUPTASKS) && this.groupTasks != null) {
            final boolean needClear = !builder.hasGroupTasks();
            final int tmpFieldCnt = this.groupTasks.copyChangeToSs(builder.getGroupTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGroupTasks();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(BattlePassTaskInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGroupId()) {
            this.innerSetGroupId(proto.getGroupId());
        } else {
            this.innerSetGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasGroupTasks()) {
            this.getGroupTasks().mergeFromSs(proto.getGroupTasks());
        } else {
            if (this.groupTasks != null) {
                this.groupTasks.mergeFromSs(proto.getGroupTasks());
            }
        }
        this.markAll();
        return BattlePassTaskInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(BattlePassTaskInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGroupId()) {
            this.setGroupId(proto.getGroupId());
            fieldCnt++;
        }
        if (proto.hasGroupTasks()) {
            this.getGroupTasks().mergeChangeFromSs(proto.getGroupTasks());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        BattlePassTaskInfo.Builder builder = BattlePassTaskInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_GROUPTASKS) && this.groupTasks != null) {
            this.groupTasks.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.groupTasks != null) {
            this.groupTasks.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.groupId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BattlePassTaskInfoProp)) {
            return false;
        }
        final BattlePassTaskInfoProp otherNode = (BattlePassTaskInfoProp) node;
        if (this.groupId != otherNode.groupId) {
            return false;
        }
        if (!this.getGroupTasks().compareDataTo(otherNode.getGroupTasks())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Clan.ClanActivityModel;
import com.yorha.proto.Clan;
import com.yorha.proto.ClanPB.ClanActivityModelPB;
import com.yorha.proto.ClanPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanActivityModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_KING = 0;
    public static final int FIELD_INDEX_SCORERANK = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private ClanActCommonScoreProp king = null;
    private Int32ClanScoreRankMapProp scoreRank = null;

    public ClanActivityModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanActivityModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get king
     *
     * @return king value
     */
    public ClanActCommonScoreProp getKing() {
        if (this.king == null) {
            this.king = new ClanActCommonScoreProp(this, FIELD_INDEX_KING);
        }
        return this.king;
    }

    /**
     * get scoreRank
     *
     * @return scoreRank value
     */
    public Int32ClanScoreRankMapProp getScoreRank() {
        if (this.scoreRank == null) {
            this.scoreRank = new Int32ClanScoreRankMapProp(this, FIELD_INDEX_SCORERANK);
        }
        return this.scoreRank;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putScoreRankV(ClanScoreRankProp v) {
        this.getScoreRank().put(v.getActivityId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ClanScoreRankProp addEmptyScoreRank(Integer k) {
        return this.getScoreRank().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getScoreRankSize() {
        if (this.scoreRank == null) {
            return 0;
        }
        return this.scoreRank.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isScoreRankEmpty() {
        if (this.scoreRank == null) {
            return true;
        }
        return this.scoreRank.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ClanScoreRankProp getScoreRankV(Integer k) {
        if (this.scoreRank == null || !this.scoreRank.containsKey(k)) {
            return null;
        }
        return this.scoreRank.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearScoreRank() {
        if (this.scoreRank != null) {
            this.scoreRank.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeScoreRankV(Integer k) {
        if (this.scoreRank != null) {
            this.scoreRank.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanActivityModelPB.Builder getCopyCsBuilder() {
        final ClanActivityModelPB.Builder builder = ClanActivityModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanActivityModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.king != null) {
            ClanPB.ClanActCommonScorePB.Builder tmpBuilder = ClanPB.ClanActCommonScorePB.newBuilder();
            final int tmpFieldCnt = this.king.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKing(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKing();
            }
        }  else if (builder.hasKing()) {
            // 清理King
            builder.clearKing();
            fieldCnt++;
        }
        if (this.scoreRank != null) {
            ClanPB.Int32ClanScoreRankMapPB.Builder tmpBuilder = ClanPB.Int32ClanScoreRankMapPB.newBuilder();
            final int tmpFieldCnt = this.scoreRank.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScoreRank(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScoreRank();
            }
        }  else if (builder.hasScoreRank()) {
            // 清理ScoreRank
            builder.clearScoreRank();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanActivityModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KING) && this.king != null) {
            final boolean needClear = !builder.hasKing();
            final int tmpFieldCnt = this.king.copyChangeToCs(builder.getKingBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKing();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCORERANK) && this.scoreRank != null) {
            final boolean needClear = !builder.hasScoreRank();
            final int tmpFieldCnt = this.scoreRank.copyChangeToCs(builder.getScoreRankBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreRank();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanActivityModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KING) && this.king != null) {
            final boolean needClear = !builder.hasKing();
            final int tmpFieldCnt = this.king.copyChangeToAndClearDeleteKeysCs(builder.getKingBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKing();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCORERANK) && this.scoreRank != null) {
            final boolean needClear = !builder.hasScoreRank();
            final int tmpFieldCnt = this.scoreRank.copyChangeToAndClearDeleteKeysCs(builder.getScoreRankBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreRank();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanActivityModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKing()) {
            this.getKing().mergeFromCs(proto.getKing());
        } else {
            if (this.king != null) {
                this.king.mergeFromCs(proto.getKing());
            }
        }
        if (proto.hasScoreRank()) {
            this.getScoreRank().mergeFromCs(proto.getScoreRank());
        } else {
            if (this.scoreRank != null) {
                this.scoreRank.mergeFromCs(proto.getScoreRank());
            }
        }
        this.markAll();
        return ClanActivityModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanActivityModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKing()) {
            this.getKing().mergeChangeFromCs(proto.getKing());
            fieldCnt++;
        }
        if (proto.hasScoreRank()) {
            this.getScoreRank().mergeChangeFromCs(proto.getScoreRank());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanActivityModel.Builder getCopyDbBuilder() {
        final ClanActivityModel.Builder builder = ClanActivityModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanActivityModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.king != null) {
            Clan.ClanActCommonScore.Builder tmpBuilder = Clan.ClanActCommonScore.newBuilder();
            final int tmpFieldCnt = this.king.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKing(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKing();
            }
        }  else if (builder.hasKing()) {
            // 清理King
            builder.clearKing();
            fieldCnt++;
        }
        if (this.scoreRank != null) {
            Clan.Int32ClanScoreRankMap.Builder tmpBuilder = Clan.Int32ClanScoreRankMap.newBuilder();
            final int tmpFieldCnt = this.scoreRank.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScoreRank(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScoreRank();
            }
        }  else if (builder.hasScoreRank()) {
            // 清理ScoreRank
            builder.clearScoreRank();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanActivityModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KING) && this.king != null) {
            final boolean needClear = !builder.hasKing();
            final int tmpFieldCnt = this.king.copyChangeToDb(builder.getKingBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKing();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCORERANK) && this.scoreRank != null) {
            final boolean needClear = !builder.hasScoreRank();
            final int tmpFieldCnt = this.scoreRank.copyChangeToDb(builder.getScoreRankBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreRank();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanActivityModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKing()) {
            this.getKing().mergeFromDb(proto.getKing());
        } else {
            if (this.king != null) {
                this.king.mergeFromDb(proto.getKing());
            }
        }
        if (proto.hasScoreRank()) {
            this.getScoreRank().mergeFromDb(proto.getScoreRank());
        } else {
            if (this.scoreRank != null) {
                this.scoreRank.mergeFromDb(proto.getScoreRank());
            }
        }
        this.markAll();
        return ClanActivityModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanActivityModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKing()) {
            this.getKing().mergeChangeFromDb(proto.getKing());
            fieldCnt++;
        }
        if (proto.hasScoreRank()) {
            this.getScoreRank().mergeChangeFromDb(proto.getScoreRank());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanActivityModel.Builder getCopySsBuilder() {
        final ClanActivityModel.Builder builder = ClanActivityModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanActivityModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.king != null) {
            Clan.ClanActCommonScore.Builder tmpBuilder = Clan.ClanActCommonScore.newBuilder();
            final int tmpFieldCnt = this.king.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKing(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKing();
            }
        }  else if (builder.hasKing()) {
            // 清理King
            builder.clearKing();
            fieldCnt++;
        }
        if (this.scoreRank != null) {
            Clan.Int32ClanScoreRankMap.Builder tmpBuilder = Clan.Int32ClanScoreRankMap.newBuilder();
            final int tmpFieldCnt = this.scoreRank.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScoreRank(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScoreRank();
            }
        }  else if (builder.hasScoreRank()) {
            // 清理ScoreRank
            builder.clearScoreRank();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanActivityModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KING) && this.king != null) {
            final boolean needClear = !builder.hasKing();
            final int tmpFieldCnt = this.king.copyChangeToSs(builder.getKingBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKing();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCORERANK) && this.scoreRank != null) {
            final boolean needClear = !builder.hasScoreRank();
            final int tmpFieldCnt = this.scoreRank.copyChangeToSs(builder.getScoreRankBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreRank();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanActivityModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKing()) {
            this.getKing().mergeFromSs(proto.getKing());
        } else {
            if (this.king != null) {
                this.king.mergeFromSs(proto.getKing());
            }
        }
        if (proto.hasScoreRank()) {
            this.getScoreRank().mergeFromSs(proto.getScoreRank());
        } else {
            if (this.scoreRank != null) {
                this.scoreRank.mergeFromSs(proto.getScoreRank());
            }
        }
        this.markAll();
        return ClanActivityModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanActivityModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKing()) {
            this.getKing().mergeChangeFromSs(proto.getKing());
            fieldCnt++;
        }
        if (proto.hasScoreRank()) {
            this.getScoreRank().mergeChangeFromSs(proto.getScoreRank());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanActivityModel.Builder builder = ClanActivityModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_KING) && this.king != null) {
            this.king.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SCORERANK) && this.scoreRank != null) {
            this.scoreRank.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.king != null) {
            this.king.markAll();
        }
        if (this.scoreRank != null) {
            this.scoreRank.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanActivityModelProp)) {
            return false;
        }
        final ClanActivityModelProp otherNode = (ClanActivityModelProp) node;
        if (!this.getKing().compareDataTo(otherNode.getKing())) {
            return false;
        }
        if (!this.getScoreRank().compareDataTo(otherNode.getScoreRank())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.Hero;
import com.yorha.proto.Basic;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.HeroPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class HeroProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_HEROID = 0;
    public static final int FIELD_INDEX_LEVEL = 1;
    public static final int FIELD_INDEX_STAR = 2;
    public static final int FIELD_INDEX_ANGER = 3;
    public static final int FIELD_INDEX_SKILLS = 4;
    public static final int FIELD_INDEX_TALENTIDS = 5;
    public static final int FIELD_INDEX_ISAWAKE = 6;

    public static final int FIELD_COUNT = 7;

    private long markBits0 = 0L;

    private int heroId = Constant.DEFAULT_INT_VALUE;
    private int level = Constant.DEFAULT_INT_VALUE;
    private int star = Constant.DEFAULT_INT_VALUE;
    private int anger = Constant.DEFAULT_INT_VALUE;
    private SimpleSkillListProp skills = null;
    private Int32ListProp talentIds = null;
    private boolean isAwake = Constant.DEFAULT_BOOLEAN_VALUE;

    public HeroProp() {
        super(null, 0, FIELD_COUNT);
    }

    public HeroProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get heroId
     *
     * @return heroId value
     */
    public int getHeroId() {
        return this.heroId;
    }

    /**
     * set heroId && set marked
     *
     * @param heroId new value
     * @return current object
     */
    public HeroProp setHeroId(int heroId) {
        if (this.heroId != heroId) {
            this.mark(FIELD_INDEX_HEROID);
            this.heroId = heroId;
        }
        return this;
    }

    /**
     * inner set heroId
     *
     * @param heroId new value
     */
    private void innerSetHeroId(int heroId) {
        this.heroId = heroId;
    }

    /**
     * get level
     *
     * @return level value
     */
    public int getLevel() {
        return this.level;
    }

    /**
     * set level && set marked
     *
     * @param level new value
     * @return current object
     */
    public HeroProp setLevel(int level) {
        if (this.level != level) {
            this.mark(FIELD_INDEX_LEVEL);
            this.level = level;
        }
        return this;
    }

    /**
     * inner set level
     *
     * @param level new value
     */
    private void innerSetLevel(int level) {
        this.level = level;
    }

    /**
     * get star
     *
     * @return star value
     */
    public int getStar() {
        return this.star;
    }

    /**
     * set star && set marked
     *
     * @param star new value
     * @return current object
     */
    public HeroProp setStar(int star) {
        if (this.star != star) {
            this.mark(FIELD_INDEX_STAR);
            this.star = star;
        }
        return this;
    }

    /**
     * inner set star
     *
     * @param star new value
     */
    private void innerSetStar(int star) {
        this.star = star;
    }

    /**
     * get anger
     *
     * @return anger value
     */
    public int getAnger() {
        return this.anger;
    }

    /**
     * set anger && set marked
     *
     * @param anger new value
     * @return current object
     */
    public HeroProp setAnger(int anger) {
        if (this.anger != anger) {
            this.mark(FIELD_INDEX_ANGER);
            this.anger = anger;
        }
        return this;
    }

    /**
     * inner set anger
     *
     * @param anger new value
     */
    private void innerSetAnger(int anger) {
        this.anger = anger;
    }

    /**
     * get skills
     *
     * @return skills value
     */
    public SimpleSkillListProp getSkills() {
        if (this.skills == null) {
            this.skills = new SimpleSkillListProp(this, FIELD_INDEX_SKILLS);
        }
        return this.skills;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addSkills(SimpleSkillProp v) {
        this.getSkills().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public SimpleSkillProp getSkillsIndex(int index) {
        return this.getSkills().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public SimpleSkillProp removeSkills(SimpleSkillProp v) {
        if (this.skills == null) {
            return null;
        }
        if(this.skills.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getSkillsSize() {
        if (this.skills == null) {
            return 0;
        }
        return this.skills.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isSkillsEmpty() {
        if (this.skills == null) {
            return true;
        }
        return this.getSkills().isEmpty();
    }

    /**
     * clear list
     */
    public void clearSkills() {
        this.getSkills().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public SimpleSkillProp removeSkillsIndex(int index) {
        return this.getSkills().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public SimpleSkillProp setSkillsIndex(int index, SimpleSkillProp v) {
        return this.getSkills().set(index, v);
    }
    /**
     * get talentIds
     *
     * @return talentIds value
     */
    public Int32ListProp getTalentIds() {
        if (this.talentIds == null) {
            this.talentIds = new Int32ListProp(this, FIELD_INDEX_TALENTIDS);
        }
        return this.talentIds;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addTalentIds(Integer v) {
        this.getTalentIds().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getTalentIdsIndex(int index) {
        return this.getTalentIds().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeTalentIds(Integer v) {
        if (this.talentIds == null) {
            return null;
        }
        if(this.talentIds.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getTalentIdsSize() {
        if (this.talentIds == null) {
            return 0;
        }
        return this.talentIds.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isTalentIdsEmpty() {
        if (this.talentIds == null) {
            return true;
        }
        return this.getTalentIds().isEmpty();
    }

    /**
     * clear list
     */
    public void clearTalentIds() {
        this.getTalentIds().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeTalentIdsIndex(int index) {
        return this.getTalentIds().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setTalentIdsIndex(int index, Integer v) {
        return this.getTalentIds().set(index, v);
    }
    /**
     * get isAwake
     *
     * @return isAwake value
     */
    public boolean getIsAwake() {
        return this.isAwake;
    }

    /**
     * set isAwake && set marked
     *
     * @param isAwake new value
     * @return current object
     */
    public HeroProp setIsAwake(boolean isAwake) {
        if (this.isAwake != isAwake) {
            this.mark(FIELD_INDEX_ISAWAKE);
            this.isAwake = isAwake;
        }
        return this;
    }

    /**
     * inner set isAwake
     *
     * @param isAwake new value
     */
    private void innerSetIsAwake(boolean isAwake) {
        this.isAwake = isAwake;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public HeroPB.Builder getCopyCsBuilder() {
        final HeroPB.Builder builder = HeroPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(HeroPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getStar() != 0) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }  else if (builder.hasStar()) {
            // 清理Star
            builder.clearStar();
            fieldCnt++;
        }
        if (this.getAnger() != 0) {
            builder.setAnger(this.getAnger());
            fieldCnt++;
        }  else if (builder.hasAnger()) {
            // 清理Anger
            builder.clearAnger();
            fieldCnt++;
        }
        if (this.skills != null) {
            StructPB.SimpleSkillListPB.Builder tmpBuilder = StructPB.SimpleSkillListPB.newBuilder();
            final int tmpFieldCnt = this.skills.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkills(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkills();
            }
        }  else if (builder.hasSkills()) {
            // 清理Skills
            builder.clearSkills();
            fieldCnt++;
        }
        if (this.getIsAwake()) {
            builder.setIsAwake(this.getIsAwake());
            fieldCnt++;
        }  else if (builder.hasIsAwake()) {
            // 清理IsAwake
            builder.clearIsAwake();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(HeroPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ANGER)) {
            builder.setAnger(this.getAnger());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLS) && this.skills != null) {
            final boolean needClear = !builder.hasSkills();
            final int tmpFieldCnt = this.skills.copyChangeToCs(builder.getSkillsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkills();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISAWAKE)) {
            builder.setIsAwake(this.getIsAwake());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(HeroPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ANGER)) {
            builder.setAnger(this.getAnger());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLS) && this.skills != null) {
            final boolean needClear = !builder.hasSkills();
            final int tmpFieldCnt = this.skills.copyChangeToCs(builder.getSkillsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkills();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISAWAKE)) {
            builder.setIsAwake(this.getIsAwake());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(HeroPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStar()) {
            this.innerSetStar(proto.getStar());
        } else {
            this.innerSetStar(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAnger()) {
            this.innerSetAnger(proto.getAnger());
        } else {
            this.innerSetAnger(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkills()) {
            this.getSkills().mergeFromCs(proto.getSkills());
        } else {
            if (this.skills != null) {
                this.skills.mergeFromCs(proto.getSkills());
            }
        }
        if (proto.hasIsAwake()) {
            this.innerSetIsAwake(proto.getIsAwake());
        } else {
            this.innerSetIsAwake(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return HeroProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(HeroPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasStar()) {
            this.setStar(proto.getStar());
            fieldCnt++;
        }
        if (proto.hasAnger()) {
            this.setAnger(proto.getAnger());
            fieldCnt++;
        }
        if (proto.hasSkills()) {
            this.getSkills().mergeChangeFromCs(proto.getSkills());
            fieldCnt++;
        }
        if (proto.hasIsAwake()) {
            this.setIsAwake(proto.getIsAwake());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Hero.Builder getCopyDbBuilder() {
        final Hero.Builder builder = Hero.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(Hero.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getStar() != 0) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }  else if (builder.hasStar()) {
            // 清理Star
            builder.clearStar();
            fieldCnt++;
        }
        if (this.skills != null) {
            Struct.SimpleSkillList.Builder tmpBuilder = Struct.SimpleSkillList.newBuilder();
            final int tmpFieldCnt = this.skills.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkills(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkills();
            }
        }  else if (builder.hasSkills()) {
            // 清理Skills
            builder.clearSkills();
            fieldCnt++;
        }
        if (this.talentIds != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.talentIds.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTalentIds(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTalentIds();
            }
        }  else if (builder.hasTalentIds()) {
            // 清理TalentIds
            builder.clearTalentIds();
            fieldCnt++;
        }
        if (this.getIsAwake()) {
            builder.setIsAwake(this.getIsAwake());
            fieldCnt++;
        }  else if (builder.hasIsAwake()) {
            // 清理IsAwake
            builder.clearIsAwake();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(Hero.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLS) && this.skills != null) {
            final boolean needClear = !builder.hasSkills();
            final int tmpFieldCnt = this.skills.copyChangeToDb(builder.getSkillsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkills();
            }
        }
        if (this.hasMark(FIELD_INDEX_TALENTIDS) && this.talentIds != null) {
            final boolean needClear = !builder.hasTalentIds();
            final int tmpFieldCnt = this.talentIds.copyChangeToDb(builder.getTalentIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTalentIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISAWAKE)) {
            builder.setIsAwake(this.getIsAwake());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(Hero proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStar()) {
            this.innerSetStar(proto.getStar());
        } else {
            this.innerSetStar(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkills()) {
            this.getSkills().mergeFromDb(proto.getSkills());
        } else {
            if (this.skills != null) {
                this.skills.mergeFromDb(proto.getSkills());
            }
        }
        if (proto.hasTalentIds()) {
            this.getTalentIds().mergeFromDb(proto.getTalentIds());
        } else {
            if (this.talentIds != null) {
                this.talentIds.mergeFromDb(proto.getTalentIds());
            }
        }
        if (proto.hasIsAwake()) {
            this.innerSetIsAwake(proto.getIsAwake());
        } else {
            this.innerSetIsAwake(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return HeroProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(Hero proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasStar()) {
            this.setStar(proto.getStar());
            fieldCnt++;
        }
        if (proto.hasSkills()) {
            this.getSkills().mergeChangeFromDb(proto.getSkills());
            fieldCnt++;
        }
        if (proto.hasTalentIds()) {
            this.getTalentIds().mergeChangeFromDb(proto.getTalentIds());
            fieldCnt++;
        }
        if (proto.hasIsAwake()) {
            this.setIsAwake(proto.getIsAwake());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Hero.Builder getCopySsBuilder() {
        final Hero.Builder builder = Hero.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(Hero.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getStar() != 0) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }  else if (builder.hasStar()) {
            // 清理Star
            builder.clearStar();
            fieldCnt++;
        }
        if (this.getAnger() != 0) {
            builder.setAnger(this.getAnger());
            fieldCnt++;
        }  else if (builder.hasAnger()) {
            // 清理Anger
            builder.clearAnger();
            fieldCnt++;
        }
        if (this.skills != null) {
            Struct.SimpleSkillList.Builder tmpBuilder = Struct.SimpleSkillList.newBuilder();
            final int tmpFieldCnt = this.skills.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkills(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkills();
            }
        }  else if (builder.hasSkills()) {
            // 清理Skills
            builder.clearSkills();
            fieldCnt++;
        }
        if (this.talentIds != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.talentIds.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTalentIds(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTalentIds();
            }
        }  else if (builder.hasTalentIds()) {
            // 清理TalentIds
            builder.clearTalentIds();
            fieldCnt++;
        }
        if (this.getIsAwake()) {
            builder.setIsAwake(this.getIsAwake());
            fieldCnt++;
        }  else if (builder.hasIsAwake()) {
            // 清理IsAwake
            builder.clearIsAwake();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(Hero.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ANGER)) {
            builder.setAnger(this.getAnger());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLS) && this.skills != null) {
            final boolean needClear = !builder.hasSkills();
            final int tmpFieldCnt = this.skills.copyChangeToSs(builder.getSkillsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkills();
            }
        }
        if (this.hasMark(FIELD_INDEX_TALENTIDS) && this.talentIds != null) {
            final boolean needClear = !builder.hasTalentIds();
            final int tmpFieldCnt = this.talentIds.copyChangeToSs(builder.getTalentIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTalentIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISAWAKE)) {
            builder.setIsAwake(this.getIsAwake());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(Hero proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStar()) {
            this.innerSetStar(proto.getStar());
        } else {
            this.innerSetStar(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAnger()) {
            this.innerSetAnger(proto.getAnger());
        } else {
            this.innerSetAnger(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkills()) {
            this.getSkills().mergeFromSs(proto.getSkills());
        } else {
            if (this.skills != null) {
                this.skills.mergeFromSs(proto.getSkills());
            }
        }
        if (proto.hasTalentIds()) {
            this.getTalentIds().mergeFromSs(proto.getTalentIds());
        } else {
            if (this.talentIds != null) {
                this.talentIds.mergeFromSs(proto.getTalentIds());
            }
        }
        if (proto.hasIsAwake()) {
            this.innerSetIsAwake(proto.getIsAwake());
        } else {
            this.innerSetIsAwake(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return HeroProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(Hero proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasStar()) {
            this.setStar(proto.getStar());
            fieldCnt++;
        }
        if (proto.hasAnger()) {
            this.setAnger(proto.getAnger());
            fieldCnt++;
        }
        if (proto.hasSkills()) {
            this.getSkills().mergeChangeFromSs(proto.getSkills());
            fieldCnt++;
        }
        if (proto.hasTalentIds()) {
            this.getTalentIds().mergeChangeFromSs(proto.getTalentIds());
            fieldCnt++;
        }
        if (proto.hasIsAwake()) {
            this.setIsAwake(proto.getIsAwake());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        Hero.Builder builder = Hero.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SKILLS) && this.skills != null) {
            this.skills.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TALENTIDS) && this.talentIds != null) {
            this.talentIds.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.skills != null) {
            this.skills.markAll();
        }
        if (this.talentIds != null) {
            this.talentIds.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.heroId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof HeroProp)) {
            return false;
        }
        final HeroProp otherNode = (HeroProp) node;
        if (this.heroId != otherNode.heroId) {
            return false;
        }
        if (this.level != otherNode.level) {
            return false;
        }
        if (this.star != otherNode.star) {
            return false;
        }
        if (this.anger != otherNode.anger) {
            return false;
        }
        if (!this.getSkills().compareDataTo(otherNode.getSkills())) {
            return false;
        }
        if (!this.getTalentIds().compareDataTo(otherNode.getTalentIds())) {
            return false;
        }
        if (this.isAwake != otherNode.isAwake) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 57;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructMail.MailKingGainTaxData;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMailPB.MailKingGainTaxDataPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class MailKingGainTaxDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TAX = 0;
    public static final int FIELD_INDEX_CALTSMS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private Int32CurrencyMapProp tax = null;
    private long calTsMs = Constant.DEFAULT_LONG_VALUE;

    public MailKingGainTaxDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MailKingGainTaxDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get tax
     *
     * @return tax value
     */
    public Int32CurrencyMapProp getTax() {
        if (this.tax == null) {
            this.tax = new Int32CurrencyMapProp(this, FIELD_INDEX_TAX);
        }
        return this.tax;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTaxV(CurrencyProp v) {
        this.getTax().put(v.getType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public CurrencyProp addEmptyTax(Integer k) {
        return this.getTax().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTaxSize() {
        if (this.tax == null) {
            return 0;
        }
        return this.tax.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTaxEmpty() {
        if (this.tax == null) {
            return true;
        }
        return this.tax.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public CurrencyProp getTaxV(Integer k) {
        if (this.tax == null || !this.tax.containsKey(k)) {
            return null;
        }
        return this.tax.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTax() {
        if (this.tax != null) {
            this.tax.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTaxV(Integer k) {
        if (this.tax != null) {
            this.tax.remove(k);
        }
    }
    /**
     * get calTsMs
     *
     * @return calTsMs value
     */
    public long getCalTsMs() {
        return this.calTsMs;
    }

    /**
     * set calTsMs && set marked
     *
     * @param calTsMs new value
     * @return current object
     */
    public MailKingGainTaxDataProp setCalTsMs(long calTsMs) {
        if (this.calTsMs != calTsMs) {
            this.mark(FIELD_INDEX_CALTSMS);
            this.calTsMs = calTsMs;
        }
        return this;
    }

    /**
     * inner set calTsMs
     *
     * @param calTsMs new value
     */
    private void innerSetCalTsMs(long calTsMs) {
        this.calTsMs = calTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailKingGainTaxDataPB.Builder getCopyCsBuilder() {
        final MailKingGainTaxDataPB.Builder builder = MailKingGainTaxDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MailKingGainTaxDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.tax != null) {
            StructPB.Int32CurrencyMapPB.Builder tmpBuilder = StructPB.Int32CurrencyMapPB.newBuilder();
            final int tmpFieldCnt = this.tax.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTax(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTax();
            }
        }  else if (builder.hasTax()) {
            // 清理Tax
            builder.clearTax();
            fieldCnt++;
        }
        if (this.getCalTsMs() != 0L) {
            builder.setCalTsMs(this.getCalTsMs());
            fieldCnt++;
        }  else if (builder.hasCalTsMs()) {
            // 清理CalTsMs
            builder.clearCalTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MailKingGainTaxDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TAX) && this.tax != null) {
            final boolean needClear = !builder.hasTax();
            final int tmpFieldCnt = this.tax.copyChangeToCs(builder.getTaxBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTax();
            }
        }
        if (this.hasMark(FIELD_INDEX_CALTSMS)) {
            builder.setCalTsMs(this.getCalTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MailKingGainTaxDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TAX) && this.tax != null) {
            final boolean needClear = !builder.hasTax();
            final int tmpFieldCnt = this.tax.copyChangeToAndClearDeleteKeysCs(builder.getTaxBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTax();
            }
        }
        if (this.hasMark(FIELD_INDEX_CALTSMS)) {
            builder.setCalTsMs(this.getCalTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MailKingGainTaxDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTax()) {
            this.getTax().mergeFromCs(proto.getTax());
        } else {
            if (this.tax != null) {
                this.tax.mergeFromCs(proto.getTax());
            }
        }
        if (proto.hasCalTsMs()) {
            this.innerSetCalTsMs(proto.getCalTsMs());
        } else {
            this.innerSetCalTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MailKingGainTaxDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MailKingGainTaxDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTax()) {
            this.getTax().mergeChangeFromCs(proto.getTax());
            fieldCnt++;
        }
        if (proto.hasCalTsMs()) {
            this.setCalTsMs(proto.getCalTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailKingGainTaxData.Builder getCopyDbBuilder() {
        final MailKingGainTaxData.Builder builder = MailKingGainTaxData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MailKingGainTaxData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.tax != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.tax.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTax(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTax();
            }
        }  else if (builder.hasTax()) {
            // 清理Tax
            builder.clearTax();
            fieldCnt++;
        }
        if (this.getCalTsMs() != 0L) {
            builder.setCalTsMs(this.getCalTsMs());
            fieldCnt++;
        }  else if (builder.hasCalTsMs()) {
            // 清理CalTsMs
            builder.clearCalTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MailKingGainTaxData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TAX) && this.tax != null) {
            final boolean needClear = !builder.hasTax();
            final int tmpFieldCnt = this.tax.copyChangeToDb(builder.getTaxBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTax();
            }
        }
        if (this.hasMark(FIELD_INDEX_CALTSMS)) {
            builder.setCalTsMs(this.getCalTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MailKingGainTaxData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTax()) {
            this.getTax().mergeFromDb(proto.getTax());
        } else {
            if (this.tax != null) {
                this.tax.mergeFromDb(proto.getTax());
            }
        }
        if (proto.hasCalTsMs()) {
            this.innerSetCalTsMs(proto.getCalTsMs());
        } else {
            this.innerSetCalTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MailKingGainTaxDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MailKingGainTaxData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTax()) {
            this.getTax().mergeChangeFromDb(proto.getTax());
            fieldCnt++;
        }
        if (proto.hasCalTsMs()) {
            this.setCalTsMs(proto.getCalTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailKingGainTaxData.Builder getCopySsBuilder() {
        final MailKingGainTaxData.Builder builder = MailKingGainTaxData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MailKingGainTaxData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.tax != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.tax.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTax(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTax();
            }
        }  else if (builder.hasTax()) {
            // 清理Tax
            builder.clearTax();
            fieldCnt++;
        }
        if (this.getCalTsMs() != 0L) {
            builder.setCalTsMs(this.getCalTsMs());
            fieldCnt++;
        }  else if (builder.hasCalTsMs()) {
            // 清理CalTsMs
            builder.clearCalTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MailKingGainTaxData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TAX) && this.tax != null) {
            final boolean needClear = !builder.hasTax();
            final int tmpFieldCnt = this.tax.copyChangeToSs(builder.getTaxBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTax();
            }
        }
        if (this.hasMark(FIELD_INDEX_CALTSMS)) {
            builder.setCalTsMs(this.getCalTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MailKingGainTaxData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTax()) {
            this.getTax().mergeFromSs(proto.getTax());
        } else {
            if (this.tax != null) {
                this.tax.mergeFromSs(proto.getTax());
            }
        }
        if (proto.hasCalTsMs()) {
            this.innerSetCalTsMs(proto.getCalTsMs());
        } else {
            this.innerSetCalTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MailKingGainTaxDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MailKingGainTaxData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTax()) {
            this.getTax().mergeChangeFromSs(proto.getTax());
            fieldCnt++;
        }
        if (proto.hasCalTsMs()) {
            this.setCalTsMs(proto.getCalTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MailKingGainTaxData.Builder builder = MailKingGainTaxData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TAX) && this.tax != null) {
            this.tax.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.tax != null) {
            this.tax.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MailKingGainTaxDataProp)) {
            return false;
        }
        final MailKingGainTaxDataProp otherNode = (MailKingGainTaxDataProp) node;
        if (!this.getTax().compareDataTo(otherNode.getTax())) {
            return false;
        }
        if (this.calTsMs != otherNode.calTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.SimpleHero;
import com.yorha.proto.StructPB.SimpleHeroPB;


/**
 * <AUTHOR> auto gen
 */
public class SimpleHeroProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_HEROID = 0;
    public static final int FIELD_INDEX_LEVEL = 1;
    public static final int FIELD_INDEX_STAR = 2;
    public static final int FIELD_INDEX_ISAWAKE = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int heroId = Constant.DEFAULT_INT_VALUE;
    private int level = Constant.DEFAULT_INT_VALUE;
    private int star = Constant.DEFAULT_INT_VALUE;
    private boolean isAwake = Constant.DEFAULT_BOOLEAN_VALUE;

    public SimpleHeroProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SimpleHeroProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get heroId
     *
     * @return heroId value
     */
    public int getHeroId() {
        return this.heroId;
    }

    /**
     * set heroId && set marked
     *
     * @param heroId new value
     * @return current object
     */
    public SimpleHeroProp setHeroId(int heroId) {
        if (this.heroId != heroId) {
            this.mark(FIELD_INDEX_HEROID);
            this.heroId = heroId;
        }
        return this;
    }

    /**
     * inner set heroId
     *
     * @param heroId new value
     */
    private void innerSetHeroId(int heroId) {
        this.heroId = heroId;
    }

    /**
     * get level
     *
     * @return level value
     */
    public int getLevel() {
        return this.level;
    }

    /**
     * set level && set marked
     *
     * @param level new value
     * @return current object
     */
    public SimpleHeroProp setLevel(int level) {
        if (this.level != level) {
            this.mark(FIELD_INDEX_LEVEL);
            this.level = level;
        }
        return this;
    }

    /**
     * inner set level
     *
     * @param level new value
     */
    private void innerSetLevel(int level) {
        this.level = level;
    }

    /**
     * get star
     *
     * @return star value
     */
    public int getStar() {
        return this.star;
    }

    /**
     * set star && set marked
     *
     * @param star new value
     * @return current object
     */
    public SimpleHeroProp setStar(int star) {
        if (this.star != star) {
            this.mark(FIELD_INDEX_STAR);
            this.star = star;
        }
        return this;
    }

    /**
     * inner set star
     *
     * @param star new value
     */
    private void innerSetStar(int star) {
        this.star = star;
    }

    /**
     * get isAwake
     *
     * @return isAwake value
     */
    public boolean getIsAwake() {
        return this.isAwake;
    }

    /**
     * set isAwake && set marked
     *
     * @param isAwake new value
     * @return current object
     */
    public SimpleHeroProp setIsAwake(boolean isAwake) {
        if (this.isAwake != isAwake) {
            this.mark(FIELD_INDEX_ISAWAKE);
            this.isAwake = isAwake;
        }
        return this;
    }

    /**
     * inner set isAwake
     *
     * @param isAwake new value
     */
    private void innerSetIsAwake(boolean isAwake) {
        this.isAwake = isAwake;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SimpleHeroPB.Builder getCopyCsBuilder() {
        final SimpleHeroPB.Builder builder = SimpleHeroPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SimpleHeroPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getStar() != 0) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }  else if (builder.hasStar()) {
            // 清理Star
            builder.clearStar();
            fieldCnt++;
        }
        if (this.getIsAwake()) {
            builder.setIsAwake(this.getIsAwake());
            fieldCnt++;
        }  else if (builder.hasIsAwake()) {
            // 清理IsAwake
            builder.clearIsAwake();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SimpleHeroPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISAWAKE)) {
            builder.setIsAwake(this.getIsAwake());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SimpleHeroPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISAWAKE)) {
            builder.setIsAwake(this.getIsAwake());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SimpleHeroPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStar()) {
            this.innerSetStar(proto.getStar());
        } else {
            this.innerSetStar(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIsAwake()) {
            this.innerSetIsAwake(proto.getIsAwake());
        } else {
            this.innerSetIsAwake(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return SimpleHeroProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SimpleHeroPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasStar()) {
            this.setStar(proto.getStar());
            fieldCnt++;
        }
        if (proto.hasIsAwake()) {
            this.setIsAwake(proto.getIsAwake());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SimpleHero.Builder getCopyDbBuilder() {
        final SimpleHero.Builder builder = SimpleHero.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SimpleHero.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getStar() != 0) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }  else if (builder.hasStar()) {
            // 清理Star
            builder.clearStar();
            fieldCnt++;
        }
        if (this.getIsAwake()) {
            builder.setIsAwake(this.getIsAwake());
            fieldCnt++;
        }  else if (builder.hasIsAwake()) {
            // 清理IsAwake
            builder.clearIsAwake();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SimpleHero.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISAWAKE)) {
            builder.setIsAwake(this.getIsAwake());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SimpleHero proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStar()) {
            this.innerSetStar(proto.getStar());
        } else {
            this.innerSetStar(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIsAwake()) {
            this.innerSetIsAwake(proto.getIsAwake());
        } else {
            this.innerSetIsAwake(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return SimpleHeroProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SimpleHero proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasStar()) {
            this.setStar(proto.getStar());
            fieldCnt++;
        }
        if (proto.hasIsAwake()) {
            this.setIsAwake(proto.getIsAwake());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SimpleHero.Builder getCopySsBuilder() {
        final SimpleHero.Builder builder = SimpleHero.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SimpleHero.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getStar() != 0) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }  else if (builder.hasStar()) {
            // 清理Star
            builder.clearStar();
            fieldCnt++;
        }
        if (this.getIsAwake()) {
            builder.setIsAwake(this.getIsAwake());
            fieldCnt++;
        }  else if (builder.hasIsAwake()) {
            // 清理IsAwake
            builder.clearIsAwake();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SimpleHero.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISAWAKE)) {
            builder.setIsAwake(this.getIsAwake());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SimpleHero proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStar()) {
            this.innerSetStar(proto.getStar());
        } else {
            this.innerSetStar(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIsAwake()) {
            this.innerSetIsAwake(proto.getIsAwake());
        } else {
            this.innerSetIsAwake(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return SimpleHeroProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SimpleHero proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasStar()) {
            this.setStar(proto.getStar());
            fieldCnt++;
        }
        if (proto.hasIsAwake()) {
            this.setIsAwake(proto.getIsAwake());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SimpleHero.Builder builder = SimpleHero.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.heroId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SimpleHeroProp)) {
            return false;
        }
        final SimpleHeroProp otherNode = (SimpleHeroProp) node;
        if (this.heroId != otherNode.heroId) {
            return false;
        }
        if (this.level != otherNode.level) {
            return false;
        }
        if (this.star != otherNode.star) {
            return false;
        }
        if (this.isAwake != otherNode.isAwake) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
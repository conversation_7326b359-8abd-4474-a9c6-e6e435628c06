package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.SpyData;
import com.yorha.proto.Basic;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.SpyDataPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class SpyDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TARGETTYPE = 0;
    public static final int FIELD_INDEX_CLANBRIEFNAME = 1;
    public static final int FIELD_INDEX_POINT = 2;
    public static final int FIELD_INDEX_SPYARMYDATA = 3;
    public static final int FIELD_INDEX_SPYCITYDATA = 4;
    public static final int FIELD_INDEX_SPYRALLYARMYDATA = 5;
    public static final int FIELD_INDEX_SPYBUILDINGDATA = 6;
    public static final int FIELD_INDEX_SPYASSISTARMYDATA = 7;
    public static final int FIELD_INDEX_TARGETID = 8;
    public static final int FIELD_INDEX_CARDHEAD = 9;
    public static final int FIELD_INDEX_SPYTECH = 10;

    public static final int FIELD_COUNT = 11;

    private long markBits0 = 0L;

    private String targetType = Constant.DEFAULT_STR_VALUE;
    private String clanBriefName = Constant.DEFAULT_STR_VALUE;
    private PointProp point = null;
    private SpyArmyDataProp spyArmyData = null;
    private SpyCityDataProp spyCityData = null;
    private SpyArmyDataProp spyRallyArmyData = null;
    private SpyBuildingDataProp SpyBuildingData = null;
    private SpyArmyDataProp spyAssistArmyData = null;
    private long targetId = Constant.DEFAULT_LONG_VALUE;
    private PlayerCardHeadProp cardHead = null;
    private Int32ListProp spyTech = null;

    public SpyDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SpyDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get targetType
     *
     * @return targetType value
     */
    public String getTargetType() {
        return this.targetType;
    }

    /**
     * set targetType && set marked
     *
     * @param targetType new value
     * @return current object
     */
    public SpyDataProp setTargetType(String targetType) {
        if (targetType == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.targetType, targetType)) {
            this.mark(FIELD_INDEX_TARGETTYPE);
            this.targetType = targetType;
        }
        return this;
    }

    /**
     * inner set targetType
     *
     * @param targetType new value
     */
    private void innerSetTargetType(String targetType) {
        this.targetType = targetType;
    }

    /**
     * get clanBriefName
     *
     * @return clanBriefName value
     */
    public String getClanBriefName() {
        return this.clanBriefName;
    }

    /**
     * set clanBriefName && set marked
     *
     * @param clanBriefName new value
     * @return current object
     */
    public SpyDataProp setClanBriefName(String clanBriefName) {
        if (clanBriefName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanBriefName, clanBriefName)) {
            this.mark(FIELD_INDEX_CLANBRIEFNAME);
            this.clanBriefName = clanBriefName;
        }
        return this;
    }

    /**
     * inner set clanBriefName
     *
     * @param clanBriefName new value
     */
    private void innerSetClanBriefName(String clanBriefName) {
        this.clanBriefName = clanBriefName;
    }

    /**
     * get point
     *
     * @return point value
     */
    public PointProp getPoint() {
        if (this.point == null) {
            this.point = new PointProp(this, FIELD_INDEX_POINT);
        }
        return this.point;
    }

    /**
     * get spyArmyData
     *
     * @return spyArmyData value
     */
    public SpyArmyDataProp getSpyArmyData() {
        if (this.spyArmyData == null) {
            this.spyArmyData = new SpyArmyDataProp(this, FIELD_INDEX_SPYARMYDATA);
        }
        return this.spyArmyData;
    }

    /**
     * get spyCityData
     *
     * @return spyCityData value
     */
    public SpyCityDataProp getSpyCityData() {
        if (this.spyCityData == null) {
            this.spyCityData = new SpyCityDataProp(this, FIELD_INDEX_SPYCITYDATA);
        }
        return this.spyCityData;
    }

    /**
     * get spyRallyArmyData
     *
     * @return spyRallyArmyData value
     */
    public SpyArmyDataProp getSpyRallyArmyData() {
        if (this.spyRallyArmyData == null) {
            this.spyRallyArmyData = new SpyArmyDataProp(this, FIELD_INDEX_SPYRALLYARMYDATA);
        }
        return this.spyRallyArmyData;
    }

    /**
     * get SpyBuildingData
     *
     * @return SpyBuildingData value
     */
    public SpyBuildingDataProp getSpyBuildingData() {
        if (this.SpyBuildingData == null) {
            this.SpyBuildingData = new SpyBuildingDataProp(this, FIELD_INDEX_SPYBUILDINGDATA);
        }
        return this.SpyBuildingData;
    }

    /**
     * get spyAssistArmyData
     *
     * @return spyAssistArmyData value
     */
    public SpyArmyDataProp getSpyAssistArmyData() {
        if (this.spyAssistArmyData == null) {
            this.spyAssistArmyData = new SpyArmyDataProp(this, FIELD_INDEX_SPYASSISTARMYDATA);
        }
        return this.spyAssistArmyData;
    }

    /**
     * get targetId
     *
     * @return targetId value
     */
    public long getTargetId() {
        return this.targetId;
    }

    /**
     * set targetId && set marked
     *
     * @param targetId new value
     * @return current object
     */
    public SpyDataProp setTargetId(long targetId) {
        if (this.targetId != targetId) {
            this.mark(FIELD_INDEX_TARGETID);
            this.targetId = targetId;
        }
        return this;
    }

    /**
     * inner set targetId
     *
     * @param targetId new value
     */
    private void innerSetTargetId(long targetId) {
        this.targetId = targetId;
    }

    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }

    /**
     * get spyTech
     *
     * @return spyTech value
     */
    public Int32ListProp getSpyTech() {
        if (this.spyTech == null) {
            this.spyTech = new Int32ListProp(this, FIELD_INDEX_SPYTECH);
        }
        return this.spyTech;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addSpyTech(Integer v) {
        this.getSpyTech().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getSpyTechIndex(int index) {
        return this.getSpyTech().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeSpyTech(Integer v) {
        if (this.spyTech == null) {
            return null;
        }
        if(this.spyTech.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getSpyTechSize() {
        if (this.spyTech == null) {
            return 0;
        }
        return this.spyTech.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isSpyTechEmpty() {
        if (this.spyTech == null) {
            return true;
        }
        return this.getSpyTech().isEmpty();
    }

    /**
     * clear list
     */
    public void clearSpyTech() {
        this.getSpyTech().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeSpyTechIndex(int index) {
        return this.getSpyTech().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setSpyTechIndex(int index, Integer v) {
        return this.getSpyTech().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SpyDataPB.Builder getCopyCsBuilder() {
        final SpyDataPB.Builder builder = SpyDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SpyDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getTargetType().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTargetType(this.getTargetType());
            fieldCnt++;
        }  else if (builder.hasTargetType()) {
            // 清理TargetType
            builder.clearTargetType();
            fieldCnt++;
        }
        if (!this.getClanBriefName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanBriefName(this.getClanBriefName());
            fieldCnt++;
        }  else if (builder.hasClanBriefName()) {
            // 清理ClanBriefName
            builder.clearClanBriefName();
            fieldCnt++;
        }
        if (this.point != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.point.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.spyArmyData != null) {
            StructPB.SpyArmyDataPB.Builder tmpBuilder = StructPB.SpyArmyDataPB.newBuilder();
            final int tmpFieldCnt = this.spyArmyData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyArmyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyArmyData();
            }
        }  else if (builder.hasSpyArmyData()) {
            // 清理SpyArmyData
            builder.clearSpyArmyData();
            fieldCnt++;
        }
        if (this.spyCityData != null) {
            StructPB.SpyCityDataPB.Builder tmpBuilder = StructPB.SpyCityDataPB.newBuilder();
            final int tmpFieldCnt = this.spyCityData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyCityData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyCityData();
            }
        }  else if (builder.hasSpyCityData()) {
            // 清理SpyCityData
            builder.clearSpyCityData();
            fieldCnt++;
        }
        if (this.spyRallyArmyData != null) {
            StructPB.SpyArmyDataPB.Builder tmpBuilder = StructPB.SpyArmyDataPB.newBuilder();
            final int tmpFieldCnt = this.spyRallyArmyData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyRallyArmyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyRallyArmyData();
            }
        }  else if (builder.hasSpyRallyArmyData()) {
            // 清理SpyRallyArmyData
            builder.clearSpyRallyArmyData();
            fieldCnt++;
        }
        if (this.SpyBuildingData != null) {
            StructPB.SpyBuildingDataPB.Builder tmpBuilder = StructPB.SpyBuildingDataPB.newBuilder();
            final int tmpFieldCnt = this.SpyBuildingData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyBuildingData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyBuildingData();
            }
        }  else if (builder.hasSpyBuildingData()) {
            // 清理SpyBuildingData
            builder.clearSpyBuildingData();
            fieldCnt++;
        }
        if (this.spyAssistArmyData != null) {
            StructPB.SpyArmyDataPB.Builder tmpBuilder = StructPB.SpyArmyDataPB.newBuilder();
            final int tmpFieldCnt = this.spyAssistArmyData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyAssistArmyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyAssistArmyData();
            }
        }  else if (builder.hasSpyAssistArmyData()) {
            // 清理SpyAssistArmyData
            builder.clearSpyAssistArmyData();
            fieldCnt++;
        }
        if (this.getTargetId() != 0L) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }  else if (builder.hasTargetId()) {
            // 清理TargetId
            builder.clearTargetId();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.spyTech != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.spyTech.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyTech(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyTech();
            }
        }  else if (builder.hasSpyTech()) {
            // 清理SpyTech
            builder.clearSpyTech();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SpyDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TARGETTYPE)) {
            builder.setTargetType(this.getTargetType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANBRIEFNAME)) {
            builder.setClanBriefName(this.getClanBriefName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYARMYDATA) && this.spyArmyData != null) {
            final boolean needClear = !builder.hasSpyArmyData();
            final int tmpFieldCnt = this.spyArmyData.copyChangeToCs(builder.getSpyArmyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyArmyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYCITYDATA) && this.spyCityData != null) {
            final boolean needClear = !builder.hasSpyCityData();
            final int tmpFieldCnt = this.spyCityData.copyChangeToCs(builder.getSpyCityDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyCityData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYRALLYARMYDATA) && this.spyRallyArmyData != null) {
            final boolean needClear = !builder.hasSpyRallyArmyData();
            final int tmpFieldCnt = this.spyRallyArmyData.copyChangeToCs(builder.getSpyRallyArmyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyRallyArmyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYBUILDINGDATA) && this.SpyBuildingData != null) {
            final boolean needClear = !builder.hasSpyBuildingData();
            final int tmpFieldCnt = this.SpyBuildingData.copyChangeToCs(builder.getSpyBuildingDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyBuildingData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYASSISTARMYDATA) && this.spyAssistArmyData != null) {
            final boolean needClear = !builder.hasSpyAssistArmyData();
            final int tmpFieldCnt = this.spyAssistArmyData.copyChangeToCs(builder.getSpyAssistArmyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyAssistArmyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYTECH) && this.spyTech != null) {
            final boolean needClear = !builder.hasSpyTech();
            final int tmpFieldCnt = this.spyTech.copyChangeToCs(builder.getSpyTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyTech();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SpyDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TARGETTYPE)) {
            builder.setTargetType(this.getTargetType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANBRIEFNAME)) {
            builder.setClanBriefName(this.getClanBriefName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToAndClearDeleteKeysCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYARMYDATA) && this.spyArmyData != null) {
            final boolean needClear = !builder.hasSpyArmyData();
            final int tmpFieldCnt = this.spyArmyData.copyChangeToAndClearDeleteKeysCs(builder.getSpyArmyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyArmyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYCITYDATA) && this.spyCityData != null) {
            final boolean needClear = !builder.hasSpyCityData();
            final int tmpFieldCnt = this.spyCityData.copyChangeToAndClearDeleteKeysCs(builder.getSpyCityDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyCityData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYRALLYARMYDATA) && this.spyRallyArmyData != null) {
            final boolean needClear = !builder.hasSpyRallyArmyData();
            final int tmpFieldCnt = this.spyRallyArmyData.copyChangeToAndClearDeleteKeysCs(builder.getSpyRallyArmyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyRallyArmyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYBUILDINGDATA) && this.SpyBuildingData != null) {
            final boolean needClear = !builder.hasSpyBuildingData();
            final int tmpFieldCnt = this.SpyBuildingData.copyChangeToAndClearDeleteKeysCs(builder.getSpyBuildingDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyBuildingData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYASSISTARMYDATA) && this.spyAssistArmyData != null) {
            final boolean needClear = !builder.hasSpyAssistArmyData();
            final int tmpFieldCnt = this.spyAssistArmyData.copyChangeToAndClearDeleteKeysCs(builder.getSpyAssistArmyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyAssistArmyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToAndClearDeleteKeysCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYTECH) && this.spyTech != null) {
            final boolean needClear = !builder.hasSpyTech();
            final int tmpFieldCnt = this.spyTech.copyChangeToCs(builder.getSpyTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyTech();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SpyDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTargetType()) {
            this.innerSetTargetType(proto.getTargetType());
        } else {
            this.innerSetTargetType(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanBriefName()) {
            this.innerSetClanBriefName(proto.getClanBriefName());
        } else {
            this.innerSetClanBriefName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromCs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromCs(proto.getPoint());
            }
        }
        if (proto.hasSpyArmyData()) {
            this.getSpyArmyData().mergeFromCs(proto.getSpyArmyData());
        } else {
            if (this.spyArmyData != null) {
                this.spyArmyData.mergeFromCs(proto.getSpyArmyData());
            }
        }
        if (proto.hasSpyCityData()) {
            this.getSpyCityData().mergeFromCs(proto.getSpyCityData());
        } else {
            if (this.spyCityData != null) {
                this.spyCityData.mergeFromCs(proto.getSpyCityData());
            }
        }
        if (proto.hasSpyRallyArmyData()) {
            this.getSpyRallyArmyData().mergeFromCs(proto.getSpyRallyArmyData());
        } else {
            if (this.spyRallyArmyData != null) {
                this.spyRallyArmyData.mergeFromCs(proto.getSpyRallyArmyData());
            }
        }
        if (proto.hasSpyBuildingData()) {
            this.getSpyBuildingData().mergeFromCs(proto.getSpyBuildingData());
        } else {
            if (this.SpyBuildingData != null) {
                this.SpyBuildingData.mergeFromCs(proto.getSpyBuildingData());
            }
        }
        if (proto.hasSpyAssistArmyData()) {
            this.getSpyAssistArmyData().mergeFromCs(proto.getSpyAssistArmyData());
        } else {
            if (this.spyAssistArmyData != null) {
                this.spyAssistArmyData.mergeFromCs(proto.getSpyAssistArmyData());
            }
        }
        if (proto.hasTargetId()) {
            this.innerSetTargetId(proto.getTargetId());
        } else {
            this.innerSetTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromCs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromCs(proto.getCardHead());
            }
        }
        if (proto.hasSpyTech()) {
            this.getSpyTech().mergeFromCs(proto.getSpyTech());
        } else {
            if (this.spyTech != null) {
                this.spyTech.mergeFromCs(proto.getSpyTech());
            }
        }
        this.markAll();
        return SpyDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SpyDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTargetType()) {
            this.setTargetType(proto.getTargetType());
            fieldCnt++;
        }
        if (proto.hasClanBriefName()) {
            this.setClanBriefName(proto.getClanBriefName());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromCs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasSpyArmyData()) {
            this.getSpyArmyData().mergeChangeFromCs(proto.getSpyArmyData());
            fieldCnt++;
        }
        if (proto.hasSpyCityData()) {
            this.getSpyCityData().mergeChangeFromCs(proto.getSpyCityData());
            fieldCnt++;
        }
        if (proto.hasSpyRallyArmyData()) {
            this.getSpyRallyArmyData().mergeChangeFromCs(proto.getSpyRallyArmyData());
            fieldCnt++;
        }
        if (proto.hasSpyBuildingData()) {
            this.getSpyBuildingData().mergeChangeFromCs(proto.getSpyBuildingData());
            fieldCnt++;
        }
        if (proto.hasSpyAssistArmyData()) {
            this.getSpyAssistArmyData().mergeChangeFromCs(proto.getSpyAssistArmyData());
            fieldCnt++;
        }
        if (proto.hasTargetId()) {
            this.setTargetId(proto.getTargetId());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromCs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasSpyTech()) {
            this.getSpyTech().mergeChangeFromCs(proto.getSpyTech());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SpyData.Builder getCopyDbBuilder() {
        final SpyData.Builder builder = SpyData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SpyData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getTargetType().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTargetType(this.getTargetType());
            fieldCnt++;
        }  else if (builder.hasTargetType()) {
            // 清理TargetType
            builder.clearTargetType();
            fieldCnt++;
        }
        if (!this.getClanBriefName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanBriefName(this.getClanBriefName());
            fieldCnt++;
        }  else if (builder.hasClanBriefName()) {
            // 清理ClanBriefName
            builder.clearClanBriefName();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.spyArmyData != null) {
            Struct.SpyArmyData.Builder tmpBuilder = Struct.SpyArmyData.newBuilder();
            final int tmpFieldCnt = this.spyArmyData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyArmyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyArmyData();
            }
        }  else if (builder.hasSpyArmyData()) {
            // 清理SpyArmyData
            builder.clearSpyArmyData();
            fieldCnt++;
        }
        if (this.spyCityData != null) {
            Struct.SpyCityData.Builder tmpBuilder = Struct.SpyCityData.newBuilder();
            final int tmpFieldCnt = this.spyCityData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyCityData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyCityData();
            }
        }  else if (builder.hasSpyCityData()) {
            // 清理SpyCityData
            builder.clearSpyCityData();
            fieldCnt++;
        }
        if (this.spyRallyArmyData != null) {
            Struct.SpyArmyData.Builder tmpBuilder = Struct.SpyArmyData.newBuilder();
            final int tmpFieldCnt = this.spyRallyArmyData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyRallyArmyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyRallyArmyData();
            }
        }  else if (builder.hasSpyRallyArmyData()) {
            // 清理SpyRallyArmyData
            builder.clearSpyRallyArmyData();
            fieldCnt++;
        }
        if (this.SpyBuildingData != null) {
            Struct.SpyBuildingData.Builder tmpBuilder = Struct.SpyBuildingData.newBuilder();
            final int tmpFieldCnt = this.SpyBuildingData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyBuildingData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyBuildingData();
            }
        }  else if (builder.hasSpyBuildingData()) {
            // 清理SpyBuildingData
            builder.clearSpyBuildingData();
            fieldCnt++;
        }
        if (this.spyAssistArmyData != null) {
            Struct.SpyArmyData.Builder tmpBuilder = Struct.SpyArmyData.newBuilder();
            final int tmpFieldCnt = this.spyAssistArmyData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyAssistArmyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyAssistArmyData();
            }
        }  else if (builder.hasSpyAssistArmyData()) {
            // 清理SpyAssistArmyData
            builder.clearSpyAssistArmyData();
            fieldCnt++;
        }
        if (this.getTargetId() != 0L) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }  else if (builder.hasTargetId()) {
            // 清理TargetId
            builder.clearTargetId();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.spyTech != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.spyTech.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyTech(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyTech();
            }
        }  else if (builder.hasSpyTech()) {
            // 清理SpyTech
            builder.clearSpyTech();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SpyData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TARGETTYPE)) {
            builder.setTargetType(this.getTargetType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANBRIEFNAME)) {
            builder.setClanBriefName(this.getClanBriefName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToDb(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYARMYDATA) && this.spyArmyData != null) {
            final boolean needClear = !builder.hasSpyArmyData();
            final int tmpFieldCnt = this.spyArmyData.copyChangeToDb(builder.getSpyArmyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyArmyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYCITYDATA) && this.spyCityData != null) {
            final boolean needClear = !builder.hasSpyCityData();
            final int tmpFieldCnt = this.spyCityData.copyChangeToDb(builder.getSpyCityDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyCityData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYRALLYARMYDATA) && this.spyRallyArmyData != null) {
            final boolean needClear = !builder.hasSpyRallyArmyData();
            final int tmpFieldCnt = this.spyRallyArmyData.copyChangeToDb(builder.getSpyRallyArmyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyRallyArmyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYBUILDINGDATA) && this.SpyBuildingData != null) {
            final boolean needClear = !builder.hasSpyBuildingData();
            final int tmpFieldCnt = this.SpyBuildingData.copyChangeToDb(builder.getSpyBuildingDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyBuildingData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYASSISTARMYDATA) && this.spyAssistArmyData != null) {
            final boolean needClear = !builder.hasSpyAssistArmyData();
            final int tmpFieldCnt = this.spyAssistArmyData.copyChangeToDb(builder.getSpyAssistArmyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyAssistArmyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToDb(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYTECH) && this.spyTech != null) {
            final boolean needClear = !builder.hasSpyTech();
            final int tmpFieldCnt = this.spyTech.copyChangeToDb(builder.getSpyTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyTech();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SpyData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTargetType()) {
            this.innerSetTargetType(proto.getTargetType());
        } else {
            this.innerSetTargetType(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanBriefName()) {
            this.innerSetClanBriefName(proto.getClanBriefName());
        } else {
            this.innerSetClanBriefName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromDb(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromDb(proto.getPoint());
            }
        }
        if (proto.hasSpyArmyData()) {
            this.getSpyArmyData().mergeFromDb(proto.getSpyArmyData());
        } else {
            if (this.spyArmyData != null) {
                this.spyArmyData.mergeFromDb(proto.getSpyArmyData());
            }
        }
        if (proto.hasSpyCityData()) {
            this.getSpyCityData().mergeFromDb(proto.getSpyCityData());
        } else {
            if (this.spyCityData != null) {
                this.spyCityData.mergeFromDb(proto.getSpyCityData());
            }
        }
        if (proto.hasSpyRallyArmyData()) {
            this.getSpyRallyArmyData().mergeFromDb(proto.getSpyRallyArmyData());
        } else {
            if (this.spyRallyArmyData != null) {
                this.spyRallyArmyData.mergeFromDb(proto.getSpyRallyArmyData());
            }
        }
        if (proto.hasSpyBuildingData()) {
            this.getSpyBuildingData().mergeFromDb(proto.getSpyBuildingData());
        } else {
            if (this.SpyBuildingData != null) {
                this.SpyBuildingData.mergeFromDb(proto.getSpyBuildingData());
            }
        }
        if (proto.hasSpyAssistArmyData()) {
            this.getSpyAssistArmyData().mergeFromDb(proto.getSpyAssistArmyData());
        } else {
            if (this.spyAssistArmyData != null) {
                this.spyAssistArmyData.mergeFromDb(proto.getSpyAssistArmyData());
            }
        }
        if (proto.hasTargetId()) {
            this.innerSetTargetId(proto.getTargetId());
        } else {
            this.innerSetTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromDb(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromDb(proto.getCardHead());
            }
        }
        if (proto.hasSpyTech()) {
            this.getSpyTech().mergeFromDb(proto.getSpyTech());
        } else {
            if (this.spyTech != null) {
                this.spyTech.mergeFromDb(proto.getSpyTech());
            }
        }
        this.markAll();
        return SpyDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SpyData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTargetType()) {
            this.setTargetType(proto.getTargetType());
            fieldCnt++;
        }
        if (proto.hasClanBriefName()) {
            this.setClanBriefName(proto.getClanBriefName());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromDb(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasSpyArmyData()) {
            this.getSpyArmyData().mergeChangeFromDb(proto.getSpyArmyData());
            fieldCnt++;
        }
        if (proto.hasSpyCityData()) {
            this.getSpyCityData().mergeChangeFromDb(proto.getSpyCityData());
            fieldCnt++;
        }
        if (proto.hasSpyRallyArmyData()) {
            this.getSpyRallyArmyData().mergeChangeFromDb(proto.getSpyRallyArmyData());
            fieldCnt++;
        }
        if (proto.hasSpyBuildingData()) {
            this.getSpyBuildingData().mergeChangeFromDb(proto.getSpyBuildingData());
            fieldCnt++;
        }
        if (proto.hasSpyAssistArmyData()) {
            this.getSpyAssistArmyData().mergeChangeFromDb(proto.getSpyAssistArmyData());
            fieldCnt++;
        }
        if (proto.hasTargetId()) {
            this.setTargetId(proto.getTargetId());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromDb(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasSpyTech()) {
            this.getSpyTech().mergeChangeFromDb(proto.getSpyTech());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SpyData.Builder getCopySsBuilder() {
        final SpyData.Builder builder = SpyData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SpyData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getTargetType().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTargetType(this.getTargetType());
            fieldCnt++;
        }  else if (builder.hasTargetType()) {
            // 清理TargetType
            builder.clearTargetType();
            fieldCnt++;
        }
        if (!this.getClanBriefName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanBriefName(this.getClanBriefName());
            fieldCnt++;
        }  else if (builder.hasClanBriefName()) {
            // 清理ClanBriefName
            builder.clearClanBriefName();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.spyArmyData != null) {
            Struct.SpyArmyData.Builder tmpBuilder = Struct.SpyArmyData.newBuilder();
            final int tmpFieldCnt = this.spyArmyData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyArmyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyArmyData();
            }
        }  else if (builder.hasSpyArmyData()) {
            // 清理SpyArmyData
            builder.clearSpyArmyData();
            fieldCnt++;
        }
        if (this.spyCityData != null) {
            Struct.SpyCityData.Builder tmpBuilder = Struct.SpyCityData.newBuilder();
            final int tmpFieldCnt = this.spyCityData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyCityData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyCityData();
            }
        }  else if (builder.hasSpyCityData()) {
            // 清理SpyCityData
            builder.clearSpyCityData();
            fieldCnt++;
        }
        if (this.spyRallyArmyData != null) {
            Struct.SpyArmyData.Builder tmpBuilder = Struct.SpyArmyData.newBuilder();
            final int tmpFieldCnt = this.spyRallyArmyData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyRallyArmyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyRallyArmyData();
            }
        }  else if (builder.hasSpyRallyArmyData()) {
            // 清理SpyRallyArmyData
            builder.clearSpyRallyArmyData();
            fieldCnt++;
        }
        if (this.SpyBuildingData != null) {
            Struct.SpyBuildingData.Builder tmpBuilder = Struct.SpyBuildingData.newBuilder();
            final int tmpFieldCnt = this.SpyBuildingData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyBuildingData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyBuildingData();
            }
        }  else if (builder.hasSpyBuildingData()) {
            // 清理SpyBuildingData
            builder.clearSpyBuildingData();
            fieldCnt++;
        }
        if (this.spyAssistArmyData != null) {
            Struct.SpyArmyData.Builder tmpBuilder = Struct.SpyArmyData.newBuilder();
            final int tmpFieldCnt = this.spyAssistArmyData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyAssistArmyData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyAssistArmyData();
            }
        }  else if (builder.hasSpyAssistArmyData()) {
            // 清理SpyAssistArmyData
            builder.clearSpyAssistArmyData();
            fieldCnt++;
        }
        if (this.getTargetId() != 0L) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }  else if (builder.hasTargetId()) {
            // 清理TargetId
            builder.clearTargetId();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.spyTech != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.spyTech.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpyTech(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpyTech();
            }
        }  else if (builder.hasSpyTech()) {
            // 清理SpyTech
            builder.clearSpyTech();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SpyData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TARGETTYPE)) {
            builder.setTargetType(this.getTargetType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANBRIEFNAME)) {
            builder.setClanBriefName(this.getClanBriefName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToSs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYARMYDATA) && this.spyArmyData != null) {
            final boolean needClear = !builder.hasSpyArmyData();
            final int tmpFieldCnt = this.spyArmyData.copyChangeToSs(builder.getSpyArmyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyArmyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYCITYDATA) && this.spyCityData != null) {
            final boolean needClear = !builder.hasSpyCityData();
            final int tmpFieldCnt = this.spyCityData.copyChangeToSs(builder.getSpyCityDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyCityData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYRALLYARMYDATA) && this.spyRallyArmyData != null) {
            final boolean needClear = !builder.hasSpyRallyArmyData();
            final int tmpFieldCnt = this.spyRallyArmyData.copyChangeToSs(builder.getSpyRallyArmyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyRallyArmyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYBUILDINGDATA) && this.SpyBuildingData != null) {
            final boolean needClear = !builder.hasSpyBuildingData();
            final int tmpFieldCnt = this.SpyBuildingData.copyChangeToSs(builder.getSpyBuildingDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyBuildingData();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYASSISTARMYDATA) && this.spyAssistArmyData != null) {
            final boolean needClear = !builder.hasSpyAssistArmyData();
            final int tmpFieldCnt = this.spyAssistArmyData.copyChangeToSs(builder.getSpyAssistArmyDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyAssistArmyData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_SPYTECH) && this.spyTech != null) {
            final boolean needClear = !builder.hasSpyTech();
            final int tmpFieldCnt = this.spyTech.copyChangeToSs(builder.getSpyTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpyTech();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SpyData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTargetType()) {
            this.innerSetTargetType(proto.getTargetType());
        } else {
            this.innerSetTargetType(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanBriefName()) {
            this.innerSetClanBriefName(proto.getClanBriefName());
        } else {
            this.innerSetClanBriefName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromSs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromSs(proto.getPoint());
            }
        }
        if (proto.hasSpyArmyData()) {
            this.getSpyArmyData().mergeFromSs(proto.getSpyArmyData());
        } else {
            if (this.spyArmyData != null) {
                this.spyArmyData.mergeFromSs(proto.getSpyArmyData());
            }
        }
        if (proto.hasSpyCityData()) {
            this.getSpyCityData().mergeFromSs(proto.getSpyCityData());
        } else {
            if (this.spyCityData != null) {
                this.spyCityData.mergeFromSs(proto.getSpyCityData());
            }
        }
        if (proto.hasSpyRallyArmyData()) {
            this.getSpyRallyArmyData().mergeFromSs(proto.getSpyRallyArmyData());
        } else {
            if (this.spyRallyArmyData != null) {
                this.spyRallyArmyData.mergeFromSs(proto.getSpyRallyArmyData());
            }
        }
        if (proto.hasSpyBuildingData()) {
            this.getSpyBuildingData().mergeFromSs(proto.getSpyBuildingData());
        } else {
            if (this.SpyBuildingData != null) {
                this.SpyBuildingData.mergeFromSs(proto.getSpyBuildingData());
            }
        }
        if (proto.hasSpyAssistArmyData()) {
            this.getSpyAssistArmyData().mergeFromSs(proto.getSpyAssistArmyData());
        } else {
            if (this.spyAssistArmyData != null) {
                this.spyAssistArmyData.mergeFromSs(proto.getSpyAssistArmyData());
            }
        }
        if (proto.hasTargetId()) {
            this.innerSetTargetId(proto.getTargetId());
        } else {
            this.innerSetTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        if (proto.hasSpyTech()) {
            this.getSpyTech().mergeFromSs(proto.getSpyTech());
        } else {
            if (this.spyTech != null) {
                this.spyTech.mergeFromSs(proto.getSpyTech());
            }
        }
        this.markAll();
        return SpyDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SpyData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTargetType()) {
            this.setTargetType(proto.getTargetType());
            fieldCnt++;
        }
        if (proto.hasClanBriefName()) {
            this.setClanBriefName(proto.getClanBriefName());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromSs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasSpyArmyData()) {
            this.getSpyArmyData().mergeChangeFromSs(proto.getSpyArmyData());
            fieldCnt++;
        }
        if (proto.hasSpyCityData()) {
            this.getSpyCityData().mergeChangeFromSs(proto.getSpyCityData());
            fieldCnt++;
        }
        if (proto.hasSpyRallyArmyData()) {
            this.getSpyRallyArmyData().mergeChangeFromSs(proto.getSpyRallyArmyData());
            fieldCnt++;
        }
        if (proto.hasSpyBuildingData()) {
            this.getSpyBuildingData().mergeChangeFromSs(proto.getSpyBuildingData());
            fieldCnt++;
        }
        if (proto.hasSpyAssistArmyData()) {
            this.getSpyAssistArmyData().mergeChangeFromSs(proto.getSpyAssistArmyData());
            fieldCnt++;
        }
        if (proto.hasTargetId()) {
            this.setTargetId(proto.getTargetId());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasSpyTech()) {
            this.getSpyTech().mergeChangeFromSs(proto.getSpyTech());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SpyData.Builder builder = SpyData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            this.point.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SPYARMYDATA) && this.spyArmyData != null) {
            this.spyArmyData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SPYCITYDATA) && this.spyCityData != null) {
            this.spyCityData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SPYRALLYARMYDATA) && this.spyRallyArmyData != null) {
            this.spyRallyArmyData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SPYBUILDINGDATA) && this.SpyBuildingData != null) {
            this.SpyBuildingData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SPYASSISTARMYDATA) && this.spyAssistArmyData != null) {
            this.spyAssistArmyData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SPYTECH) && this.spyTech != null) {
            this.spyTech.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.point != null) {
            this.point.markAll();
        }
        if (this.spyArmyData != null) {
            this.spyArmyData.markAll();
        }
        if (this.spyCityData != null) {
            this.spyCityData.markAll();
        }
        if (this.spyRallyArmyData != null) {
            this.spyRallyArmyData.markAll();
        }
        if (this.SpyBuildingData != null) {
            this.SpyBuildingData.markAll();
        }
        if (this.spyAssistArmyData != null) {
            this.spyAssistArmyData.markAll();
        }
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        if (this.spyTech != null) {
            this.spyTech.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SpyDataProp)) {
            return false;
        }
        final SpyDataProp otherNode = (SpyDataProp) node;
        if (!com.yorha.gemini.utils.StringUtils.equals(this.targetType, otherNode.targetType)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanBriefName, otherNode.clanBriefName)) {
            return false;
        }
        if (!this.getPoint().compareDataTo(otherNode.getPoint())) {
            return false;
        }
        if (!this.getSpyArmyData().compareDataTo(otherNode.getSpyArmyData())) {
            return false;
        }
        if (!this.getSpyCityData().compareDataTo(otherNode.getSpyCityData())) {
            return false;
        }
        if (!this.getSpyRallyArmyData().compareDataTo(otherNode.getSpyRallyArmyData())) {
            return false;
        }
        if (!this.getSpyBuildingData().compareDataTo(otherNode.getSpyBuildingData())) {
            return false;
        }
        if (!this.getSpyAssistArmyData().compareDataTo(otherNode.getSpyAssistArmyData())) {
            return false;
        }
        if (this.targetId != otherNode.targetId) {
            return false;
        }
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        if (!this.getSpyTech().compareDataTo(otherNode.getSpyTech())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 53;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
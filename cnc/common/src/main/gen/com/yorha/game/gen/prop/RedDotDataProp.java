package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.RedDotData;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.RedDotDataPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class RedDotDataProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_KEY = 0;
    public static final int FIELD_INDEX_CELLS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int key = Constant.DEFAULT_INT_VALUE;
    private Int64RedDotCellMapProp cells = null;

    public RedDotDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public RedDotDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get key
     *
     * @return key value
     */
    public int getKey() {
        return this.key;
    }

    /**
     * set key && set marked
     *
     * @param key new value
     * @return current object
     */
    public RedDotDataProp setKey(int key) {
        if (this.key != key) {
            this.mark(FIELD_INDEX_KEY);
            this.key = key;
        }
        return this;
    }

    /**
     * inner set key
     *
     * @param key new value
     */
    private void innerSetKey(int key) {
        this.key = key;
    }

    /**
     * get cells
     *
     * @return cells value
     */
    public Int64RedDotCellMapProp getCells() {
        if (this.cells == null) {
            this.cells = new Int64RedDotCellMapProp(this, FIELD_INDEX_CELLS);
        }
        return this.cells;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putCellsV(RedDotCellProp v) {
        this.getCells().put(v.getCellId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public RedDotCellProp addEmptyCells(Long k) {
        return this.getCells().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getCellsSize() {
        if (this.cells == null) {
            return 0;
        }
        return this.cells.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isCellsEmpty() {
        if (this.cells == null) {
            return true;
        }
        return this.cells.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public RedDotCellProp getCellsV(Long k) {
        if (this.cells == null || !this.cells.containsKey(k)) {
            return null;
        }
        return this.cells.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearCells() {
        if (this.cells != null) {
            this.cells.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeCellsV(Long k) {
        if (this.cells != null) {
            this.cells.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RedDotDataPB.Builder getCopyCsBuilder() {
        final RedDotDataPB.Builder builder = RedDotDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(RedDotDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getKey() != 0) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }  else if (builder.hasKey()) {
            // 清理Key
            builder.clearKey();
            fieldCnt++;
        }
        if (this.cells != null) {
            StructPB.Int64RedDotCellMapPB.Builder tmpBuilder = StructPB.Int64RedDotCellMapPB.newBuilder();
            final int tmpFieldCnt = this.cells.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCells(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCells();
            }
        }  else if (builder.hasCells()) {
            // 清理Cells
            builder.clearCells();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(RedDotDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KEY)) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CELLS) && this.cells != null) {
            final boolean needClear = !builder.hasCells();
            final int tmpFieldCnt = this.cells.copyChangeToCs(builder.getCellsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCells();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(RedDotDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KEY)) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CELLS) && this.cells != null) {
            final boolean needClear = !builder.hasCells();
            final int tmpFieldCnt = this.cells.copyChangeToAndClearDeleteKeysCs(builder.getCellsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCells();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(RedDotDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKey()) {
            this.innerSetKey(proto.getKey());
        } else {
            this.innerSetKey(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCells()) {
            this.getCells().mergeFromCs(proto.getCells());
        } else {
            if (this.cells != null) {
                this.cells.mergeFromCs(proto.getCells());
            }
        }
        this.markAll();
        return RedDotDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(RedDotDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKey()) {
            this.setKey(proto.getKey());
            fieldCnt++;
        }
        if (proto.hasCells()) {
            this.getCells().mergeChangeFromCs(proto.getCells());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RedDotData.Builder getCopyDbBuilder() {
        final RedDotData.Builder builder = RedDotData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(RedDotData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getKey() != 0) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }  else if (builder.hasKey()) {
            // 清理Key
            builder.clearKey();
            fieldCnt++;
        }
        if (this.cells != null) {
            Struct.Int64RedDotCellMap.Builder tmpBuilder = Struct.Int64RedDotCellMap.newBuilder();
            final int tmpFieldCnt = this.cells.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCells(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCells();
            }
        }  else if (builder.hasCells()) {
            // 清理Cells
            builder.clearCells();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(RedDotData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KEY)) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CELLS) && this.cells != null) {
            final boolean needClear = !builder.hasCells();
            final int tmpFieldCnt = this.cells.copyChangeToDb(builder.getCellsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCells();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(RedDotData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKey()) {
            this.innerSetKey(proto.getKey());
        } else {
            this.innerSetKey(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCells()) {
            this.getCells().mergeFromDb(proto.getCells());
        } else {
            if (this.cells != null) {
                this.cells.mergeFromDb(proto.getCells());
            }
        }
        this.markAll();
        return RedDotDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(RedDotData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKey()) {
            this.setKey(proto.getKey());
            fieldCnt++;
        }
        if (proto.hasCells()) {
            this.getCells().mergeChangeFromDb(proto.getCells());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RedDotData.Builder getCopySsBuilder() {
        final RedDotData.Builder builder = RedDotData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(RedDotData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getKey() != 0) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }  else if (builder.hasKey()) {
            // 清理Key
            builder.clearKey();
            fieldCnt++;
        }
        if (this.cells != null) {
            Struct.Int64RedDotCellMap.Builder tmpBuilder = Struct.Int64RedDotCellMap.newBuilder();
            final int tmpFieldCnt = this.cells.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCells(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCells();
            }
        }  else if (builder.hasCells()) {
            // 清理Cells
            builder.clearCells();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(RedDotData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KEY)) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CELLS) && this.cells != null) {
            final boolean needClear = !builder.hasCells();
            final int tmpFieldCnt = this.cells.copyChangeToSs(builder.getCellsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCells();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(RedDotData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKey()) {
            this.innerSetKey(proto.getKey());
        } else {
            this.innerSetKey(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCells()) {
            this.getCells().mergeFromSs(proto.getCells());
        } else {
            if (this.cells != null) {
                this.cells.mergeFromSs(proto.getCells());
            }
        }
        this.markAll();
        return RedDotDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(RedDotData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKey()) {
            this.setKey(proto.getKey());
            fieldCnt++;
        }
        if (proto.hasCells()) {
            this.getCells().mergeChangeFromSs(proto.getCells());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        RedDotData.Builder builder = RedDotData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CELLS) && this.cells != null) {
            this.cells.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.cells != null) {
            this.cells.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.key;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof RedDotDataProp)) {
            return false;
        }
        final RedDotDataProp otherNode = (RedDotDataProp) node;
        if (this.key != otherNode.key) {
            return false;
        }
        if (!this.getCells().compareDataTo(otherNode.getCells())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
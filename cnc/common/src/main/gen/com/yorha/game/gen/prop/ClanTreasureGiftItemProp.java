package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructClan.ClanTreasureGiftItem;
import com.yorha.proto.StructClanPB.ClanTreasureGiftItemPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanTreasureGiftItemProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TREASUREGIFTUNIQUEID = 0;
    public static final int FIELD_INDEX_TEMPLATEID = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private long treasureGiftUniqueId = Constant.DEFAULT_LONG_VALUE;
    private int templateId = Constant.DEFAULT_INT_VALUE;

    public ClanTreasureGiftItemProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanTreasureGiftItemProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get treasureGiftUniqueId
     *
     * @return treasureGiftUniqueId value
     */
    public long getTreasureGiftUniqueId() {
        return this.treasureGiftUniqueId;
    }

    /**
     * set treasureGiftUniqueId && set marked
     *
     * @param treasureGiftUniqueId new value
     * @return current object
     */
    public ClanTreasureGiftItemProp setTreasureGiftUniqueId(long treasureGiftUniqueId) {
        if (this.treasureGiftUniqueId != treasureGiftUniqueId) {
            this.mark(FIELD_INDEX_TREASUREGIFTUNIQUEID);
            this.treasureGiftUniqueId = treasureGiftUniqueId;
        }
        return this;
    }

    /**
     * inner set treasureGiftUniqueId
     *
     * @param treasureGiftUniqueId new value
     */
    private void innerSetTreasureGiftUniqueId(long treasureGiftUniqueId) {
        this.treasureGiftUniqueId = treasureGiftUniqueId;
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public ClanTreasureGiftItemProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanTreasureGiftItemPB.Builder getCopyCsBuilder() {
        final ClanTreasureGiftItemPB.Builder builder = ClanTreasureGiftItemPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanTreasureGiftItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTreasureGiftUniqueId() != 0L) {
            builder.setTreasureGiftUniqueId(this.getTreasureGiftUniqueId());
            fieldCnt++;
        }  else if (builder.hasTreasureGiftUniqueId()) {
            // 清理TreasureGiftUniqueId
            builder.clearTreasureGiftUniqueId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanTreasureGiftItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TREASUREGIFTUNIQUEID)) {
            builder.setTreasureGiftUniqueId(this.getTreasureGiftUniqueId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanTreasureGiftItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TREASUREGIFTUNIQUEID)) {
            builder.setTreasureGiftUniqueId(this.getTreasureGiftUniqueId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanTreasureGiftItemPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTreasureGiftUniqueId()) {
            this.innerSetTreasureGiftUniqueId(proto.getTreasureGiftUniqueId());
        } else {
            this.innerSetTreasureGiftUniqueId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanTreasureGiftItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanTreasureGiftItemPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTreasureGiftUniqueId()) {
            this.setTreasureGiftUniqueId(proto.getTreasureGiftUniqueId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanTreasureGiftItem.Builder getCopyDbBuilder() {
        final ClanTreasureGiftItem.Builder builder = ClanTreasureGiftItem.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanTreasureGiftItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTreasureGiftUniqueId() != 0L) {
            builder.setTreasureGiftUniqueId(this.getTreasureGiftUniqueId());
            fieldCnt++;
        }  else if (builder.hasTreasureGiftUniqueId()) {
            // 清理TreasureGiftUniqueId
            builder.clearTreasureGiftUniqueId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanTreasureGiftItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TREASUREGIFTUNIQUEID)) {
            builder.setTreasureGiftUniqueId(this.getTreasureGiftUniqueId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanTreasureGiftItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTreasureGiftUniqueId()) {
            this.innerSetTreasureGiftUniqueId(proto.getTreasureGiftUniqueId());
        } else {
            this.innerSetTreasureGiftUniqueId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanTreasureGiftItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanTreasureGiftItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTreasureGiftUniqueId()) {
            this.setTreasureGiftUniqueId(proto.getTreasureGiftUniqueId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanTreasureGiftItem.Builder getCopySsBuilder() {
        final ClanTreasureGiftItem.Builder builder = ClanTreasureGiftItem.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanTreasureGiftItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTreasureGiftUniqueId() != 0L) {
            builder.setTreasureGiftUniqueId(this.getTreasureGiftUniqueId());
            fieldCnt++;
        }  else if (builder.hasTreasureGiftUniqueId()) {
            // 清理TreasureGiftUniqueId
            builder.clearTreasureGiftUniqueId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanTreasureGiftItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TREASUREGIFTUNIQUEID)) {
            builder.setTreasureGiftUniqueId(this.getTreasureGiftUniqueId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanTreasureGiftItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTreasureGiftUniqueId()) {
            this.innerSetTreasureGiftUniqueId(proto.getTreasureGiftUniqueId());
        } else {
            this.innerSetTreasureGiftUniqueId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanTreasureGiftItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanTreasureGiftItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTreasureGiftUniqueId()) {
            this.setTreasureGiftUniqueId(proto.getTreasureGiftUniqueId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanTreasureGiftItem.Builder builder = ClanTreasureGiftItem.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanTreasureGiftItemProp)) {
            return false;
        }
        final ClanTreasureGiftItemProp otherNode = (ClanTreasureGiftItemProp) node;
        if (this.treasureGiftUniqueId != otherNode.treasureGiftUniqueId) {
            return false;
        }
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
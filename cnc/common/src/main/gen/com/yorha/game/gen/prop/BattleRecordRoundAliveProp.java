package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructBattle.BattleRecordRoundAlive;
import com.yorha.proto.StructBattlePB.BattleRecordRoundAlivePB;


/**
 * <AUTHOR> auto gen
 */
public class BattleRecordRoundAliveProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ROUND = 0;
    public static final int FIELD_INDEX_ALIVE = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int round = Constant.DEFAULT_INT_VALUE;
    private long alive = Constant.DEFAULT_LONG_VALUE;

    public BattleRecordRoundAliveProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BattleRecordRoundAliveProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get round
     *
     * @return round value
     */
    public int getRound() {
        return this.round;
    }

    /**
     * set round && set marked
     *
     * @param round new value
     * @return current object
     */
    public BattleRecordRoundAliveProp setRound(int round) {
        if (this.round != round) {
            this.mark(FIELD_INDEX_ROUND);
            this.round = round;
        }
        return this;
    }

    /**
     * inner set round
     *
     * @param round new value
     */
    private void innerSetRound(int round) {
        this.round = round;
    }

    /**
     * get alive
     *
     * @return alive value
     */
    public long getAlive() {
        return this.alive;
    }

    /**
     * set alive && set marked
     *
     * @param alive new value
     * @return current object
     */
    public BattleRecordRoundAliveProp setAlive(long alive) {
        if (this.alive != alive) {
            this.mark(FIELD_INDEX_ALIVE);
            this.alive = alive;
        }
        return this;
    }

    /**
     * inner set alive
     *
     * @param alive new value
     */
    private void innerSetAlive(long alive) {
        this.alive = alive;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRoundAlivePB.Builder getCopyCsBuilder() {
        final BattleRecordRoundAlivePB.Builder builder = BattleRecordRoundAlivePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BattleRecordRoundAlivePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRound() != 0) {
            builder.setRound(this.getRound());
            fieldCnt++;
        }  else if (builder.hasRound()) {
            // 清理Round
            builder.clearRound();
            fieldCnt++;
        }
        if (this.getAlive() != 0L) {
            builder.setAlive(this.getAlive());
            fieldCnt++;
        }  else if (builder.hasAlive()) {
            // 清理Alive
            builder.clearAlive();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BattleRecordRoundAlivePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ROUND)) {
            builder.setRound(this.getRound());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALIVE)) {
            builder.setAlive(this.getAlive());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BattleRecordRoundAlivePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ROUND)) {
            builder.setRound(this.getRound());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALIVE)) {
            builder.setAlive(this.getAlive());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BattleRecordRoundAlivePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRound()) {
            this.innerSetRound(proto.getRound());
        } else {
            this.innerSetRound(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAlive()) {
            this.innerSetAlive(proto.getAlive());
        } else {
            this.innerSetAlive(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return BattleRecordRoundAliveProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BattleRecordRoundAlivePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRound()) {
            this.setRound(proto.getRound());
            fieldCnt++;
        }
        if (proto.hasAlive()) {
            this.setAlive(proto.getAlive());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRoundAlive.Builder getCopyDbBuilder() {
        final BattleRecordRoundAlive.Builder builder = BattleRecordRoundAlive.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(BattleRecordRoundAlive.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRound() != 0) {
            builder.setRound(this.getRound());
            fieldCnt++;
        }  else if (builder.hasRound()) {
            // 清理Round
            builder.clearRound();
            fieldCnt++;
        }
        if (this.getAlive() != 0L) {
            builder.setAlive(this.getAlive());
            fieldCnt++;
        }  else if (builder.hasAlive()) {
            // 清理Alive
            builder.clearAlive();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(BattleRecordRoundAlive.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ROUND)) {
            builder.setRound(this.getRound());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALIVE)) {
            builder.setAlive(this.getAlive());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(BattleRecordRoundAlive proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRound()) {
            this.innerSetRound(proto.getRound());
        } else {
            this.innerSetRound(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAlive()) {
            this.innerSetAlive(proto.getAlive());
        } else {
            this.innerSetAlive(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return BattleRecordRoundAliveProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(BattleRecordRoundAlive proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRound()) {
            this.setRound(proto.getRound());
            fieldCnt++;
        }
        if (proto.hasAlive()) {
            this.setAlive(proto.getAlive());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRoundAlive.Builder getCopySsBuilder() {
        final BattleRecordRoundAlive.Builder builder = BattleRecordRoundAlive.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(BattleRecordRoundAlive.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRound() != 0) {
            builder.setRound(this.getRound());
            fieldCnt++;
        }  else if (builder.hasRound()) {
            // 清理Round
            builder.clearRound();
            fieldCnt++;
        }
        if (this.getAlive() != 0L) {
            builder.setAlive(this.getAlive());
            fieldCnt++;
        }  else if (builder.hasAlive()) {
            // 清理Alive
            builder.clearAlive();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(BattleRecordRoundAlive.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ROUND)) {
            builder.setRound(this.getRound());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALIVE)) {
            builder.setAlive(this.getAlive());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(BattleRecordRoundAlive proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRound()) {
            this.innerSetRound(proto.getRound());
        } else {
            this.innerSetRound(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAlive()) {
            this.innerSetAlive(proto.getAlive());
        } else {
            this.innerSetAlive(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return BattleRecordRoundAliveProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(BattleRecordRoundAlive proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRound()) {
            this.setRound(proto.getRound());
            fieldCnt++;
        }
        if (proto.hasAlive()) {
            this.setAlive(proto.getAlive());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        BattleRecordRoundAlive.Builder builder = BattleRecordRoundAlive.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BattleRecordRoundAliveProp)) {
            return false;
        }
        final BattleRecordRoundAliveProp otherNode = (BattleRecordRoundAliveProp) node;
        if (this.round != otherNode.round) {
            return false;
        }
        if (this.alive != otherNode.alive) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
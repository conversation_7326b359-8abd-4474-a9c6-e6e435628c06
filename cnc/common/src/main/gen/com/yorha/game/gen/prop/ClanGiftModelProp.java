package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Clan.ClanGiftModel;
import com.yorha.proto.StructClan;
import com.yorha.proto.ClanPB.ClanGiftModelPB;
import com.yorha.proto.StructClanPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanGiftModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_GIFTLEVEL = 0;
    public static final int FIELD_INDEX_CURGIFTPOINTS = 1;
    public static final int FIELD_INDEX_CURKEYNUM = 2;
    public static final int FIELD_INDEX_CURTREASURETEMPLATEID = 3;
    public static final int FIELD_INDEX_GIFTINDEX = 4;
    public static final int FIELD_INDEX_TREASUREGIFTINDEX = 5;
    public static final int FIELD_INDEX_CLANGIFTS = 6;

    public static final int FIELD_COUNT = 7;

    private long markBits0 = 0L;

    private int giftLevel = Constant.DEFAULT_INT_VALUE;
    private int curGiftPoints = Constant.DEFAULT_INT_VALUE;
    private int curKeyNum = Constant.DEFAULT_INT_VALUE;
    private int curTreasureTemplateId = Constant.DEFAULT_INT_VALUE;
    private long giftIndex = Constant.DEFAULT_LONG_VALUE;
    private long treasureGiftIndex = Constant.DEFAULT_LONG_VALUE;
    private ClanGiftItemListProp clanGifts = null;

    public ClanGiftModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanGiftModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get giftLevel
     *
     * @return giftLevel value
     */
    public int getGiftLevel() {
        return this.giftLevel;
    }

    /**
     * set giftLevel && set marked
     *
     * @param giftLevel new value
     * @return current object
     */
    public ClanGiftModelProp setGiftLevel(int giftLevel) {
        if (this.giftLevel != giftLevel) {
            this.mark(FIELD_INDEX_GIFTLEVEL);
            this.giftLevel = giftLevel;
        }
        return this;
    }

    /**
     * inner set giftLevel
     *
     * @param giftLevel new value
     */
    private void innerSetGiftLevel(int giftLevel) {
        this.giftLevel = giftLevel;
    }

    /**
     * get curGiftPoints
     *
     * @return curGiftPoints value
     */
    public int getCurGiftPoints() {
        return this.curGiftPoints;
    }

    /**
     * set curGiftPoints && set marked
     *
     * @param curGiftPoints new value
     * @return current object
     */
    public ClanGiftModelProp setCurGiftPoints(int curGiftPoints) {
        if (this.curGiftPoints != curGiftPoints) {
            this.mark(FIELD_INDEX_CURGIFTPOINTS);
            this.curGiftPoints = curGiftPoints;
        }
        return this;
    }

    /**
     * inner set curGiftPoints
     *
     * @param curGiftPoints new value
     */
    private void innerSetCurGiftPoints(int curGiftPoints) {
        this.curGiftPoints = curGiftPoints;
    }

    /**
     * get curKeyNum
     *
     * @return curKeyNum value
     */
    public int getCurKeyNum() {
        return this.curKeyNum;
    }

    /**
     * set curKeyNum && set marked
     *
     * @param curKeyNum new value
     * @return current object
     */
    public ClanGiftModelProp setCurKeyNum(int curKeyNum) {
        if (this.curKeyNum != curKeyNum) {
            this.mark(FIELD_INDEX_CURKEYNUM);
            this.curKeyNum = curKeyNum;
        }
        return this;
    }

    /**
     * inner set curKeyNum
     *
     * @param curKeyNum new value
     */
    private void innerSetCurKeyNum(int curKeyNum) {
        this.curKeyNum = curKeyNum;
    }

    /**
     * get curTreasureTemplateId
     *
     * @return curTreasureTemplateId value
     */
    public int getCurTreasureTemplateId() {
        return this.curTreasureTemplateId;
    }

    /**
     * set curTreasureTemplateId && set marked
     *
     * @param curTreasureTemplateId new value
     * @return current object
     */
    public ClanGiftModelProp setCurTreasureTemplateId(int curTreasureTemplateId) {
        if (this.curTreasureTemplateId != curTreasureTemplateId) {
            this.mark(FIELD_INDEX_CURTREASURETEMPLATEID);
            this.curTreasureTemplateId = curTreasureTemplateId;
        }
        return this;
    }

    /**
     * inner set curTreasureTemplateId
     *
     * @param curTreasureTemplateId new value
     */
    private void innerSetCurTreasureTemplateId(int curTreasureTemplateId) {
        this.curTreasureTemplateId = curTreasureTemplateId;
    }

    /**
     * get giftIndex
     *
     * @return giftIndex value
     */
    public long getGiftIndex() {
        return this.giftIndex;
    }

    /**
     * set giftIndex && set marked
     *
     * @param giftIndex new value
     * @return current object
     */
    public ClanGiftModelProp setGiftIndex(long giftIndex) {
        if (this.giftIndex != giftIndex) {
            this.mark(FIELD_INDEX_GIFTINDEX);
            this.giftIndex = giftIndex;
        }
        return this;
    }

    /**
     * inner set giftIndex
     *
     * @param giftIndex new value
     */
    private void innerSetGiftIndex(long giftIndex) {
        this.giftIndex = giftIndex;
    }

    /**
     * get treasureGiftIndex
     *
     * @return treasureGiftIndex value
     */
    public long getTreasureGiftIndex() {
        return this.treasureGiftIndex;
    }

    /**
     * set treasureGiftIndex && set marked
     *
     * @param treasureGiftIndex new value
     * @return current object
     */
    public ClanGiftModelProp setTreasureGiftIndex(long treasureGiftIndex) {
        if (this.treasureGiftIndex != treasureGiftIndex) {
            this.mark(FIELD_INDEX_TREASUREGIFTINDEX);
            this.treasureGiftIndex = treasureGiftIndex;
        }
        return this;
    }

    /**
     * inner set treasureGiftIndex
     *
     * @param treasureGiftIndex new value
     */
    private void innerSetTreasureGiftIndex(long treasureGiftIndex) {
        this.treasureGiftIndex = treasureGiftIndex;
    }

    /**
     * get clanGifts
     *
     * @return clanGifts value
     */
    public ClanGiftItemListProp getClanGifts() {
        if (this.clanGifts == null) {
            this.clanGifts = new ClanGiftItemListProp(this, FIELD_INDEX_CLANGIFTS);
        }
        return this.clanGifts;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addClanGifts(ClanGiftItemProp v) {
        this.getClanGifts().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ClanGiftItemProp getClanGiftsIndex(int index) {
        return this.getClanGifts().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public ClanGiftItemProp removeClanGifts(ClanGiftItemProp v) {
        if (this.clanGifts == null) {
            return null;
        }
        if(this.clanGifts.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getClanGiftsSize() {
        if (this.clanGifts == null) {
            return 0;
        }
        return this.clanGifts.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isClanGiftsEmpty() {
        if (this.clanGifts == null) {
            return true;
        }
        return this.getClanGifts().isEmpty();
    }

    /**
     * clear list
     */
    public void clearClanGifts() {
        this.getClanGifts().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ClanGiftItemProp removeClanGiftsIndex(int index) {
        return this.getClanGifts().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public ClanGiftItemProp setClanGiftsIndex(int index, ClanGiftItemProp v) {
        return this.getClanGifts().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanGiftModelPB.Builder getCopyCsBuilder() {
        final ClanGiftModelPB.Builder builder = ClanGiftModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanGiftModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGiftLevel() != 0) {
            builder.setGiftLevel(this.getGiftLevel());
            fieldCnt++;
        }  else if (builder.hasGiftLevel()) {
            // 清理GiftLevel
            builder.clearGiftLevel();
            fieldCnt++;
        }
        if (this.getCurGiftPoints() != 0) {
            builder.setCurGiftPoints(this.getCurGiftPoints());
            fieldCnt++;
        }  else if (builder.hasCurGiftPoints()) {
            // 清理CurGiftPoints
            builder.clearCurGiftPoints();
            fieldCnt++;
        }
        if (this.getCurKeyNum() != 0) {
            builder.setCurKeyNum(this.getCurKeyNum());
            fieldCnt++;
        }  else if (builder.hasCurKeyNum()) {
            // 清理CurKeyNum
            builder.clearCurKeyNum();
            fieldCnt++;
        }
        if (this.getCurTreasureTemplateId() != 0) {
            builder.setCurTreasureTemplateId(this.getCurTreasureTemplateId());
            fieldCnt++;
        }  else if (builder.hasCurTreasureTemplateId()) {
            // 清理CurTreasureTemplateId
            builder.clearCurTreasureTemplateId();
            fieldCnt++;
        }
        if (this.getGiftIndex() != 0L) {
            builder.setGiftIndex(this.getGiftIndex());
            fieldCnt++;
        }  else if (builder.hasGiftIndex()) {
            // 清理GiftIndex
            builder.clearGiftIndex();
            fieldCnt++;
        }
        if (this.getTreasureGiftIndex() != 0L) {
            builder.setTreasureGiftIndex(this.getTreasureGiftIndex());
            fieldCnt++;
        }  else if (builder.hasTreasureGiftIndex()) {
            // 清理TreasureGiftIndex
            builder.clearTreasureGiftIndex();
            fieldCnt++;
        }
        if (this.clanGifts != null) {
            StructClanPB.ClanGiftItemListPB.Builder tmpBuilder = StructClanPB.ClanGiftItemListPB.newBuilder();
            final int tmpFieldCnt = this.clanGifts.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanGifts(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanGifts();
            }
        }  else if (builder.hasClanGifts()) {
            // 清理ClanGifts
            builder.clearClanGifts();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanGiftModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GIFTLEVEL)) {
            builder.setGiftLevel(this.getGiftLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURGIFTPOINTS)) {
            builder.setCurGiftPoints(this.getCurGiftPoints());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURKEYNUM)) {
            builder.setCurKeyNum(this.getCurKeyNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURTREASURETEMPLATEID)) {
            builder.setCurTreasureTemplateId(this.getCurTreasureTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTINDEX)) {
            builder.setGiftIndex(this.getGiftIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TREASUREGIFTINDEX)) {
            builder.setTreasureGiftIndex(this.getTreasureGiftIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANGIFTS) && this.clanGifts != null) {
            final boolean needClear = !builder.hasClanGifts();
            final int tmpFieldCnt = this.clanGifts.copyChangeToCs(builder.getClanGiftsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanGifts();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanGiftModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GIFTLEVEL)) {
            builder.setGiftLevel(this.getGiftLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURGIFTPOINTS)) {
            builder.setCurGiftPoints(this.getCurGiftPoints());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURKEYNUM)) {
            builder.setCurKeyNum(this.getCurKeyNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURTREASURETEMPLATEID)) {
            builder.setCurTreasureTemplateId(this.getCurTreasureTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTINDEX)) {
            builder.setGiftIndex(this.getGiftIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TREASUREGIFTINDEX)) {
            builder.setTreasureGiftIndex(this.getTreasureGiftIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANGIFTS) && this.clanGifts != null) {
            final boolean needClear = !builder.hasClanGifts();
            final int tmpFieldCnt = this.clanGifts.copyChangeToCs(builder.getClanGiftsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanGifts();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanGiftModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGiftLevel()) {
            this.innerSetGiftLevel(proto.getGiftLevel());
        } else {
            this.innerSetGiftLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurGiftPoints()) {
            this.innerSetCurGiftPoints(proto.getCurGiftPoints());
        } else {
            this.innerSetCurGiftPoints(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurKeyNum()) {
            this.innerSetCurKeyNum(proto.getCurKeyNum());
        } else {
            this.innerSetCurKeyNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurTreasureTemplateId()) {
            this.innerSetCurTreasureTemplateId(proto.getCurTreasureTemplateId());
        } else {
            this.innerSetCurTreasureTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasGiftIndex()) {
            this.innerSetGiftIndex(proto.getGiftIndex());
        } else {
            this.innerSetGiftIndex(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTreasureGiftIndex()) {
            this.innerSetTreasureGiftIndex(proto.getTreasureGiftIndex());
        } else {
            this.innerSetTreasureGiftIndex(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanGifts()) {
            this.getClanGifts().mergeFromCs(proto.getClanGifts());
        } else {
            if (this.clanGifts != null) {
                this.clanGifts.mergeFromCs(proto.getClanGifts());
            }
        }
        this.markAll();
        return ClanGiftModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanGiftModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGiftLevel()) {
            this.setGiftLevel(proto.getGiftLevel());
            fieldCnt++;
        }
        if (proto.hasCurGiftPoints()) {
            this.setCurGiftPoints(proto.getCurGiftPoints());
            fieldCnt++;
        }
        if (proto.hasCurKeyNum()) {
            this.setCurKeyNum(proto.getCurKeyNum());
            fieldCnt++;
        }
        if (proto.hasCurTreasureTemplateId()) {
            this.setCurTreasureTemplateId(proto.getCurTreasureTemplateId());
            fieldCnt++;
        }
        if (proto.hasGiftIndex()) {
            this.setGiftIndex(proto.getGiftIndex());
            fieldCnt++;
        }
        if (proto.hasTreasureGiftIndex()) {
            this.setTreasureGiftIndex(proto.getTreasureGiftIndex());
            fieldCnt++;
        }
        if (proto.hasClanGifts()) {
            this.getClanGifts().mergeChangeFromCs(proto.getClanGifts());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanGiftModel.Builder getCopyDbBuilder() {
        final ClanGiftModel.Builder builder = ClanGiftModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanGiftModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGiftLevel() != 0) {
            builder.setGiftLevel(this.getGiftLevel());
            fieldCnt++;
        }  else if (builder.hasGiftLevel()) {
            // 清理GiftLevel
            builder.clearGiftLevel();
            fieldCnt++;
        }
        if (this.getCurGiftPoints() != 0) {
            builder.setCurGiftPoints(this.getCurGiftPoints());
            fieldCnt++;
        }  else if (builder.hasCurGiftPoints()) {
            // 清理CurGiftPoints
            builder.clearCurGiftPoints();
            fieldCnt++;
        }
        if (this.getCurKeyNum() != 0) {
            builder.setCurKeyNum(this.getCurKeyNum());
            fieldCnt++;
        }  else if (builder.hasCurKeyNum()) {
            // 清理CurKeyNum
            builder.clearCurKeyNum();
            fieldCnt++;
        }
        if (this.getCurTreasureTemplateId() != 0) {
            builder.setCurTreasureTemplateId(this.getCurTreasureTemplateId());
            fieldCnt++;
        }  else if (builder.hasCurTreasureTemplateId()) {
            // 清理CurTreasureTemplateId
            builder.clearCurTreasureTemplateId();
            fieldCnt++;
        }
        if (this.getGiftIndex() != 0L) {
            builder.setGiftIndex(this.getGiftIndex());
            fieldCnt++;
        }  else if (builder.hasGiftIndex()) {
            // 清理GiftIndex
            builder.clearGiftIndex();
            fieldCnt++;
        }
        if (this.getTreasureGiftIndex() != 0L) {
            builder.setTreasureGiftIndex(this.getTreasureGiftIndex());
            fieldCnt++;
        }  else if (builder.hasTreasureGiftIndex()) {
            // 清理TreasureGiftIndex
            builder.clearTreasureGiftIndex();
            fieldCnt++;
        }
        if (this.clanGifts != null) {
            StructClan.ClanGiftItemList.Builder tmpBuilder = StructClan.ClanGiftItemList.newBuilder();
            final int tmpFieldCnt = this.clanGifts.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanGifts(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanGifts();
            }
        }  else if (builder.hasClanGifts()) {
            // 清理ClanGifts
            builder.clearClanGifts();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanGiftModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GIFTLEVEL)) {
            builder.setGiftLevel(this.getGiftLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURGIFTPOINTS)) {
            builder.setCurGiftPoints(this.getCurGiftPoints());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURKEYNUM)) {
            builder.setCurKeyNum(this.getCurKeyNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURTREASURETEMPLATEID)) {
            builder.setCurTreasureTemplateId(this.getCurTreasureTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTINDEX)) {
            builder.setGiftIndex(this.getGiftIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TREASUREGIFTINDEX)) {
            builder.setTreasureGiftIndex(this.getTreasureGiftIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANGIFTS) && this.clanGifts != null) {
            final boolean needClear = !builder.hasClanGifts();
            final int tmpFieldCnt = this.clanGifts.copyChangeToDb(builder.getClanGiftsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanGifts();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanGiftModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGiftLevel()) {
            this.innerSetGiftLevel(proto.getGiftLevel());
        } else {
            this.innerSetGiftLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurGiftPoints()) {
            this.innerSetCurGiftPoints(proto.getCurGiftPoints());
        } else {
            this.innerSetCurGiftPoints(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurKeyNum()) {
            this.innerSetCurKeyNum(proto.getCurKeyNum());
        } else {
            this.innerSetCurKeyNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurTreasureTemplateId()) {
            this.innerSetCurTreasureTemplateId(proto.getCurTreasureTemplateId());
        } else {
            this.innerSetCurTreasureTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasGiftIndex()) {
            this.innerSetGiftIndex(proto.getGiftIndex());
        } else {
            this.innerSetGiftIndex(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTreasureGiftIndex()) {
            this.innerSetTreasureGiftIndex(proto.getTreasureGiftIndex());
        } else {
            this.innerSetTreasureGiftIndex(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanGifts()) {
            this.getClanGifts().mergeFromDb(proto.getClanGifts());
        } else {
            if (this.clanGifts != null) {
                this.clanGifts.mergeFromDb(proto.getClanGifts());
            }
        }
        this.markAll();
        return ClanGiftModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanGiftModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGiftLevel()) {
            this.setGiftLevel(proto.getGiftLevel());
            fieldCnt++;
        }
        if (proto.hasCurGiftPoints()) {
            this.setCurGiftPoints(proto.getCurGiftPoints());
            fieldCnt++;
        }
        if (proto.hasCurKeyNum()) {
            this.setCurKeyNum(proto.getCurKeyNum());
            fieldCnt++;
        }
        if (proto.hasCurTreasureTemplateId()) {
            this.setCurTreasureTemplateId(proto.getCurTreasureTemplateId());
            fieldCnt++;
        }
        if (proto.hasGiftIndex()) {
            this.setGiftIndex(proto.getGiftIndex());
            fieldCnt++;
        }
        if (proto.hasTreasureGiftIndex()) {
            this.setTreasureGiftIndex(proto.getTreasureGiftIndex());
            fieldCnt++;
        }
        if (proto.hasClanGifts()) {
            this.getClanGifts().mergeChangeFromDb(proto.getClanGifts());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanGiftModel.Builder getCopySsBuilder() {
        final ClanGiftModel.Builder builder = ClanGiftModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanGiftModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGiftLevel() != 0) {
            builder.setGiftLevel(this.getGiftLevel());
            fieldCnt++;
        }  else if (builder.hasGiftLevel()) {
            // 清理GiftLevel
            builder.clearGiftLevel();
            fieldCnt++;
        }
        if (this.getCurGiftPoints() != 0) {
            builder.setCurGiftPoints(this.getCurGiftPoints());
            fieldCnt++;
        }  else if (builder.hasCurGiftPoints()) {
            // 清理CurGiftPoints
            builder.clearCurGiftPoints();
            fieldCnt++;
        }
        if (this.getCurKeyNum() != 0) {
            builder.setCurKeyNum(this.getCurKeyNum());
            fieldCnt++;
        }  else if (builder.hasCurKeyNum()) {
            // 清理CurKeyNum
            builder.clearCurKeyNum();
            fieldCnt++;
        }
        if (this.getCurTreasureTemplateId() != 0) {
            builder.setCurTreasureTemplateId(this.getCurTreasureTemplateId());
            fieldCnt++;
        }  else if (builder.hasCurTreasureTemplateId()) {
            // 清理CurTreasureTemplateId
            builder.clearCurTreasureTemplateId();
            fieldCnt++;
        }
        if (this.getGiftIndex() != 0L) {
            builder.setGiftIndex(this.getGiftIndex());
            fieldCnt++;
        }  else if (builder.hasGiftIndex()) {
            // 清理GiftIndex
            builder.clearGiftIndex();
            fieldCnt++;
        }
        if (this.getTreasureGiftIndex() != 0L) {
            builder.setTreasureGiftIndex(this.getTreasureGiftIndex());
            fieldCnt++;
        }  else if (builder.hasTreasureGiftIndex()) {
            // 清理TreasureGiftIndex
            builder.clearTreasureGiftIndex();
            fieldCnt++;
        }
        if (this.clanGifts != null) {
            StructClan.ClanGiftItemList.Builder tmpBuilder = StructClan.ClanGiftItemList.newBuilder();
            final int tmpFieldCnt = this.clanGifts.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanGifts(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanGifts();
            }
        }  else if (builder.hasClanGifts()) {
            // 清理ClanGifts
            builder.clearClanGifts();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanGiftModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GIFTLEVEL)) {
            builder.setGiftLevel(this.getGiftLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURGIFTPOINTS)) {
            builder.setCurGiftPoints(this.getCurGiftPoints());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURKEYNUM)) {
            builder.setCurKeyNum(this.getCurKeyNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURTREASURETEMPLATEID)) {
            builder.setCurTreasureTemplateId(this.getCurTreasureTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GIFTINDEX)) {
            builder.setGiftIndex(this.getGiftIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TREASUREGIFTINDEX)) {
            builder.setTreasureGiftIndex(this.getTreasureGiftIndex());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANGIFTS) && this.clanGifts != null) {
            final boolean needClear = !builder.hasClanGifts();
            final int tmpFieldCnt = this.clanGifts.copyChangeToSs(builder.getClanGiftsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanGifts();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanGiftModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGiftLevel()) {
            this.innerSetGiftLevel(proto.getGiftLevel());
        } else {
            this.innerSetGiftLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurGiftPoints()) {
            this.innerSetCurGiftPoints(proto.getCurGiftPoints());
        } else {
            this.innerSetCurGiftPoints(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurKeyNum()) {
            this.innerSetCurKeyNum(proto.getCurKeyNum());
        } else {
            this.innerSetCurKeyNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurTreasureTemplateId()) {
            this.innerSetCurTreasureTemplateId(proto.getCurTreasureTemplateId());
        } else {
            this.innerSetCurTreasureTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasGiftIndex()) {
            this.innerSetGiftIndex(proto.getGiftIndex());
        } else {
            this.innerSetGiftIndex(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTreasureGiftIndex()) {
            this.innerSetTreasureGiftIndex(proto.getTreasureGiftIndex());
        } else {
            this.innerSetTreasureGiftIndex(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanGifts()) {
            this.getClanGifts().mergeFromSs(proto.getClanGifts());
        } else {
            if (this.clanGifts != null) {
                this.clanGifts.mergeFromSs(proto.getClanGifts());
            }
        }
        this.markAll();
        return ClanGiftModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanGiftModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGiftLevel()) {
            this.setGiftLevel(proto.getGiftLevel());
            fieldCnt++;
        }
        if (proto.hasCurGiftPoints()) {
            this.setCurGiftPoints(proto.getCurGiftPoints());
            fieldCnt++;
        }
        if (proto.hasCurKeyNum()) {
            this.setCurKeyNum(proto.getCurKeyNum());
            fieldCnt++;
        }
        if (proto.hasCurTreasureTemplateId()) {
            this.setCurTreasureTemplateId(proto.getCurTreasureTemplateId());
            fieldCnt++;
        }
        if (proto.hasGiftIndex()) {
            this.setGiftIndex(proto.getGiftIndex());
            fieldCnt++;
        }
        if (proto.hasTreasureGiftIndex()) {
            this.setTreasureGiftIndex(proto.getTreasureGiftIndex());
            fieldCnt++;
        }
        if (proto.hasClanGifts()) {
            this.getClanGifts().mergeChangeFromSs(proto.getClanGifts());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanGiftModel.Builder builder = ClanGiftModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CLANGIFTS) && this.clanGifts != null) {
            this.clanGifts.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.clanGifts != null) {
            this.clanGifts.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanGiftModelProp)) {
            return false;
        }
        final ClanGiftModelProp otherNode = (ClanGiftModelProp) node;
        if (this.giftLevel != otherNode.giftLevel) {
            return false;
        }
        if (this.curGiftPoints != otherNode.curGiftPoints) {
            return false;
        }
        if (this.curKeyNum != otherNode.curKeyNum) {
            return false;
        }
        if (this.curTreasureTemplateId != otherNode.curTreasureTemplateId) {
            return false;
        }
        if (this.giftIndex != otherNode.giftIndex) {
            return false;
        }
        if (this.treasureGiftIndex != otherNode.treasureGiftIndex) {
            return false;
        }
        if (!this.getClanGifts().compareDataTo(otherNode.getClanGifts())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 57;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
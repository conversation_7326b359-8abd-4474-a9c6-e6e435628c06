package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.ClanResBuilding.ClanResBuildingEntity;
import com.yorha.proto.Struct;
import com.yorha.proto.StructClan;
import com.yorha.proto.StructCommon;
import com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructClanPB;
import com.yorha.proto.StructCommonPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanResBuildingProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ENTITYID = 0;
    public static final int FIELD_INDEX_TEMPLATEID = 1;
    public static final int FIELD_INDEX_STATE = 2;
    public static final int FIELD_INDEX_POINT = 3;
    public static final int FIELD_INDEX_CLANID = 4;
    public static final int FIELD_INDEX_SHOWCLANSIMPLENAME = 5;
    public static final int FIELD_INDEX_SHOWCLANNAME = 6;
    public static final int FIELD_INDEX_DISAPPEARTSMS = 7;
    public static final int FIELD_INDEX_INNERARMY = 8;
    public static final int FIELD_INDEX_PROGRESSINFO = 9;
    public static final int FIELD_INDEX_FLAGINFO = 10;
    public static final int FIELD_INDEX_CURRENTCOLLECTNUM = 11;
    public static final int FIELD_INDEX_EACHPLAYERCOLLECTMAP = 12;
    public static final int FIELD_INDEX_ISCOLLECTING = 13;
    public static final int FIELD_INDEX_ZONEID = 14;

    public static final int FIELD_COUNT = 15;

    private long markBits0 = 0L;
    private PropertyChangeListener listener;

    private long entityId = Constant.DEFAULT_LONG_VALUE;
    private int templateId = Constant.DEFAULT_INT_VALUE;
    private ClanResBuildingStage state = ClanResBuildingStage.forNumber(0);
    private PointProp point = null;
    private long clanId = Constant.DEFAULT_LONG_VALUE;
    private String showClanSimpleName = Constant.DEFAULT_STR_VALUE;
    private String showClanName = Constant.DEFAULT_STR_VALUE;
    private long disappearTsMs = Constant.DEFAULT_LONG_VALUE;
    private CityInnerArmyProp innerArmy = null;
    private ProgressInfoProp progressInfo = null;
    private ClanFlagInfoProp flagInfo = null;
    private int currentCollectNum = Constant.DEFAULT_INT_VALUE;
    private Int64ProgressInfoMapProp eachPlayerCollectMap = null;
    private boolean isCollecting = Constant.DEFAULT_BOOLEAN_VALUE;
    private int zoneId = Constant.DEFAULT_INT_VALUE;

    public ClanResBuildingProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanResBuildingProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get entityId
     *
     * @return entityId value
     */
    public long getEntityId() {
        return this.entityId;
    }

    /**
     * set entityId && set marked
     *
     * @param entityId new value
     * @return current object
     */
    public ClanResBuildingProp setEntityId(long entityId) {
        if (this.entityId != entityId) {
            this.mark(FIELD_INDEX_ENTITYID);
            this.entityId = entityId;
        }
        return this;
    }

    /**
     * inner set entityId
     *
     * @param entityId new value
     */
    private void innerSetEntityId(long entityId) {
        this.entityId = entityId;
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public ClanResBuildingProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }

    /**
     * get state
     *
     * @return state value
     */
    public ClanResBuildingStage getState() {
        return this.state;
    }

    /**
     * set state && set marked
     *
     * @param state new value
     * @return current object
     */
    public ClanResBuildingProp setState(ClanResBuildingStage state) {
        if (state == null) {
            throw new NullPointerException();
        }
        if (this.state != state) {
            this.mark(FIELD_INDEX_STATE);
            this.state = state;
        }
        return this;
    }

    /**
     * inner set state
     *
     * @param state new value
     */
    private void innerSetState(ClanResBuildingStage state) {
        this.state = state;
    }

    /**
     * get point
     *
     * @return point value
     */
    public PointProp getPoint() {
        if (this.point == null) {
            this.point = new PointProp(this, FIELD_INDEX_POINT);
        }
        return this.point;
    }

    /**
     * get clanId
     *
     * @return clanId value
     */
    public long getClanId() {
        return this.clanId;
    }

    /**
     * set clanId && set marked
     *
     * @param clanId new value
     * @return current object
     */
    public ClanResBuildingProp setClanId(long clanId) {
        if (this.clanId != clanId) {
            this.mark(FIELD_INDEX_CLANID);
            this.clanId = clanId;
        }
        return this;
    }

    /**
     * inner set clanId
     *
     * @param clanId new value
     */
    private void innerSetClanId(long clanId) {
        this.clanId = clanId;
    }

    /**
     * get showClanSimpleName
     *
     * @return showClanSimpleName value
     */
    public String getShowClanSimpleName() {
        return this.showClanSimpleName;
    }

    /**
     * set showClanSimpleName && set marked
     *
     * @param showClanSimpleName new value
     * @return current object
     */
    public ClanResBuildingProp setShowClanSimpleName(String showClanSimpleName) {
        if (showClanSimpleName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.showClanSimpleName, showClanSimpleName)) {
            this.mark(FIELD_INDEX_SHOWCLANSIMPLENAME);
            this.showClanSimpleName = showClanSimpleName;
        }
        return this;
    }

    /**
     * inner set showClanSimpleName
     *
     * @param showClanSimpleName new value
     */
    private void innerSetShowClanSimpleName(String showClanSimpleName) {
        this.showClanSimpleName = showClanSimpleName;
    }

    /**
     * get showClanName
     *
     * @return showClanName value
     */
    public String getShowClanName() {
        return this.showClanName;
    }

    /**
     * set showClanName && set marked
     *
     * @param showClanName new value
     * @return current object
     */
    public ClanResBuildingProp setShowClanName(String showClanName) {
        if (showClanName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.showClanName, showClanName)) {
            this.mark(FIELD_INDEX_SHOWCLANNAME);
            this.showClanName = showClanName;
        }
        return this;
    }

    /**
     * inner set showClanName
     *
     * @param showClanName new value
     */
    private void innerSetShowClanName(String showClanName) {
        this.showClanName = showClanName;
    }

    /**
     * get disappearTsMs
     *
     * @return disappearTsMs value
     */
    public long getDisappearTsMs() {
        return this.disappearTsMs;
    }

    /**
     * set disappearTsMs && set marked
     *
     * @param disappearTsMs new value
     * @return current object
     */
    public ClanResBuildingProp setDisappearTsMs(long disappearTsMs) {
        if (this.disappearTsMs != disappearTsMs) {
            this.mark(FIELD_INDEX_DISAPPEARTSMS);
            this.disappearTsMs = disappearTsMs;
        }
        return this;
    }

    /**
     * inner set disappearTsMs
     *
     * @param disappearTsMs new value
     */
    private void innerSetDisappearTsMs(long disappearTsMs) {
        this.disappearTsMs = disappearTsMs;
    }

    /**
     * get innerArmy
     *
     * @return innerArmy value
     */
    public CityInnerArmyProp getInnerArmy() {
        if (this.innerArmy == null) {
            this.innerArmy = new CityInnerArmyProp(this, FIELD_INDEX_INNERARMY);
        }
        return this.innerArmy;
    }

    /**
     * get progressInfo
     *
     * @return progressInfo value
     */
    public ProgressInfoProp getProgressInfo() {
        if (this.progressInfo == null) {
            this.progressInfo = new ProgressInfoProp(this, FIELD_INDEX_PROGRESSINFO);
        }
        return this.progressInfo;
    }

    /**
     * get flagInfo
     *
     * @return flagInfo value
     */
    public ClanFlagInfoProp getFlagInfo() {
        if (this.flagInfo == null) {
            this.flagInfo = new ClanFlagInfoProp(this, FIELD_INDEX_FLAGINFO);
        }
        return this.flagInfo;
    }

    /**
     * get currentCollectNum
     *
     * @return currentCollectNum value
     */
    public int getCurrentCollectNum() {
        return this.currentCollectNum;
    }

    /**
     * set currentCollectNum && set marked
     *
     * @param currentCollectNum new value
     * @return current object
     */
    public ClanResBuildingProp setCurrentCollectNum(int currentCollectNum) {
        if (this.currentCollectNum != currentCollectNum) {
            this.mark(FIELD_INDEX_CURRENTCOLLECTNUM);
            this.currentCollectNum = currentCollectNum;
        }
        return this;
    }

    /**
     * inner set currentCollectNum
     *
     * @param currentCollectNum new value
     */
    private void innerSetCurrentCollectNum(int currentCollectNum) {
        this.currentCollectNum = currentCollectNum;
    }

    /**
     * get eachPlayerCollectMap
     *
     * @return eachPlayerCollectMap value
     */
    public Int64ProgressInfoMapProp getEachPlayerCollectMap() {
        if (this.eachPlayerCollectMap == null) {
            this.eachPlayerCollectMap = new Int64ProgressInfoMapProp(this, FIELD_INDEX_EACHPLAYERCOLLECTMAP);
        }
        return this.eachPlayerCollectMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putEachPlayerCollectMapV(ProgressInfoProp v) {
        this.getEachPlayerCollectMap().put(v.getUid(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ProgressInfoProp addEmptyEachPlayerCollectMap(Long k) {
        return this.getEachPlayerCollectMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getEachPlayerCollectMapSize() {
        if (this.eachPlayerCollectMap == null) {
            return 0;
        }
        return this.eachPlayerCollectMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isEachPlayerCollectMapEmpty() {
        if (this.eachPlayerCollectMap == null) {
            return true;
        }
        return this.eachPlayerCollectMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ProgressInfoProp getEachPlayerCollectMapV(Long k) {
        if (this.eachPlayerCollectMap == null || !this.eachPlayerCollectMap.containsKey(k)) {
            return null;
        }
        return this.eachPlayerCollectMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearEachPlayerCollectMap() {
        if (this.eachPlayerCollectMap != null) {
            this.eachPlayerCollectMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeEachPlayerCollectMapV(Long k) {
        if (this.eachPlayerCollectMap != null) {
            this.eachPlayerCollectMap.remove(k);
        }
    }
    /**
     * get isCollecting
     *
     * @return isCollecting value
     */
    public boolean getIsCollecting() {
        return this.isCollecting;
    }

    /**
     * set isCollecting && set marked
     *
     * @param isCollecting new value
     * @return current object
     */
    public ClanResBuildingProp setIsCollecting(boolean isCollecting) {
        if (this.isCollecting != isCollecting) {
            this.mark(FIELD_INDEX_ISCOLLECTING);
            this.isCollecting = isCollecting;
        }
        return this;
    }

    /**
     * inner set isCollecting
     *
     * @param isCollecting new value
     */
    private void innerSetIsCollecting(boolean isCollecting) {
        this.isCollecting = isCollecting;
    }

    /**
     * get zoneId
     *
     * @return zoneId value
     */
    public int getZoneId() {
        return this.zoneId;
    }

    /**
     * set zoneId && set marked
     *
     * @param zoneId new value
     * @return current object
     */
    public ClanResBuildingProp setZoneId(int zoneId) {
        if (this.zoneId != zoneId) {
            this.mark(FIELD_INDEX_ZONEID);
            this.zoneId = zoneId;
        }
        return this;
    }

    /**
     * inner set zoneId
     *
     * @param zoneId new value
     */
    private void innerSetZoneId(int zoneId) {
        this.zoneId = zoneId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanResBuildingEntityPB.Builder getCopyCsBuilder() {
        final ClanResBuildingEntityPB.Builder builder = ClanResBuildingEntityPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanResBuildingEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getEntityId() != 0L) {
            builder.setEntityId(this.getEntityId());
            fieldCnt++;
        }  else if (builder.hasEntityId()) {
            // 清理EntityId
            builder.clearEntityId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getState() != ClanResBuildingStage.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.point != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.point.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getShowClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setShowClanSimpleName(this.getShowClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasShowClanSimpleName()) {
            // 清理ShowClanSimpleName
            builder.clearShowClanSimpleName();
            fieldCnt++;
        }
        if (!this.getShowClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setShowClanName(this.getShowClanName());
            fieldCnt++;
        }  else if (builder.hasShowClanName()) {
            // 清理ShowClanName
            builder.clearShowClanName();
            fieldCnt++;
        }
        if (this.progressInfo != null) {
            StructCommonPB.ProgressInfoPB.Builder tmpBuilder = StructCommonPB.ProgressInfoPB.newBuilder();
            final int tmpFieldCnt = this.progressInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setProgressInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearProgressInfo();
            }
        }  else if (builder.hasProgressInfo()) {
            // 清理ProgressInfo
            builder.clearProgressInfo();
            fieldCnt++;
        }
        if (this.flagInfo != null) {
            StructClanPB.ClanFlagInfoPB.Builder tmpBuilder = StructClanPB.ClanFlagInfoPB.newBuilder();
            final int tmpFieldCnt = this.flagInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFlagInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFlagInfo();
            }
        }  else if (builder.hasFlagInfo()) {
            // 清理FlagInfo
            builder.clearFlagInfo();
            fieldCnt++;
        }
        if (this.getCurrentCollectNum() != 0) {
            builder.setCurrentCollectNum(this.getCurrentCollectNum());
            fieldCnt++;
        }  else if (builder.hasCurrentCollectNum()) {
            // 清理CurrentCollectNum
            builder.clearCurrentCollectNum();
            fieldCnt++;
        }
        if (this.getIsCollecting()) {
            builder.setIsCollecting(this.getIsCollecting());
            fieldCnt++;
        }  else if (builder.hasIsCollecting()) {
            // 清理IsCollecting
            builder.clearIsCollecting();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanResBuildingEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ENTITYID)) {
            builder.setEntityId(this.getEntityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCLANSIMPLENAME)) {
            builder.setShowClanSimpleName(this.getShowClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCLANNAME)) {
            builder.setShowClanName(this.getShowClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PROGRESSINFO) && this.progressInfo != null) {
            final boolean needClear = !builder.hasProgressInfo();
            final int tmpFieldCnt = this.progressInfo.copyChangeToCs(builder.getProgressInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearProgressInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_FLAGINFO) && this.flagInfo != null) {
            final boolean needClear = !builder.hasFlagInfo();
            final int tmpFieldCnt = this.flagInfo.copyChangeToCs(builder.getFlagInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFlagInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_CURRENTCOLLECTNUM)) {
            builder.setCurrentCollectNum(this.getCurrentCollectNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISCOLLECTING)) {
            builder.setIsCollecting(this.getIsCollecting());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanResBuildingEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ENTITYID)) {
            builder.setEntityId(this.getEntityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToAndClearDeleteKeysCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCLANSIMPLENAME)) {
            builder.setShowClanSimpleName(this.getShowClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCLANNAME)) {
            builder.setShowClanName(this.getShowClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PROGRESSINFO) && this.progressInfo != null) {
            final boolean needClear = !builder.hasProgressInfo();
            final int tmpFieldCnt = this.progressInfo.copyChangeToAndClearDeleteKeysCs(builder.getProgressInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearProgressInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_FLAGINFO) && this.flagInfo != null) {
            final boolean needClear = !builder.hasFlagInfo();
            final int tmpFieldCnt = this.flagInfo.copyChangeToAndClearDeleteKeysCs(builder.getFlagInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFlagInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_CURRENTCOLLECTNUM)) {
            builder.setCurrentCollectNum(this.getCurrentCollectNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISCOLLECTING)) {
            builder.setIsCollecting(this.getIsCollecting());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanResBuildingEntityPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasEntityId()) {
            this.innerSetEntityId(proto.getEntityId());
        } else {
            this.innerSetEntityId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(ClanResBuildingStage.forNumber(0));
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromCs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromCs(proto.getPoint());
            }
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasShowClanSimpleName()) {
            this.innerSetShowClanSimpleName(proto.getShowClanSimpleName());
        } else {
            this.innerSetShowClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasShowClanName()) {
            this.innerSetShowClanName(proto.getShowClanName());
        } else {
            this.innerSetShowClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasProgressInfo()) {
            this.getProgressInfo().mergeFromCs(proto.getProgressInfo());
        } else {
            if (this.progressInfo != null) {
                this.progressInfo.mergeFromCs(proto.getProgressInfo());
            }
        }
        if (proto.hasFlagInfo()) {
            this.getFlagInfo().mergeFromCs(proto.getFlagInfo());
        } else {
            if (this.flagInfo != null) {
                this.flagInfo.mergeFromCs(proto.getFlagInfo());
            }
        }
        if (proto.hasCurrentCollectNum()) {
            this.innerSetCurrentCollectNum(proto.getCurrentCollectNum());
        } else {
            this.innerSetCurrentCollectNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIsCollecting()) {
            this.innerSetIsCollecting(proto.getIsCollecting());
        } else {
            this.innerSetIsCollecting(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanResBuildingProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanResBuildingEntityPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasEntityId()) {
            this.setEntityId(proto.getEntityId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromCs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasShowClanSimpleName()) {
            this.setShowClanSimpleName(proto.getShowClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasShowClanName()) {
            this.setShowClanName(proto.getShowClanName());
            fieldCnt++;
        }
        if (proto.hasProgressInfo()) {
            this.getProgressInfo().mergeChangeFromCs(proto.getProgressInfo());
            fieldCnt++;
        }
        if (proto.hasFlagInfo()) {
            this.getFlagInfo().mergeChangeFromCs(proto.getFlagInfo());
            fieldCnt++;
        }
        if (proto.hasCurrentCollectNum()) {
            this.setCurrentCollectNum(proto.getCurrentCollectNum());
            fieldCnt++;
        }
        if (proto.hasIsCollecting()) {
            this.setIsCollecting(proto.getIsCollecting());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanResBuildingEntity.Builder getCopyDbBuilder() {
        final ClanResBuildingEntity.Builder builder = ClanResBuildingEntity.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanResBuildingEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getEntityId() != 0L) {
            builder.setEntityId(this.getEntityId());
            fieldCnt++;
        }  else if (builder.hasEntityId()) {
            // 清理EntityId
            builder.clearEntityId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getState() != ClanResBuildingStage.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getShowClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setShowClanSimpleName(this.getShowClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasShowClanSimpleName()) {
            // 清理ShowClanSimpleName
            builder.clearShowClanSimpleName();
            fieldCnt++;
        }
        if (!this.getShowClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setShowClanName(this.getShowClanName());
            fieldCnt++;
        }  else if (builder.hasShowClanName()) {
            // 清理ShowClanName
            builder.clearShowClanName();
            fieldCnt++;
        }
        if (this.getDisappearTsMs() != 0L) {
            builder.setDisappearTsMs(this.getDisappearTsMs());
            fieldCnt++;
        }  else if (builder.hasDisappearTsMs()) {
            // 清理DisappearTsMs
            builder.clearDisappearTsMs();
            fieldCnt++;
        }
        if (this.innerArmy != null) {
            Struct.CityInnerArmy.Builder tmpBuilder = Struct.CityInnerArmy.newBuilder();
            final int tmpFieldCnt = this.innerArmy.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInnerArmy(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInnerArmy();
            }
        }  else if (builder.hasInnerArmy()) {
            // 清理InnerArmy
            builder.clearInnerArmy();
            fieldCnt++;
        }
        if (this.progressInfo != null) {
            StructCommon.ProgressInfo.Builder tmpBuilder = StructCommon.ProgressInfo.newBuilder();
            final int tmpFieldCnt = this.progressInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setProgressInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearProgressInfo();
            }
        }  else if (builder.hasProgressInfo()) {
            // 清理ProgressInfo
            builder.clearProgressInfo();
            fieldCnt++;
        }
        if (this.flagInfo != null) {
            StructClan.ClanFlagInfo.Builder tmpBuilder = StructClan.ClanFlagInfo.newBuilder();
            final int tmpFieldCnt = this.flagInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFlagInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFlagInfo();
            }
        }  else if (builder.hasFlagInfo()) {
            // 清理FlagInfo
            builder.clearFlagInfo();
            fieldCnt++;
        }
        if (this.getCurrentCollectNum() != 0) {
            builder.setCurrentCollectNum(this.getCurrentCollectNum());
            fieldCnt++;
        }  else if (builder.hasCurrentCollectNum()) {
            // 清理CurrentCollectNum
            builder.clearCurrentCollectNum();
            fieldCnt++;
        }
        if (this.eachPlayerCollectMap != null) {
            StructCommon.Int64ProgressInfoMap.Builder tmpBuilder = StructCommon.Int64ProgressInfoMap.newBuilder();
            final int tmpFieldCnt = this.eachPlayerCollectMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setEachPlayerCollectMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearEachPlayerCollectMap();
            }
        }  else if (builder.hasEachPlayerCollectMap()) {
            // 清理EachPlayerCollectMap
            builder.clearEachPlayerCollectMap();
            fieldCnt++;
        }
        if (this.getIsCollecting()) {
            builder.setIsCollecting(this.getIsCollecting());
            fieldCnt++;
        }  else if (builder.hasIsCollecting()) {
            // 清理IsCollecting
            builder.clearIsCollecting();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanResBuildingEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ENTITYID)) {
            builder.setEntityId(this.getEntityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToDb(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCLANSIMPLENAME)) {
            builder.setShowClanSimpleName(this.getShowClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCLANNAME)) {
            builder.setShowClanName(this.getShowClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DISAPPEARTSMS)) {
            builder.setDisappearTsMs(this.getDisappearTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INNERARMY) && this.innerArmy != null) {
            final boolean needClear = !builder.hasInnerArmy();
            final int tmpFieldCnt = this.innerArmy.copyChangeToDb(builder.getInnerArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerArmy();
            }
        }
        if (this.hasMark(FIELD_INDEX_PROGRESSINFO) && this.progressInfo != null) {
            final boolean needClear = !builder.hasProgressInfo();
            final int tmpFieldCnt = this.progressInfo.copyChangeToDb(builder.getProgressInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearProgressInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_FLAGINFO) && this.flagInfo != null) {
            final boolean needClear = !builder.hasFlagInfo();
            final int tmpFieldCnt = this.flagInfo.copyChangeToDb(builder.getFlagInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFlagInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_CURRENTCOLLECTNUM)) {
            builder.setCurrentCollectNum(this.getCurrentCollectNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EACHPLAYERCOLLECTMAP) && this.eachPlayerCollectMap != null) {
            final boolean needClear = !builder.hasEachPlayerCollectMap();
            final int tmpFieldCnt = this.eachPlayerCollectMap.copyChangeToDb(builder.getEachPlayerCollectMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearEachPlayerCollectMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISCOLLECTING)) {
            builder.setIsCollecting(this.getIsCollecting());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanResBuildingEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasEntityId()) {
            this.innerSetEntityId(proto.getEntityId());
        } else {
            this.innerSetEntityId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(ClanResBuildingStage.forNumber(0));
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromDb(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromDb(proto.getPoint());
            }
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasShowClanSimpleName()) {
            this.innerSetShowClanSimpleName(proto.getShowClanSimpleName());
        } else {
            this.innerSetShowClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasShowClanName()) {
            this.innerSetShowClanName(proto.getShowClanName());
        } else {
            this.innerSetShowClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasDisappearTsMs()) {
            this.innerSetDisappearTsMs(proto.getDisappearTsMs());
        } else {
            this.innerSetDisappearTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasInnerArmy()) {
            this.getInnerArmy().mergeFromDb(proto.getInnerArmy());
        } else {
            if (this.innerArmy != null) {
                this.innerArmy.mergeFromDb(proto.getInnerArmy());
            }
        }
        if (proto.hasProgressInfo()) {
            this.getProgressInfo().mergeFromDb(proto.getProgressInfo());
        } else {
            if (this.progressInfo != null) {
                this.progressInfo.mergeFromDb(proto.getProgressInfo());
            }
        }
        if (proto.hasFlagInfo()) {
            this.getFlagInfo().mergeFromDb(proto.getFlagInfo());
        } else {
            if (this.flagInfo != null) {
                this.flagInfo.mergeFromDb(proto.getFlagInfo());
            }
        }
        if (proto.hasCurrentCollectNum()) {
            this.innerSetCurrentCollectNum(proto.getCurrentCollectNum());
        } else {
            this.innerSetCurrentCollectNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEachPlayerCollectMap()) {
            this.getEachPlayerCollectMap().mergeFromDb(proto.getEachPlayerCollectMap());
        } else {
            if (this.eachPlayerCollectMap != null) {
                this.eachPlayerCollectMap.mergeFromDb(proto.getEachPlayerCollectMap());
            }
        }
        if (proto.hasIsCollecting()) {
            this.innerSetIsCollecting(proto.getIsCollecting());
        } else {
            this.innerSetIsCollecting(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanResBuildingProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanResBuildingEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasEntityId()) {
            this.setEntityId(proto.getEntityId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromDb(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasShowClanSimpleName()) {
            this.setShowClanSimpleName(proto.getShowClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasShowClanName()) {
            this.setShowClanName(proto.getShowClanName());
            fieldCnt++;
        }
        if (proto.hasDisappearTsMs()) {
            this.setDisappearTsMs(proto.getDisappearTsMs());
            fieldCnt++;
        }
        if (proto.hasInnerArmy()) {
            this.getInnerArmy().mergeChangeFromDb(proto.getInnerArmy());
            fieldCnt++;
        }
        if (proto.hasProgressInfo()) {
            this.getProgressInfo().mergeChangeFromDb(proto.getProgressInfo());
            fieldCnt++;
        }
        if (proto.hasFlagInfo()) {
            this.getFlagInfo().mergeChangeFromDb(proto.getFlagInfo());
            fieldCnt++;
        }
        if (proto.hasCurrentCollectNum()) {
            this.setCurrentCollectNum(proto.getCurrentCollectNum());
            fieldCnt++;
        }
        if (proto.hasEachPlayerCollectMap()) {
            this.getEachPlayerCollectMap().mergeChangeFromDb(proto.getEachPlayerCollectMap());
            fieldCnt++;
        }
        if (proto.hasIsCollecting()) {
            this.setIsCollecting(proto.getIsCollecting());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanResBuildingEntity.Builder getCopySsBuilder() {
        final ClanResBuildingEntity.Builder builder = ClanResBuildingEntity.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanResBuildingEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getEntityId() != 0L) {
            builder.setEntityId(this.getEntityId());
            fieldCnt++;
        }  else if (builder.hasEntityId()) {
            // 清理EntityId
            builder.clearEntityId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getState() != ClanResBuildingStage.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getShowClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setShowClanSimpleName(this.getShowClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasShowClanSimpleName()) {
            // 清理ShowClanSimpleName
            builder.clearShowClanSimpleName();
            fieldCnt++;
        }
        if (!this.getShowClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setShowClanName(this.getShowClanName());
            fieldCnt++;
        }  else if (builder.hasShowClanName()) {
            // 清理ShowClanName
            builder.clearShowClanName();
            fieldCnt++;
        }
        if (this.getDisappearTsMs() != 0L) {
            builder.setDisappearTsMs(this.getDisappearTsMs());
            fieldCnt++;
        }  else if (builder.hasDisappearTsMs()) {
            // 清理DisappearTsMs
            builder.clearDisappearTsMs();
            fieldCnt++;
        }
        if (this.innerArmy != null) {
            Struct.CityInnerArmy.Builder tmpBuilder = Struct.CityInnerArmy.newBuilder();
            final int tmpFieldCnt = this.innerArmy.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInnerArmy(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInnerArmy();
            }
        }  else if (builder.hasInnerArmy()) {
            // 清理InnerArmy
            builder.clearInnerArmy();
            fieldCnt++;
        }
        if (this.progressInfo != null) {
            StructCommon.ProgressInfo.Builder tmpBuilder = StructCommon.ProgressInfo.newBuilder();
            final int tmpFieldCnt = this.progressInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setProgressInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearProgressInfo();
            }
        }  else if (builder.hasProgressInfo()) {
            // 清理ProgressInfo
            builder.clearProgressInfo();
            fieldCnt++;
        }
        if (this.flagInfo != null) {
            StructClan.ClanFlagInfo.Builder tmpBuilder = StructClan.ClanFlagInfo.newBuilder();
            final int tmpFieldCnt = this.flagInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFlagInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFlagInfo();
            }
        }  else if (builder.hasFlagInfo()) {
            // 清理FlagInfo
            builder.clearFlagInfo();
            fieldCnt++;
        }
        if (this.getCurrentCollectNum() != 0) {
            builder.setCurrentCollectNum(this.getCurrentCollectNum());
            fieldCnt++;
        }  else if (builder.hasCurrentCollectNum()) {
            // 清理CurrentCollectNum
            builder.clearCurrentCollectNum();
            fieldCnt++;
        }
        if (this.eachPlayerCollectMap != null) {
            StructCommon.Int64ProgressInfoMap.Builder tmpBuilder = StructCommon.Int64ProgressInfoMap.newBuilder();
            final int tmpFieldCnt = this.eachPlayerCollectMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setEachPlayerCollectMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearEachPlayerCollectMap();
            }
        }  else if (builder.hasEachPlayerCollectMap()) {
            // 清理EachPlayerCollectMap
            builder.clearEachPlayerCollectMap();
            fieldCnt++;
        }
        if (this.getIsCollecting()) {
            builder.setIsCollecting(this.getIsCollecting());
            fieldCnt++;
        }  else if (builder.hasIsCollecting()) {
            // 清理IsCollecting
            builder.clearIsCollecting();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanResBuildingEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ENTITYID)) {
            builder.setEntityId(this.getEntityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToSs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCLANSIMPLENAME)) {
            builder.setShowClanSimpleName(this.getShowClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCLANNAME)) {
            builder.setShowClanName(this.getShowClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DISAPPEARTSMS)) {
            builder.setDisappearTsMs(this.getDisappearTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INNERARMY) && this.innerArmy != null) {
            final boolean needClear = !builder.hasInnerArmy();
            final int tmpFieldCnt = this.innerArmy.copyChangeToSs(builder.getInnerArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerArmy();
            }
        }
        if (this.hasMark(FIELD_INDEX_PROGRESSINFO) && this.progressInfo != null) {
            final boolean needClear = !builder.hasProgressInfo();
            final int tmpFieldCnt = this.progressInfo.copyChangeToSs(builder.getProgressInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearProgressInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_FLAGINFO) && this.flagInfo != null) {
            final boolean needClear = !builder.hasFlagInfo();
            final int tmpFieldCnt = this.flagInfo.copyChangeToSs(builder.getFlagInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFlagInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_CURRENTCOLLECTNUM)) {
            builder.setCurrentCollectNum(this.getCurrentCollectNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EACHPLAYERCOLLECTMAP) && this.eachPlayerCollectMap != null) {
            final boolean needClear = !builder.hasEachPlayerCollectMap();
            final int tmpFieldCnt = this.eachPlayerCollectMap.copyChangeToSs(builder.getEachPlayerCollectMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearEachPlayerCollectMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISCOLLECTING)) {
            builder.setIsCollecting(this.getIsCollecting());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanResBuildingEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasEntityId()) {
            this.innerSetEntityId(proto.getEntityId());
        } else {
            this.innerSetEntityId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(ClanResBuildingStage.forNumber(0));
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromSs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromSs(proto.getPoint());
            }
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasShowClanSimpleName()) {
            this.innerSetShowClanSimpleName(proto.getShowClanSimpleName());
        } else {
            this.innerSetShowClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasShowClanName()) {
            this.innerSetShowClanName(proto.getShowClanName());
        } else {
            this.innerSetShowClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasDisappearTsMs()) {
            this.innerSetDisappearTsMs(proto.getDisappearTsMs());
        } else {
            this.innerSetDisappearTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasInnerArmy()) {
            this.getInnerArmy().mergeFromSs(proto.getInnerArmy());
        } else {
            if (this.innerArmy != null) {
                this.innerArmy.mergeFromSs(proto.getInnerArmy());
            }
        }
        if (proto.hasProgressInfo()) {
            this.getProgressInfo().mergeFromSs(proto.getProgressInfo());
        } else {
            if (this.progressInfo != null) {
                this.progressInfo.mergeFromSs(proto.getProgressInfo());
            }
        }
        if (proto.hasFlagInfo()) {
            this.getFlagInfo().mergeFromSs(proto.getFlagInfo());
        } else {
            if (this.flagInfo != null) {
                this.flagInfo.mergeFromSs(proto.getFlagInfo());
            }
        }
        if (proto.hasCurrentCollectNum()) {
            this.innerSetCurrentCollectNum(proto.getCurrentCollectNum());
        } else {
            this.innerSetCurrentCollectNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEachPlayerCollectMap()) {
            this.getEachPlayerCollectMap().mergeFromSs(proto.getEachPlayerCollectMap());
        } else {
            if (this.eachPlayerCollectMap != null) {
                this.eachPlayerCollectMap.mergeFromSs(proto.getEachPlayerCollectMap());
            }
        }
        if (proto.hasIsCollecting()) {
            this.innerSetIsCollecting(proto.getIsCollecting());
        } else {
            this.innerSetIsCollecting(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanResBuildingProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanResBuildingEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasEntityId()) {
            this.setEntityId(proto.getEntityId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromSs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasShowClanSimpleName()) {
            this.setShowClanSimpleName(proto.getShowClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasShowClanName()) {
            this.setShowClanName(proto.getShowClanName());
            fieldCnt++;
        }
        if (proto.hasDisappearTsMs()) {
            this.setDisappearTsMs(proto.getDisappearTsMs());
            fieldCnt++;
        }
        if (proto.hasInnerArmy()) {
            this.getInnerArmy().mergeChangeFromSs(proto.getInnerArmy());
            fieldCnt++;
        }
        if (proto.hasProgressInfo()) {
            this.getProgressInfo().mergeChangeFromSs(proto.getProgressInfo());
            fieldCnt++;
        }
        if (proto.hasFlagInfo()) {
            this.getFlagInfo().mergeChangeFromSs(proto.getFlagInfo());
            fieldCnt++;
        }
        if (proto.hasCurrentCollectNum()) {
            this.setCurrentCollectNum(proto.getCurrentCollectNum());
            fieldCnt++;
        }
        if (proto.hasEachPlayerCollectMap()) {
            this.getEachPlayerCollectMap().mergeChangeFromSs(proto.getEachPlayerCollectMap());
            fieldCnt++;
        }
        if (proto.hasIsCollecting()) {
            this.setIsCollecting(proto.getIsCollecting());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanResBuildingEntity.Builder builder = ClanResBuildingEntity.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            this.point.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_INNERARMY) && this.innerArmy != null) {
            this.innerArmy.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PROGRESSINFO) && this.progressInfo != null) {
            this.progressInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_FLAGINFO) && this.flagInfo != null) {
            this.flagInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_EACHPLAYERCOLLECTMAP) && this.eachPlayerCollectMap != null) {
            this.eachPlayerCollectMap.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.point != null) {
            this.point.markAll();
        }
        if (this.innerArmy != null) {
            this.innerArmy.markAll();
        }
        if (this.progressInfo != null) {
            this.progressInfo.markAll();
        }
        if (this.flagInfo != null) {
            this.flagInfo.markAll();
        }
        if (this.eachPlayerCollectMap != null) {
            this.eachPlayerCollectMap.markAll();
        }
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("ClanResBuildingProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("ClanResBuildingProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanResBuildingProp)) {
            return false;
        }
        final ClanResBuildingProp otherNode = (ClanResBuildingProp) node;
        if (this.entityId != otherNode.entityId) {
            return false;
        }
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        if (this.state != otherNode.state) {
            return false;
        }
        if (!this.getPoint().compareDataTo(otherNode.getPoint())) {
            return false;
        }
        if (this.clanId != otherNode.clanId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.showClanSimpleName, otherNode.showClanSimpleName)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.showClanName, otherNode.showClanName)) {
            return false;
        }
        if (this.disappearTsMs != otherNode.disappearTsMs) {
            return false;
        }
        if (!this.getInnerArmy().compareDataTo(otherNode.getInnerArmy())) {
            return false;
        }
        if (!this.getProgressInfo().compareDataTo(otherNode.getProgressInfo())) {
            return false;
        }
        if (!this.getFlagInfo().compareDataTo(otherNode.getFlagInfo())) {
            return false;
        }
        if (this.currentCollectNum != otherNode.currentCollectNum) {
            return false;
        }
        if (!this.getEachPlayerCollectMap().compareDataTo(otherNode.getEachPlayerCollectMap())) {
            return false;
        }
        if (this.isCollecting != otherNode.isCollecting) {
            return false;
        }
        if (this.zoneId != otherNode.zoneId) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    public static ClanResBuildingProp of(ClanResBuildingEntity fullAttrDb, ClanResBuildingEntity changeAttrDb) {
        ClanResBuildingProp prop = new ClanResBuildingProp();
        prop.mergeFromDb(fullAttrDb);
        prop.mergeChangeFromDb(changeAttrDb);
        prop.unMarkAll();
        return prop;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 49;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
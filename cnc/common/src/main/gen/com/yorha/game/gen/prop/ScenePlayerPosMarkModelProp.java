package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.ScenePlayerPosMarkModel;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.ScenePlayerPosMarkModelPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ScenePlayerPosMarkModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_PERSONALPOSMARKMAP = 0;
    public static final int FIELD_INDEX_CLANPOSMARKVERSION = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private Int64PositionMarkInfoMapProp personalPosMarkMap = null;
    private int clanPosMarkVersion = Constant.DEFAULT_INT_VALUE;

    public ScenePlayerPosMarkModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ScenePlayerPosMarkModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get personalPosMarkMap
     *
     * @return personalPosMarkMap value
     */
    public Int64PositionMarkInfoMapProp getPersonalPosMarkMap() {
        if (this.personalPosMarkMap == null) {
            this.personalPosMarkMap = new Int64PositionMarkInfoMapProp(this, FIELD_INDEX_PERSONALPOSMARKMAP);
        }
        return this.personalPosMarkMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putPersonalPosMarkMapV(PositionMarkInfoProp v) {
        this.getPersonalPosMarkMap().put(v.getMarkId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PositionMarkInfoProp addEmptyPersonalPosMarkMap(Long k) {
        return this.getPersonalPosMarkMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getPersonalPosMarkMapSize() {
        if (this.personalPosMarkMap == null) {
            return 0;
        }
        return this.personalPosMarkMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isPersonalPosMarkMapEmpty() {
        if (this.personalPosMarkMap == null) {
            return true;
        }
        return this.personalPosMarkMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PositionMarkInfoProp getPersonalPosMarkMapV(Long k) {
        if (this.personalPosMarkMap == null || !this.personalPosMarkMap.containsKey(k)) {
            return null;
        }
        return this.personalPosMarkMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearPersonalPosMarkMap() {
        if (this.personalPosMarkMap != null) {
            this.personalPosMarkMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removePersonalPosMarkMapV(Long k) {
        if (this.personalPosMarkMap != null) {
            this.personalPosMarkMap.remove(k);
        }
    }
    /**
     * get clanPosMarkVersion
     *
     * @return clanPosMarkVersion value
     */
    public int getClanPosMarkVersion() {
        return this.clanPosMarkVersion;
    }

    /**
     * set clanPosMarkVersion && set marked
     *
     * @param clanPosMarkVersion new value
     * @return current object
     */
    public ScenePlayerPosMarkModelProp setClanPosMarkVersion(int clanPosMarkVersion) {
        if (this.clanPosMarkVersion != clanPosMarkVersion) {
            this.mark(FIELD_INDEX_CLANPOSMARKVERSION);
            this.clanPosMarkVersion = clanPosMarkVersion;
        }
        return this;
    }

    /**
     * inner set clanPosMarkVersion
     *
     * @param clanPosMarkVersion new value
     */
    private void innerSetClanPosMarkVersion(int clanPosMarkVersion) {
        this.clanPosMarkVersion = clanPosMarkVersion;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerPosMarkModelPB.Builder getCopyCsBuilder() {
        final ScenePlayerPosMarkModelPB.Builder builder = ScenePlayerPosMarkModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ScenePlayerPosMarkModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.personalPosMarkMap != null) {
            StructPB.Int64PositionMarkInfoMapPB.Builder tmpBuilder = StructPB.Int64PositionMarkInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.personalPosMarkMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPersonalPosMarkMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPersonalPosMarkMap();
            }
        }  else if (builder.hasPersonalPosMarkMap()) {
            // 清理PersonalPosMarkMap
            builder.clearPersonalPosMarkMap();
            fieldCnt++;
        }
        if (this.getClanPosMarkVersion() != 0) {
            builder.setClanPosMarkVersion(this.getClanPosMarkVersion());
            fieldCnt++;
        }  else if (builder.hasClanPosMarkVersion()) {
            // 清理ClanPosMarkVersion
            builder.clearClanPosMarkVersion();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ScenePlayerPosMarkModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PERSONALPOSMARKMAP) && this.personalPosMarkMap != null) {
            final boolean needClear = !builder.hasPersonalPosMarkMap();
            final int tmpFieldCnt = this.personalPosMarkMap.copyChangeToCs(builder.getPersonalPosMarkMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPersonalPosMarkMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANPOSMARKVERSION)) {
            builder.setClanPosMarkVersion(this.getClanPosMarkVersion());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ScenePlayerPosMarkModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PERSONALPOSMARKMAP) && this.personalPosMarkMap != null) {
            final boolean needClear = !builder.hasPersonalPosMarkMap();
            final int tmpFieldCnt = this.personalPosMarkMap.copyChangeToAndClearDeleteKeysCs(builder.getPersonalPosMarkMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPersonalPosMarkMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANPOSMARKVERSION)) {
            builder.setClanPosMarkVersion(this.getClanPosMarkVersion());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ScenePlayerPosMarkModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPersonalPosMarkMap()) {
            this.getPersonalPosMarkMap().mergeFromCs(proto.getPersonalPosMarkMap());
        } else {
            if (this.personalPosMarkMap != null) {
                this.personalPosMarkMap.mergeFromCs(proto.getPersonalPosMarkMap());
            }
        }
        if (proto.hasClanPosMarkVersion()) {
            this.innerSetClanPosMarkVersion(proto.getClanPosMarkVersion());
        } else {
            this.innerSetClanPosMarkVersion(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ScenePlayerPosMarkModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ScenePlayerPosMarkModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPersonalPosMarkMap()) {
            this.getPersonalPosMarkMap().mergeChangeFromCs(proto.getPersonalPosMarkMap());
            fieldCnt++;
        }
        if (proto.hasClanPosMarkVersion()) {
            this.setClanPosMarkVersion(proto.getClanPosMarkVersion());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerPosMarkModel.Builder getCopyDbBuilder() {
        final ScenePlayerPosMarkModel.Builder builder = ScenePlayerPosMarkModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ScenePlayerPosMarkModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.personalPosMarkMap != null) {
            Struct.Int64PositionMarkInfoMap.Builder tmpBuilder = Struct.Int64PositionMarkInfoMap.newBuilder();
            final int tmpFieldCnt = this.personalPosMarkMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPersonalPosMarkMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPersonalPosMarkMap();
            }
        }  else if (builder.hasPersonalPosMarkMap()) {
            // 清理PersonalPosMarkMap
            builder.clearPersonalPosMarkMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ScenePlayerPosMarkModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PERSONALPOSMARKMAP) && this.personalPosMarkMap != null) {
            final boolean needClear = !builder.hasPersonalPosMarkMap();
            final int tmpFieldCnt = this.personalPosMarkMap.copyChangeToDb(builder.getPersonalPosMarkMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPersonalPosMarkMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ScenePlayerPosMarkModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPersonalPosMarkMap()) {
            this.getPersonalPosMarkMap().mergeFromDb(proto.getPersonalPosMarkMap());
        } else {
            if (this.personalPosMarkMap != null) {
                this.personalPosMarkMap.mergeFromDb(proto.getPersonalPosMarkMap());
            }
        }
        this.markAll();
        return ScenePlayerPosMarkModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ScenePlayerPosMarkModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPersonalPosMarkMap()) {
            this.getPersonalPosMarkMap().mergeChangeFromDb(proto.getPersonalPosMarkMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerPosMarkModel.Builder getCopySsBuilder() {
        final ScenePlayerPosMarkModel.Builder builder = ScenePlayerPosMarkModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ScenePlayerPosMarkModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.personalPosMarkMap != null) {
            Struct.Int64PositionMarkInfoMap.Builder tmpBuilder = Struct.Int64PositionMarkInfoMap.newBuilder();
            final int tmpFieldCnt = this.personalPosMarkMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPersonalPosMarkMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPersonalPosMarkMap();
            }
        }  else if (builder.hasPersonalPosMarkMap()) {
            // 清理PersonalPosMarkMap
            builder.clearPersonalPosMarkMap();
            fieldCnt++;
        }
        if (this.getClanPosMarkVersion() != 0) {
            builder.setClanPosMarkVersion(this.getClanPosMarkVersion());
            fieldCnt++;
        }  else if (builder.hasClanPosMarkVersion()) {
            // 清理ClanPosMarkVersion
            builder.clearClanPosMarkVersion();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ScenePlayerPosMarkModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PERSONALPOSMARKMAP) && this.personalPosMarkMap != null) {
            final boolean needClear = !builder.hasPersonalPosMarkMap();
            final int tmpFieldCnt = this.personalPosMarkMap.copyChangeToSs(builder.getPersonalPosMarkMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPersonalPosMarkMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANPOSMARKVERSION)) {
            builder.setClanPosMarkVersion(this.getClanPosMarkVersion());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ScenePlayerPosMarkModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPersonalPosMarkMap()) {
            this.getPersonalPosMarkMap().mergeFromSs(proto.getPersonalPosMarkMap());
        } else {
            if (this.personalPosMarkMap != null) {
                this.personalPosMarkMap.mergeFromSs(proto.getPersonalPosMarkMap());
            }
        }
        if (proto.hasClanPosMarkVersion()) {
            this.innerSetClanPosMarkVersion(proto.getClanPosMarkVersion());
        } else {
            this.innerSetClanPosMarkVersion(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ScenePlayerPosMarkModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ScenePlayerPosMarkModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPersonalPosMarkMap()) {
            this.getPersonalPosMarkMap().mergeChangeFromSs(proto.getPersonalPosMarkMap());
            fieldCnt++;
        }
        if (proto.hasClanPosMarkVersion()) {
            this.setClanPosMarkVersion(proto.getClanPosMarkVersion());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ScenePlayerPosMarkModel.Builder builder = ScenePlayerPosMarkModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_PERSONALPOSMARKMAP) && this.personalPosMarkMap != null) {
            this.personalPosMarkMap.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.personalPosMarkMap != null) {
            this.personalPosMarkMap.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ScenePlayerPosMarkModelProp)) {
            return false;
        }
        final ScenePlayerPosMarkModelProp otherNode = (ScenePlayerPosMarkModelProp) node;
        if (!this.getPersonalPosMarkMap().compareDataTo(otherNode.getPersonalPosMarkMap())) {
            return false;
        }
        if (this.clanPosMarkVersion != otherNode.clanPosMarkVersion) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
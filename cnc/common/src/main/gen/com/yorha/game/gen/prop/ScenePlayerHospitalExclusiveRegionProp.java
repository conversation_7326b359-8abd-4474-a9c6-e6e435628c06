package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ScenePlayerHospitalExclusiveRegion;
import com.yorha.proto.StructPB.ScenePlayerHospitalExclusiveRegionPB;


/**
 * <AUTHOR> auto gen
 */
public class ScenePlayerHospitalExclusiveRegionProp extends AbstractPropNode {

    public static final int FIELD_INDEX_SOLDIERID = 0;
    public static final int FIELD_INDEX_WAITINGSOLDIERS = 1;
    public static final int FIELD_INDEX_INTREATMENTSOLDIERS = 2;
    public static final int FIELD_INDEX_TREATOVERSOLDIERS = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int soldierId = Constant.DEFAULT_INT_VALUE;
    private int waitingSoldiers = Constant.DEFAULT_INT_VALUE;
    private int inTreatmentSoldiers = Constant.DEFAULT_INT_VALUE;
    private int treatOverSoldiers = Constant.DEFAULT_INT_VALUE;

    public ScenePlayerHospitalExclusiveRegionProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ScenePlayerHospitalExclusiveRegionProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get soldierId
     *
     * @return soldierId value
     */
    public int getSoldierId() {
        return this.soldierId;
    }

    /**
     * set soldierId && set marked
     *
     * @param soldierId new value
     * @return current object
     */
    public ScenePlayerHospitalExclusiveRegionProp setSoldierId(int soldierId) {
        if (this.soldierId != soldierId) {
            this.mark(FIELD_INDEX_SOLDIERID);
            this.soldierId = soldierId;
        }
        return this;
    }

    /**
     * inner set soldierId
     *
     * @param soldierId new value
     */
    private void innerSetSoldierId(int soldierId) {
        this.soldierId = soldierId;
    }

    /**
     * get waitingSoldiers
     *
     * @return waitingSoldiers value
     */
    public int getWaitingSoldiers() {
        return this.waitingSoldiers;
    }

    /**
     * set waitingSoldiers && set marked
     *
     * @param waitingSoldiers new value
     * @return current object
     */
    public ScenePlayerHospitalExclusiveRegionProp setWaitingSoldiers(int waitingSoldiers) {
        if (this.waitingSoldiers != waitingSoldiers) {
            this.mark(FIELD_INDEX_WAITINGSOLDIERS);
            this.waitingSoldiers = waitingSoldiers;
        }
        return this;
    }

    /**
     * inner set waitingSoldiers
     *
     * @param waitingSoldiers new value
     */
    private void innerSetWaitingSoldiers(int waitingSoldiers) {
        this.waitingSoldiers = waitingSoldiers;
    }

    /**
     * get inTreatmentSoldiers
     *
     * @return inTreatmentSoldiers value
     */
    public int getInTreatmentSoldiers() {
        return this.inTreatmentSoldiers;
    }

    /**
     * set inTreatmentSoldiers && set marked
     *
     * @param inTreatmentSoldiers new value
     * @return current object
     */
    public ScenePlayerHospitalExclusiveRegionProp setInTreatmentSoldiers(int inTreatmentSoldiers) {
        if (this.inTreatmentSoldiers != inTreatmentSoldiers) {
            this.mark(FIELD_INDEX_INTREATMENTSOLDIERS);
            this.inTreatmentSoldiers = inTreatmentSoldiers;
        }
        return this;
    }

    /**
     * inner set inTreatmentSoldiers
     *
     * @param inTreatmentSoldiers new value
     */
    private void innerSetInTreatmentSoldiers(int inTreatmentSoldiers) {
        this.inTreatmentSoldiers = inTreatmentSoldiers;
    }

    /**
     * get treatOverSoldiers
     *
     * @return treatOverSoldiers value
     */
    public int getTreatOverSoldiers() {
        return this.treatOverSoldiers;
    }

    /**
     * set treatOverSoldiers && set marked
     *
     * @param treatOverSoldiers new value
     * @return current object
     */
    public ScenePlayerHospitalExclusiveRegionProp setTreatOverSoldiers(int treatOverSoldiers) {
        if (this.treatOverSoldiers != treatOverSoldiers) {
            this.mark(FIELD_INDEX_TREATOVERSOLDIERS);
            this.treatOverSoldiers = treatOverSoldiers;
        }
        return this;
    }

    /**
     * inner set treatOverSoldiers
     *
     * @param treatOverSoldiers new value
     */
    private void innerSetTreatOverSoldiers(int treatOverSoldiers) {
        this.treatOverSoldiers = treatOverSoldiers;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerHospitalExclusiveRegionPB.Builder getCopyCsBuilder() {
        final ScenePlayerHospitalExclusiveRegionPB.Builder builder = ScenePlayerHospitalExclusiveRegionPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ScenePlayerHospitalExclusiveRegionPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSoldierId() != 0) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }  else if (builder.hasSoldierId()) {
            // 清理SoldierId
            builder.clearSoldierId();
            fieldCnt++;
        }
        if (this.getWaitingSoldiers() != 0) {
            builder.setWaitingSoldiers(this.getWaitingSoldiers());
            fieldCnt++;
        }  else if (builder.hasWaitingSoldiers()) {
            // 清理WaitingSoldiers
            builder.clearWaitingSoldiers();
            fieldCnt++;
        }
        if (this.getInTreatmentSoldiers() != 0) {
            builder.setInTreatmentSoldiers(this.getInTreatmentSoldiers());
            fieldCnt++;
        }  else if (builder.hasInTreatmentSoldiers()) {
            // 清理InTreatmentSoldiers
            builder.clearInTreatmentSoldiers();
            fieldCnt++;
        }
        if (this.getTreatOverSoldiers() != 0) {
            builder.setTreatOverSoldiers(this.getTreatOverSoldiers());
            fieldCnt++;
        }  else if (builder.hasTreatOverSoldiers()) {
            // 清理TreatOverSoldiers
            builder.clearTreatOverSoldiers();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ScenePlayerHospitalExclusiveRegionPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERID)) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WAITINGSOLDIERS)) {
            builder.setWaitingSoldiers(this.getWaitingSoldiers());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INTREATMENTSOLDIERS)) {
            builder.setInTreatmentSoldiers(this.getInTreatmentSoldiers());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TREATOVERSOLDIERS)) {
            builder.setTreatOverSoldiers(this.getTreatOverSoldiers());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ScenePlayerHospitalExclusiveRegionPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERID)) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WAITINGSOLDIERS)) {
            builder.setWaitingSoldiers(this.getWaitingSoldiers());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INTREATMENTSOLDIERS)) {
            builder.setInTreatmentSoldiers(this.getInTreatmentSoldiers());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TREATOVERSOLDIERS)) {
            builder.setTreatOverSoldiers(this.getTreatOverSoldiers());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ScenePlayerHospitalExclusiveRegionPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierId()) {
            this.innerSetSoldierId(proto.getSoldierId());
        } else {
            this.innerSetSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasWaitingSoldiers()) {
            this.innerSetWaitingSoldiers(proto.getWaitingSoldiers());
        } else {
            this.innerSetWaitingSoldiers(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasInTreatmentSoldiers()) {
            this.innerSetInTreatmentSoldiers(proto.getInTreatmentSoldiers());
        } else {
            this.innerSetInTreatmentSoldiers(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTreatOverSoldiers()) {
            this.innerSetTreatOverSoldiers(proto.getTreatOverSoldiers());
        } else {
            this.innerSetTreatOverSoldiers(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ScenePlayerHospitalExclusiveRegionProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ScenePlayerHospitalExclusiveRegionPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierId()) {
            this.setSoldierId(proto.getSoldierId());
            fieldCnt++;
        }
        if (proto.hasWaitingSoldiers()) {
            this.setWaitingSoldiers(proto.getWaitingSoldiers());
            fieldCnt++;
        }
        if (proto.hasInTreatmentSoldiers()) {
            this.setInTreatmentSoldiers(proto.getInTreatmentSoldiers());
            fieldCnt++;
        }
        if (proto.hasTreatOverSoldiers()) {
            this.setTreatOverSoldiers(proto.getTreatOverSoldiers());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerHospitalExclusiveRegion.Builder getCopyDbBuilder() {
        final ScenePlayerHospitalExclusiveRegion.Builder builder = ScenePlayerHospitalExclusiveRegion.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ScenePlayerHospitalExclusiveRegion.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSoldierId() != 0) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }  else if (builder.hasSoldierId()) {
            // 清理SoldierId
            builder.clearSoldierId();
            fieldCnt++;
        }
        if (this.getWaitingSoldiers() != 0) {
            builder.setWaitingSoldiers(this.getWaitingSoldiers());
            fieldCnt++;
        }  else if (builder.hasWaitingSoldiers()) {
            // 清理WaitingSoldiers
            builder.clearWaitingSoldiers();
            fieldCnt++;
        }
        if (this.getInTreatmentSoldiers() != 0) {
            builder.setInTreatmentSoldiers(this.getInTreatmentSoldiers());
            fieldCnt++;
        }  else if (builder.hasInTreatmentSoldiers()) {
            // 清理InTreatmentSoldiers
            builder.clearInTreatmentSoldiers();
            fieldCnt++;
        }
        if (this.getTreatOverSoldiers() != 0) {
            builder.setTreatOverSoldiers(this.getTreatOverSoldiers());
            fieldCnt++;
        }  else if (builder.hasTreatOverSoldiers()) {
            // 清理TreatOverSoldiers
            builder.clearTreatOverSoldiers();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ScenePlayerHospitalExclusiveRegion.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERID)) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WAITINGSOLDIERS)) {
            builder.setWaitingSoldiers(this.getWaitingSoldiers());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INTREATMENTSOLDIERS)) {
            builder.setInTreatmentSoldiers(this.getInTreatmentSoldiers());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TREATOVERSOLDIERS)) {
            builder.setTreatOverSoldiers(this.getTreatOverSoldiers());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ScenePlayerHospitalExclusiveRegion proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierId()) {
            this.innerSetSoldierId(proto.getSoldierId());
        } else {
            this.innerSetSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasWaitingSoldiers()) {
            this.innerSetWaitingSoldiers(proto.getWaitingSoldiers());
        } else {
            this.innerSetWaitingSoldiers(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasInTreatmentSoldiers()) {
            this.innerSetInTreatmentSoldiers(proto.getInTreatmentSoldiers());
        } else {
            this.innerSetInTreatmentSoldiers(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTreatOverSoldiers()) {
            this.innerSetTreatOverSoldiers(proto.getTreatOverSoldiers());
        } else {
            this.innerSetTreatOverSoldiers(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ScenePlayerHospitalExclusiveRegionProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ScenePlayerHospitalExclusiveRegion proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierId()) {
            this.setSoldierId(proto.getSoldierId());
            fieldCnt++;
        }
        if (proto.hasWaitingSoldiers()) {
            this.setWaitingSoldiers(proto.getWaitingSoldiers());
            fieldCnt++;
        }
        if (proto.hasInTreatmentSoldiers()) {
            this.setInTreatmentSoldiers(proto.getInTreatmentSoldiers());
            fieldCnt++;
        }
        if (proto.hasTreatOverSoldiers()) {
            this.setTreatOverSoldiers(proto.getTreatOverSoldiers());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerHospitalExclusiveRegion.Builder getCopySsBuilder() {
        final ScenePlayerHospitalExclusiveRegion.Builder builder = ScenePlayerHospitalExclusiveRegion.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ScenePlayerHospitalExclusiveRegion.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSoldierId() != 0) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }  else if (builder.hasSoldierId()) {
            // 清理SoldierId
            builder.clearSoldierId();
            fieldCnt++;
        }
        if (this.getWaitingSoldiers() != 0) {
            builder.setWaitingSoldiers(this.getWaitingSoldiers());
            fieldCnt++;
        }  else if (builder.hasWaitingSoldiers()) {
            // 清理WaitingSoldiers
            builder.clearWaitingSoldiers();
            fieldCnt++;
        }
        if (this.getInTreatmentSoldiers() != 0) {
            builder.setInTreatmentSoldiers(this.getInTreatmentSoldiers());
            fieldCnt++;
        }  else if (builder.hasInTreatmentSoldiers()) {
            // 清理InTreatmentSoldiers
            builder.clearInTreatmentSoldiers();
            fieldCnt++;
        }
        if (this.getTreatOverSoldiers() != 0) {
            builder.setTreatOverSoldiers(this.getTreatOverSoldiers());
            fieldCnt++;
        }  else if (builder.hasTreatOverSoldiers()) {
            // 清理TreatOverSoldiers
            builder.clearTreatOverSoldiers();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ScenePlayerHospitalExclusiveRegion.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERID)) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WAITINGSOLDIERS)) {
            builder.setWaitingSoldiers(this.getWaitingSoldiers());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INTREATMENTSOLDIERS)) {
            builder.setInTreatmentSoldiers(this.getInTreatmentSoldiers());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TREATOVERSOLDIERS)) {
            builder.setTreatOverSoldiers(this.getTreatOverSoldiers());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ScenePlayerHospitalExclusiveRegion proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierId()) {
            this.innerSetSoldierId(proto.getSoldierId());
        } else {
            this.innerSetSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasWaitingSoldiers()) {
            this.innerSetWaitingSoldiers(proto.getWaitingSoldiers());
        } else {
            this.innerSetWaitingSoldiers(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasInTreatmentSoldiers()) {
            this.innerSetInTreatmentSoldiers(proto.getInTreatmentSoldiers());
        } else {
            this.innerSetInTreatmentSoldiers(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTreatOverSoldiers()) {
            this.innerSetTreatOverSoldiers(proto.getTreatOverSoldiers());
        } else {
            this.innerSetTreatOverSoldiers(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ScenePlayerHospitalExclusiveRegionProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ScenePlayerHospitalExclusiveRegion proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierId()) {
            this.setSoldierId(proto.getSoldierId());
            fieldCnt++;
        }
        if (proto.hasWaitingSoldiers()) {
            this.setWaitingSoldiers(proto.getWaitingSoldiers());
            fieldCnt++;
        }
        if (proto.hasInTreatmentSoldiers()) {
            this.setInTreatmentSoldiers(proto.getInTreatmentSoldiers());
            fieldCnt++;
        }
        if (proto.hasTreatOverSoldiers()) {
            this.setTreatOverSoldiers(proto.getTreatOverSoldiers());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ScenePlayerHospitalExclusiveRegion.Builder builder = ScenePlayerHospitalExclusiveRegion.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ScenePlayerHospitalExclusiveRegionProp)) {
            return false;
        }
        final ScenePlayerHospitalExclusiveRegionProp otherNode = (ScenePlayerHospitalExclusiveRegionProp) node;
        if (this.soldierId != otherNode.soldierId) {
            return false;
        }
        if (this.waitingSoldiers != otherNode.waitingSoldiers) {
            return false;
        }
        if (this.inTreatmentSoldiers != otherNode.inTreatmentSoldiers) {
            return false;
        }
        if (this.treatOverSoldiers != otherNode.treatOverSoldiers) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
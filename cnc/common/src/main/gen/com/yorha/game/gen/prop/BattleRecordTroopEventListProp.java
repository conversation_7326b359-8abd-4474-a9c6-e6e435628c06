package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructBattlePB.BattleRecordTroopEventListPB;
import com.yorha.proto.StructBattle.BattleRecordTroopEventList;
import com.yorha.proto.StructBattlePB.BattleRecordTroopEventPB;
import com.yorha.proto.StructBattle.BattleRecordTroopEvent;

/**
 * <AUTHOR> auto gen
 */
public class BattleRecordTroopEventListProp extends AbstractListNode<BattleRecordTroopEventProp> {
    /**
     * Create a BattleRecordTroopEventListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public BattleRecordTroopEventListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to BattleRecordTroopEventListProp
     *
     * @return new object
     */
    @Override
    public BattleRecordTroopEventProp addEmptyValue() {
        final BattleRecordTroopEventProp newProp = new BattleRecordTroopEventProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordTroopEventListPB.Builder getCopyCsBuilder() {
        final BattleRecordTroopEventListPB.Builder builder = BattleRecordTroopEventListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(BattleRecordTroopEventListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return BattleRecordTroopEventListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final BattleRecordTroopEventProp v : this) {
            final BattleRecordTroopEventPB.Builder itemBuilder = BattleRecordTroopEventPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return BattleRecordTroopEventListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(BattleRecordTroopEventListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return BattleRecordTroopEventListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(BattleRecordTroopEventListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (BattleRecordTroopEventPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return BattleRecordTroopEventListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(BattleRecordTroopEventListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordTroopEventList.Builder getCopyDbBuilder() {
        final BattleRecordTroopEventList.Builder builder = BattleRecordTroopEventList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(BattleRecordTroopEventList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return BattleRecordTroopEventListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final BattleRecordTroopEventProp v : this) {
            final BattleRecordTroopEvent.Builder itemBuilder = BattleRecordTroopEvent.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return BattleRecordTroopEventListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(BattleRecordTroopEventList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return BattleRecordTroopEventListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(BattleRecordTroopEventList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (BattleRecordTroopEvent v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return BattleRecordTroopEventListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(BattleRecordTroopEventList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordTroopEventList.Builder getCopySsBuilder() {
        final BattleRecordTroopEventList.Builder builder = BattleRecordTroopEventList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(BattleRecordTroopEventList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return BattleRecordTroopEventListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final BattleRecordTroopEventProp v : this) {
            final BattleRecordTroopEvent.Builder itemBuilder = BattleRecordTroopEvent.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return BattleRecordTroopEventListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(BattleRecordTroopEventList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return BattleRecordTroopEventListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(BattleRecordTroopEventList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (BattleRecordTroopEvent v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return BattleRecordTroopEventListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(BattleRecordTroopEventList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        BattleRecordTroopEventList.Builder builder = BattleRecordTroopEventList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
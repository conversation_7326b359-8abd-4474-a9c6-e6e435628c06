package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.MapBuilding.ConstructInfo;
import com.yorha.proto.MapBuildingPB.ConstructInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class ConstructInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TYPE = 0;
    public static final int FIELD_INDEX_CURRENTDURABILITY = 1;
    public static final int FIELD_INDEX_MAXDURABILITY = 2;
    public static final int FIELD_INDEX_ISONFIRE = 3;
    public static final int FIELD_INDEX_ISCONNECTEDTOCOMMANDNET = 4;
    public static final int FIELD_INDEX_BEFOREREBUILDTEMPLATEID = 5;
    public static final int FIELD_INDEX_AFFECTEDBYWHICHMAINBASE = 6;
    public static final int FIELD_INDEX_NOMAINBASE = 7;
    public static final int FIELD_INDEX_STARTSTAFFID = 8;
    public static final int FIELD_INDEX_STARTPLAYERNAME = 9;

    public static final int FIELD_COUNT = 10;

    private long markBits0 = 0L;

    private MapBuildingType type = MapBuildingType.forNumber(0);
    private int currentDurability = Constant.DEFAULT_INT_VALUE;
    private int maxDurability = Constant.DEFAULT_INT_VALUE;
    private boolean isOnFire = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean isConnectedToCommandNet = Constant.DEFAULT_BOOLEAN_VALUE;
    private int beforeRebuildTemplateId = Constant.DEFAULT_INT_VALUE;
    private int affectedByWhichMainBase = Constant.DEFAULT_INT_VALUE;
    private int noMainBase = Constant.DEFAULT_INT_VALUE;
    private int startStaffId = Constant.DEFAULT_INT_VALUE;
    private String startPlayerName = Constant.DEFAULT_STR_VALUE;

    public ConstructInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ConstructInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get type
     *
     * @return type value
     */
    public MapBuildingType getType() {
        return this.type;
    }

    /**
     * set type && set marked
     *
     * @param type new value
     * @return current object
     */
    public ConstructInfoProp setType(MapBuildingType type) {
        if (type == null) {
            throw new NullPointerException();
        }
        if (this.type != type) {
            this.mark(FIELD_INDEX_TYPE);
            this.type = type;
        }
        return this;
    }

    /**
     * inner set type
     *
     * @param type new value
     */
    private void innerSetType(MapBuildingType type) {
        this.type = type;
    }

    /**
     * get currentDurability
     *
     * @return currentDurability value
     */
    public int getCurrentDurability() {
        return this.currentDurability;
    }

    /**
     * set currentDurability && set marked
     *
     * @param currentDurability new value
     * @return current object
     */
    public ConstructInfoProp setCurrentDurability(int currentDurability) {
        if (this.currentDurability != currentDurability) {
            this.mark(FIELD_INDEX_CURRENTDURABILITY);
            this.currentDurability = currentDurability;
        }
        return this;
    }

    /**
     * inner set currentDurability
     *
     * @param currentDurability new value
     */
    private void innerSetCurrentDurability(int currentDurability) {
        this.currentDurability = currentDurability;
    }

    /**
     * get maxDurability
     *
     * @return maxDurability value
     */
    public int getMaxDurability() {
        return this.maxDurability;
    }

    /**
     * set maxDurability && set marked
     *
     * @param maxDurability new value
     * @return current object
     */
    public ConstructInfoProp setMaxDurability(int maxDurability) {
        if (this.maxDurability != maxDurability) {
            this.mark(FIELD_INDEX_MAXDURABILITY);
            this.maxDurability = maxDurability;
        }
        return this;
    }

    /**
     * inner set maxDurability
     *
     * @param maxDurability new value
     */
    private void innerSetMaxDurability(int maxDurability) {
        this.maxDurability = maxDurability;
    }

    /**
     * get isOnFire
     *
     * @return isOnFire value
     */
    public boolean getIsOnFire() {
        return this.isOnFire;
    }

    /**
     * set isOnFire && set marked
     *
     * @param isOnFire new value
     * @return current object
     */
    public ConstructInfoProp setIsOnFire(boolean isOnFire) {
        if (this.isOnFire != isOnFire) {
            this.mark(FIELD_INDEX_ISONFIRE);
            this.isOnFire = isOnFire;
        }
        return this;
    }

    /**
     * inner set isOnFire
     *
     * @param isOnFire new value
     */
    private void innerSetIsOnFire(boolean isOnFire) {
        this.isOnFire = isOnFire;
    }

    /**
     * get isConnectedToCommandNet
     *
     * @return isConnectedToCommandNet value
     */
    public boolean getIsConnectedToCommandNet() {
        return this.isConnectedToCommandNet;
    }

    /**
     * set isConnectedToCommandNet && set marked
     *
     * @param isConnectedToCommandNet new value
     * @return current object
     */
    public ConstructInfoProp setIsConnectedToCommandNet(boolean isConnectedToCommandNet) {
        if (this.isConnectedToCommandNet != isConnectedToCommandNet) {
            this.mark(FIELD_INDEX_ISCONNECTEDTOCOMMANDNET);
            this.isConnectedToCommandNet = isConnectedToCommandNet;
        }
        return this;
    }

    /**
     * inner set isConnectedToCommandNet
     *
     * @param isConnectedToCommandNet new value
     */
    private void innerSetIsConnectedToCommandNet(boolean isConnectedToCommandNet) {
        this.isConnectedToCommandNet = isConnectedToCommandNet;
    }

    /**
     * get beforeRebuildTemplateId
     *
     * @return beforeRebuildTemplateId value
     */
    public int getBeforeRebuildTemplateId() {
        return this.beforeRebuildTemplateId;
    }

    /**
     * set beforeRebuildTemplateId && set marked
     *
     * @param beforeRebuildTemplateId new value
     * @return current object
     */
    public ConstructInfoProp setBeforeRebuildTemplateId(int beforeRebuildTemplateId) {
        if (this.beforeRebuildTemplateId != beforeRebuildTemplateId) {
            this.mark(FIELD_INDEX_BEFOREREBUILDTEMPLATEID);
            this.beforeRebuildTemplateId = beforeRebuildTemplateId;
        }
        return this;
    }

    /**
     * inner set beforeRebuildTemplateId
     *
     * @param beforeRebuildTemplateId new value
     */
    private void innerSetBeforeRebuildTemplateId(int beforeRebuildTemplateId) {
        this.beforeRebuildTemplateId = beforeRebuildTemplateId;
    }

    /**
     * get affectedByWhichMainBase
     *
     * @return affectedByWhichMainBase value
     */
    public int getAffectedByWhichMainBase() {
        return this.affectedByWhichMainBase;
    }

    /**
     * set affectedByWhichMainBase && set marked
     *
     * @param affectedByWhichMainBase new value
     * @return current object
     */
    public ConstructInfoProp setAffectedByWhichMainBase(int affectedByWhichMainBase) {
        if (this.affectedByWhichMainBase != affectedByWhichMainBase) {
            this.mark(FIELD_INDEX_AFFECTEDBYWHICHMAINBASE);
            this.affectedByWhichMainBase = affectedByWhichMainBase;
        }
        return this;
    }

    /**
     * inner set affectedByWhichMainBase
     *
     * @param affectedByWhichMainBase new value
     */
    private void innerSetAffectedByWhichMainBase(int affectedByWhichMainBase) {
        this.affectedByWhichMainBase = affectedByWhichMainBase;
    }

    /**
     * get noMainBase
     *
     * @return noMainBase value
     */
    public int getNoMainBase() {
        return this.noMainBase;
    }

    /**
     * set noMainBase && set marked
     *
     * @param noMainBase new value
     * @return current object
     */
    public ConstructInfoProp setNoMainBase(int noMainBase) {
        if (this.noMainBase != noMainBase) {
            this.mark(FIELD_INDEX_NOMAINBASE);
            this.noMainBase = noMainBase;
        }
        return this;
    }

    /**
     * inner set noMainBase
     *
     * @param noMainBase new value
     */
    private void innerSetNoMainBase(int noMainBase) {
        this.noMainBase = noMainBase;
    }

    /**
     * get startStaffId
     *
     * @return startStaffId value
     */
    public int getStartStaffId() {
        return this.startStaffId;
    }

    /**
     * set startStaffId && set marked
     *
     * @param startStaffId new value
     * @return current object
     */
    public ConstructInfoProp setStartStaffId(int startStaffId) {
        if (this.startStaffId != startStaffId) {
            this.mark(FIELD_INDEX_STARTSTAFFID);
            this.startStaffId = startStaffId;
        }
        return this;
    }

    /**
     * inner set startStaffId
     *
     * @param startStaffId new value
     */
    private void innerSetStartStaffId(int startStaffId) {
        this.startStaffId = startStaffId;
    }

    /**
     * get startPlayerName
     *
     * @return startPlayerName value
     */
    public String getStartPlayerName() {
        return this.startPlayerName;
    }

    /**
     * set startPlayerName && set marked
     *
     * @param startPlayerName new value
     * @return current object
     */
    public ConstructInfoProp setStartPlayerName(String startPlayerName) {
        if (startPlayerName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.startPlayerName, startPlayerName)) {
            this.mark(FIELD_INDEX_STARTPLAYERNAME);
            this.startPlayerName = startPlayerName;
        }
        return this;
    }

    /**
     * inner set startPlayerName
     *
     * @param startPlayerName new value
     */
    private void innerSetStartPlayerName(String startPlayerName) {
        this.startPlayerName = startPlayerName;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ConstructInfoPB.Builder getCopyCsBuilder() {
        final ConstructInfoPB.Builder builder = ConstructInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ConstructInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != MapBuildingType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getCurrentDurability() != 0) {
            builder.setCurrentDurability(this.getCurrentDurability());
            fieldCnt++;
        }  else if (builder.hasCurrentDurability()) {
            // 清理CurrentDurability
            builder.clearCurrentDurability();
            fieldCnt++;
        }
        if (this.getMaxDurability() != 0) {
            builder.setMaxDurability(this.getMaxDurability());
            fieldCnt++;
        }  else if (builder.hasMaxDurability()) {
            // 清理MaxDurability
            builder.clearMaxDurability();
            fieldCnt++;
        }
        if (this.getIsOnFire()) {
            builder.setIsOnFire(this.getIsOnFire());
            fieldCnt++;
        }  else if (builder.hasIsOnFire()) {
            // 清理IsOnFire
            builder.clearIsOnFire();
            fieldCnt++;
        }
        if (this.getIsConnectedToCommandNet()) {
            builder.setIsConnectedToCommandNet(this.getIsConnectedToCommandNet());
            fieldCnt++;
        }  else if (builder.hasIsConnectedToCommandNet()) {
            // 清理IsConnectedToCommandNet
            builder.clearIsConnectedToCommandNet();
            fieldCnt++;
        }
        if (this.getBeforeRebuildTemplateId() != 0) {
            builder.setBeforeRebuildTemplateId(this.getBeforeRebuildTemplateId());
            fieldCnt++;
        }  else if (builder.hasBeforeRebuildTemplateId()) {
            // 清理BeforeRebuildTemplateId
            builder.clearBeforeRebuildTemplateId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ConstructInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURRENTDURABILITY)) {
            builder.setCurrentDurability(this.getCurrentDurability());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXDURABILITY)) {
            builder.setMaxDurability(this.getMaxDurability());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISONFIRE)) {
            builder.setIsOnFire(this.getIsOnFire());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISCONNECTEDTOCOMMANDNET)) {
            builder.setIsConnectedToCommandNet(this.getIsConnectedToCommandNet());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BEFOREREBUILDTEMPLATEID)) {
            builder.setBeforeRebuildTemplateId(this.getBeforeRebuildTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ConstructInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURRENTDURABILITY)) {
            builder.setCurrentDurability(this.getCurrentDurability());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXDURABILITY)) {
            builder.setMaxDurability(this.getMaxDurability());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISONFIRE)) {
            builder.setIsOnFire(this.getIsOnFire());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISCONNECTEDTOCOMMANDNET)) {
            builder.setIsConnectedToCommandNet(this.getIsConnectedToCommandNet());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BEFOREREBUILDTEMPLATEID)) {
            builder.setBeforeRebuildTemplateId(this.getBeforeRebuildTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ConstructInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(MapBuildingType.forNumber(0));
        }
        if (proto.hasCurrentDurability()) {
            this.innerSetCurrentDurability(proto.getCurrentDurability());
        } else {
            this.innerSetCurrentDurability(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMaxDurability()) {
            this.innerSetMaxDurability(proto.getMaxDurability());
        } else {
            this.innerSetMaxDurability(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIsOnFire()) {
            this.innerSetIsOnFire(proto.getIsOnFire());
        } else {
            this.innerSetIsOnFire(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasIsConnectedToCommandNet()) {
            this.innerSetIsConnectedToCommandNet(proto.getIsConnectedToCommandNet());
        } else {
            this.innerSetIsConnectedToCommandNet(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasBeforeRebuildTemplateId()) {
            this.innerSetBeforeRebuildTemplateId(proto.getBeforeRebuildTemplateId());
        } else {
            this.innerSetBeforeRebuildTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ConstructInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ConstructInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasCurrentDurability()) {
            this.setCurrentDurability(proto.getCurrentDurability());
            fieldCnt++;
        }
        if (proto.hasMaxDurability()) {
            this.setMaxDurability(proto.getMaxDurability());
            fieldCnt++;
        }
        if (proto.hasIsOnFire()) {
            this.setIsOnFire(proto.getIsOnFire());
            fieldCnt++;
        }
        if (proto.hasIsConnectedToCommandNet()) {
            this.setIsConnectedToCommandNet(proto.getIsConnectedToCommandNet());
            fieldCnt++;
        }
        if (proto.hasBeforeRebuildTemplateId()) {
            this.setBeforeRebuildTemplateId(proto.getBeforeRebuildTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ConstructInfo.Builder getCopyDbBuilder() {
        final ConstructInfo.Builder builder = ConstructInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ConstructInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != MapBuildingType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getCurrentDurability() != 0) {
            builder.setCurrentDurability(this.getCurrentDurability());
            fieldCnt++;
        }  else if (builder.hasCurrentDurability()) {
            // 清理CurrentDurability
            builder.clearCurrentDurability();
            fieldCnt++;
        }
        if (this.getMaxDurability() != 0) {
            builder.setMaxDurability(this.getMaxDurability());
            fieldCnt++;
        }  else if (builder.hasMaxDurability()) {
            // 清理MaxDurability
            builder.clearMaxDurability();
            fieldCnt++;
        }
        if (this.getIsOnFire()) {
            builder.setIsOnFire(this.getIsOnFire());
            fieldCnt++;
        }  else if (builder.hasIsOnFire()) {
            // 清理IsOnFire
            builder.clearIsOnFire();
            fieldCnt++;
        }
        if (this.getIsConnectedToCommandNet()) {
            builder.setIsConnectedToCommandNet(this.getIsConnectedToCommandNet());
            fieldCnt++;
        }  else if (builder.hasIsConnectedToCommandNet()) {
            // 清理IsConnectedToCommandNet
            builder.clearIsConnectedToCommandNet();
            fieldCnt++;
        }
        if (this.getBeforeRebuildTemplateId() != 0) {
            builder.setBeforeRebuildTemplateId(this.getBeforeRebuildTemplateId());
            fieldCnt++;
        }  else if (builder.hasBeforeRebuildTemplateId()) {
            // 清理BeforeRebuildTemplateId
            builder.clearBeforeRebuildTemplateId();
            fieldCnt++;
        }
        if (this.getAffectedByWhichMainBase() != 0) {
            builder.setAffectedByWhichMainBase(this.getAffectedByWhichMainBase());
            fieldCnt++;
        }  else if (builder.hasAffectedByWhichMainBase()) {
            // 清理AffectedByWhichMainBase
            builder.clearAffectedByWhichMainBase();
            fieldCnt++;
        }
        if (this.getNoMainBase() != 0) {
            builder.setNoMainBase(this.getNoMainBase());
            fieldCnt++;
        }  else if (builder.hasNoMainBase()) {
            // 清理NoMainBase
            builder.clearNoMainBase();
            fieldCnt++;
        }
        if (this.getStartStaffId() != 0) {
            builder.setStartStaffId(this.getStartStaffId());
            fieldCnt++;
        }  else if (builder.hasStartStaffId()) {
            // 清理StartStaffId
            builder.clearStartStaffId();
            fieldCnt++;
        }
        if (!this.getStartPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setStartPlayerName(this.getStartPlayerName());
            fieldCnt++;
        }  else if (builder.hasStartPlayerName()) {
            // 清理StartPlayerName
            builder.clearStartPlayerName();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ConstructInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURRENTDURABILITY)) {
            builder.setCurrentDurability(this.getCurrentDurability());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXDURABILITY)) {
            builder.setMaxDurability(this.getMaxDurability());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISONFIRE)) {
            builder.setIsOnFire(this.getIsOnFire());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISCONNECTEDTOCOMMANDNET)) {
            builder.setIsConnectedToCommandNet(this.getIsConnectedToCommandNet());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BEFOREREBUILDTEMPLATEID)) {
            builder.setBeforeRebuildTemplateId(this.getBeforeRebuildTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_AFFECTEDBYWHICHMAINBASE)) {
            builder.setAffectedByWhichMainBase(this.getAffectedByWhichMainBase());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NOMAINBASE)) {
            builder.setNoMainBase(this.getNoMainBase());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTSTAFFID)) {
            builder.setStartStaffId(this.getStartStaffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTPLAYERNAME)) {
            builder.setStartPlayerName(this.getStartPlayerName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ConstructInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(MapBuildingType.forNumber(0));
        }
        if (proto.hasCurrentDurability()) {
            this.innerSetCurrentDurability(proto.getCurrentDurability());
        } else {
            this.innerSetCurrentDurability(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMaxDurability()) {
            this.innerSetMaxDurability(proto.getMaxDurability());
        } else {
            this.innerSetMaxDurability(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIsOnFire()) {
            this.innerSetIsOnFire(proto.getIsOnFire());
        } else {
            this.innerSetIsOnFire(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasIsConnectedToCommandNet()) {
            this.innerSetIsConnectedToCommandNet(proto.getIsConnectedToCommandNet());
        } else {
            this.innerSetIsConnectedToCommandNet(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasBeforeRebuildTemplateId()) {
            this.innerSetBeforeRebuildTemplateId(proto.getBeforeRebuildTemplateId());
        } else {
            this.innerSetBeforeRebuildTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAffectedByWhichMainBase()) {
            this.innerSetAffectedByWhichMainBase(proto.getAffectedByWhichMainBase());
        } else {
            this.innerSetAffectedByWhichMainBase(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNoMainBase()) {
            this.innerSetNoMainBase(proto.getNoMainBase());
        } else {
            this.innerSetNoMainBase(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartStaffId()) {
            this.innerSetStartStaffId(proto.getStartStaffId());
        } else {
            this.innerSetStartStaffId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartPlayerName()) {
            this.innerSetStartPlayerName(proto.getStartPlayerName());
        } else {
            this.innerSetStartPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return ConstructInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ConstructInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasCurrentDurability()) {
            this.setCurrentDurability(proto.getCurrentDurability());
            fieldCnt++;
        }
        if (proto.hasMaxDurability()) {
            this.setMaxDurability(proto.getMaxDurability());
            fieldCnt++;
        }
        if (proto.hasIsOnFire()) {
            this.setIsOnFire(proto.getIsOnFire());
            fieldCnt++;
        }
        if (proto.hasIsConnectedToCommandNet()) {
            this.setIsConnectedToCommandNet(proto.getIsConnectedToCommandNet());
            fieldCnt++;
        }
        if (proto.hasBeforeRebuildTemplateId()) {
            this.setBeforeRebuildTemplateId(proto.getBeforeRebuildTemplateId());
            fieldCnt++;
        }
        if (proto.hasAffectedByWhichMainBase()) {
            this.setAffectedByWhichMainBase(proto.getAffectedByWhichMainBase());
            fieldCnt++;
        }
        if (proto.hasNoMainBase()) {
            this.setNoMainBase(proto.getNoMainBase());
            fieldCnt++;
        }
        if (proto.hasStartStaffId()) {
            this.setStartStaffId(proto.getStartStaffId());
            fieldCnt++;
        }
        if (proto.hasStartPlayerName()) {
            this.setStartPlayerName(proto.getStartPlayerName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ConstructInfo.Builder getCopySsBuilder() {
        final ConstructInfo.Builder builder = ConstructInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ConstructInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != MapBuildingType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getCurrentDurability() != 0) {
            builder.setCurrentDurability(this.getCurrentDurability());
            fieldCnt++;
        }  else if (builder.hasCurrentDurability()) {
            // 清理CurrentDurability
            builder.clearCurrentDurability();
            fieldCnt++;
        }
        if (this.getMaxDurability() != 0) {
            builder.setMaxDurability(this.getMaxDurability());
            fieldCnt++;
        }  else if (builder.hasMaxDurability()) {
            // 清理MaxDurability
            builder.clearMaxDurability();
            fieldCnt++;
        }
        if (this.getIsOnFire()) {
            builder.setIsOnFire(this.getIsOnFire());
            fieldCnt++;
        }  else if (builder.hasIsOnFire()) {
            // 清理IsOnFire
            builder.clearIsOnFire();
            fieldCnt++;
        }
        if (this.getIsConnectedToCommandNet()) {
            builder.setIsConnectedToCommandNet(this.getIsConnectedToCommandNet());
            fieldCnt++;
        }  else if (builder.hasIsConnectedToCommandNet()) {
            // 清理IsConnectedToCommandNet
            builder.clearIsConnectedToCommandNet();
            fieldCnt++;
        }
        if (this.getBeforeRebuildTemplateId() != 0) {
            builder.setBeforeRebuildTemplateId(this.getBeforeRebuildTemplateId());
            fieldCnt++;
        }  else if (builder.hasBeforeRebuildTemplateId()) {
            // 清理BeforeRebuildTemplateId
            builder.clearBeforeRebuildTemplateId();
            fieldCnt++;
        }
        if (this.getAffectedByWhichMainBase() != 0) {
            builder.setAffectedByWhichMainBase(this.getAffectedByWhichMainBase());
            fieldCnt++;
        }  else if (builder.hasAffectedByWhichMainBase()) {
            // 清理AffectedByWhichMainBase
            builder.clearAffectedByWhichMainBase();
            fieldCnt++;
        }
        if (this.getNoMainBase() != 0) {
            builder.setNoMainBase(this.getNoMainBase());
            fieldCnt++;
        }  else if (builder.hasNoMainBase()) {
            // 清理NoMainBase
            builder.clearNoMainBase();
            fieldCnt++;
        }
        if (this.getStartStaffId() != 0) {
            builder.setStartStaffId(this.getStartStaffId());
            fieldCnt++;
        }  else if (builder.hasStartStaffId()) {
            // 清理StartStaffId
            builder.clearStartStaffId();
            fieldCnt++;
        }
        if (!this.getStartPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setStartPlayerName(this.getStartPlayerName());
            fieldCnt++;
        }  else if (builder.hasStartPlayerName()) {
            // 清理StartPlayerName
            builder.clearStartPlayerName();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ConstructInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURRENTDURABILITY)) {
            builder.setCurrentDurability(this.getCurrentDurability());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXDURABILITY)) {
            builder.setMaxDurability(this.getMaxDurability());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISONFIRE)) {
            builder.setIsOnFire(this.getIsOnFire());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISCONNECTEDTOCOMMANDNET)) {
            builder.setIsConnectedToCommandNet(this.getIsConnectedToCommandNet());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BEFOREREBUILDTEMPLATEID)) {
            builder.setBeforeRebuildTemplateId(this.getBeforeRebuildTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_AFFECTEDBYWHICHMAINBASE)) {
            builder.setAffectedByWhichMainBase(this.getAffectedByWhichMainBase());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NOMAINBASE)) {
            builder.setNoMainBase(this.getNoMainBase());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTSTAFFID)) {
            builder.setStartStaffId(this.getStartStaffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTPLAYERNAME)) {
            builder.setStartPlayerName(this.getStartPlayerName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ConstructInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(MapBuildingType.forNumber(0));
        }
        if (proto.hasCurrentDurability()) {
            this.innerSetCurrentDurability(proto.getCurrentDurability());
        } else {
            this.innerSetCurrentDurability(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMaxDurability()) {
            this.innerSetMaxDurability(proto.getMaxDurability());
        } else {
            this.innerSetMaxDurability(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIsOnFire()) {
            this.innerSetIsOnFire(proto.getIsOnFire());
        } else {
            this.innerSetIsOnFire(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasIsConnectedToCommandNet()) {
            this.innerSetIsConnectedToCommandNet(proto.getIsConnectedToCommandNet());
        } else {
            this.innerSetIsConnectedToCommandNet(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasBeforeRebuildTemplateId()) {
            this.innerSetBeforeRebuildTemplateId(proto.getBeforeRebuildTemplateId());
        } else {
            this.innerSetBeforeRebuildTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAffectedByWhichMainBase()) {
            this.innerSetAffectedByWhichMainBase(proto.getAffectedByWhichMainBase());
        } else {
            this.innerSetAffectedByWhichMainBase(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNoMainBase()) {
            this.innerSetNoMainBase(proto.getNoMainBase());
        } else {
            this.innerSetNoMainBase(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartStaffId()) {
            this.innerSetStartStaffId(proto.getStartStaffId());
        } else {
            this.innerSetStartStaffId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartPlayerName()) {
            this.innerSetStartPlayerName(proto.getStartPlayerName());
        } else {
            this.innerSetStartPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return ConstructInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ConstructInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasCurrentDurability()) {
            this.setCurrentDurability(proto.getCurrentDurability());
            fieldCnt++;
        }
        if (proto.hasMaxDurability()) {
            this.setMaxDurability(proto.getMaxDurability());
            fieldCnt++;
        }
        if (proto.hasIsOnFire()) {
            this.setIsOnFire(proto.getIsOnFire());
            fieldCnt++;
        }
        if (proto.hasIsConnectedToCommandNet()) {
            this.setIsConnectedToCommandNet(proto.getIsConnectedToCommandNet());
            fieldCnt++;
        }
        if (proto.hasBeforeRebuildTemplateId()) {
            this.setBeforeRebuildTemplateId(proto.getBeforeRebuildTemplateId());
            fieldCnt++;
        }
        if (proto.hasAffectedByWhichMainBase()) {
            this.setAffectedByWhichMainBase(proto.getAffectedByWhichMainBase());
            fieldCnt++;
        }
        if (proto.hasNoMainBase()) {
            this.setNoMainBase(proto.getNoMainBase());
            fieldCnt++;
        }
        if (proto.hasStartStaffId()) {
            this.setStartStaffId(proto.getStartStaffId());
            fieldCnt++;
        }
        if (proto.hasStartPlayerName()) {
            this.setStartPlayerName(proto.getStartPlayerName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ConstructInfo.Builder builder = ConstructInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ConstructInfoProp)) {
            return false;
        }
        final ConstructInfoProp otherNode = (ConstructInfoProp) node;
        if (this.type != otherNode.type) {
            return false;
        }
        if (this.currentDurability != otherNode.currentDurability) {
            return false;
        }
        if (this.maxDurability != otherNode.maxDurability) {
            return false;
        }
        if (this.isOnFire != otherNode.isOnFire) {
            return false;
        }
        if (this.isConnectedToCommandNet != otherNode.isConnectedToCommandNet) {
            return false;
        }
        if (this.beforeRebuildTemplateId != otherNode.beforeRebuildTemplateId) {
            return false;
        }
        if (this.affectedByWhichMainBase != otherNode.affectedByWhichMainBase) {
            return false;
        }
        if (this.noMainBase != otherNode.noMainBase) {
            return false;
        }
        if (this.startStaffId != otherNode.startStaffId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.startPlayerName, otherNode.startPlayerName)) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 54;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerVipModel;
import com.yorha.proto.Basic;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.PlayerVipModelPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerVipModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_VIPEXP = 0;
    public static final int FIELD_INDEX_LEVELTOEXCLUSIVEBOXINFO = 1;
    public static final int FIELD_INDEX_VIPCUMLOGINDAYS = 2;
    public static final int FIELD_INDEX_LASTUPDATECUMLOGINTSMS = 3;
    public static final int FIELD_INDEX_BOUGHTGOODS = 4;
    public static final int FIELD_INDEX_LASTGETPRIVILEGEBOXTSMS = 5;
    public static final int FIELD_INDEX_LASTGETDAILYBOXTSMS = 6;
    public static final int FIELD_INDEX_EXTRACANGETDAILYBOXLEVEL = 7;
    public static final int FIELD_INDEX_VIPBOXSELECTEDHERO = 8;
    public static final int FIELD_INDEX_VIPSTORESELECTEDHERO = 9;

    public static final int FIELD_COUNT = 10;

    private long markBits0 = 0L;

    private int vipExp = Constant.DEFAULT_INT_VALUE;
    private Int32ExclusiveBoxInfoMapProp levelToExclusiveBoxInfo = null;
    private int vipCumLoginDays = Constant.DEFAULT_INT_VALUE;
    private long lastUpdateCumLoginTsMs = Constant.DEFAULT_LONG_VALUE;
    private Int32BoughtGoodsInfoMapProp boughtGoods = null;
    private long lastGetPrivilegeBoxTsMs = Constant.DEFAULT_LONG_VALUE;
    private long lastGetDailyBoxTsMs = Constant.DEFAULT_LONG_VALUE;
    private Int32ListProp extraCanGetDailyBoxLevel = null;
    private Int32VipSelectedHeroInfoMapProp vipBoxSelectedHero = null;
    private Int32VipSelectedHeroInfoMapProp vipStoreSelectedHero = null;

    public PlayerVipModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerVipModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get vipExp
     *
     * @return vipExp value
     */
    public int getVipExp() {
        return this.vipExp;
    }

    /**
     * set vipExp && set marked
     *
     * @param vipExp new value
     * @return current object
     */
    public PlayerVipModelProp setVipExp(int vipExp) {
        if (this.vipExp != vipExp) {
            this.mark(FIELD_INDEX_VIPEXP);
            this.vipExp = vipExp;
        }
        return this;
    }

    /**
     * inner set vipExp
     *
     * @param vipExp new value
     */
    private void innerSetVipExp(int vipExp) {
        this.vipExp = vipExp;
    }

    /**
     * get levelToExclusiveBoxInfo
     *
     * @return levelToExclusiveBoxInfo value
     */
    public Int32ExclusiveBoxInfoMapProp getLevelToExclusiveBoxInfo() {
        if (this.levelToExclusiveBoxInfo == null) {
            this.levelToExclusiveBoxInfo = new Int32ExclusiveBoxInfoMapProp(this, FIELD_INDEX_LEVELTOEXCLUSIVEBOXINFO);
        }
        return this.levelToExclusiveBoxInfo;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putLevelToExclusiveBoxInfoV(ExclusiveBoxInfoProp v) {
        this.getLevelToExclusiveBoxInfo().put(v.getLevel(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ExclusiveBoxInfoProp addEmptyLevelToExclusiveBoxInfo(Integer k) {
        return this.getLevelToExclusiveBoxInfo().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getLevelToExclusiveBoxInfoSize() {
        if (this.levelToExclusiveBoxInfo == null) {
            return 0;
        }
        return this.levelToExclusiveBoxInfo.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isLevelToExclusiveBoxInfoEmpty() {
        if (this.levelToExclusiveBoxInfo == null) {
            return true;
        }
        return this.levelToExclusiveBoxInfo.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ExclusiveBoxInfoProp getLevelToExclusiveBoxInfoV(Integer k) {
        if (this.levelToExclusiveBoxInfo == null || !this.levelToExclusiveBoxInfo.containsKey(k)) {
            return null;
        }
        return this.levelToExclusiveBoxInfo.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearLevelToExclusiveBoxInfo() {
        if (this.levelToExclusiveBoxInfo != null) {
            this.levelToExclusiveBoxInfo.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeLevelToExclusiveBoxInfoV(Integer k) {
        if (this.levelToExclusiveBoxInfo != null) {
            this.levelToExclusiveBoxInfo.remove(k);
        }
    }
    /**
     * get vipCumLoginDays
     *
     * @return vipCumLoginDays value
     */
    public int getVipCumLoginDays() {
        return this.vipCumLoginDays;
    }

    /**
     * set vipCumLoginDays && set marked
     *
     * @param vipCumLoginDays new value
     * @return current object
     */
    public PlayerVipModelProp setVipCumLoginDays(int vipCumLoginDays) {
        if (this.vipCumLoginDays != vipCumLoginDays) {
            this.mark(FIELD_INDEX_VIPCUMLOGINDAYS);
            this.vipCumLoginDays = vipCumLoginDays;
        }
        return this;
    }

    /**
     * inner set vipCumLoginDays
     *
     * @param vipCumLoginDays new value
     */
    private void innerSetVipCumLoginDays(int vipCumLoginDays) {
        this.vipCumLoginDays = vipCumLoginDays;
    }

    /**
     * get lastUpdateCumLoginTsMs
     *
     * @return lastUpdateCumLoginTsMs value
     */
    public long getLastUpdateCumLoginTsMs() {
        return this.lastUpdateCumLoginTsMs;
    }

    /**
     * set lastUpdateCumLoginTsMs && set marked
     *
     * @param lastUpdateCumLoginTsMs new value
     * @return current object
     */
    public PlayerVipModelProp setLastUpdateCumLoginTsMs(long lastUpdateCumLoginTsMs) {
        if (this.lastUpdateCumLoginTsMs != lastUpdateCumLoginTsMs) {
            this.mark(FIELD_INDEX_LASTUPDATECUMLOGINTSMS);
            this.lastUpdateCumLoginTsMs = lastUpdateCumLoginTsMs;
        }
        return this;
    }

    /**
     * inner set lastUpdateCumLoginTsMs
     *
     * @param lastUpdateCumLoginTsMs new value
     */
    private void innerSetLastUpdateCumLoginTsMs(long lastUpdateCumLoginTsMs) {
        this.lastUpdateCumLoginTsMs = lastUpdateCumLoginTsMs;
    }

    /**
     * get boughtGoods
     *
     * @return boughtGoods value
     */
    public Int32BoughtGoodsInfoMapProp getBoughtGoods() {
        if (this.boughtGoods == null) {
            this.boughtGoods = new Int32BoughtGoodsInfoMapProp(this, FIELD_INDEX_BOUGHTGOODS);
        }
        return this.boughtGoods;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putBoughtGoodsV(BoughtGoodsInfoProp v) {
        this.getBoughtGoods().put(v.getConfigGoodsId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public BoughtGoodsInfoProp addEmptyBoughtGoods(Integer k) {
        return this.getBoughtGoods().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getBoughtGoodsSize() {
        if (this.boughtGoods == null) {
            return 0;
        }
        return this.boughtGoods.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isBoughtGoodsEmpty() {
        if (this.boughtGoods == null) {
            return true;
        }
        return this.boughtGoods.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public BoughtGoodsInfoProp getBoughtGoodsV(Integer k) {
        if (this.boughtGoods == null || !this.boughtGoods.containsKey(k)) {
            return null;
        }
        return this.boughtGoods.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearBoughtGoods() {
        if (this.boughtGoods != null) {
            this.boughtGoods.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeBoughtGoodsV(Integer k) {
        if (this.boughtGoods != null) {
            this.boughtGoods.remove(k);
        }
    }
    /**
     * get lastGetPrivilegeBoxTsMs
     *
     * @return lastGetPrivilegeBoxTsMs value
     */
    public long getLastGetPrivilegeBoxTsMs() {
        return this.lastGetPrivilegeBoxTsMs;
    }

    /**
     * set lastGetPrivilegeBoxTsMs && set marked
     *
     * @param lastGetPrivilegeBoxTsMs new value
     * @return current object
     */
    public PlayerVipModelProp setLastGetPrivilegeBoxTsMs(long lastGetPrivilegeBoxTsMs) {
        if (this.lastGetPrivilegeBoxTsMs != lastGetPrivilegeBoxTsMs) {
            this.mark(FIELD_INDEX_LASTGETPRIVILEGEBOXTSMS);
            this.lastGetPrivilegeBoxTsMs = lastGetPrivilegeBoxTsMs;
        }
        return this;
    }

    /**
     * inner set lastGetPrivilegeBoxTsMs
     *
     * @param lastGetPrivilegeBoxTsMs new value
     */
    private void innerSetLastGetPrivilegeBoxTsMs(long lastGetPrivilegeBoxTsMs) {
        this.lastGetPrivilegeBoxTsMs = lastGetPrivilegeBoxTsMs;
    }

    /**
     * get lastGetDailyBoxTsMs
     *
     * @return lastGetDailyBoxTsMs value
     */
    public long getLastGetDailyBoxTsMs() {
        return this.lastGetDailyBoxTsMs;
    }

    /**
     * set lastGetDailyBoxTsMs && set marked
     *
     * @param lastGetDailyBoxTsMs new value
     * @return current object
     */
    public PlayerVipModelProp setLastGetDailyBoxTsMs(long lastGetDailyBoxTsMs) {
        if (this.lastGetDailyBoxTsMs != lastGetDailyBoxTsMs) {
            this.mark(FIELD_INDEX_LASTGETDAILYBOXTSMS);
            this.lastGetDailyBoxTsMs = lastGetDailyBoxTsMs;
        }
        return this;
    }

    /**
     * inner set lastGetDailyBoxTsMs
     *
     * @param lastGetDailyBoxTsMs new value
     */
    private void innerSetLastGetDailyBoxTsMs(long lastGetDailyBoxTsMs) {
        this.lastGetDailyBoxTsMs = lastGetDailyBoxTsMs;
    }

    /**
     * get extraCanGetDailyBoxLevel
     *
     * @return extraCanGetDailyBoxLevel value
     */
    public Int32ListProp getExtraCanGetDailyBoxLevel() {
        if (this.extraCanGetDailyBoxLevel == null) {
            this.extraCanGetDailyBoxLevel = new Int32ListProp(this, FIELD_INDEX_EXTRACANGETDAILYBOXLEVEL);
        }
        return this.extraCanGetDailyBoxLevel;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addExtraCanGetDailyBoxLevel(Integer v) {
        this.getExtraCanGetDailyBoxLevel().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getExtraCanGetDailyBoxLevelIndex(int index) {
        return this.getExtraCanGetDailyBoxLevel().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeExtraCanGetDailyBoxLevel(Integer v) {
        if (this.extraCanGetDailyBoxLevel == null) {
            return null;
        }
        if(this.extraCanGetDailyBoxLevel.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getExtraCanGetDailyBoxLevelSize() {
        if (this.extraCanGetDailyBoxLevel == null) {
            return 0;
        }
        return this.extraCanGetDailyBoxLevel.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isExtraCanGetDailyBoxLevelEmpty() {
        if (this.extraCanGetDailyBoxLevel == null) {
            return true;
        }
        return this.getExtraCanGetDailyBoxLevel().isEmpty();
    }

    /**
     * clear list
     */
    public void clearExtraCanGetDailyBoxLevel() {
        this.getExtraCanGetDailyBoxLevel().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeExtraCanGetDailyBoxLevelIndex(int index) {
        return this.getExtraCanGetDailyBoxLevel().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setExtraCanGetDailyBoxLevelIndex(int index, Integer v) {
        return this.getExtraCanGetDailyBoxLevel().set(index, v);
    }
    /**
     * get vipBoxSelectedHero
     *
     * @return vipBoxSelectedHero value
     */
    public Int32VipSelectedHeroInfoMapProp getVipBoxSelectedHero() {
        if (this.vipBoxSelectedHero == null) {
            this.vipBoxSelectedHero = new Int32VipSelectedHeroInfoMapProp(this, FIELD_INDEX_VIPBOXSELECTEDHERO);
        }
        return this.vipBoxSelectedHero;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putVipBoxSelectedHeroV(VipSelectedHeroInfoProp v) {
        this.getVipBoxSelectedHero().put(v.getUid(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public VipSelectedHeroInfoProp addEmptyVipBoxSelectedHero(Integer k) {
        return this.getVipBoxSelectedHero().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getVipBoxSelectedHeroSize() {
        if (this.vipBoxSelectedHero == null) {
            return 0;
        }
        return this.vipBoxSelectedHero.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isVipBoxSelectedHeroEmpty() {
        if (this.vipBoxSelectedHero == null) {
            return true;
        }
        return this.vipBoxSelectedHero.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public VipSelectedHeroInfoProp getVipBoxSelectedHeroV(Integer k) {
        if (this.vipBoxSelectedHero == null || !this.vipBoxSelectedHero.containsKey(k)) {
            return null;
        }
        return this.vipBoxSelectedHero.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearVipBoxSelectedHero() {
        if (this.vipBoxSelectedHero != null) {
            this.vipBoxSelectedHero.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeVipBoxSelectedHeroV(Integer k) {
        if (this.vipBoxSelectedHero != null) {
            this.vipBoxSelectedHero.remove(k);
        }
    }
    /**
     * get vipStoreSelectedHero
     *
     * @return vipStoreSelectedHero value
     */
    public Int32VipSelectedHeroInfoMapProp getVipStoreSelectedHero() {
        if (this.vipStoreSelectedHero == null) {
            this.vipStoreSelectedHero = new Int32VipSelectedHeroInfoMapProp(this, FIELD_INDEX_VIPSTORESELECTEDHERO);
        }
        return this.vipStoreSelectedHero;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putVipStoreSelectedHeroV(VipSelectedHeroInfoProp v) {
        this.getVipStoreSelectedHero().put(v.getUid(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public VipSelectedHeroInfoProp addEmptyVipStoreSelectedHero(Integer k) {
        return this.getVipStoreSelectedHero().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getVipStoreSelectedHeroSize() {
        if (this.vipStoreSelectedHero == null) {
            return 0;
        }
        return this.vipStoreSelectedHero.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isVipStoreSelectedHeroEmpty() {
        if (this.vipStoreSelectedHero == null) {
            return true;
        }
        return this.vipStoreSelectedHero.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public VipSelectedHeroInfoProp getVipStoreSelectedHeroV(Integer k) {
        if (this.vipStoreSelectedHero == null || !this.vipStoreSelectedHero.containsKey(k)) {
            return null;
        }
        return this.vipStoreSelectedHero.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearVipStoreSelectedHero() {
        if (this.vipStoreSelectedHero != null) {
            this.vipStoreSelectedHero.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeVipStoreSelectedHeroV(Integer k) {
        if (this.vipStoreSelectedHero != null) {
            this.vipStoreSelectedHero.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerVipModelPB.Builder getCopyCsBuilder() {
        final PlayerVipModelPB.Builder builder = PlayerVipModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerVipModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getVipExp() != 0) {
            builder.setVipExp(this.getVipExp());
            fieldCnt++;
        }  else if (builder.hasVipExp()) {
            // 清理VipExp
            builder.clearVipExp();
            fieldCnt++;
        }
        if (this.levelToExclusiveBoxInfo != null) {
            PlayerPB.Int32ExclusiveBoxInfoMapPB.Builder tmpBuilder = PlayerPB.Int32ExclusiveBoxInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.levelToExclusiveBoxInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLevelToExclusiveBoxInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLevelToExclusiveBoxInfo();
            }
        }  else if (builder.hasLevelToExclusiveBoxInfo()) {
            // 清理LevelToExclusiveBoxInfo
            builder.clearLevelToExclusiveBoxInfo();
            fieldCnt++;
        }
        if (this.getVipCumLoginDays() != 0) {
            builder.setVipCumLoginDays(this.getVipCumLoginDays());
            fieldCnt++;
        }  else if (builder.hasVipCumLoginDays()) {
            // 清理VipCumLoginDays
            builder.clearVipCumLoginDays();
            fieldCnt++;
        }
        if (this.boughtGoods != null) {
            PlayerPB.Int32BoughtGoodsInfoMapPB.Builder tmpBuilder = PlayerPB.Int32BoughtGoodsInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.boughtGoods.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBoughtGoods(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBoughtGoods();
            }
        }  else if (builder.hasBoughtGoods()) {
            // 清理BoughtGoods
            builder.clearBoughtGoods();
            fieldCnt++;
        }
        if (this.getLastGetPrivilegeBoxTsMs() != 0L) {
            builder.setLastGetPrivilegeBoxTsMs(this.getLastGetPrivilegeBoxTsMs());
            fieldCnt++;
        }  else if (builder.hasLastGetPrivilegeBoxTsMs()) {
            // 清理LastGetPrivilegeBoxTsMs
            builder.clearLastGetPrivilegeBoxTsMs();
            fieldCnt++;
        }
        if (this.getLastGetDailyBoxTsMs() != 0L) {
            builder.setLastGetDailyBoxTsMs(this.getLastGetDailyBoxTsMs());
            fieldCnt++;
        }  else if (builder.hasLastGetDailyBoxTsMs()) {
            // 清理LastGetDailyBoxTsMs
            builder.clearLastGetDailyBoxTsMs();
            fieldCnt++;
        }
        if (this.extraCanGetDailyBoxLevel != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.extraCanGetDailyBoxLevel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExtraCanGetDailyBoxLevel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExtraCanGetDailyBoxLevel();
            }
        }  else if (builder.hasExtraCanGetDailyBoxLevel()) {
            // 清理ExtraCanGetDailyBoxLevel
            builder.clearExtraCanGetDailyBoxLevel();
            fieldCnt++;
        }
        if (this.vipBoxSelectedHero != null) {
            PlayerPB.Int32VipSelectedHeroInfoMapPB.Builder tmpBuilder = PlayerPB.Int32VipSelectedHeroInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.vipBoxSelectedHero.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setVipBoxSelectedHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearVipBoxSelectedHero();
            }
        }  else if (builder.hasVipBoxSelectedHero()) {
            // 清理VipBoxSelectedHero
            builder.clearVipBoxSelectedHero();
            fieldCnt++;
        }
        if (this.vipStoreSelectedHero != null) {
            PlayerPB.Int32VipSelectedHeroInfoMapPB.Builder tmpBuilder = PlayerPB.Int32VipSelectedHeroInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.vipStoreSelectedHero.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setVipStoreSelectedHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearVipStoreSelectedHero();
            }
        }  else if (builder.hasVipStoreSelectedHero()) {
            // 清理VipStoreSelectedHero
            builder.clearVipStoreSelectedHero();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerVipModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_VIPEXP)) {
            builder.setVipExp(this.getVipExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVELTOEXCLUSIVEBOXINFO) && this.levelToExclusiveBoxInfo != null) {
            final boolean needClear = !builder.hasLevelToExclusiveBoxInfo();
            final int tmpFieldCnt = this.levelToExclusiveBoxInfo.copyChangeToCs(builder.getLevelToExclusiveBoxInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLevelToExclusiveBoxInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIPCUMLOGINDAYS)) {
            builder.setVipCumLoginDays(this.getVipCumLoginDays());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHTGOODS) && this.boughtGoods != null) {
            final boolean needClear = !builder.hasBoughtGoods();
            final int tmpFieldCnt = this.boughtGoods.copyChangeToCs(builder.getBoughtGoodsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBoughtGoods();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTGETPRIVILEGEBOXTSMS)) {
            builder.setLastGetPrivilegeBoxTsMs(this.getLastGetPrivilegeBoxTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTGETDAILYBOXTSMS)) {
            builder.setLastGetDailyBoxTsMs(this.getLastGetDailyBoxTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXTRACANGETDAILYBOXLEVEL) && this.extraCanGetDailyBoxLevel != null) {
            final boolean needClear = !builder.hasExtraCanGetDailyBoxLevel();
            final int tmpFieldCnt = this.extraCanGetDailyBoxLevel.copyChangeToCs(builder.getExtraCanGetDailyBoxLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExtraCanGetDailyBoxLevel();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIPBOXSELECTEDHERO) && this.vipBoxSelectedHero != null) {
            final boolean needClear = !builder.hasVipBoxSelectedHero();
            final int tmpFieldCnt = this.vipBoxSelectedHero.copyChangeToCs(builder.getVipBoxSelectedHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearVipBoxSelectedHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIPSTORESELECTEDHERO) && this.vipStoreSelectedHero != null) {
            final boolean needClear = !builder.hasVipStoreSelectedHero();
            final int tmpFieldCnt = this.vipStoreSelectedHero.copyChangeToCs(builder.getVipStoreSelectedHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearVipStoreSelectedHero();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerVipModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_VIPEXP)) {
            builder.setVipExp(this.getVipExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVELTOEXCLUSIVEBOXINFO) && this.levelToExclusiveBoxInfo != null) {
            final boolean needClear = !builder.hasLevelToExclusiveBoxInfo();
            final int tmpFieldCnt = this.levelToExclusiveBoxInfo.copyChangeToAndClearDeleteKeysCs(builder.getLevelToExclusiveBoxInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLevelToExclusiveBoxInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIPCUMLOGINDAYS)) {
            builder.setVipCumLoginDays(this.getVipCumLoginDays());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHTGOODS) && this.boughtGoods != null) {
            final boolean needClear = !builder.hasBoughtGoods();
            final int tmpFieldCnt = this.boughtGoods.copyChangeToAndClearDeleteKeysCs(builder.getBoughtGoodsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBoughtGoods();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTGETPRIVILEGEBOXTSMS)) {
            builder.setLastGetPrivilegeBoxTsMs(this.getLastGetPrivilegeBoxTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTGETDAILYBOXTSMS)) {
            builder.setLastGetDailyBoxTsMs(this.getLastGetDailyBoxTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXTRACANGETDAILYBOXLEVEL) && this.extraCanGetDailyBoxLevel != null) {
            final boolean needClear = !builder.hasExtraCanGetDailyBoxLevel();
            final int tmpFieldCnt = this.extraCanGetDailyBoxLevel.copyChangeToCs(builder.getExtraCanGetDailyBoxLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExtraCanGetDailyBoxLevel();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIPBOXSELECTEDHERO) && this.vipBoxSelectedHero != null) {
            final boolean needClear = !builder.hasVipBoxSelectedHero();
            final int tmpFieldCnt = this.vipBoxSelectedHero.copyChangeToAndClearDeleteKeysCs(builder.getVipBoxSelectedHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearVipBoxSelectedHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIPSTORESELECTEDHERO) && this.vipStoreSelectedHero != null) {
            final boolean needClear = !builder.hasVipStoreSelectedHero();
            final int tmpFieldCnt = this.vipStoreSelectedHero.copyChangeToAndClearDeleteKeysCs(builder.getVipStoreSelectedHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearVipStoreSelectedHero();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerVipModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasVipExp()) {
            this.innerSetVipExp(proto.getVipExp());
        } else {
            this.innerSetVipExp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevelToExclusiveBoxInfo()) {
            this.getLevelToExclusiveBoxInfo().mergeFromCs(proto.getLevelToExclusiveBoxInfo());
        } else {
            if (this.levelToExclusiveBoxInfo != null) {
                this.levelToExclusiveBoxInfo.mergeFromCs(proto.getLevelToExclusiveBoxInfo());
            }
        }
        if (proto.hasVipCumLoginDays()) {
            this.innerSetVipCumLoginDays(proto.getVipCumLoginDays());
        } else {
            this.innerSetVipCumLoginDays(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBoughtGoods()) {
            this.getBoughtGoods().mergeFromCs(proto.getBoughtGoods());
        } else {
            if (this.boughtGoods != null) {
                this.boughtGoods.mergeFromCs(proto.getBoughtGoods());
            }
        }
        if (proto.hasLastGetPrivilegeBoxTsMs()) {
            this.innerSetLastGetPrivilegeBoxTsMs(proto.getLastGetPrivilegeBoxTsMs());
        } else {
            this.innerSetLastGetPrivilegeBoxTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastGetDailyBoxTsMs()) {
            this.innerSetLastGetDailyBoxTsMs(proto.getLastGetDailyBoxTsMs());
        } else {
            this.innerSetLastGetDailyBoxTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasExtraCanGetDailyBoxLevel()) {
            this.getExtraCanGetDailyBoxLevel().mergeFromCs(proto.getExtraCanGetDailyBoxLevel());
        } else {
            if (this.extraCanGetDailyBoxLevel != null) {
                this.extraCanGetDailyBoxLevel.mergeFromCs(proto.getExtraCanGetDailyBoxLevel());
            }
        }
        if (proto.hasVipBoxSelectedHero()) {
            this.getVipBoxSelectedHero().mergeFromCs(proto.getVipBoxSelectedHero());
        } else {
            if (this.vipBoxSelectedHero != null) {
                this.vipBoxSelectedHero.mergeFromCs(proto.getVipBoxSelectedHero());
            }
        }
        if (proto.hasVipStoreSelectedHero()) {
            this.getVipStoreSelectedHero().mergeFromCs(proto.getVipStoreSelectedHero());
        } else {
            if (this.vipStoreSelectedHero != null) {
                this.vipStoreSelectedHero.mergeFromCs(proto.getVipStoreSelectedHero());
            }
        }
        this.markAll();
        return PlayerVipModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerVipModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasVipExp()) {
            this.setVipExp(proto.getVipExp());
            fieldCnt++;
        }
        if (proto.hasLevelToExclusiveBoxInfo()) {
            this.getLevelToExclusiveBoxInfo().mergeChangeFromCs(proto.getLevelToExclusiveBoxInfo());
            fieldCnt++;
        }
        if (proto.hasVipCumLoginDays()) {
            this.setVipCumLoginDays(proto.getVipCumLoginDays());
            fieldCnt++;
        }
        if (proto.hasBoughtGoods()) {
            this.getBoughtGoods().mergeChangeFromCs(proto.getBoughtGoods());
            fieldCnt++;
        }
        if (proto.hasLastGetPrivilegeBoxTsMs()) {
            this.setLastGetPrivilegeBoxTsMs(proto.getLastGetPrivilegeBoxTsMs());
            fieldCnt++;
        }
        if (proto.hasLastGetDailyBoxTsMs()) {
            this.setLastGetDailyBoxTsMs(proto.getLastGetDailyBoxTsMs());
            fieldCnt++;
        }
        if (proto.hasExtraCanGetDailyBoxLevel()) {
            this.getExtraCanGetDailyBoxLevel().mergeChangeFromCs(proto.getExtraCanGetDailyBoxLevel());
            fieldCnt++;
        }
        if (proto.hasVipBoxSelectedHero()) {
            this.getVipBoxSelectedHero().mergeChangeFromCs(proto.getVipBoxSelectedHero());
            fieldCnt++;
        }
        if (proto.hasVipStoreSelectedHero()) {
            this.getVipStoreSelectedHero().mergeChangeFromCs(proto.getVipStoreSelectedHero());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerVipModel.Builder getCopyDbBuilder() {
        final PlayerVipModel.Builder builder = PlayerVipModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerVipModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getVipExp() != 0) {
            builder.setVipExp(this.getVipExp());
            fieldCnt++;
        }  else if (builder.hasVipExp()) {
            // 清理VipExp
            builder.clearVipExp();
            fieldCnt++;
        }
        if (this.levelToExclusiveBoxInfo != null) {
            Player.Int32ExclusiveBoxInfoMap.Builder tmpBuilder = Player.Int32ExclusiveBoxInfoMap.newBuilder();
            final int tmpFieldCnt = this.levelToExclusiveBoxInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLevelToExclusiveBoxInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLevelToExclusiveBoxInfo();
            }
        }  else if (builder.hasLevelToExclusiveBoxInfo()) {
            // 清理LevelToExclusiveBoxInfo
            builder.clearLevelToExclusiveBoxInfo();
            fieldCnt++;
        }
        if (this.getVipCumLoginDays() != 0) {
            builder.setVipCumLoginDays(this.getVipCumLoginDays());
            fieldCnt++;
        }  else if (builder.hasVipCumLoginDays()) {
            // 清理VipCumLoginDays
            builder.clearVipCumLoginDays();
            fieldCnt++;
        }
        if (this.getLastUpdateCumLoginTsMs() != 0L) {
            builder.setLastUpdateCumLoginTsMs(this.getLastUpdateCumLoginTsMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateCumLoginTsMs()) {
            // 清理LastUpdateCumLoginTsMs
            builder.clearLastUpdateCumLoginTsMs();
            fieldCnt++;
        }
        if (this.boughtGoods != null) {
            Player.Int32BoughtGoodsInfoMap.Builder tmpBuilder = Player.Int32BoughtGoodsInfoMap.newBuilder();
            final int tmpFieldCnt = this.boughtGoods.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBoughtGoods(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBoughtGoods();
            }
        }  else if (builder.hasBoughtGoods()) {
            // 清理BoughtGoods
            builder.clearBoughtGoods();
            fieldCnt++;
        }
        if (this.getLastGetPrivilegeBoxTsMs() != 0L) {
            builder.setLastGetPrivilegeBoxTsMs(this.getLastGetPrivilegeBoxTsMs());
            fieldCnt++;
        }  else if (builder.hasLastGetPrivilegeBoxTsMs()) {
            // 清理LastGetPrivilegeBoxTsMs
            builder.clearLastGetPrivilegeBoxTsMs();
            fieldCnt++;
        }
        if (this.getLastGetDailyBoxTsMs() != 0L) {
            builder.setLastGetDailyBoxTsMs(this.getLastGetDailyBoxTsMs());
            fieldCnt++;
        }  else if (builder.hasLastGetDailyBoxTsMs()) {
            // 清理LastGetDailyBoxTsMs
            builder.clearLastGetDailyBoxTsMs();
            fieldCnt++;
        }
        if (this.extraCanGetDailyBoxLevel != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.extraCanGetDailyBoxLevel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExtraCanGetDailyBoxLevel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExtraCanGetDailyBoxLevel();
            }
        }  else if (builder.hasExtraCanGetDailyBoxLevel()) {
            // 清理ExtraCanGetDailyBoxLevel
            builder.clearExtraCanGetDailyBoxLevel();
            fieldCnt++;
        }
        if (this.vipBoxSelectedHero != null) {
            Player.Int32VipSelectedHeroInfoMap.Builder tmpBuilder = Player.Int32VipSelectedHeroInfoMap.newBuilder();
            final int tmpFieldCnt = this.vipBoxSelectedHero.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setVipBoxSelectedHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearVipBoxSelectedHero();
            }
        }  else if (builder.hasVipBoxSelectedHero()) {
            // 清理VipBoxSelectedHero
            builder.clearVipBoxSelectedHero();
            fieldCnt++;
        }
        if (this.vipStoreSelectedHero != null) {
            Player.Int32VipSelectedHeroInfoMap.Builder tmpBuilder = Player.Int32VipSelectedHeroInfoMap.newBuilder();
            final int tmpFieldCnt = this.vipStoreSelectedHero.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setVipStoreSelectedHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearVipStoreSelectedHero();
            }
        }  else if (builder.hasVipStoreSelectedHero()) {
            // 清理VipStoreSelectedHero
            builder.clearVipStoreSelectedHero();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerVipModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_VIPEXP)) {
            builder.setVipExp(this.getVipExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVELTOEXCLUSIVEBOXINFO) && this.levelToExclusiveBoxInfo != null) {
            final boolean needClear = !builder.hasLevelToExclusiveBoxInfo();
            final int tmpFieldCnt = this.levelToExclusiveBoxInfo.copyChangeToDb(builder.getLevelToExclusiveBoxInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLevelToExclusiveBoxInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIPCUMLOGINDAYS)) {
            builder.setVipCumLoginDays(this.getVipCumLoginDays());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATECUMLOGINTSMS)) {
            builder.setLastUpdateCumLoginTsMs(this.getLastUpdateCumLoginTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHTGOODS) && this.boughtGoods != null) {
            final boolean needClear = !builder.hasBoughtGoods();
            final int tmpFieldCnt = this.boughtGoods.copyChangeToDb(builder.getBoughtGoodsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBoughtGoods();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTGETPRIVILEGEBOXTSMS)) {
            builder.setLastGetPrivilegeBoxTsMs(this.getLastGetPrivilegeBoxTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTGETDAILYBOXTSMS)) {
            builder.setLastGetDailyBoxTsMs(this.getLastGetDailyBoxTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXTRACANGETDAILYBOXLEVEL) && this.extraCanGetDailyBoxLevel != null) {
            final boolean needClear = !builder.hasExtraCanGetDailyBoxLevel();
            final int tmpFieldCnt = this.extraCanGetDailyBoxLevel.copyChangeToDb(builder.getExtraCanGetDailyBoxLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExtraCanGetDailyBoxLevel();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIPBOXSELECTEDHERO) && this.vipBoxSelectedHero != null) {
            final boolean needClear = !builder.hasVipBoxSelectedHero();
            final int tmpFieldCnt = this.vipBoxSelectedHero.copyChangeToDb(builder.getVipBoxSelectedHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearVipBoxSelectedHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIPSTORESELECTEDHERO) && this.vipStoreSelectedHero != null) {
            final boolean needClear = !builder.hasVipStoreSelectedHero();
            final int tmpFieldCnt = this.vipStoreSelectedHero.copyChangeToDb(builder.getVipStoreSelectedHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearVipStoreSelectedHero();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerVipModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasVipExp()) {
            this.innerSetVipExp(proto.getVipExp());
        } else {
            this.innerSetVipExp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevelToExclusiveBoxInfo()) {
            this.getLevelToExclusiveBoxInfo().mergeFromDb(proto.getLevelToExclusiveBoxInfo());
        } else {
            if (this.levelToExclusiveBoxInfo != null) {
                this.levelToExclusiveBoxInfo.mergeFromDb(proto.getLevelToExclusiveBoxInfo());
            }
        }
        if (proto.hasVipCumLoginDays()) {
            this.innerSetVipCumLoginDays(proto.getVipCumLoginDays());
        } else {
            this.innerSetVipCumLoginDays(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastUpdateCumLoginTsMs()) {
            this.innerSetLastUpdateCumLoginTsMs(proto.getLastUpdateCumLoginTsMs());
        } else {
            this.innerSetLastUpdateCumLoginTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBoughtGoods()) {
            this.getBoughtGoods().mergeFromDb(proto.getBoughtGoods());
        } else {
            if (this.boughtGoods != null) {
                this.boughtGoods.mergeFromDb(proto.getBoughtGoods());
            }
        }
        if (proto.hasLastGetPrivilegeBoxTsMs()) {
            this.innerSetLastGetPrivilegeBoxTsMs(proto.getLastGetPrivilegeBoxTsMs());
        } else {
            this.innerSetLastGetPrivilegeBoxTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastGetDailyBoxTsMs()) {
            this.innerSetLastGetDailyBoxTsMs(proto.getLastGetDailyBoxTsMs());
        } else {
            this.innerSetLastGetDailyBoxTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasExtraCanGetDailyBoxLevel()) {
            this.getExtraCanGetDailyBoxLevel().mergeFromDb(proto.getExtraCanGetDailyBoxLevel());
        } else {
            if (this.extraCanGetDailyBoxLevel != null) {
                this.extraCanGetDailyBoxLevel.mergeFromDb(proto.getExtraCanGetDailyBoxLevel());
            }
        }
        if (proto.hasVipBoxSelectedHero()) {
            this.getVipBoxSelectedHero().mergeFromDb(proto.getVipBoxSelectedHero());
        } else {
            if (this.vipBoxSelectedHero != null) {
                this.vipBoxSelectedHero.mergeFromDb(proto.getVipBoxSelectedHero());
            }
        }
        if (proto.hasVipStoreSelectedHero()) {
            this.getVipStoreSelectedHero().mergeFromDb(proto.getVipStoreSelectedHero());
        } else {
            if (this.vipStoreSelectedHero != null) {
                this.vipStoreSelectedHero.mergeFromDb(proto.getVipStoreSelectedHero());
            }
        }
        this.markAll();
        return PlayerVipModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerVipModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasVipExp()) {
            this.setVipExp(proto.getVipExp());
            fieldCnt++;
        }
        if (proto.hasLevelToExclusiveBoxInfo()) {
            this.getLevelToExclusiveBoxInfo().mergeChangeFromDb(proto.getLevelToExclusiveBoxInfo());
            fieldCnt++;
        }
        if (proto.hasVipCumLoginDays()) {
            this.setVipCumLoginDays(proto.getVipCumLoginDays());
            fieldCnt++;
        }
        if (proto.hasLastUpdateCumLoginTsMs()) {
            this.setLastUpdateCumLoginTsMs(proto.getLastUpdateCumLoginTsMs());
            fieldCnt++;
        }
        if (proto.hasBoughtGoods()) {
            this.getBoughtGoods().mergeChangeFromDb(proto.getBoughtGoods());
            fieldCnt++;
        }
        if (proto.hasLastGetPrivilegeBoxTsMs()) {
            this.setLastGetPrivilegeBoxTsMs(proto.getLastGetPrivilegeBoxTsMs());
            fieldCnt++;
        }
        if (proto.hasLastGetDailyBoxTsMs()) {
            this.setLastGetDailyBoxTsMs(proto.getLastGetDailyBoxTsMs());
            fieldCnt++;
        }
        if (proto.hasExtraCanGetDailyBoxLevel()) {
            this.getExtraCanGetDailyBoxLevel().mergeChangeFromDb(proto.getExtraCanGetDailyBoxLevel());
            fieldCnt++;
        }
        if (proto.hasVipBoxSelectedHero()) {
            this.getVipBoxSelectedHero().mergeChangeFromDb(proto.getVipBoxSelectedHero());
            fieldCnt++;
        }
        if (proto.hasVipStoreSelectedHero()) {
            this.getVipStoreSelectedHero().mergeChangeFromDb(proto.getVipStoreSelectedHero());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerVipModel.Builder getCopySsBuilder() {
        final PlayerVipModel.Builder builder = PlayerVipModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerVipModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getVipExp() != 0) {
            builder.setVipExp(this.getVipExp());
            fieldCnt++;
        }  else if (builder.hasVipExp()) {
            // 清理VipExp
            builder.clearVipExp();
            fieldCnt++;
        }
        if (this.levelToExclusiveBoxInfo != null) {
            Player.Int32ExclusiveBoxInfoMap.Builder tmpBuilder = Player.Int32ExclusiveBoxInfoMap.newBuilder();
            final int tmpFieldCnt = this.levelToExclusiveBoxInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLevelToExclusiveBoxInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLevelToExclusiveBoxInfo();
            }
        }  else if (builder.hasLevelToExclusiveBoxInfo()) {
            // 清理LevelToExclusiveBoxInfo
            builder.clearLevelToExclusiveBoxInfo();
            fieldCnt++;
        }
        if (this.getVipCumLoginDays() != 0) {
            builder.setVipCumLoginDays(this.getVipCumLoginDays());
            fieldCnt++;
        }  else if (builder.hasVipCumLoginDays()) {
            // 清理VipCumLoginDays
            builder.clearVipCumLoginDays();
            fieldCnt++;
        }
        if (this.getLastUpdateCumLoginTsMs() != 0L) {
            builder.setLastUpdateCumLoginTsMs(this.getLastUpdateCumLoginTsMs());
            fieldCnt++;
        }  else if (builder.hasLastUpdateCumLoginTsMs()) {
            // 清理LastUpdateCumLoginTsMs
            builder.clearLastUpdateCumLoginTsMs();
            fieldCnt++;
        }
        if (this.boughtGoods != null) {
            Player.Int32BoughtGoodsInfoMap.Builder tmpBuilder = Player.Int32BoughtGoodsInfoMap.newBuilder();
            final int tmpFieldCnt = this.boughtGoods.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBoughtGoods(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBoughtGoods();
            }
        }  else if (builder.hasBoughtGoods()) {
            // 清理BoughtGoods
            builder.clearBoughtGoods();
            fieldCnt++;
        }
        if (this.getLastGetPrivilegeBoxTsMs() != 0L) {
            builder.setLastGetPrivilegeBoxTsMs(this.getLastGetPrivilegeBoxTsMs());
            fieldCnt++;
        }  else if (builder.hasLastGetPrivilegeBoxTsMs()) {
            // 清理LastGetPrivilegeBoxTsMs
            builder.clearLastGetPrivilegeBoxTsMs();
            fieldCnt++;
        }
        if (this.getLastGetDailyBoxTsMs() != 0L) {
            builder.setLastGetDailyBoxTsMs(this.getLastGetDailyBoxTsMs());
            fieldCnt++;
        }  else if (builder.hasLastGetDailyBoxTsMs()) {
            // 清理LastGetDailyBoxTsMs
            builder.clearLastGetDailyBoxTsMs();
            fieldCnt++;
        }
        if (this.extraCanGetDailyBoxLevel != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.extraCanGetDailyBoxLevel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExtraCanGetDailyBoxLevel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExtraCanGetDailyBoxLevel();
            }
        }  else if (builder.hasExtraCanGetDailyBoxLevel()) {
            // 清理ExtraCanGetDailyBoxLevel
            builder.clearExtraCanGetDailyBoxLevel();
            fieldCnt++;
        }
        if (this.vipBoxSelectedHero != null) {
            Player.Int32VipSelectedHeroInfoMap.Builder tmpBuilder = Player.Int32VipSelectedHeroInfoMap.newBuilder();
            final int tmpFieldCnt = this.vipBoxSelectedHero.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setVipBoxSelectedHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearVipBoxSelectedHero();
            }
        }  else if (builder.hasVipBoxSelectedHero()) {
            // 清理VipBoxSelectedHero
            builder.clearVipBoxSelectedHero();
            fieldCnt++;
        }
        if (this.vipStoreSelectedHero != null) {
            Player.Int32VipSelectedHeroInfoMap.Builder tmpBuilder = Player.Int32VipSelectedHeroInfoMap.newBuilder();
            final int tmpFieldCnt = this.vipStoreSelectedHero.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setVipStoreSelectedHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearVipStoreSelectedHero();
            }
        }  else if (builder.hasVipStoreSelectedHero()) {
            // 清理VipStoreSelectedHero
            builder.clearVipStoreSelectedHero();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerVipModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_VIPEXP)) {
            builder.setVipExp(this.getVipExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVELTOEXCLUSIVEBOXINFO) && this.levelToExclusiveBoxInfo != null) {
            final boolean needClear = !builder.hasLevelToExclusiveBoxInfo();
            final int tmpFieldCnt = this.levelToExclusiveBoxInfo.copyChangeToSs(builder.getLevelToExclusiveBoxInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLevelToExclusiveBoxInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIPCUMLOGINDAYS)) {
            builder.setVipCumLoginDays(this.getVipCumLoginDays());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTUPDATECUMLOGINTSMS)) {
            builder.setLastUpdateCumLoginTsMs(this.getLastUpdateCumLoginTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHTGOODS) && this.boughtGoods != null) {
            final boolean needClear = !builder.hasBoughtGoods();
            final int tmpFieldCnt = this.boughtGoods.copyChangeToSs(builder.getBoughtGoodsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBoughtGoods();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTGETPRIVILEGEBOXTSMS)) {
            builder.setLastGetPrivilegeBoxTsMs(this.getLastGetPrivilegeBoxTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTGETDAILYBOXTSMS)) {
            builder.setLastGetDailyBoxTsMs(this.getLastGetDailyBoxTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXTRACANGETDAILYBOXLEVEL) && this.extraCanGetDailyBoxLevel != null) {
            final boolean needClear = !builder.hasExtraCanGetDailyBoxLevel();
            final int tmpFieldCnt = this.extraCanGetDailyBoxLevel.copyChangeToSs(builder.getExtraCanGetDailyBoxLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExtraCanGetDailyBoxLevel();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIPBOXSELECTEDHERO) && this.vipBoxSelectedHero != null) {
            final boolean needClear = !builder.hasVipBoxSelectedHero();
            final int tmpFieldCnt = this.vipBoxSelectedHero.copyChangeToSs(builder.getVipBoxSelectedHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearVipBoxSelectedHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIPSTORESELECTEDHERO) && this.vipStoreSelectedHero != null) {
            final boolean needClear = !builder.hasVipStoreSelectedHero();
            final int tmpFieldCnt = this.vipStoreSelectedHero.copyChangeToSs(builder.getVipStoreSelectedHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearVipStoreSelectedHero();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerVipModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasVipExp()) {
            this.innerSetVipExp(proto.getVipExp());
        } else {
            this.innerSetVipExp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevelToExclusiveBoxInfo()) {
            this.getLevelToExclusiveBoxInfo().mergeFromSs(proto.getLevelToExclusiveBoxInfo());
        } else {
            if (this.levelToExclusiveBoxInfo != null) {
                this.levelToExclusiveBoxInfo.mergeFromSs(proto.getLevelToExclusiveBoxInfo());
            }
        }
        if (proto.hasVipCumLoginDays()) {
            this.innerSetVipCumLoginDays(proto.getVipCumLoginDays());
        } else {
            this.innerSetVipCumLoginDays(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastUpdateCumLoginTsMs()) {
            this.innerSetLastUpdateCumLoginTsMs(proto.getLastUpdateCumLoginTsMs());
        } else {
            this.innerSetLastUpdateCumLoginTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBoughtGoods()) {
            this.getBoughtGoods().mergeFromSs(proto.getBoughtGoods());
        } else {
            if (this.boughtGoods != null) {
                this.boughtGoods.mergeFromSs(proto.getBoughtGoods());
            }
        }
        if (proto.hasLastGetPrivilegeBoxTsMs()) {
            this.innerSetLastGetPrivilegeBoxTsMs(proto.getLastGetPrivilegeBoxTsMs());
        } else {
            this.innerSetLastGetPrivilegeBoxTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastGetDailyBoxTsMs()) {
            this.innerSetLastGetDailyBoxTsMs(proto.getLastGetDailyBoxTsMs());
        } else {
            this.innerSetLastGetDailyBoxTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasExtraCanGetDailyBoxLevel()) {
            this.getExtraCanGetDailyBoxLevel().mergeFromSs(proto.getExtraCanGetDailyBoxLevel());
        } else {
            if (this.extraCanGetDailyBoxLevel != null) {
                this.extraCanGetDailyBoxLevel.mergeFromSs(proto.getExtraCanGetDailyBoxLevel());
            }
        }
        if (proto.hasVipBoxSelectedHero()) {
            this.getVipBoxSelectedHero().mergeFromSs(proto.getVipBoxSelectedHero());
        } else {
            if (this.vipBoxSelectedHero != null) {
                this.vipBoxSelectedHero.mergeFromSs(proto.getVipBoxSelectedHero());
            }
        }
        if (proto.hasVipStoreSelectedHero()) {
            this.getVipStoreSelectedHero().mergeFromSs(proto.getVipStoreSelectedHero());
        } else {
            if (this.vipStoreSelectedHero != null) {
                this.vipStoreSelectedHero.mergeFromSs(proto.getVipStoreSelectedHero());
            }
        }
        this.markAll();
        return PlayerVipModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerVipModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasVipExp()) {
            this.setVipExp(proto.getVipExp());
            fieldCnt++;
        }
        if (proto.hasLevelToExclusiveBoxInfo()) {
            this.getLevelToExclusiveBoxInfo().mergeChangeFromSs(proto.getLevelToExclusiveBoxInfo());
            fieldCnt++;
        }
        if (proto.hasVipCumLoginDays()) {
            this.setVipCumLoginDays(proto.getVipCumLoginDays());
            fieldCnt++;
        }
        if (proto.hasLastUpdateCumLoginTsMs()) {
            this.setLastUpdateCumLoginTsMs(proto.getLastUpdateCumLoginTsMs());
            fieldCnt++;
        }
        if (proto.hasBoughtGoods()) {
            this.getBoughtGoods().mergeChangeFromSs(proto.getBoughtGoods());
            fieldCnt++;
        }
        if (proto.hasLastGetPrivilegeBoxTsMs()) {
            this.setLastGetPrivilegeBoxTsMs(proto.getLastGetPrivilegeBoxTsMs());
            fieldCnt++;
        }
        if (proto.hasLastGetDailyBoxTsMs()) {
            this.setLastGetDailyBoxTsMs(proto.getLastGetDailyBoxTsMs());
            fieldCnt++;
        }
        if (proto.hasExtraCanGetDailyBoxLevel()) {
            this.getExtraCanGetDailyBoxLevel().mergeChangeFromSs(proto.getExtraCanGetDailyBoxLevel());
            fieldCnt++;
        }
        if (proto.hasVipBoxSelectedHero()) {
            this.getVipBoxSelectedHero().mergeChangeFromSs(proto.getVipBoxSelectedHero());
            fieldCnt++;
        }
        if (proto.hasVipStoreSelectedHero()) {
            this.getVipStoreSelectedHero().mergeChangeFromSs(proto.getVipStoreSelectedHero());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerVipModel.Builder builder = PlayerVipModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_LEVELTOEXCLUSIVEBOXINFO) && this.levelToExclusiveBoxInfo != null) {
            this.levelToExclusiveBoxInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BOUGHTGOODS) && this.boughtGoods != null) {
            this.boughtGoods.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_EXTRACANGETDAILYBOXLEVEL) && this.extraCanGetDailyBoxLevel != null) {
            this.extraCanGetDailyBoxLevel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_VIPBOXSELECTEDHERO) && this.vipBoxSelectedHero != null) {
            this.vipBoxSelectedHero.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_VIPSTORESELECTEDHERO) && this.vipStoreSelectedHero != null) {
            this.vipStoreSelectedHero.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.levelToExclusiveBoxInfo != null) {
            this.levelToExclusiveBoxInfo.markAll();
        }
        if (this.boughtGoods != null) {
            this.boughtGoods.markAll();
        }
        if (this.extraCanGetDailyBoxLevel != null) {
            this.extraCanGetDailyBoxLevel.markAll();
        }
        if (this.vipBoxSelectedHero != null) {
            this.vipBoxSelectedHero.markAll();
        }
        if (this.vipStoreSelectedHero != null) {
            this.vipStoreSelectedHero.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerVipModelProp)) {
            return false;
        }
        final PlayerVipModelProp otherNode = (PlayerVipModelProp) node;
        if (this.vipExp != otherNode.vipExp) {
            return false;
        }
        if (!this.getLevelToExclusiveBoxInfo().compareDataTo(otherNode.getLevelToExclusiveBoxInfo())) {
            return false;
        }
        if (this.vipCumLoginDays != otherNode.vipCumLoginDays) {
            return false;
        }
        if (this.lastUpdateCumLoginTsMs != otherNode.lastUpdateCumLoginTsMs) {
            return false;
        }
        if (!this.getBoughtGoods().compareDataTo(otherNode.getBoughtGoods())) {
            return false;
        }
        if (this.lastGetPrivilegeBoxTsMs != otherNode.lastGetPrivilegeBoxTsMs) {
            return false;
        }
        if (this.lastGetDailyBoxTsMs != otherNode.lastGetDailyBoxTsMs) {
            return false;
        }
        if (!this.getExtraCanGetDailyBoxLevel().compareDataTo(otherNode.getExtraCanGetDailyBoxLevel())) {
            return false;
        }
        if (!this.getVipBoxSelectedHero().compareDataTo(otherNode.getVipBoxSelectedHero())) {
            return false;
        }
        if (!this.getVipStoreSelectedHero().compareDataTo(otherNode.getVipStoreSelectedHero())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 54;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zone.KingdomOfficeInfo;
import com.yorha.proto.Struct;
import com.yorha.proto.ZonePB.KingdomOfficeInfoPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class KingdomOfficeInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_OFFICEID = 0;
    public static final int FIELD_INDEX_PLAYERID = 1;
    public static final int FIELD_INDEX_PLAYERCARDHEAD = 2;
    public static final int FIELD_INDEX_SNAME = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int officeId = Constant.DEFAULT_INT_VALUE;
    private long playerId = Constant.DEFAULT_LONG_VALUE;
    private PlayerCardHeadProp playerCardHead = null;
    private String sname = Constant.DEFAULT_STR_VALUE;

    public KingdomOfficeInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public KingdomOfficeInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get officeId
     *
     * @return officeId value
     */
    public int getOfficeId() {
        return this.officeId;
    }

    /**
     * set officeId && set marked
     *
     * @param officeId new value
     * @return current object
     */
    public KingdomOfficeInfoProp setOfficeId(int officeId) {
        if (this.officeId != officeId) {
            this.mark(FIELD_INDEX_OFFICEID);
            this.officeId = officeId;
        }
        return this;
    }

    /**
     * inner set officeId
     *
     * @param officeId new value
     */
    private void innerSetOfficeId(int officeId) {
        this.officeId = officeId;
    }

    /**
     * get playerId
     *
     * @return playerId value
     */
    public long getPlayerId() {
        return this.playerId;
    }

    /**
     * set playerId && set marked
     *
     * @param playerId new value
     * @return current object
     */
    public KingdomOfficeInfoProp setPlayerId(long playerId) {
        if (this.playerId != playerId) {
            this.mark(FIELD_INDEX_PLAYERID);
            this.playerId = playerId;
        }
        return this;
    }

    /**
     * inner set playerId
     *
     * @param playerId new value
     */
    private void innerSetPlayerId(long playerId) {
        this.playerId = playerId;
    }

    /**
     * get playerCardHead
     *
     * @return playerCardHead value
     */
    public PlayerCardHeadProp getPlayerCardHead() {
        if (this.playerCardHead == null) {
            this.playerCardHead = new PlayerCardHeadProp(this, FIELD_INDEX_PLAYERCARDHEAD);
        }
        return this.playerCardHead;
    }

    /**
     * get sname
     *
     * @return sname value
     */
    public String getSname() {
        return this.sname;
    }

    /**
     * set sname && set marked
     *
     * @param sname new value
     * @return current object
     */
    public KingdomOfficeInfoProp setSname(String sname) {
        if (sname == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.sname, sname)) {
            this.mark(FIELD_INDEX_SNAME);
            this.sname = sname;
        }
        return this;
    }

    /**
     * inner set sname
     *
     * @param sname new value
     */
    private void innerSetSname(String sname) {
        this.sname = sname;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomOfficeInfoPB.Builder getCopyCsBuilder() {
        final KingdomOfficeInfoPB.Builder builder = KingdomOfficeInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(KingdomOfficeInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getOfficeId() != 0) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }  else if (builder.hasOfficeId()) {
            // 清理OfficeId
            builder.clearOfficeId();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.playerCardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.playerCardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerCardHead();
            }
        }  else if (builder.hasPlayerCardHead()) {
            // 清理PlayerCardHead
            builder.clearPlayerCardHead();
            fieldCnt++;
        }
        if (!this.getSname().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSname(this.getSname());
            fieldCnt++;
        }  else if (builder.hasSname()) {
            // 清理Sname
            builder.clearSname();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(KingdomOfficeInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OFFICEID)) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCARDHEAD) && this.playerCardHead != null) {
            final boolean needClear = !builder.hasPlayerCardHead();
            final int tmpFieldCnt = this.playerCardHead.copyChangeToCs(builder.getPlayerCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_SNAME)) {
            builder.setSname(this.getSname());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(KingdomOfficeInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OFFICEID)) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCARDHEAD) && this.playerCardHead != null) {
            final boolean needClear = !builder.hasPlayerCardHead();
            final int tmpFieldCnt = this.playerCardHead.copyChangeToAndClearDeleteKeysCs(builder.getPlayerCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_SNAME)) {
            builder.setSname(this.getSname());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(KingdomOfficeInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOfficeId()) {
            this.innerSetOfficeId(proto.getOfficeId());
        } else {
            this.innerSetOfficeId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerCardHead()) {
            this.getPlayerCardHead().mergeFromCs(proto.getPlayerCardHead());
        } else {
            if (this.playerCardHead != null) {
                this.playerCardHead.mergeFromCs(proto.getPlayerCardHead());
            }
        }
        if (proto.hasSname()) {
            this.innerSetSname(proto.getSname());
        } else {
            this.innerSetSname(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return KingdomOfficeInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(KingdomOfficeInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOfficeId()) {
            this.setOfficeId(proto.getOfficeId());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasPlayerCardHead()) {
            this.getPlayerCardHead().mergeChangeFromCs(proto.getPlayerCardHead());
            fieldCnt++;
        }
        if (proto.hasSname()) {
            this.setSname(proto.getSname());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomOfficeInfo.Builder getCopyDbBuilder() {
        final KingdomOfficeInfo.Builder builder = KingdomOfficeInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(KingdomOfficeInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getOfficeId() != 0) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }  else if (builder.hasOfficeId()) {
            // 清理OfficeId
            builder.clearOfficeId();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(KingdomOfficeInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OFFICEID)) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(KingdomOfficeInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOfficeId()) {
            this.innerSetOfficeId(proto.getOfficeId());
        } else {
            this.innerSetOfficeId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return KingdomOfficeInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(KingdomOfficeInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOfficeId()) {
            this.setOfficeId(proto.getOfficeId());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomOfficeInfo.Builder getCopySsBuilder() {
        final KingdomOfficeInfo.Builder builder = KingdomOfficeInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(KingdomOfficeInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getOfficeId() != 0) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }  else if (builder.hasOfficeId()) {
            // 清理OfficeId
            builder.clearOfficeId();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.playerCardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.playerCardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerCardHead();
            }
        }  else if (builder.hasPlayerCardHead()) {
            // 清理PlayerCardHead
            builder.clearPlayerCardHead();
            fieldCnt++;
        }
        if (!this.getSname().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSname(this.getSname());
            fieldCnt++;
        }  else if (builder.hasSname()) {
            // 清理Sname
            builder.clearSname();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(KingdomOfficeInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OFFICEID)) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCARDHEAD) && this.playerCardHead != null) {
            final boolean needClear = !builder.hasPlayerCardHead();
            final int tmpFieldCnt = this.playerCardHead.copyChangeToSs(builder.getPlayerCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_SNAME)) {
            builder.setSname(this.getSname());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(KingdomOfficeInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOfficeId()) {
            this.innerSetOfficeId(proto.getOfficeId());
        } else {
            this.innerSetOfficeId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerCardHead()) {
            this.getPlayerCardHead().mergeFromSs(proto.getPlayerCardHead());
        } else {
            if (this.playerCardHead != null) {
                this.playerCardHead.mergeFromSs(proto.getPlayerCardHead());
            }
        }
        if (proto.hasSname()) {
            this.innerSetSname(proto.getSname());
        } else {
            this.innerSetSname(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return KingdomOfficeInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(KingdomOfficeInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOfficeId()) {
            this.setOfficeId(proto.getOfficeId());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasPlayerCardHead()) {
            this.getPlayerCardHead().mergeChangeFromSs(proto.getPlayerCardHead());
            fieldCnt++;
        }
        if (proto.hasSname()) {
            this.setSname(proto.getSname());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        KingdomOfficeInfo.Builder builder = KingdomOfficeInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCARDHEAD) && this.playerCardHead != null) {
            this.playerCardHead.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.playerCardHead != null) {
            this.playerCardHead.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.officeId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof KingdomOfficeInfoProp)) {
            return false;
        }
        final KingdomOfficeInfoProp otherNode = (KingdomOfficeInfoProp) node;
        if (this.officeId != otherNode.officeId) {
            return false;
        }
        if (this.playerId != otherNode.playerId) {
            return false;
        }
        if (!this.getPlayerCardHead().compareDataTo(otherNode.getPlayerCardHead())) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.sname, otherNode.sname)) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
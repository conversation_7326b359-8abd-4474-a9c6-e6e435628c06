package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.PickLimitInfo;
import com.yorha.proto.StructPB.PickLimitInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class PickLimitInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_OVERTIME = 0;
    public static final int FIELD_INDEX_COUNT = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private long overTime = Constant.DEFAULT_LONG_VALUE;
    private int count = Constant.DEFAULT_INT_VALUE;

    public PickLimitInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PickLimitInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get overTime
     *
     * @return overTime value
     */
    public long getOverTime() {
        return this.overTime;
    }

    /**
     * set overTime && set marked
     *
     * @param overTime new value
     * @return current object
     */
    public PickLimitInfoProp setOverTime(long overTime) {
        if (this.overTime != overTime) {
            this.mark(FIELD_INDEX_OVERTIME);
            this.overTime = overTime;
        }
        return this;
    }

    /**
     * inner set overTime
     *
     * @param overTime new value
     */
    private void innerSetOverTime(long overTime) {
        this.overTime = overTime;
    }

    /**
     * get count
     *
     * @return count value
     */
    public int getCount() {
        return this.count;
    }

    /**
     * set count && set marked
     *
     * @param count new value
     * @return current object
     */
    public PickLimitInfoProp setCount(int count) {
        if (this.count != count) {
            this.mark(FIELD_INDEX_COUNT);
            this.count = count;
        }
        return this;
    }

    /**
     * inner set count
     *
     * @param count new value
     */
    private void innerSetCount(int count) {
        this.count = count;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PickLimitInfoPB.Builder getCopyCsBuilder() {
        final PickLimitInfoPB.Builder builder = PickLimitInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PickLimitInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getOverTime() != 0L) {
            builder.setOverTime(this.getOverTime());
            fieldCnt++;
        }  else if (builder.hasOverTime()) {
            // 清理OverTime
            builder.clearOverTime();
            fieldCnt++;
        }
        if (this.getCount() != 0) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }  else if (builder.hasCount()) {
            // 清理Count
            builder.clearCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PickLimitInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OVERTIME)) {
            builder.setOverTime(this.getOverTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PickLimitInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OVERTIME)) {
            builder.setOverTime(this.getOverTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PickLimitInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOverTime()) {
            this.innerSetOverTime(proto.getOverTime());
        } else {
            this.innerSetOverTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCount()) {
            this.innerSetCount(proto.getCount());
        } else {
            this.innerSetCount(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PickLimitInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PickLimitInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOverTime()) {
            this.setOverTime(proto.getOverTime());
            fieldCnt++;
        }
        if (proto.hasCount()) {
            this.setCount(proto.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PickLimitInfo.Builder getCopyDbBuilder() {
        final PickLimitInfo.Builder builder = PickLimitInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PickLimitInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getOverTime() != 0L) {
            builder.setOverTime(this.getOverTime());
            fieldCnt++;
        }  else if (builder.hasOverTime()) {
            // 清理OverTime
            builder.clearOverTime();
            fieldCnt++;
        }
        if (this.getCount() != 0) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }  else if (builder.hasCount()) {
            // 清理Count
            builder.clearCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PickLimitInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OVERTIME)) {
            builder.setOverTime(this.getOverTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PickLimitInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOverTime()) {
            this.innerSetOverTime(proto.getOverTime());
        } else {
            this.innerSetOverTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCount()) {
            this.innerSetCount(proto.getCount());
        } else {
            this.innerSetCount(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PickLimitInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PickLimitInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOverTime()) {
            this.setOverTime(proto.getOverTime());
            fieldCnt++;
        }
        if (proto.hasCount()) {
            this.setCount(proto.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PickLimitInfo.Builder getCopySsBuilder() {
        final PickLimitInfo.Builder builder = PickLimitInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PickLimitInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getOverTime() != 0L) {
            builder.setOverTime(this.getOverTime());
            fieldCnt++;
        }  else if (builder.hasOverTime()) {
            // 清理OverTime
            builder.clearOverTime();
            fieldCnt++;
        }
        if (this.getCount() != 0) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }  else if (builder.hasCount()) {
            // 清理Count
            builder.clearCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PickLimitInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OVERTIME)) {
            builder.setOverTime(this.getOverTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PickLimitInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOverTime()) {
            this.innerSetOverTime(proto.getOverTime());
        } else {
            this.innerSetOverTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCount()) {
            this.innerSetCount(proto.getCount());
        } else {
            this.innerSetCount(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PickLimitInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PickLimitInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOverTime()) {
            this.setOverTime(proto.getOverTime());
            fieldCnt++;
        }
        if (proto.hasCount()) {
            this.setCount(proto.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PickLimitInfo.Builder builder = PickLimitInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PickLimitInfoProp)) {
            return false;
        }
        final PickLimitInfoProp otherNode = (PickLimitInfoProp) node;
        if (this.overTime != otherNode.overTime) {
            return false;
        }
        if (this.count != otherNode.count) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityStoreGoodRecord;
import com.yorha.proto.StructPB.ActivityStoreGoodRecordPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityStoreGoodRecordProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_GOODID = 0;
    public static final int FIELD_INDEX_BUYCOUNT = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int goodId = Constant.DEFAULT_INT_VALUE;
    private int buyCount = Constant.DEFAULT_INT_VALUE;

    public ActivityStoreGoodRecordProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityStoreGoodRecordProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get goodId
     *
     * @return goodId value
     */
    public int getGoodId() {
        return this.goodId;
    }

    /**
     * set goodId && set marked
     *
     * @param goodId new value
     * @return current object
     */
    public ActivityStoreGoodRecordProp setGoodId(int goodId) {
        if (this.goodId != goodId) {
            this.mark(FIELD_INDEX_GOODID);
            this.goodId = goodId;
        }
        return this;
    }

    /**
     * inner set goodId
     *
     * @param goodId new value
     */
    private void innerSetGoodId(int goodId) {
        this.goodId = goodId;
    }

    /**
     * get buyCount
     *
     * @return buyCount value
     */
    public int getBuyCount() {
        return this.buyCount;
    }

    /**
     * set buyCount && set marked
     *
     * @param buyCount new value
     * @return current object
     */
    public ActivityStoreGoodRecordProp setBuyCount(int buyCount) {
        if (this.buyCount != buyCount) {
            this.mark(FIELD_INDEX_BUYCOUNT);
            this.buyCount = buyCount;
        }
        return this;
    }

    /**
     * inner set buyCount
     *
     * @param buyCount new value
     */
    private void innerSetBuyCount(int buyCount) {
        this.buyCount = buyCount;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityStoreGoodRecordPB.Builder getCopyCsBuilder() {
        final ActivityStoreGoodRecordPB.Builder builder = ActivityStoreGoodRecordPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityStoreGoodRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGoodId() != 0) {
            builder.setGoodId(this.getGoodId());
            fieldCnt++;
        }  else if (builder.hasGoodId()) {
            // 清理GoodId
            builder.clearGoodId();
            fieldCnt++;
        }
        if (this.getBuyCount() != 0) {
            builder.setBuyCount(this.getBuyCount());
            fieldCnt++;
        }  else if (builder.hasBuyCount()) {
            // 清理BuyCount
            builder.clearBuyCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityStoreGoodRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODID)) {
            builder.setGoodId(this.getGoodId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUYCOUNT)) {
            builder.setBuyCount(this.getBuyCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityStoreGoodRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODID)) {
            builder.setGoodId(this.getGoodId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUYCOUNT)) {
            builder.setBuyCount(this.getBuyCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityStoreGoodRecordPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGoodId()) {
            this.innerSetGoodId(proto.getGoodId());
        } else {
            this.innerSetGoodId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuyCount()) {
            this.innerSetBuyCount(proto.getBuyCount());
        } else {
            this.innerSetBuyCount(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivityStoreGoodRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityStoreGoodRecordPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGoodId()) {
            this.setGoodId(proto.getGoodId());
            fieldCnt++;
        }
        if (proto.hasBuyCount()) {
            this.setBuyCount(proto.getBuyCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityStoreGoodRecord.Builder getCopyDbBuilder() {
        final ActivityStoreGoodRecord.Builder builder = ActivityStoreGoodRecord.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityStoreGoodRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGoodId() != 0) {
            builder.setGoodId(this.getGoodId());
            fieldCnt++;
        }  else if (builder.hasGoodId()) {
            // 清理GoodId
            builder.clearGoodId();
            fieldCnt++;
        }
        if (this.getBuyCount() != 0) {
            builder.setBuyCount(this.getBuyCount());
            fieldCnt++;
        }  else if (builder.hasBuyCount()) {
            // 清理BuyCount
            builder.clearBuyCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityStoreGoodRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODID)) {
            builder.setGoodId(this.getGoodId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUYCOUNT)) {
            builder.setBuyCount(this.getBuyCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityStoreGoodRecord proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGoodId()) {
            this.innerSetGoodId(proto.getGoodId());
        } else {
            this.innerSetGoodId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuyCount()) {
            this.innerSetBuyCount(proto.getBuyCount());
        } else {
            this.innerSetBuyCount(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivityStoreGoodRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityStoreGoodRecord proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGoodId()) {
            this.setGoodId(proto.getGoodId());
            fieldCnt++;
        }
        if (proto.hasBuyCount()) {
            this.setBuyCount(proto.getBuyCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityStoreGoodRecord.Builder getCopySsBuilder() {
        final ActivityStoreGoodRecord.Builder builder = ActivityStoreGoodRecord.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityStoreGoodRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGoodId() != 0) {
            builder.setGoodId(this.getGoodId());
            fieldCnt++;
        }  else if (builder.hasGoodId()) {
            // 清理GoodId
            builder.clearGoodId();
            fieldCnt++;
        }
        if (this.getBuyCount() != 0) {
            builder.setBuyCount(this.getBuyCount());
            fieldCnt++;
        }  else if (builder.hasBuyCount()) {
            // 清理BuyCount
            builder.clearBuyCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityStoreGoodRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODID)) {
            builder.setGoodId(this.getGoodId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUYCOUNT)) {
            builder.setBuyCount(this.getBuyCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityStoreGoodRecord proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGoodId()) {
            this.innerSetGoodId(proto.getGoodId());
        } else {
            this.innerSetGoodId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuyCount()) {
            this.innerSetBuyCount(proto.getBuyCount());
        } else {
            this.innerSetBuyCount(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivityStoreGoodRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityStoreGoodRecord proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGoodId()) {
            this.setGoodId(proto.getGoodId());
            fieldCnt++;
        }
        if (proto.hasBuyCount()) {
            this.setBuyCount(proto.getBuyCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityStoreGoodRecord.Builder builder = ActivityStoreGoodRecord.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.goodId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityStoreGoodRecordProp)) {
            return false;
        }
        final ActivityStoreGoodRecordProp otherNode = (ActivityStoreGoodRecordProp) node;
        if (this.goodId != otherNode.goodId) {
            return false;
        }
        if (this.buyCount != otherNode.buyCount) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
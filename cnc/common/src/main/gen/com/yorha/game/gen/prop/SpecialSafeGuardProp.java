package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.SpecialSafeGuard;
import com.yorha.proto.Basic;
import com.yorha.proto.StructPB.SpecialSafeGuardPB;
import com.yorha.proto.BasicPB;


/**
 * <AUTHOR> auto gen
 */
public class SpecialSafeGuardProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ISOPENSAFEGUARD = 0;
    public static final int FIELD_INDEX_SAFEGUARDREASONS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private boolean isOpenSafeGuard = Constant.DEFAULT_BOOLEAN_VALUE;
    private Int32SetProp safeGuardReasons = null;

    public SpecialSafeGuardProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SpecialSafeGuardProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get isOpenSafeGuard
     *
     * @return isOpenSafeGuard value
     */
    public boolean getIsOpenSafeGuard() {
        return this.isOpenSafeGuard;
    }

    /**
     * set isOpenSafeGuard && set marked
     *
     * @param isOpenSafeGuard new value
     * @return current object
     */
    public SpecialSafeGuardProp setIsOpenSafeGuard(boolean isOpenSafeGuard) {
        if (this.isOpenSafeGuard != isOpenSafeGuard) {
            this.mark(FIELD_INDEX_ISOPENSAFEGUARD);
            this.isOpenSafeGuard = isOpenSafeGuard;
        }
        return this;
    }

    /**
     * inner set isOpenSafeGuard
     *
     * @param isOpenSafeGuard new value
     */
    private void innerSetIsOpenSafeGuard(boolean isOpenSafeGuard) {
        this.isOpenSafeGuard = isOpenSafeGuard;
    }

    /**
     * get safeGuardReasons
     *
     * @return safeGuardReasons value
     */
    public Int32SetProp getSafeGuardReasons() {
        if (this.safeGuardReasons == null) {
            this.safeGuardReasons = new Int32SetProp(this, FIELD_INDEX_SAFEGUARDREASONS);
        }
        return this.safeGuardReasons;
    }


    /**
     * add value to set
     *
     * @param e value
     */
    public void addSafeGuardReasons(Integer e) {
        this.getSafeGuardReasons().add(e);
    }

    /**
     * remove the specified element in set
     *
     * @param e element
     * @return e if success or null by fail
     */
    public Integer removeSafeGuardReasons(Integer e) {
        if (this.safeGuardReasons == null) {
            return null;
        }
        if(this.safeGuardReasons.remove(e)) {
            return e;
        }
        return null;
    }

    /**
     * get set size
     *
     * @return set size
     */
    public int getSafeGuardReasonsSize() {
        if (this.safeGuardReasons == null) {
            return 0;
        }
        return this.safeGuardReasons.size();
    }

    /**
     * get is a empty set
     *
     * @return true if empty
     */
    public boolean isSafeGuardReasonsEmpty() {
        if (this.safeGuardReasons == null) {
            return true;
        }
        return this.getSafeGuardReasons().isEmpty();
    }

    /**
     * clear set
     */
    public void clearSafeGuardReasons() {
        this.getSafeGuardReasons().clear();
    }

    /**
     * Returns true if this set contains the specified element.
     *
     * @param e elem
     * @return true if this set contains the specified element, false otherwise
     */
    public boolean isSafeGuardReasonsContains(Integer e) {
        return this.safeGuardReasons != null && this.safeGuardReasons.contains(e);
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SpecialSafeGuardPB.Builder getCopyCsBuilder() {
        final SpecialSafeGuardPB.Builder builder = SpecialSafeGuardPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SpecialSafeGuardPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIsOpenSafeGuard()) {
            builder.setIsOpenSafeGuard(this.getIsOpenSafeGuard());
            fieldCnt++;
        }  else if (builder.hasIsOpenSafeGuard()) {
            // 清理IsOpenSafeGuard
            builder.clearIsOpenSafeGuard();
            fieldCnt++;
        }
        if (this.safeGuardReasons != null) {
            BasicPB.Int32SetPB.Builder tmpBuilder = BasicPB.Int32SetPB.newBuilder();
            final int tmpFieldCnt = this.safeGuardReasons.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSafeGuardReasons(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSafeGuardReasons();
            }
        }  else if (builder.hasSafeGuardReasons()) {
            // 清理SafeGuardReasons
            builder.clearSafeGuardReasons();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SpecialSafeGuardPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISOPENSAFEGUARD)) {
            builder.setIsOpenSafeGuard(this.getIsOpenSafeGuard());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SAFEGUARDREASONS) && this.safeGuardReasons != null) {
            final boolean needClear = !builder.hasSafeGuardReasons();
            final int tmpFieldCnt = this.safeGuardReasons.copyChangeToCs(builder.getSafeGuardReasonsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSafeGuardReasons();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SpecialSafeGuardPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISOPENSAFEGUARD)) {
            builder.setIsOpenSafeGuard(this.getIsOpenSafeGuard());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SAFEGUARDREASONS) && this.safeGuardReasons != null) {
            final boolean needClear = !builder.hasSafeGuardReasons();
            final int tmpFieldCnt = this.safeGuardReasons.copyChangeToAndClearDeleteKeysCs(builder.getSafeGuardReasonsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSafeGuardReasons();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SpecialSafeGuardPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIsOpenSafeGuard()) {
            this.innerSetIsOpenSafeGuard(proto.getIsOpenSafeGuard());
        } else {
            this.innerSetIsOpenSafeGuard(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasSafeGuardReasons()) {
            this.getSafeGuardReasons().mergeFromCs(proto.getSafeGuardReasons());
        } else {
            if (this.safeGuardReasons != null) {
                this.safeGuardReasons.mergeFromCs(proto.getSafeGuardReasons());
            }
        }
        this.markAll();
        return SpecialSafeGuardProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SpecialSafeGuardPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIsOpenSafeGuard()) {
            this.setIsOpenSafeGuard(proto.getIsOpenSafeGuard());
            fieldCnt++;
        }
        if (proto.hasSafeGuardReasons()) {
            this.getSafeGuardReasons().mergeChangeFromCs(proto.getSafeGuardReasons());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SpecialSafeGuard.Builder getCopyDbBuilder() {
        final SpecialSafeGuard.Builder builder = SpecialSafeGuard.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SpecialSafeGuard.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIsOpenSafeGuard()) {
            builder.setIsOpenSafeGuard(this.getIsOpenSafeGuard());
            fieldCnt++;
        }  else if (builder.hasIsOpenSafeGuard()) {
            // 清理IsOpenSafeGuard
            builder.clearIsOpenSafeGuard();
            fieldCnt++;
        }
        if (this.safeGuardReasons != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.safeGuardReasons.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSafeGuardReasons(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSafeGuardReasons();
            }
        }  else if (builder.hasSafeGuardReasons()) {
            // 清理SafeGuardReasons
            builder.clearSafeGuardReasons();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SpecialSafeGuard.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISOPENSAFEGUARD)) {
            builder.setIsOpenSafeGuard(this.getIsOpenSafeGuard());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SAFEGUARDREASONS) && this.safeGuardReasons != null) {
            final boolean needClear = !builder.hasSafeGuardReasons();
            final int tmpFieldCnt = this.safeGuardReasons.copyChangeToDb(builder.getSafeGuardReasonsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSafeGuardReasons();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SpecialSafeGuard proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIsOpenSafeGuard()) {
            this.innerSetIsOpenSafeGuard(proto.getIsOpenSafeGuard());
        } else {
            this.innerSetIsOpenSafeGuard(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasSafeGuardReasons()) {
            this.getSafeGuardReasons().mergeFromDb(proto.getSafeGuardReasons());
        } else {
            if (this.safeGuardReasons != null) {
                this.safeGuardReasons.mergeFromDb(proto.getSafeGuardReasons());
            }
        }
        this.markAll();
        return SpecialSafeGuardProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SpecialSafeGuard proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIsOpenSafeGuard()) {
            this.setIsOpenSafeGuard(proto.getIsOpenSafeGuard());
            fieldCnt++;
        }
        if (proto.hasSafeGuardReasons()) {
            this.getSafeGuardReasons().mergeChangeFromDb(proto.getSafeGuardReasons());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SpecialSafeGuard.Builder getCopySsBuilder() {
        final SpecialSafeGuard.Builder builder = SpecialSafeGuard.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SpecialSafeGuard.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIsOpenSafeGuard()) {
            builder.setIsOpenSafeGuard(this.getIsOpenSafeGuard());
            fieldCnt++;
        }  else if (builder.hasIsOpenSafeGuard()) {
            // 清理IsOpenSafeGuard
            builder.clearIsOpenSafeGuard();
            fieldCnt++;
        }
        if (this.safeGuardReasons != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.safeGuardReasons.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSafeGuardReasons(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSafeGuardReasons();
            }
        }  else if (builder.hasSafeGuardReasons()) {
            // 清理SafeGuardReasons
            builder.clearSafeGuardReasons();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SpecialSafeGuard.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISOPENSAFEGUARD)) {
            builder.setIsOpenSafeGuard(this.getIsOpenSafeGuard());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SAFEGUARDREASONS) && this.safeGuardReasons != null) {
            final boolean needClear = !builder.hasSafeGuardReasons();
            final int tmpFieldCnt = this.safeGuardReasons.copyChangeToSs(builder.getSafeGuardReasonsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSafeGuardReasons();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SpecialSafeGuard proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIsOpenSafeGuard()) {
            this.innerSetIsOpenSafeGuard(proto.getIsOpenSafeGuard());
        } else {
            this.innerSetIsOpenSafeGuard(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasSafeGuardReasons()) {
            this.getSafeGuardReasons().mergeFromSs(proto.getSafeGuardReasons());
        } else {
            if (this.safeGuardReasons != null) {
                this.safeGuardReasons.mergeFromSs(proto.getSafeGuardReasons());
            }
        }
        this.markAll();
        return SpecialSafeGuardProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SpecialSafeGuard proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIsOpenSafeGuard()) {
            this.setIsOpenSafeGuard(proto.getIsOpenSafeGuard());
            fieldCnt++;
        }
        if (proto.hasSafeGuardReasons()) {
            this.getSafeGuardReasons().mergeChangeFromSs(proto.getSafeGuardReasons());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SpecialSafeGuard.Builder builder = SpecialSafeGuard.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SAFEGUARDREASONS) && this.safeGuardReasons != null) {
            this.safeGuardReasons.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.safeGuardReasons != null) {
            this.safeGuardReasons.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SpecialSafeGuardProp)) {
            return false;
        }
        final SpecialSafeGuardProp otherNode = (SpecialSafeGuardProp) node;
        if (this.isOpenSafeGuard != otherNode.isOpenSafeGuard) {
            return false;
        }
        if (!this.getSafeGuardReasons().compareDataTo(otherNode.getSafeGuardReasons())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
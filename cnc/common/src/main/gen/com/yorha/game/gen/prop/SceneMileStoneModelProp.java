package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.SceneMileStoneModel;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.SceneMileStoneModelPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class SceneMileStoneModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CURMILESTONE = 0;
    public static final int FIELD_INDEX_MILESTONEINFO = 1;
    public static final int FIELD_INDEX_LASTRESETTSMS = 2;
    public static final int FIELD_INDEX_TEMPLATEID = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int curMileStone = Constant.DEFAULT_INT_VALUE;
    private Int32MileStoneInfoMapProp mileStoneInfo = null;
    private long lastResetTsMs = Constant.DEFAULT_LONG_VALUE;
    private int templateId = Constant.DEFAULT_INT_VALUE;

    public SceneMileStoneModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SceneMileStoneModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get curMileStone
     *
     * @return curMileStone value
     */
    public int getCurMileStone() {
        return this.curMileStone;
    }

    /**
     * set curMileStone && set marked
     *
     * @param curMileStone new value
     * @return current object
     */
    public SceneMileStoneModelProp setCurMileStone(int curMileStone) {
        if (this.curMileStone != curMileStone) {
            this.mark(FIELD_INDEX_CURMILESTONE);
            this.curMileStone = curMileStone;
        }
        return this;
    }

    /**
     * inner set curMileStone
     *
     * @param curMileStone new value
     */
    private void innerSetCurMileStone(int curMileStone) {
        this.curMileStone = curMileStone;
    }

    /**
     * get mileStoneInfo
     *
     * @return mileStoneInfo value
     */
    public Int32MileStoneInfoMapProp getMileStoneInfo() {
        if (this.mileStoneInfo == null) {
            this.mileStoneInfo = new Int32MileStoneInfoMapProp(this, FIELD_INDEX_MILESTONEINFO);
        }
        return this.mileStoneInfo;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putMileStoneInfoV(MileStoneInfoProp v) {
        this.getMileStoneInfo().put(v.getMileStoneId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public MileStoneInfoProp addEmptyMileStoneInfo(Integer k) {
        return this.getMileStoneInfo().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getMileStoneInfoSize() {
        if (this.mileStoneInfo == null) {
            return 0;
        }
        return this.mileStoneInfo.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isMileStoneInfoEmpty() {
        if (this.mileStoneInfo == null) {
            return true;
        }
        return this.mileStoneInfo.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public MileStoneInfoProp getMileStoneInfoV(Integer k) {
        if (this.mileStoneInfo == null || !this.mileStoneInfo.containsKey(k)) {
            return null;
        }
        return this.mileStoneInfo.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearMileStoneInfo() {
        if (this.mileStoneInfo != null) {
            this.mileStoneInfo.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeMileStoneInfoV(Integer k) {
        if (this.mileStoneInfo != null) {
            this.mileStoneInfo.remove(k);
        }
    }
    /**
     * get lastResetTsMs
     *
     * @return lastResetTsMs value
     */
    public long getLastResetTsMs() {
        return this.lastResetTsMs;
    }

    /**
     * set lastResetTsMs && set marked
     *
     * @param lastResetTsMs new value
     * @return current object
     */
    public SceneMileStoneModelProp setLastResetTsMs(long lastResetTsMs) {
        if (this.lastResetTsMs != lastResetTsMs) {
            this.mark(FIELD_INDEX_LASTRESETTSMS);
            this.lastResetTsMs = lastResetTsMs;
        }
        return this;
    }

    /**
     * inner set lastResetTsMs
     *
     * @param lastResetTsMs new value
     */
    private void innerSetLastResetTsMs(long lastResetTsMs) {
        this.lastResetTsMs = lastResetTsMs;
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public SceneMileStoneModelProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneMileStoneModelPB.Builder getCopyCsBuilder() {
        final SceneMileStoneModelPB.Builder builder = SceneMileStoneModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SceneMileStoneModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurMileStone() != 0) {
            builder.setCurMileStone(this.getCurMileStone());
            fieldCnt++;
        }  else if (builder.hasCurMileStone()) {
            // 清理CurMileStone
            builder.clearCurMileStone();
            fieldCnt++;
        }
        if (this.mileStoneInfo != null) {
            StructPB.Int32MileStoneInfoMapPB.Builder tmpBuilder = StructPB.Int32MileStoneInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.mileStoneInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMileStoneInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMileStoneInfo();
            }
        }  else if (builder.hasMileStoneInfo()) {
            // 清理MileStoneInfo
            builder.clearMileStoneInfo();
            fieldCnt++;
        }
        if (this.getLastResetTsMs() != 0L) {
            builder.setLastResetTsMs(this.getLastResetTsMs());
            fieldCnt++;
        }  else if (builder.hasLastResetTsMs()) {
            // 清理LastResetTsMs
            builder.clearLastResetTsMs();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SceneMileStoneModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURMILESTONE)) {
            builder.setCurMileStone(this.getCurMileStone());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MILESTONEINFO) && this.mileStoneInfo != null) {
            final boolean needClear = !builder.hasMileStoneInfo();
            final int tmpFieldCnt = this.mileStoneInfo.copyChangeToCs(builder.getMileStoneInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMileStoneInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTRESETTSMS)) {
            builder.setLastResetTsMs(this.getLastResetTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SceneMileStoneModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURMILESTONE)) {
            builder.setCurMileStone(this.getCurMileStone());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MILESTONEINFO) && this.mileStoneInfo != null) {
            final boolean needClear = !builder.hasMileStoneInfo();
            final int tmpFieldCnt = this.mileStoneInfo.copyChangeToAndClearDeleteKeysCs(builder.getMileStoneInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMileStoneInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTRESETTSMS)) {
            builder.setLastResetTsMs(this.getLastResetTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SceneMileStoneModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurMileStone()) {
            this.innerSetCurMileStone(proto.getCurMileStone());
        } else {
            this.innerSetCurMileStone(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMileStoneInfo()) {
            this.getMileStoneInfo().mergeFromCs(proto.getMileStoneInfo());
        } else {
            if (this.mileStoneInfo != null) {
                this.mileStoneInfo.mergeFromCs(proto.getMileStoneInfo());
            }
        }
        if (proto.hasLastResetTsMs()) {
            this.innerSetLastResetTsMs(proto.getLastResetTsMs());
        } else {
            this.innerSetLastResetTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SceneMileStoneModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SceneMileStoneModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurMileStone()) {
            this.setCurMileStone(proto.getCurMileStone());
            fieldCnt++;
        }
        if (proto.hasMileStoneInfo()) {
            this.getMileStoneInfo().mergeChangeFromCs(proto.getMileStoneInfo());
            fieldCnt++;
        }
        if (proto.hasLastResetTsMs()) {
            this.setLastResetTsMs(proto.getLastResetTsMs());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneMileStoneModel.Builder getCopyDbBuilder() {
        final SceneMileStoneModel.Builder builder = SceneMileStoneModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SceneMileStoneModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurMileStone() != 0) {
            builder.setCurMileStone(this.getCurMileStone());
            fieldCnt++;
        }  else if (builder.hasCurMileStone()) {
            // 清理CurMileStone
            builder.clearCurMileStone();
            fieldCnt++;
        }
        if (this.mileStoneInfo != null) {
            Struct.Int32MileStoneInfoMap.Builder tmpBuilder = Struct.Int32MileStoneInfoMap.newBuilder();
            final int tmpFieldCnt = this.mileStoneInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMileStoneInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMileStoneInfo();
            }
        }  else if (builder.hasMileStoneInfo()) {
            // 清理MileStoneInfo
            builder.clearMileStoneInfo();
            fieldCnt++;
        }
        if (this.getLastResetTsMs() != 0L) {
            builder.setLastResetTsMs(this.getLastResetTsMs());
            fieldCnt++;
        }  else if (builder.hasLastResetTsMs()) {
            // 清理LastResetTsMs
            builder.clearLastResetTsMs();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SceneMileStoneModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURMILESTONE)) {
            builder.setCurMileStone(this.getCurMileStone());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MILESTONEINFO) && this.mileStoneInfo != null) {
            final boolean needClear = !builder.hasMileStoneInfo();
            final int tmpFieldCnt = this.mileStoneInfo.copyChangeToDb(builder.getMileStoneInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMileStoneInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTRESETTSMS)) {
            builder.setLastResetTsMs(this.getLastResetTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SceneMileStoneModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurMileStone()) {
            this.innerSetCurMileStone(proto.getCurMileStone());
        } else {
            this.innerSetCurMileStone(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMileStoneInfo()) {
            this.getMileStoneInfo().mergeFromDb(proto.getMileStoneInfo());
        } else {
            if (this.mileStoneInfo != null) {
                this.mileStoneInfo.mergeFromDb(proto.getMileStoneInfo());
            }
        }
        if (proto.hasLastResetTsMs()) {
            this.innerSetLastResetTsMs(proto.getLastResetTsMs());
        } else {
            this.innerSetLastResetTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SceneMileStoneModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SceneMileStoneModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurMileStone()) {
            this.setCurMileStone(proto.getCurMileStone());
            fieldCnt++;
        }
        if (proto.hasMileStoneInfo()) {
            this.getMileStoneInfo().mergeChangeFromDb(proto.getMileStoneInfo());
            fieldCnt++;
        }
        if (proto.hasLastResetTsMs()) {
            this.setLastResetTsMs(proto.getLastResetTsMs());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneMileStoneModel.Builder getCopySsBuilder() {
        final SceneMileStoneModel.Builder builder = SceneMileStoneModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SceneMileStoneModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurMileStone() != 0) {
            builder.setCurMileStone(this.getCurMileStone());
            fieldCnt++;
        }  else if (builder.hasCurMileStone()) {
            // 清理CurMileStone
            builder.clearCurMileStone();
            fieldCnt++;
        }
        if (this.mileStoneInfo != null) {
            Struct.Int32MileStoneInfoMap.Builder tmpBuilder = Struct.Int32MileStoneInfoMap.newBuilder();
            final int tmpFieldCnt = this.mileStoneInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMileStoneInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMileStoneInfo();
            }
        }  else if (builder.hasMileStoneInfo()) {
            // 清理MileStoneInfo
            builder.clearMileStoneInfo();
            fieldCnt++;
        }
        if (this.getLastResetTsMs() != 0L) {
            builder.setLastResetTsMs(this.getLastResetTsMs());
            fieldCnt++;
        }  else if (builder.hasLastResetTsMs()) {
            // 清理LastResetTsMs
            builder.clearLastResetTsMs();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SceneMileStoneModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURMILESTONE)) {
            builder.setCurMileStone(this.getCurMileStone());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MILESTONEINFO) && this.mileStoneInfo != null) {
            final boolean needClear = !builder.hasMileStoneInfo();
            final int tmpFieldCnt = this.mileStoneInfo.copyChangeToSs(builder.getMileStoneInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMileStoneInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTRESETTSMS)) {
            builder.setLastResetTsMs(this.getLastResetTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SceneMileStoneModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurMileStone()) {
            this.innerSetCurMileStone(proto.getCurMileStone());
        } else {
            this.innerSetCurMileStone(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMileStoneInfo()) {
            this.getMileStoneInfo().mergeFromSs(proto.getMileStoneInfo());
        } else {
            if (this.mileStoneInfo != null) {
                this.mileStoneInfo.mergeFromSs(proto.getMileStoneInfo());
            }
        }
        if (proto.hasLastResetTsMs()) {
            this.innerSetLastResetTsMs(proto.getLastResetTsMs());
        } else {
            this.innerSetLastResetTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SceneMileStoneModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SceneMileStoneModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurMileStone()) {
            this.setCurMileStone(proto.getCurMileStone());
            fieldCnt++;
        }
        if (proto.hasMileStoneInfo()) {
            this.getMileStoneInfo().mergeChangeFromSs(proto.getMileStoneInfo());
            fieldCnt++;
        }
        if (proto.hasLastResetTsMs()) {
            this.setLastResetTsMs(proto.getLastResetTsMs());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SceneMileStoneModel.Builder builder = SceneMileStoneModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_MILESTONEINFO) && this.mileStoneInfo != null) {
            this.mileStoneInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.mileStoneInfo != null) {
            this.mileStoneInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SceneMileStoneModelProp)) {
            return false;
        }
        final SceneMileStoneModelProp otherNode = (SceneMileStoneModelProp) node;
        if (this.curMileStone != otherNode.curMileStone) {
            return false;
        }
        if (!this.getMileStoneInfo().compareDataTo(otherNode.getMileStoneInfo())) {
            return false;
        }
        if (this.lastResetTsMs != otherNode.lastResetTsMs) {
            return false;
        }
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
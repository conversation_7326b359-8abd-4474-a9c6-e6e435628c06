package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.ClanScoreItem;
import com.yorha.proto.PlayerPB.ClanScoreItemPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanScoreItemProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_SCORETYPE = 0;
    public static final int FIELD_INDEX_SCORE = 1;
    public static final int FIELD_INDEX_TODAYADD = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int scoreType = Constant.DEFAULT_INT_VALUE;
    private long score = Constant.DEFAULT_LONG_VALUE;
    private long todayAdd = Constant.DEFAULT_LONG_VALUE;

    public ClanScoreItemProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanScoreItemProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get scoreType
     *
     * @return scoreType value
     */
    public int getScoreType() {
        return this.scoreType;
    }

    /**
     * set scoreType && set marked
     *
     * @param scoreType new value
     * @return current object
     */
    public ClanScoreItemProp setScoreType(int scoreType) {
        if (this.scoreType != scoreType) {
            this.mark(FIELD_INDEX_SCORETYPE);
            this.scoreType = scoreType;
        }
        return this;
    }

    /**
     * inner set scoreType
     *
     * @param scoreType new value
     */
    private void innerSetScoreType(int scoreType) {
        this.scoreType = scoreType;
    }

    /**
     * get score
     *
     * @return score value
     */
    public long getScore() {
        return this.score;
    }

    /**
     * set score && set marked
     *
     * @param score new value
     * @return current object
     */
    public ClanScoreItemProp setScore(long score) {
        if (this.score != score) {
            this.mark(FIELD_INDEX_SCORE);
            this.score = score;
        }
        return this;
    }

    /**
     * inner set score
     *
     * @param score new value
     */
    private void innerSetScore(long score) {
        this.score = score;
    }

    /**
     * get todayAdd
     *
     * @return todayAdd value
     */
    public long getTodayAdd() {
        return this.todayAdd;
    }

    /**
     * set todayAdd && set marked
     *
     * @param todayAdd new value
     * @return current object
     */
    public ClanScoreItemProp setTodayAdd(long todayAdd) {
        if (this.todayAdd != todayAdd) {
            this.mark(FIELD_INDEX_TODAYADD);
            this.todayAdd = todayAdd;
        }
        return this;
    }

    /**
     * inner set todayAdd
     *
     * @param todayAdd new value
     */
    private void innerSetTodayAdd(long todayAdd) {
        this.todayAdd = todayAdd;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanScoreItemPB.Builder getCopyCsBuilder() {
        final ClanScoreItemPB.Builder builder = ClanScoreItemPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanScoreItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getScoreType() != 0) {
            builder.setScoreType(this.getScoreType());
            fieldCnt++;
        }  else if (builder.hasScoreType()) {
            // 清理ScoreType
            builder.clearScoreType();
            fieldCnt++;
        }
        if (this.getScore() != 0L) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (this.getTodayAdd() != 0L) {
            builder.setTodayAdd(this.getTodayAdd());
            fieldCnt++;
        }  else if (builder.hasTodayAdd()) {
            // 清理TodayAdd
            builder.clearTodayAdd();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanScoreItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SCORETYPE)) {
            builder.setScoreType(this.getScoreType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TODAYADD)) {
            builder.setTodayAdd(this.getTodayAdd());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanScoreItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SCORETYPE)) {
            builder.setScoreType(this.getScoreType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TODAYADD)) {
            builder.setTodayAdd(this.getTodayAdd());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanScoreItemPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasScoreType()) {
            this.innerSetScoreType(proto.getScoreType());
        } else {
            this.innerSetScoreType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTodayAdd()) {
            this.innerSetTodayAdd(proto.getTodayAdd());
        } else {
            this.innerSetTodayAdd(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanScoreItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanScoreItemPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasScoreType()) {
            this.setScoreType(proto.getScoreType());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasTodayAdd()) {
            this.setTodayAdd(proto.getTodayAdd());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanScoreItem.Builder getCopyDbBuilder() {
        final ClanScoreItem.Builder builder = ClanScoreItem.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanScoreItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getScoreType() != 0) {
            builder.setScoreType(this.getScoreType());
            fieldCnt++;
        }  else if (builder.hasScoreType()) {
            // 清理ScoreType
            builder.clearScoreType();
            fieldCnt++;
        }
        if (this.getScore() != 0L) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (this.getTodayAdd() != 0L) {
            builder.setTodayAdd(this.getTodayAdd());
            fieldCnt++;
        }  else if (builder.hasTodayAdd()) {
            // 清理TodayAdd
            builder.clearTodayAdd();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanScoreItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SCORETYPE)) {
            builder.setScoreType(this.getScoreType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TODAYADD)) {
            builder.setTodayAdd(this.getTodayAdd());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanScoreItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasScoreType()) {
            this.innerSetScoreType(proto.getScoreType());
        } else {
            this.innerSetScoreType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTodayAdd()) {
            this.innerSetTodayAdd(proto.getTodayAdd());
        } else {
            this.innerSetTodayAdd(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanScoreItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanScoreItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasScoreType()) {
            this.setScoreType(proto.getScoreType());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasTodayAdd()) {
            this.setTodayAdd(proto.getTodayAdd());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanScoreItem.Builder getCopySsBuilder() {
        final ClanScoreItem.Builder builder = ClanScoreItem.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanScoreItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getScoreType() != 0) {
            builder.setScoreType(this.getScoreType());
            fieldCnt++;
        }  else if (builder.hasScoreType()) {
            // 清理ScoreType
            builder.clearScoreType();
            fieldCnt++;
        }
        if (this.getScore() != 0L) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (this.getTodayAdd() != 0L) {
            builder.setTodayAdd(this.getTodayAdd());
            fieldCnt++;
        }  else if (builder.hasTodayAdd()) {
            // 清理TodayAdd
            builder.clearTodayAdd();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanScoreItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SCORETYPE)) {
            builder.setScoreType(this.getScoreType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TODAYADD)) {
            builder.setTodayAdd(this.getTodayAdd());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanScoreItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasScoreType()) {
            this.innerSetScoreType(proto.getScoreType());
        } else {
            this.innerSetScoreType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTodayAdd()) {
            this.innerSetTodayAdd(proto.getTodayAdd());
        } else {
            this.innerSetTodayAdd(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanScoreItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanScoreItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasScoreType()) {
            this.setScoreType(proto.getScoreType());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasTodayAdd()) {
            this.setTodayAdd(proto.getTodayAdd());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanScoreItem.Builder builder = ClanScoreItem.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.scoreType;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanScoreItemProp)) {
            return false;
        }
        final ClanScoreItemProp otherNode = (ClanScoreItemProp) node;
        if (this.scoreType != otherNode.scoreType) {
            return false;
        }
        if (this.score != otherNode.score) {
            return false;
        }
        if (this.todayAdd != otherNode.todayAdd) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
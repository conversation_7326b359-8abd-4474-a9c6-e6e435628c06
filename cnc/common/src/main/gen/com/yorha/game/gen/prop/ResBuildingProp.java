package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.ResBuilding.ResBuildingEntity;
import com.yorha.proto.ResBuilding;
import com.yorha.proto.Struct;
import com.yorha.proto.ResBuildingPB.ResBuildingEntityPB;
import com.yorha.proto.ResBuildingPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ResBuildingProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TEMPLATEID = 0;
    public static final int FIELD_INDEX_STATE = 1;
    public static final int FIELD_INDEX_POINT = 2;
    public static final int FIELD_INDEX_CLANID = 3;
    public static final int FIELD_INDEX_CLANSIMPLENAME = 4;
    public static final int FIELD_INDEX_CURNUM = 5;
    public static final int FIELD_INDEX_COLLECT = 6;
    public static final int FIELD_INDEX_ARROW = 7;
    public static final int FIELD_INDEX_EXPRESSION = 8;

    public static final int FIELD_COUNT = 9;

    private long markBits0 = 0L;
    private PropertyChangeListener listener;

    private int templateId = Constant.DEFAULT_INT_VALUE;
    private ResourceBuildingState state = ResourceBuildingState.forNumber(0);
    private PointProp point = null;
    private long clanId = Constant.DEFAULT_LONG_VALUE;
    private String clanSimpleName = Constant.DEFAULT_STR_VALUE;
    private int curNum = Constant.DEFAULT_INT_VALUE;
    private CollectInfoProp collect = null;
    private Int64ArmyArrowItemMapProp arrow = null;
    private ExpressionProp expression = null;

    public ResBuildingProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ResBuildingProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public ResBuildingProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }

    /**
     * get state
     *
     * @return state value
     */
    public ResourceBuildingState getState() {
        return this.state;
    }

    /**
     * set state && set marked
     *
     * @param state new value
     * @return current object
     */
    public ResBuildingProp setState(ResourceBuildingState state) {
        if (state == null) {
            throw new NullPointerException();
        }
        if (this.state != state) {
            this.mark(FIELD_INDEX_STATE);
            this.state = state;
        }
        return this;
    }

    /**
     * inner set state
     *
     * @param state new value
     */
    private void innerSetState(ResourceBuildingState state) {
        this.state = state;
    }

    /**
     * get point
     *
     * @return point value
     */
    public PointProp getPoint() {
        if (this.point == null) {
            this.point = new PointProp(this, FIELD_INDEX_POINT);
        }
        return this.point;
    }

    /**
     * get clanId
     *
     * @return clanId value
     */
    public long getClanId() {
        return this.clanId;
    }

    /**
     * set clanId && set marked
     *
     * @param clanId new value
     * @return current object
     */
    public ResBuildingProp setClanId(long clanId) {
        if (this.clanId != clanId) {
            this.mark(FIELD_INDEX_CLANID);
            this.clanId = clanId;
        }
        return this;
    }

    /**
     * inner set clanId
     *
     * @param clanId new value
     */
    private void innerSetClanId(long clanId) {
        this.clanId = clanId;
    }

    /**
     * get clanSimpleName
     *
     * @return clanSimpleName value
     */
    public String getClanSimpleName() {
        return this.clanSimpleName;
    }

    /**
     * set clanSimpleName && set marked
     *
     * @param clanSimpleName new value
     * @return current object
     */
    public ResBuildingProp setClanSimpleName(String clanSimpleName) {
        if (clanSimpleName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanSimpleName, clanSimpleName)) {
            this.mark(FIELD_INDEX_CLANSIMPLENAME);
            this.clanSimpleName = clanSimpleName;
        }
        return this;
    }

    /**
     * inner set clanSimpleName
     *
     * @param clanSimpleName new value
     */
    private void innerSetClanSimpleName(String clanSimpleName) {
        this.clanSimpleName = clanSimpleName;
    }

    /**
     * get curNum
     *
     * @return curNum value
     */
    public int getCurNum() {
        return this.curNum;
    }

    /**
     * set curNum && set marked
     *
     * @param curNum new value
     * @return current object
     */
    public ResBuildingProp setCurNum(int curNum) {
        if (this.curNum != curNum) {
            this.mark(FIELD_INDEX_CURNUM);
            this.curNum = curNum;
        }
        return this;
    }

    /**
     * inner set curNum
     *
     * @param curNum new value
     */
    private void innerSetCurNum(int curNum) {
        this.curNum = curNum;
    }

    /**
     * get collect
     *
     * @return collect value
     */
    public CollectInfoProp getCollect() {
        if (this.collect == null) {
            this.collect = new CollectInfoProp(this, FIELD_INDEX_COLLECT);
        }
        return this.collect;
    }

    /**
     * get arrow
     *
     * @return arrow value
     */
    public Int64ArmyArrowItemMapProp getArrow() {
        if (this.arrow == null) {
            this.arrow = new Int64ArmyArrowItemMapProp(this, FIELD_INDEX_ARROW);
        }
        return this.arrow;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putArrowV(ArmyArrowItemProp v) {
        this.getArrow().put(v.getArmyId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ArmyArrowItemProp addEmptyArrow(Long k) {
        return this.getArrow().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getArrowSize() {
        if (this.arrow == null) {
            return 0;
        }
        return this.arrow.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isArrowEmpty() {
        if (this.arrow == null) {
            return true;
        }
        return this.arrow.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ArmyArrowItemProp getArrowV(Long k) {
        if (this.arrow == null || !this.arrow.containsKey(k)) {
            return null;
        }
        return this.arrow.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearArrow() {
        if (this.arrow != null) {
            this.arrow.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeArrowV(Long k) {
        if (this.arrow != null) {
            this.arrow.remove(k);
        }
    }
    /**
     * get expression
     *
     * @return expression value
     */
    public ExpressionProp getExpression() {
        if (this.expression == null) {
            this.expression = new ExpressionProp(this, FIELD_INDEX_EXPRESSION);
        }
        return this.expression;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ResBuildingEntityPB.Builder getCopyCsBuilder() {
        final ResBuildingEntityPB.Builder builder = ResBuildingEntityPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ResBuildingEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getState() != ResourceBuildingState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.point != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.point.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasClanSimpleName()) {
            // 清理ClanSimpleName
            builder.clearClanSimpleName();
            fieldCnt++;
        }
        if (this.getCurNum() != 0) {
            builder.setCurNum(this.getCurNum());
            fieldCnt++;
        }  else if (builder.hasCurNum()) {
            // 清理CurNum
            builder.clearCurNum();
            fieldCnt++;
        }
        if (this.collect != null) {
            ResBuildingPB.CollectInfoPB.Builder tmpBuilder = ResBuildingPB.CollectInfoPB.newBuilder();
            final int tmpFieldCnt = this.collect.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCollect(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCollect();
            }
        }  else if (builder.hasCollect()) {
            // 清理Collect
            builder.clearCollect();
            fieldCnt++;
        }
        if (this.arrow != null) {
            StructPB.Int64ArmyArrowItemMapPB.Builder tmpBuilder = StructPB.Int64ArmyArrowItemMapPB.newBuilder();
            final int tmpFieldCnt = this.arrow.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArrow(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArrow();
            }
        }  else if (builder.hasArrow()) {
            // 清理Arrow
            builder.clearArrow();
            fieldCnt++;
        }
        if (this.expression != null) {
            StructPB.ExpressionPB.Builder tmpBuilder = StructPB.ExpressionPB.newBuilder();
            final int tmpFieldCnt = this.expression.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExpression(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExpression();
            }
        }  else if (builder.hasExpression()) {
            // 清理Expression
            builder.clearExpression();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ResBuildingEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURNUM)) {
            builder.setCurNum(this.getCurNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COLLECT) && this.collect != null) {
            final boolean needClear = !builder.hasCollect();
            final int tmpFieldCnt = this.collect.copyChangeToCs(builder.getCollectBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCollect();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARROW) && this.arrow != null) {
            final boolean needClear = !builder.hasArrow();
            final int tmpFieldCnt = this.arrow.copyChangeToCs(builder.getArrowBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArrow();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            final boolean needClear = !builder.hasExpression();
            final int tmpFieldCnt = this.expression.copyChangeToCs(builder.getExpressionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpression();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ResBuildingEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToAndClearDeleteKeysCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURNUM)) {
            builder.setCurNum(this.getCurNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COLLECT) && this.collect != null) {
            final boolean needClear = !builder.hasCollect();
            final int tmpFieldCnt = this.collect.copyChangeToAndClearDeleteKeysCs(builder.getCollectBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCollect();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARROW) && this.arrow != null) {
            final boolean needClear = !builder.hasArrow();
            final int tmpFieldCnt = this.arrow.copyChangeToAndClearDeleteKeysCs(builder.getArrowBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArrow();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            final boolean needClear = !builder.hasExpression();
            final int tmpFieldCnt = this.expression.copyChangeToAndClearDeleteKeysCs(builder.getExpressionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpression();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ResBuildingEntityPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(ResourceBuildingState.forNumber(0));
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromCs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromCs(proto.getPoint());
            }
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanSimpleName()) {
            this.innerSetClanSimpleName(proto.getClanSimpleName());
        } else {
            this.innerSetClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasCurNum()) {
            this.innerSetCurNum(proto.getCurNum());
        } else {
            this.innerSetCurNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCollect()) {
            this.getCollect().mergeFromCs(proto.getCollect());
        } else {
            if (this.collect != null) {
                this.collect.mergeFromCs(proto.getCollect());
            }
        }
        if (proto.hasArrow()) {
            this.getArrow().mergeFromCs(proto.getArrow());
        } else {
            if (this.arrow != null) {
                this.arrow.mergeFromCs(proto.getArrow());
            }
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeFromCs(proto.getExpression());
        } else {
            if (this.expression != null) {
                this.expression.mergeFromCs(proto.getExpression());
            }
        }
        this.markAll();
        return ResBuildingProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ResBuildingEntityPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromCs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasClanSimpleName()) {
            this.setClanSimpleName(proto.getClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasCurNum()) {
            this.setCurNum(proto.getCurNum());
            fieldCnt++;
        }
        if (proto.hasCollect()) {
            this.getCollect().mergeChangeFromCs(proto.getCollect());
            fieldCnt++;
        }
        if (proto.hasArrow()) {
            this.getArrow().mergeChangeFromCs(proto.getArrow());
            fieldCnt++;
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeChangeFromCs(proto.getExpression());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ResBuildingEntity.Builder getCopyDbBuilder() {
        final ResBuildingEntity.Builder builder = ResBuildingEntity.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ResBuildingEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getState() != ResourceBuildingState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasClanSimpleName()) {
            // 清理ClanSimpleName
            builder.clearClanSimpleName();
            fieldCnt++;
        }
        if (this.getCurNum() != 0) {
            builder.setCurNum(this.getCurNum());
            fieldCnt++;
        }  else if (builder.hasCurNum()) {
            // 清理CurNum
            builder.clearCurNum();
            fieldCnt++;
        }
        if (this.collect != null) {
            ResBuilding.CollectInfo.Builder tmpBuilder = ResBuilding.CollectInfo.newBuilder();
            final int tmpFieldCnt = this.collect.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCollect(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCollect();
            }
        }  else if (builder.hasCollect()) {
            // 清理Collect
            builder.clearCollect();
            fieldCnt++;
        }
        if (this.expression != null) {
            Struct.Expression.Builder tmpBuilder = Struct.Expression.newBuilder();
            final int tmpFieldCnt = this.expression.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExpression(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExpression();
            }
        }  else if (builder.hasExpression()) {
            // 清理Expression
            builder.clearExpression();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ResBuildingEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToDb(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURNUM)) {
            builder.setCurNum(this.getCurNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COLLECT) && this.collect != null) {
            final boolean needClear = !builder.hasCollect();
            final int tmpFieldCnt = this.collect.copyChangeToDb(builder.getCollectBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCollect();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            final boolean needClear = !builder.hasExpression();
            final int tmpFieldCnt = this.expression.copyChangeToDb(builder.getExpressionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpression();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ResBuildingEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(ResourceBuildingState.forNumber(0));
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromDb(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromDb(proto.getPoint());
            }
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanSimpleName()) {
            this.innerSetClanSimpleName(proto.getClanSimpleName());
        } else {
            this.innerSetClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasCurNum()) {
            this.innerSetCurNum(proto.getCurNum());
        } else {
            this.innerSetCurNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCollect()) {
            this.getCollect().mergeFromDb(proto.getCollect());
        } else {
            if (this.collect != null) {
                this.collect.mergeFromDb(proto.getCollect());
            }
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeFromDb(proto.getExpression());
        } else {
            if (this.expression != null) {
                this.expression.mergeFromDb(proto.getExpression());
            }
        }
        this.markAll();
        return ResBuildingProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ResBuildingEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromDb(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasClanSimpleName()) {
            this.setClanSimpleName(proto.getClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasCurNum()) {
            this.setCurNum(proto.getCurNum());
            fieldCnt++;
        }
        if (proto.hasCollect()) {
            this.getCollect().mergeChangeFromDb(proto.getCollect());
            fieldCnt++;
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeChangeFromDb(proto.getExpression());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ResBuildingEntity.Builder getCopySsBuilder() {
        final ResBuildingEntity.Builder builder = ResBuildingEntity.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ResBuildingEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getState() != ResourceBuildingState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasClanSimpleName()) {
            // 清理ClanSimpleName
            builder.clearClanSimpleName();
            fieldCnt++;
        }
        if (this.getCurNum() != 0) {
            builder.setCurNum(this.getCurNum());
            fieldCnt++;
        }  else if (builder.hasCurNum()) {
            // 清理CurNum
            builder.clearCurNum();
            fieldCnt++;
        }
        if (this.collect != null) {
            ResBuilding.CollectInfo.Builder tmpBuilder = ResBuilding.CollectInfo.newBuilder();
            final int tmpFieldCnt = this.collect.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCollect(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCollect();
            }
        }  else if (builder.hasCollect()) {
            // 清理Collect
            builder.clearCollect();
            fieldCnt++;
        }
        if (this.arrow != null) {
            Struct.Int64ArmyArrowItemMap.Builder tmpBuilder = Struct.Int64ArmyArrowItemMap.newBuilder();
            final int tmpFieldCnt = this.arrow.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArrow(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArrow();
            }
        }  else if (builder.hasArrow()) {
            // 清理Arrow
            builder.clearArrow();
            fieldCnt++;
        }
        if (this.expression != null) {
            Struct.Expression.Builder tmpBuilder = Struct.Expression.newBuilder();
            final int tmpFieldCnt = this.expression.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExpression(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExpression();
            }
        }  else if (builder.hasExpression()) {
            // 清理Expression
            builder.clearExpression();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ResBuildingEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToSs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURNUM)) {
            builder.setCurNum(this.getCurNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COLLECT) && this.collect != null) {
            final boolean needClear = !builder.hasCollect();
            final int tmpFieldCnt = this.collect.copyChangeToSs(builder.getCollectBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCollect();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARROW) && this.arrow != null) {
            final boolean needClear = !builder.hasArrow();
            final int tmpFieldCnt = this.arrow.copyChangeToSs(builder.getArrowBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArrow();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            final boolean needClear = !builder.hasExpression();
            final int tmpFieldCnt = this.expression.copyChangeToSs(builder.getExpressionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpression();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ResBuildingEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(ResourceBuildingState.forNumber(0));
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromSs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromSs(proto.getPoint());
            }
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanSimpleName()) {
            this.innerSetClanSimpleName(proto.getClanSimpleName());
        } else {
            this.innerSetClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasCurNum()) {
            this.innerSetCurNum(proto.getCurNum());
        } else {
            this.innerSetCurNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCollect()) {
            this.getCollect().mergeFromSs(proto.getCollect());
        } else {
            if (this.collect != null) {
                this.collect.mergeFromSs(proto.getCollect());
            }
        }
        if (proto.hasArrow()) {
            this.getArrow().mergeFromSs(proto.getArrow());
        } else {
            if (this.arrow != null) {
                this.arrow.mergeFromSs(proto.getArrow());
            }
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeFromSs(proto.getExpression());
        } else {
            if (this.expression != null) {
                this.expression.mergeFromSs(proto.getExpression());
            }
        }
        this.markAll();
        return ResBuildingProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ResBuildingEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromSs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasClanSimpleName()) {
            this.setClanSimpleName(proto.getClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasCurNum()) {
            this.setCurNum(proto.getCurNum());
            fieldCnt++;
        }
        if (proto.hasCollect()) {
            this.getCollect().mergeChangeFromSs(proto.getCollect());
            fieldCnt++;
        }
        if (proto.hasArrow()) {
            this.getArrow().mergeChangeFromSs(proto.getArrow());
            fieldCnt++;
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeChangeFromSs(proto.getExpression());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ResBuildingEntity.Builder builder = ResBuildingEntity.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            this.point.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_COLLECT) && this.collect != null) {
            this.collect.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ARROW) && this.arrow != null) {
            this.arrow.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            this.expression.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.point != null) {
            this.point.markAll();
        }
        if (this.collect != null) {
            this.collect.markAll();
        }
        if (this.arrow != null) {
            this.arrow.markAll();
        }
        if (this.expression != null) {
            this.expression.markAll();
        }
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("ResBuildingProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("ResBuildingProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ResBuildingProp)) {
            return false;
        }
        final ResBuildingProp otherNode = (ResBuildingProp) node;
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        if (this.state != otherNode.state) {
            return false;
        }
        if (!this.getPoint().compareDataTo(otherNode.getPoint())) {
            return false;
        }
        if (this.clanId != otherNode.clanId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanSimpleName, otherNode.clanSimpleName)) {
            return false;
        }
        if (this.curNum != otherNode.curNum) {
            return false;
        }
        if (!this.getCollect().compareDataTo(otherNode.getCollect())) {
            return false;
        }
        if (!this.getArrow().compareDataTo(otherNode.getArrow())) {
            return false;
        }
        if (!this.getExpression().compareDataTo(otherNode.getExpression())) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    public static ResBuildingProp of(ResBuildingEntity fullAttrDb, ResBuildingEntity changeAttrDb) {
        ResBuildingProp prop = new ResBuildingProp();
        prop.mergeFromDb(fullAttrDb);
        prop.mergeChangeFromDb(changeAttrDb);
        prop.unMarkAll();
        return prop;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 55;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
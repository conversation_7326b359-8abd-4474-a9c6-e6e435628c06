package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerGoodsHistory;
import com.yorha.proto.PlayerPB.PlayerGoodsHistoryPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerGoodsHistoryProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_GOODSID = 0;
    public static final int FIELD_INDEX_BOUGHTTIMES = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int goodsId = Constant.DEFAULT_INT_VALUE;
    private int boughtTimes = Constant.DEFAULT_INT_VALUE;

    public PlayerGoodsHistoryProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerGoodsHistoryProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get goodsId
     *
     * @return goodsId value
     */
    public int getGoodsId() {
        return this.goodsId;
    }

    /**
     * set goodsId && set marked
     *
     * @param goodsId new value
     * @return current object
     */
    public PlayerGoodsHistoryProp setGoodsId(int goodsId) {
        if (this.goodsId != goodsId) {
            this.mark(FIELD_INDEX_GOODSID);
            this.goodsId = goodsId;
        }
        return this;
    }

    /**
     * inner set goodsId
     *
     * @param goodsId new value
     */
    private void innerSetGoodsId(int goodsId) {
        this.goodsId = goodsId;
    }

    /**
     * get boughtTimes
     *
     * @return boughtTimes value
     */
    public int getBoughtTimes() {
        return this.boughtTimes;
    }

    /**
     * set boughtTimes && set marked
     *
     * @param boughtTimes new value
     * @return current object
     */
    public PlayerGoodsHistoryProp setBoughtTimes(int boughtTimes) {
        if (this.boughtTimes != boughtTimes) {
            this.mark(FIELD_INDEX_BOUGHTTIMES);
            this.boughtTimes = boughtTimes;
        }
        return this;
    }

    /**
     * inner set boughtTimes
     *
     * @param boughtTimes new value
     */
    private void innerSetBoughtTimes(int boughtTimes) {
        this.boughtTimes = boughtTimes;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerGoodsHistoryPB.Builder getCopyCsBuilder() {
        final PlayerGoodsHistoryPB.Builder builder = PlayerGoodsHistoryPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerGoodsHistoryPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGoodsId() != 0) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }  else if (builder.hasGoodsId()) {
            // 清理GoodsId
            builder.clearGoodsId();
            fieldCnt++;
        }
        if (this.getBoughtTimes() != 0) {
            builder.setBoughtTimes(this.getBoughtTimes());
            fieldCnt++;
        }  else if (builder.hasBoughtTimes()) {
            // 清理BoughtTimes
            builder.clearBoughtTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerGoodsHistoryPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODSID)) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHTTIMES)) {
            builder.setBoughtTimes(this.getBoughtTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerGoodsHistoryPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODSID)) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHTTIMES)) {
            builder.setBoughtTimes(this.getBoughtTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerGoodsHistoryPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGoodsId()) {
            this.innerSetGoodsId(proto.getGoodsId());
        } else {
            this.innerSetGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBoughtTimes()) {
            this.innerSetBoughtTimes(proto.getBoughtTimes());
        } else {
            this.innerSetBoughtTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerGoodsHistoryProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerGoodsHistoryPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGoodsId()) {
            this.setGoodsId(proto.getGoodsId());
            fieldCnt++;
        }
        if (proto.hasBoughtTimes()) {
            this.setBoughtTimes(proto.getBoughtTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerGoodsHistory.Builder getCopyDbBuilder() {
        final PlayerGoodsHistory.Builder builder = PlayerGoodsHistory.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerGoodsHistory.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGoodsId() != 0) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }  else if (builder.hasGoodsId()) {
            // 清理GoodsId
            builder.clearGoodsId();
            fieldCnt++;
        }
        if (this.getBoughtTimes() != 0) {
            builder.setBoughtTimes(this.getBoughtTimes());
            fieldCnt++;
        }  else if (builder.hasBoughtTimes()) {
            // 清理BoughtTimes
            builder.clearBoughtTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerGoodsHistory.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODSID)) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHTTIMES)) {
            builder.setBoughtTimes(this.getBoughtTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerGoodsHistory proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGoodsId()) {
            this.innerSetGoodsId(proto.getGoodsId());
        } else {
            this.innerSetGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBoughtTimes()) {
            this.innerSetBoughtTimes(proto.getBoughtTimes());
        } else {
            this.innerSetBoughtTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerGoodsHistoryProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerGoodsHistory proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGoodsId()) {
            this.setGoodsId(proto.getGoodsId());
            fieldCnt++;
        }
        if (proto.hasBoughtTimes()) {
            this.setBoughtTimes(proto.getBoughtTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerGoodsHistory.Builder getCopySsBuilder() {
        final PlayerGoodsHistory.Builder builder = PlayerGoodsHistory.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerGoodsHistory.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGoodsId() != 0) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }  else if (builder.hasGoodsId()) {
            // 清理GoodsId
            builder.clearGoodsId();
            fieldCnt++;
        }
        if (this.getBoughtTimes() != 0) {
            builder.setBoughtTimes(this.getBoughtTimes());
            fieldCnt++;
        }  else if (builder.hasBoughtTimes()) {
            // 清理BoughtTimes
            builder.clearBoughtTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerGoodsHistory.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODSID)) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHTTIMES)) {
            builder.setBoughtTimes(this.getBoughtTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerGoodsHistory proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGoodsId()) {
            this.innerSetGoodsId(proto.getGoodsId());
        } else {
            this.innerSetGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBoughtTimes()) {
            this.innerSetBoughtTimes(proto.getBoughtTimes());
        } else {
            this.innerSetBoughtTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerGoodsHistoryProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerGoodsHistory proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGoodsId()) {
            this.setGoodsId(proto.getGoodsId());
            fieldCnt++;
        }
        if (proto.hasBoughtTimes()) {
            this.setBoughtTimes(proto.getBoughtTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerGoodsHistory.Builder builder = PlayerGoodsHistory.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.goodsId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerGoodsHistoryProp)) {
            return false;
        }
        final PlayerGoodsHistoryProp otherNode = (PlayerGoodsHistoryProp) node;
        if (this.goodsId != otherNode.goodsId) {
            return false;
        }
        if (this.boughtTimes != otherNode.boughtTimes) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
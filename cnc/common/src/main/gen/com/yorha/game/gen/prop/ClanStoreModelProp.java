package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Clan.ClanStoreModel;
import com.yorha.proto.Struct;
import com.yorha.proto.ClanPB.ClanStoreModelPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanStoreModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_BUYRECORDS = 0;
    public static final int FIELD_INDEX_STOCKRECORDS = 1;
    public static final int FIELD_INDEX_STOREINFO = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private Int64ClanStoreLogItemMapProp buyRecords = null;
    private Int64ClanStoreLogItemMapProp stockRecords = null;
    private Int32ClanStoreItemInfoMapProp storeInfo = null;

    public ClanStoreModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanStoreModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get buyRecords
     *
     * @return buyRecords value
     */
    public Int64ClanStoreLogItemMapProp getBuyRecords() {
        if (this.buyRecords == null) {
            this.buyRecords = new Int64ClanStoreLogItemMapProp(this, FIELD_INDEX_BUYRECORDS);
        }
        return this.buyRecords;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putBuyRecordsV(ClanStoreLogItemProp v) {
        this.getBuyRecords().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ClanStoreLogItemProp addEmptyBuyRecords(Long k) {
        return this.getBuyRecords().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getBuyRecordsSize() {
        if (this.buyRecords == null) {
            return 0;
        }
        return this.buyRecords.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isBuyRecordsEmpty() {
        if (this.buyRecords == null) {
            return true;
        }
        return this.buyRecords.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ClanStoreLogItemProp getBuyRecordsV(Long k) {
        if (this.buyRecords == null || !this.buyRecords.containsKey(k)) {
            return null;
        }
        return this.buyRecords.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearBuyRecords() {
        if (this.buyRecords != null) {
            this.buyRecords.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeBuyRecordsV(Long k) {
        if (this.buyRecords != null) {
            this.buyRecords.remove(k);
        }
    }
    /**
     * get stockRecords
     *
     * @return stockRecords value
     */
    public Int64ClanStoreLogItemMapProp getStockRecords() {
        if (this.stockRecords == null) {
            this.stockRecords = new Int64ClanStoreLogItemMapProp(this, FIELD_INDEX_STOCKRECORDS);
        }
        return this.stockRecords;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putStockRecordsV(ClanStoreLogItemProp v) {
        this.getStockRecords().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ClanStoreLogItemProp addEmptyStockRecords(Long k) {
        return this.getStockRecords().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getStockRecordsSize() {
        if (this.stockRecords == null) {
            return 0;
        }
        return this.stockRecords.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isStockRecordsEmpty() {
        if (this.stockRecords == null) {
            return true;
        }
        return this.stockRecords.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ClanStoreLogItemProp getStockRecordsV(Long k) {
        if (this.stockRecords == null || !this.stockRecords.containsKey(k)) {
            return null;
        }
        return this.stockRecords.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearStockRecords() {
        if (this.stockRecords != null) {
            this.stockRecords.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeStockRecordsV(Long k) {
        if (this.stockRecords != null) {
            this.stockRecords.remove(k);
        }
    }
    /**
     * get storeInfo
     *
     * @return storeInfo value
     */
    public Int32ClanStoreItemInfoMapProp getStoreInfo() {
        if (this.storeInfo == null) {
            this.storeInfo = new Int32ClanStoreItemInfoMapProp(this, FIELD_INDEX_STOREINFO);
        }
        return this.storeInfo;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putStoreInfoV(ClanStoreItemInfoProp v) {
        this.getStoreInfo().put(v.getItemId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ClanStoreItemInfoProp addEmptyStoreInfo(Integer k) {
        return this.getStoreInfo().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getStoreInfoSize() {
        if (this.storeInfo == null) {
            return 0;
        }
        return this.storeInfo.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isStoreInfoEmpty() {
        if (this.storeInfo == null) {
            return true;
        }
        return this.storeInfo.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ClanStoreItemInfoProp getStoreInfoV(Integer k) {
        if (this.storeInfo == null || !this.storeInfo.containsKey(k)) {
            return null;
        }
        return this.storeInfo.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearStoreInfo() {
        if (this.storeInfo != null) {
            this.storeInfo.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeStoreInfoV(Integer k) {
        if (this.storeInfo != null) {
            this.storeInfo.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanStoreModelPB.Builder getCopyCsBuilder() {
        final ClanStoreModelPB.Builder builder = ClanStoreModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanStoreModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.buyRecords != null) {
            StructPB.Int64ClanStoreLogItemMapPB.Builder tmpBuilder = StructPB.Int64ClanStoreLogItemMapPB.newBuilder();
            final int tmpFieldCnt = this.buyRecords.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuyRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuyRecords();
            }
        }  else if (builder.hasBuyRecords()) {
            // 清理BuyRecords
            builder.clearBuyRecords();
            fieldCnt++;
        }
        if (this.stockRecords != null) {
            StructPB.Int64ClanStoreLogItemMapPB.Builder tmpBuilder = StructPB.Int64ClanStoreLogItemMapPB.newBuilder();
            final int tmpFieldCnt = this.stockRecords.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStockRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStockRecords();
            }
        }  else if (builder.hasStockRecords()) {
            // 清理StockRecords
            builder.clearStockRecords();
            fieldCnt++;
        }
        if (this.storeInfo != null) {
            StructPB.Int32ClanStoreItemInfoMapPB.Builder tmpBuilder = StructPB.Int32ClanStoreItemInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.storeInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStoreInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStoreInfo();
            }
        }  else if (builder.hasStoreInfo()) {
            // 清理StoreInfo
            builder.clearStoreInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanStoreModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUYRECORDS) && this.buyRecords != null) {
            final boolean needClear = !builder.hasBuyRecords();
            final int tmpFieldCnt = this.buyRecords.copyChangeToCs(builder.getBuyRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuyRecords();
            }
        }
        if (this.hasMark(FIELD_INDEX_STOCKRECORDS) && this.stockRecords != null) {
            final boolean needClear = !builder.hasStockRecords();
            final int tmpFieldCnt = this.stockRecords.copyChangeToCs(builder.getStockRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStockRecords();
            }
        }
        if (this.hasMark(FIELD_INDEX_STOREINFO) && this.storeInfo != null) {
            final boolean needClear = !builder.hasStoreInfo();
            final int tmpFieldCnt = this.storeInfo.copyChangeToCs(builder.getStoreInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStoreInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanStoreModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUYRECORDS) && this.buyRecords != null) {
            final boolean needClear = !builder.hasBuyRecords();
            final int tmpFieldCnt = this.buyRecords.copyChangeToAndClearDeleteKeysCs(builder.getBuyRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuyRecords();
            }
        }
        if (this.hasMark(FIELD_INDEX_STOCKRECORDS) && this.stockRecords != null) {
            final boolean needClear = !builder.hasStockRecords();
            final int tmpFieldCnt = this.stockRecords.copyChangeToAndClearDeleteKeysCs(builder.getStockRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStockRecords();
            }
        }
        if (this.hasMark(FIELD_INDEX_STOREINFO) && this.storeInfo != null) {
            final boolean needClear = !builder.hasStoreInfo();
            final int tmpFieldCnt = this.storeInfo.copyChangeToAndClearDeleteKeysCs(builder.getStoreInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStoreInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanStoreModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBuyRecords()) {
            this.getBuyRecords().mergeFromCs(proto.getBuyRecords());
        } else {
            if (this.buyRecords != null) {
                this.buyRecords.mergeFromCs(proto.getBuyRecords());
            }
        }
        if (proto.hasStockRecords()) {
            this.getStockRecords().mergeFromCs(proto.getStockRecords());
        } else {
            if (this.stockRecords != null) {
                this.stockRecords.mergeFromCs(proto.getStockRecords());
            }
        }
        if (proto.hasStoreInfo()) {
            this.getStoreInfo().mergeFromCs(proto.getStoreInfo());
        } else {
            if (this.storeInfo != null) {
                this.storeInfo.mergeFromCs(proto.getStoreInfo());
            }
        }
        this.markAll();
        return ClanStoreModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanStoreModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBuyRecords()) {
            this.getBuyRecords().mergeChangeFromCs(proto.getBuyRecords());
            fieldCnt++;
        }
        if (proto.hasStockRecords()) {
            this.getStockRecords().mergeChangeFromCs(proto.getStockRecords());
            fieldCnt++;
        }
        if (proto.hasStoreInfo()) {
            this.getStoreInfo().mergeChangeFromCs(proto.getStoreInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanStoreModel.Builder getCopyDbBuilder() {
        final ClanStoreModel.Builder builder = ClanStoreModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanStoreModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.buyRecords != null) {
            Struct.Int64ClanStoreLogItemMap.Builder tmpBuilder = Struct.Int64ClanStoreLogItemMap.newBuilder();
            final int tmpFieldCnt = this.buyRecords.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuyRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuyRecords();
            }
        }  else if (builder.hasBuyRecords()) {
            // 清理BuyRecords
            builder.clearBuyRecords();
            fieldCnt++;
        }
        if (this.stockRecords != null) {
            Struct.Int64ClanStoreLogItemMap.Builder tmpBuilder = Struct.Int64ClanStoreLogItemMap.newBuilder();
            final int tmpFieldCnt = this.stockRecords.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStockRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStockRecords();
            }
        }  else if (builder.hasStockRecords()) {
            // 清理StockRecords
            builder.clearStockRecords();
            fieldCnt++;
        }
        if (this.storeInfo != null) {
            Struct.Int32ClanStoreItemInfoMap.Builder tmpBuilder = Struct.Int32ClanStoreItemInfoMap.newBuilder();
            final int tmpFieldCnt = this.storeInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStoreInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStoreInfo();
            }
        }  else if (builder.hasStoreInfo()) {
            // 清理StoreInfo
            builder.clearStoreInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanStoreModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUYRECORDS) && this.buyRecords != null) {
            final boolean needClear = !builder.hasBuyRecords();
            final int tmpFieldCnt = this.buyRecords.copyChangeToDb(builder.getBuyRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuyRecords();
            }
        }
        if (this.hasMark(FIELD_INDEX_STOCKRECORDS) && this.stockRecords != null) {
            final boolean needClear = !builder.hasStockRecords();
            final int tmpFieldCnt = this.stockRecords.copyChangeToDb(builder.getStockRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStockRecords();
            }
        }
        if (this.hasMark(FIELD_INDEX_STOREINFO) && this.storeInfo != null) {
            final boolean needClear = !builder.hasStoreInfo();
            final int tmpFieldCnt = this.storeInfo.copyChangeToDb(builder.getStoreInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStoreInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanStoreModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBuyRecords()) {
            this.getBuyRecords().mergeFromDb(proto.getBuyRecords());
        } else {
            if (this.buyRecords != null) {
                this.buyRecords.mergeFromDb(proto.getBuyRecords());
            }
        }
        if (proto.hasStockRecords()) {
            this.getStockRecords().mergeFromDb(proto.getStockRecords());
        } else {
            if (this.stockRecords != null) {
                this.stockRecords.mergeFromDb(proto.getStockRecords());
            }
        }
        if (proto.hasStoreInfo()) {
            this.getStoreInfo().mergeFromDb(proto.getStoreInfo());
        } else {
            if (this.storeInfo != null) {
                this.storeInfo.mergeFromDb(proto.getStoreInfo());
            }
        }
        this.markAll();
        return ClanStoreModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanStoreModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBuyRecords()) {
            this.getBuyRecords().mergeChangeFromDb(proto.getBuyRecords());
            fieldCnt++;
        }
        if (proto.hasStockRecords()) {
            this.getStockRecords().mergeChangeFromDb(proto.getStockRecords());
            fieldCnt++;
        }
        if (proto.hasStoreInfo()) {
            this.getStoreInfo().mergeChangeFromDb(proto.getStoreInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanStoreModel.Builder getCopySsBuilder() {
        final ClanStoreModel.Builder builder = ClanStoreModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanStoreModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.buyRecords != null) {
            Struct.Int64ClanStoreLogItemMap.Builder tmpBuilder = Struct.Int64ClanStoreLogItemMap.newBuilder();
            final int tmpFieldCnt = this.buyRecords.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuyRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuyRecords();
            }
        }  else if (builder.hasBuyRecords()) {
            // 清理BuyRecords
            builder.clearBuyRecords();
            fieldCnt++;
        }
        if (this.stockRecords != null) {
            Struct.Int64ClanStoreLogItemMap.Builder tmpBuilder = Struct.Int64ClanStoreLogItemMap.newBuilder();
            final int tmpFieldCnt = this.stockRecords.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStockRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStockRecords();
            }
        }  else if (builder.hasStockRecords()) {
            // 清理StockRecords
            builder.clearStockRecords();
            fieldCnt++;
        }
        if (this.storeInfo != null) {
            Struct.Int32ClanStoreItemInfoMap.Builder tmpBuilder = Struct.Int32ClanStoreItemInfoMap.newBuilder();
            final int tmpFieldCnt = this.storeInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStoreInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStoreInfo();
            }
        }  else if (builder.hasStoreInfo()) {
            // 清理StoreInfo
            builder.clearStoreInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanStoreModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUYRECORDS) && this.buyRecords != null) {
            final boolean needClear = !builder.hasBuyRecords();
            final int tmpFieldCnt = this.buyRecords.copyChangeToSs(builder.getBuyRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuyRecords();
            }
        }
        if (this.hasMark(FIELD_INDEX_STOCKRECORDS) && this.stockRecords != null) {
            final boolean needClear = !builder.hasStockRecords();
            final int tmpFieldCnt = this.stockRecords.copyChangeToSs(builder.getStockRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStockRecords();
            }
        }
        if (this.hasMark(FIELD_INDEX_STOREINFO) && this.storeInfo != null) {
            final boolean needClear = !builder.hasStoreInfo();
            final int tmpFieldCnt = this.storeInfo.copyChangeToSs(builder.getStoreInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStoreInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanStoreModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBuyRecords()) {
            this.getBuyRecords().mergeFromSs(proto.getBuyRecords());
        } else {
            if (this.buyRecords != null) {
                this.buyRecords.mergeFromSs(proto.getBuyRecords());
            }
        }
        if (proto.hasStockRecords()) {
            this.getStockRecords().mergeFromSs(proto.getStockRecords());
        } else {
            if (this.stockRecords != null) {
                this.stockRecords.mergeFromSs(proto.getStockRecords());
            }
        }
        if (proto.hasStoreInfo()) {
            this.getStoreInfo().mergeFromSs(proto.getStoreInfo());
        } else {
            if (this.storeInfo != null) {
                this.storeInfo.mergeFromSs(proto.getStoreInfo());
            }
        }
        this.markAll();
        return ClanStoreModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanStoreModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBuyRecords()) {
            this.getBuyRecords().mergeChangeFromSs(proto.getBuyRecords());
            fieldCnt++;
        }
        if (proto.hasStockRecords()) {
            this.getStockRecords().mergeChangeFromSs(proto.getStockRecords());
            fieldCnt++;
        }
        if (proto.hasStoreInfo()) {
            this.getStoreInfo().mergeChangeFromSs(proto.getStoreInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanStoreModel.Builder builder = ClanStoreModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_BUYRECORDS) && this.buyRecords != null) {
            this.buyRecords.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_STOCKRECORDS) && this.stockRecords != null) {
            this.stockRecords.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_STOREINFO) && this.storeInfo != null) {
            this.storeInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.buyRecords != null) {
            this.buyRecords.markAll();
        }
        if (this.stockRecords != null) {
            this.stockRecords.markAll();
        }
        if (this.storeInfo != null) {
            this.storeInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanStoreModelProp)) {
            return false;
        }
        final ClanStoreModelProp otherNode = (ClanStoreModelProp) node;
        if (!this.getBuyRecords().compareDataTo(otherNode.getBuyRecords())) {
            return false;
        }
        if (!this.getStockRecords().compareDataTo(otherNode.getStockRecords())) {
            return false;
        }
        if (!this.getStoreInfo().compareDataTo(otherNode.getStoreInfo())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
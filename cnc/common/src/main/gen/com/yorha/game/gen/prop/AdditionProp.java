package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.Addition;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.AdditionPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class AdditionProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ADDITIONID = 0;
    public static final int FIELD_INDEX_TOTALVALUE = 1;
    public static final int FIELD_INDEX_ADDITIONITEMS = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int additionId = Constant.DEFAULT_INT_VALUE;
    private long totalValue = Constant.DEFAULT_LONG_VALUE;
    private Int32AdditionItemMapProp additionItems = null;

    public AdditionProp() {
        super(null, 0, FIELD_COUNT);
    }

    public AdditionProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get additionId
     *
     * @return additionId value
     */
    public int getAdditionId() {
        return this.additionId;
    }

    /**
     * set additionId && set marked
     *
     * @param additionId new value
     * @return current object
     */
    public AdditionProp setAdditionId(int additionId) {
        if (this.additionId != additionId) {
            this.mark(FIELD_INDEX_ADDITIONID);
            this.additionId = additionId;
        }
        return this;
    }

    /**
     * inner set additionId
     *
     * @param additionId new value
     */
    private void innerSetAdditionId(int additionId) {
        this.additionId = additionId;
    }

    /**
     * get totalValue
     *
     * @return totalValue value
     */
    public long getTotalValue() {
        return this.totalValue;
    }

    /**
     * set totalValue && set marked
     *
     * @param totalValue new value
     * @return current object
     */
    public AdditionProp setTotalValue(long totalValue) {
        if (this.totalValue != totalValue) {
            this.mark(FIELD_INDEX_TOTALVALUE);
            this.totalValue = totalValue;
        }
        return this;
    }

    /**
     * inner set totalValue
     *
     * @param totalValue new value
     */
    private void innerSetTotalValue(long totalValue) {
        this.totalValue = totalValue;
    }

    /**
     * get additionItems
     *
     * @return additionItems value
     */
    public Int32AdditionItemMapProp getAdditionItems() {
        if (this.additionItems == null) {
            this.additionItems = new Int32AdditionItemMapProp(this, FIELD_INDEX_ADDITIONITEMS);
        }
        return this.additionItems;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putAdditionItemsV(AdditionItemProp v) {
        this.getAdditionItems().put(v.getSourceId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public AdditionItemProp addEmptyAdditionItems(Integer k) {
        return this.getAdditionItems().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getAdditionItemsSize() {
        if (this.additionItems == null) {
            return 0;
        }
        return this.additionItems.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isAdditionItemsEmpty() {
        if (this.additionItems == null) {
            return true;
        }
        return this.additionItems.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public AdditionItemProp getAdditionItemsV(Integer k) {
        if (this.additionItems == null || !this.additionItems.containsKey(k)) {
            return null;
        }
        return this.additionItems.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearAdditionItems() {
        if (this.additionItems != null) {
            this.additionItems.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeAdditionItemsV(Integer k) {
        if (this.additionItems != null) {
            this.additionItems.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public AdditionPB.Builder getCopyCsBuilder() {
        final AdditionPB.Builder builder = AdditionPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(AdditionPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getAdditionId() != 0) {
            builder.setAdditionId(this.getAdditionId());
            fieldCnt++;
        }  else if (builder.hasAdditionId()) {
            // 清理AdditionId
            builder.clearAdditionId();
            fieldCnt++;
        }
        if (this.getTotalValue() != 0L) {
            builder.setTotalValue(this.getTotalValue());
            fieldCnt++;
        }  else if (builder.hasTotalValue()) {
            // 清理TotalValue
            builder.clearTotalValue();
            fieldCnt++;
        }
        if (this.additionItems != null) {
            StructPB.Int32AdditionItemMapPB.Builder tmpBuilder = StructPB.Int32AdditionItemMapPB.newBuilder();
            final int tmpFieldCnt = this.additionItems.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionItems();
            }
        }  else if (builder.hasAdditionItems()) {
            // 清理AdditionItems
            builder.clearAdditionItems();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(AdditionPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ADDITIONID)) {
            builder.setAdditionId(this.getAdditionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALVALUE)) {
            builder.setTotalValue(this.getTotalValue());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONITEMS) && this.additionItems != null) {
            final boolean needClear = !builder.hasAdditionItems();
            final int tmpFieldCnt = this.additionItems.copyChangeToCs(builder.getAdditionItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionItems();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(AdditionPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ADDITIONID)) {
            builder.setAdditionId(this.getAdditionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALVALUE)) {
            builder.setTotalValue(this.getTotalValue());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONITEMS) && this.additionItems != null) {
            final boolean needClear = !builder.hasAdditionItems();
            final int tmpFieldCnt = this.additionItems.copyChangeToAndClearDeleteKeysCs(builder.getAdditionItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionItems();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(AdditionPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAdditionId()) {
            this.innerSetAdditionId(proto.getAdditionId());
        } else {
            this.innerSetAdditionId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTotalValue()) {
            this.innerSetTotalValue(proto.getTotalValue());
        } else {
            this.innerSetTotalValue(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasAdditionItems()) {
            this.getAdditionItems().mergeFromCs(proto.getAdditionItems());
        } else {
            if (this.additionItems != null) {
                this.additionItems.mergeFromCs(proto.getAdditionItems());
            }
        }
        this.markAll();
        return AdditionProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(AdditionPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAdditionId()) {
            this.setAdditionId(proto.getAdditionId());
            fieldCnt++;
        }
        if (proto.hasTotalValue()) {
            this.setTotalValue(proto.getTotalValue());
            fieldCnt++;
        }
        if (proto.hasAdditionItems()) {
            this.getAdditionItems().mergeChangeFromCs(proto.getAdditionItems());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Addition.Builder getCopyDbBuilder() {
        final Addition.Builder builder = Addition.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(Addition.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getAdditionId() != 0) {
            builder.setAdditionId(this.getAdditionId());
            fieldCnt++;
        }  else if (builder.hasAdditionId()) {
            // 清理AdditionId
            builder.clearAdditionId();
            fieldCnt++;
        }
        if (this.getTotalValue() != 0L) {
            builder.setTotalValue(this.getTotalValue());
            fieldCnt++;
        }  else if (builder.hasTotalValue()) {
            // 清理TotalValue
            builder.clearTotalValue();
            fieldCnt++;
        }
        if (this.additionItems != null) {
            Struct.Int32AdditionItemMap.Builder tmpBuilder = Struct.Int32AdditionItemMap.newBuilder();
            final int tmpFieldCnt = this.additionItems.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionItems();
            }
        }  else if (builder.hasAdditionItems()) {
            // 清理AdditionItems
            builder.clearAdditionItems();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(Addition.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ADDITIONID)) {
            builder.setAdditionId(this.getAdditionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALVALUE)) {
            builder.setTotalValue(this.getTotalValue());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONITEMS) && this.additionItems != null) {
            final boolean needClear = !builder.hasAdditionItems();
            final int tmpFieldCnt = this.additionItems.copyChangeToDb(builder.getAdditionItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionItems();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(Addition proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAdditionId()) {
            this.innerSetAdditionId(proto.getAdditionId());
        } else {
            this.innerSetAdditionId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTotalValue()) {
            this.innerSetTotalValue(proto.getTotalValue());
        } else {
            this.innerSetTotalValue(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasAdditionItems()) {
            this.getAdditionItems().mergeFromDb(proto.getAdditionItems());
        } else {
            if (this.additionItems != null) {
                this.additionItems.mergeFromDb(proto.getAdditionItems());
            }
        }
        this.markAll();
        return AdditionProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(Addition proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAdditionId()) {
            this.setAdditionId(proto.getAdditionId());
            fieldCnt++;
        }
        if (proto.hasTotalValue()) {
            this.setTotalValue(proto.getTotalValue());
            fieldCnt++;
        }
        if (proto.hasAdditionItems()) {
            this.getAdditionItems().mergeChangeFromDb(proto.getAdditionItems());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Addition.Builder getCopySsBuilder() {
        final Addition.Builder builder = Addition.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(Addition.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getAdditionId() != 0) {
            builder.setAdditionId(this.getAdditionId());
            fieldCnt++;
        }  else if (builder.hasAdditionId()) {
            // 清理AdditionId
            builder.clearAdditionId();
            fieldCnt++;
        }
        if (this.getTotalValue() != 0L) {
            builder.setTotalValue(this.getTotalValue());
            fieldCnt++;
        }  else if (builder.hasTotalValue()) {
            // 清理TotalValue
            builder.clearTotalValue();
            fieldCnt++;
        }
        if (this.additionItems != null) {
            Struct.Int32AdditionItemMap.Builder tmpBuilder = Struct.Int32AdditionItemMap.newBuilder();
            final int tmpFieldCnt = this.additionItems.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionItems();
            }
        }  else if (builder.hasAdditionItems()) {
            // 清理AdditionItems
            builder.clearAdditionItems();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(Addition.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ADDITIONID)) {
            builder.setAdditionId(this.getAdditionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALVALUE)) {
            builder.setTotalValue(this.getTotalValue());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONITEMS) && this.additionItems != null) {
            final boolean needClear = !builder.hasAdditionItems();
            final int tmpFieldCnt = this.additionItems.copyChangeToSs(builder.getAdditionItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionItems();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(Addition proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAdditionId()) {
            this.innerSetAdditionId(proto.getAdditionId());
        } else {
            this.innerSetAdditionId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTotalValue()) {
            this.innerSetTotalValue(proto.getTotalValue());
        } else {
            this.innerSetTotalValue(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasAdditionItems()) {
            this.getAdditionItems().mergeFromSs(proto.getAdditionItems());
        } else {
            if (this.additionItems != null) {
                this.additionItems.mergeFromSs(proto.getAdditionItems());
            }
        }
        this.markAll();
        return AdditionProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(Addition proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAdditionId()) {
            this.setAdditionId(proto.getAdditionId());
            fieldCnt++;
        }
        if (proto.hasTotalValue()) {
            this.setTotalValue(proto.getTotalValue());
            fieldCnt++;
        }
        if (proto.hasAdditionItems()) {
            this.getAdditionItems().mergeChangeFromSs(proto.getAdditionItems());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        Addition.Builder builder = Addition.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONITEMS) && this.additionItems != null) {
            this.additionItems.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.additionItems != null) {
            this.additionItems.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.additionId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof AdditionProp)) {
            return false;
        }
        final AdditionProp otherNode = (AdditionProp) node;
        if (this.additionId != otherNode.additionId) {
            return false;
        }
        if (this.totalValue != otherNode.totalValue) {
            return false;
        }
        if (!this.getAdditionItems().compareDataTo(otherNode.getAdditionItems())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
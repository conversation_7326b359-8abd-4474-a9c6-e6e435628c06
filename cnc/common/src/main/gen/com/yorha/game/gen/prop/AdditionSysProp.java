package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.AdditionSys;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.AdditionSysPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class AdditionSysProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ADDITION = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32AdditionMapProp addition = null;

    public AdditionSysProp() {
        super(null, 0, FIELD_COUNT);
    }

    public AdditionSysProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get addition
     *
     * @return addition value
     */
    public Int32AdditionMapProp getAddition() {
        if (this.addition == null) {
            this.addition = new Int32AdditionMapProp(this, FIELD_INDEX_ADDITION);
        }
        return this.addition;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putAdditionV(AdditionProp v) {
        this.getAddition().put(v.getAdditionId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public AdditionProp addEmptyAddition(Integer k) {
        return this.getAddition().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getAdditionSize() {
        if (this.addition == null) {
            return 0;
        }
        return this.addition.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isAdditionEmpty() {
        if (this.addition == null) {
            return true;
        }
        return this.addition.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public AdditionProp getAdditionV(Integer k) {
        if (this.addition == null || !this.addition.containsKey(k)) {
            return null;
        }
        return this.addition.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearAddition() {
        if (this.addition != null) {
            this.addition.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeAdditionV(Integer k) {
        if (this.addition != null) {
            this.addition.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public AdditionSysPB.Builder getCopyCsBuilder() {
        final AdditionSysPB.Builder builder = AdditionSysPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(AdditionSysPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.addition != null) {
            StructPB.Int32AdditionMapPB.Builder tmpBuilder = StructPB.Int32AdditionMapPB.newBuilder();
            final int tmpFieldCnt = this.addition.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAddition(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAddition();
            }
        }  else if (builder.hasAddition()) {
            // 清理Addition
            builder.clearAddition();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(AdditionSysPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ADDITION) && this.addition != null) {
            final boolean needClear = !builder.hasAddition();
            final int tmpFieldCnt = this.addition.copyChangeToCs(builder.getAdditionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAddition();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(AdditionSysPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ADDITION) && this.addition != null) {
            final boolean needClear = !builder.hasAddition();
            final int tmpFieldCnt = this.addition.copyChangeToAndClearDeleteKeysCs(builder.getAdditionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAddition();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(AdditionSysPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAddition()) {
            this.getAddition().mergeFromCs(proto.getAddition());
        } else {
            if (this.addition != null) {
                this.addition.mergeFromCs(proto.getAddition());
            }
        }
        this.markAll();
        return AdditionSysProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(AdditionSysPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAddition()) {
            this.getAddition().mergeChangeFromCs(proto.getAddition());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public AdditionSys.Builder getCopyDbBuilder() {
        final AdditionSys.Builder builder = AdditionSys.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(AdditionSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.addition != null) {
            Struct.Int32AdditionMap.Builder tmpBuilder = Struct.Int32AdditionMap.newBuilder();
            final int tmpFieldCnt = this.addition.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAddition(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAddition();
            }
        }  else if (builder.hasAddition()) {
            // 清理Addition
            builder.clearAddition();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(AdditionSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ADDITION) && this.addition != null) {
            final boolean needClear = !builder.hasAddition();
            final int tmpFieldCnt = this.addition.copyChangeToDb(builder.getAdditionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAddition();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(AdditionSys proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAddition()) {
            this.getAddition().mergeFromDb(proto.getAddition());
        } else {
            if (this.addition != null) {
                this.addition.mergeFromDb(proto.getAddition());
            }
        }
        this.markAll();
        return AdditionSysProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(AdditionSys proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAddition()) {
            this.getAddition().mergeChangeFromDb(proto.getAddition());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public AdditionSys.Builder getCopySsBuilder() {
        final AdditionSys.Builder builder = AdditionSys.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(AdditionSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.addition != null) {
            Struct.Int32AdditionMap.Builder tmpBuilder = Struct.Int32AdditionMap.newBuilder();
            final int tmpFieldCnt = this.addition.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAddition(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAddition();
            }
        }  else if (builder.hasAddition()) {
            // 清理Addition
            builder.clearAddition();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(AdditionSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ADDITION) && this.addition != null) {
            final boolean needClear = !builder.hasAddition();
            final int tmpFieldCnt = this.addition.copyChangeToSs(builder.getAdditionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAddition();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(AdditionSys proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAddition()) {
            this.getAddition().mergeFromSs(proto.getAddition());
        } else {
            if (this.addition != null) {
                this.addition.mergeFromSs(proto.getAddition());
            }
        }
        this.markAll();
        return AdditionSysProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(AdditionSys proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAddition()) {
            this.getAddition().mergeChangeFromSs(proto.getAddition());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        AdditionSys.Builder builder = AdditionSys.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ADDITION) && this.addition != null) {
            this.addition.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.addition != null) {
            this.addition.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof AdditionSysProp)) {
            return false;
        }
        final AdditionSysProp otherNode = (AdditionSysProp) node;
        if (!this.getAddition().compareDataTo(otherNode.getAddition())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
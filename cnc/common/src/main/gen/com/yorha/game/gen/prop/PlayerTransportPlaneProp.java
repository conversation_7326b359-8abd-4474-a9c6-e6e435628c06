package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Player.PlayerTransportPlane;
import com.yorha.proto.PlayerPB.PlayerTransportPlanePB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerTransportPlaneProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_PLANEMODEL = 1;
    public static final int FIELD_INDEX_STATUS = 2;
    public static final int FIELD_INDEX_CDRESETMS = 3;
    public static final int FIELD_INDEX_RETURNOVERMS = 4;
    public static final int FIELD_INDEX_ISNEEDREMINDER = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private long id = Constant.DEFAULT_LONG_VALUE;
    private int planeModel = Constant.DEFAULT_INT_VALUE;
    private PlaneStatus status = PlaneStatus.forNumber(0);
    private long cdResetMs = Constant.DEFAULT_LONG_VALUE;
    private long returnOverMs = Constant.DEFAULT_LONG_VALUE;
    private boolean isNeedReminder = Constant.DEFAULT_BOOLEAN_VALUE;

    public PlayerTransportPlaneProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerTransportPlaneProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public long getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public PlayerTransportPlaneProp setId(long id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(long id) {
        this.id = id;
    }

    /**
     * get planeModel
     *
     * @return planeModel value
     */
    public int getPlaneModel() {
        return this.planeModel;
    }

    /**
     * set planeModel && set marked
     *
     * @param planeModel new value
     * @return current object
     */
    public PlayerTransportPlaneProp setPlaneModel(int planeModel) {
        if (this.planeModel != planeModel) {
            this.mark(FIELD_INDEX_PLANEMODEL);
            this.planeModel = planeModel;
        }
        return this;
    }

    /**
     * inner set planeModel
     *
     * @param planeModel new value
     */
    private void innerSetPlaneModel(int planeModel) {
        this.planeModel = planeModel;
    }

    /**
     * get status
     *
     * @return status value
     */
    public PlaneStatus getStatus() {
        return this.status;
    }

    /**
     * set status && set marked
     *
     * @param status new value
     * @return current object
     */
    public PlayerTransportPlaneProp setStatus(PlaneStatus status) {
        if (status == null) {
            throw new NullPointerException();
        }
        if (this.status != status) {
            this.mark(FIELD_INDEX_STATUS);
            this.status = status;
        }
        return this;
    }

    /**
     * inner set status
     *
     * @param status new value
     */
    private void innerSetStatus(PlaneStatus status) {
        this.status = status;
    }

    /**
     * get cdResetMs
     *
     * @return cdResetMs value
     */
    public long getCdResetMs() {
        return this.cdResetMs;
    }

    /**
     * set cdResetMs && set marked
     *
     * @param cdResetMs new value
     * @return current object
     */
    public PlayerTransportPlaneProp setCdResetMs(long cdResetMs) {
        if (this.cdResetMs != cdResetMs) {
            this.mark(FIELD_INDEX_CDRESETMS);
            this.cdResetMs = cdResetMs;
        }
        return this;
    }

    /**
     * inner set cdResetMs
     *
     * @param cdResetMs new value
     */
    private void innerSetCdResetMs(long cdResetMs) {
        this.cdResetMs = cdResetMs;
    }

    /**
     * get returnOverMs
     *
     * @return returnOverMs value
     */
    public long getReturnOverMs() {
        return this.returnOverMs;
    }

    /**
     * set returnOverMs && set marked
     *
     * @param returnOverMs new value
     * @return current object
     */
    public PlayerTransportPlaneProp setReturnOverMs(long returnOverMs) {
        if (this.returnOverMs != returnOverMs) {
            this.mark(FIELD_INDEX_RETURNOVERMS);
            this.returnOverMs = returnOverMs;
        }
        return this;
    }

    /**
     * inner set returnOverMs
     *
     * @param returnOverMs new value
     */
    private void innerSetReturnOverMs(long returnOverMs) {
        this.returnOverMs = returnOverMs;
    }

    /**
     * get isNeedReminder
     *
     * @return isNeedReminder value
     */
    public boolean getIsNeedReminder() {
        return this.isNeedReminder;
    }

    /**
     * set isNeedReminder && set marked
     *
     * @param isNeedReminder new value
     * @return current object
     */
    public PlayerTransportPlaneProp setIsNeedReminder(boolean isNeedReminder) {
        if (this.isNeedReminder != isNeedReminder) {
            this.mark(FIELD_INDEX_ISNEEDREMINDER);
            this.isNeedReminder = isNeedReminder;
        }
        return this;
    }

    /**
     * inner set isNeedReminder
     *
     * @param isNeedReminder new value
     */
    private void innerSetIsNeedReminder(boolean isNeedReminder) {
        this.isNeedReminder = isNeedReminder;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerTransportPlanePB.Builder getCopyCsBuilder() {
        final PlayerTransportPlanePB.Builder builder = PlayerTransportPlanePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerTransportPlanePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getPlaneModel() != 0) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }  else if (builder.hasPlaneModel()) {
            // 清理PlaneModel
            builder.clearPlaneModel();
            fieldCnt++;
        }
        if (this.getStatus() != PlaneStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.getCdResetMs() != 0L) {
            builder.setCdResetMs(this.getCdResetMs());
            fieldCnt++;
        }  else if (builder.hasCdResetMs()) {
            // 清理CdResetMs
            builder.clearCdResetMs();
            fieldCnt++;
        }
        if (this.getReturnOverMs() != 0L) {
            builder.setReturnOverMs(this.getReturnOverMs());
            fieldCnt++;
        }  else if (builder.hasReturnOverMs()) {
            // 清理ReturnOverMs
            builder.clearReturnOverMs();
            fieldCnt++;
        }
        if (this.getIsNeedReminder()) {
            builder.setIsNeedReminder(this.getIsNeedReminder());
            fieldCnt++;
        }  else if (builder.hasIsNeedReminder()) {
            // 清理IsNeedReminder
            builder.clearIsNeedReminder();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerTransportPlanePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANEMODEL)) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CDRESETMS)) {
            builder.setCdResetMs(this.getCdResetMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RETURNOVERMS)) {
            builder.setReturnOverMs(this.getReturnOverMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISNEEDREMINDER)) {
            builder.setIsNeedReminder(this.getIsNeedReminder());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerTransportPlanePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANEMODEL)) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CDRESETMS)) {
            builder.setCdResetMs(this.getCdResetMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RETURNOVERMS)) {
            builder.setReturnOverMs(this.getReturnOverMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISNEEDREMINDER)) {
            builder.setIsNeedReminder(this.getIsNeedReminder());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerTransportPlanePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlaneModel()) {
            this.innerSetPlaneModel(proto.getPlaneModel());
        } else {
            this.innerSetPlaneModel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(PlaneStatus.forNumber(0));
        }
        if (proto.hasCdResetMs()) {
            this.innerSetCdResetMs(proto.getCdResetMs());
        } else {
            this.innerSetCdResetMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasReturnOverMs()) {
            this.innerSetReturnOverMs(proto.getReturnOverMs());
        } else {
            this.innerSetReturnOverMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIsNeedReminder()) {
            this.innerSetIsNeedReminder(proto.getIsNeedReminder());
        } else {
            this.innerSetIsNeedReminder(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return PlayerTransportPlaneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerTransportPlanePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasPlaneModel()) {
            this.setPlaneModel(proto.getPlaneModel());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasCdResetMs()) {
            this.setCdResetMs(proto.getCdResetMs());
            fieldCnt++;
        }
        if (proto.hasReturnOverMs()) {
            this.setReturnOverMs(proto.getReturnOverMs());
            fieldCnt++;
        }
        if (proto.hasIsNeedReminder()) {
            this.setIsNeedReminder(proto.getIsNeedReminder());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerTransportPlane.Builder getCopyDbBuilder() {
        final PlayerTransportPlane.Builder builder = PlayerTransportPlane.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerTransportPlane.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getPlaneModel() != 0) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }  else if (builder.hasPlaneModel()) {
            // 清理PlaneModel
            builder.clearPlaneModel();
            fieldCnt++;
        }
        if (this.getStatus() != PlaneStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.getCdResetMs() != 0L) {
            builder.setCdResetMs(this.getCdResetMs());
            fieldCnt++;
        }  else if (builder.hasCdResetMs()) {
            // 清理CdResetMs
            builder.clearCdResetMs();
            fieldCnt++;
        }
        if (this.getReturnOverMs() != 0L) {
            builder.setReturnOverMs(this.getReturnOverMs());
            fieldCnt++;
        }  else if (builder.hasReturnOverMs()) {
            // 清理ReturnOverMs
            builder.clearReturnOverMs();
            fieldCnt++;
        }
        if (this.getIsNeedReminder()) {
            builder.setIsNeedReminder(this.getIsNeedReminder());
            fieldCnt++;
        }  else if (builder.hasIsNeedReminder()) {
            // 清理IsNeedReminder
            builder.clearIsNeedReminder();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerTransportPlane.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANEMODEL)) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CDRESETMS)) {
            builder.setCdResetMs(this.getCdResetMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RETURNOVERMS)) {
            builder.setReturnOverMs(this.getReturnOverMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISNEEDREMINDER)) {
            builder.setIsNeedReminder(this.getIsNeedReminder());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerTransportPlane proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlaneModel()) {
            this.innerSetPlaneModel(proto.getPlaneModel());
        } else {
            this.innerSetPlaneModel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(PlaneStatus.forNumber(0));
        }
        if (proto.hasCdResetMs()) {
            this.innerSetCdResetMs(proto.getCdResetMs());
        } else {
            this.innerSetCdResetMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasReturnOverMs()) {
            this.innerSetReturnOverMs(proto.getReturnOverMs());
        } else {
            this.innerSetReturnOverMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIsNeedReminder()) {
            this.innerSetIsNeedReminder(proto.getIsNeedReminder());
        } else {
            this.innerSetIsNeedReminder(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return PlayerTransportPlaneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerTransportPlane proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasPlaneModel()) {
            this.setPlaneModel(proto.getPlaneModel());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasCdResetMs()) {
            this.setCdResetMs(proto.getCdResetMs());
            fieldCnt++;
        }
        if (proto.hasReturnOverMs()) {
            this.setReturnOverMs(proto.getReturnOverMs());
            fieldCnt++;
        }
        if (proto.hasIsNeedReminder()) {
            this.setIsNeedReminder(proto.getIsNeedReminder());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerTransportPlane.Builder getCopySsBuilder() {
        final PlayerTransportPlane.Builder builder = PlayerTransportPlane.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerTransportPlane.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getPlaneModel() != 0) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }  else if (builder.hasPlaneModel()) {
            // 清理PlaneModel
            builder.clearPlaneModel();
            fieldCnt++;
        }
        if (this.getStatus() != PlaneStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.getCdResetMs() != 0L) {
            builder.setCdResetMs(this.getCdResetMs());
            fieldCnt++;
        }  else if (builder.hasCdResetMs()) {
            // 清理CdResetMs
            builder.clearCdResetMs();
            fieldCnt++;
        }
        if (this.getReturnOverMs() != 0L) {
            builder.setReturnOverMs(this.getReturnOverMs());
            fieldCnt++;
        }  else if (builder.hasReturnOverMs()) {
            // 清理ReturnOverMs
            builder.clearReturnOverMs();
            fieldCnt++;
        }
        if (this.getIsNeedReminder()) {
            builder.setIsNeedReminder(this.getIsNeedReminder());
            fieldCnt++;
        }  else if (builder.hasIsNeedReminder()) {
            // 清理IsNeedReminder
            builder.clearIsNeedReminder();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerTransportPlane.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANEMODEL)) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CDRESETMS)) {
            builder.setCdResetMs(this.getCdResetMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RETURNOVERMS)) {
            builder.setReturnOverMs(this.getReturnOverMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISNEEDREMINDER)) {
            builder.setIsNeedReminder(this.getIsNeedReminder());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerTransportPlane proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlaneModel()) {
            this.innerSetPlaneModel(proto.getPlaneModel());
        } else {
            this.innerSetPlaneModel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(PlaneStatus.forNumber(0));
        }
        if (proto.hasCdResetMs()) {
            this.innerSetCdResetMs(proto.getCdResetMs());
        } else {
            this.innerSetCdResetMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasReturnOverMs()) {
            this.innerSetReturnOverMs(proto.getReturnOverMs());
        } else {
            this.innerSetReturnOverMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIsNeedReminder()) {
            this.innerSetIsNeedReminder(proto.getIsNeedReminder());
        } else {
            this.innerSetIsNeedReminder(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return PlayerTransportPlaneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerTransportPlane proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasPlaneModel()) {
            this.setPlaneModel(proto.getPlaneModel());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasCdResetMs()) {
            this.setCdResetMs(proto.getCdResetMs());
            fieldCnt++;
        }
        if (proto.hasReturnOverMs()) {
            this.setReturnOverMs(proto.getReturnOverMs());
            fieldCnt++;
        }
        if (proto.hasIsNeedReminder()) {
            this.setIsNeedReminder(proto.getIsNeedReminder());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerTransportPlane.Builder builder = PlayerTransportPlane.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerTransportPlaneProp)) {
            return false;
        }
        final PlayerTransportPlaneProp otherNode = (PlayerTransportPlaneProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.planeModel != otherNode.planeModel) {
            return false;
        }
        if (this.status != otherNode.status) {
            return false;
        }
        if (this.cdResetMs != otherNode.cdResetMs) {
            return false;
        }
        if (this.returnOverMs != otherNode.returnOverMs) {
            return false;
        }
        if (this.isNeedReminder != otherNode.isNeedReminder) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
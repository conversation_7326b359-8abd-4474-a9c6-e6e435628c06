package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.ResBuilding.CollectInfo;
import com.yorha.proto.ResBuildingPB.CollectInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class CollectInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_PLAYERID = 0;
    public static final int FIELD_INDEX_ARMYID = 1;
    public static final int FIELD_INDEX_PLAYERNAME = 2;
    public static final int FIELD_INDEX_CLANSIMPLENAME = 3;
    public static final int FIELD_INDEX_ENDTSMS = 4;
    public static final int FIELD_INDEX_ISCOLLECTOUT = 5;
    public static final int FIELD_INDEX_STARTTSMS = 6;
    public static final int FIELD_INDEX_CLANID = 7;
    public static final int FIELD_INDEX_ESTIMATENUM = 8;
    public static final int FIELD_INDEX_PLAYERCAMP = 9;
    public static final int FIELD_INDEX_ZONEID = 10;

    public static final int FIELD_COUNT = 11;

    private long markBits0 = 0L;

    private long playerId = Constant.DEFAULT_LONG_VALUE;
    private long armyId = Constant.DEFAULT_LONG_VALUE;
    private String playerName = Constant.DEFAULT_STR_VALUE;
    private String clanSimpleName = Constant.DEFAULT_STR_VALUE;
    private long endTsMs = Constant.DEFAULT_LONG_VALUE;
    private boolean isCollectOut = Constant.DEFAULT_BOOLEAN_VALUE;
    private long startTsMs = Constant.DEFAULT_LONG_VALUE;
    private long clanId = Constant.DEFAULT_LONG_VALUE;
    private long estimateNum = Constant.DEFAULT_LONG_VALUE;
    private Camp playerCamp = Camp.forNumber(0);
    private int zoneId = Constant.DEFAULT_INT_VALUE;

    public CollectInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public CollectInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get playerId
     *
     * @return playerId value
     */
    public long getPlayerId() {
        return this.playerId;
    }

    /**
     * set playerId && set marked
     *
     * @param playerId new value
     * @return current object
     */
    public CollectInfoProp setPlayerId(long playerId) {
        if (this.playerId != playerId) {
            this.mark(FIELD_INDEX_PLAYERID);
            this.playerId = playerId;
        }
        return this;
    }

    /**
     * inner set playerId
     *
     * @param playerId new value
     */
    private void innerSetPlayerId(long playerId) {
        this.playerId = playerId;
    }

    /**
     * get armyId
     *
     * @return armyId value
     */
    public long getArmyId() {
        return this.armyId;
    }

    /**
     * set armyId && set marked
     *
     * @param armyId new value
     * @return current object
     */
    public CollectInfoProp setArmyId(long armyId) {
        if (this.armyId != armyId) {
            this.mark(FIELD_INDEX_ARMYID);
            this.armyId = armyId;
        }
        return this;
    }

    /**
     * inner set armyId
     *
     * @param armyId new value
     */
    private void innerSetArmyId(long armyId) {
        this.armyId = armyId;
    }

    /**
     * get playerName
     *
     * @return playerName value
     */
    public String getPlayerName() {
        return this.playerName;
    }

    /**
     * set playerName && set marked
     *
     * @param playerName new value
     * @return current object
     */
    public CollectInfoProp setPlayerName(String playerName) {
        if (playerName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.playerName, playerName)) {
            this.mark(FIELD_INDEX_PLAYERNAME);
            this.playerName = playerName;
        }
        return this;
    }

    /**
     * inner set playerName
     *
     * @param playerName new value
     */
    private void innerSetPlayerName(String playerName) {
        this.playerName = playerName;
    }

    /**
     * get clanSimpleName
     *
     * @return clanSimpleName value
     */
    public String getClanSimpleName() {
        return this.clanSimpleName;
    }

    /**
     * set clanSimpleName && set marked
     *
     * @param clanSimpleName new value
     * @return current object
     */
    public CollectInfoProp setClanSimpleName(String clanSimpleName) {
        if (clanSimpleName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanSimpleName, clanSimpleName)) {
            this.mark(FIELD_INDEX_CLANSIMPLENAME);
            this.clanSimpleName = clanSimpleName;
        }
        return this;
    }

    /**
     * inner set clanSimpleName
     *
     * @param clanSimpleName new value
     */
    private void innerSetClanSimpleName(String clanSimpleName) {
        this.clanSimpleName = clanSimpleName;
    }

    /**
     * get endTsMs
     *
     * @return endTsMs value
     */
    public long getEndTsMs() {
        return this.endTsMs;
    }

    /**
     * set endTsMs && set marked
     *
     * @param endTsMs new value
     * @return current object
     */
    public CollectInfoProp setEndTsMs(long endTsMs) {
        if (this.endTsMs != endTsMs) {
            this.mark(FIELD_INDEX_ENDTSMS);
            this.endTsMs = endTsMs;
        }
        return this;
    }

    /**
     * inner set endTsMs
     *
     * @param endTsMs new value
     */
    private void innerSetEndTsMs(long endTsMs) {
        this.endTsMs = endTsMs;
    }

    /**
     * get isCollectOut
     *
     * @return isCollectOut value
     */
    public boolean getIsCollectOut() {
        return this.isCollectOut;
    }

    /**
     * set isCollectOut && set marked
     *
     * @param isCollectOut new value
     * @return current object
     */
    public CollectInfoProp setIsCollectOut(boolean isCollectOut) {
        if (this.isCollectOut != isCollectOut) {
            this.mark(FIELD_INDEX_ISCOLLECTOUT);
            this.isCollectOut = isCollectOut;
        }
        return this;
    }

    /**
     * inner set isCollectOut
     *
     * @param isCollectOut new value
     */
    private void innerSetIsCollectOut(boolean isCollectOut) {
        this.isCollectOut = isCollectOut;
    }

    /**
     * get startTsMs
     *
     * @return startTsMs value
     */
    public long getStartTsMs() {
        return this.startTsMs;
    }

    /**
     * set startTsMs && set marked
     *
     * @param startTsMs new value
     * @return current object
     */
    public CollectInfoProp setStartTsMs(long startTsMs) {
        if (this.startTsMs != startTsMs) {
            this.mark(FIELD_INDEX_STARTTSMS);
            this.startTsMs = startTsMs;
        }
        return this;
    }

    /**
     * inner set startTsMs
     *
     * @param startTsMs new value
     */
    private void innerSetStartTsMs(long startTsMs) {
        this.startTsMs = startTsMs;
    }

    /**
     * get clanId
     *
     * @return clanId value
     */
    public long getClanId() {
        return this.clanId;
    }

    /**
     * set clanId && set marked
     *
     * @param clanId new value
     * @return current object
     */
    public CollectInfoProp setClanId(long clanId) {
        if (this.clanId != clanId) {
            this.mark(FIELD_INDEX_CLANID);
            this.clanId = clanId;
        }
        return this;
    }

    /**
     * inner set clanId
     *
     * @param clanId new value
     */
    private void innerSetClanId(long clanId) {
        this.clanId = clanId;
    }

    /**
     * get estimateNum
     *
     * @return estimateNum value
     */
    public long getEstimateNum() {
        return this.estimateNum;
    }

    /**
     * set estimateNum && set marked
     *
     * @param estimateNum new value
     * @return current object
     */
    public CollectInfoProp setEstimateNum(long estimateNum) {
        if (this.estimateNum != estimateNum) {
            this.mark(FIELD_INDEX_ESTIMATENUM);
            this.estimateNum = estimateNum;
        }
        return this;
    }

    /**
     * inner set estimateNum
     *
     * @param estimateNum new value
     */
    private void innerSetEstimateNum(long estimateNum) {
        this.estimateNum = estimateNum;
    }

    /**
     * get playerCamp
     *
     * @return playerCamp value
     */
    public Camp getPlayerCamp() {
        return this.playerCamp;
    }

    /**
     * set playerCamp && set marked
     *
     * @param playerCamp new value
     * @return current object
     */
    public CollectInfoProp setPlayerCamp(Camp playerCamp) {
        if (playerCamp == null) {
            throw new NullPointerException();
        }
        if (this.playerCamp != playerCamp) {
            this.mark(FIELD_INDEX_PLAYERCAMP);
            this.playerCamp = playerCamp;
        }
        return this;
    }

    /**
     * inner set playerCamp
     *
     * @param playerCamp new value
     */
    private void innerSetPlayerCamp(Camp playerCamp) {
        this.playerCamp = playerCamp;
    }

    /**
     * get zoneId
     *
     * @return zoneId value
     */
    public int getZoneId() {
        return this.zoneId;
    }

    /**
     * set zoneId && set marked
     *
     * @param zoneId new value
     * @return current object
     */
    public CollectInfoProp setZoneId(int zoneId) {
        if (this.zoneId != zoneId) {
            this.mark(FIELD_INDEX_ZONEID);
            this.zoneId = zoneId;
        }
        return this;
    }

    /**
     * inner set zoneId
     *
     * @param zoneId new value
     */
    private void innerSetZoneId(int zoneId) {
        this.zoneId = zoneId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CollectInfoPB.Builder getCopyCsBuilder() {
        final CollectInfoPB.Builder builder = CollectInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(CollectInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getArmyId() != 0L) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }  else if (builder.hasArmyId()) {
            // 清理ArmyId
            builder.clearArmyId();
            fieldCnt++;
        }
        if (!this.getPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setPlayerName(this.getPlayerName());
            fieldCnt++;
        }  else if (builder.hasPlayerName()) {
            // 清理PlayerName
            builder.clearPlayerName();
            fieldCnt++;
        }
        if (!this.getClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasClanSimpleName()) {
            // 清理ClanSimpleName
            builder.clearClanSimpleName();
            fieldCnt++;
        }
        if (this.getEndTsMs() != 0L) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }  else if (builder.hasEndTsMs()) {
            // 清理EndTsMs
            builder.clearEndTsMs();
            fieldCnt++;
        }
        if (this.getIsCollectOut()) {
            builder.setIsCollectOut(this.getIsCollectOut());
            fieldCnt++;
        }  else if (builder.hasIsCollectOut()) {
            // 清理IsCollectOut
            builder.clearIsCollectOut();
            fieldCnt++;
        }
        if (this.getStartTsMs() != 0L) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStartTsMs()) {
            // 清理StartTsMs
            builder.clearStartTsMs();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (this.getEstimateNum() != 0L) {
            builder.setEstimateNum(this.getEstimateNum());
            fieldCnt++;
        }  else if (builder.hasEstimateNum()) {
            // 清理EstimateNum
            builder.clearEstimateNum();
            fieldCnt++;
        }
        if (this.getPlayerCamp() != Camp.forNumber(0)) {
            builder.setPlayerCamp(this.getPlayerCamp());
            fieldCnt++;
        }  else if (builder.hasPlayerCamp()) {
            // 清理PlayerCamp
            builder.clearPlayerCamp();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(CollectInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERNAME)) {
            builder.setPlayerName(this.getPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISCOLLECTOUT)) {
            builder.setIsCollectOut(this.getIsCollectOut());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ESTIMATENUM)) {
            builder.setEstimateNum(this.getEstimateNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCAMP)) {
            builder.setPlayerCamp(this.getPlayerCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(CollectInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERNAME)) {
            builder.setPlayerName(this.getPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISCOLLECTOUT)) {
            builder.setIsCollectOut(this.getIsCollectOut());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ESTIMATENUM)) {
            builder.setEstimateNum(this.getEstimateNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCAMP)) {
            builder.setPlayerCamp(this.getPlayerCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(CollectInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasArmyId()) {
            this.innerSetArmyId(proto.getArmyId());
        } else {
            this.innerSetArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerName()) {
            this.innerSetPlayerName(proto.getPlayerName());
        } else {
            this.innerSetPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanSimpleName()) {
            this.innerSetClanSimpleName(proto.getClanSimpleName());
        } else {
            this.innerSetClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasEndTsMs()) {
            this.innerSetEndTsMs(proto.getEndTsMs());
        } else {
            this.innerSetEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIsCollectOut()) {
            this.innerSetIsCollectOut(proto.getIsCollectOut());
        } else {
            this.innerSetIsCollectOut(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasStartTsMs()) {
            this.innerSetStartTsMs(proto.getStartTsMs());
        } else {
            this.innerSetStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEstimateNum()) {
            this.innerSetEstimateNum(proto.getEstimateNum());
        } else {
            this.innerSetEstimateNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerCamp()) {
            this.innerSetPlayerCamp(proto.getPlayerCamp());
        } else {
            this.innerSetPlayerCamp(Camp.forNumber(0));
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return CollectInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(CollectInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasArmyId()) {
            this.setArmyId(proto.getArmyId());
            fieldCnt++;
        }
        if (proto.hasPlayerName()) {
            this.setPlayerName(proto.getPlayerName());
            fieldCnt++;
        }
        if (proto.hasClanSimpleName()) {
            this.setClanSimpleName(proto.getClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasEndTsMs()) {
            this.setEndTsMs(proto.getEndTsMs());
            fieldCnt++;
        }
        if (proto.hasIsCollectOut()) {
            this.setIsCollectOut(proto.getIsCollectOut());
            fieldCnt++;
        }
        if (proto.hasStartTsMs()) {
            this.setStartTsMs(proto.getStartTsMs());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasEstimateNum()) {
            this.setEstimateNum(proto.getEstimateNum());
            fieldCnt++;
        }
        if (proto.hasPlayerCamp()) {
            this.setPlayerCamp(proto.getPlayerCamp());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CollectInfo.Builder getCopyDbBuilder() {
        final CollectInfo.Builder builder = CollectInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(CollectInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getArmyId() != 0L) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }  else if (builder.hasArmyId()) {
            // 清理ArmyId
            builder.clearArmyId();
            fieldCnt++;
        }
        if (!this.getPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setPlayerName(this.getPlayerName());
            fieldCnt++;
        }  else if (builder.hasPlayerName()) {
            // 清理PlayerName
            builder.clearPlayerName();
            fieldCnt++;
        }
        if (!this.getClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasClanSimpleName()) {
            // 清理ClanSimpleName
            builder.clearClanSimpleName();
            fieldCnt++;
        }
        if (this.getEndTsMs() != 0L) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }  else if (builder.hasEndTsMs()) {
            // 清理EndTsMs
            builder.clearEndTsMs();
            fieldCnt++;
        }
        if (this.getIsCollectOut()) {
            builder.setIsCollectOut(this.getIsCollectOut());
            fieldCnt++;
        }  else if (builder.hasIsCollectOut()) {
            // 清理IsCollectOut
            builder.clearIsCollectOut();
            fieldCnt++;
        }
        if (this.getStartTsMs() != 0L) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStartTsMs()) {
            // 清理StartTsMs
            builder.clearStartTsMs();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (this.getEstimateNum() != 0L) {
            builder.setEstimateNum(this.getEstimateNum());
            fieldCnt++;
        }  else if (builder.hasEstimateNum()) {
            // 清理EstimateNum
            builder.clearEstimateNum();
            fieldCnt++;
        }
        if (this.getPlayerCamp() != Camp.forNumber(0)) {
            builder.setPlayerCamp(this.getPlayerCamp());
            fieldCnt++;
        }  else if (builder.hasPlayerCamp()) {
            // 清理PlayerCamp
            builder.clearPlayerCamp();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(CollectInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERNAME)) {
            builder.setPlayerName(this.getPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISCOLLECTOUT)) {
            builder.setIsCollectOut(this.getIsCollectOut());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ESTIMATENUM)) {
            builder.setEstimateNum(this.getEstimateNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCAMP)) {
            builder.setPlayerCamp(this.getPlayerCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(CollectInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasArmyId()) {
            this.innerSetArmyId(proto.getArmyId());
        } else {
            this.innerSetArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerName()) {
            this.innerSetPlayerName(proto.getPlayerName());
        } else {
            this.innerSetPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanSimpleName()) {
            this.innerSetClanSimpleName(proto.getClanSimpleName());
        } else {
            this.innerSetClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasEndTsMs()) {
            this.innerSetEndTsMs(proto.getEndTsMs());
        } else {
            this.innerSetEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIsCollectOut()) {
            this.innerSetIsCollectOut(proto.getIsCollectOut());
        } else {
            this.innerSetIsCollectOut(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasStartTsMs()) {
            this.innerSetStartTsMs(proto.getStartTsMs());
        } else {
            this.innerSetStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEstimateNum()) {
            this.innerSetEstimateNum(proto.getEstimateNum());
        } else {
            this.innerSetEstimateNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerCamp()) {
            this.innerSetPlayerCamp(proto.getPlayerCamp());
        } else {
            this.innerSetPlayerCamp(Camp.forNumber(0));
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return CollectInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(CollectInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasArmyId()) {
            this.setArmyId(proto.getArmyId());
            fieldCnt++;
        }
        if (proto.hasPlayerName()) {
            this.setPlayerName(proto.getPlayerName());
            fieldCnt++;
        }
        if (proto.hasClanSimpleName()) {
            this.setClanSimpleName(proto.getClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasEndTsMs()) {
            this.setEndTsMs(proto.getEndTsMs());
            fieldCnt++;
        }
        if (proto.hasIsCollectOut()) {
            this.setIsCollectOut(proto.getIsCollectOut());
            fieldCnt++;
        }
        if (proto.hasStartTsMs()) {
            this.setStartTsMs(proto.getStartTsMs());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasEstimateNum()) {
            this.setEstimateNum(proto.getEstimateNum());
            fieldCnt++;
        }
        if (proto.hasPlayerCamp()) {
            this.setPlayerCamp(proto.getPlayerCamp());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CollectInfo.Builder getCopySsBuilder() {
        final CollectInfo.Builder builder = CollectInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(CollectInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getArmyId() != 0L) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }  else if (builder.hasArmyId()) {
            // 清理ArmyId
            builder.clearArmyId();
            fieldCnt++;
        }
        if (!this.getPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setPlayerName(this.getPlayerName());
            fieldCnt++;
        }  else if (builder.hasPlayerName()) {
            // 清理PlayerName
            builder.clearPlayerName();
            fieldCnt++;
        }
        if (!this.getClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasClanSimpleName()) {
            // 清理ClanSimpleName
            builder.clearClanSimpleName();
            fieldCnt++;
        }
        if (this.getEndTsMs() != 0L) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }  else if (builder.hasEndTsMs()) {
            // 清理EndTsMs
            builder.clearEndTsMs();
            fieldCnt++;
        }
        if (this.getIsCollectOut()) {
            builder.setIsCollectOut(this.getIsCollectOut());
            fieldCnt++;
        }  else if (builder.hasIsCollectOut()) {
            // 清理IsCollectOut
            builder.clearIsCollectOut();
            fieldCnt++;
        }
        if (this.getStartTsMs() != 0L) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStartTsMs()) {
            // 清理StartTsMs
            builder.clearStartTsMs();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (this.getEstimateNum() != 0L) {
            builder.setEstimateNum(this.getEstimateNum());
            fieldCnt++;
        }  else if (builder.hasEstimateNum()) {
            // 清理EstimateNum
            builder.clearEstimateNum();
            fieldCnt++;
        }
        if (this.getPlayerCamp() != Camp.forNumber(0)) {
            builder.setPlayerCamp(this.getPlayerCamp());
            fieldCnt++;
        }  else if (builder.hasPlayerCamp()) {
            // 清理PlayerCamp
            builder.clearPlayerCamp();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(CollectInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERNAME)) {
            builder.setPlayerName(this.getPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISCOLLECTOUT)) {
            builder.setIsCollectOut(this.getIsCollectOut());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ESTIMATENUM)) {
            builder.setEstimateNum(this.getEstimateNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCAMP)) {
            builder.setPlayerCamp(this.getPlayerCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(CollectInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasArmyId()) {
            this.innerSetArmyId(proto.getArmyId());
        } else {
            this.innerSetArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerName()) {
            this.innerSetPlayerName(proto.getPlayerName());
        } else {
            this.innerSetPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanSimpleName()) {
            this.innerSetClanSimpleName(proto.getClanSimpleName());
        } else {
            this.innerSetClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasEndTsMs()) {
            this.innerSetEndTsMs(proto.getEndTsMs());
        } else {
            this.innerSetEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIsCollectOut()) {
            this.innerSetIsCollectOut(proto.getIsCollectOut());
        } else {
            this.innerSetIsCollectOut(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasStartTsMs()) {
            this.innerSetStartTsMs(proto.getStartTsMs());
        } else {
            this.innerSetStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEstimateNum()) {
            this.innerSetEstimateNum(proto.getEstimateNum());
        } else {
            this.innerSetEstimateNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerCamp()) {
            this.innerSetPlayerCamp(proto.getPlayerCamp());
        } else {
            this.innerSetPlayerCamp(Camp.forNumber(0));
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return CollectInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(CollectInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasArmyId()) {
            this.setArmyId(proto.getArmyId());
            fieldCnt++;
        }
        if (proto.hasPlayerName()) {
            this.setPlayerName(proto.getPlayerName());
            fieldCnt++;
        }
        if (proto.hasClanSimpleName()) {
            this.setClanSimpleName(proto.getClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasEndTsMs()) {
            this.setEndTsMs(proto.getEndTsMs());
            fieldCnt++;
        }
        if (proto.hasIsCollectOut()) {
            this.setIsCollectOut(proto.getIsCollectOut());
            fieldCnt++;
        }
        if (proto.hasStartTsMs()) {
            this.setStartTsMs(proto.getStartTsMs());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasEstimateNum()) {
            this.setEstimateNum(proto.getEstimateNum());
            fieldCnt++;
        }
        if (proto.hasPlayerCamp()) {
            this.setPlayerCamp(proto.getPlayerCamp());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        CollectInfo.Builder builder = CollectInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof CollectInfoProp)) {
            return false;
        }
        final CollectInfoProp otherNode = (CollectInfoProp) node;
        if (this.playerId != otherNode.playerId) {
            return false;
        }
        if (this.armyId != otherNode.armyId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.playerName, otherNode.playerName)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanSimpleName, otherNode.clanSimpleName)) {
            return false;
        }
        if (this.endTsMs != otherNode.endTsMs) {
            return false;
        }
        if (this.isCollectOut != otherNode.isCollectOut) {
            return false;
        }
        if (this.startTsMs != otherNode.startTsMs) {
            return false;
        }
        if (this.clanId != otherNode.clanId) {
            return false;
        }
        if (this.estimateNum != otherNode.estimateNum) {
            return false;
        }
        if (this.playerCamp != otherNode.playerCamp) {
            return false;
        }
        if (this.zoneId != otherNode.zoneId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 53;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
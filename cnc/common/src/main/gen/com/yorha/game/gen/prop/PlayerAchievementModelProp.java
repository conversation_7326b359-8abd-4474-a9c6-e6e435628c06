package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerAchievementModel;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.PlayerAchievementModelPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerAchievementModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ACHIEVEMENTS = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32AchievementMapProp achievements = null;

    public PlayerAchievementModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerAchievementModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get achievements
     *
     * @return achievements value
     */
    public Int32AchievementMapProp getAchievements() {
        if (this.achievements == null) {
            this.achievements = new Int32AchievementMapProp(this, FIELD_INDEX_ACHIEVEMENTS);
        }
        return this.achievements;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putAchievementsV(AchievementProp v) {
        this.getAchievements().put(v.getAchievementId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public AchievementProp addEmptyAchievements(Integer k) {
        return this.getAchievements().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getAchievementsSize() {
        if (this.achievements == null) {
            return 0;
        }
        return this.achievements.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isAchievementsEmpty() {
        if (this.achievements == null) {
            return true;
        }
        return this.achievements.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public AchievementProp getAchievementsV(Integer k) {
        if (this.achievements == null || !this.achievements.containsKey(k)) {
            return null;
        }
        return this.achievements.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearAchievements() {
        if (this.achievements != null) {
            this.achievements.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeAchievementsV(Integer k) {
        if (this.achievements != null) {
            this.achievements.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerAchievementModelPB.Builder getCopyCsBuilder() {
        final PlayerAchievementModelPB.Builder builder = PlayerAchievementModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerAchievementModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.achievements != null) {
            StructPB.Int32AchievementMapPB.Builder tmpBuilder = StructPB.Int32AchievementMapPB.newBuilder();
            final int tmpFieldCnt = this.achievements.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAchievements(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAchievements();
            }
        }  else if (builder.hasAchievements()) {
            // 清理Achievements
            builder.clearAchievements();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerAchievementModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTS) && this.achievements != null) {
            final boolean needClear = !builder.hasAchievements();
            final int tmpFieldCnt = this.achievements.copyChangeToCs(builder.getAchievementsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAchievements();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerAchievementModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTS) && this.achievements != null) {
            final boolean needClear = !builder.hasAchievements();
            final int tmpFieldCnt = this.achievements.copyChangeToAndClearDeleteKeysCs(builder.getAchievementsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAchievements();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerAchievementModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAchievements()) {
            this.getAchievements().mergeFromCs(proto.getAchievements());
        } else {
            if (this.achievements != null) {
                this.achievements.mergeFromCs(proto.getAchievements());
            }
        }
        this.markAll();
        return PlayerAchievementModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerAchievementModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAchievements()) {
            this.getAchievements().mergeChangeFromCs(proto.getAchievements());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerAchievementModel.Builder getCopyDbBuilder() {
        final PlayerAchievementModel.Builder builder = PlayerAchievementModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerAchievementModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.achievements != null) {
            Struct.Int32AchievementMap.Builder tmpBuilder = Struct.Int32AchievementMap.newBuilder();
            final int tmpFieldCnt = this.achievements.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAchievements(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAchievements();
            }
        }  else if (builder.hasAchievements()) {
            // 清理Achievements
            builder.clearAchievements();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerAchievementModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTS) && this.achievements != null) {
            final boolean needClear = !builder.hasAchievements();
            final int tmpFieldCnt = this.achievements.copyChangeToDb(builder.getAchievementsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAchievements();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerAchievementModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAchievements()) {
            this.getAchievements().mergeFromDb(proto.getAchievements());
        } else {
            if (this.achievements != null) {
                this.achievements.mergeFromDb(proto.getAchievements());
            }
        }
        this.markAll();
        return PlayerAchievementModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerAchievementModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAchievements()) {
            this.getAchievements().mergeChangeFromDb(proto.getAchievements());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerAchievementModel.Builder getCopySsBuilder() {
        final PlayerAchievementModel.Builder builder = PlayerAchievementModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerAchievementModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.achievements != null) {
            Struct.Int32AchievementMap.Builder tmpBuilder = Struct.Int32AchievementMap.newBuilder();
            final int tmpFieldCnt = this.achievements.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAchievements(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAchievements();
            }
        }  else if (builder.hasAchievements()) {
            // 清理Achievements
            builder.clearAchievements();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerAchievementModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTS) && this.achievements != null) {
            final boolean needClear = !builder.hasAchievements();
            final int tmpFieldCnt = this.achievements.copyChangeToSs(builder.getAchievementsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAchievements();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerAchievementModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAchievements()) {
            this.getAchievements().mergeFromSs(proto.getAchievements());
        } else {
            if (this.achievements != null) {
                this.achievements.mergeFromSs(proto.getAchievements());
            }
        }
        this.markAll();
        return PlayerAchievementModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerAchievementModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAchievements()) {
            this.getAchievements().mergeChangeFromSs(proto.getAchievements());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerAchievementModel.Builder builder = PlayerAchievementModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTS) && this.achievements != null) {
            this.achievements.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.achievements != null) {
            this.achievements.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerAchievementModelProp)) {
            return false;
        }
        final PlayerAchievementModelProp otherNode = (PlayerAchievementModelProp) node;
        if (!this.getAchievements().compareDataTo(otherNode.getAchievements())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
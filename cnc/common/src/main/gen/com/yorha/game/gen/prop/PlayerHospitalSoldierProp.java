package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.PlayerHospitalSoldier;
import com.yorha.proto.StructPB.PlayerHospitalSoldierPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerHospitalSoldierProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_SOLDIERID = 0;
    public static final int FIELD_INDEX_SEVERENUM = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int soldierId = Constant.DEFAULT_INT_VALUE;
    private int severeNum = Constant.DEFAULT_INT_VALUE;

    public PlayerHospitalSoldierProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerHospitalSoldierProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get soldierId
     *
     * @return soldierId value
     */
    public int getSoldierId() {
        return this.soldierId;
    }

    /**
     * set soldierId && set marked
     *
     * @param soldierId new value
     * @return current object
     */
    public PlayerHospitalSoldierProp setSoldierId(int soldierId) {
        if (this.soldierId != soldierId) {
            this.mark(FIELD_INDEX_SOLDIERID);
            this.soldierId = soldierId;
        }
        return this;
    }

    /**
     * inner set soldierId
     *
     * @param soldierId new value
     */
    private void innerSetSoldierId(int soldierId) {
        this.soldierId = soldierId;
    }

    /**
     * get severeNum
     *
     * @return severeNum value
     */
    public int getSevereNum() {
        return this.severeNum;
    }

    /**
     * set severeNum && set marked
     *
     * @param severeNum new value
     * @return current object
     */
    public PlayerHospitalSoldierProp setSevereNum(int severeNum) {
        if (this.severeNum != severeNum) {
            this.mark(FIELD_INDEX_SEVERENUM);
            this.severeNum = severeNum;
        }
        return this;
    }

    /**
     * inner set severeNum
     *
     * @param severeNum new value
     */
    private void innerSetSevereNum(int severeNum) {
        this.severeNum = severeNum;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerHospitalSoldierPB.Builder getCopyCsBuilder() {
        final PlayerHospitalSoldierPB.Builder builder = PlayerHospitalSoldierPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerHospitalSoldierPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSoldierId() != 0) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }  else if (builder.hasSoldierId()) {
            // 清理SoldierId
            builder.clearSoldierId();
            fieldCnt++;
        }
        if (this.getSevereNum() != 0) {
            builder.setSevereNum(this.getSevereNum());
            fieldCnt++;
        }  else if (builder.hasSevereNum()) {
            // 清理SevereNum
            builder.clearSevereNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerHospitalSoldierPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERID)) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SEVERENUM)) {
            builder.setSevereNum(this.getSevereNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerHospitalSoldierPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERID)) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SEVERENUM)) {
            builder.setSevereNum(this.getSevereNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerHospitalSoldierPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierId()) {
            this.innerSetSoldierId(proto.getSoldierId());
        } else {
            this.innerSetSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSevereNum()) {
            this.innerSetSevereNum(proto.getSevereNum());
        } else {
            this.innerSetSevereNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerHospitalSoldierProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerHospitalSoldierPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierId()) {
            this.setSoldierId(proto.getSoldierId());
            fieldCnt++;
        }
        if (proto.hasSevereNum()) {
            this.setSevereNum(proto.getSevereNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerHospitalSoldier.Builder getCopyDbBuilder() {
        final PlayerHospitalSoldier.Builder builder = PlayerHospitalSoldier.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerHospitalSoldier.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSoldierId() != 0) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }  else if (builder.hasSoldierId()) {
            // 清理SoldierId
            builder.clearSoldierId();
            fieldCnt++;
        }
        if (this.getSevereNum() != 0) {
            builder.setSevereNum(this.getSevereNum());
            fieldCnt++;
        }  else if (builder.hasSevereNum()) {
            // 清理SevereNum
            builder.clearSevereNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerHospitalSoldier.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERID)) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SEVERENUM)) {
            builder.setSevereNum(this.getSevereNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerHospitalSoldier proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierId()) {
            this.innerSetSoldierId(proto.getSoldierId());
        } else {
            this.innerSetSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSevereNum()) {
            this.innerSetSevereNum(proto.getSevereNum());
        } else {
            this.innerSetSevereNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerHospitalSoldierProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerHospitalSoldier proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierId()) {
            this.setSoldierId(proto.getSoldierId());
            fieldCnt++;
        }
        if (proto.hasSevereNum()) {
            this.setSevereNum(proto.getSevereNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerHospitalSoldier.Builder getCopySsBuilder() {
        final PlayerHospitalSoldier.Builder builder = PlayerHospitalSoldier.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerHospitalSoldier.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSoldierId() != 0) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }  else if (builder.hasSoldierId()) {
            // 清理SoldierId
            builder.clearSoldierId();
            fieldCnt++;
        }
        if (this.getSevereNum() != 0) {
            builder.setSevereNum(this.getSevereNum());
            fieldCnt++;
        }  else if (builder.hasSevereNum()) {
            // 清理SevereNum
            builder.clearSevereNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerHospitalSoldier.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERID)) {
            builder.setSoldierId(this.getSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SEVERENUM)) {
            builder.setSevereNum(this.getSevereNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerHospitalSoldier proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierId()) {
            this.innerSetSoldierId(proto.getSoldierId());
        } else {
            this.innerSetSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSevereNum()) {
            this.innerSetSevereNum(proto.getSevereNum());
        } else {
            this.innerSetSevereNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerHospitalSoldierProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerHospitalSoldier proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierId()) {
            this.setSoldierId(proto.getSoldierId());
            fieldCnt++;
        }
        if (proto.hasSevereNum()) {
            this.setSevereNum(proto.getSevereNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerHospitalSoldier.Builder builder = PlayerHospitalSoldier.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.soldierId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerHospitalSoldierProp)) {
            return false;
        }
        final PlayerHospitalSoldierProp otherNode = (PlayerHospitalSoldierProp) node;
        if (this.soldierId != otherNode.soldierId) {
            return false;
        }
        if (this.severeNum != otherNode.severeNum) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
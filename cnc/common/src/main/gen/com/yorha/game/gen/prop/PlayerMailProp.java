package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructMail.PlayerMail;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import com.yorha.proto.StructMailPB.PlayerMailPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructMailPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerMailProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_MAILID = 0;
    public static final int FIELD_INDEX_MAILTEMPLATEID = 1;
    public static final int FIELD_INDEX_READSTATE = 2;
    public static final int FIELD_INDEX_SENDER = 3;
    public static final int FIELD_INDEX_CREATETIMESTAMP = 4;
    public static final int FIELD_INDEX_STORE = 5;
    public static final int FIELD_INDEX_REWARDSTATE = 6;
    public static final int FIELD_INDEX_TITLE = 7;
    public static final int FIELD_INDEX_ITEMREWARD = 8;
    public static final int FIELD_INDEX_LANGUAGE = 9;
    public static final int FIELD_INDEX_MAILTYPE = 10;
    public static final int FIELD_INDEX_OFFLINEEXPIREDTIME = 11;
    public static final int FIELD_INDEX_CANRECEIVE = 12;
    public static final int FIELD_INDEX_CONTENT = 13;
    public static final int FIELD_INDEX_RECEIVERCARD = 14;
    public static final int FIELD_INDEX_ISIDIPMAIL = 15;
    public static final int FIELD_INDEX_INVITESTATE = 16;
    public static final int FIELD_INDEX_ISSENDER = 17;

    public static final int FIELD_COUNT = 18;

    private long markBits0 = 0L;

    private long mailId = Constant.DEFAULT_LONG_VALUE;
    private int mailTemplateId = Constant.DEFAULT_INT_VALUE;
    private MailReadState readState = MailReadState.forNumber(0);
    private MailSenderProp sender = null;
    private long createTimestamp = Constant.DEFAULT_LONG_VALUE;
    private boolean store = Constant.DEFAULT_BOOLEAN_VALUE;
    private MailRewardState rewardState = MailRewardState.forNumber(0);
    private MailShowTitleProp title = null;
    private ItemPairListProp itemReward = null;
    private Language language = Language.forNumber(0);
    private MailType mailType = MailType.forNumber(0);
    private long offlineExpiredTime = Constant.DEFAULT_LONG_VALUE;
    private boolean canReceive = Constant.DEFAULT_BOOLEAN_VALUE;
    private MailContentProp content = null;
    private PlayerMailReceiverCardProp receiverCard = null;
    private boolean isIdIpMail = Constant.DEFAULT_BOOLEAN_VALUE;
    private MailInviteState inviteState = MailInviteState.forNumber(0);
    private boolean isSender = Constant.DEFAULT_BOOLEAN_VALUE;

    public PlayerMailProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerMailProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get mailId
     *
     * @return mailId value
     */
    public long getMailId() {
        return this.mailId;
    }

    /**
     * set mailId && set marked
     *
     * @param mailId new value
     * @return current object
     */
    public PlayerMailProp setMailId(long mailId) {
        if (this.mailId != mailId) {
            this.mark(FIELD_INDEX_MAILID);
            this.mailId = mailId;
        }
        return this;
    }

    /**
     * inner set mailId
     *
     * @param mailId new value
     */
    private void innerSetMailId(long mailId) {
        this.mailId = mailId;
    }

    /**
     * get mailTemplateId
     *
     * @return mailTemplateId value
     */
    public int getMailTemplateId() {
        return this.mailTemplateId;
    }

    /**
     * set mailTemplateId && set marked
     *
     * @param mailTemplateId new value
     * @return current object
     */
    public PlayerMailProp setMailTemplateId(int mailTemplateId) {
        if (this.mailTemplateId != mailTemplateId) {
            this.mark(FIELD_INDEX_MAILTEMPLATEID);
            this.mailTemplateId = mailTemplateId;
        }
        return this;
    }

    /**
     * inner set mailTemplateId
     *
     * @param mailTemplateId new value
     */
    private void innerSetMailTemplateId(int mailTemplateId) {
        this.mailTemplateId = mailTemplateId;
    }

    /**
     * get readState
     *
     * @return readState value
     */
    public MailReadState getReadState() {
        return this.readState;
    }

    /**
     * set readState && set marked
     *
     * @param readState new value
     * @return current object
     */
    public PlayerMailProp setReadState(MailReadState readState) {
        if (readState == null) {
            throw new NullPointerException();
        }
        if (this.readState != readState) {
            this.mark(FIELD_INDEX_READSTATE);
            this.readState = readState;
        }
        return this;
    }

    /**
     * inner set readState
     *
     * @param readState new value
     */
    private void innerSetReadState(MailReadState readState) {
        this.readState = readState;
    }

    /**
     * get sender
     *
     * @return sender value
     */
    public MailSenderProp getSender() {
        if (this.sender == null) {
            this.sender = new MailSenderProp(this, FIELD_INDEX_SENDER);
        }
        return this.sender;
    }

    /**
     * get createTimestamp
     *
     * @return createTimestamp value
     */
    public long getCreateTimestamp() {
        return this.createTimestamp;
    }

    /**
     * set createTimestamp && set marked
     *
     * @param createTimestamp new value
     * @return current object
     */
    public PlayerMailProp setCreateTimestamp(long createTimestamp) {
        if (this.createTimestamp != createTimestamp) {
            this.mark(FIELD_INDEX_CREATETIMESTAMP);
            this.createTimestamp = createTimestamp;
        }
        return this;
    }

    /**
     * inner set createTimestamp
     *
     * @param createTimestamp new value
     */
    private void innerSetCreateTimestamp(long createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

    /**
     * get store
     *
     * @return store value
     */
    public boolean getStore() {
        return this.store;
    }

    /**
     * set store && set marked
     *
     * @param store new value
     * @return current object
     */
    public PlayerMailProp setStore(boolean store) {
        if (this.store != store) {
            this.mark(FIELD_INDEX_STORE);
            this.store = store;
        }
        return this;
    }

    /**
     * inner set store
     *
     * @param store new value
     */
    private void innerSetStore(boolean store) {
        this.store = store;
    }

    /**
     * get rewardState
     *
     * @return rewardState value
     */
    public MailRewardState getRewardState() {
        return this.rewardState;
    }

    /**
     * set rewardState && set marked
     *
     * @param rewardState new value
     * @return current object
     */
    public PlayerMailProp setRewardState(MailRewardState rewardState) {
        if (rewardState == null) {
            throw new NullPointerException();
        }
        if (this.rewardState != rewardState) {
            this.mark(FIELD_INDEX_REWARDSTATE);
            this.rewardState = rewardState;
        }
        return this;
    }

    /**
     * inner set rewardState
     *
     * @param rewardState new value
     */
    private void innerSetRewardState(MailRewardState rewardState) {
        this.rewardState = rewardState;
    }

    /**
     * get title
     *
     * @return title value
     */
    public MailShowTitleProp getTitle() {
        if (this.title == null) {
            this.title = new MailShowTitleProp(this, FIELD_INDEX_TITLE);
        }
        return this.title;
    }

    /**
     * get itemReward
     *
     * @return itemReward value
     */
    public ItemPairListProp getItemReward() {
        if (this.itemReward == null) {
            this.itemReward = new ItemPairListProp(this, FIELD_INDEX_ITEMREWARD);
        }
        return this.itemReward;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addItemReward(ItemPairProp v) {
        this.getItemReward().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ItemPairProp getItemRewardIndex(int index) {
        return this.getItemReward().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public ItemPairProp removeItemReward(ItemPairProp v) {
        if (this.itemReward == null) {
            return null;
        }
        if(this.itemReward.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getItemRewardSize() {
        if (this.itemReward == null) {
            return 0;
        }
        return this.itemReward.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isItemRewardEmpty() {
        if (this.itemReward == null) {
            return true;
        }
        return this.getItemReward().isEmpty();
    }

    /**
     * clear list
     */
    public void clearItemReward() {
        this.getItemReward().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ItemPairProp removeItemRewardIndex(int index) {
        return this.getItemReward().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public ItemPairProp setItemRewardIndex(int index, ItemPairProp v) {
        return this.getItemReward().set(index, v);
    }
    /**
     * get language
     *
     * @return language value
     */
    public Language getLanguage() {
        return this.language;
    }

    /**
     * set language && set marked
     *
     * @param language new value
     * @return current object
     */
    public PlayerMailProp setLanguage(Language language) {
        if (language == null) {
            throw new NullPointerException();
        }
        if (this.language != language) {
            this.mark(FIELD_INDEX_LANGUAGE);
            this.language = language;
        }
        return this;
    }

    /**
     * inner set language
     *
     * @param language new value
     */
    private void innerSetLanguage(Language language) {
        this.language = language;
    }

    /**
     * get mailType
     *
     * @return mailType value
     */
    public MailType getMailType() {
        return this.mailType;
    }

    /**
     * set mailType && set marked
     *
     * @param mailType new value
     * @return current object
     */
    public PlayerMailProp setMailType(MailType mailType) {
        if (mailType == null) {
            throw new NullPointerException();
        }
        if (this.mailType != mailType) {
            this.mark(FIELD_INDEX_MAILTYPE);
            this.mailType = mailType;
        }
        return this;
    }

    /**
     * inner set mailType
     *
     * @param mailType new value
     */
    private void innerSetMailType(MailType mailType) {
        this.mailType = mailType;
    }

    /**
     * get offlineExpiredTime
     *
     * @return offlineExpiredTime value
     */
    public long getOfflineExpiredTime() {
        return this.offlineExpiredTime;
    }

    /**
     * set offlineExpiredTime && set marked
     *
     * @param offlineExpiredTime new value
     * @return current object
     */
    public PlayerMailProp setOfflineExpiredTime(long offlineExpiredTime) {
        if (this.offlineExpiredTime != offlineExpiredTime) {
            this.mark(FIELD_INDEX_OFFLINEEXPIREDTIME);
            this.offlineExpiredTime = offlineExpiredTime;
        }
        return this;
    }

    /**
     * inner set offlineExpiredTime
     *
     * @param offlineExpiredTime new value
     */
    private void innerSetOfflineExpiredTime(long offlineExpiredTime) {
        this.offlineExpiredTime = offlineExpiredTime;
    }

    /**
     * get canReceive
     *
     * @return canReceive value
     */
    public boolean getCanReceive() {
        return this.canReceive;
    }

    /**
     * set canReceive && set marked
     *
     * @param canReceive new value
     * @return current object
     */
    public PlayerMailProp setCanReceive(boolean canReceive) {
        if (this.canReceive != canReceive) {
            this.mark(FIELD_INDEX_CANRECEIVE);
            this.canReceive = canReceive;
        }
        return this;
    }

    /**
     * inner set canReceive
     *
     * @param canReceive new value
     */
    private void innerSetCanReceive(boolean canReceive) {
        this.canReceive = canReceive;
    }

    /**
     * get content
     *
     * @return content value
     */
    public MailContentProp getContent() {
        if (this.content == null) {
            this.content = new MailContentProp(this, FIELD_INDEX_CONTENT);
        }
        return this.content;
    }

    /**
     * get receiverCard
     *
     * @return receiverCard value
     */
    public PlayerMailReceiverCardProp getReceiverCard() {
        if (this.receiverCard == null) {
            this.receiverCard = new PlayerMailReceiverCardProp(this, FIELD_INDEX_RECEIVERCARD);
        }
        return this.receiverCard;
    }

    /**
     * get isIdIpMail
     *
     * @return isIdIpMail value
     */
    public boolean getIsIdIpMail() {
        return this.isIdIpMail;
    }

    /**
     * set isIdIpMail && set marked
     *
     * @param isIdIpMail new value
     * @return current object
     */
    public PlayerMailProp setIsIdIpMail(boolean isIdIpMail) {
        if (this.isIdIpMail != isIdIpMail) {
            this.mark(FIELD_INDEX_ISIDIPMAIL);
            this.isIdIpMail = isIdIpMail;
        }
        return this;
    }

    /**
     * inner set isIdIpMail
     *
     * @param isIdIpMail new value
     */
    private void innerSetIsIdIpMail(boolean isIdIpMail) {
        this.isIdIpMail = isIdIpMail;
    }

    /**
     * get inviteState
     *
     * @return inviteState value
     */
    public MailInviteState getInviteState() {
        return this.inviteState;
    }

    /**
     * set inviteState && set marked
     *
     * @param inviteState new value
     * @return current object
     */
    public PlayerMailProp setInviteState(MailInviteState inviteState) {
        if (inviteState == null) {
            throw new NullPointerException();
        }
        if (this.inviteState != inviteState) {
            this.mark(FIELD_INDEX_INVITESTATE);
            this.inviteState = inviteState;
        }
        return this;
    }

    /**
     * inner set inviteState
     *
     * @param inviteState new value
     */
    private void innerSetInviteState(MailInviteState inviteState) {
        this.inviteState = inviteState;
    }

    /**
     * get isSender
     *
     * @return isSender value
     */
    public boolean getIsSender() {
        return this.isSender;
    }

    /**
     * set isSender && set marked
     *
     * @param isSender new value
     * @return current object
     */
    public PlayerMailProp setIsSender(boolean isSender) {
        if (this.isSender != isSender) {
            this.mark(FIELD_INDEX_ISSENDER);
            this.isSender = isSender;
        }
        return this;
    }

    /**
     * inner set isSender
     *
     * @param isSender new value
     */
    private void innerSetIsSender(boolean isSender) {
        this.isSender = isSender;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMailPB.Builder getCopyCsBuilder() {
        final PlayerMailPB.Builder builder = PlayerMailPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerMailPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMailId() != 0L) {
            builder.setMailId(this.getMailId());
            fieldCnt++;
        }  else if (builder.hasMailId()) {
            // 清理MailId
            builder.clearMailId();
            fieldCnt++;
        }
        if (this.getMailTemplateId() != 0) {
            builder.setMailTemplateId(this.getMailTemplateId());
            fieldCnt++;
        }  else if (builder.hasMailTemplateId()) {
            // 清理MailTemplateId
            builder.clearMailTemplateId();
            fieldCnt++;
        }
        if (this.getReadState() != MailReadState.forNumber(0)) {
            builder.setReadState(this.getReadState());
            fieldCnt++;
        }  else if (builder.hasReadState()) {
            // 清理ReadState
            builder.clearReadState();
            fieldCnt++;
        }
        if (this.sender != null) {
            StructMailPB.MailSenderPB.Builder tmpBuilder = StructMailPB.MailSenderPB.newBuilder();
            final int tmpFieldCnt = this.sender.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSender(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSender();
            }
        }  else if (builder.hasSender()) {
            // 清理Sender
            builder.clearSender();
            fieldCnt++;
        }
        if (this.getCreateTimestamp() != 0L) {
            builder.setCreateTimestamp(this.getCreateTimestamp());
            fieldCnt++;
        }  else if (builder.hasCreateTimestamp()) {
            // 清理CreateTimestamp
            builder.clearCreateTimestamp();
            fieldCnt++;
        }
        if (this.getStore()) {
            builder.setStore(this.getStore());
            fieldCnt++;
        }  else if (builder.hasStore()) {
            // 清理Store
            builder.clearStore();
            fieldCnt++;
        }
        if (this.getRewardState() != MailRewardState.forNumber(0)) {
            builder.setRewardState(this.getRewardState());
            fieldCnt++;
        }  else if (builder.hasRewardState()) {
            // 清理RewardState
            builder.clearRewardState();
            fieldCnt++;
        }
        if (this.title != null) {
            StructMailPB.MailShowTitlePB.Builder tmpBuilder = StructMailPB.MailShowTitlePB.newBuilder();
            final int tmpFieldCnt = this.title.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTitle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTitle();
            }
        }  else if (builder.hasTitle()) {
            // 清理Title
            builder.clearTitle();
            fieldCnt++;
        }
        if (this.itemReward != null) {
            StructPB.ItemPairListPB.Builder tmpBuilder = StructPB.ItemPairListPB.newBuilder();
            final int tmpFieldCnt = this.itemReward.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItemReward(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItemReward();
            }
        }  else if (builder.hasItemReward()) {
            // 清理ItemReward
            builder.clearItemReward();
            fieldCnt++;
        }
        if (this.getLanguage() != Language.forNumber(0)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }  else if (builder.hasLanguage()) {
            // 清理Language
            builder.clearLanguage();
            fieldCnt++;
        }
        if (this.getMailType() != MailType.forNumber(0)) {
            builder.setMailType(this.getMailType());
            fieldCnt++;
        }  else if (builder.hasMailType()) {
            // 清理MailType
            builder.clearMailType();
            fieldCnt++;
        }
        if (this.getOfflineExpiredTime() != 0L) {
            builder.setOfflineExpiredTime(this.getOfflineExpiredTime());
            fieldCnt++;
        }  else if (builder.hasOfflineExpiredTime()) {
            // 清理OfflineExpiredTime
            builder.clearOfflineExpiredTime();
            fieldCnt++;
        }
        if (this.getCanReceive()) {
            builder.setCanReceive(this.getCanReceive());
            fieldCnt++;
        }  else if (builder.hasCanReceive()) {
            // 清理CanReceive
            builder.clearCanReceive();
            fieldCnt++;
        }
        if (this.content != null) {
            StructMailPB.MailContentPB.Builder tmpBuilder = StructMailPB.MailContentPB.newBuilder();
            final int tmpFieldCnt = this.content.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setContent(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearContent();
            }
        }  else if (builder.hasContent()) {
            // 清理Content
            builder.clearContent();
            fieldCnt++;
        }
        if (this.receiverCard != null) {
            StructMailPB.PlayerMailReceiverCardPB.Builder tmpBuilder = StructMailPB.PlayerMailReceiverCardPB.newBuilder();
            final int tmpFieldCnt = this.receiverCard.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setReceiverCard(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearReceiverCard();
            }
        }  else if (builder.hasReceiverCard()) {
            // 清理ReceiverCard
            builder.clearReceiverCard();
            fieldCnt++;
        }
        if (this.getIsIdIpMail()) {
            builder.setIsIdIpMail(this.getIsIdIpMail());
            fieldCnt++;
        }  else if (builder.hasIsIdIpMail()) {
            // 清理IsIdIpMail
            builder.clearIsIdIpMail();
            fieldCnt++;
        }
        if (this.getInviteState() != MailInviteState.forNumber(0)) {
            builder.setInviteState(this.getInviteState());
            fieldCnt++;
        }  else if (builder.hasInviteState()) {
            // 清理InviteState
            builder.clearInviteState();
            fieldCnt++;
        }
        if (this.getIsSender()) {
            builder.setIsSender(this.getIsSender());
            fieldCnt++;
        }  else if (builder.hasIsSender()) {
            // 清理IsSender
            builder.clearIsSender();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerMailPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAILID)) {
            builder.setMailId(this.getMailId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAILTEMPLATEID)) {
            builder.setMailTemplateId(this.getMailTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_READSTATE)) {
            builder.setReadState(this.getReadState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SENDER) && this.sender != null) {
            final boolean needClear = !builder.hasSender();
            final int tmpFieldCnt = this.sender.copyChangeToCs(builder.getSenderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSender();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETIMESTAMP)) {
            builder.setCreateTimestamp(this.getCreateTimestamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STORE)) {
            builder.setStore(this.getStore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDSTATE)) {
            builder.setRewardState(this.getRewardState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TITLE) && this.title != null) {
            final boolean needClear = !builder.hasTitle();
            final int tmpFieldCnt = this.title.copyChangeToCs(builder.getTitleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTitle();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            final boolean needClear = !builder.hasItemReward();
            final int tmpFieldCnt = this.itemReward.copyChangeToCs(builder.getItemRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemReward();
            }
        }
        if (this.hasMark(FIELD_INDEX_LANGUAGE)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAILTYPE)) {
            builder.setMailType(this.getMailType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OFFLINEEXPIREDTIME)) {
            builder.setOfflineExpiredTime(this.getOfflineExpiredTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANRECEIVE)) {
            builder.setCanReceive(this.getCanReceive());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTENT) && this.content != null) {
            final boolean needClear = !builder.hasContent();
            final int tmpFieldCnt = this.content.copyChangeToCs(builder.getContentBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContent();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECEIVERCARD) && this.receiverCard != null) {
            final boolean needClear = !builder.hasReceiverCard();
            final int tmpFieldCnt = this.receiverCard.copyChangeToCs(builder.getReceiverCardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearReceiverCard();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISIDIPMAIL)) {
            builder.setIsIdIpMail(this.getIsIdIpMail());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITESTATE)) {
            builder.setInviteState(this.getInviteState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISSENDER)) {
            builder.setIsSender(this.getIsSender());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerMailPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAILID)) {
            builder.setMailId(this.getMailId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAILTEMPLATEID)) {
            builder.setMailTemplateId(this.getMailTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_READSTATE)) {
            builder.setReadState(this.getReadState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SENDER) && this.sender != null) {
            final boolean needClear = !builder.hasSender();
            final int tmpFieldCnt = this.sender.copyChangeToAndClearDeleteKeysCs(builder.getSenderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSender();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETIMESTAMP)) {
            builder.setCreateTimestamp(this.getCreateTimestamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STORE)) {
            builder.setStore(this.getStore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDSTATE)) {
            builder.setRewardState(this.getRewardState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TITLE) && this.title != null) {
            final boolean needClear = !builder.hasTitle();
            final int tmpFieldCnt = this.title.copyChangeToAndClearDeleteKeysCs(builder.getTitleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTitle();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            final boolean needClear = !builder.hasItemReward();
            final int tmpFieldCnt = this.itemReward.copyChangeToCs(builder.getItemRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemReward();
            }
        }
        if (this.hasMark(FIELD_INDEX_LANGUAGE)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAILTYPE)) {
            builder.setMailType(this.getMailType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OFFLINEEXPIREDTIME)) {
            builder.setOfflineExpiredTime(this.getOfflineExpiredTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANRECEIVE)) {
            builder.setCanReceive(this.getCanReceive());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTENT) && this.content != null) {
            final boolean needClear = !builder.hasContent();
            final int tmpFieldCnt = this.content.copyChangeToAndClearDeleteKeysCs(builder.getContentBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContent();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECEIVERCARD) && this.receiverCard != null) {
            final boolean needClear = !builder.hasReceiverCard();
            final int tmpFieldCnt = this.receiverCard.copyChangeToAndClearDeleteKeysCs(builder.getReceiverCardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearReceiverCard();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISIDIPMAIL)) {
            builder.setIsIdIpMail(this.getIsIdIpMail());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITESTATE)) {
            builder.setInviteState(this.getInviteState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISSENDER)) {
            builder.setIsSender(this.getIsSender());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerMailPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMailId()) {
            this.innerSetMailId(proto.getMailId());
        } else {
            this.innerSetMailId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMailTemplateId()) {
            this.innerSetMailTemplateId(proto.getMailTemplateId());
        } else {
            this.innerSetMailTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasReadState()) {
            this.innerSetReadState(proto.getReadState());
        } else {
            this.innerSetReadState(MailReadState.forNumber(0));
        }
        if (proto.hasSender()) {
            this.getSender().mergeFromCs(proto.getSender());
        } else {
            if (this.sender != null) {
                this.sender.mergeFromCs(proto.getSender());
            }
        }
        if (proto.hasCreateTimestamp()) {
            this.innerSetCreateTimestamp(proto.getCreateTimestamp());
        } else {
            this.innerSetCreateTimestamp(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStore()) {
            this.innerSetStore(proto.getStore());
        } else {
            this.innerSetStore(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasRewardState()) {
            this.innerSetRewardState(proto.getRewardState());
        } else {
            this.innerSetRewardState(MailRewardState.forNumber(0));
        }
        if (proto.hasTitle()) {
            this.getTitle().mergeFromCs(proto.getTitle());
        } else {
            if (this.title != null) {
                this.title.mergeFromCs(proto.getTitle());
            }
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeFromCs(proto.getItemReward());
        } else {
            if (this.itemReward != null) {
                this.itemReward.mergeFromCs(proto.getItemReward());
            }
        }
        if (proto.hasLanguage()) {
            this.innerSetLanguage(proto.getLanguage());
        } else {
            this.innerSetLanguage(Language.forNumber(0));
        }
        if (proto.hasMailType()) {
            this.innerSetMailType(proto.getMailType());
        } else {
            this.innerSetMailType(MailType.forNumber(0));
        }
        if (proto.hasOfflineExpiredTime()) {
            this.innerSetOfflineExpiredTime(proto.getOfflineExpiredTime());
        } else {
            this.innerSetOfflineExpiredTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCanReceive()) {
            this.innerSetCanReceive(proto.getCanReceive());
        } else {
            this.innerSetCanReceive(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasContent()) {
            this.getContent().mergeFromCs(proto.getContent());
        } else {
            if (this.content != null) {
                this.content.mergeFromCs(proto.getContent());
            }
        }
        if (proto.hasReceiverCard()) {
            this.getReceiverCard().mergeFromCs(proto.getReceiverCard());
        } else {
            if (this.receiverCard != null) {
                this.receiverCard.mergeFromCs(proto.getReceiverCard());
            }
        }
        if (proto.hasIsIdIpMail()) {
            this.innerSetIsIdIpMail(proto.getIsIdIpMail());
        } else {
            this.innerSetIsIdIpMail(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasInviteState()) {
            this.innerSetInviteState(proto.getInviteState());
        } else {
            this.innerSetInviteState(MailInviteState.forNumber(0));
        }
        if (proto.hasIsSender()) {
            this.innerSetIsSender(proto.getIsSender());
        } else {
            this.innerSetIsSender(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return PlayerMailProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerMailPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMailId()) {
            this.setMailId(proto.getMailId());
            fieldCnt++;
        }
        if (proto.hasMailTemplateId()) {
            this.setMailTemplateId(proto.getMailTemplateId());
            fieldCnt++;
        }
        if (proto.hasReadState()) {
            this.setReadState(proto.getReadState());
            fieldCnt++;
        }
        if (proto.hasSender()) {
            this.getSender().mergeChangeFromCs(proto.getSender());
            fieldCnt++;
        }
        if (proto.hasCreateTimestamp()) {
            this.setCreateTimestamp(proto.getCreateTimestamp());
            fieldCnt++;
        }
        if (proto.hasStore()) {
            this.setStore(proto.getStore());
            fieldCnt++;
        }
        if (proto.hasRewardState()) {
            this.setRewardState(proto.getRewardState());
            fieldCnt++;
        }
        if (proto.hasTitle()) {
            this.getTitle().mergeChangeFromCs(proto.getTitle());
            fieldCnt++;
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeChangeFromCs(proto.getItemReward());
            fieldCnt++;
        }
        if (proto.hasLanguage()) {
            this.setLanguage(proto.getLanguage());
            fieldCnt++;
        }
        if (proto.hasMailType()) {
            this.setMailType(proto.getMailType());
            fieldCnt++;
        }
        if (proto.hasOfflineExpiredTime()) {
            this.setOfflineExpiredTime(proto.getOfflineExpiredTime());
            fieldCnt++;
        }
        if (proto.hasCanReceive()) {
            this.setCanReceive(proto.getCanReceive());
            fieldCnt++;
        }
        if (proto.hasContent()) {
            this.getContent().mergeChangeFromCs(proto.getContent());
            fieldCnt++;
        }
        if (proto.hasReceiverCard()) {
            this.getReceiverCard().mergeChangeFromCs(proto.getReceiverCard());
            fieldCnt++;
        }
        if (proto.hasIsIdIpMail()) {
            this.setIsIdIpMail(proto.getIsIdIpMail());
            fieldCnt++;
        }
        if (proto.hasInviteState()) {
            this.setInviteState(proto.getInviteState());
            fieldCnt++;
        }
        if (proto.hasIsSender()) {
            this.setIsSender(proto.getIsSender());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMail.Builder getCopyDbBuilder() {
        final PlayerMail.Builder builder = PlayerMail.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerMail.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMailId() != 0L) {
            builder.setMailId(this.getMailId());
            fieldCnt++;
        }  else if (builder.hasMailId()) {
            // 清理MailId
            builder.clearMailId();
            fieldCnt++;
        }
        if (this.getMailTemplateId() != 0) {
            builder.setMailTemplateId(this.getMailTemplateId());
            fieldCnt++;
        }  else if (builder.hasMailTemplateId()) {
            // 清理MailTemplateId
            builder.clearMailTemplateId();
            fieldCnt++;
        }
        if (this.getReadState() != MailReadState.forNumber(0)) {
            builder.setReadState(this.getReadState());
            fieldCnt++;
        }  else if (builder.hasReadState()) {
            // 清理ReadState
            builder.clearReadState();
            fieldCnt++;
        }
        if (this.sender != null) {
            StructMail.MailSender.Builder tmpBuilder = StructMail.MailSender.newBuilder();
            final int tmpFieldCnt = this.sender.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSender(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSender();
            }
        }  else if (builder.hasSender()) {
            // 清理Sender
            builder.clearSender();
            fieldCnt++;
        }
        if (this.getCreateTimestamp() != 0L) {
            builder.setCreateTimestamp(this.getCreateTimestamp());
            fieldCnt++;
        }  else if (builder.hasCreateTimestamp()) {
            // 清理CreateTimestamp
            builder.clearCreateTimestamp();
            fieldCnt++;
        }
        if (this.getStore()) {
            builder.setStore(this.getStore());
            fieldCnt++;
        }  else if (builder.hasStore()) {
            // 清理Store
            builder.clearStore();
            fieldCnt++;
        }
        if (this.getRewardState() != MailRewardState.forNumber(0)) {
            builder.setRewardState(this.getRewardState());
            fieldCnt++;
        }  else if (builder.hasRewardState()) {
            // 清理RewardState
            builder.clearRewardState();
            fieldCnt++;
        }
        if (this.title != null) {
            StructMail.MailShowTitle.Builder tmpBuilder = StructMail.MailShowTitle.newBuilder();
            final int tmpFieldCnt = this.title.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTitle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTitle();
            }
        }  else if (builder.hasTitle()) {
            // 清理Title
            builder.clearTitle();
            fieldCnt++;
        }
        if (this.itemReward != null) {
            Struct.ItemPairList.Builder tmpBuilder = Struct.ItemPairList.newBuilder();
            final int tmpFieldCnt = this.itemReward.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItemReward(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItemReward();
            }
        }  else if (builder.hasItemReward()) {
            // 清理ItemReward
            builder.clearItemReward();
            fieldCnt++;
        }
        if (this.getLanguage() != Language.forNumber(0)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }  else if (builder.hasLanguage()) {
            // 清理Language
            builder.clearLanguage();
            fieldCnt++;
        }
        if (this.getMailType() != MailType.forNumber(0)) {
            builder.setMailType(this.getMailType());
            fieldCnt++;
        }  else if (builder.hasMailType()) {
            // 清理MailType
            builder.clearMailType();
            fieldCnt++;
        }
        if (this.getOfflineExpiredTime() != 0L) {
            builder.setOfflineExpiredTime(this.getOfflineExpiredTime());
            fieldCnt++;
        }  else if (builder.hasOfflineExpiredTime()) {
            // 清理OfflineExpiredTime
            builder.clearOfflineExpiredTime();
            fieldCnt++;
        }
        if (this.getCanReceive()) {
            builder.setCanReceive(this.getCanReceive());
            fieldCnt++;
        }  else if (builder.hasCanReceive()) {
            // 清理CanReceive
            builder.clearCanReceive();
            fieldCnt++;
        }
        if (this.content != null) {
            StructMail.MailContent.Builder tmpBuilder = StructMail.MailContent.newBuilder();
            final int tmpFieldCnt = this.content.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setContent(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearContent();
            }
        }  else if (builder.hasContent()) {
            // 清理Content
            builder.clearContent();
            fieldCnt++;
        }
        if (this.receiverCard != null) {
            StructMail.PlayerMailReceiverCard.Builder tmpBuilder = StructMail.PlayerMailReceiverCard.newBuilder();
            final int tmpFieldCnt = this.receiverCard.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setReceiverCard(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearReceiverCard();
            }
        }  else if (builder.hasReceiverCard()) {
            // 清理ReceiverCard
            builder.clearReceiverCard();
            fieldCnt++;
        }
        if (this.getIsIdIpMail()) {
            builder.setIsIdIpMail(this.getIsIdIpMail());
            fieldCnt++;
        }  else if (builder.hasIsIdIpMail()) {
            // 清理IsIdIpMail
            builder.clearIsIdIpMail();
            fieldCnt++;
        }
        if (this.getInviteState() != MailInviteState.forNumber(0)) {
            builder.setInviteState(this.getInviteState());
            fieldCnt++;
        }  else if (builder.hasInviteState()) {
            // 清理InviteState
            builder.clearInviteState();
            fieldCnt++;
        }
        if (this.getIsSender()) {
            builder.setIsSender(this.getIsSender());
            fieldCnt++;
        }  else if (builder.hasIsSender()) {
            // 清理IsSender
            builder.clearIsSender();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerMail.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAILID)) {
            builder.setMailId(this.getMailId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAILTEMPLATEID)) {
            builder.setMailTemplateId(this.getMailTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_READSTATE)) {
            builder.setReadState(this.getReadState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SENDER) && this.sender != null) {
            final boolean needClear = !builder.hasSender();
            final int tmpFieldCnt = this.sender.copyChangeToDb(builder.getSenderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSender();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETIMESTAMP)) {
            builder.setCreateTimestamp(this.getCreateTimestamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STORE)) {
            builder.setStore(this.getStore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDSTATE)) {
            builder.setRewardState(this.getRewardState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TITLE) && this.title != null) {
            final boolean needClear = !builder.hasTitle();
            final int tmpFieldCnt = this.title.copyChangeToDb(builder.getTitleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTitle();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            final boolean needClear = !builder.hasItemReward();
            final int tmpFieldCnt = this.itemReward.copyChangeToDb(builder.getItemRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemReward();
            }
        }
        if (this.hasMark(FIELD_INDEX_LANGUAGE)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAILTYPE)) {
            builder.setMailType(this.getMailType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OFFLINEEXPIREDTIME)) {
            builder.setOfflineExpiredTime(this.getOfflineExpiredTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANRECEIVE)) {
            builder.setCanReceive(this.getCanReceive());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTENT) && this.content != null) {
            final boolean needClear = !builder.hasContent();
            final int tmpFieldCnt = this.content.copyChangeToDb(builder.getContentBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContent();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECEIVERCARD) && this.receiverCard != null) {
            final boolean needClear = !builder.hasReceiverCard();
            final int tmpFieldCnt = this.receiverCard.copyChangeToDb(builder.getReceiverCardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearReceiverCard();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISIDIPMAIL)) {
            builder.setIsIdIpMail(this.getIsIdIpMail());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITESTATE)) {
            builder.setInviteState(this.getInviteState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISSENDER)) {
            builder.setIsSender(this.getIsSender());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerMail proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMailId()) {
            this.innerSetMailId(proto.getMailId());
        } else {
            this.innerSetMailId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMailTemplateId()) {
            this.innerSetMailTemplateId(proto.getMailTemplateId());
        } else {
            this.innerSetMailTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasReadState()) {
            this.innerSetReadState(proto.getReadState());
        } else {
            this.innerSetReadState(MailReadState.forNumber(0));
        }
        if (proto.hasSender()) {
            this.getSender().mergeFromDb(proto.getSender());
        } else {
            if (this.sender != null) {
                this.sender.mergeFromDb(proto.getSender());
            }
        }
        if (proto.hasCreateTimestamp()) {
            this.innerSetCreateTimestamp(proto.getCreateTimestamp());
        } else {
            this.innerSetCreateTimestamp(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStore()) {
            this.innerSetStore(proto.getStore());
        } else {
            this.innerSetStore(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasRewardState()) {
            this.innerSetRewardState(proto.getRewardState());
        } else {
            this.innerSetRewardState(MailRewardState.forNumber(0));
        }
        if (proto.hasTitle()) {
            this.getTitle().mergeFromDb(proto.getTitle());
        } else {
            if (this.title != null) {
                this.title.mergeFromDb(proto.getTitle());
            }
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeFromDb(proto.getItemReward());
        } else {
            if (this.itemReward != null) {
                this.itemReward.mergeFromDb(proto.getItemReward());
            }
        }
        if (proto.hasLanguage()) {
            this.innerSetLanguage(proto.getLanguage());
        } else {
            this.innerSetLanguage(Language.forNumber(0));
        }
        if (proto.hasMailType()) {
            this.innerSetMailType(proto.getMailType());
        } else {
            this.innerSetMailType(MailType.forNumber(0));
        }
        if (proto.hasOfflineExpiredTime()) {
            this.innerSetOfflineExpiredTime(proto.getOfflineExpiredTime());
        } else {
            this.innerSetOfflineExpiredTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCanReceive()) {
            this.innerSetCanReceive(proto.getCanReceive());
        } else {
            this.innerSetCanReceive(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasContent()) {
            this.getContent().mergeFromDb(proto.getContent());
        } else {
            if (this.content != null) {
                this.content.mergeFromDb(proto.getContent());
            }
        }
        if (proto.hasReceiverCard()) {
            this.getReceiverCard().mergeFromDb(proto.getReceiverCard());
        } else {
            if (this.receiverCard != null) {
                this.receiverCard.mergeFromDb(proto.getReceiverCard());
            }
        }
        if (proto.hasIsIdIpMail()) {
            this.innerSetIsIdIpMail(proto.getIsIdIpMail());
        } else {
            this.innerSetIsIdIpMail(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasInviteState()) {
            this.innerSetInviteState(proto.getInviteState());
        } else {
            this.innerSetInviteState(MailInviteState.forNumber(0));
        }
        if (proto.hasIsSender()) {
            this.innerSetIsSender(proto.getIsSender());
        } else {
            this.innerSetIsSender(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return PlayerMailProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerMail proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMailId()) {
            this.setMailId(proto.getMailId());
            fieldCnt++;
        }
        if (proto.hasMailTemplateId()) {
            this.setMailTemplateId(proto.getMailTemplateId());
            fieldCnt++;
        }
        if (proto.hasReadState()) {
            this.setReadState(proto.getReadState());
            fieldCnt++;
        }
        if (proto.hasSender()) {
            this.getSender().mergeChangeFromDb(proto.getSender());
            fieldCnt++;
        }
        if (proto.hasCreateTimestamp()) {
            this.setCreateTimestamp(proto.getCreateTimestamp());
            fieldCnt++;
        }
        if (proto.hasStore()) {
            this.setStore(proto.getStore());
            fieldCnt++;
        }
        if (proto.hasRewardState()) {
            this.setRewardState(proto.getRewardState());
            fieldCnt++;
        }
        if (proto.hasTitle()) {
            this.getTitle().mergeChangeFromDb(proto.getTitle());
            fieldCnt++;
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeChangeFromDb(proto.getItemReward());
            fieldCnt++;
        }
        if (proto.hasLanguage()) {
            this.setLanguage(proto.getLanguage());
            fieldCnt++;
        }
        if (proto.hasMailType()) {
            this.setMailType(proto.getMailType());
            fieldCnt++;
        }
        if (proto.hasOfflineExpiredTime()) {
            this.setOfflineExpiredTime(proto.getOfflineExpiredTime());
            fieldCnt++;
        }
        if (proto.hasCanReceive()) {
            this.setCanReceive(proto.getCanReceive());
            fieldCnt++;
        }
        if (proto.hasContent()) {
            this.getContent().mergeChangeFromDb(proto.getContent());
            fieldCnt++;
        }
        if (proto.hasReceiverCard()) {
            this.getReceiverCard().mergeChangeFromDb(proto.getReceiverCard());
            fieldCnt++;
        }
        if (proto.hasIsIdIpMail()) {
            this.setIsIdIpMail(proto.getIsIdIpMail());
            fieldCnt++;
        }
        if (proto.hasInviteState()) {
            this.setInviteState(proto.getInviteState());
            fieldCnt++;
        }
        if (proto.hasIsSender()) {
            this.setIsSender(proto.getIsSender());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMail.Builder getCopySsBuilder() {
        final PlayerMail.Builder builder = PlayerMail.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerMail.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMailId() != 0L) {
            builder.setMailId(this.getMailId());
            fieldCnt++;
        }  else if (builder.hasMailId()) {
            // 清理MailId
            builder.clearMailId();
            fieldCnt++;
        }
        if (this.getMailTemplateId() != 0) {
            builder.setMailTemplateId(this.getMailTemplateId());
            fieldCnt++;
        }  else if (builder.hasMailTemplateId()) {
            // 清理MailTemplateId
            builder.clearMailTemplateId();
            fieldCnt++;
        }
        if (this.getReadState() != MailReadState.forNumber(0)) {
            builder.setReadState(this.getReadState());
            fieldCnt++;
        }  else if (builder.hasReadState()) {
            // 清理ReadState
            builder.clearReadState();
            fieldCnt++;
        }
        if (this.sender != null) {
            StructMail.MailSender.Builder tmpBuilder = StructMail.MailSender.newBuilder();
            final int tmpFieldCnt = this.sender.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSender(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSender();
            }
        }  else if (builder.hasSender()) {
            // 清理Sender
            builder.clearSender();
            fieldCnt++;
        }
        if (this.getCreateTimestamp() != 0L) {
            builder.setCreateTimestamp(this.getCreateTimestamp());
            fieldCnt++;
        }  else if (builder.hasCreateTimestamp()) {
            // 清理CreateTimestamp
            builder.clearCreateTimestamp();
            fieldCnt++;
        }
        if (this.getStore()) {
            builder.setStore(this.getStore());
            fieldCnt++;
        }  else if (builder.hasStore()) {
            // 清理Store
            builder.clearStore();
            fieldCnt++;
        }
        if (this.getRewardState() != MailRewardState.forNumber(0)) {
            builder.setRewardState(this.getRewardState());
            fieldCnt++;
        }  else if (builder.hasRewardState()) {
            // 清理RewardState
            builder.clearRewardState();
            fieldCnt++;
        }
        if (this.title != null) {
            StructMail.MailShowTitle.Builder tmpBuilder = StructMail.MailShowTitle.newBuilder();
            final int tmpFieldCnt = this.title.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTitle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTitle();
            }
        }  else if (builder.hasTitle()) {
            // 清理Title
            builder.clearTitle();
            fieldCnt++;
        }
        if (this.itemReward != null) {
            Struct.ItemPairList.Builder tmpBuilder = Struct.ItemPairList.newBuilder();
            final int tmpFieldCnt = this.itemReward.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItemReward(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItemReward();
            }
        }  else if (builder.hasItemReward()) {
            // 清理ItemReward
            builder.clearItemReward();
            fieldCnt++;
        }
        if (this.getLanguage() != Language.forNumber(0)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }  else if (builder.hasLanguage()) {
            // 清理Language
            builder.clearLanguage();
            fieldCnt++;
        }
        if (this.getMailType() != MailType.forNumber(0)) {
            builder.setMailType(this.getMailType());
            fieldCnt++;
        }  else if (builder.hasMailType()) {
            // 清理MailType
            builder.clearMailType();
            fieldCnt++;
        }
        if (this.getOfflineExpiredTime() != 0L) {
            builder.setOfflineExpiredTime(this.getOfflineExpiredTime());
            fieldCnt++;
        }  else if (builder.hasOfflineExpiredTime()) {
            // 清理OfflineExpiredTime
            builder.clearOfflineExpiredTime();
            fieldCnt++;
        }
        if (this.getCanReceive()) {
            builder.setCanReceive(this.getCanReceive());
            fieldCnt++;
        }  else if (builder.hasCanReceive()) {
            // 清理CanReceive
            builder.clearCanReceive();
            fieldCnt++;
        }
        if (this.content != null) {
            StructMail.MailContent.Builder tmpBuilder = StructMail.MailContent.newBuilder();
            final int tmpFieldCnt = this.content.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setContent(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearContent();
            }
        }  else if (builder.hasContent()) {
            // 清理Content
            builder.clearContent();
            fieldCnt++;
        }
        if (this.receiverCard != null) {
            StructMail.PlayerMailReceiverCard.Builder tmpBuilder = StructMail.PlayerMailReceiverCard.newBuilder();
            final int tmpFieldCnt = this.receiverCard.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setReceiverCard(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearReceiverCard();
            }
        }  else if (builder.hasReceiverCard()) {
            // 清理ReceiverCard
            builder.clearReceiverCard();
            fieldCnt++;
        }
        if (this.getIsIdIpMail()) {
            builder.setIsIdIpMail(this.getIsIdIpMail());
            fieldCnt++;
        }  else if (builder.hasIsIdIpMail()) {
            // 清理IsIdIpMail
            builder.clearIsIdIpMail();
            fieldCnt++;
        }
        if (this.getInviteState() != MailInviteState.forNumber(0)) {
            builder.setInviteState(this.getInviteState());
            fieldCnt++;
        }  else if (builder.hasInviteState()) {
            // 清理InviteState
            builder.clearInviteState();
            fieldCnt++;
        }
        if (this.getIsSender()) {
            builder.setIsSender(this.getIsSender());
            fieldCnt++;
        }  else if (builder.hasIsSender()) {
            // 清理IsSender
            builder.clearIsSender();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerMail.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAILID)) {
            builder.setMailId(this.getMailId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAILTEMPLATEID)) {
            builder.setMailTemplateId(this.getMailTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_READSTATE)) {
            builder.setReadState(this.getReadState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SENDER) && this.sender != null) {
            final boolean needClear = !builder.hasSender();
            final int tmpFieldCnt = this.sender.copyChangeToSs(builder.getSenderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSender();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETIMESTAMP)) {
            builder.setCreateTimestamp(this.getCreateTimestamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STORE)) {
            builder.setStore(this.getStore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDSTATE)) {
            builder.setRewardState(this.getRewardState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TITLE) && this.title != null) {
            final boolean needClear = !builder.hasTitle();
            final int tmpFieldCnt = this.title.copyChangeToSs(builder.getTitleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTitle();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            final boolean needClear = !builder.hasItemReward();
            final int tmpFieldCnt = this.itemReward.copyChangeToSs(builder.getItemRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemReward();
            }
        }
        if (this.hasMark(FIELD_INDEX_LANGUAGE)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAILTYPE)) {
            builder.setMailType(this.getMailType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OFFLINEEXPIREDTIME)) {
            builder.setOfflineExpiredTime(this.getOfflineExpiredTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANRECEIVE)) {
            builder.setCanReceive(this.getCanReceive());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTENT) && this.content != null) {
            final boolean needClear = !builder.hasContent();
            final int tmpFieldCnt = this.content.copyChangeToSs(builder.getContentBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContent();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECEIVERCARD) && this.receiverCard != null) {
            final boolean needClear = !builder.hasReceiverCard();
            final int tmpFieldCnt = this.receiverCard.copyChangeToSs(builder.getReceiverCardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearReceiverCard();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISIDIPMAIL)) {
            builder.setIsIdIpMail(this.getIsIdIpMail());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INVITESTATE)) {
            builder.setInviteState(this.getInviteState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISSENDER)) {
            builder.setIsSender(this.getIsSender());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerMail proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMailId()) {
            this.innerSetMailId(proto.getMailId());
        } else {
            this.innerSetMailId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMailTemplateId()) {
            this.innerSetMailTemplateId(proto.getMailTemplateId());
        } else {
            this.innerSetMailTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasReadState()) {
            this.innerSetReadState(proto.getReadState());
        } else {
            this.innerSetReadState(MailReadState.forNumber(0));
        }
        if (proto.hasSender()) {
            this.getSender().mergeFromSs(proto.getSender());
        } else {
            if (this.sender != null) {
                this.sender.mergeFromSs(proto.getSender());
            }
        }
        if (proto.hasCreateTimestamp()) {
            this.innerSetCreateTimestamp(proto.getCreateTimestamp());
        } else {
            this.innerSetCreateTimestamp(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStore()) {
            this.innerSetStore(proto.getStore());
        } else {
            this.innerSetStore(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasRewardState()) {
            this.innerSetRewardState(proto.getRewardState());
        } else {
            this.innerSetRewardState(MailRewardState.forNumber(0));
        }
        if (proto.hasTitle()) {
            this.getTitle().mergeFromSs(proto.getTitle());
        } else {
            if (this.title != null) {
                this.title.mergeFromSs(proto.getTitle());
            }
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeFromSs(proto.getItemReward());
        } else {
            if (this.itemReward != null) {
                this.itemReward.mergeFromSs(proto.getItemReward());
            }
        }
        if (proto.hasLanguage()) {
            this.innerSetLanguage(proto.getLanguage());
        } else {
            this.innerSetLanguage(Language.forNumber(0));
        }
        if (proto.hasMailType()) {
            this.innerSetMailType(proto.getMailType());
        } else {
            this.innerSetMailType(MailType.forNumber(0));
        }
        if (proto.hasOfflineExpiredTime()) {
            this.innerSetOfflineExpiredTime(proto.getOfflineExpiredTime());
        } else {
            this.innerSetOfflineExpiredTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCanReceive()) {
            this.innerSetCanReceive(proto.getCanReceive());
        } else {
            this.innerSetCanReceive(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasContent()) {
            this.getContent().mergeFromSs(proto.getContent());
        } else {
            if (this.content != null) {
                this.content.mergeFromSs(proto.getContent());
            }
        }
        if (proto.hasReceiverCard()) {
            this.getReceiverCard().mergeFromSs(proto.getReceiverCard());
        } else {
            if (this.receiverCard != null) {
                this.receiverCard.mergeFromSs(proto.getReceiverCard());
            }
        }
        if (proto.hasIsIdIpMail()) {
            this.innerSetIsIdIpMail(proto.getIsIdIpMail());
        } else {
            this.innerSetIsIdIpMail(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasInviteState()) {
            this.innerSetInviteState(proto.getInviteState());
        } else {
            this.innerSetInviteState(MailInviteState.forNumber(0));
        }
        if (proto.hasIsSender()) {
            this.innerSetIsSender(proto.getIsSender());
        } else {
            this.innerSetIsSender(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return PlayerMailProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerMail proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMailId()) {
            this.setMailId(proto.getMailId());
            fieldCnt++;
        }
        if (proto.hasMailTemplateId()) {
            this.setMailTemplateId(proto.getMailTemplateId());
            fieldCnt++;
        }
        if (proto.hasReadState()) {
            this.setReadState(proto.getReadState());
            fieldCnt++;
        }
        if (proto.hasSender()) {
            this.getSender().mergeChangeFromSs(proto.getSender());
            fieldCnt++;
        }
        if (proto.hasCreateTimestamp()) {
            this.setCreateTimestamp(proto.getCreateTimestamp());
            fieldCnt++;
        }
        if (proto.hasStore()) {
            this.setStore(proto.getStore());
            fieldCnt++;
        }
        if (proto.hasRewardState()) {
            this.setRewardState(proto.getRewardState());
            fieldCnt++;
        }
        if (proto.hasTitle()) {
            this.getTitle().mergeChangeFromSs(proto.getTitle());
            fieldCnt++;
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeChangeFromSs(proto.getItemReward());
            fieldCnt++;
        }
        if (proto.hasLanguage()) {
            this.setLanguage(proto.getLanguage());
            fieldCnt++;
        }
        if (proto.hasMailType()) {
            this.setMailType(proto.getMailType());
            fieldCnt++;
        }
        if (proto.hasOfflineExpiredTime()) {
            this.setOfflineExpiredTime(proto.getOfflineExpiredTime());
            fieldCnt++;
        }
        if (proto.hasCanReceive()) {
            this.setCanReceive(proto.getCanReceive());
            fieldCnt++;
        }
        if (proto.hasContent()) {
            this.getContent().mergeChangeFromSs(proto.getContent());
            fieldCnt++;
        }
        if (proto.hasReceiverCard()) {
            this.getReceiverCard().mergeChangeFromSs(proto.getReceiverCard());
            fieldCnt++;
        }
        if (proto.hasIsIdIpMail()) {
            this.setIsIdIpMail(proto.getIsIdIpMail());
            fieldCnt++;
        }
        if (proto.hasInviteState()) {
            this.setInviteState(proto.getInviteState());
            fieldCnt++;
        }
        if (proto.hasIsSender()) {
            this.setIsSender(proto.getIsSender());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerMail.Builder builder = PlayerMail.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SENDER) && this.sender != null) {
            this.sender.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TITLE) && this.title != null) {
            this.title.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            this.itemReward.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CONTENT) && this.content != null) {
            this.content.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RECEIVERCARD) && this.receiverCard != null) {
            this.receiverCard.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.sender != null) {
            this.sender.markAll();
        }
        if (this.title != null) {
            this.title.markAll();
        }
        if (this.itemReward != null) {
            this.itemReward.markAll();
        }
        if (this.content != null) {
            this.content.markAll();
        }
        if (this.receiverCard != null) {
            this.receiverCard.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.mailId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerMailProp)) {
            return false;
        }
        final PlayerMailProp otherNode = (PlayerMailProp) node;
        if (this.mailId != otherNode.mailId) {
            return false;
        }
        if (this.mailTemplateId != otherNode.mailTemplateId) {
            return false;
        }
        if (this.readState != otherNode.readState) {
            return false;
        }
        if (!this.getSender().compareDataTo(otherNode.getSender())) {
            return false;
        }
        if (this.createTimestamp != otherNode.createTimestamp) {
            return false;
        }
        if (this.store != otherNode.store) {
            return false;
        }
        if (this.rewardState != otherNode.rewardState) {
            return false;
        }
        if (!this.getTitle().compareDataTo(otherNode.getTitle())) {
            return false;
        }
        if (!this.getItemReward().compareDataTo(otherNode.getItemReward())) {
            return false;
        }
        if (this.language != otherNode.language) {
            return false;
        }
        if (this.mailType != otherNode.mailType) {
            return false;
        }
        if (this.offlineExpiredTime != otherNode.offlineExpiredTime) {
            return false;
        }
        if (this.canReceive != otherNode.canReceive) {
            return false;
        }
        if (!this.getContent().compareDataTo(otherNode.getContent())) {
            return false;
        }
        if (!this.getReceiverCard().compareDataTo(otherNode.getReceiverCard())) {
            return false;
        }
        if (this.isIdIpMail != otherNode.isIdIpMail) {
            return false;
        }
        if (this.inviteState != otherNode.inviteState) {
            return false;
        }
        if (this.isSender != otherNode.isSender) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 46;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
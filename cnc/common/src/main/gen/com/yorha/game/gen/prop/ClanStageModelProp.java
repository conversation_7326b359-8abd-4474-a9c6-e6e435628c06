package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Struct.ClanStageModel;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ClanStageModelPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanStageModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_STAGE = 0;
    public static final int FIELD_INDEX_DISSOLUTIONTSMS = 1;
    public static final int FIELD_INDEX_APPLYOWNERMEMBERINFO = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private ClanStageType stage = ClanStageType.forNumber(0);
    private long dissolutionTsMs = Constant.DEFAULT_LONG_VALUE;
    private Int64ClanApplyOwnerInfoMapProp applyOwnerMemberInfo = null;

    public ClanStageModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanStageModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get stage
     *
     * @return stage value
     */
    public ClanStageType getStage() {
        return this.stage;
    }

    /**
     * set stage && set marked
     *
     * @param stage new value
     * @return current object
     */
    public ClanStageModelProp setStage(ClanStageType stage) {
        if (stage == null) {
            throw new NullPointerException();
        }
        if (this.stage != stage) {
            this.mark(FIELD_INDEX_STAGE);
            this.stage = stage;
        }
        return this;
    }

    /**
     * inner set stage
     *
     * @param stage new value
     */
    private void innerSetStage(ClanStageType stage) {
        this.stage = stage;
    }

    /**
     * get dissolutionTsMs
     *
     * @return dissolutionTsMs value
     */
    public long getDissolutionTsMs() {
        return this.dissolutionTsMs;
    }

    /**
     * set dissolutionTsMs && set marked
     *
     * @param dissolutionTsMs new value
     * @return current object
     */
    public ClanStageModelProp setDissolutionTsMs(long dissolutionTsMs) {
        if (this.dissolutionTsMs != dissolutionTsMs) {
            this.mark(FIELD_INDEX_DISSOLUTIONTSMS);
            this.dissolutionTsMs = dissolutionTsMs;
        }
        return this;
    }

    /**
     * inner set dissolutionTsMs
     *
     * @param dissolutionTsMs new value
     */
    private void innerSetDissolutionTsMs(long dissolutionTsMs) {
        this.dissolutionTsMs = dissolutionTsMs;
    }

    /**
     * get applyOwnerMemberInfo
     *
     * @return applyOwnerMemberInfo value
     */
    public Int64ClanApplyOwnerInfoMapProp getApplyOwnerMemberInfo() {
        if (this.applyOwnerMemberInfo == null) {
            this.applyOwnerMemberInfo = new Int64ClanApplyOwnerInfoMapProp(this, FIELD_INDEX_APPLYOWNERMEMBERINFO);
        }
        return this.applyOwnerMemberInfo;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putApplyOwnerMemberInfoV(ClanApplyOwnerInfoProp v) {
        this.getApplyOwnerMemberInfo().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ClanApplyOwnerInfoProp addEmptyApplyOwnerMemberInfo(Long k) {
        return this.getApplyOwnerMemberInfo().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getApplyOwnerMemberInfoSize() {
        if (this.applyOwnerMemberInfo == null) {
            return 0;
        }
        return this.applyOwnerMemberInfo.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isApplyOwnerMemberInfoEmpty() {
        if (this.applyOwnerMemberInfo == null) {
            return true;
        }
        return this.applyOwnerMemberInfo.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ClanApplyOwnerInfoProp getApplyOwnerMemberInfoV(Long k) {
        if (this.applyOwnerMemberInfo == null || !this.applyOwnerMemberInfo.containsKey(k)) {
            return null;
        }
        return this.applyOwnerMemberInfo.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearApplyOwnerMemberInfo() {
        if (this.applyOwnerMemberInfo != null) {
            this.applyOwnerMemberInfo.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeApplyOwnerMemberInfoV(Long k) {
        if (this.applyOwnerMemberInfo != null) {
            this.applyOwnerMemberInfo.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanStageModelPB.Builder getCopyCsBuilder() {
        final ClanStageModelPB.Builder builder = ClanStageModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanStageModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getStage() != ClanStageType.forNumber(0)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }  else if (builder.hasStage()) {
            // 清理Stage
            builder.clearStage();
            fieldCnt++;
        }
        if (this.getDissolutionTsMs() != 0L) {
            builder.setDissolutionTsMs(this.getDissolutionTsMs());
            fieldCnt++;
        }  else if (builder.hasDissolutionTsMs()) {
            // 清理DissolutionTsMs
            builder.clearDissolutionTsMs();
            fieldCnt++;
        }
        if (this.applyOwnerMemberInfo != null) {
            StructPB.Int64ClanApplyOwnerInfoMapPB.Builder tmpBuilder = StructPB.Int64ClanApplyOwnerInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.applyOwnerMemberInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setApplyOwnerMemberInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearApplyOwnerMemberInfo();
            }
        }  else if (builder.hasApplyOwnerMemberInfo()) {
            // 清理ApplyOwnerMemberInfo
            builder.clearApplyOwnerMemberInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanStageModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STAGE)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DISSOLUTIONTSMS)) {
            builder.setDissolutionTsMs(this.getDissolutionTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_APPLYOWNERMEMBERINFO) && this.applyOwnerMemberInfo != null) {
            final boolean needClear = !builder.hasApplyOwnerMemberInfo();
            final int tmpFieldCnt = this.applyOwnerMemberInfo.copyChangeToCs(builder.getApplyOwnerMemberInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearApplyOwnerMemberInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanStageModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STAGE)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DISSOLUTIONTSMS)) {
            builder.setDissolutionTsMs(this.getDissolutionTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_APPLYOWNERMEMBERINFO) && this.applyOwnerMemberInfo != null) {
            final boolean needClear = !builder.hasApplyOwnerMemberInfo();
            final int tmpFieldCnt = this.applyOwnerMemberInfo.copyChangeToAndClearDeleteKeysCs(builder.getApplyOwnerMemberInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearApplyOwnerMemberInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanStageModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasStage()) {
            this.innerSetStage(proto.getStage());
        } else {
            this.innerSetStage(ClanStageType.forNumber(0));
        }
        if (proto.hasDissolutionTsMs()) {
            this.innerSetDissolutionTsMs(proto.getDissolutionTsMs());
        } else {
            this.innerSetDissolutionTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasApplyOwnerMemberInfo()) {
            this.getApplyOwnerMemberInfo().mergeFromCs(proto.getApplyOwnerMemberInfo());
        } else {
            if (this.applyOwnerMemberInfo != null) {
                this.applyOwnerMemberInfo.mergeFromCs(proto.getApplyOwnerMemberInfo());
            }
        }
        this.markAll();
        return ClanStageModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanStageModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasStage()) {
            this.setStage(proto.getStage());
            fieldCnt++;
        }
        if (proto.hasDissolutionTsMs()) {
            this.setDissolutionTsMs(proto.getDissolutionTsMs());
            fieldCnt++;
        }
        if (proto.hasApplyOwnerMemberInfo()) {
            this.getApplyOwnerMemberInfo().mergeChangeFromCs(proto.getApplyOwnerMemberInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanStageModel.Builder getCopyDbBuilder() {
        final ClanStageModel.Builder builder = ClanStageModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanStageModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getStage() != ClanStageType.forNumber(0)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }  else if (builder.hasStage()) {
            // 清理Stage
            builder.clearStage();
            fieldCnt++;
        }
        if (this.getDissolutionTsMs() != 0L) {
            builder.setDissolutionTsMs(this.getDissolutionTsMs());
            fieldCnt++;
        }  else if (builder.hasDissolutionTsMs()) {
            // 清理DissolutionTsMs
            builder.clearDissolutionTsMs();
            fieldCnt++;
        }
        if (this.applyOwnerMemberInfo != null) {
            Struct.Int64ClanApplyOwnerInfoMap.Builder tmpBuilder = Struct.Int64ClanApplyOwnerInfoMap.newBuilder();
            final int tmpFieldCnt = this.applyOwnerMemberInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setApplyOwnerMemberInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearApplyOwnerMemberInfo();
            }
        }  else if (builder.hasApplyOwnerMemberInfo()) {
            // 清理ApplyOwnerMemberInfo
            builder.clearApplyOwnerMemberInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanStageModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STAGE)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DISSOLUTIONTSMS)) {
            builder.setDissolutionTsMs(this.getDissolutionTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_APPLYOWNERMEMBERINFO) && this.applyOwnerMemberInfo != null) {
            final boolean needClear = !builder.hasApplyOwnerMemberInfo();
            final int tmpFieldCnt = this.applyOwnerMemberInfo.copyChangeToDb(builder.getApplyOwnerMemberInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearApplyOwnerMemberInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanStageModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasStage()) {
            this.innerSetStage(proto.getStage());
        } else {
            this.innerSetStage(ClanStageType.forNumber(0));
        }
        if (proto.hasDissolutionTsMs()) {
            this.innerSetDissolutionTsMs(proto.getDissolutionTsMs());
        } else {
            this.innerSetDissolutionTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasApplyOwnerMemberInfo()) {
            this.getApplyOwnerMemberInfo().mergeFromDb(proto.getApplyOwnerMemberInfo());
        } else {
            if (this.applyOwnerMemberInfo != null) {
                this.applyOwnerMemberInfo.mergeFromDb(proto.getApplyOwnerMemberInfo());
            }
        }
        this.markAll();
        return ClanStageModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanStageModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasStage()) {
            this.setStage(proto.getStage());
            fieldCnt++;
        }
        if (proto.hasDissolutionTsMs()) {
            this.setDissolutionTsMs(proto.getDissolutionTsMs());
            fieldCnt++;
        }
        if (proto.hasApplyOwnerMemberInfo()) {
            this.getApplyOwnerMemberInfo().mergeChangeFromDb(proto.getApplyOwnerMemberInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanStageModel.Builder getCopySsBuilder() {
        final ClanStageModel.Builder builder = ClanStageModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanStageModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getStage() != ClanStageType.forNumber(0)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }  else if (builder.hasStage()) {
            // 清理Stage
            builder.clearStage();
            fieldCnt++;
        }
        if (this.getDissolutionTsMs() != 0L) {
            builder.setDissolutionTsMs(this.getDissolutionTsMs());
            fieldCnt++;
        }  else if (builder.hasDissolutionTsMs()) {
            // 清理DissolutionTsMs
            builder.clearDissolutionTsMs();
            fieldCnt++;
        }
        if (this.applyOwnerMemberInfo != null) {
            Struct.Int64ClanApplyOwnerInfoMap.Builder tmpBuilder = Struct.Int64ClanApplyOwnerInfoMap.newBuilder();
            final int tmpFieldCnt = this.applyOwnerMemberInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setApplyOwnerMemberInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearApplyOwnerMemberInfo();
            }
        }  else if (builder.hasApplyOwnerMemberInfo()) {
            // 清理ApplyOwnerMemberInfo
            builder.clearApplyOwnerMemberInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanStageModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STAGE)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DISSOLUTIONTSMS)) {
            builder.setDissolutionTsMs(this.getDissolutionTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_APPLYOWNERMEMBERINFO) && this.applyOwnerMemberInfo != null) {
            final boolean needClear = !builder.hasApplyOwnerMemberInfo();
            final int tmpFieldCnt = this.applyOwnerMemberInfo.copyChangeToSs(builder.getApplyOwnerMemberInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearApplyOwnerMemberInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanStageModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasStage()) {
            this.innerSetStage(proto.getStage());
        } else {
            this.innerSetStage(ClanStageType.forNumber(0));
        }
        if (proto.hasDissolutionTsMs()) {
            this.innerSetDissolutionTsMs(proto.getDissolutionTsMs());
        } else {
            this.innerSetDissolutionTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasApplyOwnerMemberInfo()) {
            this.getApplyOwnerMemberInfo().mergeFromSs(proto.getApplyOwnerMemberInfo());
        } else {
            if (this.applyOwnerMemberInfo != null) {
                this.applyOwnerMemberInfo.mergeFromSs(proto.getApplyOwnerMemberInfo());
            }
        }
        this.markAll();
        return ClanStageModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanStageModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasStage()) {
            this.setStage(proto.getStage());
            fieldCnt++;
        }
        if (proto.hasDissolutionTsMs()) {
            this.setDissolutionTsMs(proto.getDissolutionTsMs());
            fieldCnt++;
        }
        if (proto.hasApplyOwnerMemberInfo()) {
            this.getApplyOwnerMemberInfo().mergeChangeFromSs(proto.getApplyOwnerMemberInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanStageModel.Builder builder = ClanStageModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_APPLYOWNERMEMBERINFO) && this.applyOwnerMemberInfo != null) {
            this.applyOwnerMemberInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.applyOwnerMemberInfo != null) {
            this.applyOwnerMemberInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanStageModelProp)) {
            return false;
        }
        final ClanStageModelProp otherNode = (ClanStageModelProp) node;
        if (this.stage != otherNode.stage) {
            return false;
        }
        if (this.dissolutionTsMs != otherNode.dissolutionTsMs) {
            return false;
        }
        if (!this.getApplyOwnerMemberInfo().compareDataTo(otherNode.getApplyOwnerMemberInfo())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructBattle.BattleRecordAll;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructBattlePB.BattleRecordAllPB;
import com.yorha.proto.StructBattlePB;


/**
 * <AUTHOR> auto gen
 */
public class BattleRecordAllProp extends AbstractPropNode {

    public static final int FIELD_INDEX_RECORDID = 0;
    public static final int FIELD_INDEX_STARTMILLIS = 1;
    public static final int FIELD_INDEX_ENDMILLIS = 2;
    public static final int FIELD_INDEX_SINGLERECORDLIST = 3;
    public static final int FIELD_INDEX_SELFSUMMARY = 4;
    public static final int FIELD_INDEX_ROUNDALIVELIST = 5;
    public static final int FIELD_INDEX_ROUNDEVENTLIST = 6;

    public static final int FIELD_COUNT = 7;

    private long markBits0 = 0L;

    private long recordId = Constant.DEFAULT_LONG_VALUE;
    private long startMillis = Constant.DEFAULT_LONG_VALUE;
    private long endMillis = Constant.DEFAULT_LONG_VALUE;
    private BattleRecordOneListProp singleRecordList = null;
    private BattleRecordRoleSummaryProp selfSummary = null;
    private BattleRecordRoundAliveListProp roundAliveList = null;
    private BattleRecordRoundEventListProp roundEventList = null;

    public BattleRecordAllProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BattleRecordAllProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get recordId
     *
     * @return recordId value
     */
    public long getRecordId() {
        return this.recordId;
    }

    /**
     * set recordId && set marked
     *
     * @param recordId new value
     * @return current object
     */
    public BattleRecordAllProp setRecordId(long recordId) {
        if (this.recordId != recordId) {
            this.mark(FIELD_INDEX_RECORDID);
            this.recordId = recordId;
        }
        return this;
    }

    /**
     * inner set recordId
     *
     * @param recordId new value
     */
    private void innerSetRecordId(long recordId) {
        this.recordId = recordId;
    }

    /**
     * get startMillis
     *
     * @return startMillis value
     */
    public long getStartMillis() {
        return this.startMillis;
    }

    /**
     * set startMillis && set marked
     *
     * @param startMillis new value
     * @return current object
     */
    public BattleRecordAllProp setStartMillis(long startMillis) {
        if (this.startMillis != startMillis) {
            this.mark(FIELD_INDEX_STARTMILLIS);
            this.startMillis = startMillis;
        }
        return this;
    }

    /**
     * inner set startMillis
     *
     * @param startMillis new value
     */
    private void innerSetStartMillis(long startMillis) {
        this.startMillis = startMillis;
    }

    /**
     * get endMillis
     *
     * @return endMillis value
     */
    public long getEndMillis() {
        return this.endMillis;
    }

    /**
     * set endMillis && set marked
     *
     * @param endMillis new value
     * @return current object
     */
    public BattleRecordAllProp setEndMillis(long endMillis) {
        if (this.endMillis != endMillis) {
            this.mark(FIELD_INDEX_ENDMILLIS);
            this.endMillis = endMillis;
        }
        return this;
    }

    /**
     * inner set endMillis
     *
     * @param endMillis new value
     */
    private void innerSetEndMillis(long endMillis) {
        this.endMillis = endMillis;
    }

    /**
     * get singleRecordList
     *
     * @return singleRecordList value
     */
    public BattleRecordOneListProp getSingleRecordList() {
        if (this.singleRecordList == null) {
            this.singleRecordList = new BattleRecordOneListProp(this, FIELD_INDEX_SINGLERECORDLIST);
        }
        return this.singleRecordList;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addSingleRecordList(BattleRecordOneProp v) {
        this.getSingleRecordList().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public BattleRecordOneProp getSingleRecordListIndex(int index) {
        return this.getSingleRecordList().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public BattleRecordOneProp removeSingleRecordList(BattleRecordOneProp v) {
        if (this.singleRecordList == null) {
            return null;
        }
        if(this.singleRecordList.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getSingleRecordListSize() {
        if (this.singleRecordList == null) {
            return 0;
        }
        return this.singleRecordList.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isSingleRecordListEmpty() {
        if (this.singleRecordList == null) {
            return true;
        }
        return this.getSingleRecordList().isEmpty();
    }

    /**
     * clear list
     */
    public void clearSingleRecordList() {
        this.getSingleRecordList().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public BattleRecordOneProp removeSingleRecordListIndex(int index) {
        return this.getSingleRecordList().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public BattleRecordOneProp setSingleRecordListIndex(int index, BattleRecordOneProp v) {
        return this.getSingleRecordList().set(index, v);
    }
    /**
     * get selfSummary
     *
     * @return selfSummary value
     */
    public BattleRecordRoleSummaryProp getSelfSummary() {
        if (this.selfSummary == null) {
            this.selfSummary = new BattleRecordRoleSummaryProp(this, FIELD_INDEX_SELFSUMMARY);
        }
        return this.selfSummary;
    }

    /**
     * get roundAliveList
     *
     * @return roundAliveList value
     */
    public BattleRecordRoundAliveListProp getRoundAliveList() {
        if (this.roundAliveList == null) {
            this.roundAliveList = new BattleRecordRoundAliveListProp(this, FIELD_INDEX_ROUNDALIVELIST);
        }
        return this.roundAliveList;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addRoundAliveList(BattleRecordRoundAliveProp v) {
        this.getRoundAliveList().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public BattleRecordRoundAliveProp getRoundAliveListIndex(int index) {
        return this.getRoundAliveList().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public BattleRecordRoundAliveProp removeRoundAliveList(BattleRecordRoundAliveProp v) {
        if (this.roundAliveList == null) {
            return null;
        }
        if(this.roundAliveList.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getRoundAliveListSize() {
        if (this.roundAliveList == null) {
            return 0;
        }
        return this.roundAliveList.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isRoundAliveListEmpty() {
        if (this.roundAliveList == null) {
            return true;
        }
        return this.getRoundAliveList().isEmpty();
    }

    /**
     * clear list
     */
    public void clearRoundAliveList() {
        this.getRoundAliveList().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public BattleRecordRoundAliveProp removeRoundAliveListIndex(int index) {
        return this.getRoundAliveList().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public BattleRecordRoundAliveProp setRoundAliveListIndex(int index, BattleRecordRoundAliveProp v) {
        return this.getRoundAliveList().set(index, v);
    }
    /**
     * get roundEventList
     *
     * @return roundEventList value
     */
    public BattleRecordRoundEventListProp getRoundEventList() {
        if (this.roundEventList == null) {
            this.roundEventList = new BattleRecordRoundEventListProp(this, FIELD_INDEX_ROUNDEVENTLIST);
        }
        return this.roundEventList;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addRoundEventList(BattleRecordRoundEventProp v) {
        this.getRoundEventList().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public BattleRecordRoundEventProp getRoundEventListIndex(int index) {
        return this.getRoundEventList().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public BattleRecordRoundEventProp removeRoundEventList(BattleRecordRoundEventProp v) {
        if (this.roundEventList == null) {
            return null;
        }
        if(this.roundEventList.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getRoundEventListSize() {
        if (this.roundEventList == null) {
            return 0;
        }
        return this.roundEventList.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isRoundEventListEmpty() {
        if (this.roundEventList == null) {
            return true;
        }
        return this.getRoundEventList().isEmpty();
    }

    /**
     * clear list
     */
    public void clearRoundEventList() {
        this.getRoundEventList().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public BattleRecordRoundEventProp removeRoundEventListIndex(int index) {
        return this.getRoundEventList().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public BattleRecordRoundEventProp setRoundEventListIndex(int index, BattleRecordRoundEventProp v) {
        return this.getRoundEventList().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordAllPB.Builder getCopyCsBuilder() {
        final BattleRecordAllPB.Builder builder = BattleRecordAllPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BattleRecordAllPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRecordId() != 0L) {
            builder.setRecordId(this.getRecordId());
            fieldCnt++;
        }  else if (builder.hasRecordId()) {
            // 清理RecordId
            builder.clearRecordId();
            fieldCnt++;
        }
        if (this.getStartMillis() != 0L) {
            builder.setStartMillis(this.getStartMillis());
            fieldCnt++;
        }  else if (builder.hasStartMillis()) {
            // 清理StartMillis
            builder.clearStartMillis();
            fieldCnt++;
        }
        if (this.getEndMillis() != 0L) {
            builder.setEndMillis(this.getEndMillis());
            fieldCnt++;
        }  else if (builder.hasEndMillis()) {
            // 清理EndMillis
            builder.clearEndMillis();
            fieldCnt++;
        }
        if (this.singleRecordList != null) {
            StructBattlePB.BattleRecordOneListPB.Builder tmpBuilder = StructBattlePB.BattleRecordOneListPB.newBuilder();
            final int tmpFieldCnt = this.singleRecordList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSingleRecordList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSingleRecordList();
            }
        }  else if (builder.hasSingleRecordList()) {
            // 清理SingleRecordList
            builder.clearSingleRecordList();
            fieldCnt++;
        }
        if (this.selfSummary != null) {
            StructBattlePB.BattleRecordRoleSummaryPB.Builder tmpBuilder = StructBattlePB.BattleRecordRoleSummaryPB.newBuilder();
            final int tmpFieldCnt = this.selfSummary.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSelfSummary(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSelfSummary();
            }
        }  else if (builder.hasSelfSummary()) {
            // 清理SelfSummary
            builder.clearSelfSummary();
            fieldCnt++;
        }
        if (this.roundAliveList != null) {
            StructBattlePB.BattleRecordRoundAliveListPB.Builder tmpBuilder = StructBattlePB.BattleRecordRoundAliveListPB.newBuilder();
            final int tmpFieldCnt = this.roundAliveList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRoundAliveList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRoundAliveList();
            }
        }  else if (builder.hasRoundAliveList()) {
            // 清理RoundAliveList
            builder.clearRoundAliveList();
            fieldCnt++;
        }
        if (this.roundEventList != null) {
            StructBattlePB.BattleRecordRoundEventListPB.Builder tmpBuilder = StructBattlePB.BattleRecordRoundEventListPB.newBuilder();
            final int tmpFieldCnt = this.roundEventList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRoundEventList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRoundEventList();
            }
        }  else if (builder.hasRoundEventList()) {
            // 清理RoundEventList
            builder.clearRoundEventList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BattleRecordAllPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECORDID)) {
            builder.setRecordId(this.getRecordId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTMILLIS)) {
            builder.setStartMillis(this.getStartMillis());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDMILLIS)) {
            builder.setEndMillis(this.getEndMillis());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SINGLERECORDLIST) && this.singleRecordList != null) {
            final boolean needClear = !builder.hasSingleRecordList();
            final int tmpFieldCnt = this.singleRecordList.copyChangeToCs(builder.getSingleRecordListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSingleRecordList();
            }
        }
        if (this.hasMark(FIELD_INDEX_SELFSUMMARY) && this.selfSummary != null) {
            final boolean needClear = !builder.hasSelfSummary();
            final int tmpFieldCnt = this.selfSummary.copyChangeToCs(builder.getSelfSummaryBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSelfSummary();
            }
        }
        if (this.hasMark(FIELD_INDEX_ROUNDALIVELIST) && this.roundAliveList != null) {
            final boolean needClear = !builder.hasRoundAliveList();
            final int tmpFieldCnt = this.roundAliveList.copyChangeToCs(builder.getRoundAliveListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRoundAliveList();
            }
        }
        if (this.hasMark(FIELD_INDEX_ROUNDEVENTLIST) && this.roundEventList != null) {
            final boolean needClear = !builder.hasRoundEventList();
            final int tmpFieldCnt = this.roundEventList.copyChangeToCs(builder.getRoundEventListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRoundEventList();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BattleRecordAllPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECORDID)) {
            builder.setRecordId(this.getRecordId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTMILLIS)) {
            builder.setStartMillis(this.getStartMillis());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDMILLIS)) {
            builder.setEndMillis(this.getEndMillis());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SINGLERECORDLIST) && this.singleRecordList != null) {
            final boolean needClear = !builder.hasSingleRecordList();
            final int tmpFieldCnt = this.singleRecordList.copyChangeToCs(builder.getSingleRecordListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSingleRecordList();
            }
        }
        if (this.hasMark(FIELD_INDEX_SELFSUMMARY) && this.selfSummary != null) {
            final boolean needClear = !builder.hasSelfSummary();
            final int tmpFieldCnt = this.selfSummary.copyChangeToAndClearDeleteKeysCs(builder.getSelfSummaryBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSelfSummary();
            }
        }
        if (this.hasMark(FIELD_INDEX_ROUNDALIVELIST) && this.roundAliveList != null) {
            final boolean needClear = !builder.hasRoundAliveList();
            final int tmpFieldCnt = this.roundAliveList.copyChangeToCs(builder.getRoundAliveListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRoundAliveList();
            }
        }
        if (this.hasMark(FIELD_INDEX_ROUNDEVENTLIST) && this.roundEventList != null) {
            final boolean needClear = !builder.hasRoundEventList();
            final int tmpFieldCnt = this.roundEventList.copyChangeToCs(builder.getRoundEventListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRoundEventList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BattleRecordAllPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRecordId()) {
            this.innerSetRecordId(proto.getRecordId());
        } else {
            this.innerSetRecordId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStartMillis()) {
            this.innerSetStartMillis(proto.getStartMillis());
        } else {
            this.innerSetStartMillis(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndMillis()) {
            this.innerSetEndMillis(proto.getEndMillis());
        } else {
            this.innerSetEndMillis(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSingleRecordList()) {
            this.getSingleRecordList().mergeFromCs(proto.getSingleRecordList());
        } else {
            if (this.singleRecordList != null) {
                this.singleRecordList.mergeFromCs(proto.getSingleRecordList());
            }
        }
        if (proto.hasSelfSummary()) {
            this.getSelfSummary().mergeFromCs(proto.getSelfSummary());
        } else {
            if (this.selfSummary != null) {
                this.selfSummary.mergeFromCs(proto.getSelfSummary());
            }
        }
        if (proto.hasRoundAliveList()) {
            this.getRoundAliveList().mergeFromCs(proto.getRoundAliveList());
        } else {
            if (this.roundAliveList != null) {
                this.roundAliveList.mergeFromCs(proto.getRoundAliveList());
            }
        }
        if (proto.hasRoundEventList()) {
            this.getRoundEventList().mergeFromCs(proto.getRoundEventList());
        } else {
            if (this.roundEventList != null) {
                this.roundEventList.mergeFromCs(proto.getRoundEventList());
            }
        }
        this.markAll();
        return BattleRecordAllProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BattleRecordAllPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRecordId()) {
            this.setRecordId(proto.getRecordId());
            fieldCnt++;
        }
        if (proto.hasStartMillis()) {
            this.setStartMillis(proto.getStartMillis());
            fieldCnt++;
        }
        if (proto.hasEndMillis()) {
            this.setEndMillis(proto.getEndMillis());
            fieldCnt++;
        }
        if (proto.hasSingleRecordList()) {
            this.getSingleRecordList().mergeChangeFromCs(proto.getSingleRecordList());
            fieldCnt++;
        }
        if (proto.hasSelfSummary()) {
            this.getSelfSummary().mergeChangeFromCs(proto.getSelfSummary());
            fieldCnt++;
        }
        if (proto.hasRoundAliveList()) {
            this.getRoundAliveList().mergeChangeFromCs(proto.getRoundAliveList());
            fieldCnt++;
        }
        if (proto.hasRoundEventList()) {
            this.getRoundEventList().mergeChangeFromCs(proto.getRoundEventList());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordAll.Builder getCopyDbBuilder() {
        final BattleRecordAll.Builder builder = BattleRecordAll.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(BattleRecordAll.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRecordId() != 0L) {
            builder.setRecordId(this.getRecordId());
            fieldCnt++;
        }  else if (builder.hasRecordId()) {
            // 清理RecordId
            builder.clearRecordId();
            fieldCnt++;
        }
        if (this.getStartMillis() != 0L) {
            builder.setStartMillis(this.getStartMillis());
            fieldCnt++;
        }  else if (builder.hasStartMillis()) {
            // 清理StartMillis
            builder.clearStartMillis();
            fieldCnt++;
        }
        if (this.getEndMillis() != 0L) {
            builder.setEndMillis(this.getEndMillis());
            fieldCnt++;
        }  else if (builder.hasEndMillis()) {
            // 清理EndMillis
            builder.clearEndMillis();
            fieldCnt++;
        }
        if (this.singleRecordList != null) {
            StructBattle.BattleRecordOneList.Builder tmpBuilder = StructBattle.BattleRecordOneList.newBuilder();
            final int tmpFieldCnt = this.singleRecordList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSingleRecordList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSingleRecordList();
            }
        }  else if (builder.hasSingleRecordList()) {
            // 清理SingleRecordList
            builder.clearSingleRecordList();
            fieldCnt++;
        }
        if (this.selfSummary != null) {
            StructBattle.BattleRecordRoleSummary.Builder tmpBuilder = StructBattle.BattleRecordRoleSummary.newBuilder();
            final int tmpFieldCnt = this.selfSummary.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSelfSummary(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSelfSummary();
            }
        }  else if (builder.hasSelfSummary()) {
            // 清理SelfSummary
            builder.clearSelfSummary();
            fieldCnt++;
        }
        if (this.roundAliveList != null) {
            StructBattle.BattleRecordRoundAliveList.Builder tmpBuilder = StructBattle.BattleRecordRoundAliveList.newBuilder();
            final int tmpFieldCnt = this.roundAliveList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRoundAliveList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRoundAliveList();
            }
        }  else if (builder.hasRoundAliveList()) {
            // 清理RoundAliveList
            builder.clearRoundAliveList();
            fieldCnt++;
        }
        if (this.roundEventList != null) {
            StructBattle.BattleRecordRoundEventList.Builder tmpBuilder = StructBattle.BattleRecordRoundEventList.newBuilder();
            final int tmpFieldCnt = this.roundEventList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRoundEventList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRoundEventList();
            }
        }  else if (builder.hasRoundEventList()) {
            // 清理RoundEventList
            builder.clearRoundEventList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(BattleRecordAll.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECORDID)) {
            builder.setRecordId(this.getRecordId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTMILLIS)) {
            builder.setStartMillis(this.getStartMillis());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDMILLIS)) {
            builder.setEndMillis(this.getEndMillis());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SINGLERECORDLIST) && this.singleRecordList != null) {
            final boolean needClear = !builder.hasSingleRecordList();
            final int tmpFieldCnt = this.singleRecordList.copyChangeToDb(builder.getSingleRecordListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSingleRecordList();
            }
        }
        if (this.hasMark(FIELD_INDEX_SELFSUMMARY) && this.selfSummary != null) {
            final boolean needClear = !builder.hasSelfSummary();
            final int tmpFieldCnt = this.selfSummary.copyChangeToDb(builder.getSelfSummaryBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSelfSummary();
            }
        }
        if (this.hasMark(FIELD_INDEX_ROUNDALIVELIST) && this.roundAliveList != null) {
            final boolean needClear = !builder.hasRoundAliveList();
            final int tmpFieldCnt = this.roundAliveList.copyChangeToDb(builder.getRoundAliveListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRoundAliveList();
            }
        }
        if (this.hasMark(FIELD_INDEX_ROUNDEVENTLIST) && this.roundEventList != null) {
            final boolean needClear = !builder.hasRoundEventList();
            final int tmpFieldCnt = this.roundEventList.copyChangeToDb(builder.getRoundEventListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRoundEventList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(BattleRecordAll proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRecordId()) {
            this.innerSetRecordId(proto.getRecordId());
        } else {
            this.innerSetRecordId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStartMillis()) {
            this.innerSetStartMillis(proto.getStartMillis());
        } else {
            this.innerSetStartMillis(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndMillis()) {
            this.innerSetEndMillis(proto.getEndMillis());
        } else {
            this.innerSetEndMillis(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSingleRecordList()) {
            this.getSingleRecordList().mergeFromDb(proto.getSingleRecordList());
        } else {
            if (this.singleRecordList != null) {
                this.singleRecordList.mergeFromDb(proto.getSingleRecordList());
            }
        }
        if (proto.hasSelfSummary()) {
            this.getSelfSummary().mergeFromDb(proto.getSelfSummary());
        } else {
            if (this.selfSummary != null) {
                this.selfSummary.mergeFromDb(proto.getSelfSummary());
            }
        }
        if (proto.hasRoundAliveList()) {
            this.getRoundAliveList().mergeFromDb(proto.getRoundAliveList());
        } else {
            if (this.roundAliveList != null) {
                this.roundAliveList.mergeFromDb(proto.getRoundAliveList());
            }
        }
        if (proto.hasRoundEventList()) {
            this.getRoundEventList().mergeFromDb(proto.getRoundEventList());
        } else {
            if (this.roundEventList != null) {
                this.roundEventList.mergeFromDb(proto.getRoundEventList());
            }
        }
        this.markAll();
        return BattleRecordAllProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(BattleRecordAll proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRecordId()) {
            this.setRecordId(proto.getRecordId());
            fieldCnt++;
        }
        if (proto.hasStartMillis()) {
            this.setStartMillis(proto.getStartMillis());
            fieldCnt++;
        }
        if (proto.hasEndMillis()) {
            this.setEndMillis(proto.getEndMillis());
            fieldCnt++;
        }
        if (proto.hasSingleRecordList()) {
            this.getSingleRecordList().mergeChangeFromDb(proto.getSingleRecordList());
            fieldCnt++;
        }
        if (proto.hasSelfSummary()) {
            this.getSelfSummary().mergeChangeFromDb(proto.getSelfSummary());
            fieldCnt++;
        }
        if (proto.hasRoundAliveList()) {
            this.getRoundAliveList().mergeChangeFromDb(proto.getRoundAliveList());
            fieldCnt++;
        }
        if (proto.hasRoundEventList()) {
            this.getRoundEventList().mergeChangeFromDb(proto.getRoundEventList());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordAll.Builder getCopySsBuilder() {
        final BattleRecordAll.Builder builder = BattleRecordAll.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(BattleRecordAll.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRecordId() != 0L) {
            builder.setRecordId(this.getRecordId());
            fieldCnt++;
        }  else if (builder.hasRecordId()) {
            // 清理RecordId
            builder.clearRecordId();
            fieldCnt++;
        }
        if (this.getStartMillis() != 0L) {
            builder.setStartMillis(this.getStartMillis());
            fieldCnt++;
        }  else if (builder.hasStartMillis()) {
            // 清理StartMillis
            builder.clearStartMillis();
            fieldCnt++;
        }
        if (this.getEndMillis() != 0L) {
            builder.setEndMillis(this.getEndMillis());
            fieldCnt++;
        }  else if (builder.hasEndMillis()) {
            // 清理EndMillis
            builder.clearEndMillis();
            fieldCnt++;
        }
        if (this.singleRecordList != null) {
            StructBattle.BattleRecordOneList.Builder tmpBuilder = StructBattle.BattleRecordOneList.newBuilder();
            final int tmpFieldCnt = this.singleRecordList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSingleRecordList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSingleRecordList();
            }
        }  else if (builder.hasSingleRecordList()) {
            // 清理SingleRecordList
            builder.clearSingleRecordList();
            fieldCnt++;
        }
        if (this.selfSummary != null) {
            StructBattle.BattleRecordRoleSummary.Builder tmpBuilder = StructBattle.BattleRecordRoleSummary.newBuilder();
            final int tmpFieldCnt = this.selfSummary.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSelfSummary(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSelfSummary();
            }
        }  else if (builder.hasSelfSummary()) {
            // 清理SelfSummary
            builder.clearSelfSummary();
            fieldCnt++;
        }
        if (this.roundAliveList != null) {
            StructBattle.BattleRecordRoundAliveList.Builder tmpBuilder = StructBattle.BattleRecordRoundAliveList.newBuilder();
            final int tmpFieldCnt = this.roundAliveList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRoundAliveList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRoundAliveList();
            }
        }  else if (builder.hasRoundAliveList()) {
            // 清理RoundAliveList
            builder.clearRoundAliveList();
            fieldCnt++;
        }
        if (this.roundEventList != null) {
            StructBattle.BattleRecordRoundEventList.Builder tmpBuilder = StructBattle.BattleRecordRoundEventList.newBuilder();
            final int tmpFieldCnt = this.roundEventList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRoundEventList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRoundEventList();
            }
        }  else if (builder.hasRoundEventList()) {
            // 清理RoundEventList
            builder.clearRoundEventList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(BattleRecordAll.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECORDID)) {
            builder.setRecordId(this.getRecordId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTMILLIS)) {
            builder.setStartMillis(this.getStartMillis());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDMILLIS)) {
            builder.setEndMillis(this.getEndMillis());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SINGLERECORDLIST) && this.singleRecordList != null) {
            final boolean needClear = !builder.hasSingleRecordList();
            final int tmpFieldCnt = this.singleRecordList.copyChangeToSs(builder.getSingleRecordListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSingleRecordList();
            }
        }
        if (this.hasMark(FIELD_INDEX_SELFSUMMARY) && this.selfSummary != null) {
            final boolean needClear = !builder.hasSelfSummary();
            final int tmpFieldCnt = this.selfSummary.copyChangeToSs(builder.getSelfSummaryBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSelfSummary();
            }
        }
        if (this.hasMark(FIELD_INDEX_ROUNDALIVELIST) && this.roundAliveList != null) {
            final boolean needClear = !builder.hasRoundAliveList();
            final int tmpFieldCnt = this.roundAliveList.copyChangeToSs(builder.getRoundAliveListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRoundAliveList();
            }
        }
        if (this.hasMark(FIELD_INDEX_ROUNDEVENTLIST) && this.roundEventList != null) {
            final boolean needClear = !builder.hasRoundEventList();
            final int tmpFieldCnt = this.roundEventList.copyChangeToSs(builder.getRoundEventListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRoundEventList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(BattleRecordAll proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRecordId()) {
            this.innerSetRecordId(proto.getRecordId());
        } else {
            this.innerSetRecordId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStartMillis()) {
            this.innerSetStartMillis(proto.getStartMillis());
        } else {
            this.innerSetStartMillis(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndMillis()) {
            this.innerSetEndMillis(proto.getEndMillis());
        } else {
            this.innerSetEndMillis(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSingleRecordList()) {
            this.getSingleRecordList().mergeFromSs(proto.getSingleRecordList());
        } else {
            if (this.singleRecordList != null) {
                this.singleRecordList.mergeFromSs(proto.getSingleRecordList());
            }
        }
        if (proto.hasSelfSummary()) {
            this.getSelfSummary().mergeFromSs(proto.getSelfSummary());
        } else {
            if (this.selfSummary != null) {
                this.selfSummary.mergeFromSs(proto.getSelfSummary());
            }
        }
        if (proto.hasRoundAliveList()) {
            this.getRoundAliveList().mergeFromSs(proto.getRoundAliveList());
        } else {
            if (this.roundAliveList != null) {
                this.roundAliveList.mergeFromSs(proto.getRoundAliveList());
            }
        }
        if (proto.hasRoundEventList()) {
            this.getRoundEventList().mergeFromSs(proto.getRoundEventList());
        } else {
            if (this.roundEventList != null) {
                this.roundEventList.mergeFromSs(proto.getRoundEventList());
            }
        }
        this.markAll();
        return BattleRecordAllProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(BattleRecordAll proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRecordId()) {
            this.setRecordId(proto.getRecordId());
            fieldCnt++;
        }
        if (proto.hasStartMillis()) {
            this.setStartMillis(proto.getStartMillis());
            fieldCnt++;
        }
        if (proto.hasEndMillis()) {
            this.setEndMillis(proto.getEndMillis());
            fieldCnt++;
        }
        if (proto.hasSingleRecordList()) {
            this.getSingleRecordList().mergeChangeFromSs(proto.getSingleRecordList());
            fieldCnt++;
        }
        if (proto.hasSelfSummary()) {
            this.getSelfSummary().mergeChangeFromSs(proto.getSelfSummary());
            fieldCnt++;
        }
        if (proto.hasRoundAliveList()) {
            this.getRoundAliveList().mergeChangeFromSs(proto.getRoundAliveList());
            fieldCnt++;
        }
        if (proto.hasRoundEventList()) {
            this.getRoundEventList().mergeChangeFromSs(proto.getRoundEventList());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        BattleRecordAll.Builder builder = BattleRecordAll.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SINGLERECORDLIST) && this.singleRecordList != null) {
            this.singleRecordList.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SELFSUMMARY) && this.selfSummary != null) {
            this.selfSummary.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ROUNDALIVELIST) && this.roundAliveList != null) {
            this.roundAliveList.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ROUNDEVENTLIST) && this.roundEventList != null) {
            this.roundEventList.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.singleRecordList != null) {
            this.singleRecordList.markAll();
        }
        if (this.selfSummary != null) {
            this.selfSummary.markAll();
        }
        if (this.roundAliveList != null) {
            this.roundAliveList.markAll();
        }
        if (this.roundEventList != null) {
            this.roundEventList.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BattleRecordAllProp)) {
            return false;
        }
        final BattleRecordAllProp otherNode = (BattleRecordAllProp) node;
        if (this.recordId != otherNode.recordId) {
            return false;
        }
        if (this.startMillis != otherNode.startMillis) {
            return false;
        }
        if (this.endMillis != otherNode.endMillis) {
            return false;
        }
        if (!this.getSingleRecordList().compareDataTo(otherNode.getSingleRecordList())) {
            return false;
        }
        if (!this.getSelfSummary().compareDataTo(otherNode.getSelfSummary())) {
            return false;
        }
        if (!this.getRoundAliveList().compareDataTo(otherNode.getRoundAliveList())) {
            return false;
        }
        if (!this.getRoundEventList().compareDataTo(otherNode.getRoundEventList())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 57;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.PlayerContacts;
import com.yorha.proto.Basic;
import com.yorha.proto.StructPB.PlayerContactsPB;
import com.yorha.proto.BasicPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerContactsProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_PLAYERID = 0;
    public static final int FIELD_INDEX_CACHEDZONEID = 1;
    public static final int FIELD_INDEX_CACHEREASON = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private long playerId = Constant.DEFAULT_LONG_VALUE;
    private int cachedZoneId = Constant.DEFAULT_INT_VALUE;
    private StringSetProp cacheReason = null;

    public PlayerContactsProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerContactsProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get playerId
     *
     * @return playerId value
     */
    public long getPlayerId() {
        return this.playerId;
    }

    /**
     * set playerId && set marked
     *
     * @param playerId new value
     * @return current object
     */
    public PlayerContactsProp setPlayerId(long playerId) {
        if (this.playerId != playerId) {
            this.mark(FIELD_INDEX_PLAYERID);
            this.playerId = playerId;
        }
        return this;
    }

    /**
     * inner set playerId
     *
     * @param playerId new value
     */
    private void innerSetPlayerId(long playerId) {
        this.playerId = playerId;
    }

    /**
     * get cachedZoneId
     *
     * @return cachedZoneId value
     */
    public int getCachedZoneId() {
        return this.cachedZoneId;
    }

    /**
     * set cachedZoneId && set marked
     *
     * @param cachedZoneId new value
     * @return current object
     */
    public PlayerContactsProp setCachedZoneId(int cachedZoneId) {
        if (this.cachedZoneId != cachedZoneId) {
            this.mark(FIELD_INDEX_CACHEDZONEID);
            this.cachedZoneId = cachedZoneId;
        }
        return this;
    }

    /**
     * inner set cachedZoneId
     *
     * @param cachedZoneId new value
     */
    private void innerSetCachedZoneId(int cachedZoneId) {
        this.cachedZoneId = cachedZoneId;
    }

    /**
     * get cacheReason
     *
     * @return cacheReason value
     */
    public StringSetProp getCacheReason() {
        if (this.cacheReason == null) {
            this.cacheReason = new StringSetProp(this, FIELD_INDEX_CACHEREASON);
        }
        return this.cacheReason;
    }


    /**
     * add value to set
     *
     * @param e value
     */
    public void addCacheReason(String e) {
        this.getCacheReason().add(e);
    }

    /**
     * remove the specified element in set
     *
     * @param e element
     * @return e if success or null by fail
     */
    public String removeCacheReason(String e) {
        if (this.cacheReason == null) {
            return null;
        }
        if(this.cacheReason.remove(e)) {
            return e;
        }
        return null;
    }

    /**
     * get set size
     *
     * @return set size
     */
    public int getCacheReasonSize() {
        if (this.cacheReason == null) {
            return 0;
        }
        return this.cacheReason.size();
    }

    /**
     * get is a empty set
     *
     * @return true if empty
     */
    public boolean isCacheReasonEmpty() {
        if (this.cacheReason == null) {
            return true;
        }
        return this.getCacheReason().isEmpty();
    }

    /**
     * clear set
     */
    public void clearCacheReason() {
        this.getCacheReason().clear();
    }

    /**
     * Returns true if this set contains the specified element.
     *
     * @param e elem
     * @return true if this set contains the specified element, false otherwise
     */
    public boolean isCacheReasonContains(String e) {
        return this.cacheReason != null && this.cacheReason.contains(e);
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerContactsPB.Builder getCopyCsBuilder() {
        final PlayerContactsPB.Builder builder = PlayerContactsPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerContactsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getCachedZoneId() != 0) {
            builder.setCachedZoneId(this.getCachedZoneId());
            fieldCnt++;
        }  else if (builder.hasCachedZoneId()) {
            // 清理CachedZoneId
            builder.clearCachedZoneId();
            fieldCnt++;
        }
        if (this.cacheReason != null) {
            BasicPB.StringSetPB.Builder tmpBuilder = BasicPB.StringSetPB.newBuilder();
            final int tmpFieldCnt = this.cacheReason.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCacheReason(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCacheReason();
            }
        }  else if (builder.hasCacheReason()) {
            // 清理CacheReason
            builder.clearCacheReason();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerContactsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CACHEDZONEID)) {
            builder.setCachedZoneId(this.getCachedZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CACHEREASON) && this.cacheReason != null) {
            final boolean needClear = !builder.hasCacheReason();
            final int tmpFieldCnt = this.cacheReason.copyChangeToCs(builder.getCacheReasonBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCacheReason();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerContactsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CACHEDZONEID)) {
            builder.setCachedZoneId(this.getCachedZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CACHEREASON) && this.cacheReason != null) {
            final boolean needClear = !builder.hasCacheReason();
            final int tmpFieldCnt = this.cacheReason.copyChangeToAndClearDeleteKeysCs(builder.getCacheReasonBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCacheReason();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerContactsPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCachedZoneId()) {
            this.innerSetCachedZoneId(proto.getCachedZoneId());
        } else {
            this.innerSetCachedZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCacheReason()) {
            this.getCacheReason().mergeFromCs(proto.getCacheReason());
        } else {
            if (this.cacheReason != null) {
                this.cacheReason.mergeFromCs(proto.getCacheReason());
            }
        }
        this.markAll();
        return PlayerContactsProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerContactsPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasCachedZoneId()) {
            this.setCachedZoneId(proto.getCachedZoneId());
            fieldCnt++;
        }
        if (proto.hasCacheReason()) {
            this.getCacheReason().mergeChangeFromCs(proto.getCacheReason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerContacts.Builder getCopyDbBuilder() {
        final PlayerContacts.Builder builder = PlayerContacts.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerContacts.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getCachedZoneId() != 0) {
            builder.setCachedZoneId(this.getCachedZoneId());
            fieldCnt++;
        }  else if (builder.hasCachedZoneId()) {
            // 清理CachedZoneId
            builder.clearCachedZoneId();
            fieldCnt++;
        }
        if (this.cacheReason != null) {
            Basic.StringSet.Builder tmpBuilder = Basic.StringSet.newBuilder();
            final int tmpFieldCnt = this.cacheReason.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCacheReason(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCacheReason();
            }
        }  else if (builder.hasCacheReason()) {
            // 清理CacheReason
            builder.clearCacheReason();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerContacts.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CACHEDZONEID)) {
            builder.setCachedZoneId(this.getCachedZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CACHEREASON) && this.cacheReason != null) {
            final boolean needClear = !builder.hasCacheReason();
            final int tmpFieldCnt = this.cacheReason.copyChangeToDb(builder.getCacheReasonBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCacheReason();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerContacts proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCachedZoneId()) {
            this.innerSetCachedZoneId(proto.getCachedZoneId());
        } else {
            this.innerSetCachedZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCacheReason()) {
            this.getCacheReason().mergeFromDb(proto.getCacheReason());
        } else {
            if (this.cacheReason != null) {
                this.cacheReason.mergeFromDb(proto.getCacheReason());
            }
        }
        this.markAll();
        return PlayerContactsProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerContacts proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasCachedZoneId()) {
            this.setCachedZoneId(proto.getCachedZoneId());
            fieldCnt++;
        }
        if (proto.hasCacheReason()) {
            this.getCacheReason().mergeChangeFromDb(proto.getCacheReason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerContacts.Builder getCopySsBuilder() {
        final PlayerContacts.Builder builder = PlayerContacts.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerContacts.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getCachedZoneId() != 0) {
            builder.setCachedZoneId(this.getCachedZoneId());
            fieldCnt++;
        }  else if (builder.hasCachedZoneId()) {
            // 清理CachedZoneId
            builder.clearCachedZoneId();
            fieldCnt++;
        }
        if (this.cacheReason != null) {
            Basic.StringSet.Builder tmpBuilder = Basic.StringSet.newBuilder();
            final int tmpFieldCnt = this.cacheReason.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCacheReason(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCacheReason();
            }
        }  else if (builder.hasCacheReason()) {
            // 清理CacheReason
            builder.clearCacheReason();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerContacts.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CACHEDZONEID)) {
            builder.setCachedZoneId(this.getCachedZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CACHEREASON) && this.cacheReason != null) {
            final boolean needClear = !builder.hasCacheReason();
            final int tmpFieldCnt = this.cacheReason.copyChangeToSs(builder.getCacheReasonBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCacheReason();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerContacts proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCachedZoneId()) {
            this.innerSetCachedZoneId(proto.getCachedZoneId());
        } else {
            this.innerSetCachedZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCacheReason()) {
            this.getCacheReason().mergeFromSs(proto.getCacheReason());
        } else {
            if (this.cacheReason != null) {
                this.cacheReason.mergeFromSs(proto.getCacheReason());
            }
        }
        this.markAll();
        return PlayerContactsProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerContacts proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasCachedZoneId()) {
            this.setCachedZoneId(proto.getCachedZoneId());
            fieldCnt++;
        }
        if (proto.hasCacheReason()) {
            this.getCacheReason().mergeChangeFromSs(proto.getCacheReason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerContacts.Builder builder = PlayerContacts.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CACHEREASON) && this.cacheReason != null) {
            this.cacheReason.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.cacheReason != null) {
            this.cacheReason.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.playerId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerContactsProp)) {
            return false;
        }
        final PlayerContactsProp otherNode = (PlayerContactsProp) node;
        if (this.playerId != otherNode.playerId) {
            return false;
        }
        if (this.cachedZoneId != otherNode.cachedZoneId) {
            return false;
        }
        if (!this.getCacheReason().compareDataTo(otherNode.getCacheReason())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
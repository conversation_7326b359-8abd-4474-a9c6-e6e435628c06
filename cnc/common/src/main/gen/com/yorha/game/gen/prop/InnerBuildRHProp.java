package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructPlayer.InnerBuildRH;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPlayerPB.InnerBuildRHPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class InnerBuildRHProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_BUILDID = 0;
    public static final int FIELD_INDEX_BUILDTYPE = 1;
    public static final int FIELD_INDEX_BUILDSTATERH = 2;
    public static final int FIELD_INDEX_POINT = 3;
    public static final int FIELD_INDEX_LEVEL = 4;
    public static final int FIELD_INDEX_INBUILDING = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private int buildId = Constant.DEFAULT_INT_VALUE;
    private int buildType = Constant.DEFAULT_INT_VALUE;
    private BuildStateRH buildStateRH = BuildStateRH.forNumber(0);
    private PointProp point = null;
    private int level = Constant.DEFAULT_INT_VALUE;
    private boolean inBuilding = Constant.DEFAULT_BOOLEAN_VALUE;

    public InnerBuildRHProp() {
        super(null, 0, FIELD_COUNT);
    }

    public InnerBuildRHProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get buildId
     *
     * @return buildId value
     */
    public int getBuildId() {
        return this.buildId;
    }

    /**
     * set buildId && set marked
     *
     * @param buildId new value
     * @return current object
     */
    public InnerBuildRHProp setBuildId(int buildId) {
        if (this.buildId != buildId) {
            this.mark(FIELD_INDEX_BUILDID);
            this.buildId = buildId;
        }
        return this;
    }

    /**
     * inner set buildId
     *
     * @param buildId new value
     */
    private void innerSetBuildId(int buildId) {
        this.buildId = buildId;
    }

    /**
     * get buildType
     *
     * @return buildType value
     */
    public int getBuildType() {
        return this.buildType;
    }

    /**
     * set buildType && set marked
     *
     * @param buildType new value
     * @return current object
     */
    public InnerBuildRHProp setBuildType(int buildType) {
        if (this.buildType != buildType) {
            this.mark(FIELD_INDEX_BUILDTYPE);
            this.buildType = buildType;
        }
        return this;
    }

    /**
     * inner set buildType
     *
     * @param buildType new value
     */
    private void innerSetBuildType(int buildType) {
        this.buildType = buildType;
    }

    /**
     * get buildStateRH
     *
     * @return buildStateRH value
     */
    public BuildStateRH getBuildStateRH() {
        return this.buildStateRH;
    }

    /**
     * set buildStateRH && set marked
     *
     * @param buildStateRH new value
     * @return current object
     */
    public InnerBuildRHProp setBuildStateRH(BuildStateRH buildStateRH) {
        if (buildStateRH == null) {
            throw new NullPointerException();
        }
        if (this.buildStateRH != buildStateRH) {
            this.mark(FIELD_INDEX_BUILDSTATERH);
            this.buildStateRH = buildStateRH;
        }
        return this;
    }

    /**
     * inner set buildStateRH
     *
     * @param buildStateRH new value
     */
    private void innerSetBuildStateRH(BuildStateRH buildStateRH) {
        this.buildStateRH = buildStateRH;
    }

    /**
     * get point
     *
     * @return point value
     */
    public PointProp getPoint() {
        if (this.point == null) {
            this.point = new PointProp(this, FIELD_INDEX_POINT);
        }
        return this.point;
    }

    /**
     * get level
     *
     * @return level value
     */
    public int getLevel() {
        return this.level;
    }

    /**
     * set level && set marked
     *
     * @param level new value
     * @return current object
     */
    public InnerBuildRHProp setLevel(int level) {
        if (this.level != level) {
            this.mark(FIELD_INDEX_LEVEL);
            this.level = level;
        }
        return this;
    }

    /**
     * inner set level
     *
     * @param level new value
     */
    private void innerSetLevel(int level) {
        this.level = level;
    }

    /**
     * get inBuilding
     *
     * @return inBuilding value
     */
    public boolean getInBuilding() {
        return this.inBuilding;
    }

    /**
     * set inBuilding && set marked
     *
     * @param inBuilding new value
     * @return current object
     */
    public InnerBuildRHProp setInBuilding(boolean inBuilding) {
        if (this.inBuilding != inBuilding) {
            this.mark(FIELD_INDEX_INBUILDING);
            this.inBuilding = inBuilding;
        }
        return this;
    }

    /**
     * inner set inBuilding
     *
     * @param inBuilding new value
     */
    private void innerSetInBuilding(boolean inBuilding) {
        this.inBuilding = inBuilding;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public InnerBuildRHPB.Builder getCopyCsBuilder() {
        final InnerBuildRHPB.Builder builder = InnerBuildRHPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(InnerBuildRHPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBuildId() != 0) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }  else if (builder.hasBuildId()) {
            // 清理BuildId
            builder.clearBuildId();
            fieldCnt++;
        }
        if (this.getBuildType() != 0) {
            builder.setBuildType(this.getBuildType());
            fieldCnt++;
        }  else if (builder.hasBuildType()) {
            // 清理BuildType
            builder.clearBuildType();
            fieldCnt++;
        }
        if (this.getBuildStateRH() != BuildStateRH.forNumber(0)) {
            builder.setBuildStateRH(this.getBuildStateRH());
            fieldCnt++;
        }  else if (builder.hasBuildStateRH()) {
            // 清理BuildStateRH
            builder.clearBuildStateRH();
            fieldCnt++;
        }
        if (this.point != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.point.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getInBuilding()) {
            builder.setInBuilding(this.getInBuilding());
            fieldCnt++;
        }  else if (builder.hasInBuilding()) {
            // 清理InBuilding
            builder.clearInBuilding();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(InnerBuildRHPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUILDID)) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDTYPE)) {
            builder.setBuildType(this.getBuildType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDSTATERH)) {
            builder.setBuildStateRH(this.getBuildStateRH());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INBUILDING)) {
            builder.setInBuilding(this.getInBuilding());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(InnerBuildRHPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUILDID)) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDTYPE)) {
            builder.setBuildType(this.getBuildType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDSTATERH)) {
            builder.setBuildStateRH(this.getBuildStateRH());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToAndClearDeleteKeysCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INBUILDING)) {
            builder.setInBuilding(this.getInBuilding());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(InnerBuildRHPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBuildId()) {
            this.innerSetBuildId(proto.getBuildId());
        } else {
            this.innerSetBuildId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuildType()) {
            this.innerSetBuildType(proto.getBuildType());
        } else {
            this.innerSetBuildType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuildStateRH()) {
            this.innerSetBuildStateRH(proto.getBuildStateRH());
        } else {
            this.innerSetBuildStateRH(BuildStateRH.forNumber(0));
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromCs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromCs(proto.getPoint());
            }
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasInBuilding()) {
            this.innerSetInBuilding(proto.getInBuilding());
        } else {
            this.innerSetInBuilding(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return InnerBuildRHProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(InnerBuildRHPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBuildId()) {
            this.setBuildId(proto.getBuildId());
            fieldCnt++;
        }
        if (proto.hasBuildType()) {
            this.setBuildType(proto.getBuildType());
            fieldCnt++;
        }
        if (proto.hasBuildStateRH()) {
            this.setBuildStateRH(proto.getBuildStateRH());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromCs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasInBuilding()) {
            this.setInBuilding(proto.getInBuilding());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public InnerBuildRH.Builder getCopyDbBuilder() {
        final InnerBuildRH.Builder builder = InnerBuildRH.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(InnerBuildRH.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBuildId() != 0) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }  else if (builder.hasBuildId()) {
            // 清理BuildId
            builder.clearBuildId();
            fieldCnt++;
        }
        if (this.getBuildType() != 0) {
            builder.setBuildType(this.getBuildType());
            fieldCnt++;
        }  else if (builder.hasBuildType()) {
            // 清理BuildType
            builder.clearBuildType();
            fieldCnt++;
        }
        if (this.getBuildStateRH() != BuildStateRH.forNumber(0)) {
            builder.setBuildStateRH(this.getBuildStateRH());
            fieldCnt++;
        }  else if (builder.hasBuildStateRH()) {
            // 清理BuildStateRH
            builder.clearBuildStateRH();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getInBuilding()) {
            builder.setInBuilding(this.getInBuilding());
            fieldCnt++;
        }  else if (builder.hasInBuilding()) {
            // 清理InBuilding
            builder.clearInBuilding();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(InnerBuildRH.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUILDID)) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDTYPE)) {
            builder.setBuildType(this.getBuildType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDSTATERH)) {
            builder.setBuildStateRH(this.getBuildStateRH());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToDb(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INBUILDING)) {
            builder.setInBuilding(this.getInBuilding());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(InnerBuildRH proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBuildId()) {
            this.innerSetBuildId(proto.getBuildId());
        } else {
            this.innerSetBuildId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuildType()) {
            this.innerSetBuildType(proto.getBuildType());
        } else {
            this.innerSetBuildType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuildStateRH()) {
            this.innerSetBuildStateRH(proto.getBuildStateRH());
        } else {
            this.innerSetBuildStateRH(BuildStateRH.forNumber(0));
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromDb(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromDb(proto.getPoint());
            }
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasInBuilding()) {
            this.innerSetInBuilding(proto.getInBuilding());
        } else {
            this.innerSetInBuilding(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return InnerBuildRHProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(InnerBuildRH proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBuildId()) {
            this.setBuildId(proto.getBuildId());
            fieldCnt++;
        }
        if (proto.hasBuildType()) {
            this.setBuildType(proto.getBuildType());
            fieldCnt++;
        }
        if (proto.hasBuildStateRH()) {
            this.setBuildStateRH(proto.getBuildStateRH());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromDb(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasInBuilding()) {
            this.setInBuilding(proto.getInBuilding());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public InnerBuildRH.Builder getCopySsBuilder() {
        final InnerBuildRH.Builder builder = InnerBuildRH.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(InnerBuildRH.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBuildId() != 0) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }  else if (builder.hasBuildId()) {
            // 清理BuildId
            builder.clearBuildId();
            fieldCnt++;
        }
        if (this.getBuildType() != 0) {
            builder.setBuildType(this.getBuildType());
            fieldCnt++;
        }  else if (builder.hasBuildType()) {
            // 清理BuildType
            builder.clearBuildType();
            fieldCnt++;
        }
        if (this.getBuildStateRH() != BuildStateRH.forNumber(0)) {
            builder.setBuildStateRH(this.getBuildStateRH());
            fieldCnt++;
        }  else if (builder.hasBuildStateRH()) {
            // 清理BuildStateRH
            builder.clearBuildStateRH();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getInBuilding()) {
            builder.setInBuilding(this.getInBuilding());
            fieldCnt++;
        }  else if (builder.hasInBuilding()) {
            // 清理InBuilding
            builder.clearInBuilding();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(InnerBuildRH.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUILDID)) {
            builder.setBuildId(this.getBuildId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDTYPE)) {
            builder.setBuildType(this.getBuildType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDSTATERH)) {
            builder.setBuildStateRH(this.getBuildStateRH());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToSs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INBUILDING)) {
            builder.setInBuilding(this.getInBuilding());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(InnerBuildRH proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBuildId()) {
            this.innerSetBuildId(proto.getBuildId());
        } else {
            this.innerSetBuildId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuildType()) {
            this.innerSetBuildType(proto.getBuildType());
        } else {
            this.innerSetBuildType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuildStateRH()) {
            this.innerSetBuildStateRH(proto.getBuildStateRH());
        } else {
            this.innerSetBuildStateRH(BuildStateRH.forNumber(0));
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromSs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromSs(proto.getPoint());
            }
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasInBuilding()) {
            this.innerSetInBuilding(proto.getInBuilding());
        } else {
            this.innerSetInBuilding(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return InnerBuildRHProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(InnerBuildRH proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBuildId()) {
            this.setBuildId(proto.getBuildId());
            fieldCnt++;
        }
        if (proto.hasBuildType()) {
            this.setBuildType(proto.getBuildType());
            fieldCnt++;
        }
        if (proto.hasBuildStateRH()) {
            this.setBuildStateRH(proto.getBuildStateRH());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromSs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasInBuilding()) {
            this.setInBuilding(proto.getInBuilding());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        InnerBuildRH.Builder builder = InnerBuildRH.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            this.point.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.point != null) {
            this.point.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.buildId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof InnerBuildRHProp)) {
            return false;
        }
        final InnerBuildRHProp otherNode = (InnerBuildRHProp) node;
        if (this.buildId != otherNode.buildId) {
            return false;
        }
        if (this.buildType != otherNode.buildType) {
            return false;
        }
        if (this.buildStateRH != otherNode.buildStateRH) {
            return false;
        }
        if (!this.getPoint().compareDataTo(otherNode.getPoint())) {
            return false;
        }
        if (this.level != otherNode.level) {
            return false;
        }
        if (this.inBuilding != otherNode.inBuilding) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerMailModel;
import com.yorha.proto.Basic;
import com.yorha.proto.Player;
import com.yorha.proto.StructMail;
import com.yorha.proto.PlayerPB.PlayerMailModelPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.PlayerPB;
import com.yorha.proto.StructMailPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerMailModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_MAILS = 0;
    public static final int FIELD_INDEX_MAILREADINDEX = 1;
    public static final int FIELD_INDEX_SENDMAILS = 2;
    public static final int FIELD_INDEX_SHIELDPERSONS = 3;
    public static final int FIELD_INDEX_UNUSEDMAIL = 4;
    public static final int FIELD_INDEX_DELETEMAILS = 5;
    public static final int FIELD_INDEX_READEDMAILS = 6;

    public static final int FIELD_COUNT = 7;

    private long markBits0 = 0L;

    private Int64PlayerMailMapProp mails = null;
    private MailReadIndexProp mailreadIndex = null;
    private Int64PlayerMailMapProp sendMails = null;
    private Int64PlayerShieldMailPersonMapProp shieldPersons = null;
    private PlayerMailProp unusedMail = null;
    private Int64SetProp deleteMails = null;
    private Int64SetProp readedMails = null;

    public PlayerMailModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerMailModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get mails
     *
     * @return mails value
     */
    public Int64PlayerMailMapProp getMails() {
        if (this.mails == null) {
            this.mails = new Int64PlayerMailMapProp(this, FIELD_INDEX_MAILS);
        }
        return this.mails;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putMailsV(PlayerMailProp v) {
        this.getMails().put(v.getMailId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerMailProp addEmptyMails(Long k) {
        return this.getMails().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getMailsSize() {
        if (this.mails == null) {
            return 0;
        }
        return this.mails.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isMailsEmpty() {
        if (this.mails == null) {
            return true;
        }
        return this.mails.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerMailProp getMailsV(Long k) {
        if (this.mails == null || !this.mails.containsKey(k)) {
            return null;
        }
        return this.mails.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearMails() {
        if (this.mails != null) {
            this.mails.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeMailsV(Long k) {
        if (this.mails != null) {
            this.mails.remove(k);
        }
    }
    /**
     * get mailreadIndex
     *
     * @return mailreadIndex value
     */
    public MailReadIndexProp getMailreadIndex() {
        if (this.mailreadIndex == null) {
            this.mailreadIndex = new MailReadIndexProp(this, FIELD_INDEX_MAILREADINDEX);
        }
        return this.mailreadIndex;
    }

    /**
     * get sendMails
     *
     * @return sendMails value
     */
    public Int64PlayerMailMapProp getSendMails() {
        if (this.sendMails == null) {
            this.sendMails = new Int64PlayerMailMapProp(this, FIELD_INDEX_SENDMAILS);
        }
        return this.sendMails;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putSendMailsV(PlayerMailProp v) {
        this.getSendMails().put(v.getMailId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerMailProp addEmptySendMails(Long k) {
        return this.getSendMails().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getSendMailsSize() {
        if (this.sendMails == null) {
            return 0;
        }
        return this.sendMails.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isSendMailsEmpty() {
        if (this.sendMails == null) {
            return true;
        }
        return this.sendMails.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerMailProp getSendMailsV(Long k) {
        if (this.sendMails == null || !this.sendMails.containsKey(k)) {
            return null;
        }
        return this.sendMails.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearSendMails() {
        if (this.sendMails != null) {
            this.sendMails.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeSendMailsV(Long k) {
        if (this.sendMails != null) {
            this.sendMails.remove(k);
        }
    }
    /**
     * get shieldPersons
     *
     * @return shieldPersons value
     */
    public Int64PlayerShieldMailPersonMapProp getShieldPersons() {
        if (this.shieldPersons == null) {
            this.shieldPersons = new Int64PlayerShieldMailPersonMapProp(this, FIELD_INDEX_SHIELDPERSONS);
        }
        return this.shieldPersons;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putShieldPersonsV(PlayerShieldMailPersonProp v) {
        this.getShieldPersons().put(v.getPlayerId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerShieldMailPersonProp addEmptyShieldPersons(Long k) {
        return this.getShieldPersons().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getShieldPersonsSize() {
        if (this.shieldPersons == null) {
            return 0;
        }
        return this.shieldPersons.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isShieldPersonsEmpty() {
        if (this.shieldPersons == null) {
            return true;
        }
        return this.shieldPersons.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerShieldMailPersonProp getShieldPersonsV(Long k) {
        if (this.shieldPersons == null || !this.shieldPersons.containsKey(k)) {
            return null;
        }
        return this.shieldPersons.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearShieldPersons() {
        if (this.shieldPersons != null) {
            this.shieldPersons.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeShieldPersonsV(Long k) {
        if (this.shieldPersons != null) {
            this.shieldPersons.remove(k);
        }
    }
    /**
     * get unusedMail
     *
     * @return unusedMail value
     */
    public PlayerMailProp getUnusedMail() {
        if (this.unusedMail == null) {
            this.unusedMail = new PlayerMailProp(this, FIELD_INDEX_UNUSEDMAIL);
        }
        return this.unusedMail;
    }

    /**
     * get deleteMails
     *
     * @return deleteMails value
     */
    public Int64SetProp getDeleteMails() {
        if (this.deleteMails == null) {
            this.deleteMails = new Int64SetProp(this, FIELD_INDEX_DELETEMAILS);
        }
        return this.deleteMails;
    }


    /**
     * add value to set
     *
     * @param e value
     */
    public void addDeleteMails(Long e) {
        this.getDeleteMails().add(e);
    }

    /**
     * remove the specified element in set
     *
     * @param e element
     * @return e if success or null by fail
     */
    public Long removeDeleteMails(Long e) {
        if (this.deleteMails == null) {
            return null;
        }
        if(this.deleteMails.remove(e)) {
            return e;
        }
        return null;
    }

    /**
     * get set size
     *
     * @return set size
     */
    public int getDeleteMailsSize() {
        if (this.deleteMails == null) {
            return 0;
        }
        return this.deleteMails.size();
    }

    /**
     * get is a empty set
     *
     * @return true if empty
     */
    public boolean isDeleteMailsEmpty() {
        if (this.deleteMails == null) {
            return true;
        }
        return this.getDeleteMails().isEmpty();
    }

    /**
     * clear set
     */
    public void clearDeleteMails() {
        this.getDeleteMails().clear();
    }

    /**
     * Returns true if this set contains the specified element.
     *
     * @param e elem
     * @return true if this set contains the specified element, false otherwise
     */
    public boolean isDeleteMailsContains(Long e) {
        return this.deleteMails != null && this.deleteMails.contains(e);
    }

    /**
     * get readedMails
     *
     * @return readedMails value
     */
    public Int64SetProp getReadedMails() {
        if (this.readedMails == null) {
            this.readedMails = new Int64SetProp(this, FIELD_INDEX_READEDMAILS);
        }
        return this.readedMails;
    }


    /**
     * add value to set
     *
     * @param e value
     */
    public void addReadedMails(Long e) {
        this.getReadedMails().add(e);
    }

    /**
     * remove the specified element in set
     *
     * @param e element
     * @return e if success or null by fail
     */
    public Long removeReadedMails(Long e) {
        if (this.readedMails == null) {
            return null;
        }
        if(this.readedMails.remove(e)) {
            return e;
        }
        return null;
    }

    /**
     * get set size
     *
     * @return set size
     */
    public int getReadedMailsSize() {
        if (this.readedMails == null) {
            return 0;
        }
        return this.readedMails.size();
    }

    /**
     * get is a empty set
     *
     * @return true if empty
     */
    public boolean isReadedMailsEmpty() {
        if (this.readedMails == null) {
            return true;
        }
        return this.getReadedMails().isEmpty();
    }

    /**
     * clear set
     */
    public void clearReadedMails() {
        this.getReadedMails().clear();
    }

    /**
     * Returns true if this set contains the specified element.
     *
     * @param e elem
     * @return true if this set contains the specified element, false otherwise
     */
    public boolean isReadedMailsContains(Long e) {
        return this.readedMails != null && this.readedMails.contains(e);
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMailModelPB.Builder getCopyCsBuilder() {
        final PlayerMailModelPB.Builder builder = PlayerMailModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerMailModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.shieldPersons != null) {
            PlayerPB.Int64PlayerShieldMailPersonMapPB.Builder tmpBuilder = PlayerPB.Int64PlayerShieldMailPersonMapPB.newBuilder();
            final int tmpFieldCnt = this.shieldPersons.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setShieldPersons(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearShieldPersons();
            }
        }  else if (builder.hasShieldPersons()) {
            // 清理ShieldPersons
            builder.clearShieldPersons();
            fieldCnt++;
        }
        if (this.unusedMail != null) {
            StructMailPB.PlayerMailPB.Builder tmpBuilder = StructMailPB.PlayerMailPB.newBuilder();
            final int tmpFieldCnt = this.unusedMail.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnusedMail(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnusedMail();
            }
        }  else if (builder.hasUnusedMail()) {
            // 清理UnusedMail
            builder.clearUnusedMail();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerMailModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SHIELDPERSONS) && this.shieldPersons != null) {
            final boolean needClear = !builder.hasShieldPersons();
            final int tmpFieldCnt = this.shieldPersons.copyChangeToCs(builder.getShieldPersonsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearShieldPersons();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNUSEDMAIL) && this.unusedMail != null) {
            final boolean needClear = !builder.hasUnusedMail();
            final int tmpFieldCnt = this.unusedMail.copyChangeToCs(builder.getUnusedMailBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnusedMail();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerMailModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SHIELDPERSONS) && this.shieldPersons != null) {
            final boolean needClear = !builder.hasShieldPersons();
            final int tmpFieldCnt = this.shieldPersons.copyChangeToAndClearDeleteKeysCs(builder.getShieldPersonsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearShieldPersons();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNUSEDMAIL) && this.unusedMail != null) {
            final boolean needClear = !builder.hasUnusedMail();
            final int tmpFieldCnt = this.unusedMail.copyChangeToAndClearDeleteKeysCs(builder.getUnusedMailBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnusedMail();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerMailModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasShieldPersons()) {
            this.getShieldPersons().mergeFromCs(proto.getShieldPersons());
        } else {
            if (this.shieldPersons != null) {
                this.shieldPersons.mergeFromCs(proto.getShieldPersons());
            }
        }
        if (proto.hasUnusedMail()) {
            this.getUnusedMail().mergeFromCs(proto.getUnusedMail());
        } else {
            if (this.unusedMail != null) {
                this.unusedMail.mergeFromCs(proto.getUnusedMail());
            }
        }
        this.markAll();
        return PlayerMailModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerMailModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasShieldPersons()) {
            this.getShieldPersons().mergeChangeFromCs(proto.getShieldPersons());
            fieldCnt++;
        }
        if (proto.hasUnusedMail()) {
            this.getUnusedMail().mergeChangeFromCs(proto.getUnusedMail());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMailModel.Builder getCopyDbBuilder() {
        final PlayerMailModel.Builder builder = PlayerMailModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerMailModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.mailreadIndex != null) {
            StructMail.MailReadIndex.Builder tmpBuilder = StructMail.MailReadIndex.newBuilder();
            final int tmpFieldCnt = this.mailreadIndex.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMailreadIndex(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMailreadIndex();
            }
        }  else if (builder.hasMailreadIndex()) {
            // 清理MailreadIndex
            builder.clearMailreadIndex();
            fieldCnt++;
        }
        if (this.sendMails != null) {
            StructMail.Int64PlayerMailMap.Builder tmpBuilder = StructMail.Int64PlayerMailMap.newBuilder();
            final int tmpFieldCnt = this.sendMails.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSendMails(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSendMails();
            }
        }  else if (builder.hasSendMails()) {
            // 清理SendMails
            builder.clearSendMails();
            fieldCnt++;
        }
        if (this.shieldPersons != null) {
            Player.Int64PlayerShieldMailPersonMap.Builder tmpBuilder = Player.Int64PlayerShieldMailPersonMap.newBuilder();
            final int tmpFieldCnt = this.shieldPersons.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setShieldPersons(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearShieldPersons();
            }
        }  else if (builder.hasShieldPersons()) {
            // 清理ShieldPersons
            builder.clearShieldPersons();
            fieldCnt++;
        }
        if (this.unusedMail != null) {
            StructMail.PlayerMail.Builder tmpBuilder = StructMail.PlayerMail.newBuilder();
            final int tmpFieldCnt = this.unusedMail.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnusedMail(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnusedMail();
            }
        }  else if (builder.hasUnusedMail()) {
            // 清理UnusedMail
            builder.clearUnusedMail();
            fieldCnt++;
        }
        if (this.deleteMails != null) {
            Basic.Int64Set.Builder tmpBuilder = Basic.Int64Set.newBuilder();
            final int tmpFieldCnt = this.deleteMails.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDeleteMails(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDeleteMails();
            }
        }  else if (builder.hasDeleteMails()) {
            // 清理DeleteMails
            builder.clearDeleteMails();
            fieldCnt++;
        }
        if (this.readedMails != null) {
            Basic.Int64Set.Builder tmpBuilder = Basic.Int64Set.newBuilder();
            final int tmpFieldCnt = this.readedMails.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setReadedMails(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearReadedMails();
            }
        }  else if (builder.hasReadedMails()) {
            // 清理ReadedMails
            builder.clearReadedMails();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerMailModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAILREADINDEX) && this.mailreadIndex != null) {
            final boolean needClear = !builder.hasMailreadIndex();
            final int tmpFieldCnt = this.mailreadIndex.copyChangeToDb(builder.getMailreadIndexBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMailreadIndex();
            }
        }
        if (this.hasMark(FIELD_INDEX_SENDMAILS) && this.sendMails != null) {
            final boolean needClear = !builder.hasSendMails();
            final int tmpFieldCnt = this.sendMails.copyChangeToDb(builder.getSendMailsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSendMails();
            }
        }
        if (this.hasMark(FIELD_INDEX_SHIELDPERSONS) && this.shieldPersons != null) {
            final boolean needClear = !builder.hasShieldPersons();
            final int tmpFieldCnt = this.shieldPersons.copyChangeToDb(builder.getShieldPersonsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearShieldPersons();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNUSEDMAIL) && this.unusedMail != null) {
            final boolean needClear = !builder.hasUnusedMail();
            final int tmpFieldCnt = this.unusedMail.copyChangeToDb(builder.getUnusedMailBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnusedMail();
            }
        }
        if (this.hasMark(FIELD_INDEX_DELETEMAILS) && this.deleteMails != null) {
            final boolean needClear = !builder.hasDeleteMails();
            final int tmpFieldCnt = this.deleteMails.copyChangeToDb(builder.getDeleteMailsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDeleteMails();
            }
        }
        if (this.hasMark(FIELD_INDEX_READEDMAILS) && this.readedMails != null) {
            final boolean needClear = !builder.hasReadedMails();
            final int tmpFieldCnt = this.readedMails.copyChangeToDb(builder.getReadedMailsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearReadedMails();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerMailModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMailreadIndex()) {
            this.getMailreadIndex().mergeFromDb(proto.getMailreadIndex());
        } else {
            if (this.mailreadIndex != null) {
                this.mailreadIndex.mergeFromDb(proto.getMailreadIndex());
            }
        }
        if (proto.hasSendMails()) {
            this.getSendMails().mergeFromDb(proto.getSendMails());
        } else {
            if (this.sendMails != null) {
                this.sendMails.mergeFromDb(proto.getSendMails());
            }
        }
        if (proto.hasShieldPersons()) {
            this.getShieldPersons().mergeFromDb(proto.getShieldPersons());
        } else {
            if (this.shieldPersons != null) {
                this.shieldPersons.mergeFromDb(proto.getShieldPersons());
            }
        }
        if (proto.hasUnusedMail()) {
            this.getUnusedMail().mergeFromDb(proto.getUnusedMail());
        } else {
            if (this.unusedMail != null) {
                this.unusedMail.mergeFromDb(proto.getUnusedMail());
            }
        }
        if (proto.hasDeleteMails()) {
            this.getDeleteMails().mergeFromDb(proto.getDeleteMails());
        } else {
            if (this.deleteMails != null) {
                this.deleteMails.mergeFromDb(proto.getDeleteMails());
            }
        }
        if (proto.hasReadedMails()) {
            this.getReadedMails().mergeFromDb(proto.getReadedMails());
        } else {
            if (this.readedMails != null) {
                this.readedMails.mergeFromDb(proto.getReadedMails());
            }
        }
        this.markAll();
        return PlayerMailModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerMailModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMailreadIndex()) {
            this.getMailreadIndex().mergeChangeFromDb(proto.getMailreadIndex());
            fieldCnt++;
        }
        if (proto.hasSendMails()) {
            this.getSendMails().mergeChangeFromDb(proto.getSendMails());
            fieldCnt++;
        }
        if (proto.hasShieldPersons()) {
            this.getShieldPersons().mergeChangeFromDb(proto.getShieldPersons());
            fieldCnt++;
        }
        if (proto.hasUnusedMail()) {
            this.getUnusedMail().mergeChangeFromDb(proto.getUnusedMail());
            fieldCnt++;
        }
        if (proto.hasDeleteMails()) {
            this.getDeleteMails().mergeChangeFromDb(proto.getDeleteMails());
            fieldCnt++;
        }
        if (proto.hasReadedMails()) {
            this.getReadedMails().mergeChangeFromDb(proto.getReadedMails());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMailModel.Builder getCopySsBuilder() {
        final PlayerMailModel.Builder builder = PlayerMailModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerMailModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.mails != null) {
            StructMail.Int64PlayerMailMap.Builder tmpBuilder = StructMail.Int64PlayerMailMap.newBuilder();
            final int tmpFieldCnt = this.mails.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMails(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMails();
            }
        }  else if (builder.hasMails()) {
            // 清理Mails
            builder.clearMails();
            fieldCnt++;
        }
        if (this.mailreadIndex != null) {
            StructMail.MailReadIndex.Builder tmpBuilder = StructMail.MailReadIndex.newBuilder();
            final int tmpFieldCnt = this.mailreadIndex.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMailreadIndex(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMailreadIndex();
            }
        }  else if (builder.hasMailreadIndex()) {
            // 清理MailreadIndex
            builder.clearMailreadIndex();
            fieldCnt++;
        }
        if (this.sendMails != null) {
            StructMail.Int64PlayerMailMap.Builder tmpBuilder = StructMail.Int64PlayerMailMap.newBuilder();
            final int tmpFieldCnt = this.sendMails.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSendMails(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSendMails();
            }
        }  else if (builder.hasSendMails()) {
            // 清理SendMails
            builder.clearSendMails();
            fieldCnt++;
        }
        if (this.shieldPersons != null) {
            Player.Int64PlayerShieldMailPersonMap.Builder tmpBuilder = Player.Int64PlayerShieldMailPersonMap.newBuilder();
            final int tmpFieldCnt = this.shieldPersons.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setShieldPersons(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearShieldPersons();
            }
        }  else if (builder.hasShieldPersons()) {
            // 清理ShieldPersons
            builder.clearShieldPersons();
            fieldCnt++;
        }
        if (this.unusedMail != null) {
            StructMail.PlayerMail.Builder tmpBuilder = StructMail.PlayerMail.newBuilder();
            final int tmpFieldCnt = this.unusedMail.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnusedMail(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnusedMail();
            }
        }  else if (builder.hasUnusedMail()) {
            // 清理UnusedMail
            builder.clearUnusedMail();
            fieldCnt++;
        }
        if (this.deleteMails != null) {
            Basic.Int64Set.Builder tmpBuilder = Basic.Int64Set.newBuilder();
            final int tmpFieldCnt = this.deleteMails.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDeleteMails(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDeleteMails();
            }
        }  else if (builder.hasDeleteMails()) {
            // 清理DeleteMails
            builder.clearDeleteMails();
            fieldCnt++;
        }
        if (this.readedMails != null) {
            Basic.Int64Set.Builder tmpBuilder = Basic.Int64Set.newBuilder();
            final int tmpFieldCnt = this.readedMails.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setReadedMails(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearReadedMails();
            }
        }  else if (builder.hasReadedMails()) {
            // 清理ReadedMails
            builder.clearReadedMails();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerMailModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAILS) && this.mails != null) {
            final boolean needClear = !builder.hasMails();
            final int tmpFieldCnt = this.mails.copyChangeToSs(builder.getMailsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMails();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAILREADINDEX) && this.mailreadIndex != null) {
            final boolean needClear = !builder.hasMailreadIndex();
            final int tmpFieldCnt = this.mailreadIndex.copyChangeToSs(builder.getMailreadIndexBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMailreadIndex();
            }
        }
        if (this.hasMark(FIELD_INDEX_SENDMAILS) && this.sendMails != null) {
            final boolean needClear = !builder.hasSendMails();
            final int tmpFieldCnt = this.sendMails.copyChangeToSs(builder.getSendMailsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSendMails();
            }
        }
        if (this.hasMark(FIELD_INDEX_SHIELDPERSONS) && this.shieldPersons != null) {
            final boolean needClear = !builder.hasShieldPersons();
            final int tmpFieldCnt = this.shieldPersons.copyChangeToSs(builder.getShieldPersonsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearShieldPersons();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNUSEDMAIL) && this.unusedMail != null) {
            final boolean needClear = !builder.hasUnusedMail();
            final int tmpFieldCnt = this.unusedMail.copyChangeToSs(builder.getUnusedMailBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnusedMail();
            }
        }
        if (this.hasMark(FIELD_INDEX_DELETEMAILS) && this.deleteMails != null) {
            final boolean needClear = !builder.hasDeleteMails();
            final int tmpFieldCnt = this.deleteMails.copyChangeToSs(builder.getDeleteMailsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDeleteMails();
            }
        }
        if (this.hasMark(FIELD_INDEX_READEDMAILS) && this.readedMails != null) {
            final boolean needClear = !builder.hasReadedMails();
            final int tmpFieldCnt = this.readedMails.copyChangeToSs(builder.getReadedMailsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearReadedMails();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerMailModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMails()) {
            this.getMails().mergeFromSs(proto.getMails());
        } else {
            if (this.mails != null) {
                this.mails.mergeFromSs(proto.getMails());
            }
        }
        if (proto.hasMailreadIndex()) {
            this.getMailreadIndex().mergeFromSs(proto.getMailreadIndex());
        } else {
            if (this.mailreadIndex != null) {
                this.mailreadIndex.mergeFromSs(proto.getMailreadIndex());
            }
        }
        if (proto.hasSendMails()) {
            this.getSendMails().mergeFromSs(proto.getSendMails());
        } else {
            if (this.sendMails != null) {
                this.sendMails.mergeFromSs(proto.getSendMails());
            }
        }
        if (proto.hasShieldPersons()) {
            this.getShieldPersons().mergeFromSs(proto.getShieldPersons());
        } else {
            if (this.shieldPersons != null) {
                this.shieldPersons.mergeFromSs(proto.getShieldPersons());
            }
        }
        if (proto.hasUnusedMail()) {
            this.getUnusedMail().mergeFromSs(proto.getUnusedMail());
        } else {
            if (this.unusedMail != null) {
                this.unusedMail.mergeFromSs(proto.getUnusedMail());
            }
        }
        if (proto.hasDeleteMails()) {
            this.getDeleteMails().mergeFromSs(proto.getDeleteMails());
        } else {
            if (this.deleteMails != null) {
                this.deleteMails.mergeFromSs(proto.getDeleteMails());
            }
        }
        if (proto.hasReadedMails()) {
            this.getReadedMails().mergeFromSs(proto.getReadedMails());
        } else {
            if (this.readedMails != null) {
                this.readedMails.mergeFromSs(proto.getReadedMails());
            }
        }
        this.markAll();
        return PlayerMailModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerMailModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMails()) {
            this.getMails().mergeChangeFromSs(proto.getMails());
            fieldCnt++;
        }
        if (proto.hasMailreadIndex()) {
            this.getMailreadIndex().mergeChangeFromSs(proto.getMailreadIndex());
            fieldCnt++;
        }
        if (proto.hasSendMails()) {
            this.getSendMails().mergeChangeFromSs(proto.getSendMails());
            fieldCnt++;
        }
        if (proto.hasShieldPersons()) {
            this.getShieldPersons().mergeChangeFromSs(proto.getShieldPersons());
            fieldCnt++;
        }
        if (proto.hasUnusedMail()) {
            this.getUnusedMail().mergeChangeFromSs(proto.getUnusedMail());
            fieldCnt++;
        }
        if (proto.hasDeleteMails()) {
            this.getDeleteMails().mergeChangeFromSs(proto.getDeleteMails());
            fieldCnt++;
        }
        if (proto.hasReadedMails()) {
            this.getReadedMails().mergeChangeFromSs(proto.getReadedMails());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerMailModel.Builder builder = PlayerMailModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_MAILS) && this.mails != null) {
            this.mails.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_MAILREADINDEX) && this.mailreadIndex != null) {
            this.mailreadIndex.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SENDMAILS) && this.sendMails != null) {
            this.sendMails.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SHIELDPERSONS) && this.shieldPersons != null) {
            this.shieldPersons.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_UNUSEDMAIL) && this.unusedMail != null) {
            this.unusedMail.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DELETEMAILS) && this.deleteMails != null) {
            this.deleteMails.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_READEDMAILS) && this.readedMails != null) {
            this.readedMails.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.mails != null) {
            this.mails.markAll();
        }
        if (this.mailreadIndex != null) {
            this.mailreadIndex.markAll();
        }
        if (this.sendMails != null) {
            this.sendMails.markAll();
        }
        if (this.shieldPersons != null) {
            this.shieldPersons.markAll();
        }
        if (this.unusedMail != null) {
            this.unusedMail.markAll();
        }
        if (this.deleteMails != null) {
            this.deleteMails.markAll();
        }
        if (this.readedMails != null) {
            this.readedMails.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerMailModelProp)) {
            return false;
        }
        final PlayerMailModelProp otherNode = (PlayerMailModelProp) node;
        if (!this.getMails().compareDataTo(otherNode.getMails())) {
            return false;
        }
        if (!this.getMailreadIndex().compareDataTo(otherNode.getMailreadIndex())) {
            return false;
        }
        if (!this.getSendMails().compareDataTo(otherNode.getSendMails())) {
            return false;
        }
        if (!this.getShieldPersons().compareDataTo(otherNode.getShieldPersons())) {
            return false;
        }
        if (!this.getUnusedMail().compareDataTo(otherNode.getUnusedMail())) {
            return false;
        }
        if (!this.getDeleteMails().compareDataTo(otherNode.getDeleteMails())) {
            return false;
        }
        if (!this.getReadedMails().compareDataTo(otherNode.getReadedMails())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 57;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
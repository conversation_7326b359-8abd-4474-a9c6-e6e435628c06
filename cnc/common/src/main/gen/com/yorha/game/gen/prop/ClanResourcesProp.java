package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Clan.ClanResources;
import com.yorha.proto.Clan;
import com.yorha.proto.Struct;
import com.yorha.proto.ClanPB.ClanResourcesPB;
import com.yorha.proto.ClanPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanResourcesProp extends AbstractPropNode {

    public static final int FIELD_INDEX_LASTCALTSMS = 0;
    public static final int FIELD_INDEX_USAGERECORD = 1;
    public static final int FIELD_INDEX_WAREHOUSEMAP = 2;
    public static final int FIELD_INDEX_WAREHOUSELOGBASICINFO = 3;
    public static final int FIELD_INDEX_RESOURCEINFOS = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private long lastCalTsMs = Constant.DEFAULT_LONG_VALUE;
    private Int64WarehouseUsageItemMapProp usageRecord = null;
    private Int32WarehouseInfoMapProp warehouseMap = null;
    private ClanLogBasicInfoProp wareHouseLogBasicInfo = null;
    private Int32SingleClanResourceInfoMapProp resourceInfos = null;

    public ClanResourcesProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanResourcesProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get lastCalTsMs
     *
     * @return lastCalTsMs value
     */
    public long getLastCalTsMs() {
        return this.lastCalTsMs;
    }

    /**
     * set lastCalTsMs && set marked
     *
     * @param lastCalTsMs new value
     * @return current object
     */
    public ClanResourcesProp setLastCalTsMs(long lastCalTsMs) {
        if (this.lastCalTsMs != lastCalTsMs) {
            this.mark(FIELD_INDEX_LASTCALTSMS);
            this.lastCalTsMs = lastCalTsMs;
        }
        return this;
    }

    /**
     * inner set lastCalTsMs
     *
     * @param lastCalTsMs new value
     */
    private void innerSetLastCalTsMs(long lastCalTsMs) {
        this.lastCalTsMs = lastCalTsMs;
    }

    /**
     * get usageRecord
     *
     * @return usageRecord value
     */
    public Int64WarehouseUsageItemMapProp getUsageRecord() {
        if (this.usageRecord == null) {
            this.usageRecord = new Int64WarehouseUsageItemMapProp(this, FIELD_INDEX_USAGERECORD);
        }
        return this.usageRecord;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putUsageRecordV(WarehouseUsageItemProp v) {
        this.getUsageRecord().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public WarehouseUsageItemProp addEmptyUsageRecord(Long k) {
        return this.getUsageRecord().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getUsageRecordSize() {
        if (this.usageRecord == null) {
            return 0;
        }
        return this.usageRecord.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isUsageRecordEmpty() {
        if (this.usageRecord == null) {
            return true;
        }
        return this.usageRecord.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public WarehouseUsageItemProp getUsageRecordV(Long k) {
        if (this.usageRecord == null || !this.usageRecord.containsKey(k)) {
            return null;
        }
        return this.usageRecord.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearUsageRecord() {
        if (this.usageRecord != null) {
            this.usageRecord.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeUsageRecordV(Long k) {
        if (this.usageRecord != null) {
            this.usageRecord.remove(k);
        }
    }
    /**
     * get warehouseMap
     *
     * @return warehouseMap value
     */
    public Int32WarehouseInfoMapProp getWarehouseMap() {
        if (this.warehouseMap == null) {
            this.warehouseMap = new Int32WarehouseInfoMapProp(this, FIELD_INDEX_WAREHOUSEMAP);
        }
        return this.warehouseMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putWarehouseMapV(WarehouseInfoProp v) {
        this.getWarehouseMap().put(v.getType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public WarehouseInfoProp addEmptyWarehouseMap(Integer k) {
        return this.getWarehouseMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getWarehouseMapSize() {
        if (this.warehouseMap == null) {
            return 0;
        }
        return this.warehouseMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isWarehouseMapEmpty() {
        if (this.warehouseMap == null) {
            return true;
        }
        return this.warehouseMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public WarehouseInfoProp getWarehouseMapV(Integer k) {
        if (this.warehouseMap == null || !this.warehouseMap.containsKey(k)) {
            return null;
        }
        return this.warehouseMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearWarehouseMap() {
        if (this.warehouseMap != null) {
            this.warehouseMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeWarehouseMapV(Integer k) {
        if (this.warehouseMap != null) {
            this.warehouseMap.remove(k);
        }
    }
    /**
     * get wareHouseLogBasicInfo
     *
     * @return wareHouseLogBasicInfo value
     */
    public ClanLogBasicInfoProp getWareHouseLogBasicInfo() {
        if (this.wareHouseLogBasicInfo == null) {
            this.wareHouseLogBasicInfo = new ClanLogBasicInfoProp(this, FIELD_INDEX_WAREHOUSELOGBASICINFO);
        }
        return this.wareHouseLogBasicInfo;
    }

    /**
     * get resourceInfos
     *
     * @return resourceInfos value
     */
    public Int32SingleClanResourceInfoMapProp getResourceInfos() {
        if (this.resourceInfos == null) {
            this.resourceInfos = new Int32SingleClanResourceInfoMapProp(this, FIELD_INDEX_RESOURCEINFOS);
        }
        return this.resourceInfos;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putResourceInfosV(SingleClanResourceInfoProp v) {
        this.getResourceInfos().put(v.getType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public SingleClanResourceInfoProp addEmptyResourceInfos(Integer k) {
        return this.getResourceInfos().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getResourceInfosSize() {
        if (this.resourceInfos == null) {
            return 0;
        }
        return this.resourceInfos.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isResourceInfosEmpty() {
        if (this.resourceInfos == null) {
            return true;
        }
        return this.resourceInfos.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public SingleClanResourceInfoProp getResourceInfosV(Integer k) {
        if (this.resourceInfos == null || !this.resourceInfos.containsKey(k)) {
            return null;
        }
        return this.resourceInfos.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearResourceInfos() {
        if (this.resourceInfos != null) {
            this.resourceInfos.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeResourceInfosV(Integer k) {
        if (this.resourceInfos != null) {
            this.resourceInfos.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanResourcesPB.Builder getCopyCsBuilder() {
        final ClanResourcesPB.Builder builder = ClanResourcesPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanResourcesPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLastCalTsMs() != 0L) {
            builder.setLastCalTsMs(this.getLastCalTsMs());
            fieldCnt++;
        }  else if (builder.hasLastCalTsMs()) {
            // 清理LastCalTsMs
            builder.clearLastCalTsMs();
            fieldCnt++;
        }
        if (this.usageRecord != null) {
            StructPB.Int64WarehouseUsageItemMapPB.Builder tmpBuilder = StructPB.Int64WarehouseUsageItemMapPB.newBuilder();
            final int tmpFieldCnt = this.usageRecord.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUsageRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUsageRecord();
            }
        }  else if (builder.hasUsageRecord()) {
            // 清理UsageRecord
            builder.clearUsageRecord();
            fieldCnt++;
        }
        if (this.warehouseMap != null) {
            ClanPB.Int32WarehouseInfoMapPB.Builder tmpBuilder = ClanPB.Int32WarehouseInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.warehouseMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWarehouseMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWarehouseMap();
            }
        }  else if (builder.hasWarehouseMap()) {
            // 清理WarehouseMap
            builder.clearWarehouseMap();
            fieldCnt++;
        }
        if (this.resourceInfos != null) {
            StructPB.Int32SingleClanResourceInfoMapPB.Builder tmpBuilder = StructPB.Int32SingleClanResourceInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.resourceInfos.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResourceInfos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResourceInfos();
            }
        }  else if (builder.hasResourceInfos()) {
            // 清理ResourceInfos
            builder.clearResourceInfos();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanResourcesPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LASTCALTSMS)) {
            builder.setLastCalTsMs(this.getLastCalTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USAGERECORD) && this.usageRecord != null) {
            final boolean needClear = !builder.hasUsageRecord();
            final int tmpFieldCnt = this.usageRecord.copyChangeToCs(builder.getUsageRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUsageRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_WAREHOUSEMAP) && this.warehouseMap != null) {
            final boolean needClear = !builder.hasWarehouseMap();
            final int tmpFieldCnt = this.warehouseMap.copyChangeToCs(builder.getWarehouseMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWarehouseMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESOURCEINFOS) && this.resourceInfos != null) {
            final boolean needClear = !builder.hasResourceInfos();
            final int tmpFieldCnt = this.resourceInfos.copyChangeToCs(builder.getResourceInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResourceInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanResourcesPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LASTCALTSMS)) {
            builder.setLastCalTsMs(this.getLastCalTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USAGERECORD) && this.usageRecord != null) {
            final boolean needClear = !builder.hasUsageRecord();
            final int tmpFieldCnt = this.usageRecord.copyChangeToAndClearDeleteKeysCs(builder.getUsageRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUsageRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_WAREHOUSEMAP) && this.warehouseMap != null) {
            final boolean needClear = !builder.hasWarehouseMap();
            final int tmpFieldCnt = this.warehouseMap.copyChangeToAndClearDeleteKeysCs(builder.getWarehouseMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWarehouseMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESOURCEINFOS) && this.resourceInfos != null) {
            final boolean needClear = !builder.hasResourceInfos();
            final int tmpFieldCnt = this.resourceInfos.copyChangeToAndClearDeleteKeysCs(builder.getResourceInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResourceInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanResourcesPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLastCalTsMs()) {
            this.innerSetLastCalTsMs(proto.getLastCalTsMs());
        } else {
            this.innerSetLastCalTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasUsageRecord()) {
            this.getUsageRecord().mergeFromCs(proto.getUsageRecord());
        } else {
            if (this.usageRecord != null) {
                this.usageRecord.mergeFromCs(proto.getUsageRecord());
            }
        }
        if (proto.hasWarehouseMap()) {
            this.getWarehouseMap().mergeFromCs(proto.getWarehouseMap());
        } else {
            if (this.warehouseMap != null) {
                this.warehouseMap.mergeFromCs(proto.getWarehouseMap());
            }
        }
        if (proto.hasResourceInfos()) {
            this.getResourceInfos().mergeFromCs(proto.getResourceInfos());
        } else {
            if (this.resourceInfos != null) {
                this.resourceInfos.mergeFromCs(proto.getResourceInfos());
            }
        }
        this.markAll();
        return ClanResourcesProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanResourcesPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLastCalTsMs()) {
            this.setLastCalTsMs(proto.getLastCalTsMs());
            fieldCnt++;
        }
        if (proto.hasUsageRecord()) {
            this.getUsageRecord().mergeChangeFromCs(proto.getUsageRecord());
            fieldCnt++;
        }
        if (proto.hasWarehouseMap()) {
            this.getWarehouseMap().mergeChangeFromCs(proto.getWarehouseMap());
            fieldCnt++;
        }
        if (proto.hasResourceInfos()) {
            this.getResourceInfos().mergeChangeFromCs(proto.getResourceInfos());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanResources.Builder getCopyDbBuilder() {
        final ClanResources.Builder builder = ClanResources.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanResources.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLastCalTsMs() != 0L) {
            builder.setLastCalTsMs(this.getLastCalTsMs());
            fieldCnt++;
        }  else if (builder.hasLastCalTsMs()) {
            // 清理LastCalTsMs
            builder.clearLastCalTsMs();
            fieldCnt++;
        }
        if (this.usageRecord != null) {
            Struct.Int64WarehouseUsageItemMap.Builder tmpBuilder = Struct.Int64WarehouseUsageItemMap.newBuilder();
            final int tmpFieldCnt = this.usageRecord.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUsageRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUsageRecord();
            }
        }  else if (builder.hasUsageRecord()) {
            // 清理UsageRecord
            builder.clearUsageRecord();
            fieldCnt++;
        }
        if (this.warehouseMap != null) {
            Clan.Int32WarehouseInfoMap.Builder tmpBuilder = Clan.Int32WarehouseInfoMap.newBuilder();
            final int tmpFieldCnt = this.warehouseMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWarehouseMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWarehouseMap();
            }
        }  else if (builder.hasWarehouseMap()) {
            // 清理WarehouseMap
            builder.clearWarehouseMap();
            fieldCnt++;
        }
        if (this.wareHouseLogBasicInfo != null) {
            Clan.ClanLogBasicInfo.Builder tmpBuilder = Clan.ClanLogBasicInfo.newBuilder();
            final int tmpFieldCnt = this.wareHouseLogBasicInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWareHouseLogBasicInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWareHouseLogBasicInfo();
            }
        }  else if (builder.hasWareHouseLogBasicInfo()) {
            // 清理WareHouseLogBasicInfo
            builder.clearWareHouseLogBasicInfo();
            fieldCnt++;
        }
        if (this.resourceInfos != null) {
            Struct.Int32SingleClanResourceInfoMap.Builder tmpBuilder = Struct.Int32SingleClanResourceInfoMap.newBuilder();
            final int tmpFieldCnt = this.resourceInfos.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResourceInfos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResourceInfos();
            }
        }  else if (builder.hasResourceInfos()) {
            // 清理ResourceInfos
            builder.clearResourceInfos();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanResources.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LASTCALTSMS)) {
            builder.setLastCalTsMs(this.getLastCalTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USAGERECORD) && this.usageRecord != null) {
            final boolean needClear = !builder.hasUsageRecord();
            final int tmpFieldCnt = this.usageRecord.copyChangeToDb(builder.getUsageRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUsageRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_WAREHOUSEMAP) && this.warehouseMap != null) {
            final boolean needClear = !builder.hasWarehouseMap();
            final int tmpFieldCnt = this.warehouseMap.copyChangeToDb(builder.getWarehouseMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWarehouseMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_WAREHOUSELOGBASICINFO) && this.wareHouseLogBasicInfo != null) {
            final boolean needClear = !builder.hasWareHouseLogBasicInfo();
            final int tmpFieldCnt = this.wareHouseLogBasicInfo.copyChangeToDb(builder.getWareHouseLogBasicInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWareHouseLogBasicInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESOURCEINFOS) && this.resourceInfos != null) {
            final boolean needClear = !builder.hasResourceInfos();
            final int tmpFieldCnt = this.resourceInfos.copyChangeToDb(builder.getResourceInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResourceInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanResources proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLastCalTsMs()) {
            this.innerSetLastCalTsMs(proto.getLastCalTsMs());
        } else {
            this.innerSetLastCalTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasUsageRecord()) {
            this.getUsageRecord().mergeFromDb(proto.getUsageRecord());
        } else {
            if (this.usageRecord != null) {
                this.usageRecord.mergeFromDb(proto.getUsageRecord());
            }
        }
        if (proto.hasWarehouseMap()) {
            this.getWarehouseMap().mergeFromDb(proto.getWarehouseMap());
        } else {
            if (this.warehouseMap != null) {
                this.warehouseMap.mergeFromDb(proto.getWarehouseMap());
            }
        }
        if (proto.hasWareHouseLogBasicInfo()) {
            this.getWareHouseLogBasicInfo().mergeFromDb(proto.getWareHouseLogBasicInfo());
        } else {
            if (this.wareHouseLogBasicInfo != null) {
                this.wareHouseLogBasicInfo.mergeFromDb(proto.getWareHouseLogBasicInfo());
            }
        }
        if (proto.hasResourceInfos()) {
            this.getResourceInfos().mergeFromDb(proto.getResourceInfos());
        } else {
            if (this.resourceInfos != null) {
                this.resourceInfos.mergeFromDb(proto.getResourceInfos());
            }
        }
        this.markAll();
        return ClanResourcesProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanResources proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLastCalTsMs()) {
            this.setLastCalTsMs(proto.getLastCalTsMs());
            fieldCnt++;
        }
        if (proto.hasUsageRecord()) {
            this.getUsageRecord().mergeChangeFromDb(proto.getUsageRecord());
            fieldCnt++;
        }
        if (proto.hasWarehouseMap()) {
            this.getWarehouseMap().mergeChangeFromDb(proto.getWarehouseMap());
            fieldCnt++;
        }
        if (proto.hasWareHouseLogBasicInfo()) {
            this.getWareHouseLogBasicInfo().mergeChangeFromDb(proto.getWareHouseLogBasicInfo());
            fieldCnt++;
        }
        if (proto.hasResourceInfos()) {
            this.getResourceInfos().mergeChangeFromDb(proto.getResourceInfos());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanResources.Builder getCopySsBuilder() {
        final ClanResources.Builder builder = ClanResources.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanResources.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLastCalTsMs() != 0L) {
            builder.setLastCalTsMs(this.getLastCalTsMs());
            fieldCnt++;
        }  else if (builder.hasLastCalTsMs()) {
            // 清理LastCalTsMs
            builder.clearLastCalTsMs();
            fieldCnt++;
        }
        if (this.usageRecord != null) {
            Struct.Int64WarehouseUsageItemMap.Builder tmpBuilder = Struct.Int64WarehouseUsageItemMap.newBuilder();
            final int tmpFieldCnt = this.usageRecord.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUsageRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUsageRecord();
            }
        }  else if (builder.hasUsageRecord()) {
            // 清理UsageRecord
            builder.clearUsageRecord();
            fieldCnt++;
        }
        if (this.warehouseMap != null) {
            Clan.Int32WarehouseInfoMap.Builder tmpBuilder = Clan.Int32WarehouseInfoMap.newBuilder();
            final int tmpFieldCnt = this.warehouseMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWarehouseMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWarehouseMap();
            }
        }  else if (builder.hasWarehouseMap()) {
            // 清理WarehouseMap
            builder.clearWarehouseMap();
            fieldCnt++;
        }
        if (this.wareHouseLogBasicInfo != null) {
            Clan.ClanLogBasicInfo.Builder tmpBuilder = Clan.ClanLogBasicInfo.newBuilder();
            final int tmpFieldCnt = this.wareHouseLogBasicInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWareHouseLogBasicInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWareHouseLogBasicInfo();
            }
        }  else if (builder.hasWareHouseLogBasicInfo()) {
            // 清理WareHouseLogBasicInfo
            builder.clearWareHouseLogBasicInfo();
            fieldCnt++;
        }
        if (this.resourceInfos != null) {
            Struct.Int32SingleClanResourceInfoMap.Builder tmpBuilder = Struct.Int32SingleClanResourceInfoMap.newBuilder();
            final int tmpFieldCnt = this.resourceInfos.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResourceInfos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResourceInfos();
            }
        }  else if (builder.hasResourceInfos()) {
            // 清理ResourceInfos
            builder.clearResourceInfos();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanResources.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LASTCALTSMS)) {
            builder.setLastCalTsMs(this.getLastCalTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USAGERECORD) && this.usageRecord != null) {
            final boolean needClear = !builder.hasUsageRecord();
            final int tmpFieldCnt = this.usageRecord.copyChangeToSs(builder.getUsageRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUsageRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_WAREHOUSEMAP) && this.warehouseMap != null) {
            final boolean needClear = !builder.hasWarehouseMap();
            final int tmpFieldCnt = this.warehouseMap.copyChangeToSs(builder.getWarehouseMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWarehouseMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_WAREHOUSELOGBASICINFO) && this.wareHouseLogBasicInfo != null) {
            final boolean needClear = !builder.hasWareHouseLogBasicInfo();
            final int tmpFieldCnt = this.wareHouseLogBasicInfo.copyChangeToSs(builder.getWareHouseLogBasicInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWareHouseLogBasicInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESOURCEINFOS) && this.resourceInfos != null) {
            final boolean needClear = !builder.hasResourceInfos();
            final int tmpFieldCnt = this.resourceInfos.copyChangeToSs(builder.getResourceInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResourceInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanResources proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLastCalTsMs()) {
            this.innerSetLastCalTsMs(proto.getLastCalTsMs());
        } else {
            this.innerSetLastCalTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasUsageRecord()) {
            this.getUsageRecord().mergeFromSs(proto.getUsageRecord());
        } else {
            if (this.usageRecord != null) {
                this.usageRecord.mergeFromSs(proto.getUsageRecord());
            }
        }
        if (proto.hasWarehouseMap()) {
            this.getWarehouseMap().mergeFromSs(proto.getWarehouseMap());
        } else {
            if (this.warehouseMap != null) {
                this.warehouseMap.mergeFromSs(proto.getWarehouseMap());
            }
        }
        if (proto.hasWareHouseLogBasicInfo()) {
            this.getWareHouseLogBasicInfo().mergeFromSs(proto.getWareHouseLogBasicInfo());
        } else {
            if (this.wareHouseLogBasicInfo != null) {
                this.wareHouseLogBasicInfo.mergeFromSs(proto.getWareHouseLogBasicInfo());
            }
        }
        if (proto.hasResourceInfos()) {
            this.getResourceInfos().mergeFromSs(proto.getResourceInfos());
        } else {
            if (this.resourceInfos != null) {
                this.resourceInfos.mergeFromSs(proto.getResourceInfos());
            }
        }
        this.markAll();
        return ClanResourcesProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanResources proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLastCalTsMs()) {
            this.setLastCalTsMs(proto.getLastCalTsMs());
            fieldCnt++;
        }
        if (proto.hasUsageRecord()) {
            this.getUsageRecord().mergeChangeFromSs(proto.getUsageRecord());
            fieldCnt++;
        }
        if (proto.hasWarehouseMap()) {
            this.getWarehouseMap().mergeChangeFromSs(proto.getWarehouseMap());
            fieldCnt++;
        }
        if (proto.hasWareHouseLogBasicInfo()) {
            this.getWareHouseLogBasicInfo().mergeChangeFromSs(proto.getWareHouseLogBasicInfo());
            fieldCnt++;
        }
        if (proto.hasResourceInfos()) {
            this.getResourceInfos().mergeChangeFromSs(proto.getResourceInfos());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanResources.Builder builder = ClanResources.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_USAGERECORD) && this.usageRecord != null) {
            this.usageRecord.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_WAREHOUSEMAP) && this.warehouseMap != null) {
            this.warehouseMap.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_WAREHOUSELOGBASICINFO) && this.wareHouseLogBasicInfo != null) {
            this.wareHouseLogBasicInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RESOURCEINFOS) && this.resourceInfos != null) {
            this.resourceInfos.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.usageRecord != null) {
            this.usageRecord.markAll();
        }
        if (this.warehouseMap != null) {
            this.warehouseMap.markAll();
        }
        if (this.wareHouseLogBasicInfo != null) {
            this.wareHouseLogBasicInfo.markAll();
        }
        if (this.resourceInfos != null) {
            this.resourceInfos.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanResourcesProp)) {
            return false;
        }
        final ClanResourcesProp otherNode = (ClanResourcesProp) node;
        if (this.lastCalTsMs != otherNode.lastCalTsMs) {
            return false;
        }
        if (!this.getUsageRecord().compareDataTo(otherNode.getUsageRecord())) {
            return false;
        }
        if (!this.getWarehouseMap().compareDataTo(otherNode.getWarehouseMap())) {
            return false;
        }
        if (!this.getWareHouseLogBasicInfo().compareDataTo(otherNode.getWareHouseLogBasicInfo())) {
            return false;
        }
        if (!this.getResourceInfos().compareDataTo(otherNode.getResourceInfos())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
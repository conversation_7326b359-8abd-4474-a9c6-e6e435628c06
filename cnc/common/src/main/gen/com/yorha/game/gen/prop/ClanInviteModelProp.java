package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Clan.ClanInviteModel;
import com.yorha.proto.StructCommon;
import com.yorha.proto.ClanPB.ClanInviteModelPB;
import com.yorha.proto.StructCommonPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanInviteModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_INVITERECORDS = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int64InvitePlayerRecordMapProp inviteRecords = null;

    public ClanInviteModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanInviteModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get inviteRecords
     *
     * @return inviteRecords value
     */
    public Int64InvitePlayerRecordMapProp getInviteRecords() {
        if (this.inviteRecords == null) {
            this.inviteRecords = new Int64InvitePlayerRecordMapProp(this, FIELD_INDEX_INVITERECORDS);
        }
        return this.inviteRecords;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putInviteRecordsV(InvitePlayerRecordProp v) {
        this.getInviteRecords().put(v.getPlayerId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public InvitePlayerRecordProp addEmptyInviteRecords(Long k) {
        return this.getInviteRecords().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getInviteRecordsSize() {
        if (this.inviteRecords == null) {
            return 0;
        }
        return this.inviteRecords.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isInviteRecordsEmpty() {
        if (this.inviteRecords == null) {
            return true;
        }
        return this.inviteRecords.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public InvitePlayerRecordProp getInviteRecordsV(Long k) {
        if (this.inviteRecords == null || !this.inviteRecords.containsKey(k)) {
            return null;
        }
        return this.inviteRecords.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearInviteRecords() {
        if (this.inviteRecords != null) {
            this.inviteRecords.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeInviteRecordsV(Long k) {
        if (this.inviteRecords != null) {
            this.inviteRecords.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanInviteModelPB.Builder getCopyCsBuilder() {
        final ClanInviteModelPB.Builder builder = ClanInviteModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanInviteModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.inviteRecords != null) {
            StructCommonPB.Int64InvitePlayerRecordMapPB.Builder tmpBuilder = StructCommonPB.Int64InvitePlayerRecordMapPB.newBuilder();
            final int tmpFieldCnt = this.inviteRecords.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInviteRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInviteRecords();
            }
        }  else if (builder.hasInviteRecords()) {
            // 清理InviteRecords
            builder.clearInviteRecords();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanInviteModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INVITERECORDS) && this.inviteRecords != null) {
            final boolean needClear = !builder.hasInviteRecords();
            final int tmpFieldCnt = this.inviteRecords.copyChangeToCs(builder.getInviteRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInviteRecords();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanInviteModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INVITERECORDS) && this.inviteRecords != null) {
            final boolean needClear = !builder.hasInviteRecords();
            final int tmpFieldCnt = this.inviteRecords.copyChangeToAndClearDeleteKeysCs(builder.getInviteRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInviteRecords();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanInviteModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasInviteRecords()) {
            this.getInviteRecords().mergeFromCs(proto.getInviteRecords());
        } else {
            if (this.inviteRecords != null) {
                this.inviteRecords.mergeFromCs(proto.getInviteRecords());
            }
        }
        this.markAll();
        return ClanInviteModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanInviteModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasInviteRecords()) {
            this.getInviteRecords().mergeChangeFromCs(proto.getInviteRecords());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanInviteModel.Builder getCopyDbBuilder() {
        final ClanInviteModel.Builder builder = ClanInviteModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanInviteModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.inviteRecords != null) {
            StructCommon.Int64InvitePlayerRecordMap.Builder tmpBuilder = StructCommon.Int64InvitePlayerRecordMap.newBuilder();
            final int tmpFieldCnt = this.inviteRecords.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInviteRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInviteRecords();
            }
        }  else if (builder.hasInviteRecords()) {
            // 清理InviteRecords
            builder.clearInviteRecords();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanInviteModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INVITERECORDS) && this.inviteRecords != null) {
            final boolean needClear = !builder.hasInviteRecords();
            final int tmpFieldCnt = this.inviteRecords.copyChangeToDb(builder.getInviteRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInviteRecords();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanInviteModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasInviteRecords()) {
            this.getInviteRecords().mergeFromDb(proto.getInviteRecords());
        } else {
            if (this.inviteRecords != null) {
                this.inviteRecords.mergeFromDb(proto.getInviteRecords());
            }
        }
        this.markAll();
        return ClanInviteModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanInviteModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasInviteRecords()) {
            this.getInviteRecords().mergeChangeFromDb(proto.getInviteRecords());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanInviteModel.Builder getCopySsBuilder() {
        final ClanInviteModel.Builder builder = ClanInviteModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanInviteModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.inviteRecords != null) {
            StructCommon.Int64InvitePlayerRecordMap.Builder tmpBuilder = StructCommon.Int64InvitePlayerRecordMap.newBuilder();
            final int tmpFieldCnt = this.inviteRecords.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInviteRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInviteRecords();
            }
        }  else if (builder.hasInviteRecords()) {
            // 清理InviteRecords
            builder.clearInviteRecords();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanInviteModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INVITERECORDS) && this.inviteRecords != null) {
            final boolean needClear = !builder.hasInviteRecords();
            final int tmpFieldCnt = this.inviteRecords.copyChangeToSs(builder.getInviteRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInviteRecords();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanInviteModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasInviteRecords()) {
            this.getInviteRecords().mergeFromSs(proto.getInviteRecords());
        } else {
            if (this.inviteRecords != null) {
                this.inviteRecords.mergeFromSs(proto.getInviteRecords());
            }
        }
        this.markAll();
        return ClanInviteModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanInviteModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasInviteRecords()) {
            this.getInviteRecords().mergeChangeFromSs(proto.getInviteRecords());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanInviteModel.Builder builder = ClanInviteModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_INVITERECORDS) && this.inviteRecords != null) {
            this.inviteRecords.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.inviteRecords != null) {
            this.inviteRecords.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanInviteModelProp)) {
            return false;
        }
        final ClanInviteModelProp otherNode = (ClanInviteModelProp) node;
        if (!this.getInviteRecords().compareDataTo(otherNode.getInviteRecords())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.ScenePlayerMonsterModel;
import com.yorha.proto.PlayerPB.ScenePlayerMonsterModelPB;


/**
 * <AUTHOR> auto gen
 */
public class ScenePlayerMonsterModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TODAYFREEHITCOUNT = 0;
    public static final int FIELD_INDEX_COUNTLASTUPDATETSMS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int todayFreeHitCount = Constant.DEFAULT_INT_VALUE;
    private long countLastUpdateTsMs = Constant.DEFAULT_LONG_VALUE;

    public ScenePlayerMonsterModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ScenePlayerMonsterModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get todayFreeHitCount
     *
     * @return todayFreeHitCount value
     */
    public int getTodayFreeHitCount() {
        return this.todayFreeHitCount;
    }

    /**
     * set todayFreeHitCount && set marked
     *
     * @param todayFreeHitCount new value
     * @return current object
     */
    public ScenePlayerMonsterModelProp setTodayFreeHitCount(int todayFreeHitCount) {
        if (this.todayFreeHitCount != todayFreeHitCount) {
            this.mark(FIELD_INDEX_TODAYFREEHITCOUNT);
            this.todayFreeHitCount = todayFreeHitCount;
        }
        return this;
    }

    /**
     * inner set todayFreeHitCount
     *
     * @param todayFreeHitCount new value
     */
    private void innerSetTodayFreeHitCount(int todayFreeHitCount) {
        this.todayFreeHitCount = todayFreeHitCount;
    }

    /**
     * get countLastUpdateTsMs
     *
     * @return countLastUpdateTsMs value
     */
    public long getCountLastUpdateTsMs() {
        return this.countLastUpdateTsMs;
    }

    /**
     * set countLastUpdateTsMs && set marked
     *
     * @param countLastUpdateTsMs new value
     * @return current object
     */
    public ScenePlayerMonsterModelProp setCountLastUpdateTsMs(long countLastUpdateTsMs) {
        if (this.countLastUpdateTsMs != countLastUpdateTsMs) {
            this.mark(FIELD_INDEX_COUNTLASTUPDATETSMS);
            this.countLastUpdateTsMs = countLastUpdateTsMs;
        }
        return this;
    }

    /**
     * inner set countLastUpdateTsMs
     *
     * @param countLastUpdateTsMs new value
     */
    private void innerSetCountLastUpdateTsMs(long countLastUpdateTsMs) {
        this.countLastUpdateTsMs = countLastUpdateTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerMonsterModelPB.Builder getCopyCsBuilder() {
        final ScenePlayerMonsterModelPB.Builder builder = ScenePlayerMonsterModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ScenePlayerMonsterModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTodayFreeHitCount() != 0) {
            builder.setTodayFreeHitCount(this.getTodayFreeHitCount());
            fieldCnt++;
        }  else if (builder.hasTodayFreeHitCount()) {
            // 清理TodayFreeHitCount
            builder.clearTodayFreeHitCount();
            fieldCnt++;
        }
        if (this.getCountLastUpdateTsMs() != 0L) {
            builder.setCountLastUpdateTsMs(this.getCountLastUpdateTsMs());
            fieldCnt++;
        }  else if (builder.hasCountLastUpdateTsMs()) {
            // 清理CountLastUpdateTsMs
            builder.clearCountLastUpdateTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ScenePlayerMonsterModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TODAYFREEHITCOUNT)) {
            builder.setTodayFreeHitCount(this.getTodayFreeHitCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNTLASTUPDATETSMS)) {
            builder.setCountLastUpdateTsMs(this.getCountLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ScenePlayerMonsterModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TODAYFREEHITCOUNT)) {
            builder.setTodayFreeHitCount(this.getTodayFreeHitCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNTLASTUPDATETSMS)) {
            builder.setCountLastUpdateTsMs(this.getCountLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ScenePlayerMonsterModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTodayFreeHitCount()) {
            this.innerSetTodayFreeHitCount(proto.getTodayFreeHitCount());
        } else {
            this.innerSetTodayFreeHitCount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCountLastUpdateTsMs()) {
            this.innerSetCountLastUpdateTsMs(proto.getCountLastUpdateTsMs());
        } else {
            this.innerSetCountLastUpdateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ScenePlayerMonsterModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ScenePlayerMonsterModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTodayFreeHitCount()) {
            this.setTodayFreeHitCount(proto.getTodayFreeHitCount());
            fieldCnt++;
        }
        if (proto.hasCountLastUpdateTsMs()) {
            this.setCountLastUpdateTsMs(proto.getCountLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerMonsterModel.Builder getCopyDbBuilder() {
        final ScenePlayerMonsterModel.Builder builder = ScenePlayerMonsterModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ScenePlayerMonsterModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTodayFreeHitCount() != 0) {
            builder.setTodayFreeHitCount(this.getTodayFreeHitCount());
            fieldCnt++;
        }  else if (builder.hasTodayFreeHitCount()) {
            // 清理TodayFreeHitCount
            builder.clearTodayFreeHitCount();
            fieldCnt++;
        }
        if (this.getCountLastUpdateTsMs() != 0L) {
            builder.setCountLastUpdateTsMs(this.getCountLastUpdateTsMs());
            fieldCnt++;
        }  else if (builder.hasCountLastUpdateTsMs()) {
            // 清理CountLastUpdateTsMs
            builder.clearCountLastUpdateTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ScenePlayerMonsterModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TODAYFREEHITCOUNT)) {
            builder.setTodayFreeHitCount(this.getTodayFreeHitCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNTLASTUPDATETSMS)) {
            builder.setCountLastUpdateTsMs(this.getCountLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ScenePlayerMonsterModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTodayFreeHitCount()) {
            this.innerSetTodayFreeHitCount(proto.getTodayFreeHitCount());
        } else {
            this.innerSetTodayFreeHitCount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCountLastUpdateTsMs()) {
            this.innerSetCountLastUpdateTsMs(proto.getCountLastUpdateTsMs());
        } else {
            this.innerSetCountLastUpdateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ScenePlayerMonsterModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ScenePlayerMonsterModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTodayFreeHitCount()) {
            this.setTodayFreeHitCount(proto.getTodayFreeHitCount());
            fieldCnt++;
        }
        if (proto.hasCountLastUpdateTsMs()) {
            this.setCountLastUpdateTsMs(proto.getCountLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerMonsterModel.Builder getCopySsBuilder() {
        final ScenePlayerMonsterModel.Builder builder = ScenePlayerMonsterModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ScenePlayerMonsterModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTodayFreeHitCount() != 0) {
            builder.setTodayFreeHitCount(this.getTodayFreeHitCount());
            fieldCnt++;
        }  else if (builder.hasTodayFreeHitCount()) {
            // 清理TodayFreeHitCount
            builder.clearTodayFreeHitCount();
            fieldCnt++;
        }
        if (this.getCountLastUpdateTsMs() != 0L) {
            builder.setCountLastUpdateTsMs(this.getCountLastUpdateTsMs());
            fieldCnt++;
        }  else if (builder.hasCountLastUpdateTsMs()) {
            // 清理CountLastUpdateTsMs
            builder.clearCountLastUpdateTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ScenePlayerMonsterModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TODAYFREEHITCOUNT)) {
            builder.setTodayFreeHitCount(this.getTodayFreeHitCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNTLASTUPDATETSMS)) {
            builder.setCountLastUpdateTsMs(this.getCountLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ScenePlayerMonsterModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTodayFreeHitCount()) {
            this.innerSetTodayFreeHitCount(proto.getTodayFreeHitCount());
        } else {
            this.innerSetTodayFreeHitCount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCountLastUpdateTsMs()) {
            this.innerSetCountLastUpdateTsMs(proto.getCountLastUpdateTsMs());
        } else {
            this.innerSetCountLastUpdateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ScenePlayerMonsterModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ScenePlayerMonsterModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTodayFreeHitCount()) {
            this.setTodayFreeHitCount(proto.getTodayFreeHitCount());
            fieldCnt++;
        }
        if (proto.hasCountLastUpdateTsMs()) {
            this.setCountLastUpdateTsMs(proto.getCountLastUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ScenePlayerMonsterModel.Builder builder = ScenePlayerMonsterModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ScenePlayerMonsterModelProp)) {
            return false;
        }
        final ScenePlayerMonsterModelProp otherNode = (ScenePlayerMonsterModelProp) node;
        if (this.todayFreeHitCount != otherNode.todayFreeHitCount) {
            return false;
        }
        if (this.countLastUpdateTsMs != otherNode.countLastUpdateTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
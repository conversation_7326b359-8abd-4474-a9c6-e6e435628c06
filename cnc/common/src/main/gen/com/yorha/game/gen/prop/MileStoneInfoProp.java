package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Struct.MileStoneInfo;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.MileStoneInfoPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class MileStoneInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_MILESTONEID = 0;
    public static final int FIELD_INDEX_PROCESS = 1;
    public static final int FIELD_INDEX_STATUS = 2;
    public static final int FIELD_INDEX_ENDTSMS = 3;
    public static final int FIELD_INDEX_RANKINFO = 4;
    public static final int FIELD_INDEX_REWARDINFO = 5;
    public static final int FIELD_INDEX_GAMEPLAYEROPENDATA = 6;
    public static final int FIELD_INDEX_ISSUCCESS = 7;
    public static final int FIELD_INDEX_STARTTSMS = 8;

    public static final int FIELD_COUNT = 9;

    private long markBits0 = 0L;

    private int mileStoneId = Constant.DEFAULT_INT_VALUE;
    private long process = Constant.DEFAULT_LONG_VALUE;
    private MileStoneStatus status = MileStoneStatus.forNumber(0);
    private long endTsMs = Constant.DEFAULT_LONG_VALUE;
    private MileStoneRankProp rankInfo = null;
    private MileStoneRewardProp rewardInfo = null;
    private Int32MileStoneGamePlayerOpenDataMapProp gamePlayerOpenData = null;
    private boolean isSuccess = Constant.DEFAULT_BOOLEAN_VALUE;
    private long startTsMs = Constant.DEFAULT_LONG_VALUE;

    public MileStoneInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MileStoneInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get mileStoneId
     *
     * @return mileStoneId value
     */
    public int getMileStoneId() {
        return this.mileStoneId;
    }

    /**
     * set mileStoneId && set marked
     *
     * @param mileStoneId new value
     * @return current object
     */
    public MileStoneInfoProp setMileStoneId(int mileStoneId) {
        if (this.mileStoneId != mileStoneId) {
            this.mark(FIELD_INDEX_MILESTONEID);
            this.mileStoneId = mileStoneId;
        }
        return this;
    }

    /**
     * inner set mileStoneId
     *
     * @param mileStoneId new value
     */
    private void innerSetMileStoneId(int mileStoneId) {
        this.mileStoneId = mileStoneId;
    }

    /**
     * get process
     *
     * @return process value
     */
    public long getProcess() {
        return this.process;
    }

    /**
     * set process && set marked
     *
     * @param process new value
     * @return current object
     */
    public MileStoneInfoProp setProcess(long process) {
        if (this.process != process) {
            this.mark(FIELD_INDEX_PROCESS);
            this.process = process;
        }
        return this;
    }

    /**
     * inner set process
     *
     * @param process new value
     */
    private void innerSetProcess(long process) {
        this.process = process;
    }

    /**
     * get status
     *
     * @return status value
     */
    public MileStoneStatus getStatus() {
        return this.status;
    }

    /**
     * set status && set marked
     *
     * @param status new value
     * @return current object
     */
    public MileStoneInfoProp setStatus(MileStoneStatus status) {
        if (status == null) {
            throw new NullPointerException();
        }
        if (this.status != status) {
            this.mark(FIELD_INDEX_STATUS);
            this.status = status;
        }
        return this;
    }

    /**
     * inner set status
     *
     * @param status new value
     */
    private void innerSetStatus(MileStoneStatus status) {
        this.status = status;
    }

    /**
     * get endTsMs
     *
     * @return endTsMs value
     */
    public long getEndTsMs() {
        return this.endTsMs;
    }

    /**
     * set endTsMs && set marked
     *
     * @param endTsMs new value
     * @return current object
     */
    public MileStoneInfoProp setEndTsMs(long endTsMs) {
        if (this.endTsMs != endTsMs) {
            this.mark(FIELD_INDEX_ENDTSMS);
            this.endTsMs = endTsMs;
        }
        return this;
    }

    /**
     * inner set endTsMs
     *
     * @param endTsMs new value
     */
    private void innerSetEndTsMs(long endTsMs) {
        this.endTsMs = endTsMs;
    }

    /**
     * get rankInfo
     *
     * @return rankInfo value
     */
    public MileStoneRankProp getRankInfo() {
        if (this.rankInfo == null) {
            this.rankInfo = new MileStoneRankProp(this, FIELD_INDEX_RANKINFO);
        }
        return this.rankInfo;
    }

    /**
     * get rewardInfo
     *
     * @return rewardInfo value
     */
    public MileStoneRewardProp getRewardInfo() {
        if (this.rewardInfo == null) {
            this.rewardInfo = new MileStoneRewardProp(this, FIELD_INDEX_REWARDINFO);
        }
        return this.rewardInfo;
    }

    /**
     * get gamePlayerOpenData
     *
     * @return gamePlayerOpenData value
     */
    public Int32MileStoneGamePlayerOpenDataMapProp getGamePlayerOpenData() {
        if (this.gamePlayerOpenData == null) {
            this.gamePlayerOpenData = new Int32MileStoneGamePlayerOpenDataMapProp(this, FIELD_INDEX_GAMEPLAYEROPENDATA);
        }
        return this.gamePlayerOpenData;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putGamePlayerOpenDataV(MileStoneGamePlayerOpenDataProp v) {
        this.getGamePlayerOpenData().put(v.getGamePlayerType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public MileStoneGamePlayerOpenDataProp addEmptyGamePlayerOpenData(Integer k) {
        return this.getGamePlayerOpenData().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getGamePlayerOpenDataSize() {
        if (this.gamePlayerOpenData == null) {
            return 0;
        }
        return this.gamePlayerOpenData.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isGamePlayerOpenDataEmpty() {
        if (this.gamePlayerOpenData == null) {
            return true;
        }
        return this.gamePlayerOpenData.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public MileStoneGamePlayerOpenDataProp getGamePlayerOpenDataV(Integer k) {
        if (this.gamePlayerOpenData == null || !this.gamePlayerOpenData.containsKey(k)) {
            return null;
        }
        return this.gamePlayerOpenData.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearGamePlayerOpenData() {
        if (this.gamePlayerOpenData != null) {
            this.gamePlayerOpenData.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeGamePlayerOpenDataV(Integer k) {
        if (this.gamePlayerOpenData != null) {
            this.gamePlayerOpenData.remove(k);
        }
    }
    /**
     * get isSuccess
     *
     * @return isSuccess value
     */
    public boolean getIsSuccess() {
        return this.isSuccess;
    }

    /**
     * set isSuccess && set marked
     *
     * @param isSuccess new value
     * @return current object
     */
    public MileStoneInfoProp setIsSuccess(boolean isSuccess) {
        if (this.isSuccess != isSuccess) {
            this.mark(FIELD_INDEX_ISSUCCESS);
            this.isSuccess = isSuccess;
        }
        return this;
    }

    /**
     * inner set isSuccess
     *
     * @param isSuccess new value
     */
    private void innerSetIsSuccess(boolean isSuccess) {
        this.isSuccess = isSuccess;
    }

    /**
     * get startTsMs
     *
     * @return startTsMs value
     */
    public long getStartTsMs() {
        return this.startTsMs;
    }

    /**
     * set startTsMs && set marked
     *
     * @param startTsMs new value
     * @return current object
     */
    public MileStoneInfoProp setStartTsMs(long startTsMs) {
        if (this.startTsMs != startTsMs) {
            this.mark(FIELD_INDEX_STARTTSMS);
            this.startTsMs = startTsMs;
        }
        return this;
    }

    /**
     * inner set startTsMs
     *
     * @param startTsMs new value
     */
    private void innerSetStartTsMs(long startTsMs) {
        this.startTsMs = startTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneInfoPB.Builder getCopyCsBuilder() {
        final MileStoneInfoPB.Builder builder = MileStoneInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MileStoneInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMileStoneId() != 0) {
            builder.setMileStoneId(this.getMileStoneId());
            fieldCnt++;
        }  else if (builder.hasMileStoneId()) {
            // 清理MileStoneId
            builder.clearMileStoneId();
            fieldCnt++;
        }
        if (this.getProcess() != 0L) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }  else if (builder.hasProcess()) {
            // 清理Process
            builder.clearProcess();
            fieldCnt++;
        }
        if (this.getStatus() != MileStoneStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.getEndTsMs() != 0L) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }  else if (builder.hasEndTsMs()) {
            // 清理EndTsMs
            builder.clearEndTsMs();
            fieldCnt++;
        }
        if (this.rankInfo != null) {
            StructPB.MileStoneRankPB.Builder tmpBuilder = StructPB.MileStoneRankPB.newBuilder();
            final int tmpFieldCnt = this.rankInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRankInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRankInfo();
            }
        }  else if (builder.hasRankInfo()) {
            // 清理RankInfo
            builder.clearRankInfo();
            fieldCnt++;
        }
        if (this.rewardInfo != null) {
            StructPB.MileStoneRewardPB.Builder tmpBuilder = StructPB.MileStoneRewardPB.newBuilder();
            final int tmpFieldCnt = this.rewardInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardInfo();
            }
        }  else if (builder.hasRewardInfo()) {
            // 清理RewardInfo
            builder.clearRewardInfo();
            fieldCnt++;
        }
        if (this.gamePlayerOpenData != null) {
            StructPB.Int32MileStoneGamePlayerOpenDataMapPB.Builder tmpBuilder = StructPB.Int32MileStoneGamePlayerOpenDataMapPB.newBuilder();
            final int tmpFieldCnt = this.gamePlayerOpenData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGamePlayerOpenData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGamePlayerOpenData();
            }
        }  else if (builder.hasGamePlayerOpenData()) {
            // 清理GamePlayerOpenData
            builder.clearGamePlayerOpenData();
            fieldCnt++;
        }
        if (this.getIsSuccess()) {
            builder.setIsSuccess(this.getIsSuccess());
            fieldCnt++;
        }  else if (builder.hasIsSuccess()) {
            // 清理IsSuccess
            builder.clearIsSuccess();
            fieldCnt++;
        }
        if (this.getStartTsMs() != 0L) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStartTsMs()) {
            // 清理StartTsMs
            builder.clearStartTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MileStoneInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MILESTONEID)) {
            builder.setMileStoneId(this.getMileStoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PROCESS)) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RANKINFO) && this.rankInfo != null) {
            final boolean needClear = !builder.hasRankInfo();
            final int tmpFieldCnt = this.rankInfo.copyChangeToCs(builder.getRankInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRankInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDINFO) && this.rewardInfo != null) {
            final boolean needClear = !builder.hasRewardInfo();
            final int tmpFieldCnt = this.rewardInfo.copyChangeToCs(builder.getRewardInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_GAMEPLAYEROPENDATA) && this.gamePlayerOpenData != null) {
            final boolean needClear = !builder.hasGamePlayerOpenData();
            final int tmpFieldCnt = this.gamePlayerOpenData.copyChangeToCs(builder.getGamePlayerOpenDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGamePlayerOpenData();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISSUCCESS)) {
            builder.setIsSuccess(this.getIsSuccess());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MileStoneInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MILESTONEID)) {
            builder.setMileStoneId(this.getMileStoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PROCESS)) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RANKINFO) && this.rankInfo != null) {
            final boolean needClear = !builder.hasRankInfo();
            final int tmpFieldCnt = this.rankInfo.copyChangeToAndClearDeleteKeysCs(builder.getRankInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRankInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDINFO) && this.rewardInfo != null) {
            final boolean needClear = !builder.hasRewardInfo();
            final int tmpFieldCnt = this.rewardInfo.copyChangeToAndClearDeleteKeysCs(builder.getRewardInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_GAMEPLAYEROPENDATA) && this.gamePlayerOpenData != null) {
            final boolean needClear = !builder.hasGamePlayerOpenData();
            final int tmpFieldCnt = this.gamePlayerOpenData.copyChangeToAndClearDeleteKeysCs(builder.getGamePlayerOpenDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGamePlayerOpenData();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISSUCCESS)) {
            builder.setIsSuccess(this.getIsSuccess());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MileStoneInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMileStoneId()) {
            this.innerSetMileStoneId(proto.getMileStoneId());
        } else {
            this.innerSetMileStoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasProcess()) {
            this.innerSetProcess(proto.getProcess());
        } else {
            this.innerSetProcess(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(MileStoneStatus.forNumber(0));
        }
        if (proto.hasEndTsMs()) {
            this.innerSetEndTsMs(proto.getEndTsMs());
        } else {
            this.innerSetEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRankInfo()) {
            this.getRankInfo().mergeFromCs(proto.getRankInfo());
        } else {
            if (this.rankInfo != null) {
                this.rankInfo.mergeFromCs(proto.getRankInfo());
            }
        }
        if (proto.hasRewardInfo()) {
            this.getRewardInfo().mergeFromCs(proto.getRewardInfo());
        } else {
            if (this.rewardInfo != null) {
                this.rewardInfo.mergeFromCs(proto.getRewardInfo());
            }
        }
        if (proto.hasGamePlayerOpenData()) {
            this.getGamePlayerOpenData().mergeFromCs(proto.getGamePlayerOpenData());
        } else {
            if (this.gamePlayerOpenData != null) {
                this.gamePlayerOpenData.mergeFromCs(proto.getGamePlayerOpenData());
            }
        }
        if (proto.hasIsSuccess()) {
            this.innerSetIsSuccess(proto.getIsSuccess());
        } else {
            this.innerSetIsSuccess(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasStartTsMs()) {
            this.innerSetStartTsMs(proto.getStartTsMs());
        } else {
            this.innerSetStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MileStoneInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MileStoneInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMileStoneId()) {
            this.setMileStoneId(proto.getMileStoneId());
            fieldCnt++;
        }
        if (proto.hasProcess()) {
            this.setProcess(proto.getProcess());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasEndTsMs()) {
            this.setEndTsMs(proto.getEndTsMs());
            fieldCnt++;
        }
        if (proto.hasRankInfo()) {
            this.getRankInfo().mergeChangeFromCs(proto.getRankInfo());
            fieldCnt++;
        }
        if (proto.hasRewardInfo()) {
            this.getRewardInfo().mergeChangeFromCs(proto.getRewardInfo());
            fieldCnt++;
        }
        if (proto.hasGamePlayerOpenData()) {
            this.getGamePlayerOpenData().mergeChangeFromCs(proto.getGamePlayerOpenData());
            fieldCnt++;
        }
        if (proto.hasIsSuccess()) {
            this.setIsSuccess(proto.getIsSuccess());
            fieldCnt++;
        }
        if (proto.hasStartTsMs()) {
            this.setStartTsMs(proto.getStartTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneInfo.Builder getCopyDbBuilder() {
        final MileStoneInfo.Builder builder = MileStoneInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MileStoneInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMileStoneId() != 0) {
            builder.setMileStoneId(this.getMileStoneId());
            fieldCnt++;
        }  else if (builder.hasMileStoneId()) {
            // 清理MileStoneId
            builder.clearMileStoneId();
            fieldCnt++;
        }
        if (this.getProcess() != 0L) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }  else if (builder.hasProcess()) {
            // 清理Process
            builder.clearProcess();
            fieldCnt++;
        }
        if (this.getStatus() != MileStoneStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.getEndTsMs() != 0L) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }  else if (builder.hasEndTsMs()) {
            // 清理EndTsMs
            builder.clearEndTsMs();
            fieldCnt++;
        }
        if (this.rankInfo != null) {
            Struct.MileStoneRank.Builder tmpBuilder = Struct.MileStoneRank.newBuilder();
            final int tmpFieldCnt = this.rankInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRankInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRankInfo();
            }
        }  else if (builder.hasRankInfo()) {
            // 清理RankInfo
            builder.clearRankInfo();
            fieldCnt++;
        }
        if (this.rewardInfo != null) {
            Struct.MileStoneReward.Builder tmpBuilder = Struct.MileStoneReward.newBuilder();
            final int tmpFieldCnt = this.rewardInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardInfo();
            }
        }  else if (builder.hasRewardInfo()) {
            // 清理RewardInfo
            builder.clearRewardInfo();
            fieldCnt++;
        }
        if (this.gamePlayerOpenData != null) {
            Struct.Int32MileStoneGamePlayerOpenDataMap.Builder tmpBuilder = Struct.Int32MileStoneGamePlayerOpenDataMap.newBuilder();
            final int tmpFieldCnt = this.gamePlayerOpenData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGamePlayerOpenData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGamePlayerOpenData();
            }
        }  else if (builder.hasGamePlayerOpenData()) {
            // 清理GamePlayerOpenData
            builder.clearGamePlayerOpenData();
            fieldCnt++;
        }
        if (this.getIsSuccess()) {
            builder.setIsSuccess(this.getIsSuccess());
            fieldCnt++;
        }  else if (builder.hasIsSuccess()) {
            // 清理IsSuccess
            builder.clearIsSuccess();
            fieldCnt++;
        }
        if (this.getStartTsMs() != 0L) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStartTsMs()) {
            // 清理StartTsMs
            builder.clearStartTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MileStoneInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MILESTONEID)) {
            builder.setMileStoneId(this.getMileStoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PROCESS)) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RANKINFO) && this.rankInfo != null) {
            final boolean needClear = !builder.hasRankInfo();
            final int tmpFieldCnt = this.rankInfo.copyChangeToDb(builder.getRankInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRankInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDINFO) && this.rewardInfo != null) {
            final boolean needClear = !builder.hasRewardInfo();
            final int tmpFieldCnt = this.rewardInfo.copyChangeToDb(builder.getRewardInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_GAMEPLAYEROPENDATA) && this.gamePlayerOpenData != null) {
            final boolean needClear = !builder.hasGamePlayerOpenData();
            final int tmpFieldCnt = this.gamePlayerOpenData.copyChangeToDb(builder.getGamePlayerOpenDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGamePlayerOpenData();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISSUCCESS)) {
            builder.setIsSuccess(this.getIsSuccess());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MileStoneInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMileStoneId()) {
            this.innerSetMileStoneId(proto.getMileStoneId());
        } else {
            this.innerSetMileStoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasProcess()) {
            this.innerSetProcess(proto.getProcess());
        } else {
            this.innerSetProcess(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(MileStoneStatus.forNumber(0));
        }
        if (proto.hasEndTsMs()) {
            this.innerSetEndTsMs(proto.getEndTsMs());
        } else {
            this.innerSetEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRankInfo()) {
            this.getRankInfo().mergeFromDb(proto.getRankInfo());
        } else {
            if (this.rankInfo != null) {
                this.rankInfo.mergeFromDb(proto.getRankInfo());
            }
        }
        if (proto.hasRewardInfo()) {
            this.getRewardInfo().mergeFromDb(proto.getRewardInfo());
        } else {
            if (this.rewardInfo != null) {
                this.rewardInfo.mergeFromDb(proto.getRewardInfo());
            }
        }
        if (proto.hasGamePlayerOpenData()) {
            this.getGamePlayerOpenData().mergeFromDb(proto.getGamePlayerOpenData());
        } else {
            if (this.gamePlayerOpenData != null) {
                this.gamePlayerOpenData.mergeFromDb(proto.getGamePlayerOpenData());
            }
        }
        if (proto.hasIsSuccess()) {
            this.innerSetIsSuccess(proto.getIsSuccess());
        } else {
            this.innerSetIsSuccess(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasStartTsMs()) {
            this.innerSetStartTsMs(proto.getStartTsMs());
        } else {
            this.innerSetStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MileStoneInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MileStoneInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMileStoneId()) {
            this.setMileStoneId(proto.getMileStoneId());
            fieldCnt++;
        }
        if (proto.hasProcess()) {
            this.setProcess(proto.getProcess());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasEndTsMs()) {
            this.setEndTsMs(proto.getEndTsMs());
            fieldCnt++;
        }
        if (proto.hasRankInfo()) {
            this.getRankInfo().mergeChangeFromDb(proto.getRankInfo());
            fieldCnt++;
        }
        if (proto.hasRewardInfo()) {
            this.getRewardInfo().mergeChangeFromDb(proto.getRewardInfo());
            fieldCnt++;
        }
        if (proto.hasGamePlayerOpenData()) {
            this.getGamePlayerOpenData().mergeChangeFromDb(proto.getGamePlayerOpenData());
            fieldCnt++;
        }
        if (proto.hasIsSuccess()) {
            this.setIsSuccess(proto.getIsSuccess());
            fieldCnt++;
        }
        if (proto.hasStartTsMs()) {
            this.setStartTsMs(proto.getStartTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneInfo.Builder getCopySsBuilder() {
        final MileStoneInfo.Builder builder = MileStoneInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MileStoneInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMileStoneId() != 0) {
            builder.setMileStoneId(this.getMileStoneId());
            fieldCnt++;
        }  else if (builder.hasMileStoneId()) {
            // 清理MileStoneId
            builder.clearMileStoneId();
            fieldCnt++;
        }
        if (this.getProcess() != 0L) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }  else if (builder.hasProcess()) {
            // 清理Process
            builder.clearProcess();
            fieldCnt++;
        }
        if (this.getStatus() != MileStoneStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.getEndTsMs() != 0L) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }  else if (builder.hasEndTsMs()) {
            // 清理EndTsMs
            builder.clearEndTsMs();
            fieldCnt++;
        }
        if (this.rankInfo != null) {
            Struct.MileStoneRank.Builder tmpBuilder = Struct.MileStoneRank.newBuilder();
            final int tmpFieldCnt = this.rankInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRankInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRankInfo();
            }
        }  else if (builder.hasRankInfo()) {
            // 清理RankInfo
            builder.clearRankInfo();
            fieldCnt++;
        }
        if (this.rewardInfo != null) {
            Struct.MileStoneReward.Builder tmpBuilder = Struct.MileStoneReward.newBuilder();
            final int tmpFieldCnt = this.rewardInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardInfo();
            }
        }  else if (builder.hasRewardInfo()) {
            // 清理RewardInfo
            builder.clearRewardInfo();
            fieldCnt++;
        }
        if (this.gamePlayerOpenData != null) {
            Struct.Int32MileStoneGamePlayerOpenDataMap.Builder tmpBuilder = Struct.Int32MileStoneGamePlayerOpenDataMap.newBuilder();
            final int tmpFieldCnt = this.gamePlayerOpenData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGamePlayerOpenData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGamePlayerOpenData();
            }
        }  else if (builder.hasGamePlayerOpenData()) {
            // 清理GamePlayerOpenData
            builder.clearGamePlayerOpenData();
            fieldCnt++;
        }
        if (this.getIsSuccess()) {
            builder.setIsSuccess(this.getIsSuccess());
            fieldCnt++;
        }  else if (builder.hasIsSuccess()) {
            // 清理IsSuccess
            builder.clearIsSuccess();
            fieldCnt++;
        }
        if (this.getStartTsMs() != 0L) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStartTsMs()) {
            // 清理StartTsMs
            builder.clearStartTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MileStoneInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MILESTONEID)) {
            builder.setMileStoneId(this.getMileStoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PROCESS)) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTSMS)) {
            builder.setEndTsMs(this.getEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RANKINFO) && this.rankInfo != null) {
            final boolean needClear = !builder.hasRankInfo();
            final int tmpFieldCnt = this.rankInfo.copyChangeToSs(builder.getRankInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRankInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDINFO) && this.rewardInfo != null) {
            final boolean needClear = !builder.hasRewardInfo();
            final int tmpFieldCnt = this.rewardInfo.copyChangeToSs(builder.getRewardInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_GAMEPLAYEROPENDATA) && this.gamePlayerOpenData != null) {
            final boolean needClear = !builder.hasGamePlayerOpenData();
            final int tmpFieldCnt = this.gamePlayerOpenData.copyChangeToSs(builder.getGamePlayerOpenDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGamePlayerOpenData();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISSUCCESS)) {
            builder.setIsSuccess(this.getIsSuccess());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MileStoneInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMileStoneId()) {
            this.innerSetMileStoneId(proto.getMileStoneId());
        } else {
            this.innerSetMileStoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasProcess()) {
            this.innerSetProcess(proto.getProcess());
        } else {
            this.innerSetProcess(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(MileStoneStatus.forNumber(0));
        }
        if (proto.hasEndTsMs()) {
            this.innerSetEndTsMs(proto.getEndTsMs());
        } else {
            this.innerSetEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRankInfo()) {
            this.getRankInfo().mergeFromSs(proto.getRankInfo());
        } else {
            if (this.rankInfo != null) {
                this.rankInfo.mergeFromSs(proto.getRankInfo());
            }
        }
        if (proto.hasRewardInfo()) {
            this.getRewardInfo().mergeFromSs(proto.getRewardInfo());
        } else {
            if (this.rewardInfo != null) {
                this.rewardInfo.mergeFromSs(proto.getRewardInfo());
            }
        }
        if (proto.hasGamePlayerOpenData()) {
            this.getGamePlayerOpenData().mergeFromSs(proto.getGamePlayerOpenData());
        } else {
            if (this.gamePlayerOpenData != null) {
                this.gamePlayerOpenData.mergeFromSs(proto.getGamePlayerOpenData());
            }
        }
        if (proto.hasIsSuccess()) {
            this.innerSetIsSuccess(proto.getIsSuccess());
        } else {
            this.innerSetIsSuccess(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasStartTsMs()) {
            this.innerSetStartTsMs(proto.getStartTsMs());
        } else {
            this.innerSetStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MileStoneInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MileStoneInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMileStoneId()) {
            this.setMileStoneId(proto.getMileStoneId());
            fieldCnt++;
        }
        if (proto.hasProcess()) {
            this.setProcess(proto.getProcess());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasEndTsMs()) {
            this.setEndTsMs(proto.getEndTsMs());
            fieldCnt++;
        }
        if (proto.hasRankInfo()) {
            this.getRankInfo().mergeChangeFromSs(proto.getRankInfo());
            fieldCnt++;
        }
        if (proto.hasRewardInfo()) {
            this.getRewardInfo().mergeChangeFromSs(proto.getRewardInfo());
            fieldCnt++;
        }
        if (proto.hasGamePlayerOpenData()) {
            this.getGamePlayerOpenData().mergeChangeFromSs(proto.getGamePlayerOpenData());
            fieldCnt++;
        }
        if (proto.hasIsSuccess()) {
            this.setIsSuccess(proto.getIsSuccess());
            fieldCnt++;
        }
        if (proto.hasStartTsMs()) {
            this.setStartTsMs(proto.getStartTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MileStoneInfo.Builder builder = MileStoneInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_RANKINFO) && this.rankInfo != null) {
            this.rankInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_REWARDINFO) && this.rewardInfo != null) {
            this.rewardInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_GAMEPLAYEROPENDATA) && this.gamePlayerOpenData != null) {
            this.gamePlayerOpenData.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.rankInfo != null) {
            this.rankInfo.markAll();
        }
        if (this.rewardInfo != null) {
            this.rewardInfo.markAll();
        }
        if (this.gamePlayerOpenData != null) {
            this.gamePlayerOpenData.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.mileStoneId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MileStoneInfoProp)) {
            return false;
        }
        final MileStoneInfoProp otherNode = (MileStoneInfoProp) node;
        if (this.mileStoneId != otherNode.mileStoneId) {
            return false;
        }
        if (this.process != otherNode.process) {
            return false;
        }
        if (this.status != otherNode.status) {
            return false;
        }
        if (this.endTsMs != otherNode.endTsMs) {
            return false;
        }
        if (!this.getRankInfo().compareDataTo(otherNode.getRankInfo())) {
            return false;
        }
        if (!this.getRewardInfo().compareDataTo(otherNode.getRewardInfo())) {
            return false;
        }
        if (!this.getGamePlayerOpenData().compareDataTo(otherNode.getGamePlayerOpenData())) {
            return false;
        }
        if (this.isSuccess != otherNode.isSuccess) {
            return false;
        }
        if (this.startTsMs != otherNode.startTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 55;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
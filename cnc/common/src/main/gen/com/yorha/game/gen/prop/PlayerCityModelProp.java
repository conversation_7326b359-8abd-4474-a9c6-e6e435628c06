package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerCityModel;
import com.yorha.proto.Player;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.PlayerCityModelPB;
import com.yorha.proto.PlayerPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerCityModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_UNLOCKEDSKINS = 0;
    public static final int FIELD_INDEX_USINGSKIN = 1;
    public static final int FIELD_INDEX_PLAYERWALLMODEL = 2;
    public static final int FIELD_INDEX_UNLOCKEDNAMEPLATE = 3;
    public static final int FIELD_INDEX_USINGNAMEPLATE = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private Int32CityDressMapProp unlockedSkins = null;
    private int usingSkin = Constant.DEFAULT_INT_VALUE;
    private PlayerWallModelProp playerWallModel = null;
    private Int32PlayerNameplateMapProp unlockedNameplate = null;
    private int usingNameplate = Constant.DEFAULT_INT_VALUE;

    public PlayerCityModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerCityModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get unlockedSkins
     *
     * @return unlockedSkins value
     */
    public Int32CityDressMapProp getUnlockedSkins() {
        if (this.unlockedSkins == null) {
            this.unlockedSkins = new Int32CityDressMapProp(this, FIELD_INDEX_UNLOCKEDSKINS);
        }
        return this.unlockedSkins;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putUnlockedSkinsV(CityDressProp v) {
        this.getUnlockedSkins().put(v.getDressTemplateId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public CityDressProp addEmptyUnlockedSkins(Integer k) {
        return this.getUnlockedSkins().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getUnlockedSkinsSize() {
        if (this.unlockedSkins == null) {
            return 0;
        }
        return this.unlockedSkins.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isUnlockedSkinsEmpty() {
        if (this.unlockedSkins == null) {
            return true;
        }
        return this.unlockedSkins.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public CityDressProp getUnlockedSkinsV(Integer k) {
        if (this.unlockedSkins == null || !this.unlockedSkins.containsKey(k)) {
            return null;
        }
        return this.unlockedSkins.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearUnlockedSkins() {
        if (this.unlockedSkins != null) {
            this.unlockedSkins.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeUnlockedSkinsV(Integer k) {
        if (this.unlockedSkins != null) {
            this.unlockedSkins.remove(k);
        }
    }
    /**
     * get usingSkin
     *
     * @return usingSkin value
     */
    public int getUsingSkin() {
        return this.usingSkin;
    }

    /**
     * set usingSkin && set marked
     *
     * @param usingSkin new value
     * @return current object
     */
    public PlayerCityModelProp setUsingSkin(int usingSkin) {
        if (this.usingSkin != usingSkin) {
            this.mark(FIELD_INDEX_USINGSKIN);
            this.usingSkin = usingSkin;
        }
        return this;
    }

    /**
     * inner set usingSkin
     *
     * @param usingSkin new value
     */
    private void innerSetUsingSkin(int usingSkin) {
        this.usingSkin = usingSkin;
    }

    /**
     * get playerWallModel
     *
     * @return playerWallModel value
     */
    public PlayerWallModelProp getPlayerWallModel() {
        if (this.playerWallModel == null) {
            this.playerWallModel = new PlayerWallModelProp(this, FIELD_INDEX_PLAYERWALLMODEL);
        }
        return this.playerWallModel;
    }

    /**
     * get unlockedNameplate
     *
     * @return unlockedNameplate value
     */
    public Int32PlayerNameplateMapProp getUnlockedNameplate() {
        if (this.unlockedNameplate == null) {
            this.unlockedNameplate = new Int32PlayerNameplateMapProp(this, FIELD_INDEX_UNLOCKEDNAMEPLATE);
        }
        return this.unlockedNameplate;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putUnlockedNameplateV(PlayerNameplateProp v) {
        this.getUnlockedNameplate().put(v.getTemplateId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerNameplateProp addEmptyUnlockedNameplate(Integer k) {
        return this.getUnlockedNameplate().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getUnlockedNameplateSize() {
        if (this.unlockedNameplate == null) {
            return 0;
        }
        return this.unlockedNameplate.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isUnlockedNameplateEmpty() {
        if (this.unlockedNameplate == null) {
            return true;
        }
        return this.unlockedNameplate.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerNameplateProp getUnlockedNameplateV(Integer k) {
        if (this.unlockedNameplate == null || !this.unlockedNameplate.containsKey(k)) {
            return null;
        }
        return this.unlockedNameplate.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearUnlockedNameplate() {
        if (this.unlockedNameplate != null) {
            this.unlockedNameplate.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeUnlockedNameplateV(Integer k) {
        if (this.unlockedNameplate != null) {
            this.unlockedNameplate.remove(k);
        }
    }
    /**
     * get usingNameplate
     *
     * @return usingNameplate value
     */
    public int getUsingNameplate() {
        return this.usingNameplate;
    }

    /**
     * set usingNameplate && set marked
     *
     * @param usingNameplate new value
     * @return current object
     */
    public PlayerCityModelProp setUsingNameplate(int usingNameplate) {
        if (this.usingNameplate != usingNameplate) {
            this.mark(FIELD_INDEX_USINGNAMEPLATE);
            this.usingNameplate = usingNameplate;
        }
        return this;
    }

    /**
     * inner set usingNameplate
     *
     * @param usingNameplate new value
     */
    private void innerSetUsingNameplate(int usingNameplate) {
        this.usingNameplate = usingNameplate;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerCityModelPB.Builder getCopyCsBuilder() {
        final PlayerCityModelPB.Builder builder = PlayerCityModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerCityModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.unlockedSkins != null) {
            StructPB.Int32CityDressMapPB.Builder tmpBuilder = StructPB.Int32CityDressMapPB.newBuilder();
            final int tmpFieldCnt = this.unlockedSkins.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnlockedSkins(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnlockedSkins();
            }
        }  else if (builder.hasUnlockedSkins()) {
            // 清理UnlockedSkins
            builder.clearUnlockedSkins();
            fieldCnt++;
        }
        if (this.unlockedNameplate != null) {
            PlayerPB.Int32PlayerNameplateMapPB.Builder tmpBuilder = PlayerPB.Int32PlayerNameplateMapPB.newBuilder();
            final int tmpFieldCnt = this.unlockedNameplate.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnlockedNameplate(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnlockedNameplate();
            }
        }  else if (builder.hasUnlockedNameplate()) {
            // 清理UnlockedNameplate
            builder.clearUnlockedNameplate();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerCityModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNLOCKEDSKINS) && this.unlockedSkins != null) {
            final boolean needClear = !builder.hasUnlockedSkins();
            final int tmpFieldCnt = this.unlockedSkins.copyChangeToCs(builder.getUnlockedSkinsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnlockedSkins();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKEDNAMEPLATE) && this.unlockedNameplate != null) {
            final boolean needClear = !builder.hasUnlockedNameplate();
            final int tmpFieldCnt = this.unlockedNameplate.copyChangeToCs(builder.getUnlockedNameplateBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnlockedNameplate();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerCityModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNLOCKEDSKINS) && this.unlockedSkins != null) {
            final boolean needClear = !builder.hasUnlockedSkins();
            final int tmpFieldCnt = this.unlockedSkins.copyChangeToAndClearDeleteKeysCs(builder.getUnlockedSkinsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnlockedSkins();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKEDNAMEPLATE) && this.unlockedNameplate != null) {
            final boolean needClear = !builder.hasUnlockedNameplate();
            final int tmpFieldCnt = this.unlockedNameplate.copyChangeToAndClearDeleteKeysCs(builder.getUnlockedNameplateBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnlockedNameplate();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerCityModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnlockedSkins()) {
            this.getUnlockedSkins().mergeFromCs(proto.getUnlockedSkins());
        } else {
            if (this.unlockedSkins != null) {
                this.unlockedSkins.mergeFromCs(proto.getUnlockedSkins());
            }
        }
        if (proto.hasUnlockedNameplate()) {
            this.getUnlockedNameplate().mergeFromCs(proto.getUnlockedNameplate());
        } else {
            if (this.unlockedNameplate != null) {
                this.unlockedNameplate.mergeFromCs(proto.getUnlockedNameplate());
            }
        }
        this.markAll();
        return PlayerCityModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerCityModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnlockedSkins()) {
            this.getUnlockedSkins().mergeChangeFromCs(proto.getUnlockedSkins());
            fieldCnt++;
        }
        if (proto.hasUnlockedNameplate()) {
            this.getUnlockedNameplate().mergeChangeFromCs(proto.getUnlockedNameplate());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerCityModel.Builder getCopyDbBuilder() {
        final PlayerCityModel.Builder builder = PlayerCityModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerCityModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.unlockedSkins != null) {
            Struct.Int32CityDressMap.Builder tmpBuilder = Struct.Int32CityDressMap.newBuilder();
            final int tmpFieldCnt = this.unlockedSkins.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnlockedSkins(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnlockedSkins();
            }
        }  else if (builder.hasUnlockedSkins()) {
            // 清理UnlockedSkins
            builder.clearUnlockedSkins();
            fieldCnt++;
        }
        if (this.getUsingSkin() != 0) {
            builder.setUsingSkin(this.getUsingSkin());
            fieldCnt++;
        }  else if (builder.hasUsingSkin()) {
            // 清理UsingSkin
            builder.clearUsingSkin();
            fieldCnt++;
        }
        if (this.playerWallModel != null) {
            Player.PlayerWallModel.Builder tmpBuilder = Player.PlayerWallModel.newBuilder();
            final int tmpFieldCnt = this.playerWallModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerWallModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerWallModel();
            }
        }  else if (builder.hasPlayerWallModel()) {
            // 清理PlayerWallModel
            builder.clearPlayerWallModel();
            fieldCnt++;
        }
        if (this.unlockedNameplate != null) {
            Player.Int32PlayerNameplateMap.Builder tmpBuilder = Player.Int32PlayerNameplateMap.newBuilder();
            final int tmpFieldCnt = this.unlockedNameplate.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnlockedNameplate(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnlockedNameplate();
            }
        }  else if (builder.hasUnlockedNameplate()) {
            // 清理UnlockedNameplate
            builder.clearUnlockedNameplate();
            fieldCnt++;
        }
        if (this.getUsingNameplate() != 0) {
            builder.setUsingNameplate(this.getUsingNameplate());
            fieldCnt++;
        }  else if (builder.hasUsingNameplate()) {
            // 清理UsingNameplate
            builder.clearUsingNameplate();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerCityModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNLOCKEDSKINS) && this.unlockedSkins != null) {
            final boolean needClear = !builder.hasUnlockedSkins();
            final int tmpFieldCnt = this.unlockedSkins.copyChangeToDb(builder.getUnlockedSkinsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnlockedSkins();
            }
        }
        if (this.hasMark(FIELD_INDEX_USINGSKIN)) {
            builder.setUsingSkin(this.getUsingSkin());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERWALLMODEL) && this.playerWallModel != null) {
            final boolean needClear = !builder.hasPlayerWallModel();
            final int tmpFieldCnt = this.playerWallModel.copyChangeToDb(builder.getPlayerWallModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerWallModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKEDNAMEPLATE) && this.unlockedNameplate != null) {
            final boolean needClear = !builder.hasUnlockedNameplate();
            final int tmpFieldCnt = this.unlockedNameplate.copyChangeToDb(builder.getUnlockedNameplateBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnlockedNameplate();
            }
        }
        if (this.hasMark(FIELD_INDEX_USINGNAMEPLATE)) {
            builder.setUsingNameplate(this.getUsingNameplate());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerCityModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnlockedSkins()) {
            this.getUnlockedSkins().mergeFromDb(proto.getUnlockedSkins());
        } else {
            if (this.unlockedSkins != null) {
                this.unlockedSkins.mergeFromDb(proto.getUnlockedSkins());
            }
        }
        if (proto.hasUsingSkin()) {
            this.innerSetUsingSkin(proto.getUsingSkin());
        } else {
            this.innerSetUsingSkin(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPlayerWallModel()) {
            this.getPlayerWallModel().mergeFromDb(proto.getPlayerWallModel());
        } else {
            if (this.playerWallModel != null) {
                this.playerWallModel.mergeFromDb(proto.getPlayerWallModel());
            }
        }
        if (proto.hasUnlockedNameplate()) {
            this.getUnlockedNameplate().mergeFromDb(proto.getUnlockedNameplate());
        } else {
            if (this.unlockedNameplate != null) {
                this.unlockedNameplate.mergeFromDb(proto.getUnlockedNameplate());
            }
        }
        if (proto.hasUsingNameplate()) {
            this.innerSetUsingNameplate(proto.getUsingNameplate());
        } else {
            this.innerSetUsingNameplate(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerCityModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerCityModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnlockedSkins()) {
            this.getUnlockedSkins().mergeChangeFromDb(proto.getUnlockedSkins());
            fieldCnt++;
        }
        if (proto.hasUsingSkin()) {
            this.setUsingSkin(proto.getUsingSkin());
            fieldCnt++;
        }
        if (proto.hasPlayerWallModel()) {
            this.getPlayerWallModel().mergeChangeFromDb(proto.getPlayerWallModel());
            fieldCnt++;
        }
        if (proto.hasUnlockedNameplate()) {
            this.getUnlockedNameplate().mergeChangeFromDb(proto.getUnlockedNameplate());
            fieldCnt++;
        }
        if (proto.hasUsingNameplate()) {
            this.setUsingNameplate(proto.getUsingNameplate());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerCityModel.Builder getCopySsBuilder() {
        final PlayerCityModel.Builder builder = PlayerCityModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerCityModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.unlockedSkins != null) {
            Struct.Int32CityDressMap.Builder tmpBuilder = Struct.Int32CityDressMap.newBuilder();
            final int tmpFieldCnt = this.unlockedSkins.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnlockedSkins(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnlockedSkins();
            }
        }  else if (builder.hasUnlockedSkins()) {
            // 清理UnlockedSkins
            builder.clearUnlockedSkins();
            fieldCnt++;
        }
        if (this.getUsingSkin() != 0) {
            builder.setUsingSkin(this.getUsingSkin());
            fieldCnt++;
        }  else if (builder.hasUsingSkin()) {
            // 清理UsingSkin
            builder.clearUsingSkin();
            fieldCnt++;
        }
        if (this.playerWallModel != null) {
            Player.PlayerWallModel.Builder tmpBuilder = Player.PlayerWallModel.newBuilder();
            final int tmpFieldCnt = this.playerWallModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerWallModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerWallModel();
            }
        }  else if (builder.hasPlayerWallModel()) {
            // 清理PlayerWallModel
            builder.clearPlayerWallModel();
            fieldCnt++;
        }
        if (this.unlockedNameplate != null) {
            Player.Int32PlayerNameplateMap.Builder tmpBuilder = Player.Int32PlayerNameplateMap.newBuilder();
            final int tmpFieldCnt = this.unlockedNameplate.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnlockedNameplate(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnlockedNameplate();
            }
        }  else if (builder.hasUnlockedNameplate()) {
            // 清理UnlockedNameplate
            builder.clearUnlockedNameplate();
            fieldCnt++;
        }
        if (this.getUsingNameplate() != 0) {
            builder.setUsingNameplate(this.getUsingNameplate());
            fieldCnt++;
        }  else if (builder.hasUsingNameplate()) {
            // 清理UsingNameplate
            builder.clearUsingNameplate();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerCityModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNLOCKEDSKINS) && this.unlockedSkins != null) {
            final boolean needClear = !builder.hasUnlockedSkins();
            final int tmpFieldCnt = this.unlockedSkins.copyChangeToSs(builder.getUnlockedSkinsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnlockedSkins();
            }
        }
        if (this.hasMark(FIELD_INDEX_USINGSKIN)) {
            builder.setUsingSkin(this.getUsingSkin());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERWALLMODEL) && this.playerWallModel != null) {
            final boolean needClear = !builder.hasPlayerWallModel();
            final int tmpFieldCnt = this.playerWallModel.copyChangeToSs(builder.getPlayerWallModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerWallModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKEDNAMEPLATE) && this.unlockedNameplate != null) {
            final boolean needClear = !builder.hasUnlockedNameplate();
            final int tmpFieldCnt = this.unlockedNameplate.copyChangeToSs(builder.getUnlockedNameplateBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnlockedNameplate();
            }
        }
        if (this.hasMark(FIELD_INDEX_USINGNAMEPLATE)) {
            builder.setUsingNameplate(this.getUsingNameplate());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerCityModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnlockedSkins()) {
            this.getUnlockedSkins().mergeFromSs(proto.getUnlockedSkins());
        } else {
            if (this.unlockedSkins != null) {
                this.unlockedSkins.mergeFromSs(proto.getUnlockedSkins());
            }
        }
        if (proto.hasUsingSkin()) {
            this.innerSetUsingSkin(proto.getUsingSkin());
        } else {
            this.innerSetUsingSkin(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPlayerWallModel()) {
            this.getPlayerWallModel().mergeFromSs(proto.getPlayerWallModel());
        } else {
            if (this.playerWallModel != null) {
                this.playerWallModel.mergeFromSs(proto.getPlayerWallModel());
            }
        }
        if (proto.hasUnlockedNameplate()) {
            this.getUnlockedNameplate().mergeFromSs(proto.getUnlockedNameplate());
        } else {
            if (this.unlockedNameplate != null) {
                this.unlockedNameplate.mergeFromSs(proto.getUnlockedNameplate());
            }
        }
        if (proto.hasUsingNameplate()) {
            this.innerSetUsingNameplate(proto.getUsingNameplate());
        } else {
            this.innerSetUsingNameplate(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerCityModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerCityModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnlockedSkins()) {
            this.getUnlockedSkins().mergeChangeFromSs(proto.getUnlockedSkins());
            fieldCnt++;
        }
        if (proto.hasUsingSkin()) {
            this.setUsingSkin(proto.getUsingSkin());
            fieldCnt++;
        }
        if (proto.hasPlayerWallModel()) {
            this.getPlayerWallModel().mergeChangeFromSs(proto.getPlayerWallModel());
            fieldCnt++;
        }
        if (proto.hasUnlockedNameplate()) {
            this.getUnlockedNameplate().mergeChangeFromSs(proto.getUnlockedNameplate());
            fieldCnt++;
        }
        if (proto.hasUsingNameplate()) {
            this.setUsingNameplate(proto.getUsingNameplate());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerCityModel.Builder builder = PlayerCityModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKEDSKINS) && this.unlockedSkins != null) {
            this.unlockedSkins.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLAYERWALLMODEL) && this.playerWallModel != null) {
            this.playerWallModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKEDNAMEPLATE) && this.unlockedNameplate != null) {
            this.unlockedNameplate.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.unlockedSkins != null) {
            this.unlockedSkins.markAll();
        }
        if (this.playerWallModel != null) {
            this.playerWallModel.markAll();
        }
        if (this.unlockedNameplate != null) {
            this.unlockedNameplate.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerCityModelProp)) {
            return false;
        }
        final PlayerCityModelProp otherNode = (PlayerCityModelProp) node;
        if (!this.getUnlockedSkins().compareDataTo(otherNode.getUnlockedSkins())) {
            return false;
        }
        if (this.usingSkin != otherNode.usingSkin) {
            return false;
        }
        if (!this.getPlayerWallModel().compareDataTo(otherNode.getPlayerWallModel())) {
            return false;
        }
        if (!this.getUnlockedNameplate().compareDataTo(otherNode.getUnlockedNameplate())) {
            return false;
        }
        if (this.usingNameplate != otherNode.usingNameplate) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
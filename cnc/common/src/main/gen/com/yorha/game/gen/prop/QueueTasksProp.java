package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructPlayer.QueueTasks;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.StructPlayerPB.QueueTasksPB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class QueueTasksProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_QUEUETASKLIST = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int id = Constant.DEFAULT_INT_VALUE;
    private QueueTaskListProp queueTaskList = null;

    public QueueTasksProp() {
        super(null, 0, FIELD_COUNT);
    }

    public QueueTasksProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public int getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public QueueTasksProp setId(int id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(int id) {
        this.id = id;
    }

    /**
     * get queueTaskList
     *
     * @return queueTaskList value
     */
    public QueueTaskListProp getQueueTaskList() {
        if (this.queueTaskList == null) {
            this.queueTaskList = new QueueTaskListProp(this, FIELD_INDEX_QUEUETASKLIST);
        }
        return this.queueTaskList;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addQueueTaskList(QueueTaskProp v) {
        this.getQueueTaskList().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public QueueTaskProp getQueueTaskListIndex(int index) {
        return this.getQueueTaskList().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public QueueTaskProp removeQueueTaskList(QueueTaskProp v) {
        if (this.queueTaskList == null) {
            return null;
        }
        if(this.queueTaskList.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getQueueTaskListSize() {
        if (this.queueTaskList == null) {
            return 0;
        }
        return this.queueTaskList.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isQueueTaskListEmpty() {
        if (this.queueTaskList == null) {
            return true;
        }
        return this.getQueueTaskList().isEmpty();
    }

    /**
     * clear list
     */
    public void clearQueueTaskList() {
        this.getQueueTaskList().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public QueueTaskProp removeQueueTaskListIndex(int index) {
        return this.getQueueTaskList().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public QueueTaskProp setQueueTaskListIndex(int index, QueueTaskProp v) {
        return this.getQueueTaskList().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public QueueTasksPB.Builder getCopyCsBuilder() {
        final QueueTasksPB.Builder builder = QueueTasksPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(QueueTasksPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.queueTaskList != null) {
            StructPlayerPB.QueueTaskListPB.Builder tmpBuilder = StructPlayerPB.QueueTaskListPB.newBuilder();
            final int tmpFieldCnt = this.queueTaskList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setQueueTaskList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearQueueTaskList();
            }
        }  else if (builder.hasQueueTaskList()) {
            // 清理QueueTaskList
            builder.clearQueueTaskList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(QueueTasksPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_QUEUETASKLIST) && this.queueTaskList != null) {
            final boolean needClear = !builder.hasQueueTaskList();
            final int tmpFieldCnt = this.queueTaskList.copyChangeToCs(builder.getQueueTaskListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearQueueTaskList();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(QueueTasksPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_QUEUETASKLIST) && this.queueTaskList != null) {
            final boolean needClear = !builder.hasQueueTaskList();
            final int tmpFieldCnt = this.queueTaskList.copyChangeToCs(builder.getQueueTaskListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearQueueTaskList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(QueueTasksPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasQueueTaskList()) {
            this.getQueueTaskList().mergeFromCs(proto.getQueueTaskList());
        } else {
            if (this.queueTaskList != null) {
                this.queueTaskList.mergeFromCs(proto.getQueueTaskList());
            }
        }
        this.markAll();
        return QueueTasksProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(QueueTasksPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasQueueTaskList()) {
            this.getQueueTaskList().mergeChangeFromCs(proto.getQueueTaskList());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public QueueTasks.Builder getCopyDbBuilder() {
        final QueueTasks.Builder builder = QueueTasks.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(QueueTasks.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.queueTaskList != null) {
            StructPlayer.QueueTaskList.Builder tmpBuilder = StructPlayer.QueueTaskList.newBuilder();
            final int tmpFieldCnt = this.queueTaskList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setQueueTaskList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearQueueTaskList();
            }
        }  else if (builder.hasQueueTaskList()) {
            // 清理QueueTaskList
            builder.clearQueueTaskList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(QueueTasks.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_QUEUETASKLIST) && this.queueTaskList != null) {
            final boolean needClear = !builder.hasQueueTaskList();
            final int tmpFieldCnt = this.queueTaskList.copyChangeToDb(builder.getQueueTaskListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearQueueTaskList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(QueueTasks proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasQueueTaskList()) {
            this.getQueueTaskList().mergeFromDb(proto.getQueueTaskList());
        } else {
            if (this.queueTaskList != null) {
                this.queueTaskList.mergeFromDb(proto.getQueueTaskList());
            }
        }
        this.markAll();
        return QueueTasksProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(QueueTasks proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasQueueTaskList()) {
            this.getQueueTaskList().mergeChangeFromDb(proto.getQueueTaskList());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public QueueTasks.Builder getCopySsBuilder() {
        final QueueTasks.Builder builder = QueueTasks.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(QueueTasks.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.queueTaskList != null) {
            StructPlayer.QueueTaskList.Builder tmpBuilder = StructPlayer.QueueTaskList.newBuilder();
            final int tmpFieldCnt = this.queueTaskList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setQueueTaskList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearQueueTaskList();
            }
        }  else if (builder.hasQueueTaskList()) {
            // 清理QueueTaskList
            builder.clearQueueTaskList();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(QueueTasks.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_QUEUETASKLIST) && this.queueTaskList != null) {
            final boolean needClear = !builder.hasQueueTaskList();
            final int tmpFieldCnt = this.queueTaskList.copyChangeToSs(builder.getQueueTaskListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearQueueTaskList();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(QueueTasks proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasQueueTaskList()) {
            this.getQueueTaskList().mergeFromSs(proto.getQueueTaskList());
        } else {
            if (this.queueTaskList != null) {
                this.queueTaskList.mergeFromSs(proto.getQueueTaskList());
            }
        }
        this.markAll();
        return QueueTasksProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(QueueTasks proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasQueueTaskList()) {
            this.getQueueTaskList().mergeChangeFromSs(proto.getQueueTaskList());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        QueueTasks.Builder builder = QueueTasks.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_QUEUETASKLIST) && this.queueTaskList != null) {
            this.queueTaskList.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.queueTaskList != null) {
            this.queueTaskList.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof QueueTasksProp)) {
            return false;
        }
        final QueueTasksProp otherNode = (QueueTasksProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (!this.getQueueTaskList().compareDataTo(otherNode.getQueueTaskList())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
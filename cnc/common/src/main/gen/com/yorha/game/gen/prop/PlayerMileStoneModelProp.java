package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerMileStoneModel;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.PlayerMileStoneModelPB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerMileStoneModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CURMILESTONEID = 0;
    public static final int FIELD_INDEX_MILESTONEINFO = 1;
    public static final int FIELD_INDEX_CURENDTSMS = 2;
    public static final int FIELD_INDEX_TEMPLATEID = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int curMileStoneId = Constant.DEFAULT_INT_VALUE;
    private Int32PlayerMileStoneInfoMapProp mileStoneInfo = null;
    private long curEndTsMs = Constant.DEFAULT_LONG_VALUE;
    private int templateId = Constant.DEFAULT_INT_VALUE;

    public PlayerMileStoneModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerMileStoneModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get curMileStoneId
     *
     * @return curMileStoneId value
     */
    public int getCurMileStoneId() {
        return this.curMileStoneId;
    }

    /**
     * set curMileStoneId && set marked
     *
     * @param curMileStoneId new value
     * @return current object
     */
    public PlayerMileStoneModelProp setCurMileStoneId(int curMileStoneId) {
        if (this.curMileStoneId != curMileStoneId) {
            this.mark(FIELD_INDEX_CURMILESTONEID);
            this.curMileStoneId = curMileStoneId;
        }
        return this;
    }

    /**
     * inner set curMileStoneId
     *
     * @param curMileStoneId new value
     */
    private void innerSetCurMileStoneId(int curMileStoneId) {
        this.curMileStoneId = curMileStoneId;
    }

    /**
     * get mileStoneInfo
     *
     * @return mileStoneInfo value
     */
    public Int32PlayerMileStoneInfoMapProp getMileStoneInfo() {
        if (this.mileStoneInfo == null) {
            this.mileStoneInfo = new Int32PlayerMileStoneInfoMapProp(this, FIELD_INDEX_MILESTONEINFO);
        }
        return this.mileStoneInfo;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putMileStoneInfoV(PlayerMileStoneInfoProp v) {
        this.getMileStoneInfo().put(v.getMileStoneId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerMileStoneInfoProp addEmptyMileStoneInfo(Integer k) {
        return this.getMileStoneInfo().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getMileStoneInfoSize() {
        if (this.mileStoneInfo == null) {
            return 0;
        }
        return this.mileStoneInfo.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isMileStoneInfoEmpty() {
        if (this.mileStoneInfo == null) {
            return true;
        }
        return this.mileStoneInfo.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerMileStoneInfoProp getMileStoneInfoV(Integer k) {
        if (this.mileStoneInfo == null || !this.mileStoneInfo.containsKey(k)) {
            return null;
        }
        return this.mileStoneInfo.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearMileStoneInfo() {
        if (this.mileStoneInfo != null) {
            this.mileStoneInfo.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeMileStoneInfoV(Integer k) {
        if (this.mileStoneInfo != null) {
            this.mileStoneInfo.remove(k);
        }
    }
    /**
     * get curEndTsMs
     *
     * @return curEndTsMs value
     */
    public long getCurEndTsMs() {
        return this.curEndTsMs;
    }

    /**
     * set curEndTsMs && set marked
     *
     * @param curEndTsMs new value
     * @return current object
     */
    public PlayerMileStoneModelProp setCurEndTsMs(long curEndTsMs) {
        if (this.curEndTsMs != curEndTsMs) {
            this.mark(FIELD_INDEX_CURENDTSMS);
            this.curEndTsMs = curEndTsMs;
        }
        return this;
    }

    /**
     * inner set curEndTsMs
     *
     * @param curEndTsMs new value
     */
    private void innerSetCurEndTsMs(long curEndTsMs) {
        this.curEndTsMs = curEndTsMs;
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public PlayerMileStoneModelProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMileStoneModelPB.Builder getCopyCsBuilder() {
        final PlayerMileStoneModelPB.Builder builder = PlayerMileStoneModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerMileStoneModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurMileStoneId() != 0) {
            builder.setCurMileStoneId(this.getCurMileStoneId());
            fieldCnt++;
        }  else if (builder.hasCurMileStoneId()) {
            // 清理CurMileStoneId
            builder.clearCurMileStoneId();
            fieldCnt++;
        }
        if (this.mileStoneInfo != null) {
            PlayerPB.Int32PlayerMileStoneInfoMapPB.Builder tmpBuilder = PlayerPB.Int32PlayerMileStoneInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.mileStoneInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMileStoneInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMileStoneInfo();
            }
        }  else if (builder.hasMileStoneInfo()) {
            // 清理MileStoneInfo
            builder.clearMileStoneInfo();
            fieldCnt++;
        }
        if (this.getCurEndTsMs() != 0L) {
            builder.setCurEndTsMs(this.getCurEndTsMs());
            fieldCnt++;
        }  else if (builder.hasCurEndTsMs()) {
            // 清理CurEndTsMs
            builder.clearCurEndTsMs();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerMileStoneModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURMILESTONEID)) {
            builder.setCurMileStoneId(this.getCurMileStoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MILESTONEINFO) && this.mileStoneInfo != null) {
            final boolean needClear = !builder.hasMileStoneInfo();
            final int tmpFieldCnt = this.mileStoneInfo.copyChangeToCs(builder.getMileStoneInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMileStoneInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_CURENDTSMS)) {
            builder.setCurEndTsMs(this.getCurEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerMileStoneModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURMILESTONEID)) {
            builder.setCurMileStoneId(this.getCurMileStoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MILESTONEINFO) && this.mileStoneInfo != null) {
            final boolean needClear = !builder.hasMileStoneInfo();
            final int tmpFieldCnt = this.mileStoneInfo.copyChangeToAndClearDeleteKeysCs(builder.getMileStoneInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMileStoneInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_CURENDTSMS)) {
            builder.setCurEndTsMs(this.getCurEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerMileStoneModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurMileStoneId()) {
            this.innerSetCurMileStoneId(proto.getCurMileStoneId());
        } else {
            this.innerSetCurMileStoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMileStoneInfo()) {
            this.getMileStoneInfo().mergeFromCs(proto.getMileStoneInfo());
        } else {
            if (this.mileStoneInfo != null) {
                this.mileStoneInfo.mergeFromCs(proto.getMileStoneInfo());
            }
        }
        if (proto.hasCurEndTsMs()) {
            this.innerSetCurEndTsMs(proto.getCurEndTsMs());
        } else {
            this.innerSetCurEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerMileStoneModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerMileStoneModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurMileStoneId()) {
            this.setCurMileStoneId(proto.getCurMileStoneId());
            fieldCnt++;
        }
        if (proto.hasMileStoneInfo()) {
            this.getMileStoneInfo().mergeChangeFromCs(proto.getMileStoneInfo());
            fieldCnt++;
        }
        if (proto.hasCurEndTsMs()) {
            this.setCurEndTsMs(proto.getCurEndTsMs());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMileStoneModel.Builder getCopyDbBuilder() {
        final PlayerMileStoneModel.Builder builder = PlayerMileStoneModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerMileStoneModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurMileStoneId() != 0) {
            builder.setCurMileStoneId(this.getCurMileStoneId());
            fieldCnt++;
        }  else if (builder.hasCurMileStoneId()) {
            // 清理CurMileStoneId
            builder.clearCurMileStoneId();
            fieldCnt++;
        }
        if (this.mileStoneInfo != null) {
            Player.Int32PlayerMileStoneInfoMap.Builder tmpBuilder = Player.Int32PlayerMileStoneInfoMap.newBuilder();
            final int tmpFieldCnt = this.mileStoneInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMileStoneInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMileStoneInfo();
            }
        }  else if (builder.hasMileStoneInfo()) {
            // 清理MileStoneInfo
            builder.clearMileStoneInfo();
            fieldCnt++;
        }
        if (this.getCurEndTsMs() != 0L) {
            builder.setCurEndTsMs(this.getCurEndTsMs());
            fieldCnt++;
        }  else if (builder.hasCurEndTsMs()) {
            // 清理CurEndTsMs
            builder.clearCurEndTsMs();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerMileStoneModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURMILESTONEID)) {
            builder.setCurMileStoneId(this.getCurMileStoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MILESTONEINFO) && this.mileStoneInfo != null) {
            final boolean needClear = !builder.hasMileStoneInfo();
            final int tmpFieldCnt = this.mileStoneInfo.copyChangeToDb(builder.getMileStoneInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMileStoneInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_CURENDTSMS)) {
            builder.setCurEndTsMs(this.getCurEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerMileStoneModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurMileStoneId()) {
            this.innerSetCurMileStoneId(proto.getCurMileStoneId());
        } else {
            this.innerSetCurMileStoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMileStoneInfo()) {
            this.getMileStoneInfo().mergeFromDb(proto.getMileStoneInfo());
        } else {
            if (this.mileStoneInfo != null) {
                this.mileStoneInfo.mergeFromDb(proto.getMileStoneInfo());
            }
        }
        if (proto.hasCurEndTsMs()) {
            this.innerSetCurEndTsMs(proto.getCurEndTsMs());
        } else {
            this.innerSetCurEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerMileStoneModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerMileStoneModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurMileStoneId()) {
            this.setCurMileStoneId(proto.getCurMileStoneId());
            fieldCnt++;
        }
        if (proto.hasMileStoneInfo()) {
            this.getMileStoneInfo().mergeChangeFromDb(proto.getMileStoneInfo());
            fieldCnt++;
        }
        if (proto.hasCurEndTsMs()) {
            this.setCurEndTsMs(proto.getCurEndTsMs());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMileStoneModel.Builder getCopySsBuilder() {
        final PlayerMileStoneModel.Builder builder = PlayerMileStoneModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerMileStoneModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurMileStoneId() != 0) {
            builder.setCurMileStoneId(this.getCurMileStoneId());
            fieldCnt++;
        }  else if (builder.hasCurMileStoneId()) {
            // 清理CurMileStoneId
            builder.clearCurMileStoneId();
            fieldCnt++;
        }
        if (this.mileStoneInfo != null) {
            Player.Int32PlayerMileStoneInfoMap.Builder tmpBuilder = Player.Int32PlayerMileStoneInfoMap.newBuilder();
            final int tmpFieldCnt = this.mileStoneInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMileStoneInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMileStoneInfo();
            }
        }  else if (builder.hasMileStoneInfo()) {
            // 清理MileStoneInfo
            builder.clearMileStoneInfo();
            fieldCnt++;
        }
        if (this.getCurEndTsMs() != 0L) {
            builder.setCurEndTsMs(this.getCurEndTsMs());
            fieldCnt++;
        }  else if (builder.hasCurEndTsMs()) {
            // 清理CurEndTsMs
            builder.clearCurEndTsMs();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerMileStoneModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURMILESTONEID)) {
            builder.setCurMileStoneId(this.getCurMileStoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MILESTONEINFO) && this.mileStoneInfo != null) {
            final boolean needClear = !builder.hasMileStoneInfo();
            final int tmpFieldCnt = this.mileStoneInfo.copyChangeToSs(builder.getMileStoneInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMileStoneInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_CURENDTSMS)) {
            builder.setCurEndTsMs(this.getCurEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerMileStoneModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurMileStoneId()) {
            this.innerSetCurMileStoneId(proto.getCurMileStoneId());
        } else {
            this.innerSetCurMileStoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMileStoneInfo()) {
            this.getMileStoneInfo().mergeFromSs(proto.getMileStoneInfo());
        } else {
            if (this.mileStoneInfo != null) {
                this.mileStoneInfo.mergeFromSs(proto.getMileStoneInfo());
            }
        }
        if (proto.hasCurEndTsMs()) {
            this.innerSetCurEndTsMs(proto.getCurEndTsMs());
        } else {
            this.innerSetCurEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerMileStoneModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerMileStoneModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurMileStoneId()) {
            this.setCurMileStoneId(proto.getCurMileStoneId());
            fieldCnt++;
        }
        if (proto.hasMileStoneInfo()) {
            this.getMileStoneInfo().mergeChangeFromSs(proto.getMileStoneInfo());
            fieldCnt++;
        }
        if (proto.hasCurEndTsMs()) {
            this.setCurEndTsMs(proto.getCurEndTsMs());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerMileStoneModel.Builder builder = PlayerMileStoneModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_MILESTONEINFO) && this.mileStoneInfo != null) {
            this.mileStoneInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.mileStoneInfo != null) {
            this.mileStoneInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerMileStoneModelProp)) {
            return false;
        }
        final PlayerMileStoneModelProp otherNode = (PlayerMileStoneModelProp) node;
        if (this.curMileStoneId != otherNode.curMileStoneId) {
            return false;
        }
        if (!this.getMileStoneInfo().compareDataTo(otherNode.getMileStoneInfo())) {
            return false;
        }
        if (this.curEndTsMs != otherNode.curEndTsMs) {
            return false;
        }
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructBattle.BuffSpecData;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructBattlePB.BuffSpecDataPB;
import com.yorha.proto.StructBattlePB;


/**
 * <AUTHOR> auto gen
 */
public class BuffSpecDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_SHIELDDATA = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private BuffSpecDataShieldProp shieldData = null;

    public BuffSpecDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BuffSpecDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get shieldData
     *
     * @return shieldData value
     */
    public BuffSpecDataShieldProp getShieldData() {
        if (this.shieldData == null) {
            this.shieldData = new BuffSpecDataShieldProp(this, FIELD_INDEX_SHIELDDATA);
        }
        return this.shieldData;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BuffSpecDataPB.Builder getCopyCsBuilder() {
        final BuffSpecDataPB.Builder builder = BuffSpecDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BuffSpecDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BuffSpecDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BuffSpecDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BuffSpecDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        this.markAll();
        return BuffSpecDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BuffSpecDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BuffSpecData.Builder getCopyDbBuilder() {
        final BuffSpecData.Builder builder = BuffSpecData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(BuffSpecData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.shieldData != null) {
            StructBattle.BuffSpecDataShield.Builder tmpBuilder = StructBattle.BuffSpecDataShield.newBuilder();
            final int tmpFieldCnt = this.shieldData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setShieldData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearShieldData();
            }
        }  else if (builder.hasShieldData()) {
            // 清理ShieldData
            builder.clearShieldData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(BuffSpecData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SHIELDDATA) && this.shieldData != null) {
            final boolean needClear = !builder.hasShieldData();
            final int tmpFieldCnt = this.shieldData.copyChangeToDb(builder.getShieldDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearShieldData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(BuffSpecData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasShieldData()) {
            this.getShieldData().mergeFromDb(proto.getShieldData());
        } else {
            if (this.shieldData != null) {
                this.shieldData.mergeFromDb(proto.getShieldData());
            }
        }
        this.markAll();
        return BuffSpecDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(BuffSpecData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasShieldData()) {
            this.getShieldData().mergeChangeFromDb(proto.getShieldData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BuffSpecData.Builder getCopySsBuilder() {
        final BuffSpecData.Builder builder = BuffSpecData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(BuffSpecData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.shieldData != null) {
            StructBattle.BuffSpecDataShield.Builder tmpBuilder = StructBattle.BuffSpecDataShield.newBuilder();
            final int tmpFieldCnt = this.shieldData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setShieldData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearShieldData();
            }
        }  else if (builder.hasShieldData()) {
            // 清理ShieldData
            builder.clearShieldData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(BuffSpecData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SHIELDDATA) && this.shieldData != null) {
            final boolean needClear = !builder.hasShieldData();
            final int tmpFieldCnt = this.shieldData.copyChangeToSs(builder.getShieldDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearShieldData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(BuffSpecData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasShieldData()) {
            this.getShieldData().mergeFromSs(proto.getShieldData());
        } else {
            if (this.shieldData != null) {
                this.shieldData.mergeFromSs(proto.getShieldData());
            }
        }
        this.markAll();
        return BuffSpecDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(BuffSpecData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasShieldData()) {
            this.getShieldData().mergeChangeFromSs(proto.getShieldData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        BuffSpecData.Builder builder = BuffSpecData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SHIELDDATA) && this.shieldData != null) {
            this.shieldData.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.shieldData != null) {
            this.shieldData.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BuffSpecDataProp)) {
            return false;
        }
        final BuffSpecDataProp otherNode = (BuffSpecDataProp) node;
        if (!this.getShieldData().compareDataTo(otherNode.getShieldData())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
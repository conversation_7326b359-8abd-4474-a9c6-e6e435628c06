package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructMail.MailSendParams;
import com.yorha.proto.Struct;
import com.yorha.proto.StructMail;
import com.yorha.proto.StructMailPB.MailSendParamsPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructMailPB;


/**
 * <AUTHOR> auto gen
 */
public class MailSendParamsProp extends AbstractPropNode {

    public static final int FIELD_INDEX_MAILTEMPLATEID = 0;
    public static final int FIELD_INDEX_SENDER = 1;
    public static final int FIELD_INDEX_CONTENT = 2;
    public static final int FIELD_INDEX_TITLE = 3;
    public static final int FIELD_INDEX_ITEMREWARD = 4;
    public static final int FIELD_INDEX_EXPIRETIMESTAMP = 5;
    public static final int FIELD_INDEX_ISONLYFORSHOW = 6;
    public static final int FIELD_INDEX_HASINVITEDSTATUS = 7;
    public static final int FIELD_INDEX_ADDITEMBYMAIL = 8;

    public static final int FIELD_COUNT = 9;

    private long markBits0 = 0L;

    private int mailTemplateId = Constant.DEFAULT_INT_VALUE;
    private MailSenderProp sender = null;
    private MailContentProp content = null;
    private MailShowTitleProp title = null;
    private ItemPairListProp itemReward = null;
    private long expireTimestamp = Constant.DEFAULT_LONG_VALUE;
    private boolean isOnlyForShow = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean hasInvitedStatus = Constant.DEFAULT_BOOLEAN_VALUE;
    private AddItemByMailProp addItemByMail = null;

    public MailSendParamsProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MailSendParamsProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get mailTemplateId
     *
     * @return mailTemplateId value
     */
    public int getMailTemplateId() {
        return this.mailTemplateId;
    }

    /**
     * set mailTemplateId && set marked
     *
     * @param mailTemplateId new value
     * @return current object
     */
    public MailSendParamsProp setMailTemplateId(int mailTemplateId) {
        if (this.mailTemplateId != mailTemplateId) {
            this.mark(FIELD_INDEX_MAILTEMPLATEID);
            this.mailTemplateId = mailTemplateId;
        }
        return this;
    }

    /**
     * inner set mailTemplateId
     *
     * @param mailTemplateId new value
     */
    private void innerSetMailTemplateId(int mailTemplateId) {
        this.mailTemplateId = mailTemplateId;
    }

    /**
     * get sender
     *
     * @return sender value
     */
    public MailSenderProp getSender() {
        if (this.sender == null) {
            this.sender = new MailSenderProp(this, FIELD_INDEX_SENDER);
        }
        return this.sender;
    }

    /**
     * get content
     *
     * @return content value
     */
    public MailContentProp getContent() {
        if (this.content == null) {
            this.content = new MailContentProp(this, FIELD_INDEX_CONTENT);
        }
        return this.content;
    }

    /**
     * get title
     *
     * @return title value
     */
    public MailShowTitleProp getTitle() {
        if (this.title == null) {
            this.title = new MailShowTitleProp(this, FIELD_INDEX_TITLE);
        }
        return this.title;
    }

    /**
     * get itemReward
     *
     * @return itemReward value
     */
    public ItemPairListProp getItemReward() {
        if (this.itemReward == null) {
            this.itemReward = new ItemPairListProp(this, FIELD_INDEX_ITEMREWARD);
        }
        return this.itemReward;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addItemReward(ItemPairProp v) {
        this.getItemReward().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ItemPairProp getItemRewardIndex(int index) {
        return this.getItemReward().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public ItemPairProp removeItemReward(ItemPairProp v) {
        if (this.itemReward == null) {
            return null;
        }
        if(this.itemReward.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getItemRewardSize() {
        if (this.itemReward == null) {
            return 0;
        }
        return this.itemReward.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isItemRewardEmpty() {
        if (this.itemReward == null) {
            return true;
        }
        return this.getItemReward().isEmpty();
    }

    /**
     * clear list
     */
    public void clearItemReward() {
        this.getItemReward().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public ItemPairProp removeItemRewardIndex(int index) {
        return this.getItemReward().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public ItemPairProp setItemRewardIndex(int index, ItemPairProp v) {
        return this.getItemReward().set(index, v);
    }
    /**
     * get expireTimestamp
     *
     * @return expireTimestamp value
     */
    public long getExpireTimestamp() {
        return this.expireTimestamp;
    }

    /**
     * set expireTimestamp && set marked
     *
     * @param expireTimestamp new value
     * @return current object
     */
    public MailSendParamsProp setExpireTimestamp(long expireTimestamp) {
        if (this.expireTimestamp != expireTimestamp) {
            this.mark(FIELD_INDEX_EXPIRETIMESTAMP);
            this.expireTimestamp = expireTimestamp;
        }
        return this;
    }

    /**
     * inner set expireTimestamp
     *
     * @param expireTimestamp new value
     */
    private void innerSetExpireTimestamp(long expireTimestamp) {
        this.expireTimestamp = expireTimestamp;
    }

    /**
     * get isOnlyForShow
     *
     * @return isOnlyForShow value
     */
    public boolean getIsOnlyForShow() {
        return this.isOnlyForShow;
    }

    /**
     * set isOnlyForShow && set marked
     *
     * @param isOnlyForShow new value
     * @return current object
     */
    public MailSendParamsProp setIsOnlyForShow(boolean isOnlyForShow) {
        if (this.isOnlyForShow != isOnlyForShow) {
            this.mark(FIELD_INDEX_ISONLYFORSHOW);
            this.isOnlyForShow = isOnlyForShow;
        }
        return this;
    }

    /**
     * inner set isOnlyForShow
     *
     * @param isOnlyForShow new value
     */
    private void innerSetIsOnlyForShow(boolean isOnlyForShow) {
        this.isOnlyForShow = isOnlyForShow;
    }

    /**
     * get hasInvitedStatus
     *
     * @return hasInvitedStatus value
     */
    public boolean getHasInvitedStatus() {
        return this.hasInvitedStatus;
    }

    /**
     * set hasInvitedStatus && set marked
     *
     * @param hasInvitedStatus new value
     * @return current object
     */
    public MailSendParamsProp setHasInvitedStatus(boolean hasInvitedStatus) {
        if (this.hasInvitedStatus != hasInvitedStatus) {
            this.mark(FIELD_INDEX_HASINVITEDSTATUS);
            this.hasInvitedStatus = hasInvitedStatus;
        }
        return this;
    }

    /**
     * inner set hasInvitedStatus
     *
     * @param hasInvitedStatus new value
     */
    private void innerSetHasInvitedStatus(boolean hasInvitedStatus) {
        this.hasInvitedStatus = hasInvitedStatus;
    }

    /**
     * get addItemByMail
     *
     * @return addItemByMail value
     */
    public AddItemByMailProp getAddItemByMail() {
        if (this.addItemByMail == null) {
            this.addItemByMail = new AddItemByMailProp(this, FIELD_INDEX_ADDITEMBYMAIL);
        }
        return this.addItemByMail;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailSendParamsPB.Builder getCopyCsBuilder() {
        final MailSendParamsPB.Builder builder = MailSendParamsPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MailSendParamsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMailTemplateId() != 0) {
            builder.setMailTemplateId(this.getMailTemplateId());
            fieldCnt++;
        }  else if (builder.hasMailTemplateId()) {
            // 清理MailTemplateId
            builder.clearMailTemplateId();
            fieldCnt++;
        }
        if (this.sender != null) {
            StructMailPB.MailSenderPB.Builder tmpBuilder = StructMailPB.MailSenderPB.newBuilder();
            final int tmpFieldCnt = this.sender.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSender(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSender();
            }
        }  else if (builder.hasSender()) {
            // 清理Sender
            builder.clearSender();
            fieldCnt++;
        }
        if (this.content != null) {
            StructMailPB.MailContentPB.Builder tmpBuilder = StructMailPB.MailContentPB.newBuilder();
            final int tmpFieldCnt = this.content.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setContent(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearContent();
            }
        }  else if (builder.hasContent()) {
            // 清理Content
            builder.clearContent();
            fieldCnt++;
        }
        if (this.title != null) {
            StructMailPB.MailShowTitlePB.Builder tmpBuilder = StructMailPB.MailShowTitlePB.newBuilder();
            final int tmpFieldCnt = this.title.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTitle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTitle();
            }
        }  else if (builder.hasTitle()) {
            // 清理Title
            builder.clearTitle();
            fieldCnt++;
        }
        if (this.itemReward != null) {
            StructPB.ItemPairListPB.Builder tmpBuilder = StructPB.ItemPairListPB.newBuilder();
            final int tmpFieldCnt = this.itemReward.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItemReward(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItemReward();
            }
        }  else if (builder.hasItemReward()) {
            // 清理ItemReward
            builder.clearItemReward();
            fieldCnt++;
        }
        if (this.getExpireTimestamp() != 0L) {
            builder.setExpireTimestamp(this.getExpireTimestamp());
            fieldCnt++;
        }  else if (builder.hasExpireTimestamp()) {
            // 清理ExpireTimestamp
            builder.clearExpireTimestamp();
            fieldCnt++;
        }
        if (this.getIsOnlyForShow()) {
            builder.setIsOnlyForShow(this.getIsOnlyForShow());
            fieldCnt++;
        }  else if (builder.hasIsOnlyForShow()) {
            // 清理IsOnlyForShow
            builder.clearIsOnlyForShow();
            fieldCnt++;
        }
        if (this.getHasInvitedStatus()) {
            builder.setHasInvitedStatus(this.getHasInvitedStatus());
            fieldCnt++;
        }  else if (builder.hasHasInvitedStatus()) {
            // 清理HasInvitedStatus
            builder.clearHasInvitedStatus();
            fieldCnt++;
        }
        if (this.addItemByMail != null) {
            StructMailPB.AddItemByMailPB.Builder tmpBuilder = StructMailPB.AddItemByMailPB.newBuilder();
            final int tmpFieldCnt = this.addItemByMail.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAddItemByMail(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAddItemByMail();
            }
        }  else if (builder.hasAddItemByMail()) {
            // 清理AddItemByMail
            builder.clearAddItemByMail();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MailSendParamsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAILTEMPLATEID)) {
            builder.setMailTemplateId(this.getMailTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SENDER) && this.sender != null) {
            final boolean needClear = !builder.hasSender();
            final int tmpFieldCnt = this.sender.copyChangeToCs(builder.getSenderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSender();
            }
        }
        if (this.hasMark(FIELD_INDEX_CONTENT) && this.content != null) {
            final boolean needClear = !builder.hasContent();
            final int tmpFieldCnt = this.content.copyChangeToCs(builder.getContentBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContent();
            }
        }
        if (this.hasMark(FIELD_INDEX_TITLE) && this.title != null) {
            final boolean needClear = !builder.hasTitle();
            final int tmpFieldCnt = this.title.copyChangeToCs(builder.getTitleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTitle();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            final boolean needClear = !builder.hasItemReward();
            final int tmpFieldCnt = this.itemReward.copyChangeToCs(builder.getItemRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemReward();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXPIRETIMESTAMP)) {
            builder.setExpireTimestamp(this.getExpireTimestamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISONLYFORSHOW)) {
            builder.setIsOnlyForShow(this.getIsOnlyForShow());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HASINVITEDSTATUS)) {
            builder.setHasInvitedStatus(this.getHasInvitedStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ADDITEMBYMAIL) && this.addItemByMail != null) {
            final boolean needClear = !builder.hasAddItemByMail();
            final int tmpFieldCnt = this.addItemByMail.copyChangeToCs(builder.getAddItemByMailBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAddItemByMail();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MailSendParamsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAILTEMPLATEID)) {
            builder.setMailTemplateId(this.getMailTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SENDER) && this.sender != null) {
            final boolean needClear = !builder.hasSender();
            final int tmpFieldCnt = this.sender.copyChangeToAndClearDeleteKeysCs(builder.getSenderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSender();
            }
        }
        if (this.hasMark(FIELD_INDEX_CONTENT) && this.content != null) {
            final boolean needClear = !builder.hasContent();
            final int tmpFieldCnt = this.content.copyChangeToAndClearDeleteKeysCs(builder.getContentBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContent();
            }
        }
        if (this.hasMark(FIELD_INDEX_TITLE) && this.title != null) {
            final boolean needClear = !builder.hasTitle();
            final int tmpFieldCnt = this.title.copyChangeToAndClearDeleteKeysCs(builder.getTitleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTitle();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            final boolean needClear = !builder.hasItemReward();
            final int tmpFieldCnt = this.itemReward.copyChangeToCs(builder.getItemRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemReward();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXPIRETIMESTAMP)) {
            builder.setExpireTimestamp(this.getExpireTimestamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISONLYFORSHOW)) {
            builder.setIsOnlyForShow(this.getIsOnlyForShow());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HASINVITEDSTATUS)) {
            builder.setHasInvitedStatus(this.getHasInvitedStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ADDITEMBYMAIL) && this.addItemByMail != null) {
            final boolean needClear = !builder.hasAddItemByMail();
            final int tmpFieldCnt = this.addItemByMail.copyChangeToAndClearDeleteKeysCs(builder.getAddItemByMailBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAddItemByMail();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MailSendParamsPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMailTemplateId()) {
            this.innerSetMailTemplateId(proto.getMailTemplateId());
        } else {
            this.innerSetMailTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSender()) {
            this.getSender().mergeFromCs(proto.getSender());
        } else {
            if (this.sender != null) {
                this.sender.mergeFromCs(proto.getSender());
            }
        }
        if (proto.hasContent()) {
            this.getContent().mergeFromCs(proto.getContent());
        } else {
            if (this.content != null) {
                this.content.mergeFromCs(proto.getContent());
            }
        }
        if (proto.hasTitle()) {
            this.getTitle().mergeFromCs(proto.getTitle());
        } else {
            if (this.title != null) {
                this.title.mergeFromCs(proto.getTitle());
            }
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeFromCs(proto.getItemReward());
        } else {
            if (this.itemReward != null) {
                this.itemReward.mergeFromCs(proto.getItemReward());
            }
        }
        if (proto.hasExpireTimestamp()) {
            this.innerSetExpireTimestamp(proto.getExpireTimestamp());
        } else {
            this.innerSetExpireTimestamp(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIsOnlyForShow()) {
            this.innerSetIsOnlyForShow(proto.getIsOnlyForShow());
        } else {
            this.innerSetIsOnlyForShow(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasHasInvitedStatus()) {
            this.innerSetHasInvitedStatus(proto.getHasInvitedStatus());
        } else {
            this.innerSetHasInvitedStatus(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasAddItemByMail()) {
            this.getAddItemByMail().mergeFromCs(proto.getAddItemByMail());
        } else {
            if (this.addItemByMail != null) {
                this.addItemByMail.mergeFromCs(proto.getAddItemByMail());
            }
        }
        this.markAll();
        return MailSendParamsProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MailSendParamsPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMailTemplateId()) {
            this.setMailTemplateId(proto.getMailTemplateId());
            fieldCnt++;
        }
        if (proto.hasSender()) {
            this.getSender().mergeChangeFromCs(proto.getSender());
            fieldCnt++;
        }
        if (proto.hasContent()) {
            this.getContent().mergeChangeFromCs(proto.getContent());
            fieldCnt++;
        }
        if (proto.hasTitle()) {
            this.getTitle().mergeChangeFromCs(proto.getTitle());
            fieldCnt++;
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeChangeFromCs(proto.getItemReward());
            fieldCnt++;
        }
        if (proto.hasExpireTimestamp()) {
            this.setExpireTimestamp(proto.getExpireTimestamp());
            fieldCnt++;
        }
        if (proto.hasIsOnlyForShow()) {
            this.setIsOnlyForShow(proto.getIsOnlyForShow());
            fieldCnt++;
        }
        if (proto.hasHasInvitedStatus()) {
            this.setHasInvitedStatus(proto.getHasInvitedStatus());
            fieldCnt++;
        }
        if (proto.hasAddItemByMail()) {
            this.getAddItemByMail().mergeChangeFromCs(proto.getAddItemByMail());
            fieldCnt++;
        }
        return fieldCnt;
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailSendParams.Builder getCopySsBuilder() {
        final MailSendParams.Builder builder = MailSendParams.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MailSendParams.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMailTemplateId() != 0) {
            builder.setMailTemplateId(this.getMailTemplateId());
            fieldCnt++;
        }  else if (builder.hasMailTemplateId()) {
            // 清理MailTemplateId
            builder.clearMailTemplateId();
            fieldCnt++;
        }
        if (this.sender != null) {
            StructMail.MailSender.Builder tmpBuilder = StructMail.MailSender.newBuilder();
            final int tmpFieldCnt = this.sender.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSender(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSender();
            }
        }  else if (builder.hasSender()) {
            // 清理Sender
            builder.clearSender();
            fieldCnt++;
        }
        if (this.content != null) {
            StructMail.MailContent.Builder tmpBuilder = StructMail.MailContent.newBuilder();
            final int tmpFieldCnt = this.content.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setContent(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearContent();
            }
        }  else if (builder.hasContent()) {
            // 清理Content
            builder.clearContent();
            fieldCnt++;
        }
        if (this.title != null) {
            StructMail.MailShowTitle.Builder tmpBuilder = StructMail.MailShowTitle.newBuilder();
            final int tmpFieldCnt = this.title.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTitle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTitle();
            }
        }  else if (builder.hasTitle()) {
            // 清理Title
            builder.clearTitle();
            fieldCnt++;
        }
        if (this.itemReward != null) {
            Struct.ItemPairList.Builder tmpBuilder = Struct.ItemPairList.newBuilder();
            final int tmpFieldCnt = this.itemReward.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItemReward(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItemReward();
            }
        }  else if (builder.hasItemReward()) {
            // 清理ItemReward
            builder.clearItemReward();
            fieldCnt++;
        }
        if (this.getExpireTimestamp() != 0L) {
            builder.setExpireTimestamp(this.getExpireTimestamp());
            fieldCnt++;
        }  else if (builder.hasExpireTimestamp()) {
            // 清理ExpireTimestamp
            builder.clearExpireTimestamp();
            fieldCnt++;
        }
        if (this.getIsOnlyForShow()) {
            builder.setIsOnlyForShow(this.getIsOnlyForShow());
            fieldCnt++;
        }  else if (builder.hasIsOnlyForShow()) {
            // 清理IsOnlyForShow
            builder.clearIsOnlyForShow();
            fieldCnt++;
        }
        if (this.getHasInvitedStatus()) {
            builder.setHasInvitedStatus(this.getHasInvitedStatus());
            fieldCnt++;
        }  else if (builder.hasHasInvitedStatus()) {
            // 清理HasInvitedStatus
            builder.clearHasInvitedStatus();
            fieldCnt++;
        }
        if (this.addItemByMail != null) {
            StructMail.AddItemByMail.Builder tmpBuilder = StructMail.AddItemByMail.newBuilder();
            final int tmpFieldCnt = this.addItemByMail.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAddItemByMail(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAddItemByMail();
            }
        }  else if (builder.hasAddItemByMail()) {
            // 清理AddItemByMail
            builder.clearAddItemByMail();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MailSendParams.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAILTEMPLATEID)) {
            builder.setMailTemplateId(this.getMailTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SENDER) && this.sender != null) {
            final boolean needClear = !builder.hasSender();
            final int tmpFieldCnt = this.sender.copyChangeToSs(builder.getSenderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSender();
            }
        }
        if (this.hasMark(FIELD_INDEX_CONTENT) && this.content != null) {
            final boolean needClear = !builder.hasContent();
            final int tmpFieldCnt = this.content.copyChangeToSs(builder.getContentBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContent();
            }
        }
        if (this.hasMark(FIELD_INDEX_TITLE) && this.title != null) {
            final boolean needClear = !builder.hasTitle();
            final int tmpFieldCnt = this.title.copyChangeToSs(builder.getTitleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTitle();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            final boolean needClear = !builder.hasItemReward();
            final int tmpFieldCnt = this.itemReward.copyChangeToSs(builder.getItemRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItemReward();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXPIRETIMESTAMP)) {
            builder.setExpireTimestamp(this.getExpireTimestamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISONLYFORSHOW)) {
            builder.setIsOnlyForShow(this.getIsOnlyForShow());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HASINVITEDSTATUS)) {
            builder.setHasInvitedStatus(this.getHasInvitedStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ADDITEMBYMAIL) && this.addItemByMail != null) {
            final boolean needClear = !builder.hasAddItemByMail();
            final int tmpFieldCnt = this.addItemByMail.copyChangeToSs(builder.getAddItemByMailBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAddItemByMail();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MailSendParams proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMailTemplateId()) {
            this.innerSetMailTemplateId(proto.getMailTemplateId());
        } else {
            this.innerSetMailTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSender()) {
            this.getSender().mergeFromSs(proto.getSender());
        } else {
            if (this.sender != null) {
                this.sender.mergeFromSs(proto.getSender());
            }
        }
        if (proto.hasContent()) {
            this.getContent().mergeFromSs(proto.getContent());
        } else {
            if (this.content != null) {
                this.content.mergeFromSs(proto.getContent());
            }
        }
        if (proto.hasTitle()) {
            this.getTitle().mergeFromSs(proto.getTitle());
        } else {
            if (this.title != null) {
                this.title.mergeFromSs(proto.getTitle());
            }
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeFromSs(proto.getItemReward());
        } else {
            if (this.itemReward != null) {
                this.itemReward.mergeFromSs(proto.getItemReward());
            }
        }
        if (proto.hasExpireTimestamp()) {
            this.innerSetExpireTimestamp(proto.getExpireTimestamp());
        } else {
            this.innerSetExpireTimestamp(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIsOnlyForShow()) {
            this.innerSetIsOnlyForShow(proto.getIsOnlyForShow());
        } else {
            this.innerSetIsOnlyForShow(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasHasInvitedStatus()) {
            this.innerSetHasInvitedStatus(proto.getHasInvitedStatus());
        } else {
            this.innerSetHasInvitedStatus(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasAddItemByMail()) {
            this.getAddItemByMail().mergeFromSs(proto.getAddItemByMail());
        } else {
            if (this.addItemByMail != null) {
                this.addItemByMail.mergeFromSs(proto.getAddItemByMail());
            }
        }
        this.markAll();
        return MailSendParamsProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MailSendParams proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMailTemplateId()) {
            this.setMailTemplateId(proto.getMailTemplateId());
            fieldCnt++;
        }
        if (proto.hasSender()) {
            this.getSender().mergeChangeFromSs(proto.getSender());
            fieldCnt++;
        }
        if (proto.hasContent()) {
            this.getContent().mergeChangeFromSs(proto.getContent());
            fieldCnt++;
        }
        if (proto.hasTitle()) {
            this.getTitle().mergeChangeFromSs(proto.getTitle());
            fieldCnt++;
        }
        if (proto.hasItemReward()) {
            this.getItemReward().mergeChangeFromSs(proto.getItemReward());
            fieldCnt++;
        }
        if (proto.hasExpireTimestamp()) {
            this.setExpireTimestamp(proto.getExpireTimestamp());
            fieldCnt++;
        }
        if (proto.hasIsOnlyForShow()) {
            this.setIsOnlyForShow(proto.getIsOnlyForShow());
            fieldCnt++;
        }
        if (proto.hasHasInvitedStatus()) {
            this.setHasInvitedStatus(proto.getHasInvitedStatus());
            fieldCnt++;
        }
        if (proto.hasAddItemByMail()) {
            this.getAddItemByMail().mergeChangeFromSs(proto.getAddItemByMail());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MailSendParams.Builder builder = MailSendParams.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SENDER) && this.sender != null) {
            this.sender.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CONTENT) && this.content != null) {
            this.content.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TITLE) && this.title != null) {
            this.title.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ITEMREWARD) && this.itemReward != null) {
            this.itemReward.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ADDITEMBYMAIL) && this.addItemByMail != null) {
            this.addItemByMail.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.sender != null) {
            this.sender.markAll();
        }
        if (this.content != null) {
            this.content.markAll();
        }
        if (this.title != null) {
            this.title.markAll();
        }
        if (this.itemReward != null) {
            this.itemReward.markAll();
        }
        if (this.addItemByMail != null) {
            this.addItemByMail.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MailSendParamsProp)) {
            return false;
        }
        final MailSendParamsProp otherNode = (MailSendParamsProp) node;
        if (this.mailTemplateId != otherNode.mailTemplateId) {
            return false;
        }
        if (!this.getSender().compareDataTo(otherNode.getSender())) {
            return false;
        }
        if (!this.getContent().compareDataTo(otherNode.getContent())) {
            return false;
        }
        if (!this.getTitle().compareDataTo(otherNode.getTitle())) {
            return false;
        }
        if (!this.getItemReward().compareDataTo(otherNode.getItemReward())) {
            return false;
        }
        if (this.expireTimestamp != otherNode.expireTimestamp) {
            return false;
        }
        if (this.isOnlyForShow != otherNode.isOnlyForShow) {
            return false;
        }
        if (this.hasInvitedStatus != otherNode.hasInvitedStatus) {
            return false;
        }
        if (!this.getAddItemByMail().compareDataTo(otherNode.getAddItemByMail())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 55;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.SceneTransportPlane;
import com.yorha.proto.StructPB.SceneTransportPlanePB;


/**
 * <AUTHOR> auto gen
 */
public class SceneTransportPlaneProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_PLANEMODEL = 1;
    public static final int FIELD_INDEX_PLANESPEED = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private long id = Constant.DEFAULT_LONG_VALUE;
    private int planeModel = Constant.DEFAULT_INT_VALUE;
    private int planeSpeed = Constant.DEFAULT_INT_VALUE;

    public SceneTransportPlaneProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SceneTransportPlaneProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public long getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public SceneTransportPlaneProp setId(long id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(long id) {
        this.id = id;
    }

    /**
     * get planeModel
     *
     * @return planeModel value
     */
    public int getPlaneModel() {
        return this.planeModel;
    }

    /**
     * set planeModel && set marked
     *
     * @param planeModel new value
     * @return current object
     */
    public SceneTransportPlaneProp setPlaneModel(int planeModel) {
        if (this.planeModel != planeModel) {
            this.mark(FIELD_INDEX_PLANEMODEL);
            this.planeModel = planeModel;
        }
        return this;
    }

    /**
     * inner set planeModel
     *
     * @param planeModel new value
     */
    private void innerSetPlaneModel(int planeModel) {
        this.planeModel = planeModel;
    }

    /**
     * get planeSpeed
     *
     * @return planeSpeed value
     */
    public int getPlaneSpeed() {
        return this.planeSpeed;
    }

    /**
     * set planeSpeed && set marked
     *
     * @param planeSpeed new value
     * @return current object
     */
    public SceneTransportPlaneProp setPlaneSpeed(int planeSpeed) {
        if (this.planeSpeed != planeSpeed) {
            this.mark(FIELD_INDEX_PLANESPEED);
            this.planeSpeed = planeSpeed;
        }
        return this;
    }

    /**
     * inner set planeSpeed
     *
     * @param planeSpeed new value
     */
    private void innerSetPlaneSpeed(int planeSpeed) {
        this.planeSpeed = planeSpeed;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneTransportPlanePB.Builder getCopyCsBuilder() {
        final SceneTransportPlanePB.Builder builder = SceneTransportPlanePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SceneTransportPlanePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getPlaneModel() != 0) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }  else if (builder.hasPlaneModel()) {
            // 清理PlaneModel
            builder.clearPlaneModel();
            fieldCnt++;
        }
        if (this.getPlaneSpeed() != 0) {
            builder.setPlaneSpeed(this.getPlaneSpeed());
            fieldCnt++;
        }  else if (builder.hasPlaneSpeed()) {
            // 清理PlaneSpeed
            builder.clearPlaneSpeed();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SceneTransportPlanePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANEMODEL)) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANESPEED)) {
            builder.setPlaneSpeed(this.getPlaneSpeed());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SceneTransportPlanePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANEMODEL)) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANESPEED)) {
            builder.setPlaneSpeed(this.getPlaneSpeed());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SceneTransportPlanePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlaneModel()) {
            this.innerSetPlaneModel(proto.getPlaneModel());
        } else {
            this.innerSetPlaneModel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPlaneSpeed()) {
            this.innerSetPlaneSpeed(proto.getPlaneSpeed());
        } else {
            this.innerSetPlaneSpeed(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SceneTransportPlaneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SceneTransportPlanePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasPlaneModel()) {
            this.setPlaneModel(proto.getPlaneModel());
            fieldCnt++;
        }
        if (proto.hasPlaneSpeed()) {
            this.setPlaneSpeed(proto.getPlaneSpeed());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneTransportPlane.Builder getCopyDbBuilder() {
        final SceneTransportPlane.Builder builder = SceneTransportPlane.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SceneTransportPlane.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getPlaneModel() != 0) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }  else if (builder.hasPlaneModel()) {
            // 清理PlaneModel
            builder.clearPlaneModel();
            fieldCnt++;
        }
        if (this.getPlaneSpeed() != 0) {
            builder.setPlaneSpeed(this.getPlaneSpeed());
            fieldCnt++;
        }  else if (builder.hasPlaneSpeed()) {
            // 清理PlaneSpeed
            builder.clearPlaneSpeed();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SceneTransportPlane.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANEMODEL)) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANESPEED)) {
            builder.setPlaneSpeed(this.getPlaneSpeed());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SceneTransportPlane proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlaneModel()) {
            this.innerSetPlaneModel(proto.getPlaneModel());
        } else {
            this.innerSetPlaneModel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPlaneSpeed()) {
            this.innerSetPlaneSpeed(proto.getPlaneSpeed());
        } else {
            this.innerSetPlaneSpeed(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SceneTransportPlaneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SceneTransportPlane proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasPlaneModel()) {
            this.setPlaneModel(proto.getPlaneModel());
            fieldCnt++;
        }
        if (proto.hasPlaneSpeed()) {
            this.setPlaneSpeed(proto.getPlaneSpeed());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneTransportPlane.Builder getCopySsBuilder() {
        final SceneTransportPlane.Builder builder = SceneTransportPlane.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SceneTransportPlane.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getPlaneModel() != 0) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }  else if (builder.hasPlaneModel()) {
            // 清理PlaneModel
            builder.clearPlaneModel();
            fieldCnt++;
        }
        if (this.getPlaneSpeed() != 0) {
            builder.setPlaneSpeed(this.getPlaneSpeed());
            fieldCnt++;
        }  else if (builder.hasPlaneSpeed()) {
            // 清理PlaneSpeed
            builder.clearPlaneSpeed();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SceneTransportPlane.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANEMODEL)) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANESPEED)) {
            builder.setPlaneSpeed(this.getPlaneSpeed());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SceneTransportPlane proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlaneModel()) {
            this.innerSetPlaneModel(proto.getPlaneModel());
        } else {
            this.innerSetPlaneModel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPlaneSpeed()) {
            this.innerSetPlaneSpeed(proto.getPlaneSpeed());
        } else {
            this.innerSetPlaneSpeed(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SceneTransportPlaneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SceneTransportPlane proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasPlaneModel()) {
            this.setPlaneModel(proto.getPlaneModel());
            fieldCnt++;
        }
        if (proto.hasPlaneSpeed()) {
            this.setPlaneSpeed(proto.getPlaneSpeed());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SceneTransportPlane.Builder builder = SceneTransportPlane.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SceneTransportPlaneProp)) {
            return false;
        }
        final SceneTransportPlaneProp otherNode = (SceneTransportPlaneProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.planeModel != otherNode.planeModel) {
            return false;
        }
        if (this.planeSpeed != otherNode.planeSpeed) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
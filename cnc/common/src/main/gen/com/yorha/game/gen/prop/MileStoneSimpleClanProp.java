package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.MileStoneSimpleClan;
import com.yorha.proto.StructPB.MileStoneSimpleClanPB;


/**
 * <AUTHOR> auto gen
 */
public class MileStoneSimpleClanProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CLANID = 0;
    public static final int FIELD_INDEX_NAME = 1;
    public static final int FIELD_INDEX_SIMPLENAME = 2;
    public static final int FIELD_INDEX_FLAGCOLOR = 3;
    public static final int FIELD_INDEX_FLAGSHADING = 4;
    public static final int FIELD_INDEX_FLAGSIGN = 5;
    public static final int FIELD_INDEX_TERRITORYCOLOR = 6;
    public static final int FIELD_INDEX_NATIONFLAGID = 7;

    public static final int FIELD_COUNT = 8;

    private long markBits0 = 0L;

    private long clanId = Constant.DEFAULT_LONG_VALUE;
    private String name = Constant.DEFAULT_STR_VALUE;
    private String simpleName = Constant.DEFAULT_STR_VALUE;
    private int flagColor = Constant.DEFAULT_INT_VALUE;
    private int flagShading = Constant.DEFAULT_INT_VALUE;
    private int flagSign = Constant.DEFAULT_INT_VALUE;
    private int territoryColor = Constant.DEFAULT_INT_VALUE;
    private int nationFlagId = Constant.DEFAULT_INT_VALUE;

    public MileStoneSimpleClanProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MileStoneSimpleClanProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get clanId
     *
     * @return clanId value
     */
    public long getClanId() {
        return this.clanId;
    }

    /**
     * set clanId && set marked
     *
     * @param clanId new value
     * @return current object
     */
    public MileStoneSimpleClanProp setClanId(long clanId) {
        if (this.clanId != clanId) {
            this.mark(FIELD_INDEX_CLANID);
            this.clanId = clanId;
        }
        return this;
    }

    /**
     * inner set clanId
     *
     * @param clanId new value
     */
    private void innerSetClanId(long clanId) {
        this.clanId = clanId;
    }

    /**
     * get name
     *
     * @return name value
     */
    public String getName() {
        return this.name;
    }

    /**
     * set name && set marked
     *
     * @param name new value
     * @return current object
     */
    public MileStoneSimpleClanProp setName(String name) {
        if (name == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.name, name)) {
            this.mark(FIELD_INDEX_NAME);
            this.name = name;
        }
        return this;
    }

    /**
     * inner set name
     *
     * @param name new value
     */
    private void innerSetName(String name) {
        this.name = name;
    }

    /**
     * get simpleName
     *
     * @return simpleName value
     */
    public String getSimpleName() {
        return this.simpleName;
    }

    /**
     * set simpleName && set marked
     *
     * @param simpleName new value
     * @return current object
     */
    public MileStoneSimpleClanProp setSimpleName(String simpleName) {
        if (simpleName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.simpleName, simpleName)) {
            this.mark(FIELD_INDEX_SIMPLENAME);
            this.simpleName = simpleName;
        }
        return this;
    }

    /**
     * inner set simpleName
     *
     * @param simpleName new value
     */
    private void innerSetSimpleName(String simpleName) {
        this.simpleName = simpleName;
    }

    /**
     * get flagColor
     *
     * @return flagColor value
     */
    public int getFlagColor() {
        return this.flagColor;
    }

    /**
     * set flagColor && set marked
     *
     * @param flagColor new value
     * @return current object
     */
    public MileStoneSimpleClanProp setFlagColor(int flagColor) {
        if (this.flagColor != flagColor) {
            this.mark(FIELD_INDEX_FLAGCOLOR);
            this.flagColor = flagColor;
        }
        return this;
    }

    /**
     * inner set flagColor
     *
     * @param flagColor new value
     */
    private void innerSetFlagColor(int flagColor) {
        this.flagColor = flagColor;
    }

    /**
     * get flagShading
     *
     * @return flagShading value
     */
    public int getFlagShading() {
        return this.flagShading;
    }

    /**
     * set flagShading && set marked
     *
     * @param flagShading new value
     * @return current object
     */
    public MileStoneSimpleClanProp setFlagShading(int flagShading) {
        if (this.flagShading != flagShading) {
            this.mark(FIELD_INDEX_FLAGSHADING);
            this.flagShading = flagShading;
        }
        return this;
    }

    /**
     * inner set flagShading
     *
     * @param flagShading new value
     */
    private void innerSetFlagShading(int flagShading) {
        this.flagShading = flagShading;
    }

    /**
     * get flagSign
     *
     * @return flagSign value
     */
    public int getFlagSign() {
        return this.flagSign;
    }

    /**
     * set flagSign && set marked
     *
     * @param flagSign new value
     * @return current object
     */
    public MileStoneSimpleClanProp setFlagSign(int flagSign) {
        if (this.flagSign != flagSign) {
            this.mark(FIELD_INDEX_FLAGSIGN);
            this.flagSign = flagSign;
        }
        return this;
    }

    /**
     * inner set flagSign
     *
     * @param flagSign new value
     */
    private void innerSetFlagSign(int flagSign) {
        this.flagSign = flagSign;
    }

    /**
     * get territoryColor
     *
     * @return territoryColor value
     */
    public int getTerritoryColor() {
        return this.territoryColor;
    }

    /**
     * set territoryColor && set marked
     *
     * @param territoryColor new value
     * @return current object
     */
    public MileStoneSimpleClanProp setTerritoryColor(int territoryColor) {
        if (this.territoryColor != territoryColor) {
            this.mark(FIELD_INDEX_TERRITORYCOLOR);
            this.territoryColor = territoryColor;
        }
        return this;
    }

    /**
     * inner set territoryColor
     *
     * @param territoryColor new value
     */
    private void innerSetTerritoryColor(int territoryColor) {
        this.territoryColor = territoryColor;
    }

    /**
     * get nationFlagId
     *
     * @return nationFlagId value
     */
    public int getNationFlagId() {
        return this.nationFlagId;
    }

    /**
     * set nationFlagId && set marked
     *
     * @param nationFlagId new value
     * @return current object
     */
    public MileStoneSimpleClanProp setNationFlagId(int nationFlagId) {
        if (this.nationFlagId != nationFlagId) {
            this.mark(FIELD_INDEX_NATIONFLAGID);
            this.nationFlagId = nationFlagId;
        }
        return this;
    }

    /**
     * inner set nationFlagId
     *
     * @param nationFlagId new value
     */
    private void innerSetNationFlagId(int nationFlagId) {
        this.nationFlagId = nationFlagId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneSimpleClanPB.Builder getCopyCsBuilder() {
        final MileStoneSimpleClanPB.Builder builder = MileStoneSimpleClanPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MileStoneSimpleClanPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        if (!this.getSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSimpleName(this.getSimpleName());
            fieldCnt++;
        }  else if (builder.hasSimpleName()) {
            // 清理SimpleName
            builder.clearSimpleName();
            fieldCnt++;
        }
        if (this.getFlagColor() != 0) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }  else if (builder.hasFlagColor()) {
            // 清理FlagColor
            builder.clearFlagColor();
            fieldCnt++;
        }
        if (this.getFlagShading() != 0) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }  else if (builder.hasFlagShading()) {
            // 清理FlagShading
            builder.clearFlagShading();
            fieldCnt++;
        }
        if (this.getFlagSign() != 0) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }  else if (builder.hasFlagSign()) {
            // 清理FlagSign
            builder.clearFlagSign();
            fieldCnt++;
        }
        if (this.getTerritoryColor() != 0) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }  else if (builder.hasTerritoryColor()) {
            // 清理TerritoryColor
            builder.clearTerritoryColor();
            fieldCnt++;
        }
        if (this.getNationFlagId() != 0) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }  else if (builder.hasNationFlagId()) {
            // 清理NationFlagId
            builder.clearNationFlagId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MileStoneSimpleClanPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SIMPLENAME)) {
            builder.setSimpleName(this.getSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGCOLOR)) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSHADING)) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSIGN)) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TERRITORYCOLOR)) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MileStoneSimpleClanPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SIMPLENAME)) {
            builder.setSimpleName(this.getSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGCOLOR)) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSHADING)) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSIGN)) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TERRITORYCOLOR)) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MileStoneSimpleClanPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasSimpleName()) {
            this.innerSetSimpleName(proto.getSimpleName());
        } else {
            this.innerSetSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasFlagColor()) {
            this.innerSetFlagColor(proto.getFlagColor());
        } else {
            this.innerSetFlagColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagShading()) {
            this.innerSetFlagShading(proto.getFlagShading());
        } else {
            this.innerSetFlagShading(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagSign()) {
            this.innerSetFlagSign(proto.getFlagSign());
        } else {
            this.innerSetFlagSign(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTerritoryColor()) {
            this.innerSetTerritoryColor(proto.getTerritoryColor());
        } else {
            this.innerSetTerritoryColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNationFlagId()) {
            this.innerSetNationFlagId(proto.getNationFlagId());
        } else {
            this.innerSetNationFlagId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return MileStoneSimpleClanProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MileStoneSimpleClanPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        if (proto.hasSimpleName()) {
            this.setSimpleName(proto.getSimpleName());
            fieldCnt++;
        }
        if (proto.hasFlagColor()) {
            this.setFlagColor(proto.getFlagColor());
            fieldCnt++;
        }
        if (proto.hasFlagShading()) {
            this.setFlagShading(proto.getFlagShading());
            fieldCnt++;
        }
        if (proto.hasFlagSign()) {
            this.setFlagSign(proto.getFlagSign());
            fieldCnt++;
        }
        if (proto.hasTerritoryColor()) {
            this.setTerritoryColor(proto.getTerritoryColor());
            fieldCnt++;
        }
        if (proto.hasNationFlagId()) {
            this.setNationFlagId(proto.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneSimpleClan.Builder getCopyDbBuilder() {
        final MileStoneSimpleClan.Builder builder = MileStoneSimpleClan.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MileStoneSimpleClan.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        if (!this.getSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSimpleName(this.getSimpleName());
            fieldCnt++;
        }  else if (builder.hasSimpleName()) {
            // 清理SimpleName
            builder.clearSimpleName();
            fieldCnt++;
        }
        if (this.getFlagColor() != 0) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }  else if (builder.hasFlagColor()) {
            // 清理FlagColor
            builder.clearFlagColor();
            fieldCnt++;
        }
        if (this.getFlagShading() != 0) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }  else if (builder.hasFlagShading()) {
            // 清理FlagShading
            builder.clearFlagShading();
            fieldCnt++;
        }
        if (this.getFlagSign() != 0) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }  else if (builder.hasFlagSign()) {
            // 清理FlagSign
            builder.clearFlagSign();
            fieldCnt++;
        }
        if (this.getTerritoryColor() != 0) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }  else if (builder.hasTerritoryColor()) {
            // 清理TerritoryColor
            builder.clearTerritoryColor();
            fieldCnt++;
        }
        if (this.getNationFlagId() != 0) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }  else if (builder.hasNationFlagId()) {
            // 清理NationFlagId
            builder.clearNationFlagId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MileStoneSimpleClan.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SIMPLENAME)) {
            builder.setSimpleName(this.getSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGCOLOR)) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSHADING)) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSIGN)) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TERRITORYCOLOR)) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MileStoneSimpleClan proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasSimpleName()) {
            this.innerSetSimpleName(proto.getSimpleName());
        } else {
            this.innerSetSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasFlagColor()) {
            this.innerSetFlagColor(proto.getFlagColor());
        } else {
            this.innerSetFlagColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagShading()) {
            this.innerSetFlagShading(proto.getFlagShading());
        } else {
            this.innerSetFlagShading(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagSign()) {
            this.innerSetFlagSign(proto.getFlagSign());
        } else {
            this.innerSetFlagSign(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTerritoryColor()) {
            this.innerSetTerritoryColor(proto.getTerritoryColor());
        } else {
            this.innerSetTerritoryColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNationFlagId()) {
            this.innerSetNationFlagId(proto.getNationFlagId());
        } else {
            this.innerSetNationFlagId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return MileStoneSimpleClanProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MileStoneSimpleClan proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        if (proto.hasSimpleName()) {
            this.setSimpleName(proto.getSimpleName());
            fieldCnt++;
        }
        if (proto.hasFlagColor()) {
            this.setFlagColor(proto.getFlagColor());
            fieldCnt++;
        }
        if (proto.hasFlagShading()) {
            this.setFlagShading(proto.getFlagShading());
            fieldCnt++;
        }
        if (proto.hasFlagSign()) {
            this.setFlagSign(proto.getFlagSign());
            fieldCnt++;
        }
        if (proto.hasTerritoryColor()) {
            this.setTerritoryColor(proto.getTerritoryColor());
            fieldCnt++;
        }
        if (proto.hasNationFlagId()) {
            this.setNationFlagId(proto.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneSimpleClan.Builder getCopySsBuilder() {
        final MileStoneSimpleClan.Builder builder = MileStoneSimpleClan.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MileStoneSimpleClan.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        if (!this.getSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSimpleName(this.getSimpleName());
            fieldCnt++;
        }  else if (builder.hasSimpleName()) {
            // 清理SimpleName
            builder.clearSimpleName();
            fieldCnt++;
        }
        if (this.getFlagColor() != 0) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }  else if (builder.hasFlagColor()) {
            // 清理FlagColor
            builder.clearFlagColor();
            fieldCnt++;
        }
        if (this.getFlagShading() != 0) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }  else if (builder.hasFlagShading()) {
            // 清理FlagShading
            builder.clearFlagShading();
            fieldCnt++;
        }
        if (this.getFlagSign() != 0) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }  else if (builder.hasFlagSign()) {
            // 清理FlagSign
            builder.clearFlagSign();
            fieldCnt++;
        }
        if (this.getTerritoryColor() != 0) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }  else if (builder.hasTerritoryColor()) {
            // 清理TerritoryColor
            builder.clearTerritoryColor();
            fieldCnt++;
        }
        if (this.getNationFlagId() != 0) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }  else if (builder.hasNationFlagId()) {
            // 清理NationFlagId
            builder.clearNationFlagId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MileStoneSimpleClan.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SIMPLENAME)) {
            builder.setSimpleName(this.getSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGCOLOR)) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSHADING)) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSIGN)) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TERRITORYCOLOR)) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MileStoneSimpleClan proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasSimpleName()) {
            this.innerSetSimpleName(proto.getSimpleName());
        } else {
            this.innerSetSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasFlagColor()) {
            this.innerSetFlagColor(proto.getFlagColor());
        } else {
            this.innerSetFlagColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagShading()) {
            this.innerSetFlagShading(proto.getFlagShading());
        } else {
            this.innerSetFlagShading(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagSign()) {
            this.innerSetFlagSign(proto.getFlagSign());
        } else {
            this.innerSetFlagSign(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTerritoryColor()) {
            this.innerSetTerritoryColor(proto.getTerritoryColor());
        } else {
            this.innerSetTerritoryColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNationFlagId()) {
            this.innerSetNationFlagId(proto.getNationFlagId());
        } else {
            this.innerSetNationFlagId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return MileStoneSimpleClanProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MileStoneSimpleClan proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        if (proto.hasSimpleName()) {
            this.setSimpleName(proto.getSimpleName());
            fieldCnt++;
        }
        if (proto.hasFlagColor()) {
            this.setFlagColor(proto.getFlagColor());
            fieldCnt++;
        }
        if (proto.hasFlagShading()) {
            this.setFlagShading(proto.getFlagShading());
            fieldCnt++;
        }
        if (proto.hasFlagSign()) {
            this.setFlagSign(proto.getFlagSign());
            fieldCnt++;
        }
        if (proto.hasTerritoryColor()) {
            this.setTerritoryColor(proto.getTerritoryColor());
            fieldCnt++;
        }
        if (proto.hasNationFlagId()) {
            this.setNationFlagId(proto.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MileStoneSimpleClan.Builder builder = MileStoneSimpleClan.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MileStoneSimpleClanProp)) {
            return false;
        }
        final MileStoneSimpleClanProp otherNode = (MileStoneSimpleClanProp) node;
        if (this.clanId != otherNode.clanId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.name, otherNode.name)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.simpleName, otherNode.simpleName)) {
            return false;
        }
        if (this.flagColor != otherNode.flagColor) {
            return false;
        }
        if (this.flagShading != otherNode.flagShading) {
            return false;
        }
        if (this.flagSign != otherNode.flagSign) {
            return false;
        }
        if (this.territoryColor != otherNode.territoryColor) {
            return false;
        }
        if (this.nationFlagId != otherNode.nationFlagId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 56;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zone.KingdomSentGiftInfo;
import com.yorha.proto.Struct;
import com.yorha.proto.ZonePB.KingdomSentGiftInfoPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class KingdomSentGiftInfoProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_PLAYERID = 0;
    public static final int FIELD_INDEX_SENTTSMS = 1;
    public static final int FIELD_INDEX_PLAYERCARDHEAD = 2;
    public static final int FIELD_INDEX_SNAME = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private long playerId = Constant.DEFAULT_LONG_VALUE;
    private long sentTsMs = Constant.DEFAULT_LONG_VALUE;
    private PlayerCardHeadProp playerCardHead = null;
    private String sname = Constant.DEFAULT_STR_VALUE;

    public KingdomSentGiftInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public KingdomSentGiftInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get playerId
     *
     * @return playerId value
     */
    public long getPlayerId() {
        return this.playerId;
    }

    /**
     * set playerId && set marked
     *
     * @param playerId new value
     * @return current object
     */
    public KingdomSentGiftInfoProp setPlayerId(long playerId) {
        if (this.playerId != playerId) {
            this.mark(FIELD_INDEX_PLAYERID);
            this.playerId = playerId;
        }
        return this;
    }

    /**
     * inner set playerId
     *
     * @param playerId new value
     */
    private void innerSetPlayerId(long playerId) {
        this.playerId = playerId;
    }

    /**
     * get sentTsMs
     *
     * @return sentTsMs value
     */
    public long getSentTsMs() {
        return this.sentTsMs;
    }

    /**
     * set sentTsMs && set marked
     *
     * @param sentTsMs new value
     * @return current object
     */
    public KingdomSentGiftInfoProp setSentTsMs(long sentTsMs) {
        if (this.sentTsMs != sentTsMs) {
            this.mark(FIELD_INDEX_SENTTSMS);
            this.sentTsMs = sentTsMs;
        }
        return this;
    }

    /**
     * inner set sentTsMs
     *
     * @param sentTsMs new value
     */
    private void innerSetSentTsMs(long sentTsMs) {
        this.sentTsMs = sentTsMs;
    }

    /**
     * get playerCardHead
     *
     * @return playerCardHead value
     */
    public PlayerCardHeadProp getPlayerCardHead() {
        if (this.playerCardHead == null) {
            this.playerCardHead = new PlayerCardHeadProp(this, FIELD_INDEX_PLAYERCARDHEAD);
        }
        return this.playerCardHead;
    }

    /**
     * get sname
     *
     * @return sname value
     */
    public String getSname() {
        return this.sname;
    }

    /**
     * set sname && set marked
     *
     * @param sname new value
     * @return current object
     */
    public KingdomSentGiftInfoProp setSname(String sname) {
        if (sname == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.sname, sname)) {
            this.mark(FIELD_INDEX_SNAME);
            this.sname = sname;
        }
        return this;
    }

    /**
     * inner set sname
     *
     * @param sname new value
     */
    private void innerSetSname(String sname) {
        this.sname = sname;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomSentGiftInfoPB.Builder getCopyCsBuilder() {
        final KingdomSentGiftInfoPB.Builder builder = KingdomSentGiftInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(KingdomSentGiftInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getSentTsMs() != 0L) {
            builder.setSentTsMs(this.getSentTsMs());
            fieldCnt++;
        }  else if (builder.hasSentTsMs()) {
            // 清理SentTsMs
            builder.clearSentTsMs();
            fieldCnt++;
        }
        if (this.playerCardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.playerCardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerCardHead();
            }
        }  else if (builder.hasPlayerCardHead()) {
            // 清理PlayerCardHead
            builder.clearPlayerCardHead();
            fieldCnt++;
        }
        if (!this.getSname().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSname(this.getSname());
            fieldCnt++;
        }  else if (builder.hasSname()) {
            // 清理Sname
            builder.clearSname();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(KingdomSentGiftInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SENTTSMS)) {
            builder.setSentTsMs(this.getSentTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCARDHEAD) && this.playerCardHead != null) {
            final boolean needClear = !builder.hasPlayerCardHead();
            final int tmpFieldCnt = this.playerCardHead.copyChangeToCs(builder.getPlayerCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_SNAME)) {
            builder.setSname(this.getSname());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(KingdomSentGiftInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SENTTSMS)) {
            builder.setSentTsMs(this.getSentTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCARDHEAD) && this.playerCardHead != null) {
            final boolean needClear = !builder.hasPlayerCardHead();
            final int tmpFieldCnt = this.playerCardHead.copyChangeToAndClearDeleteKeysCs(builder.getPlayerCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_SNAME)) {
            builder.setSname(this.getSname());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(KingdomSentGiftInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSentTsMs()) {
            this.innerSetSentTsMs(proto.getSentTsMs());
        } else {
            this.innerSetSentTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerCardHead()) {
            this.getPlayerCardHead().mergeFromCs(proto.getPlayerCardHead());
        } else {
            if (this.playerCardHead != null) {
                this.playerCardHead.mergeFromCs(proto.getPlayerCardHead());
            }
        }
        if (proto.hasSname()) {
            this.innerSetSname(proto.getSname());
        } else {
            this.innerSetSname(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return KingdomSentGiftInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(KingdomSentGiftInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasSentTsMs()) {
            this.setSentTsMs(proto.getSentTsMs());
            fieldCnt++;
        }
        if (proto.hasPlayerCardHead()) {
            this.getPlayerCardHead().mergeChangeFromCs(proto.getPlayerCardHead());
            fieldCnt++;
        }
        if (proto.hasSname()) {
            this.setSname(proto.getSname());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomSentGiftInfo.Builder getCopyDbBuilder() {
        final KingdomSentGiftInfo.Builder builder = KingdomSentGiftInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(KingdomSentGiftInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getSentTsMs() != 0L) {
            builder.setSentTsMs(this.getSentTsMs());
            fieldCnt++;
        }  else if (builder.hasSentTsMs()) {
            // 清理SentTsMs
            builder.clearSentTsMs();
            fieldCnt++;
        }
        if (this.playerCardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.playerCardHead.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerCardHead();
            }
        }  else if (builder.hasPlayerCardHead()) {
            // 清理PlayerCardHead
            builder.clearPlayerCardHead();
            fieldCnt++;
        }
        if (!this.getSname().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSname(this.getSname());
            fieldCnt++;
        }  else if (builder.hasSname()) {
            // 清理Sname
            builder.clearSname();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(KingdomSentGiftInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SENTTSMS)) {
            builder.setSentTsMs(this.getSentTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCARDHEAD) && this.playerCardHead != null) {
            final boolean needClear = !builder.hasPlayerCardHead();
            final int tmpFieldCnt = this.playerCardHead.copyChangeToDb(builder.getPlayerCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_SNAME)) {
            builder.setSname(this.getSname());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(KingdomSentGiftInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSentTsMs()) {
            this.innerSetSentTsMs(proto.getSentTsMs());
        } else {
            this.innerSetSentTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerCardHead()) {
            this.getPlayerCardHead().mergeFromDb(proto.getPlayerCardHead());
        } else {
            if (this.playerCardHead != null) {
                this.playerCardHead.mergeFromDb(proto.getPlayerCardHead());
            }
        }
        if (proto.hasSname()) {
            this.innerSetSname(proto.getSname());
        } else {
            this.innerSetSname(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return KingdomSentGiftInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(KingdomSentGiftInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasSentTsMs()) {
            this.setSentTsMs(proto.getSentTsMs());
            fieldCnt++;
        }
        if (proto.hasPlayerCardHead()) {
            this.getPlayerCardHead().mergeChangeFromDb(proto.getPlayerCardHead());
            fieldCnt++;
        }
        if (proto.hasSname()) {
            this.setSname(proto.getSname());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomSentGiftInfo.Builder getCopySsBuilder() {
        final KingdomSentGiftInfo.Builder builder = KingdomSentGiftInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(KingdomSentGiftInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getSentTsMs() != 0L) {
            builder.setSentTsMs(this.getSentTsMs());
            fieldCnt++;
        }  else if (builder.hasSentTsMs()) {
            // 清理SentTsMs
            builder.clearSentTsMs();
            fieldCnt++;
        }
        if (this.playerCardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.playerCardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerCardHead();
            }
        }  else if (builder.hasPlayerCardHead()) {
            // 清理PlayerCardHead
            builder.clearPlayerCardHead();
            fieldCnt++;
        }
        if (!this.getSname().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSname(this.getSname());
            fieldCnt++;
        }  else if (builder.hasSname()) {
            // 清理Sname
            builder.clearSname();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(KingdomSentGiftInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SENTTSMS)) {
            builder.setSentTsMs(this.getSentTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCARDHEAD) && this.playerCardHead != null) {
            final boolean needClear = !builder.hasPlayerCardHead();
            final int tmpFieldCnt = this.playerCardHead.copyChangeToSs(builder.getPlayerCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_SNAME)) {
            builder.setSname(this.getSname());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(KingdomSentGiftInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSentTsMs()) {
            this.innerSetSentTsMs(proto.getSentTsMs());
        } else {
            this.innerSetSentTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerCardHead()) {
            this.getPlayerCardHead().mergeFromSs(proto.getPlayerCardHead());
        } else {
            if (this.playerCardHead != null) {
                this.playerCardHead.mergeFromSs(proto.getPlayerCardHead());
            }
        }
        if (proto.hasSname()) {
            this.innerSetSname(proto.getSname());
        } else {
            this.innerSetSname(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return KingdomSentGiftInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(KingdomSentGiftInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasSentTsMs()) {
            this.setSentTsMs(proto.getSentTsMs());
            fieldCnt++;
        }
        if (proto.hasPlayerCardHead()) {
            this.getPlayerCardHead().mergeChangeFromSs(proto.getPlayerCardHead());
            fieldCnt++;
        }
        if (proto.hasSname()) {
            this.setSname(proto.getSname());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        KingdomSentGiftInfo.Builder builder = KingdomSentGiftInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCARDHEAD) && this.playerCardHead != null) {
            this.playerCardHead.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.playerCardHead != null) {
            this.playerCardHead.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.playerId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof KingdomSentGiftInfoProp)) {
            return false;
        }
        final KingdomSentGiftInfoProp otherNode = (KingdomSentGiftInfoProp) node;
        if (this.playerId != otherNode.playerId) {
            return false;
        }
        if (this.sentTsMs != otherNode.sentTsMs) {
            return false;
        }
        if (!this.getPlayerCardHead().compareDataTo(otherNode.getPlayerCardHead())) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.sname, otherNode.sname)) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
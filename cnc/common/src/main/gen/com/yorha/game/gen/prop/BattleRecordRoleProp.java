package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructBattle.BattleRecordRole;
import com.yorha.proto.Struct;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructBattlePB.BattleRecordRolePB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructBattlePB;


/**
 * <AUTHOR> auto gen
 */
public class BattleRecordRoleProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CLANNAME = 0;
    public static final int FIELD_INDEX_LOCATION = 1;
    public static final int FIELD_INDEX_MAINHERO = 2;
    public static final int FIELD_INDEX_DEPUTYHERO = 3;
    public static final int FIELD_INDEX_REWARD = 4;
    public static final int FIELD_INDEX_SOLDIERREPORT = 5;
    public static final int FIELD_INDEX_LOSTPOWER = 6;
    public static final int FIELD_INDEX_ROLETYPE = 7;
    public static final int FIELD_INDEX_ROLEID = 8;
    public static final int FIELD_INDEX_BUILDINGID = 9;
    public static final int FIELD_INDEX_PLANE = 10;
    public static final int FIELD_INDEX_CARDHEAD = 11;
    public static final int FIELD_INDEX_PLUNDER = 12;
    public static final int FIELD_INDEX_GUARDTOWERREPORT = 13;
    public static final int FIELD_INDEX_ENTITYTYPE = 14;

    public static final int FIELD_COUNT = 15;

    private long markBits0 = 0L;

    private String clanName = Constant.DEFAULT_STR_VALUE;
    private PointProp location = null;
    private BattleRecordHeroSummaryProp mainHero = null;
    private BattleRecordHeroSummaryProp deputyHero = null;
    private BattleRecordRewardProp reward = null;
    private BattleRecordSoldierReportProp soldierReport = null;
    private long lostPower = Constant.DEFAULT_LONG_VALUE;
    private SceneObjType roleType = SceneObjType.forNumber(0);
    private long roleId = Constant.DEFAULT_LONG_VALUE;
    private long buildingId = Constant.DEFAULT_LONG_VALUE;
    private BattleRecordPlaneSummaryProp plane = null;
    private PlayerCardHeadProp cardHead = null;
    private PlunderProp plunder = null;
    private BattleRecordSoldierReportProp guardTowerReport = null;
    private int entityType = Constant.DEFAULT_INT_VALUE;

    public BattleRecordRoleProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BattleRecordRoleProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get clanName
     *
     * @return clanName value
     */
    public String getClanName() {
        return this.clanName;
    }

    /**
     * set clanName && set marked
     *
     * @param clanName new value
     * @return current object
     */
    public BattleRecordRoleProp setClanName(String clanName) {
        if (clanName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanName, clanName)) {
            this.mark(FIELD_INDEX_CLANNAME);
            this.clanName = clanName;
        }
        return this;
    }

    /**
     * inner set clanName
     *
     * @param clanName new value
     */
    private void innerSetClanName(String clanName) {
        this.clanName = clanName;
    }

    /**
     * get location
     *
     * @return location value
     */
    public PointProp getLocation() {
        if (this.location == null) {
            this.location = new PointProp(this, FIELD_INDEX_LOCATION);
        }
        return this.location;
    }

    /**
     * get mainHero
     *
     * @return mainHero value
     */
    public BattleRecordHeroSummaryProp getMainHero() {
        if (this.mainHero == null) {
            this.mainHero = new BattleRecordHeroSummaryProp(this, FIELD_INDEX_MAINHERO);
        }
        return this.mainHero;
    }

    /**
     * get deputyHero
     *
     * @return deputyHero value
     */
    public BattleRecordHeroSummaryProp getDeputyHero() {
        if (this.deputyHero == null) {
            this.deputyHero = new BattleRecordHeroSummaryProp(this, FIELD_INDEX_DEPUTYHERO);
        }
        return this.deputyHero;
    }

    /**
     * get reward
     *
     * @return reward value
     */
    public BattleRecordRewardProp getReward() {
        if (this.reward == null) {
            this.reward = new BattleRecordRewardProp(this, FIELD_INDEX_REWARD);
        }
        return this.reward;
    }

    /**
     * get soldierReport
     *
     * @return soldierReport value
     */
    public BattleRecordSoldierReportProp getSoldierReport() {
        if (this.soldierReport == null) {
            this.soldierReport = new BattleRecordSoldierReportProp(this, FIELD_INDEX_SOLDIERREPORT);
        }
        return this.soldierReport;
    }

    /**
     * get lostPower
     *
     * @return lostPower value
     */
    public long getLostPower() {
        return this.lostPower;
    }

    /**
     * set lostPower && set marked
     *
     * @param lostPower new value
     * @return current object
     */
    public BattleRecordRoleProp setLostPower(long lostPower) {
        if (this.lostPower != lostPower) {
            this.mark(FIELD_INDEX_LOSTPOWER);
            this.lostPower = lostPower;
        }
        return this;
    }

    /**
     * inner set lostPower
     *
     * @param lostPower new value
     */
    private void innerSetLostPower(long lostPower) {
        this.lostPower = lostPower;
    }

    /**
     * get roleType
     *
     * @return roleType value
     */
    public SceneObjType getRoleType() {
        return this.roleType;
    }

    /**
     * set roleType && set marked
     *
     * @param roleType new value
     * @return current object
     */
    public BattleRecordRoleProp setRoleType(SceneObjType roleType) {
        if (roleType == null) {
            throw new NullPointerException();
        }
        if (this.roleType != roleType) {
            this.mark(FIELD_INDEX_ROLETYPE);
            this.roleType = roleType;
        }
        return this;
    }

    /**
     * inner set roleType
     *
     * @param roleType new value
     */
    private void innerSetRoleType(SceneObjType roleType) {
        this.roleType = roleType;
    }

    /**
     * get roleId
     *
     * @return roleId value
     */
    public long getRoleId() {
        return this.roleId;
    }

    /**
     * set roleId && set marked
     *
     * @param roleId new value
     * @return current object
     */
    public BattleRecordRoleProp setRoleId(long roleId) {
        if (this.roleId != roleId) {
            this.mark(FIELD_INDEX_ROLEID);
            this.roleId = roleId;
        }
        return this;
    }

    /**
     * inner set roleId
     *
     * @param roleId new value
     */
    private void innerSetRoleId(long roleId) {
        this.roleId = roleId;
    }

    /**
     * get buildingId
     *
     * @return buildingId value
     */
    public long getBuildingId() {
        return this.buildingId;
    }

    /**
     * set buildingId && set marked
     *
     * @param buildingId new value
     * @return current object
     */
    public BattleRecordRoleProp setBuildingId(long buildingId) {
        if (this.buildingId != buildingId) {
            this.mark(FIELD_INDEX_BUILDINGID);
            this.buildingId = buildingId;
        }
        return this;
    }

    /**
     * inner set buildingId
     *
     * @param buildingId new value
     */
    private void innerSetBuildingId(long buildingId) {
        this.buildingId = buildingId;
    }

    /**
     * get plane
     *
     * @return plane value
     */
    public BattleRecordPlaneSummaryProp getPlane() {
        if (this.plane == null) {
            this.plane = new BattleRecordPlaneSummaryProp(this, FIELD_INDEX_PLANE);
        }
        return this.plane;
    }

    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }

    /**
     * get plunder
     *
     * @return plunder value
     */
    public PlunderProp getPlunder() {
        if (this.plunder == null) {
            this.plunder = new PlunderProp(this, FIELD_INDEX_PLUNDER);
        }
        return this.plunder;
    }

    /**
     * get guardTowerReport
     *
     * @return guardTowerReport value
     */
    public BattleRecordSoldierReportProp getGuardTowerReport() {
        if (this.guardTowerReport == null) {
            this.guardTowerReport = new BattleRecordSoldierReportProp(this, FIELD_INDEX_GUARDTOWERREPORT);
        }
        return this.guardTowerReport;
    }

    /**
     * get entityType
     *
     * @return entityType value
     */
    public int getEntityType() {
        return this.entityType;
    }

    /**
     * set entityType && set marked
     *
     * @param entityType new value
     * @return current object
     */
    public BattleRecordRoleProp setEntityType(int entityType) {
        if (this.entityType != entityType) {
            this.mark(FIELD_INDEX_ENTITYTYPE);
            this.entityType = entityType;
        }
        return this;
    }

    /**
     * inner set entityType
     *
     * @param entityType new value
     */
    private void innerSetEntityType(int entityType) {
        this.entityType = entityType;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRolePB.Builder getCopyCsBuilder() {
        final BattleRecordRolePB.Builder builder = BattleRecordRolePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BattleRecordRolePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }  else if (builder.hasClanName()) {
            // 清理ClanName
            builder.clearClanName();
            fieldCnt++;
        }
        if (this.location != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.location.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLocation(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLocation();
            }
        }  else if (builder.hasLocation()) {
            // 清理Location
            builder.clearLocation();
            fieldCnt++;
        }
        if (this.mainHero != null) {
            StructBattlePB.BattleRecordHeroSummaryPB.Builder tmpBuilder = StructBattlePB.BattleRecordHeroSummaryPB.newBuilder();
            final int tmpFieldCnt = this.mainHero.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMainHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMainHero();
            }
        }  else if (builder.hasMainHero()) {
            // 清理MainHero
            builder.clearMainHero();
            fieldCnt++;
        }
        if (this.deputyHero != null) {
            StructBattlePB.BattleRecordHeroSummaryPB.Builder tmpBuilder = StructBattlePB.BattleRecordHeroSummaryPB.newBuilder();
            final int tmpFieldCnt = this.deputyHero.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDeputyHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDeputyHero();
            }
        }  else if (builder.hasDeputyHero()) {
            // 清理DeputyHero
            builder.clearDeputyHero();
            fieldCnt++;
        }
        if (this.reward != null) {
            StructBattlePB.BattleRecordRewardPB.Builder tmpBuilder = StructBattlePB.BattleRecordRewardPB.newBuilder();
            final int tmpFieldCnt = this.reward.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setReward(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearReward();
            }
        }  else if (builder.hasReward()) {
            // 清理Reward
            builder.clearReward();
            fieldCnt++;
        }
        if (this.soldierReport != null) {
            StructBattlePB.BattleRecordSoldierReportPB.Builder tmpBuilder = StructBattlePB.BattleRecordSoldierReportPB.newBuilder();
            final int tmpFieldCnt = this.soldierReport.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldierReport(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldierReport();
            }
        }  else if (builder.hasSoldierReport()) {
            // 清理SoldierReport
            builder.clearSoldierReport();
            fieldCnt++;
        }
        if (this.getLostPower() != 0L) {
            builder.setLostPower(this.getLostPower());
            fieldCnt++;
        }  else if (builder.hasLostPower()) {
            // 清理LostPower
            builder.clearLostPower();
            fieldCnt++;
        }
        if (this.getRoleType() != SceneObjType.forNumber(0)) {
            builder.setRoleType(this.getRoleType());
            fieldCnt++;
        }  else if (builder.hasRoleType()) {
            // 清理RoleType
            builder.clearRoleType();
            fieldCnt++;
        }
        if (this.getRoleId() != 0L) {
            builder.setRoleId(this.getRoleId());
            fieldCnt++;
        }  else if (builder.hasRoleId()) {
            // 清理RoleId
            builder.clearRoleId();
            fieldCnt++;
        }
        if (this.getBuildingId() != 0L) {
            builder.setBuildingId(this.getBuildingId());
            fieldCnt++;
        }  else if (builder.hasBuildingId()) {
            // 清理BuildingId
            builder.clearBuildingId();
            fieldCnt++;
        }
        if (this.plane != null) {
            StructBattlePB.BattleRecordPlaneSummaryPB.Builder tmpBuilder = StructBattlePB.BattleRecordPlaneSummaryPB.newBuilder();
            final int tmpFieldCnt = this.plane.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlane();
            }
        }  else if (builder.hasPlane()) {
            // 清理Plane
            builder.clearPlane();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.plunder != null) {
            StructBattlePB.PlunderPB.Builder tmpBuilder = StructBattlePB.PlunderPB.newBuilder();
            final int tmpFieldCnt = this.plunder.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlunder(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlunder();
            }
        }  else if (builder.hasPlunder()) {
            // 清理Plunder
            builder.clearPlunder();
            fieldCnt++;
        }
        if (this.guardTowerReport != null) {
            StructBattlePB.BattleRecordSoldierReportPB.Builder tmpBuilder = StructBattlePB.BattleRecordSoldierReportPB.newBuilder();
            final int tmpFieldCnt = this.guardTowerReport.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGuardTowerReport(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGuardTowerReport();
            }
        }  else if (builder.hasGuardTowerReport()) {
            // 清理GuardTowerReport
            builder.clearGuardTowerReport();
            fieldCnt++;
        }
        if (this.getEntityType() != 0) {
            builder.setEntityType(this.getEntityType());
            fieldCnt++;
        }  else if (builder.hasEntityType()) {
            // 清理EntityType
            builder.clearEntityType();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BattleRecordRolePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOCATION) && this.location != null) {
            final boolean needClear = !builder.hasLocation();
            final int tmpFieldCnt = this.location.copyChangeToCs(builder.getLocationBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLocation();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAINHERO) && this.mainHero != null) {
            final boolean needClear = !builder.hasMainHero();
            final int tmpFieldCnt = this.mainHero.copyChangeToCs(builder.getMainHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMainHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHERO) && this.deputyHero != null) {
            final boolean needClear = !builder.hasDeputyHero();
            final int tmpFieldCnt = this.deputyHero.copyChangeToCs(builder.getDeputyHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDeputyHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARD) && this.reward != null) {
            final boolean needClear = !builder.hasReward();
            final int tmpFieldCnt = this.reward.copyChangeToCs(builder.getRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearReward();
            }
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERREPORT) && this.soldierReport != null) {
            final boolean needClear = !builder.hasSoldierReport();
            final int tmpFieldCnt = this.soldierReport.copyChangeToCs(builder.getSoldierReportBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldierReport();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOSTPOWER)) {
            builder.setLostPower(this.getLostPower());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ROLETYPE)) {
            builder.setRoleType(this.getRoleType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ROLEID)) {
            builder.setRoleId(this.getRoleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDINGID)) {
            builder.setBuildingId(this.getBuildingId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANE) && this.plane != null) {
            final boolean needClear = !builder.hasPlane();
            final int tmpFieldCnt = this.plane.copyChangeToCs(builder.getPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLUNDER) && this.plunder != null) {
            final boolean needClear = !builder.hasPlunder();
            final int tmpFieldCnt = this.plunder.copyChangeToCs(builder.getPlunderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlunder();
            }
        }
        if (this.hasMark(FIELD_INDEX_GUARDTOWERREPORT) && this.guardTowerReport != null) {
            final boolean needClear = !builder.hasGuardTowerReport();
            final int tmpFieldCnt = this.guardTowerReport.copyChangeToCs(builder.getGuardTowerReportBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGuardTowerReport();
            }
        }
        if (this.hasMark(FIELD_INDEX_ENTITYTYPE)) {
            builder.setEntityType(this.getEntityType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BattleRecordRolePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOCATION) && this.location != null) {
            final boolean needClear = !builder.hasLocation();
            final int tmpFieldCnt = this.location.copyChangeToAndClearDeleteKeysCs(builder.getLocationBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLocation();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAINHERO) && this.mainHero != null) {
            final boolean needClear = !builder.hasMainHero();
            final int tmpFieldCnt = this.mainHero.copyChangeToAndClearDeleteKeysCs(builder.getMainHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMainHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHERO) && this.deputyHero != null) {
            final boolean needClear = !builder.hasDeputyHero();
            final int tmpFieldCnt = this.deputyHero.copyChangeToAndClearDeleteKeysCs(builder.getDeputyHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDeputyHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARD) && this.reward != null) {
            final boolean needClear = !builder.hasReward();
            final int tmpFieldCnt = this.reward.copyChangeToAndClearDeleteKeysCs(builder.getRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearReward();
            }
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERREPORT) && this.soldierReport != null) {
            final boolean needClear = !builder.hasSoldierReport();
            final int tmpFieldCnt = this.soldierReport.copyChangeToAndClearDeleteKeysCs(builder.getSoldierReportBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldierReport();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOSTPOWER)) {
            builder.setLostPower(this.getLostPower());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ROLETYPE)) {
            builder.setRoleType(this.getRoleType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ROLEID)) {
            builder.setRoleId(this.getRoleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDINGID)) {
            builder.setBuildingId(this.getBuildingId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANE) && this.plane != null) {
            final boolean needClear = !builder.hasPlane();
            final int tmpFieldCnt = this.plane.copyChangeToAndClearDeleteKeysCs(builder.getPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToAndClearDeleteKeysCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLUNDER) && this.plunder != null) {
            final boolean needClear = !builder.hasPlunder();
            final int tmpFieldCnt = this.plunder.copyChangeToAndClearDeleteKeysCs(builder.getPlunderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlunder();
            }
        }
        if (this.hasMark(FIELD_INDEX_GUARDTOWERREPORT) && this.guardTowerReport != null) {
            final boolean needClear = !builder.hasGuardTowerReport();
            final int tmpFieldCnt = this.guardTowerReport.copyChangeToAndClearDeleteKeysCs(builder.getGuardTowerReportBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGuardTowerReport();
            }
        }
        if (this.hasMark(FIELD_INDEX_ENTITYTYPE)) {
            builder.setEntityType(this.getEntityType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BattleRecordRolePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasClanName()) {
            this.innerSetClanName(proto.getClanName());
        } else {
            this.innerSetClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasLocation()) {
            this.getLocation().mergeFromCs(proto.getLocation());
        } else {
            if (this.location != null) {
                this.location.mergeFromCs(proto.getLocation());
            }
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeFromCs(proto.getMainHero());
        } else {
            if (this.mainHero != null) {
                this.mainHero.mergeFromCs(proto.getMainHero());
            }
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeFromCs(proto.getDeputyHero());
        } else {
            if (this.deputyHero != null) {
                this.deputyHero.mergeFromCs(proto.getDeputyHero());
            }
        }
        if (proto.hasReward()) {
            this.getReward().mergeFromCs(proto.getReward());
        } else {
            if (this.reward != null) {
                this.reward.mergeFromCs(proto.getReward());
            }
        }
        if (proto.hasSoldierReport()) {
            this.getSoldierReport().mergeFromCs(proto.getSoldierReport());
        } else {
            if (this.soldierReport != null) {
                this.soldierReport.mergeFromCs(proto.getSoldierReport());
            }
        }
        if (proto.hasLostPower()) {
            this.innerSetLostPower(proto.getLostPower());
        } else {
            this.innerSetLostPower(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRoleType()) {
            this.innerSetRoleType(proto.getRoleType());
        } else {
            this.innerSetRoleType(SceneObjType.forNumber(0));
        }
        if (proto.hasRoleId()) {
            this.innerSetRoleId(proto.getRoleId());
        } else {
            this.innerSetRoleId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBuildingId()) {
            this.innerSetBuildingId(proto.getBuildingId());
        } else {
            this.innerSetBuildingId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlane()) {
            this.getPlane().mergeFromCs(proto.getPlane());
        } else {
            if (this.plane != null) {
                this.plane.mergeFromCs(proto.getPlane());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromCs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromCs(proto.getCardHead());
            }
        }
        if (proto.hasPlunder()) {
            this.getPlunder().mergeFromCs(proto.getPlunder());
        } else {
            if (this.plunder != null) {
                this.plunder.mergeFromCs(proto.getPlunder());
            }
        }
        if (proto.hasGuardTowerReport()) {
            this.getGuardTowerReport().mergeFromCs(proto.getGuardTowerReport());
        } else {
            if (this.guardTowerReport != null) {
                this.guardTowerReport.mergeFromCs(proto.getGuardTowerReport());
            }
        }
        if (proto.hasEntityType()) {
            this.innerSetEntityType(proto.getEntityType());
        } else {
            this.innerSetEntityType(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return BattleRecordRoleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BattleRecordRolePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasClanName()) {
            this.setClanName(proto.getClanName());
            fieldCnt++;
        }
        if (proto.hasLocation()) {
            this.getLocation().mergeChangeFromCs(proto.getLocation());
            fieldCnt++;
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeChangeFromCs(proto.getMainHero());
            fieldCnt++;
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeChangeFromCs(proto.getDeputyHero());
            fieldCnt++;
        }
        if (proto.hasReward()) {
            this.getReward().mergeChangeFromCs(proto.getReward());
            fieldCnt++;
        }
        if (proto.hasSoldierReport()) {
            this.getSoldierReport().mergeChangeFromCs(proto.getSoldierReport());
            fieldCnt++;
        }
        if (proto.hasLostPower()) {
            this.setLostPower(proto.getLostPower());
            fieldCnt++;
        }
        if (proto.hasRoleType()) {
            this.setRoleType(proto.getRoleType());
            fieldCnt++;
        }
        if (proto.hasRoleId()) {
            this.setRoleId(proto.getRoleId());
            fieldCnt++;
        }
        if (proto.hasBuildingId()) {
            this.setBuildingId(proto.getBuildingId());
            fieldCnt++;
        }
        if (proto.hasPlane()) {
            this.getPlane().mergeChangeFromCs(proto.getPlane());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromCs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasPlunder()) {
            this.getPlunder().mergeChangeFromCs(proto.getPlunder());
            fieldCnt++;
        }
        if (proto.hasGuardTowerReport()) {
            this.getGuardTowerReport().mergeChangeFromCs(proto.getGuardTowerReport());
            fieldCnt++;
        }
        if (proto.hasEntityType()) {
            this.setEntityType(proto.getEntityType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRole.Builder getCopyDbBuilder() {
        final BattleRecordRole.Builder builder = BattleRecordRole.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(BattleRecordRole.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }  else if (builder.hasClanName()) {
            // 清理ClanName
            builder.clearClanName();
            fieldCnt++;
        }
        if (this.location != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.location.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLocation(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLocation();
            }
        }  else if (builder.hasLocation()) {
            // 清理Location
            builder.clearLocation();
            fieldCnt++;
        }
        if (this.mainHero != null) {
            StructBattle.BattleRecordHeroSummary.Builder tmpBuilder = StructBattle.BattleRecordHeroSummary.newBuilder();
            final int tmpFieldCnt = this.mainHero.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMainHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMainHero();
            }
        }  else if (builder.hasMainHero()) {
            // 清理MainHero
            builder.clearMainHero();
            fieldCnt++;
        }
        if (this.deputyHero != null) {
            StructBattle.BattleRecordHeroSummary.Builder tmpBuilder = StructBattle.BattleRecordHeroSummary.newBuilder();
            final int tmpFieldCnt = this.deputyHero.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDeputyHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDeputyHero();
            }
        }  else if (builder.hasDeputyHero()) {
            // 清理DeputyHero
            builder.clearDeputyHero();
            fieldCnt++;
        }
        if (this.reward != null) {
            StructBattle.BattleRecordReward.Builder tmpBuilder = StructBattle.BattleRecordReward.newBuilder();
            final int tmpFieldCnt = this.reward.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setReward(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearReward();
            }
        }  else if (builder.hasReward()) {
            // 清理Reward
            builder.clearReward();
            fieldCnt++;
        }
        if (this.soldierReport != null) {
            StructBattle.BattleRecordSoldierReport.Builder tmpBuilder = StructBattle.BattleRecordSoldierReport.newBuilder();
            final int tmpFieldCnt = this.soldierReport.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldierReport(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldierReport();
            }
        }  else if (builder.hasSoldierReport()) {
            // 清理SoldierReport
            builder.clearSoldierReport();
            fieldCnt++;
        }
        if (this.getLostPower() != 0L) {
            builder.setLostPower(this.getLostPower());
            fieldCnt++;
        }  else if (builder.hasLostPower()) {
            // 清理LostPower
            builder.clearLostPower();
            fieldCnt++;
        }
        if (this.getRoleType() != SceneObjType.forNumber(0)) {
            builder.setRoleType(this.getRoleType());
            fieldCnt++;
        }  else if (builder.hasRoleType()) {
            // 清理RoleType
            builder.clearRoleType();
            fieldCnt++;
        }
        if (this.getRoleId() != 0L) {
            builder.setRoleId(this.getRoleId());
            fieldCnt++;
        }  else if (builder.hasRoleId()) {
            // 清理RoleId
            builder.clearRoleId();
            fieldCnt++;
        }
        if (this.getBuildingId() != 0L) {
            builder.setBuildingId(this.getBuildingId());
            fieldCnt++;
        }  else if (builder.hasBuildingId()) {
            // 清理BuildingId
            builder.clearBuildingId();
            fieldCnt++;
        }
        if (this.plane != null) {
            StructBattle.BattleRecordPlaneSummary.Builder tmpBuilder = StructBattle.BattleRecordPlaneSummary.newBuilder();
            final int tmpFieldCnt = this.plane.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlane();
            }
        }  else if (builder.hasPlane()) {
            // 清理Plane
            builder.clearPlane();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.plunder != null) {
            StructBattle.Plunder.Builder tmpBuilder = StructBattle.Plunder.newBuilder();
            final int tmpFieldCnt = this.plunder.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlunder(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlunder();
            }
        }  else if (builder.hasPlunder()) {
            // 清理Plunder
            builder.clearPlunder();
            fieldCnt++;
        }
        if (this.guardTowerReport != null) {
            StructBattle.BattleRecordSoldierReport.Builder tmpBuilder = StructBattle.BattleRecordSoldierReport.newBuilder();
            final int tmpFieldCnt = this.guardTowerReport.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGuardTowerReport(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGuardTowerReport();
            }
        }  else if (builder.hasGuardTowerReport()) {
            // 清理GuardTowerReport
            builder.clearGuardTowerReport();
            fieldCnt++;
        }
        if (this.getEntityType() != 0) {
            builder.setEntityType(this.getEntityType());
            fieldCnt++;
        }  else if (builder.hasEntityType()) {
            // 清理EntityType
            builder.clearEntityType();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(BattleRecordRole.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOCATION) && this.location != null) {
            final boolean needClear = !builder.hasLocation();
            final int tmpFieldCnt = this.location.copyChangeToDb(builder.getLocationBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLocation();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAINHERO) && this.mainHero != null) {
            final boolean needClear = !builder.hasMainHero();
            final int tmpFieldCnt = this.mainHero.copyChangeToDb(builder.getMainHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMainHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHERO) && this.deputyHero != null) {
            final boolean needClear = !builder.hasDeputyHero();
            final int tmpFieldCnt = this.deputyHero.copyChangeToDb(builder.getDeputyHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDeputyHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARD) && this.reward != null) {
            final boolean needClear = !builder.hasReward();
            final int tmpFieldCnt = this.reward.copyChangeToDb(builder.getRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearReward();
            }
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERREPORT) && this.soldierReport != null) {
            final boolean needClear = !builder.hasSoldierReport();
            final int tmpFieldCnt = this.soldierReport.copyChangeToDb(builder.getSoldierReportBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldierReport();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOSTPOWER)) {
            builder.setLostPower(this.getLostPower());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ROLETYPE)) {
            builder.setRoleType(this.getRoleType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ROLEID)) {
            builder.setRoleId(this.getRoleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDINGID)) {
            builder.setBuildingId(this.getBuildingId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANE) && this.plane != null) {
            final boolean needClear = !builder.hasPlane();
            final int tmpFieldCnt = this.plane.copyChangeToDb(builder.getPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToDb(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLUNDER) && this.plunder != null) {
            final boolean needClear = !builder.hasPlunder();
            final int tmpFieldCnt = this.plunder.copyChangeToDb(builder.getPlunderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlunder();
            }
        }
        if (this.hasMark(FIELD_INDEX_GUARDTOWERREPORT) && this.guardTowerReport != null) {
            final boolean needClear = !builder.hasGuardTowerReport();
            final int tmpFieldCnt = this.guardTowerReport.copyChangeToDb(builder.getGuardTowerReportBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGuardTowerReport();
            }
        }
        if (this.hasMark(FIELD_INDEX_ENTITYTYPE)) {
            builder.setEntityType(this.getEntityType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(BattleRecordRole proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasClanName()) {
            this.innerSetClanName(proto.getClanName());
        } else {
            this.innerSetClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasLocation()) {
            this.getLocation().mergeFromDb(proto.getLocation());
        } else {
            if (this.location != null) {
                this.location.mergeFromDb(proto.getLocation());
            }
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeFromDb(proto.getMainHero());
        } else {
            if (this.mainHero != null) {
                this.mainHero.mergeFromDb(proto.getMainHero());
            }
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeFromDb(proto.getDeputyHero());
        } else {
            if (this.deputyHero != null) {
                this.deputyHero.mergeFromDb(proto.getDeputyHero());
            }
        }
        if (proto.hasReward()) {
            this.getReward().mergeFromDb(proto.getReward());
        } else {
            if (this.reward != null) {
                this.reward.mergeFromDb(proto.getReward());
            }
        }
        if (proto.hasSoldierReport()) {
            this.getSoldierReport().mergeFromDb(proto.getSoldierReport());
        } else {
            if (this.soldierReport != null) {
                this.soldierReport.mergeFromDb(proto.getSoldierReport());
            }
        }
        if (proto.hasLostPower()) {
            this.innerSetLostPower(proto.getLostPower());
        } else {
            this.innerSetLostPower(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRoleType()) {
            this.innerSetRoleType(proto.getRoleType());
        } else {
            this.innerSetRoleType(SceneObjType.forNumber(0));
        }
        if (proto.hasRoleId()) {
            this.innerSetRoleId(proto.getRoleId());
        } else {
            this.innerSetRoleId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBuildingId()) {
            this.innerSetBuildingId(proto.getBuildingId());
        } else {
            this.innerSetBuildingId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlane()) {
            this.getPlane().mergeFromDb(proto.getPlane());
        } else {
            if (this.plane != null) {
                this.plane.mergeFromDb(proto.getPlane());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromDb(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromDb(proto.getCardHead());
            }
        }
        if (proto.hasPlunder()) {
            this.getPlunder().mergeFromDb(proto.getPlunder());
        } else {
            if (this.plunder != null) {
                this.plunder.mergeFromDb(proto.getPlunder());
            }
        }
        if (proto.hasGuardTowerReport()) {
            this.getGuardTowerReport().mergeFromDb(proto.getGuardTowerReport());
        } else {
            if (this.guardTowerReport != null) {
                this.guardTowerReport.mergeFromDb(proto.getGuardTowerReport());
            }
        }
        if (proto.hasEntityType()) {
            this.innerSetEntityType(proto.getEntityType());
        } else {
            this.innerSetEntityType(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return BattleRecordRoleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(BattleRecordRole proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasClanName()) {
            this.setClanName(proto.getClanName());
            fieldCnt++;
        }
        if (proto.hasLocation()) {
            this.getLocation().mergeChangeFromDb(proto.getLocation());
            fieldCnt++;
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeChangeFromDb(proto.getMainHero());
            fieldCnt++;
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeChangeFromDb(proto.getDeputyHero());
            fieldCnt++;
        }
        if (proto.hasReward()) {
            this.getReward().mergeChangeFromDb(proto.getReward());
            fieldCnt++;
        }
        if (proto.hasSoldierReport()) {
            this.getSoldierReport().mergeChangeFromDb(proto.getSoldierReport());
            fieldCnt++;
        }
        if (proto.hasLostPower()) {
            this.setLostPower(proto.getLostPower());
            fieldCnt++;
        }
        if (proto.hasRoleType()) {
            this.setRoleType(proto.getRoleType());
            fieldCnt++;
        }
        if (proto.hasRoleId()) {
            this.setRoleId(proto.getRoleId());
            fieldCnt++;
        }
        if (proto.hasBuildingId()) {
            this.setBuildingId(proto.getBuildingId());
            fieldCnt++;
        }
        if (proto.hasPlane()) {
            this.getPlane().mergeChangeFromDb(proto.getPlane());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromDb(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasPlunder()) {
            this.getPlunder().mergeChangeFromDb(proto.getPlunder());
            fieldCnt++;
        }
        if (proto.hasGuardTowerReport()) {
            this.getGuardTowerReport().mergeChangeFromDb(proto.getGuardTowerReport());
            fieldCnt++;
        }
        if (proto.hasEntityType()) {
            this.setEntityType(proto.getEntityType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRole.Builder getCopySsBuilder() {
        final BattleRecordRole.Builder builder = BattleRecordRole.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(BattleRecordRole.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }  else if (builder.hasClanName()) {
            // 清理ClanName
            builder.clearClanName();
            fieldCnt++;
        }
        if (this.location != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.location.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLocation(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLocation();
            }
        }  else if (builder.hasLocation()) {
            // 清理Location
            builder.clearLocation();
            fieldCnt++;
        }
        if (this.mainHero != null) {
            StructBattle.BattleRecordHeroSummary.Builder tmpBuilder = StructBattle.BattleRecordHeroSummary.newBuilder();
            final int tmpFieldCnt = this.mainHero.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMainHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMainHero();
            }
        }  else if (builder.hasMainHero()) {
            // 清理MainHero
            builder.clearMainHero();
            fieldCnt++;
        }
        if (this.deputyHero != null) {
            StructBattle.BattleRecordHeroSummary.Builder tmpBuilder = StructBattle.BattleRecordHeroSummary.newBuilder();
            final int tmpFieldCnt = this.deputyHero.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDeputyHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDeputyHero();
            }
        }  else if (builder.hasDeputyHero()) {
            // 清理DeputyHero
            builder.clearDeputyHero();
            fieldCnt++;
        }
        if (this.reward != null) {
            StructBattle.BattleRecordReward.Builder tmpBuilder = StructBattle.BattleRecordReward.newBuilder();
            final int tmpFieldCnt = this.reward.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setReward(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearReward();
            }
        }  else if (builder.hasReward()) {
            // 清理Reward
            builder.clearReward();
            fieldCnt++;
        }
        if (this.soldierReport != null) {
            StructBattle.BattleRecordSoldierReport.Builder tmpBuilder = StructBattle.BattleRecordSoldierReport.newBuilder();
            final int tmpFieldCnt = this.soldierReport.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldierReport(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldierReport();
            }
        }  else if (builder.hasSoldierReport()) {
            // 清理SoldierReport
            builder.clearSoldierReport();
            fieldCnt++;
        }
        if (this.getLostPower() != 0L) {
            builder.setLostPower(this.getLostPower());
            fieldCnt++;
        }  else if (builder.hasLostPower()) {
            // 清理LostPower
            builder.clearLostPower();
            fieldCnt++;
        }
        if (this.getRoleType() != SceneObjType.forNumber(0)) {
            builder.setRoleType(this.getRoleType());
            fieldCnt++;
        }  else if (builder.hasRoleType()) {
            // 清理RoleType
            builder.clearRoleType();
            fieldCnt++;
        }
        if (this.getRoleId() != 0L) {
            builder.setRoleId(this.getRoleId());
            fieldCnt++;
        }  else if (builder.hasRoleId()) {
            // 清理RoleId
            builder.clearRoleId();
            fieldCnt++;
        }
        if (this.getBuildingId() != 0L) {
            builder.setBuildingId(this.getBuildingId());
            fieldCnt++;
        }  else if (builder.hasBuildingId()) {
            // 清理BuildingId
            builder.clearBuildingId();
            fieldCnt++;
        }
        if (this.plane != null) {
            StructBattle.BattleRecordPlaneSummary.Builder tmpBuilder = StructBattle.BattleRecordPlaneSummary.newBuilder();
            final int tmpFieldCnt = this.plane.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlane();
            }
        }  else if (builder.hasPlane()) {
            // 清理Plane
            builder.clearPlane();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.plunder != null) {
            StructBattle.Plunder.Builder tmpBuilder = StructBattle.Plunder.newBuilder();
            final int tmpFieldCnt = this.plunder.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlunder(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlunder();
            }
        }  else if (builder.hasPlunder()) {
            // 清理Plunder
            builder.clearPlunder();
            fieldCnt++;
        }
        if (this.guardTowerReport != null) {
            StructBattle.BattleRecordSoldierReport.Builder tmpBuilder = StructBattle.BattleRecordSoldierReport.newBuilder();
            final int tmpFieldCnt = this.guardTowerReport.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGuardTowerReport(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGuardTowerReport();
            }
        }  else if (builder.hasGuardTowerReport()) {
            // 清理GuardTowerReport
            builder.clearGuardTowerReport();
            fieldCnt++;
        }
        if (this.getEntityType() != 0) {
            builder.setEntityType(this.getEntityType());
            fieldCnt++;
        }  else if (builder.hasEntityType()) {
            // 清理EntityType
            builder.clearEntityType();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(BattleRecordRole.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOCATION) && this.location != null) {
            final boolean needClear = !builder.hasLocation();
            final int tmpFieldCnt = this.location.copyChangeToSs(builder.getLocationBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLocation();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAINHERO) && this.mainHero != null) {
            final boolean needClear = !builder.hasMainHero();
            final int tmpFieldCnt = this.mainHero.copyChangeToSs(builder.getMainHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMainHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHERO) && this.deputyHero != null) {
            final boolean needClear = !builder.hasDeputyHero();
            final int tmpFieldCnt = this.deputyHero.copyChangeToSs(builder.getDeputyHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDeputyHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARD) && this.reward != null) {
            final boolean needClear = !builder.hasReward();
            final int tmpFieldCnt = this.reward.copyChangeToSs(builder.getRewardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearReward();
            }
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERREPORT) && this.soldierReport != null) {
            final boolean needClear = !builder.hasSoldierReport();
            final int tmpFieldCnt = this.soldierReport.copyChangeToSs(builder.getSoldierReportBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldierReport();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOSTPOWER)) {
            builder.setLostPower(this.getLostPower());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ROLETYPE)) {
            builder.setRoleType(this.getRoleType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ROLEID)) {
            builder.setRoleId(this.getRoleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDINGID)) {
            builder.setBuildingId(this.getBuildingId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANE) && this.plane != null) {
            final boolean needClear = !builder.hasPlane();
            final int tmpFieldCnt = this.plane.copyChangeToSs(builder.getPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLUNDER) && this.plunder != null) {
            final boolean needClear = !builder.hasPlunder();
            final int tmpFieldCnt = this.plunder.copyChangeToSs(builder.getPlunderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlunder();
            }
        }
        if (this.hasMark(FIELD_INDEX_GUARDTOWERREPORT) && this.guardTowerReport != null) {
            final boolean needClear = !builder.hasGuardTowerReport();
            final int tmpFieldCnt = this.guardTowerReport.copyChangeToSs(builder.getGuardTowerReportBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGuardTowerReport();
            }
        }
        if (this.hasMark(FIELD_INDEX_ENTITYTYPE)) {
            builder.setEntityType(this.getEntityType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(BattleRecordRole proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasClanName()) {
            this.innerSetClanName(proto.getClanName());
        } else {
            this.innerSetClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasLocation()) {
            this.getLocation().mergeFromSs(proto.getLocation());
        } else {
            if (this.location != null) {
                this.location.mergeFromSs(proto.getLocation());
            }
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeFromSs(proto.getMainHero());
        } else {
            if (this.mainHero != null) {
                this.mainHero.mergeFromSs(proto.getMainHero());
            }
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeFromSs(proto.getDeputyHero());
        } else {
            if (this.deputyHero != null) {
                this.deputyHero.mergeFromSs(proto.getDeputyHero());
            }
        }
        if (proto.hasReward()) {
            this.getReward().mergeFromSs(proto.getReward());
        } else {
            if (this.reward != null) {
                this.reward.mergeFromSs(proto.getReward());
            }
        }
        if (proto.hasSoldierReport()) {
            this.getSoldierReport().mergeFromSs(proto.getSoldierReport());
        } else {
            if (this.soldierReport != null) {
                this.soldierReport.mergeFromSs(proto.getSoldierReport());
            }
        }
        if (proto.hasLostPower()) {
            this.innerSetLostPower(proto.getLostPower());
        } else {
            this.innerSetLostPower(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRoleType()) {
            this.innerSetRoleType(proto.getRoleType());
        } else {
            this.innerSetRoleType(SceneObjType.forNumber(0));
        }
        if (proto.hasRoleId()) {
            this.innerSetRoleId(proto.getRoleId());
        } else {
            this.innerSetRoleId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBuildingId()) {
            this.innerSetBuildingId(proto.getBuildingId());
        } else {
            this.innerSetBuildingId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlane()) {
            this.getPlane().mergeFromSs(proto.getPlane());
        } else {
            if (this.plane != null) {
                this.plane.mergeFromSs(proto.getPlane());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        if (proto.hasPlunder()) {
            this.getPlunder().mergeFromSs(proto.getPlunder());
        } else {
            if (this.plunder != null) {
                this.plunder.mergeFromSs(proto.getPlunder());
            }
        }
        if (proto.hasGuardTowerReport()) {
            this.getGuardTowerReport().mergeFromSs(proto.getGuardTowerReport());
        } else {
            if (this.guardTowerReport != null) {
                this.guardTowerReport.mergeFromSs(proto.getGuardTowerReport());
            }
        }
        if (proto.hasEntityType()) {
            this.innerSetEntityType(proto.getEntityType());
        } else {
            this.innerSetEntityType(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return BattleRecordRoleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(BattleRecordRole proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasClanName()) {
            this.setClanName(proto.getClanName());
            fieldCnt++;
        }
        if (proto.hasLocation()) {
            this.getLocation().mergeChangeFromSs(proto.getLocation());
            fieldCnt++;
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeChangeFromSs(proto.getMainHero());
            fieldCnt++;
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeChangeFromSs(proto.getDeputyHero());
            fieldCnt++;
        }
        if (proto.hasReward()) {
            this.getReward().mergeChangeFromSs(proto.getReward());
            fieldCnt++;
        }
        if (proto.hasSoldierReport()) {
            this.getSoldierReport().mergeChangeFromSs(proto.getSoldierReport());
            fieldCnt++;
        }
        if (proto.hasLostPower()) {
            this.setLostPower(proto.getLostPower());
            fieldCnt++;
        }
        if (proto.hasRoleType()) {
            this.setRoleType(proto.getRoleType());
            fieldCnt++;
        }
        if (proto.hasRoleId()) {
            this.setRoleId(proto.getRoleId());
            fieldCnt++;
        }
        if (proto.hasBuildingId()) {
            this.setBuildingId(proto.getBuildingId());
            fieldCnt++;
        }
        if (proto.hasPlane()) {
            this.getPlane().mergeChangeFromSs(proto.getPlane());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasPlunder()) {
            this.getPlunder().mergeChangeFromSs(proto.getPlunder());
            fieldCnt++;
        }
        if (proto.hasGuardTowerReport()) {
            this.getGuardTowerReport().mergeChangeFromSs(proto.getGuardTowerReport());
            fieldCnt++;
        }
        if (proto.hasEntityType()) {
            this.setEntityType(proto.getEntityType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        BattleRecordRole.Builder builder = BattleRecordRole.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_LOCATION) && this.location != null) {
            this.location.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_MAINHERO) && this.mainHero != null) {
            this.mainHero.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHERO) && this.deputyHero != null) {
            this.deputyHero.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_REWARD) && this.reward != null) {
            this.reward.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERREPORT) && this.soldierReport != null) {
            this.soldierReport.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLANE) && this.plane != null) {
            this.plane.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLUNDER) && this.plunder != null) {
            this.plunder.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_GUARDTOWERREPORT) && this.guardTowerReport != null) {
            this.guardTowerReport.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.location != null) {
            this.location.markAll();
        }
        if (this.mainHero != null) {
            this.mainHero.markAll();
        }
        if (this.deputyHero != null) {
            this.deputyHero.markAll();
        }
        if (this.reward != null) {
            this.reward.markAll();
        }
        if (this.soldierReport != null) {
            this.soldierReport.markAll();
        }
        if (this.plane != null) {
            this.plane.markAll();
        }
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        if (this.plunder != null) {
            this.plunder.markAll();
        }
        if (this.guardTowerReport != null) {
            this.guardTowerReport.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BattleRecordRoleProp)) {
            return false;
        }
        final BattleRecordRoleProp otherNode = (BattleRecordRoleProp) node;
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanName, otherNode.clanName)) {
            return false;
        }
        if (!this.getLocation().compareDataTo(otherNode.getLocation())) {
            return false;
        }
        if (!this.getMainHero().compareDataTo(otherNode.getMainHero())) {
            return false;
        }
        if (!this.getDeputyHero().compareDataTo(otherNode.getDeputyHero())) {
            return false;
        }
        if (!this.getReward().compareDataTo(otherNode.getReward())) {
            return false;
        }
        if (!this.getSoldierReport().compareDataTo(otherNode.getSoldierReport())) {
            return false;
        }
        if (this.lostPower != otherNode.lostPower) {
            return false;
        }
        if (this.roleType != otherNode.roleType) {
            return false;
        }
        if (this.roleId != otherNode.roleId) {
            return false;
        }
        if (this.buildingId != otherNode.buildingId) {
            return false;
        }
        if (!this.getPlane().compareDataTo(otherNode.getPlane())) {
            return false;
        }
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        if (!this.getPlunder().compareDataTo(otherNode.getPlunder())) {
            return false;
        }
        if (!this.getGuardTowerReport().compareDataTo(otherNode.getGuardTowerReport())) {
            return false;
        }
        if (this.entityType != otherNode.entityType) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 49;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityFiveDaysUnit;
import com.yorha.proto.StructPB.ActivityFiveDaysUnitPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityFiveDaysUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_WHAT = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private String what = Constant.DEFAULT_STR_VALUE;

    public ActivityFiveDaysUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityFiveDaysUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get what
     *
     * @return what value
     */
    public String getWhat() {
        return this.what;
    }

    /**
     * set what && set marked
     *
     * @param what new value
     * @return current object
     */
    public ActivityFiveDaysUnitProp setWhat(String what) {
        if (what == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.what, what)) {
            this.mark(FIELD_INDEX_WHAT);
            this.what = what;
        }
        return this;
    }

    /**
     * inner set what
     *
     * @param what new value
     */
    private void innerSetWhat(String what) {
        this.what = what;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityFiveDaysUnitPB.Builder getCopyCsBuilder() {
        final ActivityFiveDaysUnitPB.Builder builder = ActivityFiveDaysUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityFiveDaysUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getWhat().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setWhat(this.getWhat());
            fieldCnt++;
        }  else if (builder.hasWhat()) {
            // 清理What
            builder.clearWhat();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityFiveDaysUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_WHAT)) {
            builder.setWhat(this.getWhat());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityFiveDaysUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_WHAT)) {
            builder.setWhat(this.getWhat());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityFiveDaysUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasWhat()) {
            this.innerSetWhat(proto.getWhat());
        } else {
            this.innerSetWhat(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return ActivityFiveDaysUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityFiveDaysUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasWhat()) {
            this.setWhat(proto.getWhat());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityFiveDaysUnit.Builder getCopyDbBuilder() {
        final ActivityFiveDaysUnit.Builder builder = ActivityFiveDaysUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityFiveDaysUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getWhat().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setWhat(this.getWhat());
            fieldCnt++;
        }  else if (builder.hasWhat()) {
            // 清理What
            builder.clearWhat();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityFiveDaysUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_WHAT)) {
            builder.setWhat(this.getWhat());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityFiveDaysUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasWhat()) {
            this.innerSetWhat(proto.getWhat());
        } else {
            this.innerSetWhat(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return ActivityFiveDaysUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityFiveDaysUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasWhat()) {
            this.setWhat(proto.getWhat());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityFiveDaysUnit.Builder getCopySsBuilder() {
        final ActivityFiveDaysUnit.Builder builder = ActivityFiveDaysUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityFiveDaysUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getWhat().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setWhat(this.getWhat());
            fieldCnt++;
        }  else if (builder.hasWhat()) {
            // 清理What
            builder.clearWhat();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityFiveDaysUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_WHAT)) {
            builder.setWhat(this.getWhat());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityFiveDaysUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasWhat()) {
            this.innerSetWhat(proto.getWhat());
        } else {
            this.innerSetWhat(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return ActivityFiveDaysUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityFiveDaysUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasWhat()) {
            this.setWhat(proto.getWhat());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityFiveDaysUnit.Builder builder = ActivityFiveDaysUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityFiveDaysUnitProp)) {
            return false;
        }
        final ActivityFiveDaysUnitProp otherNode = (ActivityFiveDaysUnitProp) node;
        if (!com.yorha.gemini.utils.StringUtils.equals(this.what, otherNode.what)) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
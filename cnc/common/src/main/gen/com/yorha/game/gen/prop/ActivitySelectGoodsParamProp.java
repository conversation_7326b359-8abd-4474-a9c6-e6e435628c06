package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivitySelectGoodsParam;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ActivitySelectGoodsParamPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivitySelectGoodsParamProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ACTID = 0;
    public static final int FIELD_INDEX_UNITID = 1;
    public static final int FIELD_INDEX_SELECTGOODSINFOS = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int actId = Constant.DEFAULT_INT_VALUE;
    private int unitId = Constant.DEFAULT_INT_VALUE;
    private Int32ActivitySelectGoodsInfoMapProp selectGoodsInfos = null;

    public ActivitySelectGoodsParamProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivitySelectGoodsParamProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get actId
     *
     * @return actId value
     */
    public int getActId() {
        return this.actId;
    }

    /**
     * set actId && set marked
     *
     * @param actId new value
     * @return current object
     */
    public ActivitySelectGoodsParamProp setActId(int actId) {
        if (this.actId != actId) {
            this.mark(FIELD_INDEX_ACTID);
            this.actId = actId;
        }
        return this;
    }

    /**
     * inner set actId
     *
     * @param actId new value
     */
    private void innerSetActId(int actId) {
        this.actId = actId;
    }

    /**
     * get unitId
     *
     * @return unitId value
     */
    public int getUnitId() {
        return this.unitId;
    }

    /**
     * set unitId && set marked
     *
     * @param unitId new value
     * @return current object
     */
    public ActivitySelectGoodsParamProp setUnitId(int unitId) {
        if (this.unitId != unitId) {
            this.mark(FIELD_INDEX_UNITID);
            this.unitId = unitId;
        }
        return this;
    }

    /**
     * inner set unitId
     *
     * @param unitId new value
     */
    private void innerSetUnitId(int unitId) {
        this.unitId = unitId;
    }

    /**
     * get selectGoodsInfos
     *
     * @return selectGoodsInfos value
     */
    public Int32ActivitySelectGoodsInfoMapProp getSelectGoodsInfos() {
        if (this.selectGoodsInfos == null) {
            this.selectGoodsInfos = new Int32ActivitySelectGoodsInfoMapProp(this, FIELD_INDEX_SELECTGOODSINFOS);
        }
        return this.selectGoodsInfos;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putSelectGoodsInfosV(ActivitySelectGoodsInfoProp v) {
        this.getSelectGoodsInfos().put(v.getRewardId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ActivitySelectGoodsInfoProp addEmptySelectGoodsInfos(Integer k) {
        return this.getSelectGoodsInfos().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getSelectGoodsInfosSize() {
        if (this.selectGoodsInfos == null) {
            return 0;
        }
        return this.selectGoodsInfos.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isSelectGoodsInfosEmpty() {
        if (this.selectGoodsInfos == null) {
            return true;
        }
        return this.selectGoodsInfos.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ActivitySelectGoodsInfoProp getSelectGoodsInfosV(Integer k) {
        if (this.selectGoodsInfos == null || !this.selectGoodsInfos.containsKey(k)) {
            return null;
        }
        return this.selectGoodsInfos.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearSelectGoodsInfos() {
        if (this.selectGoodsInfos != null) {
            this.selectGoodsInfos.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeSelectGoodsInfosV(Integer k) {
        if (this.selectGoodsInfos != null) {
            this.selectGoodsInfos.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivitySelectGoodsParamPB.Builder getCopyCsBuilder() {
        final ActivitySelectGoodsParamPB.Builder builder = ActivitySelectGoodsParamPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivitySelectGoodsParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActId() != 0) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }  else if (builder.hasActId()) {
            // 清理ActId
            builder.clearActId();
            fieldCnt++;
        }
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        if (this.selectGoodsInfos != null) {
            StructPB.Int32ActivitySelectGoodsInfoMapPB.Builder tmpBuilder = StructPB.Int32ActivitySelectGoodsInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.selectGoodsInfos.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSelectGoodsInfos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSelectGoodsInfos();
            }
        }  else if (builder.hasSelectGoodsInfos()) {
            // 清理SelectGoodsInfos
            builder.clearSelectGoodsInfos();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivitySelectGoodsParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SELECTGOODSINFOS) && this.selectGoodsInfos != null) {
            final boolean needClear = !builder.hasSelectGoodsInfos();
            final int tmpFieldCnt = this.selectGoodsInfos.copyChangeToCs(builder.getSelectGoodsInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSelectGoodsInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivitySelectGoodsParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SELECTGOODSINFOS) && this.selectGoodsInfos != null) {
            final boolean needClear = !builder.hasSelectGoodsInfos();
            final int tmpFieldCnt = this.selectGoodsInfos.copyChangeToAndClearDeleteKeysCs(builder.getSelectGoodsInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSelectGoodsInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivitySelectGoodsParamPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActId()) {
            this.innerSetActId(proto.getActId());
        } else {
            this.innerSetActId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSelectGoodsInfos()) {
            this.getSelectGoodsInfos().mergeFromCs(proto.getSelectGoodsInfos());
        } else {
            if (this.selectGoodsInfos != null) {
                this.selectGoodsInfos.mergeFromCs(proto.getSelectGoodsInfos());
            }
        }
        this.markAll();
        return ActivitySelectGoodsParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivitySelectGoodsParamPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActId()) {
            this.setActId(proto.getActId());
            fieldCnt++;
        }
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        if (proto.hasSelectGoodsInfos()) {
            this.getSelectGoodsInfos().mergeChangeFromCs(proto.getSelectGoodsInfos());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivitySelectGoodsParam.Builder getCopyDbBuilder() {
        final ActivitySelectGoodsParam.Builder builder = ActivitySelectGoodsParam.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivitySelectGoodsParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActId() != 0) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }  else if (builder.hasActId()) {
            // 清理ActId
            builder.clearActId();
            fieldCnt++;
        }
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        if (this.selectGoodsInfos != null) {
            Struct.Int32ActivitySelectGoodsInfoMap.Builder tmpBuilder = Struct.Int32ActivitySelectGoodsInfoMap.newBuilder();
            final int tmpFieldCnt = this.selectGoodsInfos.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSelectGoodsInfos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSelectGoodsInfos();
            }
        }  else if (builder.hasSelectGoodsInfos()) {
            // 清理SelectGoodsInfos
            builder.clearSelectGoodsInfos();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivitySelectGoodsParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SELECTGOODSINFOS) && this.selectGoodsInfos != null) {
            final boolean needClear = !builder.hasSelectGoodsInfos();
            final int tmpFieldCnt = this.selectGoodsInfos.copyChangeToDb(builder.getSelectGoodsInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSelectGoodsInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivitySelectGoodsParam proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActId()) {
            this.innerSetActId(proto.getActId());
        } else {
            this.innerSetActId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSelectGoodsInfos()) {
            this.getSelectGoodsInfos().mergeFromDb(proto.getSelectGoodsInfos());
        } else {
            if (this.selectGoodsInfos != null) {
                this.selectGoodsInfos.mergeFromDb(proto.getSelectGoodsInfos());
            }
        }
        this.markAll();
        return ActivitySelectGoodsParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivitySelectGoodsParam proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActId()) {
            this.setActId(proto.getActId());
            fieldCnt++;
        }
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        if (proto.hasSelectGoodsInfos()) {
            this.getSelectGoodsInfos().mergeChangeFromDb(proto.getSelectGoodsInfos());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivitySelectGoodsParam.Builder getCopySsBuilder() {
        final ActivitySelectGoodsParam.Builder builder = ActivitySelectGoodsParam.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivitySelectGoodsParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActId() != 0) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }  else if (builder.hasActId()) {
            // 清理ActId
            builder.clearActId();
            fieldCnt++;
        }
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        if (this.selectGoodsInfos != null) {
            Struct.Int32ActivitySelectGoodsInfoMap.Builder tmpBuilder = Struct.Int32ActivitySelectGoodsInfoMap.newBuilder();
            final int tmpFieldCnt = this.selectGoodsInfos.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSelectGoodsInfos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSelectGoodsInfos();
            }
        }  else if (builder.hasSelectGoodsInfos()) {
            // 清理SelectGoodsInfos
            builder.clearSelectGoodsInfos();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivitySelectGoodsParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SELECTGOODSINFOS) && this.selectGoodsInfos != null) {
            final boolean needClear = !builder.hasSelectGoodsInfos();
            final int tmpFieldCnt = this.selectGoodsInfos.copyChangeToSs(builder.getSelectGoodsInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSelectGoodsInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivitySelectGoodsParam proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActId()) {
            this.innerSetActId(proto.getActId());
        } else {
            this.innerSetActId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSelectGoodsInfos()) {
            this.getSelectGoodsInfos().mergeFromSs(proto.getSelectGoodsInfos());
        } else {
            if (this.selectGoodsInfos != null) {
                this.selectGoodsInfos.mergeFromSs(proto.getSelectGoodsInfos());
            }
        }
        this.markAll();
        return ActivitySelectGoodsParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivitySelectGoodsParam proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActId()) {
            this.setActId(proto.getActId());
            fieldCnt++;
        }
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        if (proto.hasSelectGoodsInfos()) {
            this.getSelectGoodsInfos().mergeChangeFromSs(proto.getSelectGoodsInfos());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivitySelectGoodsParam.Builder builder = ActivitySelectGoodsParam.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SELECTGOODSINFOS) && this.selectGoodsInfos != null) {
            this.selectGoodsInfos.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.selectGoodsInfos != null) {
            this.selectGoodsInfos.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivitySelectGoodsParamProp)) {
            return false;
        }
        final ActivitySelectGoodsParamProp otherNode = (ActivitySelectGoodsParamProp) node;
        if (this.actId != otherNode.actId) {
            return false;
        }
        if (this.unitId != otherNode.unitId) {
            return false;
        }
        if (!this.getSelectGoodsInfos().compareDataTo(otherNode.getSelectGoodsInfos())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
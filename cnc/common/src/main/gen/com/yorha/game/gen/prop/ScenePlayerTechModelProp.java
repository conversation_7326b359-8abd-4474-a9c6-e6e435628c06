package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructPlayer.ScenePlayerTechModel;
import com.yorha.proto.Basic;
import com.yorha.proto.StructPlayerPB.ScenePlayerTechModelPB;
import com.yorha.proto.BasicPB;


/**
 * <AUTHOR> auto gen
 */
public class ScenePlayerTechModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_UNLOCKRESOURCE = 0;
    public static final int FIELD_INDEX_UNLOCKSPYLEVEL = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private Int32ListProp unlockResource = null;
    private Int32ListProp unlockSpyLevel = null;

    public ScenePlayerTechModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ScenePlayerTechModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get unlockResource
     *
     * @return unlockResource value
     */
    public Int32ListProp getUnlockResource() {
        if (this.unlockResource == null) {
            this.unlockResource = new Int32ListProp(this, FIELD_INDEX_UNLOCKRESOURCE);
        }
        return this.unlockResource;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addUnlockResource(Integer v) {
        this.getUnlockResource().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getUnlockResourceIndex(int index) {
        return this.getUnlockResource().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeUnlockResource(Integer v) {
        if (this.unlockResource == null) {
            return null;
        }
        if(this.unlockResource.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getUnlockResourceSize() {
        if (this.unlockResource == null) {
            return 0;
        }
        return this.unlockResource.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isUnlockResourceEmpty() {
        if (this.unlockResource == null) {
            return true;
        }
        return this.getUnlockResource().isEmpty();
    }

    /**
     * clear list
     */
    public void clearUnlockResource() {
        this.getUnlockResource().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeUnlockResourceIndex(int index) {
        return this.getUnlockResource().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setUnlockResourceIndex(int index, Integer v) {
        return this.getUnlockResource().set(index, v);
    }
    /**
     * get unlockSpyLevel
     *
     * @return unlockSpyLevel value
     */
    public Int32ListProp getUnlockSpyLevel() {
        if (this.unlockSpyLevel == null) {
            this.unlockSpyLevel = new Int32ListProp(this, FIELD_INDEX_UNLOCKSPYLEVEL);
        }
        return this.unlockSpyLevel;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addUnlockSpyLevel(Integer v) {
        this.getUnlockSpyLevel().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getUnlockSpyLevelIndex(int index) {
        return this.getUnlockSpyLevel().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeUnlockSpyLevel(Integer v) {
        if (this.unlockSpyLevel == null) {
            return null;
        }
        if(this.unlockSpyLevel.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getUnlockSpyLevelSize() {
        if (this.unlockSpyLevel == null) {
            return 0;
        }
        return this.unlockSpyLevel.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isUnlockSpyLevelEmpty() {
        if (this.unlockSpyLevel == null) {
            return true;
        }
        return this.getUnlockSpyLevel().isEmpty();
    }

    /**
     * clear list
     */
    public void clearUnlockSpyLevel() {
        this.getUnlockSpyLevel().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeUnlockSpyLevelIndex(int index) {
        return this.getUnlockSpyLevel().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setUnlockSpyLevelIndex(int index, Integer v) {
        return this.getUnlockSpyLevel().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerTechModelPB.Builder getCopyCsBuilder() {
        final ScenePlayerTechModelPB.Builder builder = ScenePlayerTechModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ScenePlayerTechModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.unlockResource != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.unlockResource.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnlockResource(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnlockResource();
            }
        }  else if (builder.hasUnlockResource()) {
            // 清理UnlockResource
            builder.clearUnlockResource();
            fieldCnt++;
        }
        if (this.unlockSpyLevel != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.unlockSpyLevel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnlockSpyLevel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnlockSpyLevel();
            }
        }  else if (builder.hasUnlockSpyLevel()) {
            // 清理UnlockSpyLevel
            builder.clearUnlockSpyLevel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ScenePlayerTechModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNLOCKRESOURCE) && this.unlockResource != null) {
            final boolean needClear = !builder.hasUnlockResource();
            final int tmpFieldCnt = this.unlockResource.copyChangeToCs(builder.getUnlockResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnlockResource();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKSPYLEVEL) && this.unlockSpyLevel != null) {
            final boolean needClear = !builder.hasUnlockSpyLevel();
            final int tmpFieldCnt = this.unlockSpyLevel.copyChangeToCs(builder.getUnlockSpyLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnlockSpyLevel();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ScenePlayerTechModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNLOCKRESOURCE) && this.unlockResource != null) {
            final boolean needClear = !builder.hasUnlockResource();
            final int tmpFieldCnt = this.unlockResource.copyChangeToCs(builder.getUnlockResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnlockResource();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKSPYLEVEL) && this.unlockSpyLevel != null) {
            final boolean needClear = !builder.hasUnlockSpyLevel();
            final int tmpFieldCnt = this.unlockSpyLevel.copyChangeToCs(builder.getUnlockSpyLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnlockSpyLevel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ScenePlayerTechModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnlockResource()) {
            this.getUnlockResource().mergeFromCs(proto.getUnlockResource());
        } else {
            if (this.unlockResource != null) {
                this.unlockResource.mergeFromCs(proto.getUnlockResource());
            }
        }
        if (proto.hasUnlockSpyLevel()) {
            this.getUnlockSpyLevel().mergeFromCs(proto.getUnlockSpyLevel());
        } else {
            if (this.unlockSpyLevel != null) {
                this.unlockSpyLevel.mergeFromCs(proto.getUnlockSpyLevel());
            }
        }
        this.markAll();
        return ScenePlayerTechModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ScenePlayerTechModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnlockResource()) {
            this.getUnlockResource().mergeChangeFromCs(proto.getUnlockResource());
            fieldCnt++;
        }
        if (proto.hasUnlockSpyLevel()) {
            this.getUnlockSpyLevel().mergeChangeFromCs(proto.getUnlockSpyLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerTechModel.Builder getCopyDbBuilder() {
        final ScenePlayerTechModel.Builder builder = ScenePlayerTechModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ScenePlayerTechModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.unlockResource != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.unlockResource.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnlockResource(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnlockResource();
            }
        }  else if (builder.hasUnlockResource()) {
            // 清理UnlockResource
            builder.clearUnlockResource();
            fieldCnt++;
        }
        if (this.unlockSpyLevel != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.unlockSpyLevel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnlockSpyLevel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnlockSpyLevel();
            }
        }  else if (builder.hasUnlockSpyLevel()) {
            // 清理UnlockSpyLevel
            builder.clearUnlockSpyLevel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ScenePlayerTechModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNLOCKRESOURCE) && this.unlockResource != null) {
            final boolean needClear = !builder.hasUnlockResource();
            final int tmpFieldCnt = this.unlockResource.copyChangeToDb(builder.getUnlockResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnlockResource();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKSPYLEVEL) && this.unlockSpyLevel != null) {
            final boolean needClear = !builder.hasUnlockSpyLevel();
            final int tmpFieldCnt = this.unlockSpyLevel.copyChangeToDb(builder.getUnlockSpyLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnlockSpyLevel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ScenePlayerTechModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnlockResource()) {
            this.getUnlockResource().mergeFromDb(proto.getUnlockResource());
        } else {
            if (this.unlockResource != null) {
                this.unlockResource.mergeFromDb(proto.getUnlockResource());
            }
        }
        if (proto.hasUnlockSpyLevel()) {
            this.getUnlockSpyLevel().mergeFromDb(proto.getUnlockSpyLevel());
        } else {
            if (this.unlockSpyLevel != null) {
                this.unlockSpyLevel.mergeFromDb(proto.getUnlockSpyLevel());
            }
        }
        this.markAll();
        return ScenePlayerTechModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ScenePlayerTechModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnlockResource()) {
            this.getUnlockResource().mergeChangeFromDb(proto.getUnlockResource());
            fieldCnt++;
        }
        if (proto.hasUnlockSpyLevel()) {
            this.getUnlockSpyLevel().mergeChangeFromDb(proto.getUnlockSpyLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerTechModel.Builder getCopySsBuilder() {
        final ScenePlayerTechModel.Builder builder = ScenePlayerTechModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ScenePlayerTechModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.unlockResource != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.unlockResource.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnlockResource(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnlockResource();
            }
        }  else if (builder.hasUnlockResource()) {
            // 清理UnlockResource
            builder.clearUnlockResource();
            fieldCnt++;
        }
        if (this.unlockSpyLevel != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.unlockSpyLevel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUnlockSpyLevel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUnlockSpyLevel();
            }
        }  else if (builder.hasUnlockSpyLevel()) {
            // 清理UnlockSpyLevel
            builder.clearUnlockSpyLevel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ScenePlayerTechModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNLOCKRESOURCE) && this.unlockResource != null) {
            final boolean needClear = !builder.hasUnlockResource();
            final int tmpFieldCnt = this.unlockResource.copyChangeToSs(builder.getUnlockResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnlockResource();
            }
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKSPYLEVEL) && this.unlockSpyLevel != null) {
            final boolean needClear = !builder.hasUnlockSpyLevel();
            final int tmpFieldCnt = this.unlockSpyLevel.copyChangeToSs(builder.getUnlockSpyLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUnlockSpyLevel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ScenePlayerTechModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnlockResource()) {
            this.getUnlockResource().mergeFromSs(proto.getUnlockResource());
        } else {
            if (this.unlockResource != null) {
                this.unlockResource.mergeFromSs(proto.getUnlockResource());
            }
        }
        if (proto.hasUnlockSpyLevel()) {
            this.getUnlockSpyLevel().mergeFromSs(proto.getUnlockSpyLevel());
        } else {
            if (this.unlockSpyLevel != null) {
                this.unlockSpyLevel.mergeFromSs(proto.getUnlockSpyLevel());
            }
        }
        this.markAll();
        return ScenePlayerTechModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ScenePlayerTechModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnlockResource()) {
            this.getUnlockResource().mergeChangeFromSs(proto.getUnlockResource());
            fieldCnt++;
        }
        if (proto.hasUnlockSpyLevel()) {
            this.getUnlockSpyLevel().mergeChangeFromSs(proto.getUnlockSpyLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ScenePlayerTechModel.Builder builder = ScenePlayerTechModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKRESOURCE) && this.unlockResource != null) {
            this.unlockResource.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKSPYLEVEL) && this.unlockSpyLevel != null) {
            this.unlockSpyLevel.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.unlockResource != null) {
            this.unlockResource.markAll();
        }
        if (this.unlockSpyLevel != null) {
            this.unlockSpyLevel.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ScenePlayerTechModelProp)) {
            return false;
        }
        final ScenePlayerTechModelProp otherNode = (ScenePlayerTechModelProp) node;
        if (!this.getUnlockResource().compareDataTo(otherNode.getUnlockResource())) {
            return false;
        }
        if (!this.getUnlockSpyLevel().compareDataTo(otherNode.getUnlockSpyLevel())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
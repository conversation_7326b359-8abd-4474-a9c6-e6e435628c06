package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.Zone.ZoneInfoEntity;
import com.yorha.proto.Struct;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructCommon;
import com.yorha.proto.Zone;
import com.yorha.proto.ZonePB.ZoneInfoEntityPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructBattlePB;
import com.yorha.proto.StructCommonPB;
import com.yorha.proto.ZonePB;


/**
 * <AUTHOR> auto gen
 */
public class ZoneInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_LANGUAGELIMIT = 0;
    public static final int FIELD_INDEX_CURMONSTERMILESTONE = 1;
    public static final int FIELD_INDEX_NEXTMILESTONESTAMP = 2;
    public static final int FIELD_INDEX_SERVEROPENTSMS = 3;
    public static final int FIELD_INDEX_SCENEMILESTONEMODEL = 4;
    public static final int FIELD_INDEX_CROSSDATA = 5;
    public static final int FIELD_INDEX_INTERNALPLAYERNUM = 6;
    public static final int FIELD_INDEX_ISOPENED = 7;
    public static final int FIELD_INDEX_REFRESHMODEL = 8;
    public static final int FIELD_INDEX_SCENEACTIVITYINFOMODEL = 9;
    public static final int FIELD_INDEX_KINGDOMMODEL = 10;
    public static final int FIELD_INDEX_DEVBUFFSYS = 11;
    public static final int FIELD_INDEX_ADDITIONSYS = 12;
    public static final int FIELD_INDEX_REGIONDRIVETRAFFICHARDWARELIMIT = 13;
    public static final int FIELD_INDEX_SEASONMODEL = 14;

    public static final int FIELD_COUNT = 15;

    private long markBits0 = 0L;
    private PropertyChangeListener listener;

    private Int32LanguageLimitItemMapProp languageLimit = null;
    private int curMonsterMilestone = Constant.DEFAULT_INT_VALUE;
    private long nextMilestoneStamp = Constant.DEFAULT_LONG_VALUE;
    private long serverOpenTsMs = Constant.DEFAULT_LONG_VALUE;
    private SceneMileStoneModelProp sceneMileStoneModel = null;
    private CrossDataModelProp crossData = null;
    private int internalPlayerNum = Constant.DEFAULT_INT_VALUE;
    private boolean isOpened = Constant.DEFAULT_BOOLEAN_VALUE;
    private RefreshModelProp refreshModel = null;
    private SceneActivityInfoModelProp sceneActivityInfoModel = null;
    private KingdomModelProp kingdomModel = null;
    private DevBuffSysProp devBuffSys = null;
    private AdditionSysProp additionSys = null;
    private Int32RegionDriveTrafficHardwareLimitMapProp regionDriveTrafficHardwareLimit = null;
    private ZoneSeasonModelProp seasonModel = null;

    public ZoneInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ZoneInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get languageLimit
     *
     * @return languageLimit value
     */
    public Int32LanguageLimitItemMapProp getLanguageLimit() {
        if (this.languageLimit == null) {
            this.languageLimit = new Int32LanguageLimitItemMapProp(this, FIELD_INDEX_LANGUAGELIMIT);
        }
        return this.languageLimit;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putLanguageLimitV(LanguageLimitItemProp v) {
        this.getLanguageLimit().put(v.getType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public LanguageLimitItemProp addEmptyLanguageLimit(Integer k) {
        return this.getLanguageLimit().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getLanguageLimitSize() {
        if (this.languageLimit == null) {
            return 0;
        }
        return this.languageLimit.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isLanguageLimitEmpty() {
        if (this.languageLimit == null) {
            return true;
        }
        return this.languageLimit.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public LanguageLimitItemProp getLanguageLimitV(Integer k) {
        if (this.languageLimit == null || !this.languageLimit.containsKey(k)) {
            return null;
        }
        return this.languageLimit.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearLanguageLimit() {
        if (this.languageLimit != null) {
            this.languageLimit.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeLanguageLimitV(Integer k) {
        if (this.languageLimit != null) {
            this.languageLimit.remove(k);
        }
    }
    /**
     * get curMonsterMilestone
     *
     * @return curMonsterMilestone value
     */
    public int getCurMonsterMilestone() {
        return this.curMonsterMilestone;
    }

    /**
     * set curMonsterMilestone && set marked
     *
     * @param curMonsterMilestone new value
     * @return current object
     */
    public ZoneInfoProp setCurMonsterMilestone(int curMonsterMilestone) {
        if (this.curMonsterMilestone != curMonsterMilestone) {
            this.mark(FIELD_INDEX_CURMONSTERMILESTONE);
            this.curMonsterMilestone = curMonsterMilestone;
        }
        return this;
    }

    /**
     * inner set curMonsterMilestone
     *
     * @param curMonsterMilestone new value
     */
    private void innerSetCurMonsterMilestone(int curMonsterMilestone) {
        this.curMonsterMilestone = curMonsterMilestone;
    }

    /**
     * get nextMilestoneStamp
     *
     * @return nextMilestoneStamp value
     */
    public long getNextMilestoneStamp() {
        return this.nextMilestoneStamp;
    }

    /**
     * set nextMilestoneStamp && set marked
     *
     * @param nextMilestoneStamp new value
     * @return current object
     */
    public ZoneInfoProp setNextMilestoneStamp(long nextMilestoneStamp) {
        if (this.nextMilestoneStamp != nextMilestoneStamp) {
            this.mark(FIELD_INDEX_NEXTMILESTONESTAMP);
            this.nextMilestoneStamp = nextMilestoneStamp;
        }
        return this;
    }

    /**
     * inner set nextMilestoneStamp
     *
     * @param nextMilestoneStamp new value
     */
    private void innerSetNextMilestoneStamp(long nextMilestoneStamp) {
        this.nextMilestoneStamp = nextMilestoneStamp;
    }

    /**
     * get serverOpenTsMs
     *
     * @return serverOpenTsMs value
     */
    public long getServerOpenTsMs() {
        return this.serverOpenTsMs;
    }

    /**
     * set serverOpenTsMs && set marked
     *
     * @param serverOpenTsMs new value
     * @return current object
     */
    public ZoneInfoProp setServerOpenTsMs(long serverOpenTsMs) {
        if (this.serverOpenTsMs != serverOpenTsMs) {
            this.mark(FIELD_INDEX_SERVEROPENTSMS);
            this.serverOpenTsMs = serverOpenTsMs;
        }
        return this;
    }

    /**
     * inner set serverOpenTsMs
     *
     * @param serverOpenTsMs new value
     */
    private void innerSetServerOpenTsMs(long serverOpenTsMs) {
        this.serverOpenTsMs = serverOpenTsMs;
    }

    /**
     * get sceneMileStoneModel
     *
     * @return sceneMileStoneModel value
     */
    public SceneMileStoneModelProp getSceneMileStoneModel() {
        if (this.sceneMileStoneModel == null) {
            this.sceneMileStoneModel = new SceneMileStoneModelProp(this, FIELD_INDEX_SCENEMILESTONEMODEL);
        }
        return this.sceneMileStoneModel;
    }

    /**
     * get crossData
     *
     * @return crossData value
     */
    public CrossDataModelProp getCrossData() {
        if (this.crossData == null) {
            this.crossData = new CrossDataModelProp(this, FIELD_INDEX_CROSSDATA);
        }
        return this.crossData;
    }

    /**
     * get internalPlayerNum
     *
     * @return internalPlayerNum value
     */
    public int getInternalPlayerNum() {
        return this.internalPlayerNum;
    }

    /**
     * set internalPlayerNum && set marked
     *
     * @param internalPlayerNum new value
     * @return current object
     */
    public ZoneInfoProp setInternalPlayerNum(int internalPlayerNum) {
        if (this.internalPlayerNum != internalPlayerNum) {
            this.mark(FIELD_INDEX_INTERNALPLAYERNUM);
            this.internalPlayerNum = internalPlayerNum;
        }
        return this;
    }

    /**
     * inner set internalPlayerNum
     *
     * @param internalPlayerNum new value
     */
    private void innerSetInternalPlayerNum(int internalPlayerNum) {
        this.internalPlayerNum = internalPlayerNum;
    }

    /**
     * get isOpened
     *
     * @return isOpened value
     */
    public boolean getIsOpened() {
        return this.isOpened;
    }

    /**
     * set isOpened && set marked
     *
     * @param isOpened new value
     * @return current object
     */
    public ZoneInfoProp setIsOpened(boolean isOpened) {
        if (this.isOpened != isOpened) {
            this.mark(FIELD_INDEX_ISOPENED);
            this.isOpened = isOpened;
        }
        return this;
    }

    /**
     * inner set isOpened
     *
     * @param isOpened new value
     */
    private void innerSetIsOpened(boolean isOpened) {
        this.isOpened = isOpened;
    }

    /**
     * get refreshModel
     *
     * @return refreshModel value
     */
    public RefreshModelProp getRefreshModel() {
        if (this.refreshModel == null) {
            this.refreshModel = new RefreshModelProp(this, FIELD_INDEX_REFRESHMODEL);
        }
        return this.refreshModel;
    }

    /**
     * get sceneActivityInfoModel
     *
     * @return sceneActivityInfoModel value
     */
    public SceneActivityInfoModelProp getSceneActivityInfoModel() {
        if (this.sceneActivityInfoModel == null) {
            this.sceneActivityInfoModel = new SceneActivityInfoModelProp(this, FIELD_INDEX_SCENEACTIVITYINFOMODEL);
        }
        return this.sceneActivityInfoModel;
    }

    /**
     * get kingdomModel
     *
     * @return kingdomModel value
     */
    public KingdomModelProp getKingdomModel() {
        if (this.kingdomModel == null) {
            this.kingdomModel = new KingdomModelProp(this, FIELD_INDEX_KINGDOMMODEL);
        }
        return this.kingdomModel;
    }

    /**
     * get devBuffSys
     *
     * @return devBuffSys value
     */
    public DevBuffSysProp getDevBuffSys() {
        if (this.devBuffSys == null) {
            this.devBuffSys = new DevBuffSysProp(this, FIELD_INDEX_DEVBUFFSYS);
        }
        return this.devBuffSys;
    }

    /**
     * get additionSys
     *
     * @return additionSys value
     */
    public AdditionSysProp getAdditionSys() {
        if (this.additionSys == null) {
            this.additionSys = new AdditionSysProp(this, FIELD_INDEX_ADDITIONSYS);
        }
        return this.additionSys;
    }

    /**
     * get regionDriveTrafficHardwareLimit
     *
     * @return regionDriveTrafficHardwareLimit value
     */
    public Int32RegionDriveTrafficHardwareLimitMapProp getRegionDriveTrafficHardwareLimit() {
        if (this.regionDriveTrafficHardwareLimit == null) {
            this.regionDriveTrafficHardwareLimit = new Int32RegionDriveTrafficHardwareLimitMapProp(this, FIELD_INDEX_REGIONDRIVETRAFFICHARDWARELIMIT);
        }
        return this.regionDriveTrafficHardwareLimit;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putRegionDriveTrafficHardwareLimitV(RegionDriveTrafficHardwareLimitProp v) {
        this.getRegionDriveTrafficHardwareLimit().put(v.getRegionId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public RegionDriveTrafficHardwareLimitProp addEmptyRegionDriveTrafficHardwareLimit(Integer k) {
        return this.getRegionDriveTrafficHardwareLimit().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getRegionDriveTrafficHardwareLimitSize() {
        if (this.regionDriveTrafficHardwareLimit == null) {
            return 0;
        }
        return this.regionDriveTrafficHardwareLimit.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isRegionDriveTrafficHardwareLimitEmpty() {
        if (this.regionDriveTrafficHardwareLimit == null) {
            return true;
        }
        return this.regionDriveTrafficHardwareLimit.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public RegionDriveTrafficHardwareLimitProp getRegionDriveTrafficHardwareLimitV(Integer k) {
        if (this.regionDriveTrafficHardwareLimit == null || !this.regionDriveTrafficHardwareLimit.containsKey(k)) {
            return null;
        }
        return this.regionDriveTrafficHardwareLimit.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearRegionDriveTrafficHardwareLimit() {
        if (this.regionDriveTrafficHardwareLimit != null) {
            this.regionDriveTrafficHardwareLimit.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeRegionDriveTrafficHardwareLimitV(Integer k) {
        if (this.regionDriveTrafficHardwareLimit != null) {
            this.regionDriveTrafficHardwareLimit.remove(k);
        }
    }
    /**
     * get seasonModel
     *
     * @return seasonModel value
     */
    public ZoneSeasonModelProp getSeasonModel() {
        if (this.seasonModel == null) {
            this.seasonModel = new ZoneSeasonModelProp(this, FIELD_INDEX_SEASONMODEL);
        }
        return this.seasonModel;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneInfoEntityPB.Builder getCopyCsBuilder() {
        final ZoneInfoEntityPB.Builder builder = ZoneInfoEntityPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ZoneInfoEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurMonsterMilestone() != 0) {
            builder.setCurMonsterMilestone(this.getCurMonsterMilestone());
            fieldCnt++;
        }  else if (builder.hasCurMonsterMilestone()) {
            // 清理CurMonsterMilestone
            builder.clearCurMonsterMilestone();
            fieldCnt++;
        }
        if (this.getNextMilestoneStamp() != 0L) {
            builder.setNextMilestoneStamp(this.getNextMilestoneStamp());
            fieldCnt++;
        }  else if (builder.hasNextMilestoneStamp()) {
            // 清理NextMilestoneStamp
            builder.clearNextMilestoneStamp();
            fieldCnt++;
        }
        if (this.getServerOpenTsMs() != 0L) {
            builder.setServerOpenTsMs(this.getServerOpenTsMs());
            fieldCnt++;
        }  else if (builder.hasServerOpenTsMs()) {
            // 清理ServerOpenTsMs
            builder.clearServerOpenTsMs();
            fieldCnt++;
        }
        if (this.crossData != null) {
            StructCommonPB.CrossDataModelPB.Builder tmpBuilder = StructCommonPB.CrossDataModelPB.newBuilder();
            final int tmpFieldCnt = this.crossData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCrossData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCrossData();
            }
        }  else if (builder.hasCrossData()) {
            // 清理CrossData
            builder.clearCrossData();
            fieldCnt++;
        }
        if (this.getInternalPlayerNum() != 0) {
            builder.setInternalPlayerNum(this.getInternalPlayerNum());
            fieldCnt++;
        }  else if (builder.hasInternalPlayerNum()) {
            // 清理InternalPlayerNum
            builder.clearInternalPlayerNum();
            fieldCnt++;
        }
        if (this.getIsOpened()) {
            builder.setIsOpened(this.getIsOpened());
            fieldCnt++;
        }  else if (builder.hasIsOpened()) {
            // 清理IsOpened
            builder.clearIsOpened();
            fieldCnt++;
        }
        if (this.kingdomModel != null) {
            ZonePB.KingdomModelPB.Builder tmpBuilder = ZonePB.KingdomModelPB.newBuilder();
            final int tmpFieldCnt = this.kingdomModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKingdomModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKingdomModel();
            }
        }  else if (builder.hasKingdomModel()) {
            // 清理KingdomModel
            builder.clearKingdomModel();
            fieldCnt++;
        }
        if (this.devBuffSys != null) {
            StructBattlePB.DevBuffSysPB.Builder tmpBuilder = StructBattlePB.DevBuffSysPB.newBuilder();
            final int tmpFieldCnt = this.devBuffSys.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSys();
            }
        }  else if (builder.hasDevBuffSys()) {
            // 清理DevBuffSys
            builder.clearDevBuffSys();
            fieldCnt++;
        }
        if (this.additionSys != null) {
            StructPB.AdditionSysPB.Builder tmpBuilder = StructPB.AdditionSysPB.newBuilder();
            final int tmpFieldCnt = this.additionSys.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionSys();
            }
        }  else if (builder.hasAdditionSys()) {
            // 清理AdditionSys
            builder.clearAdditionSys();
            fieldCnt++;
        }
        if (this.seasonModel != null) {
            ZonePB.ZoneSeasonModelPB.Builder tmpBuilder = ZonePB.ZoneSeasonModelPB.newBuilder();
            final int tmpFieldCnt = this.seasonModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSeasonModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSeasonModel();
            }
        }  else if (builder.hasSeasonModel()) {
            // 清理SeasonModel
            builder.clearSeasonModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ZoneInfoEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURMONSTERMILESTONE)) {
            builder.setCurMonsterMilestone(this.getCurMonsterMilestone());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTMILESTONESTAMP)) {
            builder.setNextMilestoneStamp(this.getNextMilestoneStamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SERVEROPENTSMS)) {
            builder.setServerOpenTsMs(this.getServerOpenTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CROSSDATA) && this.crossData != null) {
            final boolean needClear = !builder.hasCrossData();
            final int tmpFieldCnt = this.crossData.copyChangeToCs(builder.getCrossDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCrossData();
            }
        }
        if (this.hasMark(FIELD_INDEX_INTERNALPLAYERNUM)) {
            builder.setInternalPlayerNum(this.getInternalPlayerNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISOPENED)) {
            builder.setIsOpened(this.getIsOpened());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_KINGDOMMODEL) && this.kingdomModel != null) {
            final boolean needClear = !builder.hasKingdomModel();
            final int tmpFieldCnt = this.kingdomModel.copyChangeToCs(builder.getKingdomModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingdomModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToCs(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            final boolean needClear = !builder.hasAdditionSys();
            final int tmpFieldCnt = this.additionSys.copyChangeToCs(builder.getAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_SEASONMODEL) && this.seasonModel != null) {
            final boolean needClear = !builder.hasSeasonModel();
            final int tmpFieldCnt = this.seasonModel.copyChangeToCs(builder.getSeasonModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSeasonModel();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ZoneInfoEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURMONSTERMILESTONE)) {
            builder.setCurMonsterMilestone(this.getCurMonsterMilestone());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTMILESTONESTAMP)) {
            builder.setNextMilestoneStamp(this.getNextMilestoneStamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SERVEROPENTSMS)) {
            builder.setServerOpenTsMs(this.getServerOpenTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CROSSDATA) && this.crossData != null) {
            final boolean needClear = !builder.hasCrossData();
            final int tmpFieldCnt = this.crossData.copyChangeToAndClearDeleteKeysCs(builder.getCrossDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCrossData();
            }
        }
        if (this.hasMark(FIELD_INDEX_INTERNALPLAYERNUM)) {
            builder.setInternalPlayerNum(this.getInternalPlayerNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISOPENED)) {
            builder.setIsOpened(this.getIsOpened());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_KINGDOMMODEL) && this.kingdomModel != null) {
            final boolean needClear = !builder.hasKingdomModel();
            final int tmpFieldCnt = this.kingdomModel.copyChangeToAndClearDeleteKeysCs(builder.getKingdomModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingdomModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToAndClearDeleteKeysCs(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            final boolean needClear = !builder.hasAdditionSys();
            final int tmpFieldCnt = this.additionSys.copyChangeToAndClearDeleteKeysCs(builder.getAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_SEASONMODEL) && this.seasonModel != null) {
            final boolean needClear = !builder.hasSeasonModel();
            final int tmpFieldCnt = this.seasonModel.copyChangeToAndClearDeleteKeysCs(builder.getSeasonModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSeasonModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ZoneInfoEntityPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurMonsterMilestone()) {
            this.innerSetCurMonsterMilestone(proto.getCurMonsterMilestone());
        } else {
            this.innerSetCurMonsterMilestone(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNextMilestoneStamp()) {
            this.innerSetNextMilestoneStamp(proto.getNextMilestoneStamp());
        } else {
            this.innerSetNextMilestoneStamp(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasServerOpenTsMs()) {
            this.innerSetServerOpenTsMs(proto.getServerOpenTsMs());
        } else {
            this.innerSetServerOpenTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCrossData()) {
            this.getCrossData().mergeFromCs(proto.getCrossData());
        } else {
            if (this.crossData != null) {
                this.crossData.mergeFromCs(proto.getCrossData());
            }
        }
        if (proto.hasInternalPlayerNum()) {
            this.innerSetInternalPlayerNum(proto.getInternalPlayerNum());
        } else {
            this.innerSetInternalPlayerNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIsOpened()) {
            this.innerSetIsOpened(proto.getIsOpened());
        } else {
            this.innerSetIsOpened(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasKingdomModel()) {
            this.getKingdomModel().mergeFromCs(proto.getKingdomModel());
        } else {
            if (this.kingdomModel != null) {
                this.kingdomModel.mergeFromCs(proto.getKingdomModel());
            }
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeFromCs(proto.getDevBuffSys());
        } else {
            if (this.devBuffSys != null) {
                this.devBuffSys.mergeFromCs(proto.getDevBuffSys());
            }
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeFromCs(proto.getAdditionSys());
        } else {
            if (this.additionSys != null) {
                this.additionSys.mergeFromCs(proto.getAdditionSys());
            }
        }
        if (proto.hasSeasonModel()) {
            this.getSeasonModel().mergeFromCs(proto.getSeasonModel());
        } else {
            if (this.seasonModel != null) {
                this.seasonModel.mergeFromCs(proto.getSeasonModel());
            }
        }
        this.markAll();
        return ZoneInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ZoneInfoEntityPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurMonsterMilestone()) {
            this.setCurMonsterMilestone(proto.getCurMonsterMilestone());
            fieldCnt++;
        }
        if (proto.hasNextMilestoneStamp()) {
            this.setNextMilestoneStamp(proto.getNextMilestoneStamp());
            fieldCnt++;
        }
        if (proto.hasServerOpenTsMs()) {
            this.setServerOpenTsMs(proto.getServerOpenTsMs());
            fieldCnt++;
        }
        if (proto.hasCrossData()) {
            this.getCrossData().mergeChangeFromCs(proto.getCrossData());
            fieldCnt++;
        }
        if (proto.hasInternalPlayerNum()) {
            this.setInternalPlayerNum(proto.getInternalPlayerNum());
            fieldCnt++;
        }
        if (proto.hasIsOpened()) {
            this.setIsOpened(proto.getIsOpened());
            fieldCnt++;
        }
        if (proto.hasKingdomModel()) {
            this.getKingdomModel().mergeChangeFromCs(proto.getKingdomModel());
            fieldCnt++;
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeChangeFromCs(proto.getDevBuffSys());
            fieldCnt++;
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeChangeFromCs(proto.getAdditionSys());
            fieldCnt++;
        }
        if (proto.hasSeasonModel()) {
            this.getSeasonModel().mergeChangeFromCs(proto.getSeasonModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneInfoEntity.Builder getCopyDbBuilder() {
        final ZoneInfoEntity.Builder builder = ZoneInfoEntity.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ZoneInfoEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.languageLimit != null) {
            Zone.Int32LanguageLimitItemMap.Builder tmpBuilder = Zone.Int32LanguageLimitItemMap.newBuilder();
            final int tmpFieldCnt = this.languageLimit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLanguageLimit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLanguageLimit();
            }
        }  else if (builder.hasLanguageLimit()) {
            // 清理LanguageLimit
            builder.clearLanguageLimit();
            fieldCnt++;
        }
        if (this.getCurMonsterMilestone() != 0) {
            builder.setCurMonsterMilestone(this.getCurMonsterMilestone());
            fieldCnt++;
        }  else if (builder.hasCurMonsterMilestone()) {
            // 清理CurMonsterMilestone
            builder.clearCurMonsterMilestone();
            fieldCnt++;
        }
        if (this.getNextMilestoneStamp() != 0L) {
            builder.setNextMilestoneStamp(this.getNextMilestoneStamp());
            fieldCnt++;
        }  else if (builder.hasNextMilestoneStamp()) {
            // 清理NextMilestoneStamp
            builder.clearNextMilestoneStamp();
            fieldCnt++;
        }
        if (this.getServerOpenTsMs() != 0L) {
            builder.setServerOpenTsMs(this.getServerOpenTsMs());
            fieldCnt++;
        }  else if (builder.hasServerOpenTsMs()) {
            // 清理ServerOpenTsMs
            builder.clearServerOpenTsMs();
            fieldCnt++;
        }
        if (this.sceneMileStoneModel != null) {
            Struct.SceneMileStoneModel.Builder tmpBuilder = Struct.SceneMileStoneModel.newBuilder();
            final int tmpFieldCnt = this.sceneMileStoneModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSceneMileStoneModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSceneMileStoneModel();
            }
        }  else if (builder.hasSceneMileStoneModel()) {
            // 清理SceneMileStoneModel
            builder.clearSceneMileStoneModel();
            fieldCnt++;
        }
        if (this.crossData != null) {
            StructCommon.CrossDataModel.Builder tmpBuilder = StructCommon.CrossDataModel.newBuilder();
            final int tmpFieldCnt = this.crossData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCrossData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCrossData();
            }
        }  else if (builder.hasCrossData()) {
            // 清理CrossData
            builder.clearCrossData();
            fieldCnt++;
        }
        if (this.getInternalPlayerNum() != 0) {
            builder.setInternalPlayerNum(this.getInternalPlayerNum());
            fieldCnt++;
        }  else if (builder.hasInternalPlayerNum()) {
            // 清理InternalPlayerNum
            builder.clearInternalPlayerNum();
            fieldCnt++;
        }
        if (this.getIsOpened()) {
            builder.setIsOpened(this.getIsOpened());
            fieldCnt++;
        }  else if (builder.hasIsOpened()) {
            // 清理IsOpened
            builder.clearIsOpened();
            fieldCnt++;
        }
        if (this.refreshModel != null) {
            Zone.RefreshModel.Builder tmpBuilder = Zone.RefreshModel.newBuilder();
            final int tmpFieldCnt = this.refreshModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRefreshModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRefreshModel();
            }
        }  else if (builder.hasRefreshModel()) {
            // 清理RefreshModel
            builder.clearRefreshModel();
            fieldCnt++;
        }
        if (this.sceneActivityInfoModel != null) {
            Zone.SceneActivityInfoModel.Builder tmpBuilder = Zone.SceneActivityInfoModel.newBuilder();
            final int tmpFieldCnt = this.sceneActivityInfoModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSceneActivityInfoModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSceneActivityInfoModel();
            }
        }  else if (builder.hasSceneActivityInfoModel()) {
            // 清理SceneActivityInfoModel
            builder.clearSceneActivityInfoModel();
            fieldCnt++;
        }
        if (this.kingdomModel != null) {
            Zone.KingdomModel.Builder tmpBuilder = Zone.KingdomModel.newBuilder();
            final int tmpFieldCnt = this.kingdomModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKingdomModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKingdomModel();
            }
        }  else if (builder.hasKingdomModel()) {
            // 清理KingdomModel
            builder.clearKingdomModel();
            fieldCnt++;
        }
        if (this.devBuffSys != null) {
            StructBattle.DevBuffSys.Builder tmpBuilder = StructBattle.DevBuffSys.newBuilder();
            final int tmpFieldCnt = this.devBuffSys.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSys();
            }
        }  else if (builder.hasDevBuffSys()) {
            // 清理DevBuffSys
            builder.clearDevBuffSys();
            fieldCnt++;
        }
        if (this.additionSys != null) {
            Struct.AdditionSys.Builder tmpBuilder = Struct.AdditionSys.newBuilder();
            final int tmpFieldCnt = this.additionSys.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionSys();
            }
        }  else if (builder.hasAdditionSys()) {
            // 清理AdditionSys
            builder.clearAdditionSys();
            fieldCnt++;
        }
        if (this.regionDriveTrafficHardwareLimit != null) {
            Zone.Int32RegionDriveTrafficHardwareLimitMap.Builder tmpBuilder = Zone.Int32RegionDriveTrafficHardwareLimitMap.newBuilder();
            final int tmpFieldCnt = this.regionDriveTrafficHardwareLimit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRegionDriveTrafficHardwareLimit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRegionDriveTrafficHardwareLimit();
            }
        }  else if (builder.hasRegionDriveTrafficHardwareLimit()) {
            // 清理RegionDriveTrafficHardwareLimit
            builder.clearRegionDriveTrafficHardwareLimit();
            fieldCnt++;
        }
        if (this.seasonModel != null) {
            Zone.ZoneSeasonModel.Builder tmpBuilder = Zone.ZoneSeasonModel.newBuilder();
            final int tmpFieldCnt = this.seasonModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSeasonModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSeasonModel();
            }
        }  else if (builder.hasSeasonModel()) {
            // 清理SeasonModel
            builder.clearSeasonModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ZoneInfoEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LANGUAGELIMIT) && this.languageLimit != null) {
            final boolean needClear = !builder.hasLanguageLimit();
            final int tmpFieldCnt = this.languageLimit.copyChangeToDb(builder.getLanguageLimitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLanguageLimit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CURMONSTERMILESTONE)) {
            builder.setCurMonsterMilestone(this.getCurMonsterMilestone());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTMILESTONESTAMP)) {
            builder.setNextMilestoneStamp(this.getNextMilestoneStamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SERVEROPENTSMS)) {
            builder.setServerOpenTsMs(this.getServerOpenTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCENEMILESTONEMODEL) && this.sceneMileStoneModel != null) {
            final boolean needClear = !builder.hasSceneMileStoneModel();
            final int tmpFieldCnt = this.sceneMileStoneModel.copyChangeToDb(builder.getSceneMileStoneModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSceneMileStoneModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_CROSSDATA) && this.crossData != null) {
            final boolean needClear = !builder.hasCrossData();
            final int tmpFieldCnt = this.crossData.copyChangeToDb(builder.getCrossDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCrossData();
            }
        }
        if (this.hasMark(FIELD_INDEX_INTERNALPLAYERNUM)) {
            builder.setInternalPlayerNum(this.getInternalPlayerNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISOPENED)) {
            builder.setIsOpened(this.getIsOpened());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REFRESHMODEL) && this.refreshModel != null) {
            final boolean needClear = !builder.hasRefreshModel();
            final int tmpFieldCnt = this.refreshModel.copyChangeToDb(builder.getRefreshModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRefreshModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCENEACTIVITYINFOMODEL) && this.sceneActivityInfoModel != null) {
            final boolean needClear = !builder.hasSceneActivityInfoModel();
            final int tmpFieldCnt = this.sceneActivityInfoModel.copyChangeToDb(builder.getSceneActivityInfoModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSceneActivityInfoModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_KINGDOMMODEL) && this.kingdomModel != null) {
            final boolean needClear = !builder.hasKingdomModel();
            final int tmpFieldCnt = this.kingdomModel.copyChangeToDb(builder.getKingdomModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingdomModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToDb(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            final boolean needClear = !builder.hasAdditionSys();
            final int tmpFieldCnt = this.additionSys.copyChangeToDb(builder.getAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_REGIONDRIVETRAFFICHARDWARELIMIT) && this.regionDriveTrafficHardwareLimit != null) {
            final boolean needClear = !builder.hasRegionDriveTrafficHardwareLimit();
            final int tmpFieldCnt = this.regionDriveTrafficHardwareLimit.copyChangeToDb(builder.getRegionDriveTrafficHardwareLimitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRegionDriveTrafficHardwareLimit();
            }
        }
        if (this.hasMark(FIELD_INDEX_SEASONMODEL) && this.seasonModel != null) {
            final boolean needClear = !builder.hasSeasonModel();
            final int tmpFieldCnt = this.seasonModel.copyChangeToDb(builder.getSeasonModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSeasonModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ZoneInfoEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLanguageLimit()) {
            this.getLanguageLimit().mergeFromDb(proto.getLanguageLimit());
        } else {
            if (this.languageLimit != null) {
                this.languageLimit.mergeFromDb(proto.getLanguageLimit());
            }
        }
        if (proto.hasCurMonsterMilestone()) {
            this.innerSetCurMonsterMilestone(proto.getCurMonsterMilestone());
        } else {
            this.innerSetCurMonsterMilestone(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNextMilestoneStamp()) {
            this.innerSetNextMilestoneStamp(proto.getNextMilestoneStamp());
        } else {
            this.innerSetNextMilestoneStamp(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasServerOpenTsMs()) {
            this.innerSetServerOpenTsMs(proto.getServerOpenTsMs());
        } else {
            this.innerSetServerOpenTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSceneMileStoneModel()) {
            this.getSceneMileStoneModel().mergeFromDb(proto.getSceneMileStoneModel());
        } else {
            if (this.sceneMileStoneModel != null) {
                this.sceneMileStoneModel.mergeFromDb(proto.getSceneMileStoneModel());
            }
        }
        if (proto.hasCrossData()) {
            this.getCrossData().mergeFromDb(proto.getCrossData());
        } else {
            if (this.crossData != null) {
                this.crossData.mergeFromDb(proto.getCrossData());
            }
        }
        if (proto.hasInternalPlayerNum()) {
            this.innerSetInternalPlayerNum(proto.getInternalPlayerNum());
        } else {
            this.innerSetInternalPlayerNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIsOpened()) {
            this.innerSetIsOpened(proto.getIsOpened());
        } else {
            this.innerSetIsOpened(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasRefreshModel()) {
            this.getRefreshModel().mergeFromDb(proto.getRefreshModel());
        } else {
            if (this.refreshModel != null) {
                this.refreshModel.mergeFromDb(proto.getRefreshModel());
            }
        }
        if (proto.hasSceneActivityInfoModel()) {
            this.getSceneActivityInfoModel().mergeFromDb(proto.getSceneActivityInfoModel());
        } else {
            if (this.sceneActivityInfoModel != null) {
                this.sceneActivityInfoModel.mergeFromDb(proto.getSceneActivityInfoModel());
            }
        }
        if (proto.hasKingdomModel()) {
            this.getKingdomModel().mergeFromDb(proto.getKingdomModel());
        } else {
            if (this.kingdomModel != null) {
                this.kingdomModel.mergeFromDb(proto.getKingdomModel());
            }
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeFromDb(proto.getDevBuffSys());
        } else {
            if (this.devBuffSys != null) {
                this.devBuffSys.mergeFromDb(proto.getDevBuffSys());
            }
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeFromDb(proto.getAdditionSys());
        } else {
            if (this.additionSys != null) {
                this.additionSys.mergeFromDb(proto.getAdditionSys());
            }
        }
        if (proto.hasRegionDriveTrafficHardwareLimit()) {
            this.getRegionDriveTrafficHardwareLimit().mergeFromDb(proto.getRegionDriveTrafficHardwareLimit());
        } else {
            if (this.regionDriveTrafficHardwareLimit != null) {
                this.regionDriveTrafficHardwareLimit.mergeFromDb(proto.getRegionDriveTrafficHardwareLimit());
            }
        }
        if (proto.hasSeasonModel()) {
            this.getSeasonModel().mergeFromDb(proto.getSeasonModel());
        } else {
            if (this.seasonModel != null) {
                this.seasonModel.mergeFromDb(proto.getSeasonModel());
            }
        }
        this.markAll();
        return ZoneInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ZoneInfoEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLanguageLimit()) {
            this.getLanguageLimit().mergeChangeFromDb(proto.getLanguageLimit());
            fieldCnt++;
        }
        if (proto.hasCurMonsterMilestone()) {
            this.setCurMonsterMilestone(proto.getCurMonsterMilestone());
            fieldCnt++;
        }
        if (proto.hasNextMilestoneStamp()) {
            this.setNextMilestoneStamp(proto.getNextMilestoneStamp());
            fieldCnt++;
        }
        if (proto.hasServerOpenTsMs()) {
            this.setServerOpenTsMs(proto.getServerOpenTsMs());
            fieldCnt++;
        }
        if (proto.hasSceneMileStoneModel()) {
            this.getSceneMileStoneModel().mergeChangeFromDb(proto.getSceneMileStoneModel());
            fieldCnt++;
        }
        if (proto.hasCrossData()) {
            this.getCrossData().mergeChangeFromDb(proto.getCrossData());
            fieldCnt++;
        }
        if (proto.hasInternalPlayerNum()) {
            this.setInternalPlayerNum(proto.getInternalPlayerNum());
            fieldCnt++;
        }
        if (proto.hasIsOpened()) {
            this.setIsOpened(proto.getIsOpened());
            fieldCnt++;
        }
        if (proto.hasRefreshModel()) {
            this.getRefreshModel().mergeChangeFromDb(proto.getRefreshModel());
            fieldCnt++;
        }
        if (proto.hasSceneActivityInfoModel()) {
            this.getSceneActivityInfoModel().mergeChangeFromDb(proto.getSceneActivityInfoModel());
            fieldCnt++;
        }
        if (proto.hasKingdomModel()) {
            this.getKingdomModel().mergeChangeFromDb(proto.getKingdomModel());
            fieldCnt++;
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeChangeFromDb(proto.getDevBuffSys());
            fieldCnt++;
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeChangeFromDb(proto.getAdditionSys());
            fieldCnt++;
        }
        if (proto.hasRegionDriveTrafficHardwareLimit()) {
            this.getRegionDriveTrafficHardwareLimit().mergeChangeFromDb(proto.getRegionDriveTrafficHardwareLimit());
            fieldCnt++;
        }
        if (proto.hasSeasonModel()) {
            this.getSeasonModel().mergeChangeFromDb(proto.getSeasonModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneInfoEntity.Builder getCopySsBuilder() {
        final ZoneInfoEntity.Builder builder = ZoneInfoEntity.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ZoneInfoEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.languageLimit != null) {
            Zone.Int32LanguageLimitItemMap.Builder tmpBuilder = Zone.Int32LanguageLimitItemMap.newBuilder();
            final int tmpFieldCnt = this.languageLimit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLanguageLimit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLanguageLimit();
            }
        }  else if (builder.hasLanguageLimit()) {
            // 清理LanguageLimit
            builder.clearLanguageLimit();
            fieldCnt++;
        }
        if (this.getCurMonsterMilestone() != 0) {
            builder.setCurMonsterMilestone(this.getCurMonsterMilestone());
            fieldCnt++;
        }  else if (builder.hasCurMonsterMilestone()) {
            // 清理CurMonsterMilestone
            builder.clearCurMonsterMilestone();
            fieldCnt++;
        }
        if (this.getNextMilestoneStamp() != 0L) {
            builder.setNextMilestoneStamp(this.getNextMilestoneStamp());
            fieldCnt++;
        }  else if (builder.hasNextMilestoneStamp()) {
            // 清理NextMilestoneStamp
            builder.clearNextMilestoneStamp();
            fieldCnt++;
        }
        if (this.getServerOpenTsMs() != 0L) {
            builder.setServerOpenTsMs(this.getServerOpenTsMs());
            fieldCnt++;
        }  else if (builder.hasServerOpenTsMs()) {
            // 清理ServerOpenTsMs
            builder.clearServerOpenTsMs();
            fieldCnt++;
        }
        if (this.sceneMileStoneModel != null) {
            Struct.SceneMileStoneModel.Builder tmpBuilder = Struct.SceneMileStoneModel.newBuilder();
            final int tmpFieldCnt = this.sceneMileStoneModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSceneMileStoneModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSceneMileStoneModel();
            }
        }  else if (builder.hasSceneMileStoneModel()) {
            // 清理SceneMileStoneModel
            builder.clearSceneMileStoneModel();
            fieldCnt++;
        }
        if (this.crossData != null) {
            StructCommon.CrossDataModel.Builder tmpBuilder = StructCommon.CrossDataModel.newBuilder();
            final int tmpFieldCnt = this.crossData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCrossData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCrossData();
            }
        }  else if (builder.hasCrossData()) {
            // 清理CrossData
            builder.clearCrossData();
            fieldCnt++;
        }
        if (this.getInternalPlayerNum() != 0) {
            builder.setInternalPlayerNum(this.getInternalPlayerNum());
            fieldCnt++;
        }  else if (builder.hasInternalPlayerNum()) {
            // 清理InternalPlayerNum
            builder.clearInternalPlayerNum();
            fieldCnt++;
        }
        if (this.getIsOpened()) {
            builder.setIsOpened(this.getIsOpened());
            fieldCnt++;
        }  else if (builder.hasIsOpened()) {
            // 清理IsOpened
            builder.clearIsOpened();
            fieldCnt++;
        }
        if (this.refreshModel != null) {
            Zone.RefreshModel.Builder tmpBuilder = Zone.RefreshModel.newBuilder();
            final int tmpFieldCnt = this.refreshModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRefreshModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRefreshModel();
            }
        }  else if (builder.hasRefreshModel()) {
            // 清理RefreshModel
            builder.clearRefreshModel();
            fieldCnt++;
        }
        if (this.sceneActivityInfoModel != null) {
            Zone.SceneActivityInfoModel.Builder tmpBuilder = Zone.SceneActivityInfoModel.newBuilder();
            final int tmpFieldCnt = this.sceneActivityInfoModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSceneActivityInfoModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSceneActivityInfoModel();
            }
        }  else if (builder.hasSceneActivityInfoModel()) {
            // 清理SceneActivityInfoModel
            builder.clearSceneActivityInfoModel();
            fieldCnt++;
        }
        if (this.kingdomModel != null) {
            Zone.KingdomModel.Builder tmpBuilder = Zone.KingdomModel.newBuilder();
            final int tmpFieldCnt = this.kingdomModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKingdomModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKingdomModel();
            }
        }  else if (builder.hasKingdomModel()) {
            // 清理KingdomModel
            builder.clearKingdomModel();
            fieldCnt++;
        }
        if (this.devBuffSys != null) {
            StructBattle.DevBuffSys.Builder tmpBuilder = StructBattle.DevBuffSys.newBuilder();
            final int tmpFieldCnt = this.devBuffSys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSys();
            }
        }  else if (builder.hasDevBuffSys()) {
            // 清理DevBuffSys
            builder.clearDevBuffSys();
            fieldCnt++;
        }
        if (this.additionSys != null) {
            Struct.AdditionSys.Builder tmpBuilder = Struct.AdditionSys.newBuilder();
            final int tmpFieldCnt = this.additionSys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionSys();
            }
        }  else if (builder.hasAdditionSys()) {
            // 清理AdditionSys
            builder.clearAdditionSys();
            fieldCnt++;
        }
        if (this.regionDriveTrafficHardwareLimit != null) {
            Zone.Int32RegionDriveTrafficHardwareLimitMap.Builder tmpBuilder = Zone.Int32RegionDriveTrafficHardwareLimitMap.newBuilder();
            final int tmpFieldCnt = this.regionDriveTrafficHardwareLimit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRegionDriveTrafficHardwareLimit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRegionDriveTrafficHardwareLimit();
            }
        }  else if (builder.hasRegionDriveTrafficHardwareLimit()) {
            // 清理RegionDriveTrafficHardwareLimit
            builder.clearRegionDriveTrafficHardwareLimit();
            fieldCnt++;
        }
        if (this.seasonModel != null) {
            Zone.ZoneSeasonModel.Builder tmpBuilder = Zone.ZoneSeasonModel.newBuilder();
            final int tmpFieldCnt = this.seasonModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSeasonModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSeasonModel();
            }
        }  else if (builder.hasSeasonModel()) {
            // 清理SeasonModel
            builder.clearSeasonModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ZoneInfoEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LANGUAGELIMIT) && this.languageLimit != null) {
            final boolean needClear = !builder.hasLanguageLimit();
            final int tmpFieldCnt = this.languageLimit.copyChangeToSs(builder.getLanguageLimitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLanguageLimit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CURMONSTERMILESTONE)) {
            builder.setCurMonsterMilestone(this.getCurMonsterMilestone());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTMILESTONESTAMP)) {
            builder.setNextMilestoneStamp(this.getNextMilestoneStamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SERVEROPENTSMS)) {
            builder.setServerOpenTsMs(this.getServerOpenTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCENEMILESTONEMODEL) && this.sceneMileStoneModel != null) {
            final boolean needClear = !builder.hasSceneMileStoneModel();
            final int tmpFieldCnt = this.sceneMileStoneModel.copyChangeToSs(builder.getSceneMileStoneModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSceneMileStoneModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_CROSSDATA) && this.crossData != null) {
            final boolean needClear = !builder.hasCrossData();
            final int tmpFieldCnt = this.crossData.copyChangeToSs(builder.getCrossDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCrossData();
            }
        }
        if (this.hasMark(FIELD_INDEX_INTERNALPLAYERNUM)) {
            builder.setInternalPlayerNum(this.getInternalPlayerNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISOPENED)) {
            builder.setIsOpened(this.getIsOpened());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REFRESHMODEL) && this.refreshModel != null) {
            final boolean needClear = !builder.hasRefreshModel();
            final int tmpFieldCnt = this.refreshModel.copyChangeToSs(builder.getRefreshModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRefreshModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCENEACTIVITYINFOMODEL) && this.sceneActivityInfoModel != null) {
            final boolean needClear = !builder.hasSceneActivityInfoModel();
            final int tmpFieldCnt = this.sceneActivityInfoModel.copyChangeToSs(builder.getSceneActivityInfoModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSceneActivityInfoModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_KINGDOMMODEL) && this.kingdomModel != null) {
            final boolean needClear = !builder.hasKingdomModel();
            final int tmpFieldCnt = this.kingdomModel.copyChangeToSs(builder.getKingdomModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingdomModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToSs(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            final boolean needClear = !builder.hasAdditionSys();
            final int tmpFieldCnt = this.additionSys.copyChangeToSs(builder.getAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_REGIONDRIVETRAFFICHARDWARELIMIT) && this.regionDriveTrafficHardwareLimit != null) {
            final boolean needClear = !builder.hasRegionDriveTrafficHardwareLimit();
            final int tmpFieldCnt = this.regionDriveTrafficHardwareLimit.copyChangeToSs(builder.getRegionDriveTrafficHardwareLimitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRegionDriveTrafficHardwareLimit();
            }
        }
        if (this.hasMark(FIELD_INDEX_SEASONMODEL) && this.seasonModel != null) {
            final boolean needClear = !builder.hasSeasonModel();
            final int tmpFieldCnt = this.seasonModel.copyChangeToSs(builder.getSeasonModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSeasonModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ZoneInfoEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLanguageLimit()) {
            this.getLanguageLimit().mergeFromSs(proto.getLanguageLimit());
        } else {
            if (this.languageLimit != null) {
                this.languageLimit.mergeFromSs(proto.getLanguageLimit());
            }
        }
        if (proto.hasCurMonsterMilestone()) {
            this.innerSetCurMonsterMilestone(proto.getCurMonsterMilestone());
        } else {
            this.innerSetCurMonsterMilestone(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNextMilestoneStamp()) {
            this.innerSetNextMilestoneStamp(proto.getNextMilestoneStamp());
        } else {
            this.innerSetNextMilestoneStamp(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasServerOpenTsMs()) {
            this.innerSetServerOpenTsMs(proto.getServerOpenTsMs());
        } else {
            this.innerSetServerOpenTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSceneMileStoneModel()) {
            this.getSceneMileStoneModel().mergeFromSs(proto.getSceneMileStoneModel());
        } else {
            if (this.sceneMileStoneModel != null) {
                this.sceneMileStoneModel.mergeFromSs(proto.getSceneMileStoneModel());
            }
        }
        if (proto.hasCrossData()) {
            this.getCrossData().mergeFromSs(proto.getCrossData());
        } else {
            if (this.crossData != null) {
                this.crossData.mergeFromSs(proto.getCrossData());
            }
        }
        if (proto.hasInternalPlayerNum()) {
            this.innerSetInternalPlayerNum(proto.getInternalPlayerNum());
        } else {
            this.innerSetInternalPlayerNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIsOpened()) {
            this.innerSetIsOpened(proto.getIsOpened());
        } else {
            this.innerSetIsOpened(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasRefreshModel()) {
            this.getRefreshModel().mergeFromSs(proto.getRefreshModel());
        } else {
            if (this.refreshModel != null) {
                this.refreshModel.mergeFromSs(proto.getRefreshModel());
            }
        }
        if (proto.hasSceneActivityInfoModel()) {
            this.getSceneActivityInfoModel().mergeFromSs(proto.getSceneActivityInfoModel());
        } else {
            if (this.sceneActivityInfoModel != null) {
                this.sceneActivityInfoModel.mergeFromSs(proto.getSceneActivityInfoModel());
            }
        }
        if (proto.hasKingdomModel()) {
            this.getKingdomModel().mergeFromSs(proto.getKingdomModel());
        } else {
            if (this.kingdomModel != null) {
                this.kingdomModel.mergeFromSs(proto.getKingdomModel());
            }
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeFromSs(proto.getDevBuffSys());
        } else {
            if (this.devBuffSys != null) {
                this.devBuffSys.mergeFromSs(proto.getDevBuffSys());
            }
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeFromSs(proto.getAdditionSys());
        } else {
            if (this.additionSys != null) {
                this.additionSys.mergeFromSs(proto.getAdditionSys());
            }
        }
        if (proto.hasRegionDriveTrafficHardwareLimit()) {
            this.getRegionDriveTrafficHardwareLimit().mergeFromSs(proto.getRegionDriveTrafficHardwareLimit());
        } else {
            if (this.regionDriveTrafficHardwareLimit != null) {
                this.regionDriveTrafficHardwareLimit.mergeFromSs(proto.getRegionDriveTrafficHardwareLimit());
            }
        }
        if (proto.hasSeasonModel()) {
            this.getSeasonModel().mergeFromSs(proto.getSeasonModel());
        } else {
            if (this.seasonModel != null) {
                this.seasonModel.mergeFromSs(proto.getSeasonModel());
            }
        }
        this.markAll();
        return ZoneInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ZoneInfoEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLanguageLimit()) {
            this.getLanguageLimit().mergeChangeFromSs(proto.getLanguageLimit());
            fieldCnt++;
        }
        if (proto.hasCurMonsterMilestone()) {
            this.setCurMonsterMilestone(proto.getCurMonsterMilestone());
            fieldCnt++;
        }
        if (proto.hasNextMilestoneStamp()) {
            this.setNextMilestoneStamp(proto.getNextMilestoneStamp());
            fieldCnt++;
        }
        if (proto.hasServerOpenTsMs()) {
            this.setServerOpenTsMs(proto.getServerOpenTsMs());
            fieldCnt++;
        }
        if (proto.hasSceneMileStoneModel()) {
            this.getSceneMileStoneModel().mergeChangeFromSs(proto.getSceneMileStoneModel());
            fieldCnt++;
        }
        if (proto.hasCrossData()) {
            this.getCrossData().mergeChangeFromSs(proto.getCrossData());
            fieldCnt++;
        }
        if (proto.hasInternalPlayerNum()) {
            this.setInternalPlayerNum(proto.getInternalPlayerNum());
            fieldCnt++;
        }
        if (proto.hasIsOpened()) {
            this.setIsOpened(proto.getIsOpened());
            fieldCnt++;
        }
        if (proto.hasRefreshModel()) {
            this.getRefreshModel().mergeChangeFromSs(proto.getRefreshModel());
            fieldCnt++;
        }
        if (proto.hasSceneActivityInfoModel()) {
            this.getSceneActivityInfoModel().mergeChangeFromSs(proto.getSceneActivityInfoModel());
            fieldCnt++;
        }
        if (proto.hasKingdomModel()) {
            this.getKingdomModel().mergeChangeFromSs(proto.getKingdomModel());
            fieldCnt++;
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeChangeFromSs(proto.getDevBuffSys());
            fieldCnt++;
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeChangeFromSs(proto.getAdditionSys());
            fieldCnt++;
        }
        if (proto.hasRegionDriveTrafficHardwareLimit()) {
            this.getRegionDriveTrafficHardwareLimit().mergeChangeFromSs(proto.getRegionDriveTrafficHardwareLimit());
            fieldCnt++;
        }
        if (proto.hasSeasonModel()) {
            this.getSeasonModel().mergeChangeFromSs(proto.getSeasonModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ZoneInfoEntity.Builder builder = ZoneInfoEntity.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_LANGUAGELIMIT) && this.languageLimit != null) {
            this.languageLimit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SCENEMILESTONEMODEL) && this.sceneMileStoneModel != null) {
            this.sceneMileStoneModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CROSSDATA) && this.crossData != null) {
            this.crossData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_REFRESHMODEL) && this.refreshModel != null) {
            this.refreshModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SCENEACTIVITYINFOMODEL) && this.sceneActivityInfoModel != null) {
            this.sceneActivityInfoModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_KINGDOMMODEL) && this.kingdomModel != null) {
            this.kingdomModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            this.devBuffSys.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            this.additionSys.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_REGIONDRIVETRAFFICHARDWARELIMIT) && this.regionDriveTrafficHardwareLimit != null) {
            this.regionDriveTrafficHardwareLimit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SEASONMODEL) && this.seasonModel != null) {
            this.seasonModel.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.languageLimit != null) {
            this.languageLimit.markAll();
        }
        if (this.sceneMileStoneModel != null) {
            this.sceneMileStoneModel.markAll();
        }
        if (this.crossData != null) {
            this.crossData.markAll();
        }
        if (this.refreshModel != null) {
            this.refreshModel.markAll();
        }
        if (this.sceneActivityInfoModel != null) {
            this.sceneActivityInfoModel.markAll();
        }
        if (this.kingdomModel != null) {
            this.kingdomModel.markAll();
        }
        if (this.devBuffSys != null) {
            this.devBuffSys.markAll();
        }
        if (this.additionSys != null) {
            this.additionSys.markAll();
        }
        if (this.regionDriveTrafficHardwareLimit != null) {
            this.regionDriveTrafficHardwareLimit.markAll();
        }
        if (this.seasonModel != null) {
            this.seasonModel.markAll();
        }
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("ZoneInfoProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("ZoneInfoProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ZoneInfoProp)) {
            return false;
        }
        final ZoneInfoProp otherNode = (ZoneInfoProp) node;
        if (!this.getLanguageLimit().compareDataTo(otherNode.getLanguageLimit())) {
            return false;
        }
        if (this.curMonsterMilestone != otherNode.curMonsterMilestone) {
            return false;
        }
        if (this.nextMilestoneStamp != otherNode.nextMilestoneStamp) {
            return false;
        }
        if (this.serverOpenTsMs != otherNode.serverOpenTsMs) {
            return false;
        }
        if (!this.getSceneMileStoneModel().compareDataTo(otherNode.getSceneMileStoneModel())) {
            return false;
        }
        if (!this.getCrossData().compareDataTo(otherNode.getCrossData())) {
            return false;
        }
        if (this.internalPlayerNum != otherNode.internalPlayerNum) {
            return false;
        }
        if (this.isOpened != otherNode.isOpened) {
            return false;
        }
        if (!this.getRefreshModel().compareDataTo(otherNode.getRefreshModel())) {
            return false;
        }
        if (!this.getSceneActivityInfoModel().compareDataTo(otherNode.getSceneActivityInfoModel())) {
            return false;
        }
        if (!this.getKingdomModel().compareDataTo(otherNode.getKingdomModel())) {
            return false;
        }
        if (!this.getDevBuffSys().compareDataTo(otherNode.getDevBuffSys())) {
            return false;
        }
        if (!this.getAdditionSys().compareDataTo(otherNode.getAdditionSys())) {
            return false;
        }
        if (!this.getRegionDriveTrafficHardwareLimit().compareDataTo(otherNode.getRegionDriveTrafficHardwareLimit())) {
            return false;
        }
        if (!this.getSeasonModel().compareDataTo(otherNode.getSeasonModel())) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    public static ZoneInfoProp of(ZoneInfoEntity fullAttrDb, ZoneInfoEntity changeAttrDb) {
        ZoneInfoProp prop = new ZoneInfoProp();
        prop.mergeFromDb(fullAttrDb);
        prop.mergeChangeFromDb(changeAttrDb);
        prop.unMarkAll();
        return prop;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 49;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
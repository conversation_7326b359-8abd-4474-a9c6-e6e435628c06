package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityContinuesGiftUnit;
import com.yorha.proto.StructPB.ActivityContinuesGiftUnitPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityContinuesGiftUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_REWARDEDLEVEL = 0;
    public static final int FIELD_INDEX_UNLOCKEDLEVEL = 1;
    public static final int FIELD_INDEX_HASDISCOUNT = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int rewardedLevel = Constant.DEFAULT_INT_VALUE;
    private int unlockedLevel = Constant.DEFAULT_INT_VALUE;
    private boolean hasDiscount = Constant.DEFAULT_BOOLEAN_VALUE;

    public ActivityContinuesGiftUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityContinuesGiftUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get rewardedLevel
     *
     * @return rewardedLevel value
     */
    public int getRewardedLevel() {
        return this.rewardedLevel;
    }

    /**
     * set rewardedLevel && set marked
     *
     * @param rewardedLevel new value
     * @return current object
     */
    public ActivityContinuesGiftUnitProp setRewardedLevel(int rewardedLevel) {
        if (this.rewardedLevel != rewardedLevel) {
            this.mark(FIELD_INDEX_REWARDEDLEVEL);
            this.rewardedLevel = rewardedLevel;
        }
        return this;
    }

    /**
     * inner set rewardedLevel
     *
     * @param rewardedLevel new value
     */
    private void innerSetRewardedLevel(int rewardedLevel) {
        this.rewardedLevel = rewardedLevel;
    }

    /**
     * get unlockedLevel
     *
     * @return unlockedLevel value
     */
    public int getUnlockedLevel() {
        return this.unlockedLevel;
    }

    /**
     * set unlockedLevel && set marked
     *
     * @param unlockedLevel new value
     * @return current object
     */
    public ActivityContinuesGiftUnitProp setUnlockedLevel(int unlockedLevel) {
        if (this.unlockedLevel != unlockedLevel) {
            this.mark(FIELD_INDEX_UNLOCKEDLEVEL);
            this.unlockedLevel = unlockedLevel;
        }
        return this;
    }

    /**
     * inner set unlockedLevel
     *
     * @param unlockedLevel new value
     */
    private void innerSetUnlockedLevel(int unlockedLevel) {
        this.unlockedLevel = unlockedLevel;
    }

    /**
     * get hasDiscount
     *
     * @return hasDiscount value
     */
    public boolean getHasDiscount() {
        return this.hasDiscount;
    }

    /**
     * set hasDiscount && set marked
     *
     * @param hasDiscount new value
     * @return current object
     */
    public ActivityContinuesGiftUnitProp setHasDiscount(boolean hasDiscount) {
        if (this.hasDiscount != hasDiscount) {
            this.mark(FIELD_INDEX_HASDISCOUNT);
            this.hasDiscount = hasDiscount;
        }
        return this;
    }

    /**
     * inner set hasDiscount
     *
     * @param hasDiscount new value
     */
    private void innerSetHasDiscount(boolean hasDiscount) {
        this.hasDiscount = hasDiscount;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityContinuesGiftUnitPB.Builder getCopyCsBuilder() {
        final ActivityContinuesGiftUnitPB.Builder builder = ActivityContinuesGiftUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityContinuesGiftUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRewardedLevel() != 0) {
            builder.setRewardedLevel(this.getRewardedLevel());
            fieldCnt++;
        }  else if (builder.hasRewardedLevel()) {
            // 清理RewardedLevel
            builder.clearRewardedLevel();
            fieldCnt++;
        }
        if (this.getUnlockedLevel() != 0) {
            builder.setUnlockedLevel(this.getUnlockedLevel());
            fieldCnt++;
        }  else if (builder.hasUnlockedLevel()) {
            // 清理UnlockedLevel
            builder.clearUnlockedLevel();
            fieldCnt++;
        }
        if (this.getHasDiscount()) {
            builder.setHasDiscount(this.getHasDiscount());
            fieldCnt++;
        }  else if (builder.hasHasDiscount()) {
            // 清理HasDiscount
            builder.clearHasDiscount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityContinuesGiftUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDEDLEVEL)) {
            builder.setRewardedLevel(this.getRewardedLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKEDLEVEL)) {
            builder.setUnlockedLevel(this.getUnlockedLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HASDISCOUNT)) {
            builder.setHasDiscount(this.getHasDiscount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityContinuesGiftUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDEDLEVEL)) {
            builder.setRewardedLevel(this.getRewardedLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKEDLEVEL)) {
            builder.setUnlockedLevel(this.getUnlockedLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HASDISCOUNT)) {
            builder.setHasDiscount(this.getHasDiscount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityContinuesGiftUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardedLevel()) {
            this.innerSetRewardedLevel(proto.getRewardedLevel());
        } else {
            this.innerSetRewardedLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnlockedLevel()) {
            this.innerSetUnlockedLevel(proto.getUnlockedLevel());
        } else {
            this.innerSetUnlockedLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHasDiscount()) {
            this.innerSetHasDiscount(proto.getHasDiscount());
        } else {
            this.innerSetHasDiscount(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ActivityContinuesGiftUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityContinuesGiftUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardedLevel()) {
            this.setRewardedLevel(proto.getRewardedLevel());
            fieldCnt++;
        }
        if (proto.hasUnlockedLevel()) {
            this.setUnlockedLevel(proto.getUnlockedLevel());
            fieldCnt++;
        }
        if (proto.hasHasDiscount()) {
            this.setHasDiscount(proto.getHasDiscount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityContinuesGiftUnit.Builder getCopyDbBuilder() {
        final ActivityContinuesGiftUnit.Builder builder = ActivityContinuesGiftUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityContinuesGiftUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRewardedLevel() != 0) {
            builder.setRewardedLevel(this.getRewardedLevel());
            fieldCnt++;
        }  else if (builder.hasRewardedLevel()) {
            // 清理RewardedLevel
            builder.clearRewardedLevel();
            fieldCnt++;
        }
        if (this.getUnlockedLevel() != 0) {
            builder.setUnlockedLevel(this.getUnlockedLevel());
            fieldCnt++;
        }  else if (builder.hasUnlockedLevel()) {
            // 清理UnlockedLevel
            builder.clearUnlockedLevel();
            fieldCnt++;
        }
        if (this.getHasDiscount()) {
            builder.setHasDiscount(this.getHasDiscount());
            fieldCnt++;
        }  else if (builder.hasHasDiscount()) {
            // 清理HasDiscount
            builder.clearHasDiscount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityContinuesGiftUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDEDLEVEL)) {
            builder.setRewardedLevel(this.getRewardedLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKEDLEVEL)) {
            builder.setUnlockedLevel(this.getUnlockedLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HASDISCOUNT)) {
            builder.setHasDiscount(this.getHasDiscount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityContinuesGiftUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardedLevel()) {
            this.innerSetRewardedLevel(proto.getRewardedLevel());
        } else {
            this.innerSetRewardedLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnlockedLevel()) {
            this.innerSetUnlockedLevel(proto.getUnlockedLevel());
        } else {
            this.innerSetUnlockedLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHasDiscount()) {
            this.innerSetHasDiscount(proto.getHasDiscount());
        } else {
            this.innerSetHasDiscount(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ActivityContinuesGiftUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityContinuesGiftUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardedLevel()) {
            this.setRewardedLevel(proto.getRewardedLevel());
            fieldCnt++;
        }
        if (proto.hasUnlockedLevel()) {
            this.setUnlockedLevel(proto.getUnlockedLevel());
            fieldCnt++;
        }
        if (proto.hasHasDiscount()) {
            this.setHasDiscount(proto.getHasDiscount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityContinuesGiftUnit.Builder getCopySsBuilder() {
        final ActivityContinuesGiftUnit.Builder builder = ActivityContinuesGiftUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityContinuesGiftUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRewardedLevel() != 0) {
            builder.setRewardedLevel(this.getRewardedLevel());
            fieldCnt++;
        }  else if (builder.hasRewardedLevel()) {
            // 清理RewardedLevel
            builder.clearRewardedLevel();
            fieldCnt++;
        }
        if (this.getUnlockedLevel() != 0) {
            builder.setUnlockedLevel(this.getUnlockedLevel());
            fieldCnt++;
        }  else if (builder.hasUnlockedLevel()) {
            // 清理UnlockedLevel
            builder.clearUnlockedLevel();
            fieldCnt++;
        }
        if (this.getHasDiscount()) {
            builder.setHasDiscount(this.getHasDiscount());
            fieldCnt++;
        }  else if (builder.hasHasDiscount()) {
            // 清理HasDiscount
            builder.clearHasDiscount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityContinuesGiftUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDEDLEVEL)) {
            builder.setRewardedLevel(this.getRewardedLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKEDLEVEL)) {
            builder.setUnlockedLevel(this.getUnlockedLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HASDISCOUNT)) {
            builder.setHasDiscount(this.getHasDiscount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityContinuesGiftUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardedLevel()) {
            this.innerSetRewardedLevel(proto.getRewardedLevel());
        } else {
            this.innerSetRewardedLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnlockedLevel()) {
            this.innerSetUnlockedLevel(proto.getUnlockedLevel());
        } else {
            this.innerSetUnlockedLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHasDiscount()) {
            this.innerSetHasDiscount(proto.getHasDiscount());
        } else {
            this.innerSetHasDiscount(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ActivityContinuesGiftUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityContinuesGiftUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardedLevel()) {
            this.setRewardedLevel(proto.getRewardedLevel());
            fieldCnt++;
        }
        if (proto.hasUnlockedLevel()) {
            this.setUnlockedLevel(proto.getUnlockedLevel());
            fieldCnt++;
        }
        if (proto.hasHasDiscount()) {
            this.setHasDiscount(proto.getHasDiscount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityContinuesGiftUnit.Builder builder = ActivityContinuesGiftUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityContinuesGiftUnitProp)) {
            return false;
        }
        final ActivityContinuesGiftUnitProp otherNode = (ActivityContinuesGiftUnitProp) node;
        if (this.rewardedLevel != otherNode.rewardedLevel) {
            return false;
        }
        if (this.unlockedLevel != otherNode.unlockedLevel) {
            return false;
        }
        if (this.hasDiscount != otherNode.hasDiscount) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
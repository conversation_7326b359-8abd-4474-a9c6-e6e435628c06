package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityZlcbUnit;
import com.yorha.proto.StructPB.ActivityZlcbUnitPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityZlcbUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_INNERRESOURCESCORE = 0;
    public static final int FIELD_INDEX_MAPRESOURCESCORE = 1;
    public static final int FIELD_INDEX_SCORE = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int innerResourceScore = Constant.DEFAULT_INT_VALUE;
    private int mapResourceScore = Constant.DEFAULT_INT_VALUE;
    private int score = Constant.DEFAULT_INT_VALUE;

    public ActivityZlcbUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityZlcbUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get innerResourceScore
     *
     * @return innerResourceScore value
     */
    public int getInnerResourceScore() {
        return this.innerResourceScore;
    }

    /**
     * set innerResourceScore && set marked
     *
     * @param innerResourceScore new value
     * @return current object
     */
    public ActivityZlcbUnitProp setInnerResourceScore(int innerResourceScore) {
        if (this.innerResourceScore != innerResourceScore) {
            this.mark(FIELD_INDEX_INNERRESOURCESCORE);
            this.innerResourceScore = innerResourceScore;
        }
        return this;
    }

    /**
     * inner set innerResourceScore
     *
     * @param innerResourceScore new value
     */
    private void innerSetInnerResourceScore(int innerResourceScore) {
        this.innerResourceScore = innerResourceScore;
    }

    /**
     * get mapResourceScore
     *
     * @return mapResourceScore value
     */
    public int getMapResourceScore() {
        return this.mapResourceScore;
    }

    /**
     * set mapResourceScore && set marked
     *
     * @param mapResourceScore new value
     * @return current object
     */
    public ActivityZlcbUnitProp setMapResourceScore(int mapResourceScore) {
        if (this.mapResourceScore != mapResourceScore) {
            this.mark(FIELD_INDEX_MAPRESOURCESCORE);
            this.mapResourceScore = mapResourceScore;
        }
        return this;
    }

    /**
     * inner set mapResourceScore
     *
     * @param mapResourceScore new value
     */
    private void innerSetMapResourceScore(int mapResourceScore) {
        this.mapResourceScore = mapResourceScore;
    }

    /**
     * get score
     *
     * @return score value
     */
    public int getScore() {
        return this.score;
    }

    /**
     * set score && set marked
     *
     * @param score new value
     * @return current object
     */
    public ActivityZlcbUnitProp setScore(int score) {
        if (this.score != score) {
            this.mark(FIELD_INDEX_SCORE);
            this.score = score;
        }
        return this;
    }

    /**
     * inner set score
     *
     * @param score new value
     */
    private void innerSetScore(int score) {
        this.score = score;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityZlcbUnitPB.Builder getCopyCsBuilder() {
        final ActivityZlcbUnitPB.Builder builder = ActivityZlcbUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityZlcbUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getInnerResourceScore() != 0) {
            builder.setInnerResourceScore(this.getInnerResourceScore());
            fieldCnt++;
        }  else if (builder.hasInnerResourceScore()) {
            // 清理InnerResourceScore
            builder.clearInnerResourceScore();
            fieldCnt++;
        }
        if (this.getMapResourceScore() != 0) {
            builder.setMapResourceScore(this.getMapResourceScore());
            fieldCnt++;
        }  else if (builder.hasMapResourceScore()) {
            // 清理MapResourceScore
            builder.clearMapResourceScore();
            fieldCnt++;
        }
        if (this.getScore() != 0) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityZlcbUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INNERRESOURCESCORE)) {
            builder.setInnerResourceScore(this.getInnerResourceScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPRESOURCESCORE)) {
            builder.setMapResourceScore(this.getMapResourceScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityZlcbUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INNERRESOURCESCORE)) {
            builder.setInnerResourceScore(this.getInnerResourceScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPRESOURCESCORE)) {
            builder.setMapResourceScore(this.getMapResourceScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityZlcbUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasInnerResourceScore()) {
            this.innerSetInnerResourceScore(proto.getInnerResourceScore());
        } else {
            this.innerSetInnerResourceScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMapResourceScore()) {
            this.innerSetMapResourceScore(proto.getMapResourceScore());
        } else {
            this.innerSetMapResourceScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivityZlcbUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityZlcbUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasInnerResourceScore()) {
            this.setInnerResourceScore(proto.getInnerResourceScore());
            fieldCnt++;
        }
        if (proto.hasMapResourceScore()) {
            this.setMapResourceScore(proto.getMapResourceScore());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityZlcbUnit.Builder getCopyDbBuilder() {
        final ActivityZlcbUnit.Builder builder = ActivityZlcbUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityZlcbUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getInnerResourceScore() != 0) {
            builder.setInnerResourceScore(this.getInnerResourceScore());
            fieldCnt++;
        }  else if (builder.hasInnerResourceScore()) {
            // 清理InnerResourceScore
            builder.clearInnerResourceScore();
            fieldCnt++;
        }
        if (this.getMapResourceScore() != 0) {
            builder.setMapResourceScore(this.getMapResourceScore());
            fieldCnt++;
        }  else if (builder.hasMapResourceScore()) {
            // 清理MapResourceScore
            builder.clearMapResourceScore();
            fieldCnt++;
        }
        if (this.getScore() != 0) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityZlcbUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INNERRESOURCESCORE)) {
            builder.setInnerResourceScore(this.getInnerResourceScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPRESOURCESCORE)) {
            builder.setMapResourceScore(this.getMapResourceScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityZlcbUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasInnerResourceScore()) {
            this.innerSetInnerResourceScore(proto.getInnerResourceScore());
        } else {
            this.innerSetInnerResourceScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMapResourceScore()) {
            this.innerSetMapResourceScore(proto.getMapResourceScore());
        } else {
            this.innerSetMapResourceScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivityZlcbUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityZlcbUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasInnerResourceScore()) {
            this.setInnerResourceScore(proto.getInnerResourceScore());
            fieldCnt++;
        }
        if (proto.hasMapResourceScore()) {
            this.setMapResourceScore(proto.getMapResourceScore());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityZlcbUnit.Builder getCopySsBuilder() {
        final ActivityZlcbUnit.Builder builder = ActivityZlcbUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityZlcbUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getInnerResourceScore() != 0) {
            builder.setInnerResourceScore(this.getInnerResourceScore());
            fieldCnt++;
        }  else if (builder.hasInnerResourceScore()) {
            // 清理InnerResourceScore
            builder.clearInnerResourceScore();
            fieldCnt++;
        }
        if (this.getMapResourceScore() != 0) {
            builder.setMapResourceScore(this.getMapResourceScore());
            fieldCnt++;
        }  else if (builder.hasMapResourceScore()) {
            // 清理MapResourceScore
            builder.clearMapResourceScore();
            fieldCnt++;
        }
        if (this.getScore() != 0) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityZlcbUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INNERRESOURCESCORE)) {
            builder.setInnerResourceScore(this.getInnerResourceScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPRESOURCESCORE)) {
            builder.setMapResourceScore(this.getMapResourceScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityZlcbUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasInnerResourceScore()) {
            this.innerSetInnerResourceScore(proto.getInnerResourceScore());
        } else {
            this.innerSetInnerResourceScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMapResourceScore()) {
            this.innerSetMapResourceScore(proto.getMapResourceScore());
        } else {
            this.innerSetMapResourceScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivityZlcbUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityZlcbUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasInnerResourceScore()) {
            this.setInnerResourceScore(proto.getInnerResourceScore());
            fieldCnt++;
        }
        if (proto.hasMapResourceScore()) {
            this.setMapResourceScore(proto.getMapResourceScore());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityZlcbUnit.Builder builder = ActivityZlcbUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityZlcbUnitProp)) {
            return false;
        }
        final ActivityZlcbUnitProp otherNode = (ActivityZlcbUnitProp) node;
        if (this.innerResourceScore != otherNode.innerResourceScore) {
            return false;
        }
        if (this.mapResourceScore != otherNode.mapResourceScore) {
            return false;
        }
        if (this.score != otherNode.score) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
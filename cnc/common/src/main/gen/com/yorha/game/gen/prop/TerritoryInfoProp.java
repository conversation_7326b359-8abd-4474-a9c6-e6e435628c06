package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Clan.TerritoryInfo;
import com.yorha.proto.Basic;
import com.yorha.proto.Clan;
import com.yorha.proto.StructClan;
import com.yorha.proto.ClanPB.TerritoryInfoPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.ClanPB;
import com.yorha.proto.StructClanPB;


/**
 * <AUTHOR> auto gen
 */
public class TerritoryInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_PARTNUM = 0;
    public static final int FIELD_INDEX_TERRITORYPOWER = 1;
    public static final int FIELD_INDEX_CLANBUILDING = 2;
    public static final int FIELD_INDEX_POWERLEVELMAX = 3;
    public static final int FIELD_INDEX_BUILDCOSTRESOURCES = 4;
    public static final int FIELD_INDEX_OWNPARTIDSET = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private int partNum = Constant.DEFAULT_INT_VALUE;
    private long territoryPower = Constant.DEFAULT_LONG_VALUE;
    private Int32ClanBuildingInfoMapProp clanBuilding = null;
    private int powerLevelMax = Constant.DEFAULT_INT_VALUE;
    private Int64ClanBuildCostResourceMapProp buildCostResources = null;
    private Int32SetProp ownPartIdSet = null;

    public TerritoryInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public TerritoryInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get partNum
     *
     * @return partNum value
     */
    public int getPartNum() {
        return this.partNum;
    }

    /**
     * set partNum && set marked
     *
     * @param partNum new value
     * @return current object
     */
    public TerritoryInfoProp setPartNum(int partNum) {
        if (this.partNum != partNum) {
            this.mark(FIELD_INDEX_PARTNUM);
            this.partNum = partNum;
        }
        return this;
    }

    /**
     * inner set partNum
     *
     * @param partNum new value
     */
    private void innerSetPartNum(int partNum) {
        this.partNum = partNum;
    }

    /**
     * get territoryPower
     *
     * @return territoryPower value
     */
    public long getTerritoryPower() {
        return this.territoryPower;
    }

    /**
     * set territoryPower && set marked
     *
     * @param territoryPower new value
     * @return current object
     */
    public TerritoryInfoProp setTerritoryPower(long territoryPower) {
        if (this.territoryPower != territoryPower) {
            this.mark(FIELD_INDEX_TERRITORYPOWER);
            this.territoryPower = territoryPower;
        }
        return this;
    }

    /**
     * inner set territoryPower
     *
     * @param territoryPower new value
     */
    private void innerSetTerritoryPower(long territoryPower) {
        this.territoryPower = territoryPower;
    }

    /**
     * get clanBuilding
     *
     * @return clanBuilding value
     */
    public Int32ClanBuildingInfoMapProp getClanBuilding() {
        if (this.clanBuilding == null) {
            this.clanBuilding = new Int32ClanBuildingInfoMapProp(this, FIELD_INDEX_CLANBUILDING);
        }
        return this.clanBuilding;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putClanBuildingV(ClanBuildingInfoProp v) {
        this.getClanBuilding().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ClanBuildingInfoProp addEmptyClanBuilding(Integer k) {
        return this.getClanBuilding().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getClanBuildingSize() {
        if (this.clanBuilding == null) {
            return 0;
        }
        return this.clanBuilding.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isClanBuildingEmpty() {
        if (this.clanBuilding == null) {
            return true;
        }
        return this.clanBuilding.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ClanBuildingInfoProp getClanBuildingV(Integer k) {
        if (this.clanBuilding == null || !this.clanBuilding.containsKey(k)) {
            return null;
        }
        return this.clanBuilding.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearClanBuilding() {
        if (this.clanBuilding != null) {
            this.clanBuilding.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeClanBuildingV(Integer k) {
        if (this.clanBuilding != null) {
            this.clanBuilding.remove(k);
        }
    }
    /**
     * get powerLevelMax
     *
     * @return powerLevelMax value
     */
    public int getPowerLevelMax() {
        return this.powerLevelMax;
    }

    /**
     * set powerLevelMax && set marked
     *
     * @param powerLevelMax new value
     * @return current object
     */
    public TerritoryInfoProp setPowerLevelMax(int powerLevelMax) {
        if (this.powerLevelMax != powerLevelMax) {
            this.mark(FIELD_INDEX_POWERLEVELMAX);
            this.powerLevelMax = powerLevelMax;
        }
        return this;
    }

    /**
     * inner set powerLevelMax
     *
     * @param powerLevelMax new value
     */
    private void innerSetPowerLevelMax(int powerLevelMax) {
        this.powerLevelMax = powerLevelMax;
    }

    /**
     * get buildCostResources
     *
     * @return buildCostResources value
     */
    public Int64ClanBuildCostResourceMapProp getBuildCostResources() {
        if (this.buildCostResources == null) {
            this.buildCostResources = new Int64ClanBuildCostResourceMapProp(this, FIELD_INDEX_BUILDCOSTRESOURCES);
        }
        return this.buildCostResources;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putBuildCostResourcesV(ClanBuildCostResourceProp v) {
        this.getBuildCostResources().put(v.getBuildingId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ClanBuildCostResourceProp addEmptyBuildCostResources(Long k) {
        return this.getBuildCostResources().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getBuildCostResourcesSize() {
        if (this.buildCostResources == null) {
            return 0;
        }
        return this.buildCostResources.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isBuildCostResourcesEmpty() {
        if (this.buildCostResources == null) {
            return true;
        }
        return this.buildCostResources.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ClanBuildCostResourceProp getBuildCostResourcesV(Long k) {
        if (this.buildCostResources == null || !this.buildCostResources.containsKey(k)) {
            return null;
        }
        return this.buildCostResources.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearBuildCostResources() {
        if (this.buildCostResources != null) {
            this.buildCostResources.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeBuildCostResourcesV(Long k) {
        if (this.buildCostResources != null) {
            this.buildCostResources.remove(k);
        }
    }
    /**
     * get ownPartIdSet
     *
     * @return ownPartIdSet value
     */
    public Int32SetProp getOwnPartIdSet() {
        if (this.ownPartIdSet == null) {
            this.ownPartIdSet = new Int32SetProp(this, FIELD_INDEX_OWNPARTIDSET);
        }
        return this.ownPartIdSet;
    }


    /**
     * add value to set
     *
     * @param e value
     */
    public void addOwnPartIdSet(Integer e) {
        this.getOwnPartIdSet().add(e);
    }

    /**
     * remove the specified element in set
     *
     * @param e element
     * @return e if success or null by fail
     */
    public Integer removeOwnPartIdSet(Integer e) {
        if (this.ownPartIdSet == null) {
            return null;
        }
        if(this.ownPartIdSet.remove(e)) {
            return e;
        }
        return null;
    }

    /**
     * get set size
     *
     * @return set size
     */
    public int getOwnPartIdSetSize() {
        if (this.ownPartIdSet == null) {
            return 0;
        }
        return this.ownPartIdSet.size();
    }

    /**
     * get is a empty set
     *
     * @return true if empty
     */
    public boolean isOwnPartIdSetEmpty() {
        if (this.ownPartIdSet == null) {
            return true;
        }
        return this.getOwnPartIdSet().isEmpty();
    }

    /**
     * clear set
     */
    public void clearOwnPartIdSet() {
        this.getOwnPartIdSet().clear();
    }

    /**
     * Returns true if this set contains the specified element.
     *
     * @param e elem
     * @return true if this set contains the specified element, false otherwise
     */
    public boolean isOwnPartIdSetContains(Integer e) {
        return this.ownPartIdSet != null && this.ownPartIdSet.contains(e);
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TerritoryInfoPB.Builder getCopyCsBuilder() {
        final TerritoryInfoPB.Builder builder = TerritoryInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(TerritoryInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPartNum() != 0) {
            builder.setPartNum(this.getPartNum());
            fieldCnt++;
        }  else if (builder.hasPartNum()) {
            // 清理PartNum
            builder.clearPartNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(TerritoryInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PARTNUM)) {
            builder.setPartNum(this.getPartNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(TerritoryInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PARTNUM)) {
            builder.setPartNum(this.getPartNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(TerritoryInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPartNum()) {
            this.innerSetPartNum(proto.getPartNum());
        } else {
            this.innerSetPartNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return TerritoryInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(TerritoryInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPartNum()) {
            this.setPartNum(proto.getPartNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TerritoryInfo.Builder getCopyDbBuilder() {
        final TerritoryInfo.Builder builder = TerritoryInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(TerritoryInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPartNum() != 0) {
            builder.setPartNum(this.getPartNum());
            fieldCnt++;
        }  else if (builder.hasPartNum()) {
            // 清理PartNum
            builder.clearPartNum();
            fieldCnt++;
        }
        if (this.getTerritoryPower() != 0L) {
            builder.setTerritoryPower(this.getTerritoryPower());
            fieldCnt++;
        }  else if (builder.hasTerritoryPower()) {
            // 清理TerritoryPower
            builder.clearTerritoryPower();
            fieldCnt++;
        }
        if (this.clanBuilding != null) {
            StructClan.Int32ClanBuildingInfoMap.Builder tmpBuilder = StructClan.Int32ClanBuildingInfoMap.newBuilder();
            final int tmpFieldCnt = this.clanBuilding.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanBuilding(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanBuilding();
            }
        }  else if (builder.hasClanBuilding()) {
            // 清理ClanBuilding
            builder.clearClanBuilding();
            fieldCnt++;
        }
        if (this.getPowerLevelMax() != 0) {
            builder.setPowerLevelMax(this.getPowerLevelMax());
            fieldCnt++;
        }  else if (builder.hasPowerLevelMax()) {
            // 清理PowerLevelMax
            builder.clearPowerLevelMax();
            fieldCnt++;
        }
        if (this.buildCostResources != null) {
            Clan.Int64ClanBuildCostResourceMap.Builder tmpBuilder = Clan.Int64ClanBuildCostResourceMap.newBuilder();
            final int tmpFieldCnt = this.buildCostResources.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuildCostResources(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuildCostResources();
            }
        }  else if (builder.hasBuildCostResources()) {
            // 清理BuildCostResources
            builder.clearBuildCostResources();
            fieldCnt++;
        }
        if (this.ownPartIdSet != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.ownPartIdSet.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOwnPartIdSet(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOwnPartIdSet();
            }
        }  else if (builder.hasOwnPartIdSet()) {
            // 清理OwnPartIdSet
            builder.clearOwnPartIdSet();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(TerritoryInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PARTNUM)) {
            builder.setPartNum(this.getPartNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TERRITORYPOWER)) {
            builder.setTerritoryPower(this.getTerritoryPower());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANBUILDING) && this.clanBuilding != null) {
            final boolean needClear = !builder.hasClanBuilding();
            final int tmpFieldCnt = this.clanBuilding.copyChangeToDb(builder.getClanBuildingBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanBuilding();
            }
        }
        if (this.hasMark(FIELD_INDEX_POWERLEVELMAX)) {
            builder.setPowerLevelMax(this.getPowerLevelMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDCOSTRESOURCES) && this.buildCostResources != null) {
            final boolean needClear = !builder.hasBuildCostResources();
            final int tmpFieldCnt = this.buildCostResources.copyChangeToDb(builder.getBuildCostResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuildCostResources();
            }
        }
        if (this.hasMark(FIELD_INDEX_OWNPARTIDSET) && this.ownPartIdSet != null) {
            final boolean needClear = !builder.hasOwnPartIdSet();
            final int tmpFieldCnt = this.ownPartIdSet.copyChangeToDb(builder.getOwnPartIdSetBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOwnPartIdSet();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(TerritoryInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPartNum()) {
            this.innerSetPartNum(proto.getPartNum());
        } else {
            this.innerSetPartNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTerritoryPower()) {
            this.innerSetTerritoryPower(proto.getTerritoryPower());
        } else {
            this.innerSetTerritoryPower(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanBuilding()) {
            this.getClanBuilding().mergeFromDb(proto.getClanBuilding());
        } else {
            if (this.clanBuilding != null) {
                this.clanBuilding.mergeFromDb(proto.getClanBuilding());
            }
        }
        if (proto.hasPowerLevelMax()) {
            this.innerSetPowerLevelMax(proto.getPowerLevelMax());
        } else {
            this.innerSetPowerLevelMax(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuildCostResources()) {
            this.getBuildCostResources().mergeFromDb(proto.getBuildCostResources());
        } else {
            if (this.buildCostResources != null) {
                this.buildCostResources.mergeFromDb(proto.getBuildCostResources());
            }
        }
        if (proto.hasOwnPartIdSet()) {
            this.getOwnPartIdSet().mergeFromDb(proto.getOwnPartIdSet());
        } else {
            if (this.ownPartIdSet != null) {
                this.ownPartIdSet.mergeFromDb(proto.getOwnPartIdSet());
            }
        }
        this.markAll();
        return TerritoryInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(TerritoryInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPartNum()) {
            this.setPartNum(proto.getPartNum());
            fieldCnt++;
        }
        if (proto.hasTerritoryPower()) {
            this.setTerritoryPower(proto.getTerritoryPower());
            fieldCnt++;
        }
        if (proto.hasClanBuilding()) {
            this.getClanBuilding().mergeChangeFromDb(proto.getClanBuilding());
            fieldCnt++;
        }
        if (proto.hasPowerLevelMax()) {
            this.setPowerLevelMax(proto.getPowerLevelMax());
            fieldCnt++;
        }
        if (proto.hasBuildCostResources()) {
            this.getBuildCostResources().mergeChangeFromDb(proto.getBuildCostResources());
            fieldCnt++;
        }
        if (proto.hasOwnPartIdSet()) {
            this.getOwnPartIdSet().mergeChangeFromDb(proto.getOwnPartIdSet());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TerritoryInfo.Builder getCopySsBuilder() {
        final TerritoryInfo.Builder builder = TerritoryInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(TerritoryInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPartNum() != 0) {
            builder.setPartNum(this.getPartNum());
            fieldCnt++;
        }  else if (builder.hasPartNum()) {
            // 清理PartNum
            builder.clearPartNum();
            fieldCnt++;
        }
        if (this.getTerritoryPower() != 0L) {
            builder.setTerritoryPower(this.getTerritoryPower());
            fieldCnt++;
        }  else if (builder.hasTerritoryPower()) {
            // 清理TerritoryPower
            builder.clearTerritoryPower();
            fieldCnt++;
        }
        if (this.clanBuilding != null) {
            StructClan.Int32ClanBuildingInfoMap.Builder tmpBuilder = StructClan.Int32ClanBuildingInfoMap.newBuilder();
            final int tmpFieldCnt = this.clanBuilding.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanBuilding(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanBuilding();
            }
        }  else if (builder.hasClanBuilding()) {
            // 清理ClanBuilding
            builder.clearClanBuilding();
            fieldCnt++;
        }
        if (this.getPowerLevelMax() != 0) {
            builder.setPowerLevelMax(this.getPowerLevelMax());
            fieldCnt++;
        }  else if (builder.hasPowerLevelMax()) {
            // 清理PowerLevelMax
            builder.clearPowerLevelMax();
            fieldCnt++;
        }
        if (this.buildCostResources != null) {
            Clan.Int64ClanBuildCostResourceMap.Builder tmpBuilder = Clan.Int64ClanBuildCostResourceMap.newBuilder();
            final int tmpFieldCnt = this.buildCostResources.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuildCostResources(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuildCostResources();
            }
        }  else if (builder.hasBuildCostResources()) {
            // 清理BuildCostResources
            builder.clearBuildCostResources();
            fieldCnt++;
        }
        if (this.ownPartIdSet != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.ownPartIdSet.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOwnPartIdSet(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOwnPartIdSet();
            }
        }  else if (builder.hasOwnPartIdSet()) {
            // 清理OwnPartIdSet
            builder.clearOwnPartIdSet();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(TerritoryInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PARTNUM)) {
            builder.setPartNum(this.getPartNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TERRITORYPOWER)) {
            builder.setTerritoryPower(this.getTerritoryPower());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANBUILDING) && this.clanBuilding != null) {
            final boolean needClear = !builder.hasClanBuilding();
            final int tmpFieldCnt = this.clanBuilding.copyChangeToSs(builder.getClanBuildingBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanBuilding();
            }
        }
        if (this.hasMark(FIELD_INDEX_POWERLEVELMAX)) {
            builder.setPowerLevelMax(this.getPowerLevelMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUILDCOSTRESOURCES) && this.buildCostResources != null) {
            final boolean needClear = !builder.hasBuildCostResources();
            final int tmpFieldCnt = this.buildCostResources.copyChangeToSs(builder.getBuildCostResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuildCostResources();
            }
        }
        if (this.hasMark(FIELD_INDEX_OWNPARTIDSET) && this.ownPartIdSet != null) {
            final boolean needClear = !builder.hasOwnPartIdSet();
            final int tmpFieldCnt = this.ownPartIdSet.copyChangeToSs(builder.getOwnPartIdSetBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOwnPartIdSet();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(TerritoryInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPartNum()) {
            this.innerSetPartNum(proto.getPartNum());
        } else {
            this.innerSetPartNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTerritoryPower()) {
            this.innerSetTerritoryPower(proto.getTerritoryPower());
        } else {
            this.innerSetTerritoryPower(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanBuilding()) {
            this.getClanBuilding().mergeFromSs(proto.getClanBuilding());
        } else {
            if (this.clanBuilding != null) {
                this.clanBuilding.mergeFromSs(proto.getClanBuilding());
            }
        }
        if (proto.hasPowerLevelMax()) {
            this.innerSetPowerLevelMax(proto.getPowerLevelMax());
        } else {
            this.innerSetPowerLevelMax(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuildCostResources()) {
            this.getBuildCostResources().mergeFromSs(proto.getBuildCostResources());
        } else {
            if (this.buildCostResources != null) {
                this.buildCostResources.mergeFromSs(proto.getBuildCostResources());
            }
        }
        if (proto.hasOwnPartIdSet()) {
            this.getOwnPartIdSet().mergeFromSs(proto.getOwnPartIdSet());
        } else {
            if (this.ownPartIdSet != null) {
                this.ownPartIdSet.mergeFromSs(proto.getOwnPartIdSet());
            }
        }
        this.markAll();
        return TerritoryInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(TerritoryInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPartNum()) {
            this.setPartNum(proto.getPartNum());
            fieldCnt++;
        }
        if (proto.hasTerritoryPower()) {
            this.setTerritoryPower(proto.getTerritoryPower());
            fieldCnt++;
        }
        if (proto.hasClanBuilding()) {
            this.getClanBuilding().mergeChangeFromSs(proto.getClanBuilding());
            fieldCnt++;
        }
        if (proto.hasPowerLevelMax()) {
            this.setPowerLevelMax(proto.getPowerLevelMax());
            fieldCnt++;
        }
        if (proto.hasBuildCostResources()) {
            this.getBuildCostResources().mergeChangeFromSs(proto.getBuildCostResources());
            fieldCnt++;
        }
        if (proto.hasOwnPartIdSet()) {
            this.getOwnPartIdSet().mergeChangeFromSs(proto.getOwnPartIdSet());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        TerritoryInfo.Builder builder = TerritoryInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CLANBUILDING) && this.clanBuilding != null) {
            this.clanBuilding.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BUILDCOSTRESOURCES) && this.buildCostResources != null) {
            this.buildCostResources.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_OWNPARTIDSET) && this.ownPartIdSet != null) {
            this.ownPartIdSet.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.clanBuilding != null) {
            this.clanBuilding.markAll();
        }
        if (this.buildCostResources != null) {
            this.buildCostResources.markAll();
        }
        if (this.ownPartIdSet != null) {
            this.ownPartIdSet.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof TerritoryInfoProp)) {
            return false;
        }
        final TerritoryInfoProp otherNode = (TerritoryInfoProp) node;
        if (this.partNum != otherNode.partNum) {
            return false;
        }
        if (this.territoryPower != otherNode.territoryPower) {
            return false;
        }
        if (!this.getClanBuilding().compareDataTo(otherNode.getClanBuilding())) {
            return false;
        }
        if (this.powerLevelMax != otherNode.powerLevelMax) {
            return false;
        }
        if (!this.getBuildCostResources().compareDataTo(otherNode.getBuildCostResources())) {
            return false;
        }
        if (!this.getOwnPartIdSet().compareDataTo(otherNode.getOwnPartIdSet())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
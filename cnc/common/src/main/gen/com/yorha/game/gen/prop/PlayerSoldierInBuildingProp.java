package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.PlayerSoldierInBuilding;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.PlayerSoldierInBuildingPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerSoldierInBuildingProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TRAININGSOLDIERS = 0;
    public static final int FIELD_INDEX_TRAININGOVERSOLDIERS = 1;
    public static final int FIELD_INDEX_LEVELUPPINGSOLDIERS = 2;
    public static final int FIELD_INDEX_FIRSTTRAINTIMEUSED = 3;
    public static final int FIELD_INDEX_UPTRAINANDLEVELUPLIMIT = 4;
    public static final int FIELD_INDEX_UPLEFTTIMES = 5;
    public static final int FIELD_INDEX_UPTRAINS = 6;

    public static final int FIELD_COUNT = 7;

    private long markBits0 = 0L;

    private Int32PlayerSoldierUnitMapProp trainingSoldiers = null;
    private Int32PlayerSoldierUnitMapProp trainingOverSoldiers = null;
    private Int32PlayerSoldierLevelUpUnitMapProp levelUppingSoldiers = null;
    private boolean firstTrainTimeUsed = Constant.DEFAULT_BOOLEAN_VALUE;
    private int upTrainAndLevelUpLimit = Constant.DEFAULT_INT_VALUE;
    private int upleftTimes = Constant.DEFAULT_INT_VALUE;
    private Int32UpTrainInfoMapProp upTrains = null;

    public PlayerSoldierInBuildingProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerSoldierInBuildingProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get trainingSoldiers
     *
     * @return trainingSoldiers value
     */
    public Int32PlayerSoldierUnitMapProp getTrainingSoldiers() {
        if (this.trainingSoldiers == null) {
            this.trainingSoldiers = new Int32PlayerSoldierUnitMapProp(this, FIELD_INDEX_TRAININGSOLDIERS);
        }
        return this.trainingSoldiers;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTrainingSoldiersV(PlayerSoldierUnitProp v) {
        this.getTrainingSoldiers().put(v.getSoldierId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerSoldierUnitProp addEmptyTrainingSoldiers(Integer k) {
        return this.getTrainingSoldiers().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTrainingSoldiersSize() {
        if (this.trainingSoldiers == null) {
            return 0;
        }
        return this.trainingSoldiers.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTrainingSoldiersEmpty() {
        if (this.trainingSoldiers == null) {
            return true;
        }
        return this.trainingSoldiers.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerSoldierUnitProp getTrainingSoldiersV(Integer k) {
        if (this.trainingSoldiers == null || !this.trainingSoldiers.containsKey(k)) {
            return null;
        }
        return this.trainingSoldiers.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTrainingSoldiers() {
        if (this.trainingSoldiers != null) {
            this.trainingSoldiers.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTrainingSoldiersV(Integer k) {
        if (this.trainingSoldiers != null) {
            this.trainingSoldiers.remove(k);
        }
    }
    /**
     * get trainingOverSoldiers
     *
     * @return trainingOverSoldiers value
     */
    public Int32PlayerSoldierUnitMapProp getTrainingOverSoldiers() {
        if (this.trainingOverSoldiers == null) {
            this.trainingOverSoldiers = new Int32PlayerSoldierUnitMapProp(this, FIELD_INDEX_TRAININGOVERSOLDIERS);
        }
        return this.trainingOverSoldiers;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTrainingOverSoldiersV(PlayerSoldierUnitProp v) {
        this.getTrainingOverSoldiers().put(v.getSoldierId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerSoldierUnitProp addEmptyTrainingOverSoldiers(Integer k) {
        return this.getTrainingOverSoldiers().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTrainingOverSoldiersSize() {
        if (this.trainingOverSoldiers == null) {
            return 0;
        }
        return this.trainingOverSoldiers.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTrainingOverSoldiersEmpty() {
        if (this.trainingOverSoldiers == null) {
            return true;
        }
        return this.trainingOverSoldiers.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerSoldierUnitProp getTrainingOverSoldiersV(Integer k) {
        if (this.trainingOverSoldiers == null || !this.trainingOverSoldiers.containsKey(k)) {
            return null;
        }
        return this.trainingOverSoldiers.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTrainingOverSoldiers() {
        if (this.trainingOverSoldiers != null) {
            this.trainingOverSoldiers.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTrainingOverSoldiersV(Integer k) {
        if (this.trainingOverSoldiers != null) {
            this.trainingOverSoldiers.remove(k);
        }
    }
    /**
     * get levelUppingSoldiers
     *
     * @return levelUppingSoldiers value
     */
    public Int32PlayerSoldierLevelUpUnitMapProp getLevelUppingSoldiers() {
        if (this.levelUppingSoldiers == null) {
            this.levelUppingSoldiers = new Int32PlayerSoldierLevelUpUnitMapProp(this, FIELD_INDEX_LEVELUPPINGSOLDIERS);
        }
        return this.levelUppingSoldiers;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putLevelUppingSoldiersV(PlayerSoldierLevelUpUnitProp v) {
        this.getLevelUppingSoldiers().put(v.getToSoldierId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerSoldierLevelUpUnitProp addEmptyLevelUppingSoldiers(Integer k) {
        return this.getLevelUppingSoldiers().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getLevelUppingSoldiersSize() {
        if (this.levelUppingSoldiers == null) {
            return 0;
        }
        return this.levelUppingSoldiers.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isLevelUppingSoldiersEmpty() {
        if (this.levelUppingSoldiers == null) {
            return true;
        }
        return this.levelUppingSoldiers.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerSoldierLevelUpUnitProp getLevelUppingSoldiersV(Integer k) {
        if (this.levelUppingSoldiers == null || !this.levelUppingSoldiers.containsKey(k)) {
            return null;
        }
        return this.levelUppingSoldiers.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearLevelUppingSoldiers() {
        if (this.levelUppingSoldiers != null) {
            this.levelUppingSoldiers.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeLevelUppingSoldiersV(Integer k) {
        if (this.levelUppingSoldiers != null) {
            this.levelUppingSoldiers.remove(k);
        }
    }
    /**
     * get firstTrainTimeUsed
     *
     * @return firstTrainTimeUsed value
     */
    public boolean getFirstTrainTimeUsed() {
        return this.firstTrainTimeUsed;
    }

    /**
     * set firstTrainTimeUsed && set marked
     *
     * @param firstTrainTimeUsed new value
     * @return current object
     */
    public PlayerSoldierInBuildingProp setFirstTrainTimeUsed(boolean firstTrainTimeUsed) {
        if (this.firstTrainTimeUsed != firstTrainTimeUsed) {
            this.mark(FIELD_INDEX_FIRSTTRAINTIMEUSED);
            this.firstTrainTimeUsed = firstTrainTimeUsed;
        }
        return this;
    }

    /**
     * inner set firstTrainTimeUsed
     *
     * @param firstTrainTimeUsed new value
     */
    private void innerSetFirstTrainTimeUsed(boolean firstTrainTimeUsed) {
        this.firstTrainTimeUsed = firstTrainTimeUsed;
    }

    /**
     * get upTrainAndLevelUpLimit
     *
     * @return upTrainAndLevelUpLimit value
     */
    public int getUpTrainAndLevelUpLimit() {
        return this.upTrainAndLevelUpLimit;
    }

    /**
     * set upTrainAndLevelUpLimit && set marked
     *
     * @param upTrainAndLevelUpLimit new value
     * @return current object
     */
    public PlayerSoldierInBuildingProp setUpTrainAndLevelUpLimit(int upTrainAndLevelUpLimit) {
        if (this.upTrainAndLevelUpLimit != upTrainAndLevelUpLimit) {
            this.mark(FIELD_INDEX_UPTRAINANDLEVELUPLIMIT);
            this.upTrainAndLevelUpLimit = upTrainAndLevelUpLimit;
        }
        return this;
    }

    /**
     * inner set upTrainAndLevelUpLimit
     *
     * @param upTrainAndLevelUpLimit new value
     */
    private void innerSetUpTrainAndLevelUpLimit(int upTrainAndLevelUpLimit) {
        this.upTrainAndLevelUpLimit = upTrainAndLevelUpLimit;
    }

    /**
     * get upleftTimes
     *
     * @return upleftTimes value
     */
    public int getUpleftTimes() {
        return this.upleftTimes;
    }

    /**
     * set upleftTimes && set marked
     *
     * @param upleftTimes new value
     * @return current object
     */
    public PlayerSoldierInBuildingProp setUpleftTimes(int upleftTimes) {
        if (this.upleftTimes != upleftTimes) {
            this.mark(FIELD_INDEX_UPLEFTTIMES);
            this.upleftTimes = upleftTimes;
        }
        return this;
    }

    /**
     * inner set upleftTimes
     *
     * @param upleftTimes new value
     */
    private void innerSetUpleftTimes(int upleftTimes) {
        this.upleftTimes = upleftTimes;
    }

    /**
     * get upTrains
     *
     * @return upTrains value
     */
    public Int32UpTrainInfoMapProp getUpTrains() {
        if (this.upTrains == null) {
            this.upTrains = new Int32UpTrainInfoMapProp(this, FIELD_INDEX_UPTRAINS);
        }
        return this.upTrains;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putUpTrainsV(UpTrainInfoProp v) {
        this.getUpTrains().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public UpTrainInfoProp addEmptyUpTrains(Integer k) {
        return this.getUpTrains().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getUpTrainsSize() {
        if (this.upTrains == null) {
            return 0;
        }
        return this.upTrains.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isUpTrainsEmpty() {
        if (this.upTrains == null) {
            return true;
        }
        return this.upTrains.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public UpTrainInfoProp getUpTrainsV(Integer k) {
        if (this.upTrains == null || !this.upTrains.containsKey(k)) {
            return null;
        }
        return this.upTrains.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearUpTrains() {
        if (this.upTrains != null) {
            this.upTrains.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeUpTrainsV(Integer k) {
        if (this.upTrains != null) {
            this.upTrains.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerSoldierInBuildingPB.Builder getCopyCsBuilder() {
        final PlayerSoldierInBuildingPB.Builder builder = PlayerSoldierInBuildingPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerSoldierInBuildingPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.trainingSoldiers != null) {
            StructPB.Int32PlayerSoldierUnitMapPB.Builder tmpBuilder = StructPB.Int32PlayerSoldierUnitMapPB.newBuilder();
            final int tmpFieldCnt = this.trainingSoldiers.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTrainingSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTrainingSoldiers();
            }
        }  else if (builder.hasTrainingSoldiers()) {
            // 清理TrainingSoldiers
            builder.clearTrainingSoldiers();
            fieldCnt++;
        }
        if (this.trainingOverSoldiers != null) {
            StructPB.Int32PlayerSoldierUnitMapPB.Builder tmpBuilder = StructPB.Int32PlayerSoldierUnitMapPB.newBuilder();
            final int tmpFieldCnt = this.trainingOverSoldiers.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTrainingOverSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTrainingOverSoldiers();
            }
        }  else if (builder.hasTrainingOverSoldiers()) {
            // 清理TrainingOverSoldiers
            builder.clearTrainingOverSoldiers();
            fieldCnt++;
        }
        if (this.levelUppingSoldiers != null) {
            StructPB.Int32PlayerSoldierLevelUpUnitMapPB.Builder tmpBuilder = StructPB.Int32PlayerSoldierLevelUpUnitMapPB.newBuilder();
            final int tmpFieldCnt = this.levelUppingSoldiers.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLevelUppingSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLevelUppingSoldiers();
            }
        }  else if (builder.hasLevelUppingSoldiers()) {
            // 清理LevelUppingSoldiers
            builder.clearLevelUppingSoldiers();
            fieldCnt++;
        }
        if (this.getFirstTrainTimeUsed()) {
            builder.setFirstTrainTimeUsed(this.getFirstTrainTimeUsed());
            fieldCnt++;
        }  else if (builder.hasFirstTrainTimeUsed()) {
            // 清理FirstTrainTimeUsed
            builder.clearFirstTrainTimeUsed();
            fieldCnt++;
        }
        if (this.getUpTrainAndLevelUpLimit() != 0) {
            builder.setUpTrainAndLevelUpLimit(this.getUpTrainAndLevelUpLimit());
            fieldCnt++;
        }  else if (builder.hasUpTrainAndLevelUpLimit()) {
            // 清理UpTrainAndLevelUpLimit
            builder.clearUpTrainAndLevelUpLimit();
            fieldCnt++;
        }
        if (this.getUpleftTimes() != 0) {
            builder.setUpleftTimes(this.getUpleftTimes());
            fieldCnt++;
        }  else if (builder.hasUpleftTimes()) {
            // 清理UpleftTimes
            builder.clearUpleftTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerSoldierInBuildingPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TRAININGSOLDIERS) && this.trainingSoldiers != null) {
            final boolean needClear = !builder.hasTrainingSoldiers();
            final int tmpFieldCnt = this.trainingSoldiers.copyChangeToCs(builder.getTrainingSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTrainingSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_TRAININGOVERSOLDIERS) && this.trainingOverSoldiers != null) {
            final boolean needClear = !builder.hasTrainingOverSoldiers();
            final int tmpFieldCnt = this.trainingOverSoldiers.copyChangeToCs(builder.getTrainingOverSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTrainingOverSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_LEVELUPPINGSOLDIERS) && this.levelUppingSoldiers != null) {
            final boolean needClear = !builder.hasLevelUppingSoldiers();
            final int tmpFieldCnt = this.levelUppingSoldiers.copyChangeToCs(builder.getLevelUppingSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLevelUppingSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_FIRSTTRAINTIMEUSED)) {
            builder.setFirstTrainTimeUsed(this.getFirstTrainTimeUsed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UPTRAINANDLEVELUPLIMIT)) {
            builder.setUpTrainAndLevelUpLimit(this.getUpTrainAndLevelUpLimit());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UPLEFTTIMES)) {
            builder.setUpleftTimes(this.getUpleftTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerSoldierInBuildingPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TRAININGSOLDIERS) && this.trainingSoldiers != null) {
            final boolean needClear = !builder.hasTrainingSoldiers();
            final int tmpFieldCnt = this.trainingSoldiers.copyChangeToAndClearDeleteKeysCs(builder.getTrainingSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTrainingSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_TRAININGOVERSOLDIERS) && this.trainingOverSoldiers != null) {
            final boolean needClear = !builder.hasTrainingOverSoldiers();
            final int tmpFieldCnt = this.trainingOverSoldiers.copyChangeToAndClearDeleteKeysCs(builder.getTrainingOverSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTrainingOverSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_LEVELUPPINGSOLDIERS) && this.levelUppingSoldiers != null) {
            final boolean needClear = !builder.hasLevelUppingSoldiers();
            final int tmpFieldCnt = this.levelUppingSoldiers.copyChangeToAndClearDeleteKeysCs(builder.getLevelUppingSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLevelUppingSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_FIRSTTRAINTIMEUSED)) {
            builder.setFirstTrainTimeUsed(this.getFirstTrainTimeUsed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UPTRAINANDLEVELUPLIMIT)) {
            builder.setUpTrainAndLevelUpLimit(this.getUpTrainAndLevelUpLimit());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UPLEFTTIMES)) {
            builder.setUpleftTimes(this.getUpleftTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerSoldierInBuildingPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTrainingSoldiers()) {
            this.getTrainingSoldiers().mergeFromCs(proto.getTrainingSoldiers());
        } else {
            if (this.trainingSoldiers != null) {
                this.trainingSoldiers.mergeFromCs(proto.getTrainingSoldiers());
            }
        }
        if (proto.hasTrainingOverSoldiers()) {
            this.getTrainingOverSoldiers().mergeFromCs(proto.getTrainingOverSoldiers());
        } else {
            if (this.trainingOverSoldiers != null) {
                this.trainingOverSoldiers.mergeFromCs(proto.getTrainingOverSoldiers());
            }
        }
        if (proto.hasLevelUppingSoldiers()) {
            this.getLevelUppingSoldiers().mergeFromCs(proto.getLevelUppingSoldiers());
        } else {
            if (this.levelUppingSoldiers != null) {
                this.levelUppingSoldiers.mergeFromCs(proto.getLevelUppingSoldiers());
            }
        }
        if (proto.hasFirstTrainTimeUsed()) {
            this.innerSetFirstTrainTimeUsed(proto.getFirstTrainTimeUsed());
        } else {
            this.innerSetFirstTrainTimeUsed(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUpTrainAndLevelUpLimit()) {
            this.innerSetUpTrainAndLevelUpLimit(proto.getUpTrainAndLevelUpLimit());
        } else {
            this.innerSetUpTrainAndLevelUpLimit(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUpleftTimes()) {
            this.innerSetUpleftTimes(proto.getUpleftTimes());
        } else {
            this.innerSetUpleftTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerSoldierInBuildingProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerSoldierInBuildingPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTrainingSoldiers()) {
            this.getTrainingSoldiers().mergeChangeFromCs(proto.getTrainingSoldiers());
            fieldCnt++;
        }
        if (proto.hasTrainingOverSoldiers()) {
            this.getTrainingOverSoldiers().mergeChangeFromCs(proto.getTrainingOverSoldiers());
            fieldCnt++;
        }
        if (proto.hasLevelUppingSoldiers()) {
            this.getLevelUppingSoldiers().mergeChangeFromCs(proto.getLevelUppingSoldiers());
            fieldCnt++;
        }
        if (proto.hasFirstTrainTimeUsed()) {
            this.setFirstTrainTimeUsed(proto.getFirstTrainTimeUsed());
            fieldCnt++;
        }
        if (proto.hasUpTrainAndLevelUpLimit()) {
            this.setUpTrainAndLevelUpLimit(proto.getUpTrainAndLevelUpLimit());
            fieldCnt++;
        }
        if (proto.hasUpleftTimes()) {
            this.setUpleftTimes(proto.getUpleftTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerSoldierInBuilding.Builder getCopyDbBuilder() {
        final PlayerSoldierInBuilding.Builder builder = PlayerSoldierInBuilding.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerSoldierInBuilding.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.trainingSoldiers != null) {
            Struct.Int32PlayerSoldierUnitMap.Builder tmpBuilder = Struct.Int32PlayerSoldierUnitMap.newBuilder();
            final int tmpFieldCnt = this.trainingSoldiers.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTrainingSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTrainingSoldiers();
            }
        }  else if (builder.hasTrainingSoldiers()) {
            // 清理TrainingSoldiers
            builder.clearTrainingSoldiers();
            fieldCnt++;
        }
        if (this.trainingOverSoldiers != null) {
            Struct.Int32PlayerSoldierUnitMap.Builder tmpBuilder = Struct.Int32PlayerSoldierUnitMap.newBuilder();
            final int tmpFieldCnt = this.trainingOverSoldiers.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTrainingOverSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTrainingOverSoldiers();
            }
        }  else if (builder.hasTrainingOverSoldiers()) {
            // 清理TrainingOverSoldiers
            builder.clearTrainingOverSoldiers();
            fieldCnt++;
        }
        if (this.levelUppingSoldiers != null) {
            Struct.Int32PlayerSoldierLevelUpUnitMap.Builder tmpBuilder = Struct.Int32PlayerSoldierLevelUpUnitMap.newBuilder();
            final int tmpFieldCnt = this.levelUppingSoldiers.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLevelUppingSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLevelUppingSoldiers();
            }
        }  else if (builder.hasLevelUppingSoldiers()) {
            // 清理LevelUppingSoldiers
            builder.clearLevelUppingSoldiers();
            fieldCnt++;
        }
        if (this.getFirstTrainTimeUsed()) {
            builder.setFirstTrainTimeUsed(this.getFirstTrainTimeUsed());
            fieldCnt++;
        }  else if (builder.hasFirstTrainTimeUsed()) {
            // 清理FirstTrainTimeUsed
            builder.clearFirstTrainTimeUsed();
            fieldCnt++;
        }
        if (this.getUpTrainAndLevelUpLimit() != 0) {
            builder.setUpTrainAndLevelUpLimit(this.getUpTrainAndLevelUpLimit());
            fieldCnt++;
        }  else if (builder.hasUpTrainAndLevelUpLimit()) {
            // 清理UpTrainAndLevelUpLimit
            builder.clearUpTrainAndLevelUpLimit();
            fieldCnt++;
        }
        if (this.getUpleftTimes() != 0) {
            builder.setUpleftTimes(this.getUpleftTimes());
            fieldCnt++;
        }  else if (builder.hasUpleftTimes()) {
            // 清理UpleftTimes
            builder.clearUpleftTimes();
            fieldCnt++;
        }
        if (this.upTrains != null) {
            Struct.Int32UpTrainInfoMap.Builder tmpBuilder = Struct.Int32UpTrainInfoMap.newBuilder();
            final int tmpFieldCnt = this.upTrains.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUpTrains(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUpTrains();
            }
        }  else if (builder.hasUpTrains()) {
            // 清理UpTrains
            builder.clearUpTrains();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerSoldierInBuilding.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TRAININGSOLDIERS) && this.trainingSoldiers != null) {
            final boolean needClear = !builder.hasTrainingSoldiers();
            final int tmpFieldCnt = this.trainingSoldiers.copyChangeToDb(builder.getTrainingSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTrainingSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_TRAININGOVERSOLDIERS) && this.trainingOverSoldiers != null) {
            final boolean needClear = !builder.hasTrainingOverSoldiers();
            final int tmpFieldCnt = this.trainingOverSoldiers.copyChangeToDb(builder.getTrainingOverSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTrainingOverSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_LEVELUPPINGSOLDIERS) && this.levelUppingSoldiers != null) {
            final boolean needClear = !builder.hasLevelUppingSoldiers();
            final int tmpFieldCnt = this.levelUppingSoldiers.copyChangeToDb(builder.getLevelUppingSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLevelUppingSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_FIRSTTRAINTIMEUSED)) {
            builder.setFirstTrainTimeUsed(this.getFirstTrainTimeUsed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UPTRAINANDLEVELUPLIMIT)) {
            builder.setUpTrainAndLevelUpLimit(this.getUpTrainAndLevelUpLimit());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UPLEFTTIMES)) {
            builder.setUpleftTimes(this.getUpleftTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UPTRAINS) && this.upTrains != null) {
            final boolean needClear = !builder.hasUpTrains();
            final int tmpFieldCnt = this.upTrains.copyChangeToDb(builder.getUpTrainsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUpTrains();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerSoldierInBuilding proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTrainingSoldiers()) {
            this.getTrainingSoldiers().mergeFromDb(proto.getTrainingSoldiers());
        } else {
            if (this.trainingSoldiers != null) {
                this.trainingSoldiers.mergeFromDb(proto.getTrainingSoldiers());
            }
        }
        if (proto.hasTrainingOverSoldiers()) {
            this.getTrainingOverSoldiers().mergeFromDb(proto.getTrainingOverSoldiers());
        } else {
            if (this.trainingOverSoldiers != null) {
                this.trainingOverSoldiers.mergeFromDb(proto.getTrainingOverSoldiers());
            }
        }
        if (proto.hasLevelUppingSoldiers()) {
            this.getLevelUppingSoldiers().mergeFromDb(proto.getLevelUppingSoldiers());
        } else {
            if (this.levelUppingSoldiers != null) {
                this.levelUppingSoldiers.mergeFromDb(proto.getLevelUppingSoldiers());
            }
        }
        if (proto.hasFirstTrainTimeUsed()) {
            this.innerSetFirstTrainTimeUsed(proto.getFirstTrainTimeUsed());
        } else {
            this.innerSetFirstTrainTimeUsed(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUpTrainAndLevelUpLimit()) {
            this.innerSetUpTrainAndLevelUpLimit(proto.getUpTrainAndLevelUpLimit());
        } else {
            this.innerSetUpTrainAndLevelUpLimit(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUpleftTimes()) {
            this.innerSetUpleftTimes(proto.getUpleftTimes());
        } else {
            this.innerSetUpleftTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUpTrains()) {
            this.getUpTrains().mergeFromDb(proto.getUpTrains());
        } else {
            if (this.upTrains != null) {
                this.upTrains.mergeFromDb(proto.getUpTrains());
            }
        }
        this.markAll();
        return PlayerSoldierInBuildingProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerSoldierInBuilding proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTrainingSoldiers()) {
            this.getTrainingSoldiers().mergeChangeFromDb(proto.getTrainingSoldiers());
            fieldCnt++;
        }
        if (proto.hasTrainingOverSoldiers()) {
            this.getTrainingOverSoldiers().mergeChangeFromDb(proto.getTrainingOverSoldiers());
            fieldCnt++;
        }
        if (proto.hasLevelUppingSoldiers()) {
            this.getLevelUppingSoldiers().mergeChangeFromDb(proto.getLevelUppingSoldiers());
            fieldCnt++;
        }
        if (proto.hasFirstTrainTimeUsed()) {
            this.setFirstTrainTimeUsed(proto.getFirstTrainTimeUsed());
            fieldCnt++;
        }
        if (proto.hasUpTrainAndLevelUpLimit()) {
            this.setUpTrainAndLevelUpLimit(proto.getUpTrainAndLevelUpLimit());
            fieldCnt++;
        }
        if (proto.hasUpleftTimes()) {
            this.setUpleftTimes(proto.getUpleftTimes());
            fieldCnt++;
        }
        if (proto.hasUpTrains()) {
            this.getUpTrains().mergeChangeFromDb(proto.getUpTrains());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerSoldierInBuilding.Builder getCopySsBuilder() {
        final PlayerSoldierInBuilding.Builder builder = PlayerSoldierInBuilding.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerSoldierInBuilding.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.trainingSoldiers != null) {
            Struct.Int32PlayerSoldierUnitMap.Builder tmpBuilder = Struct.Int32PlayerSoldierUnitMap.newBuilder();
            final int tmpFieldCnt = this.trainingSoldiers.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTrainingSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTrainingSoldiers();
            }
        }  else if (builder.hasTrainingSoldiers()) {
            // 清理TrainingSoldiers
            builder.clearTrainingSoldiers();
            fieldCnt++;
        }
        if (this.trainingOverSoldiers != null) {
            Struct.Int32PlayerSoldierUnitMap.Builder tmpBuilder = Struct.Int32PlayerSoldierUnitMap.newBuilder();
            final int tmpFieldCnt = this.trainingOverSoldiers.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTrainingOverSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTrainingOverSoldiers();
            }
        }  else if (builder.hasTrainingOverSoldiers()) {
            // 清理TrainingOverSoldiers
            builder.clearTrainingOverSoldiers();
            fieldCnt++;
        }
        if (this.levelUppingSoldiers != null) {
            Struct.Int32PlayerSoldierLevelUpUnitMap.Builder tmpBuilder = Struct.Int32PlayerSoldierLevelUpUnitMap.newBuilder();
            final int tmpFieldCnt = this.levelUppingSoldiers.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLevelUppingSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLevelUppingSoldiers();
            }
        }  else if (builder.hasLevelUppingSoldiers()) {
            // 清理LevelUppingSoldiers
            builder.clearLevelUppingSoldiers();
            fieldCnt++;
        }
        if (this.getFirstTrainTimeUsed()) {
            builder.setFirstTrainTimeUsed(this.getFirstTrainTimeUsed());
            fieldCnt++;
        }  else if (builder.hasFirstTrainTimeUsed()) {
            // 清理FirstTrainTimeUsed
            builder.clearFirstTrainTimeUsed();
            fieldCnt++;
        }
        if (this.getUpTrainAndLevelUpLimit() != 0) {
            builder.setUpTrainAndLevelUpLimit(this.getUpTrainAndLevelUpLimit());
            fieldCnt++;
        }  else if (builder.hasUpTrainAndLevelUpLimit()) {
            // 清理UpTrainAndLevelUpLimit
            builder.clearUpTrainAndLevelUpLimit();
            fieldCnt++;
        }
        if (this.getUpleftTimes() != 0) {
            builder.setUpleftTimes(this.getUpleftTimes());
            fieldCnt++;
        }  else if (builder.hasUpleftTimes()) {
            // 清理UpleftTimes
            builder.clearUpleftTimes();
            fieldCnt++;
        }
        if (this.upTrains != null) {
            Struct.Int32UpTrainInfoMap.Builder tmpBuilder = Struct.Int32UpTrainInfoMap.newBuilder();
            final int tmpFieldCnt = this.upTrains.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUpTrains(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUpTrains();
            }
        }  else if (builder.hasUpTrains()) {
            // 清理UpTrains
            builder.clearUpTrains();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerSoldierInBuilding.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TRAININGSOLDIERS) && this.trainingSoldiers != null) {
            final boolean needClear = !builder.hasTrainingSoldiers();
            final int tmpFieldCnt = this.trainingSoldiers.copyChangeToSs(builder.getTrainingSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTrainingSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_TRAININGOVERSOLDIERS) && this.trainingOverSoldiers != null) {
            final boolean needClear = !builder.hasTrainingOverSoldiers();
            final int tmpFieldCnt = this.trainingOverSoldiers.copyChangeToSs(builder.getTrainingOverSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTrainingOverSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_LEVELUPPINGSOLDIERS) && this.levelUppingSoldiers != null) {
            final boolean needClear = !builder.hasLevelUppingSoldiers();
            final int tmpFieldCnt = this.levelUppingSoldiers.copyChangeToSs(builder.getLevelUppingSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLevelUppingSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_FIRSTTRAINTIMEUSED)) {
            builder.setFirstTrainTimeUsed(this.getFirstTrainTimeUsed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UPTRAINANDLEVELUPLIMIT)) {
            builder.setUpTrainAndLevelUpLimit(this.getUpTrainAndLevelUpLimit());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UPLEFTTIMES)) {
            builder.setUpleftTimes(this.getUpleftTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UPTRAINS) && this.upTrains != null) {
            final boolean needClear = !builder.hasUpTrains();
            final int tmpFieldCnt = this.upTrains.copyChangeToSs(builder.getUpTrainsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUpTrains();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerSoldierInBuilding proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTrainingSoldiers()) {
            this.getTrainingSoldiers().mergeFromSs(proto.getTrainingSoldiers());
        } else {
            if (this.trainingSoldiers != null) {
                this.trainingSoldiers.mergeFromSs(proto.getTrainingSoldiers());
            }
        }
        if (proto.hasTrainingOverSoldiers()) {
            this.getTrainingOverSoldiers().mergeFromSs(proto.getTrainingOverSoldiers());
        } else {
            if (this.trainingOverSoldiers != null) {
                this.trainingOverSoldiers.mergeFromSs(proto.getTrainingOverSoldiers());
            }
        }
        if (proto.hasLevelUppingSoldiers()) {
            this.getLevelUppingSoldiers().mergeFromSs(proto.getLevelUppingSoldiers());
        } else {
            if (this.levelUppingSoldiers != null) {
                this.levelUppingSoldiers.mergeFromSs(proto.getLevelUppingSoldiers());
            }
        }
        if (proto.hasFirstTrainTimeUsed()) {
            this.innerSetFirstTrainTimeUsed(proto.getFirstTrainTimeUsed());
        } else {
            this.innerSetFirstTrainTimeUsed(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUpTrainAndLevelUpLimit()) {
            this.innerSetUpTrainAndLevelUpLimit(proto.getUpTrainAndLevelUpLimit());
        } else {
            this.innerSetUpTrainAndLevelUpLimit(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUpleftTimes()) {
            this.innerSetUpleftTimes(proto.getUpleftTimes());
        } else {
            this.innerSetUpleftTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUpTrains()) {
            this.getUpTrains().mergeFromSs(proto.getUpTrains());
        } else {
            if (this.upTrains != null) {
                this.upTrains.mergeFromSs(proto.getUpTrains());
            }
        }
        this.markAll();
        return PlayerSoldierInBuildingProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerSoldierInBuilding proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTrainingSoldiers()) {
            this.getTrainingSoldiers().mergeChangeFromSs(proto.getTrainingSoldiers());
            fieldCnt++;
        }
        if (proto.hasTrainingOverSoldiers()) {
            this.getTrainingOverSoldiers().mergeChangeFromSs(proto.getTrainingOverSoldiers());
            fieldCnt++;
        }
        if (proto.hasLevelUppingSoldiers()) {
            this.getLevelUppingSoldiers().mergeChangeFromSs(proto.getLevelUppingSoldiers());
            fieldCnt++;
        }
        if (proto.hasFirstTrainTimeUsed()) {
            this.setFirstTrainTimeUsed(proto.getFirstTrainTimeUsed());
            fieldCnt++;
        }
        if (proto.hasUpTrainAndLevelUpLimit()) {
            this.setUpTrainAndLevelUpLimit(proto.getUpTrainAndLevelUpLimit());
            fieldCnt++;
        }
        if (proto.hasUpleftTimes()) {
            this.setUpleftTimes(proto.getUpleftTimes());
            fieldCnt++;
        }
        if (proto.hasUpTrains()) {
            this.getUpTrains().mergeChangeFromSs(proto.getUpTrains());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerSoldierInBuilding.Builder builder = PlayerSoldierInBuilding.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TRAININGSOLDIERS) && this.trainingSoldiers != null) {
            this.trainingSoldiers.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TRAININGOVERSOLDIERS) && this.trainingOverSoldiers != null) {
            this.trainingOverSoldiers.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_LEVELUPPINGSOLDIERS) && this.levelUppingSoldiers != null) {
            this.levelUppingSoldiers.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_UPTRAINS) && this.upTrains != null) {
            this.upTrains.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.trainingSoldiers != null) {
            this.trainingSoldiers.markAll();
        }
        if (this.trainingOverSoldiers != null) {
            this.trainingOverSoldiers.markAll();
        }
        if (this.levelUppingSoldiers != null) {
            this.levelUppingSoldiers.markAll();
        }
        if (this.upTrains != null) {
            this.upTrains.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerSoldierInBuildingProp)) {
            return false;
        }
        final PlayerSoldierInBuildingProp otherNode = (PlayerSoldierInBuildingProp) node;
        if (!this.getTrainingSoldiers().compareDataTo(otherNode.getTrainingSoldiers())) {
            return false;
        }
        if (!this.getTrainingOverSoldiers().compareDataTo(otherNode.getTrainingOverSoldiers())) {
            return false;
        }
        if (!this.getLevelUppingSoldiers().compareDataTo(otherNode.getLevelUppingSoldiers())) {
            return false;
        }
        if (this.firstTrainTimeUsed != otherNode.firstTrainTimeUsed) {
            return false;
        }
        if (this.upTrainAndLevelUpLimit != otherNode.upTrainAndLevelUpLimit) {
            return false;
        }
        if (this.upleftTimes != otherNode.upleftTimes) {
            return false;
        }
        if (!this.getUpTrains().compareDataTo(otherNode.getUpTrains())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 57;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
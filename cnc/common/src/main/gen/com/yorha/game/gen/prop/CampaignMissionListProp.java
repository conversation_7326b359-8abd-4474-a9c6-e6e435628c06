package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.PlayerPB.CampaignMissionListPB;
import com.yorha.proto.Player.CampaignMissionList;
import com.yorha.proto.PlayerPB.CampaignMissionPB;
import com.yorha.proto.Player.CampaignMission;

/**
 * <AUTHOR> auto gen
 */
public class CampaignMissionListProp extends AbstractListNode<CampaignMissionProp> {
    /**
     * Create a CampaignMissionListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public CampaignMissionListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to CampaignMissionListProp
     *
     * @return new object
     */
    @Override
    public CampaignMissionProp addEmptyValue() {
        final CampaignMissionProp newProp = new CampaignMissionProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignMissionListPB.Builder getCopyCsBuilder() {
        final CampaignMissionListPB.Builder builder = CampaignMissionListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(CampaignMissionListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return CampaignMissionListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final CampaignMissionProp v : this) {
            final CampaignMissionPB.Builder itemBuilder = CampaignMissionPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return CampaignMissionListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(CampaignMissionListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return CampaignMissionListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(CampaignMissionListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (CampaignMissionPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return CampaignMissionListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(CampaignMissionListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignMissionList.Builder getCopyDbBuilder() {
        final CampaignMissionList.Builder builder = CampaignMissionList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(CampaignMissionList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return CampaignMissionListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final CampaignMissionProp v : this) {
            final CampaignMission.Builder itemBuilder = CampaignMission.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return CampaignMissionListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(CampaignMissionList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return CampaignMissionListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(CampaignMissionList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (CampaignMission v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return CampaignMissionListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(CampaignMissionList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignMissionList.Builder getCopySsBuilder() {
        final CampaignMissionList.Builder builder = CampaignMissionList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(CampaignMissionList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return CampaignMissionListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final CampaignMissionProp v : this) {
            final CampaignMission.Builder itemBuilder = CampaignMission.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return CampaignMissionListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(CampaignMissionList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return CampaignMissionListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(CampaignMissionList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (CampaignMission v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return CampaignMissionListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(CampaignMissionList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        CampaignMissionList.Builder builder = CampaignMissionList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
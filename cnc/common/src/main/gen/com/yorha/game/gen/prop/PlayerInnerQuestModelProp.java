package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerInnerQuestModel;
import com.yorha.proto.Basic;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.PlayerInnerQuestModelPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerInnerQuestModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_INNERQUEST = 0;
    public static final int FIELD_INDEX_SIDS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private Int32InnerQuestMapProp innerQuest = null;
    private StringListProp sids = null;

    public PlayerInnerQuestModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerInnerQuestModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get innerQuest
     *
     * @return innerQuest value
     */
    public Int32InnerQuestMapProp getInnerQuest() {
        if (this.innerQuest == null) {
            this.innerQuest = new Int32InnerQuestMapProp(this, FIELD_INDEX_INNERQUEST);
        }
        return this.innerQuest;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putInnerQuestV(InnerQuestProp v) {
        this.getInnerQuest().put(v.getConfigId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public InnerQuestProp addEmptyInnerQuest(Integer k) {
        return this.getInnerQuest().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getInnerQuestSize() {
        if (this.innerQuest == null) {
            return 0;
        }
        return this.innerQuest.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isInnerQuestEmpty() {
        if (this.innerQuest == null) {
            return true;
        }
        return this.innerQuest.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public InnerQuestProp getInnerQuestV(Integer k) {
        if (this.innerQuest == null || !this.innerQuest.containsKey(k)) {
            return null;
        }
        return this.innerQuest.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearInnerQuest() {
        if (this.innerQuest != null) {
            this.innerQuest.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeInnerQuestV(Integer k) {
        if (this.innerQuest != null) {
            this.innerQuest.remove(k);
        }
    }
    /**
     * get sids
     *
     * @return sids value
     */
    public StringListProp getSids() {
        if (this.sids == null) {
            this.sids = new StringListProp(this, FIELD_INDEX_SIDS);
        }
        return this.sids;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addSids(String v) {
        this.getSids().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public String getSidsIndex(int index) {
        return this.getSids().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public String removeSids(String v) {
        if (this.sids == null) {
            return null;
        }
        if(this.sids.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getSidsSize() {
        if (this.sids == null) {
            return 0;
        }
        return this.sids.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isSidsEmpty() {
        if (this.sids == null) {
            return true;
        }
        return this.getSids().isEmpty();
    }

    /**
     * clear list
     */
    public void clearSids() {
        this.getSids().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public String removeSidsIndex(int index) {
        return this.getSids().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public String setSidsIndex(int index, String v) {
        return this.getSids().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerInnerQuestModelPB.Builder getCopyCsBuilder() {
        final PlayerInnerQuestModelPB.Builder builder = PlayerInnerQuestModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerInnerQuestModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.innerQuest != null) {
            PlayerPB.Int32InnerQuestMapPB.Builder tmpBuilder = PlayerPB.Int32InnerQuestMapPB.newBuilder();
            final int tmpFieldCnt = this.innerQuest.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInnerQuest(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInnerQuest();
            }
        }  else if (builder.hasInnerQuest()) {
            // 清理InnerQuest
            builder.clearInnerQuest();
            fieldCnt++;
        }
        if (this.sids != null) {
            BasicPB.StringListPB.Builder tmpBuilder = BasicPB.StringListPB.newBuilder();
            final int tmpFieldCnt = this.sids.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSids(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSids();
            }
        }  else if (builder.hasSids()) {
            // 清理Sids
            builder.clearSids();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerInnerQuestModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INNERQUEST) && this.innerQuest != null) {
            final boolean needClear = !builder.hasInnerQuest();
            final int tmpFieldCnt = this.innerQuest.copyChangeToCs(builder.getInnerQuestBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerQuest();
            }
        }
        if (this.hasMark(FIELD_INDEX_SIDS) && this.sids != null) {
            final boolean needClear = !builder.hasSids();
            final int tmpFieldCnt = this.sids.copyChangeToCs(builder.getSidsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSids();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerInnerQuestModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INNERQUEST) && this.innerQuest != null) {
            final boolean needClear = !builder.hasInnerQuest();
            final int tmpFieldCnt = this.innerQuest.copyChangeToAndClearDeleteKeysCs(builder.getInnerQuestBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerQuest();
            }
        }
        if (this.hasMark(FIELD_INDEX_SIDS) && this.sids != null) {
            final boolean needClear = !builder.hasSids();
            final int tmpFieldCnt = this.sids.copyChangeToCs(builder.getSidsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSids();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerInnerQuestModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasInnerQuest()) {
            this.getInnerQuest().mergeFromCs(proto.getInnerQuest());
        } else {
            if (this.innerQuest != null) {
                this.innerQuest.mergeFromCs(proto.getInnerQuest());
            }
        }
        if (proto.hasSids()) {
            this.getSids().mergeFromCs(proto.getSids());
        } else {
            if (this.sids != null) {
                this.sids.mergeFromCs(proto.getSids());
            }
        }
        this.markAll();
        return PlayerInnerQuestModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerInnerQuestModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasInnerQuest()) {
            this.getInnerQuest().mergeChangeFromCs(proto.getInnerQuest());
            fieldCnt++;
        }
        if (proto.hasSids()) {
            this.getSids().mergeChangeFromCs(proto.getSids());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerInnerQuestModel.Builder getCopyDbBuilder() {
        final PlayerInnerQuestModel.Builder builder = PlayerInnerQuestModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerInnerQuestModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.innerQuest != null) {
            Player.Int32InnerQuestMap.Builder tmpBuilder = Player.Int32InnerQuestMap.newBuilder();
            final int tmpFieldCnt = this.innerQuest.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInnerQuest(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInnerQuest();
            }
        }  else if (builder.hasInnerQuest()) {
            // 清理InnerQuest
            builder.clearInnerQuest();
            fieldCnt++;
        }
        if (this.sids != null) {
            Basic.StringList.Builder tmpBuilder = Basic.StringList.newBuilder();
            final int tmpFieldCnt = this.sids.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSids(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSids();
            }
        }  else if (builder.hasSids()) {
            // 清理Sids
            builder.clearSids();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerInnerQuestModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INNERQUEST) && this.innerQuest != null) {
            final boolean needClear = !builder.hasInnerQuest();
            final int tmpFieldCnt = this.innerQuest.copyChangeToDb(builder.getInnerQuestBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerQuest();
            }
        }
        if (this.hasMark(FIELD_INDEX_SIDS) && this.sids != null) {
            final boolean needClear = !builder.hasSids();
            final int tmpFieldCnt = this.sids.copyChangeToDb(builder.getSidsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSids();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerInnerQuestModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasInnerQuest()) {
            this.getInnerQuest().mergeFromDb(proto.getInnerQuest());
        } else {
            if (this.innerQuest != null) {
                this.innerQuest.mergeFromDb(proto.getInnerQuest());
            }
        }
        if (proto.hasSids()) {
            this.getSids().mergeFromDb(proto.getSids());
        } else {
            if (this.sids != null) {
                this.sids.mergeFromDb(proto.getSids());
            }
        }
        this.markAll();
        return PlayerInnerQuestModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerInnerQuestModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasInnerQuest()) {
            this.getInnerQuest().mergeChangeFromDb(proto.getInnerQuest());
            fieldCnt++;
        }
        if (proto.hasSids()) {
            this.getSids().mergeChangeFromDb(proto.getSids());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerInnerQuestModel.Builder getCopySsBuilder() {
        final PlayerInnerQuestModel.Builder builder = PlayerInnerQuestModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerInnerQuestModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.innerQuest != null) {
            Player.Int32InnerQuestMap.Builder tmpBuilder = Player.Int32InnerQuestMap.newBuilder();
            final int tmpFieldCnt = this.innerQuest.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInnerQuest(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInnerQuest();
            }
        }  else if (builder.hasInnerQuest()) {
            // 清理InnerQuest
            builder.clearInnerQuest();
            fieldCnt++;
        }
        if (this.sids != null) {
            Basic.StringList.Builder tmpBuilder = Basic.StringList.newBuilder();
            final int tmpFieldCnt = this.sids.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSids(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSids();
            }
        }  else if (builder.hasSids()) {
            // 清理Sids
            builder.clearSids();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerInnerQuestModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INNERQUEST) && this.innerQuest != null) {
            final boolean needClear = !builder.hasInnerQuest();
            final int tmpFieldCnt = this.innerQuest.copyChangeToSs(builder.getInnerQuestBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerQuest();
            }
        }
        if (this.hasMark(FIELD_INDEX_SIDS) && this.sids != null) {
            final boolean needClear = !builder.hasSids();
            final int tmpFieldCnt = this.sids.copyChangeToSs(builder.getSidsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSids();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerInnerQuestModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasInnerQuest()) {
            this.getInnerQuest().mergeFromSs(proto.getInnerQuest());
        } else {
            if (this.innerQuest != null) {
                this.innerQuest.mergeFromSs(proto.getInnerQuest());
            }
        }
        if (proto.hasSids()) {
            this.getSids().mergeFromSs(proto.getSids());
        } else {
            if (this.sids != null) {
                this.sids.mergeFromSs(proto.getSids());
            }
        }
        this.markAll();
        return PlayerInnerQuestModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerInnerQuestModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasInnerQuest()) {
            this.getInnerQuest().mergeChangeFromSs(proto.getInnerQuest());
            fieldCnt++;
        }
        if (proto.hasSids()) {
            this.getSids().mergeChangeFromSs(proto.getSids());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerInnerQuestModel.Builder builder = PlayerInnerQuestModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_INNERQUEST) && this.innerQuest != null) {
            this.innerQuest.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SIDS) && this.sids != null) {
            this.sids.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.innerQuest != null) {
            this.innerQuest.markAll();
        }
        if (this.sids != null) {
            this.sids.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerInnerQuestModelProp)) {
            return false;
        }
        final PlayerInnerQuestModelProp otherNode = (PlayerInnerQuestModelProp) node;
        if (!this.getInnerQuest().compareDataTo(otherNode.getInnerQuest())) {
            return false;
        }
        if (!this.getSids().compareDataTo(otherNode.getSids())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
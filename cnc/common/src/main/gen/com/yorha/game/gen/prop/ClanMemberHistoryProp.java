package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructClan.ClanMemberHistory;
import com.yorha.proto.StructClan;
import com.yorha.proto.StructClanPB.ClanMemberHistoryPB;
import com.yorha.proto.StructClanPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanMemberHistoryProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_CONTRIBUTION = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private long id = Constant.DEFAULT_LONG_VALUE;
    private ClanContributionProp contribution = null;

    public ClanMemberHistoryProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanMemberHistoryProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public long getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public ClanMemberHistoryProp setId(long id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(long id) {
        this.id = id;
    }

    /**
     * get contribution
     *
     * @return contribution value
     */
    public ClanContributionProp getContribution() {
        if (this.contribution == null) {
            this.contribution = new ClanContributionProp(this, FIELD_INDEX_CONTRIBUTION);
        }
        return this.contribution;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanMemberHistoryPB.Builder getCopyCsBuilder() {
        final ClanMemberHistoryPB.Builder builder = ClanMemberHistoryPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanMemberHistoryPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.contribution != null) {
            StructClanPB.ClanContributionPB.Builder tmpBuilder = StructClanPB.ClanContributionPB.newBuilder();
            final int tmpFieldCnt = this.contribution.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setContribution(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearContribution();
            }
        }  else if (builder.hasContribution()) {
            // 清理Contribution
            builder.clearContribution();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanMemberHistoryPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTION) && this.contribution != null) {
            final boolean needClear = !builder.hasContribution();
            final int tmpFieldCnt = this.contribution.copyChangeToCs(builder.getContributionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContribution();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanMemberHistoryPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTION) && this.contribution != null) {
            final boolean needClear = !builder.hasContribution();
            final int tmpFieldCnt = this.contribution.copyChangeToAndClearDeleteKeysCs(builder.getContributionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContribution();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanMemberHistoryPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasContribution()) {
            this.getContribution().mergeFromCs(proto.getContribution());
        } else {
            if (this.contribution != null) {
                this.contribution.mergeFromCs(proto.getContribution());
            }
        }
        this.markAll();
        return ClanMemberHistoryProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanMemberHistoryPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasContribution()) {
            this.getContribution().mergeChangeFromCs(proto.getContribution());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanMemberHistory.Builder getCopyDbBuilder() {
        final ClanMemberHistory.Builder builder = ClanMemberHistory.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanMemberHistory.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.contribution != null) {
            StructClan.ClanContribution.Builder tmpBuilder = StructClan.ClanContribution.newBuilder();
            final int tmpFieldCnt = this.contribution.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setContribution(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearContribution();
            }
        }  else if (builder.hasContribution()) {
            // 清理Contribution
            builder.clearContribution();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanMemberHistory.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTION) && this.contribution != null) {
            final boolean needClear = !builder.hasContribution();
            final int tmpFieldCnt = this.contribution.copyChangeToDb(builder.getContributionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContribution();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanMemberHistory proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasContribution()) {
            this.getContribution().mergeFromDb(proto.getContribution());
        } else {
            if (this.contribution != null) {
                this.contribution.mergeFromDb(proto.getContribution());
            }
        }
        this.markAll();
        return ClanMemberHistoryProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanMemberHistory proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasContribution()) {
            this.getContribution().mergeChangeFromDb(proto.getContribution());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanMemberHistory.Builder getCopySsBuilder() {
        final ClanMemberHistory.Builder builder = ClanMemberHistory.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanMemberHistory.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.contribution != null) {
            StructClan.ClanContribution.Builder tmpBuilder = StructClan.ClanContribution.newBuilder();
            final int tmpFieldCnt = this.contribution.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setContribution(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearContribution();
            }
        }  else if (builder.hasContribution()) {
            // 清理Contribution
            builder.clearContribution();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanMemberHistory.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTION) && this.contribution != null) {
            final boolean needClear = !builder.hasContribution();
            final int tmpFieldCnt = this.contribution.copyChangeToSs(builder.getContributionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContribution();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanMemberHistory proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasContribution()) {
            this.getContribution().mergeFromSs(proto.getContribution());
        } else {
            if (this.contribution != null) {
                this.contribution.mergeFromSs(proto.getContribution());
            }
        }
        this.markAll();
        return ClanMemberHistoryProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanMemberHistory proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasContribution()) {
            this.getContribution().mergeChangeFromSs(proto.getContribution());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanMemberHistory.Builder builder = ClanMemberHistory.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTION) && this.contribution != null) {
            this.contribution.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.contribution != null) {
            this.contribution.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanMemberHistoryProp)) {
            return false;
        }
        final ClanMemberHistoryProp otherNode = (ClanMemberHistoryProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (!this.getContribution().compareDataTo(otherNode.getContribution())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
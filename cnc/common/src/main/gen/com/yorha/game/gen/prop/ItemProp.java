package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.Item;
import com.yorha.proto.StructPB.ItemPB;


/**
 * <AUTHOR> auto gen
 */
public class ItemProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_KEY = 0;
    public static final int FIELD_INDEX_TEMPLATEID = 1;
    public static final int FIELD_INDEX_NUM = 2;
    public static final int FIELD_INDEX_EXPIREDTIME = 3;
    public static final int FIELD_INDEX_CD = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private long key = Constant.DEFAULT_LONG_VALUE;
    private int templateId = Constant.DEFAULT_INT_VALUE;
    private int num = Constant.DEFAULT_INT_VALUE;
    private long expiredTime = Constant.DEFAULT_LONG_VALUE;
    private long cd = Constant.DEFAULT_LONG_VALUE;

    public ItemProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ItemProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get key
     *
     * @return key value
     */
    public long getKey() {
        return this.key;
    }

    /**
     * set key && set marked
     *
     * @param key new value
     * @return current object
     */
    public ItemProp setKey(long key) {
        if (this.key != key) {
            this.mark(FIELD_INDEX_KEY);
            this.key = key;
        }
        return this;
    }

    /**
     * inner set key
     *
     * @param key new value
     */
    private void innerSetKey(long key) {
        this.key = key;
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public ItemProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }

    /**
     * get num
     *
     * @return num value
     */
    public int getNum() {
        return this.num;
    }

    /**
     * set num && set marked
     *
     * @param num new value
     * @return current object
     */
    public ItemProp setNum(int num) {
        if (this.num != num) {
            this.mark(FIELD_INDEX_NUM);
            this.num = num;
        }
        return this;
    }

    /**
     * inner set num
     *
     * @param num new value
     */
    private void innerSetNum(int num) {
        this.num = num;
    }

    /**
     * get expiredTime
     *
     * @return expiredTime value
     */
    public long getExpiredTime() {
        return this.expiredTime;
    }

    /**
     * set expiredTime && set marked
     *
     * @param expiredTime new value
     * @return current object
     */
    public ItemProp setExpiredTime(long expiredTime) {
        if (this.expiredTime != expiredTime) {
            this.mark(FIELD_INDEX_EXPIREDTIME);
            this.expiredTime = expiredTime;
        }
        return this;
    }

    /**
     * inner set expiredTime
     *
     * @param expiredTime new value
     */
    private void innerSetExpiredTime(long expiredTime) {
        this.expiredTime = expiredTime;
    }

    /**
     * get cd
     *
     * @return cd value
     */
    public long getCd() {
        return this.cd;
    }

    /**
     * set cd && set marked
     *
     * @param cd new value
     * @return current object
     */
    public ItemProp setCd(long cd) {
        if (this.cd != cd) {
            this.mark(FIELD_INDEX_CD);
            this.cd = cd;
        }
        return this;
    }

    /**
     * inner set cd
     *
     * @param cd new value
     */
    private void innerSetCd(long cd) {
        this.cd = cd;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ItemPB.Builder getCopyCsBuilder() {
        final ItemPB.Builder builder = ItemPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getKey() != 0L) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }  else if (builder.hasKey()) {
            // 清理Key
            builder.clearKey();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        if (this.getExpiredTime() != 0L) {
            builder.setExpiredTime(this.getExpiredTime());
            fieldCnt++;
        }  else if (builder.hasExpiredTime()) {
            // 清理ExpiredTime
            builder.clearExpiredTime();
            fieldCnt++;
        }
        if (this.getCd() != 0L) {
            builder.setCd(this.getCd());
            fieldCnt++;
        }  else if (builder.hasCd()) {
            // 清理Cd
            builder.clearCd();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KEY)) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPIREDTIME)) {
            builder.setExpiredTime(this.getExpiredTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CD)) {
            builder.setCd(this.getCd());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KEY)) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPIREDTIME)) {
            builder.setExpiredTime(this.getExpiredTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CD)) {
            builder.setCd(this.getCd());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ItemPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKey()) {
            this.innerSetKey(proto.getKey());
        } else {
            this.innerSetKey(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExpiredTime()) {
            this.innerSetExpiredTime(proto.getExpiredTime());
        } else {
            this.innerSetExpiredTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCd()) {
            this.innerSetCd(proto.getCd());
        } else {
            this.innerSetCd(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ItemPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKey()) {
            this.setKey(proto.getKey());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        if (proto.hasExpiredTime()) {
            this.setExpiredTime(proto.getExpiredTime());
            fieldCnt++;
        }
        if (proto.hasCd()) {
            this.setCd(proto.getCd());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Item.Builder getCopyDbBuilder() {
        final Item.Builder builder = Item.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(Item.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getKey() != 0L) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }  else if (builder.hasKey()) {
            // 清理Key
            builder.clearKey();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        if (this.getExpiredTime() != 0L) {
            builder.setExpiredTime(this.getExpiredTime());
            fieldCnt++;
        }  else if (builder.hasExpiredTime()) {
            // 清理ExpiredTime
            builder.clearExpiredTime();
            fieldCnt++;
        }
        if (this.getCd() != 0L) {
            builder.setCd(this.getCd());
            fieldCnt++;
        }  else if (builder.hasCd()) {
            // 清理Cd
            builder.clearCd();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(Item.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KEY)) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPIREDTIME)) {
            builder.setExpiredTime(this.getExpiredTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CD)) {
            builder.setCd(this.getCd());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(Item proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKey()) {
            this.innerSetKey(proto.getKey());
        } else {
            this.innerSetKey(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExpiredTime()) {
            this.innerSetExpiredTime(proto.getExpiredTime());
        } else {
            this.innerSetExpiredTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCd()) {
            this.innerSetCd(proto.getCd());
        } else {
            this.innerSetCd(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(Item proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKey()) {
            this.setKey(proto.getKey());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        if (proto.hasExpiredTime()) {
            this.setExpiredTime(proto.getExpiredTime());
            fieldCnt++;
        }
        if (proto.hasCd()) {
            this.setCd(proto.getCd());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Item.Builder getCopySsBuilder() {
        final Item.Builder builder = Item.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(Item.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getKey() != 0L) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }  else if (builder.hasKey()) {
            // 清理Key
            builder.clearKey();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        if (this.getExpiredTime() != 0L) {
            builder.setExpiredTime(this.getExpiredTime());
            fieldCnt++;
        }  else if (builder.hasExpiredTime()) {
            // 清理ExpiredTime
            builder.clearExpiredTime();
            fieldCnt++;
        }
        if (this.getCd() != 0L) {
            builder.setCd(this.getCd());
            fieldCnt++;
        }  else if (builder.hasCd()) {
            // 清理Cd
            builder.clearCd();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(Item.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KEY)) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPIREDTIME)) {
            builder.setExpiredTime(this.getExpiredTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CD)) {
            builder.setCd(this.getCd());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(Item proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKey()) {
            this.innerSetKey(proto.getKey());
        } else {
            this.innerSetKey(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExpiredTime()) {
            this.innerSetExpiredTime(proto.getExpiredTime());
        } else {
            this.innerSetExpiredTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCd()) {
            this.innerSetCd(proto.getCd());
        } else {
            this.innerSetCd(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(Item proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKey()) {
            this.setKey(proto.getKey());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        if (proto.hasExpiredTime()) {
            this.setExpiredTime(proto.getExpiredTime());
            fieldCnt++;
        }
        if (proto.hasCd()) {
            this.setCd(proto.getCd());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        Item.Builder builder = Item.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.key;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ItemProp)) {
            return false;
        }
        final ItemProp otherNode = (ItemProp) node;
        if (this.key != otherNode.key) {
            return false;
        }
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        if (this.num != otherNode.num) {
            return false;
        }
        if (this.expiredTime != otherNode.expiredTime) {
            return false;
        }
        if (this.cd != otherNode.cd) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Player.PlayerScene;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.PlayerScenePB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerSceneProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CURSCENEID = 0;
    public static final int FIELD_INDEX_DUNGEONTYPE = 1;
    public static final int FIELD_INDEX_DUNGEONRECORD = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private long curSceneId = Constant.DEFAULT_LONG_VALUE;
    private DungeonType dungeonType = DungeonType.forNumber(0);
    private Int32DungeonSceneRecordMapProp dungeonRecord = null;

    public PlayerSceneProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerSceneProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get curSceneId
     *
     * @return curSceneId value
     */
    public long getCurSceneId() {
        return this.curSceneId;
    }

    /**
     * set curSceneId && set marked
     *
     * @param curSceneId new value
     * @return current object
     */
    public PlayerSceneProp setCurSceneId(long curSceneId) {
        if (this.curSceneId != curSceneId) {
            this.mark(FIELD_INDEX_CURSCENEID);
            this.curSceneId = curSceneId;
        }
        return this;
    }

    /**
     * inner set curSceneId
     *
     * @param curSceneId new value
     */
    private void innerSetCurSceneId(long curSceneId) {
        this.curSceneId = curSceneId;
    }

    /**
     * get dungeonType
     *
     * @return dungeonType value
     */
    public DungeonType getDungeonType() {
        return this.dungeonType;
    }

    /**
     * set dungeonType && set marked
     *
     * @param dungeonType new value
     * @return current object
     */
    public PlayerSceneProp setDungeonType(DungeonType dungeonType) {
        if (dungeonType == null) {
            throw new NullPointerException();
        }
        if (this.dungeonType != dungeonType) {
            this.mark(FIELD_INDEX_DUNGEONTYPE);
            this.dungeonType = dungeonType;
        }
        return this;
    }

    /**
     * inner set dungeonType
     *
     * @param dungeonType new value
     */
    private void innerSetDungeonType(DungeonType dungeonType) {
        this.dungeonType = dungeonType;
    }

    /**
     * get dungeonRecord
     *
     * @return dungeonRecord value
     */
    public Int32DungeonSceneRecordMapProp getDungeonRecord() {
        if (this.dungeonRecord == null) {
            this.dungeonRecord = new Int32DungeonSceneRecordMapProp(this, FIELD_INDEX_DUNGEONRECORD);
        }
        return this.dungeonRecord;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putDungeonRecordV(DungeonSceneRecordProp v) {
        this.getDungeonRecord().put(v.getDungeonType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public DungeonSceneRecordProp addEmptyDungeonRecord(Integer k) {
        return this.getDungeonRecord().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getDungeonRecordSize() {
        if (this.dungeonRecord == null) {
            return 0;
        }
        return this.dungeonRecord.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isDungeonRecordEmpty() {
        if (this.dungeonRecord == null) {
            return true;
        }
        return this.dungeonRecord.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public DungeonSceneRecordProp getDungeonRecordV(Integer k) {
        if (this.dungeonRecord == null || !this.dungeonRecord.containsKey(k)) {
            return null;
        }
        return this.dungeonRecord.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearDungeonRecord() {
        if (this.dungeonRecord != null) {
            this.dungeonRecord.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeDungeonRecordV(Integer k) {
        if (this.dungeonRecord != null) {
            this.dungeonRecord.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerScenePB.Builder getCopyCsBuilder() {
        final PlayerScenePB.Builder builder = PlayerScenePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerScenePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurSceneId() != 0L) {
            builder.setCurSceneId(this.getCurSceneId());
            fieldCnt++;
        }  else if (builder.hasCurSceneId()) {
            // 清理CurSceneId
            builder.clearCurSceneId();
            fieldCnt++;
        }
        if (this.getDungeonType() != DungeonType.forNumber(0)) {
            builder.setDungeonType(this.getDungeonType());
            fieldCnt++;
        }  else if (builder.hasDungeonType()) {
            // 清理DungeonType
            builder.clearDungeonType();
            fieldCnt++;
        }
        if (this.dungeonRecord != null) {
            PlayerPB.Int32DungeonSceneRecordMapPB.Builder tmpBuilder = PlayerPB.Int32DungeonSceneRecordMapPB.newBuilder();
            final int tmpFieldCnt = this.dungeonRecord.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDungeonRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDungeonRecord();
            }
        }  else if (builder.hasDungeonRecord()) {
            // 清理DungeonRecord
            builder.clearDungeonRecord();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerScenePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURSCENEID)) {
            builder.setCurSceneId(this.getCurSceneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONTYPE)) {
            builder.setDungeonType(this.getDungeonType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONRECORD) && this.dungeonRecord != null) {
            final boolean needClear = !builder.hasDungeonRecord();
            final int tmpFieldCnt = this.dungeonRecord.copyChangeToCs(builder.getDungeonRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDungeonRecord();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerScenePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURSCENEID)) {
            builder.setCurSceneId(this.getCurSceneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONTYPE)) {
            builder.setDungeonType(this.getDungeonType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONRECORD) && this.dungeonRecord != null) {
            final boolean needClear = !builder.hasDungeonRecord();
            final int tmpFieldCnt = this.dungeonRecord.copyChangeToAndClearDeleteKeysCs(builder.getDungeonRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDungeonRecord();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerScenePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurSceneId()) {
            this.innerSetCurSceneId(proto.getCurSceneId());
        } else {
            this.innerSetCurSceneId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasDungeonType()) {
            this.innerSetDungeonType(proto.getDungeonType());
        } else {
            this.innerSetDungeonType(DungeonType.forNumber(0));
        }
        if (proto.hasDungeonRecord()) {
            this.getDungeonRecord().mergeFromCs(proto.getDungeonRecord());
        } else {
            if (this.dungeonRecord != null) {
                this.dungeonRecord.mergeFromCs(proto.getDungeonRecord());
            }
        }
        this.markAll();
        return PlayerSceneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerScenePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurSceneId()) {
            this.setCurSceneId(proto.getCurSceneId());
            fieldCnt++;
        }
        if (proto.hasDungeonType()) {
            this.setDungeonType(proto.getDungeonType());
            fieldCnt++;
        }
        if (proto.hasDungeonRecord()) {
            this.getDungeonRecord().mergeChangeFromCs(proto.getDungeonRecord());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerScene.Builder getCopyDbBuilder() {
        final PlayerScene.Builder builder = PlayerScene.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerScene.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurSceneId() != 0L) {
            builder.setCurSceneId(this.getCurSceneId());
            fieldCnt++;
        }  else if (builder.hasCurSceneId()) {
            // 清理CurSceneId
            builder.clearCurSceneId();
            fieldCnt++;
        }
        if (this.getDungeonType() != DungeonType.forNumber(0)) {
            builder.setDungeonType(this.getDungeonType());
            fieldCnt++;
        }  else if (builder.hasDungeonType()) {
            // 清理DungeonType
            builder.clearDungeonType();
            fieldCnt++;
        }
        if (this.dungeonRecord != null) {
            Player.Int32DungeonSceneRecordMap.Builder tmpBuilder = Player.Int32DungeonSceneRecordMap.newBuilder();
            final int tmpFieldCnt = this.dungeonRecord.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDungeonRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDungeonRecord();
            }
        }  else if (builder.hasDungeonRecord()) {
            // 清理DungeonRecord
            builder.clearDungeonRecord();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerScene.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURSCENEID)) {
            builder.setCurSceneId(this.getCurSceneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONTYPE)) {
            builder.setDungeonType(this.getDungeonType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONRECORD) && this.dungeonRecord != null) {
            final boolean needClear = !builder.hasDungeonRecord();
            final int tmpFieldCnt = this.dungeonRecord.copyChangeToDb(builder.getDungeonRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDungeonRecord();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerScene proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurSceneId()) {
            this.innerSetCurSceneId(proto.getCurSceneId());
        } else {
            this.innerSetCurSceneId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasDungeonType()) {
            this.innerSetDungeonType(proto.getDungeonType());
        } else {
            this.innerSetDungeonType(DungeonType.forNumber(0));
        }
        if (proto.hasDungeonRecord()) {
            this.getDungeonRecord().mergeFromDb(proto.getDungeonRecord());
        } else {
            if (this.dungeonRecord != null) {
                this.dungeonRecord.mergeFromDb(proto.getDungeonRecord());
            }
        }
        this.markAll();
        return PlayerSceneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerScene proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurSceneId()) {
            this.setCurSceneId(proto.getCurSceneId());
            fieldCnt++;
        }
        if (proto.hasDungeonType()) {
            this.setDungeonType(proto.getDungeonType());
            fieldCnt++;
        }
        if (proto.hasDungeonRecord()) {
            this.getDungeonRecord().mergeChangeFromDb(proto.getDungeonRecord());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerScene.Builder getCopySsBuilder() {
        final PlayerScene.Builder builder = PlayerScene.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerScene.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurSceneId() != 0L) {
            builder.setCurSceneId(this.getCurSceneId());
            fieldCnt++;
        }  else if (builder.hasCurSceneId()) {
            // 清理CurSceneId
            builder.clearCurSceneId();
            fieldCnt++;
        }
        if (this.getDungeonType() != DungeonType.forNumber(0)) {
            builder.setDungeonType(this.getDungeonType());
            fieldCnt++;
        }  else if (builder.hasDungeonType()) {
            // 清理DungeonType
            builder.clearDungeonType();
            fieldCnt++;
        }
        if (this.dungeonRecord != null) {
            Player.Int32DungeonSceneRecordMap.Builder tmpBuilder = Player.Int32DungeonSceneRecordMap.newBuilder();
            final int tmpFieldCnt = this.dungeonRecord.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDungeonRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDungeonRecord();
            }
        }  else if (builder.hasDungeonRecord()) {
            // 清理DungeonRecord
            builder.clearDungeonRecord();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerScene.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURSCENEID)) {
            builder.setCurSceneId(this.getCurSceneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONTYPE)) {
            builder.setDungeonType(this.getDungeonType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONRECORD) && this.dungeonRecord != null) {
            final boolean needClear = !builder.hasDungeonRecord();
            final int tmpFieldCnt = this.dungeonRecord.copyChangeToSs(builder.getDungeonRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDungeonRecord();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerScene proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurSceneId()) {
            this.innerSetCurSceneId(proto.getCurSceneId());
        } else {
            this.innerSetCurSceneId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasDungeonType()) {
            this.innerSetDungeonType(proto.getDungeonType());
        } else {
            this.innerSetDungeonType(DungeonType.forNumber(0));
        }
        if (proto.hasDungeonRecord()) {
            this.getDungeonRecord().mergeFromSs(proto.getDungeonRecord());
        } else {
            if (this.dungeonRecord != null) {
                this.dungeonRecord.mergeFromSs(proto.getDungeonRecord());
            }
        }
        this.markAll();
        return PlayerSceneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerScene proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurSceneId()) {
            this.setCurSceneId(proto.getCurSceneId());
            fieldCnt++;
        }
        if (proto.hasDungeonType()) {
            this.setDungeonType(proto.getDungeonType());
            fieldCnt++;
        }
        if (proto.hasDungeonRecord()) {
            this.getDungeonRecord().mergeChangeFromSs(proto.getDungeonRecord());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerScene.Builder builder = PlayerScene.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONRECORD) && this.dungeonRecord != null) {
            this.dungeonRecord.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.dungeonRecord != null) {
            this.dungeonRecord.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerSceneProp)) {
            return false;
        }
        final PlayerSceneProp otherNode = (PlayerSceneProp) node;
        if (this.curSceneId != otherNode.curSceneId) {
            return false;
        }
        if (this.dungeonType != otherNode.dungeonType) {
            return false;
        }
        if (!this.getDungeonRecord().compareDataTo(otherNode.getDungeonRecord())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
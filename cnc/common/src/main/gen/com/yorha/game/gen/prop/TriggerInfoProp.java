package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.TriggerInfo;
import com.yorha.proto.StructPB.TriggerInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class TriggerInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_TRIGGERID = 0;
    public static final int FIELD_INDEX_PROCESS = 1;
    public static final int FIELD_INDEX_TRIGGERTIME = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int triggerId = Constant.DEFAULT_INT_VALUE;
    private long process = Constant.DEFAULT_LONG_VALUE;
    private int triggerTime = Constant.DEFAULT_INT_VALUE;

    public TriggerInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public TriggerInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get triggerId
     *
     * @return triggerId value
     */
    public int getTriggerId() {
        return this.triggerId;
    }

    /**
     * set triggerId && set marked
     *
     * @param triggerId new value
     * @return current object
     */
    public TriggerInfoProp setTriggerId(int triggerId) {
        if (this.triggerId != triggerId) {
            this.mark(FIELD_INDEX_TRIGGERID);
            this.triggerId = triggerId;
        }
        return this;
    }

    /**
     * inner set triggerId
     *
     * @param triggerId new value
     */
    private void innerSetTriggerId(int triggerId) {
        this.triggerId = triggerId;
    }

    /**
     * get process
     *
     * @return process value
     */
    public long getProcess() {
        return this.process;
    }

    /**
     * set process && set marked
     *
     * @param process new value
     * @return current object
     */
    public TriggerInfoProp setProcess(long process) {
        if (this.process != process) {
            this.mark(FIELD_INDEX_PROCESS);
            this.process = process;
        }
        return this;
    }

    /**
     * inner set process
     *
     * @param process new value
     */
    private void innerSetProcess(long process) {
        this.process = process;
    }

    /**
     * get triggerTime
     *
     * @return triggerTime value
     */
    public int getTriggerTime() {
        return this.triggerTime;
    }

    /**
     * set triggerTime && set marked
     *
     * @param triggerTime new value
     * @return current object
     */
    public TriggerInfoProp setTriggerTime(int triggerTime) {
        if (this.triggerTime != triggerTime) {
            this.mark(FIELD_INDEX_TRIGGERTIME);
            this.triggerTime = triggerTime;
        }
        return this;
    }

    /**
     * inner set triggerTime
     *
     * @param triggerTime new value
     */
    private void innerSetTriggerTime(int triggerTime) {
        this.triggerTime = triggerTime;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TriggerInfoPB.Builder getCopyCsBuilder() {
        final TriggerInfoPB.Builder builder = TriggerInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(TriggerInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTriggerId() != 0) {
            builder.setTriggerId(this.getTriggerId());
            fieldCnt++;
        }  else if (builder.hasTriggerId()) {
            // 清理TriggerId
            builder.clearTriggerId();
            fieldCnt++;
        }
        if (this.getProcess() != 0L) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }  else if (builder.hasProcess()) {
            // 清理Process
            builder.clearProcess();
            fieldCnt++;
        }
        if (this.getTriggerTime() != 0) {
            builder.setTriggerTime(this.getTriggerTime());
            fieldCnt++;
        }  else if (builder.hasTriggerTime()) {
            // 清理TriggerTime
            builder.clearTriggerTime();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(TriggerInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TRIGGERID)) {
            builder.setTriggerId(this.getTriggerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PROCESS)) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERTIME)) {
            builder.setTriggerTime(this.getTriggerTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(TriggerInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TRIGGERID)) {
            builder.setTriggerId(this.getTriggerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PROCESS)) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERTIME)) {
            builder.setTriggerTime(this.getTriggerTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(TriggerInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTriggerId()) {
            this.innerSetTriggerId(proto.getTriggerId());
        } else {
            this.innerSetTriggerId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasProcess()) {
            this.innerSetProcess(proto.getProcess());
        } else {
            this.innerSetProcess(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTriggerTime()) {
            this.innerSetTriggerTime(proto.getTriggerTime());
        } else {
            this.innerSetTriggerTime(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return TriggerInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(TriggerInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTriggerId()) {
            this.setTriggerId(proto.getTriggerId());
            fieldCnt++;
        }
        if (proto.hasProcess()) {
            this.setProcess(proto.getProcess());
            fieldCnt++;
        }
        if (proto.hasTriggerTime()) {
            this.setTriggerTime(proto.getTriggerTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TriggerInfo.Builder getCopyDbBuilder() {
        final TriggerInfo.Builder builder = TriggerInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(TriggerInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTriggerId() != 0) {
            builder.setTriggerId(this.getTriggerId());
            fieldCnt++;
        }  else if (builder.hasTriggerId()) {
            // 清理TriggerId
            builder.clearTriggerId();
            fieldCnt++;
        }
        if (this.getProcess() != 0L) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }  else if (builder.hasProcess()) {
            // 清理Process
            builder.clearProcess();
            fieldCnt++;
        }
        if (this.getTriggerTime() != 0) {
            builder.setTriggerTime(this.getTriggerTime());
            fieldCnt++;
        }  else if (builder.hasTriggerTime()) {
            // 清理TriggerTime
            builder.clearTriggerTime();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(TriggerInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TRIGGERID)) {
            builder.setTriggerId(this.getTriggerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PROCESS)) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERTIME)) {
            builder.setTriggerTime(this.getTriggerTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(TriggerInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTriggerId()) {
            this.innerSetTriggerId(proto.getTriggerId());
        } else {
            this.innerSetTriggerId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasProcess()) {
            this.innerSetProcess(proto.getProcess());
        } else {
            this.innerSetProcess(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTriggerTime()) {
            this.innerSetTriggerTime(proto.getTriggerTime());
        } else {
            this.innerSetTriggerTime(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return TriggerInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(TriggerInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTriggerId()) {
            this.setTriggerId(proto.getTriggerId());
            fieldCnt++;
        }
        if (proto.hasProcess()) {
            this.setProcess(proto.getProcess());
            fieldCnt++;
        }
        if (proto.hasTriggerTime()) {
            this.setTriggerTime(proto.getTriggerTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TriggerInfo.Builder getCopySsBuilder() {
        final TriggerInfo.Builder builder = TriggerInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(TriggerInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTriggerId() != 0) {
            builder.setTriggerId(this.getTriggerId());
            fieldCnt++;
        }  else if (builder.hasTriggerId()) {
            // 清理TriggerId
            builder.clearTriggerId();
            fieldCnt++;
        }
        if (this.getProcess() != 0L) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }  else if (builder.hasProcess()) {
            // 清理Process
            builder.clearProcess();
            fieldCnt++;
        }
        if (this.getTriggerTime() != 0) {
            builder.setTriggerTime(this.getTriggerTime());
            fieldCnt++;
        }  else if (builder.hasTriggerTime()) {
            // 清理TriggerTime
            builder.clearTriggerTime();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(TriggerInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TRIGGERID)) {
            builder.setTriggerId(this.getTriggerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PROCESS)) {
            builder.setProcess(this.getProcess());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERTIME)) {
            builder.setTriggerTime(this.getTriggerTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(TriggerInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTriggerId()) {
            this.innerSetTriggerId(proto.getTriggerId());
        } else {
            this.innerSetTriggerId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasProcess()) {
            this.innerSetProcess(proto.getProcess());
        } else {
            this.innerSetProcess(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTriggerTime()) {
            this.innerSetTriggerTime(proto.getTriggerTime());
        } else {
            this.innerSetTriggerTime(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return TriggerInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(TriggerInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTriggerId()) {
            this.setTriggerId(proto.getTriggerId());
            fieldCnt++;
        }
        if (proto.hasProcess()) {
            this.setProcess(proto.getProcess());
            fieldCnt++;
        }
        if (proto.hasTriggerTime()) {
            this.setTriggerTime(proto.getTriggerTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        TriggerInfo.Builder builder = TriggerInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.triggerId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof TriggerInfoProp)) {
            return false;
        }
        final TriggerInfoProp otherNode = (TriggerInfoProp) node;
        if (this.triggerId != otherNode.triggerId) {
            return false;
        }
        if (this.process != otherNode.process) {
            return false;
        }
        if (this.triggerTime != otherNode.triggerTime) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
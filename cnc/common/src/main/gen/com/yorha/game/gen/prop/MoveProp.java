package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.Move;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.MovePB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class MoveProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CURPOINT = 0;
    public static final int FIELD_INDEX_STARTTS = 1;
    public static final int FIELD_INDEX_ENDTS = 2;
    public static final int FIELD_INDEX_POINTLIST = 3;
    public static final int FIELD_INDEX_TARGETID = 4;
    public static final int FIELD_INDEX_YAW = 5;
    public static final int FIELD_INDEX_TARGETCLANID = 6;
    public static final int FIELD_INDEX_ISFROZEN = 7;

    public static final int FIELD_COUNT = 8;

    private long markBits0 = 0L;

    private PointProp curPoint = null;
    private long startTs = Constant.DEFAULT_LONG_VALUE;
    private long endTs = Constant.DEFAULT_LONG_VALUE;
    private PointListProp pointList = null;
    private long targetId = Constant.DEFAULT_LONG_VALUE;
    private PointProp yaw = null;
    private long targetClanId = Constant.DEFAULT_LONG_VALUE;
    private boolean isFrozen = Constant.DEFAULT_BOOLEAN_VALUE;

    public MoveProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MoveProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get curPoint
     *
     * @return curPoint value
     */
    public PointProp getCurPoint() {
        if (this.curPoint == null) {
            this.curPoint = new PointProp(this, FIELD_INDEX_CURPOINT);
        }
        return this.curPoint;
    }

    /**
     * get startTs
     *
     * @return startTs value
     */
    public long getStartTs() {
        return this.startTs;
    }

    /**
     * set startTs && set marked
     *
     * @param startTs new value
     * @return current object
     */
    public MoveProp setStartTs(long startTs) {
        if (this.startTs != startTs) {
            this.mark(FIELD_INDEX_STARTTS);
            this.startTs = startTs;
        }
        return this;
    }

    /**
     * inner set startTs
     *
     * @param startTs new value
     */
    private void innerSetStartTs(long startTs) {
        this.startTs = startTs;
    }

    /**
     * get endTs
     *
     * @return endTs value
     */
    public long getEndTs() {
        return this.endTs;
    }

    /**
     * set endTs && set marked
     *
     * @param endTs new value
     * @return current object
     */
    public MoveProp setEndTs(long endTs) {
        if (this.endTs != endTs) {
            this.mark(FIELD_INDEX_ENDTS);
            this.endTs = endTs;
        }
        return this;
    }

    /**
     * inner set endTs
     *
     * @param endTs new value
     */
    private void innerSetEndTs(long endTs) {
        this.endTs = endTs;
    }

    /**
     * get pointList
     *
     * @return pointList value
     */
    public PointListProp getPointList() {
        if (this.pointList == null) {
            this.pointList = new PointListProp(this, FIELD_INDEX_POINTLIST);
        }
        return this.pointList;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addPointList(PointProp v) {
        this.getPointList().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public PointProp getPointListIndex(int index) {
        return this.getPointList().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public PointProp removePointList(PointProp v) {
        if (this.pointList == null) {
            return null;
        }
        if(this.pointList.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getPointListSize() {
        if (this.pointList == null) {
            return 0;
        }
        return this.pointList.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isPointListEmpty() {
        if (this.pointList == null) {
            return true;
        }
        return this.getPointList().isEmpty();
    }

    /**
     * clear list
     */
    public void clearPointList() {
        this.getPointList().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public PointProp removePointListIndex(int index) {
        return this.getPointList().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public PointProp setPointListIndex(int index, PointProp v) {
        return this.getPointList().set(index, v);
    }
    /**
     * get targetId
     *
     * @return targetId value
     */
    public long getTargetId() {
        return this.targetId;
    }

    /**
     * set targetId && set marked
     *
     * @param targetId new value
     * @return current object
     */
    public MoveProp setTargetId(long targetId) {
        if (this.targetId != targetId) {
            this.mark(FIELD_INDEX_TARGETID);
            this.targetId = targetId;
        }
        return this;
    }

    /**
     * inner set targetId
     *
     * @param targetId new value
     */
    private void innerSetTargetId(long targetId) {
        this.targetId = targetId;
    }

    /**
     * get yaw
     *
     * @return yaw value
     */
    public PointProp getYaw() {
        if (this.yaw == null) {
            this.yaw = new PointProp(this, FIELD_INDEX_YAW);
        }
        return this.yaw;
    }

    /**
     * get targetClanId
     *
     * @return targetClanId value
     */
    public long getTargetClanId() {
        return this.targetClanId;
    }

    /**
     * set targetClanId && set marked
     *
     * @param targetClanId new value
     * @return current object
     */
    public MoveProp setTargetClanId(long targetClanId) {
        if (this.targetClanId != targetClanId) {
            this.mark(FIELD_INDEX_TARGETCLANID);
            this.targetClanId = targetClanId;
        }
        return this;
    }

    /**
     * inner set targetClanId
     *
     * @param targetClanId new value
     */
    private void innerSetTargetClanId(long targetClanId) {
        this.targetClanId = targetClanId;
    }

    /**
     * get isFrozen
     *
     * @return isFrozen value
     */
    public boolean getIsFrozen() {
        return this.isFrozen;
    }

    /**
     * set isFrozen && set marked
     *
     * @param isFrozen new value
     * @return current object
     */
    public MoveProp setIsFrozen(boolean isFrozen) {
        if (this.isFrozen != isFrozen) {
            this.mark(FIELD_INDEX_ISFROZEN);
            this.isFrozen = isFrozen;
        }
        return this;
    }

    /**
     * inner set isFrozen
     *
     * @param isFrozen new value
     */
    private void innerSetIsFrozen(boolean isFrozen) {
        this.isFrozen = isFrozen;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MovePB.Builder getCopyCsBuilder() {
        final MovePB.Builder builder = MovePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MovePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.curPoint != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.curPoint.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCurPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCurPoint();
            }
        }  else if (builder.hasCurPoint()) {
            // 清理CurPoint
            builder.clearCurPoint();
            fieldCnt++;
        }
        if (this.getStartTs() != 0L) {
            builder.setStartTs(this.getStartTs());
            fieldCnt++;
        }  else if (builder.hasStartTs()) {
            // 清理StartTs
            builder.clearStartTs();
            fieldCnt++;
        }
        if (this.getEndTs() != 0L) {
            builder.setEndTs(this.getEndTs());
            fieldCnt++;
        }  else if (builder.hasEndTs()) {
            // 清理EndTs
            builder.clearEndTs();
            fieldCnt++;
        }
        if (this.pointList != null) {
            StructPB.PointListPB.Builder tmpBuilder = StructPB.PointListPB.newBuilder();
            final int tmpFieldCnt = this.pointList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPointList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPointList();
            }
        }  else if (builder.hasPointList()) {
            // 清理PointList
            builder.clearPointList();
            fieldCnt++;
        }
        if (this.getTargetId() != 0L) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }  else if (builder.hasTargetId()) {
            // 清理TargetId
            builder.clearTargetId();
            fieldCnt++;
        }
        if (this.yaw != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.yaw.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setYaw(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearYaw();
            }
        }  else if (builder.hasYaw()) {
            // 清理Yaw
            builder.clearYaw();
            fieldCnt++;
        }
        if (this.getTargetClanId() != 0L) {
            builder.setTargetClanId(this.getTargetClanId());
            fieldCnt++;
        }  else if (builder.hasTargetClanId()) {
            // 清理TargetClanId
            builder.clearTargetClanId();
            fieldCnt++;
        }
        if (this.getIsFrozen()) {
            builder.setIsFrozen(this.getIsFrozen());
            fieldCnt++;
        }  else if (builder.hasIsFrozen()) {
            // 清理IsFrozen
            builder.clearIsFrozen();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MovePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURPOINT) && this.curPoint != null) {
            final boolean needClear = !builder.hasCurPoint();
            final int tmpFieldCnt = this.curPoint.copyChangeToCs(builder.getCurPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCurPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_STARTTS)) {
            builder.setStartTs(this.getStartTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTS)) {
            builder.setEndTs(this.getEndTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINTLIST) && this.pointList != null) {
            final boolean needClear = !builder.hasPointList();
            final int tmpFieldCnt = this.pointList.copyChangeToCs(builder.getPointListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPointList();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_YAW) && this.yaw != null) {
            final boolean needClear = !builder.hasYaw();
            final int tmpFieldCnt = this.yaw.copyChangeToCs(builder.getYawBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearYaw();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETCLANID)) {
            builder.setTargetClanId(this.getTargetClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISFROZEN)) {
            builder.setIsFrozen(this.getIsFrozen());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MovePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURPOINT) && this.curPoint != null) {
            final boolean needClear = !builder.hasCurPoint();
            final int tmpFieldCnt = this.curPoint.copyChangeToAndClearDeleteKeysCs(builder.getCurPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCurPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_STARTTS)) {
            builder.setStartTs(this.getStartTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTS)) {
            builder.setEndTs(this.getEndTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINTLIST) && this.pointList != null) {
            final boolean needClear = !builder.hasPointList();
            final int tmpFieldCnt = this.pointList.copyChangeToCs(builder.getPointListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPointList();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_YAW) && this.yaw != null) {
            final boolean needClear = !builder.hasYaw();
            final int tmpFieldCnt = this.yaw.copyChangeToAndClearDeleteKeysCs(builder.getYawBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearYaw();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETCLANID)) {
            builder.setTargetClanId(this.getTargetClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISFROZEN)) {
            builder.setIsFrozen(this.getIsFrozen());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MovePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurPoint()) {
            this.getCurPoint().mergeFromCs(proto.getCurPoint());
        } else {
            if (this.curPoint != null) {
                this.curPoint.mergeFromCs(proto.getCurPoint());
            }
        }
        if (proto.hasStartTs()) {
            this.innerSetStartTs(proto.getStartTs());
        } else {
            this.innerSetStartTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTs()) {
            this.innerSetEndTs(proto.getEndTs());
        } else {
            this.innerSetEndTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPointList()) {
            this.getPointList().mergeFromCs(proto.getPointList());
        } else {
            if (this.pointList != null) {
                this.pointList.mergeFromCs(proto.getPointList());
            }
        }
        if (proto.hasTargetId()) {
            this.innerSetTargetId(proto.getTargetId());
        } else {
            this.innerSetTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasYaw()) {
            this.getYaw().mergeFromCs(proto.getYaw());
        } else {
            if (this.yaw != null) {
                this.yaw.mergeFromCs(proto.getYaw());
            }
        }
        if (proto.hasTargetClanId()) {
            this.innerSetTargetClanId(proto.getTargetClanId());
        } else {
            this.innerSetTargetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIsFrozen()) {
            this.innerSetIsFrozen(proto.getIsFrozen());
        } else {
            this.innerSetIsFrozen(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return MoveProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MovePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurPoint()) {
            this.getCurPoint().mergeChangeFromCs(proto.getCurPoint());
            fieldCnt++;
        }
        if (proto.hasStartTs()) {
            this.setStartTs(proto.getStartTs());
            fieldCnt++;
        }
        if (proto.hasEndTs()) {
            this.setEndTs(proto.getEndTs());
            fieldCnt++;
        }
        if (proto.hasPointList()) {
            this.getPointList().mergeChangeFromCs(proto.getPointList());
            fieldCnt++;
        }
        if (proto.hasTargetId()) {
            this.setTargetId(proto.getTargetId());
            fieldCnt++;
        }
        if (proto.hasYaw()) {
            this.getYaw().mergeChangeFromCs(proto.getYaw());
            fieldCnt++;
        }
        if (proto.hasTargetClanId()) {
            this.setTargetClanId(proto.getTargetClanId());
            fieldCnt++;
        }
        if (proto.hasIsFrozen()) {
            this.setIsFrozen(proto.getIsFrozen());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Move.Builder getCopyDbBuilder() {
        final Move.Builder builder = Move.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(Move.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.curPoint != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.curPoint.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCurPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCurPoint();
            }
        }  else if (builder.hasCurPoint()) {
            // 清理CurPoint
            builder.clearCurPoint();
            fieldCnt++;
        }
        if (this.getStartTs() != 0L) {
            builder.setStartTs(this.getStartTs());
            fieldCnt++;
        }  else if (builder.hasStartTs()) {
            // 清理StartTs
            builder.clearStartTs();
            fieldCnt++;
        }
        if (this.getEndTs() != 0L) {
            builder.setEndTs(this.getEndTs());
            fieldCnt++;
        }  else if (builder.hasEndTs()) {
            // 清理EndTs
            builder.clearEndTs();
            fieldCnt++;
        }
        if (this.pointList != null) {
            Struct.PointList.Builder tmpBuilder = Struct.PointList.newBuilder();
            final int tmpFieldCnt = this.pointList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPointList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPointList();
            }
        }  else if (builder.hasPointList()) {
            // 清理PointList
            builder.clearPointList();
            fieldCnt++;
        }
        if (this.getTargetId() != 0L) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }  else if (builder.hasTargetId()) {
            // 清理TargetId
            builder.clearTargetId();
            fieldCnt++;
        }
        if (this.yaw != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.yaw.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setYaw(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearYaw();
            }
        }  else if (builder.hasYaw()) {
            // 清理Yaw
            builder.clearYaw();
            fieldCnt++;
        }
        if (this.getTargetClanId() != 0L) {
            builder.setTargetClanId(this.getTargetClanId());
            fieldCnt++;
        }  else if (builder.hasTargetClanId()) {
            // 清理TargetClanId
            builder.clearTargetClanId();
            fieldCnt++;
        }
        if (this.getIsFrozen()) {
            builder.setIsFrozen(this.getIsFrozen());
            fieldCnt++;
        }  else if (builder.hasIsFrozen()) {
            // 清理IsFrozen
            builder.clearIsFrozen();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(Move.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURPOINT) && this.curPoint != null) {
            final boolean needClear = !builder.hasCurPoint();
            final int tmpFieldCnt = this.curPoint.copyChangeToDb(builder.getCurPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCurPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_STARTTS)) {
            builder.setStartTs(this.getStartTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTS)) {
            builder.setEndTs(this.getEndTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINTLIST) && this.pointList != null) {
            final boolean needClear = !builder.hasPointList();
            final int tmpFieldCnt = this.pointList.copyChangeToDb(builder.getPointListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPointList();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_YAW) && this.yaw != null) {
            final boolean needClear = !builder.hasYaw();
            final int tmpFieldCnt = this.yaw.copyChangeToDb(builder.getYawBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearYaw();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETCLANID)) {
            builder.setTargetClanId(this.getTargetClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISFROZEN)) {
            builder.setIsFrozen(this.getIsFrozen());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(Move proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurPoint()) {
            this.getCurPoint().mergeFromDb(proto.getCurPoint());
        } else {
            if (this.curPoint != null) {
                this.curPoint.mergeFromDb(proto.getCurPoint());
            }
        }
        if (proto.hasStartTs()) {
            this.innerSetStartTs(proto.getStartTs());
        } else {
            this.innerSetStartTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTs()) {
            this.innerSetEndTs(proto.getEndTs());
        } else {
            this.innerSetEndTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPointList()) {
            this.getPointList().mergeFromDb(proto.getPointList());
        } else {
            if (this.pointList != null) {
                this.pointList.mergeFromDb(proto.getPointList());
            }
        }
        if (proto.hasTargetId()) {
            this.innerSetTargetId(proto.getTargetId());
        } else {
            this.innerSetTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasYaw()) {
            this.getYaw().mergeFromDb(proto.getYaw());
        } else {
            if (this.yaw != null) {
                this.yaw.mergeFromDb(proto.getYaw());
            }
        }
        if (proto.hasTargetClanId()) {
            this.innerSetTargetClanId(proto.getTargetClanId());
        } else {
            this.innerSetTargetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIsFrozen()) {
            this.innerSetIsFrozen(proto.getIsFrozen());
        } else {
            this.innerSetIsFrozen(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return MoveProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(Move proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurPoint()) {
            this.getCurPoint().mergeChangeFromDb(proto.getCurPoint());
            fieldCnt++;
        }
        if (proto.hasStartTs()) {
            this.setStartTs(proto.getStartTs());
            fieldCnt++;
        }
        if (proto.hasEndTs()) {
            this.setEndTs(proto.getEndTs());
            fieldCnt++;
        }
        if (proto.hasPointList()) {
            this.getPointList().mergeChangeFromDb(proto.getPointList());
            fieldCnt++;
        }
        if (proto.hasTargetId()) {
            this.setTargetId(proto.getTargetId());
            fieldCnt++;
        }
        if (proto.hasYaw()) {
            this.getYaw().mergeChangeFromDb(proto.getYaw());
            fieldCnt++;
        }
        if (proto.hasTargetClanId()) {
            this.setTargetClanId(proto.getTargetClanId());
            fieldCnt++;
        }
        if (proto.hasIsFrozen()) {
            this.setIsFrozen(proto.getIsFrozen());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Move.Builder getCopySsBuilder() {
        final Move.Builder builder = Move.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(Move.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.curPoint != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.curPoint.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCurPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCurPoint();
            }
        }  else if (builder.hasCurPoint()) {
            // 清理CurPoint
            builder.clearCurPoint();
            fieldCnt++;
        }
        if (this.getStartTs() != 0L) {
            builder.setStartTs(this.getStartTs());
            fieldCnt++;
        }  else if (builder.hasStartTs()) {
            // 清理StartTs
            builder.clearStartTs();
            fieldCnt++;
        }
        if (this.getEndTs() != 0L) {
            builder.setEndTs(this.getEndTs());
            fieldCnt++;
        }  else if (builder.hasEndTs()) {
            // 清理EndTs
            builder.clearEndTs();
            fieldCnt++;
        }
        if (this.pointList != null) {
            Struct.PointList.Builder tmpBuilder = Struct.PointList.newBuilder();
            final int tmpFieldCnt = this.pointList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPointList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPointList();
            }
        }  else if (builder.hasPointList()) {
            // 清理PointList
            builder.clearPointList();
            fieldCnt++;
        }
        if (this.getTargetId() != 0L) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }  else if (builder.hasTargetId()) {
            // 清理TargetId
            builder.clearTargetId();
            fieldCnt++;
        }
        if (this.yaw != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.yaw.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setYaw(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearYaw();
            }
        }  else if (builder.hasYaw()) {
            // 清理Yaw
            builder.clearYaw();
            fieldCnt++;
        }
        if (this.getTargetClanId() != 0L) {
            builder.setTargetClanId(this.getTargetClanId());
            fieldCnt++;
        }  else if (builder.hasTargetClanId()) {
            // 清理TargetClanId
            builder.clearTargetClanId();
            fieldCnt++;
        }
        if (this.getIsFrozen()) {
            builder.setIsFrozen(this.getIsFrozen());
            fieldCnt++;
        }  else if (builder.hasIsFrozen()) {
            // 清理IsFrozen
            builder.clearIsFrozen();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(Move.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURPOINT) && this.curPoint != null) {
            final boolean needClear = !builder.hasCurPoint();
            final int tmpFieldCnt = this.curPoint.copyChangeToSs(builder.getCurPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCurPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_STARTTS)) {
            builder.setStartTs(this.getStartTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTS)) {
            builder.setEndTs(this.getEndTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINTLIST) && this.pointList != null) {
            final boolean needClear = !builder.hasPointList();
            final int tmpFieldCnt = this.pointList.copyChangeToSs(builder.getPointListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPointList();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_YAW) && this.yaw != null) {
            final boolean needClear = !builder.hasYaw();
            final int tmpFieldCnt = this.yaw.copyChangeToSs(builder.getYawBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearYaw();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETCLANID)) {
            builder.setTargetClanId(this.getTargetClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISFROZEN)) {
            builder.setIsFrozen(this.getIsFrozen());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(Move proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurPoint()) {
            this.getCurPoint().mergeFromSs(proto.getCurPoint());
        } else {
            if (this.curPoint != null) {
                this.curPoint.mergeFromSs(proto.getCurPoint());
            }
        }
        if (proto.hasStartTs()) {
            this.innerSetStartTs(proto.getStartTs());
        } else {
            this.innerSetStartTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEndTs()) {
            this.innerSetEndTs(proto.getEndTs());
        } else {
            this.innerSetEndTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPointList()) {
            this.getPointList().mergeFromSs(proto.getPointList());
        } else {
            if (this.pointList != null) {
                this.pointList.mergeFromSs(proto.getPointList());
            }
        }
        if (proto.hasTargetId()) {
            this.innerSetTargetId(proto.getTargetId());
        } else {
            this.innerSetTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasYaw()) {
            this.getYaw().mergeFromSs(proto.getYaw());
        } else {
            if (this.yaw != null) {
                this.yaw.mergeFromSs(proto.getYaw());
            }
        }
        if (proto.hasTargetClanId()) {
            this.innerSetTargetClanId(proto.getTargetClanId());
        } else {
            this.innerSetTargetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIsFrozen()) {
            this.innerSetIsFrozen(proto.getIsFrozen());
        } else {
            this.innerSetIsFrozen(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return MoveProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(Move proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurPoint()) {
            this.getCurPoint().mergeChangeFromSs(proto.getCurPoint());
            fieldCnt++;
        }
        if (proto.hasStartTs()) {
            this.setStartTs(proto.getStartTs());
            fieldCnt++;
        }
        if (proto.hasEndTs()) {
            this.setEndTs(proto.getEndTs());
            fieldCnt++;
        }
        if (proto.hasPointList()) {
            this.getPointList().mergeChangeFromSs(proto.getPointList());
            fieldCnt++;
        }
        if (proto.hasTargetId()) {
            this.setTargetId(proto.getTargetId());
            fieldCnt++;
        }
        if (proto.hasYaw()) {
            this.getYaw().mergeChangeFromSs(proto.getYaw());
            fieldCnt++;
        }
        if (proto.hasTargetClanId()) {
            this.setTargetClanId(proto.getTargetClanId());
            fieldCnt++;
        }
        if (proto.hasIsFrozen()) {
            this.setIsFrozen(proto.getIsFrozen());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        Move.Builder builder = Move.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CURPOINT) && this.curPoint != null) {
            this.curPoint.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_POINTLIST) && this.pointList != null) {
            this.pointList.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_YAW) && this.yaw != null) {
            this.yaw.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.curPoint != null) {
            this.curPoint.markAll();
        }
        if (this.pointList != null) {
            this.pointList.markAll();
        }
        if (this.yaw != null) {
            this.yaw.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MoveProp)) {
            return false;
        }
        final MoveProp otherNode = (MoveProp) node;
        if (!this.getCurPoint().compareDataTo(otherNode.getCurPoint())) {
            return false;
        }
        if (this.startTs != otherNode.startTs) {
            return false;
        }
        if (this.endTs != otherNode.endTs) {
            return false;
        }
        if (!this.getPointList().compareDataTo(otherNode.getPointList())) {
            return false;
        }
        if (this.targetId != otherNode.targetId) {
            return false;
        }
        if (!this.getYaw().compareDataTo(otherNode.getYaw())) {
            return false;
        }
        if (this.targetClanId != otherNode.targetClanId) {
            return false;
        }
        if (this.isFrozen != otherNode.isFrozen) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 56;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructPB.RewardRecordListPB;
import com.yorha.proto.Struct.RewardRecordList;
import com.yorha.proto.StructPB.RewardRecordPB;
import com.yorha.proto.Struct.RewardRecord;

/**
 * <AUTHOR> auto gen
 */
public class RewardRecordListProp extends AbstractListNode<RewardRecordProp> {
    /**
     * Create a RewardRecordListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public RewardRecordListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to RewardRecordListProp
     *
     * @return new object
     */
    @Override
    public RewardRecordProp addEmptyValue() {
        final RewardRecordProp newProp = new RewardRecordProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RewardRecordListPB.Builder getCopyCsBuilder() {
        final RewardRecordListPB.Builder builder = RewardRecordListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(RewardRecordListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return RewardRecordListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final RewardRecordProp v : this) {
            final RewardRecordPB.Builder itemBuilder = RewardRecordPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return RewardRecordListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(RewardRecordListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return RewardRecordListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(RewardRecordListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (RewardRecordPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return RewardRecordListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(RewardRecordListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RewardRecordList.Builder getCopyDbBuilder() {
        final RewardRecordList.Builder builder = RewardRecordList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(RewardRecordList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return RewardRecordListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final RewardRecordProp v : this) {
            final RewardRecord.Builder itemBuilder = RewardRecord.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return RewardRecordListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(RewardRecordList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return RewardRecordListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(RewardRecordList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (RewardRecord v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return RewardRecordListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(RewardRecordList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RewardRecordList.Builder getCopySsBuilder() {
        final RewardRecordList.Builder builder = RewardRecordList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(RewardRecordList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return RewardRecordListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final RewardRecordProp v : this) {
            final RewardRecord.Builder itemBuilder = RewardRecord.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return RewardRecordListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(RewardRecordList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return RewardRecordListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(RewardRecordList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (RewardRecord v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return RewardRecordListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(RewardRecordList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        RewardRecordList.Builder builder = RewardRecordList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
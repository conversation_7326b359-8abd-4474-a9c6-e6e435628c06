package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.SpyCityData;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.SpyCityDataPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class SpyCityDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_WALLLEVEL = 0;
    public static final int FIELD_INDEX_CURWALLHP = 1;
    public static final int FIELD_INDEX_MAXWALLHP = 2;
    public static final int FIELD_INDEX_TOWERLEVEL = 3;
    public static final int FIELD_INDEX_CURTOWERHP = 4;
    public static final int FIELD_INDEX_MAXTOWERHP = 5;
    public static final int FIELD_INDEX_RESOURCES = 6;

    public static final int FIELD_COUNT = 7;

    private long markBits0 = 0L;

    private int wallLevel = Constant.DEFAULT_INT_VALUE;
    private int curWallHp = Constant.DEFAULT_INT_VALUE;
    private int maxWallHp = Constant.DEFAULT_INT_VALUE;
    private int towerLevel = Constant.DEFAULT_INT_VALUE;
    private int curTowerHp = Constant.DEFAULT_INT_VALUE;
    private int maxTowerHp = Constant.DEFAULT_INT_VALUE;
    private Int32CurrencyMapProp resources = null;

    public SpyCityDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SpyCityDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get wallLevel
     *
     * @return wallLevel value
     */
    public int getWallLevel() {
        return this.wallLevel;
    }

    /**
     * set wallLevel && set marked
     *
     * @param wallLevel new value
     * @return current object
     */
    public SpyCityDataProp setWallLevel(int wallLevel) {
        if (this.wallLevel != wallLevel) {
            this.mark(FIELD_INDEX_WALLLEVEL);
            this.wallLevel = wallLevel;
        }
        return this;
    }

    /**
     * inner set wallLevel
     *
     * @param wallLevel new value
     */
    private void innerSetWallLevel(int wallLevel) {
        this.wallLevel = wallLevel;
    }

    /**
     * get curWallHp
     *
     * @return curWallHp value
     */
    public int getCurWallHp() {
        return this.curWallHp;
    }

    /**
     * set curWallHp && set marked
     *
     * @param curWallHp new value
     * @return current object
     */
    public SpyCityDataProp setCurWallHp(int curWallHp) {
        if (this.curWallHp != curWallHp) {
            this.mark(FIELD_INDEX_CURWALLHP);
            this.curWallHp = curWallHp;
        }
        return this;
    }

    /**
     * inner set curWallHp
     *
     * @param curWallHp new value
     */
    private void innerSetCurWallHp(int curWallHp) {
        this.curWallHp = curWallHp;
    }

    /**
     * get maxWallHp
     *
     * @return maxWallHp value
     */
    public int getMaxWallHp() {
        return this.maxWallHp;
    }

    /**
     * set maxWallHp && set marked
     *
     * @param maxWallHp new value
     * @return current object
     */
    public SpyCityDataProp setMaxWallHp(int maxWallHp) {
        if (this.maxWallHp != maxWallHp) {
            this.mark(FIELD_INDEX_MAXWALLHP);
            this.maxWallHp = maxWallHp;
        }
        return this;
    }

    /**
     * inner set maxWallHp
     *
     * @param maxWallHp new value
     */
    private void innerSetMaxWallHp(int maxWallHp) {
        this.maxWallHp = maxWallHp;
    }

    /**
     * get towerLevel
     *
     * @return towerLevel value
     */
    public int getTowerLevel() {
        return this.towerLevel;
    }

    /**
     * set towerLevel && set marked
     *
     * @param towerLevel new value
     * @return current object
     */
    public SpyCityDataProp setTowerLevel(int towerLevel) {
        if (this.towerLevel != towerLevel) {
            this.mark(FIELD_INDEX_TOWERLEVEL);
            this.towerLevel = towerLevel;
        }
        return this;
    }

    /**
     * inner set towerLevel
     *
     * @param towerLevel new value
     */
    private void innerSetTowerLevel(int towerLevel) {
        this.towerLevel = towerLevel;
    }

    /**
     * get curTowerHp
     *
     * @return curTowerHp value
     */
    public int getCurTowerHp() {
        return this.curTowerHp;
    }

    /**
     * set curTowerHp && set marked
     *
     * @param curTowerHp new value
     * @return current object
     */
    public SpyCityDataProp setCurTowerHp(int curTowerHp) {
        if (this.curTowerHp != curTowerHp) {
            this.mark(FIELD_INDEX_CURTOWERHP);
            this.curTowerHp = curTowerHp;
        }
        return this;
    }

    /**
     * inner set curTowerHp
     *
     * @param curTowerHp new value
     */
    private void innerSetCurTowerHp(int curTowerHp) {
        this.curTowerHp = curTowerHp;
    }

    /**
     * get maxTowerHp
     *
     * @return maxTowerHp value
     */
    public int getMaxTowerHp() {
        return this.maxTowerHp;
    }

    /**
     * set maxTowerHp && set marked
     *
     * @param maxTowerHp new value
     * @return current object
     */
    public SpyCityDataProp setMaxTowerHp(int maxTowerHp) {
        if (this.maxTowerHp != maxTowerHp) {
            this.mark(FIELD_INDEX_MAXTOWERHP);
            this.maxTowerHp = maxTowerHp;
        }
        return this;
    }

    /**
     * inner set maxTowerHp
     *
     * @param maxTowerHp new value
     */
    private void innerSetMaxTowerHp(int maxTowerHp) {
        this.maxTowerHp = maxTowerHp;
    }

    /**
     * get resources
     *
     * @return resources value
     */
    public Int32CurrencyMapProp getResources() {
        if (this.resources == null) {
            this.resources = new Int32CurrencyMapProp(this, FIELD_INDEX_RESOURCES);
        }
        return this.resources;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putResourcesV(CurrencyProp v) {
        this.getResources().put(v.getType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public CurrencyProp addEmptyResources(Integer k) {
        return this.getResources().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getResourcesSize() {
        if (this.resources == null) {
            return 0;
        }
        return this.resources.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isResourcesEmpty() {
        if (this.resources == null) {
            return true;
        }
        return this.resources.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public CurrencyProp getResourcesV(Integer k) {
        if (this.resources == null || !this.resources.containsKey(k)) {
            return null;
        }
        return this.resources.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearResources() {
        if (this.resources != null) {
            this.resources.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeResourcesV(Integer k) {
        if (this.resources != null) {
            this.resources.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SpyCityDataPB.Builder getCopyCsBuilder() {
        final SpyCityDataPB.Builder builder = SpyCityDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SpyCityDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getWallLevel() != 0) {
            builder.setWallLevel(this.getWallLevel());
            fieldCnt++;
        }  else if (builder.hasWallLevel()) {
            // 清理WallLevel
            builder.clearWallLevel();
            fieldCnt++;
        }
        if (this.getCurWallHp() != 0) {
            builder.setCurWallHp(this.getCurWallHp());
            fieldCnt++;
        }  else if (builder.hasCurWallHp()) {
            // 清理CurWallHp
            builder.clearCurWallHp();
            fieldCnt++;
        }
        if (this.getMaxWallHp() != 0) {
            builder.setMaxWallHp(this.getMaxWallHp());
            fieldCnt++;
        }  else if (builder.hasMaxWallHp()) {
            // 清理MaxWallHp
            builder.clearMaxWallHp();
            fieldCnt++;
        }
        if (this.getTowerLevel() != 0) {
            builder.setTowerLevel(this.getTowerLevel());
            fieldCnt++;
        }  else if (builder.hasTowerLevel()) {
            // 清理TowerLevel
            builder.clearTowerLevel();
            fieldCnt++;
        }
        if (this.getCurTowerHp() != 0) {
            builder.setCurTowerHp(this.getCurTowerHp());
            fieldCnt++;
        }  else if (builder.hasCurTowerHp()) {
            // 清理CurTowerHp
            builder.clearCurTowerHp();
            fieldCnt++;
        }
        if (this.getMaxTowerHp() != 0) {
            builder.setMaxTowerHp(this.getMaxTowerHp());
            fieldCnt++;
        }  else if (builder.hasMaxTowerHp()) {
            // 清理MaxTowerHp
            builder.clearMaxTowerHp();
            fieldCnt++;
        }
        if (this.resources != null) {
            StructPB.Int32CurrencyMapPB.Builder tmpBuilder = StructPB.Int32CurrencyMapPB.newBuilder();
            final int tmpFieldCnt = this.resources.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResources(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResources();
            }
        }  else if (builder.hasResources()) {
            // 清理Resources
            builder.clearResources();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SpyCityDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_WALLLEVEL)) {
            builder.setWallLevel(this.getWallLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURWALLHP)) {
            builder.setCurWallHp(this.getCurWallHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXWALLHP)) {
            builder.setMaxWallHp(this.getMaxWallHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERLEVEL)) {
            builder.setTowerLevel(this.getTowerLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURTOWERHP)) {
            builder.setCurTowerHp(this.getCurTowerHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXTOWERHP)) {
            builder.setMaxTowerHp(this.getMaxTowerHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            final boolean needClear = !builder.hasResources();
            final int tmpFieldCnt = this.resources.copyChangeToCs(builder.getResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResources();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SpyCityDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_WALLLEVEL)) {
            builder.setWallLevel(this.getWallLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURWALLHP)) {
            builder.setCurWallHp(this.getCurWallHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXWALLHP)) {
            builder.setMaxWallHp(this.getMaxWallHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERLEVEL)) {
            builder.setTowerLevel(this.getTowerLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURTOWERHP)) {
            builder.setCurTowerHp(this.getCurTowerHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXTOWERHP)) {
            builder.setMaxTowerHp(this.getMaxTowerHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            final boolean needClear = !builder.hasResources();
            final int tmpFieldCnt = this.resources.copyChangeToAndClearDeleteKeysCs(builder.getResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResources();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SpyCityDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasWallLevel()) {
            this.innerSetWallLevel(proto.getWallLevel());
        } else {
            this.innerSetWallLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurWallHp()) {
            this.innerSetCurWallHp(proto.getCurWallHp());
        } else {
            this.innerSetCurWallHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMaxWallHp()) {
            this.innerSetMaxWallHp(proto.getMaxWallHp());
        } else {
            this.innerSetMaxWallHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTowerLevel()) {
            this.innerSetTowerLevel(proto.getTowerLevel());
        } else {
            this.innerSetTowerLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurTowerHp()) {
            this.innerSetCurTowerHp(proto.getCurTowerHp());
        } else {
            this.innerSetCurTowerHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMaxTowerHp()) {
            this.innerSetMaxTowerHp(proto.getMaxTowerHp());
        } else {
            this.innerSetMaxTowerHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasResources()) {
            this.getResources().mergeFromCs(proto.getResources());
        } else {
            if (this.resources != null) {
                this.resources.mergeFromCs(proto.getResources());
            }
        }
        this.markAll();
        return SpyCityDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SpyCityDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasWallLevel()) {
            this.setWallLevel(proto.getWallLevel());
            fieldCnt++;
        }
        if (proto.hasCurWallHp()) {
            this.setCurWallHp(proto.getCurWallHp());
            fieldCnt++;
        }
        if (proto.hasMaxWallHp()) {
            this.setMaxWallHp(proto.getMaxWallHp());
            fieldCnt++;
        }
        if (proto.hasTowerLevel()) {
            this.setTowerLevel(proto.getTowerLevel());
            fieldCnt++;
        }
        if (proto.hasCurTowerHp()) {
            this.setCurTowerHp(proto.getCurTowerHp());
            fieldCnt++;
        }
        if (proto.hasMaxTowerHp()) {
            this.setMaxTowerHp(proto.getMaxTowerHp());
            fieldCnt++;
        }
        if (proto.hasResources()) {
            this.getResources().mergeChangeFromCs(proto.getResources());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SpyCityData.Builder getCopyDbBuilder() {
        final SpyCityData.Builder builder = SpyCityData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SpyCityData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getWallLevel() != 0) {
            builder.setWallLevel(this.getWallLevel());
            fieldCnt++;
        }  else if (builder.hasWallLevel()) {
            // 清理WallLevel
            builder.clearWallLevel();
            fieldCnt++;
        }
        if (this.getCurWallHp() != 0) {
            builder.setCurWallHp(this.getCurWallHp());
            fieldCnt++;
        }  else if (builder.hasCurWallHp()) {
            // 清理CurWallHp
            builder.clearCurWallHp();
            fieldCnt++;
        }
        if (this.getMaxWallHp() != 0) {
            builder.setMaxWallHp(this.getMaxWallHp());
            fieldCnt++;
        }  else if (builder.hasMaxWallHp()) {
            // 清理MaxWallHp
            builder.clearMaxWallHp();
            fieldCnt++;
        }
        if (this.getTowerLevel() != 0) {
            builder.setTowerLevel(this.getTowerLevel());
            fieldCnt++;
        }  else if (builder.hasTowerLevel()) {
            // 清理TowerLevel
            builder.clearTowerLevel();
            fieldCnt++;
        }
        if (this.getCurTowerHp() != 0) {
            builder.setCurTowerHp(this.getCurTowerHp());
            fieldCnt++;
        }  else if (builder.hasCurTowerHp()) {
            // 清理CurTowerHp
            builder.clearCurTowerHp();
            fieldCnt++;
        }
        if (this.getMaxTowerHp() != 0) {
            builder.setMaxTowerHp(this.getMaxTowerHp());
            fieldCnt++;
        }  else if (builder.hasMaxTowerHp()) {
            // 清理MaxTowerHp
            builder.clearMaxTowerHp();
            fieldCnt++;
        }
        if (this.resources != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.resources.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResources(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResources();
            }
        }  else if (builder.hasResources()) {
            // 清理Resources
            builder.clearResources();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SpyCityData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_WALLLEVEL)) {
            builder.setWallLevel(this.getWallLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURWALLHP)) {
            builder.setCurWallHp(this.getCurWallHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXWALLHP)) {
            builder.setMaxWallHp(this.getMaxWallHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERLEVEL)) {
            builder.setTowerLevel(this.getTowerLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURTOWERHP)) {
            builder.setCurTowerHp(this.getCurTowerHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXTOWERHP)) {
            builder.setMaxTowerHp(this.getMaxTowerHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            final boolean needClear = !builder.hasResources();
            final int tmpFieldCnt = this.resources.copyChangeToDb(builder.getResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResources();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SpyCityData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasWallLevel()) {
            this.innerSetWallLevel(proto.getWallLevel());
        } else {
            this.innerSetWallLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurWallHp()) {
            this.innerSetCurWallHp(proto.getCurWallHp());
        } else {
            this.innerSetCurWallHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMaxWallHp()) {
            this.innerSetMaxWallHp(proto.getMaxWallHp());
        } else {
            this.innerSetMaxWallHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTowerLevel()) {
            this.innerSetTowerLevel(proto.getTowerLevel());
        } else {
            this.innerSetTowerLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurTowerHp()) {
            this.innerSetCurTowerHp(proto.getCurTowerHp());
        } else {
            this.innerSetCurTowerHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMaxTowerHp()) {
            this.innerSetMaxTowerHp(proto.getMaxTowerHp());
        } else {
            this.innerSetMaxTowerHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasResources()) {
            this.getResources().mergeFromDb(proto.getResources());
        } else {
            if (this.resources != null) {
                this.resources.mergeFromDb(proto.getResources());
            }
        }
        this.markAll();
        return SpyCityDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SpyCityData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasWallLevel()) {
            this.setWallLevel(proto.getWallLevel());
            fieldCnt++;
        }
        if (proto.hasCurWallHp()) {
            this.setCurWallHp(proto.getCurWallHp());
            fieldCnt++;
        }
        if (proto.hasMaxWallHp()) {
            this.setMaxWallHp(proto.getMaxWallHp());
            fieldCnt++;
        }
        if (proto.hasTowerLevel()) {
            this.setTowerLevel(proto.getTowerLevel());
            fieldCnt++;
        }
        if (proto.hasCurTowerHp()) {
            this.setCurTowerHp(proto.getCurTowerHp());
            fieldCnt++;
        }
        if (proto.hasMaxTowerHp()) {
            this.setMaxTowerHp(proto.getMaxTowerHp());
            fieldCnt++;
        }
        if (proto.hasResources()) {
            this.getResources().mergeChangeFromDb(proto.getResources());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SpyCityData.Builder getCopySsBuilder() {
        final SpyCityData.Builder builder = SpyCityData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SpyCityData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getWallLevel() != 0) {
            builder.setWallLevel(this.getWallLevel());
            fieldCnt++;
        }  else if (builder.hasWallLevel()) {
            // 清理WallLevel
            builder.clearWallLevel();
            fieldCnt++;
        }
        if (this.getCurWallHp() != 0) {
            builder.setCurWallHp(this.getCurWallHp());
            fieldCnt++;
        }  else if (builder.hasCurWallHp()) {
            // 清理CurWallHp
            builder.clearCurWallHp();
            fieldCnt++;
        }
        if (this.getMaxWallHp() != 0) {
            builder.setMaxWallHp(this.getMaxWallHp());
            fieldCnt++;
        }  else if (builder.hasMaxWallHp()) {
            // 清理MaxWallHp
            builder.clearMaxWallHp();
            fieldCnt++;
        }
        if (this.getTowerLevel() != 0) {
            builder.setTowerLevel(this.getTowerLevel());
            fieldCnt++;
        }  else if (builder.hasTowerLevel()) {
            // 清理TowerLevel
            builder.clearTowerLevel();
            fieldCnt++;
        }
        if (this.getCurTowerHp() != 0) {
            builder.setCurTowerHp(this.getCurTowerHp());
            fieldCnt++;
        }  else if (builder.hasCurTowerHp()) {
            // 清理CurTowerHp
            builder.clearCurTowerHp();
            fieldCnt++;
        }
        if (this.getMaxTowerHp() != 0) {
            builder.setMaxTowerHp(this.getMaxTowerHp());
            fieldCnt++;
        }  else if (builder.hasMaxTowerHp()) {
            // 清理MaxTowerHp
            builder.clearMaxTowerHp();
            fieldCnt++;
        }
        if (this.resources != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.resources.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResources(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResources();
            }
        }  else if (builder.hasResources()) {
            // 清理Resources
            builder.clearResources();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SpyCityData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_WALLLEVEL)) {
            builder.setWallLevel(this.getWallLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURWALLHP)) {
            builder.setCurWallHp(this.getCurWallHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXWALLHP)) {
            builder.setMaxWallHp(this.getMaxWallHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERLEVEL)) {
            builder.setTowerLevel(this.getTowerLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURTOWERHP)) {
            builder.setCurTowerHp(this.getCurTowerHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXTOWERHP)) {
            builder.setMaxTowerHp(this.getMaxTowerHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            final boolean needClear = !builder.hasResources();
            final int tmpFieldCnt = this.resources.copyChangeToSs(builder.getResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResources();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SpyCityData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasWallLevel()) {
            this.innerSetWallLevel(proto.getWallLevel());
        } else {
            this.innerSetWallLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurWallHp()) {
            this.innerSetCurWallHp(proto.getCurWallHp());
        } else {
            this.innerSetCurWallHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMaxWallHp()) {
            this.innerSetMaxWallHp(proto.getMaxWallHp());
        } else {
            this.innerSetMaxWallHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTowerLevel()) {
            this.innerSetTowerLevel(proto.getTowerLevel());
        } else {
            this.innerSetTowerLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurTowerHp()) {
            this.innerSetCurTowerHp(proto.getCurTowerHp());
        } else {
            this.innerSetCurTowerHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMaxTowerHp()) {
            this.innerSetMaxTowerHp(proto.getMaxTowerHp());
        } else {
            this.innerSetMaxTowerHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasResources()) {
            this.getResources().mergeFromSs(proto.getResources());
        } else {
            if (this.resources != null) {
                this.resources.mergeFromSs(proto.getResources());
            }
        }
        this.markAll();
        return SpyCityDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SpyCityData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasWallLevel()) {
            this.setWallLevel(proto.getWallLevel());
            fieldCnt++;
        }
        if (proto.hasCurWallHp()) {
            this.setCurWallHp(proto.getCurWallHp());
            fieldCnt++;
        }
        if (proto.hasMaxWallHp()) {
            this.setMaxWallHp(proto.getMaxWallHp());
            fieldCnt++;
        }
        if (proto.hasTowerLevel()) {
            this.setTowerLevel(proto.getTowerLevel());
            fieldCnt++;
        }
        if (proto.hasCurTowerHp()) {
            this.setCurTowerHp(proto.getCurTowerHp());
            fieldCnt++;
        }
        if (proto.hasMaxTowerHp()) {
            this.setMaxTowerHp(proto.getMaxTowerHp());
            fieldCnt++;
        }
        if (proto.hasResources()) {
            this.getResources().mergeChangeFromSs(proto.getResources());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SpyCityData.Builder builder = SpyCityData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            this.resources.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.resources != null) {
            this.resources.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SpyCityDataProp)) {
            return false;
        }
        final SpyCityDataProp otherNode = (SpyCityDataProp) node;
        if (this.wallLevel != otherNode.wallLevel) {
            return false;
        }
        if (this.curWallHp != otherNode.curWallHp) {
            return false;
        }
        if (this.maxWallHp != otherNode.maxWallHp) {
            return false;
        }
        if (this.towerLevel != otherNode.towerLevel) {
            return false;
        }
        if (this.curTowerHp != otherNode.curTowerHp) {
            return false;
        }
        if (this.maxTowerHp != otherNode.maxTowerHp) {
            return false;
        }
        if (!this.getResources().compareDataTo(otherNode.getResources())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 57;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
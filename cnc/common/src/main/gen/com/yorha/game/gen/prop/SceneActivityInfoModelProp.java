package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zone.SceneActivityInfoModel;
import com.yorha.proto.Zone;
import com.yorha.proto.ZonePB.SceneActivityInfoModelPB;
import com.yorha.proto.ZonePB;


/**
 * <AUTHOR> auto gen
 */
public class SceneActivityInfoModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ACTIVITYINFOS = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32ActivityInfoMapProp activityInfos = null;

    public SceneActivityInfoModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SceneActivityInfoModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get activityInfos
     *
     * @return activityInfos value
     */
    public Int32ActivityInfoMapProp getActivityInfos() {
        if (this.activityInfos == null) {
            this.activityInfos = new Int32ActivityInfoMapProp(this, FIELD_INDEX_ACTIVITYINFOS);
        }
        return this.activityInfos;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putActivityInfosV(ActivityInfoProp v) {
        this.getActivityInfos().put(v.getActivityEffect(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ActivityInfoProp addEmptyActivityInfos(Integer k) {
        return this.getActivityInfos().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getActivityInfosSize() {
        if (this.activityInfos == null) {
            return 0;
        }
        return this.activityInfos.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isActivityInfosEmpty() {
        if (this.activityInfos == null) {
            return true;
        }
        return this.activityInfos.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ActivityInfoProp getActivityInfosV(Integer k) {
        if (this.activityInfos == null || !this.activityInfos.containsKey(k)) {
            return null;
        }
        return this.activityInfos.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearActivityInfos() {
        if (this.activityInfos != null) {
            this.activityInfos.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeActivityInfosV(Integer k) {
        if (this.activityInfos != null) {
            this.activityInfos.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneActivityInfoModelPB.Builder getCopyCsBuilder() {
        final SceneActivityInfoModelPB.Builder builder = SceneActivityInfoModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SceneActivityInfoModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.activityInfos != null) {
            ZonePB.Int32ActivityInfoMapPB.Builder tmpBuilder = ZonePB.Int32ActivityInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.activityInfos.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActivityInfos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActivityInfos();
            }
        }  else if (builder.hasActivityInfos()) {
            // 清理ActivityInfos
            builder.clearActivityInfos();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SceneActivityInfoModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITYINFOS) && this.activityInfos != null) {
            final boolean needClear = !builder.hasActivityInfos();
            final int tmpFieldCnt = this.activityInfos.copyChangeToCs(builder.getActivityInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivityInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SceneActivityInfoModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITYINFOS) && this.activityInfos != null) {
            final boolean needClear = !builder.hasActivityInfos();
            final int tmpFieldCnt = this.activityInfos.copyChangeToAndClearDeleteKeysCs(builder.getActivityInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivityInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SceneActivityInfoModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActivityInfos()) {
            this.getActivityInfos().mergeFromCs(proto.getActivityInfos());
        } else {
            if (this.activityInfos != null) {
                this.activityInfos.mergeFromCs(proto.getActivityInfos());
            }
        }
        this.markAll();
        return SceneActivityInfoModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SceneActivityInfoModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActivityInfos()) {
            this.getActivityInfos().mergeChangeFromCs(proto.getActivityInfos());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneActivityInfoModel.Builder getCopyDbBuilder() {
        final SceneActivityInfoModel.Builder builder = SceneActivityInfoModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SceneActivityInfoModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.activityInfos != null) {
            Zone.Int32ActivityInfoMap.Builder tmpBuilder = Zone.Int32ActivityInfoMap.newBuilder();
            final int tmpFieldCnt = this.activityInfos.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActivityInfos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActivityInfos();
            }
        }  else if (builder.hasActivityInfos()) {
            // 清理ActivityInfos
            builder.clearActivityInfos();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SceneActivityInfoModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITYINFOS) && this.activityInfos != null) {
            final boolean needClear = !builder.hasActivityInfos();
            final int tmpFieldCnt = this.activityInfos.copyChangeToDb(builder.getActivityInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivityInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SceneActivityInfoModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActivityInfos()) {
            this.getActivityInfos().mergeFromDb(proto.getActivityInfos());
        } else {
            if (this.activityInfos != null) {
                this.activityInfos.mergeFromDb(proto.getActivityInfos());
            }
        }
        this.markAll();
        return SceneActivityInfoModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SceneActivityInfoModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActivityInfos()) {
            this.getActivityInfos().mergeChangeFromDb(proto.getActivityInfos());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneActivityInfoModel.Builder getCopySsBuilder() {
        final SceneActivityInfoModel.Builder builder = SceneActivityInfoModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SceneActivityInfoModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.activityInfos != null) {
            Zone.Int32ActivityInfoMap.Builder tmpBuilder = Zone.Int32ActivityInfoMap.newBuilder();
            final int tmpFieldCnt = this.activityInfos.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActivityInfos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActivityInfos();
            }
        }  else if (builder.hasActivityInfos()) {
            // 清理ActivityInfos
            builder.clearActivityInfos();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SceneActivityInfoModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTIVITYINFOS) && this.activityInfos != null) {
            final boolean needClear = !builder.hasActivityInfos();
            final int tmpFieldCnt = this.activityInfos.copyChangeToSs(builder.getActivityInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivityInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SceneActivityInfoModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActivityInfos()) {
            this.getActivityInfos().mergeFromSs(proto.getActivityInfos());
        } else {
            if (this.activityInfos != null) {
                this.activityInfos.mergeFromSs(proto.getActivityInfos());
            }
        }
        this.markAll();
        return SceneActivityInfoModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SceneActivityInfoModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActivityInfos()) {
            this.getActivityInfos().mergeChangeFromSs(proto.getActivityInfos());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SceneActivityInfoModel.Builder builder = SceneActivityInfoModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITYINFOS) && this.activityInfos != null) {
            this.activityInfos.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.activityInfos != null) {
            this.activityInfos.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SceneActivityInfoModelProp)) {
            return false;
        }
        final SceneActivityInfoModelProp otherNode = (SceneActivityInfoModelProp) node;
        if (!this.getActivityInfos().compareDataTo(otherNode.getActivityInfos())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
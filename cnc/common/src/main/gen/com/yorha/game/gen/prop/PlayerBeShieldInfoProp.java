package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerBeShieldInfo;
import com.yorha.proto.PlayerPB.PlayerBeShieldInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerBeShieldInfoProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_PLAYERID = 0;
    public static final int FIELD_INDEX_ZONEID = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private long playerId = Constant.DEFAULT_LONG_VALUE;
    private int zoneId = Constant.DEFAULT_INT_VALUE;

    public PlayerBeShieldInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerBeShieldInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get playerId
     *
     * @return playerId value
     */
    public long getPlayerId() {
        return this.playerId;
    }

    /**
     * set playerId && set marked
     *
     * @param playerId new value
     * @return current object
     */
    public PlayerBeShieldInfoProp setPlayerId(long playerId) {
        if (this.playerId != playerId) {
            this.mark(FIELD_INDEX_PLAYERID);
            this.playerId = playerId;
        }
        return this;
    }

    /**
     * inner set playerId
     *
     * @param playerId new value
     */
    private void innerSetPlayerId(long playerId) {
        this.playerId = playerId;
    }

    /**
     * get zoneId
     *
     * @return zoneId value
     */
    public int getZoneId() {
        return this.zoneId;
    }

    /**
     * set zoneId && set marked
     *
     * @param zoneId new value
     * @return current object
     */
    public PlayerBeShieldInfoProp setZoneId(int zoneId) {
        if (this.zoneId != zoneId) {
            this.mark(FIELD_INDEX_ZONEID);
            this.zoneId = zoneId;
        }
        return this;
    }

    /**
     * inner set zoneId
     *
     * @param zoneId new value
     */
    private void innerSetZoneId(int zoneId) {
        this.zoneId = zoneId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerBeShieldInfoPB.Builder getCopyCsBuilder() {
        final PlayerBeShieldInfoPB.Builder builder = PlayerBeShieldInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerBeShieldInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerBeShieldInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerBeShieldInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerBeShieldInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerBeShieldInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerBeShieldInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerBeShieldInfo.Builder getCopyDbBuilder() {
        final PlayerBeShieldInfo.Builder builder = PlayerBeShieldInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerBeShieldInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerBeShieldInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerBeShieldInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerBeShieldInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerBeShieldInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerBeShieldInfo.Builder getCopySsBuilder() {
        final PlayerBeShieldInfo.Builder builder = PlayerBeShieldInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerBeShieldInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerBeShieldInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerBeShieldInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerBeShieldInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerBeShieldInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerBeShieldInfo.Builder builder = PlayerBeShieldInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.playerId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerBeShieldInfoProp)) {
            return false;
        }
        final PlayerBeShieldInfoProp otherNode = (PlayerBeShieldInfoProp) node;
        if (this.playerId != otherNode.playerId) {
            return false;
        }
        if (this.zoneId != otherNode.zoneId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
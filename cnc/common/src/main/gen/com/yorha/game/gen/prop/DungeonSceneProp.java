package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Dungeon.DungeonSceneEntity;
import com.yorha.proto.DungeonPB.DungeonSceneEntityPB;


/**
 * <AUTHOR> auto gen
 */
public class DungeonSceneProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TYPE = 0;
    public static final int FIELD_INDEX_DUNGEONID = 1;
    public static final int FIELD_INDEX_STAGE = 2;
    public static final int FIELD_INDEX_ENTERSTAGETSMS = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;
    private PropertyChangeListener listener;

    private DungeonType type = DungeonType.forNumber(0);
    private int dungeonId = Constant.DEFAULT_INT_VALUE;
    private DungeonStage stage = DungeonStage.forNumber(0);
    private long enterStageTsMs = Constant.DEFAULT_LONG_VALUE;

    public DungeonSceneProp() {
        super(null, 0, FIELD_COUNT);
    }

    public DungeonSceneProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get type
     *
     * @return type value
     */
    public DungeonType getType() {
        return this.type;
    }

    /**
     * set type && set marked
     *
     * @param type new value
     * @return current object
     */
    public DungeonSceneProp setType(DungeonType type) {
        if (type == null) {
            throw new NullPointerException();
        }
        if (this.type != type) {
            this.mark(FIELD_INDEX_TYPE);
            this.type = type;
        }
        return this;
    }

    /**
     * inner set type
     *
     * @param type new value
     */
    private void innerSetType(DungeonType type) {
        this.type = type;
    }

    /**
     * get dungeonId
     *
     * @return dungeonId value
     */
    public int getDungeonId() {
        return this.dungeonId;
    }

    /**
     * set dungeonId && set marked
     *
     * @param dungeonId new value
     * @return current object
     */
    public DungeonSceneProp setDungeonId(int dungeonId) {
        if (this.dungeonId != dungeonId) {
            this.mark(FIELD_INDEX_DUNGEONID);
            this.dungeonId = dungeonId;
        }
        return this;
    }

    /**
     * inner set dungeonId
     *
     * @param dungeonId new value
     */
    private void innerSetDungeonId(int dungeonId) {
        this.dungeonId = dungeonId;
    }

    /**
     * get stage
     *
     * @return stage value
     */
    public DungeonStage getStage() {
        return this.stage;
    }

    /**
     * set stage && set marked
     *
     * @param stage new value
     * @return current object
     */
    public DungeonSceneProp setStage(DungeonStage stage) {
        if (stage == null) {
            throw new NullPointerException();
        }
        if (this.stage != stage) {
            this.mark(FIELD_INDEX_STAGE);
            this.stage = stage;
        }
        return this;
    }

    /**
     * inner set stage
     *
     * @param stage new value
     */
    private void innerSetStage(DungeonStage stage) {
        this.stage = stage;
    }

    /**
     * get enterStageTsMs
     *
     * @return enterStageTsMs value
     */
    public long getEnterStageTsMs() {
        return this.enterStageTsMs;
    }

    /**
     * set enterStageTsMs && set marked
     *
     * @param enterStageTsMs new value
     * @return current object
     */
    public DungeonSceneProp setEnterStageTsMs(long enterStageTsMs) {
        if (this.enterStageTsMs != enterStageTsMs) {
            this.mark(FIELD_INDEX_ENTERSTAGETSMS);
            this.enterStageTsMs = enterStageTsMs;
        }
        return this;
    }

    /**
     * inner set enterStageTsMs
     *
     * @param enterStageTsMs new value
     */
    private void innerSetEnterStageTsMs(long enterStageTsMs) {
        this.enterStageTsMs = enterStageTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DungeonSceneEntityPB.Builder getCopyCsBuilder() {
        final DungeonSceneEntityPB.Builder builder = DungeonSceneEntityPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(DungeonSceneEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != DungeonType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getDungeonId() != 0) {
            builder.setDungeonId(this.getDungeonId());
            fieldCnt++;
        }  else if (builder.hasDungeonId()) {
            // 清理DungeonId
            builder.clearDungeonId();
            fieldCnt++;
        }
        if (this.getStage() != DungeonStage.forNumber(0)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }  else if (builder.hasStage()) {
            // 清理Stage
            builder.clearStage();
            fieldCnt++;
        }
        if (this.getEnterStageTsMs() != 0L) {
            builder.setEnterStageTsMs(this.getEnterStageTsMs());
            fieldCnt++;
        }  else if (builder.hasEnterStageTsMs()) {
            // 清理EnterStageTsMs
            builder.clearEnterStageTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(DungeonSceneEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONID)) {
            builder.setDungeonId(this.getDungeonId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGE)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERSTAGETSMS)) {
            builder.setEnterStageTsMs(this.getEnterStageTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(DungeonSceneEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONID)) {
            builder.setDungeonId(this.getDungeonId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGE)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERSTAGETSMS)) {
            builder.setEnterStageTsMs(this.getEnterStageTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(DungeonSceneEntityPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(DungeonType.forNumber(0));
        }
        if (proto.hasDungeonId()) {
            this.innerSetDungeonId(proto.getDungeonId());
        } else {
            this.innerSetDungeonId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStage()) {
            this.innerSetStage(proto.getStage());
        } else {
            this.innerSetStage(DungeonStage.forNumber(0));
        }
        if (proto.hasEnterStageTsMs()) {
            this.innerSetEnterStageTsMs(proto.getEnterStageTsMs());
        } else {
            this.innerSetEnterStageTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return DungeonSceneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(DungeonSceneEntityPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasDungeonId()) {
            this.setDungeonId(proto.getDungeonId());
            fieldCnt++;
        }
        if (proto.hasStage()) {
            this.setStage(proto.getStage());
            fieldCnt++;
        }
        if (proto.hasEnterStageTsMs()) {
            this.setEnterStageTsMs(proto.getEnterStageTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DungeonSceneEntity.Builder getCopyDbBuilder() {
        final DungeonSceneEntity.Builder builder = DungeonSceneEntity.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(DungeonSceneEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != DungeonType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getDungeonId() != 0) {
            builder.setDungeonId(this.getDungeonId());
            fieldCnt++;
        }  else if (builder.hasDungeonId()) {
            // 清理DungeonId
            builder.clearDungeonId();
            fieldCnt++;
        }
        if (this.getStage() != DungeonStage.forNumber(0)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }  else if (builder.hasStage()) {
            // 清理Stage
            builder.clearStage();
            fieldCnt++;
        }
        if (this.getEnterStageTsMs() != 0L) {
            builder.setEnterStageTsMs(this.getEnterStageTsMs());
            fieldCnt++;
        }  else if (builder.hasEnterStageTsMs()) {
            // 清理EnterStageTsMs
            builder.clearEnterStageTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(DungeonSceneEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONID)) {
            builder.setDungeonId(this.getDungeonId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGE)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERSTAGETSMS)) {
            builder.setEnterStageTsMs(this.getEnterStageTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(DungeonSceneEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(DungeonType.forNumber(0));
        }
        if (proto.hasDungeonId()) {
            this.innerSetDungeonId(proto.getDungeonId());
        } else {
            this.innerSetDungeonId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStage()) {
            this.innerSetStage(proto.getStage());
        } else {
            this.innerSetStage(DungeonStage.forNumber(0));
        }
        if (proto.hasEnterStageTsMs()) {
            this.innerSetEnterStageTsMs(proto.getEnterStageTsMs());
        } else {
            this.innerSetEnterStageTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return DungeonSceneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(DungeonSceneEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasDungeonId()) {
            this.setDungeonId(proto.getDungeonId());
            fieldCnt++;
        }
        if (proto.hasStage()) {
            this.setStage(proto.getStage());
            fieldCnt++;
        }
        if (proto.hasEnterStageTsMs()) {
            this.setEnterStageTsMs(proto.getEnterStageTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DungeonSceneEntity.Builder getCopySsBuilder() {
        final DungeonSceneEntity.Builder builder = DungeonSceneEntity.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(DungeonSceneEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != DungeonType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getDungeonId() != 0) {
            builder.setDungeonId(this.getDungeonId());
            fieldCnt++;
        }  else if (builder.hasDungeonId()) {
            // 清理DungeonId
            builder.clearDungeonId();
            fieldCnt++;
        }
        if (this.getStage() != DungeonStage.forNumber(0)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }  else if (builder.hasStage()) {
            // 清理Stage
            builder.clearStage();
            fieldCnt++;
        }
        if (this.getEnterStageTsMs() != 0L) {
            builder.setEnterStageTsMs(this.getEnterStageTsMs());
            fieldCnt++;
        }  else if (builder.hasEnterStageTsMs()) {
            // 清理EnterStageTsMs
            builder.clearEnterStageTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(DungeonSceneEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONID)) {
            builder.setDungeonId(this.getDungeonId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGE)) {
            builder.setStage(this.getStage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERSTAGETSMS)) {
            builder.setEnterStageTsMs(this.getEnterStageTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(DungeonSceneEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(DungeonType.forNumber(0));
        }
        if (proto.hasDungeonId()) {
            this.innerSetDungeonId(proto.getDungeonId());
        } else {
            this.innerSetDungeonId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStage()) {
            this.innerSetStage(proto.getStage());
        } else {
            this.innerSetStage(DungeonStage.forNumber(0));
        }
        if (proto.hasEnterStageTsMs()) {
            this.innerSetEnterStageTsMs(proto.getEnterStageTsMs());
        } else {
            this.innerSetEnterStageTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return DungeonSceneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(DungeonSceneEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasDungeonId()) {
            this.setDungeonId(proto.getDungeonId());
            fieldCnt++;
        }
        if (proto.hasStage()) {
            this.setStage(proto.getStage());
            fieldCnt++;
        }
        if (proto.hasEnterStageTsMs()) {
            this.setEnterStageTsMs(proto.getEnterStageTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        DungeonSceneEntity.Builder builder = DungeonSceneEntity.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("DungeonSceneProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("DungeonSceneProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof DungeonSceneProp)) {
            return false;
        }
        final DungeonSceneProp otherNode = (DungeonSceneProp) node;
        if (this.type != otherNode.type) {
            return false;
        }
        if (this.dungeonId != otherNode.dungeonId) {
            return false;
        }
        if (this.stage != otherNode.stage) {
            return false;
        }
        if (this.enterStageTsMs != otherNode.enterStageTsMs) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    public static DungeonSceneProp of(DungeonSceneEntity fullAttrDb, DungeonSceneEntity changeAttrDb) {
        DungeonSceneProp prop = new DungeonSceneProp();
        prop.mergeFromDb(fullAttrDb);
        prop.mergeChangeFromDb(changeAttrDb);
        prop.unMarkAll();
        return prop;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructPlayerPB.RallyInfoListPB;
import com.yorha.proto.StructPlayer.RallyInfoList;
import com.yorha.proto.StructPlayerPB.RallyInfoPB;
import com.yorha.proto.StructPlayer.RallyInfo;

/**
 * <AUTHOR> auto gen
 */
public class RallyInfoListProp extends AbstractListNode<RallyInfoProp> {
    /**
     * Create a RallyInfoListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public RallyInfoListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to RallyInfoListProp
     *
     * @return new object
     */
    @Override
    public RallyInfoProp addEmptyValue() {
        final RallyInfoProp newProp = new RallyInfoProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RallyInfoListPB.Builder getCopyCsBuilder() {
        final RallyInfoListPB.Builder builder = RallyInfoListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(RallyInfoListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return RallyInfoListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final RallyInfoProp v : this) {
            final RallyInfoPB.Builder itemBuilder = RallyInfoPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return RallyInfoListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(RallyInfoListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return RallyInfoListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(RallyInfoListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (RallyInfoPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return RallyInfoListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(RallyInfoListPB proto) {
        return mergeFromCs(proto);
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RallyInfoList.Builder getCopySsBuilder() {
        final RallyInfoList.Builder builder = RallyInfoList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(RallyInfoList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return RallyInfoListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final RallyInfoProp v : this) {
            final RallyInfo.Builder itemBuilder = RallyInfo.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return RallyInfoListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(RallyInfoList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return RallyInfoListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(RallyInfoList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (RallyInfo v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return RallyInfoListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(RallyInfoList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        RallyInfoList.Builder builder = RallyInfoList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
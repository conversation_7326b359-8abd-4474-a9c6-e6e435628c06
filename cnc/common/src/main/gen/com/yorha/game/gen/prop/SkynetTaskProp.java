package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructSkynet.SkynetTask;
import com.yorha.proto.StructSkynetPB.SkynetTaskPB;


/**
 * <AUTHOR> auto gen
 */
public class SkynetTaskProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_SKYNETTASKID = 1;
    public static final int FIELD_INDEX_SKYNETTASKSTATUS = 2;
    public static final int FIELD_INDEX_RECEIVETSMS = 3;
    public static final int FIELD_INDEX_DOINGTSMS = 4;
    public static final int FIELD_INDEX_MONSTERID = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private int id = Constant.DEFAULT_INT_VALUE;
    private int skynetTaskId = Constant.DEFAULT_INT_VALUE;
    private SkynetTaskStatus skynetTaskStatus = SkynetTaskStatus.forNumber(0);
    private long receiveTsMs = Constant.DEFAULT_LONG_VALUE;
    private long doingTsMs = Constant.DEFAULT_LONG_VALUE;
    private long monsterId = Constant.DEFAULT_LONG_VALUE;

    public SkynetTaskProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SkynetTaskProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public int getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public SkynetTaskProp setId(int id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(int id) {
        this.id = id;
    }

    /**
     * get skynetTaskId
     *
     * @return skynetTaskId value
     */
    public int getSkynetTaskId() {
        return this.skynetTaskId;
    }

    /**
     * set skynetTaskId && set marked
     *
     * @param skynetTaskId new value
     * @return current object
     */
    public SkynetTaskProp setSkynetTaskId(int skynetTaskId) {
        if (this.skynetTaskId != skynetTaskId) {
            this.mark(FIELD_INDEX_SKYNETTASKID);
            this.skynetTaskId = skynetTaskId;
        }
        return this;
    }

    /**
     * inner set skynetTaskId
     *
     * @param skynetTaskId new value
     */
    private void innerSetSkynetTaskId(int skynetTaskId) {
        this.skynetTaskId = skynetTaskId;
    }

    /**
     * get skynetTaskStatus
     *
     * @return skynetTaskStatus value
     */
    public SkynetTaskStatus getSkynetTaskStatus() {
        return this.skynetTaskStatus;
    }

    /**
     * set skynetTaskStatus && set marked
     *
     * @param skynetTaskStatus new value
     * @return current object
     */
    public SkynetTaskProp setSkynetTaskStatus(SkynetTaskStatus skynetTaskStatus) {
        if (skynetTaskStatus == null) {
            throw new NullPointerException();
        }
        if (this.skynetTaskStatus != skynetTaskStatus) {
            this.mark(FIELD_INDEX_SKYNETTASKSTATUS);
            this.skynetTaskStatus = skynetTaskStatus;
        }
        return this;
    }

    /**
     * inner set skynetTaskStatus
     *
     * @param skynetTaskStatus new value
     */
    private void innerSetSkynetTaskStatus(SkynetTaskStatus skynetTaskStatus) {
        this.skynetTaskStatus = skynetTaskStatus;
    }

    /**
     * get receiveTsMs
     *
     * @return receiveTsMs value
     */
    public long getReceiveTsMs() {
        return this.receiveTsMs;
    }

    /**
     * set receiveTsMs && set marked
     *
     * @param receiveTsMs new value
     * @return current object
     */
    public SkynetTaskProp setReceiveTsMs(long receiveTsMs) {
        if (this.receiveTsMs != receiveTsMs) {
            this.mark(FIELD_INDEX_RECEIVETSMS);
            this.receiveTsMs = receiveTsMs;
        }
        return this;
    }

    /**
     * inner set receiveTsMs
     *
     * @param receiveTsMs new value
     */
    private void innerSetReceiveTsMs(long receiveTsMs) {
        this.receiveTsMs = receiveTsMs;
    }

    /**
     * get doingTsMs
     *
     * @return doingTsMs value
     */
    public long getDoingTsMs() {
        return this.doingTsMs;
    }

    /**
     * set doingTsMs && set marked
     *
     * @param doingTsMs new value
     * @return current object
     */
    public SkynetTaskProp setDoingTsMs(long doingTsMs) {
        if (this.doingTsMs != doingTsMs) {
            this.mark(FIELD_INDEX_DOINGTSMS);
            this.doingTsMs = doingTsMs;
        }
        return this;
    }

    /**
     * inner set doingTsMs
     *
     * @param doingTsMs new value
     */
    private void innerSetDoingTsMs(long doingTsMs) {
        this.doingTsMs = doingTsMs;
    }

    /**
     * get monsterId
     *
     * @return monsterId value
     */
    public long getMonsterId() {
        return this.monsterId;
    }

    /**
     * set monsterId && set marked
     *
     * @param monsterId new value
     * @return current object
     */
    public SkynetTaskProp setMonsterId(long monsterId) {
        if (this.monsterId != monsterId) {
            this.mark(FIELD_INDEX_MONSTERID);
            this.monsterId = monsterId;
        }
        return this;
    }

    /**
     * inner set monsterId
     *
     * @param monsterId new value
     */
    private void innerSetMonsterId(long monsterId) {
        this.monsterId = monsterId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SkynetTaskPB.Builder getCopyCsBuilder() {
        final SkynetTaskPB.Builder builder = SkynetTaskPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SkynetTaskPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getSkynetTaskId() != 0) {
            builder.setSkynetTaskId(this.getSkynetTaskId());
            fieldCnt++;
        }  else if (builder.hasSkynetTaskId()) {
            // 清理SkynetTaskId
            builder.clearSkynetTaskId();
            fieldCnt++;
        }
        if (this.getSkynetTaskStatus() != SkynetTaskStatus.forNumber(0)) {
            builder.setSkynetTaskStatus(this.getSkynetTaskStatus());
            fieldCnt++;
        }  else if (builder.hasSkynetTaskStatus()) {
            // 清理SkynetTaskStatus
            builder.clearSkynetTaskStatus();
            fieldCnt++;
        }
        if (this.getReceiveTsMs() != 0L) {
            builder.setReceiveTsMs(this.getReceiveTsMs());
            fieldCnt++;
        }  else if (builder.hasReceiveTsMs()) {
            // 清理ReceiveTsMs
            builder.clearReceiveTsMs();
            fieldCnt++;
        }
        if (this.getDoingTsMs() != 0L) {
            builder.setDoingTsMs(this.getDoingTsMs());
            fieldCnt++;
        }  else if (builder.hasDoingTsMs()) {
            // 清理DoingTsMs
            builder.clearDoingTsMs();
            fieldCnt++;
        }
        if (this.getMonsterId() != 0L) {
            builder.setMonsterId(this.getMonsterId());
            fieldCnt++;
        }  else if (builder.hasMonsterId()) {
            // 清理MonsterId
            builder.clearMonsterId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SkynetTaskPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKYNETTASKID)) {
            builder.setSkynetTaskId(this.getSkynetTaskId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKYNETTASKSTATUS)) {
            builder.setSkynetTaskStatus(this.getSkynetTaskStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RECEIVETSMS)) {
            builder.setReceiveTsMs(this.getReceiveTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DOINGTSMS)) {
            builder.setDoingTsMs(this.getDoingTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MONSTERID)) {
            builder.setMonsterId(this.getMonsterId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SkynetTaskPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKYNETTASKID)) {
            builder.setSkynetTaskId(this.getSkynetTaskId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKYNETTASKSTATUS)) {
            builder.setSkynetTaskStatus(this.getSkynetTaskStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RECEIVETSMS)) {
            builder.setReceiveTsMs(this.getReceiveTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DOINGTSMS)) {
            builder.setDoingTsMs(this.getDoingTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MONSTERID)) {
            builder.setMonsterId(this.getMonsterId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SkynetTaskPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkynetTaskId()) {
            this.innerSetSkynetTaskId(proto.getSkynetTaskId());
        } else {
            this.innerSetSkynetTaskId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkynetTaskStatus()) {
            this.innerSetSkynetTaskStatus(proto.getSkynetTaskStatus());
        } else {
            this.innerSetSkynetTaskStatus(SkynetTaskStatus.forNumber(0));
        }
        if (proto.hasReceiveTsMs()) {
            this.innerSetReceiveTsMs(proto.getReceiveTsMs());
        } else {
            this.innerSetReceiveTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasDoingTsMs()) {
            this.innerSetDoingTsMs(proto.getDoingTsMs());
        } else {
            this.innerSetDoingTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMonsterId()) {
            this.innerSetMonsterId(proto.getMonsterId());
        } else {
            this.innerSetMonsterId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return SkynetTaskProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SkynetTaskPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasSkynetTaskId()) {
            this.setSkynetTaskId(proto.getSkynetTaskId());
            fieldCnt++;
        }
        if (proto.hasSkynetTaskStatus()) {
            this.setSkynetTaskStatus(proto.getSkynetTaskStatus());
            fieldCnt++;
        }
        if (proto.hasReceiveTsMs()) {
            this.setReceiveTsMs(proto.getReceiveTsMs());
            fieldCnt++;
        }
        if (proto.hasDoingTsMs()) {
            this.setDoingTsMs(proto.getDoingTsMs());
            fieldCnt++;
        }
        if (proto.hasMonsterId()) {
            this.setMonsterId(proto.getMonsterId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SkynetTask.Builder getCopyDbBuilder() {
        final SkynetTask.Builder builder = SkynetTask.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SkynetTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getSkynetTaskId() != 0) {
            builder.setSkynetTaskId(this.getSkynetTaskId());
            fieldCnt++;
        }  else if (builder.hasSkynetTaskId()) {
            // 清理SkynetTaskId
            builder.clearSkynetTaskId();
            fieldCnt++;
        }
        if (this.getSkynetTaskStatus() != SkynetTaskStatus.forNumber(0)) {
            builder.setSkynetTaskStatus(this.getSkynetTaskStatus());
            fieldCnt++;
        }  else if (builder.hasSkynetTaskStatus()) {
            // 清理SkynetTaskStatus
            builder.clearSkynetTaskStatus();
            fieldCnt++;
        }
        if (this.getReceiveTsMs() != 0L) {
            builder.setReceiveTsMs(this.getReceiveTsMs());
            fieldCnt++;
        }  else if (builder.hasReceiveTsMs()) {
            // 清理ReceiveTsMs
            builder.clearReceiveTsMs();
            fieldCnt++;
        }
        if (this.getDoingTsMs() != 0L) {
            builder.setDoingTsMs(this.getDoingTsMs());
            fieldCnt++;
        }  else if (builder.hasDoingTsMs()) {
            // 清理DoingTsMs
            builder.clearDoingTsMs();
            fieldCnt++;
        }
        if (this.getMonsterId() != 0L) {
            builder.setMonsterId(this.getMonsterId());
            fieldCnt++;
        }  else if (builder.hasMonsterId()) {
            // 清理MonsterId
            builder.clearMonsterId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SkynetTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKYNETTASKID)) {
            builder.setSkynetTaskId(this.getSkynetTaskId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKYNETTASKSTATUS)) {
            builder.setSkynetTaskStatus(this.getSkynetTaskStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RECEIVETSMS)) {
            builder.setReceiveTsMs(this.getReceiveTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DOINGTSMS)) {
            builder.setDoingTsMs(this.getDoingTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MONSTERID)) {
            builder.setMonsterId(this.getMonsterId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SkynetTask proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkynetTaskId()) {
            this.innerSetSkynetTaskId(proto.getSkynetTaskId());
        } else {
            this.innerSetSkynetTaskId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkynetTaskStatus()) {
            this.innerSetSkynetTaskStatus(proto.getSkynetTaskStatus());
        } else {
            this.innerSetSkynetTaskStatus(SkynetTaskStatus.forNumber(0));
        }
        if (proto.hasReceiveTsMs()) {
            this.innerSetReceiveTsMs(proto.getReceiveTsMs());
        } else {
            this.innerSetReceiveTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasDoingTsMs()) {
            this.innerSetDoingTsMs(proto.getDoingTsMs());
        } else {
            this.innerSetDoingTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMonsterId()) {
            this.innerSetMonsterId(proto.getMonsterId());
        } else {
            this.innerSetMonsterId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return SkynetTaskProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SkynetTask proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasSkynetTaskId()) {
            this.setSkynetTaskId(proto.getSkynetTaskId());
            fieldCnt++;
        }
        if (proto.hasSkynetTaskStatus()) {
            this.setSkynetTaskStatus(proto.getSkynetTaskStatus());
            fieldCnt++;
        }
        if (proto.hasReceiveTsMs()) {
            this.setReceiveTsMs(proto.getReceiveTsMs());
            fieldCnt++;
        }
        if (proto.hasDoingTsMs()) {
            this.setDoingTsMs(proto.getDoingTsMs());
            fieldCnt++;
        }
        if (proto.hasMonsterId()) {
            this.setMonsterId(proto.getMonsterId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SkynetTask.Builder getCopySsBuilder() {
        final SkynetTask.Builder builder = SkynetTask.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SkynetTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getSkynetTaskId() != 0) {
            builder.setSkynetTaskId(this.getSkynetTaskId());
            fieldCnt++;
        }  else if (builder.hasSkynetTaskId()) {
            // 清理SkynetTaskId
            builder.clearSkynetTaskId();
            fieldCnt++;
        }
        if (this.getSkynetTaskStatus() != SkynetTaskStatus.forNumber(0)) {
            builder.setSkynetTaskStatus(this.getSkynetTaskStatus());
            fieldCnt++;
        }  else if (builder.hasSkynetTaskStatus()) {
            // 清理SkynetTaskStatus
            builder.clearSkynetTaskStatus();
            fieldCnt++;
        }
        if (this.getReceiveTsMs() != 0L) {
            builder.setReceiveTsMs(this.getReceiveTsMs());
            fieldCnt++;
        }  else if (builder.hasReceiveTsMs()) {
            // 清理ReceiveTsMs
            builder.clearReceiveTsMs();
            fieldCnt++;
        }
        if (this.getDoingTsMs() != 0L) {
            builder.setDoingTsMs(this.getDoingTsMs());
            fieldCnt++;
        }  else if (builder.hasDoingTsMs()) {
            // 清理DoingTsMs
            builder.clearDoingTsMs();
            fieldCnt++;
        }
        if (this.getMonsterId() != 0L) {
            builder.setMonsterId(this.getMonsterId());
            fieldCnt++;
        }  else if (builder.hasMonsterId()) {
            // 清理MonsterId
            builder.clearMonsterId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SkynetTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKYNETTASKID)) {
            builder.setSkynetTaskId(this.getSkynetTaskId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKYNETTASKSTATUS)) {
            builder.setSkynetTaskStatus(this.getSkynetTaskStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RECEIVETSMS)) {
            builder.setReceiveTsMs(this.getReceiveTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DOINGTSMS)) {
            builder.setDoingTsMs(this.getDoingTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MONSTERID)) {
            builder.setMonsterId(this.getMonsterId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SkynetTask proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkynetTaskId()) {
            this.innerSetSkynetTaskId(proto.getSkynetTaskId());
        } else {
            this.innerSetSkynetTaskId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkynetTaskStatus()) {
            this.innerSetSkynetTaskStatus(proto.getSkynetTaskStatus());
        } else {
            this.innerSetSkynetTaskStatus(SkynetTaskStatus.forNumber(0));
        }
        if (proto.hasReceiveTsMs()) {
            this.innerSetReceiveTsMs(proto.getReceiveTsMs());
        } else {
            this.innerSetReceiveTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasDoingTsMs()) {
            this.innerSetDoingTsMs(proto.getDoingTsMs());
        } else {
            this.innerSetDoingTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMonsterId()) {
            this.innerSetMonsterId(proto.getMonsterId());
        } else {
            this.innerSetMonsterId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return SkynetTaskProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SkynetTask proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasSkynetTaskId()) {
            this.setSkynetTaskId(proto.getSkynetTaskId());
            fieldCnt++;
        }
        if (proto.hasSkynetTaskStatus()) {
            this.setSkynetTaskStatus(proto.getSkynetTaskStatus());
            fieldCnt++;
        }
        if (proto.hasReceiveTsMs()) {
            this.setReceiveTsMs(proto.getReceiveTsMs());
            fieldCnt++;
        }
        if (proto.hasDoingTsMs()) {
            this.setDoingTsMs(proto.getDoingTsMs());
            fieldCnt++;
        }
        if (proto.hasMonsterId()) {
            this.setMonsterId(proto.getMonsterId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SkynetTask.Builder builder = SkynetTask.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SkynetTaskProp)) {
            return false;
        }
        final SkynetTaskProp otherNode = (SkynetTaskProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.skynetTaskId != otherNode.skynetTaskId) {
            return false;
        }
        if (this.skynetTaskStatus != otherNode.skynetTaskStatus) {
            return false;
        }
        if (this.receiveTsMs != otherNode.receiveTsMs) {
            return false;
        }
        if (this.doingTsMs != otherNode.doingTsMs) {
            return false;
        }
        if (this.monsterId != otherNode.monsterId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
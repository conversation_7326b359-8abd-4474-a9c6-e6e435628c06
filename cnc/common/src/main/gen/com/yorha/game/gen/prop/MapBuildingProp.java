package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.MapBuilding.MapBuildingEntity;
import com.yorha.proto.Basic;
import com.yorha.proto.MapBuilding;
import com.yorha.proto.Struct;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.MapBuildingPB.MapBuildingEntityPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.MapBuildingPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructBattlePB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class MapBuildingProp extends AbstractPropNode {

    public static final int FIELD_INDEX_POINT = 0;
    public static final int FIELD_INDEX_PARTID = 1;
    public static final int FIELD_INDEX_TEMPLATEID = 2;
    public static final int FIELD_INDEX_INNERARMY = 3;
    public static final int FIELD_INDEX_OCCUPYINFO = 4;
    public static final int FIELD_INDEX_TROOP = 5;
    public static final int FIELD_INDEX_BATTLE = 6;
    public static final int FIELD_INDEX_BUFFSYS = 7;
    public static final int FIELD_INDEX_CONSTRUCTINFO = 8;
    public static final int FIELD_INDEX_DEVBUFFSYS = 9;
    public static final int FIELD_INDEX_SAFEGUARD = 10;
    public static final int FIELD_INDEX_RECOMMENDSOLDIERTYPELIST = 11;
    public static final int FIELD_INDEX_PASSINGARMYNUM = 12;
    public static final int FIELD_INDEX_EXPRESSION = 13;
    public static final int FIELD_INDEX_KINGDOMMODEL = 14;
    public static final int FIELD_INDEX_ARROW = 15;

    public static final int FIELD_COUNT = 16;

    private long markBits0 = 0L;
    private PropertyChangeListener listener;

    private PointProp point = null;
    private int partId = Constant.DEFAULT_INT_VALUE;
    private int templateId = Constant.DEFAULT_INT_VALUE;
    private CityInnerArmyProp innerArmy = null;
    private OccupyInfoProp occupyinfo = null;
    private TroopProp troop = null;
    private BattleProp battle = null;
    private BuffSysProp buffSys = null;
    private ConstructInfoProp constructInfo = null;
    private SceneDevBuffSysProp devBuffSys = null;
    private SpecialSafeGuardProp safeGuard = null;
    private Int32ListProp recommendSoldierTypeList = null;
    private int passingArmyNum = Constant.DEFAULT_INT_VALUE;
    private ExpressionProp expression = null;
    private MapBuildingKingdomModelProp kingdomModel = null;
    private Int64ArmyArrowItemMapProp arrow = null;

    public MapBuildingProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MapBuildingProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get point
     *
     * @return point value
     */
    public PointProp getPoint() {
        if (this.point == null) {
            this.point = new PointProp(this, FIELD_INDEX_POINT);
        }
        return this.point;
    }

    /**
     * get partId
     *
     * @return partId value
     */
    public int getPartId() {
        return this.partId;
    }

    /**
     * set partId && set marked
     *
     * @param partId new value
     * @return current object
     */
    public MapBuildingProp setPartId(int partId) {
        if (this.partId != partId) {
            this.mark(FIELD_INDEX_PARTID);
            this.partId = partId;
        }
        return this;
    }

    /**
     * inner set partId
     *
     * @param partId new value
     */
    private void innerSetPartId(int partId) {
        this.partId = partId;
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public MapBuildingProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }

    /**
     * get innerArmy
     *
     * @return innerArmy value
     */
    public CityInnerArmyProp getInnerArmy() {
        if (this.innerArmy == null) {
            this.innerArmy = new CityInnerArmyProp(this, FIELD_INDEX_INNERARMY);
        }
        return this.innerArmy;
    }

    /**
     * get occupyinfo
     *
     * @return occupyinfo value
     */
    public OccupyInfoProp getOccupyinfo() {
        if (this.occupyinfo == null) {
            this.occupyinfo = new OccupyInfoProp(this, FIELD_INDEX_OCCUPYINFO);
        }
        return this.occupyinfo;
    }

    /**
     * get troop
     *
     * @return troop value
     */
    public TroopProp getTroop() {
        if (this.troop == null) {
            this.troop = new TroopProp(this, FIELD_INDEX_TROOP);
        }
        return this.troop;
    }

    /**
     * get battle
     *
     * @return battle value
     */
    public BattleProp getBattle() {
        if (this.battle == null) {
            this.battle = new BattleProp(this, FIELD_INDEX_BATTLE);
        }
        return this.battle;
    }

    /**
     * get buffSys
     *
     * @return buffSys value
     */
    public BuffSysProp getBuffSys() {
        if (this.buffSys == null) {
            this.buffSys = new BuffSysProp(this, FIELD_INDEX_BUFFSYS);
        }
        return this.buffSys;
    }

    /**
     * get constructInfo
     *
     * @return constructInfo value
     */
    public ConstructInfoProp getConstructInfo() {
        if (this.constructInfo == null) {
            this.constructInfo = new ConstructInfoProp(this, FIELD_INDEX_CONSTRUCTINFO);
        }
        return this.constructInfo;
    }

    /**
     * get devBuffSys
     *
     * @return devBuffSys value
     */
    public SceneDevBuffSysProp getDevBuffSys() {
        if (this.devBuffSys == null) {
            this.devBuffSys = new SceneDevBuffSysProp(this, FIELD_INDEX_DEVBUFFSYS);
        }
        return this.devBuffSys;
    }

    /**
     * get safeGuard
     *
     * @return safeGuard value
     */
    public SpecialSafeGuardProp getSafeGuard() {
        if (this.safeGuard == null) {
            this.safeGuard = new SpecialSafeGuardProp(this, FIELD_INDEX_SAFEGUARD);
        }
        return this.safeGuard;
    }

    /**
     * get recommendSoldierTypeList
     *
     * @return recommendSoldierTypeList value
     */
    public Int32ListProp getRecommendSoldierTypeList() {
        if (this.recommendSoldierTypeList == null) {
            this.recommendSoldierTypeList = new Int32ListProp(this, FIELD_INDEX_RECOMMENDSOLDIERTYPELIST);
        }
        return this.recommendSoldierTypeList;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addRecommendSoldierTypeList(Integer v) {
        this.getRecommendSoldierTypeList().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getRecommendSoldierTypeListIndex(int index) {
        return this.getRecommendSoldierTypeList().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeRecommendSoldierTypeList(Integer v) {
        if (this.recommendSoldierTypeList == null) {
            return null;
        }
        if(this.recommendSoldierTypeList.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getRecommendSoldierTypeListSize() {
        if (this.recommendSoldierTypeList == null) {
            return 0;
        }
        return this.recommendSoldierTypeList.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isRecommendSoldierTypeListEmpty() {
        if (this.recommendSoldierTypeList == null) {
            return true;
        }
        return this.getRecommendSoldierTypeList().isEmpty();
    }

    /**
     * clear list
     */
    public void clearRecommendSoldierTypeList() {
        this.getRecommendSoldierTypeList().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeRecommendSoldierTypeListIndex(int index) {
        return this.getRecommendSoldierTypeList().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setRecommendSoldierTypeListIndex(int index, Integer v) {
        return this.getRecommendSoldierTypeList().set(index, v);
    }
    /**
     * get passingArmyNum
     *
     * @return passingArmyNum value
     */
    public int getPassingArmyNum() {
        return this.passingArmyNum;
    }

    /**
     * set passingArmyNum && set marked
     *
     * @param passingArmyNum new value
     * @return current object
     */
    public MapBuildingProp setPassingArmyNum(int passingArmyNum) {
        if (this.passingArmyNum != passingArmyNum) {
            this.mark(FIELD_INDEX_PASSINGARMYNUM);
            this.passingArmyNum = passingArmyNum;
        }
        return this;
    }

    /**
     * inner set passingArmyNum
     *
     * @param passingArmyNum new value
     */
    private void innerSetPassingArmyNum(int passingArmyNum) {
        this.passingArmyNum = passingArmyNum;
    }

    /**
     * get expression
     *
     * @return expression value
     */
    public ExpressionProp getExpression() {
        if (this.expression == null) {
            this.expression = new ExpressionProp(this, FIELD_INDEX_EXPRESSION);
        }
        return this.expression;
    }

    /**
     * get kingdomModel
     *
     * @return kingdomModel value
     */
    public MapBuildingKingdomModelProp getKingdomModel() {
        if (this.kingdomModel == null) {
            this.kingdomModel = new MapBuildingKingdomModelProp(this, FIELD_INDEX_KINGDOMMODEL);
        }
        return this.kingdomModel;
    }

    /**
     * get arrow
     *
     * @return arrow value
     */
    public Int64ArmyArrowItemMapProp getArrow() {
        if (this.arrow == null) {
            this.arrow = new Int64ArmyArrowItemMapProp(this, FIELD_INDEX_ARROW);
        }
        return this.arrow;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putArrowV(ArmyArrowItemProp v) {
        this.getArrow().put(v.getArmyId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ArmyArrowItemProp addEmptyArrow(Long k) {
        return this.getArrow().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getArrowSize() {
        if (this.arrow == null) {
            return 0;
        }
        return this.arrow.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isArrowEmpty() {
        if (this.arrow == null) {
            return true;
        }
        return this.arrow.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ArmyArrowItemProp getArrowV(Long k) {
        if (this.arrow == null || !this.arrow.containsKey(k)) {
            return null;
        }
        return this.arrow.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearArrow() {
        if (this.arrow != null) {
            this.arrow.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeArrowV(Long k) {
        if (this.arrow != null) {
            this.arrow.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MapBuildingEntityPB.Builder getCopyCsBuilder() {
        final MapBuildingEntityPB.Builder builder = MapBuildingEntityPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MapBuildingEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.point != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.point.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getPartId() != 0) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }  else if (builder.hasPartId()) {
            // 清理PartId
            builder.clearPartId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.occupyinfo != null) {
            MapBuildingPB.OccupyInfoPB.Builder tmpBuilder = MapBuildingPB.OccupyInfoPB.newBuilder();
            final int tmpFieldCnt = this.occupyinfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOccupyinfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOccupyinfo();
            }
        }  else if (builder.hasOccupyinfo()) {
            // 清理Occupyinfo
            builder.clearOccupyinfo();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayerPB.TroopPB.Builder tmpBuilder = StructPlayerPB.TroopPB.newBuilder();
            final int tmpFieldCnt = this.troop.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.battle != null) {
            StructBattlePB.BattlePB.Builder tmpBuilder = StructBattlePB.BattlePB.newBuilder();
            final int tmpFieldCnt = this.battle.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattle();
            }
        }  else if (builder.hasBattle()) {
            // 清理Battle
            builder.clearBattle();
            fieldCnt++;
        }
        if (this.buffSys != null) {
            StructBattlePB.BuffSysPB.Builder tmpBuilder = StructBattlePB.BuffSysPB.newBuilder();
            final int tmpFieldCnt = this.buffSys.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuffSys();
            }
        }  else if (builder.hasBuffSys()) {
            // 清理BuffSys
            builder.clearBuffSys();
            fieldCnt++;
        }
        if (this.constructInfo != null) {
            MapBuildingPB.ConstructInfoPB.Builder tmpBuilder = MapBuildingPB.ConstructInfoPB.newBuilder();
            final int tmpFieldCnt = this.constructInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setConstructInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearConstructInfo();
            }
        }  else if (builder.hasConstructInfo()) {
            // 清理ConstructInfo
            builder.clearConstructInfo();
            fieldCnt++;
        }
        if (this.devBuffSys != null) {
            StructBattlePB.SceneDevBuffSysPB.Builder tmpBuilder = StructBattlePB.SceneDevBuffSysPB.newBuilder();
            final int tmpFieldCnt = this.devBuffSys.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSys();
            }
        }  else if (builder.hasDevBuffSys()) {
            // 清理DevBuffSys
            builder.clearDevBuffSys();
            fieldCnt++;
        }
        if (this.safeGuard != null) {
            StructPB.SpecialSafeGuardPB.Builder tmpBuilder = StructPB.SpecialSafeGuardPB.newBuilder();
            final int tmpFieldCnt = this.safeGuard.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSafeGuard(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSafeGuard();
            }
        }  else if (builder.hasSafeGuard()) {
            // 清理SafeGuard
            builder.clearSafeGuard();
            fieldCnt++;
        }
        if (this.recommendSoldierTypeList != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.recommendSoldierTypeList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRecommendSoldierTypeList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRecommendSoldierTypeList();
            }
        }  else if (builder.hasRecommendSoldierTypeList()) {
            // 清理RecommendSoldierTypeList
            builder.clearRecommendSoldierTypeList();
            fieldCnt++;
        }
        if (this.getPassingArmyNum() != 0) {
            builder.setPassingArmyNum(this.getPassingArmyNum());
            fieldCnt++;
        }  else if (builder.hasPassingArmyNum()) {
            // 清理PassingArmyNum
            builder.clearPassingArmyNum();
            fieldCnt++;
        }
        if (this.expression != null) {
            StructPB.ExpressionPB.Builder tmpBuilder = StructPB.ExpressionPB.newBuilder();
            final int tmpFieldCnt = this.expression.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExpression(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExpression();
            }
        }  else if (builder.hasExpression()) {
            // 清理Expression
            builder.clearExpression();
            fieldCnt++;
        }
        if (this.kingdomModel != null) {
            MapBuildingPB.MapBuildingKingdomModelPB.Builder tmpBuilder = MapBuildingPB.MapBuildingKingdomModelPB.newBuilder();
            final int tmpFieldCnt = this.kingdomModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKingdomModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKingdomModel();
            }
        }  else if (builder.hasKingdomModel()) {
            // 清理KingdomModel
            builder.clearKingdomModel();
            fieldCnt++;
        }
        if (this.arrow != null) {
            StructPB.Int64ArmyArrowItemMapPB.Builder tmpBuilder = StructPB.Int64ArmyArrowItemMapPB.newBuilder();
            final int tmpFieldCnt = this.arrow.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArrow(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArrow();
            }
        }  else if (builder.hasArrow()) {
            // 清理Arrow
            builder.clearArrow();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MapBuildingEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_PARTID)) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYINFO) && this.occupyinfo != null) {
            final boolean needClear = !builder.hasOccupyinfo();
            final int tmpFieldCnt = this.occupyinfo.copyChangeToCs(builder.getOccupyinfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOccupyinfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToCs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToCs(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUFFSYS) && this.buffSys != null) {
            final boolean needClear = !builder.hasBuffSys();
            final int tmpFieldCnt = this.buffSys.copyChangeToCs(builder.getBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_CONSTRUCTINFO) && this.constructInfo != null) {
            final boolean needClear = !builder.hasConstructInfo();
            final int tmpFieldCnt = this.constructInfo.copyChangeToCs(builder.getConstructInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearConstructInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToCs(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_SAFEGUARD) && this.safeGuard != null) {
            final boolean needClear = !builder.hasSafeGuard();
            final int tmpFieldCnt = this.safeGuard.copyChangeToCs(builder.getSafeGuardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSafeGuard();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECOMMENDSOLDIERTYPELIST) && this.recommendSoldierTypeList != null) {
            final boolean needClear = !builder.hasRecommendSoldierTypeList();
            final int tmpFieldCnt = this.recommendSoldierTypeList.copyChangeToCs(builder.getRecommendSoldierTypeListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRecommendSoldierTypeList();
            }
        }
        if (this.hasMark(FIELD_INDEX_PASSINGARMYNUM)) {
            builder.setPassingArmyNum(this.getPassingArmyNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            final boolean needClear = !builder.hasExpression();
            final int tmpFieldCnt = this.expression.copyChangeToCs(builder.getExpressionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpression();
            }
        }
        if (this.hasMark(FIELD_INDEX_KINGDOMMODEL) && this.kingdomModel != null) {
            final boolean needClear = !builder.hasKingdomModel();
            final int tmpFieldCnt = this.kingdomModel.copyChangeToCs(builder.getKingdomModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingdomModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARROW) && this.arrow != null) {
            final boolean needClear = !builder.hasArrow();
            final int tmpFieldCnt = this.arrow.copyChangeToCs(builder.getArrowBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArrow();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MapBuildingEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToAndClearDeleteKeysCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_PARTID)) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYINFO) && this.occupyinfo != null) {
            final boolean needClear = !builder.hasOccupyinfo();
            final int tmpFieldCnt = this.occupyinfo.copyChangeToAndClearDeleteKeysCs(builder.getOccupyinfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOccupyinfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToAndClearDeleteKeysCs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToAndClearDeleteKeysCs(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUFFSYS) && this.buffSys != null) {
            final boolean needClear = !builder.hasBuffSys();
            final int tmpFieldCnt = this.buffSys.copyChangeToAndClearDeleteKeysCs(builder.getBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_CONSTRUCTINFO) && this.constructInfo != null) {
            final boolean needClear = !builder.hasConstructInfo();
            final int tmpFieldCnt = this.constructInfo.copyChangeToAndClearDeleteKeysCs(builder.getConstructInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearConstructInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToAndClearDeleteKeysCs(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_SAFEGUARD) && this.safeGuard != null) {
            final boolean needClear = !builder.hasSafeGuard();
            final int tmpFieldCnt = this.safeGuard.copyChangeToAndClearDeleteKeysCs(builder.getSafeGuardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSafeGuard();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECOMMENDSOLDIERTYPELIST) && this.recommendSoldierTypeList != null) {
            final boolean needClear = !builder.hasRecommendSoldierTypeList();
            final int tmpFieldCnt = this.recommendSoldierTypeList.copyChangeToCs(builder.getRecommendSoldierTypeListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRecommendSoldierTypeList();
            }
        }
        if (this.hasMark(FIELD_INDEX_PASSINGARMYNUM)) {
            builder.setPassingArmyNum(this.getPassingArmyNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            final boolean needClear = !builder.hasExpression();
            final int tmpFieldCnt = this.expression.copyChangeToAndClearDeleteKeysCs(builder.getExpressionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpression();
            }
        }
        if (this.hasMark(FIELD_INDEX_KINGDOMMODEL) && this.kingdomModel != null) {
            final boolean needClear = !builder.hasKingdomModel();
            final int tmpFieldCnt = this.kingdomModel.copyChangeToAndClearDeleteKeysCs(builder.getKingdomModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingdomModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARROW) && this.arrow != null) {
            final boolean needClear = !builder.hasArrow();
            final int tmpFieldCnt = this.arrow.copyChangeToAndClearDeleteKeysCs(builder.getArrowBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArrow();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MapBuildingEntityPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromCs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromCs(proto.getPoint());
            }
        }
        if (proto.hasPartId()) {
            this.innerSetPartId(proto.getPartId());
        } else {
            this.innerSetPartId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOccupyinfo()) {
            this.getOccupyinfo().mergeFromCs(proto.getOccupyinfo());
        } else {
            if (this.occupyinfo != null) {
                this.occupyinfo.mergeFromCs(proto.getOccupyinfo());
            }
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromCs(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromCs(proto.getTroop());
            }
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeFromCs(proto.getBattle());
        } else {
            if (this.battle != null) {
                this.battle.mergeFromCs(proto.getBattle());
            }
        }
        if (proto.hasBuffSys()) {
            this.getBuffSys().mergeFromCs(proto.getBuffSys());
        } else {
            if (this.buffSys != null) {
                this.buffSys.mergeFromCs(proto.getBuffSys());
            }
        }
        if (proto.hasConstructInfo()) {
            this.getConstructInfo().mergeFromCs(proto.getConstructInfo());
        } else {
            if (this.constructInfo != null) {
                this.constructInfo.mergeFromCs(proto.getConstructInfo());
            }
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeFromCs(proto.getDevBuffSys());
        } else {
            if (this.devBuffSys != null) {
                this.devBuffSys.mergeFromCs(proto.getDevBuffSys());
            }
        }
        if (proto.hasSafeGuard()) {
            this.getSafeGuard().mergeFromCs(proto.getSafeGuard());
        } else {
            if (this.safeGuard != null) {
                this.safeGuard.mergeFromCs(proto.getSafeGuard());
            }
        }
        if (proto.hasRecommendSoldierTypeList()) {
            this.getRecommendSoldierTypeList().mergeFromCs(proto.getRecommendSoldierTypeList());
        } else {
            if (this.recommendSoldierTypeList != null) {
                this.recommendSoldierTypeList.mergeFromCs(proto.getRecommendSoldierTypeList());
            }
        }
        if (proto.hasPassingArmyNum()) {
            this.innerSetPassingArmyNum(proto.getPassingArmyNum());
        } else {
            this.innerSetPassingArmyNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeFromCs(proto.getExpression());
        } else {
            if (this.expression != null) {
                this.expression.mergeFromCs(proto.getExpression());
            }
        }
        if (proto.hasKingdomModel()) {
            this.getKingdomModel().mergeFromCs(proto.getKingdomModel());
        } else {
            if (this.kingdomModel != null) {
                this.kingdomModel.mergeFromCs(proto.getKingdomModel());
            }
        }
        if (proto.hasArrow()) {
            this.getArrow().mergeFromCs(proto.getArrow());
        } else {
            if (this.arrow != null) {
                this.arrow.mergeFromCs(proto.getArrow());
            }
        }
        this.markAll();
        return MapBuildingProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MapBuildingEntityPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromCs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasPartId()) {
            this.setPartId(proto.getPartId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasOccupyinfo()) {
            this.getOccupyinfo().mergeChangeFromCs(proto.getOccupyinfo());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromCs(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeChangeFromCs(proto.getBattle());
            fieldCnt++;
        }
        if (proto.hasBuffSys()) {
            this.getBuffSys().mergeChangeFromCs(proto.getBuffSys());
            fieldCnt++;
        }
        if (proto.hasConstructInfo()) {
            this.getConstructInfo().mergeChangeFromCs(proto.getConstructInfo());
            fieldCnt++;
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeChangeFromCs(proto.getDevBuffSys());
            fieldCnt++;
        }
        if (proto.hasSafeGuard()) {
            this.getSafeGuard().mergeChangeFromCs(proto.getSafeGuard());
            fieldCnt++;
        }
        if (proto.hasRecommendSoldierTypeList()) {
            this.getRecommendSoldierTypeList().mergeChangeFromCs(proto.getRecommendSoldierTypeList());
            fieldCnt++;
        }
        if (proto.hasPassingArmyNum()) {
            this.setPassingArmyNum(proto.getPassingArmyNum());
            fieldCnt++;
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeChangeFromCs(proto.getExpression());
            fieldCnt++;
        }
        if (proto.hasKingdomModel()) {
            this.getKingdomModel().mergeChangeFromCs(proto.getKingdomModel());
            fieldCnt++;
        }
        if (proto.hasArrow()) {
            this.getArrow().mergeChangeFromCs(proto.getArrow());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MapBuildingEntity.Builder getCopyDbBuilder() {
        final MapBuildingEntity.Builder builder = MapBuildingEntity.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MapBuildingEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPartId() != 0) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }  else if (builder.hasPartId()) {
            // 清理PartId
            builder.clearPartId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.innerArmy != null) {
            Struct.CityInnerArmy.Builder tmpBuilder = Struct.CityInnerArmy.newBuilder();
            final int tmpFieldCnt = this.innerArmy.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInnerArmy(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInnerArmy();
            }
        }  else if (builder.hasInnerArmy()) {
            // 清理InnerArmy
            builder.clearInnerArmy();
            fieldCnt++;
        }
        if (this.occupyinfo != null) {
            MapBuilding.OccupyInfo.Builder tmpBuilder = MapBuilding.OccupyInfo.newBuilder();
            final int tmpFieldCnt = this.occupyinfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOccupyinfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOccupyinfo();
            }
        }  else if (builder.hasOccupyinfo()) {
            // 清理Occupyinfo
            builder.clearOccupyinfo();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayer.Troop.Builder tmpBuilder = StructPlayer.Troop.newBuilder();
            final int tmpFieldCnt = this.troop.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.battle != null) {
            StructBattle.Battle.Builder tmpBuilder = StructBattle.Battle.newBuilder();
            final int tmpFieldCnt = this.battle.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattle();
            }
        }  else if (builder.hasBattle()) {
            // 清理Battle
            builder.clearBattle();
            fieldCnt++;
        }
        if (this.buffSys != null) {
            StructBattle.BuffSys.Builder tmpBuilder = StructBattle.BuffSys.newBuilder();
            final int tmpFieldCnt = this.buffSys.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuffSys();
            }
        }  else if (builder.hasBuffSys()) {
            // 清理BuffSys
            builder.clearBuffSys();
            fieldCnt++;
        }
        if (this.constructInfo != null) {
            MapBuilding.ConstructInfo.Builder tmpBuilder = MapBuilding.ConstructInfo.newBuilder();
            final int tmpFieldCnt = this.constructInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setConstructInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearConstructInfo();
            }
        }  else if (builder.hasConstructInfo()) {
            // 清理ConstructInfo
            builder.clearConstructInfo();
            fieldCnt++;
        }
        if (this.devBuffSys != null) {
            StructBattle.SceneDevBuffSys.Builder tmpBuilder = StructBattle.SceneDevBuffSys.newBuilder();
            final int tmpFieldCnt = this.devBuffSys.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSys();
            }
        }  else if (builder.hasDevBuffSys()) {
            // 清理DevBuffSys
            builder.clearDevBuffSys();
            fieldCnt++;
        }
        if (this.safeGuard != null) {
            Struct.SpecialSafeGuard.Builder tmpBuilder = Struct.SpecialSafeGuard.newBuilder();
            final int tmpFieldCnt = this.safeGuard.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSafeGuard(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSafeGuard();
            }
        }  else if (builder.hasSafeGuard()) {
            // 清理SafeGuard
            builder.clearSafeGuard();
            fieldCnt++;
        }
        if (this.recommendSoldierTypeList != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.recommendSoldierTypeList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRecommendSoldierTypeList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRecommendSoldierTypeList();
            }
        }  else if (builder.hasRecommendSoldierTypeList()) {
            // 清理RecommendSoldierTypeList
            builder.clearRecommendSoldierTypeList();
            fieldCnt++;
        }
        if (this.expression != null) {
            Struct.Expression.Builder tmpBuilder = Struct.Expression.newBuilder();
            final int tmpFieldCnt = this.expression.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExpression(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExpression();
            }
        }  else if (builder.hasExpression()) {
            // 清理Expression
            builder.clearExpression();
            fieldCnt++;
        }
        if (this.kingdomModel != null) {
            MapBuilding.MapBuildingKingdomModel.Builder tmpBuilder = MapBuilding.MapBuildingKingdomModel.newBuilder();
            final int tmpFieldCnt = this.kingdomModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKingdomModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKingdomModel();
            }
        }  else if (builder.hasKingdomModel()) {
            // 清理KingdomModel
            builder.clearKingdomModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MapBuildingEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PARTID)) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INNERARMY) && this.innerArmy != null) {
            final boolean needClear = !builder.hasInnerArmy();
            final int tmpFieldCnt = this.innerArmy.copyChangeToDb(builder.getInnerArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerArmy();
            }
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYINFO) && this.occupyinfo != null) {
            final boolean needClear = !builder.hasOccupyinfo();
            final int tmpFieldCnt = this.occupyinfo.copyChangeToDb(builder.getOccupyinfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOccupyinfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToDb(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToDb(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUFFSYS) && this.buffSys != null) {
            final boolean needClear = !builder.hasBuffSys();
            final int tmpFieldCnt = this.buffSys.copyChangeToDb(builder.getBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_CONSTRUCTINFO) && this.constructInfo != null) {
            final boolean needClear = !builder.hasConstructInfo();
            final int tmpFieldCnt = this.constructInfo.copyChangeToDb(builder.getConstructInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearConstructInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToDb(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_SAFEGUARD) && this.safeGuard != null) {
            final boolean needClear = !builder.hasSafeGuard();
            final int tmpFieldCnt = this.safeGuard.copyChangeToDb(builder.getSafeGuardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSafeGuard();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECOMMENDSOLDIERTYPELIST) && this.recommendSoldierTypeList != null) {
            final boolean needClear = !builder.hasRecommendSoldierTypeList();
            final int tmpFieldCnt = this.recommendSoldierTypeList.copyChangeToDb(builder.getRecommendSoldierTypeListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRecommendSoldierTypeList();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            final boolean needClear = !builder.hasExpression();
            final int tmpFieldCnt = this.expression.copyChangeToDb(builder.getExpressionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpression();
            }
        }
        if (this.hasMark(FIELD_INDEX_KINGDOMMODEL) && this.kingdomModel != null) {
            final boolean needClear = !builder.hasKingdomModel();
            final int tmpFieldCnt = this.kingdomModel.copyChangeToDb(builder.getKingdomModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingdomModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MapBuildingEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPartId()) {
            this.innerSetPartId(proto.getPartId());
        } else {
            this.innerSetPartId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasInnerArmy()) {
            this.getInnerArmy().mergeFromDb(proto.getInnerArmy());
        } else {
            if (this.innerArmy != null) {
                this.innerArmy.mergeFromDb(proto.getInnerArmy());
            }
        }
        if (proto.hasOccupyinfo()) {
            this.getOccupyinfo().mergeFromDb(proto.getOccupyinfo());
        } else {
            if (this.occupyinfo != null) {
                this.occupyinfo.mergeFromDb(proto.getOccupyinfo());
            }
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromDb(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromDb(proto.getTroop());
            }
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeFromDb(proto.getBattle());
        } else {
            if (this.battle != null) {
                this.battle.mergeFromDb(proto.getBattle());
            }
        }
        if (proto.hasBuffSys()) {
            this.getBuffSys().mergeFromDb(proto.getBuffSys());
        } else {
            if (this.buffSys != null) {
                this.buffSys.mergeFromDb(proto.getBuffSys());
            }
        }
        if (proto.hasConstructInfo()) {
            this.getConstructInfo().mergeFromDb(proto.getConstructInfo());
        } else {
            if (this.constructInfo != null) {
                this.constructInfo.mergeFromDb(proto.getConstructInfo());
            }
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeFromDb(proto.getDevBuffSys());
        } else {
            if (this.devBuffSys != null) {
                this.devBuffSys.mergeFromDb(proto.getDevBuffSys());
            }
        }
        if (proto.hasSafeGuard()) {
            this.getSafeGuard().mergeFromDb(proto.getSafeGuard());
        } else {
            if (this.safeGuard != null) {
                this.safeGuard.mergeFromDb(proto.getSafeGuard());
            }
        }
        if (proto.hasRecommendSoldierTypeList()) {
            this.getRecommendSoldierTypeList().mergeFromDb(proto.getRecommendSoldierTypeList());
        } else {
            if (this.recommendSoldierTypeList != null) {
                this.recommendSoldierTypeList.mergeFromDb(proto.getRecommendSoldierTypeList());
            }
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeFromDb(proto.getExpression());
        } else {
            if (this.expression != null) {
                this.expression.mergeFromDb(proto.getExpression());
            }
        }
        if (proto.hasKingdomModel()) {
            this.getKingdomModel().mergeFromDb(proto.getKingdomModel());
        } else {
            if (this.kingdomModel != null) {
                this.kingdomModel.mergeFromDb(proto.getKingdomModel());
            }
        }
        this.markAll();
        return MapBuildingProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MapBuildingEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPartId()) {
            this.setPartId(proto.getPartId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasInnerArmy()) {
            this.getInnerArmy().mergeChangeFromDb(proto.getInnerArmy());
            fieldCnt++;
        }
        if (proto.hasOccupyinfo()) {
            this.getOccupyinfo().mergeChangeFromDb(proto.getOccupyinfo());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromDb(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeChangeFromDb(proto.getBattle());
            fieldCnt++;
        }
        if (proto.hasBuffSys()) {
            this.getBuffSys().mergeChangeFromDb(proto.getBuffSys());
            fieldCnt++;
        }
        if (proto.hasConstructInfo()) {
            this.getConstructInfo().mergeChangeFromDb(proto.getConstructInfo());
            fieldCnt++;
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeChangeFromDb(proto.getDevBuffSys());
            fieldCnt++;
        }
        if (proto.hasSafeGuard()) {
            this.getSafeGuard().mergeChangeFromDb(proto.getSafeGuard());
            fieldCnt++;
        }
        if (proto.hasRecommendSoldierTypeList()) {
            this.getRecommendSoldierTypeList().mergeChangeFromDb(proto.getRecommendSoldierTypeList());
            fieldCnt++;
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeChangeFromDb(proto.getExpression());
            fieldCnt++;
        }
        if (proto.hasKingdomModel()) {
            this.getKingdomModel().mergeChangeFromDb(proto.getKingdomModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MapBuildingEntity.Builder getCopySsBuilder() {
        final MapBuildingEntity.Builder builder = MapBuildingEntity.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MapBuildingEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getPartId() != 0) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }  else if (builder.hasPartId()) {
            // 清理PartId
            builder.clearPartId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.innerArmy != null) {
            Struct.CityInnerArmy.Builder tmpBuilder = Struct.CityInnerArmy.newBuilder();
            final int tmpFieldCnt = this.innerArmy.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInnerArmy(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInnerArmy();
            }
        }  else if (builder.hasInnerArmy()) {
            // 清理InnerArmy
            builder.clearInnerArmy();
            fieldCnt++;
        }
        if (this.occupyinfo != null) {
            MapBuilding.OccupyInfo.Builder tmpBuilder = MapBuilding.OccupyInfo.newBuilder();
            final int tmpFieldCnt = this.occupyinfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOccupyinfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOccupyinfo();
            }
        }  else if (builder.hasOccupyinfo()) {
            // 清理Occupyinfo
            builder.clearOccupyinfo();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayer.Troop.Builder tmpBuilder = StructPlayer.Troop.newBuilder();
            final int tmpFieldCnt = this.troop.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.battle != null) {
            StructBattle.Battle.Builder tmpBuilder = StructBattle.Battle.newBuilder();
            final int tmpFieldCnt = this.battle.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattle();
            }
        }  else if (builder.hasBattle()) {
            // 清理Battle
            builder.clearBattle();
            fieldCnt++;
        }
        if (this.buffSys != null) {
            StructBattle.BuffSys.Builder tmpBuilder = StructBattle.BuffSys.newBuilder();
            final int tmpFieldCnt = this.buffSys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuffSys();
            }
        }  else if (builder.hasBuffSys()) {
            // 清理BuffSys
            builder.clearBuffSys();
            fieldCnt++;
        }
        if (this.constructInfo != null) {
            MapBuilding.ConstructInfo.Builder tmpBuilder = MapBuilding.ConstructInfo.newBuilder();
            final int tmpFieldCnt = this.constructInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setConstructInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearConstructInfo();
            }
        }  else if (builder.hasConstructInfo()) {
            // 清理ConstructInfo
            builder.clearConstructInfo();
            fieldCnt++;
        }
        if (this.devBuffSys != null) {
            StructBattle.SceneDevBuffSys.Builder tmpBuilder = StructBattle.SceneDevBuffSys.newBuilder();
            final int tmpFieldCnt = this.devBuffSys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSys();
            }
        }  else if (builder.hasDevBuffSys()) {
            // 清理DevBuffSys
            builder.clearDevBuffSys();
            fieldCnt++;
        }
        if (this.safeGuard != null) {
            Struct.SpecialSafeGuard.Builder tmpBuilder = Struct.SpecialSafeGuard.newBuilder();
            final int tmpFieldCnt = this.safeGuard.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSafeGuard(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSafeGuard();
            }
        }  else if (builder.hasSafeGuard()) {
            // 清理SafeGuard
            builder.clearSafeGuard();
            fieldCnt++;
        }
        if (this.recommendSoldierTypeList != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.recommendSoldierTypeList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRecommendSoldierTypeList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRecommendSoldierTypeList();
            }
        }  else if (builder.hasRecommendSoldierTypeList()) {
            // 清理RecommendSoldierTypeList
            builder.clearRecommendSoldierTypeList();
            fieldCnt++;
        }
        if (this.getPassingArmyNum() != 0) {
            builder.setPassingArmyNum(this.getPassingArmyNum());
            fieldCnt++;
        }  else if (builder.hasPassingArmyNum()) {
            // 清理PassingArmyNum
            builder.clearPassingArmyNum();
            fieldCnt++;
        }
        if (this.expression != null) {
            Struct.Expression.Builder tmpBuilder = Struct.Expression.newBuilder();
            final int tmpFieldCnt = this.expression.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExpression(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExpression();
            }
        }  else if (builder.hasExpression()) {
            // 清理Expression
            builder.clearExpression();
            fieldCnt++;
        }
        if (this.kingdomModel != null) {
            MapBuilding.MapBuildingKingdomModel.Builder tmpBuilder = MapBuilding.MapBuildingKingdomModel.newBuilder();
            final int tmpFieldCnt = this.kingdomModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKingdomModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKingdomModel();
            }
        }  else if (builder.hasKingdomModel()) {
            // 清理KingdomModel
            builder.clearKingdomModel();
            fieldCnt++;
        }
        if (this.arrow != null) {
            Struct.Int64ArmyArrowItemMap.Builder tmpBuilder = Struct.Int64ArmyArrowItemMap.newBuilder();
            final int tmpFieldCnt = this.arrow.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArrow(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArrow();
            }
        }  else if (builder.hasArrow()) {
            // 清理Arrow
            builder.clearArrow();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MapBuildingEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToSs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_PARTID)) {
            builder.setPartId(this.getPartId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_INNERARMY) && this.innerArmy != null) {
            final boolean needClear = !builder.hasInnerArmy();
            final int tmpFieldCnt = this.innerArmy.copyChangeToSs(builder.getInnerArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInnerArmy();
            }
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYINFO) && this.occupyinfo != null) {
            final boolean needClear = !builder.hasOccupyinfo();
            final int tmpFieldCnt = this.occupyinfo.copyChangeToSs(builder.getOccupyinfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOccupyinfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToSs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToSs(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUFFSYS) && this.buffSys != null) {
            final boolean needClear = !builder.hasBuffSys();
            final int tmpFieldCnt = this.buffSys.copyChangeToSs(builder.getBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_CONSTRUCTINFO) && this.constructInfo != null) {
            final boolean needClear = !builder.hasConstructInfo();
            final int tmpFieldCnt = this.constructInfo.copyChangeToSs(builder.getConstructInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearConstructInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToSs(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_SAFEGUARD) && this.safeGuard != null) {
            final boolean needClear = !builder.hasSafeGuard();
            final int tmpFieldCnt = this.safeGuard.copyChangeToSs(builder.getSafeGuardBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSafeGuard();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECOMMENDSOLDIERTYPELIST) && this.recommendSoldierTypeList != null) {
            final boolean needClear = !builder.hasRecommendSoldierTypeList();
            final int tmpFieldCnt = this.recommendSoldierTypeList.copyChangeToSs(builder.getRecommendSoldierTypeListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRecommendSoldierTypeList();
            }
        }
        if (this.hasMark(FIELD_INDEX_PASSINGARMYNUM)) {
            builder.setPassingArmyNum(this.getPassingArmyNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            final boolean needClear = !builder.hasExpression();
            final int tmpFieldCnt = this.expression.copyChangeToSs(builder.getExpressionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpression();
            }
        }
        if (this.hasMark(FIELD_INDEX_KINGDOMMODEL) && this.kingdomModel != null) {
            final boolean needClear = !builder.hasKingdomModel();
            final int tmpFieldCnt = this.kingdomModel.copyChangeToSs(builder.getKingdomModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingdomModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARROW) && this.arrow != null) {
            final boolean needClear = !builder.hasArrow();
            final int tmpFieldCnt = this.arrow.copyChangeToSs(builder.getArrowBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArrow();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MapBuildingEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromSs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromSs(proto.getPoint());
            }
        }
        if (proto.hasPartId()) {
            this.innerSetPartId(proto.getPartId());
        } else {
            this.innerSetPartId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasInnerArmy()) {
            this.getInnerArmy().mergeFromSs(proto.getInnerArmy());
        } else {
            if (this.innerArmy != null) {
                this.innerArmy.mergeFromSs(proto.getInnerArmy());
            }
        }
        if (proto.hasOccupyinfo()) {
            this.getOccupyinfo().mergeFromSs(proto.getOccupyinfo());
        } else {
            if (this.occupyinfo != null) {
                this.occupyinfo.mergeFromSs(proto.getOccupyinfo());
            }
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromSs(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromSs(proto.getTroop());
            }
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeFromSs(proto.getBattle());
        } else {
            if (this.battle != null) {
                this.battle.mergeFromSs(proto.getBattle());
            }
        }
        if (proto.hasBuffSys()) {
            this.getBuffSys().mergeFromSs(proto.getBuffSys());
        } else {
            if (this.buffSys != null) {
                this.buffSys.mergeFromSs(proto.getBuffSys());
            }
        }
        if (proto.hasConstructInfo()) {
            this.getConstructInfo().mergeFromSs(proto.getConstructInfo());
        } else {
            if (this.constructInfo != null) {
                this.constructInfo.mergeFromSs(proto.getConstructInfo());
            }
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeFromSs(proto.getDevBuffSys());
        } else {
            if (this.devBuffSys != null) {
                this.devBuffSys.mergeFromSs(proto.getDevBuffSys());
            }
        }
        if (proto.hasSafeGuard()) {
            this.getSafeGuard().mergeFromSs(proto.getSafeGuard());
        } else {
            if (this.safeGuard != null) {
                this.safeGuard.mergeFromSs(proto.getSafeGuard());
            }
        }
        if (proto.hasRecommendSoldierTypeList()) {
            this.getRecommendSoldierTypeList().mergeFromSs(proto.getRecommendSoldierTypeList());
        } else {
            if (this.recommendSoldierTypeList != null) {
                this.recommendSoldierTypeList.mergeFromSs(proto.getRecommendSoldierTypeList());
            }
        }
        if (proto.hasPassingArmyNum()) {
            this.innerSetPassingArmyNum(proto.getPassingArmyNum());
        } else {
            this.innerSetPassingArmyNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeFromSs(proto.getExpression());
        } else {
            if (this.expression != null) {
                this.expression.mergeFromSs(proto.getExpression());
            }
        }
        if (proto.hasKingdomModel()) {
            this.getKingdomModel().mergeFromSs(proto.getKingdomModel());
        } else {
            if (this.kingdomModel != null) {
                this.kingdomModel.mergeFromSs(proto.getKingdomModel());
            }
        }
        if (proto.hasArrow()) {
            this.getArrow().mergeFromSs(proto.getArrow());
        } else {
            if (this.arrow != null) {
                this.arrow.mergeFromSs(proto.getArrow());
            }
        }
        this.markAll();
        return MapBuildingProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MapBuildingEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromSs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasPartId()) {
            this.setPartId(proto.getPartId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasInnerArmy()) {
            this.getInnerArmy().mergeChangeFromSs(proto.getInnerArmy());
            fieldCnt++;
        }
        if (proto.hasOccupyinfo()) {
            this.getOccupyinfo().mergeChangeFromSs(proto.getOccupyinfo());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromSs(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeChangeFromSs(proto.getBattle());
            fieldCnt++;
        }
        if (proto.hasBuffSys()) {
            this.getBuffSys().mergeChangeFromSs(proto.getBuffSys());
            fieldCnt++;
        }
        if (proto.hasConstructInfo()) {
            this.getConstructInfo().mergeChangeFromSs(proto.getConstructInfo());
            fieldCnt++;
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeChangeFromSs(proto.getDevBuffSys());
            fieldCnt++;
        }
        if (proto.hasSafeGuard()) {
            this.getSafeGuard().mergeChangeFromSs(proto.getSafeGuard());
            fieldCnt++;
        }
        if (proto.hasRecommendSoldierTypeList()) {
            this.getRecommendSoldierTypeList().mergeChangeFromSs(proto.getRecommendSoldierTypeList());
            fieldCnt++;
        }
        if (proto.hasPassingArmyNum()) {
            this.setPassingArmyNum(proto.getPassingArmyNum());
            fieldCnt++;
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeChangeFromSs(proto.getExpression());
            fieldCnt++;
        }
        if (proto.hasKingdomModel()) {
            this.getKingdomModel().mergeChangeFromSs(proto.getKingdomModel());
            fieldCnt++;
        }
        if (proto.hasArrow()) {
            this.getArrow().mergeChangeFromSs(proto.getArrow());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MapBuildingEntity.Builder builder = MapBuildingEntity.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            this.point.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_INNERARMY) && this.innerArmy != null) {
            this.innerArmy.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYINFO) && this.occupyinfo != null) {
            this.occupyinfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            this.troop.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            this.battle.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BUFFSYS) && this.buffSys != null) {
            this.buffSys.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CONSTRUCTINFO) && this.constructInfo != null) {
            this.constructInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            this.devBuffSys.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SAFEGUARD) && this.safeGuard != null) {
            this.safeGuard.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RECOMMENDSOLDIERTYPELIST) && this.recommendSoldierTypeList != null) {
            this.recommendSoldierTypeList.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            this.expression.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_KINGDOMMODEL) && this.kingdomModel != null) {
            this.kingdomModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ARROW) && this.arrow != null) {
            this.arrow.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.point != null) {
            this.point.markAll();
        }
        if (this.innerArmy != null) {
            this.innerArmy.markAll();
        }
        if (this.occupyinfo != null) {
            this.occupyinfo.markAll();
        }
        if (this.troop != null) {
            this.troop.markAll();
        }
        if (this.battle != null) {
            this.battle.markAll();
        }
        if (this.buffSys != null) {
            this.buffSys.markAll();
        }
        if (this.constructInfo != null) {
            this.constructInfo.markAll();
        }
        if (this.devBuffSys != null) {
            this.devBuffSys.markAll();
        }
        if (this.safeGuard != null) {
            this.safeGuard.markAll();
        }
        if (this.recommendSoldierTypeList != null) {
            this.recommendSoldierTypeList.markAll();
        }
        if (this.expression != null) {
            this.expression.markAll();
        }
        if (this.kingdomModel != null) {
            this.kingdomModel.markAll();
        }
        if (this.arrow != null) {
            this.arrow.markAll();
        }
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("MapBuildingProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("MapBuildingProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MapBuildingProp)) {
            return false;
        }
        final MapBuildingProp otherNode = (MapBuildingProp) node;
        if (!this.getPoint().compareDataTo(otherNode.getPoint())) {
            return false;
        }
        if (this.partId != otherNode.partId) {
            return false;
        }
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        if (!this.getInnerArmy().compareDataTo(otherNode.getInnerArmy())) {
            return false;
        }
        if (!this.getOccupyinfo().compareDataTo(otherNode.getOccupyinfo())) {
            return false;
        }
        if (!this.getTroop().compareDataTo(otherNode.getTroop())) {
            return false;
        }
        if (!this.getBattle().compareDataTo(otherNode.getBattle())) {
            return false;
        }
        if (!this.getBuffSys().compareDataTo(otherNode.getBuffSys())) {
            return false;
        }
        if (!this.getConstructInfo().compareDataTo(otherNode.getConstructInfo())) {
            return false;
        }
        if (!this.getDevBuffSys().compareDataTo(otherNode.getDevBuffSys())) {
            return false;
        }
        if (!this.getSafeGuard().compareDataTo(otherNode.getSafeGuard())) {
            return false;
        }
        if (!this.getRecommendSoldierTypeList().compareDataTo(otherNode.getRecommendSoldierTypeList())) {
            return false;
        }
        if (this.passingArmyNum != otherNode.passingArmyNum) {
            return false;
        }
        if (!this.getExpression().compareDataTo(otherNode.getExpression())) {
            return false;
        }
        if (!this.getKingdomModel().compareDataTo(otherNode.getKingdomModel())) {
            return false;
        }
        if (!this.getArrow().compareDataTo(otherNode.getArrow())) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    public static MapBuildingProp of(MapBuildingEntity fullAttrDb, MapBuildingEntity changeAttrDb) {
        MapBuildingProp prop = new MapBuildingProp();
        prop.mergeFromDb(fullAttrDb);
        prop.mergeChangeFromDb(changeAttrDb);
        prop.unMarkAll();
        return prop;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 48;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
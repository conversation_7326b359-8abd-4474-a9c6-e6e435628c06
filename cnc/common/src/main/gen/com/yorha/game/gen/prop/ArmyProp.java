package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Army.ArmyEntity;
import com.yorha.proto.Army;
import com.yorha.proto.Struct;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructClan;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.ArmyPB.ArmyEntityPB;
import com.yorha.proto.ArmyPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructBattlePB;
import com.yorha.proto.StructClanPB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class ArmyProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ARMYSTATE = 0;
    public static final int FIELD_INDEX_TROOP = 1;
    public static final int FIELD_INDEX_MOVE = 2;
    public static final int FIELD_INDEX_OWNERID = 3;
    public static final int FIELD_INDEX_FORMATIONID = 4;
    public static final int FIELD_INDEX_BATTLE = 5;
    public static final int FIELD_INDEX_BUFF = 6;
    public static final int FIELD_INDEX_CAMP = 7;
    public static final int FIELD_INDEX_CLANSNAME = 8;
    public static final int FIELD_INDEX_ENTERSTATETS = 9;
    public static final int FIELD_INDEX_CLANID = 10;
    public static final int FIELD_INDEX_HPMAX = 11;
    public static final int FIELD_INDEX_PICKUPINFO = 12;
    public static final int FIELD_INDEX_ATTACHID = 13;
    public static final int FIELD_INDEX_ATTACHSTATE = 14;
    public static final int FIELD_INDEX_TRANSPORTPLANE = 15;
    public static final int FIELD_INDEX_RESOURCE = 16;
    public static final int FIELD_INDEX_CARDHEAD = 17;
    public static final int FIELD_INDEX_CLANFLAG = 18;
    public static final int FIELD_INDEX_ARROW = 19;
    public static final int FIELD_INDEX_MODELRADIUS = 20;
    public static final int FIELD_INDEX_EXPRESSION = 21;
    public static final int FIELD_INDEX_RALLYROLE = 22;
    public static final int FIELD_INDEX_CURRALLYID = 23;
    public static final int FIELD_INDEX_CURASSISTTARGETID = 24;
    public static final int FIELD_INDEX_ZONEID = 25;

    public static final int FIELD_COUNT = 26;

    private long markBits0 = 0L;
    private PropertyChangeListener listener;

    private ArmyState armyState = ArmyState.forNumber(0);
    private TroopProp troop = null;
    private MoveProp move = null;
    private long ownerId = Constant.DEFAULT_LONG_VALUE;
    private int formationId = Constant.DEFAULT_INT_VALUE;
    private BattleProp battle = null;
    private Int32BuffMapProp buff = null;
    private Camp camp = Camp.forNumber(0);
    private String clanSname = Constant.DEFAULT_STR_VALUE;
    private long enterStateTs = Constant.DEFAULT_LONG_VALUE;
    private long clanId = Constant.DEFAULT_LONG_VALUE;
    private long hpMax = Constant.DEFAULT_LONG_VALUE;
    private PickUpInfoProp pickUpInfo = null;
    private long attachId = Constant.DEFAULT_LONG_VALUE;
    private AttachState attachState = AttachState.forNumber(0);
    private SceneTransportPlaneProp transportPlane = null;
    private ArmyResourcesModelProp resource = null;
    private PlayerCardHeadProp cardHead = null;
    private ClanFlagInfoProp clanFlag = null;
    private Int64ArmyArrowItemMapProp arrow = null;
    private int modelRadius = Constant.DEFAULT_INT_VALUE;
    private ExpressionProp expression = null;
    private RallyArmyRoleType rallyRole = RallyArmyRoleType.forNumber(0);
    private long curRallyId = Constant.DEFAULT_LONG_VALUE;
    private long curAssistTargetId = Constant.DEFAULT_LONG_VALUE;
    private int zoneId = Constant.DEFAULT_INT_VALUE;

    public ArmyProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ArmyProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get armyState
     *
     * @return armyState value
     */
    public ArmyState getArmyState() {
        return this.armyState;
    }

    /**
     * set armyState && set marked
     *
     * @param armyState new value
     * @return current object
     */
    public ArmyProp setArmyState(ArmyState armyState) {
        if (armyState == null) {
            throw new NullPointerException();
        }
        if (this.armyState != armyState) {
            this.mark(FIELD_INDEX_ARMYSTATE);
            this.armyState = armyState;
        }
        return this;
    }

    /**
     * inner set armyState
     *
     * @param armyState new value
     */
    private void innerSetArmyState(ArmyState armyState) {
        this.armyState = armyState;
    }

    /**
     * get troop
     *
     * @return troop value
     */
    public TroopProp getTroop() {
        if (this.troop == null) {
            this.troop = new TroopProp(this, FIELD_INDEX_TROOP);
        }
        return this.troop;
    }

    /**
     * get move
     *
     * @return move value
     */
    public MoveProp getMove() {
        if (this.move == null) {
            this.move = new MoveProp(this, FIELD_INDEX_MOVE);
        }
        return this.move;
    }

    /**
     * get ownerId
     *
     * @return ownerId value
     */
    public long getOwnerId() {
        return this.ownerId;
    }

    /**
     * set ownerId && set marked
     *
     * @param ownerId new value
     * @return current object
     */
    public ArmyProp setOwnerId(long ownerId) {
        if (this.ownerId != ownerId) {
            this.mark(FIELD_INDEX_OWNERID);
            this.ownerId = ownerId;
        }
        return this;
    }

    /**
     * inner set ownerId
     *
     * @param ownerId new value
     */
    private void innerSetOwnerId(long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * get formationId
     *
     * @return formationId value
     */
    public int getFormationId() {
        return this.formationId;
    }

    /**
     * set formationId && set marked
     *
     * @param formationId new value
     * @return current object
     */
    public ArmyProp setFormationId(int formationId) {
        if (this.formationId != formationId) {
            this.mark(FIELD_INDEX_FORMATIONID);
            this.formationId = formationId;
        }
        return this;
    }

    /**
     * inner set formationId
     *
     * @param formationId new value
     */
    private void innerSetFormationId(int formationId) {
        this.formationId = formationId;
    }

    /**
     * get battle
     *
     * @return battle value
     */
    public BattleProp getBattle() {
        if (this.battle == null) {
            this.battle = new BattleProp(this, FIELD_INDEX_BATTLE);
        }
        return this.battle;
    }

    /**
     * get buff
     *
     * @return buff value
     */
    public Int32BuffMapProp getBuff() {
        if (this.buff == null) {
            this.buff = new Int32BuffMapProp(this, FIELD_INDEX_BUFF);
        }
        return this.buff;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putBuffV(BuffProp v) {
        this.getBuff().put(v.getGroupId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public BuffProp addEmptyBuff(Integer k) {
        return this.getBuff().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getBuffSize() {
        if (this.buff == null) {
            return 0;
        }
        return this.buff.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isBuffEmpty() {
        if (this.buff == null) {
            return true;
        }
        return this.buff.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public BuffProp getBuffV(Integer k) {
        if (this.buff == null || !this.buff.containsKey(k)) {
            return null;
        }
        return this.buff.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearBuff() {
        if (this.buff != null) {
            this.buff.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeBuffV(Integer k) {
        if (this.buff != null) {
            this.buff.remove(k);
        }
    }
    /**
     * get camp
     *
     * @return camp value
     */
    public Camp getCamp() {
        return this.camp;
    }

    /**
     * set camp && set marked
     *
     * @param camp new value
     * @return current object
     */
    public ArmyProp setCamp(Camp camp) {
        if (camp == null) {
            throw new NullPointerException();
        }
        if (this.camp != camp) {
            this.mark(FIELD_INDEX_CAMP);
            this.camp = camp;
        }
        return this;
    }

    /**
     * inner set camp
     *
     * @param camp new value
     */
    private void innerSetCamp(Camp camp) {
        this.camp = camp;
    }

    /**
     * get clanSname
     *
     * @return clanSname value
     */
    public String getClanSname() {
        return this.clanSname;
    }

    /**
     * set clanSname && set marked
     *
     * @param clanSname new value
     * @return current object
     */
    public ArmyProp setClanSname(String clanSname) {
        if (clanSname == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanSname, clanSname)) {
            this.mark(FIELD_INDEX_CLANSNAME);
            this.clanSname = clanSname;
        }
        return this;
    }

    /**
     * inner set clanSname
     *
     * @param clanSname new value
     */
    private void innerSetClanSname(String clanSname) {
        this.clanSname = clanSname;
    }

    /**
     * get enterStateTs
     *
     * @return enterStateTs value
     */
    public long getEnterStateTs() {
        return this.enterStateTs;
    }

    /**
     * set enterStateTs && set marked
     *
     * @param enterStateTs new value
     * @return current object
     */
    public ArmyProp setEnterStateTs(long enterStateTs) {
        if (this.enterStateTs != enterStateTs) {
            this.mark(FIELD_INDEX_ENTERSTATETS);
            this.enterStateTs = enterStateTs;
        }
        return this;
    }

    /**
     * inner set enterStateTs
     *
     * @param enterStateTs new value
     */
    private void innerSetEnterStateTs(long enterStateTs) {
        this.enterStateTs = enterStateTs;
    }

    /**
     * get clanId
     *
     * @return clanId value
     */
    public long getClanId() {
        return this.clanId;
    }

    /**
     * set clanId && set marked
     *
     * @param clanId new value
     * @return current object
     */
    public ArmyProp setClanId(long clanId) {
        if (this.clanId != clanId) {
            this.mark(FIELD_INDEX_CLANID);
            this.clanId = clanId;
        }
        return this;
    }

    /**
     * inner set clanId
     *
     * @param clanId new value
     */
    private void innerSetClanId(long clanId) {
        this.clanId = clanId;
    }

    /**
     * get hpMax
     *
     * @return hpMax value
     */
    public long getHpMax() {
        return this.hpMax;
    }

    /**
     * set hpMax && set marked
     *
     * @param hpMax new value
     * @return current object
     */
    public ArmyProp setHpMax(long hpMax) {
        if (this.hpMax != hpMax) {
            this.mark(FIELD_INDEX_HPMAX);
            this.hpMax = hpMax;
        }
        return this;
    }

    /**
     * inner set hpMax
     *
     * @param hpMax new value
     */
    private void innerSetHpMax(long hpMax) {
        this.hpMax = hpMax;
    }

    /**
     * get pickUpInfo
     *
     * @return pickUpInfo value
     */
    public PickUpInfoProp getPickUpInfo() {
        if (this.pickUpInfo == null) {
            this.pickUpInfo = new PickUpInfoProp(this, FIELD_INDEX_PICKUPINFO);
        }
        return this.pickUpInfo;
    }

    /**
     * get attachId
     *
     * @return attachId value
     */
    public long getAttachId() {
        return this.attachId;
    }

    /**
     * set attachId && set marked
     *
     * @param attachId new value
     * @return current object
     */
    public ArmyProp setAttachId(long attachId) {
        if (this.attachId != attachId) {
            this.mark(FIELD_INDEX_ATTACHID);
            this.attachId = attachId;
        }
        return this;
    }

    /**
     * inner set attachId
     *
     * @param attachId new value
     */
    private void innerSetAttachId(long attachId) {
        this.attachId = attachId;
    }

    /**
     * get attachState
     *
     * @return attachState value
     */
    public AttachState getAttachState() {
        return this.attachState;
    }

    /**
     * set attachState && set marked
     *
     * @param attachState new value
     * @return current object
     */
    public ArmyProp setAttachState(AttachState attachState) {
        if (attachState == null) {
            throw new NullPointerException();
        }
        if (this.attachState != attachState) {
            this.mark(FIELD_INDEX_ATTACHSTATE);
            this.attachState = attachState;
        }
        return this;
    }

    /**
     * inner set attachState
     *
     * @param attachState new value
     */
    private void innerSetAttachState(AttachState attachState) {
        this.attachState = attachState;
    }

    /**
     * get transportPlane
     *
     * @return transportPlane value
     */
    public SceneTransportPlaneProp getTransportPlane() {
        if (this.transportPlane == null) {
            this.transportPlane = new SceneTransportPlaneProp(this, FIELD_INDEX_TRANSPORTPLANE);
        }
        return this.transportPlane;
    }

    /**
     * get resource
     *
     * @return resource value
     */
    public ArmyResourcesModelProp getResource() {
        if (this.resource == null) {
            this.resource = new ArmyResourcesModelProp(this, FIELD_INDEX_RESOURCE);
        }
        return this.resource;
    }

    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }

    /**
     * get clanFlag
     *
     * @return clanFlag value
     */
    public ClanFlagInfoProp getClanFlag() {
        if (this.clanFlag == null) {
            this.clanFlag = new ClanFlagInfoProp(this, FIELD_INDEX_CLANFLAG);
        }
        return this.clanFlag;
    }

    /**
     * get arrow
     *
     * @return arrow value
     */
    public Int64ArmyArrowItemMapProp getArrow() {
        if (this.arrow == null) {
            this.arrow = new Int64ArmyArrowItemMapProp(this, FIELD_INDEX_ARROW);
        }
        return this.arrow;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putArrowV(ArmyArrowItemProp v) {
        this.getArrow().put(v.getArmyId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ArmyArrowItemProp addEmptyArrow(Long k) {
        return this.getArrow().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getArrowSize() {
        if (this.arrow == null) {
            return 0;
        }
        return this.arrow.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isArrowEmpty() {
        if (this.arrow == null) {
            return true;
        }
        return this.arrow.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ArmyArrowItemProp getArrowV(Long k) {
        if (this.arrow == null || !this.arrow.containsKey(k)) {
            return null;
        }
        return this.arrow.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearArrow() {
        if (this.arrow != null) {
            this.arrow.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeArrowV(Long k) {
        if (this.arrow != null) {
            this.arrow.remove(k);
        }
    }
    /**
     * get modelRadius
     *
     * @return modelRadius value
     */
    public int getModelRadius() {
        return this.modelRadius;
    }

    /**
     * set modelRadius && set marked
     *
     * @param modelRadius new value
     * @return current object
     */
    public ArmyProp setModelRadius(int modelRadius) {
        if (this.modelRadius != modelRadius) {
            this.mark(FIELD_INDEX_MODELRADIUS);
            this.modelRadius = modelRadius;
        }
        return this;
    }

    /**
     * inner set modelRadius
     *
     * @param modelRadius new value
     */
    private void innerSetModelRadius(int modelRadius) {
        this.modelRadius = modelRadius;
    }

    /**
     * get expression
     *
     * @return expression value
     */
    public ExpressionProp getExpression() {
        if (this.expression == null) {
            this.expression = new ExpressionProp(this, FIELD_INDEX_EXPRESSION);
        }
        return this.expression;
    }

    /**
     * get rallyRole
     *
     * @return rallyRole value
     */
    public RallyArmyRoleType getRallyRole() {
        return this.rallyRole;
    }

    /**
     * set rallyRole && set marked
     *
     * @param rallyRole new value
     * @return current object
     */
    public ArmyProp setRallyRole(RallyArmyRoleType rallyRole) {
        if (rallyRole == null) {
            throw new NullPointerException();
        }
        if (this.rallyRole != rallyRole) {
            this.mark(FIELD_INDEX_RALLYROLE);
            this.rallyRole = rallyRole;
        }
        return this;
    }

    /**
     * inner set rallyRole
     *
     * @param rallyRole new value
     */
    private void innerSetRallyRole(RallyArmyRoleType rallyRole) {
        this.rallyRole = rallyRole;
    }

    /**
     * get curRallyId
     *
     * @return curRallyId value
     */
    public long getCurRallyId() {
        return this.curRallyId;
    }

    /**
     * set curRallyId && set marked
     *
     * @param curRallyId new value
     * @return current object
     */
    public ArmyProp setCurRallyId(long curRallyId) {
        if (this.curRallyId != curRallyId) {
            this.mark(FIELD_INDEX_CURRALLYID);
            this.curRallyId = curRallyId;
        }
        return this;
    }

    /**
     * inner set curRallyId
     *
     * @param curRallyId new value
     */
    private void innerSetCurRallyId(long curRallyId) {
        this.curRallyId = curRallyId;
    }

    /**
     * get curAssistTargetId
     *
     * @return curAssistTargetId value
     */
    public long getCurAssistTargetId() {
        return this.curAssistTargetId;
    }

    /**
     * set curAssistTargetId && set marked
     *
     * @param curAssistTargetId new value
     * @return current object
     */
    public ArmyProp setCurAssistTargetId(long curAssistTargetId) {
        if (this.curAssistTargetId != curAssistTargetId) {
            this.mark(FIELD_INDEX_CURASSISTTARGETID);
            this.curAssistTargetId = curAssistTargetId;
        }
        return this;
    }

    /**
     * inner set curAssistTargetId
     *
     * @param curAssistTargetId new value
     */
    private void innerSetCurAssistTargetId(long curAssistTargetId) {
        this.curAssistTargetId = curAssistTargetId;
    }

    /**
     * get zoneId
     *
     * @return zoneId value
     */
    public int getZoneId() {
        return this.zoneId;
    }

    /**
     * set zoneId && set marked
     *
     * @param zoneId new value
     * @return current object
     */
    public ArmyProp setZoneId(int zoneId) {
        if (this.zoneId != zoneId) {
            this.mark(FIELD_INDEX_ZONEID);
            this.zoneId = zoneId;
        }
        return this;
    }

    /**
     * inner set zoneId
     *
     * @param zoneId new value
     */
    private void innerSetZoneId(int zoneId) {
        this.zoneId = zoneId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArmyEntityPB.Builder getCopyCsBuilder() {
        final ArmyEntityPB.Builder builder = ArmyEntityPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ArmyEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getArmyState() != ArmyState.forNumber(0)) {
            builder.setArmyState(this.getArmyState());
            fieldCnt++;
        }  else if (builder.hasArmyState()) {
            // 清理ArmyState
            builder.clearArmyState();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayerPB.TroopPB.Builder tmpBuilder = StructPlayerPB.TroopPB.newBuilder();
            final int tmpFieldCnt = this.troop.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.move != null) {
            StructPB.MovePB.Builder tmpBuilder = StructPB.MovePB.newBuilder();
            final int tmpFieldCnt = this.move.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMove(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMove();
            }
        }  else if (builder.hasMove()) {
            // 清理Move
            builder.clearMove();
            fieldCnt++;
        }
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.getFormationId() != 0) {
            builder.setFormationId(this.getFormationId());
            fieldCnt++;
        }  else if (builder.hasFormationId()) {
            // 清理FormationId
            builder.clearFormationId();
            fieldCnt++;
        }
        if (this.battle != null) {
            StructBattlePB.BattlePB.Builder tmpBuilder = StructBattlePB.BattlePB.newBuilder();
            final int tmpFieldCnt = this.battle.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattle();
            }
        }  else if (builder.hasBattle()) {
            // 清理Battle
            builder.clearBattle();
            fieldCnt++;
        }
        if (this.buff != null) {
            StructBattlePB.Int32BuffMapPB.Builder tmpBuilder = StructBattlePB.Int32BuffMapPB.newBuilder();
            final int tmpFieldCnt = this.buff.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuff(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuff();
            }
        }  else if (builder.hasBuff()) {
            // 清理Buff
            builder.clearBuff();
            fieldCnt++;
        }
        if (this.getCamp() != Camp.forNumber(0)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }  else if (builder.hasCamp()) {
            // 清理Camp
            builder.clearCamp();
            fieldCnt++;
        }
        if (!this.getClanSname().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSname(this.getClanSname());
            fieldCnt++;
        }  else if (builder.hasClanSname()) {
            // 清理ClanSname
            builder.clearClanSname();
            fieldCnt++;
        }
        if (this.getEnterStateTs() != 0L) {
            builder.setEnterStateTs(this.getEnterStateTs());
            fieldCnt++;
        }  else if (builder.hasEnterStateTs()) {
            // 清理EnterStateTs
            builder.clearEnterStateTs();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (this.getHpMax() != 0L) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }  else if (builder.hasHpMax()) {
            // 清理HpMax
            builder.clearHpMax();
            fieldCnt++;
        }
        if (this.pickUpInfo != null) {
            StructPB.PickUpInfoPB.Builder tmpBuilder = StructPB.PickUpInfoPB.newBuilder();
            final int tmpFieldCnt = this.pickUpInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPickUpInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPickUpInfo();
            }
        }  else if (builder.hasPickUpInfo()) {
            // 清理PickUpInfo
            builder.clearPickUpInfo();
            fieldCnt++;
        }
        if (this.getAttachId() != 0L) {
            builder.setAttachId(this.getAttachId());
            fieldCnt++;
        }  else if (builder.hasAttachId()) {
            // 清理AttachId
            builder.clearAttachId();
            fieldCnt++;
        }
        if (this.getAttachState() != AttachState.forNumber(0)) {
            builder.setAttachState(this.getAttachState());
            fieldCnt++;
        }  else if (builder.hasAttachState()) {
            // 清理AttachState
            builder.clearAttachState();
            fieldCnt++;
        }
        if (this.transportPlane != null) {
            StructPB.SceneTransportPlanePB.Builder tmpBuilder = StructPB.SceneTransportPlanePB.newBuilder();
            final int tmpFieldCnt = this.transportPlane.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTransportPlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTransportPlane();
            }
        }  else if (builder.hasTransportPlane()) {
            // 清理TransportPlane
            builder.clearTransportPlane();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.clanFlag != null) {
            StructClanPB.ClanFlagInfoPB.Builder tmpBuilder = StructClanPB.ClanFlagInfoPB.newBuilder();
            final int tmpFieldCnt = this.clanFlag.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanFlag(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanFlag();
            }
        }  else if (builder.hasClanFlag()) {
            // 清理ClanFlag
            builder.clearClanFlag();
            fieldCnt++;
        }
        if (this.arrow != null) {
            StructPB.Int64ArmyArrowItemMapPB.Builder tmpBuilder = StructPB.Int64ArmyArrowItemMapPB.newBuilder();
            final int tmpFieldCnt = this.arrow.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArrow(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArrow();
            }
        }  else if (builder.hasArrow()) {
            // 清理Arrow
            builder.clearArrow();
            fieldCnt++;
        }
        if (this.getModelRadius() != 0) {
            builder.setModelRadius(this.getModelRadius());
            fieldCnt++;
        }  else if (builder.hasModelRadius()) {
            // 清理ModelRadius
            builder.clearModelRadius();
            fieldCnt++;
        }
        if (this.expression != null) {
            StructPB.ExpressionPB.Builder tmpBuilder = StructPB.ExpressionPB.newBuilder();
            final int tmpFieldCnt = this.expression.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExpression(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExpression();
            }
        }  else if (builder.hasExpression()) {
            // 清理Expression
            builder.clearExpression();
            fieldCnt++;
        }
        if (this.getRallyRole() != RallyArmyRoleType.forNumber(0)) {
            builder.setRallyRole(this.getRallyRole());
            fieldCnt++;
        }  else if (builder.hasRallyRole()) {
            // 清理RallyRole
            builder.clearRallyRole();
            fieldCnt++;
        }
        if (this.getCurRallyId() != 0L) {
            builder.setCurRallyId(this.getCurRallyId());
            fieldCnt++;
        }  else if (builder.hasCurRallyId()) {
            // 清理CurRallyId
            builder.clearCurRallyId();
            fieldCnt++;
        }
        if (this.getCurAssistTargetId() != 0L) {
            builder.setCurAssistTargetId(this.getCurAssistTargetId());
            fieldCnt++;
        }  else if (builder.hasCurAssistTargetId()) {
            // 清理CurAssistTargetId
            builder.clearCurAssistTargetId();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ArmyEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYSTATE)) {
            builder.setArmyState(this.getArmyState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToCs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            final boolean needClear = !builder.hasMove();
            final int tmpFieldCnt = this.move.copyChangeToCs(builder.getMoveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMove();
            }
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FORMATIONID)) {
            builder.setFormationId(this.getFormationId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToCs(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUFF) && this.buff != null) {
            final boolean needClear = !builder.hasBuff();
            final int tmpFieldCnt = this.buff.copyChangeToCs(builder.getBuffBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuff();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSNAME)) {
            builder.setClanSname(this.getClanSname());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERSTATETS)) {
            builder.setEnterStateTs(this.getEnterStateTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HPMAX)) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICKUPINFO) && this.pickUpInfo != null) {
            final boolean needClear = !builder.hasPickUpInfo();
            final int tmpFieldCnt = this.pickUpInfo.copyChangeToCs(builder.getPickUpInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPickUpInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_ATTACHID)) {
            builder.setAttachId(this.getAttachId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ATTACHSTATE)) {
            builder.setAttachState(this.getAttachState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRANSPORTPLANE) && this.transportPlane != null) {
            final boolean needClear = !builder.hasTransportPlane();
            final int tmpFieldCnt = this.transportPlane.copyChangeToCs(builder.getTransportPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTransportPlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANFLAG) && this.clanFlag != null) {
            final boolean needClear = !builder.hasClanFlag();
            final int tmpFieldCnt = this.clanFlag.copyChangeToCs(builder.getClanFlagBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanFlag();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARROW) && this.arrow != null) {
            final boolean needClear = !builder.hasArrow();
            final int tmpFieldCnt = this.arrow.copyChangeToCs(builder.getArrowBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArrow();
            }
        }
        if (this.hasMark(FIELD_INDEX_MODELRADIUS)) {
            builder.setModelRadius(this.getModelRadius());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            final boolean needClear = !builder.hasExpression();
            final int tmpFieldCnt = this.expression.copyChangeToCs(builder.getExpressionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpression();
            }
        }
        if (this.hasMark(FIELD_INDEX_RALLYROLE)) {
            builder.setRallyRole(this.getRallyRole());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURRALLYID)) {
            builder.setCurRallyId(this.getCurRallyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURASSISTTARGETID)) {
            builder.setCurAssistTargetId(this.getCurAssistTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ArmyEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYSTATE)) {
            builder.setArmyState(this.getArmyState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToAndClearDeleteKeysCs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            final boolean needClear = !builder.hasMove();
            final int tmpFieldCnt = this.move.copyChangeToAndClearDeleteKeysCs(builder.getMoveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMove();
            }
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FORMATIONID)) {
            builder.setFormationId(this.getFormationId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToAndClearDeleteKeysCs(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUFF) && this.buff != null) {
            final boolean needClear = !builder.hasBuff();
            final int tmpFieldCnt = this.buff.copyChangeToAndClearDeleteKeysCs(builder.getBuffBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuff();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSNAME)) {
            builder.setClanSname(this.getClanSname());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERSTATETS)) {
            builder.setEnterStateTs(this.getEnterStateTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HPMAX)) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICKUPINFO) && this.pickUpInfo != null) {
            final boolean needClear = !builder.hasPickUpInfo();
            final int tmpFieldCnt = this.pickUpInfo.copyChangeToAndClearDeleteKeysCs(builder.getPickUpInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPickUpInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_ATTACHID)) {
            builder.setAttachId(this.getAttachId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ATTACHSTATE)) {
            builder.setAttachState(this.getAttachState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRANSPORTPLANE) && this.transportPlane != null) {
            final boolean needClear = !builder.hasTransportPlane();
            final int tmpFieldCnt = this.transportPlane.copyChangeToAndClearDeleteKeysCs(builder.getTransportPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTransportPlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToAndClearDeleteKeysCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANFLAG) && this.clanFlag != null) {
            final boolean needClear = !builder.hasClanFlag();
            final int tmpFieldCnt = this.clanFlag.copyChangeToAndClearDeleteKeysCs(builder.getClanFlagBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanFlag();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARROW) && this.arrow != null) {
            final boolean needClear = !builder.hasArrow();
            final int tmpFieldCnt = this.arrow.copyChangeToAndClearDeleteKeysCs(builder.getArrowBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArrow();
            }
        }
        if (this.hasMark(FIELD_INDEX_MODELRADIUS)) {
            builder.setModelRadius(this.getModelRadius());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            final boolean needClear = !builder.hasExpression();
            final int tmpFieldCnt = this.expression.copyChangeToAndClearDeleteKeysCs(builder.getExpressionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpression();
            }
        }
        if (this.hasMark(FIELD_INDEX_RALLYROLE)) {
            builder.setRallyRole(this.getRallyRole());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURRALLYID)) {
            builder.setCurRallyId(this.getCurRallyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURASSISTTARGETID)) {
            builder.setCurAssistTargetId(this.getCurAssistTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ArmyEntityPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasArmyState()) {
            this.innerSetArmyState(proto.getArmyState());
        } else {
            this.innerSetArmyState(ArmyState.forNumber(0));
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromCs(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromCs(proto.getTroop());
            }
        }
        if (proto.hasMove()) {
            this.getMove().mergeFromCs(proto.getMove());
        } else {
            if (this.move != null) {
                this.move.mergeFromCs(proto.getMove());
            }
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasFormationId()) {
            this.innerSetFormationId(proto.getFormationId());
        } else {
            this.innerSetFormationId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeFromCs(proto.getBattle());
        } else {
            if (this.battle != null) {
                this.battle.mergeFromCs(proto.getBattle());
            }
        }
        if (proto.hasBuff()) {
            this.getBuff().mergeFromCs(proto.getBuff());
        } else {
            if (this.buff != null) {
                this.buff.mergeFromCs(proto.getBuff());
            }
        }
        if (proto.hasCamp()) {
            this.innerSetCamp(proto.getCamp());
        } else {
            this.innerSetCamp(Camp.forNumber(0));
        }
        if (proto.hasClanSname()) {
            this.innerSetClanSname(proto.getClanSname());
        } else {
            this.innerSetClanSname(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasEnterStateTs()) {
            this.innerSetEnterStateTs(proto.getEnterStateTs());
        } else {
            this.innerSetEnterStateTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasHpMax()) {
            this.innerSetHpMax(proto.getHpMax());
        } else {
            this.innerSetHpMax(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPickUpInfo()) {
            this.getPickUpInfo().mergeFromCs(proto.getPickUpInfo());
        } else {
            if (this.pickUpInfo != null) {
                this.pickUpInfo.mergeFromCs(proto.getPickUpInfo());
            }
        }
        if (proto.hasAttachId()) {
            this.innerSetAttachId(proto.getAttachId());
        } else {
            this.innerSetAttachId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasAttachState()) {
            this.innerSetAttachState(proto.getAttachState());
        } else {
            this.innerSetAttachState(AttachState.forNumber(0));
        }
        if (proto.hasTransportPlane()) {
            this.getTransportPlane().mergeFromCs(proto.getTransportPlane());
        } else {
            if (this.transportPlane != null) {
                this.transportPlane.mergeFromCs(proto.getTransportPlane());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromCs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromCs(proto.getCardHead());
            }
        }
        if (proto.hasClanFlag()) {
            this.getClanFlag().mergeFromCs(proto.getClanFlag());
        } else {
            if (this.clanFlag != null) {
                this.clanFlag.mergeFromCs(proto.getClanFlag());
            }
        }
        if (proto.hasArrow()) {
            this.getArrow().mergeFromCs(proto.getArrow());
        } else {
            if (this.arrow != null) {
                this.arrow.mergeFromCs(proto.getArrow());
            }
        }
        if (proto.hasModelRadius()) {
            this.innerSetModelRadius(proto.getModelRadius());
        } else {
            this.innerSetModelRadius(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeFromCs(proto.getExpression());
        } else {
            if (this.expression != null) {
                this.expression.mergeFromCs(proto.getExpression());
            }
        }
        if (proto.hasRallyRole()) {
            this.innerSetRallyRole(proto.getRallyRole());
        } else {
            this.innerSetRallyRole(RallyArmyRoleType.forNumber(0));
        }
        if (proto.hasCurRallyId()) {
            this.innerSetCurRallyId(proto.getCurRallyId());
        } else {
            this.innerSetCurRallyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCurAssistTargetId()) {
            this.innerSetCurAssistTargetId(proto.getCurAssistTargetId());
        } else {
            this.innerSetCurAssistTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ArmyProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ArmyEntityPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasArmyState()) {
            this.setArmyState(proto.getArmyState());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromCs(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasMove()) {
            this.getMove().mergeChangeFromCs(proto.getMove());
            fieldCnt++;
        }
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasFormationId()) {
            this.setFormationId(proto.getFormationId());
            fieldCnt++;
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeChangeFromCs(proto.getBattle());
            fieldCnt++;
        }
        if (proto.hasBuff()) {
            this.getBuff().mergeChangeFromCs(proto.getBuff());
            fieldCnt++;
        }
        if (proto.hasCamp()) {
            this.setCamp(proto.getCamp());
            fieldCnt++;
        }
        if (proto.hasClanSname()) {
            this.setClanSname(proto.getClanSname());
            fieldCnt++;
        }
        if (proto.hasEnterStateTs()) {
            this.setEnterStateTs(proto.getEnterStateTs());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasHpMax()) {
            this.setHpMax(proto.getHpMax());
            fieldCnt++;
        }
        if (proto.hasPickUpInfo()) {
            this.getPickUpInfo().mergeChangeFromCs(proto.getPickUpInfo());
            fieldCnt++;
        }
        if (proto.hasAttachId()) {
            this.setAttachId(proto.getAttachId());
            fieldCnt++;
        }
        if (proto.hasAttachState()) {
            this.setAttachState(proto.getAttachState());
            fieldCnt++;
        }
        if (proto.hasTransportPlane()) {
            this.getTransportPlane().mergeChangeFromCs(proto.getTransportPlane());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromCs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasClanFlag()) {
            this.getClanFlag().mergeChangeFromCs(proto.getClanFlag());
            fieldCnt++;
        }
        if (proto.hasArrow()) {
            this.getArrow().mergeChangeFromCs(proto.getArrow());
            fieldCnt++;
        }
        if (proto.hasModelRadius()) {
            this.setModelRadius(proto.getModelRadius());
            fieldCnt++;
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeChangeFromCs(proto.getExpression());
            fieldCnt++;
        }
        if (proto.hasRallyRole()) {
            this.setRallyRole(proto.getRallyRole());
            fieldCnt++;
        }
        if (proto.hasCurRallyId()) {
            this.setCurRallyId(proto.getCurRallyId());
            fieldCnt++;
        }
        if (proto.hasCurAssistTargetId()) {
            this.setCurAssistTargetId(proto.getCurAssistTargetId());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArmyEntity.Builder getCopyDbBuilder() {
        final ArmyEntity.Builder builder = ArmyEntity.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ArmyEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getArmyState() != ArmyState.forNumber(0)) {
            builder.setArmyState(this.getArmyState());
            fieldCnt++;
        }  else if (builder.hasArmyState()) {
            // 清理ArmyState
            builder.clearArmyState();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayer.Troop.Builder tmpBuilder = StructPlayer.Troop.newBuilder();
            final int tmpFieldCnt = this.troop.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.move != null) {
            Struct.Move.Builder tmpBuilder = Struct.Move.newBuilder();
            final int tmpFieldCnt = this.move.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMove(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMove();
            }
        }  else if (builder.hasMove()) {
            // 清理Move
            builder.clearMove();
            fieldCnt++;
        }
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.getFormationId() != 0) {
            builder.setFormationId(this.getFormationId());
            fieldCnt++;
        }  else if (builder.hasFormationId()) {
            // 清理FormationId
            builder.clearFormationId();
            fieldCnt++;
        }
        if (this.battle != null) {
            StructBattle.Battle.Builder tmpBuilder = StructBattle.Battle.newBuilder();
            final int tmpFieldCnt = this.battle.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattle();
            }
        }  else if (builder.hasBattle()) {
            // 清理Battle
            builder.clearBattle();
            fieldCnt++;
        }
        if (this.getCamp() != Camp.forNumber(0)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }  else if (builder.hasCamp()) {
            // 清理Camp
            builder.clearCamp();
            fieldCnt++;
        }
        if (!this.getClanSname().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSname(this.getClanSname());
            fieldCnt++;
        }  else if (builder.hasClanSname()) {
            // 清理ClanSname
            builder.clearClanSname();
            fieldCnt++;
        }
        if (this.getEnterStateTs() != 0L) {
            builder.setEnterStateTs(this.getEnterStateTs());
            fieldCnt++;
        }  else if (builder.hasEnterStateTs()) {
            // 清理EnterStateTs
            builder.clearEnterStateTs();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (this.getHpMax() != 0L) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }  else if (builder.hasHpMax()) {
            // 清理HpMax
            builder.clearHpMax();
            fieldCnt++;
        }
        if (this.pickUpInfo != null) {
            Struct.PickUpInfo.Builder tmpBuilder = Struct.PickUpInfo.newBuilder();
            final int tmpFieldCnt = this.pickUpInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPickUpInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPickUpInfo();
            }
        }  else if (builder.hasPickUpInfo()) {
            // 清理PickUpInfo
            builder.clearPickUpInfo();
            fieldCnt++;
        }
        if (this.getAttachId() != 0L) {
            builder.setAttachId(this.getAttachId());
            fieldCnt++;
        }  else if (builder.hasAttachId()) {
            // 清理AttachId
            builder.clearAttachId();
            fieldCnt++;
        }
        if (this.getAttachState() != AttachState.forNumber(0)) {
            builder.setAttachState(this.getAttachState());
            fieldCnt++;
        }  else if (builder.hasAttachState()) {
            // 清理AttachState
            builder.clearAttachState();
            fieldCnt++;
        }
        if (this.transportPlane != null) {
            Struct.SceneTransportPlane.Builder tmpBuilder = Struct.SceneTransportPlane.newBuilder();
            final int tmpFieldCnt = this.transportPlane.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTransportPlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTransportPlane();
            }
        }  else if (builder.hasTransportPlane()) {
            // 清理TransportPlane
            builder.clearTransportPlane();
            fieldCnt++;
        }
        if (this.resource != null) {
            Army.ArmyResourcesModel.Builder tmpBuilder = Army.ArmyResourcesModel.newBuilder();
            final int tmpFieldCnt = this.resource.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResource(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResource();
            }
        }  else if (builder.hasResource()) {
            // 清理Resource
            builder.clearResource();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.clanFlag != null) {
            StructClan.ClanFlagInfo.Builder tmpBuilder = StructClan.ClanFlagInfo.newBuilder();
            final int tmpFieldCnt = this.clanFlag.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanFlag(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanFlag();
            }
        }  else if (builder.hasClanFlag()) {
            // 清理ClanFlag
            builder.clearClanFlag();
            fieldCnt++;
        }
        if (this.expression != null) {
            Struct.Expression.Builder tmpBuilder = Struct.Expression.newBuilder();
            final int tmpFieldCnt = this.expression.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExpression(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExpression();
            }
        }  else if (builder.hasExpression()) {
            // 清理Expression
            builder.clearExpression();
            fieldCnt++;
        }
        if (this.getRallyRole() != RallyArmyRoleType.forNumber(0)) {
            builder.setRallyRole(this.getRallyRole());
            fieldCnt++;
        }  else if (builder.hasRallyRole()) {
            // 清理RallyRole
            builder.clearRallyRole();
            fieldCnt++;
        }
        if (this.getCurRallyId() != 0L) {
            builder.setCurRallyId(this.getCurRallyId());
            fieldCnt++;
        }  else if (builder.hasCurRallyId()) {
            // 清理CurRallyId
            builder.clearCurRallyId();
            fieldCnt++;
        }
        if (this.getCurAssistTargetId() != 0L) {
            builder.setCurAssistTargetId(this.getCurAssistTargetId());
            fieldCnt++;
        }  else if (builder.hasCurAssistTargetId()) {
            // 清理CurAssistTargetId
            builder.clearCurAssistTargetId();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ArmyEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYSTATE)) {
            builder.setArmyState(this.getArmyState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToDb(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            final boolean needClear = !builder.hasMove();
            final int tmpFieldCnt = this.move.copyChangeToDb(builder.getMoveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMove();
            }
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FORMATIONID)) {
            builder.setFormationId(this.getFormationId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToDb(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSNAME)) {
            builder.setClanSname(this.getClanSname());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERSTATETS)) {
            builder.setEnterStateTs(this.getEnterStateTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HPMAX)) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICKUPINFO) && this.pickUpInfo != null) {
            final boolean needClear = !builder.hasPickUpInfo();
            final int tmpFieldCnt = this.pickUpInfo.copyChangeToDb(builder.getPickUpInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPickUpInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_ATTACHID)) {
            builder.setAttachId(this.getAttachId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ATTACHSTATE)) {
            builder.setAttachState(this.getAttachState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRANSPORTPLANE) && this.transportPlane != null) {
            final boolean needClear = !builder.hasTransportPlane();
            final int tmpFieldCnt = this.transportPlane.copyChangeToDb(builder.getTransportPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTransportPlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESOURCE) && this.resource != null) {
            final boolean needClear = !builder.hasResource();
            final int tmpFieldCnt = this.resource.copyChangeToDb(builder.getResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResource();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToDb(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANFLAG) && this.clanFlag != null) {
            final boolean needClear = !builder.hasClanFlag();
            final int tmpFieldCnt = this.clanFlag.copyChangeToDb(builder.getClanFlagBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanFlag();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            final boolean needClear = !builder.hasExpression();
            final int tmpFieldCnt = this.expression.copyChangeToDb(builder.getExpressionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpression();
            }
        }
        if (this.hasMark(FIELD_INDEX_RALLYROLE)) {
            builder.setRallyRole(this.getRallyRole());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURRALLYID)) {
            builder.setCurRallyId(this.getCurRallyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURASSISTTARGETID)) {
            builder.setCurAssistTargetId(this.getCurAssistTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ArmyEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasArmyState()) {
            this.innerSetArmyState(proto.getArmyState());
        } else {
            this.innerSetArmyState(ArmyState.forNumber(0));
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromDb(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromDb(proto.getTroop());
            }
        }
        if (proto.hasMove()) {
            this.getMove().mergeFromDb(proto.getMove());
        } else {
            if (this.move != null) {
                this.move.mergeFromDb(proto.getMove());
            }
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasFormationId()) {
            this.innerSetFormationId(proto.getFormationId());
        } else {
            this.innerSetFormationId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeFromDb(proto.getBattle());
        } else {
            if (this.battle != null) {
                this.battle.mergeFromDb(proto.getBattle());
            }
        }
        if (proto.hasCamp()) {
            this.innerSetCamp(proto.getCamp());
        } else {
            this.innerSetCamp(Camp.forNumber(0));
        }
        if (proto.hasClanSname()) {
            this.innerSetClanSname(proto.getClanSname());
        } else {
            this.innerSetClanSname(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasEnterStateTs()) {
            this.innerSetEnterStateTs(proto.getEnterStateTs());
        } else {
            this.innerSetEnterStateTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasHpMax()) {
            this.innerSetHpMax(proto.getHpMax());
        } else {
            this.innerSetHpMax(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPickUpInfo()) {
            this.getPickUpInfo().mergeFromDb(proto.getPickUpInfo());
        } else {
            if (this.pickUpInfo != null) {
                this.pickUpInfo.mergeFromDb(proto.getPickUpInfo());
            }
        }
        if (proto.hasAttachId()) {
            this.innerSetAttachId(proto.getAttachId());
        } else {
            this.innerSetAttachId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasAttachState()) {
            this.innerSetAttachState(proto.getAttachState());
        } else {
            this.innerSetAttachState(AttachState.forNumber(0));
        }
        if (proto.hasTransportPlane()) {
            this.getTransportPlane().mergeFromDb(proto.getTransportPlane());
        } else {
            if (this.transportPlane != null) {
                this.transportPlane.mergeFromDb(proto.getTransportPlane());
            }
        }
        if (proto.hasResource()) {
            this.getResource().mergeFromDb(proto.getResource());
        } else {
            if (this.resource != null) {
                this.resource.mergeFromDb(proto.getResource());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromDb(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromDb(proto.getCardHead());
            }
        }
        if (proto.hasClanFlag()) {
            this.getClanFlag().mergeFromDb(proto.getClanFlag());
        } else {
            if (this.clanFlag != null) {
                this.clanFlag.mergeFromDb(proto.getClanFlag());
            }
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeFromDb(proto.getExpression());
        } else {
            if (this.expression != null) {
                this.expression.mergeFromDb(proto.getExpression());
            }
        }
        if (proto.hasRallyRole()) {
            this.innerSetRallyRole(proto.getRallyRole());
        } else {
            this.innerSetRallyRole(RallyArmyRoleType.forNumber(0));
        }
        if (proto.hasCurRallyId()) {
            this.innerSetCurRallyId(proto.getCurRallyId());
        } else {
            this.innerSetCurRallyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCurAssistTargetId()) {
            this.innerSetCurAssistTargetId(proto.getCurAssistTargetId());
        } else {
            this.innerSetCurAssistTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ArmyProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ArmyEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasArmyState()) {
            this.setArmyState(proto.getArmyState());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromDb(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasMove()) {
            this.getMove().mergeChangeFromDb(proto.getMove());
            fieldCnt++;
        }
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasFormationId()) {
            this.setFormationId(proto.getFormationId());
            fieldCnt++;
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeChangeFromDb(proto.getBattle());
            fieldCnt++;
        }
        if (proto.hasCamp()) {
            this.setCamp(proto.getCamp());
            fieldCnt++;
        }
        if (proto.hasClanSname()) {
            this.setClanSname(proto.getClanSname());
            fieldCnt++;
        }
        if (proto.hasEnterStateTs()) {
            this.setEnterStateTs(proto.getEnterStateTs());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasHpMax()) {
            this.setHpMax(proto.getHpMax());
            fieldCnt++;
        }
        if (proto.hasPickUpInfo()) {
            this.getPickUpInfo().mergeChangeFromDb(proto.getPickUpInfo());
            fieldCnt++;
        }
        if (proto.hasAttachId()) {
            this.setAttachId(proto.getAttachId());
            fieldCnt++;
        }
        if (proto.hasAttachState()) {
            this.setAttachState(proto.getAttachState());
            fieldCnt++;
        }
        if (proto.hasTransportPlane()) {
            this.getTransportPlane().mergeChangeFromDb(proto.getTransportPlane());
            fieldCnt++;
        }
        if (proto.hasResource()) {
            this.getResource().mergeChangeFromDb(proto.getResource());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromDb(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasClanFlag()) {
            this.getClanFlag().mergeChangeFromDb(proto.getClanFlag());
            fieldCnt++;
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeChangeFromDb(proto.getExpression());
            fieldCnt++;
        }
        if (proto.hasRallyRole()) {
            this.setRallyRole(proto.getRallyRole());
            fieldCnt++;
        }
        if (proto.hasCurRallyId()) {
            this.setCurRallyId(proto.getCurRallyId());
            fieldCnt++;
        }
        if (proto.hasCurAssistTargetId()) {
            this.setCurAssistTargetId(proto.getCurAssistTargetId());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArmyEntity.Builder getCopySsBuilder() {
        final ArmyEntity.Builder builder = ArmyEntity.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ArmyEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getArmyState() != ArmyState.forNumber(0)) {
            builder.setArmyState(this.getArmyState());
            fieldCnt++;
        }  else if (builder.hasArmyState()) {
            // 清理ArmyState
            builder.clearArmyState();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayer.Troop.Builder tmpBuilder = StructPlayer.Troop.newBuilder();
            final int tmpFieldCnt = this.troop.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.move != null) {
            Struct.Move.Builder tmpBuilder = Struct.Move.newBuilder();
            final int tmpFieldCnt = this.move.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMove(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMove();
            }
        }  else if (builder.hasMove()) {
            // 清理Move
            builder.clearMove();
            fieldCnt++;
        }
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.getFormationId() != 0) {
            builder.setFormationId(this.getFormationId());
            fieldCnt++;
        }  else if (builder.hasFormationId()) {
            // 清理FormationId
            builder.clearFormationId();
            fieldCnt++;
        }
        if (this.battle != null) {
            StructBattle.Battle.Builder tmpBuilder = StructBattle.Battle.newBuilder();
            final int tmpFieldCnt = this.battle.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattle();
            }
        }  else if (builder.hasBattle()) {
            // 清理Battle
            builder.clearBattle();
            fieldCnt++;
        }
        if (this.buff != null) {
            StructBattle.Int32BuffMap.Builder tmpBuilder = StructBattle.Int32BuffMap.newBuilder();
            final int tmpFieldCnt = this.buff.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuff(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuff();
            }
        }  else if (builder.hasBuff()) {
            // 清理Buff
            builder.clearBuff();
            fieldCnt++;
        }
        if (this.getCamp() != Camp.forNumber(0)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }  else if (builder.hasCamp()) {
            // 清理Camp
            builder.clearCamp();
            fieldCnt++;
        }
        if (!this.getClanSname().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSname(this.getClanSname());
            fieldCnt++;
        }  else if (builder.hasClanSname()) {
            // 清理ClanSname
            builder.clearClanSname();
            fieldCnt++;
        }
        if (this.getEnterStateTs() != 0L) {
            builder.setEnterStateTs(this.getEnterStateTs());
            fieldCnt++;
        }  else if (builder.hasEnterStateTs()) {
            // 清理EnterStateTs
            builder.clearEnterStateTs();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (this.getHpMax() != 0L) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }  else if (builder.hasHpMax()) {
            // 清理HpMax
            builder.clearHpMax();
            fieldCnt++;
        }
        if (this.pickUpInfo != null) {
            Struct.PickUpInfo.Builder tmpBuilder = Struct.PickUpInfo.newBuilder();
            final int tmpFieldCnt = this.pickUpInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPickUpInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPickUpInfo();
            }
        }  else if (builder.hasPickUpInfo()) {
            // 清理PickUpInfo
            builder.clearPickUpInfo();
            fieldCnt++;
        }
        if (this.getAttachId() != 0L) {
            builder.setAttachId(this.getAttachId());
            fieldCnt++;
        }  else if (builder.hasAttachId()) {
            // 清理AttachId
            builder.clearAttachId();
            fieldCnt++;
        }
        if (this.getAttachState() != AttachState.forNumber(0)) {
            builder.setAttachState(this.getAttachState());
            fieldCnt++;
        }  else if (builder.hasAttachState()) {
            // 清理AttachState
            builder.clearAttachState();
            fieldCnt++;
        }
        if (this.transportPlane != null) {
            Struct.SceneTransportPlane.Builder tmpBuilder = Struct.SceneTransportPlane.newBuilder();
            final int tmpFieldCnt = this.transportPlane.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTransportPlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTransportPlane();
            }
        }  else if (builder.hasTransportPlane()) {
            // 清理TransportPlane
            builder.clearTransportPlane();
            fieldCnt++;
        }
        if (this.resource != null) {
            Army.ArmyResourcesModel.Builder tmpBuilder = Army.ArmyResourcesModel.newBuilder();
            final int tmpFieldCnt = this.resource.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResource(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResource();
            }
        }  else if (builder.hasResource()) {
            // 清理Resource
            builder.clearResource();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.clanFlag != null) {
            StructClan.ClanFlagInfo.Builder tmpBuilder = StructClan.ClanFlagInfo.newBuilder();
            final int tmpFieldCnt = this.clanFlag.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanFlag(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanFlag();
            }
        }  else if (builder.hasClanFlag()) {
            // 清理ClanFlag
            builder.clearClanFlag();
            fieldCnt++;
        }
        if (this.arrow != null) {
            Struct.Int64ArmyArrowItemMap.Builder tmpBuilder = Struct.Int64ArmyArrowItemMap.newBuilder();
            final int tmpFieldCnt = this.arrow.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArrow(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArrow();
            }
        }  else if (builder.hasArrow()) {
            // 清理Arrow
            builder.clearArrow();
            fieldCnt++;
        }
        if (this.getModelRadius() != 0) {
            builder.setModelRadius(this.getModelRadius());
            fieldCnt++;
        }  else if (builder.hasModelRadius()) {
            // 清理ModelRadius
            builder.clearModelRadius();
            fieldCnt++;
        }
        if (this.expression != null) {
            Struct.Expression.Builder tmpBuilder = Struct.Expression.newBuilder();
            final int tmpFieldCnt = this.expression.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExpression(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExpression();
            }
        }  else if (builder.hasExpression()) {
            // 清理Expression
            builder.clearExpression();
            fieldCnt++;
        }
        if (this.getRallyRole() != RallyArmyRoleType.forNumber(0)) {
            builder.setRallyRole(this.getRallyRole());
            fieldCnt++;
        }  else if (builder.hasRallyRole()) {
            // 清理RallyRole
            builder.clearRallyRole();
            fieldCnt++;
        }
        if (this.getCurRallyId() != 0L) {
            builder.setCurRallyId(this.getCurRallyId());
            fieldCnt++;
        }  else if (builder.hasCurRallyId()) {
            // 清理CurRallyId
            builder.clearCurRallyId();
            fieldCnt++;
        }
        if (this.getCurAssistTargetId() != 0L) {
            builder.setCurAssistTargetId(this.getCurAssistTargetId());
            fieldCnt++;
        }  else if (builder.hasCurAssistTargetId()) {
            // 清理CurAssistTargetId
            builder.clearCurAssistTargetId();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ArmyEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYSTATE)) {
            builder.setArmyState(this.getArmyState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToSs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            final boolean needClear = !builder.hasMove();
            final int tmpFieldCnt = this.move.copyChangeToSs(builder.getMoveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMove();
            }
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FORMATIONID)) {
            builder.setFormationId(this.getFormationId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToSs(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUFF) && this.buff != null) {
            final boolean needClear = !builder.hasBuff();
            final int tmpFieldCnt = this.buff.copyChangeToSs(builder.getBuffBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuff();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSNAME)) {
            builder.setClanSname(this.getClanSname());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERSTATETS)) {
            builder.setEnterStateTs(this.getEnterStateTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HPMAX)) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICKUPINFO) && this.pickUpInfo != null) {
            final boolean needClear = !builder.hasPickUpInfo();
            final int tmpFieldCnt = this.pickUpInfo.copyChangeToSs(builder.getPickUpInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPickUpInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_ATTACHID)) {
            builder.setAttachId(this.getAttachId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ATTACHSTATE)) {
            builder.setAttachState(this.getAttachState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRANSPORTPLANE) && this.transportPlane != null) {
            final boolean needClear = !builder.hasTransportPlane();
            final int tmpFieldCnt = this.transportPlane.copyChangeToSs(builder.getTransportPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTransportPlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESOURCE) && this.resource != null) {
            final boolean needClear = !builder.hasResource();
            final int tmpFieldCnt = this.resource.copyChangeToSs(builder.getResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResource();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANFLAG) && this.clanFlag != null) {
            final boolean needClear = !builder.hasClanFlag();
            final int tmpFieldCnt = this.clanFlag.copyChangeToSs(builder.getClanFlagBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanFlag();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARROW) && this.arrow != null) {
            final boolean needClear = !builder.hasArrow();
            final int tmpFieldCnt = this.arrow.copyChangeToSs(builder.getArrowBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArrow();
            }
        }
        if (this.hasMark(FIELD_INDEX_MODELRADIUS)) {
            builder.setModelRadius(this.getModelRadius());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            final boolean needClear = !builder.hasExpression();
            final int tmpFieldCnt = this.expression.copyChangeToSs(builder.getExpressionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpression();
            }
        }
        if (this.hasMark(FIELD_INDEX_RALLYROLE)) {
            builder.setRallyRole(this.getRallyRole());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURRALLYID)) {
            builder.setCurRallyId(this.getCurRallyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURASSISTTARGETID)) {
            builder.setCurAssistTargetId(this.getCurAssistTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ArmyEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasArmyState()) {
            this.innerSetArmyState(proto.getArmyState());
        } else {
            this.innerSetArmyState(ArmyState.forNumber(0));
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromSs(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromSs(proto.getTroop());
            }
        }
        if (proto.hasMove()) {
            this.getMove().mergeFromSs(proto.getMove());
        } else {
            if (this.move != null) {
                this.move.mergeFromSs(proto.getMove());
            }
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasFormationId()) {
            this.innerSetFormationId(proto.getFormationId());
        } else {
            this.innerSetFormationId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeFromSs(proto.getBattle());
        } else {
            if (this.battle != null) {
                this.battle.mergeFromSs(proto.getBattle());
            }
        }
        if (proto.hasBuff()) {
            this.getBuff().mergeFromSs(proto.getBuff());
        } else {
            if (this.buff != null) {
                this.buff.mergeFromSs(proto.getBuff());
            }
        }
        if (proto.hasCamp()) {
            this.innerSetCamp(proto.getCamp());
        } else {
            this.innerSetCamp(Camp.forNumber(0));
        }
        if (proto.hasClanSname()) {
            this.innerSetClanSname(proto.getClanSname());
        } else {
            this.innerSetClanSname(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasEnterStateTs()) {
            this.innerSetEnterStateTs(proto.getEnterStateTs());
        } else {
            this.innerSetEnterStateTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasHpMax()) {
            this.innerSetHpMax(proto.getHpMax());
        } else {
            this.innerSetHpMax(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPickUpInfo()) {
            this.getPickUpInfo().mergeFromSs(proto.getPickUpInfo());
        } else {
            if (this.pickUpInfo != null) {
                this.pickUpInfo.mergeFromSs(proto.getPickUpInfo());
            }
        }
        if (proto.hasAttachId()) {
            this.innerSetAttachId(proto.getAttachId());
        } else {
            this.innerSetAttachId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasAttachState()) {
            this.innerSetAttachState(proto.getAttachState());
        } else {
            this.innerSetAttachState(AttachState.forNumber(0));
        }
        if (proto.hasTransportPlane()) {
            this.getTransportPlane().mergeFromSs(proto.getTransportPlane());
        } else {
            if (this.transportPlane != null) {
                this.transportPlane.mergeFromSs(proto.getTransportPlane());
            }
        }
        if (proto.hasResource()) {
            this.getResource().mergeFromSs(proto.getResource());
        } else {
            if (this.resource != null) {
                this.resource.mergeFromSs(proto.getResource());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        if (proto.hasClanFlag()) {
            this.getClanFlag().mergeFromSs(proto.getClanFlag());
        } else {
            if (this.clanFlag != null) {
                this.clanFlag.mergeFromSs(proto.getClanFlag());
            }
        }
        if (proto.hasArrow()) {
            this.getArrow().mergeFromSs(proto.getArrow());
        } else {
            if (this.arrow != null) {
                this.arrow.mergeFromSs(proto.getArrow());
            }
        }
        if (proto.hasModelRadius()) {
            this.innerSetModelRadius(proto.getModelRadius());
        } else {
            this.innerSetModelRadius(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeFromSs(proto.getExpression());
        } else {
            if (this.expression != null) {
                this.expression.mergeFromSs(proto.getExpression());
            }
        }
        if (proto.hasRallyRole()) {
            this.innerSetRallyRole(proto.getRallyRole());
        } else {
            this.innerSetRallyRole(RallyArmyRoleType.forNumber(0));
        }
        if (proto.hasCurRallyId()) {
            this.innerSetCurRallyId(proto.getCurRallyId());
        } else {
            this.innerSetCurRallyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCurAssistTargetId()) {
            this.innerSetCurAssistTargetId(proto.getCurAssistTargetId());
        } else {
            this.innerSetCurAssistTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ArmyProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ArmyEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasArmyState()) {
            this.setArmyState(proto.getArmyState());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromSs(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasMove()) {
            this.getMove().mergeChangeFromSs(proto.getMove());
            fieldCnt++;
        }
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasFormationId()) {
            this.setFormationId(proto.getFormationId());
            fieldCnt++;
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeChangeFromSs(proto.getBattle());
            fieldCnt++;
        }
        if (proto.hasBuff()) {
            this.getBuff().mergeChangeFromSs(proto.getBuff());
            fieldCnt++;
        }
        if (proto.hasCamp()) {
            this.setCamp(proto.getCamp());
            fieldCnt++;
        }
        if (proto.hasClanSname()) {
            this.setClanSname(proto.getClanSname());
            fieldCnt++;
        }
        if (proto.hasEnterStateTs()) {
            this.setEnterStateTs(proto.getEnterStateTs());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasHpMax()) {
            this.setHpMax(proto.getHpMax());
            fieldCnt++;
        }
        if (proto.hasPickUpInfo()) {
            this.getPickUpInfo().mergeChangeFromSs(proto.getPickUpInfo());
            fieldCnt++;
        }
        if (proto.hasAttachId()) {
            this.setAttachId(proto.getAttachId());
            fieldCnt++;
        }
        if (proto.hasAttachState()) {
            this.setAttachState(proto.getAttachState());
            fieldCnt++;
        }
        if (proto.hasTransportPlane()) {
            this.getTransportPlane().mergeChangeFromSs(proto.getTransportPlane());
            fieldCnt++;
        }
        if (proto.hasResource()) {
            this.getResource().mergeChangeFromSs(proto.getResource());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasClanFlag()) {
            this.getClanFlag().mergeChangeFromSs(proto.getClanFlag());
            fieldCnt++;
        }
        if (proto.hasArrow()) {
            this.getArrow().mergeChangeFromSs(proto.getArrow());
            fieldCnt++;
        }
        if (proto.hasModelRadius()) {
            this.setModelRadius(proto.getModelRadius());
            fieldCnt++;
        }
        if (proto.hasExpression()) {
            this.getExpression().mergeChangeFromSs(proto.getExpression());
            fieldCnt++;
        }
        if (proto.hasRallyRole()) {
            this.setRallyRole(proto.getRallyRole());
            fieldCnt++;
        }
        if (proto.hasCurRallyId()) {
            this.setCurRallyId(proto.getCurRallyId());
            fieldCnt++;
        }
        if (proto.hasCurAssistTargetId()) {
            this.setCurAssistTargetId(proto.getCurAssistTargetId());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ArmyEntity.Builder builder = ArmyEntity.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            this.troop.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            this.move.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            this.battle.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BUFF) && this.buff != null) {
            this.buff.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PICKUPINFO) && this.pickUpInfo != null) {
            this.pickUpInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TRANSPORTPLANE) && this.transportPlane != null) {
            this.transportPlane.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RESOURCE) && this.resource != null) {
            this.resource.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CLANFLAG) && this.clanFlag != null) {
            this.clanFlag.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ARROW) && this.arrow != null) {
            this.arrow.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_EXPRESSION) && this.expression != null) {
            this.expression.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.troop != null) {
            this.troop.markAll();
        }
        if (this.move != null) {
            this.move.markAll();
        }
        if (this.battle != null) {
            this.battle.markAll();
        }
        if (this.buff != null) {
            this.buff.markAll();
        }
        if (this.pickUpInfo != null) {
            this.pickUpInfo.markAll();
        }
        if (this.transportPlane != null) {
            this.transportPlane.markAll();
        }
        if (this.resource != null) {
            this.resource.markAll();
        }
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        if (this.clanFlag != null) {
            this.clanFlag.markAll();
        }
        if (this.arrow != null) {
            this.arrow.markAll();
        }
        if (this.expression != null) {
            this.expression.markAll();
        }
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("ArmyProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("ArmyProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ArmyProp)) {
            return false;
        }
        final ArmyProp otherNode = (ArmyProp) node;
        if (this.armyState != otherNode.armyState) {
            return false;
        }
        if (!this.getTroop().compareDataTo(otherNode.getTroop())) {
            return false;
        }
        if (!this.getMove().compareDataTo(otherNode.getMove())) {
            return false;
        }
        if (this.ownerId != otherNode.ownerId) {
            return false;
        }
        if (this.formationId != otherNode.formationId) {
            return false;
        }
        if (!this.getBattle().compareDataTo(otherNode.getBattle())) {
            return false;
        }
        if (!this.getBuff().compareDataTo(otherNode.getBuff())) {
            return false;
        }
        if (this.camp != otherNode.camp) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanSname, otherNode.clanSname)) {
            return false;
        }
        if (this.enterStateTs != otherNode.enterStateTs) {
            return false;
        }
        if (this.clanId != otherNode.clanId) {
            return false;
        }
        if (this.hpMax != otherNode.hpMax) {
            return false;
        }
        if (!this.getPickUpInfo().compareDataTo(otherNode.getPickUpInfo())) {
            return false;
        }
        if (this.attachId != otherNode.attachId) {
            return false;
        }
        if (this.attachState != otherNode.attachState) {
            return false;
        }
        if (!this.getTransportPlane().compareDataTo(otherNode.getTransportPlane())) {
            return false;
        }
        if (!this.getResource().compareDataTo(otherNode.getResource())) {
            return false;
        }
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        if (!this.getClanFlag().compareDataTo(otherNode.getClanFlag())) {
            return false;
        }
        if (!this.getArrow().compareDataTo(otherNode.getArrow())) {
            return false;
        }
        if (this.modelRadius != otherNode.modelRadius) {
            return false;
        }
        if (!this.getExpression().compareDataTo(otherNode.getExpression())) {
            return false;
        }
        if (this.rallyRole != otherNode.rallyRole) {
            return false;
        }
        if (this.curRallyId != otherNode.curRallyId) {
            return false;
        }
        if (this.curAssistTargetId != otherNode.curAssistTargetId) {
            return false;
        }
        if (this.zoneId != otherNode.zoneId) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    public static ArmyProp of(ArmyEntity fullAttrDb, ArmyEntity changeAttrDb) {
        ArmyProp prop = new ArmyProp();
        prop.mergeFromDb(fullAttrDb);
        prop.mergeChangeFromDb(changeAttrDb);
        prop.unMarkAll();
        return prop;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 38;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerDefendInfo;
import com.yorha.proto.PlayerPB.PlayerDefendInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerDefendInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CAMPAIGNID = 0;
    public static final int FIELD_INDEX_BASELEVEL = 1;
    public static final int FIELD_INDEX_STARTTSMS = 2;
    public static final int FIELD_INDEX_BATTLEINFO = 3;
    public static final int FIELD_INDEX_NEXTCAMPAIGNID = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private int campaignId = Constant.DEFAULT_INT_VALUE;
    private int baseLevel = Constant.DEFAULT_INT_VALUE;
    private long startTsMs = Constant.DEFAULT_LONG_VALUE;
    private com.google.protobuf.ByteString battleInfo = Constant.DEFAULT_BYTE_STRING_VALUE;
    private int nextCampaignId = Constant.DEFAULT_INT_VALUE;

    public PlayerDefendInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerDefendInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get campaignId
     *
     * @return campaignId value
     */
    public int getCampaignId() {
        return this.campaignId;
    }

    /**
     * set campaignId && set marked
     *
     * @param campaignId new value
     * @return current object
     */
    public PlayerDefendInfoProp setCampaignId(int campaignId) {
        if (this.campaignId != campaignId) {
            this.mark(FIELD_INDEX_CAMPAIGNID);
            this.campaignId = campaignId;
        }
        return this;
    }

    /**
     * inner set campaignId
     *
     * @param campaignId new value
     */
    private void innerSetCampaignId(int campaignId) {
        this.campaignId = campaignId;
    }

    /**
     * get baseLevel
     *
     * @return baseLevel value
     */
    public int getBaseLevel() {
        return this.baseLevel;
    }

    /**
     * set baseLevel && set marked
     *
     * @param baseLevel new value
     * @return current object
     */
    public PlayerDefendInfoProp setBaseLevel(int baseLevel) {
        if (this.baseLevel != baseLevel) {
            this.mark(FIELD_INDEX_BASELEVEL);
            this.baseLevel = baseLevel;
        }
        return this;
    }

    /**
     * inner set baseLevel
     *
     * @param baseLevel new value
     */
    private void innerSetBaseLevel(int baseLevel) {
        this.baseLevel = baseLevel;
    }

    /**
     * get startTsMs
     *
     * @return startTsMs value
     */
    public long getStartTsMs() {
        return this.startTsMs;
    }

    /**
     * set startTsMs && set marked
     *
     * @param startTsMs new value
     * @return current object
     */
    public PlayerDefendInfoProp setStartTsMs(long startTsMs) {
        if (this.startTsMs != startTsMs) {
            this.mark(FIELD_INDEX_STARTTSMS);
            this.startTsMs = startTsMs;
        }
        return this;
    }

    /**
     * inner set startTsMs
     *
     * @param startTsMs new value
     */
    private void innerSetStartTsMs(long startTsMs) {
        this.startTsMs = startTsMs;
    }

    /**
     * get battleInfo
     *
     * @return battleInfo value
     */
    public com.google.protobuf.ByteString getBattleInfo() {
        return this.battleInfo;
    }

    /**
     * set battleInfo && set marked
     *
     * @param battleInfo new value
     * @return current object
     */
    public PlayerDefendInfoProp setBattleInfo(com.google.protobuf.ByteString battleInfo) {
        if (this.battleInfo != battleInfo) {
            this.mark(FIELD_INDEX_BATTLEINFO);
            this.battleInfo = battleInfo;
        }
        return this;
    }

    /**
     * inner set battleInfo
     *
     * @param battleInfo new value
     */
    private void innerSetBattleInfo(com.google.protobuf.ByteString battleInfo) {
        this.battleInfo = battleInfo;
    }

    /**
     * get nextCampaignId
     *
     * @return nextCampaignId value
     */
    public int getNextCampaignId() {
        return this.nextCampaignId;
    }

    /**
     * set nextCampaignId && set marked
     *
     * @param nextCampaignId new value
     * @return current object
     */
    public PlayerDefendInfoProp setNextCampaignId(int nextCampaignId) {
        if (this.nextCampaignId != nextCampaignId) {
            this.mark(FIELD_INDEX_NEXTCAMPAIGNID);
            this.nextCampaignId = nextCampaignId;
        }
        return this;
    }

    /**
     * inner set nextCampaignId
     *
     * @param nextCampaignId new value
     */
    private void innerSetNextCampaignId(int nextCampaignId) {
        this.nextCampaignId = nextCampaignId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerDefendInfoPB.Builder getCopyCsBuilder() {
        final PlayerDefendInfoPB.Builder builder = PlayerDefendInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerDefendInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCampaignId() != 0) {
            builder.setCampaignId(this.getCampaignId());
            fieldCnt++;
        }  else if (builder.hasCampaignId()) {
            // 清理CampaignId
            builder.clearCampaignId();
            fieldCnt++;
        }
        if (this.getBaseLevel() != 0) {
            builder.setBaseLevel(this.getBaseLevel());
            fieldCnt++;
        }  else if (builder.hasBaseLevel()) {
            // 清理BaseLevel
            builder.clearBaseLevel();
            fieldCnt++;
        }
        if (this.getStartTsMs() != 0L) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStartTsMs()) {
            // 清理StartTsMs
            builder.clearStartTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerDefendInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CAMPAIGNID)) {
            builder.setCampaignId(this.getCampaignId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASELEVEL)) {
            builder.setBaseLevel(this.getBaseLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerDefendInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CAMPAIGNID)) {
            builder.setCampaignId(this.getCampaignId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASELEVEL)) {
            builder.setBaseLevel(this.getBaseLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerDefendInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCampaignId()) {
            this.innerSetCampaignId(proto.getCampaignId());
        } else {
            this.innerSetCampaignId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBaseLevel()) {
            this.innerSetBaseLevel(proto.getBaseLevel());
        } else {
            this.innerSetBaseLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartTsMs()) {
            this.innerSetStartTsMs(proto.getStartTsMs());
        } else {
            this.innerSetStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerDefendInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerDefendInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCampaignId()) {
            this.setCampaignId(proto.getCampaignId());
            fieldCnt++;
        }
        if (proto.hasBaseLevel()) {
            this.setBaseLevel(proto.getBaseLevel());
            fieldCnt++;
        }
        if (proto.hasStartTsMs()) {
            this.setStartTsMs(proto.getStartTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerDefendInfo.Builder getCopyDbBuilder() {
        final PlayerDefendInfo.Builder builder = PlayerDefendInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerDefendInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCampaignId() != 0) {
            builder.setCampaignId(this.getCampaignId());
            fieldCnt++;
        }  else if (builder.hasCampaignId()) {
            // 清理CampaignId
            builder.clearCampaignId();
            fieldCnt++;
        }
        if (this.getBaseLevel() != 0) {
            builder.setBaseLevel(this.getBaseLevel());
            fieldCnt++;
        }  else if (builder.hasBaseLevel()) {
            // 清理BaseLevel
            builder.clearBaseLevel();
            fieldCnt++;
        }
        if (this.getStartTsMs() != 0L) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStartTsMs()) {
            // 清理StartTsMs
            builder.clearStartTsMs();
            fieldCnt++;
        }
        if (this.getBattleInfo() != com.google.protobuf.ByteString.EMPTY) {
            builder.setBattleInfo(this.getBattleInfo());
            fieldCnt++;
        }  else if (builder.hasBattleInfo()) {
            // 清理BattleInfo
            builder.clearBattleInfo();
            fieldCnt++;
        }
        if (this.getNextCampaignId() != 0) {
            builder.setNextCampaignId(this.getNextCampaignId());
            fieldCnt++;
        }  else if (builder.hasNextCampaignId()) {
            // 清理NextCampaignId
            builder.clearNextCampaignId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerDefendInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CAMPAIGNID)) {
            builder.setCampaignId(this.getCampaignId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASELEVEL)) {
            builder.setBaseLevel(this.getBaseLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLEINFO)) {
            builder.setBattleInfo(this.getBattleInfo());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTCAMPAIGNID)) {
            builder.setNextCampaignId(this.getNextCampaignId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerDefendInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCampaignId()) {
            this.innerSetCampaignId(proto.getCampaignId());
        } else {
            this.innerSetCampaignId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBaseLevel()) {
            this.innerSetBaseLevel(proto.getBaseLevel());
        } else {
            this.innerSetBaseLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartTsMs()) {
            this.innerSetStartTsMs(proto.getStartTsMs());
        } else {
            this.innerSetStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBattleInfo()) {
            this.innerSetBattleInfo(proto.getBattleInfo());
        } else {
            this.innerSetBattleInfo(Constant.DEFAULT_BYTE_STRING_VALUE);
        }
        if (proto.hasNextCampaignId()) {
            this.innerSetNextCampaignId(proto.getNextCampaignId());
        } else {
            this.innerSetNextCampaignId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerDefendInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerDefendInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCampaignId()) {
            this.setCampaignId(proto.getCampaignId());
            fieldCnt++;
        }
        if (proto.hasBaseLevel()) {
            this.setBaseLevel(proto.getBaseLevel());
            fieldCnt++;
        }
        if (proto.hasStartTsMs()) {
            this.setStartTsMs(proto.getStartTsMs());
            fieldCnt++;
        }
        if (proto.hasBattleInfo()) {
            this.setBattleInfo(proto.getBattleInfo());
            fieldCnt++;
        }
        if (proto.hasNextCampaignId()) {
            this.setNextCampaignId(proto.getNextCampaignId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerDefendInfo.Builder getCopySsBuilder() {
        final PlayerDefendInfo.Builder builder = PlayerDefendInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerDefendInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCampaignId() != 0) {
            builder.setCampaignId(this.getCampaignId());
            fieldCnt++;
        }  else if (builder.hasCampaignId()) {
            // 清理CampaignId
            builder.clearCampaignId();
            fieldCnt++;
        }
        if (this.getBaseLevel() != 0) {
            builder.setBaseLevel(this.getBaseLevel());
            fieldCnt++;
        }  else if (builder.hasBaseLevel()) {
            // 清理BaseLevel
            builder.clearBaseLevel();
            fieldCnt++;
        }
        if (this.getStartTsMs() != 0L) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStartTsMs()) {
            // 清理StartTsMs
            builder.clearStartTsMs();
            fieldCnt++;
        }
        if (this.getBattleInfo() != com.google.protobuf.ByteString.EMPTY) {
            builder.setBattleInfo(this.getBattleInfo());
            fieldCnt++;
        }  else if (builder.hasBattleInfo()) {
            // 清理BattleInfo
            builder.clearBattleInfo();
            fieldCnt++;
        }
        if (this.getNextCampaignId() != 0) {
            builder.setNextCampaignId(this.getNextCampaignId());
            fieldCnt++;
        }  else if (builder.hasNextCampaignId()) {
            // 清理NextCampaignId
            builder.clearNextCampaignId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerDefendInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CAMPAIGNID)) {
            builder.setCampaignId(this.getCampaignId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASELEVEL)) {
            builder.setBaseLevel(this.getBaseLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTSMS)) {
            builder.setStartTsMs(this.getStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLEINFO)) {
            builder.setBattleInfo(this.getBattleInfo());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NEXTCAMPAIGNID)) {
            builder.setNextCampaignId(this.getNextCampaignId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerDefendInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCampaignId()) {
            this.innerSetCampaignId(proto.getCampaignId());
        } else {
            this.innerSetCampaignId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBaseLevel()) {
            this.innerSetBaseLevel(proto.getBaseLevel());
        } else {
            this.innerSetBaseLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartTsMs()) {
            this.innerSetStartTsMs(proto.getStartTsMs());
        } else {
            this.innerSetStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBattleInfo()) {
            this.innerSetBattleInfo(proto.getBattleInfo());
        } else {
            this.innerSetBattleInfo(Constant.DEFAULT_BYTE_STRING_VALUE);
        }
        if (proto.hasNextCampaignId()) {
            this.innerSetNextCampaignId(proto.getNextCampaignId());
        } else {
            this.innerSetNextCampaignId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerDefendInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerDefendInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCampaignId()) {
            this.setCampaignId(proto.getCampaignId());
            fieldCnt++;
        }
        if (proto.hasBaseLevel()) {
            this.setBaseLevel(proto.getBaseLevel());
            fieldCnt++;
        }
        if (proto.hasStartTsMs()) {
            this.setStartTsMs(proto.getStartTsMs());
            fieldCnt++;
        }
        if (proto.hasBattleInfo()) {
            this.setBattleInfo(proto.getBattleInfo());
            fieldCnt++;
        }
        if (proto.hasNextCampaignId()) {
            this.setNextCampaignId(proto.getNextCampaignId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerDefendInfo.Builder builder = PlayerDefendInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerDefendInfoProp)) {
            return false;
        }
        final PlayerDefendInfoProp otherNode = (PlayerDefendInfoProp) node;
        if (this.campaignId != otherNode.campaignId) {
            return false;
        }
        if (this.baseLevel != otherNode.baseLevel) {
            return false;
        }
        if (this.startTsMs != otherNode.startTsMs) {
            return false;
        }
        if (this.battleInfo != otherNode.battleInfo) {
            return false;
        }
        if (this.nextCampaignId != otherNode.nextCampaignId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
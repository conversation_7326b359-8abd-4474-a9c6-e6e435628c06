package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Monster.MonsterEntity;
import com.yorha.proto.Struct;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.MonsterPB.MonsterEntityPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructBattlePB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class MonsterProp extends AbstractPropNode {

    public static final int FIELD_INDEX_MONSTERID = 0;
    public static final int FIELD_INDEX_TROOP = 1;
    public static final int FIELD_INDEX_MOVE = 2;
    public static final int FIELD_INDEX_BATTLE = 3;
    public static final int FIELD_INDEX_BUFF = 4;
    public static final int FIELD_INDEX_CAMP = 5;
    public static final int FIELD_INDEX_LIFETIME = 6;
    public static final int FIELD_INDEX_CREATETYPE = 7;
    public static final int FIELD_INDEX_BORNTIME = 8;
    public static final int FIELD_INDEX_DEADTIME = 9;
    public static final int FIELD_INDEX_CAST = 10;
    public static final int FIELD_INDEX_GROUPID = 11;
    public static final int FIELD_INDEX_SUMMONPLAYERID = 12;
    public static final int FIELD_INDEX_CLANID = 13;

    public static final int FIELD_COUNT = 14;

    private long markBits0 = 0L;
    private PropertyChangeListener listener;

    private int monsterId = Constant.DEFAULT_INT_VALUE;
    private TroopProp troop = null;
    private MoveProp move = null;
    private BattleProp battle = null;
    private Int32BuffMapProp buff = null;
    private Camp camp = Camp.forNumber(0);
    private long lifeTime = Constant.DEFAULT_LONG_VALUE;
    private MonsterCreateType createType = MonsterCreateType.forNumber(0);
    private long bornTime = Constant.DEFAULT_LONG_VALUE;
    private long deadTime = Constant.DEFAULT_LONG_VALUE;
    private MonsterCastProp cast = null;
    private int groupId = Constant.DEFAULT_INT_VALUE;
    private long summonPlayerId = Constant.DEFAULT_LONG_VALUE;
    private long clanId = Constant.DEFAULT_LONG_VALUE;

    public MonsterProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MonsterProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get monsterId
     *
     * @return monsterId value
     */
    public int getMonsterId() {
        return this.monsterId;
    }

    /**
     * set monsterId && set marked
     *
     * @param monsterId new value
     * @return current object
     */
    public MonsterProp setMonsterId(int monsterId) {
        if (this.monsterId != monsterId) {
            this.mark(FIELD_INDEX_MONSTERID);
            this.monsterId = monsterId;
        }
        return this;
    }

    /**
     * inner set monsterId
     *
     * @param monsterId new value
     */
    private void innerSetMonsterId(int monsterId) {
        this.monsterId = monsterId;
    }

    /**
     * get troop
     *
     * @return troop value
     */
    public TroopProp getTroop() {
        if (this.troop == null) {
            this.troop = new TroopProp(this, FIELD_INDEX_TROOP);
        }
        return this.troop;
    }

    /**
     * get move
     *
     * @return move value
     */
    public MoveProp getMove() {
        if (this.move == null) {
            this.move = new MoveProp(this, FIELD_INDEX_MOVE);
        }
        return this.move;
    }

    /**
     * get battle
     *
     * @return battle value
     */
    public BattleProp getBattle() {
        if (this.battle == null) {
            this.battle = new BattleProp(this, FIELD_INDEX_BATTLE);
        }
        return this.battle;
    }

    /**
     * get buff
     *
     * @return buff value
     */
    public Int32BuffMapProp getBuff() {
        if (this.buff == null) {
            this.buff = new Int32BuffMapProp(this, FIELD_INDEX_BUFF);
        }
        return this.buff;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putBuffV(BuffProp v) {
        this.getBuff().put(v.getGroupId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public BuffProp addEmptyBuff(Integer k) {
        return this.getBuff().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getBuffSize() {
        if (this.buff == null) {
            return 0;
        }
        return this.buff.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isBuffEmpty() {
        if (this.buff == null) {
            return true;
        }
        return this.buff.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public BuffProp getBuffV(Integer k) {
        if (this.buff == null || !this.buff.containsKey(k)) {
            return null;
        }
        return this.buff.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearBuff() {
        if (this.buff != null) {
            this.buff.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeBuffV(Integer k) {
        if (this.buff != null) {
            this.buff.remove(k);
        }
    }
    /**
     * get camp
     *
     * @return camp value
     */
    public Camp getCamp() {
        return this.camp;
    }

    /**
     * set camp && set marked
     *
     * @param camp new value
     * @return current object
     */
    public MonsterProp setCamp(Camp camp) {
        if (camp == null) {
            throw new NullPointerException();
        }
        if (this.camp != camp) {
            this.mark(FIELD_INDEX_CAMP);
            this.camp = camp;
        }
        return this;
    }

    /**
     * inner set camp
     *
     * @param camp new value
     */
    private void innerSetCamp(Camp camp) {
        this.camp = camp;
    }

    /**
     * get lifeTime
     *
     * @return lifeTime value
     */
    public long getLifeTime() {
        return this.lifeTime;
    }

    /**
     * set lifeTime && set marked
     *
     * @param lifeTime new value
     * @return current object
     */
    public MonsterProp setLifeTime(long lifeTime) {
        if (this.lifeTime != lifeTime) {
            this.mark(FIELD_INDEX_LIFETIME);
            this.lifeTime = lifeTime;
        }
        return this;
    }

    /**
     * inner set lifeTime
     *
     * @param lifeTime new value
     */
    private void innerSetLifeTime(long lifeTime) {
        this.lifeTime = lifeTime;
    }

    /**
     * get createType
     *
     * @return createType value
     */
    public MonsterCreateType getCreateType() {
        return this.createType;
    }

    /**
     * set createType && set marked
     *
     * @param createType new value
     * @return current object
     */
    public MonsterProp setCreateType(MonsterCreateType createType) {
        if (createType == null) {
            throw new NullPointerException();
        }
        if (this.createType != createType) {
            this.mark(FIELD_INDEX_CREATETYPE);
            this.createType = createType;
        }
        return this;
    }

    /**
     * inner set createType
     *
     * @param createType new value
     */
    private void innerSetCreateType(MonsterCreateType createType) {
        this.createType = createType;
    }

    /**
     * get bornTime
     *
     * @return bornTime value
     */
    public long getBornTime() {
        return this.bornTime;
    }

    /**
     * set bornTime && set marked
     *
     * @param bornTime new value
     * @return current object
     */
    public MonsterProp setBornTime(long bornTime) {
        if (this.bornTime != bornTime) {
            this.mark(FIELD_INDEX_BORNTIME);
            this.bornTime = bornTime;
        }
        return this;
    }

    /**
     * inner set bornTime
     *
     * @param bornTime new value
     */
    private void innerSetBornTime(long bornTime) {
        this.bornTime = bornTime;
    }

    /**
     * get deadTime
     *
     * @return deadTime value
     */
    public long getDeadTime() {
        return this.deadTime;
    }

    /**
     * set deadTime && set marked
     *
     * @param deadTime new value
     * @return current object
     */
    public MonsterProp setDeadTime(long deadTime) {
        if (this.deadTime != deadTime) {
            this.mark(FIELD_INDEX_DEADTIME);
            this.deadTime = deadTime;
        }
        return this;
    }

    /**
     * inner set deadTime
     *
     * @param deadTime new value
     */
    private void innerSetDeadTime(long deadTime) {
        this.deadTime = deadTime;
    }

    /**
     * get cast
     *
     * @return cast value
     */
    public MonsterCastProp getCast() {
        if (this.cast == null) {
            this.cast = new MonsterCastProp(this, FIELD_INDEX_CAST);
        }
        return this.cast;
    }

    /**
     * get groupId
     *
     * @return groupId value
     */
    public int getGroupId() {
        return this.groupId;
    }

    /**
     * set groupId && set marked
     *
     * @param groupId new value
     * @return current object
     */
    public MonsterProp setGroupId(int groupId) {
        if (this.groupId != groupId) {
            this.mark(FIELD_INDEX_GROUPID);
            this.groupId = groupId;
        }
        return this;
    }

    /**
     * inner set groupId
     *
     * @param groupId new value
     */
    private void innerSetGroupId(int groupId) {
        this.groupId = groupId;
    }

    /**
     * get summonPlayerId
     *
     * @return summonPlayerId value
     */
    public long getSummonPlayerId() {
        return this.summonPlayerId;
    }

    /**
     * set summonPlayerId && set marked
     *
     * @param summonPlayerId new value
     * @return current object
     */
    public MonsterProp setSummonPlayerId(long summonPlayerId) {
        if (this.summonPlayerId != summonPlayerId) {
            this.mark(FIELD_INDEX_SUMMONPLAYERID);
            this.summonPlayerId = summonPlayerId;
        }
        return this;
    }

    /**
     * inner set summonPlayerId
     *
     * @param summonPlayerId new value
     */
    private void innerSetSummonPlayerId(long summonPlayerId) {
        this.summonPlayerId = summonPlayerId;
    }

    /**
     * get clanId
     *
     * @return clanId value
     */
    public long getClanId() {
        return this.clanId;
    }

    /**
     * set clanId && set marked
     *
     * @param clanId new value
     * @return current object
     */
    public MonsterProp setClanId(long clanId) {
        if (this.clanId != clanId) {
            this.mark(FIELD_INDEX_CLANID);
            this.clanId = clanId;
        }
        return this;
    }

    /**
     * inner set clanId
     *
     * @param clanId new value
     */
    private void innerSetClanId(long clanId) {
        this.clanId = clanId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MonsterEntityPB.Builder getCopyCsBuilder() {
        final MonsterEntityPB.Builder builder = MonsterEntityPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MonsterEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMonsterId() != 0) {
            builder.setMonsterId(this.getMonsterId());
            fieldCnt++;
        }  else if (builder.hasMonsterId()) {
            // 清理MonsterId
            builder.clearMonsterId();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayerPB.TroopPB.Builder tmpBuilder = StructPlayerPB.TroopPB.newBuilder();
            final int tmpFieldCnt = this.troop.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.move != null) {
            StructPB.MovePB.Builder tmpBuilder = StructPB.MovePB.newBuilder();
            final int tmpFieldCnt = this.move.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMove(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMove();
            }
        }  else if (builder.hasMove()) {
            // 清理Move
            builder.clearMove();
            fieldCnt++;
        }
        if (this.battle != null) {
            StructBattlePB.BattlePB.Builder tmpBuilder = StructBattlePB.BattlePB.newBuilder();
            final int tmpFieldCnt = this.battle.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattle();
            }
        }  else if (builder.hasBattle()) {
            // 清理Battle
            builder.clearBattle();
            fieldCnt++;
        }
        if (this.buff != null) {
            StructBattlePB.Int32BuffMapPB.Builder tmpBuilder = StructBattlePB.Int32BuffMapPB.newBuilder();
            final int tmpFieldCnt = this.buff.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuff(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuff();
            }
        }  else if (builder.hasBuff()) {
            // 清理Buff
            builder.clearBuff();
            fieldCnt++;
        }
        if (this.getCamp() != Camp.forNumber(0)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }  else if (builder.hasCamp()) {
            // 清理Camp
            builder.clearCamp();
            fieldCnt++;
        }
        if (this.getLifeTime() != 0L) {
            builder.setLifeTime(this.getLifeTime());
            fieldCnt++;
        }  else if (builder.hasLifeTime()) {
            // 清理LifeTime
            builder.clearLifeTime();
            fieldCnt++;
        }
        if (this.getCreateType() != MonsterCreateType.forNumber(0)) {
            builder.setCreateType(this.getCreateType());
            fieldCnt++;
        }  else if (builder.hasCreateType()) {
            // 清理CreateType
            builder.clearCreateType();
            fieldCnt++;
        }
        if (this.getBornTime() != 0L) {
            builder.setBornTime(this.getBornTime());
            fieldCnt++;
        }  else if (builder.hasBornTime()) {
            // 清理BornTime
            builder.clearBornTime();
            fieldCnt++;
        }
        if (this.getDeadTime() != 0L) {
            builder.setDeadTime(this.getDeadTime());
            fieldCnt++;
        }  else if (builder.hasDeadTime()) {
            // 清理DeadTime
            builder.clearDeadTime();
            fieldCnt++;
        }
        if (this.cast != null) {
            StructPB.MonsterCastPB.Builder tmpBuilder = StructPB.MonsterCastPB.newBuilder();
            final int tmpFieldCnt = this.cast.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCast(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCast();
            }
        }  else if (builder.hasCast()) {
            // 清理Cast
            builder.clearCast();
            fieldCnt++;
        }
        if (this.getGroupId() != 0) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }  else if (builder.hasGroupId()) {
            // 清理GroupId
            builder.clearGroupId();
            fieldCnt++;
        }
        if (this.getSummonPlayerId() != 0L) {
            builder.setSummonPlayerId(this.getSummonPlayerId());
            fieldCnt++;
        }  else if (builder.hasSummonPlayerId()) {
            // 清理SummonPlayerId
            builder.clearSummonPlayerId();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MonsterEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MONSTERID)) {
            builder.setMonsterId(this.getMonsterId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToCs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            final boolean needClear = !builder.hasMove();
            final int tmpFieldCnt = this.move.copyChangeToCs(builder.getMoveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMove();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToCs(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUFF) && this.buff != null) {
            final boolean needClear = !builder.hasBuff();
            final int tmpFieldCnt = this.buff.copyChangeToCs(builder.getBuffBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuff();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LIFETIME)) {
            builder.setLifeTime(this.getLifeTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CREATETYPE)) {
            builder.setCreateType(this.getCreateType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BORNTIME)) {
            builder.setBornTime(this.getBornTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEADTIME)) {
            builder.setDeadTime(this.getDeadTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CAST) && this.cast != null) {
            final boolean needClear = !builder.hasCast();
            final int tmpFieldCnt = this.cast.copyChangeToCs(builder.getCastBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCast();
            }
        }
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SUMMONPLAYERID)) {
            builder.setSummonPlayerId(this.getSummonPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MonsterEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MONSTERID)) {
            builder.setMonsterId(this.getMonsterId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToAndClearDeleteKeysCs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            final boolean needClear = !builder.hasMove();
            final int tmpFieldCnt = this.move.copyChangeToAndClearDeleteKeysCs(builder.getMoveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMove();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToAndClearDeleteKeysCs(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUFF) && this.buff != null) {
            final boolean needClear = !builder.hasBuff();
            final int tmpFieldCnt = this.buff.copyChangeToAndClearDeleteKeysCs(builder.getBuffBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuff();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LIFETIME)) {
            builder.setLifeTime(this.getLifeTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CREATETYPE)) {
            builder.setCreateType(this.getCreateType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BORNTIME)) {
            builder.setBornTime(this.getBornTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEADTIME)) {
            builder.setDeadTime(this.getDeadTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CAST) && this.cast != null) {
            final boolean needClear = !builder.hasCast();
            final int tmpFieldCnt = this.cast.copyChangeToAndClearDeleteKeysCs(builder.getCastBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCast();
            }
        }
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SUMMONPLAYERID)) {
            builder.setSummonPlayerId(this.getSummonPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MonsterEntityPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMonsterId()) {
            this.innerSetMonsterId(proto.getMonsterId());
        } else {
            this.innerSetMonsterId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromCs(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromCs(proto.getTroop());
            }
        }
        if (proto.hasMove()) {
            this.getMove().mergeFromCs(proto.getMove());
        } else {
            if (this.move != null) {
                this.move.mergeFromCs(proto.getMove());
            }
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeFromCs(proto.getBattle());
        } else {
            if (this.battle != null) {
                this.battle.mergeFromCs(proto.getBattle());
            }
        }
        if (proto.hasBuff()) {
            this.getBuff().mergeFromCs(proto.getBuff());
        } else {
            if (this.buff != null) {
                this.buff.mergeFromCs(proto.getBuff());
            }
        }
        if (proto.hasCamp()) {
            this.innerSetCamp(proto.getCamp());
        } else {
            this.innerSetCamp(Camp.forNumber(0));
        }
        if (proto.hasLifeTime()) {
            this.innerSetLifeTime(proto.getLifeTime());
        } else {
            this.innerSetLifeTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCreateType()) {
            this.innerSetCreateType(proto.getCreateType());
        } else {
            this.innerSetCreateType(MonsterCreateType.forNumber(0));
        }
        if (proto.hasBornTime()) {
            this.innerSetBornTime(proto.getBornTime());
        } else {
            this.innerSetBornTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasDeadTime()) {
            this.innerSetDeadTime(proto.getDeadTime());
        } else {
            this.innerSetDeadTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCast()) {
            this.getCast().mergeFromCs(proto.getCast());
        } else {
            if (this.cast != null) {
                this.cast.mergeFromCs(proto.getCast());
            }
        }
        if (proto.hasGroupId()) {
            this.innerSetGroupId(proto.getGroupId());
        } else {
            this.innerSetGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSummonPlayerId()) {
            this.innerSetSummonPlayerId(proto.getSummonPlayerId());
        } else {
            this.innerSetSummonPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MonsterProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MonsterEntityPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMonsterId()) {
            this.setMonsterId(proto.getMonsterId());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromCs(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasMove()) {
            this.getMove().mergeChangeFromCs(proto.getMove());
            fieldCnt++;
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeChangeFromCs(proto.getBattle());
            fieldCnt++;
        }
        if (proto.hasBuff()) {
            this.getBuff().mergeChangeFromCs(proto.getBuff());
            fieldCnt++;
        }
        if (proto.hasCamp()) {
            this.setCamp(proto.getCamp());
            fieldCnt++;
        }
        if (proto.hasLifeTime()) {
            this.setLifeTime(proto.getLifeTime());
            fieldCnt++;
        }
        if (proto.hasCreateType()) {
            this.setCreateType(proto.getCreateType());
            fieldCnt++;
        }
        if (proto.hasBornTime()) {
            this.setBornTime(proto.getBornTime());
            fieldCnt++;
        }
        if (proto.hasDeadTime()) {
            this.setDeadTime(proto.getDeadTime());
            fieldCnt++;
        }
        if (proto.hasCast()) {
            this.getCast().mergeChangeFromCs(proto.getCast());
            fieldCnt++;
        }
        if (proto.hasGroupId()) {
            this.setGroupId(proto.getGroupId());
            fieldCnt++;
        }
        if (proto.hasSummonPlayerId()) {
            this.setSummonPlayerId(proto.getSummonPlayerId());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MonsterEntity.Builder getCopyDbBuilder() {
        final MonsterEntity.Builder builder = MonsterEntity.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MonsterEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMonsterId() != 0) {
            builder.setMonsterId(this.getMonsterId());
            fieldCnt++;
        }  else if (builder.hasMonsterId()) {
            // 清理MonsterId
            builder.clearMonsterId();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayer.Troop.Builder tmpBuilder = StructPlayer.Troop.newBuilder();
            final int tmpFieldCnt = this.troop.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.move != null) {
            Struct.Move.Builder tmpBuilder = Struct.Move.newBuilder();
            final int tmpFieldCnt = this.move.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMove(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMove();
            }
        }  else if (builder.hasMove()) {
            // 清理Move
            builder.clearMove();
            fieldCnt++;
        }
        if (this.battle != null) {
            StructBattle.Battle.Builder tmpBuilder = StructBattle.Battle.newBuilder();
            final int tmpFieldCnt = this.battle.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattle();
            }
        }  else if (builder.hasBattle()) {
            // 清理Battle
            builder.clearBattle();
            fieldCnt++;
        }
        if (this.buff != null) {
            StructBattle.Int32BuffMap.Builder tmpBuilder = StructBattle.Int32BuffMap.newBuilder();
            final int tmpFieldCnt = this.buff.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuff(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuff();
            }
        }  else if (builder.hasBuff()) {
            // 清理Buff
            builder.clearBuff();
            fieldCnt++;
        }
        if (this.getCamp() != Camp.forNumber(0)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }  else if (builder.hasCamp()) {
            // 清理Camp
            builder.clearCamp();
            fieldCnt++;
        }
        if (this.getLifeTime() != 0L) {
            builder.setLifeTime(this.getLifeTime());
            fieldCnt++;
        }  else if (builder.hasLifeTime()) {
            // 清理LifeTime
            builder.clearLifeTime();
            fieldCnt++;
        }
        if (this.getCreateType() != MonsterCreateType.forNumber(0)) {
            builder.setCreateType(this.getCreateType());
            fieldCnt++;
        }  else if (builder.hasCreateType()) {
            // 清理CreateType
            builder.clearCreateType();
            fieldCnt++;
        }
        if (this.getBornTime() != 0L) {
            builder.setBornTime(this.getBornTime());
            fieldCnt++;
        }  else if (builder.hasBornTime()) {
            // 清理BornTime
            builder.clearBornTime();
            fieldCnt++;
        }
        if (this.getDeadTime() != 0L) {
            builder.setDeadTime(this.getDeadTime());
            fieldCnt++;
        }  else if (builder.hasDeadTime()) {
            // 清理DeadTime
            builder.clearDeadTime();
            fieldCnt++;
        }
        if (this.cast != null) {
            Struct.MonsterCast.Builder tmpBuilder = Struct.MonsterCast.newBuilder();
            final int tmpFieldCnt = this.cast.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCast(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCast();
            }
        }  else if (builder.hasCast()) {
            // 清理Cast
            builder.clearCast();
            fieldCnt++;
        }
        if (this.getGroupId() != 0) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }  else if (builder.hasGroupId()) {
            // 清理GroupId
            builder.clearGroupId();
            fieldCnt++;
        }
        if (this.getSummonPlayerId() != 0L) {
            builder.setSummonPlayerId(this.getSummonPlayerId());
            fieldCnt++;
        }  else if (builder.hasSummonPlayerId()) {
            // 清理SummonPlayerId
            builder.clearSummonPlayerId();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MonsterEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MONSTERID)) {
            builder.setMonsterId(this.getMonsterId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToDb(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            final boolean needClear = !builder.hasMove();
            final int tmpFieldCnt = this.move.copyChangeToDb(builder.getMoveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMove();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToDb(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUFF) && this.buff != null) {
            final boolean needClear = !builder.hasBuff();
            final int tmpFieldCnt = this.buff.copyChangeToDb(builder.getBuffBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuff();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LIFETIME)) {
            builder.setLifeTime(this.getLifeTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CREATETYPE)) {
            builder.setCreateType(this.getCreateType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BORNTIME)) {
            builder.setBornTime(this.getBornTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEADTIME)) {
            builder.setDeadTime(this.getDeadTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CAST) && this.cast != null) {
            final boolean needClear = !builder.hasCast();
            final int tmpFieldCnt = this.cast.copyChangeToDb(builder.getCastBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCast();
            }
        }
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SUMMONPLAYERID)) {
            builder.setSummonPlayerId(this.getSummonPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MonsterEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMonsterId()) {
            this.innerSetMonsterId(proto.getMonsterId());
        } else {
            this.innerSetMonsterId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromDb(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromDb(proto.getTroop());
            }
        }
        if (proto.hasMove()) {
            this.getMove().mergeFromDb(proto.getMove());
        } else {
            if (this.move != null) {
                this.move.mergeFromDb(proto.getMove());
            }
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeFromDb(proto.getBattle());
        } else {
            if (this.battle != null) {
                this.battle.mergeFromDb(proto.getBattle());
            }
        }
        if (proto.hasBuff()) {
            this.getBuff().mergeFromDb(proto.getBuff());
        } else {
            if (this.buff != null) {
                this.buff.mergeFromDb(proto.getBuff());
            }
        }
        if (proto.hasCamp()) {
            this.innerSetCamp(proto.getCamp());
        } else {
            this.innerSetCamp(Camp.forNumber(0));
        }
        if (proto.hasLifeTime()) {
            this.innerSetLifeTime(proto.getLifeTime());
        } else {
            this.innerSetLifeTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCreateType()) {
            this.innerSetCreateType(proto.getCreateType());
        } else {
            this.innerSetCreateType(MonsterCreateType.forNumber(0));
        }
        if (proto.hasBornTime()) {
            this.innerSetBornTime(proto.getBornTime());
        } else {
            this.innerSetBornTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasDeadTime()) {
            this.innerSetDeadTime(proto.getDeadTime());
        } else {
            this.innerSetDeadTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCast()) {
            this.getCast().mergeFromDb(proto.getCast());
        } else {
            if (this.cast != null) {
                this.cast.mergeFromDb(proto.getCast());
            }
        }
        if (proto.hasGroupId()) {
            this.innerSetGroupId(proto.getGroupId());
        } else {
            this.innerSetGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSummonPlayerId()) {
            this.innerSetSummonPlayerId(proto.getSummonPlayerId());
        } else {
            this.innerSetSummonPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MonsterProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MonsterEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMonsterId()) {
            this.setMonsterId(proto.getMonsterId());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromDb(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasMove()) {
            this.getMove().mergeChangeFromDb(proto.getMove());
            fieldCnt++;
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeChangeFromDb(proto.getBattle());
            fieldCnt++;
        }
        if (proto.hasBuff()) {
            this.getBuff().mergeChangeFromDb(proto.getBuff());
            fieldCnt++;
        }
        if (proto.hasCamp()) {
            this.setCamp(proto.getCamp());
            fieldCnt++;
        }
        if (proto.hasLifeTime()) {
            this.setLifeTime(proto.getLifeTime());
            fieldCnt++;
        }
        if (proto.hasCreateType()) {
            this.setCreateType(proto.getCreateType());
            fieldCnt++;
        }
        if (proto.hasBornTime()) {
            this.setBornTime(proto.getBornTime());
            fieldCnt++;
        }
        if (proto.hasDeadTime()) {
            this.setDeadTime(proto.getDeadTime());
            fieldCnt++;
        }
        if (proto.hasCast()) {
            this.getCast().mergeChangeFromDb(proto.getCast());
            fieldCnt++;
        }
        if (proto.hasGroupId()) {
            this.setGroupId(proto.getGroupId());
            fieldCnt++;
        }
        if (proto.hasSummonPlayerId()) {
            this.setSummonPlayerId(proto.getSummonPlayerId());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MonsterEntity.Builder getCopySsBuilder() {
        final MonsterEntity.Builder builder = MonsterEntity.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MonsterEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMonsterId() != 0) {
            builder.setMonsterId(this.getMonsterId());
            fieldCnt++;
        }  else if (builder.hasMonsterId()) {
            // 清理MonsterId
            builder.clearMonsterId();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayer.Troop.Builder tmpBuilder = StructPlayer.Troop.newBuilder();
            final int tmpFieldCnt = this.troop.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.move != null) {
            Struct.Move.Builder tmpBuilder = Struct.Move.newBuilder();
            final int tmpFieldCnt = this.move.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMove(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMove();
            }
        }  else if (builder.hasMove()) {
            // 清理Move
            builder.clearMove();
            fieldCnt++;
        }
        if (this.battle != null) {
            StructBattle.Battle.Builder tmpBuilder = StructBattle.Battle.newBuilder();
            final int tmpFieldCnt = this.battle.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattle(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattle();
            }
        }  else if (builder.hasBattle()) {
            // 清理Battle
            builder.clearBattle();
            fieldCnt++;
        }
        if (this.buff != null) {
            StructBattle.Int32BuffMap.Builder tmpBuilder = StructBattle.Int32BuffMap.newBuilder();
            final int tmpFieldCnt = this.buff.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuff(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuff();
            }
        }  else if (builder.hasBuff()) {
            // 清理Buff
            builder.clearBuff();
            fieldCnt++;
        }
        if (this.getCamp() != Camp.forNumber(0)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }  else if (builder.hasCamp()) {
            // 清理Camp
            builder.clearCamp();
            fieldCnt++;
        }
        if (this.getLifeTime() != 0L) {
            builder.setLifeTime(this.getLifeTime());
            fieldCnt++;
        }  else if (builder.hasLifeTime()) {
            // 清理LifeTime
            builder.clearLifeTime();
            fieldCnt++;
        }
        if (this.getCreateType() != MonsterCreateType.forNumber(0)) {
            builder.setCreateType(this.getCreateType());
            fieldCnt++;
        }  else if (builder.hasCreateType()) {
            // 清理CreateType
            builder.clearCreateType();
            fieldCnt++;
        }
        if (this.getBornTime() != 0L) {
            builder.setBornTime(this.getBornTime());
            fieldCnt++;
        }  else if (builder.hasBornTime()) {
            // 清理BornTime
            builder.clearBornTime();
            fieldCnt++;
        }
        if (this.getDeadTime() != 0L) {
            builder.setDeadTime(this.getDeadTime());
            fieldCnt++;
        }  else if (builder.hasDeadTime()) {
            // 清理DeadTime
            builder.clearDeadTime();
            fieldCnt++;
        }
        if (this.cast != null) {
            Struct.MonsterCast.Builder tmpBuilder = Struct.MonsterCast.newBuilder();
            final int tmpFieldCnt = this.cast.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCast(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCast();
            }
        }  else if (builder.hasCast()) {
            // 清理Cast
            builder.clearCast();
            fieldCnt++;
        }
        if (this.getGroupId() != 0) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }  else if (builder.hasGroupId()) {
            // 清理GroupId
            builder.clearGroupId();
            fieldCnt++;
        }
        if (this.getSummonPlayerId() != 0L) {
            builder.setSummonPlayerId(this.getSummonPlayerId());
            fieldCnt++;
        }  else if (builder.hasSummonPlayerId()) {
            // 清理SummonPlayerId
            builder.clearSummonPlayerId();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MonsterEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MONSTERID)) {
            builder.setMonsterId(this.getMonsterId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToSs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            final boolean needClear = !builder.hasMove();
            final int tmpFieldCnt = this.move.copyChangeToSs(builder.getMoveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMove();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            final boolean needClear = !builder.hasBattle();
            final int tmpFieldCnt = this.battle.copyChangeToSs(builder.getBattleBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattle();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUFF) && this.buff != null) {
            final boolean needClear = !builder.hasBuff();
            final int tmpFieldCnt = this.buff.copyChangeToSs(builder.getBuffBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuff();
            }
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LIFETIME)) {
            builder.setLifeTime(this.getLifeTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CREATETYPE)) {
            builder.setCreateType(this.getCreateType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BORNTIME)) {
            builder.setBornTime(this.getBornTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEADTIME)) {
            builder.setDeadTime(this.getDeadTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CAST) && this.cast != null) {
            final boolean needClear = !builder.hasCast();
            final int tmpFieldCnt = this.cast.copyChangeToSs(builder.getCastBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCast();
            }
        }
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SUMMONPLAYERID)) {
            builder.setSummonPlayerId(this.getSummonPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MonsterEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMonsterId()) {
            this.innerSetMonsterId(proto.getMonsterId());
        } else {
            this.innerSetMonsterId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromSs(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromSs(proto.getTroop());
            }
        }
        if (proto.hasMove()) {
            this.getMove().mergeFromSs(proto.getMove());
        } else {
            if (this.move != null) {
                this.move.mergeFromSs(proto.getMove());
            }
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeFromSs(proto.getBattle());
        } else {
            if (this.battle != null) {
                this.battle.mergeFromSs(proto.getBattle());
            }
        }
        if (proto.hasBuff()) {
            this.getBuff().mergeFromSs(proto.getBuff());
        } else {
            if (this.buff != null) {
                this.buff.mergeFromSs(proto.getBuff());
            }
        }
        if (proto.hasCamp()) {
            this.innerSetCamp(proto.getCamp());
        } else {
            this.innerSetCamp(Camp.forNumber(0));
        }
        if (proto.hasLifeTime()) {
            this.innerSetLifeTime(proto.getLifeTime());
        } else {
            this.innerSetLifeTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCreateType()) {
            this.innerSetCreateType(proto.getCreateType());
        } else {
            this.innerSetCreateType(MonsterCreateType.forNumber(0));
        }
        if (proto.hasBornTime()) {
            this.innerSetBornTime(proto.getBornTime());
        } else {
            this.innerSetBornTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasDeadTime()) {
            this.innerSetDeadTime(proto.getDeadTime());
        } else {
            this.innerSetDeadTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCast()) {
            this.getCast().mergeFromSs(proto.getCast());
        } else {
            if (this.cast != null) {
                this.cast.mergeFromSs(proto.getCast());
            }
        }
        if (proto.hasGroupId()) {
            this.innerSetGroupId(proto.getGroupId());
        } else {
            this.innerSetGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSummonPlayerId()) {
            this.innerSetSummonPlayerId(proto.getSummonPlayerId());
        } else {
            this.innerSetSummonPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MonsterProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MonsterEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMonsterId()) {
            this.setMonsterId(proto.getMonsterId());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromSs(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasMove()) {
            this.getMove().mergeChangeFromSs(proto.getMove());
            fieldCnt++;
        }
        if (proto.hasBattle()) {
            this.getBattle().mergeChangeFromSs(proto.getBattle());
            fieldCnt++;
        }
        if (proto.hasBuff()) {
            this.getBuff().mergeChangeFromSs(proto.getBuff());
            fieldCnt++;
        }
        if (proto.hasCamp()) {
            this.setCamp(proto.getCamp());
            fieldCnt++;
        }
        if (proto.hasLifeTime()) {
            this.setLifeTime(proto.getLifeTime());
            fieldCnt++;
        }
        if (proto.hasCreateType()) {
            this.setCreateType(proto.getCreateType());
            fieldCnt++;
        }
        if (proto.hasBornTime()) {
            this.setBornTime(proto.getBornTime());
            fieldCnt++;
        }
        if (proto.hasDeadTime()) {
            this.setDeadTime(proto.getDeadTime());
            fieldCnt++;
        }
        if (proto.hasCast()) {
            this.getCast().mergeChangeFromSs(proto.getCast());
            fieldCnt++;
        }
        if (proto.hasGroupId()) {
            this.setGroupId(proto.getGroupId());
            fieldCnt++;
        }
        if (proto.hasSummonPlayerId()) {
            this.setSummonPlayerId(proto.getSummonPlayerId());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MonsterEntity.Builder builder = MonsterEntity.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            this.troop.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            this.move.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BATTLE) && this.battle != null) {
            this.battle.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BUFF) && this.buff != null) {
            this.buff.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CAST) && this.cast != null) {
            this.cast.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.troop != null) {
            this.troop.markAll();
        }
        if (this.move != null) {
            this.move.markAll();
        }
        if (this.battle != null) {
            this.battle.markAll();
        }
        if (this.buff != null) {
            this.buff.markAll();
        }
        if (this.cast != null) {
            this.cast.markAll();
        }
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("MonsterProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("MonsterProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MonsterProp)) {
            return false;
        }
        final MonsterProp otherNode = (MonsterProp) node;
        if (this.monsterId != otherNode.monsterId) {
            return false;
        }
        if (!this.getTroop().compareDataTo(otherNode.getTroop())) {
            return false;
        }
        if (!this.getMove().compareDataTo(otherNode.getMove())) {
            return false;
        }
        if (!this.getBattle().compareDataTo(otherNode.getBattle())) {
            return false;
        }
        if (!this.getBuff().compareDataTo(otherNode.getBuff())) {
            return false;
        }
        if (this.camp != otherNode.camp) {
            return false;
        }
        if (this.lifeTime != otherNode.lifeTime) {
            return false;
        }
        if (this.createType != otherNode.createType) {
            return false;
        }
        if (this.bornTime != otherNode.bornTime) {
            return false;
        }
        if (this.deadTime != otherNode.deadTime) {
            return false;
        }
        if (!this.getCast().compareDataTo(otherNode.getCast())) {
            return false;
        }
        if (this.groupId != otherNode.groupId) {
            return false;
        }
        if (this.summonPlayerId != otherNode.summonPlayerId) {
            return false;
        }
        if (this.clanId != otherNode.clanId) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    public static MonsterProp of(MonsterEntity fullAttrDb, MonsterEntity changeAttrDb) {
        MonsterProp prop = new MonsterProp();
        prop.mergeFromDb(fullAttrDb);
        prop.mergeChangeFromDb(changeAttrDb);
        prop.unMarkAll();
        return prop;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 50;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
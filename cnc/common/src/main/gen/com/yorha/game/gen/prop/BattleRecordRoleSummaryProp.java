package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructBattle.BattleRecordRoleSummary;
import com.yorha.proto.Struct;
import com.yorha.proto.StructBattlePB.BattleRecordRoleSummaryPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class BattleRecordRoleSummaryProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CLANNAME = 0;
    public static final int FIELD_INDEX_TOTAL = 1;
    public static final int FIELD_INDEX_LEFTALIVE = 2;
    public static final int FIELD_INDEX_CARDHEAD = 3;
    public static final int FIELD_INDEX_ROLETYPE = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private String clanName = Constant.DEFAULT_STR_VALUE;
    private int total = Constant.DEFAULT_INT_VALUE;
    private int leftAlive = Constant.DEFAULT_INT_VALUE;
    private PlayerCardHeadProp cardHead = null;
    private SceneObjType roleType = SceneObjType.forNumber(0);

    public BattleRecordRoleSummaryProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BattleRecordRoleSummaryProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get clanName
     *
     * @return clanName value
     */
    public String getClanName() {
        return this.clanName;
    }

    /**
     * set clanName && set marked
     *
     * @param clanName new value
     * @return current object
     */
    public BattleRecordRoleSummaryProp setClanName(String clanName) {
        if (clanName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanName, clanName)) {
            this.mark(FIELD_INDEX_CLANNAME);
            this.clanName = clanName;
        }
        return this;
    }

    /**
     * inner set clanName
     *
     * @param clanName new value
     */
    private void innerSetClanName(String clanName) {
        this.clanName = clanName;
    }

    /**
     * get total
     *
     * @return total value
     */
    public int getTotal() {
        return this.total;
    }

    /**
     * set total && set marked
     *
     * @param total new value
     * @return current object
     */
    public BattleRecordRoleSummaryProp setTotal(int total) {
        if (this.total != total) {
            this.mark(FIELD_INDEX_TOTAL);
            this.total = total;
        }
        return this;
    }

    /**
     * inner set total
     *
     * @param total new value
     */
    private void innerSetTotal(int total) {
        this.total = total;
    }

    /**
     * get leftAlive
     *
     * @return leftAlive value
     */
    public int getLeftAlive() {
        return this.leftAlive;
    }

    /**
     * set leftAlive && set marked
     *
     * @param leftAlive new value
     * @return current object
     */
    public BattleRecordRoleSummaryProp setLeftAlive(int leftAlive) {
        if (this.leftAlive != leftAlive) {
            this.mark(FIELD_INDEX_LEFTALIVE);
            this.leftAlive = leftAlive;
        }
        return this;
    }

    /**
     * inner set leftAlive
     *
     * @param leftAlive new value
     */
    private void innerSetLeftAlive(int leftAlive) {
        this.leftAlive = leftAlive;
    }

    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }

    /**
     * get roleType
     *
     * @return roleType value
     */
    public SceneObjType getRoleType() {
        return this.roleType;
    }

    /**
     * set roleType && set marked
     *
     * @param roleType new value
     * @return current object
     */
    public BattleRecordRoleSummaryProp setRoleType(SceneObjType roleType) {
        if (roleType == null) {
            throw new NullPointerException();
        }
        if (this.roleType != roleType) {
            this.mark(FIELD_INDEX_ROLETYPE);
            this.roleType = roleType;
        }
        return this;
    }

    /**
     * inner set roleType
     *
     * @param roleType new value
     */
    private void innerSetRoleType(SceneObjType roleType) {
        this.roleType = roleType;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRoleSummaryPB.Builder getCopyCsBuilder() {
        final BattleRecordRoleSummaryPB.Builder builder = BattleRecordRoleSummaryPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BattleRecordRoleSummaryPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }  else if (builder.hasClanName()) {
            // 清理ClanName
            builder.clearClanName();
            fieldCnt++;
        }
        if (this.getTotal() != 0) {
            builder.setTotal(this.getTotal());
            fieldCnt++;
        }  else if (builder.hasTotal()) {
            // 清理Total
            builder.clearTotal();
            fieldCnt++;
        }
        if (this.getLeftAlive() != 0) {
            builder.setLeftAlive(this.getLeftAlive());
            fieldCnt++;
        }  else if (builder.hasLeftAlive()) {
            // 清理LeftAlive
            builder.clearLeftAlive();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.getRoleType() != SceneObjType.forNumber(0)) {
            builder.setRoleType(this.getRoleType());
            fieldCnt++;
        }  else if (builder.hasRoleType()) {
            // 清理RoleType
            builder.clearRoleType();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BattleRecordRoleSummaryPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTAL)) {
            builder.setTotal(this.getTotal());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEFTALIVE)) {
            builder.setLeftAlive(this.getLeftAlive());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_ROLETYPE)) {
            builder.setRoleType(this.getRoleType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BattleRecordRoleSummaryPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTAL)) {
            builder.setTotal(this.getTotal());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEFTALIVE)) {
            builder.setLeftAlive(this.getLeftAlive());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToAndClearDeleteKeysCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_ROLETYPE)) {
            builder.setRoleType(this.getRoleType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BattleRecordRoleSummaryPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasClanName()) {
            this.innerSetClanName(proto.getClanName());
        } else {
            this.innerSetClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTotal()) {
            this.innerSetTotal(proto.getTotal());
        } else {
            this.innerSetTotal(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLeftAlive()) {
            this.innerSetLeftAlive(proto.getLeftAlive());
        } else {
            this.innerSetLeftAlive(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromCs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromCs(proto.getCardHead());
            }
        }
        if (proto.hasRoleType()) {
            this.innerSetRoleType(proto.getRoleType());
        } else {
            this.innerSetRoleType(SceneObjType.forNumber(0));
        }
        this.markAll();
        return BattleRecordRoleSummaryProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BattleRecordRoleSummaryPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasClanName()) {
            this.setClanName(proto.getClanName());
            fieldCnt++;
        }
        if (proto.hasTotal()) {
            this.setTotal(proto.getTotal());
            fieldCnt++;
        }
        if (proto.hasLeftAlive()) {
            this.setLeftAlive(proto.getLeftAlive());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromCs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasRoleType()) {
            this.setRoleType(proto.getRoleType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRoleSummary.Builder getCopyDbBuilder() {
        final BattleRecordRoleSummary.Builder builder = BattleRecordRoleSummary.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(BattleRecordRoleSummary.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }  else if (builder.hasClanName()) {
            // 清理ClanName
            builder.clearClanName();
            fieldCnt++;
        }
        if (this.getTotal() != 0) {
            builder.setTotal(this.getTotal());
            fieldCnt++;
        }  else if (builder.hasTotal()) {
            // 清理Total
            builder.clearTotal();
            fieldCnt++;
        }
        if (this.getLeftAlive() != 0) {
            builder.setLeftAlive(this.getLeftAlive());
            fieldCnt++;
        }  else if (builder.hasLeftAlive()) {
            // 清理LeftAlive
            builder.clearLeftAlive();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.getRoleType() != SceneObjType.forNumber(0)) {
            builder.setRoleType(this.getRoleType());
            fieldCnt++;
        }  else if (builder.hasRoleType()) {
            // 清理RoleType
            builder.clearRoleType();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(BattleRecordRoleSummary.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTAL)) {
            builder.setTotal(this.getTotal());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEFTALIVE)) {
            builder.setLeftAlive(this.getLeftAlive());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToDb(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_ROLETYPE)) {
            builder.setRoleType(this.getRoleType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(BattleRecordRoleSummary proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasClanName()) {
            this.innerSetClanName(proto.getClanName());
        } else {
            this.innerSetClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTotal()) {
            this.innerSetTotal(proto.getTotal());
        } else {
            this.innerSetTotal(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLeftAlive()) {
            this.innerSetLeftAlive(proto.getLeftAlive());
        } else {
            this.innerSetLeftAlive(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromDb(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromDb(proto.getCardHead());
            }
        }
        if (proto.hasRoleType()) {
            this.innerSetRoleType(proto.getRoleType());
        } else {
            this.innerSetRoleType(SceneObjType.forNumber(0));
        }
        this.markAll();
        return BattleRecordRoleSummaryProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(BattleRecordRoleSummary proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasClanName()) {
            this.setClanName(proto.getClanName());
            fieldCnt++;
        }
        if (proto.hasTotal()) {
            this.setTotal(proto.getTotal());
            fieldCnt++;
        }
        if (proto.hasLeftAlive()) {
            this.setLeftAlive(proto.getLeftAlive());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromDb(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasRoleType()) {
            this.setRoleType(proto.getRoleType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRoleSummary.Builder getCopySsBuilder() {
        final BattleRecordRoleSummary.Builder builder = BattleRecordRoleSummary.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(BattleRecordRoleSummary.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }  else if (builder.hasClanName()) {
            // 清理ClanName
            builder.clearClanName();
            fieldCnt++;
        }
        if (this.getTotal() != 0) {
            builder.setTotal(this.getTotal());
            fieldCnt++;
        }  else if (builder.hasTotal()) {
            // 清理Total
            builder.clearTotal();
            fieldCnt++;
        }
        if (this.getLeftAlive() != 0) {
            builder.setLeftAlive(this.getLeftAlive());
            fieldCnt++;
        }  else if (builder.hasLeftAlive()) {
            // 清理LeftAlive
            builder.clearLeftAlive();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.getRoleType() != SceneObjType.forNumber(0)) {
            builder.setRoleType(this.getRoleType());
            fieldCnt++;
        }  else if (builder.hasRoleType()) {
            // 清理RoleType
            builder.clearRoleType();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(BattleRecordRoleSummary.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTAL)) {
            builder.setTotal(this.getTotal());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEFTALIVE)) {
            builder.setLeftAlive(this.getLeftAlive());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_ROLETYPE)) {
            builder.setRoleType(this.getRoleType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(BattleRecordRoleSummary proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasClanName()) {
            this.innerSetClanName(proto.getClanName());
        } else {
            this.innerSetClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTotal()) {
            this.innerSetTotal(proto.getTotal());
        } else {
            this.innerSetTotal(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLeftAlive()) {
            this.innerSetLeftAlive(proto.getLeftAlive());
        } else {
            this.innerSetLeftAlive(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        if (proto.hasRoleType()) {
            this.innerSetRoleType(proto.getRoleType());
        } else {
            this.innerSetRoleType(SceneObjType.forNumber(0));
        }
        this.markAll();
        return BattleRecordRoleSummaryProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(BattleRecordRoleSummary proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasClanName()) {
            this.setClanName(proto.getClanName());
            fieldCnt++;
        }
        if (proto.hasTotal()) {
            this.setTotal(proto.getTotal());
            fieldCnt++;
        }
        if (proto.hasLeftAlive()) {
            this.setLeftAlive(proto.getLeftAlive());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasRoleType()) {
            this.setRoleType(proto.getRoleType());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        BattleRecordRoleSummary.Builder builder = BattleRecordRoleSummary.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BattleRecordRoleSummaryProp)) {
            return false;
        }
        final BattleRecordRoleSummaryProp otherNode = (BattleRecordRoleSummaryProp) node;
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanName, otherNode.clanName)) {
            return false;
        }
        if (this.total != otherNode.total) {
            return false;
        }
        if (this.leftAlive != otherNode.leftAlive) {
            return false;
        }
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        if (this.roleType != otherNode.roleType) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
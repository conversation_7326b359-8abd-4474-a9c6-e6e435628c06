package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.ScenePlayerArmyModel;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.ScenePlayerArmyModelPB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class ScenePlayerArmyModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ARMY = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int64ScenePlayerArmyStatusMapProp army = null;

    public ScenePlayerArmyModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ScenePlayerArmyModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get army
     *
     * @return army value
     */
    public Int64ScenePlayerArmyStatusMapProp getArmy() {
        if (this.army == null) {
            this.army = new Int64ScenePlayerArmyStatusMapProp(this, FIELD_INDEX_ARMY);
        }
        return this.army;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putArmyV(ScenePlayerArmyStatusProp v) {
        this.getArmy().put(v.getArmyId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ScenePlayerArmyStatusProp addEmptyArmy(Long k) {
        return this.getArmy().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getArmySize() {
        if (this.army == null) {
            return 0;
        }
        return this.army.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isArmyEmpty() {
        if (this.army == null) {
            return true;
        }
        return this.army.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ScenePlayerArmyStatusProp getArmyV(Long k) {
        if (this.army == null || !this.army.containsKey(k)) {
            return null;
        }
        return this.army.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearArmy() {
        if (this.army != null) {
            this.army.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeArmyV(Long k) {
        if (this.army != null) {
            this.army.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerArmyModelPB.Builder getCopyCsBuilder() {
        final ScenePlayerArmyModelPB.Builder builder = ScenePlayerArmyModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ScenePlayerArmyModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.army != null) {
            PlayerPB.Int64ScenePlayerArmyStatusMapPB.Builder tmpBuilder = PlayerPB.Int64ScenePlayerArmyStatusMapPB.newBuilder();
            final int tmpFieldCnt = this.army.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArmy(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArmy();
            }
        }  else if (builder.hasArmy()) {
            // 清理Army
            builder.clearArmy();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ScenePlayerArmyModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMY) && this.army != null) {
            final boolean needClear = !builder.hasArmy();
            final int tmpFieldCnt = this.army.copyChangeToCs(builder.getArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmy();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ScenePlayerArmyModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMY) && this.army != null) {
            final boolean needClear = !builder.hasArmy();
            final int tmpFieldCnt = this.army.copyChangeToAndClearDeleteKeysCs(builder.getArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmy();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ScenePlayerArmyModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasArmy()) {
            this.getArmy().mergeFromCs(proto.getArmy());
        } else {
            if (this.army != null) {
                this.army.mergeFromCs(proto.getArmy());
            }
        }
        this.markAll();
        return ScenePlayerArmyModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ScenePlayerArmyModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasArmy()) {
            this.getArmy().mergeChangeFromCs(proto.getArmy());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerArmyModel.Builder getCopyDbBuilder() {
        final ScenePlayerArmyModel.Builder builder = ScenePlayerArmyModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ScenePlayerArmyModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.army != null) {
            Player.Int64ScenePlayerArmyStatusMap.Builder tmpBuilder = Player.Int64ScenePlayerArmyStatusMap.newBuilder();
            final int tmpFieldCnt = this.army.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArmy(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArmy();
            }
        }  else if (builder.hasArmy()) {
            // 清理Army
            builder.clearArmy();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ScenePlayerArmyModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMY) && this.army != null) {
            final boolean needClear = !builder.hasArmy();
            final int tmpFieldCnt = this.army.copyChangeToDb(builder.getArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmy();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ScenePlayerArmyModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasArmy()) {
            this.getArmy().mergeFromDb(proto.getArmy());
        } else {
            if (this.army != null) {
                this.army.mergeFromDb(proto.getArmy());
            }
        }
        this.markAll();
        return ScenePlayerArmyModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ScenePlayerArmyModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasArmy()) {
            this.getArmy().mergeChangeFromDb(proto.getArmy());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerArmyModel.Builder getCopySsBuilder() {
        final ScenePlayerArmyModel.Builder builder = ScenePlayerArmyModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ScenePlayerArmyModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.army != null) {
            Player.Int64ScenePlayerArmyStatusMap.Builder tmpBuilder = Player.Int64ScenePlayerArmyStatusMap.newBuilder();
            final int tmpFieldCnt = this.army.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArmy(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArmy();
            }
        }  else if (builder.hasArmy()) {
            // 清理Army
            builder.clearArmy();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ScenePlayerArmyModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMY) && this.army != null) {
            final boolean needClear = !builder.hasArmy();
            final int tmpFieldCnt = this.army.copyChangeToSs(builder.getArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmy();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ScenePlayerArmyModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasArmy()) {
            this.getArmy().mergeFromSs(proto.getArmy());
        } else {
            if (this.army != null) {
                this.army.mergeFromSs(proto.getArmy());
            }
        }
        this.markAll();
        return ScenePlayerArmyModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ScenePlayerArmyModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasArmy()) {
            this.getArmy().mergeChangeFromSs(proto.getArmy());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ScenePlayerArmyModel.Builder builder = ScenePlayerArmyModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ARMY) && this.army != null) {
            this.army.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.army != null) {
            this.army.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ScenePlayerArmyModelProp)) {
            return false;
        }
        final ScenePlayerArmyModelProp otherNode = (ScenePlayerArmyModelProp) node;
        if (!this.getArmy().compareDataTo(otherNode.getArmy())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerPlaneModel;
import com.yorha.proto.Player;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.PlayerPlaneModelPB;
import com.yorha.proto.PlayerPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerPlaneModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_PLAYERBATTLEPLANE = 0;
    public static final int FIELD_INDEX_PLAYERTRANSPORTPLANE = 1;
    public static final int FIELD_INDEX_PLAYERSPYPLANE = 2;
    public static final int FIELD_INDEX_SYSASSISTINFOS = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private Int64PlayerBattlePlaneMapProp playerBattlePlane = null;
    private Int64PlayerTransportPlaneMapProp playerTransportPlane = null;
    private Int64PlayerSpyPlaneMapProp playerSpyPlane = null;
    private Int64SysAssistInfoMapProp sysAssistInfos = null;

    public PlayerPlaneModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerPlaneModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get playerBattlePlane
     *
     * @return playerBattlePlane value
     */
    public Int64PlayerBattlePlaneMapProp getPlayerBattlePlane() {
        if (this.playerBattlePlane == null) {
            this.playerBattlePlane = new Int64PlayerBattlePlaneMapProp(this, FIELD_INDEX_PLAYERBATTLEPLANE);
        }
        return this.playerBattlePlane;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putPlayerBattlePlaneV(PlayerBattlePlaneProp v) {
        this.getPlayerBattlePlane().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerBattlePlaneProp addEmptyPlayerBattlePlane(Long k) {
        return this.getPlayerBattlePlane().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getPlayerBattlePlaneSize() {
        if (this.playerBattlePlane == null) {
            return 0;
        }
        return this.playerBattlePlane.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isPlayerBattlePlaneEmpty() {
        if (this.playerBattlePlane == null) {
            return true;
        }
        return this.playerBattlePlane.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerBattlePlaneProp getPlayerBattlePlaneV(Long k) {
        if (this.playerBattlePlane == null || !this.playerBattlePlane.containsKey(k)) {
            return null;
        }
        return this.playerBattlePlane.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearPlayerBattlePlane() {
        if (this.playerBattlePlane != null) {
            this.playerBattlePlane.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removePlayerBattlePlaneV(Long k) {
        if (this.playerBattlePlane != null) {
            this.playerBattlePlane.remove(k);
        }
    }
    /**
     * get playerTransportPlane
     *
     * @return playerTransportPlane value
     */
    public Int64PlayerTransportPlaneMapProp getPlayerTransportPlane() {
        if (this.playerTransportPlane == null) {
            this.playerTransportPlane = new Int64PlayerTransportPlaneMapProp(this, FIELD_INDEX_PLAYERTRANSPORTPLANE);
        }
        return this.playerTransportPlane;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putPlayerTransportPlaneV(PlayerTransportPlaneProp v) {
        this.getPlayerTransportPlane().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerTransportPlaneProp addEmptyPlayerTransportPlane(Long k) {
        return this.getPlayerTransportPlane().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getPlayerTransportPlaneSize() {
        if (this.playerTransportPlane == null) {
            return 0;
        }
        return this.playerTransportPlane.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isPlayerTransportPlaneEmpty() {
        if (this.playerTransportPlane == null) {
            return true;
        }
        return this.playerTransportPlane.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerTransportPlaneProp getPlayerTransportPlaneV(Long k) {
        if (this.playerTransportPlane == null || !this.playerTransportPlane.containsKey(k)) {
            return null;
        }
        return this.playerTransportPlane.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearPlayerTransportPlane() {
        if (this.playerTransportPlane != null) {
            this.playerTransportPlane.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removePlayerTransportPlaneV(Long k) {
        if (this.playerTransportPlane != null) {
            this.playerTransportPlane.remove(k);
        }
    }
    /**
     * get playerSpyPlane
     *
     * @return playerSpyPlane value
     */
    public Int64PlayerSpyPlaneMapProp getPlayerSpyPlane() {
        if (this.playerSpyPlane == null) {
            this.playerSpyPlane = new Int64PlayerSpyPlaneMapProp(this, FIELD_INDEX_PLAYERSPYPLANE);
        }
        return this.playerSpyPlane;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putPlayerSpyPlaneV(PlayerSpyPlaneProp v) {
        this.getPlayerSpyPlane().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerSpyPlaneProp addEmptyPlayerSpyPlane(Long k) {
        return this.getPlayerSpyPlane().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getPlayerSpyPlaneSize() {
        if (this.playerSpyPlane == null) {
            return 0;
        }
        return this.playerSpyPlane.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isPlayerSpyPlaneEmpty() {
        if (this.playerSpyPlane == null) {
            return true;
        }
        return this.playerSpyPlane.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerSpyPlaneProp getPlayerSpyPlaneV(Long k) {
        if (this.playerSpyPlane == null || !this.playerSpyPlane.containsKey(k)) {
            return null;
        }
        return this.playerSpyPlane.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearPlayerSpyPlane() {
        if (this.playerSpyPlane != null) {
            this.playerSpyPlane.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removePlayerSpyPlaneV(Long k) {
        if (this.playerSpyPlane != null) {
            this.playerSpyPlane.remove(k);
        }
    }
    /**
     * get sysAssistInfos
     *
     * @return sysAssistInfos value
     */
    public Int64SysAssistInfoMapProp getSysAssistInfos() {
        if (this.sysAssistInfos == null) {
            this.sysAssistInfos = new Int64SysAssistInfoMapProp(this, FIELD_INDEX_SYSASSISTINFOS);
        }
        return this.sysAssistInfos;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putSysAssistInfosV(SysAssistInfoProp v) {
        this.getSysAssistInfos().put(v.getExpiration(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public SysAssistInfoProp addEmptySysAssistInfos(Long k) {
        return this.getSysAssistInfos().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getSysAssistInfosSize() {
        if (this.sysAssistInfos == null) {
            return 0;
        }
        return this.sysAssistInfos.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isSysAssistInfosEmpty() {
        if (this.sysAssistInfos == null) {
            return true;
        }
        return this.sysAssistInfos.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public SysAssistInfoProp getSysAssistInfosV(Long k) {
        if (this.sysAssistInfos == null || !this.sysAssistInfos.containsKey(k)) {
            return null;
        }
        return this.sysAssistInfos.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearSysAssistInfos() {
        if (this.sysAssistInfos != null) {
            this.sysAssistInfos.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeSysAssistInfosV(Long k) {
        if (this.sysAssistInfos != null) {
            this.sysAssistInfos.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerPlaneModelPB.Builder getCopyCsBuilder() {
        final PlayerPlaneModelPB.Builder builder = PlayerPlaneModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerPlaneModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.playerBattlePlane != null) {
            PlayerPB.Int64PlayerBattlePlaneMapPB.Builder tmpBuilder = PlayerPB.Int64PlayerBattlePlaneMapPB.newBuilder();
            final int tmpFieldCnt = this.playerBattlePlane.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerBattlePlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerBattlePlane();
            }
        }  else if (builder.hasPlayerBattlePlane()) {
            // 清理PlayerBattlePlane
            builder.clearPlayerBattlePlane();
            fieldCnt++;
        }
        if (this.playerTransportPlane != null) {
            PlayerPB.Int64PlayerTransportPlaneMapPB.Builder tmpBuilder = PlayerPB.Int64PlayerTransportPlaneMapPB.newBuilder();
            final int tmpFieldCnt = this.playerTransportPlane.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerTransportPlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerTransportPlane();
            }
        }  else if (builder.hasPlayerTransportPlane()) {
            // 清理PlayerTransportPlane
            builder.clearPlayerTransportPlane();
            fieldCnt++;
        }
        if (this.playerSpyPlane != null) {
            PlayerPB.Int64PlayerSpyPlaneMapPB.Builder tmpBuilder = PlayerPB.Int64PlayerSpyPlaneMapPB.newBuilder();
            final int tmpFieldCnt = this.playerSpyPlane.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerSpyPlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerSpyPlane();
            }
        }  else if (builder.hasPlayerSpyPlane()) {
            // 清理PlayerSpyPlane
            builder.clearPlayerSpyPlane();
            fieldCnt++;
        }
        if (this.sysAssistInfos != null) {
            StructPB.Int64SysAssistInfoMapPB.Builder tmpBuilder = StructPB.Int64SysAssistInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.sysAssistInfos.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSysAssistInfos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSysAssistInfos();
            }
        }  else if (builder.hasSysAssistInfos()) {
            // 清理SysAssistInfos
            builder.clearSysAssistInfos();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerPlaneModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERBATTLEPLANE) && this.playerBattlePlane != null) {
            final boolean needClear = !builder.hasPlayerBattlePlane();
            final int tmpFieldCnt = this.playerBattlePlane.copyChangeToCs(builder.getPlayerBattlePlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerBattlePlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERTRANSPORTPLANE) && this.playerTransportPlane != null) {
            final boolean needClear = !builder.hasPlayerTransportPlane();
            final int tmpFieldCnt = this.playerTransportPlane.copyChangeToCs(builder.getPlayerTransportPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerTransportPlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERSPYPLANE) && this.playerSpyPlane != null) {
            final boolean needClear = !builder.hasPlayerSpyPlane();
            final int tmpFieldCnt = this.playerSpyPlane.copyChangeToCs(builder.getPlayerSpyPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerSpyPlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_SYSASSISTINFOS) && this.sysAssistInfos != null) {
            final boolean needClear = !builder.hasSysAssistInfos();
            final int tmpFieldCnt = this.sysAssistInfos.copyChangeToCs(builder.getSysAssistInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSysAssistInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerPlaneModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERBATTLEPLANE) && this.playerBattlePlane != null) {
            final boolean needClear = !builder.hasPlayerBattlePlane();
            final int tmpFieldCnt = this.playerBattlePlane.copyChangeToAndClearDeleteKeysCs(builder.getPlayerBattlePlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerBattlePlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERTRANSPORTPLANE) && this.playerTransportPlane != null) {
            final boolean needClear = !builder.hasPlayerTransportPlane();
            final int tmpFieldCnt = this.playerTransportPlane.copyChangeToAndClearDeleteKeysCs(builder.getPlayerTransportPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerTransportPlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERSPYPLANE) && this.playerSpyPlane != null) {
            final boolean needClear = !builder.hasPlayerSpyPlane();
            final int tmpFieldCnt = this.playerSpyPlane.copyChangeToAndClearDeleteKeysCs(builder.getPlayerSpyPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerSpyPlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_SYSASSISTINFOS) && this.sysAssistInfos != null) {
            final boolean needClear = !builder.hasSysAssistInfos();
            final int tmpFieldCnt = this.sysAssistInfos.copyChangeToAndClearDeleteKeysCs(builder.getSysAssistInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSysAssistInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerPlaneModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerBattlePlane()) {
            this.getPlayerBattlePlane().mergeFromCs(proto.getPlayerBattlePlane());
        } else {
            if (this.playerBattlePlane != null) {
                this.playerBattlePlane.mergeFromCs(proto.getPlayerBattlePlane());
            }
        }
        if (proto.hasPlayerTransportPlane()) {
            this.getPlayerTransportPlane().mergeFromCs(proto.getPlayerTransportPlane());
        } else {
            if (this.playerTransportPlane != null) {
                this.playerTransportPlane.mergeFromCs(proto.getPlayerTransportPlane());
            }
        }
        if (proto.hasPlayerSpyPlane()) {
            this.getPlayerSpyPlane().mergeFromCs(proto.getPlayerSpyPlane());
        } else {
            if (this.playerSpyPlane != null) {
                this.playerSpyPlane.mergeFromCs(proto.getPlayerSpyPlane());
            }
        }
        if (proto.hasSysAssistInfos()) {
            this.getSysAssistInfos().mergeFromCs(proto.getSysAssistInfos());
        } else {
            if (this.sysAssistInfos != null) {
                this.sysAssistInfos.mergeFromCs(proto.getSysAssistInfos());
            }
        }
        this.markAll();
        return PlayerPlaneModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerPlaneModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerBattlePlane()) {
            this.getPlayerBattlePlane().mergeChangeFromCs(proto.getPlayerBattlePlane());
            fieldCnt++;
        }
        if (proto.hasPlayerTransportPlane()) {
            this.getPlayerTransportPlane().mergeChangeFromCs(proto.getPlayerTransportPlane());
            fieldCnt++;
        }
        if (proto.hasPlayerSpyPlane()) {
            this.getPlayerSpyPlane().mergeChangeFromCs(proto.getPlayerSpyPlane());
            fieldCnt++;
        }
        if (proto.hasSysAssistInfos()) {
            this.getSysAssistInfos().mergeChangeFromCs(proto.getSysAssistInfos());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerPlaneModel.Builder getCopyDbBuilder() {
        final PlayerPlaneModel.Builder builder = PlayerPlaneModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerPlaneModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.playerBattlePlane != null) {
            Player.Int64PlayerBattlePlaneMap.Builder tmpBuilder = Player.Int64PlayerBattlePlaneMap.newBuilder();
            final int tmpFieldCnt = this.playerBattlePlane.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerBattlePlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerBattlePlane();
            }
        }  else if (builder.hasPlayerBattlePlane()) {
            // 清理PlayerBattlePlane
            builder.clearPlayerBattlePlane();
            fieldCnt++;
        }
        if (this.playerTransportPlane != null) {
            Player.Int64PlayerTransportPlaneMap.Builder tmpBuilder = Player.Int64PlayerTransportPlaneMap.newBuilder();
            final int tmpFieldCnt = this.playerTransportPlane.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerTransportPlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerTransportPlane();
            }
        }  else if (builder.hasPlayerTransportPlane()) {
            // 清理PlayerTransportPlane
            builder.clearPlayerTransportPlane();
            fieldCnt++;
        }
        if (this.playerSpyPlane != null) {
            Player.Int64PlayerSpyPlaneMap.Builder tmpBuilder = Player.Int64PlayerSpyPlaneMap.newBuilder();
            final int tmpFieldCnt = this.playerSpyPlane.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerSpyPlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerSpyPlane();
            }
        }  else if (builder.hasPlayerSpyPlane()) {
            // 清理PlayerSpyPlane
            builder.clearPlayerSpyPlane();
            fieldCnt++;
        }
        if (this.sysAssistInfos != null) {
            Struct.Int64SysAssistInfoMap.Builder tmpBuilder = Struct.Int64SysAssistInfoMap.newBuilder();
            final int tmpFieldCnt = this.sysAssistInfos.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSysAssistInfos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSysAssistInfos();
            }
        }  else if (builder.hasSysAssistInfos()) {
            // 清理SysAssistInfos
            builder.clearSysAssistInfos();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerPlaneModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERBATTLEPLANE) && this.playerBattlePlane != null) {
            final boolean needClear = !builder.hasPlayerBattlePlane();
            final int tmpFieldCnt = this.playerBattlePlane.copyChangeToDb(builder.getPlayerBattlePlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerBattlePlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERTRANSPORTPLANE) && this.playerTransportPlane != null) {
            final boolean needClear = !builder.hasPlayerTransportPlane();
            final int tmpFieldCnt = this.playerTransportPlane.copyChangeToDb(builder.getPlayerTransportPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerTransportPlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERSPYPLANE) && this.playerSpyPlane != null) {
            final boolean needClear = !builder.hasPlayerSpyPlane();
            final int tmpFieldCnt = this.playerSpyPlane.copyChangeToDb(builder.getPlayerSpyPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerSpyPlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_SYSASSISTINFOS) && this.sysAssistInfos != null) {
            final boolean needClear = !builder.hasSysAssistInfos();
            final int tmpFieldCnt = this.sysAssistInfos.copyChangeToDb(builder.getSysAssistInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSysAssistInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerPlaneModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerBattlePlane()) {
            this.getPlayerBattlePlane().mergeFromDb(proto.getPlayerBattlePlane());
        } else {
            if (this.playerBattlePlane != null) {
                this.playerBattlePlane.mergeFromDb(proto.getPlayerBattlePlane());
            }
        }
        if (proto.hasPlayerTransportPlane()) {
            this.getPlayerTransportPlane().mergeFromDb(proto.getPlayerTransportPlane());
        } else {
            if (this.playerTransportPlane != null) {
                this.playerTransportPlane.mergeFromDb(proto.getPlayerTransportPlane());
            }
        }
        if (proto.hasPlayerSpyPlane()) {
            this.getPlayerSpyPlane().mergeFromDb(proto.getPlayerSpyPlane());
        } else {
            if (this.playerSpyPlane != null) {
                this.playerSpyPlane.mergeFromDb(proto.getPlayerSpyPlane());
            }
        }
        if (proto.hasSysAssistInfos()) {
            this.getSysAssistInfos().mergeFromDb(proto.getSysAssistInfos());
        } else {
            if (this.sysAssistInfos != null) {
                this.sysAssistInfos.mergeFromDb(proto.getSysAssistInfos());
            }
        }
        this.markAll();
        return PlayerPlaneModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerPlaneModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerBattlePlane()) {
            this.getPlayerBattlePlane().mergeChangeFromDb(proto.getPlayerBattlePlane());
            fieldCnt++;
        }
        if (proto.hasPlayerTransportPlane()) {
            this.getPlayerTransportPlane().mergeChangeFromDb(proto.getPlayerTransportPlane());
            fieldCnt++;
        }
        if (proto.hasPlayerSpyPlane()) {
            this.getPlayerSpyPlane().mergeChangeFromDb(proto.getPlayerSpyPlane());
            fieldCnt++;
        }
        if (proto.hasSysAssistInfos()) {
            this.getSysAssistInfos().mergeChangeFromDb(proto.getSysAssistInfos());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerPlaneModel.Builder getCopySsBuilder() {
        final PlayerPlaneModel.Builder builder = PlayerPlaneModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerPlaneModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.playerBattlePlane != null) {
            Player.Int64PlayerBattlePlaneMap.Builder tmpBuilder = Player.Int64PlayerBattlePlaneMap.newBuilder();
            final int tmpFieldCnt = this.playerBattlePlane.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerBattlePlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerBattlePlane();
            }
        }  else if (builder.hasPlayerBattlePlane()) {
            // 清理PlayerBattlePlane
            builder.clearPlayerBattlePlane();
            fieldCnt++;
        }
        if (this.playerTransportPlane != null) {
            Player.Int64PlayerTransportPlaneMap.Builder tmpBuilder = Player.Int64PlayerTransportPlaneMap.newBuilder();
            final int tmpFieldCnt = this.playerTransportPlane.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerTransportPlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerTransportPlane();
            }
        }  else if (builder.hasPlayerTransportPlane()) {
            // 清理PlayerTransportPlane
            builder.clearPlayerTransportPlane();
            fieldCnt++;
        }
        if (this.playerSpyPlane != null) {
            Player.Int64PlayerSpyPlaneMap.Builder tmpBuilder = Player.Int64PlayerSpyPlaneMap.newBuilder();
            final int tmpFieldCnt = this.playerSpyPlane.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerSpyPlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerSpyPlane();
            }
        }  else if (builder.hasPlayerSpyPlane()) {
            // 清理PlayerSpyPlane
            builder.clearPlayerSpyPlane();
            fieldCnt++;
        }
        if (this.sysAssistInfos != null) {
            Struct.Int64SysAssistInfoMap.Builder tmpBuilder = Struct.Int64SysAssistInfoMap.newBuilder();
            final int tmpFieldCnt = this.sysAssistInfos.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSysAssistInfos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSysAssistInfos();
            }
        }  else if (builder.hasSysAssistInfos()) {
            // 清理SysAssistInfos
            builder.clearSysAssistInfos();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerPlaneModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERBATTLEPLANE) && this.playerBattlePlane != null) {
            final boolean needClear = !builder.hasPlayerBattlePlane();
            final int tmpFieldCnt = this.playerBattlePlane.copyChangeToSs(builder.getPlayerBattlePlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerBattlePlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERTRANSPORTPLANE) && this.playerTransportPlane != null) {
            final boolean needClear = !builder.hasPlayerTransportPlane();
            final int tmpFieldCnt = this.playerTransportPlane.copyChangeToSs(builder.getPlayerTransportPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerTransportPlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERSPYPLANE) && this.playerSpyPlane != null) {
            final boolean needClear = !builder.hasPlayerSpyPlane();
            final int tmpFieldCnt = this.playerSpyPlane.copyChangeToSs(builder.getPlayerSpyPlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerSpyPlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_SYSASSISTINFOS) && this.sysAssistInfos != null) {
            final boolean needClear = !builder.hasSysAssistInfos();
            final int tmpFieldCnt = this.sysAssistInfos.copyChangeToSs(builder.getSysAssistInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSysAssistInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerPlaneModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerBattlePlane()) {
            this.getPlayerBattlePlane().mergeFromSs(proto.getPlayerBattlePlane());
        } else {
            if (this.playerBattlePlane != null) {
                this.playerBattlePlane.mergeFromSs(proto.getPlayerBattlePlane());
            }
        }
        if (proto.hasPlayerTransportPlane()) {
            this.getPlayerTransportPlane().mergeFromSs(proto.getPlayerTransportPlane());
        } else {
            if (this.playerTransportPlane != null) {
                this.playerTransportPlane.mergeFromSs(proto.getPlayerTransportPlane());
            }
        }
        if (proto.hasPlayerSpyPlane()) {
            this.getPlayerSpyPlane().mergeFromSs(proto.getPlayerSpyPlane());
        } else {
            if (this.playerSpyPlane != null) {
                this.playerSpyPlane.mergeFromSs(proto.getPlayerSpyPlane());
            }
        }
        if (proto.hasSysAssistInfos()) {
            this.getSysAssistInfos().mergeFromSs(proto.getSysAssistInfos());
        } else {
            if (this.sysAssistInfos != null) {
                this.sysAssistInfos.mergeFromSs(proto.getSysAssistInfos());
            }
        }
        this.markAll();
        return PlayerPlaneModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerPlaneModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerBattlePlane()) {
            this.getPlayerBattlePlane().mergeChangeFromSs(proto.getPlayerBattlePlane());
            fieldCnt++;
        }
        if (proto.hasPlayerTransportPlane()) {
            this.getPlayerTransportPlane().mergeChangeFromSs(proto.getPlayerTransportPlane());
            fieldCnt++;
        }
        if (proto.hasPlayerSpyPlane()) {
            this.getPlayerSpyPlane().mergeChangeFromSs(proto.getPlayerSpyPlane());
            fieldCnt++;
        }
        if (proto.hasSysAssistInfos()) {
            this.getSysAssistInfos().mergeChangeFromSs(proto.getSysAssistInfos());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerPlaneModel.Builder builder = PlayerPlaneModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERBATTLEPLANE) && this.playerBattlePlane != null) {
            this.playerBattlePlane.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLAYERTRANSPORTPLANE) && this.playerTransportPlane != null) {
            this.playerTransportPlane.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLAYERSPYPLANE) && this.playerSpyPlane != null) {
            this.playerSpyPlane.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SYSASSISTINFOS) && this.sysAssistInfos != null) {
            this.sysAssistInfos.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.playerBattlePlane != null) {
            this.playerBattlePlane.markAll();
        }
        if (this.playerTransportPlane != null) {
            this.playerTransportPlane.markAll();
        }
        if (this.playerSpyPlane != null) {
            this.playerSpyPlane.markAll();
        }
        if (this.sysAssistInfos != null) {
            this.sysAssistInfos.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerPlaneModelProp)) {
            return false;
        }
        final PlayerPlaneModelProp otherNode = (PlayerPlaneModelProp) node;
        if (!this.getPlayerBattlePlane().compareDataTo(otherNode.getPlayerBattlePlane())) {
            return false;
        }
        if (!this.getPlayerTransportPlane().compareDataTo(otherNode.getPlayerTransportPlane())) {
            return false;
        }
        if (!this.getPlayerSpyPlane().compareDataTo(otherNode.getPlayerSpyPlane())) {
            return false;
        }
        if (!this.getSysAssistInfos().compareDataTo(otherNode.getSysAssistInfos())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
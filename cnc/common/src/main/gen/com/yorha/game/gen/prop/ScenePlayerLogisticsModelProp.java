package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.ScenePlayerLogisticsModel;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.ScenePlayerLogisticsModelPB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class ScenePlayerLogisticsModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_LOGISTICSPLANES = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int64ScenePlayerLogisticsStatusMapProp logisticsPlanes = null;

    public ScenePlayerLogisticsModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ScenePlayerLogisticsModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get logisticsPlanes
     *
     * @return logisticsPlanes value
     */
    public Int64ScenePlayerLogisticsStatusMapProp getLogisticsPlanes() {
        if (this.logisticsPlanes == null) {
            this.logisticsPlanes = new Int64ScenePlayerLogisticsStatusMapProp(this, FIELD_INDEX_LOGISTICSPLANES);
        }
        return this.logisticsPlanes;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putLogisticsPlanesV(ScenePlayerLogisticsStatusProp v) {
        this.getLogisticsPlanes().put(v.getLogisticsPlaneId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ScenePlayerLogisticsStatusProp addEmptyLogisticsPlanes(Long k) {
        return this.getLogisticsPlanes().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getLogisticsPlanesSize() {
        if (this.logisticsPlanes == null) {
            return 0;
        }
        return this.logisticsPlanes.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isLogisticsPlanesEmpty() {
        if (this.logisticsPlanes == null) {
            return true;
        }
        return this.logisticsPlanes.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ScenePlayerLogisticsStatusProp getLogisticsPlanesV(Long k) {
        if (this.logisticsPlanes == null || !this.logisticsPlanes.containsKey(k)) {
            return null;
        }
        return this.logisticsPlanes.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearLogisticsPlanes() {
        if (this.logisticsPlanes != null) {
            this.logisticsPlanes.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeLogisticsPlanesV(Long k) {
        if (this.logisticsPlanes != null) {
            this.logisticsPlanes.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerLogisticsModelPB.Builder getCopyCsBuilder() {
        final ScenePlayerLogisticsModelPB.Builder builder = ScenePlayerLogisticsModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ScenePlayerLogisticsModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.logisticsPlanes != null) {
            PlayerPB.Int64ScenePlayerLogisticsStatusMapPB.Builder tmpBuilder = PlayerPB.Int64ScenePlayerLogisticsStatusMapPB.newBuilder();
            final int tmpFieldCnt = this.logisticsPlanes.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLogisticsPlanes(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLogisticsPlanes();
            }
        }  else if (builder.hasLogisticsPlanes()) {
            // 清理LogisticsPlanes
            builder.clearLogisticsPlanes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ScenePlayerLogisticsModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LOGISTICSPLANES) && this.logisticsPlanes != null) {
            final boolean needClear = !builder.hasLogisticsPlanes();
            final int tmpFieldCnt = this.logisticsPlanes.copyChangeToCs(builder.getLogisticsPlanesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLogisticsPlanes();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ScenePlayerLogisticsModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LOGISTICSPLANES) && this.logisticsPlanes != null) {
            final boolean needClear = !builder.hasLogisticsPlanes();
            final int tmpFieldCnt = this.logisticsPlanes.copyChangeToAndClearDeleteKeysCs(builder.getLogisticsPlanesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLogisticsPlanes();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ScenePlayerLogisticsModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLogisticsPlanes()) {
            this.getLogisticsPlanes().mergeFromCs(proto.getLogisticsPlanes());
        } else {
            if (this.logisticsPlanes != null) {
                this.logisticsPlanes.mergeFromCs(proto.getLogisticsPlanes());
            }
        }
        this.markAll();
        return ScenePlayerLogisticsModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ScenePlayerLogisticsModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLogisticsPlanes()) {
            this.getLogisticsPlanes().mergeChangeFromCs(proto.getLogisticsPlanes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerLogisticsModel.Builder getCopyDbBuilder() {
        final ScenePlayerLogisticsModel.Builder builder = ScenePlayerLogisticsModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ScenePlayerLogisticsModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.logisticsPlanes != null) {
            Player.Int64ScenePlayerLogisticsStatusMap.Builder tmpBuilder = Player.Int64ScenePlayerLogisticsStatusMap.newBuilder();
            final int tmpFieldCnt = this.logisticsPlanes.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLogisticsPlanes(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLogisticsPlanes();
            }
        }  else if (builder.hasLogisticsPlanes()) {
            // 清理LogisticsPlanes
            builder.clearLogisticsPlanes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ScenePlayerLogisticsModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LOGISTICSPLANES) && this.logisticsPlanes != null) {
            final boolean needClear = !builder.hasLogisticsPlanes();
            final int tmpFieldCnt = this.logisticsPlanes.copyChangeToDb(builder.getLogisticsPlanesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLogisticsPlanes();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ScenePlayerLogisticsModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLogisticsPlanes()) {
            this.getLogisticsPlanes().mergeFromDb(proto.getLogisticsPlanes());
        } else {
            if (this.logisticsPlanes != null) {
                this.logisticsPlanes.mergeFromDb(proto.getLogisticsPlanes());
            }
        }
        this.markAll();
        return ScenePlayerLogisticsModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ScenePlayerLogisticsModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLogisticsPlanes()) {
            this.getLogisticsPlanes().mergeChangeFromDb(proto.getLogisticsPlanes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerLogisticsModel.Builder getCopySsBuilder() {
        final ScenePlayerLogisticsModel.Builder builder = ScenePlayerLogisticsModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ScenePlayerLogisticsModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.logisticsPlanes != null) {
            Player.Int64ScenePlayerLogisticsStatusMap.Builder tmpBuilder = Player.Int64ScenePlayerLogisticsStatusMap.newBuilder();
            final int tmpFieldCnt = this.logisticsPlanes.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLogisticsPlanes(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLogisticsPlanes();
            }
        }  else if (builder.hasLogisticsPlanes()) {
            // 清理LogisticsPlanes
            builder.clearLogisticsPlanes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ScenePlayerLogisticsModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LOGISTICSPLANES) && this.logisticsPlanes != null) {
            final boolean needClear = !builder.hasLogisticsPlanes();
            final int tmpFieldCnt = this.logisticsPlanes.copyChangeToSs(builder.getLogisticsPlanesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLogisticsPlanes();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ScenePlayerLogisticsModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLogisticsPlanes()) {
            this.getLogisticsPlanes().mergeFromSs(proto.getLogisticsPlanes());
        } else {
            if (this.logisticsPlanes != null) {
                this.logisticsPlanes.mergeFromSs(proto.getLogisticsPlanes());
            }
        }
        this.markAll();
        return ScenePlayerLogisticsModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ScenePlayerLogisticsModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLogisticsPlanes()) {
            this.getLogisticsPlanes().mergeChangeFromSs(proto.getLogisticsPlanes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ScenePlayerLogisticsModel.Builder builder = ScenePlayerLogisticsModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_LOGISTICSPLANES) && this.logisticsPlanes != null) {
            this.logisticsPlanes.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.logisticsPlanes != null) {
            this.logisticsPlanes.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ScenePlayerLogisticsModelProp)) {
            return false;
        }
        final ScenePlayerLogisticsModelProp otherNode = (ScenePlayerLogisticsModelProp) node;
        if (!this.getLogisticsPlanes().compareDataTo(otherNode.getLogisticsPlanes())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
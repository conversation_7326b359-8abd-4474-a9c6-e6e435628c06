package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerRecruitModel;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.PlayerRecruitModelPB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerRecruitModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_SILVERRECRUITPOOLINFO = 0;
    public static final int FIELD_INDEX_GOLDRECRUITPOOLINFO = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private RecruitPoolProp silverRecruitPoolInfo = null;
    private RecruitPoolProp goldRecruitPoolInfo = null;

    public PlayerRecruitModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerRecruitModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get silverRecruitPoolInfo
     *
     * @return silverRecruitPoolInfo value
     */
    public RecruitPoolProp getSilverRecruitPoolInfo() {
        if (this.silverRecruitPoolInfo == null) {
            this.silverRecruitPoolInfo = new RecruitPoolProp(this, FIELD_INDEX_SILVERRECRUITPOOLINFO);
        }
        return this.silverRecruitPoolInfo;
    }

    /**
     * get goldRecruitPoolInfo
     *
     * @return goldRecruitPoolInfo value
     */
    public RecruitPoolProp getGoldRecruitPoolInfo() {
        if (this.goldRecruitPoolInfo == null) {
            this.goldRecruitPoolInfo = new RecruitPoolProp(this, FIELD_INDEX_GOLDRECRUITPOOLINFO);
        }
        return this.goldRecruitPoolInfo;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerRecruitModelPB.Builder getCopyCsBuilder() {
        final PlayerRecruitModelPB.Builder builder = PlayerRecruitModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerRecruitModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.silverRecruitPoolInfo != null) {
            PlayerPB.RecruitPoolPB.Builder tmpBuilder = PlayerPB.RecruitPoolPB.newBuilder();
            final int tmpFieldCnt = this.silverRecruitPoolInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSilverRecruitPoolInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSilverRecruitPoolInfo();
            }
        }  else if (builder.hasSilverRecruitPoolInfo()) {
            // 清理SilverRecruitPoolInfo
            builder.clearSilverRecruitPoolInfo();
            fieldCnt++;
        }
        if (this.goldRecruitPoolInfo != null) {
            PlayerPB.RecruitPoolPB.Builder tmpBuilder = PlayerPB.RecruitPoolPB.newBuilder();
            final int tmpFieldCnt = this.goldRecruitPoolInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGoldRecruitPoolInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGoldRecruitPoolInfo();
            }
        }  else if (builder.hasGoldRecruitPoolInfo()) {
            // 清理GoldRecruitPoolInfo
            builder.clearGoldRecruitPoolInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerRecruitModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SILVERRECRUITPOOLINFO) && this.silverRecruitPoolInfo != null) {
            final boolean needClear = !builder.hasSilverRecruitPoolInfo();
            final int tmpFieldCnt = this.silverRecruitPoolInfo.copyChangeToCs(builder.getSilverRecruitPoolInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSilverRecruitPoolInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_GOLDRECRUITPOOLINFO) && this.goldRecruitPoolInfo != null) {
            final boolean needClear = !builder.hasGoldRecruitPoolInfo();
            final int tmpFieldCnt = this.goldRecruitPoolInfo.copyChangeToCs(builder.getGoldRecruitPoolInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGoldRecruitPoolInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerRecruitModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SILVERRECRUITPOOLINFO) && this.silverRecruitPoolInfo != null) {
            final boolean needClear = !builder.hasSilverRecruitPoolInfo();
            final int tmpFieldCnt = this.silverRecruitPoolInfo.copyChangeToAndClearDeleteKeysCs(builder.getSilverRecruitPoolInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSilverRecruitPoolInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_GOLDRECRUITPOOLINFO) && this.goldRecruitPoolInfo != null) {
            final boolean needClear = !builder.hasGoldRecruitPoolInfo();
            final int tmpFieldCnt = this.goldRecruitPoolInfo.copyChangeToAndClearDeleteKeysCs(builder.getGoldRecruitPoolInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGoldRecruitPoolInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerRecruitModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSilverRecruitPoolInfo()) {
            this.getSilverRecruitPoolInfo().mergeFromCs(proto.getSilverRecruitPoolInfo());
        } else {
            if (this.silverRecruitPoolInfo != null) {
                this.silverRecruitPoolInfo.mergeFromCs(proto.getSilverRecruitPoolInfo());
            }
        }
        if (proto.hasGoldRecruitPoolInfo()) {
            this.getGoldRecruitPoolInfo().mergeFromCs(proto.getGoldRecruitPoolInfo());
        } else {
            if (this.goldRecruitPoolInfo != null) {
                this.goldRecruitPoolInfo.mergeFromCs(proto.getGoldRecruitPoolInfo());
            }
        }
        this.markAll();
        return PlayerRecruitModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerRecruitModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSilverRecruitPoolInfo()) {
            this.getSilverRecruitPoolInfo().mergeChangeFromCs(proto.getSilverRecruitPoolInfo());
            fieldCnt++;
        }
        if (proto.hasGoldRecruitPoolInfo()) {
            this.getGoldRecruitPoolInfo().mergeChangeFromCs(proto.getGoldRecruitPoolInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerRecruitModel.Builder getCopyDbBuilder() {
        final PlayerRecruitModel.Builder builder = PlayerRecruitModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerRecruitModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.silverRecruitPoolInfo != null) {
            Player.RecruitPool.Builder tmpBuilder = Player.RecruitPool.newBuilder();
            final int tmpFieldCnt = this.silverRecruitPoolInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSilverRecruitPoolInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSilverRecruitPoolInfo();
            }
        }  else if (builder.hasSilverRecruitPoolInfo()) {
            // 清理SilverRecruitPoolInfo
            builder.clearSilverRecruitPoolInfo();
            fieldCnt++;
        }
        if (this.goldRecruitPoolInfo != null) {
            Player.RecruitPool.Builder tmpBuilder = Player.RecruitPool.newBuilder();
            final int tmpFieldCnt = this.goldRecruitPoolInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGoldRecruitPoolInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGoldRecruitPoolInfo();
            }
        }  else if (builder.hasGoldRecruitPoolInfo()) {
            // 清理GoldRecruitPoolInfo
            builder.clearGoldRecruitPoolInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerRecruitModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SILVERRECRUITPOOLINFO) && this.silverRecruitPoolInfo != null) {
            final boolean needClear = !builder.hasSilverRecruitPoolInfo();
            final int tmpFieldCnt = this.silverRecruitPoolInfo.copyChangeToDb(builder.getSilverRecruitPoolInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSilverRecruitPoolInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_GOLDRECRUITPOOLINFO) && this.goldRecruitPoolInfo != null) {
            final boolean needClear = !builder.hasGoldRecruitPoolInfo();
            final int tmpFieldCnt = this.goldRecruitPoolInfo.copyChangeToDb(builder.getGoldRecruitPoolInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGoldRecruitPoolInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerRecruitModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSilverRecruitPoolInfo()) {
            this.getSilverRecruitPoolInfo().mergeFromDb(proto.getSilverRecruitPoolInfo());
        } else {
            if (this.silverRecruitPoolInfo != null) {
                this.silverRecruitPoolInfo.mergeFromDb(proto.getSilverRecruitPoolInfo());
            }
        }
        if (proto.hasGoldRecruitPoolInfo()) {
            this.getGoldRecruitPoolInfo().mergeFromDb(proto.getGoldRecruitPoolInfo());
        } else {
            if (this.goldRecruitPoolInfo != null) {
                this.goldRecruitPoolInfo.mergeFromDb(proto.getGoldRecruitPoolInfo());
            }
        }
        this.markAll();
        return PlayerRecruitModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerRecruitModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSilverRecruitPoolInfo()) {
            this.getSilverRecruitPoolInfo().mergeChangeFromDb(proto.getSilverRecruitPoolInfo());
            fieldCnt++;
        }
        if (proto.hasGoldRecruitPoolInfo()) {
            this.getGoldRecruitPoolInfo().mergeChangeFromDb(proto.getGoldRecruitPoolInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerRecruitModel.Builder getCopySsBuilder() {
        final PlayerRecruitModel.Builder builder = PlayerRecruitModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerRecruitModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.silverRecruitPoolInfo != null) {
            Player.RecruitPool.Builder tmpBuilder = Player.RecruitPool.newBuilder();
            final int tmpFieldCnt = this.silverRecruitPoolInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSilverRecruitPoolInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSilverRecruitPoolInfo();
            }
        }  else if (builder.hasSilverRecruitPoolInfo()) {
            // 清理SilverRecruitPoolInfo
            builder.clearSilverRecruitPoolInfo();
            fieldCnt++;
        }
        if (this.goldRecruitPoolInfo != null) {
            Player.RecruitPool.Builder tmpBuilder = Player.RecruitPool.newBuilder();
            final int tmpFieldCnt = this.goldRecruitPoolInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGoldRecruitPoolInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGoldRecruitPoolInfo();
            }
        }  else if (builder.hasGoldRecruitPoolInfo()) {
            // 清理GoldRecruitPoolInfo
            builder.clearGoldRecruitPoolInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerRecruitModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SILVERRECRUITPOOLINFO) && this.silverRecruitPoolInfo != null) {
            final boolean needClear = !builder.hasSilverRecruitPoolInfo();
            final int tmpFieldCnt = this.silverRecruitPoolInfo.copyChangeToSs(builder.getSilverRecruitPoolInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSilverRecruitPoolInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_GOLDRECRUITPOOLINFO) && this.goldRecruitPoolInfo != null) {
            final boolean needClear = !builder.hasGoldRecruitPoolInfo();
            final int tmpFieldCnt = this.goldRecruitPoolInfo.copyChangeToSs(builder.getGoldRecruitPoolInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGoldRecruitPoolInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerRecruitModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSilverRecruitPoolInfo()) {
            this.getSilverRecruitPoolInfo().mergeFromSs(proto.getSilverRecruitPoolInfo());
        } else {
            if (this.silverRecruitPoolInfo != null) {
                this.silverRecruitPoolInfo.mergeFromSs(proto.getSilverRecruitPoolInfo());
            }
        }
        if (proto.hasGoldRecruitPoolInfo()) {
            this.getGoldRecruitPoolInfo().mergeFromSs(proto.getGoldRecruitPoolInfo());
        } else {
            if (this.goldRecruitPoolInfo != null) {
                this.goldRecruitPoolInfo.mergeFromSs(proto.getGoldRecruitPoolInfo());
            }
        }
        this.markAll();
        return PlayerRecruitModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerRecruitModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSilverRecruitPoolInfo()) {
            this.getSilverRecruitPoolInfo().mergeChangeFromSs(proto.getSilverRecruitPoolInfo());
            fieldCnt++;
        }
        if (proto.hasGoldRecruitPoolInfo()) {
            this.getGoldRecruitPoolInfo().mergeChangeFromSs(proto.getGoldRecruitPoolInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerRecruitModel.Builder builder = PlayerRecruitModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SILVERRECRUITPOOLINFO) && this.silverRecruitPoolInfo != null) {
            this.silverRecruitPoolInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_GOLDRECRUITPOOLINFO) && this.goldRecruitPoolInfo != null) {
            this.goldRecruitPoolInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.silverRecruitPoolInfo != null) {
            this.silverRecruitPoolInfo.markAll();
        }
        if (this.goldRecruitPoolInfo != null) {
            this.goldRecruitPoolInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerRecruitModelProp)) {
            return false;
        }
        final PlayerRecruitModelProp otherNode = (PlayerRecruitModelProp) node;
        if (!this.getSilverRecruitPoolInfo().compareDataTo(otherNode.getSilverRecruitPoolInfo())) {
            return false;
        }
        if (!this.getGoldRecruitPoolInfo().compareDataTo(otherNode.getGoldRecruitPoolInfo())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zone.KingdomOfficeModel;
import com.yorha.proto.Zone;
import com.yorha.proto.ZonePB.KingdomOfficeModelPB;
import com.yorha.proto.ZonePB;


/**
 * <AUTHOR> auto gen
 */
public class KingdomOfficeModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_OFFICEINFO = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32KingdomOfficeInfoMapProp officeInfo = null;

    public KingdomOfficeModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public KingdomOfficeModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get officeInfo
     *
     * @return officeInfo value
     */
    public Int32KingdomOfficeInfoMapProp getOfficeInfo() {
        if (this.officeInfo == null) {
            this.officeInfo = new Int32KingdomOfficeInfoMapProp(this, FIELD_INDEX_OFFICEINFO);
        }
        return this.officeInfo;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putOfficeInfoV(KingdomOfficeInfoProp v) {
        this.getOfficeInfo().put(v.getOfficeId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public KingdomOfficeInfoProp addEmptyOfficeInfo(Integer k) {
        return this.getOfficeInfo().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getOfficeInfoSize() {
        if (this.officeInfo == null) {
            return 0;
        }
        return this.officeInfo.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isOfficeInfoEmpty() {
        if (this.officeInfo == null) {
            return true;
        }
        return this.officeInfo.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public KingdomOfficeInfoProp getOfficeInfoV(Integer k) {
        if (this.officeInfo == null || !this.officeInfo.containsKey(k)) {
            return null;
        }
        return this.officeInfo.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearOfficeInfo() {
        if (this.officeInfo != null) {
            this.officeInfo.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeOfficeInfoV(Integer k) {
        if (this.officeInfo != null) {
            this.officeInfo.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomOfficeModelPB.Builder getCopyCsBuilder() {
        final KingdomOfficeModelPB.Builder builder = KingdomOfficeModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(KingdomOfficeModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.officeInfo != null) {
            ZonePB.Int32KingdomOfficeInfoMapPB.Builder tmpBuilder = ZonePB.Int32KingdomOfficeInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.officeInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOfficeInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOfficeInfo();
            }
        }  else if (builder.hasOfficeInfo()) {
            // 清理OfficeInfo
            builder.clearOfficeInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(KingdomOfficeModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OFFICEINFO) && this.officeInfo != null) {
            final boolean needClear = !builder.hasOfficeInfo();
            final int tmpFieldCnt = this.officeInfo.copyChangeToCs(builder.getOfficeInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOfficeInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(KingdomOfficeModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OFFICEINFO) && this.officeInfo != null) {
            final boolean needClear = !builder.hasOfficeInfo();
            final int tmpFieldCnt = this.officeInfo.copyChangeToAndClearDeleteKeysCs(builder.getOfficeInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOfficeInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(KingdomOfficeModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOfficeInfo()) {
            this.getOfficeInfo().mergeFromCs(proto.getOfficeInfo());
        } else {
            if (this.officeInfo != null) {
                this.officeInfo.mergeFromCs(proto.getOfficeInfo());
            }
        }
        this.markAll();
        return KingdomOfficeModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(KingdomOfficeModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOfficeInfo()) {
            this.getOfficeInfo().mergeChangeFromCs(proto.getOfficeInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomOfficeModel.Builder getCopyDbBuilder() {
        final KingdomOfficeModel.Builder builder = KingdomOfficeModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(KingdomOfficeModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.officeInfo != null) {
            Zone.Int32KingdomOfficeInfoMap.Builder tmpBuilder = Zone.Int32KingdomOfficeInfoMap.newBuilder();
            final int tmpFieldCnt = this.officeInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOfficeInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOfficeInfo();
            }
        }  else if (builder.hasOfficeInfo()) {
            // 清理OfficeInfo
            builder.clearOfficeInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(KingdomOfficeModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OFFICEINFO) && this.officeInfo != null) {
            final boolean needClear = !builder.hasOfficeInfo();
            final int tmpFieldCnt = this.officeInfo.copyChangeToDb(builder.getOfficeInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOfficeInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(KingdomOfficeModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOfficeInfo()) {
            this.getOfficeInfo().mergeFromDb(proto.getOfficeInfo());
        } else {
            if (this.officeInfo != null) {
                this.officeInfo.mergeFromDb(proto.getOfficeInfo());
            }
        }
        this.markAll();
        return KingdomOfficeModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(KingdomOfficeModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOfficeInfo()) {
            this.getOfficeInfo().mergeChangeFromDb(proto.getOfficeInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomOfficeModel.Builder getCopySsBuilder() {
        final KingdomOfficeModel.Builder builder = KingdomOfficeModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(KingdomOfficeModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.officeInfo != null) {
            Zone.Int32KingdomOfficeInfoMap.Builder tmpBuilder = Zone.Int32KingdomOfficeInfoMap.newBuilder();
            final int tmpFieldCnt = this.officeInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOfficeInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOfficeInfo();
            }
        }  else if (builder.hasOfficeInfo()) {
            // 清理OfficeInfo
            builder.clearOfficeInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(KingdomOfficeModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OFFICEINFO) && this.officeInfo != null) {
            final boolean needClear = !builder.hasOfficeInfo();
            final int tmpFieldCnt = this.officeInfo.copyChangeToSs(builder.getOfficeInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOfficeInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(KingdomOfficeModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOfficeInfo()) {
            this.getOfficeInfo().mergeFromSs(proto.getOfficeInfo());
        } else {
            if (this.officeInfo != null) {
                this.officeInfo.mergeFromSs(proto.getOfficeInfo());
            }
        }
        this.markAll();
        return KingdomOfficeModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(KingdomOfficeModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOfficeInfo()) {
            this.getOfficeInfo().mergeChangeFromSs(proto.getOfficeInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        KingdomOfficeModel.Builder builder = KingdomOfficeModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_OFFICEINFO) && this.officeInfo != null) {
            this.officeInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.officeInfo != null) {
            this.officeInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof KingdomOfficeModelProp)) {
            return false;
        }
        final KingdomOfficeModelProp otherNode = (KingdomOfficeModelProp) node;
        if (!this.getOfficeInfo().compareDataTo(otherNode.getOfficeInfo())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
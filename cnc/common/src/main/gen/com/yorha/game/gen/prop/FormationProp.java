package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructPlayer.Formation;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPlayerPB.FormationPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class FormationProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_FORMATIONID = 0;
    public static final int FIELD_INDEX_MAINHEROID = 1;
    public static final int FIELD_INDEX_DEPUTYHEROID = 2;
    public static final int FIELD_INDEX_SOLDIERS = 3;
    public static final int FIELD_INDEX_ARMYID = 4;
    public static final int FIELD_INDEX_PLANEID = 5;
    public static final int FIELD_INDEX_NAME = 6;

    public static final int FIELD_COUNT = 7;

    private long markBits0 = 0L;

    private int formationId = Constant.DEFAULT_INT_VALUE;
    private int mainHeroId = Constant.DEFAULT_INT_VALUE;
    private int deputyHeroId = Constant.DEFAULT_INT_VALUE;
    private Int32SoldierMapProp soldiers = null;
    private long armyId = Constant.DEFAULT_LONG_VALUE;
    private long planeId = Constant.DEFAULT_LONG_VALUE;
    private String name = Constant.DEFAULT_STR_VALUE;

    public FormationProp() {
        super(null, 0, FIELD_COUNT);
    }

    public FormationProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get formationId
     *
     * @return formationId value
     */
    public int getFormationId() {
        return this.formationId;
    }

    /**
     * set formationId && set marked
     *
     * @param formationId new value
     * @return current object
     */
    public FormationProp setFormationId(int formationId) {
        if (this.formationId != formationId) {
            this.mark(FIELD_INDEX_FORMATIONID);
            this.formationId = formationId;
        }
        return this;
    }

    /**
     * inner set formationId
     *
     * @param formationId new value
     */
    private void innerSetFormationId(int formationId) {
        this.formationId = formationId;
    }

    /**
     * get mainHeroId
     *
     * @return mainHeroId value
     */
    public int getMainHeroId() {
        return this.mainHeroId;
    }

    /**
     * set mainHeroId && set marked
     *
     * @param mainHeroId new value
     * @return current object
     */
    public FormationProp setMainHeroId(int mainHeroId) {
        if (this.mainHeroId != mainHeroId) {
            this.mark(FIELD_INDEX_MAINHEROID);
            this.mainHeroId = mainHeroId;
        }
        return this;
    }

    /**
     * inner set mainHeroId
     *
     * @param mainHeroId new value
     */
    private void innerSetMainHeroId(int mainHeroId) {
        this.mainHeroId = mainHeroId;
    }

    /**
     * get deputyHeroId
     *
     * @return deputyHeroId value
     */
    public int getDeputyHeroId() {
        return this.deputyHeroId;
    }

    /**
     * set deputyHeroId && set marked
     *
     * @param deputyHeroId new value
     * @return current object
     */
    public FormationProp setDeputyHeroId(int deputyHeroId) {
        if (this.deputyHeroId != deputyHeroId) {
            this.mark(FIELD_INDEX_DEPUTYHEROID);
            this.deputyHeroId = deputyHeroId;
        }
        return this;
    }

    /**
     * inner set deputyHeroId
     *
     * @param deputyHeroId new value
     */
    private void innerSetDeputyHeroId(int deputyHeroId) {
        this.deputyHeroId = deputyHeroId;
    }

    /**
     * get soldiers
     *
     * @return soldiers value
     */
    public Int32SoldierMapProp getSoldiers() {
        if (this.soldiers == null) {
            this.soldiers = new Int32SoldierMapProp(this, FIELD_INDEX_SOLDIERS);
        }
        return this.soldiers;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putSoldiersV(SoldierProp v) {
        this.getSoldiers().put(v.getSoldierId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public SoldierProp addEmptySoldiers(Integer k) {
        return this.getSoldiers().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getSoldiersSize() {
        if (this.soldiers == null) {
            return 0;
        }
        return this.soldiers.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isSoldiersEmpty() {
        if (this.soldiers == null) {
            return true;
        }
        return this.soldiers.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public SoldierProp getSoldiersV(Integer k) {
        if (this.soldiers == null || !this.soldiers.containsKey(k)) {
            return null;
        }
        return this.soldiers.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearSoldiers() {
        if (this.soldiers != null) {
            this.soldiers.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeSoldiersV(Integer k) {
        if (this.soldiers != null) {
            this.soldiers.remove(k);
        }
    }
    /**
     * get armyId
     *
     * @return armyId value
     */
    public long getArmyId() {
        return this.armyId;
    }

    /**
     * set armyId && set marked
     *
     * @param armyId new value
     * @return current object
     */
    public FormationProp setArmyId(long armyId) {
        if (this.armyId != armyId) {
            this.mark(FIELD_INDEX_ARMYID);
            this.armyId = armyId;
        }
        return this;
    }

    /**
     * inner set armyId
     *
     * @param armyId new value
     */
    private void innerSetArmyId(long armyId) {
        this.armyId = armyId;
    }

    /**
     * get planeId
     *
     * @return planeId value
     */
    public long getPlaneId() {
        return this.planeId;
    }

    /**
     * set planeId && set marked
     *
     * @param planeId new value
     * @return current object
     */
    public FormationProp setPlaneId(long planeId) {
        if (this.planeId != planeId) {
            this.mark(FIELD_INDEX_PLANEID);
            this.planeId = planeId;
        }
        return this;
    }

    /**
     * inner set planeId
     *
     * @param planeId new value
     */
    private void innerSetPlaneId(long planeId) {
        this.planeId = planeId;
    }

    /**
     * get name
     *
     * @return name value
     */
    public String getName() {
        return this.name;
    }

    /**
     * set name && set marked
     *
     * @param name new value
     * @return current object
     */
    public FormationProp setName(String name) {
        if (name == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.name, name)) {
            this.mark(FIELD_INDEX_NAME);
            this.name = name;
        }
        return this;
    }

    /**
     * inner set name
     *
     * @param name new value
     */
    private void innerSetName(String name) {
        this.name = name;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public FormationPB.Builder getCopyCsBuilder() {
        final FormationPB.Builder builder = FormationPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(FormationPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getFormationId() != 0) {
            builder.setFormationId(this.getFormationId());
            fieldCnt++;
        }  else if (builder.hasFormationId()) {
            // 清理FormationId
            builder.clearFormationId();
            fieldCnt++;
        }
        if (this.getMainHeroId() != 0) {
            builder.setMainHeroId(this.getMainHeroId());
            fieldCnt++;
        }  else if (builder.hasMainHeroId()) {
            // 清理MainHeroId
            builder.clearMainHeroId();
            fieldCnt++;
        }
        if (this.getDeputyHeroId() != 0) {
            builder.setDeputyHeroId(this.getDeputyHeroId());
            fieldCnt++;
        }  else if (builder.hasDeputyHeroId()) {
            // 清理DeputyHeroId
            builder.clearDeputyHeroId();
            fieldCnt++;
        }
        if (this.soldiers != null) {
            StructPB.Int32SoldierMapPB.Builder tmpBuilder = StructPB.Int32SoldierMapPB.newBuilder();
            final int tmpFieldCnt = this.soldiers.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldiers();
            }
        }  else if (builder.hasSoldiers()) {
            // 清理Soldiers
            builder.clearSoldiers();
            fieldCnt++;
        }
        if (this.getArmyId() != 0L) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }  else if (builder.hasArmyId()) {
            // 清理ArmyId
            builder.clearArmyId();
            fieldCnt++;
        }
        if (this.getPlaneId() != 0L) {
            builder.setPlaneId(this.getPlaneId());
            fieldCnt++;
        }  else if (builder.hasPlaneId()) {
            // 清理PlaneId
            builder.clearPlaneId();
            fieldCnt++;
        }
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(FormationPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FORMATIONID)) {
            builder.setFormationId(this.getFormationId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAINHEROID)) {
            builder.setMainHeroId(this.getMainHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHEROID)) {
            builder.setDeputyHeroId(this.getDeputyHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERS) && this.soldiers != null) {
            final boolean needClear = !builder.hasSoldiers();
            final int tmpFieldCnt = this.soldiers.copyChangeToCs(builder.getSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANEID)) {
            builder.setPlaneId(this.getPlaneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(FormationPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FORMATIONID)) {
            builder.setFormationId(this.getFormationId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAINHEROID)) {
            builder.setMainHeroId(this.getMainHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHEROID)) {
            builder.setDeputyHeroId(this.getDeputyHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERS) && this.soldiers != null) {
            final boolean needClear = !builder.hasSoldiers();
            final int tmpFieldCnt = this.soldiers.copyChangeToAndClearDeleteKeysCs(builder.getSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANEID)) {
            builder.setPlaneId(this.getPlaneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(FormationPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasFormationId()) {
            this.innerSetFormationId(proto.getFormationId());
        } else {
            this.innerSetFormationId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMainHeroId()) {
            this.innerSetMainHeroId(proto.getMainHeroId());
        } else {
            this.innerSetMainHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDeputyHeroId()) {
            this.innerSetDeputyHeroId(proto.getDeputyHeroId());
        } else {
            this.innerSetDeputyHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSoldiers()) {
            this.getSoldiers().mergeFromCs(proto.getSoldiers());
        } else {
            if (this.soldiers != null) {
                this.soldiers.mergeFromCs(proto.getSoldiers());
            }
        }
        if (proto.hasArmyId()) {
            this.innerSetArmyId(proto.getArmyId());
        } else {
            this.innerSetArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlaneId()) {
            this.innerSetPlaneId(proto.getPlaneId());
        } else {
            this.innerSetPlaneId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return FormationProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(FormationPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasFormationId()) {
            this.setFormationId(proto.getFormationId());
            fieldCnt++;
        }
        if (proto.hasMainHeroId()) {
            this.setMainHeroId(proto.getMainHeroId());
            fieldCnt++;
        }
        if (proto.hasDeputyHeroId()) {
            this.setDeputyHeroId(proto.getDeputyHeroId());
            fieldCnt++;
        }
        if (proto.hasSoldiers()) {
            this.getSoldiers().mergeChangeFromCs(proto.getSoldiers());
            fieldCnt++;
        }
        if (proto.hasArmyId()) {
            this.setArmyId(proto.getArmyId());
            fieldCnt++;
        }
        if (proto.hasPlaneId()) {
            this.setPlaneId(proto.getPlaneId());
            fieldCnt++;
        }
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Formation.Builder getCopyDbBuilder() {
        final Formation.Builder builder = Formation.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(Formation.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getFormationId() != 0) {
            builder.setFormationId(this.getFormationId());
            fieldCnt++;
        }  else if (builder.hasFormationId()) {
            // 清理FormationId
            builder.clearFormationId();
            fieldCnt++;
        }
        if (this.getMainHeroId() != 0) {
            builder.setMainHeroId(this.getMainHeroId());
            fieldCnt++;
        }  else if (builder.hasMainHeroId()) {
            // 清理MainHeroId
            builder.clearMainHeroId();
            fieldCnt++;
        }
        if (this.getDeputyHeroId() != 0) {
            builder.setDeputyHeroId(this.getDeputyHeroId());
            fieldCnt++;
        }  else if (builder.hasDeputyHeroId()) {
            // 清理DeputyHeroId
            builder.clearDeputyHeroId();
            fieldCnt++;
        }
        if (this.soldiers != null) {
            Struct.Int32SoldierMap.Builder tmpBuilder = Struct.Int32SoldierMap.newBuilder();
            final int tmpFieldCnt = this.soldiers.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldiers();
            }
        }  else if (builder.hasSoldiers()) {
            // 清理Soldiers
            builder.clearSoldiers();
            fieldCnt++;
        }
        if (this.getArmyId() != 0L) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }  else if (builder.hasArmyId()) {
            // 清理ArmyId
            builder.clearArmyId();
            fieldCnt++;
        }
        if (this.getPlaneId() != 0L) {
            builder.setPlaneId(this.getPlaneId());
            fieldCnt++;
        }  else if (builder.hasPlaneId()) {
            // 清理PlaneId
            builder.clearPlaneId();
            fieldCnt++;
        }
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(Formation.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FORMATIONID)) {
            builder.setFormationId(this.getFormationId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAINHEROID)) {
            builder.setMainHeroId(this.getMainHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHEROID)) {
            builder.setDeputyHeroId(this.getDeputyHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERS) && this.soldiers != null) {
            final boolean needClear = !builder.hasSoldiers();
            final int tmpFieldCnt = this.soldiers.copyChangeToDb(builder.getSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANEID)) {
            builder.setPlaneId(this.getPlaneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(Formation proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasFormationId()) {
            this.innerSetFormationId(proto.getFormationId());
        } else {
            this.innerSetFormationId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMainHeroId()) {
            this.innerSetMainHeroId(proto.getMainHeroId());
        } else {
            this.innerSetMainHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDeputyHeroId()) {
            this.innerSetDeputyHeroId(proto.getDeputyHeroId());
        } else {
            this.innerSetDeputyHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSoldiers()) {
            this.getSoldiers().mergeFromDb(proto.getSoldiers());
        } else {
            if (this.soldiers != null) {
                this.soldiers.mergeFromDb(proto.getSoldiers());
            }
        }
        if (proto.hasArmyId()) {
            this.innerSetArmyId(proto.getArmyId());
        } else {
            this.innerSetArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlaneId()) {
            this.innerSetPlaneId(proto.getPlaneId());
        } else {
            this.innerSetPlaneId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return FormationProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(Formation proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasFormationId()) {
            this.setFormationId(proto.getFormationId());
            fieldCnt++;
        }
        if (proto.hasMainHeroId()) {
            this.setMainHeroId(proto.getMainHeroId());
            fieldCnt++;
        }
        if (proto.hasDeputyHeroId()) {
            this.setDeputyHeroId(proto.getDeputyHeroId());
            fieldCnt++;
        }
        if (proto.hasSoldiers()) {
            this.getSoldiers().mergeChangeFromDb(proto.getSoldiers());
            fieldCnt++;
        }
        if (proto.hasArmyId()) {
            this.setArmyId(proto.getArmyId());
            fieldCnt++;
        }
        if (proto.hasPlaneId()) {
            this.setPlaneId(proto.getPlaneId());
            fieldCnt++;
        }
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Formation.Builder getCopySsBuilder() {
        final Formation.Builder builder = Formation.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(Formation.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getFormationId() != 0) {
            builder.setFormationId(this.getFormationId());
            fieldCnt++;
        }  else if (builder.hasFormationId()) {
            // 清理FormationId
            builder.clearFormationId();
            fieldCnt++;
        }
        if (this.getMainHeroId() != 0) {
            builder.setMainHeroId(this.getMainHeroId());
            fieldCnt++;
        }  else if (builder.hasMainHeroId()) {
            // 清理MainHeroId
            builder.clearMainHeroId();
            fieldCnt++;
        }
        if (this.getDeputyHeroId() != 0) {
            builder.setDeputyHeroId(this.getDeputyHeroId());
            fieldCnt++;
        }  else if (builder.hasDeputyHeroId()) {
            // 清理DeputyHeroId
            builder.clearDeputyHeroId();
            fieldCnt++;
        }
        if (this.soldiers != null) {
            Struct.Int32SoldierMap.Builder tmpBuilder = Struct.Int32SoldierMap.newBuilder();
            final int tmpFieldCnt = this.soldiers.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldiers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldiers();
            }
        }  else if (builder.hasSoldiers()) {
            // 清理Soldiers
            builder.clearSoldiers();
            fieldCnt++;
        }
        if (this.getArmyId() != 0L) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }  else if (builder.hasArmyId()) {
            // 清理ArmyId
            builder.clearArmyId();
            fieldCnt++;
        }
        if (this.getPlaneId() != 0L) {
            builder.setPlaneId(this.getPlaneId());
            fieldCnt++;
        }  else if (builder.hasPlaneId()) {
            // 清理PlaneId
            builder.clearPlaneId();
            fieldCnt++;
        }
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(Formation.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FORMATIONID)) {
            builder.setFormationId(this.getFormationId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAINHEROID)) {
            builder.setMainHeroId(this.getMainHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHEROID)) {
            builder.setDeputyHeroId(this.getDeputyHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERS) && this.soldiers != null) {
            final boolean needClear = !builder.hasSoldiers();
            final int tmpFieldCnt = this.soldiers.copyChangeToSs(builder.getSoldiersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldiers();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANEID)) {
            builder.setPlaneId(this.getPlaneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(Formation proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasFormationId()) {
            this.innerSetFormationId(proto.getFormationId());
        } else {
            this.innerSetFormationId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMainHeroId()) {
            this.innerSetMainHeroId(proto.getMainHeroId());
        } else {
            this.innerSetMainHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDeputyHeroId()) {
            this.innerSetDeputyHeroId(proto.getDeputyHeroId());
        } else {
            this.innerSetDeputyHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSoldiers()) {
            this.getSoldiers().mergeFromSs(proto.getSoldiers());
        } else {
            if (this.soldiers != null) {
                this.soldiers.mergeFromSs(proto.getSoldiers());
            }
        }
        if (proto.hasArmyId()) {
            this.innerSetArmyId(proto.getArmyId());
        } else {
            this.innerSetArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlaneId()) {
            this.innerSetPlaneId(proto.getPlaneId());
        } else {
            this.innerSetPlaneId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return FormationProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(Formation proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasFormationId()) {
            this.setFormationId(proto.getFormationId());
            fieldCnt++;
        }
        if (proto.hasMainHeroId()) {
            this.setMainHeroId(proto.getMainHeroId());
            fieldCnt++;
        }
        if (proto.hasDeputyHeroId()) {
            this.setDeputyHeroId(proto.getDeputyHeroId());
            fieldCnt++;
        }
        if (proto.hasSoldiers()) {
            this.getSoldiers().mergeChangeFromSs(proto.getSoldiers());
            fieldCnt++;
        }
        if (proto.hasArmyId()) {
            this.setArmyId(proto.getArmyId());
            fieldCnt++;
        }
        if (proto.hasPlaneId()) {
            this.setPlaneId(proto.getPlaneId());
            fieldCnt++;
        }
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        Formation.Builder builder = Formation.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERS) && this.soldiers != null) {
            this.soldiers.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.soldiers != null) {
            this.soldiers.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.formationId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof FormationProp)) {
            return false;
        }
        final FormationProp otherNode = (FormationProp) node;
        if (this.formationId != otherNode.formationId) {
            return false;
        }
        if (this.mainHeroId != otherNode.mainHeroId) {
            return false;
        }
        if (this.deputyHeroId != otherNode.deputyHeroId) {
            return false;
        }
        if (!this.getSoldiers().compareDataTo(otherNode.getSoldiers())) {
            return false;
        }
        if (this.armyId != otherNode.armyId) {
            return false;
        }
        if (this.planeId != otherNode.planeId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.name, otherNode.name)) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 57;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
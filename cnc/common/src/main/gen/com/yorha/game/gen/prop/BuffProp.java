package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructBattle.Buff;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructBattlePB.BuffPB;
import com.yorha.proto.StructBattlePB;


/**
 * <AUTHOR> auto gen
 */
public class BuffProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_GROUPID = 0;
    public static final int FIELD_INDEX_TEMPLATEID = 1;
    public static final int FIELD_INDEX_SOURCE = 2;
    public static final int FIELD_INDEX_SOURCEID = 3;
    public static final int FIELD_INDEX_LAYER = 4;
    public static final int FIELD_INDEX_CREATETSMS = 5;
    public static final int FIELD_INDEX_SPECDATA = 6;

    public static final int FIELD_COUNT = 7;

    private long markBits0 = 0L;

    private int groupId = Constant.DEFAULT_INT_VALUE;
    private int templateId = Constant.DEFAULT_INT_VALUE;
    private BuffSource source = BuffSource.forNumber(0);
    private long sourceId = Constant.DEFAULT_LONG_VALUE;
    private int layer = Constant.DEFAULT_INT_VALUE;
    private long createTsMs = Constant.DEFAULT_LONG_VALUE;
    private BuffSpecDataProp specData = null;

    public BuffProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BuffProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get groupId
     *
     * @return groupId value
     */
    public int getGroupId() {
        return this.groupId;
    }

    /**
     * set groupId && set marked
     *
     * @param groupId new value
     * @return current object
     */
    public BuffProp setGroupId(int groupId) {
        if (this.groupId != groupId) {
            this.mark(FIELD_INDEX_GROUPID);
            this.groupId = groupId;
        }
        return this;
    }

    /**
     * inner set groupId
     *
     * @param groupId new value
     */
    private void innerSetGroupId(int groupId) {
        this.groupId = groupId;
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public BuffProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }

    /**
     * get source
     *
     * @return source value
     */
    public BuffSource getSource() {
        return this.source;
    }

    /**
     * set source && set marked
     *
     * @param source new value
     * @return current object
     */
    public BuffProp setSource(BuffSource source) {
        if (source == null) {
            throw new NullPointerException();
        }
        if (this.source != source) {
            this.mark(FIELD_INDEX_SOURCE);
            this.source = source;
        }
        return this;
    }

    /**
     * inner set source
     *
     * @param source new value
     */
    private void innerSetSource(BuffSource source) {
        this.source = source;
    }

    /**
     * get sourceId
     *
     * @return sourceId value
     */
    public long getSourceId() {
        return this.sourceId;
    }

    /**
     * set sourceId && set marked
     *
     * @param sourceId new value
     * @return current object
     */
    public BuffProp setSourceId(long sourceId) {
        if (this.sourceId != sourceId) {
            this.mark(FIELD_INDEX_SOURCEID);
            this.sourceId = sourceId;
        }
        return this;
    }

    /**
     * inner set sourceId
     *
     * @param sourceId new value
     */
    private void innerSetSourceId(long sourceId) {
        this.sourceId = sourceId;
    }

    /**
     * get layer
     *
     * @return layer value
     */
    public int getLayer() {
        return this.layer;
    }

    /**
     * set layer && set marked
     *
     * @param layer new value
     * @return current object
     */
    public BuffProp setLayer(int layer) {
        if (this.layer != layer) {
            this.mark(FIELD_INDEX_LAYER);
            this.layer = layer;
        }
        return this;
    }

    /**
     * inner set layer
     *
     * @param layer new value
     */
    private void innerSetLayer(int layer) {
        this.layer = layer;
    }

    /**
     * get createTsMs
     *
     * @return createTsMs value
     */
    public long getCreateTsMs() {
        return this.createTsMs;
    }

    /**
     * set createTsMs && set marked
     *
     * @param createTsMs new value
     * @return current object
     */
    public BuffProp setCreateTsMs(long createTsMs) {
        if (this.createTsMs != createTsMs) {
            this.mark(FIELD_INDEX_CREATETSMS);
            this.createTsMs = createTsMs;
        }
        return this;
    }

    /**
     * inner set createTsMs
     *
     * @param createTsMs new value
     */
    private void innerSetCreateTsMs(long createTsMs) {
        this.createTsMs = createTsMs;
    }

    /**
     * get specData
     *
     * @return specData value
     */
    public BuffSpecDataProp getSpecData() {
        if (this.specData == null) {
            this.specData = new BuffSpecDataProp(this, FIELD_INDEX_SPECDATA);
        }
        return this.specData;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BuffPB.Builder getCopyCsBuilder() {
        final BuffPB.Builder builder = BuffPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BuffPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGroupId() != 0) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }  else if (builder.hasGroupId()) {
            // 清理GroupId
            builder.clearGroupId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getLayer() != 0) {
            builder.setLayer(this.getLayer());
            fieldCnt++;
        }  else if (builder.hasLayer()) {
            // 清理Layer
            builder.clearLayer();
            fieldCnt++;
        }
        if (this.getCreateTsMs() != 0L) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }  else if (builder.hasCreateTsMs()) {
            // 清理CreateTsMs
            builder.clearCreateTsMs();
            fieldCnt++;
        }
        if (this.specData != null) {
            StructBattlePB.BuffSpecDataPB.Builder tmpBuilder = StructBattlePB.BuffSpecDataPB.newBuilder();
            final int tmpFieldCnt = this.specData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpecData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpecData();
            }
        }  else if (builder.hasSpecData()) {
            // 清理SpecData
            builder.clearSpecData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BuffPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LAYER)) {
            builder.setLayer(this.getLayer());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SPECDATA) && this.specData != null) {
            final boolean needClear = !builder.hasSpecData();
            final int tmpFieldCnt = this.specData.copyChangeToCs(builder.getSpecDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpecData();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BuffPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LAYER)) {
            builder.setLayer(this.getLayer());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SPECDATA) && this.specData != null) {
            final boolean needClear = !builder.hasSpecData();
            final int tmpFieldCnt = this.specData.copyChangeToAndClearDeleteKeysCs(builder.getSpecDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpecData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BuffPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGroupId()) {
            this.innerSetGroupId(proto.getGroupId());
        } else {
            this.innerSetGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLayer()) {
            this.innerSetLayer(proto.getLayer());
        } else {
            this.innerSetLayer(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCreateTsMs()) {
            this.innerSetCreateTsMs(proto.getCreateTsMs());
        } else {
            this.innerSetCreateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSpecData()) {
            this.getSpecData().mergeFromCs(proto.getSpecData());
        } else {
            if (this.specData != null) {
                this.specData.mergeFromCs(proto.getSpecData());
            }
        }
        this.markAll();
        return BuffProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BuffPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGroupId()) {
            this.setGroupId(proto.getGroupId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasLayer()) {
            this.setLayer(proto.getLayer());
            fieldCnt++;
        }
        if (proto.hasCreateTsMs()) {
            this.setCreateTsMs(proto.getCreateTsMs());
            fieldCnt++;
        }
        if (proto.hasSpecData()) {
            this.getSpecData().mergeChangeFromCs(proto.getSpecData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Buff.Builder getCopyDbBuilder() {
        final Buff.Builder builder = Buff.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(Buff.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGroupId() != 0) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }  else if (builder.hasGroupId()) {
            // 清理GroupId
            builder.clearGroupId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getSource() != BuffSource.forNumber(0)) {
            builder.setSource(this.getSource());
            fieldCnt++;
        }  else if (builder.hasSource()) {
            // 清理Source
            builder.clearSource();
            fieldCnt++;
        }
        if (this.getSourceId() != 0L) {
            builder.setSourceId(this.getSourceId());
            fieldCnt++;
        }  else if (builder.hasSourceId()) {
            // 清理SourceId
            builder.clearSourceId();
            fieldCnt++;
        }
        if (this.getLayer() != 0) {
            builder.setLayer(this.getLayer());
            fieldCnt++;
        }  else if (builder.hasLayer()) {
            // 清理Layer
            builder.clearLayer();
            fieldCnt++;
        }
        if (this.getCreateTsMs() != 0L) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }  else if (builder.hasCreateTsMs()) {
            // 清理CreateTsMs
            builder.clearCreateTsMs();
            fieldCnt++;
        }
        if (this.specData != null) {
            StructBattle.BuffSpecData.Builder tmpBuilder = StructBattle.BuffSpecData.newBuilder();
            final int tmpFieldCnt = this.specData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpecData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpecData();
            }
        }  else if (builder.hasSpecData()) {
            // 清理SpecData
            builder.clearSpecData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(Buff.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOURCE)) {
            builder.setSource(this.getSource());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOURCEID)) {
            builder.setSourceId(this.getSourceId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LAYER)) {
            builder.setLayer(this.getLayer());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SPECDATA) && this.specData != null) {
            final boolean needClear = !builder.hasSpecData();
            final int tmpFieldCnt = this.specData.copyChangeToDb(builder.getSpecDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpecData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(Buff proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGroupId()) {
            this.innerSetGroupId(proto.getGroupId());
        } else {
            this.innerSetGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSource()) {
            this.innerSetSource(proto.getSource());
        } else {
            this.innerSetSource(BuffSource.forNumber(0));
        }
        if (proto.hasSourceId()) {
            this.innerSetSourceId(proto.getSourceId());
        } else {
            this.innerSetSourceId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLayer()) {
            this.innerSetLayer(proto.getLayer());
        } else {
            this.innerSetLayer(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCreateTsMs()) {
            this.innerSetCreateTsMs(proto.getCreateTsMs());
        } else {
            this.innerSetCreateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSpecData()) {
            this.getSpecData().mergeFromDb(proto.getSpecData());
        } else {
            if (this.specData != null) {
                this.specData.mergeFromDb(proto.getSpecData());
            }
        }
        this.markAll();
        return BuffProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(Buff proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGroupId()) {
            this.setGroupId(proto.getGroupId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasSource()) {
            this.setSource(proto.getSource());
            fieldCnt++;
        }
        if (proto.hasSourceId()) {
            this.setSourceId(proto.getSourceId());
            fieldCnt++;
        }
        if (proto.hasLayer()) {
            this.setLayer(proto.getLayer());
            fieldCnt++;
        }
        if (proto.hasCreateTsMs()) {
            this.setCreateTsMs(proto.getCreateTsMs());
            fieldCnt++;
        }
        if (proto.hasSpecData()) {
            this.getSpecData().mergeChangeFromDb(proto.getSpecData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Buff.Builder getCopySsBuilder() {
        final Buff.Builder builder = Buff.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(Buff.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGroupId() != 0) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }  else if (builder.hasGroupId()) {
            // 清理GroupId
            builder.clearGroupId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getSource() != BuffSource.forNumber(0)) {
            builder.setSource(this.getSource());
            fieldCnt++;
        }  else if (builder.hasSource()) {
            // 清理Source
            builder.clearSource();
            fieldCnt++;
        }
        if (this.getSourceId() != 0L) {
            builder.setSourceId(this.getSourceId());
            fieldCnt++;
        }  else if (builder.hasSourceId()) {
            // 清理SourceId
            builder.clearSourceId();
            fieldCnt++;
        }
        if (this.getLayer() != 0) {
            builder.setLayer(this.getLayer());
            fieldCnt++;
        }  else if (builder.hasLayer()) {
            // 清理Layer
            builder.clearLayer();
            fieldCnt++;
        }
        if (this.getCreateTsMs() != 0L) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }  else if (builder.hasCreateTsMs()) {
            // 清理CreateTsMs
            builder.clearCreateTsMs();
            fieldCnt++;
        }
        if (this.specData != null) {
            StructBattle.BuffSpecData.Builder tmpBuilder = StructBattle.BuffSpecData.newBuilder();
            final int tmpFieldCnt = this.specData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSpecData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSpecData();
            }
        }  else if (builder.hasSpecData()) {
            // 清理SpecData
            builder.clearSpecData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(Buff.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOURCE)) {
            builder.setSource(this.getSource());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SOURCEID)) {
            builder.setSourceId(this.getSourceId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LAYER)) {
            builder.setLayer(this.getLayer());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SPECDATA) && this.specData != null) {
            final boolean needClear = !builder.hasSpecData();
            final int tmpFieldCnt = this.specData.copyChangeToSs(builder.getSpecDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSpecData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(Buff proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGroupId()) {
            this.innerSetGroupId(proto.getGroupId());
        } else {
            this.innerSetGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSource()) {
            this.innerSetSource(proto.getSource());
        } else {
            this.innerSetSource(BuffSource.forNumber(0));
        }
        if (proto.hasSourceId()) {
            this.innerSetSourceId(proto.getSourceId());
        } else {
            this.innerSetSourceId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLayer()) {
            this.innerSetLayer(proto.getLayer());
        } else {
            this.innerSetLayer(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCreateTsMs()) {
            this.innerSetCreateTsMs(proto.getCreateTsMs());
        } else {
            this.innerSetCreateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSpecData()) {
            this.getSpecData().mergeFromSs(proto.getSpecData());
        } else {
            if (this.specData != null) {
                this.specData.mergeFromSs(proto.getSpecData());
            }
        }
        this.markAll();
        return BuffProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(Buff proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGroupId()) {
            this.setGroupId(proto.getGroupId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasSource()) {
            this.setSource(proto.getSource());
            fieldCnt++;
        }
        if (proto.hasSourceId()) {
            this.setSourceId(proto.getSourceId());
            fieldCnt++;
        }
        if (proto.hasLayer()) {
            this.setLayer(proto.getLayer());
            fieldCnt++;
        }
        if (proto.hasCreateTsMs()) {
            this.setCreateTsMs(proto.getCreateTsMs());
            fieldCnt++;
        }
        if (proto.hasSpecData()) {
            this.getSpecData().mergeChangeFromSs(proto.getSpecData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        Buff.Builder builder = Buff.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SPECDATA) && this.specData != null) {
            this.specData.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.specData != null) {
            this.specData.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.groupId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BuffProp)) {
            return false;
        }
        final BuffProp otherNode = (BuffProp) node;
        if (this.groupId != otherNode.groupId) {
            return false;
        }
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        if (this.source != otherNode.source) {
            return false;
        }
        if (this.sourceId != otherNode.sourceId) {
            return false;
        }
        if (this.layer != otherNode.layer) {
            return false;
        }
        if (this.createTsMs != otherNode.createTsMs) {
            return false;
        }
        if (!this.getSpecData().compareDataTo(otherNode.getSpecData())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 57;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
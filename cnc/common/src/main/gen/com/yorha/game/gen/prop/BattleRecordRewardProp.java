package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructBattle.BattleRecordReward;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructBattlePB.BattleRecordRewardPB;
import com.yorha.proto.StructBattlePB;


/**
 * <AUTHOR> auto gen
 */
public class BattleRecordRewardProp extends AbstractPropNode {

    public static final int FIELD_INDEX_REWARDS = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private DisplayRewardListProp rewards = null;

    public BattleRecordRewardProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BattleRecordRewardProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get rewards
     *
     * @return rewards value
     */
    public DisplayRewardListProp getRewards() {
        if (this.rewards == null) {
            this.rewards = new DisplayRewardListProp(this, FIELD_INDEX_REWARDS);
        }
        return this.rewards;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addRewards(DisplayRewardProp v) {
        this.getRewards().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public DisplayRewardProp getRewardsIndex(int index) {
        return this.getRewards().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public DisplayRewardProp removeRewards(DisplayRewardProp v) {
        if (this.rewards == null) {
            return null;
        }
        if(this.rewards.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getRewardsSize() {
        if (this.rewards == null) {
            return 0;
        }
        return this.rewards.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isRewardsEmpty() {
        if (this.rewards == null) {
            return true;
        }
        return this.getRewards().isEmpty();
    }

    /**
     * clear list
     */
    public void clearRewards() {
        this.getRewards().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public DisplayRewardProp removeRewardsIndex(int index) {
        return this.getRewards().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public DisplayRewardProp setRewardsIndex(int index, DisplayRewardProp v) {
        return this.getRewards().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRewardPB.Builder getCopyCsBuilder() {
        final BattleRecordRewardPB.Builder builder = BattleRecordRewardPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BattleRecordRewardPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.rewards != null) {
            StructBattlePB.DisplayRewardListPB.Builder tmpBuilder = StructBattlePB.DisplayRewardListPB.newBuilder();
            final int tmpFieldCnt = this.rewards.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewards(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewards();
            }
        }  else if (builder.hasRewards()) {
            // 清理Rewards
            builder.clearRewards();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BattleRecordRewardPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDS) && this.rewards != null) {
            final boolean needClear = !builder.hasRewards();
            final int tmpFieldCnt = this.rewards.copyChangeToCs(builder.getRewardsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewards();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BattleRecordRewardPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDS) && this.rewards != null) {
            final boolean needClear = !builder.hasRewards();
            final int tmpFieldCnt = this.rewards.copyChangeToCs(builder.getRewardsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewards();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BattleRecordRewardPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewards()) {
            this.getRewards().mergeFromCs(proto.getRewards());
        } else {
            if (this.rewards != null) {
                this.rewards.mergeFromCs(proto.getRewards());
            }
        }
        this.markAll();
        return BattleRecordRewardProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BattleRecordRewardPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewards()) {
            this.getRewards().mergeChangeFromCs(proto.getRewards());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordReward.Builder getCopyDbBuilder() {
        final BattleRecordReward.Builder builder = BattleRecordReward.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(BattleRecordReward.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.rewards != null) {
            StructBattle.DisplayRewardList.Builder tmpBuilder = StructBattle.DisplayRewardList.newBuilder();
            final int tmpFieldCnt = this.rewards.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewards(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewards();
            }
        }  else if (builder.hasRewards()) {
            // 清理Rewards
            builder.clearRewards();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(BattleRecordReward.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDS) && this.rewards != null) {
            final boolean needClear = !builder.hasRewards();
            final int tmpFieldCnt = this.rewards.copyChangeToDb(builder.getRewardsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewards();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(BattleRecordReward proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewards()) {
            this.getRewards().mergeFromDb(proto.getRewards());
        } else {
            if (this.rewards != null) {
                this.rewards.mergeFromDb(proto.getRewards());
            }
        }
        this.markAll();
        return BattleRecordRewardProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(BattleRecordReward proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewards()) {
            this.getRewards().mergeChangeFromDb(proto.getRewards());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordReward.Builder getCopySsBuilder() {
        final BattleRecordReward.Builder builder = BattleRecordReward.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(BattleRecordReward.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.rewards != null) {
            StructBattle.DisplayRewardList.Builder tmpBuilder = StructBattle.DisplayRewardList.newBuilder();
            final int tmpFieldCnt = this.rewards.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewards(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewards();
            }
        }  else if (builder.hasRewards()) {
            // 清理Rewards
            builder.clearRewards();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(BattleRecordReward.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDS) && this.rewards != null) {
            final boolean needClear = !builder.hasRewards();
            final int tmpFieldCnt = this.rewards.copyChangeToSs(builder.getRewardsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewards();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(BattleRecordReward proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewards()) {
            this.getRewards().mergeFromSs(proto.getRewards());
        } else {
            if (this.rewards != null) {
                this.rewards.mergeFromSs(proto.getRewards());
            }
        }
        this.markAll();
        return BattleRecordRewardProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(BattleRecordReward proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewards()) {
            this.getRewards().mergeChangeFromSs(proto.getRewards());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        BattleRecordReward.Builder builder = BattleRecordReward.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_REWARDS) && this.rewards != null) {
            this.rewards.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.rewards != null) {
            this.rewards.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BattleRecordRewardProp)) {
            return false;
        }
        final BattleRecordRewardProp otherNode = (BattleRecordRewardProp) node;
        if (!this.getRewards().compareDataTo(otherNode.getRewards())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}